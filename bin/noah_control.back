#! /bin/bash

script_dir=$(cd $(dirname $0) && pwd)
mod_dir=$(dirname $script_dir)
# !!!此处修改为bin文件名，跟Makefile中bin文件名保持一致
START_CMD=start_aoiml_web.sh
chmod +x /home/<USER>/aoiMl/start_aoiml_web.sh

# 获取当前脚本的路径，保存到FILE
if [[ -L "$0" ]];then
    FILE=$(readlink -f "$0")
else
    FILE=$0
fi
BASE_DIR=$(cd $(dirname ${FILE}); pwd)/..
echo "base_dir $BASE_DIR"
# supervise 所需的目录和文件
SUPERVISE="/bin/supervise"
SUPERVISE_CONF="${BASE_DIR}/supervise/conf/supervise.conf"
STATUS_DIR="${BASE_DIR}/supervise/status/${START_CMD}"

# 初始化tools环境
function init_tools(){
    ### install jumbo
    auto_install_jumbo
    ### install
    jumbo_auto_install curl
    jumbo_auto_install gdal
    jumbo_auto_install python3
    jumbo_auto_install python3-pip
    # jumbo_auto_install python-numpy
    jumbo_auto_install python3-setuptools
    # 使用python-rtree需要将 /home/<USER>/.jumbo/lib  加入到 LD_LIBRARY_PATH
    jumbo_auto_install python-rtree

    init_python_proxy
    ### python 依赖的库请在这里安装
    install_python_vendor

    init_afs
    if [[ -d "$mod_dir/output/tools" ]];then
        echo "tools already exists"
        cd "$mod_dir/output/tools"
        bash init.sh
        echo "init tools env"
    else
        cd $mod_dir
        rm -rf output*
        if [[ -f "/home/<USER>/.bash_profile" ]];then
          rm /home/<USER>/.bash_profile
        fi
        wget -q -O output.tar.gz --no-check-certificate --header "IREPO-TOKEN:b9eb2d63-10d7-48ef-ac6f-e43b816a9cae" "https://irepo.baidu-int.com/rest/prod/v3/baidu/newdms/nutstools/releases/*******/files"
        tar xf output.tar.gz
        cd output
        tar xf nutstools.tar.gz
        cd tools
        bash init.sh
        echo "init tools env"
        source  /home/<USER>/.bash_profile
    fi
    export LC_ALL=en_US.UTF-8
}

function auto_install_jumbo() {
        source "/home/<USER>/.jumbo/etc/bashrc"
        which jumbo > /dev/null
        if [ $? -eq 0 ]
        then
                source "/home/<USER>/.jumbo/etc/bashrc"
                echo "jumbo aleadly installed"
                return 1
        fi
        echo "start install jumbo"
        bash -c "$( curl http://jumbo.baidu.com/install_jumbo.sh )"
        source ~/.bashrc
        source "/home/<USER>/.jumbo/etc/bashrc"
        which jumbo > /dev/null
        if [ $? -eq 0 ]
        then
                source "/home/<USER>/.jumbo/etc/bashrc"
                return 1
        fi
        echo "install jumbo error"
        exit
}


function jumbo_auto_install() {
        ### 安装依赖
        ined=`jumbo search $1|grep installed`
        if [ -n "$ined" ]
        then
                echo "$1 aleadly installed"
                return 1
        fi
        jumbo install $1
        if [ $? -eq 0 ]
        then
                echo "install $1 success"
                return 1
        fi
        echo "install $1 error"
        exit
}


function init_python_proxy() {
    mkdir -p /home/<USER>/.pip/
    cat > /home/<USER>/.pip/pip.conf << EOF
[global]
timeout = 60
index = https://pip.baidu-int.com/search/
index-url = https://pip.baidu-int.com/simple/
trusted-host = pip.baidu-int.com
[list]
format = columns
EOF
    echo "set python proxy success"
}

function create_python_installed_cache() {
    python3 -m pip freeze > $BASE_DIR/installed.txt
}

function do_install_python_requests() {
  ined=`cat $BASE_DIR/installed.txt|grep $1`
  if [ -n "$ined" ]
  then
          echo "$1 aleadly installed"
          return 1
  fi
  echo "prepare install $1"
  python3 -m pip install -q $1
  if [ $? -eq 0 ]
  then
      echo "install $1 success"
      return 1
  fi
  echo "install $1 error"
}

function install_python_vendor() {
    create_python_installed_cache
    python3 -m pip install -q wheel
    do_install_python_requests Shapely==1.8.0
    do_install_python_requests requests==2.14.2
    do_install_python_requests geojson==2.5.0
    do_install_python_requests gdal==2.1.3
    do_install_python_requests PyYAML==6.0
    do_install_python_requests Flask==2.0.3
    do_install_python_requests psycopg2==2.9.5
    # opencv-contrib-python 已经包含了 opencv-python，两个不能一起装
    # do_install_python_requests opencv-python==********
    do_install_python_requests opencv-contrib-python==********
    do_install_python_requests tqdm==4.64.1
    do_install_python_requests Flask-APScheduler==1.12.4
    do_install_python_requests scikit-learn==0.24.2
    do_install_python_requests Rtree==0.9.7
    do_install_python_requests pyproj==2.6.1
    do_install_python_requests wget==3.2
    do_install_python_requests pillow==7.1.1
    do_install_python_requests retrying==1.3.4
    do_install_python_requests xyconvert==0.1.2
    do_install_python_requests jsonpickle==2.2.0
    do_install_python_requests redis==4.3.5
    do_install_python_requests PyMySQL==1.0.2
}

# 初始化afs
function init_afs() {
    if [[ -d "$mod_dir/afs" ]];then
	    echo "afs目录已存在"
	    return
	  else
	    mkdir $mod_dir/afs
	    cd $mod_dir/afs
	    wget -q -O output.tar.gz --no-check-certificate --header "IREPO-TOKEN:9864e50c-18bb-44e8-acdf-fb64ea933823" "https://irepo.baidu-int.com/rest/prod/v3/baidu/inf/afs-api/releases/1.8.11.2155/files"
	    tar xf output.tar.gz
	  fi
}


function start_service() {
    # 初始化tools环境
    init_tools
    check_and_prepare_env

    echo -ne "starting up $mod_name ..."

    is_running
    if [[ $? -eq 0 ]];then
        warning "Process is already running"
        return 0
    fi

    RUN_CMD="nohup /home/<USER>/aoiMl/start_aoiml_web.sh "
    info "RUN_CMD: ${RUN_CMD}"

    cd ${BASE_DIR}
    ${SUPERVISE} -f "${RUN_CMD}" -p "${STATUS_DIR}" -F "${SUPERVISE_CONF}" &> /dev/null </dev/null
    cd -

    sleep 5

    is_running
    if [[ $? -eq 1 ]];then
        warning "Start failed!"
        return 1
    fi

    return 0
    
}

 # info 日志
function info() {
    echo "INFO: $@"
}

# warning 日志
function warning() {
    echo "WARNING: $@"
}


# 执行进程是否处于运行状态
# 返回 0 进程表示运行中，返回 1 表示运行结束
function is_running() {
    pid=$(get_pid)
    if [[ -z "${pid}" ]];then
        warning "does not find ${START_CMD}, process is not running..."
        return 1
    fi
    info "pid is ${pid}"

    sleep 5

    if [[ -d /proc/${pid} ]];then
        warning "/proc/${pid} exist, process is running..."
        return 0
    else
        warning "/proc/${pid} doesn't exist, process is not running..."
        return 1
    fi
}

# 检查文件和目录是否存在以类型
function check_existence_and_type() {
    local path=$1
    local type=$2

    if ([[ ${type} == "file" ]] && [[ -f ${path} ]]) ||
        ([[ ${type} == "dir" ]] && [[ -d ${path} ]]);then
        info "${path} is a ${type}"
        return 0
    else
        info "${path} is not a ${type}. Unexpected!"
        return 1
    fi
}

# 检查环境，主要是检查supervise的文件
function check_and_prepare_env() {
    # SUPERVISE_CONF is not necessary, leave it alone
    check_existence_and_type ${SUPERVISE} "file"
    if [[ $? -eq 1 ]];then
        exit 1
    fi

    check_existence_and_type ${STATUS_DIR} "dir"
    if [[ $? -eq 0 ]];then
        mkdir -p ${STATUS_DIR}
        if [[ $? -ne 0 ]];then
            warning "mkdir -p ${STATUS_DIR} failed!!"
            exit 1
        fi
    fi
}


# 获取父进程的进程id，有supervise的情况下父进程就是supervise进程
function get_ppid() {
    local ppid=`ps aux | grep supervise | grep "${START_CMD}" | grep -v grep | awk '{print $2}'`
    echo ${ppid}
}

# 获取执行进程的进程id，即supervise监控的进程
function get_pid() {
    local pid=`ps aux | grep "${START_CMD}" | grep -v supervise | grep -v grep | awk '{print $2}'`
    echo ${pid}
}

# 停止
function stop() {
    #is_running
    #if [[ $? -eq 1 ]];then
    #    warning "Process is not running yet!"
    #    return 0
    #fi

    local supervise_pid=$(get_ppid)
    if [[ -n "${supervise_pid}" ]];then
        info "start killing supervise: kill -n 9 ${supervise_pid}..."
        kill -n 9 ${supervise_pid}
        local ret_val=$?
        while ([[ ${ret_val} -ne 0 ]])
        do
            supervise_pid=$(get_ppid)
            if [[ -z "${supervise_pid}" ]];then
                break
            fi
            info "continue killing supervise: kill -n 9 ${supervise_pid}..."
            kill -n 9 ${supervise_pid}
            ret_val=$?
        done

        sleep 1
        while ([[ -n "${supervise_pid}" ]])
        do
            supervise_pid=$(get_ppid)
            info "waiting for supervise to exit..."
            sleep 1
        done

        info "killing supervise success"
    else
        info "does not find supervise, supervise is not running"
    fi

    # 杀掉已经执行python的进程
    ps -ef | grep 'aoiMl/flask_web.py' | awk '{print $2}' | grep -v grep | xargs -I {} kill -9  {}
    local target_pid=$(get_pid)
    if [[ -z "${target_pid}" ]];then
        info "Stop success: does not find ${START_CMD}, process is not running"
        return 0
    fi

    info "start exit gracefully: kill -n 15 ${target_pid}..."
    kill -n 15 ${target_pid}
    waited=0
    is_running
    while ([[ $? -eq 0 ]] && [[ ${waited} -le ${SIGTERM_LIMIT} ]])
    do
        sleep 5
        waited=$(expr ${waited} + 5)
        info "continue exit gracefully:kill -n 15 ${target_pid}, has waited for ${waited} seconds..."
        kill -n 15 ${target_pid}
        is_running
    done

    is_running
    if ([[ $? -eq 0 ]] && [[ ${waited} -gt ${SIGTERM_LIMIT} ]]);then
        warning "waited for ${waited} seconds, time out, going to kill the process..."

        info "start killing process: kill -n 9 ${target_pid}..."
        kill -n 9 ${target_pid}

        waited=0
        is_running
        while ([[ $? -eq 0 ]] && [[ ${waited} -le ${SIGKILL_LIMIT} ]])
        do
            sleep 5
            waited=$(expr ${waited} + 5)
            info "continue killing process:kill -n 9 ${target_pid}, has waited for ${waited} seconds..."
            kill -n 9 ${target_pid}
            is_running
        done
    fi

    is_running
    if [[ $? -eq 0 ]];then
        warning "Stop failed: target_pid=${target_pid}"
        return 1
    else
        info "Stop success: target_pid=${target_pid}"
        return 0
    fi
}


function restart() {
    stop
    if [[ $? -ne 0 ]];then
        warning "Call stop failed in restart!"
        return 1
    fi
    start_service
    if [[ $? -ne 0 ]];then
        warning "Call start failed in restart!"
        return 1
    fi
    return 0
}

# 运行状态
# 0 -> running, 1 -> starting, 2 -> stopped
function status() {
    is_running
    ret_val=$?
    if [[ ${ret_val} -eq 0 ]];then
        supervise_pid=$(get_ppid)
        target_pid=$(get_pid)
        info "Supervise process id: ${supervise_pid}, target process id: ${target_pid}"
        return 0
    else
        info "Process or supervise is not running!"
        return 2
    fi
    return 2
}

function usage() {
    echo "usage:"
    echo "cd $mod_dir && $0 {start|stop|restart|status}"
}

if [[ "$#" -ne "1" ]]; then
    usage
    exit 1
fi

ACTION=$1
case "${ACTION}" in
    start)
        start_service
    ;;
    stop)
        stop
    ;;
    restart)
        restart
    ;;
    status)
        status
    ;;
    *)
        usage
    ;;
esac