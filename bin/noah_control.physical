#! /bin/bash
## 这个脚本适配的是xxl-job 的客户端
script_dir=$(cd $(dirname $0) && pwd)
mod_dir=$(dirname $script_dir)
afs_dir=/home/<USER>/afs
#py_39_home=/home/<USER>/conda_py39/
py_39_script=/home/<USER>/bin/py39

# 获取当前脚本的路径，保存到FILE
if [[ -L "$0" ]];then
    FILE=$(readlink -f "$0")
else
    FILE=$0
fi
BASE_DIR=$(cd $(dirname ${FILE}); pwd)/..
echo "base_dir $BASE_DIR"

XXL_JOB_DIR=/home/<USER>/xxl-job
XXL_JOB_APP_NAME=xxl-job-executor-physical-aoi-ml
############################## 基础数据包准备 ###############################
# 初始化tools环境
function init_tools(){

#   物理机仅安装依赖
#    init_python_proxy
    init_afs
#    copy_python_39
#    init_py39_script
    # 安装依赖
    install_package
#    export LC_ALL=en_US.UTF-8
}

function init_python_proxy() {
    mkdir -p /home/<USER>/.pip/
    cat > /home/<USER>/.pip/pip.conf << EOF
[global]
timeout = 60
index = https://pip.baidu-int.com/search/
index-url = https://pip.baidu-int.com/simple/
trusted-host = pip.baidu-int.com
[list]
format = columns
EOF
    echo "set python proxy success"
}

# 初始化afs
function init_afs() {
    if [[ -e "$afs_dir/bin/afsshell" ]];then
	    echo "afs目录已存在"
	    return
	  else
	    wget -q -O output.tar.gz --no-check-certificate --header "IREPO-TOKEN:9864e50c-18bb-44e8-acdf-fb64ea933823" "https://irepo.baidu-int.com/rest/prod/v3/baidu/inf/afs-api/releases/1.8.11.2155/files"
	    tar xf output.tar.gz
	    mkdir -p $afs_dir
	    mv output/* $afs_dir/
	  fi
	  if [[ ! -d "$afs_dir/bin" ]];then
	    echo 'install afs_tool failed'
	    exit 100
	  fi
}


function install_package() {
    $py_39_script -m pip install opencv-contrib-python==******** opencv-python==******** redis==4.0.2 coord_convert==0.2.1 toml==0.10.2 numba==0.59.1 pandas==1.5.3 geopandas==0.13.2 openpyxl==3.0.10 loguru==0.7.3
    $py_39_script -m pip install scikit-learn==1.5.2 mapio==1.8.4 dacite==1.9.2  rdp==0.8
    # $py_39_home/py39 -m pip install apache-airflow==2.8.1 apache-airflow-providers-celery==3.5.1
}

########################## 基础任务包准备 end ###############################

function start_service() {
   echo 'invalid'
    # 初始化tools环境
#    init_tools
#    ### 启动airflow
#    mkdir -p $AIRFLOW_WORK_DIR
#    # 同步
#    rsync -avz "$BASE_DIR/airflow/" $AIRFLOW_WORK_DIR/
#    is_running
#    if [[ $? -eq 0 ]];then
#      info "Process is running!"
#      return 0
#    fi
#    nohup $py_39_home/py39 $py_39_home/bin/airflow celery worker -q $AIRFLOW_QUEUE >>$AIRFLOW_WORK_DIR/log_worker_celery.txt 2>&1 &
#    info "starting up $mod_name ..."
#    sleep 3
#    return 0
}

function install_xxl_job() {
    # install deck
    if [[ -e "/home/<USER>/.deck/etc/bashrc" ]]; then
        echo "deck installed"
    else
          bash -c "$( curl -s -L -k https://bcloud-baseenv-bj.bj.bcebos.com/BaseEnv/etc/install_deck.sh)"
    fi
    source /home/<USER>/.deck/etc/bashrc
    # install java, maven
    deck install oraclejdk-11.0.7 maven-3.8.6
    source /home/<USER>/.deck/etc/bashrc
    mkdir -p ${XXL_JOB_DIR}
    if [[ -e "${XXL_JOB_DIR}/xxl_job_opera/xxl-job-2.3.1/pom.xml" ]]; then
      echo "xxl-job 文件已经下载"
    else
        afs_cmd="$afs_dir/bin/afsshell --username=map-data-streeview --password=map-data-streeview get afs://aries.afs.baidu.com:9902/user/map-data-streeview/aoi-ml/tools/xxl_job_opera.tgz ${XXL_JOB_DIR}/"
        echo "$afs_cmd"
        eval "$afs_cmd"
        cd ${XXL_JOB_DIR}
        tar -xf "${XXL_JOB_DIR}/xxl_job_opera.tgz"
    fi
    # 切换PATH
    export PATH=/home/<USER>/.deck/1.0/common/1.0/bin:/home/<USER>/.deck/1.0/oraclejdk/11.0.7/bin:/home/<USER>/.deck/1.0/maven/3.8.6/bin:$PATH
    # 替换maven配置
    cp -f ${script_dir}/settings.xml /home/<USER>/.deck/1.0/maven/3.8.6/conf
    # 替换APP_NAME 配置
    cd ${XXL_JOB_DIR}/xxl_job_opera/xxl-job-2.3.1/xxl-job-executor-samples/xxl-job-executor-sample-springboot/src/main/resources
    # 设置一些配置文件
    sed -i "s/__APP_NAME__/${XXL_JOB_APP_NAME}/g" application.properties
    sed -i "s/server.port=8099/server.port=19788/g" application.properties
    # sed -i "s/xxl.job.executor.port=9999/xxl.job.executor.port=19789/g" application.properties
    cd ${XXL_JOB_DIR}/xxl_job_opera/xxl-job-2.3.1/
    mvn clean package
}

function  start_service_xxljob() {
        init_tools
        # 启动xxl-job
        is_running
        if [[ $? -eq 0 ]];then
             info "Process is running!"
             return 0
        fi
        # 重新编译xxl-job
        install_xxl_job
        export PATH=/home/<USER>/.deck/1.0/common/1.0/bin:/home/<USER>/.deck/1.0/oraclejdk/11.0.7/bin:/home/<USER>/.deck/1.0/maven/3.8.6/bin:$PATH
        java -version
        nohup java -jar -Xmx2g ${XXL_JOB_DIR}/xxl_job_opera/xxl-job-2.3.1/xxl-job-executor-samples/xxl-job-executor-sample-springboot/target/xxl-job-executor-sample-springboot-2.3.1.jar >>${XXL_JOB_DIR}/worker.log 2>&1 &
        sleep 3
}

 # info 日志
function info() {
    echo "INFO: $@"
}

# warning 日志
function warning() {
    echo "WARNING: $@"
}


# 执行进程是否处于运行状态
# 返回 0 进程表示运行中，返回 2 表示运行结束
function is_running() {
    lines=`ps -ef | grep xxl-job-executor-sample-springboot-2.3.1.jar  | grep -v grep|wc -l`
    if [[ lines -gt 0 ]];then
      info "running"
      return 0
    else
      info "not running"
      return 2
    fi
}

# 停止
function stop() {

   return 0
}


function restart() {
    is_running
    if [[ $? -eq 0 ]];then
      info "Process is running!"
      return 0
    fi
    start_service_xxljob
    sleep 1
    return 0
}

# 运行状态
# 0 -> running, 1 -> starting, 2 -> stopped
function status() {
    is_running
    if [[ $? -eq 0 ]];then
      info "Process is running!"
    else
      info 'process stopped!'
      # 尝试启动
      start_service_xxljob
      return 2
    fi
    return 0
}

function usage() {
    echo "usage:"
    echo "cd $mod_dir && $0 {start|stop|restart|status}"
}

if [[ "$#" -ne "1" ]]; then
    usage
    exit 1
fi

ACTION=$1
case "${ACTION}" in
    start)
        start_service_xxljob
    ;;
    stop)
        stop
    ;;
    restart)
        restart
    ;;
    status)
        status
    ;;
    *)
        usage
    ;;
esac