run_mode: prod

pg:
  poi_online:
    host: gzbh-ns-map-de16.gzbh.baidu.com
    db: poi_online
    port: 8532
    user: poi_aoi_rw
    pwd: poi_aoi_rw

  master_back:
    host: *************
    db: master_back
    port: 8033
    user: master_back
    pwd: master_back

  road:
    host: ************
    db: muku_pre
    port: 8432
    user: dbuser
    pwd: dbuser

  order:
    host: **************
    db: business_order
    port: 7432
    user: business_order_se_rw
    pwd: kpigzear

redis:
  default:
    host:
    port:
    auth:
    bns: group.bdrp-BeePlatform-proxy.redis.all
  aoi:
    host: ************
    port: 8379
    auth:
    bns: group.bdrp-map-aoi-prod-proxy.redis.all
road_node: /muku/road/master_road_ml
nuts_api: http://mapde-poi.baidu-int.com/prod/api/rcmanage/naming
beeflow_road_db_query: http://mapde-poi.baidu-int.com/prod/api/rcmanage/naming

mysql:
  default:
    host: *************
    port: 5730
    user: aoi_ml_rw
    pwd: 2x@,@@++5,8Kx
    db: aoi_ml
  beeflow:
    host: *************
    port: 5730
    user: bee_flow_r
    pwd: bee_flow_r_2021
    db: bee_flow
  beeflow_rw:
    host: *************
    port: 5730
    user: bee_flow
    pwd: nl4c/mqeTcsgpH
    db: bee_flow
  beeflow_test:
    host: *************
    port: 5730
    user: bee_flow_pre_rw
    pwd: bee_flow_pre_rw_2021
    db: bee_flow_pre
  aoi_release_data:
    host: *************
    port: 5730
    user: bee_flow_r
    pwd: bee_flow_r_2021
    db: aoi_release_data
  charging_station:
    host: chargingstation0000-offline.xdb.all.serv
    port: 8102
    user: poi_read
    pwd: kRzbvc!c4!cXBkmp
    db: charge_station_online
  poi_platform:
    host: *************
    port: 6242
    user: platform_r
    pwd: 9iF_4cLuL
    db: poi_platform

afs:
  fenghuang:
    shell: /home/<USER>/aoiMl/afs/output/bin/afsshell
    cluster: fenghuang
    username: map_data_aoi
    password: DKGjrtAiXi2r
    host: fenghuang.afs.baidu.com:9902
  aries:
    shell: /home/<USER>/aoiMl/afs/output/bin/afsshell
    cluster: aries
    username: map-data-streeview
    password: map-data-streeview
    host: aries.afs.baidu.com:9902
  aries_rpm01:
    shell: /home/<USER>/afs/bin/afsshell
    cluster: aries
    username: map-data-streeview
    password: map-data-streeview
    host: aries.afs.baidu.com:9902
  baihua:
    shell: /home/<USER>/afs/bin/afsshell
    cluster: baihua
    username: lbspoi_featuredb
    password: featuredb4operator
    host: baihua.afs:9902
  kunpeng:
    shell: /home/<USER>/aoiMl/afs/output/bin/afsshell
    cluster: kunpeng
    username: map-luban-topo
    password: map-luban-topo2023
    host: kunpeng.afs:9902
  track:
    shell: /home/<USER>/chenbaojun/scripts/afs/bin/afsshell
    cluster: kunpeng
    username: map-dataop
    password: map-dataop
    host: kunpeng.afs:9902
  charging_station:
    shell: /home/<USER>/aoiMl/afs/output/bin/afsshell
    cluster: kunpeng
    username: lbs-poi
    password: lbs-poi
    host: kunpeng.afs.baidu.com:9902
  poi_yingxiang:
    shell: /home/<USER>/chenbaojun/scripts/afs/bin/afsshell
    cluster: kunpeng
    username: poi-yingxiang
    password: map12345
    host: kunpeng.afs.baidu.com:9902
  map_aoi_fenghuang:
    shell: /home/<USER>/chenbaojun/scripts/afs/bin/afsshell
    cluster: kunpeng
    username: map_aoi_fenghuang
    password: map_aoi_fenghuang
    host: fenghuang.afs.baidu.com:9902

tmp_dir: /tmp

# deoss配置
deoss:
  ak: 100025
  sk: 81a8a6yma1d19ac02a1a2e31m8017d1
  url: http://deoss.map.baidu-int.com
  bucket: bucket_waiye_aoi_data

# 商单配置
business_order:
  delivery_db:
    url: "http://inner.map.baidu.com:8000/rcmanage/dbmanage"
    domain: "work_expimp"
    create_action: "createdb"
    keep_days: 1
  delivery_ftp:
    host: "storage.guoke.baidu.com"
    port: 8100
    user: "map_data_poi_product"
    pwd: "map_data_poi_product_123456"
    upload_dir: "business_order_auto"
