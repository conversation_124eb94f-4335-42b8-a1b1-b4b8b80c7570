run_mode: debug

pg:
  poi_online:
    host: ************
    db: poi_online
    port: 8432
    user: dbuser
    pwd: dbuser

  master_back:
    host: ************
    db: backref_test
    port: 8432
    user: dbuser
    pwd: dbuser

  road:
    host: ************
    db: muku_pre
    port: 8432
    user: dbuser
    pwd: dbuser

  order:
    host: **************
    db: business_order
    port: 7432
    user: business_order_se_rw
    pwd: kpigzear

redis:
  default:
    host: ************
    port: 6379
    auth: lutao!map2021
    bns:
mysql:
  default:
    host: ************
    port: 3306
    user: root
    pwd: root
    db: aoi_ml

afs:
  fenghuang:
    shell: /home/<USER>/aoiMl/afs/output/bin/afsshell
    cluster: fenghuang
    username: map_data_aoi
    password: DKGjrtAiXi2r
    host: fenghuang.afs.baidu.com:9902
  aries:
    shell: /root/afs/output/bin/afsshell
    cluster: aries
    username: map-data-streeview
    password: map-data-streeview
    host: aries.afs.baidu.com:9902

road_node: /muku/road/master_road_ml
nuts_api: http://*************:8000/rcmanage/naming

deoss:
  ak: 999999
  sk: 0a680a1b0352681ed191ae2a6a17d143
  url: http://************:8092
  bucket: bucket_debug

# 商单配置
business_order:
  delivery_db:
    url: "http://inner.map.baidu.com:8000/rcmanage/dbmanage"
    domain: "work_expimp"
    create_action: "createdb"
    keep_days: 1
  delivery_ftp:
    host: "storage.guoke.baidu.com"
    port: 8100
    user: "map_data_poi_product"
    pwd: "map_data_poi_product_123456"
    upload_dir: "business_order_auto"
