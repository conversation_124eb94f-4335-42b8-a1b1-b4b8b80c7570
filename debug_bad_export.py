#!/usr/bin/env python3
"""
调试商单导出问题的测试脚本
"""
import json

def debug_bad_export():
    """调试BAD导出问题"""

    # 模拟配置中的req数据
    req_data = {
        "query_database": "BUSINESS_ORDER",
        "query_table": [],
        "need_topo": 0,
        "export_format": "CSV",
        "condition_list": [],
        "filter_conditions": {},
        "order_info": {
            "order_project_name": "jiangsu_quhua",
            "order_amount": "",
            "order_output_name": "jiangsu_quhua",
            "order_need_safe": "否",
            "order_export_coord": "GCJ02",
            "order_export_encode": "GBK",
            "order_export_format": "TAB",
            "order_condition_list": [{
                "BAD_EXPORT_RANGE_TYPE": "BAD_GEOM_RANGE",
                "BAD_LEVEL": "[\"4\",\"5\"]",
                "DELIVERY_SPEC": "COMMON",
                "administrative_region": "[[\"************\"]]",
                "delivery_data_range": "BAD",
                "export_condition_type": "行政区划",
                "export_filter_criteria": "FROM_B"
            }],
            "order_filter_conditions": [{
                "bad_level": ["4", "5"],
                "cities": [],
                "counties": [],
                "data_src": ["FROM_B"],
                "delivery_spec": ["COMMON"],
                "provinces": ["************"],
                "query_table": ["BAD"],
                "towns": [],
                "villages": []
            }]
        }
    }

    print("=== 调试BAD导出问题 ===")
    print(f"配置的BAD_LEVEL: {req_data['order_info']['order_condition_list'][0]['BAD_LEVEL']}")
    print(f"配置的数据源: {req_data['order_info']['order_filter_conditions'][0]['data_src']}")

    # 分析数据源
    data_src = req_data['order_info']['order_filter_conditions'][0]['data_src'][0]
    bad_level = req_data['order_info']['order_filter_conditions'][0]['bad_level']

    print(f"\n数据源: {data_src}")
    print(f"导出级别: {bad_level}")

    # 根据数据源确定底层级别
    if data_src == "FROM_B":
        source_level = 'village'
        source_table = 'bad_admin_vil_gcj'
        sd_table = 'sd_bad_village'
    elif data_src == "FROM_C":
        source_level = 'town'
        source_table = 'bad_admin_town_mzb'
        sd_table = 'sd_bad_town'
    else:
        print(f"不支持的数据源: {data_src}")
        return

    print(f"\n数据源级别: {source_level}")
    print(f"源表: {source_table}")
    print(f"商单表: {sd_table}")

    # 分析filter_data逻辑
    print(f"\n=== filter_data 分析 ===")
    print(f"bottom_division = {source_level}")

    if source_level == 'village':
        print("会执行village分支:")
        print("- 从bad_admin_vil_gcj表查询数据")
        print("- 写入sd_bad_village表")

    if source_level == 'town':
        print("会执行town分支:")
        print("- 从bad_admin_town_mzb表查询数据")
        print("- 写入sd_bad_town表")

    # 分析quality_deal逻辑
    print(f"\n=== quality_deal 分析 ===")
    # 模拟get_higher_division逻辑
    division_levels = ['village', 'town', 'county', 'city', 'pro']
    source_index = division_levels.index(source_level)
    wait_union_divisions = division_levels[source_index + 1:]

    print(f"需要聚合的上级行政区划: {wait_union_divisions}")

    if 'town' in wait_union_divisions:
        print("会执行sync_town: 从sd_bad_village聚合生成sd_bad_town")

    # 分析export_data逻辑
    print(f"\n=== export_data 分析 ===")
    print(f"bad_level: {bad_level}")

    if '5' in bad_level:
        print("会导出village级别数据:")
        print("- 从sd_bad_village表导出")
        print("- SQL: select guid as face_id,village_code as admin_id,village_name as name_ch,{geom} from sd_bad_village")

    if '4' in bad_level:
        print("会导出town级别数据:")
        print("- 从sd_bad_town表导出")
        print("- SQL: select guid as face_id,town_code as admin_id,town_name as name_ch,{geom} from sd_bad_town")

    # 问题分析
    print(f"\n=== 问题分析 ===")
    print("根据配置分析:")
    print("1. 数据源是FROM_B，对应village级别")
    print("2. 只会从bad_admin_vil_gcj表查询数据写入sd_bad_village")
    print("3. 在quality_deal阶段会从sd_bad_village聚合生成sd_bad_town")
    print("4. 导出时:")
    print("   - village级别(5)从sd_bad_village导出")
    print("   - town级别(4)从sd_bad_town导出(聚合生成的)")

    print("\n可能的问题:")
    print("1. bad_admin_vil_gcj表中江苏省(************)的village数据是否完整?")
    print("2. 聚合过程中是否有数据丢失?")
    print("3. 导出SQL是否正确?")
    print("4. 筛选条件是否正确匹配到数据?")

    print("\n建议检查步骤:")
    print("1. 检查bad_admin_vil_gcj表中江苏省的数据量")
    print("2. 检查sd_bad_village表中的数据量")
    print("3. 检查sync_town聚合后sd_bad_town表中的数据量")
    print("4. 检查最终导出的数据量")

    return req_data

if __name__ == "__main__":
    debug_bad_export()
