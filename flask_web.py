# !/usr/bin/env python3
"""
添加web脚本，暂时只给出check_health脚本, 也可以增加其他脚本
"""
import os.path
import sys

current_directory = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_directory)

from flask import Flask
from flask_apscheduler import APScheduler
from conf import Config
from src.api import register_api
from src.job import register_scheduler

app = Flask(__name__)
scheduler = APScheduler()

# 注册任务
register_scheduler(scheduler)
# 注册api
register_api(app)
# 设置config
app.config.from_object(Config())

scheduler.init_app(app)
scheduler.start()

if __name__ == '__main__':
    app.run("0.0.0.0", 8080)
