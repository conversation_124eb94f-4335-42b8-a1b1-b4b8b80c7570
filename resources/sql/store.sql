---- 配置存储 ----
create table  if not exists aoi_ml_store (
    id bigint auto_increment not null comment 'id',
    k varchar(100) not null default '' comment 'key',
    v text comment 'value',
    remark varchar(255) not null default  '' comment 'remark',
    expire_dateline bigint not null default 0 comment '过期时间',
    dateline bigint not null default 0 comment '存储时间',
    primary key(id),
    unique (`k`)
    key(`expire_dateline`)
) engine=innodb  default charset=utf8mb4 comment 'kv信息存储';