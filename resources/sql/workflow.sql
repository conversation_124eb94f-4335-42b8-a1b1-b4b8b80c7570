--- 任务流转相关记录表 ----
CREATE TABLE `aoi_ml_task_record` (
      `task_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
      `task_parent_id` int(10) unsigned NOT NULL DEFAULT '0',
      `task_name` varchar(100) NOT NULL DEFAULT '',
      `task_type` varchar(100) NOT NULL DEFAULT '' COMMENT '任务类型',
      `task_priority` tinyint(4) NOT NULL DEFAULT '0' COMMENT '优先级',
      `task_content` text COMMENT '任务参数与任务配置',
      `task_create_time` bigint(20) unsigned NOT NULL DEFAULT '0',
      `task_last_modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      `task_status` int(10) unsigned DEFAULT '0',
      `task_current_flow` varchar(50) NOT NULL DEFAULT '' COMMENT '当前子流程',
      `task_error_type` int(10) unsigned DEFAULT '0',
      `task_error_desc` varchar(255) NOT NULL DEFAULT '',
      PRIMARY KEY (`task_id`),
      KEY `index_taskstatus` (`task_status`),
      KEY `index_task_current_flow` (`task_current_flow`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务记录表';

--- task_data ---
CREATE TABLE if not exists `aoi_ml_task_data` (
     `id` bigint unsigned NOT NULL AUTO_INCREMENT,
     `task_id` bigint unsigned not null DEFAULT 0,
     `taskd_key` varchar(64) DEFAULT '',
     `taskd_type` int(10) DEFAULT 0,
     `taskd_content` text,
     PRIMARY KEY (`id`),
     UNIQUE KEY `index_task_data_taskid_taskdkey` (`task_id`,`taskd_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='任务附件';

--- work_package_flow ---
CREATE TABLE `aoi_ml_work_package_flow` (
    `wpf_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `wpf_father_id` int(10) unsigned NOT NULL DEFAULT '0',
    `wpf_task_id` int(10) NOT NULL DEFAULT '0',
    `wpf_section_name` varchar(128) NOT NULL DEFAULT '' COMMENT '环节名称',
    `wpf_nextsection` varchar(128) DEFAULT '' COMMENT '下一环节名称',
    `wpf_status` int(10) NOT NULL DEFAULT '0' COMMENT '任务附件',
    `wpf_flow_info` text COMMENT '任务参数',
    `wpf_memo_info` text COMMENT '任务执行过程说明',
    `wpf_last_modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `wpf_belong` varchar(64) DEFAULT '' COMMENT '属于哪个环节',
    `wpf_work_mechine` varchar(255) NOT NULL DEFAULT '' COMMENT '执行机器',
    `wpf_work_path` varchar(255) NOT NULL DEFAULT '' COMMENT '执行目录',
    `wpf_fail_reason` text COMMENT '任务失败原因',
    PRIMARY KEY (`wpf_id`),
    KEY `index_wpf_task_id` (`wpf_task_id`),
    KEY `index_work_package_flow_status` (`wpf_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='子任务流转表'

--- work_package_flow_data ---
CREATE TABLE if not exists `aoi_ml_work_package_flow_data` (
      `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
      `wpf_id` int(10) unsigned DEFAULT NULL,
      `wpfd_key` varchar(128) NOT NULL,
      `wpfd_type` int(10) NOT NULL,
      `wpfd_content` longtext,
      PRIMARY KEY (`id`),
      UNIQUE KEY `index_work_package_flow_data_wpfid_wpfdkey` (`wpf_id`,`wpfd_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='子任务附件';
