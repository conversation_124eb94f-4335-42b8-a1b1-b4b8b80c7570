# !/usr/bin/env python3
from typing import List, Dict

from src.model.aoi_model import AoiModel


class FeatureContext:
    """
    要素读取流程上下文
    """

    def __init__(self):
        # 内部路组
        self.inner_road_group: str = ""
        self.inner_road_group_wkt: str = ""
        self.inner_road_group_detail: Dict = {}
        # 内部路组检测结果
        self.inner_road_detect_info: List = []
        # 最大交并比区域
        self.max_iou_detect: Dict = {}
        # 最大竞争品数据
        self.intelligence_info: Dict = {}
        # 是否已经存在
        self.aoi_exists = False
        # 所在街区
        self.street_wkt: str = ""
        # 所在街区的faceid
        self.street_faceid: str = ""
        self.bind_poi_info: Dict = {}
        self.is_seg = False
        self.tags = []
        self.may_pois = []


class ConfContText:
    """
    参数配置项
    """

    def __init__(self, aoi_model=None):
        # aoi查询相关参数
        self.aoi_model: AoiModel = aoi_model
        # 修形时是否处理聚合院落， False 不处理，True，处理聚合院落
        self.handle_group_street = False
        # 检测结果最小项
        self.detect_iou_rate_min: float = 0.2

        # 检测得分比例
        self.detect_score_rate: int = 20
        self.intelligence_score_rate: int = 20
        # 竞平批次
        self.intelligence_batch: str = ""
        # 竞品iou值
        self.intelligence_iou_min: float = 0.2
        # 日志记录
        self.log_dir: str = ""
        self.log_file: str = ""
        # 是否过滤在线的aoi
        self.filter_online_aoi: bool = True


class StrategyScores:
    """
    策略评分上下文
    """

    def __init__(self):
        # 修型结果
        self.fixed_geom_result: Dict = {}
        # 当前修型的数据
        self.current_fixed_geom: str = ""
        # 得分详细
        self.score_dict: Dict = {}
        # 总分
        self.all_score: float = 0.0
        # 修型结果
        self.fixed_result: str = ""
        self.memo: str = ""
        self.all_dict = {}


class StrategyContext:
    """
    策略分析流程上下文
    """

    def __init__(self):
        self.feature_ctx = FeatureContext()
        self.scores = StrategyScores()
        self.conf = ConfContText()
