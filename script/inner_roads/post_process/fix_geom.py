# !/usr/bin/env python3
"""
处理矢量化数据
"""
import datetime

from shapely import wkt, geometry
from shapely.validation import make_valid

from src.tools.aoi_tools import chaikins_corner_cutting
from script.inner_roads.post_process.context import StrategyContext
from src.model.aoi_model import AoiModel
from osgeo import ogr


def fix_area(ctx: StrategyContext, proceed):
    """ 策略修形
    :return:
    """
    print("start fix area")
    q = ctx.conf.aoi_model
    image_hash = ctx.feature_ctx.street_faceid
    wkt_str = ctx.scores.current_fixed_geom
    if wkt_str == "":
        print("无修型区域")
        return proceed()
    shape = wkt.loads(wkt_str)
    if image_hash == "":
        print("没有街区，以当前区域buffer300米作为街区处理")
        street_wkt = wkt.loads(ctx.scores.current_fixed_geom).buffer(0.003, join_style=1).wkt
    else:
        street_wkt = q.get_street_by_file(image_hash)
        if street_wkt == "":
            print("没有街区222，以当前区域buffer600米作为街区处理")
            street_wkt = wkt.loads(ctx.scores.current_fixed_geom).buffer(0.006, join_style=1).wkt
        else:
            street_wkt = make_valid(wkt.loads(street_wkt)).buffer(-0.00005).simplify(0.00004,
                                                                                     preserve_topology=False).wkt
            # 如果街区面积太大，影响速度，做一个缩小版的区域
            street_shape = wkt.loads(street_wkt)
            if street_shape.area / shape.area > 5:
                print("街区面积比待确定面积大5倍以上，做街区缩小")
                # 街区扩大10米与当前区域做相交
                street_wkt = shape.buffer(0.006, join_style=1).intersection(street_shape.buffer(0.0001)).wkt
            else:
                street_wkt = street_shape.union(shape.buffer(0.0001)).wkt

    write_log(ctx, "街区：{}".format(street_wkt))
    # 带选定区域，外扩5米
    smooth_polygon = shape
    # shape_item = geometry.shape(shape)
    # 抽稀，移除锯齿
    # shape_item = shape_item.simplify(0.00003, preserve_topology=True)
    # print("shape:" + shape_item.wkt)
    # smooth_polygon = geometry.Polygon(smooth_res)
    # smooth_polygon = geometry.Polygon(shape_item)
    # smooth_polygon = shape
    # print("smooth_polygon:" + smooth_polygon.wkt)
    # 外扩10米
    shape = smooth_polygon
    write_log(ctx, "外扩:" + shape.simplify(0.00003).wkt)
    #  按照街区,水系,其他aoi 切一下
    # print("street_wkt:", street_wkt)
    # print("cut_by_street")
    # shape = cut_by_street(street_wkt, shape, -0.00007, ctx)
    # shape = make_valid(shape)
    # write_log(ctx, "分隔街区:" + shape.simplify(0.00003).wkt)
    # print("cut_by_water")
    shape = cut_by_water(street_wkt, shape, q, ctx)
    # 切完再抽个稀
    shape = shape.simplify(0.00002, preserve_topology=False)
    write_log(ctx, "分隔水系:" + shape.simplify(0.00003).wkt)
    # 根据其他的aoi
    # 处理内部路
    print("process_by_inner_road")
    shape = process_by_inner_road(shape, street_wkt, q, ctx)
    write_log(ctx, "内部路：" + shape.simplify(0.00003).wkt)
    # 处理楼块
    print("process_by_bud")
    shape = process_by_bud(shape, q, ctx)
    write_log(ctx, "楼块：" + shape.simplify(0.00003).wkt)
    print("process_by_out_road")
    shape = cut_by_other_aoi(street_wkt, shape, q, ctx)
    write_log(ctx, "分隔其他aoi:" + shape.simplify(0.00003).wkt)
    shape = process_by_out_road(shape, q, ctx)
    write_log(ctx, "外部路：" + shape.simplify(0.00003).wkt)
    print("cut_by_street")

    shape = shape.buffer(-0.00005).simplify(0.00001, preserve_topology=False)
    shape = shape.buffer(+0.00005).simplify(0.00001, preserve_topology=False)
    shape = multipolygon_to_single(shape, 0, ctx=ctx)

    # 处理完buffer一下， 使边角圆滑一些, 过滤一些很细的范围

    # shape = shape.buffer(0.00004)
    # shape = shape.buffer(-0.00009)
    # shape = multipolygon_to_single(shape, 0, ctx=ctx)
    # shape = shape.buffer(0.00002).simplify(0.00002, preserve_topology=False)

    print(shape.wkt)
    ctx.scores.fixed_result = "" if shape.wkt == "POLYGON EMPTY" else shape.wkt
    write_log(ctx, "最终：" + shape.wkt)
    proceed()


def fix_with_aoi_group(group_aoi_wkt, street_wkt, ctx: StrategyContext):
    """ 通过聚合院落修形
    :param group_aoi_wkt:
    :param street_wkt:
    :return:
    """
    shape = wkt.loads(group_aoi_wkt)
    shape = shape.buffer(-0.00001)  # 依据聚合院落收缩1米， 不能完全一样
    shape = cut_by_street(street_wkt, shape, -0.00007, ctx)
    shape = multipolygon_to_single(shape, 0, ctx=ctx)
    return shape


def cut_by_water(street_wkt, shape, q: AoiModel, ctx: StrategyContext):
    """ 通过水系做切割
    :param street_wkt:
    :param shape:
    :param q
    :return:
    """
    water_list = q.get_intersection_water_by_geom(street_wkt)
    for water_wkt in water_list:
        water_shape = wkt.loads(water_wkt).buffer(0.00002)
        if shape.intersects(water_shape):
            shape = shape.difference(water_shape)
    shape = multipolygon_to_single(shape, 0, ctx=ctx)
    return shape


def cut_by_street(street_wkt, shape, buffer_val, ctx: StrategyContext):
    """ 通过街区做切割
    :param street_wkt:
    :param shape:
    :param buffer_val:
    :return:
    """
    street = wkt.loads(street_wkt)
    street_buffer = street.buffer(buffer_val)  # 道路是线，需要buffer
    if shape.intersects(street_buffer):
        shape = shape.intersection(street_buffer)
    return shape


def cut_by_other_aoi(street_wkt, shape, q: AoiModel, ctx: StrategyContext):
    """ 通过其他aoi做切割
    :param street_wkt:
    :param shape:
    :param q
    :return:
    """

    geom_list = q.get_intersection_list_by_geom(street_wkt, 1, ctx.conf.handle_group_street)
    for geom_item in geom_list:
        geom_shape = wkt.loads(geom_item)
        if shape.intersects(geom_shape):
            write_log(ctx, "其他aoi：" + geom_item)
            # print("cut aoi:" + geom_shape.wkt)
            shape = shape.difference(geom_shape)
    shape = multipolygon_to_single(shape, 0, ctx=ctx)
    return shape


def process_by_bud(shape, q: AoiModel, ctx: StrategyContext):
    """ 通过楼块修形
    :param shape:
    :param q
    :return:
    """
    bud_list = q.get_building_by_geom(shape.wkt)
    for bud in bud_list:
        bud_shape = wkt.loads(bud[1])
        if shape.overlaps(bud_shape):
            # print("bud相交, struct:" + bud[0], ", wkt: " + bud[1])
            intersect = shape.intersection(bud_shape)
            if intersect.area / bud_shape.area < 0.3:  # 小于80%移除楼块
                print("相交面积小于 80% 1:" + str(intersect.area) + "__2:" + str(bud_shape.area))
                # 交集小于80% 暂时可以压盖
                shape = shape.difference(bud_shape)
            elif intersect.area / bud_shape.area > 0.8:
                print("相交面积大于 80% 1:" + str(intersect.area) + "__2:" + str(bud_shape.area))
                shape = shape.union(bud_shape)
            else:  # 否则需要包括在内
                pass
    return shape


def process_by_inner_road(shape, street, q: AoiModel, ctx: StrategyContext):
    """ 通过内部路修形
    :param shape:
    :param street:
    :param q
    :return:
    """
    polygon_road = shape
    street_shape = wkt.loads(street)
    inner_road = get_inner_road(street_shape, q)
    # print("inner_road list:", inner_road)
    for line_item in inner_road:
        intersect = polygon_road.intersection(line_item)
        if intersect.length < 0.00001:
            continue
        write_log(ctx, "内部路类型：{}, {}, {}".format(intersect.length, line_item.type, intersect.length / line_item.length))
        write_log(ctx, "内部路：" + line_item.wkt)
        # 内部路buffer 0.3
        buffer_road = line_item.buffer(0.00003, 16, None, geometry.CAP_STYLE.flat)
        if buffer_road.convex_hull.area / shape.area > 2:
            print(shape.intersection(buffer_road.convex_hull).area / shape.area)
            print("内部路区域太大，暂时不处理")
        elif intersect.length / line_item.length < 0.2:  # 小于80%，丢弃
            polygon_road = polygon_road.difference(buffer_road)
        else:
            polygon_road = polygon_road.union(buffer_road)
    write_log(ctx, "内部路合并前：" + polygon_road.wkt)
    polygon_road = multipolygon_to_single(polygon_road, ctx=ctx)
    polygon_road = polygon_road.buffer(0.00004).buffer(-0.00004)
    polygon_road = multipolygon_to_single(polygon_road, ctx=ctx)
    return polygon_road


def process_by_out_road(shape, q: AoiModel, ctx: StrategyContext):
    """ 通过外部路切割
    :param shape:
    :param q
    :return:
    """
    res_shape = cut_out_road_level7(shape, q)
    # print("out_road list :", out_road)

    level8_roads = get_out_road_level8(shape.buffer(0.0002), q)
    print("==== 八级路处理 start ===")
    for level8_item in level8_roads:
        # 面去切割路，如果切割了好多碎片,留最大的
        diff1 = res_shape.difference(level8_item.buffer(0.00001))
        if diff1.type == "MultiPolygon":
            top1 = 0
            top2 = 0
            for i in diff1:
                if i.area > top1:
                    top1 = i.area
                if top2 < i.area < top1:
                    top2 = i.area
            if top2 * 3 < top1:
                # 如果最大的比第二大的三倍还大，直接切割,否则不处理
                res_shape = res_shape.difference(level8_item.buffer(0.00003))
                res_shape = multipolygon_to_single(res_shape)
                res_shape = res_shape.buffer(0.00004)
                res_shape = res_shape.buffer(-0.00004)
        # 计算交集
        # unioned_line = level8_item.intersection(shape)
        # if unioned_line.type == "LineString":
        #     pass
        # elif unioned_line.type == "MultiLineString":
        #     for i in unioned_line:
        #         print(i.wkt)
        # res_shape = res_shape.difference(unioned_line.buffer(0.0001))
    return res_shape


def get_inner_road(shape, q: AoiModel):
    """ 获取内部路
    :param shape:
    :param q
    :return:
    """
    line_list = q.get_line_by_geom(shape.wkt)
    line_dict = {}
    n = 0
    for line_wkt in line_list:
        line_shape = wkt.loads(line_wkt)
        line_dict[n] = line_shape
        n += 1
    i = 0
    res_list = []
    len_dict = len(line_dict)
    while i < len_dict:
        if i not in line_dict:
            i += 1
            continue
        shape = line_dict[i]
        del line_dict[i]
        res_shape, line_dict = merge_line_helper(shape, line_dict, 0)
        res_list.append(res_shape)
        i += 1

    return res_list


def cut_out_road_level7(shape, q):
    """
    外部路1-7，必须全切掉
    :param shape:
    :param q:
    :return:
    """
    origin_shape = shape
    buffered_shape = shape.buffer(0.0002)
    line_list = q.get_out_line_by_geom(buffered_shape.wkt)
    for line_info in line_list:
        line_item = wkt.loads(line_info[4])
        # 一到七级别路，外扩8米
        buffer_road = line_item.buffer(0.00008)
        origin_shape = origin_shape.difference(buffer_road)
    return origin_shape


def get_out_road_level8(shape, q):
    """ 8级别路处理
    :param shape:
    :param q
    :return:
    """
    line_list = q.get_out_line_by_geom(shape.wkt, level8=True)
    line_dict = {}
    n = 0

    # def is_line_cross(line, poly):
    #     ret = False
    #     if poly.type == "Polygon" and line.type == "LineString":
    #         poly_linestring = poly.wkt.replace("POLYGON ((", "LINESTRING(").replace("))", ")")
    #         intersection = line.intersection(wkt.loads(poly_linestring))
    #         if intersection.type == "MultiPoint" and len(intersection) % 2 == 0:
    #             ret = True
    #     return ret

    print("==== 外部路 8级路 ====")
    for line_info in line_list:
        link_id = line_info[0]
        kind = line_info[1]
        road_dir = line_info[3]
        line_wkt = line_info[4]
        lane_c = line_info[5]
        line_shape = wkt.loads(line_wkt)
        origin_shape = shape.buffer(-0.0002)
        line_dict[n] = line_shape
        n += 1
    i = 0
    res_list = []
    len_dict = len(line_dict)
    # 组合成外部路组
    while i < len_dict:
        if i not in line_dict:
            i += 1
            continue
        shape = line_dict[i]
        del line_dict[i]
        res_shape, line_dict = merge_line_helper(shape, line_dict, 0)
        res_list.append(res_shape)
        i += 1
    # for multi_line_item in res_list:
    #     print(multi_line_item.wkt)
    print("==== 外部路 -- 8级路 === end")
    return res_list


def merge_line_helper(shape, line_dict, count):
    """ 内部路组合辅助迭代函数, 限制迭代次数
    :param shape:
    :param line_dict:
    :param count 迭代次数
    :return:
    """
    if count > 900:
        return shape, line_dict
    for i, line in line_dict.items():
        if shape.touches(line):
            shape = shape.union(line)
            del line_dict[i]
            return merge_line_helper(shape, line_dict, count + 1)
    return shape, line_dict


def multipolygon_to_single(shape, buffer=0.00001, merge_first=1, ctx: StrategyContext = None):
    """ multipolygon合并
        通过buffer再收缩，合并近的，如果仍然是多个取面积最大的
    :param shape:
    :param buffer:
    :param merge_first:
    :return:
    """
    if not (hasattr(shape, 'geoms') and len(shape.geoms) > 0) \
            and not (hasattr(shape, 'interiors') and len(shape.interiors) > 0):
        return shape
    if buffer > 0:
        if merge_first == 1:  # 部分场景需要合并，部分场景需要分割
            shape = shape.buffer(buffer)
            shape = shape.buffer(-buffer)
        else:
            shape = shape.buffer(-buffer)
            # print("buffer_res_1:", shape.wkt)
            shape = shape.buffer(buffer)
    # print("buffer_res_2:", shape.wkt)
    if not (hasattr(shape, 'geoms') and len(shape.geoms) > 0) \
            and not (hasattr(shape, 'interiors') and len(shape.interiors) > 0):
        return shape
    if hasattr(shape, 'geoms'):
        max_shape = shape.geoms[0]
        for shape_item in shape.geoms:
            if max_shape.area < shape_item.area:
                max_shape = shape_item
        shape = max_shape
    if hasattr(shape, 'interiors') and len(shape.interiors) > 0:
        shape = geometry.Polygon(shape.exterior.coords)
    # print("interiors_res:", shape.wkt)
    return shape


def write_log(ctx: StrategyContext, msg: str):
    """
    记录日志
    """
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    w_msg = f"{now}\t{msg}\n"
    with open(ctx.conf.log_file, 'a+') as f:
        f.write(w_msg)
