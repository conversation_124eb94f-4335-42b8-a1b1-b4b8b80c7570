# !/usr/bin/env python3
"""
内部路策略处理常用工具
"""
from shapely import wkt
from script.inner_roads.post_process.context import StrategyContext
from src.tools import aoi_tools


def evaluate_aoi_face(ctx: StrategyContext, proceed):
    """
    aoi是否覆盖检测
    """
    # print("aoi_face")
    if ctx.scores.current_fixed_geom == "" or not ctx.conf.filter_online_aoi:
        return proceed()
    exists = ctx.conf.aoi_model.get_intersection_aoi(ctx.scores.current_fixed_geom, ctx.conf.handle_group_street)
    if len(exists) > 0:
        current_score = 0
        for existItem in exists:
            interItem = wkt.loads(existItem[0]).intersection(wkt.loads(ctx.scores.current_fixed_geom))
            iou = aoi_tools.calc_iou(interItem.wkt, ctx.scores.current_fixed_geom)
            if iou > 0.7:
                ctx.feature_ctx.aoi_exists = True
                # aoi已经存在，不再处理
                print("aoi已经存在，不处理")
                ctx.scores.current_fixed_geom = ""
                ctx.scores.score_dict['aoi_face_online'] = 0
                return proceed()
            else:
                score = 1 - iou ** 2
                if current_score < score:
                    current_score = score
        ctx.scores.score_dict['aoi_face_online'] = current_score
    else:
        # 没有数据，
        ctx.scores.score_dict['aoi_face_online'] = 1
    proceed()


def search_poi(ctx: StrategyContext, proceed):
    """
    查询绑定的poi
    """
    if not ctx.scores.fixed_result or ctx.scores.fixed_result == "":
        return proceed()
    bind = ctx.conf.aoi_model.search_poi(ctx.scores.fixed_result)
    poi_info = {}
    bind_point = 0
    intelligence_bid = ctx.feature_ctx.intelligence_info.get("bid", "")
    tag_weight = aoi_tools.get_aoi_tag_weight()
    if len(bind) == 1:
        bind_point = 0.75
        poi_info = bind[0]
        if str(poi_info['bid']) == str(intelligence_bid):
            bind_point = 1
            print("poi绑定与竞品数据一致1")
        elif poi_info['std_tag'].rstrip() not in tag_weight:
            print("非指定的aoi类型1", poi_info['std_tag'].rstrip())
            bind_point = 0
            poi_info = {}
    elif len(bind) > 1:
        other_poi = 10
        bind_point = 0
        for poi_item in bind:
            if poi_item['bid'] == intelligence_bid:
                bind_point = 1
                poi_info = poi_item
                print("poi绑定与竞品数据一致2")
                break
            if str(poi_item['std_tag']).rstrip() not in tag_weight:
                print("非指定的aoi类型2", poi_item['std_tag'])
                continue
            if poi_item.get('click_pv', 0) < 1:
                print("无pv", poi_item['std_tag'])
                continue
            if not poi_info or poi_info.get('click_pv', 0) < poi_item['click_pv']:
                bind_point = 0.5
                poi_info = poi_item
                other_poi += 1
        if bind_point < 1 and other_poi > 3 and poi_info.get('click_pv', 0) < 50:
            # 如果没有系统poi，并且周围的poi很多，这样项计算为0分
            bind_point = 0

    ctx.scores.score_dict['poi_info_score'] = bind_point
    ctx.feature_ctx.bind_poi_info = poi_info
    proceed()


def check_aoi_group(ctx: StrategyContext, proceed):
    """
    检查聚合院落
    :param ctx:
    :param proceed:
    :return:
    """
    if not ctx.conf.filter_online_aoi:
        return proceed()
    if not ctx.conf.handle_group_street:
        return proceed()
    if ctx.scores.fixed_result == "":
        return proceed()
    ret = ctx.conf.aoi_model.get_aoi_group_poi(ctx.scores.fixed_result)
    if len(ret) < 1:
        return proceed()
    ctx.scores.memo += "存在聚合院落;"
    poi_list = [i[0] for i in ret]
    data = ctx.conf.aoi_model.multi_get_poi_by_bids(poi_list)
    if len(data) < 1:
        return proceed()
    filter_list = ['高等院校', '飞机场', '火车站', '公园', '文物古迹', '中学', '小学']
    for pois in data:
        for filter_item in filter_list:
            if str(pois[1]).__contains__(filter_item):
                ctx.scores.memo += f"含有大型集合院落{filter_item};"
                print(f"包含大型集合院落 {filter_item}，过滤")
                ctx.scores.fixed_result = ""
                return proceed()
    proceed()
