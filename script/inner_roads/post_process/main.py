# !/usr/bin/env python3
"""
模块入口文件
"""
import argparse
import json
import os.path
import traceback
from pathlib import Path
import sys
from warnings import simplefilter

sys.path.insert(0, os.path.abspath("."))

from script.inner_roads.post_process.context import StrategyContext
from src.tools.utils import read_json
from src.tools.pipeline import Pipeline
from src.model.aoi_model import AoiModel
import src.tools.aoi_tools as aoi_tools
from shapely import wkt, geometry
from script.inner_roads.post_process.fix_geom import fix_area

ExoprtDict = []


def init_feature(ctx: StrategyContext, proceed):
    """
    初始化一些参数
    """
    list_street = ctx.conf.aoi_model.get_street_geoms_by_geom(ctx.feature_ctx.inner_road_group_wkt, 5)
    current_intersection_area = 0
    current_street_wkt = ''
    current_street_wkt_faceid = ''
    for street in list_street:
        shape = wkt.loads(street[1])
        intersection = shape.intersection(wkt.loads(ctx.feature_ctx.inner_road_group_wkt))
        area = aoi_tools.get_area(intersection.wkt)
        if current_intersection_area < area:
            current_intersection_area = area
            current_street_wkt_faceid = street[0]
            current_street_wkt = street[1]
    ctx.feature_ctx.street_wkt = current_street_wkt
    ctx.feature_ctx.street_faceid = current_street_wkt_faceid
    proceed()


def evaluate_aoi_intelligence(ctx: StrategyContext, proceed):
    """
    竞品数据查询
    """
    print("intelligence{}".format(ctx.feature_ctx.inner_road_group))
    inner_wkt = ctx.feature_ctx.inner_road_group_wkt
    intelList = ctx.conf.aoi_model.get_competitor_geom(inner_wkt)
    if len(intelList) < 1:
        print("无竞品数据")
        ctx.scores.current_fixed_geom = ctx.feature_ctx.inner_road_group_wkt
        proceed()
        return
    current_iou = 0
    for intel in intelList:
        intel_wkt = intel[1]
        iou = aoi_tools.calc_iou(inner_wkt, intel_wkt)
        if iou < ctx.conf.intelligence_iou_min:
            continue
        if current_iou < iou:
            # 保存分数最高的数据
            current_iou = iou
            ctx.feature_ctx.intelligence_info = {"bid": intel[0], 'wkt': intel[1]}
    ctx.scores.score_dict['intelligence'] = current_iou
    print("竞品数据:", ctx.feature_ctx.intelligence_info)
    proceed()


def evaluate_detect(ctx: StrategyContext, proceed):
    """
    检测结果扫描，并且计算得分
    """
    print("detect")
    valid_item = {}
    for index, item in enumerate(ctx.feature_ctx.inner_road_detect_info):
        # 交集并集比
        rate = aoi_tools.calc_iou(item['inner_wkt'], item['bbox_geom'])
        if rate < ctx.conf.detect_iou_rate_min:
            continue
        item['iu_rate'] = rate
        if valid_item.get("iu_rate", 0) < rate:
            valid_item = item
    ctx.feature_ctx.max_iou_detect = valid_item
    # 计算检测得分
    ctx.scores.score_dict['detect_score'] = valid_item.get("score", 0)
    ctx.scores.score_dict['detect_iou'] = valid_item.get('iu_rate', 0)
    proceed()


def evaluate_aoi_face(ctx: StrategyContext, proceed):
    """
    aoi楼块检测
    """
    print("aoi_face")
    # intel_wkt = ctx.feature_ctx.intelligence_info.get("wkt", "")
    # if intel_wkt == "":
    #     print("无竞品数据，跳过")
    #     proceed()
    #     return
    ctx.scores.current_fixed_geom = ctx.feature_ctx.intelligence_info.get("wkt", ctx.feature_ctx.inner_road_group_wkt)
    exists = ctx.conf.aoi_model.get_intersection_aoi(ctx.scores.current_fixed_geom)
    if len(exists) > 0:
        current_score = 0
        for existItem in exists:
            interItem = wkt.loads(existItem[0]).intersection(wkt.loads(ctx.scores.current_fixed_geom))
            iou = aoi_tools.calc_iou(interItem.wkt, ctx.scores.current_fixed_geom)
            if iou > 0.7:
                ctx.feature_ctx.aoi_exists = True
                # aoi已经存在，不再处理
                print("aoi已经存在，不处理")
                ctx.scores.current_fixed_geom = ""
                ctx.scores.score_dict['aoi_face_online'] = 0
                return proceed()
            else:
                score = 1 - iou
                if current_score < score:
                    current_score = score
        ctx.scores.score_dict['aoi_face_online'] = current_score
    else:
        # 没有数据，
        ctx.scores.score_dict['aoi_face_online'] = 1
    proceed()


def evluate_poi_tag(ctx: StrategyContext, proceed):
    """
    处理poi和tag
    """
    print("poi tag")
    tag_weight = aoi_tools.get_aoi_tag_weight()
    if ctx.scores.current_fixed_geom != "":
        tag_list = ctx.conf.aoi_model.get_poi_tag_by_geom(ctx.scores.current_fixed_geom)
        if len(tag_list) < 1:
            ctx.scores.current_fixed_geom = ""
            print("无poi挂接")
            ctx.scores.score_dict['poi_connect'] = 0
            return proceed()
        else:
            poi_connect = 0
            count = 0
            for tag_item in tag_list:
                if tag_item in tag_weight:
                    poi_connect += tag_weight[tag_item]
                    count += 1
            ctx.scores.score_dict['poi_connect'] = 0 if count == 0 else (poi_connect / count) / 5
    else:
        print("无geom，跳过")
    proceed()


def filter_not_intelligence(ctx: StrategyContext, proceed):
    """
        过滤不存在竞品数据
    """
    if ctx.scores.score_dict.get("intelligence", 0) == 0:
        print("没有竞品数据，过滤")
        ctx.scores.current_fixed_geom = ""
    proceed()


def search_poi(ctx: StrategyContext, proceed):
    """
    查询绑定的poi
    """
    if not ctx.scores.fixed_result or ctx.scores.fixed_result == "":
        return proceed()
    bind = ctx.conf.aoi_model.search_poi(ctx.scores.fixed_result)
    poi_info = {}
    bind_point = 0
    intelligence_bid = ctx.feature_ctx.intelligence_info.get("bid", "")
    tag_weight = aoi_tools.get_aoi_tag_weight()
    if len(bind) == 1:
        bind_point = 0.75
        poi_info = bind[0]
        if str(poi_info['bid']) == str(intelligence_bid):
            bind_point = 1
            print("poi绑定与竞品数据一致1")
        elif poi_info['std_tag'].rstrip() not in tag_weight:
            print("非指定的aoi类型1", poi_info['std_tag'].rstrip())
            bind_point = 0
            poi_info = {}
    elif len(bind) > 1:
        other_poi = 10
        bind_point = 0
        for poi_item in bind:
            if poi_item['bid'] == intelligence_bid:
                bind_point = 1
                poi_info = poi_item
                print("poi绑定与竞品数据一致2")
                break
            if str(poi_item['std_tag']).rstrip() not in tag_weight:
                print("非指定的aoi类型2", poi_item['std_tag'])
                continue
            if poi_item.get('click_pv', 0) < 1:
                print("无pv", poi_item['std_tag'])
                continue
            if not poi_info or poi_info.get('click_pv', 0) < poi_item['click_pv']:
                bind_point = 0.5
                poi_info = poi_item
                other_poi += 1
        if bind_point < 1 and other_poi > 3 and poi_info.get('click_pv', 0) < 50:
            # 如果没有系统poi，并且周围的poi很多，这样项计算为0分
            bind_point = 0

    ctx.scores.score_dict['poi_info_score'] = bind_point
    ctx.feature_ctx.bind_poi_info = poi_info
    proceed()


def calc_group(ctx: StrategyContext, proceed):
    """
    聚合院落
    :return:
    """
    intersect = ctx.conf.aoi_model.check_is_intersection_by_geom(ctx.scores.fixed_result)
    print("fix_shape_is_intersect:", intersect)
    if len(intersect) > 0:
        for intersect_item in intersect:
            print(intersect_item)
            if len(intersect_item) > 1 and intersect_item[0] == 1:
                print("存在聚合院落,依据聚合院落修形;")
                break
    proceed()


def calcScore(ctx: StrategyContext, proceed):
    """
    计算分数， 简答计算一下
    """
    # 判断裁剪比
    if ctx.scores.fixed_result == "" or ctx.scores.current_fixed_geom == "":
        ctx.scores.score_dict['fix_score'] = 0
    else:
        calc_iou = aoi_tools.calc_iou(ctx.scores.fixed_result, ctx.scores.current_fixed_geom)
        if calc_iou < 0.5:
            ctx.scores.score_dict['fix_score'] = 0
        else:
            ctx.scores.score_dict['fix_score'] = calc_iou

    all_score = (ctx.scores.score_dict.get("intelligence", 0) * 1.5
                 + ctx.scores.score_dict.get("detect_score", 0) * 0.5
                 + ctx.scores.score_dict.get("aoi_face_online", 0)
                 + ctx.scores.score_dict.get('fix_score', 0) * 2
                 + ctx.scores.score_dict.get("poi_info_score", 0) * 2) / 7 * 100
    ctx.scores.all_score = all_score
    print(all_score)
    proceed()


def export(ctx: StrategyContext, proceed):
    """
    导出数据
    """
    print("print")
    print("=================== {} ===============".format(ctx.feature_ctx.inner_road_group))
    # print("街区", ctx.feature_ctx.street_wkt)
    # print("区域", ctx.scores.current_fixed_geom)
    # print("修型", ctx.scores.fixed_result)
    print(ctx.scores.score_dict)
    print("\n")
    # 只输出竞品
    if ctx.scores.score_dict.get("intelligence",
                                 0) == 0 or ctx.scores.current_fixed_geom == "" or ctx.scores.fixed_result == "":
        # 非竞品数据不处理
        return proceed()

    img_dir = ctx.feature_ctx.inner_road_group[0]
    item = {
        'id': ctx.feature_ctx.inner_road_group,
        'recall': 0,
        'isCorrect': 0,
        'total': 0,
        'memo': ctx.scores.memo,
        'poi_bid': ctx.feature_ctx.bind_poi_info.get("bid", ""),
        'poi_name': ctx.feature_ctx.bind_poi_info.get("name", ""),
        'city': ctx.feature_ctx.bind_poi_info.get("city", ""),
        'ratio': 1,
        'poiNum': ctx.scores.score_dict.get("poi_connect", 0),
        'overlays': 0,
        'score': ctx.scores.all_score,
        'imgPredict': 'http://*************:8001/aoi_ml/products/inner_road_14w/{}/{}.jpg'
            .format(img_dir, ctx.feature_ctx.inner_road_group),
        'imgLabel': '',
        'wkt': ctx.scores.fixed_result,
        'info_wkt': ctx.feature_ctx.intelligence_info.get("wkt", "")
    }
    ExoprtDict.append(item)
    proceed()


def handle(ctx: StrategyContext):
    """
    入口函数
    :param ctx 上下文
    """
    pipeline = Pipeline(
        init_feature,
        evaluate_aoi_intelligence,
        evaluate_detect,
        evaluate_aoi_face,
        evluate_poi_tag,
        filter_not_intelligence,
        fix_area,
        search_poi,
        calcScore,
        export
    )
    pipeline(ctx)


def read_dict():
    all_dict = {}
    with open('inner_road_1130.json', 'r') as f_so:
        json_item = json.load(f_so)
        for i in json_item:
            all_dict[i['id']] = 1
    return all_dict


def do_filter(group_id, allowed_dict):
    """
    跳过部分数据
    """
    return group_id not in allowed_dict


if __name__ == '__main__':
    sorted_dict = read_dict()
    simplefilter(action='ignore', category=FutureWarning)
    parser = argparse.ArgumentParser()
    parser.add_argument("--json", required=True, type=str, help="json数据地址")
    parser.add_argument("--only_group_id", type=str, default="-", help="单独处理的任务信息")
    args = parser.parse_args()
    json_file = Path(args.json).__str__()
    __only_group_id = Path(args.only_group_id).__str__()
    json_data = read_json(json_file)
    # 数据库链接只初始化一次
    __aoiModel = AoiModel()
    # 获取街区
    all_count = len(json_data)
    current = 0
    for __group_id, __item in json_data.items():
        # 支持单个任务执行
        if __only_group_id != "-":
            if __only_group_id != __group_id:
                continue
        print("进度 {} / {}".format(current, all_count))
        current += 1
        if do_filter(__group_id, sorted_dict):
            print("过滤{}".format(__group_id))
            continue
        try:
            __ctx = StrategyContext()
            __ctx.feature_ctx.inner_road_group = __group_id
            __ctx.feature_ctx.inner_road_detect_info = __item
            __ctx.feature_ctx.inner_road_group_wkt = __item[0]['inner_wkt']
            __ctx.conf.log_dir = "log/inner_road/" + __group_id[0] + "/"
            __ctx.conf.log_file = "log/inner_road/" + __group_id[0] + "/" + __group_id + ".txt"
            __ctx.conf.aoi_model = __aoiModel
            os.makedirs(__ctx.conf.log_dir, exist_ok=True)
            handle(__ctx)
        # 有错误跳过不断开
        except Exception as e:
            traceback.print_exc()
            print(['error'], e.args, __group_id)

    # 导出数据
    out_path = 'out/' + json_file + ("single/" + __only_group_id if __only_group_id != "-" else "")
    os.makedirs(out_path, exist_ok=True)
    with open(out_path + "/out.json", 'w') as f:
        json.dump(ExoprtDict, f, indent=2, ensure_ascii=False)
