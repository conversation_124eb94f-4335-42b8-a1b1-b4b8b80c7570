# !/usr/bin/env python3
"""
语义分割模块入口文件，支持poi_tag 和 扫街
"""
import argparse
import json
import math
import multiprocessing
import os.path
import traceback
from pathlib import Path
import sys
from warnings import simplefilter
from tqdm import tqdm

root_path = os.path.abspath(os.path.dirname(__file__) + "/../../../")
sys.path.insert(0, root_path)

from script.inner_roads.post_process.context import StrategyContext
from src.tools.utils import read_json
from src.tools.pipeline import Pipeline
from src.model.aoi_model import AoiModel
import src.tools.aoi_tools as aoi_tools
from shapely import wkt, geometry
# from script.inner_roads.post_process.fix_geom import fix_area
from src.fix_geom_process.fix_geom import FixGeom
from script.inner_roads.post_process.inner_roads_tools import evaluate_aoi_face, check_aoi_group
from multiprocessing import Pool

# 是否过滤在线的aoi
FILTER_ONLINE_AOI = True


def init_feature(ctx: StrategyContext, proceed):
    """
    初始化一些参数
    """
    ctx.scores.current_fixed_geom = ctx.feature_ctx.inner_road_detect_info['geom']
    ctx.feature_ctx.tags = ctx.feature_ctx.inner_road_detect_info['tags']
    ctx.feature_ctx.street_faceid = ctx.feature_ctx.inner_road_detect_info['id']
    ctx.scores.score_dict['detect_score'] = 1
    proceed()


def fix_area(ctx: StrategyContext, proceed):
    if ctx.scores.current_fixed_geom == "":
        return proceed()
    f = FixGeom(ctx.scores.current_fixed_geom, ctx.feature_ctx.inner_road_group, log_dir=ctx.conf.log_dir,
                aoi_model=ctx.conf.aoi_model, filter_online_aoi=FILTER_ONLINE_AOI)
    ret = f()
    fixed_result = ret.get('wkt', '')
    fixed_memo = ret.get('memo', '')
    has_water = ret.get("has_water", False)
    ctx.scores.fixed_result = fixed_result
    ctx.scores.memo = fixed_memo
    ctx.scores.all_dict['has_water'] = has_water
    proceed()


def search_poi(ctx: StrategyContext, proceed):
    """
    查询绑定的poi
    """
    if not ctx.scores.fixed_result or ctx.scores.fixed_result == "":
        return proceed()
    bind = ctx.conf.aoi_model.search_poi(ctx.scores.fixed_result, ctx.conf.filter_online_aoi)
    poi_info = {}
    bind_point = 0
    tag_weight = aoi_tools.get_aoi_tag_weight()
    if len(bind) == 1:
        bind_point = 1
        poi_info = bind[0]
        if poi_info['std_tag'].rstrip() not in tag_weight:
            # print("非指定的aoi类型1", poi_info['std_tag'].rstrip())
            bind_point = 0
            poi_info = {}
    elif len(bind) > 1:
        other_poi = 0
        bind_point = 0.8
        for poi_item in bind:
            if str(poi_item['std_tag']).rstrip() not in tag_weight:
                # print("非指定的aoi类型2", poi_item['std_tag'])
                continue
            if poi_item.get('click_pv', 0) < 1:
                # print("无pv", poi_item['std_tag'])
                continue
            other_poi += 1
            # poi越多，此项分越低
            bind_point -= 0.1
            ctx.feature_ctx.may_pois.append(poi_item)
            if not poi_info or poi_info.get('click_pv', 0) < poi_item['click_pv']:
                poi_info = poi_item
        if bind_point < 0:
            # 如果没有系统poi，并且周围的poi很多，这样项计算为0分
            bind_point = 0

    if poi_info.get("name", "") == "":
        bind_point = 0
    ctx.scores.score_dict['poi_info_score'] = bind_point
    ctx.feature_ctx.bind_poi_info = poi_info
    proceed()


def calcStrangeArea(ctx: StrategyContext, proceed):
    """
    判断是否有长尾数据
    主要逻辑：遍历区域内所有的点，以30米为半径画圆，如果与区域的交点有4个则说明存在狭小区域
    :param ctx:
    :param proceed:
    :return:
    """
    if ctx.scores.fixed_result == "POLYGON EMPTY":
        ctx.scores.fixed_result = ""
    if ctx.scores.fixed_result == "" or ctx.scores.current_fixed_geom == "":
        ctx.scores.score_dict['strange_score'] = 0
    else:
        # print(ctx.scores.fixed_result)
        shape = wkt.loads(ctx.scores.fixed_result)
        boundy = shape.boundary
        joined_count = 0
        for i in boundy.coords:
            x, y = i
            # 以边界上的点为圆心,30米,去截取区域
            region_p30 = wkt.loads(f"POINT({x} {y})").buffer(0.0003)
            region_p20 = wkt.loads(f"POINT({x} {y})").buffer(0.0002)
            boundary_p30 = region_p30.boundary
            boundary_p20 = region_p20.boundary
            # 拿半径30米的圆去截取，获取交点
            unioned = boundy.intersection(boundary_p30)

            if unioned.type == "MultiPoint":
                pxy = unioned.wkt.replace("MULTIPOINT (", "").replace(")", "").split(",")
                if len(pxy) > 2:
                    joined_count += 1
                    # 再换20米的圆去截取，如果截取到，说明存在较小区域，直接放弃
                    unioned20 = boundy.intersection(boundary_p20)
                    if unioned20.type == "MultiPoint":
                        p20xy = unioned20.wkt.replace("MULTIPOINT (", "").replace(")", "").split(",")
                        if len(p20xy) > 2:
                            ctx.scores.score_dict['strange_score'] = 0
                            return proceed()
        # print("==== joined count ==", joined_count)
        if joined_count == 0:
            current_score = 1
        elif joined_count == 1:
            current_score = 0.9
        elif joined_count < 3:
            current_score = 0.7
        elif joined_count < 5:
            current_score = 0.4
        else:
            current_score = 0
        ctx.scores.score_dict['strange_score'] = current_score
    proceed()


def calcScore(ctx: StrategyContext, proceed):
    """
    计算分数， 简答计算一下
    """
    # 判断裁剪比
    if ctx.scores.fixed_result == "" or ctx.scores.current_fixed_geom == "":
        ctx.scores.score_dict['fix_score'] = 0
    else:
        area = aoi_tools.get_area(ctx.scores.fixed_result)
        shape = wkt.loads(ctx.scores.fixed_result)
        boundy = shape.boundary

        # 面积周长比
        circum = boundy.length
        r = (4 * math.pi * shape.area / (circum ** 2))
        # 面积周长比
        if r > 0.75:
            sr_score = 1
        elif r > 0.65:
            sr_score = r + 0.25
        elif r > 0.55:
            sr_score = 2 * r - 0.4
        elif r > 0.41:
            sr_score = (5 / 14.0) * r + 0.0536
        else:
            sr_score = 0
        ctx.scores.score_dict['sr_score'] = sr_score

        convex_shape = wkt.loads(ctx.scores.fixed_result).convex_hull
        convex_area = aoi_tools.get_area(convex_shape.wkt)
        if area > 8000:
            fix_score = 1
        elif area < 4000:
            fix_score = 0
            return proceed()
        elif area < 6000:
            # y = 3/20000 (x - 4000)
            fix_score = (3 / 20000.0) * (area - 4000)
        else:
            fix_score = (7 / 20000.0) * area - 1.8

        ctx.scores.score_dict['fix_score'] = fix_score
        ctx.scores.score_dict['hull_score'] = (area / convex_area)

    all_score = (ctx.scores.score_dict.get("detect_score", 0) * 2
                 + ctx.scores.score_dict.get("aoi_face_online", 0)
                 + ctx.scores.score_dict.get('fix_score', 0) * 2
                 + ctx.scores.score_dict.get('hull_score', 0) * 2
                 + ctx.scores.score_dict.get('strange_score', 0) * 3
                 + ctx.scores.score_dict.get("sr_score", 0)
                 ) / 11 * 100
    ctx.scores.all_score = all_score
    # print(all_score)
    proceed()


def export(ctx: StrategyContext, proceed):
    """
    导出数据
    """
    if ctx.scores.fixed_result == "" or ctx.scores.current_fixed_geom == "":
        return proceed()
    # print("print")
    # print("=================== {} ===============".format(ctx.feature_ctx.inner_road_group))
    # print(ctx.scores.score_dict)
    # print("\n")

    memo = ""
    if len(ctx.feature_ctx.may_pois) > 0:
        for cpoi in ctx.feature_ctx.may_pois:
            memo += f"{cpoi['name']}:{cpoi['click_pv']};"

    img_dir = ctx.feature_ctx.inner_road_group[0]
    item = {
        'id': ctx.feature_ctx.inner_road_group,
        'recall': 0,
        'isCorrect': 0,
        'total': 0,
        'memo': ctx.scores.memo + memo,
        'poi_bid': ctx.feature_ctx.bind_poi_info.get("bid", ""),
        'poi_name': ctx.feature_ctx.bind_poi_info.get("name", ""),
        'city': ctx.feature_ctx.bind_poi_info.get("city", ""),
        'ratio': 1,
        'poiNum': ctx.scores.score_dict.get("poi_info_score", 0),
        'overlays': 0,
        'score': ctx.scores.all_score,
        'imgPredict': '',
        'imgLabel': 'http://gzbh-ns-map-na002.gzbh.baidu.com:8090/aoi_ml/dingping/PaddleSeg_MultiChannel/products/'
                    'product_q4_89056/added_prediction/images/{}.jpg'.format(ctx.feature_ctx.street_faceid),
        'wkt': ctx.scores.fixed_result,
        'info_wkt': ctx.feature_ctx.inner_road_group_wkt,
        'score_dict': ctx.scores.score_dict,
        'tags': ctx.feature_ctx.tags,
        'may_pois': ctx.feature_ctx.may_pois
    }
    ctx.scores.all_dict = [item]
    proceed()


def handle(ctx: StrategyContext):
    """
    入口函数
    :param ctx 上下文
    """
    pipeline = Pipeline(
        init_feature,
        evaluate_aoi_face,
        fix_area,
        search_poi,
        check_aoi_group,
        calcStrangeArea,
        calcScore,
        export
    )
    pipeline(ctx)


def run(__item):
    """
    执行一个任务,用于多进程执行
    :param __item:
    :return:
    """
    with AoiModel() as __aoiModel:
        __group_id = __item['uuid']
        try:
            __ctx = StrategyContext()
            __ctx.feature_ctx.is_seg = True
            __ctx.feature_ctx.inner_road_group = __group_id
            __ctx.conf.filter_online_aoi = FILTER_ONLINE_AOI
            __ctx.feature_ctx.inner_road_detect_info = __item
            __ctx.feature_ctx.inner_road_group_wkt = __item['geom']
            __ctx.conf.log_dir = f"{product_dir}/log/"
            __ctx.conf.log_file = f"{product_dir}/log/" + __group_id + ".txt"
            __ctx.conf.aoi_model = __aoiModel
            os.makedirs(__ctx.conf.log_dir, exist_ok=True)
            handle(__ctx)
            if __ctx.scores.fixed_result != "":
                with open(f"{product_dir}/result/{__group_id}.json", 'w') as f2:
                    json.dump(__ctx.scores.all_dict, f2, indent=2, ensure_ascii=False)
        # 有错误跳过不断开
        except Exception as e:
            with open(f"{product_dir}/error/{__group_id}.json", 'w') as f:
                data = {"id": __group_id, "error": str(e.args) + str(traceback.format_exc())}
                json.dump(data, f)


# 执行json文件的名字
run_json_file = ""
product_dir = ""


def run_main(json_file_path, process_num=10, filter_online_aoi=True):
    """
    函数调用
    :param json_file_path: json文件
    :param process_num: 进程数
    :param filter_online_aoi: 是否过滤在线的aoi
    :return:
    """
    global FILTER_ONLINE_AOI
    FILTER_ONLINE_AOI = True
    if not filter_online_aoi:
        FILTER_ONLINE_AOI = False
    simplefilter(action='ignore', category=FutureWarning)
    global run_json_file
    global product_dir
    run_json_file = json_file_path
    product_dir = json_file_path + "_product"
    json_data_item = read_json(json_file_path)
    all_count = len(json_data_item)
    os.makedirs(f"{product_dir}/result", exist_ok=True)
    os.makedirs(f"{product_dir}/log", exist_ok=True)
    os.makedirs(f"{product_dir}/error", exist_ok=True)
    with Pool(process_num) as p:
        _ = list(tqdm(p.imap(run, json_data_item), total=all_count))


if __name__ == '__main__':
    simplefilter(action='ignore', category=FutureWarning)
    parser = argparse.ArgumentParser()
    parser.add_argument("--json", required=True, type=str, help="json数据地址")
    parser.add_argument("--only_group_id", type=str, default="|", help="单独处理的任务信息")
    args = parser.parse_args()
    json_file = Path(args.json).__str__()
    run_json_file = json_file
    product_dir = json_file + "_product"
    __only_group_id = Path(args.only_group_id).__str__()
    json_data = read_json(json_file)
    all_count = len(json_data)
    os.makedirs(f"{product_dir}/result", exist_ok=True)
    os.makedirs(f"{product_dir}/log", exist_ok=True)
    os.makedirs(f"{product_dir}/error", exist_ok=True)
    # # 指定单个任务执行
    if len(__only_group_id) > 2:
        for target in json_data:
            if target['uuid'] != __only_group_id:
                continue
            run(target)
    else:
        # 多进程执行
        with Pool(10) as p:
            _ = list(tqdm(p.imap(run, json_data), total=all_count))
