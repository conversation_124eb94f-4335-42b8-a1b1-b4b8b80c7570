# !/usr/bin/env python3
"""
内部路与竞品交集数据
"""
import json
from typing import Dict
from shapely import wkt, geometry, ops

from warnings import simplefilter
from src.tools.pg_tool import PgTool
from src.tools.aoi_tools import get_area


def load_data() -> Dict:
    """加载数据
    inner_road_range.json 由 export_range.py 生成
    """
    data_dict = {}
    with open("inner_road_range.json", 'r') as f:
        json_data = json.load(f)
        for i in json_data:
            data_dict[i['uuid']] = i['poly_geom']
    return data_dict


def handle():
    """
    处理数据
    """
    pg = PgTool()
    conn = pg.conn_poi
    corser = conn.cursor()
    data = load_data()
    explode = ['区', '市', '镇', '县']
    savedDict = {}
    for k, v in data.items():
        sql = "select st_asText(geom) as geom, bid,intel_id,name,address from aoi_intelligence where" \
              " ST_Intersects(st_geomfromText('{}', 4326), geom) ;".format(v)
        corser.execute(sql)
        conn.commit()
        ret = corser.fetchall()
        if len(ret) < 1:
            continue
        print("=================")
        print("+++++ {} +++++ {}".format(k, v))
        w1 = wkt.loads(v)
        a_w1 = get_area(w1)
        savedList = []
        for data in ret:
            a = list(filter(lambda x: str(data[3]).endswith(x), explode))
            if len(a) > 0:
                continue
            w2 = wkt.loads(data[0])
            a_w2 = get_area(w2)
            try:
                # 交集
                wi = w1.intersection(w2)
                a_wi = get_area(wi)
                # 并集合
                wu = w1.union(w2)
                a_wu = get_area(wu)
                if a_w2 / a_w1 > 10:
                    # 筛选区域比当前区域大10倍 忽略
                    continue
                if a_wi / a_wu < 0.2:
                    # 交集与并集比小于0.2 结束
                    continue
            except Exception as e:
                # 有错误，记录错误信息
                print("[error]", e, data)
                continue
            savedList.append(data)
        if len(savedList) > 0:
            print(savedList)
            savedDict[k] = {"geom": v, "in": savedList}
            print("\n")
    with open("in.json", "w") as f:
        json.dump(savedDict, f, ensure_ascii=False, indent=4)


if __name__ == '__main__':
    # 忽略警告信息
    simplefilter(action='ignore', category=FutureWarning)
    # 执行处理
    handle()
