# !/usr/bin/env python3
"""
矢量化后产生的 vectorize_info/*json 文件合并, 并且分配uuid，支持街区多aoi提取
"""
import json
import os.path
import sys
from glob import glob
from warnings import simplefilter

import tqdm
from hashlib import md5
from shapely import wkt

ROOT_PATH = os.path.abspath(os.path.dirname(__file__) + "/../../../")
sys.path.insert(0, ROOT_PATH)
from multiprocessing import Pool
from src.tools.aoi_tools import get_area

# 最小面积，4000
MIN_AREA = 4000

# 街区的文件 json_info/image/*.json
STREET_JSON_PATH = "/root/codes/baidu_beec_aoi-ml/baidu/beec/aoi-ml/flow_data/tmp/json_info/image"
# 矢量化后的文件
VEC_JSON_PATH = "/root/codes/baidu_beec_aoi-ml/baidu/beec/aoi-ml/flow_data/tmp/output_vectorize/vectorize_info"
# 是否过滤小面积区域
IS_FILTER = True


def process_handle(file_name):
    """
    多进程处理
    :param file_name:
    :return:
    """
    export_list = []
    with open(file_name) as f:
        json_data = json.load(f)
        street_id = json_data['id']
        street_file = f"{STREET_JSON_PATH}/{street_id}.json"
        features = json_data['features']
        if len(features) < 1:
            return []
        if not os.path.isfile(street_file):
            print(f"街区json不存在：{street_file}")
            return []
        with open(street_file) as sf:
            street_data = json.load(sf)
            left = street_data['region']['left']
            top = street_data['region']['top']
            right = street_data['region']['right']
            buttom = street_data['region']['bottom']
            line_str = f"LINESTRING({left} {top}, {right} {top}, {right} {buttom}, {left} {buttom}, {left} {top})"
            for feature in features:
                #  贴边的aoi过滤
                if wkt.loads(line_str).buffer(0.00001).intersects(wkt.loads(feature['geom'])):
                    continue
                area = get_area(feature['geom'])
                if IS_FILTER and area < MIN_AREA:
                    continue

                if 'id' in feature:
                    uuid_str = feature['id']
                else:
                    uuid_str = md5(str(feature['geom']).encode("utf8")).hexdigest()
                dict_item = {"id": street_id, "uuid": uuid_str, "geom": feature['geom'], "tags": feature['tags']}
                export_list.append(dict_item)
    return export_list


def main():
    """
    将矢量化后产生的 vectorize_info/*json 文件合并，并生成的格式：
    1. uuid
    2. id
    3. geom
    4. tags
    :return:
    """
    export_list = []
    path = VEC_JSON_PATH.rstrip("/")
    out_file = path + "_seg_uuid.json"
    json_files = glob(path + "/*.json")
    with Pool(10) as p:
        item = tqdm.tqdm(p.imap(process_handle, json_files), total=len(json_files), desc="create uuid after vec")
        for i in item:
            if len(i) < 1:
                continue
            for j in i:
                export_list.append(j)
    with open(out_file, 'w') as f2:
        json.dump(export_list, f2, indent=2, ensure_ascii=False)


def run_main(vec_json_path, street_json_path, is_filter=True, filter_area=MIN_AREA):
    """
    :param vec_json_path: 矢量化json
    :param street_json_path: 街区json
    :param is_filter: 是否过滤小面积区域
    :return:
    """
    simplefilter(action='ignore', category=FutureWarning)
    global VEC_JSON_PATH
    VEC_JSON_PATH = vec_json_path
    global STREET_JSON_PATH
    STREET_JSON_PATH = street_json_path
    global IS_FILTER
    IS_FILTER = is_filter
    global MIN_AREA
    MIN_AREA = filter_area
    main()
    return


if __name__ == '__main__':
    simplefilter(action='ignore', category=FutureWarning)
    main()
