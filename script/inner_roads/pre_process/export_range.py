# !/usr/bin/env python3
"""
疑似aoi区域导出
"""

import json
from shapely import wkt
import tqdm
import sys
import os

sys.path.insert(0, os.path.abspath(os.path.dirname(__file__) + "/../../../"))

from src.tools.pg_tool import PgTool


class GetBox:
    def __init__(self):
        self.current_shape = None

    def addItem(self, polygion: str):
        shape = wkt.loads(polygion)
        if self.current_shape is None:
            self.current_shape = shape.envelope
        else:
            self.current_shape = self.current_shape.union(shape.envelope).envelope

    def getPolygon(self):
        return self.current_shape.wkt


class Export:
    def __init__(self):
        """
        初始化基础类
        """
        pg = PgTool()
        # 查询图幅号的数据库
        self.conn_mesh = pg.conn_back
        self.corser_mesh = self.conn_mesh.cursor()

        # 查询poi_online
        self.conn_poi_online = pg.conn_poi
        self.corser_poi_online = self.conn_poi_online.cursor()

    def getData(self, batch_id):
        """
        查询数据
        :param batch_id 查询批次： 批次为导入数据时间，如20221101
        """
        sql = "select group_id  from nav_link_inner_group where covered=2 and batch='{}' group by group_id" \
            .format(batch_id)
        print(sql)
        self.corser_poi_online.execute(sql)
        self.conn_poi_online.commit()
        all_list = []
        for group_idList in tqdm.tqdm(self.corser_poi_online.fetchall()):
            group_id = group_idList[0]
            sql = "select st_asText(st_envelope(geom)) as geom from nav_link_inner_group where group_id='{}' " \
                .format(group_id)
            print(sql)
            self.corser_poi_online.execute(sql)
            self.conn_poi_online.commit()
            box = GetBox()
            ct = 0
            for geomItem in self.corser_poi_online.fetchall():
                ct = ct + 1
                box.addItem(geomItem[0])
            if ct > 4:
                outerGeom = box.getPolygon()
                # 查询图幅号
                group_item = group_id.split("_")
                all_list.append({"uuid": group_id, "poly_geom": outerGeom, "mesh_id": group_item[2]})
            else:
                print("生成区域失败: {}".format(group_id))
        with open("inner_road_range_v2.json", "w") as f:
            json.dump(all_list, f, indent=2, ensure_ascii=False)


if __name__ == "__main__":
    batch = sys.argv[1]
    e = Export()
    e.getData(batch)
