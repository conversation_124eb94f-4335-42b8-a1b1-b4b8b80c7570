# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
此程序对道路库的所有内部路根据拓扑关系进行分组
Authors: <AUTHORS>
Date:    2022/10/07 09:42:42
"""
import uuid
import time
import src.tools.pg_tool as pgtool


def main():
    """
    主入口地址
    """
    pgtoolCls = pgtool.PgTool()

    conn_road = pgtoolCls.conn_road
    cursor_road = conn_road.cursor()

    conn = pgtoolCls.conn_poi
    cursor = conn.cursor()

    print(time.time())
    # 按照时间筛选
    batch = time.strftime('%Y%m%d', time.localtime(time.time()))
    # 在上游的表生成索引，暂时先不需要
    # sql = "create index if not exists nav_link_s_nid_index on nav_link(s_nid);create index if not exists nav_link_e_nid_index on nav_link(e_nid)"
    # cursor_road.execute(sql)
    # conn_road.commit()
    sql = "create table if not exists nav_link_inner_group(\
            id serial, \
            link_id varchar(128) not null default '',\
            group_id varchar(128) not null default '',\
            s_nid varchar(128) not null default '',\
            e_nid varchar(128) not null default '',\
            geom geometry(Geometry, 4326),\
            covered int not null default 0,\
            link_src varchar(128) not null default '',\
            batch varchar(128) not null default '',\
            update_time timestamp not null default(now()))"
    cursor.execute(sql)
    sql = "create index if not exists nav_link_inner_group_group_id_index on nav_link_inner_group(group_id)"
    cursor.execute(sql)
    conn.commit()

    """sql = "select distinct(mesh_id) from poi where city = '南京市'"
    cursor.execute(sql)
    res = cursor.fetchall()
    mesh_id_str = ""
    for v in res:
        mesh_id_str = mesh_id_str + v[0] + "','"
    mesh_id_str = mesh_id_str[:-3]
    """

    sql = "select link_id, s_nid, e_nid, st_astext(geom) from nav_link where form = '52'"
    cursor_road.execute(sql)
    result = cursor_road.fetchall()
    link_set_all = set()
    for v in result:
        group_id = str(uuid.uuid4()).replace("-", "")
        insert_sql = ""
        s_nid = v[1]
        e_nid = v[2]
        geom = v[3]
        link_id = v[0]
        if link_id in link_set_all:
            continue
        link_set = set()
        link_set_tmp = set()
        snid_set = {}
        enid_set = {}
        geom_set = {}
        link_set.add(link_id)
        link_set_all.add(link_id)
        link_set_tmp.add(link_id)
        snid_set[link_id] = s_nid
        enid_set[link_id] = e_nid
        geom_set[link_id] = geom

        while len(link_set) > 0:
            link_tmp = link_set.pop()
            s_nid_tmp = snid_set[link_tmp]
            e_nid_tmp = enid_set[link_tmp]
            geom_tmp = geom_set[link_tmp]
            sql = "select adjoin_nid from nav_node where node_id in (%s,%s) and adjoin_nid != ''"
            cursor_road.execute(sql, [s_nid_tmp, e_nid_tmp])
            adjoin_nid_res = cursor_road.fetchall()
            adjoin_nid_tmp = ""
            if adjoin_nid_res is not None and len(adjoin_nid_res) > 0:
                for adjoin_nid_res_tmp in adjoin_nid_res:
                    adjoin_nid_tmp = adjoin_nid_tmp + adjoin_nid_res_tmp[0] + "','"
                adjoin_nid_tmp = adjoin_nid_tmp[:-3]
            sql = "select link_id,s_nid,e_nid, st_astext(geom) from nav_link where \
                    (s_nid in ('%s', '%s', '%s') \
                    or e_nid in ('%s', '%s','%s')) and form = '52' " \
                  % (s_nid_tmp, e_nid_tmp, adjoin_nid_tmp, s_nid_tmp, e_nid_tmp, adjoin_nid_tmp)
            cursor_road.execute(sql)
            res = cursor_road.fetchall()
            if res is None:
                continue
            for tmp_res in res:
                tmp_link = tmp_res[0]
                tmp_snid = tmp_res[1]
                tmp_enid = tmp_res[2]
                tmp_geom = tmp_res[3]
                if tmp_link not in link_set_tmp:
                    link_set.add(tmp_link)
                    link_set_all.add(tmp_link)
                    link_set_tmp.add(tmp_link)
                    snid_set[tmp_link] = tmp_snid
                    enid_set[tmp_link] = tmp_enid
                    geom_set[tmp_link] = tmp_geom
                    # 有交叉点，直接将交叉点插入
            insert_sql = insert_sql + "insert into nav_link_inner_group\
                    (link_id, group_id, s_nid, e_nid, geom, link_src, batch) \
                    values('%s', '%s', '%s', '%s', st_geomfromtext('%s', \
                    4326), 'BD', '%s');" % (link_tmp, group_id, s_nid_tmp, e_nid_tmp, geom_tmp, batch)
        cursor.execute(insert_sql)

    conn.commit()
    print(time.time())


if __name__ == "__main__":
    main()
