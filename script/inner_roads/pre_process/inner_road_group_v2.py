# !/usr/bin/env python3
"""
内部路分组策略
"""
import json
import os
import sys
from glob import glob
from typing import List, Set
from multiprocessing import Pool

import tqdm
from rtree.index import Index
from shapely import wkt

root_path = os.path.abspath(os.path.dirname(__file__) + "/../../../")
sys.path.insert(0, root_path)

from src.tools.pg_tool import PgTool


def cache_inner_road():
    """
    内部路按照图幅号缓存
    :return:
    """
    road_map = {}
    pg = PgTool()
    sql = "select link_id, st_astext(geom),mesh_id from nav_link where form = '52'"
    cursor = pg.conn_road.cursor()
    cursor.execute(sql)
    ret = cursor.fetchall()
    print("查询完成")
    for item in tqdm.tqdm(ret):
        mesh_id = item[2]
        link_id = item[0]
        geom = item[1]
        tmp_list = road_map.get(mesh_id, [])
        tmp_list.append(f"{link_id},{geom},{mesh_id}\n")
        road_map[mesh_id] = tmp_list
    os.makedirs(f"{root_path}/inner_roads", exist_ok=True)
    for mesh_id, data_list in tqdm.tqdm(road_map.items()):
        with open(f"{root_path}/inner_roads/{mesh_id}.txt", 'w+') as f:
            f.writelines(data_list)


class InnerRoadsGroup:
    def __init__(self, path: str, mesh_id: str):
        self.path = path
        self.mesh_id = mesh_id
        self.link_item_list = []
        self.grouped_item_set = set()
        self.grouped_dict = {}
        self.grouped_dict_multistring = {}

    def build_rtree(self):
        """
        构建rtree
        :return:
        """
        rtree = Index(interleaved=False)
        index = 0
        with open(self.path, 'r') as f:
            data = f.read().split("\n")
            for line in data:
                line = line[0:-7]
                line_item = line.split(",", 1)
                if len(line_item) < 2:
                    continue
                self.link_item_list.append({"link_id": line_item[0], "wkt": line_item[1]})
                x_min, y_min, x_max, y_max = wkt.loads(line_item[1]).envelope.bounds
                rtree.insert(index, (x_min, x_max, y_min, y_max))
                index += 1
        print('构建rtree结束')
        return rtree

    def build_group(self, rtree: Index, wkt_str: str, group_list: Set[int]):
        """
        构建分组
        :param rtree:
        :param wkt_str:
        :param group_list:
        :return:
        """
        ix_min, iy_min, ix_max, iy_max = wkt.loads(wkt_str).envelope.bounds
        indexs = rtree.intersection((ix_min, ix_max, iy_min, iy_max))
        for index in indexs:
            if index in self.grouped_item_set:
                continue
            # 判断是否真的相交
            this_shape = wkt.loads(wkt_str)
            that_shape = wkt.loads(self.link_item_list[index]['wkt'])
            if this_shape.intersects(that_shape):
                self.grouped_item_set.add(index)
                group_list.add(index)
                wkt_str = this_shape.union(that_shape).wkt
                # 继续与其他的区域成组
                if len(group_list) > 550:
                    break
                self.build_group(rtree, wkt_str, group_list)

    def join_cache_road_by_file(self):
        """
        生成内部路组
        :return:
        """
        rtree = self.build_rtree()
        for link_index, link_wkt in enumerate(self.link_item_list):
            if link_index in self.grouped_item_set:
                # print(f"已经成组 {link_index}")
                continue
            group_list = set()
            group_list.add(link_index)
            self.build_group(rtree, link_wkt['wkt'], group_list)
            self.grouped_item_set.add(link_index)
            if len(group_list) < 2:
                continue
            tmp_data = []
            current_shape = None
            for grouped_index in group_list:
                if current_shape is None:
                    current_shape = wkt.loads(self.link_item_list[grouped_index]['wkt'])
                else:
                    current_shape = current_shape.union(wkt.loads(self.link_item_list[grouped_index]['wkt']))
                tmp_data.append(self.link_item_list[grouped_index])
            print(str(self.mesh_id + "_" + str(link_index)), len(group_list))
            self.grouped_dict[str(self.mesh_id + "_" + str(link_index))] = tmp_data
            self.grouped_dict_multistring[str(self.mesh_id + "_" + str(link_index))] = current_shape.wkt

    def get_grouped_dict(self):
        """
        分组计算
        :return:
        """
        return self.grouped_dict

    def get_grouped_dict_multistring(self):
        return self.grouped_dict_multistring

def do_job(file_path):
    mesh_id = file_path[-10:-4]
    i = InnerRoadsGroup(file_path, mesh_id)
    i.join_cache_road_by_file()
    data = i.get_grouped_dict()
    line_dict = i.get_grouped_dict_multistring()
    if len(data) < 1:
        return
    with open(f'{root_path}/inner_roads_group/{mesh_id}.txt', 'w') as f:
        json.dump(data, f, indent=2)
    with open(f'{root_path}/inner_roads_group_linestring/{mesh_id}.txt', 'w') as f2:
        json.dump(line_dict, f2, indent=2)

def join_cache_road():
    """
    内部路分组
    :return:
    """
    file_list = glob(f"{root_path}/inner_roads/*.txt")
    os.makedirs(f'{root_path}/inner_roads_group', exist_ok=True)
    os.makedirs(f'{root_path}/inner_roads_group_linestring', exist_ok=True)
    with Pool(16) as p:
        _ = list(tqdm.tqdm(p.imap(do_job, file_list), total=len(file_list)))



if __name__ == '__main__':
    # 按照图幅号生成内部路
    # cache_inner_road()
    # 生成内部路组
    join_cache_road()
