# !/usr/bin/env python3
"""
分组后的数据插入数据库
"""
import json
import os
import sys
import time
from glob import glob

import tqdm

root_path = os.path.abspath(os.path.dirname(__file__) + "/../../../")
sys.path.insert(0, root_path)

from src.tools.pg_tool import PgTool


def do_insert():
    with PgTool() as p:
        # 批次
        batch = time.strftime('%Y%m%d', time.localtime(time.time()))
        file_list = glob(f"{root_path}/inner_roads_group/*.txt")
        for file in tqdm.tqdm(file_list):
            with open(file) as fp:
                json_data = json.load(fp)
                sql = ""
                for index, item in json_data.items():
                    group_id = f"v2_{batch}_{index}"
                    for j in item:
                        link_id = j['link_id']
                        geom = j['wkt']
                        sql += f"insert into nav_link_inner_group (link_id, group_id, geom, link_src, batch) values('{link_id}', '{group_id}'," \
                               f" st_geomfromtext('{geom}', 4326), 'BD', '{batch}');"
                if sql == "":
                    continue
                with p.conn_poi.cursor() as cursor:
                    cursor.execute(sql)
                    p.conn_poi.commit()


if __name__ == '__main__':
    do_insert()
