# -*- coding: UTF-8 -*-
"""
疑似aoi区域判断
"""
import time
import sys
import os
import sys
from tqdm import tqdm

ROOT_PATH = os.path.abspath(os.path.dirname(__file__) + "/../../../")
sys.path.insert(0, ROOT_PATH)

import src.tools.pg_tool as pgtool


def judge_covered(batch):
    """
    判断覆盖
    :param batch 批次， 例如 20220911
    """
    pgCls = pgtool.PgTool()
    print(time.time())
    conn = pgCls.conn_poi
    cursor = conn.cursor()

    conn_aoi = pgCls.conn_back
    cursor_aoi = conn_aoi.cursor()

    sql = "select distinct(group_id) from nav_link_inner_group where batch = %s"
    cursor.execute(sql, [batch])
    res = cursor.fetchall()
    for v in tqdm(res):
        group_id = v[0]
        sql = "select link_id, st_astext(geom) from nav_link_inner_group where group_id = '%s'" % group_id
        cursor.execute(sql)
        res_tmp = cursor.fetchall()
        if len(res_tmp) == 1 or len(res_tmp) > 500:  # 此部分内部路组过滤，不生成疑似aoi的区域
            continue
        cover_flag = 0
        contain_flag = 0
        num_link_uncoverd = 0
        for link_info in res_tmp:
            # link_id = link_info[0]
            link_geom = link_info[1]
            sql = "select face_id, aoi_level from blu_face where " \
                  "st_intersects(st_geomfromtext('%s', 4326), geom) and " \
                  " kind != '52' and src != 'SD' and memo != 'SQCL'" % link_geom
            cursor_aoi.execute(sql)
            aoi_info_res = cursor_aoi.fetchall()
            num_aoi_coverd = 0
            if aoi_info_res is not None and len(aoi_info_res) > 0:
                for aoi_info_tmp in aoi_info_res:
                    aoi_level = aoi_info_tmp[1]
                    face_id = aoi_info_tmp[0]
                    if aoi_level != 1:
                        num_aoi_coverd = num_aoi_coverd + 1
                        break
                    else:
                        sql = "select poi_bid from blu_face_poi where face_id = '%s' and poi_bid != ''" % (face_id)
                        cursor_aoi.execute(sql)
                        poi_aoi_res = cursor_aoi.fetchone()
                        if poi_aoi_res is not None and len(poi_aoi_res) > 0:
                            sql = "select count(*) from poi where bid = '%s' " % (poi_aoi_res[0])
                            sql = sql + "and std_tag like '%旅游景点%'"
                            cursor.execute(sql)
                            if cursor.fetchone()[0] > 0:
                                num_aoi_coverd = num_aoi_coverd + 1
                                break

            if num_aoi_coverd > 0:
                cover_flag = 1
            else:
                num_link_uncoverd = num_link_uncoverd + 1

        if cover_flag == 1:
            if len(res_tmp) > 2:
                for link_info in res_tmp:
                    # link_id = link_info[0]
                    link_geom = link_info[1]
                    sql = "select face_id, aoi_level from blu_face where" \
                          " st_contains(geom, st_geomfromtext('%s', 4326)) and " \
                          "kind != '52' and src != 'SD' and memo != 'SQCL'" % link_geom
                    cursor_aoi.execute(sql)
                    aoi_info_res = cursor_aoi.fetchall()
                    num_aoi_coverd_link = 0
                    if aoi_info_res is not None and len(aoi_info_res) > 0:
                        for aoi_info_tmp in aoi_info_res:
                            aoi_level = aoi_info_tmp[1]
                            face_id = aoi_info_tmp[0]
                            if aoi_level != 1:
                                num_aoi_coverd_link = num_aoi_coverd_link + 1
                                break
                            else:
                                sql = "select poi_bid from blu_face_poi where face_id = '%s' and poi_bid != ''" % (
                                    face_id)
                                cursor_aoi.execute(sql)
                                poi_aoi_res = cursor_aoi.fetchone()
                                if poi_aoi_res is not None and len(poi_aoi_res) > 0:
                                    sql = "select count(*) from poi where bid = '%s' " % (poi_aoi_res[0])
                                    sql = sql + " and std_tag like '%旅游景点%'"
                                    cursor.execute(sql)
                                    if cursor.fetchone()[0] > 0:
                                        num_aoi_coverd_link = num_aoi_coverd_link + 1
                                        break

                    if num_aoi_coverd_link > 0:
                        contain_flag = 1
                        break
                contain_flag = 1 - contain_flag
                if num_link_uncoverd > 10:
                    contain_flag = 1
        if cover_flag == 1 and contain_flag == 0:
            sql = "update nav_link_inner_group set covered = 1 where group_id = '%s'" % group_id  # 1代表被现有aoi压盖的内部路组数据
            cursor.execute(sql)
        else:
            sql = "update nav_link_inner_group set covered = 2 where group_id = '%s'" % group_id  # 2代表可生成疑似aoi区域的内部路组数据
            cursor.execute(sql)
    conn.commit()
    print(time.time())
    conn.close()


if __name__ == "__main__":
    batchid = sys.argv[1]
    judge_covered(batchid)
