# !/usr/bin/env python3
"""
解析图像检测参数
"""
import json
import os.path
from typing import Dict, <PERSON><PERSON>, List
import sys

sys.path.insert(0, '.')

from src.tools.utils import detect_bbox_to_polygon

base_path = './'
file_i = 'f'


def read_bbox():
    """
    解析bbox数据
    """
    imageIdPathMap = read_image_id_to_path()
    dataWkt = read_img_geom()
    inner_road_wkt = read_inner_road()
    with open(base_path + "script/inner_roads/data_{}/bbox_{}.json".format(file_i, file_i)) as f:
        data = json.load(f)
        sampledData = []
        for index, item in enumerate(data):
            pathItem = imageIdPathMap.get(str(item['image_id']), "")
            if pathItem == "":
                # 没有图片id
                continue
            data[index]['image_name'] = pathItem['img']
            data[index]['image_width'] = pathItem['width']
            data[index]['image_height'] = pathItem['height']
            key = str(data[index]['image_name']).replace(".jpg", "")
            data[index]['image_batch'] = key
            val = dataWkt.get(key, "")
            # 没有识别结果
            if val == "":
                continue

            inner_wk = inner_road_wkt.get(key, '')
            # 没有内部路结果
            if inner_wk == '':
                continue
            if data[index]['score'] < 0.3:
                # 识别结果小于0.3 先不处理
                continue
            data[index]['img_wkt'] = val
            data[index]['inner_wkt'] = inner_wk
            data[index]['bbox_geom'] = detect_bbox_to_polygon(val, data[index]['bbox'], data[index]['image_width'],
                                                              data[index]['image_height'])
            sampledData.append(data[index])
    return sampledData


def read_img_geom() -> Dict[str, str]:
    """
    读取图片对应的geom
    """
    with open(base_path + 'script/inner_roads/data/img_polygon.csv') as f:
        line = f.read().split("\n")
        dataDict = {}
        for i in line:
            item = i.split(",", 1)
            if len(item) < 2:
                continue
            dataDict[item[0]] = item[1]
        return dataDict


def read_image_id_to_path() -> Dict[str, Dict]:
    """
    图片id于路径的对应关系
    """
    with open(base_path + 'script/inner_roads/data_{}/img_id_to_path2_{}.json'.format(file_i, file_i)) as f:
        data = json.load(f)
        return data


def read_inner_road() -> Dict[str, Dict]:
    """
    内部路
    """
    with open(base_path + 'script/inner_roads/data/inner_road_14w.json') as f:
        data = json.load(f)
        inner_road_map = {}
        for i in data:
            inner_road_map[i['uuid']] = i['poly_geom']
        return inner_road_map


def handle() -> None:
    bboxData = read_bbox()
    meshDict = {}
    for i in bboxData:
        if meshDict.get(i['image_batch'], None) is None:
            meshDict[i['image_batch']] = [i]
        else:
            meshDict[i['image_batch']].append(i)
    with open('inner_road_export_{}.json'.format(file_i), 'w') as f:
        json.dump(meshDict, f, indent=2)


if __name__ == '__main__':
    handle()
