# !/usr/bin/env python3
"""
目标检测导出图片的宽高坐标
"""
import json
from PIL import Image
new_id_to_path = {}
with open('image_id_to_path.json', 'r') as f:
    img_data = json.load(f)
    for k, path in img_data.items():
        try:
            img = Image.open(path)
        except Exception as e:
            print(e.args)
            continue
        new_id_to_path[k] = {"img": path, "width": img.width, "height": img.height}

with open('img_id_to_path2.json', 'w') as f2:
    json.dump(new_id_to_path, f2, indent=2)
