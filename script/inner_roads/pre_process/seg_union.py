# !/usr/bin/env python3
"""
语义分割前处理
"""
import json
import os
import sys
from glob import glob
from multiprocessing import Pool
from warnings import simplefilter

root_path = os.path.abspath(os.path.dirname(__file__) + "/../../../")
sys.path.insert(0, root_path)
import tqdm
from shapely import wkt
from shapely.validation import make_valid

from src.tools.aoi_tools import get_area

import rtree


def seg_data_merge(json_file):
    """
    生成合并后的文件
    :param street_faceid:
    :param features:
    :return:
    """
    file_path = os.path.dirname(json_file)
    joined_path = os.path.abspath(file_path + "/../vectorize_info_joined")
    os.makedirs(joined_path, exist_ok=True)
    with open(json_file, 'r') as json_fp:
        json_data = json.load(json_fp)
        street_faceid = json_data.get('id', '')
        features = json_data.get('features', [])
        if len(features) < 1:
            print("无数据,结束")
        else:
            current_dict = {}
            joined_index = set()
            # 把散落的数据合并成一个
            for index1, feature1 in enumerate(features):
                if index1 in joined_index:
                    continue
                tags = feature1.get('tags', [])
                sub_area = []
                shape1 = make_valid(wkt.loads(feature1['geom']))
                sub_area.append(shape1.wkt)
                for index2, feature2 in enumerate(features):
                    if index1 == index2 or index2 in joined_index:
                        continue
                    shape2 = make_valid(wkt.loads(feature2['geom']))
                    if shape1.intersection(shape2):
                        shape1 = shape1.union(shape2)
                        joined_index.add(index2)
                        sub_area.append(shape2.wkt)
                        for t in feature2.get('tags', []):
                            tags.append(t)
                area = get_area(shape1.wkt)
                if area < 2000:
                    print(street_faceid, index1, f"area:{area} small, filtered")
                    continue
                current_dict[index1] = {
                    "wkt": shape1.wkt, "area": get_area(shape1.wkt), "tags": tags,
                    'sub_area': sub_area
                }
                joined_index.add(index1)
            if len(current_dict) < 1:
                return
            with open(f"{joined_path}/{street_faceid}.json", 'w') as f:
                json.dump(current_dict, f)


def do_handle_seg_data():
    """
    处理序列化后的语义分割数据
    :return:
    """
    JSON_PATH = \
        f"/home/<USER>/chenbaojun/products/b1/inner_roads_batch3/output_vectorize/vectorize_info"
    file_list = glob(JSON_PATH + "/*.json")
    with Pool(10) as p:
        _ = list(tqdm.tqdm(p.imap(seg_data_merge, file_list), total=len(file_list)))


inner_roads_item = []
un_matched = []


class UnionInnerRoad:

    def __init__(self):
        self.seg_item_list = []
        self.tree = rtree.index.Index(interleaved=False)
        self.build_rtree()

    def build_rtree(self):
        print("生成rtree")
        index = 0
        file_list = glob(
            "/home/<USER>/chenbaojun/products/b1/inner_roads_batch2/output_vectorize/vectorize_info_joined/*.json")
        for file_name in tqdm.tqdm(file_list, total=len(file_list)):
            face_id = file_name[-37:-5]
            with open(file_name) as f:
                for face_index, item2 in json.load(f).items():
                    item2['face_id'] = face_id
                    x_min, y_min, x_max, y_max = wkt.loads(item2['wkt']).envelope.bounds
                    self.tree.insert(index, (x_min, x_max, y_min, y_max))
                    self.seg_item_list.append(item2)
                    index += 1
        print("生成rtree结束")

    def __call__(self, i):
        inner_shape = wkt.loads(i['poly_geom'])
        ix_min, iy_min, ix_max, iy_max = inner_shape.envelope.bounds
        i_index = self.tree.intersection((ix_min, ix_max, iy_min, iy_max))
        union_list = []
        for i2 in i_index:
            union_list.append(self.seg_item_list[i2])
        if len(union_list) < 1:
            # 没关联上
            un_matched.append(i['poly_geom'] + "\n")
            return
        tmp = {
            "road_item": i,
            "union_list": union_list
        }
        inner_roads_item.append(tmp)


def data_union_inner_road():
    """
    将内部路和导出数据进行合并，这一步很快，不需要多进程
    :return:
    """
    u = UnionInnerRoad()
    # 读取top40 数据
    with open(f"{root_path}/inner_roads_top40.json") as f2:
        inner_roads_data = json.load(f2)
        for item in tqdm.tqdm(inner_roads_data):
            u(item)
    with open('seg_road_union_batch2.json', 'w') as f3:
        json.dump(inner_roads_item, f3, indent=2, ensure_ascii=False)
    # with open('seg_road_unjoined_1207.txt', 'w') as f4:
    #     f4.writelines(un_matched)


def create_street_region_json():
    """
    生成街区区域缓存文件
    :return:
    """
    all_data = {}
    file_list = glob("/*.json")
    for i in tqdm.tqdm(file_list):
        with open(i) as fp:
            json_obj = json.load(fp)
            _id = json_obj['id']
            geom = json_obj['geom']
            all_data[_id] = geom

    with open("street_region_top40_1202.json", 'w') as f:
        json.dump(all_data, f, indent=1)


if __name__ == '__main__':
    simplefilter(action='ignore', category=FutureWarning)
    # do_handle_seg_data()
    data_union_inner_road()
    # create_street_region_json()
