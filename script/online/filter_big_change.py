# !/usr/bin/env python3
"""
过滤识别与策略修型的iou<50的识别数据
"""
import json
import sys
import os
from warnings import simplefilter
from multiprocessing import Pool
import tqdm

PATH1 = os.path.abspath(os.path.dirname(__file__) + "/../../")

if PATH1 not in sys.path:
    sys.path.insert(0, PATH1)

from src.tools import aoi_tools

simplefilter(action='ignore', category=FutureWarning)


def run_iou(item):
    score = item['score']
    # 返回值： 是否>90分， 是否iou > 0.5， 具体的item值
    if score < 90:
        return False, False, None
    else:
        wkt1 = item['wkt']
        wkt2 = item['info_wkt']
        rate = aoi_tools.calc_iou(wkt1, wkt2)
        if rate > 0.5:
            return True, True, item
        else:
            return True, False, None


def filter_iou_less_50(json_file: str):
    with open("json_file") as f:
        data = json.load(f)
        all_ct = 0
        filtered = 0
        target_list = []
        with Pool(5) as p:
            for i in list(tqdm.tqdm(p.imap(run_iou, data), total=len(data))):
                score90, iou50, item = i
                if not score90:
                    continue
                all_ct += 1
                if not iou50:
                    filtered += 1
                    continue
                target_list.append(item)

    with open(json_file.replace(".json", "") + "_iou_filtered.json", 'w') as f2:
        json.dump(target_list, f2, ensure_ascii=False, indent=2)

    print(f"总数:{all_ct}, 过滤:{filtered}")


if __name__ == '__main__':
    filter_iou_less_50(
        "/home/<USER>/chenbaojun/online_json/poitag_41_200/20230214093500_38_95_sorted_t_feed_poi_tag_41_200.tar.json")
