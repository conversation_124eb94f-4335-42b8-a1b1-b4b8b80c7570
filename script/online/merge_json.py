# !/usr/bin/env python3
"""
合并与生成uuid
"""
import argparse
import json
from pathlib import Path

from glob import glob


def statistics(file_path):
    """
    执行统计
    :return:
    """
    with open(file_path) as f:
        data = json.load(f)
        ia = len(data)
        i1 = 0
        i2 = 0
        i3 = 0
        for i in data:
            if i['score'] >= 90:
                i1 += 1
            elif i['score'] >= 80:
                i2 += 1
            else:
                i3 += 1
        print(f"总数:{ia}, 90分以上{i1}, 80分以上{i2}, 80分以下{i3}")


def main(json_path):
    all_list = []
    data = glob(json_path + "/*.json")
    exported = json_path.rstrip("/") + "_sorted.json"
    for i in data:
        with open(i, 'r') as f:
            json_arr = json.load(f)
            for j in json_arr:
                if j['wkt'] == "":
                    continue
                all_list.append(j)
    sorted_list = sorted(all_list, key=lambda x: x["score"], reverse=True)
    print(f"输出到文件:{exported}")
    with open(exported, 'w') as f2:
        json.dump(sorted_list, f2, indent=2, ensure_ascii=False)
    statistics(exported)


def run_main(json_dir_path)->str:
    main(json_dir_path)
    return json_dir_path.rstrip("/") + "_sorted.json"


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--jsonpath", required=True, type=str, help="json数据文件夹")
    args = parser.parse_args()
    jsonpath = str(Path(args.jsonpath))
    main(jsonpath)
