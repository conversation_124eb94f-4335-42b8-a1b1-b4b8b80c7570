# !/usr/bin/env python3
"""
没有poi的aoi边框上线
"""
import argparse
import json
import sys
import os
import hashlib
from pathlib import Path
from typing import Any

import tqdm
from osgeo import ogr

sys.path.insert(0, os.path.abspath(os.path.dirname(__file__) + "/../../"))
from src.model.aoi_model import AoiModel
from src.tools.city_tools import meshid_to_city
from src.tools import linq


# 处理面相交数据
class Aoi:
    def __init__(self, city: str, aoi_id: str, aoi_tag: str, geom: Any, useless=False):
        self.city: str = city
        self.aoi_id: str = aoi_id
        self.aoi_tag: str = aoi_tag
        self.geom: Any = geom
        self.useless: bool = useless


def read_aoi_items(file_path: Path):
    lines = file_path.read_text(encoding='utf8').split('\n')
    for line in lines:
        if not line:
            continue

        columns = [x for x in line.split('\t') if x]
        yield Aoi(
            columns[0],
            columns[2],
            columns[3],
            ogr.CreateGeometryFromWkt(columns[1])
        )


def get_intersection_aoi(aoi_list):
    for i in range(len(aoi_list)):
        for j in range(i + 1, len(aoi_list)):
            a = aoi_list[i]
            b = aoi_list[j]
            if a.geom.Intersects(b.geom):
                yield a, b


def normalize(geom):
    buffer = 3e-5
    return geom.Buffer(-buffer).Buffer(buffer)


def flatten(geom):
    count = geom.GetGeometryCount()
    if count < 2:
        return [geom]

    result = []
    for i in range(count):
        result.append(geom.GetGeometryRef(i))

    return result


def max_geom(geom_list):
    max_area_geom = None
    max_area = 0
    for geom in geom_list:
        if not max_area_geom:
            max_area_geom = geom
            max_area = geom.Area()
            continue

        area = geom.Area()
        if area > max_area:
            max_area_geom = geom
            max_area = area

    return ogr.CreateGeometryFromWkt(str(max_area_geom))


def get_iou(geom1, geom2):
    intersection_area = geom1.Intersection(geom2).Area()
    union_area = geom1.Union(geom2).Area()
    return intersection_area / union_area


def resolve_intersection(file_path: Path):
    count_dict = {
        'intersection': 0,
        'contain': 0,
        'too much change': 0,
        'multi polygon': 0,
    }
    aoi_group_dict = linq.group_by(read_aoi_items(file_path), lambda x: x.city)
    abnormal_cases = []
    for _, aoi_list in tqdm.tqdm(aoi_group_dict.items()):
        for aoi1, aoi2 in get_intersection_aoi(aoi_list):
            count_dict['intersection'] += 1
            # 处理包含关系
            if aoi1.geom.Contains(aoi2.geom):
                count_dict['contain'] += 1
                aoi2.useless = True
                continue

            if aoi2.geom.Contains(aoi1.geom):
                count_dict['contain'] += 1
                aoi1.useless = True
                continue

            # 处理压盖关系
            area1 = aoi1.geom.Area() * 1e10
            area2 = aoi2.geom.Area() * 1e10

            mut_aoi, immut_aoi = (aoi1, aoi2) if area1 > area2 else (aoi2, aoi1)
            mut_aoi_geom = mut_aoi.geom
            mut_aoi.geom = mut_aoi.geom.Difference(immut_aoi.geom.Buffer(1e-6))

            # 处理多碎片问题
            if mut_aoi.geom.GetGeometryType() == ogr.wkbMultiPolygon:
                count_dict['multi polygon'] += 1
                geoms = flatten(mut_aoi.geom)
                mut_aoi.geom = max_geom(geoms)

            mut_aoi.geom = normalize(mut_aoi.geom)
            if get_iou(mut_aoi_geom, mut_aoi.geom) < 0.9:
                count_dict['too much change'] += 1
                mut_aoi.useless = True
                abnormal_cases.append(mut_aoi)

    abnormal_cases_output_path = Path(file_path.parent / f'{file_path.stem}_abnormal_cases.txt')
    repaired_output_path = Path(file_path.parent / f'{file_path.stem}_repaired.txt')

    abnormal_cases_output_path.write_text('\n'.join([str(x.geom) for x in abnormal_cases]))

    lines = [f'\t\t\t{aoi.city}\t\t{aoi.geom}\t{aoi.aoi_id}\t{aoi.aoi_tag}'
             for aoi_list in aoi_group_dict.values()
             for aoi in aoi_list
             if not aoi.useless and '),(' not in aoi.geom]  # 过滤polygon包含多个子区域
    repaired_output_path.write_text('\n'.join(lines), encoding='utf8')
    print("可上线的文件:" + str(repaired_output_path))
    print('\n'.join([f'- {k}: {v}' for k, v in count_dict.items()]))


def get_city(aoi_model: AoiModel, wkt_str: str):
    """
    获取城市数据，通过wkt查询
    :param aoi_model:
    :param wkt_str:
    :return:
    """
    mesh_id_arr = aoi_model.get_mesh_id(wkt_str, 1)
    if len(mesh_id_arr) < 1:
        return ""
    mesh_id = mesh_id_arr[0][0]
    if ',' in mesh_id:
        mesh_id = mesh_id.split(",")[0]
    city = meshid_to_city(mesh_id)
    return city


def main(json_file):
    aoi_model = AoiModel()
    # 需要上线的数据
    lines = []
    with open(json_file, 'r') as wf:
        data = json.load(wf)
        for i in tqdm.tqdm(data):
            if i['score'] < 90:
                # 跳过90分以下的数据
                continue
            if "city" in i and i['city'] != "":
                city = i['city']
            else:
                city = get_city(aoi_model, i['wkt'])
            if not city:
                continue
            # face_id 根据wkt 生成，避免重复处理
            face_id = hashlib.md5(str(i['wkt']).encode("utf8")).hexdigest()
            #  bid, mid, name, city, parent_bid, wkt, face_id, memo
            lines.append("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t\n".
                         format("", "", "", city, "", i['wkt'], face_id, "未绑定poi上线"))

    with open(json_file + "_online", 'w') as f:
        f.writelines(lines)
    file_path = json_file + "_online"
    # 处理面相交
    resolve_intersection(Path(file_path))


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--json", required=True, type=str, help="json数据")
    args = parser.parse_args()
    json_f = str(Path(args.json))
    main(json_f)
