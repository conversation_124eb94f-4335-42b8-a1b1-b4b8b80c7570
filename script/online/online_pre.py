# !/usr/bin/env python3
"""
上线数据预处理
"""
import argparse
import json
import sys
from pathlib import Path

sys.path.insert(0, ".")

from src.tools.pg_tool import PgTool

city_list = [
    '北京市',
    '广州市',
    '深圳市',
    '成都市',
    '杭州市',
    '武汉市',
    '西安市',
    '重庆市',
    '东莞市',
    '上海市',
    '南京市',
    '郑州市',
    '苏州市',
]


def get_poi_info(poi_cursor, bid):
    """
    获取poi信息
    """
    sql = "select bid, mid, name, city from poi where bid = '{}';".format(bid)
    poi_cursor.execute(sql)
    return poi_cursor.fetchone()


def main(json_file: str):
    pgtool = PgTool()
    with open(json_file, 'r') as wf:
        data = json.load(wf)
        for i in data:
            if i['score'] < 75:
                # 跳过75分以下的数据
                continue
            res = get_poi_info(pgtool.conn_poi.cursor(), i['poi_bid'])
            if res is None or res[3] in city_list:
                continue
            print("{}\t{}\t{}\t{}\t{}\t{}".format(res[0], res[1], res[2], res[3], "", i['wkt']))


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--json", required=True, type=str, help="json数据")
    args = parser.parse_args()
    json_f = Path(args.json).__str__()
    main(json_f)
