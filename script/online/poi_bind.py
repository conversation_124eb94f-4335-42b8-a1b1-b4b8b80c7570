# !/usr/bin/env python3
"""
poi绑定
"""
import argparse
import json
import os.path
import sys
from pathlib import Path

import tqdm
from shapely import wkt

ROOT_PATH = os.path.abspath(os.path.dirname(__file__) + "/../../")
sys.path.insert(0, ROOT_PATH)

from src.model.aoi_model import AoiModel

# 文件地址
MIN_SCORE = 85
MAY_TAGS = ['公司企业;园区']


def main(file_path):
    m = AoiModel()

    def bind_poi_info(x):
        name, std_tag, bid, relation_bid, point, click_pv = x
        d = m.multi_get_poi_by_bids([relation_bid])
        if len(d) < 1:
            return None
        check_bid = []
        for y in d:
            check_bid.append(y[2])
            check_bid.append(y[3])
        aoi_list = [m.get_aoi_by_bid(i) for i in check_bid if m.get_aoi_by_bid(i) is not None]
        maps = {"name": name, "std_tag": std_tag, "bid": bid, "relation_bid": relation_bid, "relation_info": d,
                "point": point, 'click_pv': click_pv, 'invalid': '0'}
        if len(aoi_list) > 0:
            maps['invalid'] = '1'
            maps['sub_aois'] = aoi_list
        return maps

    bind_map = {}
    not_bind_poi = []
    can_online = []
    with open(file_path) as f:
        data = json.load(f)
        for item in tqdm.tqdm(data):
            if item['score'] < MIN_SCORE:
                continue
            if len(item['may_pois']) < 1 and len(item['poi_bid']) > 2:
                can_online.append(item)
                continue
            if len(item['may_pois']) > 0:
                bid_list = [x['bid'] for x in item['may_pois']]
                ret = m.multi_get_poi_by_bids(bid_list)
                has_relation = []
                for y in ret:
                    if len(str(y[3])) < 2:
                        continue
                    bind_poi = bind_poi_info(y)
                    if bind_poi is None:
                        continue
                    has_relation.append(bind_poi)
                if len(has_relation) > 0:
                    bind_map[item['id']] = {
                        "relation": has_relation,
                        "wkt": item['wkt'],
                        "id": item['id'],
                        "may_pois": item['may_pois'],
                        "tags": item['tags'],
                    }
                    continue
            not_bind_poi.append(item)
    with open(file_path + "_can_online", 'w') as f_online:
        json.dump(can_online, f_online, indent=2, ensure_ascii=False)
    with open(file_path + "_bind", 'w') as f2:
        json.dump(bind_map, f2, indent=2, ensure_ascii=False)
    with open(file_path + "_not_bind", 'w') as f3:
        json.dump(not_bind_poi, f3, indent=2, ensure_ascii=False)
    poi_bind_check(file_path + "_bind")


def poi_bind_check(bind_json_file):
    ret_map = []
    with open(bind_json_file) as f:
        json_data = json.load(f)
        for gid, item in tqdm.tqdm(json_data.items()):
            relation = item['relation']
            parent_info = {}
            invalid = False
            for i in relation:
                if i['invalid'] == '1':
                    invalid = True
                    break
                for j in i['relation_info']:
                    name = j[0]
                    tag = j[1]
                    bid = j[2]
                    parent_bid = j[3]
                    point = j[4]
                    click_pv = j[5]
                    if wkt.loads(item['wkt']).contains(wkt.loads(point)):
                        added_pv = 0
                        if tag in MAY_TAGS:
                            added_pv = 100000
                        if bid in parent_info:
                            parent_info[bid]['click_pv'] = 100000 + parent_info[bid]['click_pv'] + added_pv
                        else:
                            parent_info[bid] = {"name": name, "tag": tag, "bid": bid, "parent_bid": parent_bid,
                                                'click_pv': click_pv + added_pv}
                sorted_list = sorted(parent_info.values(), key=lambda x: x["click_pv"], reverse=True)
            if len(sorted_list) > 0 and sorted_list[0]['click_pv'] > 100000 and not invalid:

                item['poi_name'] = sorted_list[0]['name']
                item['poi_bid'] = sorted_list[0]['bid']
                item['id'] = gid
                del item['may_pois']
                del item['relation']
                ret_map.append(item)
                with open(bind_json_file + "_bind2", 'w') as f2:
                    json.dump(ret_map, f2, indent=2, ensure_ascii=False)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--json", required=True, type=str, help="json数据地址")
    args = parser.parse_args()
    json_file = Path(args.json).__str__()
    main(json_file)
