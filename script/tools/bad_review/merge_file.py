# !/usr/bin/env python3
import json
import os.path
import sys
import glob

import tqdm

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)
from src.model.aoi_model import AoiModel
from shapely import wkt

bid_list = []

# 任务列表文件
BID_FILE = "/home/<USER>/chenbaojun/diff_new_old/export_company.txt"
# 街区文件
STREET_DIR = "/home/<USER>/chenbaojun/diff_new_old/diff_export_company/json_info/image/*.json"
# 矢量化文件夹
VEC_FILE = "/home/<USER>/chenbaojun/diff_new_old/20230204211200_17_24_20230204210600_22_product_diff_export_company.tar.json"
# 识别文件
ML_FILE = "/home/<USER>/chenbaojun/diff_new_old/20230204211500_17_25_sorted_t_diff_export_company.tar.json"
# 输出
OUT_FILE = "/home/<USER>/chenbaojun/diff_new_old/merged_vis_diff_company.json"
IMG_DIR = "vis_diff_export_company"

def get_intelligence(bid, aoi_model: AoiModel) -> str:
    """
    获取竞品
    :return:
    """
    cursor = aoi_model.conn_poi.cursor()
    sql = "select bid, st_astext(geom) from aoi_intelligence_history where " \
          f" bid='{bid}' limit 1"
    cursor.execute(sql)
    # 查询竞品
    info_wkt = ""
    ret = cursor.fetchone()
    if ret:
        info_wkt = ret[1]
    return info_wkt


with open(BID_FILE) as f:
    """
    读取bid列表
    """
    lines = f.read().split("\n")
    bid_list = [x.strip() for x in lines if x]


def get_street_map():
    m = {}
    dirs = [STREET_DIR]
    for json_dirs in dirs:
        for json_file in glob.glob(json_dirs):
            with open(json_file, "r") as f:
                data = json.load(f)
                m[data['id']] = data['geom']
    return m


def get_vec_map():
    m = {}
    with open(VEC_FILE) as f:
        for item in json.load(f):
            m[item['uuid']] = item['geom']
    return m


def get_all_ml_data():
    """
    读取
    :return:
    """
    all_json_list = []
    files = [ML_FILE]
    for json_file in files:
        with open(json_file, 'r') as f:
            json_list = json.load(f)
            for j in json_list:
                all_json_list.append(j)
    print(len(all_json_list))
    return all_json_list


aoi_model = AoiModel()
street_list = get_street_map()
ml_json_list = get_all_ml_data()
all_list = bid_list
vec_map = get_vec_map()

# 导出的json
export_json = []
for i in tqdm.tqdm(all_list):
    poi_name = ""
    poi_city = ""
    geom = aoi_model.get_geom_by_bid(i)
    conn = aoi_model.get_conn_poi()
    cursor = conn.cursor()
    sql = f"select name,city from poi where bid='{i}'"
    cursor.execute(sql)
    ret = cursor.fetchone()
    if ret:
        poi_name = ret[0]
        poi_city = ret[1]

    if not geom or geom == "":
        continue
    aoi_street_id = ""
    # 查询街区id
    for street_id, street_geom in street_list.items():
        if wkt.loads(street_geom).intersects(wkt.loads(geom)):
            aoi_street_id = street_id
            break

    if not aoi_street_id:
        continue
    print(f"{i}:{aoi_street_id}")
    info_wkt = get_intelligence(i, aoi_model)
    # 查询aoi
    aoi_wkt = ""
    aoi_info = aoi_model.get_aoi_by_bid(i)
    if aoi_info:
        aoi_wkt = aoi_info[2]
    # print(f"{i}:{aoi_wkt}")
    ml_wkt = ""
    ml_score = 0
    ml_id = ""
    # 比对结果
    vec_wkt = ""
    vec_id = ""
    for t_vec_id, t_vec_geom in get_vec_map().items():
        if wkt.loads(t_vec_geom).contains(wkt.loads(geom)):
            vec_wkt = t_vec_geom
            vec_id = t_vec_id
            break

    for ml in ml_json_list:
        if wkt.loads(ml['wkt']).contains(wkt.loads(geom)):
            ml_wkt = ml['wkt']
            ml_score = ml['score']
            ml_id = ml['id']
            break

    tmp = {
        "id": i,
        # 分数
        "score": ml_score,
        "memo": f"点:{geom}",
        # poi名称
        "poi_name": poi_name,
        # 城市
        "city": poi_city,
        "imgLabel": f"http://gzbh-ns-map-na002.gzbh.baidu.com:8090/aoi_ml/chenbaojun/products/{IMG_DIR}/added_prediction/{aoi_street_id}.jpg",
        "wkt": ml_wkt,
        # 竞品
        "info_wkt": aoi_wkt,
        "ml_id": ml_id,
        # 矢量化wkt
        "vec_wkt": vec_wkt,
        "vec_id": vec_id,
    }
    export_json.append(tmp)

with open(OUT_FILE, 'w') as f:
    json.dump(export_json, f, ensure_ascii=False, indent=2)
