# !/usr/bin/env python3
"""
任务流：结束子任务并生成下一环节任务
"""
import os
import sys
import json
from typing import Dict

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.const.work_flow import ConstWorkFlow


def get_subtasks_by_wpf_id(model: AoiMlFlowModel, wpf_id):
    """
    获取子任务
    :param task_id: 任务详情
    :return:
    """
    with model.connection.cursor() as cursor:
        sql, args = model.create_query_sql(
            "aoi_ml_work_package_flow",
            "wpf_id, wpf_task_id, wpf_section_name,wpf_status, wpf_flow_info, wpf_memo_info",
            {"wpf_id=": wpf_id})
        cursor.execute(sql, args)
        data_list = cursor.fetchone()
        return data_list


def handle_complete(subtask_id, extra_params: Dict):
    """
    手动设置任务成功,并且继续往下执行
    :param subtask_id:
    :param extra_params: 需要覆盖的参数
    :return:
    """
    with AoiMlFlowModel() as af:
        data = get_subtasks_by_wpf_id(af, subtask_id)
        if not data:
            print("无数据")
            return
        task_id = data[1]
        wpf_section_name = data[2]
        wpf_status = data[3]
        wpf_flow_info = data[4]
        if wpf_status == ConstWorkFlow.FLOW_STATUS_SUCCESS:
            print("流程已经成功，无需处理")
            return
        task_info = af.get_task_by_id(task_id, "task_type")
        task_type = task_info[0]
        wpf_flow_info = json.loads(wpf_flow_info)
        wpf_flow_info = dict(wpf_flow_info, **extra_params)
        params = json.dumps(wpf_flow_info, ensure_ascii=False)
        af.set_subtask_success(subtask_id, task_id, params)
        next_task = ConstWorkFlow.get_next_subtask(task_type, wpf_section_name)
        af.create_next_subtask(subtask_id, task_id, next_task, params)
        print("成功")


if __name__ == '__main__':
    pass
    # extra_params = {
    #     "afs_url": "/user/map-data-streeview/aoi-ml/flow_data/aoi_seg_post_job/20230215101500_47_96_20230213213600_91_product_seg_scan_成都市.tar.json"
    # }
    # handle_complete(96, extra_params)
