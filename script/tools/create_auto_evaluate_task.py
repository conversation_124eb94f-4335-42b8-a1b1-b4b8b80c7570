# !/usr/bin/env python3
"""
创建语义分割+后处理自动化脚本任务+生产评估数据
"""
import os
import sys
import json

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.const.work_flow import ConstWorkFlow

# TODO: 在此处添加数据集的 AFS 路径
AFS_URL_LIST = [
    'TODO: AFS dataset path',
]
MODEL_ID_LIST = [
    'model_multi_channel',
    'model_vis',
    'zyxpaddleseg',
    'paddleseg_vis_v3',
]
DONT_FILTER_ONLINE_AOI = '0'
FILTER_ONLINE_AOI = '1'


def create_task(afs_url, model_id):
    with AoiMlFlowModel() as a:
        # 评估任务全流程
        task_dict = {
            "task_name": ConstWorkFlow.TASK_TYPE_MODEL_EVALUATE,
            "task_type": ConstWorkFlow.TASK_TYPE_MODEL_EVALUATE,
            "task_content": json.dumps({
                "afs_url": afs_url,
                "filter_online_aoi": DONT_FILTER_ONLINE_AOI,
                "model_type": model_id,
                "exec_machine": {
                    # 评估模型，指定任务必须是na002， 其他机器先不部署
                    ConstWorkFlow.FLOW_SUBTASK_EXEC_SEG: "na002",
                },
            }, ensure_ascii=False),
            "task_current_flow": ConstWorkFlow.FLOW_SUBTASK_START,
            "task_status": ConstWorkFlow.FLOW_STATUS_WAIT,
            # 处理优先级 [-128, 127]，越大越先执行, mysql数据字段是tinyint
            "task_priority": 0,
        }
        a.create_flow_task(task_dict)


def main():
    for afs_url, model_id in [(x, y) for x in AFS_URL_LIST for y in MODEL_ID_LIST]:
        print(f'[TASK] {model_id}: {afs_url}')
        create_task(afs_url, model_id)
        print("[TASK] Success!")


if __name__ == '__main__':
    main()
