# !/usr/bin/env python3
"""
创建语义分割+后处理自动化脚本任务
"""
import os
import sys
import json

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.const.work_flow import ConstWorkFlow

with AoiMlFlowModel() as a:
    # 执行语义分割全流程
    task_dict = {
        "task_name": ConstWorkFlow.TASK_TYPE_EXEC_AOI_SEG,
        "task_type": ConstWorkFlow.TASK_TYPE_EXEC_AOI_SEG,
        "task_content": json.dumps({
            "afs_url": "/user/map-data-streeview/aoi-ml/feeds/bad_review/diff_export_company.tar",
            # 过滤在线的任务 0不过滤，1 过滤
            "filter_online_aoi": "1",
            # 新模型
            "model_type": "model_vis"
            # 新模型2
            # "model_type": "zyxpaddleseg",
            # "model_type": "paddleseg_vis_v3",
            # 旧模型
            # "model_type": "model_multi_channel",
            # 新增 sdk 模型， 有v1和v2两个版本，这两个版本只能在rpm01 上跑，必须指定机器名称
            # "model_type": "border_recognition_sdk_v1_1",
            # "model_type": "border_recognition_sdk_v2_1",
            # # 指定执行的机器,支持 * 或者指定的名称,
            # "exec_machine": {
            #     ConstWorkFlow.FLOW_SUBTASK_EXEC_SEG: "rpm01",
            # },
            # 按照优先级进行处理，优先级越大越先处理
            # "task_priority": 0,
        }, ensure_ascii=False),
        "task_current_flow": ConstWorkFlow.FLOW_SUBTASK_START,
        "task_status": ConstWorkFlow.FLOW_STATUS_WAIT,
        # 处理优先级 [-128, 127]，越大越先执行, mysql数据字段是tinyint
        "task_priority": 0,
    }
    a.create_flow_task(task_dict)
    print("任务添加成功，请不要重复添加")
