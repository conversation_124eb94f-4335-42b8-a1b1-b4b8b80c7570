# !/usr/bin/env python3
import argparse
import os
import sys
import time
from pathlib import Path

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.model.aoi_model import Aoi<PERSON><PERSON><PERSON>


def pg_check_query():
    with AoiModel() as a:
        now = time.strftime("%Y-%m-%d %H:%M:%S")
        geom = "POLYGON ((120.25441166664105 36.282998036660956, 120.25576737232937 36.28311143817766, 120.25581708901518 36.28213650434517, 120.25521898120873 36.28209811332923, 120.25521335428925 36.2820974844543, 120.25520781170165 36.2820963279816, 120.25520240280221 36.28209465420944, 120.25519717575673 36.28209247804261, 120.25519217711164 36.28208981885971, 120.25518745137946 36.28208670034054, 120.25518304064245 36.28208315025527, 120.25517898417786 36.282079200217105, 120.25517531810817 36.282074885400824, 120.25517207507946 36.28207024422951, 120.2551692839706 36.282065318032416, 120.2551669696362 36.28206015067693, 120.2551307221358 36.28196776274622, 120.25459623611987 36.28189469941019, 120.25439823923423 36.282968054166645, 120.25441166664105 36.282998036660956))"
        # 查询指定范围的内部路，检测nav_link是否可用
        ret = a.get_inner_line_by_geom(geom)
        print(f"=== {now} nav_link geom  check ok ====", len(ret))
        # 查询指定范围的poi，检测 poi的空间
        ret = a.get_poi_by_geom(geom)
        print(f"=== {now} poi geom check ok", len(ret))
        ret = a.multi_get_poi_by_bids(['10000554000097646615'])
        print(f"=== {now} poi bid check ok", len(ret))


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--item", required=True, type=str, help="json数据地址")
    args = parser.parse_args()
    check_item = str(Path(args.item))
    item_map = {
        "pg_check": pg_check_query,
    }
    if check_item in item_map:
        item_map.get(check_item)()
    else:
        print("无效的检测项")
