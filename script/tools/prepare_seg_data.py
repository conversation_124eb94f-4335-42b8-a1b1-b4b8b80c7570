# !/usr/bin/env python3
"""
产出支持vis语义分割和常规语义分割的数据
"""
import argparse
import os
import sys
import time

import tqdm

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

# 以情报点下载数据时，会直接使用 pg 出库，以情报点为中心，搜索半径为 500 米，索引目标类型要素。
SEARCH_RADIUS = 0.005

from src.model.aoi_model import AoiModel
import src.tools.aoi_tools as aoi_tools
import src.tools.function as F
from src.pre_process import split_street
from src.tools import machine_group
from src.tools.afs_tool import AfsTool
from script.ttf_download import ttfc, ttfb, ttfa


class PrepareSegData:
    def __init__(self, bid_list_file, task_name, max_side_length=2048):
        self.task_name = task_name
        self.BID_LIST_FILE = bid_list_file
        self.out_path = ROOT_PATH + "/tmp/vis_seg_data/" + self.task_name
        self.limit = 0
        self.ttfb_cache_dir = f"{ROOT_PATH}/ttfb_cache/"
        self.ttfc_cache_dir = f"{ROOT_PATH}/ttfc_cache/"
        self.max_side_length = max_side_length

    def prepare_data(self):
        """
        生成bid列表
        :param bid_list_file: bid文件
        :param out_path: 输出的地址
        :param limit: 数据限制
        :return:
        """
        mesh_list = set()
        lines = []

        os.makedirs(self.out_path + "/ttfa", exist_ok=True)
        os.makedirs(self.out_path + "/ttfb", exist_ok=True)
        os.makedirs(self.out_path + "/ttfc", exist_ok=True)
        os.makedirs(self.ttfb_cache_dir, exist_ok=True)
        os.makedirs(self.ttfc_cache_dir, exist_ok=True)
        os.makedirs(f"{ROOT_PATH}/dataset", exist_ok=True)
        a = AoiModel()
        relation_lines = []
        current = 0
        self.clean()
        with open(self.BID_LIST_FILE) as f:
            line = f.read().split("\n")
            for i in tqdm.tqdm(line, desc="生成列表"):
                bid = str(i).strip()
                poi_info = a.multi_get_poi_by_bids([bid])
                if len(poi_info) < 1:
                    continue
                poi_info = poi_info[0]
                location = poi_info[4]
                xy = str(location).replace("POINT(", "").replace(")", "").split(" ")
                x = float(xy[0])
                y = float(xy[1])
                mesh_id = aoi_tools.cal_mesh_id(x, y)
                mesh_list.add(str(mesh_id))
                lines.append(location + "\n")
                relation_lines.append(f"{bid}\t{location}\t{mesh_id}\n")
                current += 1
                if 0 < self.limit < current:
                    break
        # 写列表
        region_file = self.out_path + "/list.txt"
        with open(region_file, 'w') as f2:
            f2.writelines(lines)
        with open(self.out_path + "/bid_relation.txt", 'w') as f3:
            f3.writelines(relation_lines)
        ttfa.download_by_intelligence(region_file, f"{self.out_path}/ttfa", radius=SEARCH_RADIUS)
        ttfb.download_by_intelligence(region_file, f"{self.out_path}/ttfb", radius=SEARCH_RADIUS)
        ttfc.download_by_intelligence(region_file, f"{self.out_path}/ttfc", radius=SEARCH_RADIUS)

        # 拆分街区
        print("==== 开始拆分街区")
        self.do_split_street()
        # 生产数据
        print("=== 开始生产数据")
        self.do_create()
        # 上传到afs
        self.package_and_upload_afs()

    __call__ = prepare_data

    def __enter__(self):
        return self

    def clean(self):
        F.exec_shell_cmd(f"cd {self.out_path}/ttfa;rm -rf ./*.ttfa; ")
        F.exec_shell_cmd(f"cd {self.out_path}/ttfb;rm -rf ./*.ttfb; ")
        F.exec_shell_cmd(f"cd {self.out_path}/ttfc;rm -rf ./*.ttfc; ")

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.clean()

    def do_split_street(self):
        F.exec_shell_cmd(f"{machine_group.init_envirement_script()}cd {ROOT_PATH};"
                         f" python3 src/pre_process/split_street.py"
                         f" --back-db-path {self.out_path}/ttfb"
                         f" --road-db-path {self.out_path}/ttfa"
                         f" --process-number {4 if machine_group.machine_opera() else 8} "
                         f" >>{ROOT_PATH}/dataset/data_download_{self.task_name}.txt 2>&1")

    def do_create(self):
        """
        生产数据,只能用2048尺寸
        :return:
        """

        F.exec_shell_cmd(f"{machine_group.init_envirement_script()}cd {ROOT_PATH};"
                         f"python3 src/pre_process/create_seg_dataset.py --id {self.task_name} --padding 30"
                         f" --max-side-length {self.max_side_length}"
                         f" --region-db-path {self.out_path}/ttfb/region --road-db-path {self.out_path}/ttfa "
                         f" --back-db-path {self.out_path}/ttfb --poi-db-path {self.out_path}/ttfc "
                         f" --output-path {ROOT_PATH}/dataset/ --cache-path {ROOT_PATH}/../cache/"
                         f" --region-file-path {self.out_path}/list.txt "
                         f" --raster-src http"
                         f" --raster-type Google2021 >>{ROOT_PATH}/dataset/data_download_{self.task_name}.txt 2>&1 ")

    def package_and_upload_afs(self):
        if not os.path.isdir(f"{ROOT_PATH}/dataset/{self.task_name}"):
            print("未生成tar文件")
            return
        F.exec_shell_cmd(f"cp {self.out_path}/list.txt {ROOT_PATH}/dataset/{self.task_name}/")
        F.exec_shell_cmd(f"cp {self.out_path}/bid_relation.txt {ROOT_PATH}/dataset/{self.task_name}/")
        F.exec_shell_cmd(f"cat {self.BID_LIST_FILE} >{ROOT_PATH}/dataset/{self.task_name}/{self.task_name}_bid_list.txt")
        F.exec_shell_cmd(f"{machine_group.init_envirement_script()}cd {ROOT_PATH}/dataset/;"
                         f"tar -cf {self.task_name}.tar ./{self.task_name}")
        afs_tool = AfsTool()
        afs_tool.put(f"{ROOT_PATH}/dataset/{self.task_name}.tar", "/user/map-data-streeview/aoi-ml/feeds/bad_review/")


if __name__ == '__main__':
    parser = argparse.ArgumentParser('Split street')
    parser.add_argument(
        '--bid-list',
        dest='bid_list',
        help='bid文件列表名称',
        required=True,
        type=str,
    )
    parser.add_argument(
        '--id',
        dest='id',
        help='数据集名',
        type=str,
        required=True
    )
    parser.add_argument(
        '--max-side-length',
        dest='max_side_length',
        help='最大图片宽高。',
        type=int,
        default=2048
    )
    args = parser.parse_args()
    with PrepareSegData(args.bid_list, args.id, args.max_side_length) as p:
        p()
