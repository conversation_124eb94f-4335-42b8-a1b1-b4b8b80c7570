# !/usr/bin/env python3
"""
任务流：修改当前子任务的参数并重置任务
"""
import os
import sys
import json
from typing import Dict

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.const.work_flow import ConstWorkFlow


def get_subtasks_by_wpf_id(model: AoiMlFlowModel, wpf_id):
    """
    获取子任务
    :param model 模型
    :param wpf_id: 任务详情
    :return:
    """
    with model.connection.cursor() as cursor:
        sql, args = model.create_query_sql(
            "aoi_ml_work_package_flow",
            "wpf_id, wpf_task_id, wpf_section_name,wpf_status, wpf_flow_info, wpf_memo_info",
            {"wpf_id=": wpf_id})
        cursor.execute(sql, args)
        data_list = cursor.fetchone()
        return data_list


def repair_data_and_set_default(subtask_id, extra_params: Dict):
    """
    手动设置任务成功,并且继续往下执行
    :param subtask_id:
    :param extra_params: 需要覆盖的参数
    :return:
    """
    with AoiMlFlowModel() as af:
        data = get_subtasks_by_wpf_id(af, subtask_id)
        if not data:
            print("无数据")
            return
        wpf_flow_info = data[4]
        wpf_flow_info = json.loads(wpf_flow_info)
        print(f"===== 任务 {subtask_id} 旧参数:", wpf_flow_info)
        wpf_flow_info = dict(wpf_flow_info, **extra_params)
        print(f"===== 任务 {subtask_id}  新参数:", wpf_flow_info)
        af.update_subtask_by_wpfid(subtask_id, {
            "wpf_status": ConstWorkFlow.FLOW_STATUS_WAIT,
            'wpf_flow_info': json.dumps(wpf_flow_info, ensure_ascii=False)})
        print("修改参数成功")


if __name__ == '__main__':
    pass
    # extra_params = {
    #     "filter_online_aoi": "0"
    # }
    # repair_data_and_set_default(136, extra_params)
