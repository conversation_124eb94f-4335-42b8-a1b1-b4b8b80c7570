# !/usr/bin/env python3
import requests
import json
import os
import sys

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
sys.path.insert(0, ROOT_PATH)

import src.tools.aoi_tools as at
from typing import List, Dict

import sqlite3
import argparse
import subprocess
from src.model.aoi_model import AoiModel
from tqdm import tqdm


# 以情报点下载数据时，会直接使用 pg 出库，以情报点为中心，搜索半径为 500 米，索引目标类型要素。
SEARCH_RADIUS = 0.005


def getDownloadUrl(mesh_ids: List[str]) -> Dict[str, str]:
    """
    下载ttfa地址, 图幅号id列表
    :return:
    """
    urlDict = {}
    url = "http://api.guoke.map.baidu.com/trunk/gknls/api/nutsvnopen"
    params = {
        "action": "pullttfa",
        "trunk_id": "master_road_ml",
        "version": "head",
        "mesh_list": ",".join(mesh_ids),
        "url_multi": 1,
        "caller": "aoi下载脚本",
        "op_type": 'r',
        "task_id": "trace_road_match",
        "owner": "aoi下载脚本",
        "from": "AOI",
        "token": "37b5db14839cea41",
        "mesh_type": 0,
    }
    req = requests.post(url, json.dumps(params))
    if req.status_code != 200:
        print("获取链接失败1")
        return urlDict
    resp = req.json()
    if resp['errno'] != 0:
        print("获取链接失败2")
        return urlDict
    return resp['data']


def download_by_city(city_name: str, path: str):
    """
    按照城市下载ttfa数据
    :param city_name 城市名称 如：上海市
    : 下载地址： 当前文件夹下 data/ttfa/文件名称
    :return 没有返回
    """
    mesh_list = at.get_mesh_list(city_name)
    download_dir = path + "/data/ttfa/" + city_name
    download_by_mesh_list(mesh_list, download_dir)
    proc = subprocess.Popen(f"cd {download_dir}; " +
                            "ls -al | grep zip | awk '{print $9}'  | xargs -I {} unzip {}; rm -f *.zip", shell=True)
    proc.wait()
    print(proc.args)


def download_by_mesh_list(mesh_list: List[str], download_dir: str):
    """
    根据图幅号下载，下载到指定的路径
    :param mesh_list 图幅号列表
    :param download_dir 下载地址
    """
    os.makedirs(download_dir, exist_ok=True)
    url_dict = getDownloadUrl(mesh_list)
    for mesh_id, url in url_dict.items():
        print("start download {}, url {}".format(mesh_id, url))
        data = requests.get(url).content
        with open(download_dir + "/" + mesh_id + ".ttfa.zip", 'wb') as f:
            f.write(data)


def download_by_intelligence(region_file_path: str, download_dir: str, radius=SEARCH_RADIUS):
    os.makedirs(download_dir, exist_ok=True)

    with AoiModel() as aoi_model:
        conn = aoi_model.get_conn_road()
        with conn.cursor() as cursor:
            with open(region_file_path, "r", encoding="utf8") as f:
                lines = list(f.readlines())
                download_nav_link_by_intelligence(cursor, lines, download_dir, radius)
                download_nav_node_by_intelligence(cursor, lines, download_dir, radius)

        conn.commit()
        conn.close()


def download_nav_link_by_intelligence(cursor, lines, download_dir: str, radius):
    record_dict = dict()

    for line in tqdm(lines, total=len(lines), desc='download_nav_link_by_intelligence'):
        wkt = line.rstrip()
        sql = f'''
            SELECT link_id, s_nid, e_nid, kind, ST_AsText(geom) 
            FROM nav_link 
            WHERE ST_dwithin(ST_GeomFromText('{wkt}', 4326), geom, {radius})
        '''
        cursor.execute(sql)
        for record in list(cursor.fetchall()):
            link_id = record[0]
            record_dict[link_id] = record

    save_nav_link_to_sqlite(download_dir + "/100000.ttfa", record_dict.values())


def download_nav_node_by_intelligence(cursor, lines, download_dir: str, radius):
    nav_node_record_dict = dict()
    nav_gate_record_dict = dict()

    for line in tqdm(lines, total=len(lines), desc='download_nav_node_by_intelligence'):
        wkt = line.rstrip()
        select_nav_node_sql = f'''
            SELECT node_id, form, ST_AsText(geom)
            FROM nav_node
            WHERE ST_dwithin(ST_GeomFromText('{wkt}', 4326), geom, {radius})
        '''
        cursor.execute(select_nav_node_sql)
        for nav_node_record in list(cursor.fetchall()):
            node_id = nav_node_record[0]
            nav_node_record_dict[node_id] = nav_node_record

            select_nav_gate_sql = f'''
                SELECT gate_id, node_id, in_linkid, out_linkid
                FROM nav_gate
                WHERE node_id = '{node_id}'
            '''
            cursor.execute(select_nav_gate_sql)
            for nav_gate_record in list(cursor.fetchall()):
                gate_id = nav_gate_record[0]
                nav_gate_record_dict[gate_id] = nav_gate_record

    save_nav_node_to_sqlite(download_dir + "/100000.ttfa", nav_node_record_dict.values())
    save_nav_gate_to_sqlite(download_dir + "/100000.ttfa", nav_gate_record_dict.values())


def save_nav_link_to_sqlite(file_path: str, records):
    conn = sqlite3.connect(file_path)
    cursor = conn.cursor()

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS nav_link
        ([link_id] TEXT PRIMARY KEY, [s_nid] TEXT, [e_nid] TEXT, [kind] SMALLINT, [geom] TEXT)
    ''')
    conn.commit()

    for record in tqdm(records, total=len(records), desc='save_nav_link_to_sqlite'):
        cursor.execute(f'''
            INSERT INTO nav_link 
            VALUES('{record[0]}', '{record[1]}', '{record[2]}', {record[3]}, '{record[4]}')
        ''')

    conn.commit()
    conn.close()


def save_nav_node_to_sqlite(file_path: str, records):
    conn = sqlite3.connect(file_path)
    cursor = conn.cursor()

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS nav_node
        ([node_id] TEXT PRIMARY KEY, [form] SMALLINT, [geom] TEXT)
    ''')
    conn.commit()

    for record in tqdm(records, total=len(records), desc='save_nav_node_to_sqlite'):
        cursor.execute(f"INSERT INTO nav_node VALUES('{record[0]}', {record[1]}, '{record[2]}')")

    conn.commit()
    conn.close()


def save_nav_gate_to_sqlite(file_path: str, records):
    conn = sqlite3.connect(file_path)
    cursor = conn.cursor()

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS nav_gate
        ([gate_id] TEXT PRIMARY KEY, [node_id] TEXT, [in_linkid] TEXT, [out_linkid] TEXT)
    ''')
    conn.commit()

    for record in tqdm(records, total=len(records), desc='save_nav_gate_to_sqlite'):
        cursor.execute(f'''
            INSERT INTO nav_gate 
            VALUES('{record[0]}', '{record[1]}', '{record[2]}', '{record[3]}')
        ''')

    conn.commit()
    conn.close()


def main(args):
    """
    真实的下载场景
    :return: void
    """
    output = args.output
    if output == "":
        print("未指定下载地址，默认当前文件夹")
        output = os.path.abspath("./")

    city = args.city
    region_file_path = args.region_file_path
    if city != '':
        print("下载ttfa 城市:{}  下载路径:{}".format(city, output))
        download_by_city(city, output)
    elif region_file_path != '':
        download_by_intelligence(region_file_path, output)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--city", default='', type=str, help="城市名称")
    parser.add_argument("--region-file-path", dest='region_file_path', type=str, help="情报文件地址")
    parser.add_argument("--output", default="", type=str, help='下载地址')
    args = parser.parse_args()
    main(args)
