# !/usr/bin/env python3
import argparse
import json
import os
import subprocess
import sys
import requests

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
sys.path.insert(0, ROOT_PATH)

from retrying import retry
from src.tools import conf_tools
import sqlite3
import src.tools.aoi_tools as at
from src.model.aoi_model import AoiModel
from tqdm import tqdm


# 以情报点下载数据时，会直接使用 pg 出库，以情报点为中心，搜索半径为 500 米，索引目标类型要素。
SEARCH_RADIUS = 0.005


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def getDownloadUrl(meshid) -> str:
    """
    获取ttfb的地址
    :param meshid 单个图幅号
    :return:
    """
    url = "http://api.guoke.map.baidu.com/trunk/expimp"
    params = {"client_id": "beeflow_shell", "user_id": "beeflow_ttfb_download_20221102", "req_id": 185164,
              "authcode": "d9974b231c7976e4", "action": 95,
              "params": {"proc_version": "/chenxi_system/buildRegion/AOI建设外采后处理", "extra_type": "meshes",
                         "extra_way": meshid,
                         "format": "sqlite", "url_type": 3, "compress": 2, "lock_type": "r", "groupname": "",
                         "cityname": "", "addinfo": {},
                         "args": {"dataurl": "", "db_name": "", "host": "", "password": "", "port": 0, "username": ""}}
              }
    req = requests.post(url, json.dumps(params))
    if req.status_code != 200:
        print("处理失败")
        return ""
    resp = req.json()
    if resp['code'] != 0:
        print("请求失败")
        print(resp)
        return ""
    return resp['contents']['args']['dataurl']


def download_by_meshid(meshid, download_dir):
    """
    下载ttfb数据，根据图幅号
    :param meshid 图幅号
    :param download_dir 下载地址
    """
    os.makedirs(download_dir, exist_ok=True)
    url = getDownloadUrl(meshid)
    if url == "":
        print("获取下载地址失败：图幅号 {}".format(url))
        return
    print("开始下载{} {}".format(meshid, url))
    urlData = requests.get(url).content
    with open(download_dir + "/" + meshid + ".ttfb.zip", 'wb') as f:
        f.write(urlData)


def download_by_meshid_v2(mesh_id, download_dir):
    """
    根据图幅号下载,直接从pg数据库下载,不走接口
    :param mesh_id:
    :param download_dir:
    :return:
    """
    pg_conf_masterback = conf_tools.get_pg_conf("master_back")
    if 'host' not in pg_conf_masterback.keys():
        print("数据库信息没有配置")
        return
    pg_info_arg = 'PG:dbname={} port={} user={} host={} password={}' \
        .format(pg_conf_masterback['db'], pg_conf_masterback['port'], pg_conf_masterback['user'],
                pg_conf_masterback['host'], pg_conf_masterback['pwd'])
    # 建筑
    sql_bud_face = f"select *,st_astext(geom) as geom from bud_face where mesh_id='{mesh_id}'"
    # aoi
    sql_blu_face = f"select *,st_astext(geom) as geom from blu_face where mesh_id='{mesh_id}'"
    # 绿地
    sql_blc_face = f"select *,st_astext(geom) as geom from blc_face where mesh_id='{mesh_id}'"
    os.makedirs(download_dir, exist_ok=True)
    # 建筑
    process = subprocess.Popen(
        ['ogr2ogr', '-append', '-update', '-f', 'SQLite', '{}/{}.ttfb'.format(download_dir, mesh_id), '-lco',
         'GEOMETRY_NAME=geomaa', '-nln', 'bud_face', pg_info_arg, '-a_srs', 'WGS84', '-lco', 'GEOM_TYPE=geometry',
         '-sql', sql_bud_face])
    process.wait()
    print(process.args)
    # aoi面
    process = subprocess.Popen(
        ['ogr2ogr', '-append', '-update', '-f', 'SQLite', '{}/{}.ttfb'.format(download_dir, mesh_id), '-lco',
         'GEOMETRY_NAME=geomaaa', '-nln', 'blu_face', pg_info_arg, '-a_srs', 'WGS84', '-lco', 'GEOM_TYPE=geometry',
         '-sql', sql_blu_face])
    process.wait()
    print(process.args)
    # 绿地
    process = subprocess.Popen(
        ['ogr2ogr', '-append', '-update', '-f', 'SQLite', '{}/{}.ttfb'.format(download_dir, mesh_id), '-lco',
         'GEOMETRY_NAME=geomaa', '-nln', 'blc_face', pg_info_arg, '-a_srs', 'WGS84', '-lco', 'GEOM_TYPE=geometry',
         '-sql', sql_blc_face])
    process.wait()
    print(process.args)


def download_by_city(cityname, output):
    """
    根据城市名称下载ttfb
    :param cityname 城市名称 ，如 上海市
    """
    mesh_list = at.get_mesh_list(cityname)

    download_dir = output + "/data/ttfb/" + cityname
    index = 0
    all_data = len(mesh_list)
    for mesh_id in mesh_list:
        try:
            download_by_meshid(mesh_id, download_dir)
            index += 1
            print("{} 当前：{} / {}".format(cityname, index, all_data))
        except:
            print("{} 当前：{} / {} 下载失败".format(cityname, index, all_data))
            pass
    proc = subprocess.Popen(f"cd {download_dir}; " +
                            "ls -al | grep zip | awk '{print $9}'  | xargs -I {} unzip {}; rm -f *.zip", shell=True)
    proc.wait()
    print(proc.args)


def download_by_intelligence(region_file_path: str, download_dir: str, radius=SEARCH_RADIUS):
    os.makedirs(download_dir, exist_ok=True)

    with AoiModel() as aoi_model:
        conn = aoi_model.get_conn_back()
        with conn.cursor() as cursor:
            with open(region_file_path, "r", encoding="utf8") as f:
                lines = list(f.readlines())
                download_blu_face_by_intelligence(cursor, lines, download_dir, radius)
                download_blc_face_by_intelligence(cursor, lines, download_dir, radius)

        conn.commit()
        conn.close()


def download_blu_face_by_intelligence(cursor, lines, download_dir: str, radius):
    blu_face_record_dict = dict()
    blu_face_poi_record_dict = dict()

    for line in tqdm(lines, total=len(lines), desc='download_blu_face_by_intelligence'):
        wkt = line.rstrip()
        sql = f'''
            SELECT face.face_id, poi.poi_id, face.name_ch, ST_AsText(face.geom) 
            FROM blu_face face, blu_face_poi poi 
            WHERE face.face_id = poi.face_id
            AND ST_dwithin(ST_GeomFromText('{wkt}', 4326), face.geom, {radius})
        '''
        cursor.execute(sql)
        for record in list(cursor.fetchall()):
            face_id = record[0]
            poi_id = record[1]

            blu_face_record_dict[face_id] = (face_id, record[2], record[3])
            blu_face_poi_record_dict[poi_id] = (poi_id, face_id)

    save_blu_face_to_sqlite(download_dir + "/100000.ttfb", blu_face_record_dict.values())
    save_blu_face_poi_to_sqlite(download_dir + "/100000.ttfb", blu_face_poi_record_dict.values())


def download_blc_face_by_intelligence(cursor, lines, download_dir: str, radius):
    record_dict = dict()

    for line in tqdm(lines, total=len(lines), desc='download_blc_face_by_intelligence'):
        wkt = line.rstrip()
        sql = f'''
            SELECT face_id, kind, dis_class, ST_AsText(geom) 
            FROM blc_face
            WHERE ST_dwithin(ST_GeomFromText('{wkt}', 4326), geom, {radius})
        '''
        cursor.execute(sql)
        for record in list(cursor.fetchall()):
            face_id = record[0]
            record_dict[face_id] = record

    save_blc_face_to_sqlite(download_dir + "/100000.ttfb", record_dict.values())


def save_blu_face_to_sqlite(file_path: str, records):
    conn = sqlite3.connect(file_path)
    cursor = conn.cursor()

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS blu_face
        ([face_id] TEXT PRIMARY KEY, [name_ch] TEXT, [geom] TEXT)
    ''')
    conn.commit()

    for record in tqdm(records, total=len(records), desc='save_blu_face_to_sqlite'):
        try:
            cursor.execute(f"INSERT INTO blu_face VALUES('{record[0]}', '{record[1]}', '{record[2]}')")
        except:
            pass

    conn.commit()
    conn.close()


def save_blu_face_poi_to_sqlite(file_path: str, records):
    conn = sqlite3.connect(file_path)
    cursor = conn.cursor()

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS blu_face_poi
        ([poi_id] TEXT PRIMARY KEY, [face_id] TEXT)
    ''')
    conn.commit()

    for record in tqdm(records, total=len(records), desc='save_blu_face_poi_to_sqlite'):
        try:
            cursor.execute(f"INSERT INTO blu_face_poi VALUES('{record[0]}', '{record[1]}')")
        except:
            pass

    conn.commit()
    conn.close()


def save_blc_face_to_sqlite(file_path: str, records):
    conn = sqlite3.connect(file_path)
    cursor = conn.cursor()

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS blc_face
        ([face_id] TEXT PRIMARY KEY, [kind] SMALLINT, [dis_class] SMALLINT, [geom] TEXT)
    ''')
    conn.commit()

    for record in tqdm(records, total=len(records), desc='save_blc_face_to_sqlite'):
        try:
            cursor.execute(f"INSERT INTO blc_face VALUES('{record[0]}', {record[1]}, {record[2]}, '{record[3]}')")
        except:
            pass

    conn.commit()
    conn.close()


def main(args):
    """
    主执行方法
    :param args 解析参数
    :return: void
    """
    output = args.output

    if output == "":
        print("未指定下载地址，默认当前文件夹")
        output = os.path.abspath("./")

    city = args.city
    region_file_path = args.region_file_path
    if city != '':
        print("下载ttfb 城市:{}  下载路径:{}".format(city, output))
        download_by_city(city, output)
    elif region_file_path != '':
        download_by_intelligence(region_file_path, output)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--city", default='', type=str, help="城市名称")
    parser.add_argument("--region-file-path", dest='region_file_path', type=str, help="情报文件地址")
    parser.add_argument("--output", default="", type=str, help='下载地址')
    args = parser.parse_args()
    main(args)
