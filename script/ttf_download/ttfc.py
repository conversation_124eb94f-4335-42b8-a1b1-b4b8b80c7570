# !/usr/bin/env python3
import argparse
import os.path
import subprocess
import sys

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
sys.path.insert(0, ROOT_PATH)

import sqlite3
from src.tools import conf_tools
from src.tools.city_tools import get_city_pinying
from src.model.aoi_model import AoiModel
from tqdm import tqdm

TYPE_CITY = "city"
TYPE_MESH_ID = "mesh_id"
# 以情报点下载数据时，会直接使用 pg 出库，以情报点为中心，搜索半径为 500 米，索引目标类型要素。
SEARCH_RADIUS = 0.005


def get_sql(item, type=TYPE_CITY) -> str:
    """
    sql处理
    :type 类型, 有city 和 mesh_id
    """
    tag_path = ROOT_PATH + "/script/ttf_download/ttfc_tag.txt"
    if not os.path.exists(tag_path):
        print("筛选tag文件不存在，请检查：{}".format(tag_path))
        return ""
    sql = ""
    if type == TYPE_CITY:
        # 按城市
        sql = "select * from poi where city = '{}' and ( 1=2 ".format(item)
    elif type == TYPE_MESH_ID:
        # 按照图幅号
        sql = "select * from poi where mesh_id = '{}' and ( 1=2 ".format(item)
    with open(tag_path, 'r') as f:
        tags = f.read().split("\n")
        for tag in tags:
            sql += " or std_tag='{}' ".format(tag)
    sql += ")"
    return sql


def download_by_city(cityname, path):
    """
    下载ttfc文件
    :param cityname 城市名称，如上海市
    """
    sql = get_sql(cityname, TYPE_CITY)
    if sql == "":
        print("sql生成失败")
        return
    pg_conf = conf_tools.get_pg_conf("poi_online")
    if not pg_conf.keys().__contains__('host'):
        print("数据库信息没有配置")
        return

    pg_info_arg = 'PG:dbname={} port={} user={} host={} password={}'.format(pg_conf['db'], pg_conf['port'],
                                                                            pg_conf['user'], pg_conf['host'],
                                                                            pg_conf['pwd'])
    download_dir = path
    os.makedirs(download_dir, exist_ok=True)
    process = subprocess.Popen(
        ['ogr2ogr', '-append', '-update', '-f', 'SQLite', '{}/{}.ttfc'.format(download_dir, get_city_pinying(cityname)),
         '-lco',
         'GEOMETRY_NAME=wkt_geom', '-nln', 'poi', pg_info_arg, '-a_srs', 'WGS84', '-lco', 'GEOM_TYPE=geometry',
         '-sql', sql])
    process.wait()
    print(process.args)


def download_by_mesh_id(mesh_id, path):
    """
    根据图幅号下载ttfc
    :param mesh_id:
    :param path:
    :return:
    """
    sql = get_sql(mesh_id, type=TYPE_MESH_ID)
    if sql == "":
        print("sql生成失败")
        return
    pg_conf = conf_tools.get_pg_conf("poi_online")
    if 'host' not in pg_conf.keys():
        print("数据库信息没有配置")
        return
    pg_info_arg = 'PG:dbname={} port={} user={} host={} password={}'.format(pg_conf['db'], pg_conf['port'],
                                                                            pg_conf['user'], pg_conf['host'],
                                                                            pg_conf['pwd'])
    download_dir = path
    os.makedirs(download_dir, exist_ok=True)
    process = subprocess.Popen(
        ['ogr2ogr', '-append', '-update', '-f', 'SQLite', '{}/{}.ttfc'.format(download_dir, mesh_id), '-lco',
         'GEOMETRY_NAME=wkt_geom', '-nln', 'poi', pg_info_arg, '-a_srs', 'WGS84', '-lco', 'GEOM_TYPE=geometry',
         '-sql', sql])
    process.wait()
    print(process.args)


def download_by_intelligence(region_file_path: str, download_dir: str, radius=SEARCH_RADIUS):
    os.makedirs(download_dir, exist_ok=True)
    record_dict = dict()

    with AoiModel() as aoi_model:
        conn = aoi_model.get_conn_poi()
        with conn.cursor() as cursor:
            with open(region_file_path, "r", encoding="utf8") as f:
                lines = list(f.readlines())
                for line in tqdm(lines, total=len(lines), desc='download_by_intelligence'):
                    wkt = line.rstrip()
                    sql = f'''
                        SELECT mid, name, address, relation, std_tag, ST_AsText(geometry) 
                        FROM poi 
                        WHERE ST_dwithin(ST_GeomFromText('{wkt}', 4326), geometry, {radius})
                    '''
                    cursor.execute(sql)
                    records = list(cursor.fetchall())
                    for record in records:
                        link_id = record[0]
                        record_dict[link_id] = record

        conn.commit()
        conn.close()

    save_to_sqlite(download_dir + "/100000.ttfc", record_dict.values())


def save_to_sqlite(file_path: str, records):
    conn = sqlite3.connect(file_path)
    cursor = conn.cursor()

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS poi
        ([mid] TEXT PRIMARY KEY, [name] TEXT, [address] TEXT, [relation] TEXT, [std_tag] TEXT, [geom] TEXT)
    ''')
    conn.commit()

    for record in tqdm(records, total=len(records), desc='save_to_sqlite'):
        try:
            cursor.execute(f'''
                INSERT INTO poi 
                VALUES('{record[0]}', '{record[1]}', '{record[2]}', '{record[3]}', '{record[4]}', '{record[5]}')
            ''')
        except:
            pass

    conn.commit()
    conn.close()


def main(args):
    output = args.output
    if output == "":
        print("未指定下载地址，默认当前文件夹")
        output = os.path.abspath("./")

    city = args.city
    region_file_path = args.region_file_path
    if city != '':
        print("下载ttfc 城市:{}  下载路径:{}".format(city, output))
        download_by_city(city, output)
    elif region_file_path != '':
        download_by_intelligence(region_file_path, output)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--city", default='', type=str, help="城市名称")
    parser.add_argument("--region-file-path", dest='region_file_path', type=str, help="情报文件地址")
    parser.add_argument("--output", default="", type=str, help='下载地址')
    args = parser.parse_args()
    main(args)
