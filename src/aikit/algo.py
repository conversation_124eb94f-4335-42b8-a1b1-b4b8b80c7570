"""
提供一些未分类的算法函数，比较杂
"""
import math


def calc_overlap_ranges(ranges: list[tuple[float, float]]) -> list[tuple[tuple[float, float], int]]:
    """
    对于任意给定的区间数组，计算出这些区间的叠加结果，结果以新的区间+权重的形式返回，新区间是不会重叠的
    :param ranges: [(strat, end)]
    :return: [((start, end), num_overlap)]
    """
    points = []
    for range_id, (start, end) in enumerate(ranges):
        points.append((range_id, start, True))
        points.append((range_id, end, False))

    points.sort(key=lambda x: x[1])
    results = []
    active_ids = set()
    prev_position = None
    for range_id, position, is_start in points:
        if prev_position is not None and position > prev_position:
            a = prev_position
            n = len(active_ids)
            if len(results) > 0:
                (prev_a, prev_b), prev_n = results[-1]
                if prev_b >= prev_position and prev_n == n:
                    a = prev_a
                    results.pop()

            r = (a, position)
            results.append((r, n))

        prev_position = position
        if is_start:
            active_ids.add(range_id)
        else:
            active_ids.remove(range_id)

    return [((a, b), n) for (a, b), n in results if n > 0]


def calc_shape_factor(geom):
    """
    计算形状因子：[0, 1]
    0 - 线
    pi/4 - 正方形
    1 - 圆
    """
    return 4 * math.pi * geom.area / geom.length**2
