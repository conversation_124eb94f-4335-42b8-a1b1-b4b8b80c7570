"""
所有识别流程的输入都应该含有一个或一组范围参数，本模块提供一系列帮助方法，用于将其它牵引数据参数为范围参数。

这个模块叫 boundary 而不是 bounds，是因为 bounds 经常作为变量名，怕命名冲突了

名词解释：

- bounds：指一个元组 (left, top, right, bottom)
- envelope：指一个 WKT 格式的矩形，用 POLYGON 表示
"""
from shapely import wkt

from src.tools import pgsql


def from_wkt(wkt_str: str, buffer: float = 0) -> tuple[float, float, float, float]:
    """
    根据 WKT 字符串，获取 bounds
    :param wkt_str: WKT 字符串
    :param buffer: buffer 尺寸，与 wkt_str 同单位，若为 0，则不做任何操作
    :return: (left, top, right, bottom)
    """
    geom = wkt.loads(wkt_str)
    if buffer != 0:
        geom = geom.buffer(buffer)

    left, bottom, right, top = geom.bounds
    return left, top, right, bottom


def from_bids(bids: list[int], buffer: float) -> list[tuple[str, tuple[float, float, float, float]]]:
    """
    根据 bids，获取 bounds
    :param bids: POI 主键
    :param buffer: buffer 尺寸
    :return: [bid, (left, top, right, bottom)]
    """
    sql = """
        select bid, ST_AsText(ST_Buffer(geometry, %s)) from poi
        where bid in %s; 
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql, [buffer, bids])

    return [(bid, from_wkt(geom, buffer)) for bid, geom in ret]


def from_bid(bid: str, buffer: float):
    """
    根据 bid，获取 bounds
    :param bid: POI 主键
    :param buffer: buffer 尺寸
    :return: (left, top, right, bottom)
    """
    sql = """
        select ST_AsText(ST_Buffer(geometry, %s)) from poi
        where bid = %s;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_one(conn, sql, [bid])

    if ret is None:
        return None

    return from_wkt(ret[0], buffer)


def to_envelope(bounds: tuple[float, float, float, float]):
    """
    根据 bounds，获取 envelope wkt
    :param bounds: (left, top, right, bottom)
    :return: envelope
    """
    left, top, right, bottom = bounds
    return f"POLYGON (({left} {top}, {right} {top}, {right} {bottom}, {left} {bottom}, {left} {top}))"


def to_lbrt(bounds: tuple[float, float, float, float]):
    """
    转换 ltrb 到 lbrt 格式，lbrt 可等效于 (min_x, min_y, max_x, max_y)，在一些地方会比较方便
    :param bounds: (left, top, right, bottom)
    :return: (left, bottom, right, top)
    """
    left, top, right, bottom = bounds
    return left, bottom, right, top
