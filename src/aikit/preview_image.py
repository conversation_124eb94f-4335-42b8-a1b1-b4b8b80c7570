"""
封装 cv2，使其能够直接绘制 wkt 数据
"""
from typing import Union, Iterable, Sequence

import cv2
import numpy as np
from shapely import wkt

from src.tools import utils

COLOR_RED = (0.0, 0.0, 255.0)
COLOR_GREEN = (0.0, 255.0, 0.0)
COLOR_BLUE = (255.0, 0.0, 0.0)
COLOR_YELLOW = (0.0, 255.0, 255.0)
COLOR_PURPLE = (255.0, 0.0, 255.0)
COLOR_CYAN = (255.0, 255.0, 0.0)
COLOR_WHITE = (255.0, 255.0, 255.0)
COLOR_BLACK = (0.0, 0.0, 0.0)


def get_colors(n: int):
    """
    获取 n 个不同的颜色，且彼此间尽量保持最大的差异
    :param n: 需要生成的颜色数量
    :return: (B, G, R)
    """
    for i in range(n):
        # 计算色相，hue 均匀分布在 0 到 360 之间
        hue = int(360.0 * i / n)
        # 固定饱和度和亮度，转为 RGB
        color_hsv = np.uint8([[[hue, 255, 255]]])  # HSV: (H, S, V)
        color_rgb = cv2.cvtColor(color_hsv, cv2.COLOR_HSV2BGR)[0][0]
        # 转换为 tuple(int, int, int) 格式
        yield tuple(map(int, color_rgb))


def draw_polygon(image: np.ndarray, polygon: str, bounds: tuple, thickness: int = 2, color=COLOR_RED):
    """
    绘制 Polygon/MultiPolygon 的边框（不填充）
    :param image: 待绘制的图片，cv2 格式，即 np.ndarray
    :param polygon: 待绘制的多边形，wkt 格式
    :param bounds: 图片的地理坐标范围，与 polygon 单位相同 (left, top, right, bottom)
    :param thickness: 边框的粗细
    :param color: 颜色（BGR）
    """
    transform = _get_geo2pixel_transform(image, bounds)
    # 描多边形边界的时候，不在乎外轮廓或内部洞，直接画线就行，只有填充时需要区分。
    lines = [x for exterior, interiors in _flat_polygon_to_points(polygon) for x in [exterior, *interiors]]
    pts = [utils.convert_points(line, transform) for line in lines]
    cv2.polylines(image, pts=pts, isClosed=True, color=color, thickness=thickness)


def fill_polygon(image: np.ndarray, polygon: str, bounds: tuple, alpha: float = 1, color=COLOR_RED):
    """
    填充 Polygon/MultiPolygon 的边框
    :param image: 待绘制的图片，cv2 格式，即 np.ndarray
    :param polygon: 待绘制的多边形，wkt 格式
    :param bounds: 图片的地理坐标范围，与 polygon 单位相同 (left, top, right, bottom)
    :param alpha: 透明度
    :param color: 颜色（BGR）
    """
    is_gray = image.ndim == 2
    if is_gray:
        _fill_polygon_gray(image, polygon, bounds, alpha, color)
    else:
        _fill_polygon_rgb(image, polygon, bounds, alpha, color)


def _fill_polygon_rgb(image: np.ndarray, polygon: str, bounds: tuple, alpha: float = 1, color=COLOR_RED):
    transform = _get_geo2pixel_transform(image, bounds)

    overlay = np.zeros(image.shape, dtype=np.uint8)
    polygons = list(_flat_polygon_to_points(polygon))

    exteriors = [utils.convert_points(exterior, transform) for exterior, _ in polygons]
    cv2.fillPoly(overlay, pts=exteriors, color=color)

    interiors = [utils.convert_points(interior, transform) for _, interiors in polygons for interior in interiors]
    if interiors:
        cv2.fillPoly(overlay, pts=interiors, color=(0, 0, 0))

    mask = (overlay == color).all(axis=-1)
    if alpha < 1:
        overlay = cv2.addWeighted(image, 1, overlay, alpha, 0)

    image[mask] = overlay[mask]


def _fill_polygon_gray(image: np.ndarray, polygon: str, bounds: tuple, alpha: float, color: int):
    transform = _get_geo2pixel_transform(image, bounds)

    overlay = np.zeros_like(image)
    polygons = list(_flat_polygon_to_points(polygon))

    exteriors = [utils.convert_points(exterior, transform) for exterior, _ in polygons]
    # noinspection PyTypeChecker
    cv2.fillPoly(overlay, pts=exteriors, color=color)

    interiors = [utils.convert_points(interior, transform) for _, interiors in polygons for interior in interiors]
    if interiors:
        # noinspection PyTypeChecker
        cv2.fillPoly(overlay, pts=interiors, color=0)

    mask = overlay == color
    if alpha < 1:
        overlay = ((1 - alpha) * image + alpha * overlay).astype(image.dtype)

    image[mask] = overlay[mask]


def draw_linestring(
    image: np.ndarray, lines: Union[str, Iterable[str]], bounds: tuple, thickness: int = 2, color=COLOR_RED
):
    """
    绘制 LineString/MultiLineString 列表
    :param image: 待绘制的图片，cv2 格式，即 np.ndarray
    :param lines: 待绘制的线段列表，wkt 格式，可以是单条 wkt，也可以是多条 wkt
    :param bounds: 图片的地理坐标范围，与 lines 单位相同 (left, top, right, bottom)
    :param thickness: 线段的粗细
    :param color: 颜色（BGR）
    """
    transform = _get_geo2pixel_transform(image, bounds)
    lines = [lines] if isinstance(lines, str) else lines
    pts = [utils.convert_points(sub_line, transform) for line in lines for sub_line in _flat_line_to_points(line)]
    cv2.polylines(image, pts=pts, isClosed=False, color=color, thickness=thickness)


def draw_point(
    image: np.ndarray,
    points: Union[Iterable[Union[str, tuple[float, float]]], str, tuple[float, float]],
    bounds: tuple,
    radius: int,
    thickness: int = -1,
    color=COLOR_RED,
):
    """
    绘制点
    :param image: 待绘制的图片，cv2 格式，即 np.ndarray
    :param points: 待绘制的点，格式为 (x, y) 或 wkt，或它们的可迭代集合
    :param bounds: 图片的地理坐标范围，与 point 单位相同 (left, top, right, bottom)
    :param radius: 点的半径
    :param thickness: 圆的边框宽度，如果为负值则表示填充圆，否则表示圆的边框宽度
    :param color: 颜色（BGR）
    """
    transform = _get_geo2pixel_transform(image, bounds)

    def draw_single_point(point):
        center = _to_int_point(point, transform)
        cv2.circle(image, center, radius, color=color, thickness=thickness)

    if isinstance(points, str) or isinstance(points, tuple):
        draw_single_point(points)
    else:
        for p in points:
            draw_single_point(p)


def draw_arrow(
    image: np.ndarray,
    start: Union[str, Sequence[float]],
    end: Union[str, Sequence[float]],
    bounds: tuple,
    thickness: int = 2,
    color=COLOR_RED,
):
    """
    绘制箭头
    :param image: 待绘制的图片，cv2 格式，即 np.ndarray
    :param start: 箭头线的起点
    :param end: 箭头线的终点
    :param bounds: 图片的地理坐标范围，与 point 单位相同 (left, top, right, bottom)
    :param thickness: 圆的边框宽度，如果为负值则表示填充圆，否则表示圆的边框宽度
    :param color: 颜色（BGR）
    """
    transform = _get_geo2pixel_transform(image, bounds)
    p1 = _to_int_point(start, transform)
    p2 = _to_int_point(end, transform)

    # 箭头尺寸应该与粗细相关，而不是与长度相关
    length = np.linalg.norm(np.array(p1) - np.array(p2))
    tip_length = 4.0 * thickness / length
    cv2.arrowedLine(image, p1, p2, color=color, thickness=thickness, tipLength=tip_length, line_type=cv2.LINE_AA)


def _to_int_point(point: Union[str, any], transform):
    if isinstance(point, str):
        p = utils.get_point(point, transform)
    else:
        x, y = utils.convert_coordinate(point, transform)
        p = [int(x), int(y)]

    return p


def _get_geo2pixel_transform(image, bounds):
    """
    获取地理坐标到像素坐标的转换参数
    """
    left, top, right, bottom = bounds
    h, w = image.shape[:2]
    geo2pixel_transform = utils.get_transform_info(
        ((left, top), (0, 0)),
        ((right, bottom), (w, h)),
    )
    return geo2pixel_transform


def _flat_polygon_to_points(geom_wkt) -> list[tuple[list, list[list]]]:
    """
    将 MultiPolygon 展开为 Polygon
    """

    def flat(geometry):
        if geometry.geom_type == "Polygon":
            ring = geometry.exterior
            yield list(ring.coords), [list(interior.coords) for interior in geometry.interiors]
        elif geometry.geom_type == "MultiPolygon":
            yield from [x for g in geometry.geoms for x in flat(g)]

    geom = wkt.loads(geom_wkt) if isinstance(geom_wkt, str) else geom_wkt
    return list(flat(geom))


def _flat_line_to_points(line_wkt):
    """
    将 LineString 展开为 Point
    """

    def flat(geometry):
        if geometry.geom_type == "LineString":
            yield list(geometry.coords)
        elif geometry.geom_type == "MultiLineString":
            yield from [x for g in geometry.geoms for x in flat(g)]

    geom = wkt.loads(line_wkt) if isinstance(line_wkt, str) else line_wkt
    return list(flat(geom))
