"""
影像获取模块，外部使用仅需调用 `crop(bounds, ...)` 函数
"""

import hashlib
import logging
import math
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from pathlib import Path

import coord_convert.transform as ct
import cv2
import numpy as np
import requests
from retrying import retry

from src.aikit import boundary

# 地理信息常量：

MAX_IMAGE_LEVEL = 20
# 20 级影像每像素对应的经纬度
GEO_PER_PIXEL_20 = 2.682209014892578e-06
# 一个“瓦片图图幅”（非标准图幅）等于 128 * 128 张瓦片图。
TILE_MESH_NUMBER = 128
TILE_LENGTH_PIXEL = 256
TILE_LENGTH_WGS_20 = GEO_PER_PIXEL_20 * TILE_LENGTH_PIXEL
# 一个“瓦片图图幅”的长度始终为 128 个 19 级影像的长度，故为 20 级长度乘 2。
TILE_MESH_LENGTH_WGS = 2 * TILE_LENGTH_WGS_20 * TILE_MESH_NUMBER

# 配置常量：

IMAGE_API: dict[str, str] = {
    "google-2021": "http://m.map.baidu.com:8006/tilepic/",
    "google-2022": "http://m.map.baidu.com:8006/tilepic/2022_",
    "google-2023": "http://m.map.baidu.com:8006/tilepic/2023_",
}

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class DownloadOptions:
    """
    下载参数选项
    """

    max_threads: int = 8
    timeout: int = 10
    cache_dir: Path = Path("tmp/tiles")


DEFAULT_DOWNLOAD_OPTIONS = DownloadOptions()


def crop(
    bounds: tuple[float, float, float, float],
    version: str = "google-2023",
    level: int = 20,
    options: DownloadOptions = DEFAULT_DOWNLOAD_OPTIONS,
):
    """
    模块导出函数：根据给定的 options，从影像图上截取指定的 bounds，并输出该图片
    :param bounds: (left, top, right, bottom)
    :param version: 影像图版本
    :param level: 影像图等级
    :param options: 下载配置选项
    :return: 返回图片，以 ndarray 的格式，可用 cv2.imwrite() 保存
    """
    # 1. calculate tiles covered
    bounds = _gcj2wgs(bounds)
    positions = _get_positions(bounds, level)
    if positions.size <= 0:
        return None

    # 2. download tiles
    tile_host = IMAGE_API[version]
    cache_dir = options.cache_dir / version / str(level)
    cache_dir.mkdir(parents=True, exist_ok=True)
    try:
        tiles = _get_tiles_from_remote_or_local(positions, level, _get_tile_url_getter(tile_host), cache_dir, options)
    except Exception as e:
        logger.warning(f"failed to _get_tiles_from_remote_or_local(): {e}")
        return None

    tiles = np.array(tiles)

    # 3. combine tiles to one image
    row, column, _, _ = positions.shape
    tile_matrix = tiles.reshape((row, column))
    try:
        image = _combine_tiles(tile_matrix)
    except Exception as e:
        logger.warning(f"failed to _combine_tiles(): {e}")
        return None

    # 4. crop image
    _, min_tile_position = positions[-1, 0]
    min_height, max_height, min_width, max_width = _get_crop_indexes(
        bounds=bounds, min_tile_position=min_tile_position, level=level
    )
    image = image[min_height:max_height, min_width:max_width]
    return image if image.size > 0 else None


def _get_tiles_from_remote_or_local(tile_positions, level: int, get_url, cache_dir: Path, options: DownloadOptions):
    """
    获取瓦片图，如果本地有，则直接读取，否则从远程下载。
    """
    tiles = [(mesh, tile, get_url(mesh, tile, level)) for rows in tile_positions for mesh, tile in rows]
    tasks = [
        (cache_dir / f"{mesh_x}{mesh_y}" / f"{tile_x}_{tile_y}.jpg", url, options.timeout)
        for (mesh_x, mesh_y), (tile_x, tile_y), url in tiles
    ]
    with ThreadPoolExecutor(max_workers=options.max_threads) as executor:
        # NOTE: python 的 executor.map() 是能够确保返回值顺序与传入 tasks 顺序一致的
        return list(executor.map(_process_download, tasks))


def _process_download(task):
    """
    下载或读取图片，并返回 Path 对象，为并行处理而封装
    """
    cache_path, url, timeout = task
    if not cache_path.exists() or not _is_jpg(cache_path):
        _download(url, cache_path, timeout)

    return cache_path


def _is_jpg(file_path: Path):
    """
    判断文件是否为 jpg 格式
    """
    with open(file_path, "rb") as f:
        header = f.read(2)
        return header == b"\xff\xd8"  # jpg 文件头


def _should_retry(exception):
    if isinstance(exception, requests.exceptions.HTTPError):
        return exception.response.status_code not in [404]

    return True


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000, retry_on_exception=_should_retry)
def _download(url: str, save_path: Path, timeout: int):
    """
    下载图片到指定路径
    """
    save_path.parent.mkdir(parents=True, exist_ok=True)
    res = requests.get(url, stream=True, timeout=timeout)
    res.raise_for_status()

    content_type = res.headers.get("Content-Type")
    if not content_type.startswith("image/"):
        raise ValueError(f"unsupported Content-Type: {content_type}, url: {url}")

    with open(save_path, "wb") as f:
        for chunk in res.iter_content(chunk_size=1024):
            f.write(chunk)

    expected_size = int(res.headers.get("Content-Length", 0))
    actual_size = save_path.stat().st_size
    assert expected_size <= 0 or expected_size == actual_size, f"{expected_size=}, {actual_size=}"


def _get_tile_url_getter(host: str):
    """
    获取瓦片图 url 提供器
    """

    def get_tile_url(mesh_position, tile_position, level):
        timestamp = int(time.time() * 1000)
        appkey = "map-aoi-tile"
        secret = "2aeb2332de572858516c68519a71df12"
        sign = hashlib.md5(f"{appkey}|{timestamp}|{secret}".encode("utf-8")).hexdigest()

        mesh_x, mesh_y = mesh_position
        tile_x, tile_y = tile_position
        return f"{host}{mesh_x}_{mesh_y}/{level}/{tile_x}_{tile_y}.jpg?appkey={appkey}&t={timestamp}&sign={sign}"

    return get_tile_url


def _get_positions(bounds, level: int):
    """
    获取给定 bounds 覆盖的瓦片位置。
    返回张量的形状：(row, column, 2, 2)，可看作一个 row * column 的矩阵，每个元素是 [[mesh_x, mesh_y], [tile_x, tile_y]]
    """
    left, top, right, bottom = bounds
    tile_length_wgs = TILE_LENGTH_WGS_20 * _get_scale_factor(level)

    left = math.floor(left / tile_length_wgs)
    top = math.ceil(top / tile_length_wgs)
    right = math.ceil(right / tile_length_wgs)
    bottom = math.floor(bottom / tile_length_wgs)

    def get_mesh_position(tile_position):
        column, row = tile_position
        x = column * tile_length_wgs
        # google 瓦片图原点在左下角，但是屏幕原点在左上角，差一格，故而 +1。
        y = (row + 1) * tile_length_wgs

        # 把图幅原点移动到图幅中心，防止浮点数误差导致图幅编号计算偏移。
        half_raster_coord_length = tile_length_wgs / 2
        return (
            int((x + half_raster_coord_length) / TILE_MESH_LENGTH_WGS),
            int((y - half_raster_coord_length) / TILE_MESH_LENGTH_WGS),
        )

    return np.array(
        [
            [(get_mesh_position((column, row)), (column, row)) for column in range(left, right)]
            for row in reversed(range(bottom, top))
        ]
    )


def _combine_tiles(tiles):
    """
    将多个图片拼接为一张大图
    """
    column_images = [np.concatenate([cv2.imread(str(file_path)) for file_path in x], axis=1) for x in tiles]
    image = np.concatenate(column_images, axis=0)
    return image


def _get_crop_indexes(bounds, min_tile_position, level: int):
    """
    返回：(min_height, max_height, min_width, max_width)
    使用：image[min_height:max_height, min_width:max_width]
    """
    left, top, right, bottom = bounds
    min_x, min_y = min_tile_position
    geo_per_pixel = GEO_PER_PIXEL_20 * _get_scale_factor(level)
    left, top, right, bottom = [x / geo_per_pixel for x in [left, top, right, bottom]]
    min_x, min_y = min_x * TILE_LENGTH_PIXEL, min_y * TILE_LENGTH_PIXEL
    top, bottom = round(top - min_y), round(bottom - min_y)
    left, right = round(left - min_x), round(right - min_x)
    return -top, -bottom, left, right


def _get_scale_factor(level: int):
    """
    获取缩放比例
    """
    level = min(level, MAX_IMAGE_LEVEL)
    return 1 << (MAX_IMAGE_LEVEL - level)


def _gcj2wgs(bounds):
    """
    转换 bounds 的坐标，将 gcj02 坐标转换为 wgs84 坐标
    """
    left, top, right, bottom = bounds
    left, top = ct.gcj2wgs(left, top)
    right, bottom = ct.gcj2wgs(right, bottom)
    return left, top, right, bottom


def main():
    """
    主函数
    """
    wkt_str = "TODO"

    bounds = boundary.from_wkt(wkt_str)
    version = "google-2023"
    level = 20
    image = crop(bounds, version=version, level=level)
    height, width = image.shape[:2]
    save_name = f"image_{version}_{level}_{width}_{height}.jpg"
    save_path = DEFAULT_DOWNLOAD_OPTIONS.cache_dir / save_name
    cv2.imwrite(str(save_path), image, [int(cv2.IMWRITE_JPEG_QUALITY), 100])


if __name__ == "__main__":
    main()
