"""
用于处理全景的球状图片，将其投影变换为其它形式
"""
from enum import Enum

import cv2
import numpy as np


class CubeFace(Enum):
    """
    六面体图的面朝向
    """

    RIGHT = 0
    LEFT = 1
    FRONT = 2
    BACK = 3
    TOP = 4
    BOTTOM = 5


def project_as_cube(image: np.ndarray, cube_face: CubeFace, azimuth: float = 0.0, size=2048):
    """
    将全景图球状图转换为六面体图
    :param image: 全景图球状图
    :param cube_face: 输出六面体面的方向
    :param azimuth: 方位角（弧度）
    :param size: 六面体图的边长
    :return: cube_face 指定方向的投影面
    """
    h_size = size / 2.0
    ax_a, ax_b = np.indices((size, size))
    x, y, z = None, None, -ax_a + h_size

    if cube_face == CubeFace.RIGHT:
        x = h_size
        y = -ax_b + h_size
    elif cube_face == CubeFace.LEFT:
        x = -h_size
        y = ax_b - h_size
    elif cube_face == CubeFace.FRONT:
        x = ax_b - h_size
        y = h_size
    elif cube_face == CubeFace.BACK:
        x = -ax_b + h_size
        y = -h_size
    elif cube_face == CubeFace.TOP:
        z = h_size
        x = ax_b - h_size
        y = ax_a - h_size
    elif cube_face == CubeFace.BOTTOM:
        z = -h_size
        x = ax_b - h_size
        y = -ax_a + h_size

    r = np.sqrt(x**2 + y**2 + z**2)
    theta = np.arccos(z / r)
    phi = -np.arctan2(y, x) + azimuth

    # 此处直接舍入了，也可以使用最近邻或者插值来获取像素值
    ix = ((image.shape[1] - 1) * phi / (2 * np.pi)).astype(int)
    iy = ((image.shape[0] - 1) * theta / np.pi).astype(int)

    cube_image = image[iy, ix]
    return cube_image


def project_as_cylinder(
    image: np.ndarray, lat_range: tuple[float, float], lon_range: tuple[float, float], width: int, height: int
):
    """
    将球状全景图像投影为圆柱形，使用纬度范围和经度范围
    :param image: 球状全景图像
    :param lat_range: 纬度范围 (min_lat, max_lat)
    :param lon_range: 经度范围 (min_lon, max_lon)
    :param width: 输出图像宽度
    :param height: 输出图像高度
    :return: 圆柱形展开图像
    """
    h, w = image.shape[:2]

    # 将纬度范围和经度范围转换为弧度
    min_lat, max_lat = np.radians(lat_range)
    min_lon, max_lon = np.radians(lon_range)

    # 创建目标图像的像素网格
    x_map, y_map = np.meshgrid(np.linspace(min_lon, max_lon, width), np.linspace(min_lat, max_lat, height))

    # 调整经度，使其与中心经度对齐
    lon = x_map

    # 处理越界经度（经度超出 -pi 到 pi 的部分需要回绕）
    lon = (lon + np.pi) % (2 * np.pi) - np.pi

    lat = y_map

    # 将球面坐标转换为原图的像素坐标 (u, v)
    u = ((lon + np.pi) / (2 * np.pi)) * (w - 1)
    v = ((lat + np.pi / 2) / np.pi) * (h - 1)

    # 使用 remap 来应用坐标映射
    u = u.astype(np.float32)
    v = v.astype(np.float32)
    cylinder_image = cv2.remap(image, u, v, interpolation=cv2.INTER_LINEAR, borderMode=cv2.BORDER_WRAP)
    return cylinder_image
