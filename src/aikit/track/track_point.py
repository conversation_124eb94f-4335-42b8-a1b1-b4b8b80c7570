"""
轨迹数据查询，可用的源详见 SRC_*
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/Jn_iK1KcMy/ri87l7_YeKOiHS
"""
import hashlib
import logging
from dataclasses import dataclass, asdict, field
from datetime import datetime, timedelta

import requests
from retrying import retry

# TRACK_API = "http://m.map.baidu.com:8014/api/v2/track"
TRACK_API = "http://img.map.baidu-int.com/dedc/api/v2/track"
# AK = "zhongye_mark"
# SK = "ecf4f403b1a0f6b61314190d6794277d"
# AK = "aoi_pingtai"
# SK = "c4eedb110fded4bdfb24658d3d426f2b"
# AK = "beebus"
# SK = "8c1f20b3a3d2667148fa49a9178cdd07"
AK = "aoi_park"
SK = "4f4999b72996a70b372d0b2f1a28e72b"


SRC_QUANJING = ["quanjing"]  # 外业全景
SRC_ZHONGYUAN = ["vnet", "varea", "vmonitor", "point", "qblutao", "qbother", "qbpoi", "qbrobot", "qbgate"]  # 众源
SRC_LUTAO = ["lutao"]  # 路淘数据
SRC_BIKE = ["bike"]  # 步采数据
SRC_RWT = ["rwt"]  # 外采评估

logger = logging.getLogger(__name__)


@dataclass
class Query:
    """
    查询参数
    """

    left: float
    right: float
    top: float
    bottom: float
    stime: int
    etime: int
    source: str
    sign: str = field(init=False, default=None)
    t: str = field(init=False)
    appkey: str = AK
    quick: int = 0
    type: int = 10  # 判断查询哪类数据源。仅查询例行线数据0， 仅查询快速线数据1，查询全量数据10

    def __post_init__(self):
        now = datetime.now()
        milliseconds = (now - datetime(1970, 1, 1)).total_seconds() * 1000 + now.microsecond / 1000
        self.t = str(int(milliseconds))

        sign = self.to_queries().replace("&", "").replace("=", "")
        sign = f"{SK}{sign}{SK}"
        sign = hashlib.md5(sign.encode("utf8")).hexdigest()
        self.sign = sign

    def to_queries(self) -> str:
        """
        转换为 url 查询字符串
        """
        d = asdict(self)
        query_pairs = [(k, v) for k, v in d.items() if v is not None]
        query_pairs.sort(key=lambda x: x[0])
        queries = "&".join([f"{k}={v}" for k, v in query_pairs])
        return queries


def one_month(n=1) -> tuple[int, int]:
    """
    返回最近第 n 个月（30 天）的时间戳范围
    :param n: 表示距今第几个月，n >= 1，n = 1 表示最近 30 天
    :return: (start, end)
    """
    assert n >= 1

    now = datetime.now()
    start = now - timedelta(days=30 * n)
    end = now - timedelta(days=30 * (n - 1))
    return int(start.timestamp()), int(end.timestamp())


def get_track_points(bounds: tuple, time_span: tuple, src: list[str]):
    """
    获取轨迹点数据
    :param bounds: 空间范围，gcj02 坐标，(left, top, right, bottom)
    :param time_span: 时间范围，秒级时间戳，(start, end)
    :param src: 轨迹源
    :return: 原始数据字典（key="tracks"）
    """
    left, top, right, bottom = bounds
    stime, etime = time_span
    for source in src:
        query = Query(left, right, top, bottom, stime, etime, source)
        url = f"{TRACK_API}?{query.to_queries()}"
        resp = _get_response(url)
        data = resp.json()
        if data["errno"] != 0:
            logger.warning(f"TRACK_POINT: errno={data['errno']}, errmsg={data['errmsg']}, url={url}")
            continue

        if not data["tracks"]:
            continue

        yield from data["tracks"]


@retry(stop_max_attempt_number=64, wait_random_min=1000, wait_random_max=5000)
def _get_response(url: str):
    resp = requests.get(url)
    if resp.status_code != 200:
        raise ConnectionError(f"TRACK_POINT: status_code={resp.status_code}, url={url}")

    return resp
