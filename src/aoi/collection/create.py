"""
天级例行创建集合：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/vt5oX4oVH227CR
* 极高热防护集合
* 高热防护集合
* 潜热防护集合
"""
import tqdm
import datetime

from dataclasses import dataclass
from multiprocessing import Pool
from typing import Optional
from src.aoi.qc.common import query
from src.aoi.qc.common.monitor import send_hi, setup_logger

logger = setup_logger()  # 设置 logger

WORKERS_NUM: int = 60


@dataclass
class AoiData:
    """
    AOI数据
    """

    aoi_bid: str
    std_tag: Optional[str]
    aoi_level: Optional[int]
    aoi_complete: Optional[int]
    click_pv: Optional[int]
    nav_pv: Optional[int]
    memo: Optional[str]
    city: str


def run():
    """
    run
    """
    logger.info("start truncate_collections")
    query.truncate_collections()
    logger.info("end truncate_collections")

    logger.info("start create_aoi_main_poi_collection")
    create_aoi_main_poi_collection()
    logger.info("end create_aoi_main_poi_collection")

    logger.info("start create_over_heat_protection_aoi")
    create_over_heat_protection_aoi()
    logger.info("end create_over_heat_protection_aoi")

    logger.info("start create_high_heat_protection_aoi")
    create_high_heat_protection_aoi()
    logger.info("end create_high_heat_protection_aoi")

    logger.info("start create_potential_heat_protection_aoi")
    create_potential_heat_protection_aoi()
    logger.info("end create_potential_heat_protection_aoi")

    logger.info("start monitor")
    monitor()
    logger.info("end monitor")


def create_aoi_main_poi_collection():
    """
    创建全量aoi主点集合
    """
    logger.info("query_all_aoi_data")
    aoi_list: list[AoiData] = query_all_aoi_data()

    # 转为 dict 格式，准备入库
    records = []
    sd_bid_list = sd_parks_bid_list()
    for sd_bid in sd_bid_list:
        aoi_info = query.get_aoi_info(sd_bid)
        if aoi_info:
            aoi_info[0].memo = 'che_chang_data'
            aoi_list.append(process_aoi(aoi_info[0]))

    for aoi in tqdm.tqdm(aoi_list, desc="构建主点集合"):
        if not aoi.click_pv:
            aoi.click_pv = 0
        record = {
            "aoi_bid": aoi.aoi_bid,
            "std_tag": aoi.std_tag,
            "aoi_level": aoi.aoi_level,
            "aoi_complete": aoi.aoi_complete,
            "click_pv": aoi.click_pv,
            "nav_pv": aoi.nav_pv,
            "memo": aoi.memo,
            "city": aoi.city,
            "is_4categories": is_4categories(aoi)
        }
        records.append(record)

    logger.info("copy_aoi_main_poi_data_to_table")
    query.copy_aoi_main_poi_data_to_table(records)


def create_over_heat_protection_aoi():
    """
    创建极高热防护集合
    """
    data = query.get_over_heat_pv_data()
    query.insert_over_heat_data_to_table(data)


def create_high_heat_protection_aoi():
    """
    创建高热防护集合
    """
    data = query.get_high_heat_pv_data()
    query.copy_high_heat_data_to_table(data)


def create_potential_heat_protection_aoi():
    """
    创建潜热防护集合
    """
    data = query.get_potential_heat_pv_data()
    query.copy_potential_heat_data_to_table(data)


def query_all_aoi_data() -> list[AoiData]:
    """
    全量停车场数据
    """
    aoi_list = query.get_all_aoi_list()
    with Pool(processes=WORKERS_NUM) as pool:  # 根据CPU核数调整进程数
        results = list(tqdm.tqdm(pool.imap_unordered(process_aoi, aoi_list), total=len(aoi_list)))

    # 过滤 None
    aoi_data_list = [r for r in results if r is not None]
    return aoi_data_list


def process_aoi(aoi):
    """
    aoi信息组装
    """
    try:
        aoi_bid = aoi["bid"]
        aoi_std_tag, aoi_click_pv, city = None, None, None
        aoi_poi_res = query.get_poi_info(aoi_bid)
        if aoi_poi_res:
            aoi_std_tag = aoi_poi_res[0]['std_tag']
            aoi_click_pv = aoi_poi_res[0]['click_pv']
            city = aoi_poi_res[0]['city']

        aoi_complete_res = query.get_aoi_complete(aoi_bid)
        aoi_complete = -1
        if aoi_complete_res:
            aoi_complete = aoi_complete_res[0]['aoi_complete']

        if aoi.memo:
            memo = aoi.memo
        else:
            memo = 'AOI主点'

        return AoiData(
            aoi_bid=aoi_bid,
            std_tag=aoi_std_tag,
            aoi_level=aoi["aoi_level"],
            aoi_complete=aoi_complete,
            click_pv=aoi_click_pv,
            nav_pv=query.get_nav_pv(aoi_bid),
            memo=memo,
            city=city
        )
    except Exception as e:
        print(f"处理AOI {aoi['bid']} 时出错: {e}")
        return None


def is_4categories(aoi):
    """
    判断AOI是不是四垂类
    """
    std_tag = aoi.std_tag
    if not std_tag:
        return False
    if (
        std_tag.startswith("交通设施") or
        std_tag.startswith("购物") or
        std_tag.startswith("医疗") or
        std_tag.startswith("旅游景点")
    ):
        return True
    return False


def monitor():
    """
    监控
    """
    aoi_main_poi_collection_num = query.count_aoi_main_poi_collection()
    over_heat_protection = query.count_over_heat_protection_aoi()
    high_heat_protection = query.count_high_heat_protection_aoi()
    potential_heat_protection = query.count_potential_heat_protection_aoi()
    send_hi(
        f"""
        全量AOI主点集合：{aoi_main_poi_collection_num} 条：
        ------------------------------------------------------------------
        极高热防护集合：{over_heat_protection} 条
        高热防护集合：{high_heat_protection} 条
        潜热防护集合：{potential_heat_protection} 条
        ------------------------------------------------------------------
        数据更新时间：{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        """
    )


def sd_parks_bid_list():
    """
    商单车厂aoi bid集合(写死)
    """
    return [
        '17829768439636426751',
        '9244232929967259459',
        '2683041832107392763',
        '14601799375257020007',
        '14438664612784896735',
        '3629705865131641921',
        '15021272084288246408',
        '13661956585798879981',
        '12691166372451313864 ',
        '12342549872647564996',
        '12356693807455428443',
        '15135488056844896263',
        '2569965691469509430 ',
        '2017868023209959965',
        '7885654869463766489',
        '10855720793284810040',
        '2390024550169022878',
        '12719692449981903016',
        '10107192792855857282',
        '13997781944783080287',
        '13565859887377553154',
        '11506044226182996076',
        '7137459322277787046',
        '1884034252361868928',
        '3482585248995882684',
        '354577469702980147',
        '12682731136668671212 ',
        '18011823049522806783',
        '6156448543443242602',
        '4122857195129523020 ',
        '6103427774879099230',
        '1813642690290163138',
        '12787375059926619159',
        '17121726899470281454',
        '15244775616806211153',
        '11674590110413877291',
        '6577162540085781714',
        '5581581688430155530',
        '17133906582202429771',
        '11301758480406714297',
        '2100609521118685355',
        '13820651705774549223',
        '10317482717857271384',
        '16403478381573797144',
        '5518705183560484302',
        '4139992914714511446',
        '18137888428338164833',
        '7032576862518922436',
        '10646552222943348692',
        '2812609993679454016',
        '16376430166583573493',
        '11650585954058560987',
        '6297365807887745920',
        '17483836139943173143',
        '14569681130714003778',
        '3563337554790686134',
        '6426963488116942623',
        '16412855583779023544',
        '8909590707201394911',
        '17325100012734247924',
        '6690216261484238686',
        '8849931195976471939',
        '5786505164907508078',
        '17115895110703113857',
        '5237530917424177819',
        '8551138776652805053',
        '1939545561318891312',
        '18342261811981066965',
        '2529816894862139732',
        '14944760673080072192',
        '1225314609680693021',
        '2732216071898299541',
        '15042916560299319984',
        '5776369108604993482',
        '1258625687758045183',
        '18108900364398909233',
        '864493443494611638',
        '18011971711225823231',
        '280841009547057525',
        '11014723185348582260',
        '17627517278616551423',
        '5494933142670600130',
        '9397893590151127013',
        '3418530824846942426',
        '10781819165947804669',
        '2508239587334698011',
        '4187321976367500152',
        '3631601399568203324',
        '11352393018380498854',
        '826359056074754464',
        '9754715028188550950',
        '8094532792890128174',
        '16439393984774576099',
        '4414716663146004549',
        '18361796768248428608',
        '5233488157144690883',
        '13512630834505093007',
        '1325331549425673571',
        '10794670989797870802',
        '3942099315563645374',
        '1127132685168437881',
        '14286172806462517906',
        '370051132958508899',
        '1058951239663296295',
        '17660824616853045247',
        '8378306150248557216',
        '15825659885628204222',
        '16556968682621360433',
        '17672516514265169919',
        '10135387754540170292',
        '1628998451193271922',
        '14340872307039800137',
        '1026372596644626920',
        '14592743744264079619',
        '18364792788111259373',
        '17627195035810267135',
        '10376424561016319263',
        '17629838214518800383',
        '17997791825158471679',
        '482436289066285638',
        '408761318887612476',
        '5162416228097777927',
        '17029088586522867120',
        '10751163178249604994',
        '16891120161017828639',
        '6655952583910567509',
        '16212611089099866726',
        '7628733112278697604',
        '13167232456155423625',
        '5121774588740050144',
        '18004762170532823039',
        '13311953176170435839',
        '14340918450540090287',
        '2249021793281026452',
        '18077215870981041788',
        '9665945653393090275',
        '13343813805676281516',
        '15535615661747555006',
        '3619412434573908869',
        '8180458349387793317',
        '3340531325565634071',
    ]


if __name__ == '__main__':
    """
    main
    """
    run()
