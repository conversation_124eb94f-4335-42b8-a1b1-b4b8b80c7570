"""
【R2004】高等级道路(>=5 & <= 7)与AOI边框交割点附近(30m内)无关联大门
"""
import tqdm
import multiprocessing


from shapely import wkt
from src.aoi.qc.common import query


BUFFER_DISTANCE = 30 / 110000  # 30米缓冲范围
WORKERS_NUM = 60


def run():
    """
    run
    """
    all_aoi_list = query.get_all_base_aoi()
    all_aoi_list = filter_invalid_aoi(all_aoi_list)
    check_result = check(all_aoi_list)
    save_result(check_result)


def filter_invalid_aoi(all_aoi_list):
    """
    过滤不需要建设大门的AOI
    """
    new_list = []
    for aoi in tqdm.tqdm(all_aoi_list):
        poi_bid = aoi["poi_bid"]
        poi_info_list = query.get_poi_info(poi_bid)
        if len(poi_info_list) == 0:
            continue
        poi_info = poi_info_list[0]
        show_tag, std_tag = poi_info["show_tag"], poi_info["std_tag"]
        if show_tag in ["加气站", "加油站", "加氢站"]:
            continue

        if std_tag not in [
            "旅游景点",
            "旅游景点;博物馆",
            "旅游景点;动物园",
            "旅游景点;风景区",
            "旅游景点;公园",
            "旅游景点;海滨浴场",
            "旅游景点;教堂",
            "旅游景点;景点",
            "旅游景点;其他",
            "旅游景点;水族馆",
            "旅游景点;寺庙",
            "旅游景点;文物古迹",
            "旅游景点;游乐园",
            "旅游景点;植物园",
            "交通设施;飞机场",
            "交通设施;火车站",
            "交通设施;长途汽车站",
            "购物",
            "购物;百货商场",
            "购物;超市",
            "购物;购物中心",
            "购物;家居建材",
            "购物;其他",
            "购物;市场",
            "教育培训",
            "教育培训;成人教育",
            "教育培训;高等院校",
            "教育培训;科技馆",
            "教育培训;科研机构",
            "教育培训;培训机构",
            "教育培训;其他",
            "教育培训;特殊教育学校",
            "教育培训;图书馆",
            "教育培训;小学",
            "教育培训;幼儿园",
            "教育培训;中学",
            "医疗",
            "医疗;疗养院",
            "医疗;其他",
            "医疗;专科医院",
            "医疗;综合医院",
            "运动健身;体育场馆",
            "文化传媒;广播电视",
            "文化传媒;美术馆",
            "文化传媒;文化宫",
            "文化传媒;展览馆",
            "休闲娱乐;度假村",
            "休闲娱乐;剧院",
            "休闲娱乐;休闲广场",
            "房地产",
            "房地产;其他",
            "房地产;写字楼",
            "房地产;住宅区",
            "公司企业",
            "公司企业;厂矿",
            "公司企业;公司",
            "公司企业;农林园艺",
            "公司企业;其他",
            "公司企业;园区",
            "政府机构",
            "政府机构;各级政府",
            "政府机构;公检法机构",
            "政府机构;其他",
            "政府机构;涉外机构",
            "政府机构;行政单位",
            "政府机构;政治教育机构",
            "政府机构;中央机构",
            "酒店;星级酒店",
            "生活服务;物流公司",
            "汽车服务",
            "汽车服务;其他",
            "汽车服务;汽车检测场",
            "汽车服务;汽车美容",
            "汽车服务;汽车配件",
            "汽车服务;汽车维修",
            "汽车服务;汽车销售",
        ]:
            continue
        new_list.append(aoi)
    return new_list


def check(all_aoi_list):
    """
    多进程并行检查
    """
    cpu_count = multiprocessing.cpu_count()
    max_processes = min(WORKERS_NUM, cpu_count)
    with multiprocessing.Pool(processes=max_processes) as pool:
        results = list(tqdm.tqdm(pool.imap_unordered(check_one_aoi, all_aoi_list), total=len(all_aoi_list)))
    return [r for r in results if r is not None]


def check_one_aoi(aoi):
    """
    单个AOI检查
    """
    try:
        aoi_wkt, poi_bid, face_id = aoi["aoi_wkt"], aoi["poi_bid"], aoi["face_id"]
        intersections = query.get_aoi_link_intersections(aoi_wkt, 7, 6)
        if not intersections:
            return None

        accesses = query.get_aoi_conn_accesses(poi_bid)
        points = []
        for item in intersections:
            try:
                int_geom = wkt.loads(item["int_wkt"])
            except Exception as e:
                print(e)
                continue
            int_p_buf = int_geom.buffer(BUFFER_DISTANCE)
            if not any(int_p_buf.intersects(wkt.loads(a["access_wkt"])) for a in accesses):
                points.append(int_geom)

        if points:
            points_wkt = " ".join(p.wkt for p in points)
            return {"poi_bid": poi_bid, "face_id": face_id, "points": points_wkt}
        return None
    except Exception as e:
        print(e)
        return None


def save_result(result):
    """
    保存结果到文件
    """
    with open("./R2004.tsv", "w") as file:
        header = "poi_bid\tface_id\tpoints\n"
        file.write(header)

        for item in result:
            strs = f"{item['poi_bid']}\t" f"{item['face_id']}\t" f"{item['points']}\n"
            file.write(strs)


if __name__ == "__main__":
    """
    main
    """
    run()
