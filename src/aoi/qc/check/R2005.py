"""
【R2005】名称符合命名规范的AOI大门没有父点
"""
import tqdm
import multiprocessing

from src.aoi.qc.common import query

WORKERS_NUM = 60


def run():
    """
    run
    """
    result = []
    poi_list = query.get_poi_by_name_match("^.+?-([东南西北中]{1,2})\d*门$")
    for poi in tqdm.tqdm(poi_list):
        bid, name, relation_bid = poi["bid"], poi["name"], poi["relation_bid"]
        if relation_bid in (None, 0, "0"):
            result.append({"poi_bid": bid, "name": name})
    save_result(result)


def save_result(result):
    """
    保存结果到文件
    """
    with open("./R2005.tsv", "w") as file:
        header = "bid\tname\n"
        file.write(header)

        for item in result:
            strs = f"{item['poi_bid']}\t" f"{item['name']}\n"
            file.write(strs)


if __name__ == "__main__":
    """
    main
    """
    run()
