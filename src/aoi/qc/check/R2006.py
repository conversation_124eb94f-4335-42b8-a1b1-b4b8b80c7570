"""
【R2006】小区AOI只有车行门
"""
import tqdm
import multiprocessing

from src.aoi.qc.common import query

WORKERS_NUM = 60


def run():
    """
    run
    """
    all_aoi_list = query.get_all_base_aoi()
    check_result = check(all_aoi_list)
    save_result(check_result)


def check(all_aoi_list):
    """
    多进程并行检查
    """
    cpu_count = multiprocessing.cpu_count()
    max_processes = min(WORKERS_NUM, cpu_count)
    with multiprocessing.Pool(processes=max_processes) as pool:
        results = list(tqdm.tqdm(pool.imap_unordered(check_one_aoi, all_aoi_list), total=len(all_aoi_list)))
    return [r for r in results if r is not None]


def check_one_aoi(aoi):
    """
    单个AOI检查
    """
    try:
        aoi_wkt, poi_bid, face_id = aoi["aoi_wkt"], aoi["poi_bid"], aoi["face_id"]
        accesses = query.get_aoi_conn_accesses(poi_bid)
        for access in accesses:
            traffic = calc_access_traffic(access['access_id'])
            if traffic == '仅行人':
                return None
        if len(accesses) > 0:
            return {"poi_bid": poi_bid}
        return None
    except Exception as e:
        print(e)
        return None


def calc_access_traffic(access_id):
    """
    计算access的出入口标签
    """
    traffc_obj = []
    transit_type = -1
    obj_restrict = -1
    traffic_list = query.get_access_info(access_id)
    for (_valid_obj, _transit_type, _obj_restrict) in traffic_list:
        traffc_obj.append(_valid_obj)
        if _valid_obj == 1:
            # 只有车行数据，下面的值才有效
            transit_type = _transit_type
            obj_restrict = _obj_restrict

    if 1 in traffc_obj:
        if transit_type == 1:
            if obj_restrict == 1:
                return '出入口'
            elif obj_restrict == 2:
                return '内部通道'
            elif obj_restrict == 3:
                return '出入口'
            else:
                return '出入口'
        elif transit_type == 2:
            if obj_restrict == 2:
                return '内部入口'
            else:
                return '入口'
        elif transit_type == 3:
            if obj_restrict == 2:
                return '内部出口'
            else:
                return '出口'
        return '出入口'
    elif 99 in traffc_obj:
        return '无法通行'
    elif 2 in traffc_obj:
        return '仅行人'
    return '仅行人'


def save_result(result):
    """
    保存结果到文件
    """
    with open("./R2006.tsv", "w") as file:
        header = "poi_bid\n"
        file.write(header)

        for item in result:
            strs = f"{item['poi_bid']}\n"
            file.write(strs)


if __name__ == '__main__':
    """
    main
    """
    run()