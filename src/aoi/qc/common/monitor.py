"""
监控报警
"""
import requests
import logging
import os
import datetime


def send_hi(message: str, token: str = "de5a586636a2b1f474457e39ee6c51cfb"):
    """
    发送如流消息
    markdown内容长度超过2048个字符
    """
    msgs = [{"type": "MD", "content": message}]
    webhook = f"http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token={token}"
    message = {"message": {"body": msgs}}
    resp = requests.post(webhook, json=message)
    print(resp.json())
    return resp.json()


def setup_logger():
    """
    日志设置
    """
    log_dir = "./logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    today_str = datetime.datetime.now().strftime("%Y-%m-%d")
    log_file = os.path.join(log_dir, f"log_{today_str}.log")

    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 清除已有的 handler，避免重复输出
    if logger.handlers:
        logger.handlers.clear()

    # 输出到文件
    file_handler = logging.FileHandler(log_file, encoding="utf-8")
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 输出到控制台（可选）
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger
