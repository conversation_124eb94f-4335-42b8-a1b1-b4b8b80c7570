"""
数据获取层
"""
from src.parking.recognition import dbutils
from src.tools import pgsql


def get_all_base_aoi():
    """
    获取全量AOI(非商单&基础院落)信息
    """
    sql = """
          select st_astext(a.geom), b.poi_bid, a.face_id
          from blu_face a inner join blu_face_poi b
          on a.face_id = b.face_id
          where a.aoi_level = 2 
          and a.src != 'SD'
     """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    columns = [
        "aoi_wkt",
        "poi_bid",
        "face_id",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_aoi_link_intersections(aoi_wkt: str, max_link_level, min_link_level):
    """
    获取AOI和link交割点
    """
    sql = """
        SELECT 
            ST_AsText((dp).geom) AS intersection_point
        FROM (
            SELECT ST_Dump(
                ST_CollectionExtract(
                    ST_Intersection(geom, ST_Boundary(p.poly)), 1  -- 1 = POINT 类型
                )
            ) AS dp
            FROM nav_link, (SELECT ST_GeomFromText(%s, 4326) AS poly) AS p
            WHERE 
                kind <= %s 
                AND kind >= %s
                AND ST_Intersects(geom, p.poly)
                AND NOT ST_Within(geom, p.poly)
                AND ST_Contains(p.poly, ST_EndPoint(geom))  -- 终点在面内部
                AND NOT ST_Contains(p.poly, ST_StartPoint(geom))  -- 起点在面外
        ) AS dumped;
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql, (aoi_wkt, max_link_level, min_link_level))
    columns = [
        "int_wkt",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_aoi_conn_accesses(aoi_bid: str):
    """
    获取AOI全量关联的出入口
    """
    sql = """
        select st_astext(a.geom), a.access_id
        from blu_access a inner join blu_access_gate_rel b
        on a.access_id = b.access_id
        where a.main_bid = %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (aoi_bid,))
    columns = [
        "access_wkt",
        "access_id"
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_access_info(access_id):
    """
    获取出入口信息
    """
    sql = """
       select valid_obj, transit_type, obj_restrict 
       from blu_access_traffic 
       where access_id = %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (access_id,))
    columns = [
        "valid_obj",
        "transit_type",
        "obj_restrict",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_poi_by_name_match(pattern):
    """
    获取名称匹配poi列表
    """
    sql = """
        select bid, name, relation_bid
        from poi
        where name ~ %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (pattern, ))
    columns = [
        "bid",
        "name",
        "relation_bid"
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_poi_info(bid: str):
    """
    获取POI信息
    """
    sql = """
        select 
            show_tag, 
            std_tag, 
            click_pv, 
            city
        from poi
        where bid = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (bid, ))
    columns = [
        "show_tag",
        "std_tag",
        "click_pv",
        "city"
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def truncate_collections():
    """
    清表数据
    """
    sql = """
        truncate table aoi_main_poi;
        truncate table high_heat_protection_aoi;
        truncate table potential_heat_protection_aoi;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()


def count_aoi_main_poi_collection():
    """
    计算aoi主点集合总量
    """
    sql = """
        select count(distinct aoi_bid)
        from aoi_main_poi
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def count_over_heat_protection_aoi():
    """
    计算极高热防护集合aoi总量
    """
    sql = """
        select count(distinct aoi_bid)
        from over_heat_protection_aoi
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def count_high_heat_protection_aoi():
    """
    计算高热防护集合aoi总量
    """
    sql = """
        select count(distinct aoi_bid)
        from high_heat_protection_aoi
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def count_potential_heat_protection_aoi():
    """
    计算潜热防护集合aoi总量
    """
    sql = """
        select count(distinct aoi_bid)
        from potential_heat_protection_aoi
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_all_aoi_list():
    """
    获取所有AOI
    """
    sql = """
        select b.poi_bid, a.aoi_level
        from blu_face a inner join blu_face_poi b 
        on a.face_id = b.face_id
        where a.src!= 'SD'
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    columns = [
        "bid",
        "aoi_level"
    ]
    # 映射为 dict 列表
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_aoi_info(bid):
    """
    获取AOI
    """
    sql = """
        select b.poi_bid, a.aoi_level
        from blu_face a inner join blu_face_poi b 
        on a.face_id = b.face_id
        where a.src!= 'SD' and b.poi_bid = %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (bid, ))
    columns = [
        "bid",
        "aoi_level"
    ]
    # 映射为 dict 列表
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_aoi_complete(bid):
    """
    获取AOI精准化字段
    """
    sql = """
        select aoi_complete 
        from blu_face_complete 
        where main_bid = %s;
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (bid, ))
    columns = [
        "aoi_complete",
    ]
    # 映射为 dict 列表
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_nav_pv(bid: str):
    """
    获取月导航pv
    """
    sql = """
        select pv
        from poi_nav_500m_pv
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    if ret is None:
        return -1
    return ret[0]


def copy_aoi_main_poi_data_to_table(data, batch_size=5000):
    """
     批量写入数据到指定表中
     params：
        data: list[dict] - 每条记录是一个 dict
        table_name: str - 目标表名
        batch_size: int - 每批写入的数据量
    """
    if not data:
        return

    insert_sql_template = f"""
         INSERT INTO aoi_main_poi (
             aoi_bid, std_tag, aoi_level, aoi_complete,
             click_pv, nav_pv, memo, city, is_4categories
         ) VALUES (
             %(aoi_bid)s, %(std_tag)s, %(aoi_level)s, %(aoi_complete)s,
             %(click_pv)s, %(nav_pv)s, %(memo)s, %(city)s, %(is_4categories)s
         ) ON CONFLICT ON CONSTRAINT uniq_aoi_bid DO NOTHING;
    """

    total = len(data)
    for i in range(0, total, batch_size):
        batch = data[i: i + batch_size]
        try:
            conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
            pgsql.execute_many(conn, insert_sql_template, batch)
            print("已写入 %d / %d 条数据" % (min(i + batch_size, total), total))
        except Exception as e:
            print(f"第 {i}-{i + batch_size} 条记录写入失败: {e}")


def copy_high_heat_data_to_table(data, batch_size=5000):
    """
     批量写入数据到指定表中
     params：
        data: list[dict] - 每条记录是一个 dict
        table_name: str - 目标表名
        batch_size: int - 每批写入的数据量
    """
    if not data:
        return

    insert_sql_template = f"""
         INSERT INTO high_heat_protection_aoi (
             aoi_bid, std_tag, aoi_level, aoi_complete,
             click_pv, nav_pv, memo, city
         ) VALUES (
             %(aoi_bid)s, %(std_tag)s, %(aoi_level)s, %(aoi_complete)s,
             %(click_pv)s, %(nav_pv)s, %(memo)s, %(city)s
         )
    """

    total = len(data)
    for i in range(0, total, batch_size):
        batch = data[i: i + batch_size]
        try:
            conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
            pgsql.execute_many(conn, insert_sql_template, batch)
            print("已写入 %d / %d 条数据" % (min(i + batch_size, total), total))
        except Exception as e:
            print(f"第 {i}-{i + batch_size} 条记录写入失败: {e}")


def copy_potential_heat_data_to_table(data, batch_size=5000):
    """
     批量写入数据到指定表中
     params：
        data: list[dict] - 每条记录是一个 dict
        table_name: str - 目标表名
        batch_size: int - 每批写入的数据量
    """
    if not data:
        return

    insert_sql_template = f"""
         INSERT INTO potential_heat_protection_aoi (
             aoi_bid, std_tag, aoi_level, aoi_complete,
             click_pv, nav_pv, memo, city
         ) VALUES (
             %(aoi_bid)s, %(std_tag)s, %(aoi_level)s, %(aoi_complete)s,
             %(click_pv)s, %(nav_pv)s, %(memo)s, %(city)s
         )
    """

    total = len(data)
    for i in range(0, total, batch_size):
        batch = data[i: i + batch_size]
        try:
            conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
            pgsql.execute_many(conn, insert_sql_template, batch)
            print("已写入 %d / %d 条数据" % (min(i + batch_size, total), total))
        except Exception as e:
            print(f"第 {i}-{i + batch_size} 条记录写入失败: {e}")


def insert_over_heat_data_to_table(data, batch_size=5000):
    """
    将数据插入 potential_heat_protection_aoi 表中，避免重复插入（以 aoi_bid 为唯一键）。
    :param data: list[dict] - 每条记录是一个 dict
    :param batch_size: int - 每批写入的数据量
    """
    if not data:
        return

    insert_sql_template = """
       INSERT INTO over_heat_protection_aoi (
           aoi_bid, std_tag, aoi_level, aoi_complete,
           click_pv, nav_pv, memo, city
       ) VALUES (
           %(aoi_bid)s, %(std_tag)s, %(aoi_level)s, %(aoi_complete)s,
           %(click_pv)s, %(nav_pv)s, %(memo)s, %(city)s
       )
       ON CONFLICT (aoi_bid) DO NOTHING
    """

    total = len(data)
    for i in range(0, total, batch_size):
        batch = data[i: i + batch_size]
        try:
            conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
            pgsql.execute_many(conn, insert_sql_template, batch)
            print("已写入 %d / %d 条数据" % (min(i + batch_size, total), total))
        except Exception as e:
            print(f"第 {i}-{i + batch_size} 条记录写入失败: {e}")


def get_over_heat_pv_data():
    """
    获取极高热数据
    """
    sql = """
        select 
          aoi_bid, std_tag, aoi_level, aoi_complete,
          click_pv, nav_pv, memo, city
        from aoi_main_poi
        where 
          memo = 'che_chang_data' or (
          nav_pv >= 2000 or
          click_pv >= 100000)
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = [
        "aoi_bid",
        "std_tag",
        "aoi_level",
        "aoi_complete",
        "click_pv",
        "nav_pv",
        "memo",
        "city",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_high_heat_pv_data():
    """
    获取高热数据
    """
    sql = """
      select 
          aoi_bid, std_tag, aoi_level, aoi_complete,
          click_pv, nav_pv, memo, city
      from aoi_main_poi
      where 
        (
            (nav_pv >= 500 and nav_pv < 2000) or (click_pv >= 20000 and click_pv < 100000)
        ) or
        (
            is_4categories is TRUE and 
            (
                (nav_pv >= 300 and nav_pv < 2000) or (click_pv >= 5000 and click_pv < 100000)
            )
        ) or 
        (
            city in ('北京市', '上海市', '广州市', '深圳市') and 
            (
                (nav_pv >= 300 and nav_pv < 2000) or (click_pv >= 5000 and click_pv < 100000)
            )
        )
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = [
        "aoi_bid",
        "std_tag",
        "aoi_level",
        "aoi_complete",
        "click_pv",
        "nav_pv",
        "memo",
        "city",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_potential_heat_pv_data():
    """
    获取潜热数据
    """
    sql = """
      select 
        aoi_bid, std_tag, aoi_level, aoi_complete,
        click_pv, nav_pv, memo, city
      from aoi_main_poi
      where aoi_complete >= 4  
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = [
        "aoi_bid",
        "std_tag",
        "aoi_level",
        "aoi_complete",
        "click_pv",
        "nav_pv",
        "memo",
        "city",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result