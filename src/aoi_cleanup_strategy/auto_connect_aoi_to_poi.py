# !/usr/bin/env python3
"""
对无主点的 AOI 例行化关联主点。
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv
from src.tools.connect_aoi_to_poi import BatchMainPoiTool, try_connect_to_poi

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文。
    """
    work_dir: Path
    bid_list_path: Path
    mode: str
    face_ids: list[str] = field(default_factory=list)
    batch_tool: BatchMainPoiTool = BatchMainPoiTool()

    def insert_batch_record(self, record):
        """
        插入批处理执行记录。
        """
        self.batch_tool.insert_record(*record)


def fill_face_ids_by_lib(ctx: Context):
    """
    获取所有无主点的 face_id 集合。
    """
    business_area = '52'
    cluster_yard = 1

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = f'''
            select face_id
            from blu_face a
            where a.aoi_level > %s and 
                  a.kind != %s and
                  not exists (select * from blu_face_poi b where a.face_id = b.face_id);
        '''
        ctx.face_ids = [x[0] for x in pgsql.fetch_all(conn, sql, (cluster_yard, business_area))]


def fill_face_ids_by_file(ctx: Context):
    """
    从文件读取 face_id 集合。
    """
    with open(ctx.bid_list_path, 'r') as f:
        ctx.face_ids = [x.strip() for x in f]


@desc()
def fill_face_ids(ctx: Context, proceed):
    if ctx.mode == 'lib':
        fill_face_ids_by_lib(ctx)
    elif ctx.mode == 'file':
        fill_face_ids_by_file(ctx)

    proceed()


@desc()
def generate_connect_to_poi_records(ctx: Context, proceed):
    """
    生产主点关联批处理记录。
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for face_id in tqdm(ctx.face_ids):
            sql = f'''select st_astext(geom), mesh_id from blu_face where face_id = %s;'''
            aoi = pgsql.fetch_one(aoi_conn, sql, (face_id,))
            if not aoi:
                continue

            wkt, mesh_id = aoi
            connected_poi = try_connect_to_poi(poi_conn, aoi_conn, shapely.wkt.loads(wkt))

            if not connected_poi:
                continue

            ctx.insert_batch_record((
                connected_poi.bid,
                face_id,
                'SD',  # 无主点自动关联时固定批处理为商单数据。
                mesh_id,
                connected_poi.mid,
                'AOI 无主点关联',
            ))

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存批处理记录到文件。
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.tsv',
        [
            [
                x['face_id'],
                x['bid']
            ]
            for x in ctx.batch_tool.batch_records
        ]
    )
    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理操作。
    """
    ctx.batch_tool.execute()
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        default='lib',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    脚本主函数。
    """
    main_pipe = pipeline.Pipeline(
        fill_face_ids,
        generate_connect_to_poi_records,
        save_records,
        # execute_batch,
    )
    desc.attach(main_pipe)

    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/auto_connect_aoi_to_poi'),
        bid_list_path=Path(args.bid_list_path),
        mode=args.mode,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
