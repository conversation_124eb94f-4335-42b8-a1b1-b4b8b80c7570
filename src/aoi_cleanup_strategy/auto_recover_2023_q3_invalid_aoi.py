# !/usr/bin/env python3
"""
专门用于恢复 2023-Q3 失效的 AOI 主点。
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process import tags
from src.tools import pipeline, pgsql
from src.tools.connect_aoi_to_poi import BatchMainPoiTool

desc = pipeline.get_desc()


@dataclass
class ConnectionRecord:
    """
    用于存储主点关联信息。
    """
    face_id: str
    bid: str
    resume_bid: str = ''
    resume_mid: str = ''
    resume_mesh_id: str = ''
    resume_src: str = ''
    current_bid: str = ''
    reason: str = ''
    can_recover: bool = True


@dataclass
class Context:
    """
    脚本执行上下文。
    """
    work_dir: Path
    connection_records: list[ConnectionRecord] = field(default_factory=list)
    batch_tool: BatchMainPoiTool = BatchMainPoiTool()

    def insert_batch_record(self, record):
        """
        插入批处理执行记录。
        """
        self.batch_tool.insert_record(*record)


@desc()
def load_connection_records(ctx: Context, proceed):
    """
    脚本执行上下文。
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = f'''
            select face_id, bid
            from gate_bid_change_history 
            where action = 'connect_to_poi' and 
                  src = '主点失效商单 AOI 关联 POI';
        '''
        for face_id, bid in pgsql.fetch_all(conn, sql):
            ctx.connection_records.append(ConnectionRecord(face_id=face_id, bid=bid))

    proceed()


@desc()
def load_resume_bids(ctx: Context, proceed):
    """
    加载履历库信息。
    """
    with pgsql.get_connection(pgsql.AOI_RESUME_SLAVER_CONFIG) as conn:
        for record in tqdm(ctx.connection_records):
            if not record.can_recover:
                continue

            sql = f'''
                select bid, mid, src, mesh_id
                from blu_record
                where face_id = %s and strategy != 'auto'
                order by create_time desc
                limit 1;
            '''
            resume_info = pgsql.fetch_one(conn, sql, (record.face_id,))
            if not resume_info:
                record.reason = '履历库中无记录'
                record.can_recover = False
                continue

            bid, mid, src, mesh_id = resume_info
            if bid == record.bid:
                record.reason = '和履历库一致'
                record.can_recover = False
                continue

            record.resume_bid = bid
            record.resume_mid = mid
            record.resume_mesh_id = mesh_id
            record.resume_src = src

    proceed()


@desc()
def filter_records_by_poi_validation(ctx: Context, proceed):
    """
    过滤当前依然失效的记录。
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        for record in tqdm(ctx.connection_records):
            if not record.can_recover:
                continue

            sql = f'''
                select std_tag from poi where bid = %s;
            '''
            row = pgsql.fetch_one(conn, sql, (record.resume_bid,))
            if not row:
                record.reason = '当前主点已失效'
                record.can_recover = False
                continue

            std_tag, = row
            if std_tag in tags.ADMIN:
                record.reason = '行政区划 tag'
                record.can_recover = False

    proceed()


def get_processed_face_ids():
    """
    获取历史处理过的 bid 集合。
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = f'''
            select face_id 
            from gate_bid_change_history 
            where action = 'update_main_poi' and
                  src = 'AOI Q3 主点失效恢复';
        '''
        return [x[0] for x in pgsql.fetch_all(conn, sql)]


@desc()
def filter_records_by_history(ctx: Context, proceed):
    """
    如果历史处理过，则跳过。
    """
    processed_face_ids = set(get_processed_face_ids())
    for record in tqdm(ctx.connection_records):
        if not record.can_recover:
            continue

        if record.face_id in processed_face_ids:
            record.reason = '当前边框历史处理过'
            record.can_recover = False

    proceed()


@desc()
def fill_batch_records(ctx: Context, proceed):
    """
    填充批处理记录。
    """
    for record in ctx.connection_records:
        if not record.can_recover:
            continue
        ctx.insert_batch_record((
            record.resume_bid,
            record.face_id,
            record.resume_src,
            record.resume_mesh_id,
            record.resume_mid,
            'AOI Q3 主点失效恢复',
        ))

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理操作。
    """
    ctx.batch_tool.execute()
    proceed()


def main():
    """
    脚本主函数。
    """
    main_pipe = pipeline.Pipeline(
        load_connection_records,
        load_resume_bids,
        filter_records_by_poi_validation,
        filter_records_by_history,
        fill_batch_records,
        execute_batch,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/dingping/recover_invalid_main_poi'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
