# !/usr/bin/env python3
"""
用于自动恢复失效的边框。
see: https://ku.baidu-int.com/d/_s5O6CS0rJ_qI6
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.batch_helper import get_geom_iou
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, notice_tool, tsv
from src.tools.connect_aoi_to_poi import try_connect_to_poi, BatchMainPoiTool

GET_INVALID_RECORDS_SQL = '''
    select id, face_id, src, original_bid, current_bid
    from invalid_aoi_history
    where status = 0;
'''
GET_POI_SQL = '''
    select mid, st_astext(geometry), click_pv from poi where bid = %s;
'''

GET_POI_OLD_SQL = '''
    select mid, st_astext(geometry), click_pv from poi_old where bid = %s;
'''

GET_ALL_AOI_SQL = '''
    select a.face_id, a.src, b.poi_bid, a.aoi_level, a.kind, st_astext(a.geom)
    from blu_face a
    
    inner join blu_face_poi b
    on a.face_id = b.face_id
    
    where a.aoi_level > %s and a.update_time < %s and
          a.kind != %s;
'''
GET_AOI_COMPLETE_SQL = '''
    select aoi_complete from blu_face_complete where face_id = %s;
'''
GET_AOI_PROPERTIES_BY_FACE_ID_SQL = '''
    select st_astext(geom), mesh_id from blu_face where face_id = %s;
'''
GET_AOI_PROPERTIES_BY_BID_SQL = '''
    select a.face_id, a.mesh_id, a.src, st_astext(geom)
    from blu_face a
    
    inner join blu_face_poi b
    on a.face_id = b.face_id
    
    where b.poi_bid = %s
    limit 1;
'''
GET_REPEAT_RECORD_SQL = '''
    select release_bid from aoi_repeat_bid where invalid_bid = %s;
'''

desc = pipeline.get_desc()


@dataclass
class InvalidRecord:
    """
    用于存储主点失效的边框信息。
    """
    face_id: str
    id: int = -1
    mesh_id: str = ''
    mid: str = ''
    src: str = ''
    original_bid: str = ''
    current_bid: str = ''
    batch_bid: str = ''
    aoi_level: int = 0
    kind: str = ''
    wkt: str = ''
    aoi_complete: int = 0
    click_pv: int = 0
    mode: str = ''
    status: int = 0
    is_new: bool = False


@dataclass
class Context:
    """
    脚本执行上下文。
    """
    work_dir: Path
    records: list[InvalidRecord] = field(default_factory=list)
    batch_tool: BatchMainPoiTool = BatchMainPoiTool()
    processed_face_ids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def insert_batch_record(self, record):
        """
        插入批处理执行记录。
        """
        self.batch_tool.insert_record(*record)


def filter_records_by_tag(poi_conn: PgsqlStabilizer, bid):
    """
    根据 tag 过滤掉不需要处理的记录。
    """
    sql = '''
        select std_tag from poi where bid = %s;
    '''
    row = poi_conn.fetch_one(sql, (bid,))
    if not row:
        return False

    std_tag, = row
    if std_tag in tags.ADMIN:
        return False

    return True


@desc()
def load_history_records(ctx: Context, proceed):
    """
    加载历史失效记录。
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for record_id, face_id, src, original_bid, current_bid in tqdm(poi_conn.fetch_all(GET_INVALID_RECORDS_SQL)):
            ctx.records.append(InvalidRecord(
                id=record_id,
                face_id=face_id,
                src=src,
                original_bid=original_bid,
                current_bid=current_bid,
            ))

    proceed()


@desc()
def fill_status_for_history_records(ctx: Context, proceed):
    """
    填充历史失效记录的状态
    """
    with (
        PgsqlStabilizer(pgsql.AOI_RESUME_SLAVER_CONFIG) as resume_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_conn
    ):
        for record in tqdm(ctx.records):
            row = aoi_conn.fetch_one(GET_AOI_PROPERTIES_BY_FACE_ID_SQL, (record.face_id,))
            if row is None:
                record.status = -1  # 边框不存在
                continue

            record.wkt, record.mesh_id = row

            if record.original_bid == '' or poi_conn.fetch_one(GET_POI_SQL, (record.original_bid,)):
                # 初始 bid 空的是 poi 推送检索失败导致的，在下一次批处理恢复主点之前策略或者人工可能已经修复，这里将 bid
                # 为空的记录视为已经恢复，以便后续环节通过履历库反推到人工作业成果。
                record.status = 1  # 已恢复
            else:
                # 失效缓存需要同步履历库
                resume_info = resume_conn.fetch_one(get_select_latest_resume_sql(True), (record.face_id,))
                if resume_info:
                    resume_bid, resume_mid, resume_src, resume_mesh_id = resume_info
                    if resume_bid != record.original_bid and resume_src != 'SD':
                        if poi_conn.fetch_one(GET_POI_SQL, (resume_bid,)):
                            record.original_bid = resume_bid
                            record.src = resume_src
                            record.status = 1  # 已恢复
                    else:
                        record.src = resume_src

        proceed()


@desc()
def load_new_records(ctx: Context, proceed):
    """
    加载最新失效的记录。
    """
    business_area = '52'
    cluster_yard = 1
    maintained_face_ids = {x.face_id for x in ctx.records if x.status == 0}

    with (
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn
    ):
        # 考虑到bid同步, 5天之内有修改(新增)记录的数据不处理,保障数据能够正常新增上
        day_before = (datetime.date.today() - datetime.timedelta(days=5)).strftime("%Y-%m-%d")
        for aoi in tqdm(aoi_conn.fetch_all(GET_ALL_AOI_SQL, (cluster_yard, day_before, business_area))):
            face_id, src, bid, aoi_level, kind, wkt = aoi
            if face_id in maintained_face_ids:
                continue
            # 连续两天的记录查不到再处理,防止单天的大文件不全
            if not poi_conn.fetch_one(GET_POI_SQL, (bid,)) and not poi_conn.fetch_one(GET_POI_OLD_SQL, (bid,)):
                ctx.records.append(InvalidRecord(
                    face_id=face_id,
                    src=src,
                    original_bid=bid,
                    current_bid=bid,
                    aoi_level=aoi_level,
                    kind=kind,
                    wkt=wkt,
                    is_new=True,
                ))

    proceed()


@desc()
def fill_aoi_complete_for_new_records(ctx: Context, proceed):
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_conn:
        for record in tqdm([x for x in ctx.records if x.is_new]):
            row = aoi_conn.fetch_one(GET_AOI_COMPLETE_SQL, (record.face_id,))
            if row is None:
                continue

            record.aoi_complete, = row

    proceed()


@desc()
def generate_reconnect_to_poi_records(ctx: Context, proceed):
    """
    对处于失效中的边框尝试重新关联主点。
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for record in tqdm([x for x in ctx.records if x.status == 0]):
            # 存量失效 AOI 不再重新关联主点
            if not record.is_new:
                continue

            aoi = pgsql.fetch_one(aoi_conn, GET_AOI_PROPERTIES_BY_FACE_ID_SQL, (record.face_id,))
            if not aoi:
                continue

            wkt, mesh_id = aoi
            connected_poi = try_connect_to_poi(poi_conn, aoi_conn, shapely.wkt.loads(wkt), ignore_online_relation=True)

            should_reconnect = not (
                    connected_poi is None or
                    connected_poi.bid == record.current_bid or
                    connected_poi.bid == record.original_bid
            )

            if should_reconnect and not filter_records_by_tag(poi_conn, connected_poi.bid):
                continue

            # WARNING: 有的记录虽然不需要重新关联，但也需要尝试批处理主点，
            # 因为流式入库有失败的可能，历史推送的批处理可能没有执行成功，本次还需要推送一次。
            pending_connect_bid, pending_connect_mid = (
                (connected_poi.bid, connected_poi.mid) if should_reconnect
                else (record.current_bid, get_mid_of_poi(poi_conn, record.current_bid))
            )

            if pending_connect_mid is None:
                continue

            # 如果要批处理的 bid 和母库已经一致，则无需批处理。
            current_master_lib_bid = get_main_poi(aoi_conn, record.face_id)
            if pending_connect_bid == current_master_lib_bid:
                continue

            record.current_bid = pending_connect_bid
            record.mode = 'reconnect'
            ctx.insert_batch_record((
                pending_connect_bid,
                record.face_id,
                'SD',  # 重新关联时固定批处理为商单数据。
                mesh_id,
                pending_connect_mid,
                'AOI 主点失效重新关联',
            ))

    proceed()


def get_select_latest_resume_sql(is_manual: bool):
    """
    获取查询最新履历库记录的 SQL。
    """
    return f'''
        select bid, mid, src, mesh_id
        from blu_record
        where face_id = %s and
              task_id != ''
        order by create_time desc
        limit 1;
    ''' if is_manual else f'''
        select bid, mid, src, mesh_id 
        from blu_record 
        where face_id = %s 
        order by create_time desc 
        limit 1;
    '''


def get_main_poi(aoi_conn, face_id):
    """
    获取指定 aoi 的主点
    """
    sql = '''
        select poi_bid from blu_face_poi where face_id = %s limit 1;
    '''
    row = pgsql.fetch_one(aoi_conn, sql, (face_id,))
    if row is None:
        return None

    return row[0]


@desc()
def generate_recover_aoi_records_with_repeat_lib(ctx: Context, proceed):
    """
    使用判重库恢复主点失效边框。
    https://console.cloud.baidu-int.com/devops/icafe/issue/poiinshanghai-13624/show?source=copy-shortcut
    """
    min_iou = 0.8
    min_pv = 210

    with (
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_conn,
    ):
        for record in tqdm([x for x in ctx.records if x.status == 0]):
            # 没有判重记录直接跳过。
            row = poi_conn.fetch_one(GET_REPEAT_RECORD_SQL, (record.original_bid,))
            if row is None:
                continue

            # 判重后的 bid 依然失效，直接跳过。
            release_bid, = row
            row = poi_conn.fetch_one(GET_POI_SQL, (release_bid,))
            if row is None:
                continue

            # 判重后主点不在边框内，直接跳过。
            release_mid, release_poi_wkt, release_pv = row
            release_poi_geom = shapely.wkt.loads(release_poi_wkt)
            aoi_geom = shapely.wkt.loads(record.wkt)
            if not aoi_geom.contains(release_poi_geom):
                continue

            # 判重后的 bid 无对应边框，则换绑 + toC。
            row = aoi_conn.fetch_one(GET_AOI_PROPERTIES_BY_BID_SQL, (release_bid,))
            if row is None:
                ctx.insert_batch_record((
                    release_bid,  # 换绑到判重后的 bid
                    record.face_id,  # faceid 还是原来的
                    'CD',  # toC
                    record.mesh_id,  # 图幅号保持不变
                    release_mid,  # mid 也是用判重后的
                    'AOI 主点失效通过判重库恢复'
                ))
                record.status = 1
                record.mode = 'repeat'
                continue

            # 判重后的 bid 有对应边框，且 faceid 前后一致，则仅需 toC 即可。
            release_face_id, release_mesh_id, release_src, release_aoi_wkt = row
            if release_face_id == record.face_id:
                ctx.insert_batch_record((
                    release_bid,  # 保持换绑后的  bid 不变
                    record.face_id,  # faceid 不变
                    'CD',  # toC
                    release_mesh_id,  # 图幅号也是换绑后的
                    release_mid,  # mid 同上
                    'AOI 主点失效通过判重库恢复'
                ))
                record.status = 1
                record.mode = 'repeat'
                continue

            # faceid 前后不一致，且换绑后的边框是 toC 的则直接跳过。
            if release_src != 'SD':
                continue

            # 换绑后的边框是 toB，若此时两个边框交并比大于一定阈值，则仅需 toC 即可。
            release_aoi_geom = shapely.wkt.loads(release_aoi_wkt)
            if get_geom_iou(release_aoi_geom, aoi_geom) >= min_iou:
                ctx.insert_batch_record((
                    release_bid,  # 保持换绑后的  bid 不变
                    release_face_id,  # faceid 是换绑后的
                    'CD',  # toC
                    release_mesh_id,  # 图幅号也是换绑后的
                    release_mid,  # mid 同上
                    'AOI 主点失效通过判重库恢复'
                ))
                record.status = 1
                record.mode = 'repeat'
                continue

            # 交并比小于阈值，此时如果判重后的 pv 大于一定阈值，则投放人工。
            if release_pv > min_pv:
                # 投放人工
                record.mode = 'manual'
                pass

    proceed()


@desc()
def generate_recover_aoi_records_with_resume_lib(ctx: Context, proceed):
    """
    使用履历库恢复主点失效边框。
    """
    with (
        pgsql.get_connection(pgsql.AOI_RESUME_SLAVER_CONFIG) as resume_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        pgsql.get_connection(pgsql.CD_CONFIG) as cd_conn,
    ):
        for record in tqdm([x for x in ctx.records if x.status == 1 and x.mode == '']):
            # 如果履历库中最新的记录不是人工产生的，那就往上推，直到找到人工产生的记录。
            # 如果往上推找不到人工记录，则不进行批处理。
            # 如果找到了，继续判断人工绑定的 bid 是否已经恢复，如果恢复了则进行批处理。
            resume_info = pgsql.fetch_one(resume_conn, get_select_latest_resume_sql(True), (record.face_id,))
            if resume_info:
                resume_bid, resume_mid, resume_src, resume_mesh_id = resume_info
                if resume_bid == '':
                    # bid 为空，则是人工新增 poi，此时需要到成果库中查询 bid。
                    sql = f'''select bid from poi where mid = %s;'''
                    row = pgsql.fetch_one(cd_conn, sql, (resume_mid,))
                    if not row:
                        continue

                    resume_bid, = row

                # 判断 bid 是否恢复
                if not poi_conn.fetch_one(GET_POI_SQL, (resume_bid,)):
                    continue

                current_info = pgsql.fetch_one(resume_conn, get_select_latest_resume_sql(False), (record.face_id,))
                current_bid, _, current_src, _ = current_info

                # 如果母库最新的数据已经恢复（可能是通过其它手段恢复的），则跳过处理。
                if (resume_bid == current_bid and resume_src == current_src) or (current_src != 'SD'):
                    ctx.processed_face_ids.add(record.face_id)
                    continue

                if not filter_records_by_tag(poi_conn, resume_bid):
                    continue

                record.mode = 'resume'
                record.batch_bid = resume_bid

                ctx.insert_batch_record((
                    resume_bid,
                    record.face_id,
                    resume_src,
                    resume_mesh_id,
                    resume_mid,
                    'AOI 主点失效通过履历库恢复'
                ))

    proceed()


def get_mid_of_poi(poi_conn, bid: str):
    """
    获取 poi 的 mid。
    """
    sql = f'''select mid from poi where bid = %s;'''
    row = poi_conn.fetch_one(sql, (bid,))
    if not row:
        return None

    return row[0]


@desc()
def generate_recover_aoi_records_with_history(ctx: Context, proceed):
    """
    直接使用历史记录恢复主点失效边框。
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for record in tqdm([x for x in ctx.records if x.status == 1 and x.mode == '']):
            sql = f''' select mesh_id from blu_face where face_id = %s;'''
            aoi = pgsql.fetch_one(aoi_conn, sql, (record.face_id,))
            if not aoi:
                continue
            mesh_id = aoi[0]

            mid = get_mid_of_poi(poi_conn, record.original_bid)
            if mid is None:
                continue

            if not filter_records_by_tag(poi_conn, record.original_bid):
                continue

            record.mode = 'history'
            record.batch_bid = record.original_bid

            ctx.insert_batch_record((
                record.original_bid,
                record.face_id,
                record.src,
                mesh_id,
                mid,
                'AOI 主点失效通过历史记录恢复',
            ))

    proceed()


@desc()
def save_batch_records(ctx: Context, proceed):
    """
    保存批处理记录。
    """
    output_items = []
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for record in tqdm(ctx.batch_tool.batch_records):
            bid = record['bid']
            sql = 'select name, click_pv, std_tag from poi where bid = %s;'
            name, click_pv, std_tag = pgsql.fetch_one(poi_conn, sql, (bid,))
            output_items.append(
                [
                    name,
                    click_pv,
                    std_tag,
                    record['face_id'],
                    record['bid'],
                    record['src'],
                    record['extra'],
                ]
            )

        tsv.write_tsv(
            ctx.work_dir / 'output.tsv',
            output_items
        )

    proceed()


@desc()
def save_debug_records(ctx: Context, proceed):
    tsv.write_tsv(
        ctx.work_dir / 'debug.tsv',
        [
            [
                x.status,
                x.mode,
                x.face_id,
                x.mesh_id,
                x.mid,
                x.src,
                x.original_bid,
                x.current_bid,
                x.batch_bid,
                x.aoi_level,
                x.kind,
                # x.wkt,
                x.aoi_complete,
                x.click_pv,
            ]
            for x in ctx.records
        ]
    )
    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理操作。
    """
    ctx.processed_face_ids = ctx.processed_face_ids.union(set(ctx.batch_tool.execute()))
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知。
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'主点失效自动恢复脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d83586c9c29feea30d4fbe3da7edc2669'
    )


def execute_sql_on_history_lib(records, execute):
    """
    封装历史失效表上的事务操作。
    """
    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
    ):
        try:
            for record in tqdm(records):
                execute(cursor, record)

            conn.commit()
        except Exception as e:
            print(e)
            alert_to_infoflow(e)
            conn.rollback()


@desc()
def update_recovered_records_from_history_lib(ctx: Context, proceed):
    """
    更新已经恢复的失效记录。
    """
    sql = f'''
        update invalid_aoi_history 
        set status = %s, restore_time_v0 = now() 
        where id = %s;
    '''

    deleted_records = [x for x in ctx.records if x.status == -1]
    recovered_records = [x for x in ctx.records if x.status == 1 and x.face_id in ctx.processed_face_ids]
    execute_sql_on_history_lib(
        deleted_records + recovered_records,
        lambda cursor, record: cursor.execute(sql, (record.status, record.id,))
    )
    proceed()


@desc()
def update_maintained_records_of_history_lib(ctx: Context, proceed):
    """
    更新失效记录的主点信息。
    """
    sql = f'''
        update invalid_aoi_history 
        set current_bid = %s, 
            updated_at = now() 
        where id = %s and 
              current_bid != %s;
    '''
    execute_sql_on_history_lib(
        [x for x in ctx.records if x.status == 0 and x.face_id in ctx.processed_face_ids],
        lambda cursor, record: cursor.execute(sql, (record.current_bid, record.id, record.current_bid))
    )
    proceed()


@desc()
def insert_new_records_to_history_lib(ctx: Context, proceed):
    """
    插入新的失效记录。
    """
    sql = f'''
        insert into invalid_aoi_history(
            face_id, 
            src,
            original_bid,
            current_bid,
            aoi_level,
            aoi_complete,
            kind,
            click_pv,
            geom
        ) 
        values(%s, %s, %s, %s, %s, %s, %s, %s, st_geomfromtext(%s, 4326));
    '''
    execute_sql_on_history_lib(
        [x for x in ctx.records if x.is_new and x.face_id in ctx.processed_face_ids],
        lambda cursor, record: cursor.execute(
            sql,
            (
                record.face_id,
                record.src,
                record.original_bid,
                record.current_bid,
                record.aoi_level,
                record.aoi_complete,
                record.kind,
                record.click_pv,
                record.wkt,
            )
        )
    )
    proceed()


def main():
    """
    脚本主函数。
    """
    main_pipe = pipeline.Pipeline(
        load_history_records,
        fill_status_for_history_records,
        load_new_records,
        fill_aoi_complete_for_new_records,
        generate_recover_aoi_records_with_repeat_lib,
        generate_recover_aoi_records_with_resume_lib,
        generate_recover_aoi_records_with_history,
        generate_reconnect_to_poi_records,
        save_batch_records,
        save_debug_records,

        # 调试的时候删除
        execute_batch,
        update_recovered_records_from_history_lib,
        update_maintained_records_of_history_lib,
        insert_new_records_to_history_lib,
    )
    desc.attach(main_pipe)

    ctx = Context(
        work_dir=Path('cache/auto_recover_invalid_aoi'),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
