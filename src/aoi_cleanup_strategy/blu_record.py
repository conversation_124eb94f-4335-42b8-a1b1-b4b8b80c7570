# !/usr/bin/env python3
"""
履历库模块
"""
from src.tools import pgsql

ADD = '1'
DELETE = '2'
UPDATE = '3'


def snapshot(face_ids: list[str], task_id: str, action_type=UPDATE):
    """
    将给定 face_id 所对应 AOI 的当前信息记录到履历库。
    注意：本函数不会记录修改前的 geom 信息，即 old_geom 字段（记录该字段的合理性还有待讨论）。
    @param face_ids: 待记录履历库的 AOI face_id。
    @param task_id: 本次改动任务的标识，用于方便日后检索同批次的变更记录。
    @param action_type: 履历类型：Add - '1', Delete - '2', Update - '3'。
    """
    if not face_ids:
        return

    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as back_conn,
        pgsql.get_connection(pgsql.AOI_RESUME_CONFIG) as track_conn,
    ):
        sql = get_query_blu_face_info_sql()
        ret = pgsql.fetch_all(back_conn, sql, [tuple(face_ids)])
        if not ret:
            return

        ret = [
            [x.replace("'", "''") if type(x) == str else x for x in items]
            for items in ret
        ]
        values = [
            f'''
                (
                  '{face_id}',
                  '{poi_bid if poi_bid else ""}',
                  '{name_ch}',
                  '{action_type}',
                  '',
                  '{task_id}',
                  '{kind}',
                  '{mesh_id}',
                  '{src}',
                  '{scene_id}',
                  {label_flag},
                  {proj_flag},
                  '{memo}',
                  NULL,
                  ST_GeomFromText('{geom}', 4326),
                  '{update_time}',
                  '{poi_mid if poi_mid else ""}',
                  {aoi_level},
                  {aoi_level_source},
                  {access_complete}
                )
            '''
            for
            face_id,
            poi_bid,
            name_ch,
            kind,
            mesh_id,
            src,
            scene_id,
            label_flag,
            proj_flag,
            memo,
            geom,
            update_time,
            poi_mid,
            aoi_level,
            aoi_level_source,
            access_complete
            in ret
        ]
        values_str = ','.join(values)
        sql = f'''
            insert into blu_record(
                face_id,
                bid,
                name_ch,
                type,
                strategy,
                task_id,
                kind,
                mesh_id,
                src,
                scene_id,
                label_flag,
                proj_flag,
                memo,
                old_geom,
                geom,
                update_time,
                mid,
                aoi_level,
                aoi_level_source,
                access_complete
            )
            values {values_str};
        '''
        pgsql.execute(track_conn, sql)


def get_query_blu_face_info_sql() -> str:
    return '''
        select 
            a.face_id,
            b.poi_bid,
            a.name_ch,
            a.kind,
            a.mesh_id,
            a.src,
            a.scene_id,
            a.label_flag,
            a.proj_flag,
            a.memo,
            ST_AsText(a.geom),
            a.update_time,
            b.poi_id,
            a.aoi_level,
            a.aoi_level_source,
            a.access_complete
        from
            blu_face a
            left join blu_face_poi b on a.face_id = b.face_id
        where
            a.face_id in %s;
    '''
