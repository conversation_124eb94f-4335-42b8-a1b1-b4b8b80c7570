# !/usr/bin/env python3
"""
策略：子点未完全包含在 aoi 范围内。
"""
from dataclasses import dataclass, field

from tqdm import tqdm

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import pipeline, pgsql
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
STRATEGY_NAME = 'child_poi_out_of_aoi'
STRATEGY_VERSION = '1.0.0'
SHOW_TAGS = {
    '内部楼栋',
}
IGNORED_STD_TAGS = {
    '交通设施;充电站',
    '交通设施;其他',
    '房地产;宿舍',
    '公司企业;公司',
    '房地产;写字楼',
    '教育培训;其他',
    '教育培训;科研机构',
    '教育培训;高等院校',
    '医疗',
    '美食;蛋糕甜品店',
    '美食;小吃快餐店',
    '美食;外国餐厅',
    '医疗;其他',
    '金融;银行',
    '医疗;药店',
    '生活服务;其他',
    '运动健身;其他',
    '运动健身;体育场馆',
    '出入口;其他',
    '交通设施;其他',
    '酒店',
    '购物;商铺',
    '房地产;住宅区',
    '医疗;核酸检测点',
    '汽车服务;汽车配件',
    '生活服务;物流公司',
    '交通设施;接送点',
    '政府机构;行政单位',
    '政府机构',
    '休闲娱乐;其他',
    '医疗;发热门诊',
    '医疗;诊所',
    '政府机构;居民委员会',
    '交通设施;路侧停车位',
    '医疗;急救中心',
    '休闲娱乐;洗浴按摩',
    '汽车服务;汽车销售',
    '医疗;综合医院',
    '旅游景点',
    '教育培训;图书馆',
    '美食;咖啡厅',
    '教育培训;培训机构',
    '酒店;快捷酒店',
    '汽车服务;汽车美容',
    '教育培训;中学',
    '政府机构;社会团体',
    '生活服务;通讯营业厅',
    '生活服务;邮局',
    '文化传媒;展览馆',
    '购物;家电数码',
    '休闲娱乐;休闲广场',
    '医疗;专科医院',
    '休闲娱乐;剧院',
    '购物;便利店',
    '生活服务;宠物服务',
    '休闲娱乐'
}

desc = pipeline.get_desc()


@dataclass
class AoiRecord:
    """
    用于存储 AOI 信息。
    """
    face_id: str
    bid: str
    wkt: str


@dataclass
class Context:
    """
    策略上下文。
    """
    batch: str
    bids: list[str]
    aoi_records: list[AoiRecord] = field(default_factory=list)
    issues: list[str] = field(default_factory=list)


@desc()
def import_aoi_records(ctx: Context, proceed):
    """
    导入 AOI 记录。
    """
    face_id_set = set()
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for bid in tqdm(ctx.bids, total=len(ctx.bids), desc='导入 aoi'):
            sql = f'''
                select face.face_id, st_astext(face.geom) 
                from blu_face_poi relation
                inner join blu_face face
                on face.face_id = relation.face_id
                where relation.poi_bid = %s 
                limit 1;
            '''
            record = pgsql.fetch_one(conn, sql, (bid,))
            if record:
                face_id, wkt = record
                if face_id not in face_id_set:
                    ctx.aoi_records.append(AoiRecord(
                        face_id=face_id,
                        bid=bid,
                        wkt=wkt,
                    ))
                    face_id_set.add(face_id)

    proceed()


@desc()
def find_issues(ctx: Context, proceed):
    """
    查找问题 AOI。
    """
    min_count = 5

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        for aoi in tqdm(ctx.aoi_records, total=len(ctx.aoi_records), desc='aoi 过滤'):
            sql = f'''
                select count(*)
                from poi 
                where (relation_bid = %s or bid = %s) and
                      st_distance(st_geomfromtext(%s, 4326), geometry) > 0;
            '''
            record = conn.fetch_one(
                sql,
                (aoi.bid, aoi.bid, aoi.wkt)
            )
            count, = record
            if count > min_count:
                ctx.issues.append(aoi.bid)

    proceed()


@desc()
def save_to_db(ctx: Context, proceed):
    """
    保存结果到数据库。
    """
    results = [
        StrategyResult(
            case_id=bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail',
        )
        for bid in ctx.issues
    ]
    export_strategy_result.to_db(results)
    proceed()


def run(bids: list[str], batch: str):
    """
    运行策略。
    """
    main_pipe = pipeline.Pipeline(
        import_aoi_records,
        find_issues,
        save_to_db,
    )
    desc.attach(main_pipe)
    ctx = Context(
        bids=bids,
        batch=batch,
    )
    main_pipe(ctx)
