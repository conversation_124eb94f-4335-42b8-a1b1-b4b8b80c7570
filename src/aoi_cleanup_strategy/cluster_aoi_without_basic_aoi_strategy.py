# !/usr/bin/env python3
"""
导航清单专题：聚合区下无任何基础院落。
ref to: https://console.cloud.baidu-int.com/devops/icafe/issue/poiinshanghai-8108/show
"""
from dataclasses import dataclass
from dataclasses import field

from osgeo import ogr
from tqdm import tqdm

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import pgsql
from src.tools import pipeline
from src.tools import utils

STRATEGY_NAME = 'cluster_aoi_without_basic_aoi'
STRATEGY_VERSION = '1.0.0'

CLUSTER_AOI_LEVEL = 1
BASIC_AOI_LEVEL = 2

desc = pipeline.get_desc()


@dataclass
class Context:
    bids: list[str]
    batch: str
    args: dict
    # list[(bid, face_id, geom)]
    cluster_aoi_list: list[tuple[str, str, str]] = field(default_factory=lambda: [])
    abnormal_cluster_aoi_list: list[tuple[str, str, str]] = field(default_factory=lambda: [])


@desc()
def filter_cluster_yard(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select b.poi_bid, a.face_id, ST_AsText(a.geom)
            from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where b.poi_bid in %(bids)s and a.aoi_level = %(level)s and a.src != 'SD';
        '''
        ctx.cluster_aoi_list = pgsql.fetch_all(conn, sql, {
            'bids': tuple(ctx.bids),
            'level': CLUSTER_AOI_LEVEL,
        })

    proceed()


def filter_abnormal_yard(area_threshold: float):
    @desc('filter abnormal yard')
    def pipe(ctx: Context, proceed):
        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            for bid, face_id, wkt in tqdm(ctx.cluster_aoi_list):
                sql = f'''
                    select ST_AsText(geom) from blu_face
                    where face_id != '{face_id}'
                    and aoi_level = {BASIC_AOI_LEVEL}
                    and src != 'SD'
                    and ST_Intersects(ST_GeomFromText('{wkt}', 4326), geom);
                '''
                ret = pgsql.fetch_all(conn, sql)
                cluster_geom = ogr.CreateGeometryFromWkt(wkt)
                valid_basic_aoi_list = [
                    utils.calc_ioa(cluster_geom, x[0]) for x in ret
                ]
                valid_basic_aoi_list = [x for x in valid_basic_aoi_list if x >= area_threshold]
                if not valid_basic_aoi_list:
                    ctx.abnormal_cluster_aoi_list.append((bid, face_id, wkt))

        proceed()

    return pipe


@desc()
def save_to_db(ctx: Context, proceed):
    fail_bids = {x[0] for x in ctx.abnormal_cluster_aoi_list}
    results = [
        StrategyResult(
            case_id=bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail' if bid in fail_bids else 'pass',
            args=ctx.args,
            details={
                'face_id': face_id,
                'geom': wkt,
            }
        )
        for bid, face_id, wkt in ctx.cluster_aoi_list
    ]
    export_strategy_result.to_db(results)
    proceed()


def run(bids: list[str], batch: str):
    area_threshold = 0.7
    main_pipe = pipeline.Pipeline(
        filter_cluster_yard,
        filter_abnormal_yard(area_threshold=area_threshold),
        save_to_db,
    )
    desc.attach(main_pipe)

    ctx = Context(
        bids=bids,
        batch=batch,
        args={'area_threshold': area_threshold}
    )
    main_pipe(ctx)
