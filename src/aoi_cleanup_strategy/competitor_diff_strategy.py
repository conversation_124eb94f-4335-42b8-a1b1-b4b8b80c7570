# !/usr/bin/env python3
"""
竞品 diff 策略模块
"""
import dataclasses
import shutil
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Any
from typing import List

from tqdm import tqdm

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import linq
from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv
from src.tools import utils

STRATEGY_NAME = 'competitor_diff'
STRATEGY_VERSION = '1.0.0'

BASIC_YARD = 2
IOU_THRESHOLD = 0.5

desc = pipeline.get_desc()


@dataclass
class Context:
    work_dir: Path
    batch: str
    blu_face_path: Path = field(init=False)
    diff_dir: Path = field(init=False)
    tickets_path: Path = field(init=False)
    item_pipe: Any
    diff_items: list = field(default=None)

    def __post_init__(self):
        self.blu_face_path = self.work_dir / 'blu_face.tsv'
        self.diff_dir = self.work_dir / 'diff'
        self.diff_dir.mkdir(parents=True, exist_ok=True)
        self.tickets_path = self.work_dir / 'tickets.tsv'


@dataclass
class CompetitorItem:
    competitor_id: str
    bid: str
    crawl_time: int
    geom: str
    iou: float = field(default=0.0)


@dataclass
class DiffItem:
    face_id: str
    bid: str
    geom: str
    hit_items: List[CompetitorItem] = field(default=None)
    selected_hit_item: CompetitorItem = field(default=None)


@dataclass
class ItemContext:
    face_id: str
    save_path: Path
    connection: Any
    item: DiffItem


def export_blu_face_by_city(city_names: list[str]):
    @desc('export blu face by city')
    def pipe(ctx: Context, proceed):
        save_path = ctx.work_dir / f'blu_face.city_{len(city_names)}.tsv'
        ctx.blu_face_path = save_path
        if save_path.exists():
            proceed()
            return

        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            sql = '''
                select a.face_id, b.poi_bid, a.name_ch, a.city_name, ST_AsText(a.geom)
                from blu_face a inner join blu_face_poi b on a.face_id = b.face_id and b.poi_bid != '' 
                where a.aoi_level = 2 and a.src != 'SD' and a.city_name in %s;
            '''
            ret = pgsql.fetch_all(conn, sql, [tuple(city_names)])
            if ret:
                tsv.write_tsv(save_path, ret)

        proceed()

    return pipe


def export_blu_face_expect_city(city_names: list[str]):
    @desc('export blu face expect city')
    def pipe(ctx: Context, proceed):
        save_path = ctx.work_dir / f'blu_face.non_city_{len(city_names)}.tsv'
        ctx.blu_face_path = save_path
        if save_path.exists():
            proceed()
            return

        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            sql = '''
                select a.face_id, b.poi_bid, a.name_ch, a.city_name, ST_AsText(a.geom)
                from blu_face a inner join blu_face_poi b on a.face_id = b.face_id and b.poi_bid != '' 
                where a.aoi_level = 2 and a.src != 'SD' and a.city_name not in %s;
            '''
            ret = pgsql.fetch_all(conn, sql, [tuple(city_names)])
            if ret:
                tsv.write_tsv(save_path, ret)

        proceed()

    return pipe


def export_blu_face_by_bid(bids: list[str]):
    @desc('export blu face by bid')
    def pipe(ctx: Context, proceed):
        save_path = ctx.work_dir / f'blu_face.bid_{len(bids)}.tsv'
        ctx.blu_face_path = save_path
        if save_path.exists():
            proceed()
            return

        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            sql = '''
                select a.face_id, b.poi_bid, a.name_ch, a.city_name, ST_AsText(a.geom)
                from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
                where a.src != 'SD' and b.poi_bid in %s;
            '''
            ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
            if ret:
                tsv.write_tsv(save_path, ret)

        proceed()

    return pipe


def hit_test_with_competitor(batch_size=1000, concurrent=8):
    @desc('hit test with competitor')
    def pipe(ctx: Context, proceed):
        blu_faces = [(x[0], x[1], x[-1]) for x in tsv.read_tsv(ctx.blu_face_path)]
        batch_blu_faces = [blu_faces[i:i + batch_size] for i in range(0, len(blu_faces), batch_size)]
        with Pool(concurrent) as p:
            data = [(ctx.item_pipe, x, ctx.diff_dir) for x in batch_blu_faces]
            _ = list(tqdm(p.imap(process, data), total=len(batch_blu_faces)))
        proceed()

    return pipe


@desc()
def load_diff_items(ctx: Context, proceed):
    files = ctx.diff_dir.glob('*.json')
    ctx.diff_items = [utils.read_json(x) for x in tqdm(files)]
    proceed()


@desc()
def export_iou_distribution(ctx: Context, proceed):
    n_group = 10
    step = 1 / n_group
    results = list(linq.group_by(ctx.diff_items, lambda x: x['selected_hit_item']['iou'] // step).items())
    results.sort(key=lambda x: x[0])
    for key, items in results:
        start = key * step
        save_path = ctx.work_dir / f'iou_{start:0.2f}_{start + step:0.2f}_{len(items)}.json'
        utils.write_json(save_path, items)

    proceed()


def export_not_found_competitor_bids(case_bids: list[str]):
    @desc('export not found competitor bids')
    def pipe(ctx: Context, proceed):
        diff_item_bids = {x['bid'] for x in ctx.diff_items}
        uncovered_bids = [[bid] for bid in case_bids if bid not in diff_item_bids]

        save_path = ctx.work_dir / 'uncovered_bids.tsv'
        tsv.write_tsv(save_path, uncovered_bids)
        print(f'uncovered aoi: {len(uncovered_bids)}')
        proceed()

    return pipe


@desc()
def export_tickets(ctx: Context, proceed):
    valid_items = filter_valid_diff_items(ctx.diff_items)
    # 输出 aoi_intelligence 所需信息。
    competitor_ids = [x['selected_hit_item']['competitor_id'] for x in valid_items]
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select
                bid,
                intel_id,
                name,
                address,
                display_x,
                display_y,
                mesh_id,
                ST_AsText(geom),
                batch,
                crawl_time,
                create_time
            from
                aoi_intelligence_history
            where
                id in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(competitor_ids)])
        tsv.write_tsv(ctx.tickets_path, ret)

    proceed()


@desc()
def insert_to_aoi_intelligence(ctx: Context, proceed):
    items = list(tsv.read_tsv(ctx.tickets_path))
    bids = [x[0] for x in items]
    existed_bids = set(get_existed_aoi_intelligence_bids(bids))
    print(f'{len(existed_bids)=}')
    unimport_items = [x for x in items if x[0] not in existed_bids]
    print(f'{len(unimport_items)=}')
    if not unimport_items:
        proceed()
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        insert_values = ','.join(
            f"""
                (
                    '{bid}',
                    '{intel_id}',
                    '{name}',
                    '{address}',
                    {display_x},
                    {display_y},
                    '{mesh_id}',
                    ST_GeomFromText('{geom}', 4326),
                    '{batch}',
                    {crawl_time},
                    '{create_time}'
                )
            """
            for
            bid,
            intel_id,
            name,
            address,
            display_x,
            display_y,
            mesh_id,
            geom,
            batch,
            crawl_time,
            create_time
            in
            unimport_items
        )
        sql = f'''
            insert into aoi_intelligence (
                bid,
                intel_id,
                name,
                address,
                display_x,
                display_y,
                mesh_id,
                geom,
                batch,
                crawl_time,
                create_time
            )
            values
                {insert_values};
        '''
        with conn.cursor() as cursor:
            cursor.execute(sql)

        conn.commit()

    proceed()


@desc()
def save_to_db(ctx: Context, proceed):
    valid_items = filter_valid_diff_items(ctx.diff_items)
    # ”有效“的 item 是指召回有效，也就是没有通过策略的 item，所以命名上：valid =~ fail。
    fail_bids = [x['bid'] for x in valid_items]

    results = [
        StrategyResult(
            case_id=x['bid'],
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail' if x['bid'] in fail_bids else 'pass',
            args={'iou_threshold': IOU_THRESHOLD},
            details=x
        )
        for x in ctx.diff_items
    ]
    export_strategy_result.to_db(results)
    proceed()


def analysis_iou(n_group=10):
    @desc('analysis iou')
    def pipe(ctx: Context, proceed):
        hit_items = (x['selected_hit_item'] for x in ctx.diff_items)
        iou_list = [x['iou'] for x in hit_items]
        print_iou_distribution(iou_list, n_group)
        print(f'total: {len(iou_list)}')
        proceed()

    return pipe


def analysis_bug_case_recall(case_bids: list[str]):
    @desc('analysis bug case recall')
    def pipe(ctx: Context, proceed):
        diff_item_dict = {x['bid']: x for x in ctx.diff_items}
        recall_items = [diff_item_dict[bid] for bid in case_bids if bid in diff_item_dict]

        save_path = ctx.work_dir / 'recall_items.json'
        utils.write_json(save_path, recall_items)

        print_iou_distribution([x['selected_hit_item']['iou'] for x in recall_items], n_group=10)

        recall = 100 * len(recall_items) / len(case_bids)
        print(f'recall: {recall:0.2f}% ({len(recall_items)} / {len(case_bids)})')
        proceed()

    return pipe


def skip_existed_item(ctx: ItemContext, proceed):
    if not ctx.save_path.exists():
        proceed()


def match_competitor_aoi_by_bid(ctx: ItemContext, proceed):
    sql = f'''
        select id, bid, crawl_time, ST_AsText(geom) from aoi_intelligence_history
        where bid = '{ctx.item.bid}';
    '''
    ret = pgsql.fetch_all(ctx.connection, sql)
    if ret:
        hit_items = [
            CompetitorItem(competitor_id, bid, crawl_time, geom)
            for competitor_id, bid, crawl_time, geom in ret
        ]
        ctx.item.hit_items = hit_items
        proceed()


def calc_aoi_iou(ctx: ItemContext, proceed):
    for hit_item in ctx.item.hit_items:
        hit_item.iou = utils.calc_iou(ctx.item.geom, hit_item.geom)

    proceed()


def select_hit_item(ctx: ItemContext, proceed):
    item = ctx.item
    # 1. 如果仅匹配到 1 个竞品，则直接继续。
    if len(item.hit_items) == 1:
        item.selected_hit_item = item.hit_items[0]
        proceed()
        return

    # 2. 匹配到的竞品大于 1 个：
    # - 以竞品关联的 bid 分组：
    #   - 若分组等于 1：选取最后抓取的结果（crawl_time）。
    #   - 若分组大于 1：则选取 iou 最大的。(?)
    hit_items = linq.count_by(item.hit_items, lambda x: x.bid)
    if len(hit_items) == 1:
        item.hit_items.sort(key=lambda x: x.crawl_time)
        item.selected_hit_item = item.hit_items[-1]
    else:
        item.hit_items.sort(key=lambda x: x.iou)
        item.selected_hit_item = item.hit_items[-1]

    proceed()


def persist_item(ctx: ItemContext, proceed):
    write_json(ctx.save_path, ctx.item)
    proceed()


# helper functions:

def get_premium_aoi_bids(bids: list[str]):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid from aoi_gate_match_batch_result
            where valid = 1 and level = 3 and bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        return [x[0] for x in ret]


def get_std_tags(bids: list[str]):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid, std_tag from poi
            where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        return {bid: tag for bid, tag in ret}


def get_existed_aoi_intelligence_bids(bids: list[str]):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid from aoi_intelligence
            where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        return [x[0] for x in ret]


def print_iou_distribution(iou_list: list[float], n_group: int):
    step = 1 / n_group
    results = list(linq.count_by(iou_list, lambda x: x // step).items())
    results.sort(key=lambda x: x[0])
    for key, count in results:
        start = key * step
        print(f'[{start:0.2f}, {start + step:0.2f}): {count}')


def process(ctx):
    item_pipe, blu_faces, save_dir = ctx
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for face_id, bid, geom in blu_faces:
            item_ctx = ItemContext(
                face_id=face_id,
                save_path=save_dir / f'{face_id}.json',
                item=DiffItem(face_id, bid, geom),
                connection=conn
            )
            item_pipe(item_ctx)


def write_json(file_path: Path, content: any):
    ret = dataclasses.asdict(content)
    utils.write_json(file_path, ret)


def filter_valid_diff_items(diff_items: list[dict]):
    # FIXME: 不要在纯粹的策略函数里使用 print()，而应该作为一个切面的功能分离出去。
    print(f'total: {len(diff_items)}')
    # 筛选 iou < 0.5 的部分。
    valid_items = [x for x in diff_items if x['selected_hit_item']['iou'] < IOU_THRESHOLD]
    print(f'iou < {IOU_THRESHOLD}: {len(valid_items)}')
    # 剔除 std_tag 为 '旅游景点', '交通设施;火车站', '交通设施;飞机场'。
    bids = [x['bid'] for x in valid_items]
    std_tag_dict = get_std_tags(bids)
    valid_items = [x for x in valid_items if x['bid'] in std_tag_dict]
    print(f'has tag: {len(valid_items)}')
    invalid_tags = ['旅游景点', '交通设施;火车站', '交通设施;飞机场']
    valid_items = [x for x in valid_items if not contains_tag(std_tag_dict[x['bid']], invalid_tags)]
    print(f'filtered by std_tag: {len(valid_items)}')
    return valid_items


def contains_tag(tag: str, tags: list[str]):
    return any((x in tag) for x in tags)


def execute(bids: list[str], batch: str, temp_dir: Path):
    analysis_pipe = pipeline.Pipeline(
        load_diff_items,
        analysis_iou(),
        analysis_bug_case_recall(bids),
        # export_iou_distribution,
    )
    main_pipe = pipeline.Pipeline(
        # NOTE: 以下 3 种筛选待处理 blu_face 的方法，根据需求选择一种即可。
        # export_blu_face_by_city(important_cities),
        # export_blu_face_expect_city(important_cities),
        export_blu_face_by_bid(bids),
        hit_test_with_competitor(),
        analysis_pipe,
        export_not_found_competitor_bids(bids),
        save_to_db,
        export_tickets,
        insert_to_aoi_intelligence,
    )
    desc.attach(main_pipe)
    item_pipe = pipeline.Pipeline(
        skip_existed_item,
        match_competitor_aoi_by_bid,
        calc_aoi_iou,
        select_hit_item,
        persist_item,
    )
    ctx = Context(
        work_dir=temp_dir,
        batch=batch,
        item_pipe=item_pipe,
    )
    main_pipe(ctx)


def run(bids: list[str], batch: str):
    temp_dir = Path(f'temp-{STRATEGY_NAME}-{STRATEGY_VERSION}')
    if temp_dir.exists():
        shutil.rmtree(temp_dir, ignore_errors=True)

    temp_dir.mkdir(parents=True, exist_ok=True)

    try:
        today = datetime.now().strftime('%Y%m%d')
        save_dir = temp_dir / today / batch
        execute(bids, batch, save_dir)
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)
