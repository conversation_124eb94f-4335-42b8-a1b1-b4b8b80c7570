# !/usr/bin/env python3
"""
竞品 diff v2 策略：
将 BD 边框与抓取渠道不同途径的最新竞品（比如：GD 最新、TX 最新）同时做 diff，若有一个不通过则需要被策略召回。
"""
import random
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.batch_process.batch_helper import make_competitor_valid
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv

STRATEGY_NAME = 'competitor_diff_v2'
STRATEGY_VERSION = '1.0.0'
GET_AOI_SQL = '''
    select a.face_id, st_astext(a.geom), a.area
    from blu_face a
    inner join blu_face_poi b
    on a.face_id = b.face_id
    where b.poi_bid = %s
    limit 1;
'''
GET_GD_COMPETITOR_SQL = '''
    select name, st_astext(geom)
    from aoi_intelligence_history
    where bid = %s and
          length(intel_id) <= 10
    order by create_time desc
    limit 1;
'''
GET_TX_COMPETITOR_SQL = '''
    select name, st_astext(geom)
    from aoi_intelligence_history
    where bid = %s and
          length(intel_id) > 10
    order by create_time desc
    limit 1;
'''
GET_POI_SQL = '''
    select name, std_tag, click_pv from poi where bid = %s;
'''

desc = pipeline.get_desc()


@dataclass
class AoiRecord:
    """
    AOI 记录
    """
    face_id: str
    bid: str
    wkt: str
    name: str = ''
    pv: int = 0
    tag: str = ''
    area: float = 0.0
    gd_wkt: str = ''
    gd_name: str = ''
    tx_wkt: str = ''
    tx_name: str = ''
    can_output: bool = False


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    bids: list[str]
    batch: str
    aoi_records: list[AoiRecord] = field(default_factory=list)
    issues: list[str] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_records(ctx: Context, proceed):
    """
    导入 AOI 记录。
    """
    face_id_set = set()

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for bid in tqdm(ctx.bids):
            row = pgsql.fetch_one(conn, GET_AOI_SQL, (bid,))
            if row is None:
                continue

            face_id, wkt, area = row
            if face_id in face_id_set:
                continue

            face_id_set.add(face_id)
            ctx.aoi_records.append(AoiRecord(
                face_id=face_id,
                bid=bid,
                wkt=wkt,
                area=area,
            ))

    proceed()


def is_iou_too_small(bd_geom, original_competitor_wkt):
    """
    判断 BD 与竞品的 IOU 是否过小
    """
    min_iou = 0.6

    if original_competitor_wkt == '':
        return False

    original_competitor_geom = shapely.wkt.loads(original_competitor_wkt)
    valid_competitor_geom = make_competitor_valid(original_competitor_geom)
    intersection = bd_geom.intersection(valid_competitor_geom)
    union = bd_geom.union(valid_competitor_geom)
    iou = intersection.area / union.area
    return iou < min_iou


@desc()
def find_issues(ctx: Context, proceed):
    """
    查找问题 AOI。
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        for record in tqdm(ctx.aoi_records):
            bd_geom = shapely.wkt.loads(record.wkt)

            gd_row = poi_stabilizer.fetch_one(GET_GD_COMPETITOR_SQL, (record.bid,))
            tx_row = poi_stabilizer.fetch_one(GET_TX_COMPETITOR_SQL, (record.bid,))

            gd_wkt = gd_row[1] if gd_row else ''
            tx_wkt = tx_row[1] if tx_row else ''

            if is_iou_too_small(bd_geom, gd_wkt) or is_iou_too_small(bd_geom, tx_wkt):
                ctx.issues.append(record.bid)
                record.can_output = True
                record.gd_wkt = gd_wkt
                record.tx_wkt = tx_wkt
                record.gd_name = gd_row[0] if gd_row else ''
                record.tx_name = tx_row[0] if tx_row else ''

    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 POI 属性。
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        for record in tqdm(ctx.aoi_records):
            if not record.can_output:
                continue

            row = poi_stabilizer.fetch_one(GET_POI_SQL, (record.bid,))
            if row is None:
                continue

            record.name, record.tag, record.pv = row

    proceed()


@desc()
def save_to_db(ctx: Context, proceed):
    """
    保存结果到数据库
    """
    results = [
        StrategyResult(
            case_id=bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail',
        )
        for bid in ctx.issues
    ]
    export_strategy_result.to_db(results)
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存结果到本地文件
    """
    random.shuffle(ctx.aoi_records)
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.bid,
                x.name,
                x.pv,
                x.tag,
                x.area,
                x.wkt,
                x.gd_wkt,
                x.gd_name,
                x.tx_wkt,
                x.tx_name,
            ]
            for x in ctx.aoi_records if x.can_output
        ]
    )

    proceed()


def run(bids: list[str], batch: str):
    """
    运行策略。
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        find_issues,
        save_to_db,
        fill_poi_properties,
        # save_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/competitor_diff_v2_strategy'),
        bids=bids,
        batch=batch,
    )
    main_pipe(ctx)
