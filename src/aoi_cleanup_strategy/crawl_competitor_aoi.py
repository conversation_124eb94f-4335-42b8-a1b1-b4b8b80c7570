# !/usr/bin/env python3
"""
用于抓取竞品 AOI。
"""
import argparse
import datetime
import json
import random
import time
from dataclasses import dataclass, field
from multiprocessing import Pool
from pathlib import Path

import pymysql
import requests
from retrying import retry
from tqdm import tqdm

from src.tools import pipeline, notice_tool
from src.tools.afs_tool import AfsTool
from src.tools.conf_tools import get_mysql_conf

desc = pipeline.get_desc()


@dataclass
class CrawlInfo:
    """
    竞品信息
    """
    bid: str
    competitor_id: str
    content: str = ''
    error: str = ''
    success: bool = False

    def to_dict(self):
        return self.__dict__


@dataclass
class CrawlTask:
    """
    竞品抓取任务
    """
    json_file_path: Path
    task_id: str
    batch_id: str = ''
    src: str = ''
    items: list[CrawlInfo] = field(default_factory=list)

    def __post_init__(self):
        with open(self.json_file_path, 'r') as f:
            task_json = json.load(f)
            self.batch_id = task_json['batch_id']
            self.src = task_json['src']
            items = task_json['items']
            for item in items:
                self.items.append(CrawlInfo(item['bid'], item['competitor_id']))

        self.json_file_path.unlink(missing_ok=True)


@dataclass
class CrawlContext:
    """
    抓取上下文
    """
    info: CrawlInfo
    api: str
    appkey: str


@dataclass
class Context:
    """
    脚本执行上下文
    """
    mode: str
    batch_filter: str
    url: str
    work_dir: Path
    afs_path: str
    crawl_apis: list[str]
    appkey: str
    crawl_task: CrawlTask = None

    def generate_crawl_contexts(self):
        """
        生产抓取信息
        """
        api_count = len(self.crawl_apis)

        for item in self.crawl_task.items:
            yield CrawlContext(
                info=CrawlInfo(
                    bid=item.bid,
                    competitor_id=item.competitor_id,
                    content=item.content,
                    error=item.error,
                    success=item.success,
                ),
                api=self.crawl_apis[random.randint(0, api_count - 1)],
                appkey=self.appkey
            )


def create_db_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf()
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def execute_db_command(sql):
    """
    执行 mysql 数据库命令
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(sql)
            conn.commit()


def mark_task_error(task_id, status, memo):
    """
    标记任务为错误状态
    """
    execute_db_command(f"update crawl_task set status={status}, memo='{memo}' where id={task_id}")


def mark_task_start(task_id):
    """
    标记任务为开始执行状态
    """
    execute_db_command(f'''
        update crawl_task 
        set exec_time='{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}',
            status = 1
        where id={task_id}
    ''')


def upload_crawl_result_to_db(task_id, result_url):
    """
    将抓取结果上传到数据库
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(f'''
                update crawl_task set result_url='{result_url}', status=2 where id={task_id}
            ''')
            conn.commit()


def download_oldest_task_from_db(ctx: Context):
    """
    从数据库中下载最旧的未执行的任务
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            sql = f'''
                select id, task_url
                from crawl_task
                where status = 0             
                order by priority desc, request_time asc
                limit 1
            ''' if not ctx.batch_filter else f'''
                select id, task_url
                from crawl_task
                where status = 0 and
                      batch_id like '%{ctx.batch_filter}%'        
                order by priority desc, request_time asc
                limit 1            
            '''

            cursor.execute(sql)
            task = cursor.fetchone()
            if task:
                task_id, url = task
                return task_id, url

    return None


def fetch_afs_task(ctx: Context):
    """
    从 afs 中下载任务
    """
    afs = AfsTool('aries_rpm01')
    afs.get(ctx.url, ctx.work_dir)
    afs_file_path = Path(ctx.url).name
    local_file_path = ctx.work_dir / afs_file_path
    if not local_file_path.exists():
        return

    ctx.crawl_task = CrawlTask(task_id='', json_file_path=local_file_path)


def fetch_db_task(ctx: Context):
    """
    从数据库中下载任务
    """
    # 获取最旧的、未执行的任务。
    task = download_oldest_task_from_db(ctx)
    if not task:
        return

    task_id, url = task
    mark_task_start(task_id)
    afs = AfsTool('aries_rpm01')
    afs.get(url, ctx.work_dir)
    afs_file_path = Path(url).name
    local_file_path = ctx.work_dir / afs_file_path
    if not local_file_path.exists():
        mark_task_error(task_id, -1, f'无法下载：{url}')
        return
    ctx.crawl_task = CrawlTask(task_id=task_id, json_file_path=local_file_path)


@retry(stop_max_attempt_number=8, wait_random_min=100, wait_random_max=500)
def critical_crawl_competitor_aoi(context: CrawlContext):
    """
    抓取竞品边框
    """
    response = requests.get(
        context.api,
        params={
            'id': context.info.competitor_id,
            'source_id': context.info.bid,
        },
        headers={
            'Authorization': context.appkey,
        }
    )

    if response.status_code != 200:
        context.info.error = response.reason
        raise Exception(response.reason)
    else:
        result = response.json()
        code = result['code']
        if code != 0:
            context.info.error = result['msg']
            raise Exception(result['msg'])
        else:
            context.info.content = result

    context.info.success = True


def try_crawl_competitor_aoi(context: CrawlContext):
    """
    抓取竞品边框
    """
    try:
        critical_crawl_competitor_aoi(context)
    except Exception as e:
        context.info.error = str(e)

    return context.info


@desc()
def fetch_crawl_task(ctx: Context, proceed):
    """
    下载任务
    """
    if ctx.mode == 'db':
        fetch_db_task(ctx)
    elif ctx.mode == 'afs':
        fetch_afs_task(ctx)

    if ctx.crawl_task is None:
        return

    print(ctx.crawl_task.task_id)
    proceed()


@desc()
def crawl_competitor_aoi(ctx: Context, proceed):
    """
    抓取竞品边框
    """
    pool_size = 8

    with Pool(pool_size) as p:
        ctx.crawl_task.items = list(tqdm(
            p.imap(try_crawl_competitor_aoi, ctx.generate_crawl_contexts()),
            total=len(ctx.crawl_task.items),
            desc="抓取竞品边框"
        ))

    proceed()


@desc()
def upload_crawl_result(ctx: Context, proceed):
    """
    上传抓取结果
    """
    crawl_result = {
        'batch_id': ctx.crawl_task.batch_id,
        'src': ctx.crawl_task.src,
        'items': [x.to_dict() for x in ctx.crawl_task.items]
    }

    task_file_name = f'result_{ctx.crawl_task.batch_id}.json'
    task_file_path = ctx.work_dir / task_file_name
    with open(task_file_path, 'w', encoding='utf-8') as f:
        f.write(json.dumps(crawl_result))

    try:
        afs = AfsTool('aries_rpm01')
        afs.put(str(task_file_path), ctx.afs_path)
        upload_crawl_result_to_db(ctx.crawl_task.task_id, f'{ctx.afs_path}/{task_file_name}')
    except Exception as e:
        print(e)
        mark_task_error(ctx.crawl_task.task_id, -1, str(e))
    finally:
        task_file_path.unlink(missing_ok=True)

    proceed()


@desc()
def send_to_infoflow(ctx: Context, proceed):
    """
    发送任务完成信息
    """
    # batch demo: region_sjc_20240903020003496509
    if 'region' not in ctx.crawl_task.batch_id:
        proceed()
        return

    date = datetime.datetime.now().strftime('%Y%m%d')
    if date not in ctx.crawl_task.batch_id:
        proceed()
        return

    notice_tool.send_hi(
        f'竞品抓取任务 {ctx.crawl_task.batch_id} 已完成',
        atuserids=['chenjie02_cd'],
        token='d83586c9c29feea30d4fbe3da7edc2669'
    )
    proceed()


def parse_args():
    """
    解析参数
    """
    parser = argparse.ArgumentParser('crawl competitor aoi')
    parser.add_argument(
        '--loop',
        dest='loop',
        default=False,
        action='store_true',
    )
    parser.add_argument(
        '--no-loop',
        dest='loop',
        default=True,
        action='store_false',
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        help='运行模式 {(db) | afs}',
        type=str,
        default='db',
        required=False,
    )
    parser.add_argument(
        '--url',
        dest='url',
        help='任务信息文件地址',
        type=str,
        default=None,
        required=False,
    )
    parser.add_argument(
        '--batch-filter',
        dest='batch_filter',
        help='此参数可过滤指定批次',
        type=str,
        default=None,
        required=False,
    )
    return parser.parse_args()


def crawl(args):
    """
    抓取竞品边框
    """
    main_pipe = pipeline.Pipeline(
        fetch_crawl_task,
        crawl_competitor_aoi,
        upload_crawl_result,
        send_to_infoflow,
    )
    desc.attach(main_pipe)

    # noinspection SpellCheckingInspection
    ctx = Context(
        mode=args.mode,
        batch_filter=args.batch_filter,
        url=args.url,
        work_dir=Path('/home/<USER>/chenjie/crawl_competitor_aoi'),
        afs_path='/user/map-data-streeview/aoi-ml/crawl',
        crawl_apis=[
            'http://************:8098/de/spider/v1/aoi/detail',
            'http://*************:8098/de/spider/v1/aoi/detail',
        ],
        appkey='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9'
               '.eyJ1c2VyIjoiZGUtY2hlbmppZSIsImxvZ2luQXQiOjAsImV4cCI6MTk0NzU1NDIxOX0'
               '.E0BVQiv5ERmf5a58N362HG_QyTWvSXI07bmpfJf4RMw',
    )
    main_pipe(ctx)


def main(args):
    """
    主函数
    """
    if args.loop:
        while True:
            crawl(args)
            time.sleep(2)
    else:
        crawl(args)


if __name__ == '__main__':
    main(parse_args())
