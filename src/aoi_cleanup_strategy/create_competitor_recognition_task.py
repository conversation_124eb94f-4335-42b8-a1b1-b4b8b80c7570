# !/usr/bin/env python3
"""
创建竞品识别任务。
具体步骤是：
1. 获取竞品库最新批次的竞品边框
2. 使用竞品范围创建数据集
3. 调用识别脚本，启动识别任务
"""
import argparse
import json
import shutil
import subprocess
from dataclasses import dataclass, field
from pathlib import Path

import pymysql

from src.const.work_flow import ConstWorkFlow
from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.tools import pipeline, pgsql
from src.tools.afs_tool import AfsTool
from src.tools.conf_tools import get_mysql_conf

desc = pipeline.get_desc()


@dataclass
class CompetitorAoiRecord:
    """
    竞品边框记录
    """
    bid: str
    intel_id: str
    wkt: str


@dataclass
class Context:
    """
    脚本执行上下文
    """
    afs_path: str
    min_area: float
    max_area: float
    buffer: float
    work_dir: Path
    task_id: str = None
    batch_id: str = None
    dataset_info_path: Path = None
    dataset_path: Path = None
    dataset_tar_path: Path = None
    dataset_afs_path: str = None
    competitor_aoi_records: [CompetitorAoiRecord] = field(default_factory=list)


def create_db_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf()
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def execute_db_command(sql, args=None):
    """
    执行 mysql 数据库命令
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(sql, args)
            conn.commit()


def mark_task_error(task_id, error):
    """
    标记任务为错误状态
    """
    execute_db_command('update crawl_task set status=-1, memo=%s where id=%s', (error, task_id))


def mark_task_start(task_id):
    """
    标记任务为开始状态
    """
    execute_db_command(f'''
        update crawl_task 
        set status = 5
        where id=%s
    ''', (task_id, ))


def mark_task_finished(task_id):
    """
    标记任务为完成状态
    """
    execute_db_command(f'''
        update crawl_task 
        set status = 6
        where id=%s
    ''', (task_id,))


@desc()
def fetch_oldest_batch(ctx: Context, proceed):
    """
    获取最旧的批次号
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(f'''
                select id, batch_id
                from crawl_task
                where status = 4          
                order by finished_time
                limit 1;
            ''')
            task = cursor.fetchone()
            if task:
                task_id, batch_id = task
                mark_task_start(task_id)
                ctx.task_id = task_id
                ctx.batch_id = batch_id

    proceed()


@desc()
def load_competitor_aoi(ctx: Context, proceed):
    """
    加载竞品边框
    """
    if ctx.batch_id is None:
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = f'''
            select bid,
                   intel_id,
                   st_astext(st_envelope(st_buffer(geom::geography, %s)::geometry))
            from aoi_intelligence_history
            where batch = %s and
                  st_area(geom::geography) >= %s and 
                  st_area(geom::geography) <= %s;
        '''
        records = pgsql.fetch_all(conn, sql, (ctx.buffer, ctx.batch_id, ctx.min_area, ctx.max_area))
        for record in records:
            bid, intel_id, wkt = record
            ctx.competitor_aoi_records.append(CompetitorAoiRecord(bid, intel_id, wkt))

    if not any(ctx.competitor_aoi_records):
        mark_task_error(ctx.task_id, '无竞品边框可识别')
        return

    proceed()


@desc()
def save_dataset_info(ctx: Context, proceed):
    """
    保存数据集信息
    """
    ctx.dataset_info_path = ctx.work_dir / f'dataset_info_{ctx.batch_id}.json'
    with open(ctx.dataset_info_path, 'w', encoding='utf-8') as f:
        f.write(json.dumps([
            {
                'uuid': x.bid,
                'poly_geom': x.wkt,
                'city': [''],
                'expand': {
                    'bid': x.bid,
                    'intel_id': x.intel_id
                }
            }
            for x in ctx.competitor_aoi_records
        ], ensure_ascii=False))

    proceed()


@desc()
def upload_dataset_info_to_afs(ctx: Context, proceed):
    """
    将数据集信息上传到 afs
    """
    afs = AfsTool()
    afs.put(ctx.dataset_info_path, ctx.afs_path)
    afs_file_path = Path(ctx.afs_path) / ctx.dataset_info_path.name

    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(f'''
                update crawl_task
                set dataset_info_url = %s 
                where id = %s;
            ''', (str(afs_file_path), ctx.task_id))
            conn.commit()

    proceed()


@desc()
def create_dataset(ctx: Context, proceed):
    """
    创建数据集
    """
    output_path = Path('../output')
    ctx.dataset_path = output_path / f'dataset_{ctx.batch_id}'
    ctx.dataset_tar_path = Path(f'{str(ctx.dataset_path)}.tar')
    shutil.rmtree(ctx.dataset_path, ignore_errors=True)
    if ctx.dataset_tar_path.exists():
        ctx.dataset_tar_path.unlink()

    download_raster_process = subprocess.Popen([
        'python3',
        'src/pre_process/create_od_product_dataset.py',
        '--id',
        f'dataset_{ctx.batch_id}',
        '--region-file',
        str(ctx.dataset_info_path),
        '--output-path',
        str(output_path),
        '--cache-path',
        '/home/<USER>/cache',
        '--raster-type',
        'Google2022',
        '--raster-src',
        'http',
        '--no-clip'
    ], stdout=subprocess.PIPE)
    download_raster_process.wait()

    # 数据集中必须要有 aux_images_inner_road 文件夹，否则后续环节会报错。
    (ctx.dataset_path / 'aux_images_inner_road').mkdir(exist_ok=True)

    compress_dataset_process = subprocess.Popen([
        'tar',
        '-cf',
        str(ctx.dataset_tar_path),
        str(ctx.dataset_path)
    ], stdout=subprocess.PIPE)
    compress_dataset_process.wait()

    proceed()


@desc()
def upload_dataset_to_afs(ctx: Context, proceed):
    """
    将数据集上传到 afs
    """
    afs = AfsTool()
    afs.put(str(ctx.dataset_tar_path), ctx.afs_path)
    ctx.dataset_afs_path = f'{ctx.afs_path}/{ctx.dataset_tar_path.name}'
    proceed()


@desc()
def post_recognition_task(ctx: Context, proceed):
    """
    创建识别任务
    """
    with AoiMlFlowModel() as flow:
        # 执行语义分割全流程
        task_dict = {
            "task_name": ConstWorkFlow.TASK_TYPE_EXEC_AOI_SEG,
            "task_type": ConstWorkFlow.TASK_TYPE_EXEC_AOI_SEG,
            "task_content": json.dumps({
                "afs_url": ctx.dataset_afs_path,
                "filter_online_aoi": "1",
                "model_type": "paddleseg_vis_v3",
            }, ensure_ascii=False),
            "task_current_flow": ConstWorkFlow.FLOW_SUBTASK_START,
            "task_status": ConstWorkFlow.FLOW_STATUS_WAIT,
            "task_priority": 0,
        }
        flow.create_flow_task(task_dict)

    mark_task_finished(ctx.task_id)
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser('create competitor recognition task')
    parser.add_argument(
        '--min-area',
        dest='min_area',
        help='边框的最小面积',
        type=float,
        default=1000,
        required=False,
    )
    parser.add_argument(
        '--max-area',
        dest='max_area',
        help='边框的最大面积',
        type=float,
        default=400000,
        required=False,
    )
    parser.add_argument(
        '--buffer',
        dest='buffer',
        help='边框外扩距离',
        type=float,
        default=200,
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        fetch_oldest_batch,
        load_competitor_aoi,
        save_dataset_info,
        upload_dataset_info_to_afs,
        create_dataset,
        upload_dataset_to_afs,
        post_recognition_task,
    )
    desc.attach(main_pipe)

    ctx = Context(
        afs_path='/user/map-data-streeview/aoi-ml/crawl',
        min_area=args.min_area,
        max_area=args.max_area,
        buffer=args.buffer,
        work_dir=Path('/home/<USER>/aoiMl/create_competitor_recognition_task'),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        print(e)
        mark_task_error(ctx.task_id, str(e))
    finally:
        shutil.rmtree(ctx.dataset_path, ignore_errors=True)
        if ctx.dataset_tar_path and ctx.dataset_tar_path.exists():
            ctx.dataset_tar_path.unlink()


if __name__ == '__main__':
    main(parse_args())
