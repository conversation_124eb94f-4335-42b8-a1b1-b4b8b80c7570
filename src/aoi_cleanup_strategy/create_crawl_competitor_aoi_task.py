# !/usr/bin/env python3
"""
用于创建抓取竞品 AOI 任务。
"""
import argparse
import datetime
import json
from dataclasses import dataclass, field
from pathlib import Path
from urllib import request

import pymysql
import shapely.wkt
from shapely import MultiPolygon
from tqdm import tqdm

from src.aoi_cleanup_strategy.mesh_tools import get_wkt
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, aoi_tools
from src.tools.afs_tool import AfsTool
from src.tools.conf_tools import get_mysql_conf

TOP_CITIES = ['北京市', '上海市', '广州市', '深圳市']

desc = pipeline.get_desc()


@dataclass
class CrawlInfo:
    """
    抓取信息
    """
    bid: str
    competitor_id: str

    def to_dict(self):
        return self.__dict__


@dataclass
class Context:
    """
    脚本执行上下文
    """
    mode: str
    src: str
    region: str
    work_dir: Path
    afs_path: str
    priority: int
    crawl_bids: list[str] = field(default_factory=list)
    crawl_infos: list[CrawlInfo] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def get_crawl_predicate(self):
        intel_id_length_predicate = 'length(intel_id) <= 10' if self.src == 'dtc' else 'length(intel_id) > 10'
        return f'''
            not exists(select history.bid 
                       from aoi_intelligence_history history 
                       where history.bid = poi.bid and {intel_id_length_predicate}) and
            exists(select competitor.bid
                   from competitor_id competitor
                   where competitor.bid = poi.bid and competitor.src = '{self.src}')
        '''

    def load_crawl_bids(self, sql, stab=None):
        """
        加载抓取 bid 集合
        """
        if stab is not None:
            for item in stab.fetch_all(sql):
                self.crawl_bids.append(item[0])
        else:
            with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
                for item in stab.fetch_all(sql):
                    self.crawl_bids.append(item[0])


def load_all_build_up_bids(ctx: Context):
    """
    加载所有建成区的 bid 集合
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        records = stab.fetch_all('select st_astext(geom) from build_up')
        for record in tqdm(records, total=len(records), desc='加载抓取 bid'):
            wkt = record[0]
            sql = f'''
                select bid
                from poi
                where st_contains(st_geomfromtext('{wkt}', 4326), geometry) and {ctx.get_crawl_predicate()}
            '''
            ctx.load_crawl_bids(sql, stab)


def load_top_build_up_bids(ctx: Context):
    """
    加载头部建成区的 bid 集合
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        records = stab.fetch_all(f'''
            select st_astext(geom) 
            from build_up
            where city in ('{"','".join(TOP_CITIES)}')
        ''')
        for record in tqdm(records, total=len(records), desc='加载抓取 bid'):
            wkt = record[0]
            sql = f'''
                select bid
                from poi
                where st_contains(st_geomfromtext('{wkt}', 4326), geometry) and {ctx.get_crawl_predicate()}
            '''
            ctx.load_crawl_bids(sql, stab)


def load_top_cities_bids(ctx: Context):
    """
    加载头部城市的 bid 集合
    """
    ctx.load_crawl_bids(f'''
        select poi.bid
        from poi
        where city in ('{"','".join(TOP_CITIES)}') and {ctx.get_crawl_predicate()}
    ''')


def resolve_region_type(region: str):
    """
    解析 region 参数
    """
    if region.lower().startswith('polygon'):
        return 'wkt', region
    elif region.startswith('http'):
        return 'http', region
    else:
        items = [x.strip() for x in region.split(',')]
        if any(items):
            first_item = items[0]
            if first_item.isdigit():
                return 'mesh', items
            elif first_item.startswith('/'):
                return 'file', Path(first_item)
            else:
                return 'city', items

    return None, None


def load_wkt_bids(ctx: Context, wkt: str):
    """
    加载 wkt 范围内的 bid 集合
    """
    ctx.load_crawl_bids(f'''
        select poi.bid
        from poi
        where st_contains(st_geomfromtext('{wkt}', 4326), geometry) and {ctx.get_crawl_predicate()}
    ''')


def load_city_bids(ctx: Context, cities: list[str]):
    """
    加载城市范围内的 bid 集合
    """
    for city in tqdm(cities):
        load_mesh_bids(ctx, aoi_tools.get_mesh_list(city))


def load_mesh_bids(ctx: Context, mesh_ids: list[str]):
    """
    加载指定图幅范围内的 bid 集合
    """
    # 将多个 mesh_id 转为 polygon，再将这些 polygon 合并为 multipolygon。
    multi_polygon = MultiPolygon([shapely.wkt.loads(get_wkt(int(mesh_id))) for mesh_id in mesh_ids])
    load_wkt_bids(ctx, multi_polygon.wkt)


def load_file_bids(ctx: Context, file_path: Path):
    """
    加载存储于文件中的 bid 集合。
    """
    with open(file_path, 'r') as f:
        ctx.crawl_bids = [x.strip() for x in f.readlines()]


def load_http_bids(ctx: Context, url):
    """
    加载存储于文件服务器上的 bid 集合。
    """
    bids_path = ctx.work_dir / 'bids.tsv'
    request.urlretrieve(url, bids_path)
    if bids_path.exists():
        with open(bids_path, 'r') as f:
            ctx.crawl_bids = [x.strip() for x in f.readlines()]


@desc()
def load_crawl_bids(ctx: Context, proceed):
    """
    加载待抓取的 bid 集合。
    """
    if ctx.region is None:
        if ctx.mode == 'build_up':
            load_all_build_up_bids(ctx)
        elif ctx.mode == 'top_build_up':
            load_top_build_up_bids(ctx)
        elif ctx.mode == 'top_cities':
            load_top_cities_bids(ctx)
    else:
        region_type, region_data = resolve_region_type(ctx.region)
        if region_type == 'wkt':
            load_wkt_bids(ctx, region_data)
        elif region_type == 'city':
            load_city_bids(ctx, region_data)
        elif region_type == 'mesh':
            load_mesh_bids(ctx, region_data)
        elif region_type == 'file':
            load_file_bids(ctx, region_data)
        elif region_type == 'http':
            load_http_bids(ctx, region_data)

    proceed()


@desc()
def create_crawl_infos(ctx: Context, proceed):
    """
    创建抓取信息。
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for bid in tqdm(ctx.crawl_bids, total=len(ctx.crawl_bids), desc='生成抓取信息'):
            sql = f'''
                select bid, competitor_id from competitor_id where bid = '{bid}' and src = '{ctx.src}'
            '''
            record = stab.fetch_one(sql)
            if record:
                bid, competitor_id = record
                ctx.crawl_infos.append(CrawlInfo(bid, competitor_id))

    if not ctx.crawl_infos:
        return

    proceed()


def upload_crawl_task_to_db(batch_id, url, priority):
    """
    将抓取任务上传至数据库。
    """
    host, port, user, pwd, database = get_mysql_conf()
    with pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database,
                         charset="utf8mb4") as conn:
        with conn.cursor() as cursor:
            sql = f'''
                insert into crawl_task(task_url, status, batch_id, priority) 
                values('{url}', 0, '{batch_id}', {priority});
            '''
            cursor.execute(sql)
            conn.commit()


@desc()
def upload_crawl_task(ctx: Context, proceed):
    """
    上传抓取任务。
    """
    date = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    batch_id = f'{ctx.mode}_{ctx.src}_{date}'
    crawl_task = {
        'batch_id': batch_id,
        'src': ctx.src,
        'items': [x.to_dict() for x in ctx.crawl_infos]
    }

    task_file_name = f'task_{batch_id}.json'
    task_file_path = ctx.work_dir / task_file_name
    with open(task_file_path, 'w', encoding='utf-8') as f:
        f.write(json.dumps(crawl_task))

    try:
        afs = AfsTool()
        afs.put(str(task_file_path), ctx.afs_path)
        upload_crawl_task_to_db(batch_id, f'{ctx.afs_path}/{task_file_name}', ctx.priority)
    except Exception as e:
        print(e)
    finally:
        task_file_path.unlink(missing_ok=True)

    proceed()


def parse_args():
    """
    解析命令行参数。
    """
    parser = argparse.ArgumentParser('create crawl competitor aoi task')
    parser.add_argument(
        '--mode',
        dest='mode',
        help='运行模式 {build_up | top_build_up | top_cities | all | (region)}',
        type=str,
        default='region',
        required=False,
    )
    parser.add_argument(
        '--src',
        dest='src',
        help='竞品源 {dtc | (sjc)}',
        type=str,
        default='sjc',
        required=False,
    )
    parser.add_argument(
        '--region',
        dest='region',
        help=f'''
            指定的抓取范围，此值比 mode 高优。
            region 现支持以下几种格式：
            1. 城市名（可使用逗号分割）
            2. wkt（仅支持单个 polygon）
            3. 图幅号（可使用逗号分割）
            4. bid 文件路径（每行一个 bid）
            5. 单列 bid 文件下载地址
        ''',
        type=str,
        default=None,
        required=False,
    )
    parser.add_argument(
        '--priority',
        dest='priority',
        help='优先级',
        type=int,
        default=0,
        required=False,
    )
    return parser.parse_args()


def create_task(mode, src, region, priority):
    """
    创建抓取任务。
    """
    main_pipe = pipeline.Pipeline(
        load_crawl_bids,
        create_crawl_infos,
        upload_crawl_task,
    )
    desc.attach(main_pipe)

    # noinspection SpellCheckingInspection
    ctx = Context(
        mode=mode,
        src=src,
        region=region,
        work_dir=Path('cache/create_crawl_competitor_aoi_task'),
        afs_path='/user/map-data-streeview/aoi-ml/crawl',
        priority=priority,
    )
    main_pipe(ctx)


def get_all_mesh_ids():
    """
    获取所有 mesh_id。
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        return [x[0] for x in stab.fetch_all('select mesh_id from poi group by mesh_id')]


def main(args):
    """
    主函数。
    """
    if args.mode != 'all':
        create_task(args.mode, args.src, args.region, args.priority)
    else:
        for mesh_id in get_all_mesh_ids():
            create_task(f'all_{mesh_id}', args.src, mesh_id, args.priority)


if __name__ == '__main__':
    main(parse_args())
