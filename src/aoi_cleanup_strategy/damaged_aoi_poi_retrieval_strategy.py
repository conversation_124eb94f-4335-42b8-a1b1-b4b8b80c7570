# !/usr/bin/env python3
import csv
import sys
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from multiprocessing.pool import Pool
from pathlib import Path
from typing import List

from osgeo import ogr
from tqdm import tqdm

from src.aoi_cleanup_strategy import blu_record
from src.aoi_cleanup_strategy import poi_compute_service
from src.tools import linq
from src.tools import mesh_locker
from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv

BASIC_YARD = 2

desc = pipeline.get_desc()
date_str = datetime.now().strftime('%Y%m%d')


@dataclass
class Aoi:
    face_id: str
    poi_bid: str
    name: str
    mesh_id: str
    center: str
    geom: str


@dataclass
class Poi:
    bid: str
    name: str
    geom: str


EMPTY_POI = Poi('', '', '')


@dataclass
class Issue:
    aoi: Aoi
    redirect_poi: Poi = field(default=EMPTY_POI)
    failed_reason: str = field(default='UNKNOWN')

    def solved(self):
        """
        已解决：找到了重定向后的 POI。
        """
        return self.redirect_poi != EMPTY_POI

    def completed(self):
        """
        已完成：已处理完成，可能是已解决，也可能无法解决。
        """
        return self.solved() or self.failed_reason != 'UNKNOWN'


@dataclass
class Context:
    output_dir: Path

    # [bid, face_id]
    blu_face_path: Path = field(init=False)
    # [bid, face_id]
    damaged_poi_path: Path = field(init=False)
    # [bid, face_id, name, mesh_id, center, geom]
    damaged_blu_face_path: Path = field(init=False)

    output_solved_path: Path = field(init=False)
    output_unsolved_path: Path = field(init=False)

    issues: List[Issue] = field(init=False, default_factory=lambda: [])

    def uncompleted_issues(self):
        return [x for x in self.issues if not x.completed()]

    def __post_init__(self):
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.blu_face_path = self.output_dir / 'blu_face.tsv'
        self.damaged_poi_path = self.output_dir / 'damaged_poi.tsv'
        self.damaged_blu_face_path = self.output_dir / 'damaged_blu_face.tsv'

        self.output_solved_path = self.output_dir / 'solved.tsv'
        self.output_unsolved_path = self.output_dir / 'unsolved.tsv'


@desc()
def export_blu_face(ctx: Context, proceed):
    if ctx.blu_face_path.exists():
        proceed()
        return

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = f'''
            select b.poi_bid, a.face_id from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where a.aoi_level = {BASIC_YARD} and a.src != 'SD' and b.poi_bid != '';
        '''
        ret = pgsql.fetch_all(conn, sql)
        if ret:
            tsv.write_tsv(ctx.blu_face_path, ret)

    proceed()


@desc()
def export_damaged_poi(ctx: Context, proceed):
    if ctx.damaged_poi_path.exists():
        proceed()
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        blu_faces = list(tsv.read_tsv(ctx.blu_face_path))
        bids = [bid for bid, face_id in blu_faces]
        sql = '''
            select bid from poi 
            where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        if ret:
            valid_bids = {x[0] for x in ret}
            damaged_blu_pois = [
                [bid, face_id]
                for bid, face_id in blu_faces
                if bid not in valid_bids
            ]
            tsv.write_tsv(ctx.damaged_poi_path, damaged_blu_pois)

    proceed()


@desc()
def export_damaged_blu_face(ctx: Context, proceed):
    if ctx.damaged_blu_face_path.exists():
        proceed()
        return

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        damaged_pois = list(tsv.read_tsv(ctx.damaged_poi_path))
        face_ids = [face_id for _, face_id in damaged_pois]
        sql = '''
            select
                b.poi_bid, a.face_id, a.name_ch, a.mesh_id, gcj2mc(ST_AsText(ST_Centroid(a.geom))), ST_AsText(a.geom)
            from
                blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where
                a.src != 'SD' and a.face_id in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(face_ids)])
        tsv.write_tsv(ctx.damaged_blu_face_path, ret)

    proceed()


@desc()
def load_issue(ctx: Context, proceed):
    aoi_list = [
        Aoi(
            face_id=face_id,
            poi_bid=bid,
            name=name,
            mesh_id=mesh_id,
            center=center,
            geom=geom,
        )
        for bid, face_id, name, mesh_id, center, geom
        in tsv.read_tsv(ctx.damaged_blu_face_path)
    ]
    ctx.issues = [Issue(aoi=aoi) for aoi in aoi_list]
    proceed()


@desc()
def filter_solvable_issue(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        # 若是由于 POI 判重接口导致的 AOI 主点失效，则不予处理。（如果是“误判重”：这个问题应该交给内容生态部去解决）
        issues = ctx.uncompleted_issues()
        bids = [issue.aoi.poi_bid for issue in issues]
        sql = '''
            select invalid_bid from aoi_repeat_bid
            where invalid_bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        if ret:
            unsolvable_bids = {x[0] for x in ret}
            for issue in issues:
                if issue.aoi.poi_bid in unsolvable_bids:
                    issue.failed_reason = 'DAMAGED_BY_REDUNDANT_POI_SERVICE'

        proceed()


@desc()
def rebind_poi(ctx: Context, proceed):
    issues = ctx.uncompleted_issues()
    data = [
        (issue.aoi.face_id, issue.aoi.name, get_point(issue.aoi.center))
        for issue in issues
    ]
    with Pool(8) as p:
        rebind_bids = list(tqdm(p.imap(get_rebind_poi, data), total=len(data)))

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        valid_bids = [str(bid) for bid, face_id in rebind_bids if bid > 1]
        sql = '''
            select bid, name, ST_AsText(geometry) from poi
            where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(valid_bids)])
        bid_to_poi_dict = {bid: Poi(bid, name, geom) for bid, name, geom in ret}

    face_id_to_bid_dict = {face_id: bid for bid, face_id in rebind_bids}
    for issue in issues:
        bid = face_id_to_bid_dict.get(issue.aoi.face_id)
        if not bid:
            issue.failed_reason = 'POI_COMPUTE_SERVICE.NOTHING'
            continue
        elif bid < 0:
            issue.failed_reason = 'POI_COMPUTE_SERVICE.ERROR'
            continue
        elif bid == 0:
            issue.failed_reason = 'POI_COMPUTE_SERVICE.NOT_SURE'
            continue
        elif bid == 1:
            issue.failed_reason = 'POI_COMPUTE_SERVICE.NEW'
            continue

        # 注意：此处的 bid 是 int。
        poi = bid_to_poi_dict.get(str(bid))
        if not poi:
            issue.failed_reason = 'NEW_POI_NOT_IN_DB'
            continue

        issue.redirect_poi = poi
        issue.failed_reason = 'SOLVED'

    proceed()


@desc()
def verify_rebind_poi(ctx: Context, proceed):
    # 若新 POI 没有落在 AOI 范围内，则不予采用。
    issues = [x for x in ctx.issues if x.solved()]
    for issue in issues:
        aoi_geom = ogr.CreateGeometryFromWkt(issue.aoi.geom)
        poi_geom = ogr.CreateGeometryFromWkt(issue.redirect_poi.geom)
        if not aoi_geom.Contains(poi_geom):
            issue.failed_reason = 'POI_IS_OUTSIDE_OF_AOI'
            issue.redirect_poi = EMPTY_POI

    # 若新 POI 已关联了其它 AOI，则不予采用。
    issues = [x for x in ctx.issues if x.solved()]
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        bids = [x.redirect_poi.bid for x in issues]
        invalid_bids = {}
        if bids:
            sql = '''
                select poi_bid, face_id from blu_face_poi
                where poi_bid in %s and face_id != '';
            '''
            ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
            invalid_bids = {bid: face_id for bid, face_id in ret}

    for issue in issues:
        face_id = invalid_bids.get(issue.redirect_poi.bid)
        if face_id:
            issue.failed_reason = f"RELATED_AOI_ALREADY_EXISTS: '{face_id}'"
            issue.redirect_poi = EMPTY_POI

    proceed()


@desc()
def output_results(ctx: Context, proceed):
    solved_items = [
        [x.aoi.face_id, x.aoi.name, x.aoi.poi_bid, x.aoi.mesh_id, x.redirect_poi.bid, x.redirect_poi.name]
        for x in ctx.issues if x.solved()
    ]
    unsolved_items = [
        [x.aoi.face_id, x.aoi.name, x.aoi.poi_bid, x.aoi.mesh_id, x.failed_reason]
        for x in ctx.issues if not x.solved()
    ]

    tsv.write_tsv(ctx.output_solved_path, solved_items)
    tsv.write_tsv(ctx.output_unsolved_path, unsolved_items)

    ratio = 100 * len(solved_items) / len(ctx.issues)
    print(f'solved: {ratio:0.2f}% ({len(solved_items)} / {len(ctx.issues)})')
    proceed()


@desc()
def import_master_lib(ctx: Context, proceed):
    completed_face_ids = set(get_completed_face_ids(ctx.output_dir))
    items = [
        [mesh_id, face_id, poi_bid, redirect_bid, redirect_name]
        for face_id, name, poi_bid, mesh_id, redirect_bid, redirect_name
        in tsv.read_tsv(ctx.output_solved_path)
        if face_id not in completed_face_ids
    ]
    if not items:
        proceed()
        return

    # 按图幅分组处理
    mesh_group = linq.group_by(items, lambda x: x[0])
    print(f'mesh count: {len(mesh_group)}, item count: {len(items)}')

    mesh_locker.consume_mesh_task_queue(
        tasks=mesh_group.items(),
        lock_prefix='damaged-blu-face-poi-retrieval',
        action=modify_blu_face,
        on_completed=on_completed,
    )
    proceed()


# help functions:

def get_completed_face_ids(work_dir: Path):
    file_path = work_dir / 'completed_face_id.txt'
    if file_path.exists():
        yield from (x[0] for x in tsv.read_tsv(file_path) if x[0])


def modify_blu_face(data: list):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for mesh_id, face_id, poi_bid, redirect_bid, redirect_name in data:
            redirect_name = redirect_name.replace("'", "''")
            sql = f'''
                update blu_face set name_ch = '{redirect_name}'
                where face_id = '{face_id}';
            '''
            pgsql.execute(conn, sql)
            # 主点失效问题，处理的对象都是能够在 blu_face_poi 中找到的 item，故只需要更新，不需要插入新的。
            sql = f'''
                update blu_face_poi set poi_bid = '{redirect_bid}'
                where face_id = '{face_id}';
            '''
            pgsql.execute(conn, sql)
            sql = f'''
                update blu_access set main_bid = '{redirect_bid}'
                where main_bid = '{poi_bid}';
            '''
            pgsql.execute(conn, sql)

    return True


def on_completed(mesh_id: str, data: list):
    face_ids = [x[1] for x in data]
    blu_record.snapshot(face_ids, f'damaged-poi-retrieval-v3-{date_str}')
    print(f'success mesh: {mesh_id}')


def get_point(wkt):
    point = ogr.CreateGeometryFromWkt(wkt)
    assert point.GetGeometryType() == ogr.wkbPoint
    point = [*point.GetPoints()][0]
    x, y = point
    return x, y


def get_rebind_poi(ctx) -> tuple[int, str]:
    face_id, name, position = ctx
    return poi_compute_service.get_redundant_poi(name, position), face_id


def main():
    work_dir = Path('/home/<USER>/dingping/process_damage_aoi_poi_20230518')
    csv.field_size_limit(sys.maxsize)
    main_pipe = pipeline.Pipeline(
        export_blu_face,
        export_damaged_poi,
        export_damaged_blu_face,
        load_issue,
        filter_solvable_issue,
        rebind_poi,
        verify_rebind_poi,
        output_results,
        import_master_lib,
    )
    desc.attach(main_pipe)
    ctx = Context(output_dir=work_dir)
    main_pipe(ctx)


if __name__ == '__main__':
    main()
