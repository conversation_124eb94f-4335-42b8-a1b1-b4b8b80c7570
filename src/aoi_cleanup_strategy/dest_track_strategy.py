# !/usr/bin/env python3
"""
基于终点轨迹的边框问题召回策略
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

import numpy as np
import shapely.wkt
from shapely import MultiLineString, Point, MultiPoint, GeometryCollection
from shapely.geometry.base import BaseGeometry
from sklearn.cluster import DBSCAN
from tqdm import tqdm

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.batch_process.batch_helper import batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql
from src.tools.track_provider import get_provider

STRATEGY_NAME = 'dest_track'
STRATEGY_VERSION = '1.0.0'
MAX_TRACK_COUNT = 1000

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    批处理记录
    """
    face_id: str
    bid: str
    aoi_geom: BaseGeometry
    tracks_geom: MultiLineString = None
    clipped_geom: MultiPoint = None
    clustered_geom: MultiPoint = None
    outer_geom: BaseGeometry = None
    outer_geom_centroid: Point = None
    distance: float = 0
    area_diff: float = 0
    ratio: float = 0
    can_process: bool = True
    reason: str = ''


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    bid_list_path: Path = None
    bids: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    issues: list[str] = field(default_factory=list)
    batch: str = ''

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载 bid 集合
    """
    if ctx.bid_list_path is None:
        return

    with open(ctx.bid_list_path) as f:
        ctx.bids = f.read().splitlines()

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理记录
    """
    get_aoi_sql = '''
        select a.face_id, st_astext(a.geom)
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s
        limit 1;
    '''

    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stab:
        for bid in tqdm(ctx.bids):
            row = back_stab.fetch_one(get_aoi_sql, (bid,))
            if row is None:
                continue

            face_id, aoi_wkt, = row
            ctx.records.append(Record(
                face_id=face_id,
                bid=bid,
                aoi_geom=shapely.wkt.loads(aoi_wkt),
            ))

    proceed()


def is_overlap_road(road_stab, wkt):
    """
    是否压盖道路
    """
    search_road_radius = 10e-5
    invalid_road_forms = ['1', '33']
    min_kind = 8
    get_link_sql = '''
        select form, kind
        from nav_link 
        where st_intersects(st_buffer(st_geomfromtext(%s, 4326), %s), geom);
    '''

    for form, kind in road_stab.fetch_all(get_link_sql, (wkt, search_road_radius)):
        is_form_invalid = any([x for x in invalid_road_forms if x in form])
        is_kind_invalid = kind < min_kind
        if is_form_invalid or is_kind_invalid:
            return True

    return False


def clip_tracks(road_stab, record: Record):
    """
    对轨迹进行裁剪
    """
    aoi_buffer_radius = 500e-5
    aoi_buffered_geom = record.aoi_geom.buffer(aoi_buffer_radius)
    points = []

    for track in record.tracks_geom.geoms:
        start_point = shapely.geometry.Point(*track.coords[0])
        end_point = shapely.geometry.Point(*track.coords[-1])
        start_distance = record.aoi_geom.distance(start_point)
        end_distance = record.aoi_geom.distance(end_point)
        desired_point = start_point if start_distance < end_distance else end_point

        # 太远的点忽略
        if not aoi_buffered_geom.contains(desired_point):
            continue

        if record.aoi_geom.intersects(desired_point):
            continue

        if is_overlap_road(road_stab, desired_point.wkt):
            continue

        points.append(desired_point)

    return points


def cluster_points(multipoint: MultiPoint):
    """
    对轨迹点进行聚类
    """
    points_arr = np.array([point.coords[0] for point in multipoint.geoms])
    # noinspection PyUnresolvedReferences
    labels = DBSCAN(eps=10e-5, min_samples=1).fit(points_arr).labels_
    clustered_points = {}
    for label, point in zip(labels, multipoint.geoms):
        if label == -1:
            continue
        points_group = clustered_points.get(label, [])
        points_group.append(point)
        clustered_points[label] = points_group

    max_count = 0
    max_count_label = -1
    if not any(clustered_points):
        return []

    for key in clustered_points.keys():
        count = len(clustered_points[key])
        if count > max_count:
            max_count_label = key
            max_count = count

    return clustered_points[max_count_label]


def write_record(f, record: Record, *extra_data):
    """
    将批处理记录写入文件
    """
    data = [
               record.bid,
               record.aoi_geom.wkt,
               record.tracks_geom.wkt if record.tracks_geom is not None else '',
               record.clipped_geom.wkt if record.clipped_geom is not None else '',
               record.clustered_geom.wkt if record.clustered_geom is not None else '',
               record.outer_geom.wkt if record.outer_geom is not None else '',
           ] + list([str(x) for x in extra_data])
    f.write('\t'.join(data) + '\n')


@desc()
def fill_tracks_geom(ctx: Context, proceed):
    """
    加载轨迹数据
    """
    with get_provider('dest') as track_provider:
        def process(record: Record):
            links = []
            for row in track_provider.get_tracks(record.bid, 'bid', ())[:MAX_TRACK_COUNT]:
                links.append(shapely.wkt.loads(row['geom']))

            if not any(links):
                record.can_process = False
                record.reason = 'no tracks'
                return

            record.tracks_geom = MultiLineString(links)

        batch_process(ctx.records, process)

    proceed()


@desc()
def fill_clipped_geom(ctx: Context, proceed):
    """
    对轨迹数据进行裁剪
    """
    with PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab:
        def process(record: Record):
            points = clip_tracks(road_stab, record)
            if not any(points):
                record.can_process = False
                record.reason = 'no points after clipping'
                return

            record.clipped_geom = MultiPoint(points)

        batch_process(ctx.records, process)

    proceed()


@desc()
def fill_clustered_geom(ctx: Context, proceed):
    """
    对轨迹数据进行聚类
    """
    def process(record: Record):
        points = cluster_points(record.clipped_geom)
        if not any(points):
            record.can_process = False
            record.reason = 'not points after clustering'
            return

        record.clustered_geom = MultiPoint(points)

    batch_process(ctx.records, process)
    proceed()


@desc()
def fill_outer_geom(ctx: Context, proceed):
    """
    计算出聚合面超出 aoi 的部分
    """
    def process(record: Record):
        record.outer_geom = record.clustered_geom.difference(record.aoi_geom)
        if record.outer_geom.is_empty:
            record.can_process = False
            record.reason = 'outer geom is empty'

        record.outer_geom_centroid = record.outer_geom.centroid
        record.outer_geom = record.outer_geom.convex_hull

    batch_process(ctx.records, process)
    proceed()


@desc()
def find_issues(ctx: Context, proceed):
    """
    生成错误记录
    """
    max_ratio = 200
    min_distance = 5e-5

    def process(record: Record):
        record.distance = record.outer_geom_centroid.distance(record.aoi_geom)
        geom_collection = GeometryCollection([record.aoi_geom, record.outer_geom]).convex_hull
        record.area_diff = geom_collection.area - record.aoi_geom.area
        if record.area_diff > 0 and record.distance == 0:
            return

        record.ratio = record.area_diff / record.distance * 1e5

        if record.ratio > max_ratio:
            record.can_process = False
            record.reason = 'invalid ratio'
            ctx.issues.append(record.bid)
            return

        if record.distance < min_distance:
            record.can_process = False
            record.reason = 'invalid distance'
            ctx.issues.append(record.bid)
            return

    batch_process(ctx.records, process)
    proceed()


@desc()
def save_to_db(ctx: Context, proceed):
    """
    将错误记录保存到数据库
    """
    results = [
        StrategyResult(
            case_id=bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail',
        )
        for bid in ctx.issues
    ]
    export_strategy_result.to_db(results)
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    将批处理记录保存到文件
    """
    with open(ctx.work_dir / 'output.csv', 'w', encoding='utf-8') as f:
        for record in tqdm(ctx.records):
            write_record(f, record, record.distance * 1e5, record.area_diff * 1e10, record.ratio)

    proceed()


def run(bids: list[str], batch: str):
    """
    运行策略。
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_tracks_geom,
        fill_clipped_geom,
        fill_clustered_geom,
        fill_outer_geom,
        find_issues,
        save_to_db,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/dest_track_strategy'),
        bids=bids,
        batch=batch,
    )
    main_pipe(ctx)


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_bids,
        load_records,
        fill_tracks_geom,
        fill_clipped_geom,
        fill_clustered_geom,
        fill_outer_geom,
        find_issues,
        save_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/dest_track_strategy'),
        bid_list_path=Path(args.bid_list_path),
    )
    main_pipe(ctx)


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        required=True,
    )
    return parser.parse_args()


if __name__ == '__main__':
    main(parse_args())
