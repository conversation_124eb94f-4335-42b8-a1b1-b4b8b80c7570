# !/usr/bin/env python3
"""
工单分派模块，根据给定的 bid 列表，按以下策略进行分派：
- 竞品对比（31）：母库存在，竞品存在。
- 主点不在 AOI 范围内（33）：母库存在，竞品不存在。
- 情报新增（36）：母库不存在，竞品存在。
- 特定垂类补充（35）：母库不存在，竞品不存在。
"""
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from pathlib import Path

import requests
from tqdm import tqdm

from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv

# 仅竞品 diff 类别的工单，需要将竞品信息从 aoi_intelligence_history 表中复制到 aoi_intelligence 表中。
COMPETITOR_DIFF_CODE = 31

desc = pipeline.get_desc()


@dataclass
class Context:
    batch: str
    ticket_dir: Path
    bids: set[str] = field(default_factory=set)
    existed_bids: set[str] = field(default_factory=set)
    competitor_bids: set[str] = field(default_factory=set)
    ticket_files: dict[int, Path] = field(default_factory=dict)
    ticket_urls: dict[int, str] = field(default_factory=dict)


def import_bids(file_path: Path):
    @desc('import bids')
    def pipe(ctx: Context, proceed):
        ctx.bids = {x[0] for x in tsv.read_tsv(file_path)}
        proceed()

    return pipe


@desc()
def query_valid_bids(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid from poi where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
        ctx.bids = set(x[0] for x in ret)

    proceed()


@desc()
def query_existed_blu_face(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select poi_bid from blu_face_poi
            where poi_bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
        ctx.existed_bids = {x[0] for x in ret}

    proceed()


@desc()
def query_competitor_aoi(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid from aoi_intelligence_history
            where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
        ctx.competitor_bids = {x[0] for x in ret}

    proceed()


@desc()
def export_ticket(ctx: Context, proceed):
    date_flag = datetime.now().strftime('%Y%m%d')
    prefix = f'ticket_{ctx.batch}_{date_flag}'

    def to_tsv_list(items: set[str]):
        items = list(items)
        items.sort()
        return [[x] for x in items]

    def write_to_local(items: set[str], code: int):
        if items:
            save_path = ctx.ticket_dir / f'{prefix}_code{code}_{len(items)}.txt'
            tsv.write_tsv(save_path, to_tsv_list(items))
            ctx.ticket_files[code] = save_path

    missing_bids = ctx.bids - ctx.existed_bids
    missing_competitor_bids = ctx.bids - ctx.competitor_bids

    # 竞品对比（31）：母库存在，竞品存在。
    write_to_local(ctx.existed_bids & ctx.competitor_bids, code=31)
    # 主点不在 AOI 范围内（33）：母库存在，竞品不存在。
    write_to_local(ctx.existed_bids & missing_competitor_bids, code=33)
    # 情报新增（36）：母库不存在，竞品存在。
    write_to_local(missing_bids & ctx.competitor_bids, code=36)
    # 特定垂类补充（35）：母库不存在，竞品不存在。
    write_to_local(missing_bids & missing_competitor_bids, code=35)
    proceed()


@desc()
def insert_to_aoi_intelligence(ctx: Context, proceed):
    bids = [x[0] for x in tsv.read_tsv(ctx.ticket_files[COMPETITOR_DIFF_CODE])]
    existed_bids = set(get_existed_aoi_intelligence_bids(bids))
    print(f'{len(existed_bids)=}')
    items = get_competitor_details(bids)
    unimport_items = [x for x in items if x[0] not in existed_bids]
    print(f'{len(unimport_items)=}')
    if not unimport_items:
        proceed()
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        insert_values = ','.join(
            f"""
                (
                    '{bid}',
                    '{intel_id}',
                    '{name}',
                    '{address}',
                    {display_x},
                    {display_y},
                    '{mesh_id}',
                    ST_GeomFromText('{geom}', 4326),
                    '{batch}',
                    {crawl_time},
                    '{create_time}'
                )
            """
            for
            bid,
            intel_id,
            name,
            address,
            display_x,
            display_y,
            mesh_id,
            geom,
            batch,
            crawl_time,
            create_time
            in
            unimport_items
        )
        sql = f'''
            insert into aoi_intelligence (
                bid,
                intel_id,
                name,
                address,
                display_x,
                display_y,
                mesh_id,
                geom,
                batch,
                crawl_time,
                create_time
            )
            values
                {insert_values};
        '''
        with conn.cursor() as cursor:
            cursor.execute(sql)

        conn.commit()

    proceed()


@desc()
def upload_ticket_bid_files(ctx: Context, proceed):
    url = 'http://chenxi.vpn.guoke.baidu.com/beefs/get?uuid={uuid}'
    for code, file_path in tqdm(ctx.ticket_files.items()):
        ctx.ticket_urls[code] = url.format(uuid=upload_file(file_path))

    proceed()


@desc()
def post_tickets(ctx: Context, proceed):
    post_url = 'http://mapde-poi.baidu-int.com/prod/plan/strategy/upload'
    for code, file_url in ctx.ticket_urls.items():
        body = get_ticket_body(code, ctx.batch, ctx.ticket_files[code].name, file_url)
        res = requests.post(post_url, json=body)
        print(res.text)

    proceed()


# helpers:

def get_competitor_details(bids: list[str]):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select
                bid, intel_id, name, address, display_x, display_y,
                mesh_id, ST_AsText(geom), batch, crawl_time, create_time
            from
                aoi_intelligence_history
            where
                bid in %s;
        '''
        return pgsql.fetch_all(conn, sql, [tuple(bids)])


def get_existed_aoi_intelligence_bids(bids: list[str]):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid from aoi_intelligence
            where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        return [x[0] for x in ret]


def get_ticket_body(code: int, batch: str, src: str, file_url: str) -> dict:
    date_flag = datetime.now().strftime('%Y-%m%d')
    body = {
        "file_url": file_url,
        "batch": f"{date_flag}-{batch}",
        "strategy_type": code,
        "src": src,
        "remove_dup": False
    }
    return body


def upload_file(file_path) -> str:
    url = 'http://mapde-poi.baidu-int.com/pre/compat/upload'
    file = {'file': open(file_path, 'rb')}
    res = requests.post(url, files=file)
    if res.status_code != 200:
        raise ConnectionError()

    return res.text


def main():
    bid_file = Path('/home/<USER>/dingping/process_create_tickets/precise_aoi_qingbao_edu_diff_20230510.txt')
    pipe = pipeline.Pipeline(
        import_bids(bid_file),
        pipeline.print_desc(lambda x: f'- import bids: {len(x.bids)}'),
        query_valid_bids,
        pipeline.print_desc(lambda x: f'- valid bids: {len(x.bids)}'),
        query_existed_blu_face,
        pipeline.print_desc(lambda x: f'- existed aoi: {len(x.existed_bids)}'),
        query_competitor_aoi,
        pipeline.print_desc(lambda x: f'- competitor aoi: {len(x.competitor_bids)}'),
        export_ticket,
        insert_to_aoi_intelligence,
        upload_ticket_bid_files,
        post_tickets,
    )
    desc.attach(pipe)
    ctx = Context(batch=bid_file.stem, ticket_dir=bid_file.parent)
    pipe(ctx)


if __name__ == '__main__':
    main()
