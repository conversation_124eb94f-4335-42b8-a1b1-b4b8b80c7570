# !/usr/bin/env python3
"""
策略：独占面积率。
计算单个基础院落（aoi_level=2）独有的面积占其总面积的分数，“独有面积”即是：该基础院落减去其它基础院落（Polygon）后的剩余面积。
"""
from dataclasses import dataclass
from dataclasses import field

from osgeo import ogr

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import linq
from src.tools import pgsql
from src.tools import pipeline

STRATEGY_NAME = 'exclusive_area_radio'
STRATEGY_VERSION = '1.0.0'

MIN_EXCLUSIVE_AREA_RADIO = 0.8

desc = pipeline.get_desc()


@dataclass
class BluFace:
    bid: str
    face_id: str
    name: str
    geom: str


@dataclass
class Issue:
    blu_face: BluFace
    overlap_blu_faces: list[BluFace]
    exclusive_area_radio: float = field(default=0.0)


@dataclass
class Context:
    bids: list[str]
    batch: str
    args: dict
    issues: list[Issue] = field(default_factory=list)
    selected_issues: list[Issue] = field(default_factory=list)


@desc()
def fetch_blu_face_geoms(ctx: Context, proceed):
    def create_issue(items: list):
        _, bid, face_id, name, geom = linq.first_or_default(items, lambda x: x[0] == x[1])
        blu_face = BluFace(bid=bid, face_id=face_id, name=name, geom=geom)
        blu_faces = [
            BluFace(bid=bid, face_id=face_id, name=name, geom=geom)
            for target_bid, bid, face_id, name, geom in items
            if target_bid != bid
        ]
        return Issue(blu_face=blu_face, overlap_blu_faces=blu_faces)

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            WITH t AS (
                SELECT
                    b.poi_bid,
                    a.face_id,
                    a.geom
                FROM
                    blu_face a
                    INNER JOIN blu_face_poi b ON a.face_id = b.face_id
                WHERE
                    b.poi_bid IN %s
                    AND a.aoi_level = 2
                    AND a.src != 'SD'
            )
            SELECT
                t.poi_bid,
                b.poi_bid,
                a.face_id,
                a.name_ch,
                ST_AsText(a.geom)
            FROM
                t
                INNER JOIN blu_face a ON ST_Intersects(t.geom, a.geom)
                INNER JOIN blu_face_poi b ON a.face_id = b.face_id
            WHERE
                -- 过滤掉无主点的 AOI，因为无主点不上线，不影响导航。
                a.aoi_level = 2
                AND a.src != 'SD'
                AND b.poi_bid != '';
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
        blu_face_group = linq.group_by(ret, key=lambda x: x[0])
        ctx.issues = [create_issue(x) for x in blu_face_group.values() if len(x) > 1]

    proceed()


@desc()
def calc_exclusive_area_radio(ctx: Context, proceed):
    for issue in ctx.issues:
        # noinspection PyBroadException
        try:
            target_geom = ogr.CreateGeometryFromWkt(issue.blu_face.geom)
            original_area = target_geom.Area()
            for overlap_blu_face in issue.overlap_blu_faces:
                overlap_geom = ogr.CreateGeometryFromWkt(overlap_blu_face.geom)
                target_geom = target_geom.Difference(overlap_geom)

            exclusive_area = target_geom.Area()
            issue.exclusive_area_radio = exclusive_area / original_area
        except:
            pass

    proceed()


def filter_issues(min_exclusive_area_radio: float):
    @desc(f'filter issues by exclusive_area_radio: {min_exclusive_area_radio}')
    def pipe(ctx: Context, proceed):
        ctx.selected_issues = [
            x for x in ctx.issues
            if x.exclusive_area_radio < min_exclusive_area_radio
        ]
        proceed()

    return pipe


@desc()
def save_to_db(ctx: Context, proceed):
    fail_bids = {x.blu_face.bid for x in ctx.selected_issues}
    results = [
        StrategyResult(
            case_id=x.blu_face.bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail' if x.blu_face.bid in fail_bids else 'pass',
            args=ctx.args,
            details=x,
        )
        for x in ctx.issues
    ]
    export_strategy_result.to_db(results)
    proceed()


def run(bids: list[str], batch: str):
    main_pipe = pipeline.Pipeline(
        pipeline.print_desc(lambda x: f'bids: {len(x.bids)}'),
        fetch_blu_face_geoms,
        calc_exclusive_area_radio,
        filter_issues(min_exclusive_area_radio=MIN_EXCLUSIVE_AREA_RADIO),
        save_to_db,
    )
    desc.attach(main_pipe)

    ctx = Context(
        bids=bids,
        batch=batch,
        args={'min_exclusive_area_radio': MIN_EXCLUSIVE_AREA_RADIO}
    )
    main_pipe(ctx)
