"""
Author: zhangcong_cd <EMAIL>
Date: 2023-09-18 09:57:02
LastEditors: zhangcong_cd <EMAIL>
LastEditTime: 2023-10-16 13:54:06
FilePath: \aoi-ml\src\aoi_cleanup_strategy\cluster_mis_trajectory.py
"""
import datetime
import traceback
import warnings
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path

import pandas as pd
from pandas.errors import SettingWithCopyWarning
from tqdm import tqdm

from src.tools import notice_tool, pgsql
from src.tools import pipeline

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
warnings.filterwarnings(action="ignore", message="Mean of empty slice")

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    上下文信息结构体
    """

    trajectory_path: Path = field(init=False)
    df: pd.DataFrame = field(init=False)


def load_trajectory_data(ctx: Context, proceed):
    """
    加载聚合后的mis轨迹数据
    """
    try:
        ctx.df = pd.read_csv(ctx.trajectory_path)
    except pd.errors.EmptyDataError:
        ctx.df = pd.DataFrame()

    proceed()


@desc()
def export_mine_trajectory(ctx: Context, proceed):
    """
    导出mis挖掘到的轨迹信息
    """
    with pgsql.get_connection(pgsql.TRAJECTORY_CONFIG) as conn:
        sql = "insert into mis_mine_trajectory(bid, route_end, track_end, cuid_time, filename)values"
        num = 0
        for _, row in ctx.df.iterrows():
            bid = str(row["bid"])
            route_end = "Point" + row["route_end"].replace(",", " ")
            track_end = "Point" + row["track_end"].replace(",", " ")
            cuid_time = f"{row['cuid']}_{row['lbs_navigation_start_timestring']}"
            sql = f'''
                {sql}('{bid}', st_geomfromtext('{route_end}', 4326), st_geomfromtext('{track_end}', 4326), 
                '{cuid_time}', '{ctx.trajectory_path.stem}'),
            '''
            num = num + 1
            if num > 500:
                pgsql.execute(conn, sql.strip().strip(','))
                num = 0
                sql = "insert into mis_mine_trajectory(bid, route_end, track_end, cuid_time, filename)values"

        if num > 0:
            pgsql.execute(conn, sql.strip().strip(','))

    proceed()


@desc()
def export_cluster_trajectory(ctx: Context, proceed):
    """
    mis轨迹结果保存至AFS
    """

    with pgsql.get_connection(pgsql.TRAJECTORY_CONFIG) as conn:
        sql = "insert into mis_mine_cluster_trajectory(bid, route_end, track_end, cuid_time, filename)values"
        num = 0
        for _, row in ctx.df.iterrows():
            bid = str(row["bid"])
            # route_end = f"Point({row['route_end'][0]} {row['route_end'][1]})"
            # track_end = f"Point({row['track_end'][0]} {row['track_end'][1]})"
            route_end = "Point" + row["route_end"].replace(",", " ")
            track_end = "Point" + row["track_end"].replace(",", " ")
            cuid_time = str(row['cuid_timestamp'])
            sql = f'''
                {sql}('{bid}', st_geomfromtext('{route_end}', 4326), st_geomfromtext('{track_end}', 4326), 
                '{cuid_time}', '{ctx.trajectory_path.stem}'),
            '''
            num = num + 1
            if num > 500:
                pgsql.execute(conn, sql.strip().strip(','))
                num = 0
                sql = "insert into mis_mine_cluster_trajectory(bid, route_end, track_end, cuid_time, filename)values"

        if num > 0:
            pgsql.execute(conn, sql.strip().strip(','))

    proceed()


@desc()
def delete_invalid_mis_data(ctx: Context, proceed):
    date_str = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d")
    with pgsql.get_connection(pgsql.TRAJECTORY_CONFIG) as conn:
        sql = "delete from mis_mine_trajectory where create_time < %s"
        pgsql.execute(conn, sql, [date_str])

    proceed()


def main():
    """

    :return:
    """
    data_dir = Path("/home/<USER>/mnt_newest/mineBid")
    main_pipe = pipeline.Pipeline(
        load_trajectory_data,
        export_mine_trajectory,
        delete_invalid_mis_data,
    )
    desc.attach(main_pipe)

    latest_intact_dir = list(data_dir.iterdir())
    latest_intact_dir.sort()
    latest_intact_dir_list = latest_intact_dir[-7:]

    ctx = Context()

    for latest_intact_dir in latest_intact_dir_list:
        if not Path(latest_intact_dir / "_SUCCESS").exists():
            continue
        if Path(latest_intact_dir / "_SAVE_MIS_SUCCESS").exists():
            continue

        for file_name in tqdm(list(latest_intact_dir.glob("**/*.csv"))):
            print(file_name)
            ctx.trajectory_path = file_name
            main_pipe(ctx)

        Path(latest_intact_dir / "_SAVE_MIS_SUCCESS").touch()
        notice_tool.send_hi(
            f"未聚合mis轨迹保存至数据库完成, 轨迹日期为{latest_intact_dir.stem}",
            token=notice_tool.MIS_NOTICE,
        )


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(e)
        traceback.print_exc()
        notice_tool.send_hi("未聚合mis轨迹保存至数据库完成运行失败" + str(e), atuserids=["zhangcong_cd"])
