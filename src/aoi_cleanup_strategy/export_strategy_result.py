# !/usr/bin/env python3
"""
导出数据到策略数据库 poi_online -> strategy_result
"""
import dataclasses
import json
from dataclasses import dataclass
from dataclasses import field
from typing import Iterable
from typing import Union

import psycopg2.extras

from src.tools import pgsql

RESULT_PASS = "pass"
RESULT_FAIL = "fail"


@dataclass
class StrategyResult:
    case_id: str
    batch: str
    strategy_name: str
    strategy_version: str
    result: str
    args: dict = field(default_factory=dict)
    tags: list[str] = field(default_factory=list)
    details: Union[any, dict] = field(default_factory=dict)


def to_db(results: Iterable[StrategyResult]):
    # NOTE: 在 pycharm pro 上，下面的 sql 可能会报错，不用管这个错误，是 pycharm 自己蠢，这句 sql 配合
    # psycopg2.extras.execute_values() 就是这么写的，没有问题！
    sql = '''
        INSERT INTO strategy_result (case_id, batch, strategy_name, strategy_version, result, args, tags, details)
        VALUES %s;
    '''
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        conn.autocommit = True
        with conn.cursor() as cursor:
            psycopg2.extras.execute_values(cursor, sql, [
                (
                    x.case_id,
                    x.batch,
                    x.strategy_name,
                    x.strategy_version,
                    x.result,
                    json.dumps(x.args),
                    x.tags,
                    json.dumps(x.details if type(x.details) == dict else dataclasses.asdict(x.details)),
                )
                for x in results
            ], page_size=500)
