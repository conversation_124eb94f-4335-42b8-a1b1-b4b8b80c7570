# !/usr/bin/env python3
"""
用于过滤导航清单，同时填充一些必要的统计字段。
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.tools import pipeline, pgsql, tsv

desc = pipeline.get_desc()


@dataclass
class Intelligence:
    poi_bid: str
    poi_name: str
    poi_tag: str
    poi_city: str
    poi_pv: str
    poi_level: str
    connected_aoi: bool = False
    exists_aoi: bool = False
    main_bid: str = ''
    aoi_tag: str = ''
    aoi_name: str = ''
    name_iou: float = 0
    exists_competitor: bool = False
    competitor_name: str = ''
    competitor_wkt: str = ''
    parent_poi_bid: str = ''
    poi_wkt: str = None
    aoi_id: str = ''
    aoi_src: str = ''


@dataclass
class Context:
    work_dir: Path
    navi_bids_path: Path
    intelligences: list[Intelligence] = field(default_factory=list)


@desc()
def load_navi_bids(ctx: Context, proceed):
    with open(ctx.navi_bids_path, 'r', encoding='utf-8') as f:
        for line in f.readlines():
            bid, name, tag, city, pv, level = tuple(line.strip().split('\t'))
            ctx.intelligences.append(Intelligence(
                poi_bid=bid,
                poi_name=name,
                poi_tag=tag,
                poi_city=city,
                poi_pv=pv,
                poi_level=level,
            ))

    proceed()


@desc()
def fill_poi_wkt(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for intelligence in tqdm(ctx.intelligences, total=len(ctx.intelligences), desc='填充 poi wkt'):
            sql = f'''
                select st_astext(geometry), relation_bid from poi where bid = %s;
            '''
            record = pgsql.fetch_one(conn, sql, (intelligence.poi_bid,))
            if record:
                intelligence.poi_wkt = record[0]
                intelligence.parent_poi_bid = record[1]

    proceed()


@desc()
def fill_connected_aoi(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for intelligence in tqdm(ctx.intelligences, total=len(ctx.intelligences), desc='填充关联 aoi'):
            sql = f'''
                select face_id from blu_face_poi where poi_bid = %s limit 1;
            '''
            if pgsql.fetch_one(conn, sql, (intelligence.poi_bid,)):
                intelligence.connected_aoi = True

    proceed()


@desc()
def fill_aoi_id(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for intelligence in tqdm(ctx.intelligences, total=len(ctx.intelligences), desc='填充 aoi id'):
            sql = f'''
                select face_id, src
                from blu_face 
                where st_contains(geom, st_geomfromtext(%s, 4326)) and
                      src != 'SD' and
                      aoi_level > 1 and
                      kind != '52'
                limit 1;
            '''
            record = pgsql.fetch_one(conn, sql, (intelligence.poi_wkt,))
            if record:
                face_id, src = record
                intelligence.aoi_id = face_id
                intelligence.aoi_src = src
                intelligence.exists_aoi = True

    proceed()


@desc()
def fill_aoi_main_bid(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for intelligence in tqdm(ctx.intelligences, total=len(ctx.intelligences), desc='填充 aoi 主点 bid'):
            sql = f'''
                select poi_bid from blu_face_poi where face_id = %s;
            '''
            record = pgsql.fetch_one(conn, sql, (intelligence.aoi_id,))
            if record:
                intelligence.main_bid = record[0]

    proceed()


@desc()
def fill_main_poi_properties(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for intelligence in tqdm(ctx.intelligences, total=len(ctx.intelligences), desc='填充 aoi 主点信息'):
            sql = f'''
                select name, std_tag from poi where bid = %s;
            '''
            record = pgsql.fetch_one(conn, sql, (intelligence.main_bid,))
            if record:
                name, std_tag = record
                intelligence.aoi_name = name
                intelligence.aoi_tag = std_tag

    proceed()


@desc()
def fill_name_iou(ctx: Context, proceed):
    for intelligence in tqdm(ctx.intelligences, total=len(ctx.intelligences), desc='填充名称相似度'):
        poi_name_set = set(intelligence.poi_name)
        aoi_name_set = set(intelligence.aoi_name)
        intersection_length = len(poi_name_set.intersection(aoi_name_set))
        union_length = len(poi_name_set.union(aoi_name_set))
        if union_length > 0:
            intelligence.name_iou = intersection_length / union_length

    proceed()


@desc()
def fill_competitor_properties(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for intelligence in tqdm(ctx.intelligences, total=len(ctx.intelligences), desc='填充竞品信息'):
            sql = f'''
                select name, st_astext(geom)
                from aoi_intelligence_history 
                where bid = %s 
                order by create_time desc 
                limit 1;
            '''
            record = pgsql.fetch_one(conn, sql, (intelligence.poi_bid,))
            if record:
                name, wkt = record
                intelligence.competitor_name = name
                intelligence.competitor_wkt = wkt
                intelligence.exists_competitor = True

    proceed()


@desc()
def save_intelligences(ctx: Context, proceed):
    tsv.write_tsv(
        ctx.work_dir / 'intelligences.tsv',
        [
            [
                x.poi_bid,
                x.poi_name,
                x.poi_tag,
                x.poi_city,
                x.poi_pv,
                x.poi_level,
                x.connected_aoi,
                x.exists_aoi,
                x.main_bid if x.main_bid != '' else x.aoi_id,
                x.aoi_tag,
                x.aoi_name,
                x.aoi_src,
                x.name_iou,
                x.exists_competitor,
                x.competitor_name,
                x.competitor_wkt,
                x.parent_poi_bid,
            ]
            for x in ctx.intelligences
        ]
    )

    proceed()


@desc()
def crawl_bids(ctx: Context, proceed):
    bids = []
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for intelligence in tqdm(ctx.intelligences, total=len(ctx.intelligences), desc='填充竞品信息'):
            sql = f'''
                select * from aoi_intelligence_history where bid = %s;
            '''
            if pgsql.fetch_one(conn, sql, (intelligence.poi_bid,)):
                bids.append(intelligence.poi_bid)

    tsv.write_tsv(
        ctx.work_dir / 'crawl_bids.tsv',
        [
            [
                x
            ]
            for x in bids
        ]
    )

    proceed()


def main():
    main_pipe = pipeline.Pipeline(
        load_navi_bids,
        # crawl_bids,
        fill_poi_wkt,
        fill_connected_aoi,
        fill_aoi_id,
        fill_aoi_main_bid,
        fill_main_poi_properties,
        fill_name_iou,
        fill_competitor_properties,
        save_intelligences,
    )
    desc.attach(main_pipe)

    ctx = Context(
        work_dir=Path('/home/<USER>/dingping/filter_navi'),
        navi_bids_path=Path('/home/<USER>/chenjie/navi_bids.tsv'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
