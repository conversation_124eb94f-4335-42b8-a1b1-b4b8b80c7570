# !/usr/bin/env python3
"""
导航清单专题：基础院落重复清洗。
FIXME: 重复的 AOI 可能是由于现有机制的缺陷产生的，该脚本仅负责检出，并没有从根本上解决问题。
可能的原因有：识别上线时间长，在此期间已有人工产出入库，而识别产出仅在启动上线前做一次排重，故可能导致 AOI 重复。
ref to: https://console.cloud.baidu-int.com/devops/icafe/issue/poiinshanghai-8108/show
"""
import dataclasses
import json
import os
import sys
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path
from typing import Any
from typing import List

root_path = os.path.abspath((os.path.dirname(__file__)) + "/../../")
sys.path.insert(0, root_path)

from src.tools import linq
from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv
from src.tools import utils

desc = pipeline.get_desc()


@dataclass
class Context:
    work_dir: Path
    item_pipe: Any

    face_ids_path: Path = field(init=False)
    issues: list = field(default_factory=lambda: [])

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        self.face_ids_path = self.work_dir / 'face_ids.tsv'


@dataclass
class Aoi:
    face_id: str
    bid: str
    name: str
    city: str
    geom: Any


@dataclass
class OverlapItem:
    aoi: Aoi
    iou: float = field(default=0.0)


@dataclass
class ItemContext:
    aoi: Aoi
    overlap_items: List[OverlapItem]


@desc()
def export_premium_aoi(ctx: Context, proceed):
    if ctx.face_ids_path.exists():
        proceed()
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = f'''
            select face_id from aoi_gate_match_batch_result
            where valid = 1 and level = 3;
        '''
        ret = pgsql.fetch_all(conn, sql)
        if ret:
            tsv.write_tsv(ctx.face_ids_path, ret)

    proceed()


def export_aoi_by_bid(bids: list[str]):
    @desc('export aoi by bid')
    def pipe(ctx: Context, proceed):
        if ctx.face_ids_path.exists():
            proceed()
            return

        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            sql = '''
                select face_id from blu_face_poi
                where poi_bid in %s;
            '''
            ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
            if ret:
                tsv.write_tsv(ctx.face_ids_path, ret)

        proceed()

    return pipe


@desc()
def export_all_aoi(ctx: Context, proceed):
    if ctx.face_ids_path.exists():
        proceed()
        return

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = f'''
            select face_id from blu_face_poi
            where poi_bid != '';
        '''
        ret = pgsql.fetch_all(conn, sql)
        if ret:
            tsv.write_tsv(ctx.face_ids_path, ret)

    proceed()


def export_overlap_issues(save_dir: Path = None):
    existed_face_ids = {x.stem for x in save_dir.glob('*.json')} if save_dir else {}

    @desc('export overlap issues')
    def pipe(ctx: Context, proceed):
        face_id_list = [x[0] for x in tsv.read_tsv(ctx.face_ids_path)]
        face_id_list = [x for x in face_id_list if x not in existed_face_ids]
        for item_ctx in get_item_contexts(face_id_list):
            ctx.item_pipe(item_ctx)

        proceed()

    return pipe


def load_overlap_issues(input_dir: Path):
    @desc('load overlap issues')
    def pipe(ctx: Context, proceed):
        ctx.issues = [utils.read_json(x) for x in input_dir.glob('*.json')]
        proceed()

    return pipe


@desc()
def distinct_aoi(ctx: Context, proceed):
    """
    精准 AOI 可以与精准 AOI 压盖，这时只需要以其中一个为准。
    """
    ctx.issues = [x for x in ctx.issues if x['overlap_items']]
    face_id_pairs = {
        (issue['aoi']['face_id'], x['aoi']['face_id'])
        for issue in ctx.issues
        for x in issue['overlap_items']
    }
    unique_face_id_pairs = set()
    while len(face_id_pairs) > 0:
        a, b = face_id_pairs.pop()
        if (b, a) not in face_id_pairs:
            unique_face_id_pairs.add((a, b))

    overlap_face_ids = {b for _, b in unique_face_id_pairs}
    filtered_results = [x for x in ctx.issues if x['aoi']['face_id'] not in overlap_face_ids]
    filtered_results = [x for x in filtered_results if x['overlap_items']]

    ctx.issues = filtered_results
    proceed()


def persist_overlap_issues(output_dir: Path):
    output_dir.mkdir(parents=True, exist_ok=True)

    @desc('persist overlap issues')
    def pipe(ctx: Context, proceed):
        save_path = output_dir / 'overlap_issues.json'
        save_path.write_text(json.dumps(ctx.issues, ensure_ascii=False, indent=2), encoding='utf8')

        save_path = output_dir / 'overlap_bid.txt'
        bids = {issue['aoi']['bid'] for issue in ctx.issues}
        bids = [[x] for x in bids]
        tsv.write_tsv(save_path, bids)
        proceed()

    return pipe


def calc_aoi_iou(ctx: ItemContext, proceed):
    for overlap_item in ctx.overlap_items:
        overlap_item.iou = utils.calc_iou(ctx.aoi.geom, overlap_item.aoi.geom)

    proceed()


def filter_overlap_item_by_iou(iou: float):
    def pipe(ctx: ItemContext, proceed):
        filtered_items = [x for x in ctx.overlap_items if x.iou >= iou]
        ctx.overlap_items = filtered_items
        proceed()

    return pipe


def persist_overlap_issue(output_dir: Path):
    output_dir.mkdir(parents=True, exist_ok=True)

    def pipe(ctx: ItemContext, proceed):
        ret = dataclasses.asdict(ctx)
        json_text = json.dumps(ret, ensure_ascii=False, indent=2)
        save_path = output_dir / f'{ctx.aoi.face_id}.json'
        save_path.write_text(json_text, encoding='utf8')
        proceed()

    return pipe


# helper functions:


def get_item_contexts(face_ids):
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as back_conn,
        pgsql.get_connection(pgsql.POI_CONFIG) as poi_conn
    ):
        sql = '''
            WITH q AS (
                SELECT
                    face_id,
                    geom
                FROM
                    blu_face
                WHERE
                    aoi_level = 2
                    AND src != 'SD'
                    AND face_id IN %s
            ),
            r AS (
                SELECT
                    q.face_id AS p_fid,
                    a.face_id AS fid,
                    b.poi_bid,
                    a.name_ch,
                    a.city_name,
                    ST_AsText(a.geom) AS geom
                FROM
                    q
                    INNER JOIN blu_face AS a ON a.aoi_level = 2 AND a.src != 'SD'
                    AND ST_Intersects(a.geom, q.geom)
                    LEFT JOIN blu_face_poi AS b ON a.face_id = b.face_id
            )
            SELECT
                p_fid,
                fid,
                poi_bid,
                name_ch,
                city_name,
                geom
            FROM
                r
            WHERE
                p_fid IN (
                    SELECT
                        p_fid
                    FROM
                        r
                    GROUP BY
                        p_fid
                    HAVING
                        COUNT(*) > 1
                );
        '''
        ret = pgsql.fetch_all(back_conn, sql, [tuple(face_ids)])
        if not ret:
            return

        overlap_aoi_list = linq.group_by(ret, lambda x: x[0])
        ctx_items = [get_item_context(x) for x in overlap_aoi_list.values()]
        # 只检查精准 AOI 的 bid，压盖 AOI 的 bid 不检查，因为其也需要人工清理。
        aoi_bids = [x.aoi.bid for x in ctx_items]
        sql = '''
            select bid from poi where bid in %s;
        '''
        ret = pgsql.fetch_all(poi_conn, sql, [tuple(aoi_bids)])
        if not ret:
            return

        valid_bids = {x[0] for x in ret}
        valid_ctx_items = [x for x in ctx_items if x.aoi.bid in valid_bids]
        yield from valid_ctx_items


def get_item_context(items):
    target_aoi = [Aoi(face_id, bid, name, city, geom)
                  for key, face_id, bid, name, city, geom in items
                  if key == face_id]
    assert len(target_aoi) == 1
    target_aoi = target_aoi[0]
    overlap_items = [
        Aoi(face_id=face_id, bid=bid, name=name, city=city, geom=geom)
        for key, face_id, bid, name, city, geom in items if key != face_id
    ]
    overlap_items = [OverlapItem(x) for x in overlap_items]
    ret = ItemContext(target_aoi, overlap_items)
    return ret


def main():
    work_dir = Path(r'C:\Users\<USER>\Downloads\overlap_issues_20230420')
    output_dir = work_dir
    output_item_dir = work_dir / 'overlap_issues'

    bids_path = Path('')
    bids = [x[0] for x in tsv.read_tsv(bids_path)]

    main_pipe = pipeline.Pipeline(
        # NOTE: 根据需求选择待处理的 AOI：1. 全部的精准 AOI；2. 导出所有 AOI；3. 根据 bid 导出 AOI。
        # export_premium_aoi,
        # export_all_aoi,
        export_aoi_by_bid(bids),
        export_overlap_issues(save_dir=output_item_dir),
        load_overlap_issues(input_dir=output_item_dir),
        distinct_aoi,
        persist_overlap_issues(output_dir=output_dir),
    )
    desc.attach(main_pipe)
    item_pipe = pipeline.Pipeline(
        calc_aoi_iou,
        filter_overlap_item_by_iou(0.1),
        persist_overlap_issue(output_dir=output_item_dir),
    )
    ctx = Context(work_dir=work_dir, item_pipe=item_pipe)
    main_pipe(ctx)


if __name__ == '__main__':
    main()
