"""
Author: zhangcong_cd <EMAIL>
Date: 2023-09-18 09:57:02
LastEditors: zhangcong_cd <EMAIL>
LastEditTime: 2023-10-16 13:54:06
"""
import datetime
import time
import traceback
import warnings
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path
from typing import Dict, List

import pandas as pd
import shapely.wkt
from pandas.errors import SettingWithCopyWarning
from tqdm import tqdm

from src.tools import notice_tool, pgsql
from src.tools import pipeline

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
warnings.filterwarnings(action="ignore", message="Mean of empty slice")

desc = pipeline.get_desc()


@dataclass
class PoiInfo:
    """
    poi相关信息
    """

    bid: str = ""
    city: str = ""
    name: str = ""
    geom: str = ""
    std_tag: str = ""


@dataclass
class Context:
    """
    上下文信息结构体
    """

    trajectory_path: Path = field(init=False)
    df: pd.DataFrame = field(init=False)
    poi_dict: Dict[str, List[PoiInfo]] = field(default_factory=dict)
    num_intelligence = 0


def load_cluster_trajectory_data(ctx: Context, proceed):
    """
    加载聚合后的mis轨迹数据
    """
    try:
        ctx.df = pd.read_csv(ctx.trajectory_path)
    except pd.errors.EmptyDataError:
        ctx.df = pd.DataFrame()

    proceed()


@desc()
def filter_invalid_trajectory(ctx: Context, proceed):
    """
    根据规划终点和实际终点的距离筛选轨迹数据
    """

    valid_trajectory = []
    for _, row in ctx.df.iterrows():
        route_end = "Point" + row["route_end"].replace(",", " ")
        track_end = "Point" + row["track_end"].replace(",", " ")
        dis = shapely.wkt.loads(route_end).distance(shapely.wkt.loads(track_end))
        if dis > 0.01:
            valid_trajectory.append(0)
            continue
        valid_trajectory.append(1)

    if not ctx.df.empty:
        ctx.df["valid_trajectory"] = valid_trajectory
        ctx.df = ctx.df[ctx.df["valid_trajectory"] == 1]

    proceed()


def find_gate_by_node_geom(geom, aoi_geom):
    """
    :param geom: 点坐标
    :param aoi_geom: aoi坐标，用于计算搜索的范围
    :return:
    """
    node_dict = dict()
    perimeter = shapely.wkt.loads(aoi_geom).length
    dis = min(perimeter * 0.12, 0.001)
    with pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn:
        sql = f"""
            with tmp as (select st_geomfromtext(%s, 4326) as geom) 
            select distinct b.node_id, st_astext(st_shortestline(b.geom, st_geomfromtext(%s, 4326)))
            from nav_gate a inner join nav_node b on a.node_id = b.node_id, tmp 
            where st_dwithin(b.geom, tmp.geom, %s)
        """
        for (
                node_id,
                geom_tmp,
        ) in pgsql.fetch_all(conn, sql, [geom, aoi_geom, dis]):
            node_dict[node_id] = geom_tmp
    return node_dict


def find_gate_by_line_geom(geom, aoi_geom):
    """
    :param geom: 线坐标
    :param aoi_geom: aoi坐标，用于计算搜索的范围
    :return:
    """
    node_dict = dict()
    with pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn:
        sql = f"""
            with tmp as (select st_geomfromtext(%s, 4326) as geom) 
            select distinct b.node_id, st_astext(st_shortestline(b.geom, st_geomfromtext(%s, 4326)))
            from nav_gate a inner join nav_node b on a.node_id = b.node_id, tmp 
            where st_dwithin(b.geom, tmp.geom, 0.0002)
        """
        for (
                node_id,
                geom_tmp,
        ) in pgsql.fetch_all(conn, sql, [geom, aoi_geom]):
            node_dict[node_id] = geom_tmp
    return node_dict


def get_node_geom(node_id):
    """
    获取node的坐标信息
    """
    with pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn:
        sql = "select st_astext(geom) from nav_node where node_id = %s"
        return pgsql.fetch_one(conn, sql, [node_id])


def near_access(conn, geom, bid):
    """
    判断坐标是否位于bid对应框的出入口附近
    :param conn:
    :param geom:
    :param bid:
    :return:
    """
    sql = """
        select st_astext(geom) from blu_access, blu_access_traffic 
        where blu_access.access_id = blu_access_traffic.access_id and main_bid = %s 
        and valid_obj = 1 and transit_type <= 2
    """
    geom_access_list = pgsql.fetch_all(conn, sql, [bid])
    for (geom_access,) in geom_access_list:
        if shapely.wkt.loads(geom).distance(shapely.wkt.loads(geom_access)) <= 0.0005:
            return True
    return False


def cross_high_level_link(line_geom, node_id):
    """
    判断一条线是否跨越了高等级道路
    :param line_geom:
    :param node_id:
    :return:
    """
    with pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn:
        sql = """
            select count(*) from nav_link 
            where st_intersects(geom, st_geomfromtext(%s, 4326)) and kind <= 7 and s_nid != %s and e_nid != %s
        """
        return pgsql.fetch_one(conn, sql, [line_geom, node_id, node_id])[0] > 0


def query_related_node(bid):
    """
    #查询bid所关联的所有node
    :param bid:
    :return: node_set
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        node_dict = dict()
        sql = """
            select distinct b.node_id, st_astext(geom) 
            from blu_access a inner join blu_access_gate_rel b 
            on a.access_id = b.access_id 
            where main_bid = %s
        """
        for (node_id_tmp, access_geom_tmp) in pgsql.fetch_all(conn, sql, [bid]):
            node_dict[node_id_tmp] = access_geom_tmp
        return node_dict


def node_related_access(node_id):
    """
    查询大门是否关联了出入口
    """
    sql = """
        select count(*) from  blu_access a inner join blu_access_gate_rel b 
        on a.access_id = b.access_id 
        inner join blu_face_poi c on a.main_bid = poi_bid 
        inner join blu_face d on c.face_id = d.face_id 
        where b.node_id = %s and d.kind != '52' and d.src != 'SD' and aoi_level = 2
    """

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        return pgsql.fetch_one(conn, sql, [node_id])[0] > 0


def filter_inner_gate(node_list):
    """
    过滤内外大门附近的内内大门
    """
    node_res = []
    with pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn:
        for node_id in node_list:
            sql = "select count(*) from nav_link where (s_nid = %s or e_nid = %s) and form != '52'"
            if pgsql.fetch_one(conn, sql, [node_id, node_id])[0] > 0:
                node_res.append(node_id)

    if len(node_res) == 0:
        node_res = node_list
    return node_res


@desc()
def filter_with_aoi_info(ctx: Context, proceed):
    """
    根据aoi的范围等信息，筛选有效的mis轨迹
    :param ctx:
    :param proceed:
    :return:
    """
    aoi_lack_gate = []
    node_intelligence = []
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for _, row in ctx.df.iterrows():
            node_id_tmp_list = []
            sql = """
                select st_astext(geom)
                from blu_face a inner join blu_face_poi b 
                on a.face_id = b.face_id
                where poi_bid = %s and aoi_level = 2 and kind != '52' and src != 'SD'
            """
            bid = str(row["bid"])
            aoi_geom = pgsql.fetch_one(conn, sql, [bid])
            if aoi_geom is None:
                aoi_lack_gate.append(0)
                node_intelligence.append(",".join(node_id_tmp_list))
                continue
            aoi_geom = aoi_geom[0]
            if shapely.wkt.loads(aoi_geom).area < 5e-7:
                aoi_lack_gate.append(0)
                node_intelligence.append(",".join(node_id_tmp_list))
                continue
            track_end = "Point" + row["track_end"].replace(",", " ")
            track_end_in_aoi = shapely.wkt.loads(aoi_geom).contains(
                shapely.wkt.loads(track_end)
            )
            if track_end_in_aoi:
                if near_access(conn, row["yaw_track_line_extend"], bid):
                    aoi_lack_gate.append(0)
                    node_intelligence.append(",".join(node_id_tmp_list))
                    continue
            else:
                if near_access(conn, track_end, bid):
                    aoi_lack_gate.append(0)
                    node_intelligence.append(",".join(node_id_tmp_list))
                    continue
            aoi_perimeter = shapely.wkt.loads(aoi_geom).length
            min_dis = min(aoi_perimeter * 0.08, 0.0004)
            if (
                    shapely.wkt.loads(track_end).distance(shapely.wkt.loads(aoi_geom))
                    >= min_dis
            ):
                aoi_lack_gate.append(0)
                node_intelligence.append(",".join(node_id_tmp_list))
                continue

            if track_end_in_aoi:
                # node_dict = find_gate_by_line_geom(row["yaw_track_line_100m"], aoi_geom)
                # if len(node_dict) == 0:
                node_dict = find_gate_by_line_geom(row["yaw_track_line_extend"], aoi_geom)
            else:
                node_dict = find_gate_by_node_geom(track_end, aoi_geom)
            if len(node_dict) == 0:
                aoi_lack_gate.append(0)
                node_intelligence.append(",".join(node_id_tmp_list))
                continue

            node_related = query_related_node(bid)
            for node_id, line_geom in node_dict.items():
                if node_id in node_related:
                    continue
                if shapely.wkt.loads(line_geom).length > min(max(0.0004, 0.08 * aoi_perimeter), 0.001):
                    continue
                if cross_high_level_link(line_geom, node_id):
                    continue
                if node_related_access(node_id):
                    continue

                node_geom = get_node_geom(node_id)
                if node_geom is None:
                    continue
                existed_access = False
                for _, access_geom in node_related.items():
                    if shapely.wkt.loads(access_geom).distance(shapely.wkt.loads(node_geom[0])) < 0.0003:
                        existed_access = True
                        break
                if existed_access:
                    continue

                node_id_tmp_list.append(node_id)
            if len(node_id_tmp_list) == 0:
                aoi_lack_gate.append(0)
                node_intelligence.append(",".join(node_id_tmp_list))
                continue
            if len(node_id_tmp_list) > 1:
                node_id_tmp_list = filter_inner_gate(node_id_tmp_list)

            aoi_lack_gate.append(1)
            node_intelligence.append(",".join(node_id_tmp_list))
    ctx.df["aoi_lack_gate"] = aoi_lack_gate
    ctx.df["node_lacked"] = node_intelligence
    ctx.df = ctx.df[ctx.df["aoi_lack_gate"] == 1]
    # ctx.df.to_csv("aoi_lack_gate_result_mis", index=False, mode='w', header=True)
    proceed()


@desc()
def save_intelligence(ctx: Context, proceed):
    """
    保存情报数据
    """
    date = time.strftime("%Y%m%d", time.localtime(time.time()))
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, pgsql.get_connection(
            pgsql.ROAD_CONFIG_WITH_INDEX
    ) as conn_road:
        for _, row in ctx.df.iterrows():
            node_intelligence = row["node_lacked"].split(",")
            bid = str(row["bid"])
            for node_id in node_intelligence:
                uid = f"{node_id}_{bid}_{date}"
                sql = "select st_astext(geom) from nav_node where node_id = %s"
                node_geom = pgsql.fetch_one(conn_road, sql, [node_id])
                if node_geom is None:
                    continue
                node_geom = node_geom[0]
                date_mis_valid = (datetime.datetime.now() + datetime.timedelta(days=-180)).strftime('%Y-%m-%d')
                sql = """
                    select count(*) from mis_intelligence 
                    where bid = %s and node_id = %s and intelligence_type = 'gate_match' 
                    and data_source = 'mis轨迹挖掘' and create_time > %s
                """
                if pgsql.fetch_one(conn, sql, [bid, node_id, date_mis_valid])[0] > 0:
                    continue

                date_valid = (datetime.datetime.now() + datetime.timedelta(days=-7)).strftime('%Y-%m-%d')
                sql = """
                    select count(*) from gate_aoi_match_check_data 
                    where bid = %s and node_id = %s and conclusion = '' and update_time > %s
                """
                if pgsql.fetch_one(conn, sql, [bid, node_id, date_valid])[0] == 0:
                    # mis情报库
                    sql = """
                        insert into mis_intelligence (bid, node_id, intelligence_type, data_source, cuid) 
                        values(%s, %s, %s, %s, %s)
                    """
                    pgsql.execute(
                        conn,
                        sql,
                        [bid, node_id, "gate_match", "mis轨迹挖掘", row["cuid_timestamp"]],
                    )
                    # 关联关系作业成果库
                    sql = """
                        insert into gate_aoi_match_check_data (bid, node_id, source, ways, uid) 
                        values(%s, %s, %s, %s, %s)
                    """
                    pgsql.execute(conn, sql, [bid, node_id, "mis轨迹挖掘", "提召", uid])

                    # 一体化关联关系作业情报库
                    sql = """
                        insert into gate_match_sync_integration(uid, main_bid, node_id, is_manual, action, node_geom, batch_id, batch_name)
                        values(%s, %s, %s, 1, 'add', st_geomfromtext(%s, 4326), %s, 'mis召回关联缺失场景')
                    """
                    pgsql.execute(
                        conn,
                        sql,
                        [uid, bid, node_id, node_geom, f"mis_recall_relation_{date}"],
                    )
                    ctx.num_intelligence += 1

    proceed()


def main():
    """

    :return:
    """
    data_dir = Path("/home/<USER>/mnt_newest/mineBidMultiDayCluster")
    main_pipe = pipeline.Pipeline(
        load_cluster_trajectory_data,
        filter_invalid_trajectory,
        filter_with_aoi_info,
        save_intelligence,
    )
    desc.attach(main_pipe)

    latest_intact_dir = list(data_dir.iterdir())
    latest_intact_dir.sort()
    latest_intact_dir_list = latest_intact_dir[-7:]

    ctx = Context()

    for latest_intact_dir in latest_intact_dir_list:
        if not Path(latest_intact_dir / "_SUCCESS").exists():
            continue
        if Path(latest_intact_dir / "_ADD_ACCESS_SUCCESS").exists():
            continue

        for file_name in tqdm(list(latest_intact_dir.glob("**/*.csv"))):
            print(file_name)
            ctx.trajectory_path = file_name
            main_pipe(ctx)

        Path(latest_intact_dir / "_ADD_ACCESS_SUCCESS").touch()
        notice_tool.send_hi(
            f"mis挖掘出入口缺失情报运行完成,量级为{ctx.num_intelligence}, 轨迹日期为{latest_intact_dir.stem}",
            token=notice_tool.MIS_NOTICE,
        )


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(e)
        traceback.print_exc()
        notice_tool.send_hi("mis挖掘出入口缺失情报运行失败" + str(e), atuserids=["zhangcong_cd"])
