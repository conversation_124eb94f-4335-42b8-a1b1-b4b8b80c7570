# !/usr/bin/env python3
"""
导航清单专题：医院在小区内部场景
ref to: https://console.cloud.baidu-int.com/devops/icafe/issue/poiinshanghai-8108/show
"""
import os
import sys
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path

from osgeo import ogr
from tqdm import tqdm

from src.tools import utils

root_path = os.path.abspath((os.path.dirname(__file__)) + "/../../")
sys.path.insert(0, root_path)

from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv

CLUSTER_AOI_LEVEL = 1
BASIC_AOI_LEVEL = 2
INNER_AOI_LEVEL = 3

desc = pipeline.get_desc()


@dataclass
class Context:
    bids: list[str] = field(default_factory=lambda: [])
    # list[(bid, face_id, geom)]
    cluster_aoi_list: list[tuple[str, str, str]] = field(default_factory=lambda: [])
    abnormal_cluster_aoi_list: list[tuple[str, str, str]] = field(default_factory=lambda: [])


def load_aoi_bids(file_path: Path, column_index=0):
    @desc('load aoi bids')
    def pipe(ctx: Context, proceed):
        ctx.bids = [x[column_index] for x in tsv.read_tsv(file_path)]
        proceed()

    return pipe


@desc()
def filter_inner_yard_by_bid(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select b.poi_bid, a.face_id, a.aoi_level, ST_AsText(a.geom)
            from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where a.src != 'SD' and b.poi_bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
        ctx.cluster_aoi_list = [
            (bid, face_id, geom) for bid, face_id, level, geom in ret
            if level == INNER_AOI_LEVEL
        ]

    proceed()


def filter_abnormal_yard(area_threshold: float):
    @desc('filter abnormal yard')
    def pipe(ctx: Context, proceed):
        with (
            pgsql.get_connection(pgsql.BACK_CONFIG) as back_conn,
            pgsql.get_connection(pgsql.POI_CONFIG) as poi_conn,
        ):
            for bid, face_id, wkt in tqdm(ctx.cluster_aoi_list):
                # 找出与该 AOI 相交的其它 AOI。
                sql = f'''
                    select face_id, ST_AsText(geom) from blu_face
                    where face_id != '{face_id}'
                    and src != 'SD'
                    and aoi_level = {BASIC_AOI_LEVEL}
                    and ST_Intersects(ST_GeomFromText('{wkt}', 4326), geom);
                '''
                ret = pgsql.fetch_all(back_conn, sql)
                if not ret:
                    continue

                related_geom_dict = {face_id: geom for face_id, geom in ret}
                # 获取相交 AOI 的 bid。
                sql = '''
                    select b.poi_bid, a.face_id
                    from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
                    where a.src != 'SD' and a.face_id in %s;
                '''
                ret = pgsql.fetch_all(back_conn, sql, [tuple(related_geom_dict.keys())])
                if not ret:
                    continue

                related_bid_dict = {bid: face_id for bid, face_id in ret}
                # 筛选 std_tag 为 小区 的相交 AOI。
                sql = '''
                    select bid from poi
                    where bid in %s
                    and std_tag = '房地产;住宅区';
                '''
                ret = pgsql.fetch_all(poi_conn, sql, [tuple(related_bid_dict.keys())])
                if not ret:
                    continue

                valid_bids = [x[0] for x in ret]
                valid_face_ids = [related_bid_dict[bid] for bid in valid_bids]
                valid_geoms = [related_geom_dict[face_id] for face_id in valid_face_ids]

                hospital_geom = ogr.CreateGeometryFromWkt(wkt)
                invalid_aoi_list = [
                    utils.calc_ioa(x, hospital_geom) for x in valid_geoms
                ]
                invalid_aoi_list = [x for x in invalid_aoi_list if x >= area_threshold]
                if invalid_aoi_list:
                    ctx.abnormal_cluster_aoi_list.append((bid, face_id, wkt))

        proceed()

    return pipe


def save_abnormal_aoi_bids(output_path: Path):
    @desc('save abnormal aoi bids')
    def pipe(ctx: Context, proceed):
        tsv.write_tsv(output_path, ctx.abnormal_cluster_aoi_list)
        proceed()

    return pipe


def main():
    work_dir = Path('/home/<USER>/dingping/process_navigation_list_20230424')
    bid_file = work_dir / 'hospital_info_20230408_res_pvtop_50pv'
    output_file = work_dir / 'hospital_inside_house.txt'
    main_pipe = pipeline.Pipeline(
        load_aoi_bids(file_path=bid_file),
        filter_inner_yard_by_bid,
        filter_abnormal_yard(area_threshold=0.6),
        save_abnormal_aoi_bids(output_path=output_file),
    )
    desc.attach(main_pipe)

    ctx = Context()
    main_pipe(ctx)


if __name__ == '__main__':
    main()
