# !/usr/bin/env python3
"""
bid 源
"""
from enum import IntEnum
from pathlib import Path
from typing import Iterable

from src.tools import linq
from src.tools import pgsql
from src.tools import tsv


class NaviSourceType(IntEnum):
    Hospital = 1  # 医院
    Mall = 2  # 购物中心
    University = 6  # 大学（高等院校）
    OtherPvGt500 = 7  # 其它 pv >= 500
    OtherPvLt500 = 8  # 其它 pv < 500
    Gallery = 11  # 展览馆&剧院
    Stadium = 12  # 体育馆
    Other = 99  # 其它


def from_file(file_path: Path, bid_index: int = 0) -> list[str]:
    """
    从 *.tsv 文件中导入 bids。
    @param file_path: *.tsv 文件的路径。
    @param bid_index: 用于指定 bids 在每行中的列索引，默认为0。
    @return: bids 列表。
    """
    bids = {x[bid_index] for x in tsv.read_tsv(file_path)}
    return filter_unique_face_id(bids)


def from_navi_q2(*source_type: NaviSourceType) -> list[str]:
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid from bid_gate_todo_lists
            where source_type in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [source_type])
        return filter_unique_face_id(x[0] for x in ret)


def from_navi_q3(*source_type: NaviSourceType) -> list[str]:
    """
    TODO: 临时的查询接口，暂时来自使用本地文件，后续可能转到数据库。
    """
    source_dir = Path('/home/<USER>/dingping/navi_q3/source')
    file_prefix = 'aoi_poi_center_scene_data_20230705_pv_precise-flag_provide-flag.q3_xonegate_area.big_area'
    source_type_map = {
        NaviSourceType.University: 'edu',
        NaviSourceType.Mall: 'mall',
        NaviSourceType.Stadium: 'tiyuguan',
        NaviSourceType.Gallery: 'zhanlanguan',
        NaviSourceType.Other: 'other.pv_210',
    }
    source_types = [source_type_map[x] for x in source_type]
    file_paths = [source_dir / f'{file_prefix}.{x}' for x in source_types]
    bids = {
        fields[0]
        for file_path in file_paths
        for fields in tsv.read_tsv(file_path)
    }
    return filter_unique_face_id(bids)


def filter_unique_face_id(bids: Iterable[str]) -> list[str]:
    """
    正常情况下，bid 与 face_id 应当是一一对应的关系，但母库目前存在一个 bid 对应多个 face_id 的情况，
    这是不符合 strategy_result 表的设计原则的，故要过滤掉这些不正常的 bid，不做处理。
    """
    sql = '''
        select b.poi_bid, a.face_id
        from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
        where b.poi_bid in %s;
    '''
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        group = linq.group_by(ret, lambda x: x[0])

    return [bid for bid, face_ids in group.items() if len(face_ids) == 1]
