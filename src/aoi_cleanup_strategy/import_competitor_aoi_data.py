# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入竞品 AOI 数据，涉及修改 POI 库：
- aoi_intelligence_history: 竞品 AOI 履历库 。
- aoi_intelligence: 用于生成作业工单的竞品 AOI 库。
"""
import argparse
import csv
import math
import sys
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from pathlib import Path
from typing import Callable
from typing import Literal
from typing import Optional

import dateutil.parser
from tqdm import tqdm

from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv

desc = pipeline.get_desc()


@dataclass
class DbFields:
    bid: str
    intel_id: str
    name: str
    address: str
    display_x: float
    display_y: float
    mesh_id: str = field(init=False)
    geom: str
    batch: str
    crawl_time: int

    def __post_init__(self):
        self.mesh_id = str(calc_mesh_id(self.display_x, self.display_y))


@dataclass
class Error:
    bid: str
    intel_id: str
    reason: str


@dataclass
class Context:
    work_dir: Path
    items: list[DbFields]
    errors: list[Error] = field(default_factory=lambda: [])


# pipes:

@desc()
def filter_existed_item(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        intel_ids = {x.intel_id for x in ctx.items}
        batches = {x.batch for x in ctx.items}
        sql = '''
            select intel_id from aoi_intelligence_history
            where batch in %s and intel_id in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(batches), tuple(intel_ids)])
        if ret:
            id_set = set(x[0] for x in ret)
            ctx.errors.extend(
                Error(bid=x.bid, intel_id=x.intel_id, reason='HISTORY_DB_ID_ALREADY_EXISTS')
                for x in ctx.items if x.intel_id in id_set
            )
            ctx.items = [x for x in ctx.items if x.intel_id not in id_set]

    if ctx.items:
        proceed()


@desc()
def filter_invalid_geometry(ctx: Context, proceed):
    # 过滤无法解析出 AOI geom 的数据。
    ctx.errors.extend(
        Error(bid=x.bid, intel_id=x.intel_id, reason='HISTORY_DB_GEOM_PARSING_ERROR')
        for x in ctx.items if not x.geom
    )
    ctx.items = [x for x in ctx.items if x.geom]

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        # 处理自相交。
        for item in ctx.items:
            item.geom = make_valid(conn, item.geom)

    proceed()


@desc()
def insert_into_aoi_intelligence_history(ctx: Context, proceed):
    if not ctx.items:
        proceed()
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        values = [
            f'''
            (
                '{x.bid}',
                '{x.intel_id}', 
                '{x.name.replace("'", "''")}', 
                '{x.address.replace("'", "''")}', 
                {x.display_x}, 
                {x.display_y}, 
                '{x.mesh_id}', 
                ST_GeomFromText('{x.geom}', 4326),
                '{x.batch}',
                {x.crawl_time}
            )
            '''
            for x in ctx.items
        ]
        values_str = ','.join(values)
        sql = f"""
            insert into aoi_intelligence_history
            (bid, intel_id, name, address, display_x, display_y, mesh_id, geom, batch, crawl_time)
            values {values_str};
        """
        pgsql.execute(conn, sql)

    proceed()


@desc()
def filter_overlay_aoi(ctx: Context, proceed):
    def filter_items(conn, items: list[DbFields]):
        for item in items:
            union_geom = query_union_overlay_aoi(conn, item.geom)
            if union_geom:
                area, overlap_area = get_area_and_overlay_area(conn, item.geom, union_geom)
                # 压盖过大，过滤
                if overlap_area / area > 0.7:
                    continue

            yield item

    with pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn:
        filtered_items = list(tqdm(filter_items(aoi_conn, ctx.items)))
        filtered_ids = set(x.intel_id for x in filtered_items)
        ctx.errors.extend(
            Error(bid=x.bid, intel_id=x.intel_id, reason='TICKET_DB_OVERLAP_IS_TOO_LARGE')
            for x in ctx.items if x.intel_id not in filtered_ids
        )
        ctx.items = filtered_items

    proceed()


@desc()
def insert_into_aoi_intelligence(ctx: Context, proceed):
    if not ctx.items:
        proceed()
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        values = [
            f'''
            (
                '{x.bid}',
                '{x.intel_id}', 
                '{x.name.replace("'", "''")}', 
                '{x.address.replace("'", "''")}', 
                {x.display_x}, 
                {x.display_y}, 
                '{x.mesh_id}', 
                ST_GeomFromText('{x.geom}', 4326),
                '{x.batch}',
                1,
                '{x.crawl_time}'
            )
            '''
            for x in ctx.items
        ]
        values_str = ','.join(values)
        sql = f"""
            insert into aoi_intelligence
            (bid, intel_id, name, address, display_x, display_y, mesh_id, geom, batch, intel_src, crawl_time)
            values {values_str};
        """
        pgsql.execute(conn, sql)

    proceed()


@desc()
def save_errors(ctx: Context, proceed):
    save_dir = ctx.work_dir / 'error_logs'
    save_dir.mkdir(exist_ok=True, parents=True)
    save_path = save_dir / f"import_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.tsv"

    tsv.write_tsv(save_path, [[x.intel_id, x.bid, x.reason] for x in ctx.errors])
    proceed()


# pgsql tools:

def make_valid(conn, wkt: str) -> str:
    """
    移除自相交
    """
    POLYGON = 3

    sql = f"""
        select ST_IsValid(
            ST_GeomFromText('{wkt}', 4326)
        ) as isvalid;
    """
    ret = pgsql.fetch_one(conn, sql)
    if ret[0]:
        return wkt

    sql = f"""
        select ST_AsText(
            ST_CollectionExtract(
                ST_MakeValid(
                    ST_GeomFromText('{wkt}', '4326')
                ),
                {POLYGON}
            )
        );
    """
    ret = pgsql.fetch_one(conn, sql)
    return make_valid(conn, ret[0])


def query_union_overlay_aoi(conn, wkt):
    sql = f"""
        select ST_AsText(st_union(geom)) 
        from blu_face where 
        st_intersects(st_buffer(ST_GeomFromText('{wkt}', 4326), 0), geom) and kind != '52' and src != 'SD';
    """
    ret = pgsql.fetch_one(conn, sql)
    overlay_aoi = ret[0]
    return make_valid(conn, overlay_aoi) if overlay_aoi else None


def get_area_and_overlay_area(conn, wkt, back_wkt):
    sql = f"""
        SELECT
            st_area(ST_GeomFromText('{wkt}', 4326)),
            st_area(
                st_intersection(
                    st_buffer(ST_GeomFromText('{back_wkt}', 4326), 0),
                    ST_GeomFromText('{wkt}', 4326)
                )
            );
    """
    area, overlay_area = pgsql.fetch_one(conn, sql)
    return area, overlay_area


# helper functions:

def parse_tsv_source_file(file_path: Path, create_item: Callable[[list], DbFields], batch_size=100_000):
    batch_data = []
    rows = tsv.read_tsv(file_path)
    for row in rows:
        item = create_item(row)
        if item:
            batch_data.append(item)

        if len(batch_data) >= batch_size:
            yield batch_data
            batch_data = []

    if batch_data:
        yield batch_data


def to_wkt_polygon(points: Optional[list[tuple[float, float]]]):
    if not points or len(points) < 4:
        return None

    if points[0] != points[-1]:
        return None

    points_str = ','.join(f'{x} {y}' for x, y in points)
    return f'POLYGON(({points_str}))'


def parse_points(point_string: str, flip_coordinates=False):
    """
    解析形如下方案例的坐标串，输出 [(x, y)...]，一般来说，gd 和 tx 文件中的坐标顺序是不一样的（每次格式都会变）。
    - gd: 22.348617,113.436835;22.348644,113.437441;22.348876,113.435847...
    - tx: 115.788544,23.758898;115.788528,23.7589;115.788518,23.758893...
    """
    # noinspection PyBroadException
    try:
        points = point_string.split(';')
        points = [x.split(',') for x in points]
        if flip_coordinates:
            points = [(b, a) for a, b in points]

        return [(float(x), float(y)) for x, y in points]
    except:
        return None


def get_batch(source: Literal['tx', 'gd']):
    today = datetime.now().strftime('%Y%m%d')
    return f'{source}-{today}'


def calc_mesh_id(x: float, y: float):
    """
    根据传入的坐标计算所在的图幅号，传入坐标为国测局经纬度信息
    @param x: 经度坐标
    @param y: 纬度坐标
    @return: 该经纬度对应的图幅号
    """
    m1m2 = math.floor(y * 1.5)
    j1 = math.floor(x)
    j2 = x - math.floor(x)
    m3m4 = math.floor(j1 - 60)
    m5 = math.floor((y - m1m2 / 1.5) * 12.0)
    m6 = math.floor(j2 * 8.0)
    if (
            m1m2 < 0 or m1m2 > 99 or m3m4 < 0 or m3m4 > 99 or
            m5 < 0 or m5 > 7 or m6 < 0 or m6 > 7
    ):
        return 0

    return int(m1m2) * 10000 + int(m3m4) * 100 + int(m5) * 10 + int(m6)


def to_timestamp(time_str):
    timestamp = dateutil.parser.parse(time_str).timestamp()
    return int(timestamp)


# parse resource functions:

def parse_ditu_aoi_txt_20221031(row: list[str]):
    batch = get_batch('gd')
    flip_coordinates = True

    bid, tid, name, _, address, location, geom, crawl_time = row
    x, y = parse_points(location, flip_coordinates)[0]
    points = parse_points(geom, flip_coordinates)
    wkt = to_wkt_polygon(points)
    return DbFields(
        bid=bid,
        intel_id=tid,
        name=name,
        address=address,
        display_x=x,
        display_y=y,
        batch=batch,
        geom=wkt,
        crawl_time=to_timestamp(crawl_time),
    )


def parse_filter_head_full(row: list[str]):
    batch = get_batch('tx')
    flip_coordinates = True

    tid, bid, _, name, _, _, address, location, geom, _, _, _, _, _, _, crawl_time = row
    x, y = parse_points(location, flip_coordinates)[0]
    points = parse_points(geom, flip_coordinates)
    wkt = to_wkt_polygon(points)
    return DbFields(
        bid=bid,
        intel_id=tid,
        name=name,
        address=address,
        display_x=x,
        display_y=y,
        batch=batch,
        geom=wkt,
        crawl_time=to_timestamp(crawl_time),
    )


def parse_sjc_bid_3kw_csv_aoi(row: list[str]):
    batch = get_batch('tx')

    bid, tid, name, _, address, display_x, display_y, geom, crawl_time = row
    x, y = float(display_x), float(display_y)
    points = parse_points(geom)
    wkt = to_wkt_polygon(points)
    return DbFields(
        bid=bid,
        intel_id=tid,
        name=name,
        address=address,
        display_x=x,
        display_y=y,
        batch=batch,
        geom=wkt,
        crawl_time=int(crawl_time),
    )


PARSER_SELECTOR = {
    'tx': parse_sjc_bid_3kw_csv_aoi,
    # 'gd': parse_ditu_aoi_txt_20221031,
}


def main(args):
    csv.field_size_limit(sys.maxsize)

    file_path = Path(args.file_path)
    work_dir = file_path.parent
    main_pipe = pipeline.Pipeline(
        filter_existed_item,
        filter_invalid_geometry,
        insert_into_aoi_intelligence_history,
        # filter_overlay_aoi,
        insert_into_aoi_intelligence,
        save_errors,
    )
    desc.attach(main_pipe)

    # 文件可能较大，故采用逐行读取；又考虑到 sql 插入效率，故采用批量插入。
    # TODO: 带解析的文件，每次格式都有可能不一样，要找古李协商一下标准格式。
    # 当前解析器有：
    # - parse_ditu_aoi_txt_20221031
    # - parse_filter_head_full
    # - parse_sjc_bid_3kw_csv_aoi
    parse_file = PARSER_SELECTOR[args.data_type]
    data = parse_tsv_source_file(file_path, parse_file)
    for i, batch_data in enumerate(data):
        print(f'start: import batch {i}, count: {len(batch_data)}')
        ctx = Context(work_dir=work_dir, items=batch_data)
        main_pipe(ctx)
        print(f'end: import batch {i}')

    print('completed!')


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--file',
        desc='file_path',
        help='the file path of competitor data',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--type',
        desc='data_type',
        help="indicates file format ('tx' or 'gd'), please check the file format first!!!",
        type=str,
        required=True,
    )
    return parser.parse_args()


if __name__ == '__main__':
    main(parse_args())
