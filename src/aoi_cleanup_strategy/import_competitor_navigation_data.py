# !/usr/bin/env python3
"""
导入竞品终点导航数据，涉及修改 POI 库：
- competitor_point: 竞品终点导航数据。
- competitor_navigation_point: 导航始末点数据。

导入文件的数据格式：
数据格式 (tsv)：
bid, gid, 名称, 城市, 分类, 地址, 显示坐标, 出入口坐标, 起点坐标, 导航终点坐标（主要用这个）, 日期1, 日期2, 来源
- 导航终点坐标格式：起点;终点|起点;终点|起点;终点
- 来源值域：ditu - 高德，shejiao - 腾讯
"""
import csv
import os
import sys
from pathlib import Path

from tqdm import tqdm

root_path = os.path.abspath((os.path.dirname(__file__)) + "/../../")
sys.path.insert(0, root_path)

from src.tools import pgsql
from src.tools import tsv


def batch_insert_into_competitor_point(file_path: Path, batch_size=100_000):
    batch = []
    for row in tqdm(tsv.read_tsv(file_path)):
        if len(row) != 13:
            print(row)
            continue

        (bid,
         gid,
         name,
         city,
         category,
         address,
         display_position,
         access_position,
         start_positions,
         end_navigate_positions,
         date1,
         date2,
         source) = row
        batch.append([bid, gid, name, city, category, address, display_position, access_position, date2, source])
        if len(batch) > batch_size:
            insert_into_competitor_point(batch)
            batch.clear()

    insert_into_competitor_point(batch)


def batch_insert_into_competitor_navigation_point(file_path: Path, batch_size=100_000):
    batch = []
    for (bid,
         gid,
         name,
         city,
         category,
         address,
         display_position,
         access_position,
         start_positions,
         end_navigate_positions,
         date1,
         date2,
         source) in tqdm(tsv.read_tsv(file_path)):
        batch.append([bid, gid, end_navigate_positions, date2])
        if len(batch) > batch_size:
            insert_into_competitor_navigation_point(batch)
            batch.clear()

    insert_into_competitor_navigation_point(batch)


def insert_into_competitor_navigation_point(rows: list):
    if not rows:
        return

    def parse_start_end(point_str: str):
        point_pairs = point_str.split('|')
        point_pairs = [point_pair.split(';') for point_pair in point_pairs]
        point_pairs = [(get_geom(x[0]), get_geom(x[1])) for x in point_pairs if len(x) == 2]
        return [(start, end) for start, end in point_pairs if start != 'NULL' and end != 'NULL']

    data = [f'''
            (
                '{bid}',
                '{gid}',
                {start},
                {end},
                {try_int(date)}
            )
            '''
            for bid, gid, start_end, date in rows
            for start, end in parse_start_end(start_end)]

    if not data:
        return

    data_str = ','.join(data)
    sql = f'''
        insert into competitor_navigation_point(
            bid, gid, start_point, end_point, collect_date
        ) values {data_str}
        on conflict (bid, gid, start_point, end_point, collect_date) do nothing;
        '''

    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor
    ):
        cursor.execute(sql)
        conn.commit()


def insert_into_competitor_point(rows: list):
    if not rows:
        return

    data = [f'''
            (
                '{bid}',
                '{gid}', 
                '{quote(name)}', 
                '{quote(city)}', 
                '{quote(category)}', 
                '{quote(address)}', 
                {get_geom(display)}, 
                {get_geom(access)}, 
                {try_int(date)}, 
                '{source}'
            )
            '''
            for bid, gid, name, city, category, address, display, access, date, source in rows]
    data_str = ','.join(data)
    sql = f'''
        insert into competitor_point(
            bid, gid, name, city, category, address, display_position, access_position, collect_date, source
        ) values {data_str}
        on conflict (gid, collect_date) do nothing;
        '''

    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor
    ):
        cursor.execute(sql)
        conn.commit()


def get_geom(wkt):
    geom = parse_point(wkt)
    return f"ST_GeomFromText('{geom}', 4326)" if geom else 'NULL'


def try_int(text: str, default=0) -> int:
    # noinspection PyBroadException
    try:
        return int(text)
    except:
        return default


def try_float(text: str, default=0.0) -> float:
    # noinspection PyBroadException
    try:
        return float(text)
    except:
        return default


def parse_point(text: str):
    point = text.split(',')
    if len(point) != 2:
        return None

    x, y = point
    if try_float(x, default=-1) < 0 or try_float(y, default=-1) < 0:
        return None

    return f'POINT({x} {y})'


def quote(text: str) -> str:
    return text.replace("'", "''")


def main():
    csv.field_size_limit(sys.maxsize)

    file_path = Path('/home/<USER>/dingping/process_competitor_leadpoint/internet_qingbao_all_20221212')
    print('insert_into_competitor_point:')
    batch_insert_into_competitor_point(file_path)
    print('insert_into_competitor_navigation_point:')
    batch_insert_into_competitor_navigation_point(file_path)
    print('completed!')


if __name__ == '__main__':
    main()
