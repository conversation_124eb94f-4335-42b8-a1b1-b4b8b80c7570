# !/usr/bin/env python3
"""
策略：不同类型间错误包含。
ref to:
- https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Fr8QITM0sh/YZCUDtktsX/GowCLzU7rA9_dj
- https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Fr8QITM0sh/YZCUDtktsX/mRi7G2SHHlnzRK
"""

import dataclasses
from dataclasses import dataclass
from dataclasses import field
from typing import Callable

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import linq
from src.tools import pgsql
from src.tools import pipeline
from src.tools import utils

STRATEGY_NAME = 'incorrect_containing_relationship'
STRATEGY_VERSION = '1.0.0'

IOA_THRESHOLD = 0.7

query_desc = pipeline.get_desc()
strategy_desc = pipeline.get_desc()


@dataclass
class Aoi:
    bid: str
    geom: str
    std_tag: str = field(default="")


@dataclass
class QueryContext:
    bids: list[str]
    aoi_list: list[tuple[Aoi, list[Aoi]]] = field(default_factory=list)


@dataclass
class StrategyContext:
    batch: str
    args: dict
    aoi_list: list[tuple[Aoi, list[Aoi]]]
    selected_strategy: tuple[str, str, Callable[[Aoi, Aoi], bool]]
    issues: list[tuple[Aoi, list[Aoi]]] = field(default_factory=list)


@query_desc()
def query_aoi_info(ctx: QueryContext, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            WITH t AS (
                SELECT
                    b.poi_bid,
                    a.geom
                FROM
                    blu_face a
                    INNER JOIN blu_face_poi b ON a.face_id = b.face_id
                WHERE
                    a.src != 'SD' AND b.poi_bid IN %s
            )
            SELECT
                t.poi_bid,
                ST_AsText(t.geom),
                b.poi_bid,
                ST_AsText(a.geom)
            FROM
                t
                INNER JOIN blu_face a ON ST_Intersects(a.geom, t.geom)
                INNER JOIN blu_face_poi b ON a.face_id = b.face_id
            WHERE
                a.src != 'SD' AND b.poi_bid != t.poi_bid;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
        aoi_group = linq.group_by(ret, lambda x: x[0])
        for _, aoi_list in aoi_group.items():
            primary_bid, primary_geom, _, _ = aoi_list[0]
            aoi = Aoi(bid=primary_bid, geom=primary_geom)
            others = [Aoi(bid=bid, geom=geom) for _, _, bid, geom in aoi_list]
            ctx.aoi_list.append((aoi, others))

    proceed()


@query_desc()
def query_std_tag(ctx: QueryContext, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        bids = [aoi.bid for aoi, _ in ctx.aoi_list]
        item_bids = [x.bid for _, items in ctx.aoi_list for x in items]
        all_bids = [*bids, *item_bids]
        if not any(all_bids):
            proceed()
            return
        sql = '''
            select bid, std_tag from poi
            where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(all_bids)])
        tag_dict = {bid: tag for bid, tag in ret}

    for aoi, items in ctx.aoi_list:
        aoi.std_tag = tag_dict.get(aoi.bid, '')
        for item in items:
            item.std_tag = tag_dict.get(item.bid, '')

    proceed()


@strategy_desc()
def find_incorrect_relationship(ctx: StrategyContext, proceed):
    primary_tag, secondary_tag, is_issue = ctx.selected_strategy
    issues = [
        (aoi, [
            item
            for item in items
            if secondary_tag in item.std_tag and is_issue(aoi, item)
        ])
        for aoi, items in ctx.aoi_list
        if primary_tag in aoi.std_tag
    ]
    ctx.issues = [(aoi, items) for aoi, items in issues if items]
    proceed()


@strategy_desc()
def save_to_db(ctx: StrategyContext, proceed):
    results = [
        StrategyResult(
            case_id=aoi.bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail',
            args=ctx.args,
            details={
                'aoi': dataclasses.asdict(aoi),
                'contains': [dataclasses.asdict(x) for x in items]
            }
        )
        for aoi, items in ctx.issues
    ]
    export_strategy_result.to_db(results)
    proceed()


def get_ioa_relation(threshold: float):
    """
    返回基于几何位置的关联关系：
    - 1：y ∈ x；
    - 0：无关联；
    - -1：x ∈ y；
    """

    def inner(x: Aoi, y: Aoi):
        if utils.calc_ioa(x.geom, y.geom) >= threshold:
            return 1
        elif utils.calc_ioa(y.geom, x.geom) >= threshold:
            return -1
        else:
            return 0

    return inner


def get_bid_relation(x: Aoi, y: Aoi):
    """
    返回基于 relation_bid 的关联关系：
    - 1：y ∈ x；
    - 0：无关联；
    - -1：x ∈ y；
    """
    bids = {x.bid, y.bid}
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid, relation_bid from poi
            where bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        relation_bids = {bid: relation_bid for bid, relation_bid in ret}

    if relation_bids.get(y.bid) == x.bid:
        return 1
    elif relation_bids.get(x.bid) == y.bid:
        return -1
    else:
        return 0


def run(bids: list[str], batch: str):
    ioa_relation = get_ioa_relation(IOA_THRESHOLD)

    def ioa_directed(x: Aoi, y: Aoi) -> bool:
        return ioa_relation(x, y) == 1

    def ioa_undirected(x: Aoi, y: Aoi) -> bool:
        return ioa_relation(x, y) != 0

    def ioa_and_relation(x: Aoi, y: Aoi):
        ioa_ret = ioa_relation(x, y)
        bid_ret = get_bid_relation(x, y)
        return ioa_ret != 0 and bid_ret == 0

    strategies = [
        ('医疗', '房地产', ioa_directed),
        ('教育培训;高等院校', '医疗', ioa_directed),
        ('教育培训;高等院校', '房地产', ioa_directed),
        ('公司企业;园区', '公司企业;园区', ioa_undirected),
        ('房地产', '房地产', ioa_and_relation),
        ('教育培训;高等院校', '教育培训;高等院校', ioa_and_relation),
        ('教育培训;中学', '教育培训;中学', ioa_and_relation),
        ('教育培训;小学', '教育培训;小学', ioa_and_relation),
    ]

    query_pipe = pipeline.Pipeline(
        query_aoi_info,
        query_std_tag,
    )
    query_desc.attach(query_pipe)

    strategy_pipe = pipeline.Pipeline(
        find_incorrect_relationship,
        pipeline.print_desc(lambda x: f'find issues: {len(x.issues)}'),
        save_to_db,
    )
    strategy_desc.attach(strategy_pipe)

    ctx = QueryContext(bids=bids)
    query_pipe(ctx)
    for strategy in strategies:
        containing_tag, contained_tag, _ = strategy
        ctx = StrategyContext(
            batch=batch,
            args={
                'ioa_threshold': IOA_THRESHOLD,
                'strategy': f'{containing_tag}-{contained_tag}',
            },
            aoi_list=ctx.aoi_list,
            selected_strategy=strategy,
        )
        strategy_pipe(ctx)
