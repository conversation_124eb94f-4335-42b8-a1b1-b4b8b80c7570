# !/usr/bin/env python3
"""
Level 为空场景：若内部区域未被任何基础院落包含，直接批处理为基础院落。
ref to: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Fr8QITM0sh/YZCUDtktsX/9YVo0Pti8oAfq2
"""
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path

from tqdm import tqdm

from src.aoi_cleanup_strategy import blu_record
from src.tools import linq
from src.tools import mesh_locker
from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv
from src.tools import utils

desc = pipeline.get_desc()


@dataclass
class Yard:
    face_id: str
    mesh_id: str
    geom: str
    area_radio: float = field(default=0.0)


@dataclass
class Context:
    work_dir: Path
    inner_yards: list[Yard] = field(default_factory=list)
    # list[(face_id, mesh_id)]
    isolated_inner_yards: list[tuple[str, str]] = field(default_factory=list)


@desc()
def query_inner_yards(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = f'''
            select face_id, mesh_id, ST_AsText(geom) from blu_face
            where aoi_level = 3 and src != 'SD';
        '''
        ctx.inner_yards = [
            Yard(face_id, mesh_id, geom)
            for face_id, mesh_id, geom in pgsql.fetch_all(conn, sql)
        ]

    proceed()


@desc()
def calc_inner_yard_area_radio(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for yard in tqdm(ctx.inner_yards):
            sql = f'''
                select ST_AsText(geom) from blu_face
                where aoi_level = 2 
                      and src != 'SD'
                      and ST_Intersects(geom, ST_GeomFromText('{yard.geom}', 4326));
            '''
            ret = pgsql.fetch_all(conn, sql)
            if ret:
                yard.area_radio = max(utils.calc_ioa(x[0], yard.geom) for x in ret)

    proceed()


@desc()
def analyze_inner_yard_area_radio(ctx: Context, proceed):
    n_group = 10
    step = 1 / n_group
    results = list(linq.count_by(ctx.inner_yards, lambda x: int(x.area_radio // step)).items())
    results.sort(key=lambda x: x[0])
    for key, count in results:
        start = key * step
        retain_len = len(str(n_group))
        if key == n_group:
            print(f'[{start:0.{retain_len}f}]: {count}')
        else:
            print(f'[{start:0.{retain_len}f}, {start + step:0.{retain_len}f}): {count}')

    proceed()


def filter_isolated_inner_yards(threshold: float):
    @desc('filter isolated inner yards')
    def pipe(ctx: Context, proceed):
        ctx.isolated_inner_yards = [
            (x.face_id, x.mesh_id)
            for x in ctx.inner_yards
            if x.area_radio < threshold
        ]
        proceed()

    return pipe


@desc()
def save_isolated_inner_yards(ctx: Context, proceed):
    save_path = ctx.work_dir / 'isolated_inner_yards.tsv'
    tsv.write_tsv(save_path, ([face_id] for face_id, _ in ctx.isolated_inner_yards))
    print(f'save to: {save_path}')
    proceed()


@desc()
def modify_aoi_level_from_inner_to_basic(ctx: Context, proceed):
    def modify_blu_face(item):
        face_ids = [face_id for face_id, mesh_id in item]
        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            sql = '''
                update blu_face set aoi_level = 2
                where face_id in %s;
            '''
            pgsql.execute(conn, sql, [tuple(face_ids)])

        return True

    def on_completed(mesh_id, item):
        face_ids = [face_id for face_id, mesh_id in item]
        blu_record.snapshot(face_ids, 'isolated_inner_yard_strategy')
        print(f'success mesh: {mesh_id}')

    data = linq.group_by(ctx.isolated_inner_yards, key=lambda x: x[1])
    print(f'start archiving, mesh: {len(data)}, total: {len(ctx.isolated_inner_yards)}')
    mesh_locker.consume_mesh_task_queue(
        tasks=data.items(),
        lock_prefix='isolated_inner_yard',
        action=modify_blu_face,
        on_completed=on_completed,
    )
    proceed()


def main():
    work_dir = Path('/home/<USER>/dingping/process_isolated_inner_yard')

    main_pipe = pipeline.Pipeline(
        query_inner_yards,
        calc_inner_yard_area_radio,
        analyze_inner_yard_area_radio,
        filter_isolated_inner_yards(threshold=0.1),
        pipeline.print_desc(lambda x: f'isolated_inner_yards: {len(x.isolated_inner_yards)}'),
        save_isolated_inner_yards,
        # modify_aoi_level_from_inner_to_basic,  # 此操作会修改母库！
    )
    desc.attach(main_pipe)
    ctx = Context(work_dir)
    main_pipe(ctx)


if __name__ == '__main__':
    main()
