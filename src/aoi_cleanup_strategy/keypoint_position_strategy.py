# !/usr/bin/env python3
"""
策略：关键点法，包含两个：
- 竞品引导点距离AOI过远：
  - V1：多家竞品，距离边框大于20m
  - V2：仅使用GD引导点，距离边框大于50m
  - V3：V2基础上，排除竞品引导点落在BD关联道路link上的情况
- POI大门距离AOI过远：POI子点大门，距离边框大于20m。（有效率低，暂停迭代）
"""
import dataclasses
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path

from tqdm import tqdm

from src.tools import linq
from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv
from src.tools import utils

desc = pipeline.get_desc()


@dataclass
class PointInfo:
    point_id: str
    geom: str
    distance: float


@dataclass
class DistanceResult:
    bid: str
    geom: str
    distances: list[PointInfo]


@dataclass
class Context:
    work_dir: Path
    # {bid: wkt}
    blu_faces: dict[str, str] = field(default_factory=dict)
    distance_results: dict[str, list[DistanceResult]] = field(default_factory=dict)
    issues: dict[str, list[DistanceResult]] = field(default_factory=dict)


def import_blu_face(file_path: Path):
    @desc('import blu face')
    def pipe(ctx: Context, proceed):
        save_path = ctx.work_dir / 'blu_face.tsv'
        if save_path.exists():
            ctx.blu_faces = {x: y for x, y in tsv.read_tsv(save_path)}
            proceed()
            return

        bids = [x[0] for x in tsv.read_tsv(file_path)]
        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            sql = '''
                select b.poi_bid, ST_AsText(a.geom)
                from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
                where a.src != 'SD' and b.poi_bid in %s;
            '''
            ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
            ctx.blu_faces = {bid: wkt for bid, wkt in ret}

        tsv.write_tsv(save_path, [[k, v] for k, v in ctx.blu_faces.items()])
        proceed()

    return pipe


@desc()
def query_poi_gate_point(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        bids = ctx.blu_faces.keys()
        sql = '''
            select relation_bid, bid, ST_AsText(geometry) from poi
            where relation_bid in %s and std_tag like '出入口%';
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        ret = linq.group_by(ret, lambda x: x[0])
        points = {bid: [(point_id, wkt) for _, point_id, wkt in items] for bid, items in ret.items()}

    distances = list(calc_distances(ctx.blu_faces, points))
    ctx.distance_results['poi_gate_point'] = distances
    proceed()


@desc()
def query_competitor_lead_point(ctx: Context, proceed):
    with (pgsql.get_connection(pgsql.POI_CONFIG)) as conn:
        bids = ctx.blu_faces.keys()
        sql = '''
            select
                b.bid, b.gid, ST_AsText(b.end_point)
            from
                competitor_point a inner join competitor_navigation_point b on a.gid = b.gid
            where
                b.bid in %s and a.source = 'ditu';
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        ret = linq.group_by(ret, lambda x: x[0])
        points = {bid: [(point_id, wkt) for _, point_id, wkt in items] for bid, items in ret.items()}

    distances = list(calc_distances(ctx.blu_faces, points))
    ctx.distance_results['competitor_lead_point'] = distances
    proceed()


def filter_issue_by_distance(distance: float):
    @desc('filter issue by distance')
    def pipe(ctx: Context, proceed):
        for key, results in ctx.distance_results.items():
            ctx.issues[key] = [x for x in results if any(d.distance >= distance for d in x.distances)]

        proceed()

    return pipe


@desc()
def save_results(ctx: Context, proceed):
    for key, results in ctx.distance_results.items():
        items = [dataclasses.asdict(x) for x in results]
        utils.write_json(ctx.work_dir / f'full_{key}.json', items)

    for key, results in ctx.issues.items():
        items = [(x, x.distances[0]) for x in results]
        items = [
            (x.bid, max_distance.point_id, max_distance.distance, max_distance.geom, x.geom)
            for x, max_distance in items
        ]
        tsv.write_tsv(ctx.work_dir / f'issue_{key}.tsv', items)

    proceed()


# helper functions:

def calc_distances(blu_faces: dict[str, str], key_points: dict[str, list[tuple[str, str]]]):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for bid, aoi_wkt in tqdm(blu_faces.items()):
            point_wkt_list = key_points.get(bid)
            if not point_wkt_list:
                continue

            sql = '''
                select
                    ST_Distance(ST_ExteriorRing(%(aoi_wkt)s), %(poi_wkt)s),
                    ST_Contains(ST_GeomFromText(%(aoi_wkt)s, 4326), ST_GeomFromText('{poi_wkt}', 4326));
            '''
            distances = [
                (point_id, poi_wkt, *pgsql.fetch_one(conn, sql, {'aoi_wkt': aoi_wkt, 'poi_wkt': poi_wkt}))
                for point_id, poi_wkt in point_wkt_list
            ]
            distances = [
                PointInfo(
                    point_id=point_id,
                    geom=poi_wkt,
                    distance=(-distance if is_contains else distance) * 1e5
                )
                for point_id, poi_wkt, distance, is_contains in distances
            ]
            distances.sort(key=lambda x: x.distance, reverse=True)
            yield DistanceResult(
                bid=bid,
                geom=aoi_wkt,
                distances=distances,
            )


def main():
    bid_file = Path(
        '/home/<USER>/dingping/process_keypoint_position_20230518/precise_aoi_qingbao_pv_100_sorted.pv_210_bids'
    )
    main_pipe = pipeline.Pipeline(
        import_blu_face(bid_file),
        query_poi_gate_point,
        query_competitor_lead_point,
        filter_issue_by_distance(distance=20),
        save_results,
    )
    desc.attach(main_pipe)
    ctx = Context(work_dir=bid_file.parent)
    main_pipe(ctx)


if __name__ == '__main__':
    main()
