# !/usr/bin/env python3
"""
包含图幅相关的一些帮助方法。
"""
import json
import math

OFFSET_Y = [0, 833300, 1666700]
MESH_TO_CITY_MAP = dict()


def get_bounds(mesh_id: int):
    sy = mesh_id // 10000
    sx = (mesh_id % 10000) // 100
    qy = (mesh_id % 100) // 10
    qx = mesh_id % 10

    x = sx * 8 + qx
    y = sy * 8 + qy

    left = 600000000 + x * 1250000
    right = 600000000 + (x + 1) * 1250000
    bottom = y // 3 * 2500000 + OFFSET_Y[y % 3]
    top = (y + 1) // 3 * 2500000 + OFFSET_Y[(y + 1) % 3]

    return left, right, bottom, top


# noinspection SpellCheckingInspection
def get_mesh_id(x: float, y: float):
    """
        根据传入的坐标计算所在的图幅号，传入坐标为国测局经纬度信息
    Args:
        x: 经度坐标
        y: 纬度坐标
    Return:
        ret: 该经纬度对应的图幅号
    """
    intm1m2 = math.floor(y * 1.5)
    intj1 = math.floor(x)
    dblj2 = x - math.floor(x)
    intm3m4 = math.floor(intj1 - 60)
    intm5 = math.floor((y - intm1m2 / 1.5) * 12.0)
    intm6 = math.floor(dblj2 * 8.0)
    if (intm1m2 < 0 or intm1m2 > 99 or intm3m4 < 0 or intm3m4 > 99 or
            intm5 < 0 or intm5 > 7 or intm6 < 0 or intm6 > 7):
        return 0
    ret = int(intm1m2) * 10000 + int(intm3m4) * 100 + int(intm5) * 10 + int(intm6)
    return ret


def get_wkt(mesh_id: int):
    left, right, bottom, top = get_bounds(mesh_id)
    left *= 1e-7
    right *= 1e-7
    bottom *= 1e-7
    top *= 1e-7
    return f'POLYGON (({left} {top}, {right} {top}, {right} {bottom}, {left} {bottom}, {left} {top}))'


def get_city(mesh_id: str):
    global MESH_TO_CITY_MAP
    if not any(MESH_TO_CITY_MAP):
        with open('../../resources/mesh2city.json', 'r', encoding='utf-8') as f:
            MESH_TO_CITY_MAP = json.load(f)

    return MESH_TO_CITY_MAP.get(mesh_id, None)
