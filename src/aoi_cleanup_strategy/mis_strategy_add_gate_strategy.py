"""
Author: zhangcong_cd <EMAIL>
Date: 2023-09-18 09:57:02
LastEditors: zhangcong_cd <EMAIL>
LastEditTime: 2023-10-16 13:54:06
FilePath: \aoi-ml\src\aoi_cleanup_strategy\cluster_mis_trajectory.py
"""
import traceback
import uuid
import warnings
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path
from typing import Dict, List

import pandas as pd
import shapely.wkt
from pandas.errors import SettingWithCopyWarning
from tqdm import tqdm

from src.tools import aoi_tools, notice_tool, pgsql
from src.tools import pipeline

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
warnings.filterwarnings(action="ignore", message="Mean of empty slice")

desc = pipeline.get_desc()


@dataclass
class PoiInfo:
    """
    poi相关信息
    """

    bid: str = ""
    city: str = ""
    name: str = ""
    geom: str = ""
    std_tag: str = ""


@dataclass
class Context:
    """
    上下文信息结构体
    """

    trajectory_path: Path = field(init=False)
    df: pd.DataFrame = field(init=False)
    num_intelligence = 0


def load_cluster_trajectory_data(ctx: Context, proceed):
    """
    加载聚合后的mis轨迹数据
    """
    try:
        ctx.df = pd.read_csv(ctx.trajectory_path)
    except pd.errors.EmptyDataError:
        ctx.df = pd.DataFrame()

    proceed()


@desc()
def filter_invalid_trajectory(ctx: Context, proceed):
    """
    根据规划终点和实际终点的距离筛选轨迹数据
    """

    valid_trajectory = []
    for _, row in ctx.df.iterrows():
        route_end = "Point" + row["route_end"].replace(",", " ")
        track_end = "Point" + row["track_end"].replace(",", " ")
        dis = shapely.wkt.loads(route_end).distance(shapely.wkt.loads(track_end))
        if dis > 0.01 or dis < 0.001:
            valid_trajectory.append(0)
            continue
        valid_trajectory.append(1)

    if not ctx.df.empty:
        ctx.df["valid_trajectory"] = valid_trajectory
        ctx.df = ctx.df[ctx.df["valid_trajectory"] == 1]

    proceed()


def find_gate_by_geom(geom, aoi_geom):
    """
    :param geom: 点坐标
    :param aoi_geom: aoi坐标，用于计算搜索的范围
    :return:
    """
    node_dict = dict()
    with pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn:
        sql = f"""
            with tmp as (select st_geomfromtext(%s, 4326) as geom) 
            select distinct b.node_id, st_astext(st_shortestline(b.geom, st_geomfromtext(%s, 4326)))
            from nav_gate a inner join nav_node b on a.node_id = b.node_id, tmp 
            where st_dwithin(b.geom, tmp.geom, 0.0003)
        """
        for (
                node_id,
                geom_tmp,
        ) in pgsql.fetch_all(conn, sql, [geom, aoi_geom]):
            node_dict[node_id] = geom_tmp
    return node_dict


def near_access(conn, geom, bid):
    """
    判断坐标是否位于bid对应框的出入口附近
    :param conn:
    :param geom:
    :param bid:
    :return:
    """
    sql = "select st_astext(geom) from blu_access where main_bid = %s"
    geom_access_list = pgsql.fetch_all(conn, sql, [bid])
    for (geom_access,) in geom_access_list:
        if shapely.wkt.loads(geom).distance(shapely.wkt.loads(geom_access)) <= 0.0005:
            return True
    return False


@desc()
def filter_with_aoi_info(ctx: Context, proceed):
    """
    根据aoi的范围等信息，筛选有效的mis轨迹
    :param ctx:
    :param proceed:
    :return:
    """
    node_intelligence = []
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for _, row in ctx.df.iterrows():
            node_id_tmp_list = []
            sql = """
                select st_astext(geom)
                from blu_face a inner join blu_face_poi b 
                on a.face_id = b.face_id
                where poi_bid = %s and aoi_level = 2 and kind != '52' and src != 'SD'
            """
            bid = str(row["bid"])
            aoi_geom = pgsql.fetch_one(conn, sql, [bid])
            if aoi_geom is None:
                node_intelligence.append(",".join(node_id_tmp_list))
                continue
            aoi_geom = aoi_geom[0]
            track_end = "Point" + row["track_end"].replace(",", " ")
            track_end_in_aoi = shapely.wkt.loads(aoi_geom).contains(
                shapely.wkt.loads(track_end)
            )
            if not track_end_in_aoi:
                node_intelligence.append(",".join(node_id_tmp_list))
                continue

            if near_access(conn, row["yaw_track_line_extend"], bid):
                node_intelligence.append(",".join(node_id_tmp_list))
                continue

            # node_dict = find_gate_by_geom(row["yaw_track_line_100m"], aoi_geom)
            # if len(node_dict) > 0:
            #     node_intelligence.append(",".join(node_id_tmp_list))
            #     continue

            # line_100_end_point = shapely.wkt.loads(row["yaw_track_line_100m"]).boundary.geoms[-1]
            # if line_100_end_point.distance(shapely.wkt.loads(row["yaw_track_line_extend"]).boundary.geoms[-1])
            # > 0.0003:
            #     node_intelligence.append(",".join(node_id_tmp_list))
            #     continue

            geom_gate = shapely.wkt.loads(aoi_geom).boundary.intersection(shapely.wkt.loads(row["yaw_track_line_100m"]))
            if geom_gate.is_empty:
                node_intelligence.append(",".join(node_id_tmp_list))
                continue
            if geom_gate.type == "MultiPoint":
                node_id_tmp_list = [x.wkt for x in geom_gate.geoms]
            else:
                node_id_tmp_list.append(geom_gate.wkt)

            if len(node_id_tmp_list) > 2:
                node_id_tmp_list = []
                node_intelligence.append(",".join(node_id_tmp_list))
                continue

            node_intelligence.append(",".join(node_id_tmp_list))

    ctx.df["gate_lacked"] = node_intelligence
    ctx.df = ctx.df[ctx.df["gate_lacked"] != ""]
    # ctx.df.to_csv("gate_lack_mis_result", index=False, mode='a', header=False)
    proceed()


def query_poi_info(bid: str):
    """
    根据bid查询poi相关信息
    """
    sql = "select name, std_tag from poi where bid = %s"
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        return pgsql.fetch_one(conn, sql, [bid])


@desc()
def save_intelligence(ctx: Context, proceed):
    """
    保存情报数据
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, pgsql.get_connection(
            pgsql.TRAJECTORY_CONFIG
    ) as conn_gate:
        for _, row in tqdm(ctx.df.iterrows(), total=len(ctx.df), leave=False):
            node_intelligence = row["gate_lacked"].split(",")
            bid = str(row["bid"])
            cuid_str = row["cuid"] + "," + row["lbs_navigation_start_timestring"]
            properties = ''
            res_poi = query_poi_info(bid)
            if res_poi:
                properties = f"{res_poi[0]}|{res_poi[1]}".replace("'", '')
            for node_geom in node_intelligence:
                qb_id = uuid.uuid4().hex
                try:
                    mesh_id = str(aoi_tools.cal_mesh_id(shapely.wkt.loads(node_geom).x, shapely.wkt.loads(node_geom).y))
                except Exception as e:
                    print('======解析geom错误', e.args)
                    continue
                sql = """
                    select count(*) from mis_intelligence 
                    where st_dwithin(mark_geom, st_geomfromtext(%s, 4326), 0.00025) 
                    and intelligence_type = 'gate_lack' 
                """
                if pgsql.fetch_one(conn, sql, [node_geom])[0] == 0:
                    # mis情报库
                    sql = """
                        insert into mis_intelligence (bid, mark_geom, intelligence_type, data_source, cuid, uid) 
                        values(%s, st_geomfromtext(%s, 4326), %s, %s, %s, %s)
                    """

                    pgsql.execute(
                        conn,
                        sql,
                        [bid, node_geom, "gate_lack", "mis轨迹挖掘", cuid_str, qb_id],
                    )
                    # 一体化关联关系作业情报库
                    sql = """
                        insert into qb_gate_prepush_preprocess(qb_id, item_id, intel_type, mark_geom, lead_geom, 
                        mesh_id, cityadcode, mark_status, param_1, param_2, param_3, strategy, qb_src, push_platform, properties)
                        values(%s, %s, 0, %s, %s, %s, '', 1, '2', '0|0||0|0|0|4', '3|0|-1|0', 'MIS_MINING_NEW', 2, 1, %s)
                    """
                    pgsql.execute(
                        conn_gate,
                        sql,
                        [qb_id, bid, node_geom, node_geom, mesh_id, properties],
                    )

                    ctx.num_intelligence += 1

    proceed()


def main():
    """

    :return:
    """
    data_dir = Path("/home/<USER>/mnt_newest/mineBid")
    main_pipe = pipeline.Pipeline(
        load_cluster_trajectory_data,
        filter_invalid_trajectory,
        filter_with_aoi_info,
        save_intelligence,
    )
    desc.attach(main_pipe)

    latest_intact_dir = list(data_dir.iterdir())
    latest_intact_dir.sort()
    latest_intact_dir_list = latest_intact_dir[-7:]
    ctx = Context()

    for latest_intact_dir in latest_intact_dir_list:
        if not Path(latest_intact_dir / "_SUCCESS").exists():
            continue
        if Path(latest_intact_dir / "_ADD_GATE_SUCCESS").exists():
            continue
        success_ct = 0
        err_ct = 0
        list_len = len(list(latest_intact_dir.glob("**/*.csv")))
        for file_name in tqdm(list(latest_intact_dir.glob("**/*.csv"))):
            try:
                print(file_name)
                ctx.trajectory_path = file_name
                main_pipe(ctx)
                success_ct += 1
            except Exception as e:
                err_ct += 1
                print(e.args)
                print(traceback.format_exc())
        if list_len - success_ct < 5:
            Path(latest_intact_dir / "_ADD_GATE_SUCCESS").touch()
        notice_tool.send_hi(
            f"mis挖掘大门新增情报运行完成,量级为{ctx.num_intelligence}, 轨迹日期为{latest_intact_dir.stem}, "
            f"success: {success_ct}, error: {err_ct}, ",
            token=notice_tool.MIS_NOTICE,
        )


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        notice_tool.send_hi("mis挖掘大门新增情报运行失败" + str(e), atuserids=["chenbaojun_cd"])
        raise e
