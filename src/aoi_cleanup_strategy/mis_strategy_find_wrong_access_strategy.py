"""
Author: zhangcong_cd <EMAIL>
Date: 2023-09-18 09:57:02
LastEditors: zhangcong_cd <EMAIL>
LastEditTime: 2023-10-16 13:54:06
"""
import datetime
import time
import traceback
import warnings
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path
from typing import Dict, List

import pandas as pd
import shapely.wkt
from pandas.errors import SettingWithCopyWarning
from tqdm import tqdm

from src.tools import notice_tool, pgsql
from src.tools import pipeline

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
warnings.filterwarnings(action="ignore", message="Mean of empty slice")

desc = pipeline.get_desc()

aoi_tag_set = {
    "#交通设施;火车站",
    "#交通设施;飞机场",
    "#交通设施;长途汽车站",
    "购物;购物中心",
    "医疗;综合医院",
    "医疗;专科医院",
    "教育培训;高等院校",
    "文化传媒;展览馆",
    "休闲娱乐;剧院",
    "运动健身;体育场馆",
    "旅游景点;风景区",
    "旅游景点;公园",
    "旅游景点;文物古迹",
    "旅游景点",
    "旅游景点;其他",
    "旅游景点;博物馆",
    "旅游景点;游乐园",
    "旅游景点;寺庙",
    "旅游景点;景点",
    "旅游景点;海滨浴场",
    "旅游景点;动物园",
    "旅游景点;水族馆",
    "旅游景点;教堂",
    "旅游景点;植物园",
    "房地产;住宅区",
    "#房地产;写字楼",
    "#房地产;其他",
    "#房地产",
    "公司企业;公司",
    "公司企业;园区",
    "公司企业",
    "公司企业;厂矿",
    "公司企业;农林园艺",
    "酒店;星级酒店",
    "教育培训;中学",
    "教育培训;小学",
    "教育培训;幼儿园",
    "#教育培训;培训机构",
    "#教育培训;其他",
    "教育培训;科研机构",
    "教育培训;科技馆",
    "教育培训;成人教育",
    "教育培训;特殊教育学校",
    "#教育培训",
    "#政府机构;各级政府",
    "政府机构;行政单位",
    "政府机构;公检法机构",
    "政府机构;政治教育机构",
    "政府机构;中央机构",
    "政府机构;涉外机构",
    "购物;市场",
    "购物;家居建材",
    "#购物",
    "购物;百货商场",
    "休闲娱乐;休闲广场",
    "休闲娱乐;度假村",
    "休闲娱乐;剧院",
    "汽车服务;汽车销售",
    "#汽车服务;汽车配件",
    "#汽车服务;汽车维修",
    "汽车服务;汽车检测场",
    "文化传媒;广播电视",
    "文化传媒;文化宫",
    "文化传媒;美术馆",
    "绿地;高尔夫球场",
    "医疗;疗养院",
    "#医疗;其他",
    "医疗",
    "#生活服务;物流公司",
}


@dataclass
class PoiInfo:
    """
    poi相关信息
    """

    bid: str = ""
    city: str = ""
    name: str = ""
    geom: str = ""
    std_tag: str = ""


@dataclass
class Context:
    """
    上下文信息结构体
    """

    trajectory_path: Path = field(init=False)
    df: pd.DataFrame = field(init=False)
    poi_dict: Dict[str, List[PoiInfo]] = field(default_factory=dict)
    num_intelligence = 0


def load_cluster_trajectory_data(ctx: Context, proceed):
    """
    :param ctx:
    :param proceed:
    :return:
    """
    try:
        ctx.df = pd.read_csv(ctx.trajectory_path)
    except pd.errors.EmptyDataError:
        ctx.df = pd.DataFrame()

    proceed()


@desc()
def filter_by_dis(ctx: Context, proceed):
    """

    :param ctx:
    :param proceed:
    :return:
    """
    aoi_relate_wrong_gate = []
    for _, row in ctx.df.iterrows():
        route_end = "Point" + row["route_end"].replace(",", " ")
        track_end = "Point" + row["track_end"].replace(",", " ")
        dis = shapely.wkt.loads(route_end).distance(shapely.wkt.loads(track_end))
        if dis > 0.01 or dis < 0.001:
            aoi_relate_wrong_gate.append(0)
            continue
        aoi_relate_wrong_gate.append(1)

    if not ctx.df.empty:
        ctx.df["aoi_relate_wrong_gate"] = aoi_relate_wrong_gate
        ctx.df = ctx.df[ctx.df["aoi_relate_wrong_gate"] == 1]

    proceed()


def find_gate_by_geom(geom, aoi_geom):
    """

    :param geom: 点坐标
    :param aoi_geom: aoi坐标，用于计算搜索的范围
    :return:
    """
    node_dict = dict()
    with pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn:
        sql = f"""
            with tmp as (select st_geomfromtext(%s, 4326) as geom) 
            select distinct b.node_id, st_astext(st_shortestline(b.geom, st_geomfromtext(%s, 4326)))
            from nav_gate a inner join nav_node b on a.node_id = b.node_id, tmp 
            where st_dwithin(b.geom, tmp.geom, 0.0003)
        """
        for (
            node_id,
            geom_tmp,
        ) in pgsql.fetch_all(conn, sql, [geom, aoi_geom]):
            node_dict[node_id] = geom_tmp
    return node_dict


def near_access(conn, geom, bid):
    """

    :param conn:
    :param geom:
    :param bid:
    :return:
    """
    sql = "select st_astext(geom) from blu_access where main_bid = %s"
    geom_access_list = pgsql.fetch_all(conn, sql, [bid])
    for (geom_access,) in geom_access_list:
        if shapely.wkt.loads(geom).distance(shapely.wkt.loads(geom_access)) <= 0.0005:
            return True
    return False


def cross_high_level_link(line_geom):
    """

    :param line_geom:
    :return:
    """
    with pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn:
        sql = "select count(*) from nav_link where st_intersects(geom, st_geomfromtext(%s, 4326)) and kind <= 7"
        return pgsql.fetch_one(conn, sql, [line_geom])[0] > 0


def query_related_node(bid):
    """

    :param bid:
    :return:
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        node_set = set()
        sql = """
            select distinct b.node_id 
            from blu_access a inner join blu_access_gate_rel b 
            on a.access_id = b.access_id 
            where main_bid = %s
        """
        for (node_id_tmp,) in pgsql.fetch_all(conn, sql, [bid]):
            node_set.add(node_id_tmp)
        return node_set


@desc()
def filter_with_aoi_info(ctx: Context, proceed):
    """

    :param ctx:
    :param proceed:
    :return:
    """
    aoi_relate_wrong_gate = []

    node_intelligence = []
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for _, row in ctx.df.iterrows():
            node_id_tmp_list = []
            sql = """
                select st_astext(geom)
                from blu_face a inner join blu_face_poi b 
                on a.face_id = b.face_id
                where poi_bid = %s and aoi_level = 2 and kind != '52' and src != 'SD'
            """
            bid = str(row["bid"])
            aoi_geom = pgsql.fetch_one(conn, sql, [bid])
            if aoi_geom is None:
                aoi_relate_wrong_gate.append(0)
                node_intelligence.append(",".join(node_id_tmp_list))
                continue
            aoi_geom = aoi_geom[0]
            # track_end = "point(%f %f)" % (row["track_end"][0], row["track_end"][1])
            track_end = "Point" + row["track_end"].replace(",", " ")
            track_end_in_aoi = shapely.wkt.loads(aoi_geom).contains(
                shapely.wkt.loads(track_end)
            )
            if not track_end_in_aoi:
                if not near_access(conn, track_end, bid):
                    aoi_relate_wrong_gate.append(0)
                    node_intelligence.append(",".join(node_id_tmp_list))
                    continue
            # aoi_perimeter = shapely.wkt.loads(aoi_geom).length
            # # min_dis = min(aoi_perimeter * 0.05, 0.0004)
            min_dis = 0.0004
            if (
                shapely.wkt.loads(track_end).distance(shapely.wkt.loads(aoi_geom))
                >= min_dis
            ):
                aoi_relate_wrong_gate.append(0)
                node_intelligence.append(",".join(node_id_tmp_list))
                continue

            aoi_relate_wrong_gate.append(1)
            node_intelligence.append(",".join(node_id_tmp_list))
    ctx.df["aoi_relate_wrong_gate"] = aoi_relate_wrong_gate
    ctx.df["node_lacked"] = node_intelligence
    ctx.df = ctx.df[ctx.df["aoi_relate_wrong_gate"] == 1]
    # ctx.df.to_csv("aoi_relate_wrong_gate_result_mis", index=False, mode='w', header=True)
    proceed()


@desc()
def save_intelligence(ctx: Context, proceed):
    """

    :param ctx:
    :param proceed:
    :return:
    """
    date = time.strftime("%Y%m%d", time.localtime(time.time()))
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, pgsql.get_connection(
        pgsql.ROAD_CONFIG_WITH_INDEX
    ) as conn_road:
        for _, row in ctx.df.iterrows():
            node_intelligence = row["wrong_node"].split(",")
            bid = str(row["bid"])
            for node_id in node_intelligence:
                uid = f"{node_id}_{bid}_{date}"
                sql = "select st_astext(geom) from nav_node where node_id = %s"
                node_geom = pgsql.fetch_one(conn_road, sql, [node_id])
                if node_geom is None:
                    continue
                node_geom = node_geom[0]
                date_mis_valid = (datetime.datetime.now() + datetime.timedelta(days=-180)).strftime('%Y-%m-%d')
                sql = """
                    select count(*) from mis_intelligence 
                    where bid = %s and node_id = %s and intelligence_type = 'gate_match' 
                    and data_source = 'mis轨迹挖掘关联错误' and create_time > %s
                """
                if pgsql.fetch_one(conn, sql, [bid, node_id, date_mis_valid])[0] > 0:
                    continue

                date_valid = (datetime.datetime.now() + datetime.timedelta(days=-7)).strftime('%Y-%m-%d')
                sql = """
                    select count(*) from gate_aoi_match_check_data 
                    where bid = %s and node_id = %s and conclusion = '' and update_time > %s and ways = '清查提准'
                """
                if pgsql.fetch_one(conn, sql, [bid, node_id, date_valid])[0] == 0:
                    # mis情报库
                    sql = """
                        insert into mis_intelligence (bid, node_id, intelligence_type, data_source, cuid) 
                        values(%s, %s, %s, %s, %s)
                    """
                    pgsql.execute(
                        conn,
                        sql,
                        [
                            bid,
                            node_id,
                            "gate_match",
                            "mis轨迹挖掘关联错误",
                            row["cuid_timestamp"],
                        ],
                    )
                    # 关联关系作业成果库
                    sql = """
                        insert into gate_aoi_match_check_data (bid, node_id, source, ways, uid) 
                        values(%s, %s, %s, %s, %s)
                    """
                    pgsql.execute(conn, sql, [bid, node_id, "mis轨迹挖掘", "清查提准", uid])

                    # 一体化关联关系作业情报库
                    sql = """
                        insert into gate_match_sync_integration(uid, main_bid, node_id, is_manual, action, node_geom, batch_id, batch_name)
                        values(%s, %s, %s, 1, 'delete', st_geomfromtext(%s, 4326), %s, 'mis召回关联错误场景')
                    """
                    pgsql.execute(conn, sql, [uid, bid, node_id, node_geom, f"mis_recall_relation_{date}"])

                    # 情报量统计
                    ctx.num_intelligence += 1

    proceed()


@desc()
def get_wrong_access_info(ctx: Context, proceed):
    """

    :param ctx:
    :param proceed:
    :return:
    """
    node_wrong_list = []
    other_tag_list = []
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as conn,
        pgsql.get_connection(pgsql.TRAJECTORY_POINT_CONFIG) as conn_trajectory,
        pgsql.get_connection(pgsql.ROAD_CONFIG_WITH_INDEX) as conn_road,
        pgsql.get_connection(pgsql.POI_CONFIG) as conn_poi,
    ):
        for _, row in ctx.df.iterrows():
            other_tag = ""
            route_end = "Point" + row["route_end"].replace(",", " ")
            bid = str(row["bid"])
            node_wrong_tmp_list = []
            sql = """
                select st_astext(geom)
                from blu_face a inner join blu_face_poi b 
                on a.face_id = b.face_id
                where poi_bid = %s and aoi_level = 2 and kind != '52' and src != 'SD'
            """
            aoi_geom = pgsql.fetch_one(conn, sql, [bid])
            if aoi_geom is None:
                node_wrong_list.append(",".join(node_wrong_tmp_list))
                continue
            aoi_geom = aoi_geom[0]
            aoi_region = shapely.wkt.loads(aoi_geom)
            extra_distance = 0.0
            if aoi_region.contains(shapely.wkt.loads(route_end)):
                extra_distance = shapely.wkt.loads(route_end).distance(aoi_region.boundary)

            sql = """
                select cluster_num, st_astext(geom) from inter_gate_point_cluster 
                where  bid = %s 
            """
            cluster_num_all = 0
            cluster_num_current_position = 0
            for (
                cluster_num,
                geom_cluster,
            ) in pgsql.fetch_all(conn_trajectory, sql, [bid]):
                cluster_num_all = cluster_num_all + int(cluster_num)

                if (
                    shapely.wkt.loads(route_end).distance(
                        shapely.wkt.loads(geom_cluster)
                    )
                    <= min(0.001, 0.0005 + extra_distance)
                ):
                    cluster_num_current_position = cluster_num_current_position + int(
                        cluster_num
                    )
            if (
                cluster_num_all == 0
                or cluster_num_current_position / cluster_num_all > 0.05
                or (
                    cluster_num_current_position > 10
                    and cluster_num_current_position / cluster_num_all > 0.02
                )
            ):
                node_wrong_list.append(",".join(node_wrong_tmp_list))
                continue

            # sql = "select count(*) from ly_guide_point_split where bid = %s and st_dwithin(end_geom, st_geomfromtext(%s, 4326), 0.0005)"
            # if pgsql.fetch_one(conn_guidance, sql, [bid, route_end])[0] > 0:
            #     node_wrong_list.append(",".join(node_wrong_tmp_list))
            #     continue
            # with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn_poi:
            exists_aoi_tag = False
            sql = """
                select std_tag, st_astext(st_shortestline(geometry, st_geomfromtext(%s, 4326))) from poi 
                where st_dwithin(st_geomfromtext(%s, 4326), geometry, 0.0005) and bid != %s and (relation_bid = ''  or relation_bid = '0')
            """
            for (
                std_tag,
                line_geom,
            ) in pgsql.fetch_all(conn_poi, sql, [route_end, route_end, bid]):
                if std_tag in aoi_tag_set and not cross_high_level_link(line_geom):
                    exists_aoi_tag = True
                    other_tag = std_tag
                    break
            if not exists_aoi_tag:
                node_wrong_list.append(",".join(node_wrong_tmp_list))
                continue

            sql = "select count(*) from poi where bid = %s and not std_tag ~ '医疗' and not std_tag ~ '教育培训'"
            if pgsql.fetch_one(conn_poi, sql, [bid])[0] == 0:
                node_wrong_list.append(",".join(node_wrong_tmp_list))
                continue

            route_end = "Point" + row["route_end"].replace(",", " ")
            sql = "select count(*) from poi where st_dwithin(st_geomfromtext(%s, 4326), geometry, 0.0003) and std_tag ~ '停车场'"
            if pgsql.fetch_one(conn_poi, sql, [route_end])[0] > 0:
                node_wrong_list.append(",".join(node_wrong_tmp_list))
                continue

            sql = """
                select distinct b.node_id from blu_access a inner join blu_access_gate_rel b 
                on a.access_id = b.access_id
                where main_bid = %s and st_dwithin(st_geomfromtext(%s, 4326), geom, 0.0002)
            """
            for (node_id,) in pgsql.fetch_all(conn, sql, [bid, route_end]):
                sql = """
                    select count(*) from nav_gate a inner join nav_gate_cond b on a.gate_id = b.gate_id
                    where a.node_id = %s and a.type != 0 and a.gate_type = 1 and b.car_restric != 1
                """
                if pgsql.fetch_one(conn_road, sql, [node_id])[0] > 0:
                    node_wrong_tmp_list.append(node_id)
            if len(node_wrong_tmp_list) > 1:
                node_wrong_tmp_list = []

            node_wrong_list.append(",".join(node_wrong_tmp_list))
            if len(node_wrong_tmp_list) > 0:
                other_tag_list.append(other_tag)

    ctx.df["wrong_node"] = node_wrong_list
    ctx.df = ctx.df[ctx.df["wrong_node"] != ""]
    ctx.df["other_tag"] = other_tag_list
    # ctx.df.to_csv("aoi_wrong_gate_result_mis", index=False, mode="w", header=True)

    proceed()


def main():
    """
    :return:
    """
    data_dir = Path("/home/<USER>/mnt_newest/mineBidMultiDayCluster/")
    main_pipe = pipeline.Pipeline(
        load_cluster_trajectory_data,
        filter_by_dis,
        filter_with_aoi_info,
        get_wrong_access_info,
        save_intelligence,
    )
    desc.attach(main_pipe)

    latest_intact_dir = list(data_dir.iterdir())
    latest_intact_dir.sort()
    latest_intact_dir_list = latest_intact_dir[-7:]

    ctx = Context()
    for latest_intact_dir in latest_intact_dir_list:
        if not Path(latest_intact_dir / "_SUCCESS").exists():
            continue
        if Path(latest_intact_dir / "_DELETE_ACCESS_SUCCESS").exists():
            continue

        for file_name in tqdm(list(latest_intact_dir.glob("**/*.csv"))):
            # file_name = Path(
            #     "/home/<USER>/mnt_newest/mineBidCluster/20231019/mine_440600_20231019.csv"
            # )
            print(file_name)
            ctx.trajectory_path = file_name
            main_pipe(ctx)
            # break

        Path(latest_intact_dir / "_DELETE_ACCESS_SUCCESS").touch()
        notice_tool.send_hi(
            f"mis挖掘关联错误情报运行完成,量级为{ctx.num_intelligence}, 轨迹日期为{latest_intact_dir.stem}",
            token=notice_tool.MIS_NOTICE,
        )


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(e)
        traceback.print_exc()
        notice_tool.send_hi("mis挖掘关联错误运行失败" + str(e), atuserids=["zhangcong_cd"])
