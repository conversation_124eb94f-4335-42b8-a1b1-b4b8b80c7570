"""
Author: zhangcong_cd <EMAIL>
Date: 2023-09-18 09:57:02
LastEditors: zhangcong_cd <EMAIL>
LastEditTime: 2023-10-16 13:54:06
FilePath: \aoi-ml\src\aoi_cleanup_strategy\cluster_mis_trajectory.py
"""
import time
import traceback
import uuid
import warnings
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path
from typing import Dict, List

import pandas as pd
import shapely.wkt
from pandas.errors import SettingWithCopyWarning
from tqdm import tqdm

from src.integration import tools
from src.tools import notice_tool, pgsql
from src.tools import pipeline

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
warnings.filterwarnings(action="ignore", message="Mean of empty slice")

desc = pipeline.get_desc()
aoi_tag_set = {
    "交通设施;火车站",
    "交通设施;飞机场",
    "交通设施;长途汽车站",
    "购物;购物中心",
    "医疗;综合医院",
    "医疗;专科医院",
    "教育培训;高等院校",
    "文化传媒;展览馆",
    "休闲娱乐;剧院",
    "运动健身;体育场馆",
    "旅游景点;风景区",
    "旅游景点;公园",
    "旅游景点;文物古迹",
    "旅游景点",
    "旅游景点;其他",
    "旅游景点;博物馆",
    "旅游景点;游乐园",
    "旅游景点;寺庙",
    "旅游景点;景点",
    "旅游景点;海滨浴场",
    "旅游景点;动物园",
    "旅游景点;水族馆",
    "旅游景点;教堂",
    "旅游景点;植物园",
    "房地产;住宅区",
    "房地产;写字楼",
    "房地产;其他",
    "房地产",
    "公司企业;公司",
    "公司企业;园区",
    "公司企业",
    "公司企业;厂矿",
    "公司企业;农林园艺",
    "酒店;星级酒店",
    "教育培训;中学",
    "教育培训;小学",
    "教育培训;幼儿园",
    "教育培训;培训机构",
    "教育培训;其他",
    "教育培训;科研机构",
    "教育培训;科技馆",
    "教育培训;成人教育",
    "教育培训;特殊教育学校",
    "教育培训",
    "政府机构;各级政府",
    "政府机构;行政单位",
    "政府机构;公检法机构",
    "政府机构;政治教育机构",
    "政府机构;中央机构",
    "政府机构;涉外机构",
    "购物;市场",
    "购物;家居建材",
    "购物",
    "购物;百货商场",
    "休闲娱乐;休闲广场",
    "休闲娱乐;度假村",
    "休闲娱乐;剧院",
    "汽车服务;汽车销售",
    "汽车服务;汽车配件",
    "汽车服务;汽车维修",
    "汽车服务;汽车检测场",
    "文化传媒;广播电视",
    "文化传媒;文化宫",
    "文化传媒;美术馆",
    "绿地;高尔夫球场",
    "医疗;疗养院",
    "医疗;其他",
    "医疗",
    "生活服务;物流公司",
}


@dataclass
class PoiInfo:
    """
    poi相关信息
    """

    bid: str = ""
    city: str = ""
    name: str = ""
    geom: str = ""
    std_tag: str = ""


@dataclass
class Context:
    """
    上下文信息结构体
    """

    trajectory_path: Path = field(init=False)
    df: pd.DataFrame = field(init=False)
    poi_dict: Dict[str, List[PoiInfo]] = field(default_factory=dict)
    num_intelligence = 0


def load_cluster_trajectory_data(ctx: Context, proceed):
    """
    加载聚合后的mis轨迹数据
    """
    try:
        ctx.df = pd.read_csv(ctx.trajectory_path)
    except pd.errors.EmptyDataError:
        ctx.df = pd.DataFrame()

    proceed()


@desc()
def filter_invalid_trajectory(ctx: Context, proceed):
    """
    根据规划终点和实际终点的距离筛选轨迹数据
    """
    if not ctx.df.empty:
        ctx.df = ctx.df[ctx.df["num_yaw"] >= 3]

    valid_trajectory = []
    for _, row in ctx.df.iterrows():
        route_end = "Point" + row["route_end"].replace(",", " ")
        track_end = "Point" + row["track_end"].replace(",", " ")
        dis = shapely.wkt.loads(route_end).distance(shapely.wkt.loads(track_end))
        if dis > 0.01 or dis < 0.001:
            valid_trajectory.append(0)
            continue
        valid_trajectory.append(1)

    if not ctx.df.empty:
        ctx.df["valid_trajectory"] = valid_trajectory
        ctx.df = ctx.df[ctx.df["valid_trajectory"] == 1]

    proceed()


def query_poi_info(bid):
    """
    查询bid的子点信息
    bid:
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = "select relation_bid, std_tag from poi where bid= %s"
        return pgsql.fetch_one(conn, sql, [bid])


def query_aoi_info(bid):
    """
    根据bid查询AOI的geom信息
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = """
            select st_astext(geom) from blu_face a inner join blu_face_poi b 
            on a.face_id = b.face_id
            where poi_bid = %s
        """
        return pgsql.fetch_one(conn, sql, [bid])


@desc()
def save_bid_lack_aoi(ctx: Context, proceed):
    """
    获取疑似缺少AOI的bid信息
    """
    bid_set = set()
    for _, row in ctx.df.iterrows():
        bid = str(row["bid"])
        res = query_poi_info(bid)
        if res is None:
            continue
        else:
            relation_bid = res[0]
            if res[1] not in aoi_tag_set:
                continue
            if query_aoi_info(bid):
                continue
            if relation_bid != "" and relation_bid != "0":
                if query_aoi_info(relation_bid):
                    continue

        bid_set.add(bid)

    intelligence_list = []
    for bid_tmp in bid_set:
        intelligence_list.append(
            tools.Intelligence(
                id=uuid.uuid4().hex,
                batch="mis_add_aoi_" + time.strftime("%Y-%m-%d", time.localtime()),
                src="mis_add_aoi",
                action="add",
                element_id=bid_tmp,
            )
        )

    tools.post_intelligences(intelligence_list)
    ctx.num_intelligence += len(intelligence_list)
    # with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
    #     sql = "select bid from poi_lack_aoi where bid = %s"
    #     if not pgsql.fetch_one(conn, sql, [bid]):
    #         sql = "insert into poi_lack_aoi(bid, strategy_src) values(%s, 'mis轨迹例行挖掘')"
    #         pgsql.execute(conn, sql, [bid])

    proceed()


def main():
    """
    :return:
    """
    data_dir = Path("/home/<USER>/mnt_newest/mineBidCluster/")
    main_pipe = pipeline.Pipeline(
        load_cluster_trajectory_data,
        filter_invalid_trajectory,
        save_bid_lack_aoi,
    )
    desc.attach(main_pipe)

    latest_intact_dir = list(data_dir.iterdir())
    latest_intact_dir.sort()
    latest_intact_dir_list = latest_intact_dir[-7:]
    ctx = Context()

    for latest_intact_dir in latest_intact_dir_list:
        if not Path(latest_intact_dir / "_SUCCESS").exists():
            continue
        if Path(latest_intact_dir / "_ADD_AOI_SUCCESS").exists():
            continue

        for file_name in tqdm(list(latest_intact_dir.glob("**/*.csv"))):
            # file_name = Path(
            #     "/home/<USER>/mnt_newest/mineBidCluster/20231019/mine_440600_20231019.csv"
            # )
            print(file_name)
            ctx.trajectory_path = file_name
            main_pipe(ctx)
            # break

        Path(latest_intact_dir / "_ADD_AOI_SUCCESS").touch()
        notice_tool.send_hi(
            f"mis挖掘新增AOI情报运行完成,量级为{ctx.num_intelligence}, 轨迹日期为{latest_intact_dir.stem}",
            token=notice_tool.MIS_NOTICE,
        )


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(e)
        traceback.print_exc()
        notice_tool.send_hi("mis挖掘新增AOI情报运行失败" + str(e), atuserids=["zhangcong_cd"])
