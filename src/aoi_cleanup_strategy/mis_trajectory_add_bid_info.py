"""
Author: zhangcong_cd <EMAIL>
Date: 2023-09-18 09:57:02
LastEditors: zhangcong_cd <EMAIL>
LastEditTime: 2023-10-16 13:54:06
FilePath: \aoi-ml\src\aoi_cleanup_strategy\cluster_mis_trajectory.py
"""
import ast
import json
import traceback
import warnings
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path
from typing import Dict, List

import geopandas as gpd
import pandas as pd
import shapely.wkt
from pandas.errors import SettingWithCopyWarning
from tqdm import tqdm
import os
from src.tools import notice_tool, pipeline

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
warnings.filterwarnings(action="ignore", message="Mean of empty slice")

desc = pipeline.get_desc()

NAVI_ROUTE_YAW = 5  # 导航过程中发生偏航

NAVI_END_YAW = 3  # 终点导航偏航


@dataclass
class PoiInfo:
    """
    poi相关信息
    """

    bid: str = ""
    city: str = ""
    name: str = ""
    geom: str = ""
    std_tag: str = ""


@dataclass
class Context:
    """
    上下文信息结构体
    """

    poi_path: Path
    trajectory_path: Path = field(init=False)
    df: pd.DataFrame = field(init=False)
    poi_dict: Dict[str, List[PoiInfo]] = field(default_factory=dict)


@desc()
def loads_poi_data(ctx: Context, proceed):
    """
    从文件中加载poi信息，文件规格参考https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/zopCvUswlfjovv
    """
    if ctx.poi_dict:
        return proceed()

    with open(ctx.poi_path, "r", encoding="utf-8") as f:
        f.readline()
        lines = f.readlines()
        for line in tqdm(lines):
            data = line.strip().split("\t")
            poi_tmp = PoiInfo(
                bid=data[0],
                name=data[1],
                std_tag=data[2],
                city=data[3],
                geom=data[4],
            )
            if poi_tmp.name in ctx.poi_dict:
                ctx.poi_dict[poi_tmp.name].append(poi_tmp)
            else:
                ctx.poi_dict[poi_tmp.name] = [poi_tmp]

    proceed()


@desc()
def load_trajectory_data(ctx: Context, proceed):
    """
    加载轨迹信息，mis轨迹的规格参考https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/zDOzDolH4A/85hNZJvRSCGBSf
    """
    ctx.df = pd.read_csv(ctx.trajectory_path)
    print(f"去重0.剩余", len(ctx.df))
    ctx.df = ctx.df[
        (ctx.df["state"] == NAVI_ROUTE_YAW) & (ctx.df["part"] == NAVI_END_YAW)
        ]
    print(f"去重1.剩余", len(ctx.df))
    ctx.df = ctx.df.drop_duplicates(["mine_id"], keep="first")
    print(f"去重2.剩余", len(ctx.df))
    proceed()


@desc()
def pre_process_trajectory(ctx: Context, proceed):
    """
    mis轨迹字段预处理
    """
    ctx.df["start"] = ctx.df["start"].apply(lambda x: ast.literal_eval(x))
    ctx.df["route_end"] = ctx.df["route_end"].apply(lambda x: ast.literal_eval(x))
    ctx.df["track_end"] = ctx.df["track_end"].apply(lambda x: ast.literal_eval(x))
    ctx.df["linkid"] = ctx.df["linkid"].fillna(0).astype(int)
    ctx.df["yaw_route_line_100m"] = gpd.GeoSeries.from_wkt(
        ctx.df["yaw_route_line_100m"].dropna()
    )
    ctx.df["feedback_neg"] = 0
    ctx.df = ctx.df.reset_index(drop=True)

    proceed()


@desc()
def add_bid_info(ctx: Context, proceed):
    """
    根据名称是否相同匹配bid
    """
    bid_list = []
    std_tag_list = []

    for _, row in tqdm(ctx.df.iterrows(), total=len(ctx.df), leave=False):
        name_end_poi = json.loads(row["lbs_navigation_opt"])["end_poi_name"]
        pos_end_poi = row["route_end"]
        bid = ""
        std_tag = ""
        route_end = "point(%f %f)" % (pos_end_poi[0], pos_end_poi[1])
        if name_end_poi in ctx.poi_dict:
            poi_tmp_list = ctx.poi_dict[name_end_poi]
            for poi_tmp in poi_tmp_list:
                try:
                    if (
                            shapely.wkt.loads(route_end).distance(
                                shapely.wkt.loads(poi_tmp.geom)
                            ) < 0.01
                    ):
                        bid = poi_tmp.bid
                        std_tag = poi_tmp.std_tag
                        break
                except Exception as e:
                    print('解析end发生错误， 跳过', e)
                    continue
        bid_list.append(bid)
        std_tag_list.append(std_tag)

    ctx.df["bid"] = bid_list
    ctx.df["std_tag"] = std_tag_list
    ctx.df = ctx.df[ctx.df["bid"] != ""]

    proceed()


@desc()
def save_mine_trajectory(ctx: Context, proceed):
    """
    保存带bid信息的挖掘mis轨迹
    """
    if ctx.df.empty:
        return proceed()

    result_path = Path(str(ctx.trajectory_path).replace("mine/", "mineBid/"))
    Path.mkdir(result_path.parent, parents=True, exist_ok=True)
    # 文件存在时写入AFS会报异常
    result_path.unlink(missing_ok=True)
    ctx.df.to_csv(result_path, index=False, mode="w", header=True)

    proceed()


def main():
    """
    :return:
    """
    data_dir = Path("/home/<USER>/mnt_newest/mine")
    poi_path = Path("/home/<USER>/mnt/poi_data/poi_detail.csv")
    main_pipe = pipeline.Pipeline(
        loads_poi_data,
        load_trajectory_data,
        pre_process_trajectory,
        add_bid_info,
        save_mine_trajectory,
    )
    desc.attach(main_pipe)

    latest_intact_dir = list(data_dir.iterdir())
    latest_intact_dir.sort()
    latest_intact_dir_list = latest_intact_dir[-7:]
    ctx = Context(poi_path=poi_path)
    new_data_generate = False

    for latest_intact_dir in latest_intact_dir_list:
        if not Path(latest_intact_dir / "_SUCCESS").exists():
            continue
        if Path(str(latest_intact_dir).replace("mine/", "mineBid/")).is_dir() \
                and Path(str(latest_intact_dir).replace("mine/", "mineBid/") + "/_SUCCESS").exists():
            continue

        new_data_generate = True
        for file_name in tqdm(list(latest_intact_dir.glob("**/*.csv"))):
            print(file_name)
            ctx.trajectory_path = file_name
            main_pipe(ctx)

        # 创建标志文件代表已生成
        Path(str(latest_intact_dir).replace("mine/", "mineBid/") + "/_SUCCESS").touch()

        notice_tool.send_hi(
            f"mis挖掘轨迹绑定poi信息完成， 日期为{latest_intact_dir.stem}",
            token=notice_tool.MIS_NOTICE,
        )

    if not new_data_generate:
        notice_tool.send_hi(
            f"mis轨迹数据无新数据生成",
            token=notice_tool.MIS_NOTICE,
        )


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(e)
        traceback.print_exc()
        notice_tool.send_hi("de20-xxl-job-88:mis轨迹绑定bid运行失败" + str(e), atuserids=["chenbaojun_cd"])
