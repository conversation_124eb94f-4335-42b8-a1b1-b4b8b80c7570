"""
Author: zhangcong_cd <EMAIL>
Date: 2023-09-18 09:57:02
LastEditors: zhangcong_cd <EMAIL>
LastEditTime: 2023-10-16 13:54:06
FilePath: \aoi-ml\src\aoi_cleanup_strategy\cluster_mis_trajectory.py
"""
import ast
import os
import traceback
import warnings
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path
from typing import List

import numpy as np
import pandas as pd
from pandas.errors import SettingWithCopyWarning
from sklearn.cluster import DBSCAN
from tqdm import tqdm

from src.tools import notice_tool, pipeline

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
warnings.filterwarnings(action="ignore", message="Mean of empty slice")
os.environ['OMP_NUM_THREADS'] = "10"

desc = pipeline.get_desc()

TRAJECTORY_CLUSTER_DAYS = 7  # 轨迹聚合的天数
TRAJECTORY_MONITOR_DAYS = -7  # 轨迹监控的天数，即最新轨迹数据聚合状态监控的天数
NAVI_ROUTE_YAW = 5  # 导航过程中发生偏航
# NAVI_END_YAW = 3  # 终点导航偏航
EPS = 0.0005
MIN_SAMPLE_1 = False
PART_PRIORITY_DICT = {"3": "p1", "2": "p2", "1": "p3", "0": "p4"}
RESULT_COLS = [
    "bid",
    "std_tag",
    "mine_id",
    "dist_end_track",
    "dist_end_route",
    "geom",
    "linkid",
    "province",
    "city",
    "lbs_navigation_city",
    "yaw_rate",
    "num_yaw",
    "dist_cost",
    "num_feedback_neg",
    "num_type_track",
    "num_type_route",
    "num_type_diff",
    "num_part_short",
    "num_part_start",
    "num_part_mid",
    "num_part_end",
    "num_state_yaw",
    "num_behav_switch",
    "num_behav_early_quit",
    "num_behav_dest_change",
    "num_behav_continuous_yaw",
    "cn",
    "hcn",
    "hun",
    "yn",
    "cuid_timestamp",
    "lbs_navigation_name",
    "lbs_navigation_opt",
    "track_start",
    "track_end",
    "route_start",
    "route_end",
    "yaw_track_line_100m",
    "yaw_route_line_100m",
    "yaw_track_line_extend",
    "yaw_route_line_extend",
]


@dataclass
class PoiInfo:
    """
    poi相关信息
    """

    bid: str = ""
    city: str = ""
    name: str = ""
    geom: str = ""
    std_tag: str = ""


@dataclass
class Context:
    """
    上下文信息结构体
    """

    trajectory_path: Path = field(init=False)
    trajectory_dir_list: List[Path] = field(init=False)
    df: pd.DataFrame = field(init=False)


@desc()
def load_trajectory_data(ctx: Context, proceed):
    """
    加载聚合后的mis轨迹数据
    """

    def read_csv(file_name):
        try:
            df = pd.read_csv(file_name)
        except pd.errors.EmptyDataError:
            df = pd.DataFrame()

        return df

    ctx.df = pd.DataFrame()
    file_list = []
    try:
        for trajectory_dir in ctx.trajectory_dir_list:
            file_list.append(str(ctx.trajectory_path).replace(ctx.trajectory_path.parent.stem, trajectory_dir.stem))

        ctx.df = pd.concat((read_csv(f) for f in file_list if Path(f).exists()))
    except Exception as e:
        print("load_trajectory_data error: ", traceback.format_exc())
        return

    proceed()


@desc()
def pre_process_trajectory(ctx: Context, proceed):
    """
    mis轨迹字段预处理
    """
    ctx.df["start"] = ctx.df["start"].apply(lambda x: ast.literal_eval(x))
    ctx.df["route_end"] = ctx.df["route_end"].apply(lambda x: ast.literal_eval(x))
    ctx.df["track_end"] = ctx.df["track_end"].apply(lambda x: ast.literal_eval(x))
    ctx.df["linkid"] = ctx.df["linkid"].fillna(0).astype(int)
    ctx.df = ctx.df.reset_index(drop=True)
    ctx.df = ctx.df.drop_duplicates(["mine_id"], keep="first")

    proceed()


@desc()
def cluster_trajectory(ctx: Context, proceed):
    """
    轨迹聚合，聚合方式：bid + 导航终点 + 实际终点
    """

    def get_data(df):
        # 偏航数量
        num_yaw = len(df)
        # linkid
        linkids = df[df["linkid"] != 0]["linkid"]
        if len(linkids) >= 1:
            linkid = linkids.mode().iloc[0]
        else:
            linkid = None
        # 偏航代价
        dist_cost = df["dist_cost"].median()
        # main sign
        if len(df) >= 2:
            df["dist_cost_diff"] = df["dist_cost"] - dist_cost
            df["dist_cost_diff"] = df["dist_cost_diff"].abs()

            # 选取一条具有代表性的数据，排序规则：终点偏航(3)>行中偏航(2)>起点偏航(1)>短途偏航(0)
            # 发生变更
            df["part_priority"] = (
                df["part"].astype(str).apply(lambda x: PART_PRIORITY_DICT[x])
            )
            df["feedback_neg_priority"] = df["feedback_neg"] * -1
            df = df.sort_values(
                ["feedback_neg_priority", "part_priority", "dist_cost_diff"]
            )
        main_sign = df.iloc[0]
        geom = main_sign["start"]
        geom = ",".join(map(lambda x: str(round(x, 7)), geom))

        # 城市
        lbs_navigation_city = df["lbs_navigation_city"].mode()
        if not lbs_navigation_city.empty:
            lbs_navigation_city = lbs_navigation_city[0]
        else:
            lbs_navigation_city = None
        province = df["province"].mode()
        if not province.empty:
            province = province[0]
        else:
            province = None

        city = df["city"].mode()
        if not city.empty:
            city = city[0]
        else:
            city = None

        # result format
        res = {
            "bid": main_sign["bid"],
            "std_tag": main_sign["std_tag"],
            "mine_id": main_sign["mine_id"],
            "dist_end_track": main_sign["dist_end_track"],
            "dist_end_route": main_sign["dist_end_route"],
            "geom": geom,
            "linkid": linkid,
            "province": province,
            "city": city,
            "lbs_navigation_city": lbs_navigation_city,
            "yaw_rate": None,
            "num_yaw": num_yaw,
            "dist_cost": dist_cost,
            "num_feedback_neg": sum(df["feedback_neg"]),
            "tag_feedback_neg": "",
            "descript_feedback_neg": "",
            "num_type_track": sum(df["type"] == "track"),
            "num_type_route": sum(df["type"] == "route"),
            "num_type_diff": sum(df["type"] == "diff"),
            "num_part_short": sum(df["part"] == 0),
            "num_part_start": sum(df["part"] == 1),
            "num_part_mid": sum(df["part"] == 2),
            "num_part_end": sum(df["part"] == 3),
            "num_state_yaw": sum(df["state"] == 5),
            "num_behav_switch": sum(df["route_switch"] == 1),
            "num_behav_early_quit": sum(df["early_quit"] == 1),
            "num_behav_dest_change": sum(df["dest_change"] == 1),
            "num_behav_continuous_yaw": sum(df["continuous_yaw"] == 1),
            "cn": None,
            "hcn": None,
            "hun": None,
            "yn": None,
            "cuid_timestamp": main_sign["cuid"]
                              + ","
                              + main_sign["lbs_navigation_start_timestring"]
                              + ";"
                              + ";".join(df["cuid"] + "," + df["lbs_navigation_start_timestring"]),
            "lbs_navigation_name": main_sign["lbs_navigation_name"],
            "lbs_navigation_opt": main_sign["lbs_navigation_opt"],
            "track_start": main_sign["track_start"],
            "track_end": main_sign["track_end"],
            "route_start": main_sign["route_start"],
            "route_end": main_sign["route_end"],
            "yaw_track_line_100m": main_sign["yaw_track_line_100m"],
            "yaw_route_line_100m": main_sign["yaw_route_line_100m"],
            "yaw_track_line_extend": main_sign["yaw_track_line_extend"],
            "yaw_route_line_extend": main_sign["yaw_route_line_extend"],
        }
        return res

    result = []
    for bid, subdf in ctx.df.groupby("bid"):
        if len(ctx.df) >= 1:
            X = np.array(list(subdf["route_end"]))
            db = DBSCAN(eps=EPS, min_samples=2).fit(X)
            labels = db.labels_
        else:
            labels = [-1] * len(subdf)
        subdf["label_route_end"] = labels

        for route_end, route_subdf in subdf.groupby("label_route_end"):
            # clustering
            if len(route_subdf) >= 1:
                X = np.array(list(route_subdf["track_end"]))
                db = DBSCAN(eps=EPS, min_samples=2).fit(X)
                labels = db.labels_
            else:
                labels = [-1] * len(route_subdf)
            route_subdf["label_track_end"] = labels

            for track_end, track_subdf in route_subdf.groupby("label_track_end"):
                if route_end == -1 or track_end == -1:
                    if MIN_SAMPLE_1:
                        track_subdf = track_subdf
                    else:
                        track_subdf = track_subdf[track_subdf["feedback_neg"] == 1]
                    for i, x in track_subdf.iterrows():
                        res = get_data(x.to_frame().T)
                        result.append(res)
                    continue
                res = get_data(track_subdf)
                result.append(res)

    result = list(filter(lambda x: x is not None and len(x) >= 1, result))
    ctx.df = pd.DataFrame(result)
    if not ctx.df.empty:
        ctx.df["linkid"] = ctx.df["linkid"].fillna(0).astype(int)
        ctx.df = ctx.df[RESULT_COLS]

    proceed()


@desc()
def save_cluster_trajectory(ctx: Context, proceed):
    """
    mis轨迹结果保存至AFS
    """
    if ctx.df.empty:
        return proceed()

    ctx.df = ctx.df[ctx.df["num_yaw"] >= 2]
    result_path = Path(str(ctx.trajectory_path).replace("mineBid/", "mineBidMultiDayCluster/"))
    Path.mkdir(result_path.parent, parents=True, exist_ok=True)
    # 文件存在时写入AFS会报异常
    result_path.unlink(missing_ok=True)
    ctx.df.to_csv(result_path, index=False, mode="w", header=True)

    proceed()


def main():
    """
    :return:
    """
    data_dir = Path("/home/<USER>/mnt_newest/mineBid")
    main_pipe = pipeline.Pipeline(
        load_trajectory_data,
        pre_process_trajectory,
        cluster_trajectory,
        save_cluster_trajectory,
    )
    desc.attach(main_pipe)

    latest_intact_dir = list(data_dir.iterdir())
    latest_intact_dir.sort()
    latest_intact_dir_list = latest_intact_dir[-TRAJECTORY_MONITOR_DAYS:]
    ctx = Context()

    # for latest_intact_dir_tmp in latest_intact_dir_list:
    for i in range(len(latest_intact_dir_list)):
        latest_intact_dir_tmp = latest_intact_dir_list[i]

        # if not Path(latest_intact_dir_tmp / "_SUCCESS").exists():
        #     # 用于保证前一周的mis轨迹都完成了与bid的绑定
        #     continue
        if Path(str(latest_intact_dir_tmp).replace("mineBid/", "mineBidMultiDayCluster/") + "/_SUCCESS").exists():
            continue

        current_dir_pos = len(latest_intact_dir) - TRAJECTORY_MONITOR_DAYS + i
        ctx.trajectory_dir_list = latest_intact_dir[current_dir_pos - TRAJECTORY_CLUSTER_DAYS:current_dir_pos]

        for file_name in tqdm(list(latest_intact_dir_tmp.glob("**/*.csv"))):
            print(file_name)
            ctx.trajectory_path = file_name
            main_pipe(ctx)

        # 创建标志文件代表已生成
        Path(str(latest_intact_dir_tmp).replace("mineBid/", "mineBidMultiDayCluster/") + "/_SUCCESS").touch()

        notice_tool.send_hi(
            f"mis挖掘轨迹一周数据聚合完成， 最新日期为{latest_intact_dir_tmp.stem}",
            token=notice_tool.MIS_NOTICE,
        )


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(e)
        print(traceback.print_exc())
        notice_tool.send_hi("mis挖掘多天聚合运行失败" + str(e), atuserids=["chenbaojun_cd"])
