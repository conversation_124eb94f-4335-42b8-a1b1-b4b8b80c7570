# !/usr/bin/env python3
"""
策略：包含多个内部路组
ref to: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Fr8QITM0sh/YZCUDtktsX/GowCLzU7rA9_dj
"""

from dataclasses import dataclass
from dataclasses import field

from tqdm import tqdm

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy import road_tools
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import pgsql
from src.tools import pipeline

STRATEGY_NAME = 'multi_inner_road_group'
STRATEGY_VERSION = '1.0.0'

# 内部路组不要超过一条
MAX_INNER_ROAD_GROUP_COUNT = 1

desc = pipeline.get_desc()


@dataclass
class Aoi:
    bid: str
    name: str
    geom: str
    # [(link_id, s_nid, e_nid)]
    links: list[tuple[str, str, str]] = field(default_factory=list)
    # [[link_id]]
    road_groups: list[list[str]] = field(default_factory=list)


@dataclass
class Context:
    bids: list[str]
    batch: str
    args: dict
    blu_faces: list[Aoi] = field(default_factory=list)


@desc()
def query_blu_face_geom(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select
                b.poi_bid, a.name_ch, ST_AsText(a.geom)
            from
                blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where
                b.poi_bid in %s and a.aoi_level = 2 and a.src != 'SD';
        '''
        ctx.blu_faces = [
            Aoi(bid=bid, name=name, geom=geom)
            for bid, name, geom in pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
        ]

    proceed()


@desc()
def query_inner_road(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.ROAD_CONFIG) as conn:
        for aoi in tqdm(ctx.blu_faces):
            sql = '''
                select link_id, s_nid, e_nid from nav_link
                where form = '52' and ST_Intersects(ST_GeomFromText(%s, 4326), geom);
            '''
            aoi.links = pgsql.fetch_all(conn, sql, [aoi.geom])

    proceed()


@desc()
def group_inner_road(ctx: Context, proceed):
    for aoi in ctx.blu_faces:
        aoi.road_groups = [*road_tools.search_inner_road_group(aoi.links)]

    proceed()


@desc()
def save_to_db(ctx: Context, proceed):
    results = [
        StrategyResult(
            case_id=aoi.bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail',
        )
        for aoi in ctx.blu_faces if is_issue(aoi)
    ]
    export_strategy_result.to_db(results)
    proceed()


def is_issue(aoi: Aoi):
    return len(aoi.road_groups) > MAX_INNER_ROAD_GROUP_COUNT


def run(bids: list[str], batch: str):
    def len_issues(x: Context):
        return len([aoi for aoi in x.blu_faces if is_issue(aoi)])

    main_pipe = pipeline.Pipeline(
        query_blu_face_geom,
        query_inner_road,
        group_inner_road,
        pipeline.print_desc(lambda x: f'find issues: {len_issues(x)}'),
        save_to_db,
    )
    desc.attach(main_pipe)

    ctx = Context(
        bids=bids,
        batch=batch,
        args={
            'max_inner_road_group_count': MAX_INNER_ROAD_GROUP_COUNT,
        },
    )
    main_pipe(ctx)
