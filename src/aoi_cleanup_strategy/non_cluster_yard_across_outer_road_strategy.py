# !/usr/bin/env python3
"""
策略：非聚合区跨高等级道路
- 详情：基础院落、内部区域被7级路及以上（nav_link.kind <= 7）切开，切开最小面积占比 x% 以上，或最小面积大于 y 万平方米。
- ref to: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Fr8QITM0sh/YZCUDtktsX/GowCLzU7rA9_dj
"""
import dataclasses
from dataclasses import dataclass
from dataclasses import field
from pathlib import Path
from typing import Iterable

from shapely import ops
from shapely import wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv

STRATEGY_NAME = 'non_cluster_yard_across_outer_road'
STRATEGY_VERSION = '1.0.0'

desc = pipeline.get_desc()


@dataclass
class Aoi:
    bid: str
    name: str
    level: int
    geom: BaseGeometry
    outer_roads: list[BaseGeometry] = field(default_factory=list)


@dataclass
class Issue:
    aoi: Aoi
    segments: list[BaseGeometry] = field(default_factory=list)
    secondary_area_radio: float = field(default=0.0)
    secondary_area: float = field(default=0.0)


@dataclass
class Context:
    bids: list[str]
    batch: str
    args: dict
    aoi_list: list[Aoi] = field(default_factory=list)
    issues: list[Issue] = field(default_factory=list)
    selected_issues: list[Issue] = field(default_factory=list)


def import_bids_from_file(file_path: Path):
    @desc(f'import bids from file, {file_path=}')
    def pipe(ctx: Context, proceed):
        ctx.bids = [x[0] for x in tsv.read_tsv(file_path)]
        proceed()

    return pipe


def import_bids_from_db(source_type: int):
    @desc(f'import bids from db, {source_type=}')
    def pipe(ctx: Context, proceed):
        with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
            sql = '''
                select bid, source_type from bid_gate_todo_lists
                where source_type = %s;
            '''
            ret = pgsql.fetch_all(conn, sql, [source_type])
            ctx.bids = [x[0] for x in ret]
        proceed()

    return pipe


@desc()
def filter_non_cluster_yards(ctx: Context, proceed):
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as back_conn,
        pgsql.get_connection(pgsql.POI_CONFIG) as poi_conn,
    ):
        sql = '''
            select
                b.poi_bid, a.aoi_level, ST_AsText(a.geom)
            from
                blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where
                b.poi_bid in %s and a.aoi_level in (2,3) and a.src != 'SD';
        '''
        back_ret = pgsql.fetch_all(back_conn, sql, [tuple(ctx.bids)])
        sql = '''
            select bid, name from poi
            where bid in %s;
        '''
        poi_ret = pgsql.fetch_all(poi_conn, sql, [tuple(ctx.bids)])
        name_dict = {bid: name for bid, name in poi_ret}
        ctx.aoi_list = [
            Aoi(bid=bid, name=name_dict.get(bid, '<empty>'), level=level, geom=wkt.loads(geom))
            for bid, level, geom in back_ret
        ]

    proceed()


@desc()
def fill_outer_roads(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.ROAD_CONFIG) as conn:
        sql = '''
            select ST_AsText(geom) from nav_link
            where kind <= 7 and ST_Intersects(geom, ST_GeomFromText(%s, 4326));
        '''
        for aoi in tqdm(ctx.aoi_list):
            ret = pgsql.fetch_all(conn, sql, [aoi.geom.wkt])
            aoi.outer_roads = [wkt.loads(x[0]) for x in ret]

    proceed()


@desc()
def fill_issues(ctx: Context, proceed):
    for aoi in tqdm(ctx.aoi_list):
        segments = split_polygon_by_lines(aoi.geom, aoi.outer_roads)
        issue = Issue(aoi=aoi, segments=segments)
        if len(segments) > 1:
            total_area = aoi.geom.area
            areas = [x.area for x in segments]
            areas.sort(reverse=True)
            area_radios = [x / total_area for x in areas]

            issue.secondary_area = areas[1] * 1e10  # 换算成平方米
            issue.secondary_area_radio = area_radios[1]

        ctx.issues.append(issue)

    proceed()


def filter_issues(min_area_radio: float, min_area: float):
    @desc('filter issues')
    def pipe(ctx: Context, proceed):
        ctx.selected_issues = [
            x for x in ctx.issues
            if x.secondary_area >= min_area or x.secondary_area_radio >= min_area_radio
        ]
        proceed()

    return pipe


@desc()
def save_to_db(ctx: Context, proceed):
    def encode_value(value):
        return str(value) if isinstance(value, BaseGeometry) else value

    def dict_factory(pairs):
        return {k: [encode_value(x) for x in v] if isinstance(v, Iterable) else encode_value(v) for k, v in pairs}

    fail_bids = {x.aoi.bid for x in ctx.selected_issues}
    results = [
        StrategyResult(
            case_id=x.aoi.bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail' if x.aoi.bid in fail_bids else 'pass',
            args=ctx.args,
            details=dataclasses.asdict(x, dict_factory=dict_factory)
        )
        for x in ctx.issues
    ]
    export_strategy_result.to_db(results)
    proceed()


# helper functions:

def split_polygon_by_lines(polygon: BaseGeometry, lines: list[BaseGeometry]) -> list[BaseGeometry]:
    """
    根据给定的线段集切割给定的多边形，返回切割后的碎片。
    ref to: https://gis.stackexchange.com/a/232861
    """
    merged = ops.linemerge([polygon.boundary, *lines])
    borders = ops.unary_union(merged)
    polygons = ops.polygonize(borders)
    return list(polygons)


def run(bids: list[str], batch: str):
    min_area_radio = 0.2
    min_area = 10_000

    main_pipe = pipeline.Pipeline(
        filter_non_cluster_yards,
        fill_outer_roads,
        fill_issues,
        filter_issues(min_area_radio=min_area_radio, min_area=min_area),
        save_to_db,
    )
    desc.attach(main_pipe)
    ctx = Context(
        bids=bids,
        batch=batch,
        args={
            'min_area_radio': min_area_radio,
            'min_area': min_area,
        },
    )
    main_pipe(ctx)
