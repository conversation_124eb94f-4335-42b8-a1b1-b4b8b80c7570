# !/usr/bin/env python3
"""
用于上线 AOI 边框。
具体地：本脚本会例行化执行，上线 poi_online.online_aoi_task 表中存储的边框数据。
"""
import argparse
import datetime
import shutil
import subprocess
import urllib.request
from dataclasses import dataclass, field
from pathlib import Path

from retrying import retry
from tqdm import tqdm

from src.batch_process import tags
from src.tools import pipeline, pgsql, tsv
from src.tools.connect_aoi_to_poi import get_bids_in_working

desc = pipeline.get_desc()


@dataclass
class OnlineRecord:
    """
    存储上线边框的相关信息。
    """
    bid: str
    mid: str
    name: str
    city: str
    wkt: str
    aoi_id: str
    aoi_level: int
    remark: str
    reason: str = ''
    can_online: bool = True


@dataclass
class OnlineTask:
    """
    存储上线任务的相关信息。
    """
    batch: str
    src: str
    url: str
    path: Path = field(init=False)
    count: int


@dataclass
class Context:
    """
    脚本执行上下文。
    """
    work_dir: Path
    online_commercial_template_script: Path
    online_client_template_script: Path
    aoi_list_path: Path
    mode: str
    src: str
    online_commercial_script: Path = field(init=False)
    online_client_script: Path = field(init=False)
    commercial_records_path: Path = field(init=False)
    client_records_path: Path = field(init=False)
    online_tasks: list[OnlineTask] = field(default_factory=list)
    commercial_records: list[OnlineRecord] = field(default_factory=list)
    client_records: list[OnlineRecord] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        self.online_commercial_script = self.work_dir / self.online_commercial_template_script.name
        self.online_client_script = self.work_dir / self.online_client_template_script.name

    def get_online_count(self):
        """
        获取当前可上线的边框数量。
        """
        return (
            len([x for x in self.commercial_records if x.can_online]),
            len([x for x in self.client_records if x.can_online])
        )


@desc()
def fetch_tasks(ctx: Context, proceed):
    """
    从 poi_online.online_aoi_task 表中获取待上线的边框数据。
    """
    if ctx.mode == 'lib':
        with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
            sql = f'''
                select batch, src, url, count from online_aoi_task where completed_at is null;
            '''
            ctx.online_tasks = [
                OnlineTask(
                    batch=batch,
                    src=src,
                    url=url,
                    count=count,
                )
                for batch, src, url, count in pgsql.fetch_all(conn, sql)
            ]

    proceed()


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def download_file(url, save_path):
    """
    下载文件（附带重试功能）。
    """
    urllib.request.urlretrieve(url, save_path)


@desc()
def download_tasks(ctx: Context, proceed):
    """
    下载任务数据。
    """
    if ctx.mode == 'lib':
        if not any(ctx.online_tasks):
            return

        for task in tqdm(ctx.online_tasks):
            task.path = ctx.work_dir / f'{task.batch}.tsv'
            download_file(task.url, task.path)

    proceed()


def print_online_count(ctx: Context):
    """
    打印当前可上线的边框数量。
    """
    commercial_online_count, client_online_count = ctx.get_online_count()
    print(f'commercial_records: {commercial_online_count}')
    print(f'client_records: {client_online_count}')


def __parse_record(record_path):
    """
    解析上线数据
    """
    field_count_v2 = 8  # 新规格字段个数

    if not record_path.exists():
        return

    for items in tsv.read_tsv(record_path):
        if len(items) == field_count_v2:
            bid, mid, name, city, wkt, aoi_id, aoi_level, remark = items
            yield OnlineRecord(
                bid=bid,
                mid=mid,
                name=name,
                city=city,
                wkt=wkt,
                aoi_id=aoi_id,
                aoi_level=aoi_level,
                remark=remark,
            )
        else:
            bid, mid, name, city, wkt, aoi_id, remark = items
            yield OnlineRecord(
                bid=bid,
                mid=mid,
                name=name,
                city=city,
                wkt=wkt,
                aoi_id=aoi_id,
                aoi_level=2,
                remark=remark,
            )


@desc()
def load_lib_records(ctx: Context, proceed):
    """
    加载 AOI 上线数据。
    """
    if ctx.mode == 'lib':
        commercial_paths = [task.path for task in ctx.online_tasks if 'SD' == task.src]
        client_paths = [task.path for task in ctx.online_tasks if 'CD' == task.src]

        for path in commercial_paths:
            ctx.commercial_records.extend(__parse_record(path))

        for path in client_paths:
            ctx.client_records.extend(__parse_record(path))

        print_online_count(ctx)

    proceed()


@desc()
def load_file_records(ctx: Context, proceed):
    """
    加载本地上线数据。
    """
    if ctx.mode == 'file':
        if ctx.src == 'CD':
            ctx.client_records.extend(__parse_record(ctx.aoi_list_path))
        elif ctx.src == 'SD':
            ctx.commercial_records.extend(__parse_record(ctx.aoi_list_path))

        print_online_count(ctx)

    proceed()


@desc()
def filter_records_by_tag(ctx: Context, proceed):
    """
    使用 tag 过滤掉不能上线的边框。
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        for record in tqdm(ctx.commercial_records + ctx.client_records):
            if not record.can_online:
                continue

            sql = '''
                select std_tag from poi where bid = %s;
            '''
            row = pgsql.fetch_one(conn, sql, (record.bid,))
            if not row:
                record.reason = 'no poi'
                record.can_update = False
                continue

            std_tag, = row
            if std_tag in tags.ADMIN:
                record.reason = 'poi is admin'
                record.can_update = False

    proceed()


@desc()
def filter_records_by_repeated_bids(ctx: Context, proceed):
    """
    根据 bid 去重。
    """
    client_bids = set()
    commercial_bids = set([x.bid for x in ctx.commercial_records])

    for record in ctx.client_records:
        if not record.can_online:
            continue

        if record.bid in client_bids:
            record.reason = 'bid 重复'
            record.can_online = False
            continue

        if record.bid in commercial_bids:
            record.reason = 'bid 重复'
            record.can_online = False
            continue

        client_bids.add(record.bid)

    print_online_count(ctx)
    proceed()


@desc()
def filter_records_by_repeated_face_ids(ctx: Context, proceed):
    """
    防止上线 face_id 相同的边框。
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for record in tqdm(ctx.commercial_records + ctx.client_records):
            if not record.can_online:
                continue

            sql = f'''
                select face_id from blu_face where face_id = %s;
            '''
            if pgsql.fetch_one(conn, sql, (record.aoi_id,)):
                record.reason = 'face_id 重复'
                record.can_online = False

    print_online_count(ctx)
    proceed()


@desc()
def filter_records_by_bids_in_working(ctx: Context, proceed):
    """
    将正在作业中的 bid 过滤掉。
    """
    all_records = ctx.commercial_records + ctx.client_records
    if not any(all_records):
        proceed()
        return

    bids_in_working = set(get_bids_in_working())
    for record in tqdm(all_records):
        if not record.can_online:
            continue

        if record.bid in bids_in_working:
            record.reason = 'bid 作业中'
            record.can_online = False

    print_online_count(ctx)
    proceed()


@desc()
def filter_records_by_relation(ctx: Context, proceed):
    """
    如果 bid 在母库中已经存在关联关系，则不上线。
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for record in tqdm(ctx.commercial_records + ctx.client_records):
            if not record.can_online:
                continue

            sql = f'''
                select * from blu_face_poi where poi_bid = %s;
            '''
            if pgsql.fetch_one(conn, sql, (record.bid,)):
                record.reason = 'bid 存在关联关系'
                record.can_online = False

    print_online_count(ctx)
    proceed()


@desc()
def save_online_records(ctx: Context, proceed):
    """
    保存上线边框数据。
    """

    def save(records, record_path):
        tsv.write_tsv(
            record_path,
            [
                [
                    x.bid,
                    x.mid,
                    x.name,
                    x.city,
                    x.wkt,
                    x.aoi_id,
                    x.aoi_level,
                    x.remark,
                ]
                for x in records if x.can_online
            ]
        )

    ctx.commercial_records_path = ctx.work_dir / 'commercial.tsv'
    ctx.client_records_path = ctx.work_dir / 'client.tsv'

    save(ctx.commercial_records, ctx.commercial_records_path)
    save(ctx.client_records, ctx.client_records_path)

    proceed()


@desc()
def copy_online_script(ctx: Context, proceed):
    """
    拷贝上线脚本。
    """
    shutil.copyfile(ctx.online_commercial_template_script, ctx.online_commercial_script)
    shutil.copyfile(ctx.online_client_template_script, ctx.online_client_script)
    proceed()


def execute_online_script(script_path: Path, aoi_path: Path):
    """
    执行上线脚本。
    """
    online_process = subprocess.Popen(
        args=f'cd {script_path.parent}; python3 {script_path} {aoi_path}',
        shell=True,
    )
    online_process.wait()


@desc()
def online(ctx: Context, proceed):
    """
    执行上线操作。
    """
    commercial_online_count, client_online_count = ctx.get_online_count()

    if commercial_online_count > 0:
        execute_online_script(ctx.online_commercial_script, ctx.commercial_records_path)

    if client_online_count > 0:
        execute_online_script(ctx.online_client_script, ctx.client_records_path)

    proceed()


@desc()
def mark_tasks_as_completed(ctx: Context, proceed):
    """
    标记任务完成。
    """
    if ctx.mode == 'lib':
        with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
            for task in tqdm(ctx.online_tasks):
                sql = f'''
                    update online_aoi_task set completed_at = now() where batch = %s;
                '''
                pgsql.execute(conn, sql, (task.batch,))

    proceed()


def parse_args():
    """
    解析参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--aoi-list-path',
        dest='aoi_list_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        default='lib',
        required=False,
    )
    parser.add_argument(
        '--src',
        dest='src',
        type=str,
        default='SD',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数。
    """
    main_pipe = pipeline.Pipeline(
        fetch_tasks,
        download_tasks,
        load_lib_records,
        load_file_records,
        filter_records_by_tag,
        filter_records_by_repeated_bids,
        filter_records_by_repeated_face_ids,
        filter_records_by_bids_in_working,
        filter_records_by_relation,
        save_online_records,
        copy_online_script,
        online,
        mark_tasks_as_completed,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/linbin/export/linbin') / datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S-%f'),
        online_commercial_template_script=Path('/home/<USER>/linbin/export/linbin/run_release_sd_v2.py'),
        online_client_template_script=Path('/home/<USER>/linbin/export/linbin/run_release_cd_v2.py'),
        aoi_list_path=Path(args.aoi_list_path),
        mode=args.mode,
        src=args.src,
    )

    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
