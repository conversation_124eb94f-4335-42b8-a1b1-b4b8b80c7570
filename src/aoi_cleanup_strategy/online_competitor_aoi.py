# !/usr/bin/env python3
"""
用于上线竞品识别边框。
包含的步骤有：
1. 将识别成果和竞品数据做对比，取交并比大于一定阈值的数据
2. 修形后处理
3. 推送上线
"""
import json
import subprocess
from pathlib import Path

import pymysql
from dataclasses import dataclass

from src.tools import pipeline
from src.tools.afs_tool import AfsTool
from src.tools.conf_tools import get_mysql_conf
from src.tools.function import exec_shell_cmd

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    task_id: str = None
    batch_id: str = None
    dataset_info_url: str = None
    wpf_memo_info: str = None


def create_db_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf()
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def execute_db_command(sql, args=None):
    """
    执行 mysql 数据库命令
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(sql, args)
            conn.commit()


def mark_task_error(task_id, error):
    """
    标记任务为错误状态
    """
    execute_db_command('update crawl_task set status=-1, memo=%s where id=%s', (error, task_id))


def mark_task_start(task_id):
    """
    标记任务为开始状态
    """
    execute_db_command(f'''
        update crawl_task 
        set status = 7
        where id=%s
    ''', (task_id, ))


def mark_task_finished(task_id):
    """
    标记任务为完成状态
    """
    execute_db_command(f'''
        update crawl_task 
        set status = 8
        where id=%s
    ''', (task_id,))


@desc()
def fetch_oldest_batch(ctx: Context, proceed):
    """
    获取最旧的批处理任务
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(f'''
                select id, batch_id, dataset_info_url
                from crawl_task
                where status = 6        
                order by finished_time
                limit 1;
            ''')
            task = cursor.fetchone()
            if task:
                task_id, batch_id, dataset_info_url = task
                ctx.task_id = task_id
                ctx.batch_id = batch_id
                ctx.dataset_info_url = dataset_info_url
                proceed()


@desc()
def get_wpf_memo_info(ctx: Context, proceed):
    """
    获取 wpf_memo_info
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            sql = f'''
                select wpf_status, wpf_memo_info, wpf_fail_reason
                from aoi_ml_work_package_flow
                where wpf_flow_info like %s and
                      wpf_section_name = 'subtask_exec_seg'
                limit 1;
            '''
            print(cursor.mogrify(sql, (f'%{ctx.batch_id}%', )))
            cursor.execute(sql, (f'%{ctx.batch_id}%', ))
            record = cursor.fetchone()
            if record:
                wpf_status, wpf_memo_info, wpf_fail_reason = record
                if wpf_status == 0 or wpf_status == 1:
                    return
                elif wpf_status == 2:
                    ctx.wpf_memo_info = wpf_memo_info
                    proceed()
                else:
                    mark_task_error(ctx.task_id, wpf_fail_reason)


@desc()
def download_recognition_result(ctx: Context, proceed):
    """
    下载识别结果
    """
    if ctx.wpf_memo_info is None:
        return

    mark_task_start(ctx.task_id)
    info = json.loads(ctx.wpf_memo_info)
    afs_url = info['afs_url']
    local_file_name = Path(info['afs_url']).name
    local_folder = ctx.work_dir / ctx.batch_id
    local_file_path = local_folder / local_file_name

    afs = AfsTool()
    afs.get(afs_url, local_folder)
    if not local_file_path.exists():
        raise Exception(f'无法下载：{afs_url}')

    # afs 上的文件无后缀名，加个后缀名。
    tar_file_path = local_folder / f'{ctx.batch_id}.tar'
    local_file_path.rename(tar_file_path)
    decompress_dataset_process = subprocess.Popen([
        'tar',
        '-xf',
        str(tar_file_path),
        '-C',
        str(local_folder)
    ], stdout=subprocess.PIPE)
    decompress_dataset_process.wait()

    tar_file_path.unlink()

    proceed()


@desc()
def download_dataset_info(ctx: Context, proceed):
    """
    下载数据集信息
    """
    local_file_name = Path(ctx.dataset_info_url).name
    local_folder = ctx.work_dir / ctx.batch_id
    local_file_path = local_folder / local_file_name
    afs = AfsTool()
    afs.get(ctx.dataset_info_url, local_folder)
    if not local_file_path.exists():
        raise Exception(f'无法下载：{ctx.dataset_info_url}')

    local_file_path.rename(local_folder / 'res2detect.json')

    proceed()


@desc()
def execute_analysis_edge(ctx: Context, proceed):
    """
    执行 analysis_edge 脚本
    """
    local_folder = ctx.work_dir / ctx.batch_id
    vectorized_output = local_folder / 'vectorized_out'
    vectorized_output.mkdir(exist_ok=True)
    exec_shell_cmd(f'cd /home/<USER>/chenbaojun/aoi-ml-20230221; '
                   f'python3 analysis_edge.py {str(local_folder)} {ctx.batch_id}')

    proceed()


@desc()
def execute_generate_diff(ctx: Context, proceed):
    """
    执行 generate_diff 脚本
    """
    exec_shell_cmd(f"cd /home/<USER>/aoi-strategy/2023_03_07/src/aoi_ml; "
                   f"python3 generate_diff.py {str(ctx.work_dir / ctx.batch_id / 'results.tsv')}")
    mark_task_finished(ctx.task_id)

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        fetch_oldest_batch,
        get_wpf_memo_info,
        download_recognition_result,
        download_dataset_info,
        execute_analysis_edge,
        execute_generate_diff,
    )
    desc.attach(main_pipe)

    ctx = Context(
        work_dir=Path('/home/<USER>/aoi-strategy/2023_03_07/src/aoi_ml/online_competitor_aoi'),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        print(e)
        mark_task_error(ctx.task_id, str(e))


if __name__ == '__main__':
    main()
