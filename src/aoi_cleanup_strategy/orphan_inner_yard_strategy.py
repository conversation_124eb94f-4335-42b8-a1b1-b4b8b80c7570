# !/usr/bin/env python3
"""
策略：召回主点无父点的内部院落。
与 isolated-inner-yard-strategy 的区别是：本策略通过 relation_bid 确定父子关系，
而 isolated-inner-yard-strategy 通过空间包含关系来确定。
"""
from dataclasses import dataclass
from dataclasses import field

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import pgsql
from src.tools import pipeline

STRATEGY_NAME = 'orphan_inner_yard'
STRATEGY_VERSION = '1.0.0'

INNER_YARD_LEVEL = 3

desc = pipeline.get_desc()


@dataclass
class Context:
    bids: list[str]
    batch: str
    # [bid]
    issues: list[str] = field(default_factory=list)


@desc()
def filter_inner_yard(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select b.poi_bid
            from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where b.poi_bid in %(bids)s and a.aoi_level = %(level)s and a.src != 'SD';
        '''
        ret = pgsql.fetch_all(conn, sql, {'bids': tuple(ctx.bids), 'level': INNER_YARD_LEVEL})
        ctx.bids = [x[0] for x in ret]

    proceed()


@desc()
def find_orphan_inner_yard(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = '''
            select bid from poi
            where bid in %s and relation_bid = '0';
        '''
        ctx.issues = [x[0] for x in pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])]

    proceed()


@desc()
def save_to_db(ctx: Context, proceed):
    results = [
        StrategyResult(
            case_id=bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail',
        )
        for bid in ctx.issues
    ]
    export_strategy_result.to_db(results)
    proceed()


def run(bids: list[str], batch: str):
    main_pipe = pipeline.Pipeline(
        filter_inner_yard,
        pipeline.print_desc(lambda x: f'inner yards: {len(x.bids)}'),
        find_orphan_inner_yard,
        save_to_db,
    )
    desc.attach(main_pipe)

    ctx = Context(
        bids=bids,
        batch=batch,
    )
    main_pipe(ctx)
