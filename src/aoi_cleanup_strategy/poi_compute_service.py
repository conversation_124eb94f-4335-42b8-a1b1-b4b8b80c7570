# !/usr/bin/env python3
import json
from typing import Tuple

import requests

SERVICE_API = "http://mapde-poi.baidu-int.com/prod/poiService/getRedundantPoiList"
MAX_RETRY_COUNT = 10

FAILED = -1


def get_redundant_poi(name: str, point: Tuple[float, float], retry_count=0):
    body = get_poi_request_body(name, point)
    res = requests.post(SERVICE_API, json=body)

    def retry():
        if retry_count < MAX_RETRY_COUNT:
            return get_redundant_poi(name, point, retry_count + 1)
        else:
            return FAILED

    if res.status_code != 200:
        return retry()

    ret = json.loads(res.text)
    if ret["errno"] != 0:
        return retry()

    data = ret["data"]
    if data["errno"] != 0:
        return FAILED

    return data["gl_bid"]


def get_poi_request_body(name, point):
    random_id = 42
    x, y = point
    return {
        "type": 16,
        "ret_num": 1,
        "linking_type": 1,
        "list": [
            {
                "id": random_id,
                "name": name,
                "pc_alias": "",
                "address": "",
                "city": "",
                "tag": "",
                "phone": "",
                "point_x": x,
                "point_y": y,
                "src_type": ""
            }
        ]
    }


def main():
    print(get_redundant_poi("公园珑湖", (12916912.5759, 3413577.75966)))


if __name__ == '__main__':
    main()
