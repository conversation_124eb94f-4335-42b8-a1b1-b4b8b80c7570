# !/usr/bin/env python3
"""
脚本：根据指定的 bid 列表，对线上的 AOI 数据进行更新，即：将母库的最新数据同步到线上。
"""
import os
import sys
from dataclasses import dataclass
from dataclasses import field
from multiprocessing.pool import Pool
from pathlib import Path

import requests
from tqdm import tqdm

root_path = os.path.abspath((os.path.dirname(__file__)) + "/../../")
sys.path.insert(0, root_path)

from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv

desc = pipeline.get_desc()


@dataclass
class Context:
    work_dir: Path
    bids: list[str] = field(default_factory=list)


def import_bids(file_path: Path):
    @desc('import bids')
    def pipe(ctx: Context, proceed):
        ctx.bids = [x[0] for x in tsv.read_tsv(file_path)]
        proceed()

    return pipe


@desc()
def filter_missing_bids(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select poi_bid from blu_face_poi
            where poi_bid in %s;
        '''
        ret = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
        ctx.bids = [x[0] for x in ret]

    proceed()


def push_bids(processes: int):
    @desc('push bids')
    def pipe(ctx: Context, proceed):
        batch_size = 100
        bids_list = [ctx.bids[i:i + batch_size] for i in range(0, len(ctx.bids), batch_size)]
        with Pool(processes) as p:
            ret = list(tqdm(p.imap(update_aoi_by_bids, bids_list), total=len(bids_list)))

        failed_bids = []
        for i, count in enumerate(ret):
            if count != len(bids_list[i]):
                failed_bids.extend(bids_list[i])

        if failed_bids:
            tsv.write_tsv(ctx.work_dir / 'push_failed_bids.txt', [[x] for x in failed_bids])

        proceed()

    return pipe


def update_aoi_by_bids(bids: list[str], retry_count=0) -> int:
    url = 'http://mapde-poi.baidu-int.com/prod/api/aoiReleaseByBid'
    bids_str = ','.join(bids)
    data = {
        'src': 'api_local',
        'bids': bids_str,
    }
    res = requests.post(url, data=data)
    if res.status_code != 200:
        if retry_count < 5:
            return update_aoi_by_bids(bids, retry_count + 1)
        else:
            return 0

    res_json = res.json()
    if res_json['code'] != 0:
        return 0

    return res_json['data']['count']


def main():
    file_path = Path('/home/<USER>/dingping/process_push_aoi/mall_bids.txt')
    pipe = pipeline.Pipeline(
        import_bids(file_path),
        filter_missing_bids,
        push_bids(processes=8),
    )
    desc.attach(pipe)
    ctx = Context(work_dir=file_path.parent)
    pipe(ctx)


if __name__ == '__main__':
    main()
