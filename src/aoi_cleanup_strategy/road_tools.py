# !/usr/bin/env python3
"""
道路工具包
"""
from typing import Generator


def search_inner_road_group(links: list[tuple[str, str, str]]) -> Generator[list[str], None, None]:
    """
    查找内部路组。
    @param links: 待搜索的 link 信息：[(link_id, s_nid, e_nid)]
    @return: 分组返回内部路 link_id。
    """
    remaining_links = links
    while remaining_links:
        seen_links = set()
        first_nid = remaining_links[0][1]
        adjacent_link_dict = get_link_dict(links)
        node_dict = {link_id: (s_nid, e_nid) for link_id, s_nid, e_nid in links}
        fill_inner_road_group(seen_links, first_nid, adjacent_link_dict, node_dict)
        yield list(seen_links)
        remaining_links = [link for link in remaining_links if link[0] not in seen_links]


def get_link_dict(links: list[tuple[str, str, str]]):
    ret = {}
    for link_id, s_nid, e_nid in links:
        if s_nid not in ret:
            ret[s_nid] = []

        if e_nid not in ret:
            ret[e_nid] = []

        ret[s_nid].append(link_id)
        ret[e_nid].append(link_id)

    return ret


def fill_inner_road_group(
        seen_links: set[str],
        nid: str,
        adjacent_link_dict: dict[str, list],
        node_dict: dict[str, tuple[str, str]]):
    for link_id in adjacent_link_dict[nid]:
        if link_id not in seen_links:
            seen_links.add(link_id)
            nid1, nid2 = node_dict[link_id]
            next_nid = nid1 if nid1 != nid else nid2
            fill_inner_road_group(seen_links, next_nid, adjacent_link_dict, node_dict)
