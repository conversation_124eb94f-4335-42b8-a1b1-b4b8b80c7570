# !/usr/bin/env python3
"""
总执行脚本：根据给定的数据源，运行本模块下的所有策略。
Details: 本脚本可以自动发现本目录下的所有策略，并执行它。能够被发现的策略模块应当具备以下约定：
- 文件名后缀为 `*_strategy.py`
- 文件中包含两个字段：`STRATEGY_NAME`和`STRATEGY_VERSION`
- 文件中包含一个函数：`run(bids: list[str], batch: str) -> None`
"""
import importlib
import os
from pathlib import Path
from typing import Callable
from typing import Iterable

from src.aoi_cleanup_strategy import import_bids
from src.aoi_cleanup_strategy.import_bids import NaviSourceType


def find_strategies() -> list[tuple[str, str, Callable[[list[str], str], None]]]:
    strategy_dir = Path(os.path.dirname(__file__))
    module_names = [x.stem for x in strategy_dir.glob('*_strategy.py')]
    full_module_names = [f'src.aoi_cleanup_strategy.{x}' for x in module_names]
    modules = [importlib.import_module(x) for x in full_module_names]
    strategy_modules = [
        x for x in modules
        if hasattr(x, 'STRATEGY_NAME') and hasattr(x, 'STRATEGY_VERSION') and hasattr(x, 'run')
    ]
    strategies = [(x.STRATEGY_NAME, x.STRATEGY_VERSION, x.run) for x in strategy_modules]
    return strategies


def navi_q3_sources():
    sources = [
        NaviSourceType.University,
        NaviSourceType.Mall,
        NaviSourceType.Stadium,
        NaviSourceType.Gallery,
        NaviSourceType.Other,
    ]
    for source in sources:
        bids = import_bids.from_navi_q3(source)
        batch = f'navi-q3-{str(source.name).lower()}'
        yield bids, batch


def navi_q2_sources():
    for source in NaviSourceType:
        bids = import_bids.from_navi_q2(source)
        batch = f'navi-q2-{str(source.name).lower()}'
        yield bids, batch


def navi_q2_other_sources():
    sources = [
        NaviSourceType.OtherPvGt500,
        NaviSourceType.OtherPvLt500,
    ]
    for source in sources:
        bids = import_bids.from_navi_q2(source)
        batch = f'navi-q2-{str(source.name).lower()}'
        yield bids, batch


def main(sources: Iterable[tuple[list[str], str]]):
    strategies = find_strategies()
    items = list(sources)

    strategy_count = len(strategies)
    for i, (name, version, runner) in enumerate(strategies):
        print(f'=============================================================')
        print(f'strategy ({i + 1:0{len(str(strategy_count))}}/{strategy_count}): {name} (v{version})')
        for bids, batch in items:
            print(f'-------------------------------------------------------------')
            print(f"'{batch}': {len(bids)=}")
            runner(bids, batch)


if __name__ == '__main__':
    # main(navi_q2_sources())
    # main(navi_q2_other_sources())
    main(navi_q3_sources())
