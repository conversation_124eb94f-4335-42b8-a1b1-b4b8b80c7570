# !/usr/bin/env python3
"""
将抓取到的竞品 AOI 存入竞品库。
"""
import argparse
import datetime
import json
from dataclasses import dataclass, field
from pathlib import Path

import pymysql
from tqdm import tqdm

from src.aoi_cleanup_strategy.mesh_tools import get_mesh_id
from src.tools import pipeline, pgsql
from src.tools.afs_tool import AfsTool
from src.tools.conf_tools import get_mysql_conf

desc = pipeline.get_desc()


@dataclass
class CompetitorRecord:
    """
    竞品信息
    """
    bid: str
    competitor_id: str
    name: str
    address: str
    display_x: float
    display_y: float
    mesh_id: str
    wkt: str
    batch: str = ''


@dataclass
class CrawlInfo:
    """
    抓取信息
    """
    bid: str
    competitor_id: str
    content: dict
    error: str
    success: bool


@dataclass
class CrawlTask:
    """
    抓取任务
    """
    json_file_path: Path
    task_id: str
    batch_id: str = ''
    src: str = ''
    items: list[CrawlInfo] = field(default_factory=list)

    def __post_init__(self):
        with open(self.json_file_path, 'r') as f:
            task_json = json.load(f)
            self.batch_id = task_json['batch_id']
            self.src = task_json['src']
            items = task_json['items']
            for item in items:
                self.items.append(CrawlInfo(
                    bid=item['bid'],
                    competitor_id=item['competitor_id'],
                    content=item['content'],
                    error=item['error'],
                    success=item['success'],
                ))

        self.json_file_path.unlink(missing_ok=True)

    def get_insert_sql(self, record: CompetitorRecord):
        sql = f'''
            insert into aoi_intelligence_history(
                bid,
                intel_id,
                name,
                address,
                display_x,
                display_y,
                mesh_id,
                batch,
                geom
            )
            select %s, %s, %s, %s, %s, %s, %s, %s, st_geomfromtext(%s, 4326)
            where not exists(select 1 from aoi_intelligence_history where bid = %s and intel_id = %s)
        '''
        data = tuple([
            record.bid,
            record.competitor_id,
            record.name,
            record.address,
            record.display_x,
            record.display_y,
            record.mesh_id,
            self.batch_id,
            record.wkt,
            record.bid,
            record.competitor_id,
        ])

        return sql, data


@dataclass
class Context:
    """
    脚本执行上下文
    """
    mode: str
    url: str
    work_dir: Path
    afs_path: str
    crawl_task: CrawlTask = None
    competitor_records: list[CompetitorRecord] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


def create_db_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf()
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def execute_db_command(sql):
    """
    执行 mysql 数据库命令
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(sql)
            conn.commit()


def mark_task_error(task_id, status, memo=''):
    """
    标记任务失败
    """
    execute_db_command(f"update crawl_task set status={status}, memo='{memo}' where id={task_id}")


def mark_task_start(task_id):
    """
    标记任务开始执行
    """
    execute_db_command(f'''
        update crawl_task 
        set status = 3
        where id={task_id}
    ''')


def mark_task_finished(task_id):
    """
    标记任务执行完成
    """
    execute_db_command(f'''
        update crawl_task 
        set finished_time='{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}',
            status = 4
        where id={task_id}
    ''')


def download_oldest_task_from_db():
    """
    下载最旧的任务
    """
    with create_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(f'''
                select id, result_url
                from crawl_task
                where status = 2            
                order by request_time
                limit 1
            ''')
            task = cursor.fetchone()
            if task:
                task_id, url = task
                mark_task_start(task_id)
                return task_id, url

    return None


def fetch_afs_task(ctx: Context):
    """
    从 afs 下载任务
    """
    afs = AfsTool()
    afs.get(ctx.url, ctx.work_dir)
    afs_file_path = Path(ctx.url).name
    local_file_path = ctx.work_dir / afs_file_path
    if not local_file_path.exists():
        print('无任务')
        return

    ctx.crawl_task = CrawlTask(task_id='', json_file_path=local_file_path)


def fetch_db_task(ctx: Context):
    """
    从数据库下载任务
    """
    # 获取最旧的、未执行的任务。
    task = download_oldest_task_from_db()
    if not task:
        print('无任务')
        return

    task_id, url = task
    afs = AfsTool()
    afs.get(url, ctx.work_dir)
    afs_file_path = Path(url).name
    local_file_path = ctx.work_dir / afs_file_path
    if not local_file_path.exists():
        mark_task_error(task_id, -1, f'无法下载：{url}')
        return
    ctx.crawl_task = CrawlTask(task_id=task_id, json_file_path=local_file_path)


def parse_aoi_locations(aoi_locations):
    """
    解析 aoi 坐标
    """
    points = [' '.join(x.split(',')[::-1]) for x in aoi_locations]
    if points[0] != points[-1]:
        points.append(points[0])

    return f"POLYGON (({','.join(points)}))"


def parse_crawl_info(crawl_info: CrawlInfo):
    """
    解析抓取结果
    """
    # REF: https://ku.baidu-int.com/d/YvPneKP2cfDs7l
    if len(crawl_info.content) == 0:
        return
    data = crawl_info.content['data']
    bid = data['source_id']
    pois = data['pois']
    for poi in pois:
        poi_id = poi['id']
        if poi_id == '':
            continue
        poi_location = poi['location']
        display_x = float(poi_location['lng'])
        display_y = float(poi_location['lat'])
        aoi = poi.get('aoi', None)
        if aoi is None:
            continue
        aoi_locations = aoi.get('locations', None)
        if aoi_locations is None:
            continue
        aoi_wkt = parse_aoi_locations(aoi_locations)
        yield CompetitorRecord(
            bid=bid,
            competitor_id=poi['id'],
            name=poi['name'],
            address=poi['address'],
            display_x=display_x,
            display_y=display_y,
            mesh_id=get_mesh_id(display_x, display_y),
            wkt=aoi_wkt,
        )


@desc()
def fetch_crawl_task(ctx: Context, proceed):
    """
    获取抓取任务
    """
    if ctx.mode == 'db':
        fetch_db_task(ctx)
    elif ctx.mode == 'afs':
        fetch_afs_task(ctx)

    if ctx.crawl_task is None:
        return

    proceed()


@desc()
def parse_crawl_task(ctx: Context, proceed):
    """
    解析抓取任务
    """
    for item in tqdm(ctx.crawl_task.items, total=len(ctx.crawl_task.items), desc='解析抓取结果'):
        ctx.competitor_records.extend(parse_crawl_info(item))

    proceed()


@desc()
def save_competitor_records_to_db(ctx: Context, proceed):
    """
    将抓取结果保存到数据库
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        with conn.cursor() as cursor:
            try:
                for record in ctx.competitor_records:
                    sql, data = ctx.crawl_task.get_insert_sql(record)
                    cursor.execute(sql, data)

                conn.commit()
                mark_task_finished(ctx.crawl_task.task_id)
            except Exception as e:
                conn.rollback()
                print(e)
                mark_task_error(ctx.crawl_task.task_id, -1, record.wkt)

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser('save competitor aoi')
    parser.add_argument(
        '--mode',
        dest='mode',
        help='运行模式 {(db) | afs}',
        type=str,
        default='db',
        required=False,
    )
    parser.add_argument(
        '--url',
        dest='url',
        help='任务信息文件地址',
        type=str,
        default=None,
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        fetch_crawl_task,
        parse_crawl_task,
        save_competitor_records_to_db,
    )
    desc.attach(main_pipe)

    ctx = Context(
        mode=args.mode,
        url=args.url,
        work_dir=Path('cache/save_competitor_aoi'),
        afs_path='/user/map-data-streeview/aoi-ml/crawl',
    )
    try:
        main_pipe(ctx)
    except Exception as e:
        print(e)
        mark_task_error(ctx.crawl_task.task_id, -1, str(e))


if __name__ == '__main__':
    main(parse_args())
