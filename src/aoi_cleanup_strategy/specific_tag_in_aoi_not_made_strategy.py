# !/usr/bin/env python3
"""
策略：在 aoi 内部，特定 tag 的 poi 未制作边框。
"""
from dataclasses import dataclass, field

from tqdm import tqdm

from src.aoi_cleanup_strategy import export_strategy_result
from src.aoi_cleanup_strategy.export_strategy_result import StrategyResult
from src.tools import pipeline, pgsql

STRATEGY_NAME = 'specific_tag_in_aoi_not_made'
STRATEGY_VERSION = '1.0.0'
SPECIFIC_TAGS = {
    '政府机构;中央机构',
    '政府机构;政治教育机构',
    '政府机构;涉外机构',
    '政府机构;社会团体',
    '#政府机构;其他',
    '政府机构;民主党派',
    '政府机构;居民委员会',
    '政府机构;行政单位',
    '政府机构;公检法机构',
    '政府机构;各级政府',
    '政府机构;福利机构',
    '政府机构;党派团体',
    '政府机构',
    '#运动健身;体育场馆',
    '运动健身;健身中心',
    '运动健身;极限运动场所',
    '医疗;综合医院',
    '医疗;专科医院',
    '#医疗;诊所',
    '#医疗;体检机构',
    '医疗;疗养院',
    '医疗;疾控中心',
    '医疗;急救中心',
    '休闲娱乐;休闲广场',
    '休闲娱乐;农家院',
    '休闲娱乐;剧院',
    '休闲娱乐;度假村',
    '文化传媒;展览馆',
    '文化传媒;文化宫',
    '文化传媒;美术馆',
    '文化传媒;广播电视',
    '生活服务;物流公司',
    '#生活服务;公用事业',
    '#汽车服务;其他',
    '旅游景点;植物园',
    '旅游景点;游乐园',
    '#旅游景点;文物古迹',
    '旅游景点;寺庙',
    '旅游景点;水族馆',
    '旅游景点;景点',
    '旅游景点;教堂',
    '旅游景点;公园',
    '旅游景点;动物园',
    '旅游景点;博物馆',
    '旅游景点;风景区',
    '酒店;星级酒店',
    '#金融;银行',
    '教育培训',
    '教育培训;中学',
    '教育培训;幼儿园',
    '教育培训;小学',
    '教育培训;科研机构',
    '教育培训;科技馆',
    '教育培训;高等院校',
    '教育培训;成人教育',
    '交通设施;长途汽车站',
    '#交通设施;加油加气站',
    '交通设施;火车站',
    '#交通设施;港口',
    '#交通设施;服务区',
    '#交通设施;飞机场',
    '购物;市场',
    '购物;购物中心',
    '购物;百货商场',
    '公司企业;园区',
    '#公司企业;农林园艺',
    '#公司企业;公司',
    '#公司企业;厂矿',
    '#房地产;住宅区',
    '#房地产;写字楼',
}
IGNORED_SHOW_TAGS = {
    '快递网点',
    '快递服务点',
    '快递自提点',
    '物流',
    '羽毛球场馆',
    '游泳馆',
    '台球馆',
    '健身房',
    '警务室',
    '篮球场',
    '武术馆',
    '击剑馆',
    '网球场',
    '成人教育',
}

desc = pipeline.get_desc()


@dataclass
class AoiRecord:
    """
    用于存储 AOI 信息。
    """
    face_id: str
    bid: str
    wkt: str


@dataclass
class Context:
    """
    策略上下文。
    """
    batch: str
    bids: list[str]
    aoi_records: list[AoiRecord] = field(default_factory=list)
    issues: list[str] = field(default_factory=list)


@desc()
def import_aoi_records(ctx: Context, proceed):
    """
    导入 AOI 记录。
    """
    face_id_set = set()
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for bid in tqdm(ctx.bids, total=len(ctx.bids), desc='导入 aoi'):
            sql = f'''
                select face.face_id, st_astext(face.geom) 
                from blu_face_poi relation
                inner join blu_face face
                on face.face_id = relation.face_id
                where relation.poi_bid = %s 
                limit 1;
            '''
            record = pgsql.fetch_one(conn, sql, (bid,))
            if record:
                face_id, wkt = record
                if face_id not in face_id_set:
                    ctx.aoi_records.append(AoiRecord(
                        face_id=face_id,
                        bid=bid,
                        wkt=wkt,
                    ))
                    face_id_set.add(face_id)

    proceed()


@desc()
def find_issues(ctx: Context, proceed):
    """
    查找问题 AOI。
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        for aoi in tqdm(ctx.aoi_records, total=len(ctx.aoi_records), desc='aoi 过滤'):
            sql = f'''
                select bid, std_tag, show_tag
                from poi 
                where st_contains(st_geomfromtext(%s, 4326), geometry) and
                      bid != %s and
                      std_tag in %s and
                      show_tag not in %s and
                      relation_bid != %s and
                      click_pv > 10
                limit 1;
            '''
            record = pgsql.fetch_one(
                conn,
                sql,
                (aoi.wkt, aoi.bid, tuple(SPECIFIC_TAGS), tuple(IGNORED_SHOW_TAGS), aoi.bid)
            )
            if record:
                ctx.issues.append(aoi.bid)

    proceed()


@desc()
def save_to_db(ctx: Context, proceed):
    """
    保存结果到数据库。
    """
    results = [
        StrategyResult(
            case_id=bid,
            batch=ctx.batch,
            strategy_name=STRATEGY_NAME,
            strategy_version=STRATEGY_VERSION,
            result='fail',
        )
        for bid in ctx.issues
    ]
    export_strategy_result.to_db(results)
    proceed()


def run(bids: list[str], batch: str):
    """
    运行策略。
    """
    main_pipe = pipeline.Pipeline(
        import_aoi_records,
        find_issues,
        save_to_db,
    )
    desc.attach(main_pipe)
    ctx = Context(
        bids=bids,
        batch=batch,
    )
    main_pipe(ctx)
