# !/usr/bin/env python3
"""
未知层级策略：母库中有一部分 blu_face 的 aoi_level 为 0 (unknown)，需将其中可处理的部分筛选出来，统一赋值为 2 (基础院落)。
注意：此脚本会修改数据库！！！
"""
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from pathlib import Path

from osgeo import ogr
from tqdm import tqdm

from src.aoi_cleanup_strategy import blu_record
from src.tools import linq
from src.tools import mesh_locker
from src.tools import pgsql
from src.tools import pipeline
from src.tools import tsv
from src.tools import utils

BASIC_AOI = 2

desc = pipeline.get_desc()


@dataclass
class Context:
    work_dir: Path
    # [(face_id, mesh_id, geom)]
    blu_faces: list[tuple[str, str, str]] = field(default_factory=lambda: [])
    # [(face_id, mesh_id)]
    solvable_blu_faces: list[tuple[str, str]] = field(default_factory=lambda: [])
    unsolvable_bids: list[str] = field(default_factory=lambda: [])


@desc()
def search_aoi_with_unknown_level(ctx: Context, proceed):
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = f'''
            select face_id, mesh_id, ST_AsText(geom) from blu_face
            where aoi_level = 0 and src != 'SD';
        '''
        ctx.blu_faces = pgsql.fetch_all(conn, sql)

    proceed()


@desc()
def search_solvable_aoi(ctx: Context, proceed):
    def contains(geom1, geom2) -> bool:
        # noinspection PyBroadException
        try:
            return geom1.Contains(geom2)
        except:
            return False

    def is_solvable(target_geom, other_geom):
        """
        判断该未知层级的 AOI 是否可处理：与其它基础院落交并比均小于 0.2，且不得包含其它基础院落。
        """
        target_geom = ogr.CreateGeometryFromWkt(target_geom)
        other_geom = ogr.CreateGeometryFromWkt(other_geom)
        iou = utils.calc_iou(target_geom, other_geom)
        return iou <= 0.2 and not contains(target_geom, other_geom)

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for face_id, mesh_id, geom in tqdm(ctx.blu_faces):
            sql = f'''
                select ST_AsText(geom) from blu_face
                where aoi_level = {BASIC_AOI} 
                      and face_id != '{face_id}' 
                      and src!= 'SD'
                      and ST_Intersects(
                          geom,
                          ST_GeomFromText('{geom}', 4326)
                      );
            '''
            ret = pgsql.fetch_all(conn, sql)
            if not ret or all(is_solvable(geom, x[0]) for x in ret):
                ctx.solvable_blu_faces.append((face_id, mesh_id))

    proceed()


@desc()
def query_unsolvable_bids(ctx: Context, proceed):
    all_face_ids = {face_id for face_id, _, _ in ctx.blu_faces}
    solvable_face_ids = set(face_id for face_id, _ in ctx.solvable_blu_faces)
    unsolvable_face_ids = all_face_ids - solvable_face_ids

    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as back_conn,
        pgsql.get_connection(pgsql.POI_CONFIG) as poi_conn,
    ):
        sql = '''
            select poi_bid from blu_face_poi
            where face_id in %s;
        '''
        ret = pgsql.fetch_all(back_conn, sql, [tuple(unsolvable_face_ids)])
        unsolvable_bids = [x[0] for x in ret]

        sql = '''
            select bid from poi
            where bid in %s;
        '''
        ret = pgsql.fetch_all(poi_conn, sql, [tuple(unsolvable_bids)])
        valid_unsolvable_bids = [x[0] for x in ret]
        ctx.unsolvable_bids.extend(valid_unsolvable_bids)

    proceed()


@desc()
def save_data(ctx: Context, proceed):
    tsv.write_tsv(ctx.work_dir / 'unknown_aoi_level_blu_face.tsv', ctx.blu_faces)
    tsv.write_tsv(ctx.work_dir / 'unknown_aoi_level_solvable_blu_face.tsv', ctx.solvable_blu_faces)
    tsv.write_tsv(ctx.work_dir / 'unknown_aoi_level_unsolvable_bid.tsv', ([x] for x in ctx.unsolvable_bids))
    proceed()


@desc()
def modify_aoi_level(ctx: Context, proceed):
    mesh_group = linq.group_by(ctx.solvable_blu_faces, lambda x: x[1])
    todo_queue = list(
        (mesh_id, [face_id for face_id, _ in blu_faces])
        for mesh_id, blu_faces in mesh_group.items()
    )

    def update_aoi_level_to_blu_face(face_ids: list[str]):
        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            sql = '''
                update blu_face set aoi_level = 2
                where face_id in %s;
            '''
            pgsql.execute(conn, sql, [tuple(face_ids)])

        return True

    def on_completed(mesh_id, face_ids):
        date_str = datetime.now().strftime('%Y%m%d')
        blu_record.snapshot(face_ids, f'auto-aoi_level-{date_str}')
        print(f'success mesh: {mesh_id}')

    mesh_locker.consume_mesh_task_queue(
        todo_queue,
        'aoi-level',
        update_aoi_level_to_blu_face,
        on_completed,
    )

    proceed()


def main():
    work_dir = Path('/home/<USER>/dingping/process_unknown_aoi_level')

    main_pipe = pipeline.Pipeline(
        search_aoi_with_unknown_level,
        search_solvable_aoi,
        query_unsolvable_bids,
        save_data,
        modify_aoi_level,  # 注意：此 pipe 将修改母库！
    )
    desc.attach(main_pipe)

    ctx = Context(work_dir=work_dir)
    main_pipe(ctx)


if __name__ == '__main__':
    main()
