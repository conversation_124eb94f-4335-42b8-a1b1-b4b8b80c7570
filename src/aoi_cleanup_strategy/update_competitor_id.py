# !/usr/bin/env python3
"""
抓取竞品 Id。
"""
import argparse
import datetime
import shutil
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql
from src.tools.afs_tool import AfsTool
from src.tools.notice_tool import send_hi

desc = pipeline.get_desc()

INFOFLOW_TOKEN = 'd5070dd11c100081e2110cb89f9e71680'


@dataclass
class Context:
    mode: str
    src: str
    work_dir: Path
    afs_path: str
    table_name_prefix: str
    table_name: str
    batch: str = ''
    competitor_id_folder: Path = Path()
    competitor_data: list[str] = field(default_factory=list)
    valid_id_prefix: list[str] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

        if self.src == 'dt':
            self.valid_id_prefix.append('dtc')
        elif self.src == 'sj':
            self.valid_id_prefix.append('sjc')
        elif self.src == 'any':
            self.valid_id_prefix.append('dtc')
            self.valid_id_prefix.append('sjc')


@desc()
def download_competitor_id(ctx: Context, proceed):
    afs = AfsTool()
    file_name = None
    date = (datetime.date.today() - datetime.timedelta(days=1)).strftime('%Y%m%d')
    ctx.batch = f'{ctx.mode}_{ctx.src}_{date}'

    if ctx.mode == 'incr':
        file_name = f"jingpin_bid_incr_{date}"
    elif ctx.mode == 'full':
        file_name = f"jingpin_bid_{date}"

    if file_name is not None:
        ctx.competitor_id_folder = ctx.work_dir / file_name
        if ctx.competitor_id_folder.exists():
            shutil.rmtree(ctx.competitor_id_folder)

        afs.get(f'{ctx.afs_path}/{file_name}', ctx.work_dir)
        if ctx.competitor_id_folder.exists():
            try:
                proceed()
            except Exception as e:
                send_hi(f'三方 id 同步失败：{e}', atuserids=['chenjie02_cd'], token=INFOFLOW_TOKEN)
                raise
        else:
            error = "三方 id 今日无更新！"
            send_hi(error, atuserids=['chenjie02_cd'], token=INFOFLOW_TOKEN)
            raise Exception(error)


@desc()
def load_competitor_id(ctx: Context, proceed):
    paths = list(ctx.competitor_id_folder.glob('part-*'))
    for path in tqdm(paths, total=len(paths), desc='加载竞品 id'):
        with open(path, 'r', ) as f:
            ctx.competitor_data.extend(f.readlines())

    proceed()


def create_table(table_name):
    """
    创建数据库表
    """
    create_table_sql = f'''
        create table if not exists {table_name}(
            id bigserial primary key, 
            bid varchar(32) not null, 
            competitor_id varchar(32) not null, 
            src varchar(8) not null,
            batch varchar(32) not null
        );
    '''
    clear_table_sql = f'''delete from {table_name};'''
    create_index_sql = f'''
        create index if not exists {table_name}_bid_index 
        on {table_name}
        using btree (bid);
        
        create index if not exists {table_name}_competitor_id_index 
        on {table_name}
        using btree (competitor_id);
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(create_table_sql)
        poi_stab.execute(clear_table_sql)
        poi_stab.execute(create_index_sql)


def update_view(ctx: Context):
    """
    更新数据库视图
    """
    create_view_sql = f'''
        create or replace view {ctx.table_name_prefix} as
        select * from {ctx.table_name};
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(create_view_sql)


def drop_table(table_prefix: str, expire_day: int):
    """
    删除过期数据库表
    """
    sql = f"""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name LIKE '{table_prefix}_%';
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    tables = [(x[0], x[0].split("_")[-1]) for x in ret]
    tables = [(table_name, datetime.datetime.strptime(day, "%Y%m%d")) for table_name, day in tables]

    today = datetime.datetime.now()
    expire = today - datetime.timedelta(days=expire_day)
    expired_tables = [table_name for table_name, day in tables if day < expire]
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for table_name in expired_tables:
            # NOTE: 此处不要把 {table_name} 写成 %s 的形式，pgsql 会把它当成字符串，加上引号，导致 sql 语法错误
            sql_clear = f"""
                drop table if exists {table_name};
            """
            pgsql.execute(conn, sql_clear)

    return expired_tables


def save_competitor_id_to_file(ctx: Context):
    """
    将竞品数据保存到文件
    """
    data_path = ctx.work_dir / 'insert_data.tsv'

    with open(ctx.work_dir / 'insert_data.tsv', 'w', encoding='utf-8') as fw:
        for item in tqdm(ctx.competitor_data, total=len(ctx.competitor_data), desc='入库'):
            bid, competitor_info = tuple(item.strip().split('\t'))
            if competitor_info == '':
                continue

            competitor_id_infos = competitor_info.split(',')
            for competitor_id_info in competitor_id_infos:
                if '_' not in competitor_id_info:
                    continue

                id_prefix, competitor_id = tuple(competitor_id_info.split('_'))
                if id_prefix in ctx.valid_id_prefix:
                    fw.write(f'{bid}\t{competitor_id}\t{id_prefix}\t{ctx.batch}\n')

    return data_path


@desc()
def save_competitor_id(ctx: Context, proceed):
    """
    竞品数据入库
    """
    create_table(ctx.table_name)

    with (
        open(save_competitor_id_to_file(ctx), 'r', encoding='utf-8') as f,
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cur,
    ):
        # noinspection PyTypeChecker
        cur.copy_from(f, table=ctx.table_name, columns=(
            'bid',
            'competitor_id',
            'src',
            'batch',
        ))

    update_view(ctx)
    drop_table(ctx.table_name_prefix, 1)
    proceed()


@desc()
def clear_cache(ctx: Context, proceed):
    """
     清空缓存。
    """
    if ctx.competitor_id_folder.exists():
        shutil.rmtree(ctx.competitor_id_folder)

    insert_data_path = ctx.work_dir / 'insert_data.tsv'
    if insert_data_path.exists():
        insert_data_path.unlink()

    proceed()


def parse_args():
    parser = argparse.ArgumentParser('update_competitor_id')
    parser.add_argument(
        '--mode',
        dest='mode',
        choices=['full'],
        help='运行模式',
        type=str,
        default='full',
        required=True,
    )
    parser.add_argument(
        '--src',
        dest='src',
        help='竞品源 {dt | (sj) | any}',
        type=str,
        default='sj',
        required=True,
    )
    return parser.parse_args()


def main(args):
    main_pipe = pipeline.Pipeline(
        download_competitor_id,
        load_competitor_id,
        save_competitor_id,
        clear_cache,
    )
    desc.attach(main_pipe)

    ctx = Context(
        mode=args.mode,
        src=args.src,
        table_name_prefix='competitor_id',
        table_name=f"competitor_id_{datetime.date.today().strftime('%Y%m%d')}",
        work_dir=Path('cache/update_competitor_id'),
        afs_path='/user/map-data-streeview/aoi-ml/competitor_id'
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
