"""
ugc上报策略自动解绑处理
"""
import os
import sys
import dataclasses

from pathlib import Path
from typing import List

current_dir = Path(os.path.abspath(os.path.dirname(__file__)))
root_dir = current_dir.parents[1]
print('代码路径:====', root_dir)
sys.path.insert(0, root_dir.as_posix())

from src.tools.mysql_tool import MysqlTool
from src.tools import pipeline
from src.batch_process import flow_process_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pgsql
import datetime
import pandas as pd
import tqdm

today = datetime.date.today()
save_data = current_dir / 'auto_ugc_qb_unbind' / today.strftime('%Y%m%d')
save_data.mkdir(parents=True, exist_ok=True)

desc = pipeline.get_desc()

MYSQL_NAME = 'beeflow_rw'
STRATEGY_STATUS_DEFAULT = 0
STRATEGY_STATUS_HANDLING = 1
STRATEGY_STATUS_HANDLED = 2
STRATEGY_STATUS_HANDLE_FAILED = -1


@dataclasses.dataclass
class UgcQbData:
    """
    ugc_qb_data
    """
    id: int
    main_bid: str
    face_id: str = ''


@dataclasses.dataclass
class Ctx:
    """
    ctx
    """
    qb_list: List[UgcQbData] = dataclasses.field(default_factory=list)


def set_handle_success(_id, face_id):
    """
    设置处理成功
    :param _id: 任务id
    :param face_id: face_id
    """
    with MysqlTool(name=MYSQL_NAME) as mysql:
        sql = 'update aoi_ugc_main_geom_change_info set strategy_status = %s, face_id = %s where id = %s'
        cursor = mysql.connection.cursor()
        cursor.execute(sql, [STRATEGY_STATUS_HANDLED, face_id, _id])
        mysql.connection.commit()


def set_handle_failed(_id):
    """
    设置处理失败
    :param _id: 任务id
    :param msg: 备注信息
    :return:
    """
    with MysqlTool(name=MYSQL_NAME) as mysql:
        sql = 'update aoi_ugc_main_geom_change_info set strategy_status = %s where id = %s'
        cursor = mysql.connection.cursor()
        cursor.execute(sql, [STRATEGY_STATUS_HANDLED, _id])
        mysql.connection.commit()


def load_data(ctx: Ctx):
    """
    加载数据
    """
    with MysqlTool(name=MYSQL_NAME) as mysql:
        sql = 'select id, bid, strategy_status' \
              ' from aoi_ugc_main_geom_change_info where strategy_status = %s limit 100 '
        with mysql.connection.cursor() as cursor:
            cursor.execute(sql, [STRATEGY_STATUS_DEFAULT])
            lines = cursor.fetchall()
            if len(lines) == 0:
                print('没有需要处理的数据')
                return
            for (_id, bid, strategy_status) in lines:
                qb_data = UgcQbData(id=_id, main_bid=bid, face_id='')
                ctx.qb_list.append(qb_data)


def load_face_id(ctx: Ctx):
    """
    加加载face_id
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
        for item in tqdm.tqdm(ctx.qb_list, desc="加载face_id"):
            print("开始处理:", item)
            sql = 'select face_id from blu_face_poi where poi_bid = %s'
            res = back_stabilizer.fetch_one(sql, [item.main_bid])
            if not res:
                set_handle_failed(item.id)
                continue
            item.face_id = res[0]


def save_result(ctx: Ctx):
    """
    保存结果
    """
    now = datetime.datetime.now().strftime('%Y%m%d_%H%M')
    lines = [dataclasses.asdict(x) for x in ctx.qb_list]
    pd.DataFrame(lines).to_csv(save_data / f'result_{now}.csv', index=False)


def handle_unbind(ctx: Ctx):
    """
    处理解绑
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
        for item in tqdm.tqdm(ctx.qb_list, desc="处理解绑"):
            result = flow_process_aoi.delete_main_poi_of_aoi(back_stabilizer, item.face_id)
            if result.success:
                set_handle_success(item.id, item.face_id)


if __name__ == '__main__':
    _ctx = Ctx()
    load_data(_ctx)
    load_face_id(_ctx)
    handle_unbind(_ctx)
    save_result(_ctx)
