"""
策略自动处理
涉及到的表：
其中包含

nothing 无需处理
repeat_b_to_c  判重数据B转C
repeat_change_main_bid 判重换绑主点
"""
import os
import sys
import dataclasses
import json

from pathlib import Path
from typing import List

current_dir = Path(os.path.abspath(os.path.dirname(__file__)))
root_dir = current_dir.parents[1]
print('代码路径:====', root_dir)
sys.path.insert(0, root_dir.as_posix())

from src.tools.mysql_tool import MysqlTool
from src.tools import pipeline
from src.batch_process import flow_process_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pgsql

desc = pipeline.get_desc()
WORK_TYPE_STRATEGY = 1  # 策略处理

HANDLE_STATUS_STRATEGY = 1  # 策略处理中
HANDLE_STATUS_SUCCESS = 99  # 处理完成
HANDLE_STATUS_FAIL = -1  # 处理完成

# 策略自动化处理的相关操作
STRATEGY_REPEAT_CHANGE_MAIN_BID = 'repeat_change_main_bid'  # 换绑主点
STRATEGY_B_TO_C = 'repeat_b_to_c'  # 策略b转c
STRATEGY_NOTHING = 'nothing'

# 测试环境改 beeflow_test，线上改 beeflow_rw
# MYSQL_NAME = 'beeflow_test'
MYSQL_NAME = 'beeflow_rw'


@dataclasses.dataclass
class AoiProtectData:
    """
    Aoi失效保护情报
    """
    id: int
    bid: str
    memo: str


@dataclasses.dataclass
class Ctx:
    """
    上下文
    """
    data_list: List[AoiProtectData] = dataclasses.field(default_factory=list)


@desc("开始加载数据")
def load_wait_handle_data(ctx: Ctx, proceed):
    """
    加载待处理的数据
    :param ctx: ctx
    :param proceed: 下一步
    """
    with MysqlTool(name=MYSQL_NAME) as mysql:
        sql = 'select' \
              ' id, bid, memo,strategy_memo  ' \
              ' from aoi_protect_information' \
              ' where work_type=%s and handle_status = %s limit 30'
        cursor = mysql.connection.cursor()
        cursor.execute(sql, [WORK_TYPE_STRATEGY, HANDLE_STATUS_STRATEGY])
        ret = cursor.fetchall()
        if len(ret) < 1:
            print("无数据需要处理， 结束")
            return
        for (_id, bid, memo, strategy_memo) in ret:
            ctx.data_list.append(AoiProtectData(id=_id, bid=bid, memo=memo))

    proceed()


def set_handle_success(_id):
    """
    设置处理成功
    :param _id: 任务id
    """
    with MysqlTool(name=MYSQL_NAME) as mysql:
        sql = 'update aoi_protect_information set handle_status = %s where id = %s'
        cursor = mysql.connection.cursor()
        cursor.execute(sql, [HANDLE_STATUS_SUCCESS, _id])
        mysql.connection.commit()


def set_handle_failed(_id, msg):
    """
    设置处理失败
    :param _id: 任务id
    :param msg: 备注信息
    :return:
    """
    with MysqlTool(name=MYSQL_NAME) as mysql:
        sql = 'update aoi_protect_information set handle_status = %s, strategy_memo = %s where id = %s'
        cursor = mysql.connection.cursor()
        cursor.execute(sql, [HANDLE_STATUS_FAIL, msg, _id])
        mysql.connection.commit()


@desc("开始处理策略")
def handle_strategy(ctx: Ctx, proceed):
    """
    处理策略
    :param ctx: ctx
    :param proceed: 下一步
    """
    if len(ctx.data_list) < 1:
        proceed()
        return
    for item in ctx.data_list:
        print(f"开始处理:{item}")
        memo_data = json.loads(item.memo)
        if 'action' not in memo_data or memo_data['action'] == STRATEGY_NOTHING:
            # 策略判断不需要特殊处理，直接标记结束
            set_handle_success(item.id)
            continue
        elif memo_data['action'] == STRATEGY_B_TO_C:
            # 直接b转c， 需要两个参数
            action_data = memo_data['data']['blu_face_update_src']
            if not action_data or len(action_data) != 2:
                set_handle_failed(item.id, 'action_data 错误，无法处理')
                continue
            face_id, src = tuple(action_data)
            with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
                result = flow_process_aoi.update_src_of_aoi(back_stabilizer, face_id, src)
                if not result.success:
                    print(f"处理失败:{item.id}", result.msg)
                    set_handle_failed(item.id, f'生成流式入库失败: {result.msg}')
                else:
                    set_handle_success(item.id)
        elif memo_data['action'] == STRATEGY_REPEAT_CHANGE_MAIN_BID:
            # 换绑主点，需要四个参数
            blu_face_poi_update_bid = memo_data['data']['blu_face_poi_update_bid']
            if len(blu_face_poi_update_bid) != 4:
                set_handle_failed(item.id, 'blu_face_poi_update_bid 格式错误，无法处理')
                continue
            face_id, src, poi_bid, poi_mid = tuple(blu_face_poi_update_bid)
            with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
                result = flow_process_aoi.update_main_poi_of_aoi(back_stabilizer, face_id, src, poi_bid, poi_mid)
                if not result.success:
                    print(f"处理失败:{item.id}", result.msg)
                    set_handle_failed(item.id, f'生成流式入库失败: {result.msg}')
                else:
                    print("处理成功")
                    set_handle_success(item.id)
        else:
            set_handle_failed(item.id, '未知的策略类型')
    proceed()


def build_strategy_pipeline():
    """
    构建pipes
    """
    pipes = pipeline.Pipeline(
        load_wait_handle_data,
        handle_strategy,
    )
    desc.attach(pipes)
    return pipes


if __name__ == '__main__':
    args = sys.argv
    if len(args) > 1:
        action = int(args[1])
        if action == 'strategy':
            _ctx = Ctx()
            _pipes = build_strategy_pipeline()
            _pipes(_ctx)
        else:
            print('使用方式: python auto_aoi_protect_handle.py <action>')
            print('    其中：<action> 包括: strategy-策略处理')
    else:
        print('请输入参数：strategy')
