"""
统计 AOI Studio 客户端的各项请求耗时，输出概览指标
"""
import sys
from datetime import timedelta, datetime

import numpy as np

from src.tools import linq, pgsql

IMAGE_SUFFIXES = ["jpg", "jpeg", "png", "bmp", "gif", "webp", "tiff", "raw"]


def query_special_paths() -> list[str]:
    """
    统计是按 host+path 分组的，这里 path 的用意是区分 API，但有一些 path 的末尾中包含参数，所以要截断。
    最终结果是：path 中包含特殊路径结尾字符后面的部分，会被丢弃。
    为了能够实时增加特殊路径而无需重新发版 aoi-ml 项目，这里将配置写入数据库中。
    """
    sql = """
        select path from aoi_studio_response_time_special_path;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    paths = [x[0] for x in ret]
    # 按长度排序，先处理长从路径，以确保不会出现因 path 相互包含而导致的解析错误
    paths.sort(key=len, reverse=True)
    return paths


def query_response_times(config: dict, min_ago: datetime):
    """
    查询指定时间段内，AOI Studio 客户端的响应时间数据
    """
    sql = """
        select event_params from track_log_interface
        where client_ts > %s and event_id = 'Network_ResponseTime';
    """

    with pgsql.get_connection(config) as conn:
        timestamp = int(min_ago.timestamp() * 1000)
        ret = pgsql.fetch_all(conn, sql, [str(timestamp)])

    cases = [x[0] for x in ret]
    return cases


def parse_case(info: any, special_paths: list[str]):
    """
    解析单个案例的响应时间信息
    """
    host = info["host"]
    path = info["path"]
    path = truncate_path(path, special_paths)
    path = truncate_image_path(path)

    timespan = info["timespan_ms"]
    error = info["error"]
    return host, path, not error, float(timespan)


def truncate_path(path: str, special_paths: list[str]):
    """
    截断 path 中的参数，以便于统计
    """
    for special_path in special_paths:
        if special_path in path:
            index = path.index(special_path)
            path = path[: index + len(special_path)]
            break

    return path


def truncate_image_path(path: str):
    """
    截断图片路径，只保留 path 前缀
    """
    suffix = path.split(".")[-1]
    return path[: path.rindex("/") + 1] if suffix in IMAGE_SUFFIXES else path


def calc_summary(cases: list):
    """
    计算每个接口的平均响应时间、标准差等指标
    """
    grouped_cases = linq.group_by(cases, lambda x: f"{x[0]}{x[1]}")
    for group in grouped_cases.values():
        host, path = group[0][0], group[0][1]
        success_count = len([x for x in group if x[2]])
        timespans = np.array([x[-1] for x in group])
        yield host, path, success_count, len(group), timespans.mean(), timespans.std()


def save_to_db(timespans: list, minutes: float, created_at: datetime):
    """
    将结果保存到数据库中
    """
    sql = """
        insert into aoi_studio_response_time_summary (host, path, success, total, mean, std, time_range, created_at)
        values (%s, %s, %s, %s, %s, %s, %s, %s);
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        args = [(*x, minutes, created_at) for x in timespans]
        pgsql.execute_many(conn, sql, args)


def main(db_config: dict, minutes: float):
    """
    主函数
    """
    now = datetime.now()
    n_min_ago = now - timedelta(minutes=minutes)

    cases = query_response_times(db_config, min_ago=n_min_ago)
    if not cases:
        return print("no data")

    special_paths = query_special_paths()
    parsed_cases = [parse_case(x, special_paths) for x in cases]
    timespans = sorted(calc_summary(parsed_cases), key=lambda x: x[0])
    save_to_db(timespans, minutes, now)

    print(f"done: {len(parsed_cases)} -> {len(timespans)}")


if __name__ == "__main__":
    time_range = float(sys.argv[1])
    main(pgsql.AOI_BACK, time_range)
