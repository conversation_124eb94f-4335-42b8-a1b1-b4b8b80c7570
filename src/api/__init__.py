# !/usr/bin/env python3
from flask import Flask


def register_api(app: Flask):
    """
    注册api
    :param app:
    :return:
    """

    @app.route("/health")
    def hello_world():
        """
        检查服务状态接口，临时用flask写下
        :return: string
        """
        return "ok"

    # @app.route("/get_db")
    # def test_query_db():
    #     """
    #     测试数据库查询
    #     :return:
    #     """
    #     return test_db()
