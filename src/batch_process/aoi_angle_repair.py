"""
存在小锐角的数据修型
"""
import os
import sys
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parent.parent.parent
print('代码路径', root_path)
sys.path.insert(0, root_path.as_posix())

import numpy as np
import shapely.wkt
from scipy.ndimage import gaussian_filter1d
from shapely import Polygon, LineString
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

import pandas as pd
from src.tools import pgsql
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.batch_process.flow_process_aoi import update_geom_of_aoi

file_path = Path(os.path.abspath(__file__)).parent / 'workdir'
file_path.mkdir(parents=True, exist_ok=True)
file_name = file_path / 'export_data.tsv'


# 针对识别出的锐角区域应用高斯滤波
def smooth_sharp_angles(polygon, threshold=20, window_size=5, sigma=1, distance_threshold=10.0):
    """
    任务处理
    :param polygon:
    :param threshold:
    :param window_size:
    :param sigma:
    :param distance_threshold:
    :return:
    """
    coords = np.array(polygon.exterior.coords[:-1])  # 去除封闭的最后一个点（和第一个重复）
    new_coords = coords.copy()

    for i in range(1, len(coords) - 1):
        angle, _ = calculate_angle(coords[i - 1], coords[i], coords[i + 1])

        # 如果夹角小于指定阈值，认为是锐角
        if angle < threshold:
            # 获取锐角前后 5 个点的索引范围
            start_idx = max(i - window_size, 0)
            end_idx = min(i + window_size, len(coords) - 1)

            # 筛选满足距离条件的点
            points_to_smooth = []
            for j in range(start_idx, end_idx + 1):
                if calculate_distance(coords[i], coords[j]) <= distance_threshold:
                    points_to_smooth.append(j)

            # 若满足条件的点数大于 0，则应用高斯滤波
            if points_to_smooth:
                x_smooth = gaussian_filter1d(coords[points_to_smooth, 0], sigma=sigma)
                y_smooth = gaussian_filter1d(coords[points_to_smooth, 1], sigma=sigma)

                # 将平滑后的坐标更新到原多边形
                for idx, j in enumerate(points_to_smooth):
                    new_coords[j, 0] = x_smooth[idx]
                    new_coords[j, 1] = y_smooth[idx]

    # 重新构建平滑后的多边形
    smoothed_polygon = Polygon(new_coords)
    return smoothed_polygon


def get_geom_iou(geom1: BaseGeometry, geom2: BaseGeometry):
    """
    计算两个多边形的交并比
    """
    intersection = geom1.intersection(geom2).area
    union = geom1.union(geom2).area
    if union == 0:
        return 0.0

    return intersection / union


def make_competitor_valid(geom):
    """
    确保竞品多边形有效。
    :param geom:
    """
    if not geom:
        return None

    geom = geom.buffer(0)

    if not geom.is_valid:
        return None

    if geom.geom_type == 'MultiPolygon':
        return None

    if geom.geom_type != 'Polygon':
        return None

    if any(geom.interiors):
        geom = Polygon(geom.exterior)

    if geom.is_empty:
        return None

    return geom


# 计算两条向量之间的夹角
def calculate_angle(p1, p2, p3):
    """
    计算夹角
    :param p1:
    :param p2:
    :param p3:
    :return:
    """
    # 计算向量
    v1 = np.array(p1) - np.array(p2)
    v2 = np.array(p3) - np.array(p2)

    # 点积计算角度大小
    dot_product = np.dot(v1, v2)
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    cos_angle = dot_product / (norm_v1 * norm_v2)
    angle = np.arccos(np.clip(cos_angle, -1.0, 1.0))  # 避免浮点数精度问题

    # 叉积计算方向
    cross_product = np.cross(v1, v2)

    # 判断是凹角还是凸角
    if cross_product > 0:
        angle_type = "Convex"
    else:
        angle_type = "Concave"

    return np.degrees(angle), angle_type


# 计算两个点之间的距离
def calculate_distance(p1, p2):
    """
    距离多少
    :param p1:
    :param p2:
    :return:
    """
    return np.linalg.norm(np.array(p1) - np.array(p2))


# 计算延长边的点
def extend_line(p1, p2, distance):
    """
    计算延长线的点
    :param p1:
    :param p2:
    :param distance:
    :return:
    """
    direction = np.array(p2) - np.array(p1)
    direction /= np.linalg.norm(direction)  # 单位化
    return p2 + direction * distance


def bool_has_small_angle(polygon, angle_threshold):
    """
    包含小范围角度
    :param polygon:
    :param angle_threshold:
    :return:
    """
    coords = np.array(polygon.exterior.coords[:-1])  # 去除封闭的最后一个点
    new_coords: list = coords.tolist()
    points_to_remove = set()  # 记录需要删除的点的索引
    has_small_angle = False
    n = len(new_coords)

    i = 0
    while i < len(new_coords):
        angle, style = calculate_angle(new_coords[i - 1], new_coords[i], new_coords[(i + 1) % n])
        if angle < angle_threshold:
            has_small_angle = True
            points_to_remove.add(f"{new_coords[i][0]} {new_coords[i][1]}")
            i += 1
        else:
            i += 1

    if not has_small_angle:
        return False, None
    return True, "MULTIPOINT(" + ",".join(points_to_remove) + ")"


# 主函数：处理多边形，删除角度小于阈值的点
def process_polygon(polygon, angle_threshold, length_threshold, cut_distance, cut_range, sigma, max_distance):
    """
    处理多边形，删除角度小于阈值的点
    :param polygon:
    :param angle_threshold:
    :param length_threshold:
    :param cut_distance:
    :param cut_range:
    :param sigma:
    :param max_distance:
    :return:
    """
    coords = np.array(polygon.exterior.coords[:-1])  # 去除封闭的最后一个点
    new_coords: list = coords.tolist()
    points_to_remove = set()  # 记录需要删除的点的索引
    has_small_angle = False
    n = len(new_coords)

    i = 0
    convex_indexed = []
    concave_indexed = []
    while i < len(new_coords):
        angle, style = calculate_angle(new_coords[i - 1], new_coords[i], new_coords[(i + 1) % n])
        if angle < angle_threshold:
            has_small_angle = True
            points_to_remove.add(i)
            # if style == "Convex":
            #     convex_indexed.append(i)
            # elif style == "Concave":
            #     concave_indexed.append(i)
            i += 1
        else:
            i += 1

    if not has_small_angle:
        return polygon

    # if any(convex_indexed):
    #     # 优先删除凸包的点
    #     points_to_remove = set(convex_indexed[0:1])
    #     print(coords[list(points_to_remove)[0]])
    # else:
    #     points_to_remove = set(concave_indexed[0:1])
    #     print(coords[list(points_to_remove)[0]])

    # 根据记录的索引删除点
    new_coords = [point for idx, point in enumerate(new_coords) if idx not in points_to_remove]

    # 如果处理后的点少于3个，添加默认的切割点
    if len(new_coords) < 3:
        return None

    return process_polygon(Polygon(new_coords), angle_threshold, length_threshold, cut_distance, cut_range, sigma,
                           max_distance)


def get_sharp_angles(polygon, angle_threshold):
    """
    获取多边形的锐角点
    :param polygon:
    :param angle_threshold:
    :return:
    """
    coords = list(polygon.exterior.coords)[:-1]  # 去掉最后一个重复的点
    sharp_angles = []

    n = len(coords)

    for i in range(n):
        # i-1, i, i+1 时需要处理循环
        p1 = coords[i - 1]
        p2 = coords[i]
        p3 = coords[(i + 1) % n]  # 通过 % n 来处理最后一个点与第一个点的连接

        angle, _ = calculate_angle(p1, p2, p3)
        if angle < angle_threshold:
            sharp_angles.append((p1, p2, p3, angle))

    return sharp_angles


def smooth_sharp_angle(p1, p2, p3, trim_distance):
    """
    修剪锐角
    :param p1:
    :param p2:
    :param p3:
    :param trim_distance:
    :return:
    """
    # p2是顶点，p1, p3是两条边上的点
    line1 = LineString([p2, p1])
    line2 = LineString([p2, p3])

    # 从顶点沿边修剪指定距离
    new_point1 = line1.interpolate(trim_distance)
    new_point2 = line2.interpolate(trim_distance)

    return new_point1, new_point2


def fix_polygon_sharp_angles(polygon, angle_threshold, trim_distance):
    """
    修复多边形的锐角
    :param polygon:
    :param angle_threshold:
    :param trim_distance:
    :return:
    """
    sharp_angles = get_sharp_angles(polygon, angle_threshold)
    if not any(sharp_angles):
        return polygon

    new_coords = list(polygon.exterior.coords[:-1])

    for p1, p2, p3, angle in sharp_angles:
        # 修剪锐角，插入新的点
        new_point1, new_point2 = smooth_sharp_angle(p1, p2, p3, trim_distance)

        # 替换锐角点为修剪后的点
        idx = new_coords.index(p2)

        # 在顶点之前插入新点
        new_coords.insert(idx, (new_point2.x, new_point2.y))
        new_coords.insert(idx, (new_point1.x, new_point1.y))

        # 删除原来的锐角顶点
        new_coords.pop(idx + 2)  # 原来的 p2 已经后移两位

    return fix_polygon_sharp_angles(Polygon(new_coords), angle_threshold, trim_distance)


def try_repair():
    """
    修复多边形的锐角
    :return:
    """
    df = pd.read_csv(file_name, sep='\t', converters={'tag': str, 'bid': str})
    f1 = file_path / "geom_error_fixed.csv"
    f2 = file_path / "geom_error_manual.csv"

    # 参数手动传入
    angle_threshold = 20
    length_threshold = 0e-5
    cut_distance = 10e-5
    cut_range = 0  # 切割范围（前后 n 个点）
    sigma = 1
    max_distance = 10e-5
    export_lines_strategy = []
    export_lines_manual = []
    for _, item in tqdm(df.iterrows(), total=len(df)):
        line = item['边框形状']
        face_id = item['face_id']
        tag = item['tag']
        bid = item['bid']
        pv = item['pv']
        src = item['src']
        err_point = item['错误点']
        tmp = {
            'face_id': face_id,
            'bid': bid,
            'pv': pv,
            'src': src,
            'tag': tag,
            '错误点': err_point,
            'old_area': '',
            'new_area': '',
            'iou': '',
            'old_geom': line,
            'new_geom': '',
        }
        try:
            # 加载polygon信息
            polygon = shapely.wkt.loads(line)
        except Exception as e:
            continue
        tmp['old_area'] = polygon.area * 1e10
        smoothed_polygon = make_competitor_valid(
            process_polygon(polygon, angle_threshold, length_threshold, cut_distance, cut_range, sigma,
                            max_distance))
        if smoothed_polygon is None:
            smoothed_polygon = make_competitor_valid(fix_polygon_sharp_angles(polygon, angle_threshold, 5e-5))
        else:
            iou = get_geom_iou(polygon, smoothed_polygon)
            if iou <= 0.95:
                smoothed_polygon = make_competitor_valid(fix_polygon_sharp_angles(polygon, angle_threshold, 5e-5))
        actual_iou = get_geom_iou(polygon, smoothed_polygon)
        # 修改前后的交并比
        tmp['iou'] = actual_iou
        if actual_iou < 0.95 or '服务区' in tag:
            tmp['new_geom'] = smoothed_polygon.wkt
            tmp['new_area'] = smoothed_polygon.area * 1e10
            export_lines_manual.append(tmp)
        else:
            smoothed_polygon = shapely.set_precision(smoothed_polygon, 1e-10).simplify(1e-6)
            tmp['new_area'] = smoothed_polygon.area * 1e10
            tmp['new_geom'] = smoothed_polygon.wkt
            export_lines_strategy.append(tmp)
    pd.DataFrame(export_lines_strategy).to_csv(f1, sep='\t', index=False)
    pd.DataFrame(export_lines_manual).to_csv(f2, sep='\t', index=False)


def search():
    """
    search
    # 字段 :,face_id,bid,name,area,pv,tag,src,错误点,边框形状,主点位置
    :return:
    """
    # 根据图幅去加载
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_conn:
        sql = 'select distinct mesh_id from blu_face'
        mesh_id_data = aoi_conn.fetch_all(sql)
        mesh_ids = [x[0] for x in mesh_id_data]
    lines = []
    angle_threshold = 20
    # with open("meshes/mesh_ids.txt") as f:
    #     mesh_ids = [x.strip() for x in f]
    for mesh_id in tqdm(mesh_ids, desc='开始扫描'):
        print('mesh_id:{}'.format(mesh_id))
        try:
            # 一个图幅一个图幅跑,出现错误会丢掉一个图幅的未执行数据
            with PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_conn, PgsqlStabilizer(
                    pgsql.POI_SLAVER_CONFIG) as poi_conn:
                sql = 'select a.face_id, b.poi_bid, a.src, st_astext(a.geom) as geom, a.area from blu_face a' \
                      ' inner join blu_face_poi b on a.face_id=b.face_id where a.mesh_id = %s '
                data = aoi_conn.fetch_all(sql, (mesh_id,))
                for item in data:
                    face_id = item[0]
                    poi_bid = item[1]
                    src = item[2]
                    geom = item[3]
                    area = item[4]
                    polygon = shapely.wkt.loads(geom)
                    has_err, points = bool_has_small_angle(polygon, angle_threshold)
                    if not has_err:
                        continue
                    pv = -1
                    tag = ''
                    name = ''
                    main_geom = ''
                    if has_err:
                        sql = 'select name, st_astext(geometry) as geom, std_tag, click_pv from poi where bid=%s '
                        poi_data = poi_conn.fetch_one(sql, (poi_bid,))
                        if poi_data:
                            name = poi_data[0]
                            main_geom = poi_data[1]
                            tag = poi_data[2]
                            pv = poi_data[3]
                    data = {
                        '': "",
                        "face_id": face_id,
                        "bid": poi_bid,
                        "name": name,
                        "area": area,
                        "pv": pv,
                        "tag": tag,
                        "src": src,
                        "错误点": points,
                        "边框形状": geom,
                        "主点位置": main_geom,
                    }
                    lines.append(data)
        except Exception as e:
            print('出现错误了', e.args)
    df = pd.DataFrame(lines)
    df.to_csv(file_name, sep='\t', index=False)


def do_repair():
    """
    执行修复
    修改前后交并比≥95%直接修形（对于服务区不修形），对于修改前后交并比≤95%不修，每周一次可以导出。
    :return:
    """
    print("开始上线geom")
    strategy_path = file_path / "geom_error_fixed.csv"
    df = pd.read_csv(strategy_path, sep='\t', converters={"face_id": str, "new_geom": str})
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as stable_master_back:
        for _, row in tqdm(df.iterrows(), total=len(df), desc='开始更新'):
            face_id = row["face_id"]
            new_geom = row["new_geom"]
            print("===== face_id:{}, new_geom:{}".format(face_id, new_geom))
            update_geom_of_aoi(stable_master_back, face_id, new_geom)


def run():
    """
    执行修复程序
    :return:
    """
    # 搜索
    search()
    # 修复
    try_repair()
    # 执行入库
    do_repair()


if __name__ == '__main__':
    run()
