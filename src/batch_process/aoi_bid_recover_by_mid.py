"""
新增aoi的bid回写
"""
import os.path
import sys
from pathlib import Path
import datetime

import tqdm

root_path = Path(os.path.abspath(__file__)).parents[2]
sys.path.insert(0, root_path.as_posix())
work_dir = Path(os.path.abspath(__file__)).parent / 'aoi_bid_by_mid' / datetime.date.today().strftime('%Y%m%d')
work_dir.mkdir(parents=True, exist_ok=True)

from src.tools import pgsql
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.batch_process import flow_process_aoi
from src.tools.mysql_tool import MysqlTool
import pandas as pd

MONTH_2_AGO = (datetime.datetime.now() - datetime.timedelta(days=60)).strftime('%Y-%m-%d')

# 需要处理的aoi数据
AOI_WAIT_SQL = f"""
select b.id, b.poi_id, a.face_id,a.name_ch from blu_face a 
    inner join blu_face_poi b on a.face_id=b.face_id
     where a.src = 'CD' and b.poi_id != '' and b.poi_bid='' and a.update_time>'{MONTH_2_AGO}' 
"""

# 需要处理的poi数据
GET_BID_BY_MID = """
    select bid from poi_create_record where mid=%s order by record_id desc limit 1
"""

# 获取poi详细数据
GET_POI_DETAIL = """
select bid,name from poi where bid=%s
"""


def get_bid_by_mid(mid):
    """
    根据mid获取bid
    :param mid:
    :return:
    """
    with MysqlTool(name='beeflow') as mysql:
        with mysql.connection.cursor() as cur:
            cur.execute(GET_BID_BY_MID, [mid])
            res = cur.fetchone()
            if res:
                return res[0]
            return None


def run():
    """
    主函数
    :return:
    """
    lines = []
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_db, PgsqlStabilizer(pgsql.POI_CONFIG) as poi_db:
        wait_list = aoi_db.fetch_all(AOI_WAIT_SQL)
        for (blu_face_poi_id, mid, face_id, name_ch) in tqdm.tqdm(wait_list):
            # 根据mid获取bid
            bid = get_bid_by_mid(mid)
            if not bid or bid == '':
                continue
            # 本地poi没生效,暂时不处理
            poi_data = poi_db.fetch_one(GET_POI_DETAIL, [bid])
            if not poi_data:
                continue
            poi_name = poi_data[1]
            name_equal = name_ch == poi_name
            lines.append({
                "id": blu_face_poi_id,
                "mid": mid,
                "face_id": face_id,
                "bid": bid,
                "name_ch": name_ch,
                "poi_name": poi_name,
                "is_equal": name_ch == poi_name,
            })
            if name_equal:
                # 新增的名称如果名称一致才进行批处理
                flow_process_aoi.update_main_poi_of_aoi(aoi_db, face_id, 'CD', bid, mid)

    pd.DataFrame(lines).to_csv(
        work_dir / f'result_{datetime.datetime.now().strftime("%H%M")}.tsv', index=False, sep='\t', encoding='utf8')


if __name__ == '__main__':
    run()
