# !/usr/bin/env python3
"""
用于统计 AOI 线上量级。
"""
from dataclasses import dataclass, field
from datetime import datetime

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, notice_tool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    批处理记录
    """
    face_id: str
    bid: str
    src: str
    is_valid: bool = False
    is_accurate: bool = False

    @property
    def is_to_c(self):
        """
        判断是否是 C 端记录
        """
        return self.src != 'SD'


@dataclass
class Context:
    """
    上下文。
    """
    total_count: int = 0
    mesh_ids: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)


@desc()
def fill_total_count(ctx: Context, proceed):
    """
    填充边框总量
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn:
        sql = '''select count(*) from blu_face'''
        row = pgsql.fetch_one(aoi_conn, sql)
        ctx.total_count, = row

    proceed()


@desc()
def load_mesh_ids(ctx: Context, proceed):
    """
    加载所有的图幅号
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn:
        sql = '''select distinct mesh_id from blu_face;'''
        ctx.mesh_ids = [x[0] for x in pgsql.fetch_all(aoi_conn, sql)]

    proceed()


@desc()
def load_aois(ctx: Context, proceed):
    """
    查询存在主点的边框
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn:
        for mesh_id in tqdm(ctx.mesh_ids):
            sql = '''
                select a.face_id, a.src, b.poi_bid
                from blu_face a
                inner join blu_face_poi b 
                on a.face_id = b.face_id
                where a.mesh_id = %s;
            '''
            for face_id, src, bid in pgsql.fetch_all(aoi_conn, sql, (mesh_id,)):
                ctx.records.append(Record(
                    face_id=face_id,
                    src=src,
                    bid=bid,
                ))

    proceed()


@desc()
def check_valid(ctx: Context, proceed):
    """
    检查边框是否失效
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for record in tqdm(ctx.records):
            sql = '''select 1 from poi where bid = %s;'''
            record.is_valid = poi_conn.fetch_one(sql, (record.bid,)) is not None

    proceed()


@desc()
def check_accurate(ctx: Context, proceed):
    """
    检查边框是否准确
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn:
        for record in tqdm(ctx.records):
            if not record.is_to_c or not record.is_valid:
                continue

            sql = '''select 1 from blu_face_complete where face_id = %s and aoi_complete >= 3;'''
            record.is_accurate = pgsql.fetch_one(aoi_conn, sql, (record.face_id,)) is not None

    proceed()


@desc()
def save_result_to_db(ctx: Context, proceed):
    """
    保存结果到数据库
    """
    sql = '''
        insert into sugar_aoi_statistics (type, name, date, value) 
        values ('aoi_diff_daily', 'all_c',  %s, %s)
        on conflict (type, name, date) do update set value = excluded.value;
    '''
    to_c_valid_count = len([x for x in ctx.records if x.is_valid and x.is_to_c])

    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab:
        stab.connection.autocommit = False
        with stab.connection.cursor() as cursor:
            try:
                cursor.execute(sql, (datetime.now().date(), to_c_valid_count))
                stab.connection.commit()
            except Exception as e:
                stab.connection.rollback()
                print(e)

    proceed()


@desc()
def send_to_infoflow(ctx: Context, proceed):
    """
    发送通知。
    """
    to_c_valid_count = len([x for x in ctx.records if x.is_valid and x.is_to_c])
    to_b_valid_count = len([x for x in ctx.records if x.is_valid and not x.is_to_c])
    accurate_count = len([x for x in ctx.records if x.is_valid and x.is_to_c and x.is_accurate])
    without_poi_count = ctx.total_count - len(ctx.records)
    invalid_poi_count = len([x for x in ctx.records if not x.is_valid])

    msg = f'''C 端量: {to_c_valid_count}，
B 端量: {to_b_valid_count}，
B + C 总量：{to_c_valid_count + to_b_valid_count}，
精准 AOI 总量：{accurate_count}，
精准 AOI 占比：{accurate_count / (to_c_valid_count + to_b_valid_count):.1%}。
无主点量：{without_poi_count}，
POI 失效量：{invalid_poi_count}，
全库总量：{ctx.total_count}。
'''
    print(msg)
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d83586c9c29feea30d4fbe3da7edc2669'
    )
    proceed()


def main():
    """
    主函数。
    """
    main_pipe = pipeline.Pipeline(
        fill_total_count,
        load_mesh_ids,
        load_aois,
        check_valid,
        check_accurate,
        save_result_to_db,
        send_to_infoflow,
    )
    desc.attach(main_pipe)
    ctx = Context()
    main_pipe(ctx)


if __name__ == '__main__':
    main()
