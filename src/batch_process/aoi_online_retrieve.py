# !/usr/bin/env python3
"""
AOI 在线回捞
"""
import argparse
import json
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path

import mapio.utils.coord
import pymysql.cursors
import shapely.wkt
from mapio.poi.model import POI
from mapio.poi.poi_io import load_online_poi
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.auto_repair_mixin import get_poi_by_bid
from src.charging_station.data import Poi
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.mysql_tool import MysqlTool

SRC = 'online_retrieve'
INVALID_SRC = {'attribute_integrate', 'rg', 'satellite_traj'}
SEARCH_TIMEDELTA = timedelta(days=1)

desc = pipeline.get_desc()


@dataclass
class Ticket:
    """
    工单记录
    """
    bid: str
    change_type: str
    method: str
    old_value: str = ''
    new_value: str = ''

    @property
    def change_diff(self):
        return json.dumps({
            'old': self.old_value,
            'new': self.new_value,
        }, ensure_ascii=False)


@dataclass
class Record:
    """
    AOI 回捞记录
    """
    bid: str
    src: str
    history_face_id: str = None
    poi: Poi = None
    online_poi_info: POI = None
    online_poi_wkt: str = None

    is_poi_name_changed: bool = False
    is_poi_location_changed: bool = False
    is_poi_tag_changed: bool = False
    is_poi_status_changed: bool = False

    is_aoi_deleted: bool = False
    is_aoi_geom_changed: bool = False
    is_aoi_level_changed: bool = False
    is_aoi_complete_changed: bool = False

    old_aoi_wkt: str = None
    old_aoi_level: int = None
    old_aoi_complete: int = None

    new_aoi_wkt: str = None
    new_aoi_level: int = None
    new_aoi_complete: int = None

    can_process: bool = True
    reason: str = ''

    @property
    def can_retrieve(self):
        """
        判断是否可以回捞
        """
        return self.can_process and (
            self.is_poi_name_changed or
            self.is_poi_location_changed or
            self.is_poi_tag_changed or
            self.is_poi_status_changed or

            self.is_aoi_geom_changed or
            self.is_aoi_level_changed or
            self.is_aoi_complete_changed
        )


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    last_send_time: datetime = datetime.min
    current_record_id: int = None
    records: list[Record] = field(default_factory=list)
    changed_bids: set[str] = field(default_factory=set)
    poi_cache: dict[str, Poi] = field(default_factory=dict)
    extremely_high_heat_bids: set[str] = field(default_factory=set)
    high_heat_bids: set[str] = field(default_factory=set)
    potential_heat_bids: set[str] = field(default_factory=set)
    tickets: list[Ticket] = field(default_factory=list)
    checking_bids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def add_record(self, bid, src):
        """
        添加记录
        """
        if bid not in self.changed_bids:
            self.records.append(Record(bid=bid, src=src))
            self.changed_bids.add(bid)

    def get_poi(self, stab, bid):
        """
        获取 poi
        """
        if bid not in self.poi_cache:
            poi = get_poi_by_bid(stab, bid)
            self.poi_cache[bid] = poi
            return poi

        return self.poi_cache[bid]

    def is_desired_bid(self, bid):
        """
        是否是需要的 bid
        """
        return (
            bid in self.extremely_high_heat_bids or
            bid in self.high_heat_bids or
            bid in self.potential_heat_bids
        )

    def get_heat_level(self, bid):
        """
        获取热度等级
        """
        if bid in self.extremely_high_heat_bids:
            return 'extremely_high'

        if bid in self.high_heat_bids:
            return 'high'

        if bid in self.potential_heat_bids:
            return 'potential'

        return 'unknown'


@desc()
def load_checking_bids(ctx: Context, proceed):
    """
    填充处于核实中的 bid 集合
    """
    sql = '''
        select distinct bid
        from aoi_interception_history
        where create_time >= %s and
              src = %s;
    '''

    with (
        get_mysql_connection('beeflow_rw') as conn,
        conn.cursor() as cursor,
    ):
        start = datetime.now() - SEARCH_TIMEDELTA
        cursor.execute(sql, [start, SRC])
        ctx.checking_bids = set(x[0] for x in cursor.fetchall())

    proceed()


@desc()
def load_desired_bids(ctx: Context, proceed):
    """
    加载需要处理的 bid
    """
    get_extremely_high_heat_bids_sql = '''
        select aoi_bid from over_heat_protection_aoi;
    '''
    get_high_heat_bids_sql = '''
        select aoi_bid from high_heat_protection_aoi;
    '''
    get_potential_heat_bids_sql = '''
        select aoi_bid from potential_heat_protection_aoi;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ctx.extremely_high_heat_bids = set(x[0] for x in stab.fetch_all(get_extremely_high_heat_bids_sql))
        ctx.high_heat_bids = set(x[0] for x in stab.fetch_all(get_high_heat_bids_sql))
        ctx.potential_heat_bids = set(x[0] for x in stab.fetch_all(get_potential_heat_bids_sql))

        ctx.extremely_high_heat_bids = ctx.extremely_high_heat_bids - ctx.checking_bids
        ctx.high_heat_bids = ctx.high_heat_bids - ctx.checking_bids
        ctx.potential_heat_bids = ctx.potential_heat_bids - ctx.checking_bids

    proceed()


@desc()
def load_push_records(ctx: Context, proceed):
    """
    加载推送记录
    """
    with (
        MysqlTool('beeflow') as mysql_tool,
        mysql_tool.connection.cursor(pymysql.cursors.DictCursor) as cursor,
    ):
        sql, args = get_query_record_data(ctx.current_record_id)
        cursor.execute(sql, args)

        for row in tqdm(cursor.fetchall()):
            bid = row['bid']
            if bid == '':
                continue

            if not ctx.is_desired_bid(bid):
                continue

            sub_src = row['sub_src']
            if sub_src == '' or sub_src in INVALID_SRC:
                continue

            send_time = row['send_time']
            ctx.last_send_time = max(ctx.last_send_time, send_time)
            ctx.add_record(bid, src='push')
            ctx.current_record_id = max(ctx.current_record_id, row['record_id'])

    proceed()


@desc()
def load_ugc_records(ctx: Context, proceed):
    """
    加载 ugc 记录
    """
    sql = '''
        select bid 
        from aoi_intervention
        where create_time >= %s;
    '''

    ctx.last_send_time = datetime.now() if ctx.last_send_time == datetime.min else ctx.last_send_time

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        start = ctx.last_send_time - SEARCH_TIMEDELTA
        for bid, in tqdm(stab.fetch_all(sql, [start])):
            if not ctx.is_desired_bid(bid):
                continue

            ctx.add_record(bid, src='ugc')

    proceed()


@desc()
def load_current_record_id(ctx: Context, proceed):
    """
    加载当前推送记录 id
    """
    if ctx.current_record_id is not None:
        proceed()
        return

    cache_file = ctx.work_dir / 'current_record_id'
    if not cache_file.exists():
        ctx.current_record_id = -1
    else:
        ctx.current_record_id = int([x[0] for x in tsv.read_tsv(cache_file)][0])

    proceed()


@desc()
def fill_basic_prop(ctx: Context, proceed):
    """
    填充基础属性
    """
    with (
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab,
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stab,
        PgsqlStabilizer(pgsql.AOI_RESUME_SLAVER_CONFIG) as resume_stab,
    ):
        for record in tqdm(ctx.records):
            poi = ctx.get_poi(poi_stab, record.bid)
            if poi is None:
                record.can_process = False
                record.reason = 'poi not found'
                continue

            history_face_id = get_history_face_id(aoi_stab, resume_stab, poi.bid, ctx.last_send_time)
            if history_face_id is None:
                record.can_process = False
                record.reason = 'history face id not found'
                continue

            record.poi = poi
            record.history_face_id = history_face_id

    proceed()


@desc()
def fill_online_poi_info(ctx: Context, proceed):
    """
    填充 poi 在线信息
    """
    for record in tqdm([x for x in ctx.records if x.can_process]):
        online_poi_info = load_online_poi(record.bid, ['basic'])
        if online_poi_info is None:
            record.can_process = False
            record.reason = 'online poi info not found'
            continue

        record.online_poi_info = online_poi_info
        mc_point_x = record.online_poi_info.basic.point_x
        mc_point_y = record.online_poi_info.basic.point_y
        gcj_point_x, gcj_point_y = mapio.utils.coord.mercator_to_gcj02(mc_point_x, mc_point_y)
        record.online_poi_wkt = f'POINT({gcj_point_x} {gcj_point_y})'

    proceed()


@desc()
def fill_poi_prop_changed(ctx: Context, proceed):
    """
    填充 poi 属性变化情况
    """
    min_location_distance = 1e-6

    for record in tqdm([x for x in ctx.records if x.can_process]):
        record.is_poi_name_changed = record.poi.name != record.online_poi_info.basic.name
        record.is_poi_tag_changed = record.poi.tag != record.online_poi_info.basic.std_tag
        record.is_poi_status_changed = record.poi.status != record.online_poi_info.basic.status

        online_geom = shapely.wkt.loads(record.online_poi_wkt)
        record.is_poi_location_changed = online_geom.distance(record.poi.geom) > min_location_distance

    proceed()


@desc()
def fill_aoi_prop_changed(ctx: Context, proceed):
    """
    填充 aoi 属性变化情况
    """
    with (
        PgsqlStabilizer(pgsql.AOI_RESUME_SLAVER_CONFIG) as resume_stab,
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stab,
    ):
        for record in tqdm([x for x in ctx.records if x.can_process]):
            record.is_aoi_deleted, old_aoi_wkt = is_aoi_deleted(resume_stab, record, ctx.last_send_time)

            if record.is_aoi_deleted:
                record.old_aoi_wkt = old_aoi_wkt
                record.is_aoi_geom_changed = True
                record.is_aoi_level_changed = False
                record.is_aoi_complete_changed = False
            else:
                aoi_geom_diff = get_aoi_geom_diff(aoi_stab, resume_stab, record, ctx.last_send_time)
                record.is_aoi_geom_changed, record.old_aoi_wkt, record.new_aoi_wkt = aoi_geom_diff

                aoi_level_diff = get_aoi_level_diff(aoi_stab, resume_stab, record, ctx.last_send_time)
                record.is_aoi_level_changed, record.old_aoi_level, record.new_aoi_level = aoi_level_diff

                aoi_complete_diff = get_aoi_complete_diff(aoi_stab, resume_stab, record, ctx.last_send_time)
                record.is_aoi_complete_changed, record.old_aoi_complete, record.new_aoi_complete = aoi_complete_diff

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存批处理记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'records.csv',
        [
            [
                x.bid,
                x.src,
                ctx.get_heat_level(x.bid),

                x.is_poi_status_changed,
                x.poi.status,
                x.online_poi_info.basic.status,

                x.is_poi_name_changed,
                x.poi.name,
                x.online_poi_info.basic.name,

                x.is_poi_location_changed,
                x.poi.geom.wkt,
                x.online_poi_wkt,

                x.is_poi_tag_changed,
                x.poi.tag,
                x.online_poi_info.basic.std_tag,

                x.is_aoi_geom_changed,
                x.is_aoi_level_changed,
                x.is_aoi_complete_changed,
            ]
            for x in ctx.records if x.can_retrieve
        ]
    )
    proceed()


@desc()
def save_current_record_id(ctx: Context, proceed):
    """
    保存当前记录 id
    """
    tsv.write_tsv(ctx.work_dir / 'current_record_id', [[ctx.current_record_id]])
    proceed()


@desc()
def save_check_tickets(ctx: Context, proceed):
    """
    保存人工核实工单
    """
    tsv.write_tsv(
        ctx.work_dir / 'tickets.csv',
        [
            [
                x.bid,
                ctx.get_heat_level(x.bid),
                x.change_type,
                x.change_diff,
                SRC,
                x.method,
            ]
            for x in ctx.tickets
        ]
    )
    proceed()


@desc()
def fill_check_tickets(ctx: Context, proceed):
    """
    填充人工核实工单
    """
    for record in tqdm([x for x in ctx.records if x.can_retrieve]):
        fill_check_poi_tickets(ctx, record)
        fill_check_aoi_tickets(ctx, record)

    proceed()


@desc()
def push_check_tickets(ctx: Context, proceed):
    """
    推送人工核实工单
    """
    status_manual = 2
    sql = '''
        insert into aoi_interception_history (bid, status, change_type, change_diff, src)
        values (%s, %s, %s, %s, %s);
    '''

    with (
        get_mysql_connection('beeflow_rw') as conn,
        conn.cursor() as cursor,
    ):
        try:
            for ticket in tqdm([x for x in ctx.tickets]):
                cursor.execute(sql, [
                    ticket.bid,
                    status_manual,
                    ticket.change_type,
                    ticket.change_diff,
                    SRC,
                ])

            conn.commit()
        except Exception as e:
            print(f"{e}")
            conn.rollback()

    proceed()


# -----------------------------------
# Helper functions
# -----------------------------------

def fill_check_poi_tickets(ctx: Context, record: Record):
    """
    填充人工核实 POI 工单
    """
    access_tag = '出入口'
    is_access_poi = record.poi.name.startswith(access_tag)

    if record.is_poi_name_changed:
        ctx.tickets.append(Ticket(
            bid=record.bid,
            change_type='子点名称' if is_access_poi else '主点名称',
            old_value=record.poi.name,
            new_value=record.online_poi_info.basic.name,
            method=record.src,
        ))

    if record.is_poi_tag_changed:
        ctx.tickets.append(Ticket(
            bid=record.bid,
            change_type='子点 tag' if is_access_poi else '主点 tag',
            old_value=record.poi.tag,
            new_value=record.online_poi_info.basic.std_tag,
            method=record.src,
        ))

    if record.is_poi_status_changed:
        ctx.tickets.append(Ticket(
            bid=record.bid,
            change_type='子点状态' if is_access_poi else '主点状态',
            old_value=str(record.poi.status),
            new_value=str(record.online_poi_info.basic.status),
            method=record.src,
        ))

    if record.is_poi_location_changed:
        ctx.tickets.append(Ticket(
            bid=record.bid,
            change_type='子点坐标' if is_access_poi else '主点坐标',
            old_value=record.poi.geom.wkt,
            new_value=record.online_poi_wkt,
            method=record.src,
        ))


def fill_check_aoi_tickets(ctx: Context, record: Record):
    """
    填充人工核实 AOI 工单
    """
    if record.is_aoi_geom_changed:
        if record.is_aoi_deleted:
            ctx.tickets.append(Ticket(
                bid=record.bid,
                change_type='边框删除',
                old_value=record.old_aoi_wkt,
                new_value=record.new_aoi_wkt,
                method=record.src,
            ))
        else:
            ctx.tickets.append(Ticket(
                bid=record.bid,
                change_type='边框更新',
                old_value=record.old_aoi_wkt,
                new_value=record.new_aoi_wkt,
                method=record.src,
            ))

    if record.is_aoi_level_changed:
        ctx.tickets.append(Ticket(
            bid=record.bid,
            change_type='aoi_level 修改',
            old_value=str(record.old_aoi_level),
            new_value=str(record.new_aoi_level),
            method=record.src,
        ))

    if record.is_aoi_complete_changed:
        ctx.tickets.append(Ticket(
            bid=record.bid,
            change_type='aoi_complete 修改',
            old_value=str(record.old_aoi_complete),
            new_value=str(record.new_aoi_complete),
            method=record.src,
        ))


def get_aoi_complete_diff(aoi_stab, resume_stab, record, last_send_time):
    """
    判断 aoi_complete 是否发生变化
    """
    get_current_complete_sql = '''
        select aoi_complete from blu_face_complete where face_id = %s;
    '''
    get_changed_sql = '''
        select aoi_complete
        from blu_face_complete_history
        where main_bid = %s and
              created_at > %s and
              created_at <= %s and
              aoi_complete != %s and
              action = 'UPDATE'
        limit 1;
    '''

    row = aoi_stab.fetch_one(get_current_complete_sql, [record.history_face_id])
    if row is None:
        return False, -1, -1

    new_complete, = row
    args = [record.history_face_id, last_send_time - SEARCH_TIMEDELTA, last_send_time, new_complete]
    row = resume_stab.fetch_one(get_changed_sql, args)
    return (False, new_complete, new_complete) if row is None else (True, row[0], new_complete)


def get_aoi_level_diff(aoi_stab, resume_stab, record, last_send_time):
    """
    判断 aoi_level 是否发生变化
    """
    get_current_level_sql = '''
        select aoi_level from blu_face where face_id = %s;
    '''
    get_changed_sql = '''
        select aoi_level
        from blu_face_history
        where face_id = %s and
              created_at > %s and
              created_at <= %s and
              aoi_level != %s and
              action = 'UPDATE'
        limit 1;
    '''

    row = aoi_stab.fetch_one(get_current_level_sql, [record.history_face_id])
    if row is None:
        return False, -1, -1

    new_level, = row
    args = [record.history_face_id, last_send_time - SEARCH_TIMEDELTA, last_send_time, new_level]
    row = resume_stab.fetch_one(get_changed_sql, args)
    return (False, new_level, new_level) if row is None else (True, row[0], new_level)


def is_aoi_deleted(resume_stab, record, last_send_time):
    """
    判断 aoi 是否被删除
    """
    sql = '''
        select st_astext(geom)
        from blu_face_history
        where face_id = %s and
              created_at > %s and
              created_at <= %s and
              action = 'DELETE'
        limit 1;
    '''

    args = [record.history_face_id, last_send_time - SEARCH_TIMEDELTA, last_send_time]
    row = resume_stab.fetch_one(sql, args)
    return (False, '') if row is None else (True, row[0])


def get_aoi_geom_diff(aoi_stab, resume_stab, record, last_send_time):
    """
    判断 aoi_geom 是否发生变化
    """
    get_current_geom_sql = '''
        select st_astext(geom) from blu_face where face_id = %s;
    '''
    get_changed_sql = '''
        select st_astext(geom)
        from blu_face_history
        where face_id = %s and
              created_at > %s and
              created_at <= %s and
              not st_equals(st_snaptogrid(geom, 1e-5), st_snaptogrid(st_geomfromtext(%s, 4326), 1e-5))
        limit 1;
    '''

    row = aoi_stab.fetch_one(get_current_geom_sql, [record.history_face_id])
    if row is None:
        return False, '', ''

    new_wkt, = row
    args = [record.history_face_id, last_send_time - SEARCH_TIMEDELTA, last_send_time, new_wkt]
    row = resume_stab.fetch_one(get_changed_sql, args)
    return (False, new_wkt, new_wkt) if row is None else (True, row[0], new_wkt)


def get_history_face_id(aoi_stab, resume_stab, bid, last_send_time):
    """
    获取历史记录的 face_id
    """
    get_face_id_by_history_sql = '''
        select face_id
        from blu_face_poi_history
        where poi_bid = %s  and
              created_at > %s and
              created_at <= %s
        order by id desc
        limit 1;
    '''
    get_face_id_by_master_sql = '''
        select face_id 
        from blu_face_poi 
        where poi_bid = %s 
        limit 1;
    '''

    row = resume_stab.fetch_one(get_face_id_by_history_sql, [bid, last_send_time - SEARCH_TIMEDELTA, last_send_time])
    if row is None:
        row = aoi_stab.fetch_one(get_face_id_by_master_sql, [bid])

    return row[0] if row is not None else None


def get_query_record_data(current_record_id, is_single=False):
    """
    构造用于查询推送记录的信息
    """
    if not is_single:
        if current_record_id < 0:
            start = datetime.now() - SEARCH_TIMEDELTA
            return '''select * from place_data_release_record where send_time > %s;''', [start]
        else:
            return '''select * from place_data_release_record where record_id > %s;''', [current_record_id]
    else:
        return '''select * from place_data_release_record where record_id = %s;''', [current_record_id]


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'AOI 在线回捞脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d5070dd11c100081e2110cb89f9e71680'
    )


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--record-id',
        dest='record_id',
        type=int,
        required=False,
    )
    parser.add_argument(
        '--push',
        dest='push',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_checking_bids,
        load_desired_bids,
        load_current_record_id,
        load_push_records,
        load_ugc_records,
        fill_basic_prop,
        fill_online_poi_info,
        fill_poi_prop_changed,
        fill_aoi_prop_changed,
        fill_check_tickets,
        save_check_tickets,
        save_records,
    ]

    if args.push:
        pipes.extend([
            push_check_tickets,
            save_current_record_id,
        ])

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/aoi_online_retrieve"),
        current_record_id=args.record_id,
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == "__main__":
    main(parse_args())
