# !/usr/bin/env python3
"""
AOI 上量统计。
本脚本会区分 toB、toC 两种情况，分别给出预期上线量级和实际上线量级，并通过如流机器人通知。
"""
import argparse
import shutil
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

from tqdm import tqdm

from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.file_downloader import download_file_by_http, download_file_by_afs

desc = pipeline.get_desc()


@dataclass
class OnlineTask:
    """
    存储上线任务的相关信息。
    """
    batch: str
    src: str
    url: str
    path: Path = field(init=False)
    count: int


@dataclass
class Context:
    """
    脚本上下文。
    """
    work_dir: Path
    start_date: str
    online_tasks: list[OnlineTask] = field(default_factory=list)
    commercial_face_ids: list[str] = field(default_factory=list)
    client_face_ids: list[str] = field(default_factory=list)
    commercial_online_count: int = 0
    commercial_total_pv: int = 0
    client_online_count: int = 0
    client_total_pv: int = 0

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fetch_tasks(ctx: Context, proceed):
    """
    获取边框任务信息。
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = f'''
            select batch, src, url, count 
            from online_aoi_task 
            where completed_at >= %s and
                  batch != 'recognition_20231226' and
                  memo != '作废';
        '''
        ctx.online_tasks = [
            OnlineTask(
                batch=batch,
                src=src,
                url=url,
                count=count,
            )
            for batch, src, url, count in pgsql.fetch_all(conn, sql, (ctx.start_date,))
        ]

    proceed()


def download_file(url, save_path: Path):
    """
    下载文件。
    """
    if url.startswith('http'):
        download_file_by_http(url, save_path)
    elif url.startswith('/user/map-data-streeview'):
        download_file_by_afs(url, save_path)


@desc()
def download_tasks(ctx: Context, proceed):
    """
    下载任务数据。
    """
    ctx.work_dir.mkdir(parents=True, exist_ok=True)

    for task in tqdm(ctx.online_tasks):
        task.path = ctx.work_dir / f'{task.batch}.tsv'
        download_file(task.url, task.path)

    proceed()


@desc()
def load_desired_online_face_ids(ctx: Context, proceed):
    """
    加载 AOI 上线数据。
    """

    def load(record_path):
        for bid, mid, name, city, wkt, aoi_id, aoi_level, remark in tsv.read_tsv(record_path):
            yield aoi_id

    commercial_paths = [task.path for task in ctx.online_tasks if 'SD' == task.src]
    client_paths = [task.path for task in ctx.online_tasks if 'CD' == task.src]

    for path in tqdm(commercial_paths):
        try:
            ctx.commercial_face_ids.extend(load(path))
        except:
            print(f'error when loading file {path}, skipping')

    for path in tqdm(client_paths):
        try:
            ctx.client_face_ids.extend(load(path))
        except:
            print(f'error when loading file {path}, skipping')

    proceed()


@desc()
def get_actual_online_count(ctx: Context, proceed):
    """
    获取实际上线数量。
    """

    def get_online_count(face_ids):
        online_count = 0

        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            for face_id in tqdm(face_ids):
                sql = f'''select face_id from blu_face where face_id = %s;'''
                if pgsql.fetch_one(conn, sql, (face_id,)):
                    online_count += 1

        return online_count

    ctx.commercial_online_count = get_online_count(ctx.commercial_face_ids)
    ctx.client_online_count = get_online_count(ctx.client_face_ids)

    proceed()


@desc()
def get_actual_total_pv(ctx: Context, proceed):
    """
    获取实际上线数量。
    """

    def get_total_pv(face_ids):
        total_pv = 0

        with (
            pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
            pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        ):
            for face_id in tqdm(face_ids):
                sql = 'select poi_bid from blu_face_poi where face_id = %s;'
                row = pgsql.fetch_one(aoi_conn, sql, (face_id,))
                if not row:
                    continue
                bid, = row
                sql = 'select click_pv from poi where bid = %s;'
                row = pgsql.fetch_one(poi_conn, sql, (bid,))
                if not row:
                    continue
                click_pv, = row
                total_pv += click_pv

        return total_pv

    ctx.commercial_total_pv = get_total_pv(ctx.commercial_face_ids)
    ctx.client_total_pv = get_total_pv(ctx.client_face_ids)

    proceed()


@desc()
def send_to_infoflow(ctx: Context, proceed):
    """
    发送通知。
    """
    msg = f'''自 {ctx.start_date} 以来 AOI 自动化上线情况：
toC    
预期上线总量：{sum(task.count for task in ctx.online_tasks if task.src == 'CD')}
实际上线总量：{ctx.client_online_count}
实际上线 pv：{ctx.client_total_pv}
toB
预期上线总量：{sum(task.count for task in ctx.online_tasks if task.src == 'SD')}
实际上线总量：{ctx.commercial_online_count}
实际上线 pv：{ctx.commercial_total_pv}
合计
预期上线总量：{sum(task.count for task in ctx.online_tasks)}
实际上线总量：{ctx.commercial_online_count + ctx.client_online_count}
实际上线 pv：{ctx.commercial_total_pv + ctx.client_total_pv}
'''
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d83586c9c29feea30d4fbe3da7edc2669'
    )
    print(msg)
    proceed()


def parse_args():
    """
    解析命令行参数。
    """
    parser = argparse.ArgumentParser('aoi online statistics')
    parser.add_argument(
        '--start-date',
        dest='start_date',
        help='仅统计该日期之后的上线量',
        type=str,
        required=True,
    )
    return parser.parse_args()


def main(args):
    """
    主函数。
    """
    main_pipe = pipeline.Pipeline(
        fetch_tasks,
        download_tasks,
        load_desired_online_face_ids,
        get_actual_online_count,
        get_actual_total_pv,
        send_to_infoflow,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/aoi_online_statistics') / datetime.now().strftime('%Y-%m-%d-%H-%M-%S'),
        start_date=args.start_date,
    )

    try:
        main_pipe(ctx)
    finally:
        shutil.rmtree(ctx.work_dir, ignore_errors=True)


if __name__ == '__main__':
    main(parse_args())
