# !/usr/bin/env python3
"""
例行化 B 转 C
"""

import argparse
import datetime
import random
import uuid
from dataclasses import dataclass, field
from pathlib import Path
from typing import List

import requests
import shapely.wkt
from retrying import retry
from shapely import Polygon, MultiPoint
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from script.inner_roads.post_process.fix_geom import cut_out_road_level7
from src.batch_process import tags
from src.batch_process.batch_helper import batch_process, contains_majority, get_geom_iou
from src.batch_process.flow_process_aoi import update_src_of_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.fix_geom_process.tools import multipolygon_to_single
from src.model.aoi_model import AoiModel
from src.model_mysql import aoi_ml_model
from src.seg_post_process.smooth_point_string import smooth_polygon
from src.tools import pipeline, pgsql, tsv
from src.tools.mysql_tool import MysqlTool
from src.tools.track_provider import get_provider

DEBUG = ''
CLUSTER_AOI = 1
BASIC_AOI = 2
INNER_AOI = 3
SIMPLIFY_TOLERANCE = 5e-5
MAX_OFFSET = 2e-5
PRECISION = 1e-10
MANUAL_WORK_MIN_PV = 210
CLIENT_CONFIG = {
    'min_area': 200e-10,
    'max_area': 1000000e-10,
    'max_area_ratio': 0.5,
    'max_overlap_ratio': 0.5,
}
COMMERCIAL_CONFIG = {
    'min_area': 200e-10,
    'max_area': 1000000e-10,
    'max_area_ratio': 0.5,
    'max_overlap_ratio': 0.6,
}
IGNORED_STRATEGIES = [
    'child_poi_out_of_aoi',
    'competitor_diff'
]

desc = pipeline.get_desc()


@dataclass
class IntelligentRecord:
    """
    边框情报记录
    """
    id: str
    bid: str


@dataclass
class SrcBatchRecord:
    """
    src 批处理记录
    """
    bid: str
    mid: str
    face_id: str
    mesh_id: str
    src: str


@dataclass
class AoiRecord:
    """
    边框信息
    """
    face_id: str
    bid: str
    name: str
    geom: shapely.geometry.Polygon
    tag: str = ''
    aoi_level: int = 0

    @property
    def is_main_bid_valid(self):
        """
        判断主点是否有效
        """
        return self.bid != ''


@dataclass
class CompetitorRecord:
    """
    竞品信息
    """
    bid: str
    name: str = ''
    std_tag: str = ''
    aoi_id: str = ''
    original_aoi_level = BASIC_AOI
    aoi_level = 0
    original_geom: BaseGeometry = None
    geom: Polygon = Polygon()
    can_online_to_c: bool = True
    can_process: bool = True
    should_manual_process: bool = False
    should_b_to_c: bool = False
    area_ratio: float = 0
    overlap_ratio: float = 0
    overlap_face_id: str = ''
    overlap_bid: str = ''
    overlap_name: str = ''
    overlap_std_tag: str = ''
    overlap_pv: int = -1
    overlap_wkt: str = ''
    overlap_mesh_id: str = ''
    competitor_name: str = ''
    create_time: str = ''
    pv: int = -1
    mid: str = ''
    city: str = ''
    reason: str = ''
    overlap_mode: str = ''
    intersects_aois: List[AoiRecord] = field(default_factory=list)
    src: str = ''

    # 法务相关
    intersects_end_point_track: bool = False  # 是否和终点轨迹存在交集
    end_point_track_wkt: str = ''
    intersects_experience_track: bool = False  # 是否和经验轨迹存在交集
    experience_track_wkt: str = ''
    intersects_poi_aggregation: bool = False  # 是否和 POI 聚合区域存在交集
    poi_aggregation_wkt: str = ''

    def __post_init__(self):
        if not self.can_process:
            return

        self.original_geom = self.original_geom.buffer(0)

        if self.original_geom.geom_type == 'Polygon':
            self.geom = shapely.wkt.loads(self.original_geom.wkt)
        else:
            self.geom = self.original_geom.buffer(0)
            make_valid(self, 'init')

    def is_legally_safe(self):
        """
        是否符合法务要求
        """
        return self.intersects_end_point_track or self.intersects_poi_aggregation or self.intersects_experience_track

    def can_to_c(self):
        """
        是否可以上线 C 端
        """
        return (
            self.can_process and
            self.overlap_mode == 'no_overlap' and
            self.can_online_to_c and
            not self.should_b_to_c
        )

    def can_to_b(self):
        """
        是否可以上线 B 端
        """
        return (
            self.can_process and
            self.overlap_mode == 'no_overlap' and
            not self.can_online_to_c
        )

    def can_b_to_c(self):
        """
        是否可以 B 端转 C 端
        """
        return (
            self.can_process and
            self.can_online_to_c and
            self.overlap_mode == 'overlap_to_b'
        ) or self.should_b_to_c


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    batch: str
    mode: str
    now: str
    bid_list_path: Path
    upload_file_url: str
    download_file_url: str
    to_c_records_local_path: Path = field(init=False)
    to_b_records_local_path: Path = field(init=False)
    to_c_records_count: int = field(init=False)
    to_b_records_count: int = field(init=False)
    to_c_records_remote_url: str = field(init=False)
    to_b_records_remote_url: str = field(init=False)
    min_area: float = field(init=False)
    max_area: float = field(init=False)
    max_area_ratio: float = field(init=False)
    max_overlap_ratio: float = field(init=False)
    competitor_records: List[CompetitorRecord] = field(default_factory=list)
    src_batch_records: List[SrcBatchRecord] = field(default_factory=list)
    intelligence_records: List[IntelligentRecord] = field(default_factory=list)

    def add_competitor_record(self, **record):
        """
        添加一条竞品记录
        """
        # noinspection PyTypeChecker
        competitor_record = CompetitorRecord(
            aoi_id=record.get('aoi_id', ''),
            bid=record.get('bid', ''),
            name=record.get('name', ''),
            competitor_name=record.get('competitor_name', ''),
            create_time=record.get('create_time', ''),
            std_tag=record.get('std_tag', ''),
            original_geom=record.get('original_geom', None),
            reason=record.get('reason', ''),
            can_process=record.get('can_process', True),
            pv=record.get('pv', -1),
        )
        self.competitor_records.append(competitor_record)

    def apply_config(self, config):
        """
        切换配置
        """
        self.min_area = config['min_area']
        self.max_area = config['max_area']
        self.max_area_ratio = config['max_area_ratio']
        self.max_overlap_ratio = config['max_overlap_ratio']


def make_valid(record, prefix):
    """
    确保记录中的 geom 有效
    """
    if not record.geom:
        record.reason = f'{prefix}, none geometry'
        record.can_process = False
        return False

    record.geom = record.geom.buffer(0)

    if not record.geom.is_valid:
        record.reason = f'{prefix}, invalid geometry'
        record.can_process = False
        return False

    if record.geom.geom_type == 'MultiPolygon':
        record.geom = multipolygon_to_single(record.geom, 0)

    if record.geom.geom_type != 'Polygon':
        record.reason = f'{prefix}, not polygon'
        record.can_process = False
        return False

    if any(record.geom.interiors):
        record.geom = Polygon(record.geom.exterior)

    if record.geom.is_empty:
        record.reason = f'{prefix}, empty geometry'
        record.can_process = False
        return False

    return True


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(upload_file_url, local_file_path):
    """
    上传文件
    """
    files = {'file': open(local_file_path, 'r', encoding='utf-8')}
    file_uuid = requests.post(upload_file_url, files=files).text
    if len(file_uuid) != 32:
        raise Exception(f'upload file failed')

    return file_uuid


def load_records_by_bids(ctx: Context, bids):
    """
    加载竞品数据
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        for bid in tqdm(bids):
            # 填充竞品信息
            sql = '''
                select name, st_astext(geom), create_time
                from aoi_intelligence_history
                where bid = %s
                order by create_time desc
                limit 1;
            '''
            record = stabilizer.fetch_one(sql, (bid,))
            if not record:
                ctx.add_competitor_record(bid=bid, reason='load_records, no competitor', can_process=False)
                continue

            competitor_name, wkt, create_time = record

            # 填充 poi 信息
            sql = '''select name, std_tag, click_pv from poi where bid = %s;'''
            record = stabilizer.fetch_one(sql, (bid,))
            if not record:
                ctx.add_competitor_record(bid=bid, reason='load_records, invalid poi', can_process=False)
                continue

            name, std_tag, click_pv = record
            if std_tag not in tags.COMPETITOR_CAN_ONLINE:
                ctx.add_competitor_record(bid=bid, reason='load_records, invalid tag', can_process=False)
                continue

            # 创建记录
            ctx.add_competitor_record(
                aoi_id=uuid.uuid4().hex,
                bid=bid,
                name=name,
                competitor_name=competitor_name,
                create_time=create_time,
                std_tag=std_tag,
                original_geom=shapely.wkt.loads(wkt),
                pv=click_pv,
            )


def load_bids_by_competitor():
    """
    加载竞品数据的 bid 集合
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        sql = '''
            select distinct bid 
            from aoi_intelligence_history
            where create_time >= current_date - interval '5 day' and 
                  create_time < current_date;
        '''
        return [x[0] for x in stabilizer.fetch_all(sql)]


def load_bids_by_stock_to_b_aoi():
    """
    加载存量 B 端边框对应的 bid 集合
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as stabilizer:
        sql = '''
            select b.poi_bid
            from blu_face a
            inner join blu_face_poi b 
            on a.face_id = b.face_id
            where a.src = 'SD';
        '''
        return [x[0] for x in stabilizer.fetch_all(sql)]


def load_bids_by_file(ctx: Context):
    """
    直接从文件中读取 bid 集合
    """
    with open(ctx.bid_list_path, 'r', encoding='utf-8') as bid_file:
        return [x.strip() for x in bid_file.readlines()]


def load_bids_by_fail_records(ctx: Context):
    """
    从上线失败的记录中读取 bid 集合
    """
    fail_file = ctx.work_dir / 'fail'
    if not fail_file.exists():
        return []

    bids = [x[0] for x in tsv.read_tsv(ctx.work_dir / 'fail')]
    fail_file.unlink(missing_ok=True)

    return bids


def load_intelligence(ctx: Context):
    """
    加载边框情报记录
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        sql = '''
            select main_poi_bid, ref_qb_id
            from integration_qb 
            where work_type = 1 and  -- 批处理的任务
                  src = 4 and  -- 边框产线
                  status = 1;  -- 处理中的任务
        '''
        for bid, ref_qb_id in stabilizer.fetch_all(sql):
            ctx.intelligence_records.append(
                IntelligentRecord(
                    id=ref_qb_id,
                    bid=bid,
                )
            )


def load_bids_by_intelligence(ctx: Context):
    """
    提取边框情报中的 bid 集合
    """
    load_intelligence(ctx)
    return [x.bid for x in ctx.intelligence_records]


def load_bids_by_intelligence_history(load_count=200000):
    """
    加载历史的竞品数据,同时控制下量级
    :param load_count:  一次性加载的量级
    :return:
    """
    key = "AOI_INTELLIGENCE_HISTORY_MAX_ID"
    with aoi_ml_model.AoiMlModel() as model:
        model.connection.ping(reconnect=True)
        max_id = model.get_store(key)
        if not max_id:
            max_id = 0
        else:
            try:
                max_id = int(max_id)
            except Exception as e:
                print("max id load err", e.args)
                max_id = 0
        new_max_id = 0
        bids = []
        with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
            sql = f'select id, bid from aoi_intelligence_history ' \
                  f'    where id > {max_id} order by id asc limit {load_count}'
            print("查询sql", sql)
            need_poi_list = poi_conn.fetch_all(sql)
            if len(need_poi_list) < 1:
                # 一轮已经结束了,跑下一轮
                model.connection.ping(reconnect=True)
                model.set_store(key, '0')
                return []
            else:
                for (_id, bid) in need_poi_list:
                    bids.append(bid)
                    if new_max_id < _id:
                        new_max_id = _id
                model.connection.ping(reconnect=True)
                model.set_store(key, str(new_max_id))
                return bids


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理记录
    """
    bids = []
    if 'competitor' in ctx.mode:
        bids.extend(load_bids_by_competitor())
    if 'competitor_history' in ctx.mode:
        bids.extend(load_bids_by_intelligence_history())
    if 'stock' in ctx.mode:
        bids.extend(load_bids_by_stock_to_b_aoi())
    if 'file' in ctx.mode:
        bids.extend(load_bids_by_file(ctx))
    if 'fail' in ctx.mode:
        bids.extend(load_bids_by_fail_records(ctx))
    if 'intelligence' in ctx.mode:
        bids.extend(load_bids_by_intelligence(ctx))

    load_records_by_bids(ctx, set(bids))
    proceed()


@desc()
def use_to_b_config(ctx: Context, proceed):
    """
    使用 toB 配置
    """
    ctx.apply_config(COMMERCIAL_CONFIG)
    proceed()


@desc()
def filter_records_by_area(ctx: Context, proceed):
    """
    通过面积过滤记录
    """

    def process(record: CompetitorRecord):
        if record.geom.area < ctx.min_area:
            record.can_process = False
            record.reason = 'filter_records_by_area, area too small'
        elif record.geom.area > ctx.max_area:
            record.can_process = False
            record.reason = 'filter_records_by_area, area too large'

    batch_process(ctx.competitor_records, process)
    proceed()


@desc()
def use_to_c_config(ctx: Context, proceed):
    """
    使用 toC 配置
    """
    ctx.apply_config(CLIENT_CONFIG)
    proceed()


@desc()
def filter_records_by_ugc_qb(ctx: Context, proceed):
    """
    在ugc列表中并且近期上报过，不自动转成c端
    """
    bid_map = {}
    day_30_before = datetime.datetime.now() - datetime.timedelta(days=20)
    with MysqlTool(name='beeflow') as mysql:
        sql = 'select bid, face_id,created_at from aoi_ugc_main_geom_change_info where created_at >= %s order by id asc'
        with mysql.connection.cursor() as cursor:
            cursor.execute(sql, [day_30_before.strftime('%Y-%m-%d')])
            records = cursor.fetchall()
            for bid, face_id, created_at in records:
                bid_map[bid] = {
                    'face_id': face_id,
                    'created_at': created_at
                }

    # print("在ugc列表中的主点：", bid_map)

    def process(record: CompetitorRecord):
        info = bid_map.get(record.bid, None)
        if info:
            record.can_online_to_c = False
            record.can_process = False
            record.reason = 'filter_records_by_by_ugc, has changed in last 20 days'

    batch_process(ctx.competitor_records, process, can_process=lambda x: x.can_process and x.can_online_to_c)
    proceed()


@desc()
def filter_records_by_online_bid(ctx: Context, proceed):
    """
    过滤在线边框
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as stab:
        def process(record: CompetitorRecord):
            sql = f'''
                select 1 
                from blu_face a
                inner join blu_face_poi b
                on a.face_id = b.face_id
                where b.poi_bid = %s and
                      a.src != 'SD';
            '''
            if stab.fetch_one(sql, (record.bid,)):
                record.reason = 'has cd aoi'
                record.can_process = False

        batch_process(ctx.competitor_records, process, can_process=lambda x: x.can_process and x.can_online_to_c)

    proceed()


@desc()
def filter_records_by_overlap(ctx: Context, proceed):
    """
    过滤压盖边框
    """
    with (
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stabilizer,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer
    ):
        def process(record: CompetitorRecord):
            intersect_to_c_aois, _ = get_intersect_aois(aoi_stabilizer, record.geom.wkt)
            if overlap_aoi_too_much(ctx, record, poi_stabilizer, intersect_to_c_aois, tags.ADMIN):
                record.can_online_to_c = False
                record.reason = 'filter_records_by_overlap, overlap to c'

        batch_process(ctx.competitor_records, process, can_process=lambda x: x.can_process and x.can_online_to_c)

    proceed()


@desc()
def filter_records_by_overlap_road_area(ctx: Context, proceed):
    """
    通过压路面积过滤记录
    """
    max_overlap_ratio = 0.5

    with AoiModel() as aoi_model:
        def process(record: CompetitorRecord):
            try:
                processed_geom = shapely.wkt.loads(record.geom.wkt)
                processed_geom = cut_out_road_level7(processed_geom, aoi_model)
                processed_geom = processed_geom.buffer(0)
                if (record.geom.area - processed_geom.area) / record.geom.area > max_overlap_ratio:
                    record.can_online_to_c = False
                    record.reason = 'filter_records_by_overlap_road_area, overlap too much'
            except Exception as e:
                print(e)
                record.can_online_to_c = False
                record.reason = 'filter_records_by_overlap_road_area, error on get road'

        batch_process(ctx.competitor_records, process, can_process=lambda x: x.can_process and x.can_online_to_c)

    proceed()


def get_max_iou_aoi(poi_conn, geom, intersect_aois):
    """
    获取 iou 最大的边框
    """
    max_iou = 0
    overlap_bid = ''
    overlap_name = ''
    overlap_std_tag = ''
    overlap_face_id = ''
    overlap_wkt = ''
    overlap_mesh_id = ''
    overlap_pv = -1

    for face_id, wkt, bid, aoi_level, mesh_id in intersect_aois:
        overlap_aoi_geom = shapely.wkt.loads(wkt)
        intersection = geom.intersection(overlap_aoi_geom)
        union = geom.union(overlap_aoi_geom)
        iou = intersection.area / union.area

        sql = '''select name, std_tag, click_pv from poi where bid = %s;'''
        row = poi_conn.fetch_one(sql, (bid,))
        if not row:
            continue

        name, std_tag, click_pv = row

        if iou > max_iou:
            max_iou = iou
            overlap_bid = bid
            overlap_name = name
            overlap_std_tag = std_tag
            overlap_face_id = face_id
            overlap_wkt = wkt
            overlap_mesh_id = mesh_id
            overlap_pv = click_pv

    return max_iou, overlap_bid, overlap_name, overlap_std_tag, overlap_face_id, overlap_wkt, overlap_mesh_id, \
        overlap_pv


@desc()
def filter_records_by_iou_of_to_b_aoi(ctx: Context, proceed):
    """
    通过何商单边框的交并比过滤记录
    """
    min_iou = 0.8

    with (
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stabilizer,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer,
    ):
        def process(record: CompetitorRecord):
            if record.overlap_mode != 'overlap_to_b':
                return

            _, intersect_to_b_aois = get_intersect_aois(aoi_stabilizer, record.geom.wkt)
            max_iou, overlap_bid, overlap_name, overlap_std_tag, overlap_face_id, overlap_wkt, overlap_mesh_id, \
                overlap_pv = get_max_iou_aoi(poi_stabilizer, record.geom, intersect_to_b_aois)

            if max_iou < min_iou:
                record.can_process = False
                record.reason = 'filter_records_by_iou_of_to_b_aoi, iou too small'
            else:
                record.overlap_bid = overlap_bid
                record.overlap_face_id = overlap_face_id
                record.overlap_name = overlap_name
                record.overlap_std_tag = overlap_std_tag
                record.overlap_wkt = overlap_wkt
                record.overlap_mesh_id = overlap_mesh_id

        batch_process(ctx.competitor_records, process, can_process=lambda x: x.can_process and x.can_online_to_c)

    proceed()


@desc()
def filter_records_by_poi_validation(ctx: Context, proceed):
    """
    过滤主点失效的记录
    """
    search_radius = 0  # 工艺说要改成 0 ，但是为了防止 TA 后悔，下面的冗余代码先不删除。

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        def process(record: CompetitorRecord):
            sql = '''select std_tag, st_astext(geometry) from poi where bid = %s;'''
            row = stabilizer.fetch_one(sql, (record.bid,))
            if not row:
                record.reason = 'filter_records_by_poi_validation, no bid'
                record.can_process = False
                return

            std_tag, wkt = row
            if std_tag not in tags.COMPETITOR_CAN_ONLINE:
                record.reason = 'filter_records_by_poi_validation, invalid tag'
                record.can_process = False
                return

            poi_geom = shapely.wkt.loads(wkt)
            if not record.geom.buffer(search_radius).buffer(0).contains(poi_geom):
                record.reason = 'filter_records_by_poi_validation, poi outside'
                record.can_process = False

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def fill_overlap_mode(ctx: Context, proceed):
    """
    填充压盖模式
    """
    with (
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stabilizer,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer,
    ):
        def process(record: CompetitorRecord):
            intersect_to_c_aois, intersect_to_b_aois = get_intersect_aois(aoi_stabilizer, record.geom.wkt)
            if overlap_aoi_too_much(ctx, record, poi_stabilizer, intersect_to_c_aois, tags.ADMIN):
                record.overlap_mode = 'overlap_to_c'
                record.can_process = False
                record.reason = 'fill_overlap_mode, overlap to c'
            elif overlap_aoi_too_much(ctx, record, poi_stabilizer, intersect_to_b_aois):
                record.overlap_mode = 'overlap_to_b'
                record.src = 'b to c'
            else:
                max_iou, overlap_bid, overlap_name, overlap_std_tag, overlap_face_id, overlap_wkt, overlap_mesh_id, \
                    overlap_pv = get_max_iou_aoi(poi_stabilizer, record.geom, intersect_to_c_aois)
                record.overlap_mode = 'no_overlap'
                record.overlap_bid = overlap_bid
                record.overlap_face_id = overlap_face_id
                record.overlap_mesh_id = overlap_mesh_id
                record.overlap_name = overlap_name
                record.overlap_std_tag = overlap_std_tag
                record.overlap_wkt = overlap_wkt
                record.overlap_pv = overlap_pv
                record.aoi_level = BASIC_AOI

        batch_process(ctx.competitor_records, process)

    proceed()


def overlap_aoi(ctx, record, overlap_wkt):
    """
    判断是否压盖边框
    """
    overlap_aoi_geom = shapely.wkt.loads(overlap_wkt)
    area_ratio = record.geom.area / overlap_aoi_geom.area
    if area_ratio > ctx.max_area_ratio:
        overlap_ratio = record.geom.intersection(overlap_aoi_geom).area / record.geom.area
        if overlap_ratio > ctx.max_overlap_ratio:
            return True


def overlap_aoi_too_much(ctx, record, poi_conn, intersect_aois, ignored_tags=None):
    """
    判断是否大面积压盖边框
    """
    for face_id, wkt, bid, aoi_level, mesh_id in intersect_aois:
        sql = '''select name, std_tag, click_pv from poi where bid = %s;'''
        poi = poi_conn.fetch_one(sql, (bid,))
        if not poi:
            continue

        name, std_tag, click_pv = poi

        if ignored_tags and any(ignored_tags):
            if std_tag in ignored_tags:
                continue

        if overlap_aoi(ctx, record, wkt):
            record.overlap_face_id = face_id
            record.overlap_mesh_id = mesh_id
            record.overlap_name = name
            record.overlap_std_tag = std_tag
            record.overlap_wkt = wkt
            record.overlap_bid = bid
            record.overlap_pv = click_pv
            record.original_aoi_level = aoi_level
            return True

    return False


def get_intersect_aois(aoi_stabilizer, aoi_wkt):
    """
    获取相交的边框
    """
    intersect_to_c_aois = []
    intersect_to_b_aois = []

    sql = '''
        select a.face_id, a.src, st_astext(a.geom), b.poi_bid, a.aoi_level, a.mesh_id
        from blu_face a
        left join blu_face_poi b
        on a.face_id = b.face_id
        where st_intersects(geom, st_geomfromtext(%s, 4326)) and
              a.kind != '52';
    '''
    for face_id, src, wkt, bid, aoi_level, mesh_id in aoi_stabilizer.fetch_all(sql, (aoi_wkt,)):
        overlap_info = (face_id, wkt, bid, aoi_level, mesh_id)
        if src != 'SD':
            intersect_to_c_aois.append(overlap_info)
        else:
            intersect_to_b_aois.append(overlap_info)

    return intersect_to_c_aois, intersect_to_b_aois


def get_intersect_to_c_aoi_records(aoi_stabilizer, poi_conn, current_bid, current_wkt, ignored_tags=None):
    """
    获取所有和 C 端边框相交的记录
    """
    intersects_to_c_aois, _ = get_intersect_aois(aoi_stabilizer, current_wkt)
    for face_id, wkt, bid, aoi_level, mesh_id in intersects_to_c_aois:
        if bid == current_bid:
            continue

        sql = f'''select name, std_tag from poi where bid = %s;'''
        row = poi_conn.fetch_one(sql, (bid,))
        if not row:
            continue

        name, std_tag = row
        if ignored_tags and any(ignored_tags):
            if std_tag in ignored_tags:
                continue

        yield AoiRecord(
            face_id=face_id,
            bid=bid,
            name=name,
            geom=shapely.wkt.loads(wkt),
            aoi_level=aoi_level,
        )


@desc()
def fill_overlap_to_b_intersects_aois(ctx: Context, proceed):
    """
    为 toB 记录填充相交的边框
    """
    with (
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stabilizer,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer,
    ):
        def process(record: CompetitorRecord):
            if record.overlap_mode != 'overlap_to_b':
                return

            record.intersects_aois.extend(get_intersect_to_c_aoi_records(
                aoi_stabilizer,
                poi_stabilizer,
                record.overlap_bid,
                record.overlap_wkt,
                tags.ADMIN,
            ))

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def fill_overlap_to_b_aoi_level_by_relation(ctx: Context, proceed):
    """
    通过关联关系填充 toB 记录的 aoi_level
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        def process(record: CompetitorRecord):
            if record.overlap_mode != 'overlap_to_b':
                return

            if record.aoi_level > 0:
                return

            overlap_geom = shapely.wkt.loads(record.overlap_wkt)

            for aoi in record.intersects_aois:
                if not aoi.is_main_bid_valid or not contains_majority(overlap_geom, aoi.geom):
                    continue

                sql = '''select relation_bid from poi where bid = %s;'''
                row = poi_stabilizer.fetch_one(sql, (aoi.bid,))
                if not row:
                    continue

                relation_bid, = row
                if relation_bid != record.overlap_bid:
                    continue

                record.aoi_level = CLUSTER_AOI
                break

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def fill_overlap_to_b_aoi_level_by_spatial(ctx: Context, proceed):
    """
    通过空间关系填充 toB 记录的 aoi_level
    """

    def process(record: CompetitorRecord):
        if record.overlap_mode != 'overlap_to_b':
            return

        if record.aoi_level > 0:
            return

        overlap_geom = shapely.wkt.loads(record.overlap_wkt)

        for aoi in record.intersects_aois:
            if aoi.aoi_level == BASIC_AOI and contains_majority(aoi.geom, overlap_geom):
                record.aoi_level = INNER_AOI
            elif aoi.aoi_level == BASIC_AOI and contains_majority(overlap_geom, aoi.geom):
                record.aoi_level = CLUSTER_AOI

    batch_process(ctx.competitor_records, process)
    proceed()


def get_online_wkt(record: CompetitorRecord):
    """
    获取上线使用的 wkt
    """
    if record.overlap_mode == 'overlap_to_b':
        return record.overlap_wkt
    if record.overlap_mode == 'no_overlap':
        return record.geom.wkt
    return None


@desc()
def check_by_poi_aggregation(ctx: Context, proceed):
    """
    通过 poi 聚合进行法务风险规避
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        def process(record: CompetitorRecord):
            sql = '''
                select st_astext(geometry) from poi where relation_bid = %s or bid = %s;
            '''
            points = []
            for wkt, in stabilizer.fetch_all(sql, (record.bid, record.bid)):
                points.append(shapely.wkt.loads(wkt))

            if len(points) < 2:
                return

            convex_hull = MultiPoint(points).convex_hull
            online_geom = shapely.wkt.loads(get_online_wkt(record))
            if convex_hull.intersects(online_geom):
                record.intersects_poi_aggregation = True
                record.poi_aggregation_wkt = convex_hull.wkt

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def check_by_experience_track(ctx: Context, proceed):
    """
    通过经验轨迹进行法务风险规避
    """
    buffer_radius = 30e-5

    with get_provider('exp') as provider:
        def process(record: CompetitorRecord):
            buffered_geom = record.geom.buffer(buffer_radius)
            for track in provider.get_tracks(record.bid, 'bid', ()):
                track_wkt = track['end_track_line']
                track_geom = shapely.wkt.loads(track_wkt)
                if track_geom.intersects(buffered_geom):
                    record.intersects_experience_track = True
                    record.experience_track_wkt = track_wkt
                    break

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def filter_records_by_kind(ctx: Context, proceed):
    """
    根据种别过滤记录
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as stab:
        def process(record: CompetitorRecord):
            if record.overlap_mode != 'overlap_to_b':
                return

            sql = '''
                select 1 from blu_face where face_id = %s and kind = '52';
            '''
            if stab.fetch_one(sql, (record.overlap_face_id,)):
                record.can_process = False
                record.reason = 'business aoi'

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def save_overlap_to_b_records(ctx: Context, proceed):
    """
    保存 toB 记录
    """
    output_items = []

    def process(record: CompetitorRecord):
        if record.overlap_mode != 'overlap_to_b':
            return

        output_items.append(
            [
                record.bid,
                record.competitor_name,
                record.geom.wkt,
                record.overlap_face_id,
                record.overlap_bid,
                record.overlap_name,
                record.overlap_wkt,
            ]
        )

    batch_process(ctx.competitor_records, process)
    tsv.write_tsv(
        ctx.work_dir / f'overlap_to_b_{ctx.now}.tsv',
        output_items,
    )
    proceed()


@desc()
def save_no_overlap_records(ctx: Context, proceed):
    """
    保存无任何压盖的记录
    """
    to_c_items = []
    to_b_items = []

    def process(record: CompetitorRecord):
        if record.overlap_mode != 'no_overlap':
            return

        line = [
            record.bid,
            record.competitor_name,
            record.geom.wkt,
            record.overlap_bid,
            record.overlap_name,
            record.overlap_wkt,
        ]

        if record.can_online_to_c:
            to_c_items.append(line)
        else:
            to_b_items.append(line)

    batch_process(ctx.competitor_records, process)
    tsv.write_tsv(ctx.work_dir / f'no_overlap_to_c_{ctx.now}.tsv', to_c_items)
    tsv.write_tsv(ctx.work_dir / f'no_overlap_to_b_{ctx.now}.tsv', to_b_items)
    proceed()


@desc()
def generate_src_batch_records(ctx: Context, proceed):
    """
    生成 SRC 批处理记录
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        def process(record: CompetitorRecord):
            sql = '''select mid from poi where bid = %s;'''
            row = poi_stabilizer.fetch_one(sql, (record.bid,))
            if not row:
                record.can_process = False
                record.reason = 'no mid'
                return

            mid, = row

            ctx.src_batch_records.append(SrcBatchRecord(
                bid=record.bid,
                mid=mid,
                face_id=record.overlap_face_id,
                mesh_id=record.overlap_mesh_id,
                src='CD',
            ))

        batch_process(ctx.competitor_records, process, can_process=lambda x: x.can_b_to_c() and x.is_legally_safe())

    proceed()


@desc()
def execute_src_batch(ctx: Context, proceed):
    """
    执行 SRC 批处理
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
        for record in tqdm(ctx.src_batch_records):
            result = update_src_of_aoi(back_stabilizer, record.face_id, record.src, record.bid, record.mid)
            if not result.success:
                print(result.msg)

    proceed()


@desc()
def shake_geom(ctx: Context, proceed):
    """
    对 wkt 进行随机抖动
    """

    def process(record: CompetitorRecord):
        if record.overlap_mode != 'no_overlap':
            return

        if not make_valid(record, 'shake_geom'):
            return

        record.geom = record.geom.simplify(SIMPLIFY_TOLERANCE, preserve_topology=True)
        coords = [
            (
                x[0] + random.uniform(-MAX_OFFSET, MAX_OFFSET),
                x[1] + random.uniform(-MAX_OFFSET, MAX_OFFSET)
            )
            for x in record.geom.exterior.coords
        ]
        record.geom = Polygon(coords)
        make_valid(record, 'shake_geom')

    batch_process(ctx.competitor_records, process)
    proceed()


@desc()
def smooth_geom(ctx: Context, proceed):
    """
    平滑 wkt
    """
    min_area = 1600e-10

    def process(record: CompetitorRecord):
        if record.overlap_mode != 'no_overlap':
            return

        record.geom = record.geom.simplify(SIMPLIFY_TOLERANCE, preserve_topology=True)
        if not make_valid(record, 'smooth_geom'):
            return

        if record.geom.area >= min_area:
            record.geom = shapely.wkt.loads(smooth_polygon(record.geom.wkt))
            if not make_valid(record, 'smooth_geom'):
                return

        record.geom = shapely.set_precision(record.geom, PRECISION)

    batch_process(ctx.competitor_records, process)
    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 POI 属性
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        def process(record: CompetitorRecord):
            if record.overlap_mode != 'no_overlap':
                return

            sql = '''select mid, city from poi where bid = %s;'''
            poi = stabilizer.fetch_one(sql, (record.bid,))
            if not poi:
                record.can_process = False
                record.reason = 'fill_poi_properties, poi_not_found'
                return

            mid, city = poi
            record.mid = mid
            record.city = city

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def fill_no_overlap_intersects_aois(ctx: Context, proceed):
    """
    为 toC 记录填充相交的边框
    """
    with (
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stabilizer,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer,
    ):
        def process(record: CompetitorRecord):
            record.intersects_aois.extend(get_intersect_to_c_aoi_records(
                aoi_stabilizer,
                poi_stabilizer,
                record.bid,
                record.geom.wkt,
                tags.ADMIN,
            ))

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def fill_no_overlap_aoi_level_by_relation(ctx: Context, proceed):
    """
    通过关联关系填充 toC 记录的 aoi_level
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        def process(record: CompetitorRecord):
            if record.aoi_level != BASIC_AOI:
                return

            for aoi in record.intersects_aois:
                if not aoi.is_main_bid_valid or not contains_majority(record.geom, aoi.geom):
                    continue

                sql = '''select relation_bid from poi where bid = %s;'''
                row = poi_stabilizer.fetch_one(sql, (aoi.bid,))
                if not row:
                    continue

                relation_bid, = row
                if relation_bid != record.bid:
                    continue

                record.aoi_level = CLUSTER_AOI
                break

        batch_process(ctx.competitor_records, process)

    proceed()


@desc()
def fill_no_overlap_aoi_level_by_spatial(ctx: Context, proceed):
    """
    通过空间关系填充 toC 记录的 aoi_level
    """

    def process(record: CompetitorRecord):
        if record.aoi_level != BASIC_AOI:
            return

        for aoi in record.intersects_aois:
            if aoi.aoi_level == BASIC_AOI and contains_majority(aoi.geom, record.geom):
                record.aoi_level = INNER_AOI
            elif aoi.aoi_level == BASIC_AOI and contains_majority(record.geom, aoi.geom):
                record.aoi_level = CLUSTER_AOI

    batch_process(ctx.competitor_records, process)
    proceed()


def save_records(records: List[CompetitorRecord], file_name: Path):
    """
    保存记录信息
    """
    if not any(records):
        return

    tsv.write_tsv(
        file_name,
        [
            [
                x.bid,
                x.mid,
                x.name,
                x.city,
                x.geom.wkt,
                x.aoi_id,
                x.aoi_level,
                '',
            ]
            for x in records
        ]
    )


@desc()
def fill_record_src(ctx: Context, proceed):
    """
    填充记录来源
    """
    for record in [
        x for x in ctx.competitor_records
        if x.overlap_mode == 'no_overlap' and x.can_process and x.can_online_to_c
    ]:
        record.src = 'to c'

    for record in [
        x for x in ctx.competitor_records
        if x.overlap_mode == 'no_overlap' and x.can_process and not x.can_online_to_c
    ]:
        record.src = 'to b'

    proceed()


@desc()
def filter_records_by_online_aoi(ctx: Context, proceed):
    """
    过滤在线边框
    """
    min_iou = 0.8
    get_online_aoi_sql = '''
        select st_astext(a.geom)
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s
        limit 1;
    '''

    with PgsqlStabilizer(pgsql.BACK_CONFIG) as poi_stabilizer:
        for record in [x for x in ctx.competitor_records if x.can_to_b() or (x.can_to_c() and x.is_legally_safe())]:
            row = poi_stabilizer.fetch_one(get_online_aoi_sql, (record.bid,))
            if row:
                # 线上存在 bid 相同的边框
                if record.can_to_b():
                    # toB 的直接忽略，没必要花人力作业。
                    record.can_process = False
                    record.reason = 'filter_records_by_online_aoi, online_aoi_exists'
                elif record.can_to_c() and record.is_legally_safe():
                    # toC 的看下交并比，如果大于阈值，则需要送去人工作业。
                    wkt, = row
                    geom = shapely.wkt.loads(wkt)
                    iou = get_geom_iou(record.geom, geom)
                    if iou > min_iou:
                        # B 转 C
                        record.should_b_to_c = True
                    else:
                        # 下发人工
                        record.can_process = False
                        record.should_manual_process = True

    proceed()


@desc()
def save_online_to_c_records(ctx: Context, proceed):
    """
    保存 toC 记录
    """
    to_c_records = [x for x in ctx.competitor_records if x.can_to_c() and x.is_legally_safe()]
    ctx.to_c_records_count = len(to_c_records)
    ctx.to_c_records_local_path = ctx.work_dir / f'{DEBUG}to_c_{ctx.now}.tsv'
    save_records(to_c_records, ctx.to_c_records_local_path)

    proceed()


@desc()
def save_online_to_b_records(ctx: Context, proceed):
    """
    保存 toB 记录
    """
    to_b_records = [x for x in ctx.competitor_records if x.can_to_b()]
    ctx.to_b_records_count = len(to_b_records)
    ctx.to_b_records_local_path = ctx.work_dir / f'{DEBUG}to_b_{ctx.now}.tsv'
    save_records(to_b_records, ctx.to_b_records_local_path)

    proceed()


@desc()
def enclose_intelligence(ctx: Context, proceed):
    """
    闭环边框情报
    """
    status_done = 2
    flow_status_done = 2

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stabilizer:
        for intelligence in tqdm(ctx.intelligence_records):
            sql = '''
                update integration_qb set status = %s, flow_status = %s where ref_qb_id = %s;
            '''
            poi_stabilizer.execute(sql, (status_done, flow_status_done, intelligence.id))

    proceed()


@desc()
def save_debug_records(ctx: Context, proceed):
    """
    保存记录的调试信息
    """
    tsv.write_tsv(
        ctx.work_dir / f'debug_{ctx.now}.tsv',
        [
            [
                x.can_process,
                x.reason,

                # 竞品边框
                x.bid,
                x.competitor_name,
                x.std_tag,
                x.pv,
                x.geom.wkt,
                x.geom.area * 1e10,

                x.original_aoi_level,
                x.original_aoi_level if x.aoi_level == 0 else x.aoi_level,
                x.src,

                # 压盖边框
                x.overlap_bid,
                x.overlap_name,
                x.overlap_std_tag,
                x.overlap_pv,
                x.overlap_wkt,
                shapely.wkt.loads(x.overlap_wkt).area * 1e10 if x.overlap_wkt != '' else 0,

                x.intersects_poi_aggregation,
                x.intersects_end_point_track,
                x.intersects_experience_track,

                x.overlap_mode,
                x.can_online_to_c,
                x.should_b_to_c,
                x.is_legally_safe(),
                x.should_manual_process,
            ]
            for x in ctx.competitor_records
        ]
    )

    proceed()


@desc()
def save_manual_working_tickets(ctx: Context, proceed):
    """
    保存人工作业清单
    """
    # 1. b2c 失败的下发人工
    #   1.1 交并比 < 0.8
    #   1.2 未过法务风险
    # 2. toc 未过法务风险的下发人工
    # 3. tob 下发人工
    iou_too_small_reason = 'filter_records_by_iou_of_to_b_aoi, iou too small'
    output_items = []

    for record in ctx.competitor_records:
        if (
            record.reason == iou_too_small_reason or
            (record.can_b_to_c() and not record.is_legally_safe()) or
            (record.can_to_c() and not record.is_legally_safe()) or
            record.can_to_b() or
            record.should_manual_process
        ) and record.pv >= MANUAL_WORK_MIN_PV:
            output_items.append([record.bid, record.name, record.std_tag, record.pv])
            record.src = 'manual'

    tsv.write_tsv(ctx.work_dir / f'{DEBUG}manual_working_tickets_{ctx.now}.tsv', output_items)
    proceed()


@desc()
def save_src_batch_records(ctx: Context, proceed):
    tsv.write_tsv(
        ctx.work_dir / f'{DEBUG}src_batch_{ctx.now}.tsv',
        [
            [
                x.bid,
                x.mid,
                x.face_id,
                x.mesh_id,
                x.src,
            ]
            for x in ctx.src_batch_records
        ]
    )

    proceed()


@desc()
def upload_online_records(ctx: Context, proceed):
    """
    上传需要推送上线的边框
    """

    def upload_records(local_path: Path):
        if not local_path.exists():
            return None

        file_uuid = upload_file(ctx.upload_file_url, local_path)
        return f'{ctx.download_file_url}&uuid={file_uuid}'

    ctx.to_c_records_remote_url = upload_records(ctx.to_c_records_local_path)
    ctx.to_b_records_remote_url = upload_records(ctx.to_b_records_local_path)

    proceed()


@desc()
def import_online_records_to_lib(ctx: Context, proceed):
    """
    创建边框上线任务
    """
    with PgsqlStabilizer(pgsql.POI_CONFIG) as stabilizer:
        sql = f'''
            insert into online_aoi_task(batch, src, url, count, memo) values(%s, %s, %s, %s, %s);
        '''
        if ctx.to_c_records_count > 0:
            to_c_batch = ctx.to_c_records_local_path.stem
            stabilizer.execute(sql, (to_c_batch, 'CD', ctx.to_c_records_remote_url, ctx.to_c_records_count, ''))

        if ctx.to_b_records_count > 0:
            to_b_batch = ctx.to_b_records_local_path.stem
            stabilizer.execute(sql, (to_b_batch, 'SD', ctx.to_b_records_remote_url, ctx.to_b_records_count, ''))

    proceed()


def parse_args():
    """
    解析参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        default='intelligence',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        # 加载竞品
        load_records,

        # B 端过滤
        use_to_b_config,
        filter_records_by_area,
        filter_records_by_poi_validation,

        # 判断压盖模式（压盖 C、压盖 B、不压盖）
        fill_overlap_mode,

        # 开始 C 端过滤
        use_to_c_config,
        filter_records_by_ugc_qb,
        filter_records_by_online_bid,
        filter_records_by_overlap,
        filter_records_by_overlap_road_area,
        filter_records_by_kind,
        filter_records_by_iou_of_to_b_aoi,

        # b 转 c
        fill_overlap_to_b_intersects_aois,
        fill_overlap_to_b_aoi_level_by_relation,
        fill_overlap_to_b_aoi_level_by_spatial,
        check_by_poi_aggregation,
        check_by_experience_track,
        save_overlap_to_b_records,
        save_no_overlap_records,

        # 无压盖（包括 toc、toB）
        shake_geom,
        smooth_geom,
        filter_records_by_area,
        fill_poi_properties,
        fill_no_overlap_intersects_aois,
        fill_no_overlap_aoi_level_by_relation,
        fill_no_overlap_aoi_level_by_spatial,
        fill_record_src,
        filter_records_by_online_aoi,
        generate_src_batch_records,
        save_online_to_c_records,
        save_online_to_b_records,
        save_manual_working_tickets,
        save_src_batch_records,
        save_debug_records,
        # 调试的时候先注释
        # 调试
        # b转c
        execute_src_batch,
        # 下发人工
        enclose_intelligence,
        # 产生新的记录
        upload_online_records,
        import_online_records_to_lib,
    )
    desc.attach(main_pipe)
    now = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/auto_business_to_customer'),
        batch=f"online_competitor_{now}",
        mode=args.mode,
        bid_list_path=Path(args.bid_list_path),
        upload_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei',
        download_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile',
        now=now,
    )
    ctx.work_dir.mkdir(parents=True, exist_ok=True)
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
