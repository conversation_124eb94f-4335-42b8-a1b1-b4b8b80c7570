# !/usr/bin/env python3
"""
例行收集上线失败记录
"""
import datetime
import shutil
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.file_downloader import download_file_by_http, download_file_by_afs

desc = pipeline.get_desc()


@dataclass
class OnlineTask:
    """
    存储上线任务的相关信息。
    """
    batch: str
    src: str
    url: str
    path: Path = field(init=False)
    count: int


@dataclass
class OnlineRecord:
    """
    上线记录
    """
    bid: str
    mid: str
    name: str
    city: str
    wkt: str
    aoi_id: str
    aoi_level: str
    remark: str


@dataclass
class Context:
    """
    脚本上下文。
    """
    work_dir: Path
    online_tasks: list[OnlineTask] = field(default_factory=list)

    commercial_records: list[OnlineRecord] = field(default_factory=list)
    commercial_online_records: list[OnlineRecord] = field(default_factory=list)
    commercial_offline_records: list[OnlineRecord] = field(default_factory=list)

    client_records: list[OnlineRecord] = field(default_factory=list)
    client_online_records: list[OnlineRecord] = field(default_factory=list)
    client_offline_records: list[OnlineRecord] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fetch_tasks(ctx: Context, proceed):
    """
    获取边框任务信息。
    """
    sql = '''
        select batch, src, url, count 
        from online_aoi_task 
        where batch != 'recognition_20231226' and
              DATE(created_at) < (%s - interval '7 day') and
              memo != '作废';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ctx.online_tasks = [
            OnlineTask(
                batch=batch,
                src=src,
                url=url,
                count=count,
            )
            for batch, src, url, count in stab.fetch_all(sql, [datetime.date.today()])
        ]

    proceed()


def download_file(url, save_path: Path):
    """
    下载文件。
    """
    if url.startswith('http'):
        download_file_by_http(url, save_path)
    elif url.startswith('/user/map-data-streeview'):
        download_file_by_afs(url, save_path)


@desc()
def download_tasks(ctx: Context, proceed):
    """
    下载任务数据。
    """
    ctx.work_dir.mkdir(parents=True, exist_ok=True)

    for task in tqdm(ctx.online_tasks):
        task.path = ctx.work_dir / f'{task.batch}.tsv'
        try:
            download_file(task.url, task.path)
        except Exception as e:
            print(e)

    proceed()


@desc()
def load_desired_online_face_ids(ctx: Context, proceed):
    """
    加载 AOI 上线数据。
    """

    def load(record_path):
        for bid, mid, name, city, wkt, aoi_id, aoi_level, remark in tsv.read_tsv(record_path):
            yield OnlineRecord(
                bid=bid,
                mid=mid,
                name=name,
                city=city,
                wkt=wkt,
                aoi_id=aoi_id,
                aoi_level=aoi_level,
                remark=remark,
            )

    commercial_paths = [task.path for task in ctx.online_tasks if 'SD' == task.src]
    client_paths = [task.path for task in ctx.online_tasks if 'CD' == task.src]

    for path in tqdm(commercial_paths):
        try:
            ctx.commercial_records.extend(load(path))
        except:
            print(f'error when loading file {path}, skipping')

    for path in tqdm(client_paths):
        try:
            ctx.client_records.extend(load(path))
        except:
            print(f'error when loading file {path}, skipping')

    proceed()


@desc()
def get_actual_online_count(ctx: Context, proceed):
    """
    获取实际上线数量。
    """
    sql = f'''select 1 from blu_face_poi where poi_bid = %s;'''

    def check_status(records: list[OnlineRecord]):
        online_records = []
        offline_records = []

        with PgsqlStabilizer(pgsql.BACK_CONFIG) as stab:
            for record in tqdm(records):
                if stab.fetch_one(sql, (record.bid,)):
                    online_records.append(record)
                else:
                    offline_records.append(record)

        return online_records, offline_records

    ctx.commercial_online_records, ctx.commercial_offline_records = check_status(ctx.commercial_records)
    ctx.client_online_records, ctx.client_offline_records = check_status(ctx.client_records)

    proceed()


@desc()
def save_offline_records(ctx: Context, proceed):
    """
    保存上线失败的记录
    """
    bids = set(x.bid for x in ctx.client_offline_records)
    tsv.write_tsv(
        Path('/home/<USER>/chenjie/auto_business_to_customer') / 'fail',
        [
            [
                x,
            ]
            for x in bids
        ]
    )
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行收集上线失败记录脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d5070dd11c100081e2110cb89f9e71680'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        fetch_tasks,
        download_tasks,
        load_desired_online_face_ids,
        get_actual_online_count,
        save_offline_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_collect_fail_online_records"),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)
    finally:
        shutil.rmtree(ctx.work_dir, ignore_errors=True)


if __name__ == "__main__":
    main()
