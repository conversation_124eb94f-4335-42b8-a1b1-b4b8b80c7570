# !/usr/bin/env python3
"""
支持自动根据情报创建工单
"""
import datetime
import uuid
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.batch_helper import make_competitor_valid, batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv
from src.tools.unity_platform_postman import Ticket, post_tickets

desc = pipeline.get_desc()


@dataclass
class Intelligence:
    """
    情报信息
    """
    id: str
    bid: str
    ref_qb_batch_id: str
    strategy_type: str
    memo: str
    status: int = 0
    is_primary: bool = False
    is_redundant: bool = False
    can_process: bool = True
    reason: str = ''

    def get_status(self):
        status_doing = 1
        status_redundant = 8

        if self.is_redundant:
            return status_redundant

        return status_doing


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    all_intelligences: list[Intelligence] = field(default_factory=list)
    valid_intelligences_map: dict[str, Intelligence] = field(default_factory=dict)
    competitors: list = field(default_factory=list)

    def add_intelligence(self, intelligence):
        """
        添加情报
        """
        # 自动化任务优先于人工任务

        self.all_intelligences.append(intelligence)

        if intelligence.bid in self.valid_intelligences_map:
            old_intelligence = self.valid_intelligences_map[intelligence.bid]
            if old_intelligence.is_primary or not intelligence.is_primary:
                intelligence.is_redundant = True
                intelligence.reason = '情报冗余'
            else:
                self.valid_intelligences_map[intelligence.bid] = intelligence
                old_intelligence.is_redundant = True
                old_intelligence.reason = '情报冗余'
        else:
            self.valid_intelligences_map[intelligence.bid] = intelligence


@desc()
def fetch_intelligences_from_unity_platform(ctx: Context, proceed):
    """
    从一体化平台获取挖掘情报
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        sql = '''
            select ref_qb_id, ref_qb_batch_id, main_poi_bid, strategy_type, memo
            from integration_qb 
            where status = 0 and  -- 待创建工单
                  flow_status = 1 and  -- 整合完成
                  qb_type = 2 and  -- 挖掘情报
                  src = 4;  -- 需要制作边框
        '''
        for intelligence_id, ref_qb_batch_id, main_poi_bid, strategy_type, memo in stabilizer.fetch_all(sql):
            ctx.add_intelligence(
                Intelligence(
                    id=intelligence_id,
                    bid=main_poi_bid,
                    ref_qb_batch_id=ref_qb_batch_id,
                    strategy_type=strategy_type,
                    is_primary=strategy_type == '',
                    memo=memo,
                )
            )

    proceed()


@desc()
def check_redundancy(ctx: Context, proceed):
    """
    情报检查冗余
    """
    src_aoi = 4  # AOI边框产线
    qb_type_common = 1  # 一般情报

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        sql = '''
            select 1 
            from integration_qb
            where main_poi_bid = %s and
                  src = %s and
                  qb_type = %s
            limit 1;
        '''

        def process(intelligence: Intelligence):
            if stabilizer.fetch_one(sql, (intelligence.bid, src_aoi, qb_type_common)):
                intelligence.is_redundant = True
                intelligence.reason = '情报冗余'

        batch_process(ctx.all_intelligences, process)

    proceed()


def get_query_competitor_sql():
    """
    获取查询竞争边框的 sql
    """
    return '''
        select bid,      
               intel_id,
               name,
               address,
               display_x,
               display_y,
               mesh_id,
               st_astext(geom),
               batch,
               crawl_time
        from aoi_intelligence_history 
        where bid = %s 
        order by create_time desc
        limit 1;
    '''


@desc()
def load_competitors(ctx: Context, proceed):
    """
    加载竞争边框
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        def process(record: Intelligence):
            competitor = stabilizer.fetch_one(get_query_competitor_sql(), (record.bid,))
            if competitor:
                _, _, _, _, _, _, _, wkt, _, _, = competitor
                geom = make_competitor_valid(shapely.wkt.loads(wkt))
                if geom:
                    ctx.competitors.append(competitor)

        batch_process(ctx.all_intelligences, process)

    proceed()


@desc()
def insert_competitors_to_lib(ctx: Context, proceed):
    """
    将历史竞品边框写入到实时库中方便创建计划
    """
    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
    ):
        try:
            for competitor in tqdm(ctx.competitors):
                sql = '''
                    insert into aoi_intelligence(
                        bid, 
                        intel_id, 
                        name, 
                        address, 
                        display_x, 
                        display_y, 
                        mesh_id, 
                        geom, 
                        batch, 
                        crawl_time
                    )
                    values(%s, %s, %s, %s, %s, %s, %s, st_geomfromtext(%s, 4326), %s, %s);
                '''
                cursor.execute(sql, competitor)

            conn.commit()
            proceed()
        except Exception as e:
            print(e)
            conn.rollback()


@desc()
def post_tickets_to_unity_platform(ctx: Context, proceed):
    """
    推送作业情报到一体化平台
    """
    src_aoi = 4  # AOI边框产线
    work_type_auto = 1  # 自动作业
    work_type_manual = 2  # 人工作业
    work_factors = "边框"  # 制作要素
    batch_postfix = f"{datetime.datetime.now().strftime('%Y%m%d')}"
    tickets = []

    def process(intelligence: Intelligence):
        src_item = '_poi_adding' if 'poi_adding' in intelligence.ref_qb_batch_id else ''
        memo_item = f'_{intelligence.memo}' if (intelligence.memo is not None and intelligence.memo != '') else ''
        batch = f'auto_plan_type_{intelligence.strategy_type}{src_item}{memo_item}_{batch_postfix}'

        tickets.append(
            Ticket(
                ref_qb_id=uuid.uuid4().hex,
                src=src_aoi,
                work_type=work_type_manual if intelligence.strategy_type != '' else work_type_auto,
                work_factors=work_factors,
                strategy_type=intelligence.strategy_type,
                main_poi_bid=intelligence.bid,
                ref_qb_batch_id=batch,
                extra={
                    'qb_id': intelligence.id,
                    'extra': {'4': '0'}  # 先作业边框，后面无流程
                },
            )
        )

    batch_process(ctx.all_intelligences, process, can_process=lambda x: x.can_process and not x.is_redundant)
    fail_tickets = post_tickets(tickets)
    for fail_ticket in fail_tickets:
        print(fail_ticket.message)

    print(f'共下发：{len(tickets) - len(fail_tickets)}')
    print(f'失败量：{len(fail_tickets)}')
    print(f'冗余量：{len([x for x in ctx.all_intelligences if x.can_process and x.is_redundant])}')
    proceed()


@desc()
def mark_tickets_status(ctx: Context, proceed):
    """
    标记挖掘情报状态
    """
    with PgsqlStabilizer(pgsql.POI_CONFIG) as stabilizer:
        def process(intelligence: Intelligence):
            sql = '''
                update integration_qb 
                set status = %s, 
                    flow_status = 2 -- 已分发至下游
                where ref_qb_id = %s;
            '''
            stabilizer.execute(sql, (intelligence.get_status(), intelligence.id))

        batch_process(ctx.all_intelligences, process)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    output_items = []

    def process(intelligence: Intelligence):
        output_items.append([
            intelligence.id,
            intelligence.bid,
            intelligence.strategy_type,
            intelligence.memo,
            intelligence.get_status(),
            intelligence.is_primary,
            intelligence.is_redundant,
            intelligence.can_process,
            intelligence.reason,
        ])

    batch_process(ctx.all_intelligences, process)
    tsv.write_tsv(ctx.work_dir / 'output.csv', output_items)
    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        fetch_intelligences_from_unity_platform,
        check_redundancy,
        load_competitors,
        insert_competitors_to_lib,
        post_tickets_to_unity_platform,
        mark_tickets_status,
        save_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/auto_create_aoi_tickets'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
