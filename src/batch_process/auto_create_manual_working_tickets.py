# !/usr/bin/env python3
"""
用于例行化创建边框人工作业工单
"""
import argparse
import datetime
import importlib
import os
import uuid
from pathlib import Path

from src.batch_process.batch_helper import batch_process
from src.batch_process.create_manual_working_tickets_helper import TicketRecord, Context, TicketProviderDescription
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.auto_repair_mixin import get_poi_by_bid
from src.charging_station.data import Poi
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.mysql_tool import MysqlTool
from src.tools.unity_platform_postman import Intelligence, post_intelligences, INTELLIGENCE_TYPE_STRATEGY, \
    INTELLIGENCE_TYPE_TICKET

# 需要忽略以下计划类型下发过的清单
OLD_TICKETS_PLAN_TYPES_MAP = {
    '302': ('302', '37'),  # AOI 常规清洗高功效、聚合院落新增
    '301': ('31', '301', '304', '37'),  # AOI 清洗竞品对比、AOI 重要垂类清洗、AOI 常规清洗低功效、聚合院落新增
    '37': ('37',),  # 聚合院落新增
    '304': ('304', '61'),  # AOI 常规清洗低功效、景区重要子点清查
}
REGISTERED_PROVIDERS = [
    # 'poi_adding',
    # 'quality_statistic',
    # 'stock',
    'stock_v2',
]

desc = pipeline.get_desc()


def get_load_mode(ctx: Context):
    """
    获取加载模式
    """
    return 'mesh_id' if not ctx.bid_list_path.is_file() else 'file'


def exists_customer_aoi(aoi_conn, bid):
    """
    判断是否存在 C 端边框
    """
    sql = '''
        select *
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s and
              a.src != 'SD'
        limit 1;
    '''
    return pgsql.fetch_one(aoi_conn, sql, (bid,)) is not None


def exists_repeat_customer_aoi(aoi_conn, poi_stab, ticket: TicketRecord):
    """
    判断是否存在被判重的 C 端边框
    """
    get_release_bid_sql = '''
        select release_bid from aoi_repeat_bid where invalid_bid = %s;
    '''

    row = poi_stab.fetch_one(get_release_bid_sql, (ticket.bid,))
    if not row:
        return False

    release_bid, = row
    return exists_customer_aoi(aoi_conn, release_bid)


def can_ignore_overlap_customer_aoi(ticket: TicketRecord, overlap_poi: Poi):
    """
    检查是否可以忽略压盖的 C 端边框
    """
    ignored_tags_level1 = {
        '行政区划',
        '行政地标',
        '旅游景点',
        '交通枢纽',
    }
    ignored_tags_level2 = {
        '公司企业;园区',
        '休闲娱乐;度假村',
        '交通设施;停车场',
    }

    # 下发的数据和压盖的数据，若互相为房地产和购物中心，则忽略不过滤。
    if overlap_poi.tag == '购物;购物中心' and ticket.std_tag == '房地产;住宅区':
        return True

    if overlap_poi.tag == '房地产;住宅区' and ticket.std_tag == '购物;购物中心':
        return True

    is_main_poi = ticket.relation_bid == '0' or ticket.relation_bid == ''
    if not is_main_poi:
        return True

    return (
        any(overlap_poi.show_tag.startswith(x) for x in ignored_tags_level1) or
        any(overlap_poi.show_tag == x for x in ignored_tags_level2)
    )


def exists_overlap_customer_aoi(aoi_conn, poi_stab, ticket: TicketRecord):
    """
    判断是否存压盖了 C 端边框
    """
    sql = '''
        select b.poi_bid
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where st_intersects(a.geom, st_geomfromtext(%s, 4326)) and
              a.src != 'SD' and
              a.src != 'SQ' and
              a.kind != '52' and
              a.aoi_level = 2 and
              b.poi_bid != %s;
    '''

    if ticket.geom is None:
        return False

    for bid, in pgsql.fetch_all(aoi_conn, sql, (ticket.geom.wkt, ticket.bid)):
        overlap_poi = get_poi_by_bid(poi_stab, bid)
        if overlap_poi is None:
            continue

        if not can_ignore_overlap_customer_aoi(ticket, overlap_poi):
            return True

    return False


def can_add_basic_aoi(aoi_conn, poi_stab, ticket: TicketRecord):
    """
    判断是否可下发新增基础院落的工单
    """
    return (
        not exists_customer_aoi(aoi_conn, ticket.bid) and
        not exists_repeat_customer_aoi(aoi_conn, poi_stab, ticket) and
        not exists_overlap_customer_aoi(aoi_conn, poi_stab, ticket)
    )


def can_update_aoi(ticket: TicketRecord):
    """
    判断是否可下发更新边框的工单
    """
    # 工艺担心作业员动坏了这些 tag 的边框，等作业员熟练后可放开限制。
    ignored_tags = [
        '教育培训;高等院校',
        '医疗;专科医院',
        '医疗;综合医院',
    ]

    return ticket.std_tag not in ignored_tags


@desc()
def load_mesh_ids(ctx: Context, proceed):
    """
    加载图幅集合
    """
    if get_load_mode(ctx) != 'mesh_id':
        proceed()
        return

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        sql = '''select distinct mesh_id from poi;'''
        ctx.mesh_ids = [x[0] for x in stabilizer.fetch_all(sql)]

    proceed()


@desc()
def load_desired_bids(ctx: Context, proceed):
    """
    获取需要关注的 bid 集合
    """
    if get_load_mode(ctx) != 'file':
        proceed()
        return

    with open(ctx.bid_list_path, 'r', encoding='utf-8') as bid_file:
        ctx.desired_bids = set([x.strip() for x in bid_file.readlines()])

    proceed()


@desc()
def find_providers(ctx: Context, proceed):
    """
    查询所有工单提供器
    """
    provider_dir = Path(os.path.dirname(__file__))
    module_names = [x.stem for x in provider_dir.glob('create_manual_working_*_tickets.py')]
    full_module_names = [f'src.batch_process.{x}' for x in module_names]
    modules = [importlib.import_module(x) for x in full_module_names]
    provider_modules = [
        x for x in modules
        if (
            hasattr(x, 'PROVIDER_NAME') and
            hasattr(x, 'PROVIDER_VERSION') and
            hasattr(x, 'PROVIDER_PRIORITY') and
            hasattr(x, 'run')
        )
    ]
    for module in provider_modules:
        if module.PROVIDER_NAME not in REGISTERED_PROVIDERS:
            continue

        ctx.providers.append(TicketProviderDescription(
            name=module.PROVIDER_NAME,
            version=module.PROVIDER_VERSION,
            priority=module.PROVIDER_PRIORITY,
            runner=module.run,
        ))
        ctx.providers.sort(key=lambda x: x.priority, reverse=True)

    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    provider_count = len(ctx.providers)
    for i, provider in enumerate(ctx.providers):
        print(f'=============================================================')
        print(f'provider ({i + 1:0{len(str(provider_count))}}/{provider_count}): {provider.name} (v{provider.version})')
        ctx.add_tickets(provider.runner(ctx))

    proceed()


@desc()
def filter_records_by_old_tickets_v1(ctx: Context, proceed):
    """
    过滤掉曾经下发过的工单（v1 版本：使用 integration_qb 表，并结合日期）
    """
    invalid_flow_status = -1
    working_status = (
        0,  # 任务待创建
        1,  # 待制作 任务未领取作业
        2,  # 制作完成 制作完成，成果待回库，其他环节不可用
        6,  # 无法核实-待采集 资料未覆盖，无法核实
    )
    sql = '''
        select id
        from integration_qb 
        where main_poi_bid = %s and
              flow_status != %s and
              status in %s and
              strategy_type = %s and
              created_at >= now() - interval '180 days';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        def process(record: TicketRecord):
            if record.primary:
                return

            args = (
                record.bid,
                invalid_flow_status,
                working_status,
                record.plan_type,
            )

            if conn.fetch_one(sql, args):
                record.reason = 'has post v1'
                record.can_process = False

        batch_process(ctx.ticket_records.values(), process)

    proceed()


@desc()
def filter_records_by_old_tickets_v2(ctx: Context, proceed):
    """
    过滤掉曾经下发过的工单（v2 版本：使用 integration_qb 表，并结合 1.0 情报闭环标准）
    """
    sql = '''
        select ref_qb_id
        from integration_qb 
        where main_poi_bid = %s and
              status in (0, 1) and  -- 其它值都是无效工单
              qb_type = 2 and  -- 挖掘情报
              strategy_type = %s and
              extra -> 'flow' ->> '4' is not null and  -- 需要建设边框
              created_at >= now() - interval '180 days';
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        def process(record: TicketRecord):
            if record.primary:
                return

            if stabilizer.fetch_one(sql, (record.bid, record.plan_type)) is not None:
                record.reason = 'has post v2'
                record.can_process = False

        batch_process(ctx.ticket_records.values(), process)

    proceed()


@desc()
def filter_records_by_old_tickets_v3(ctx: Context, proceed):
    """
    过滤掉曾经下发过的工单（v3 版本：使用 strategy_feature_list 表
    """
    working_status = (1, 2, 3)

    sql = '''
        select 1
        from strategy_feature_list
        where bid = %s and
              strategy_type = %s and
              work_status in %s and
              create_time >= (curdate() - interval 180 day);
    '''

    with (
        MysqlTool(name='beeflow') as mysql,
        mysql.connection.cursor() as cursor,
    ):
        def process(record: TicketRecord):
            if record.primary:
                return

            cursor.execute(sql, (record.bid, record.plan_type, working_status))

            if cursor.fetchone() is not None:
                record.reason = 'has post v3'
                record.can_process = False

        batch_process(ctx.ticket_records.values(), process)

    proceed()


@desc()
def filter_records_by_invalid_tickets(ctx: Context, proceed):
    """
    过滤掉人工作业过并标记为无效的工单
    """
    invalid_poi_conclusion = {2, 8, 9, 5}
    sql = '''
        select poi_conclusion 
        from aoi_work_result 
        where bid = %s and
              create_time >= now() - interval '360 days';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        def process(record: TicketRecord):
            if record.primary:
                return

            row = stabilizer.fetch_one(sql, (record.bid,))
            if row is None:
                return

            poi_conclusion, = row

            if poi_conclusion in invalid_poi_conclusion:
                record.reason = 'is invalid ticket'
                record.can_process = False

        batch_process(ctx.ticket_records.values(), process)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存清单
    """
    tsv.write_tsv(
        ctx.work_dir / 'records.tsv',
        [
            (
                x.bid,
                x.src,
                x.plan_type,
                x.memo,
            )
            for x in ctx.ticket_records.values() if x.can_process
        ]
    )

    proceed()


@desc()
def save_debug_records(ctx: Context, proceed):
    """
    保存调试清单
    """
    tsv.write_tsv(
        ctx.work_dir / 'records_debug.tsv',
        [
            (
                x.reason,
                x.bid,
                x.qb_id,
                x.name,
                x.std_tag,
                x.pv,
                x.memo,
                x.plan_type,
                x.src,
            )
            for x in ctx.ticket_records.values()
        ]
    )

    proceed()


@desc()
def post_intelligences_to_unity_platform(ctx: Context, proceed):
    """
    推送情报至一体化平台
    """
    src_aoi = 4  # AOI边框产线
    flow_aoi = '4'
    flow_unknown = '0'
    batch_postfix = f"{datetime.datetime.now().strftime('%Y%m%d')}"
    intelligences = []

    def process(record: TicketRecord):
        src_item = f'_{record.src}' if record.src == 'poi_adding' else ''
        memo_item = f'_{record.memo}' if (record.memo is not None and record.memo != '') else ''
        batch = f'intelligence_plan_type_{record.plan_type}{src_item}{memo_item}_{batch_postfix}'

        record.intelligence_id = uuid.uuid4().hex
        intelligences.append(Intelligence(
            ref_qb_id=record.intelligence_id,
            src=src_aoi,
            from_src='例行化推送人工作业清单',
            ref_qb_batch_id=batch,
            main_poi_bid=record.bid,
            qb_type=INTELLIGENCE_TYPE_STRATEGY if record.qb_id == '' else INTELLIGENCE_TYPE_TICKET,
            strategy_type=record.plan_type,
            extra={
                'qb_id': record.qb_id,
                'flow': {flow_aoi: flow_unknown}
            },
            memo=record.memo,
        ))

    batch_process(ctx.ticket_records.values(), process)
    post_intelligences(intelligences)
    proceed()


# noinspection PyUnusedLocal
@desc()
def enclose_intelligence(ctx: Context, proceed):
    """
    闭环情报
    """
    status_work_done = 2

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stabilizer:
        sql = '''
            update integration_qb
            set status = %s
            where work_type = 1 and  -- 自动化任务
                  status = 5 and  -- 工单无法完成
                  src = 4;  -- 需要制作边框
        '''
        poi_stabilizer.execute(sql, (status_work_done,))

    proceed()


# noinspection SpellCheckingInspection
@desc()
def send_result_to_infoflow(ctx: Context, proceed):
    """
    如流通知投放结果
    """
    notice_tool.send_hi(
        f'''今日边框例行化情报推送结果如下: 
总量：{ctx.get_valid_tickets_count()}

按照来源区分
存量-普通垂类-基础院落：{ctx.get_valid_tickets_count(memo='普通垂类基础院落')}
存量-特殊类型-基础院落：{ctx.get_valid_tickets_count(memo='特殊类型基础院落')}
存量-重点垂类-基础院落：{ctx.get_valid_tickets_count(memo='重点垂类基础院落')}
存量-普通垂类-聚合院落：{ctx.get_valid_tickets_count(memo='普通垂类聚合院落')}
存量-重点垂类-聚合院落：{ctx.get_valid_tickets_count(memo='重点垂类聚合院落')}
存量-旅游景点：{ctx.get_valid_tickets_count(memo='旅游景点')}
POI 新增：{ctx.get_valid_tickets_count(src='poi_adding')}

按照计划类型区分
AOI 常规清洗高功效（302）：{ctx.get_valid_tickets_count(plan_type='302')}
AOI 常规清洗低功效（304）：{ctx.get_valid_tickets_count(plan_type='304')}
AOI 重要垂类清洗（301）：{ctx.get_valid_tickets_count(plan_type='301')}
聚合院落新增（37）：{ctx.get_valid_tickets_count(plan_type='37')}
''',
        atuserids=['chenjie02_cd', 'hexinyi_cd'],
        token='d83586c9c29feea30d4fbe3da7edc2669'
    )

    proceed()


def parse_args():
    """
    解析参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        default='',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        # 创建工单
        find_providers,
        load_desired_bids,
        load_mesh_ids,
        create_tickets,
        # filter_records_by_old_tickets_v1,
        # filter_records_by_old_tickets_v2,
        filter_records_by_old_tickets_v3,
        filter_records_by_invalid_tickets,

        # 保存评估数据
        save_records,
        save_debug_records,

        # 情报处理（调试时注释）
        # post_intelligences_to_unity_platform,
        # enclose_intelligence,
        # send_result_to_infoflow,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/auto_create_manual_working_tickets'),
        bid_list_path=Path(args.bid_list_path),
        min_pv=180,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
