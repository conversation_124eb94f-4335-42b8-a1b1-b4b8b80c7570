# !/usr/bin/env python3
"""
例行提取精准化建设清单
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.pgsql_table_backup_tool import Backup
from src.tools import pipeline, pgsql, tsv

MIN_NAVI_PV = 30
MIN_CLICK_PV = 180

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    记录
    """
    bid: str
    name: str = None
    relation_bid: str = None
    std_tag: str = None
    new_std_tag: str = None
    click_pv: int = -1
    navi_pv: int = -1
    wkt: str = None
    can_process: bool = True
    reason: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    records: dict[str, Record] = field(default_factory=dict)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_high_navi_pv_records(ctx: Context, proceed):
    """
    获取导航高 pv 记录
    """
    sql = '''
        select bid, pv from poi_nav_500m_pv where pv >= %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for bid, pv in tqdm(stab.fetch_all(sql, (MIN_NAVI_PV,))):
            record = ctx.records.get(bid, None)
            if record is None:
                ctx.records[bid] = Record(
                    bid=bid,
                    navi_pv=pv,
                )
            else:
                record.navi_pv = pv

    proceed()


@desc()
def load_high_click_pv_records(ctx: Context, proceed):
    """
    获取算路高 pv 记录
    """
    sql = '''
        select bid, name, relation_bid, click_pv, std_tag, st_astext(geometry) from poi where click_pv >= %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for bid, name, relation_bid, click_pv, std_tag, wkt in tqdm(stab.fetch_all(sql, (MIN_CLICK_PV,))):
            record = ctx.records.get(bid, None)
            if record is None:
                ctx.records[bid] = Record(
                    bid=bid,
                    name=name,
                    relation_bid=relation_bid,
                    std_tag=std_tag,
                    click_pv=click_pv,
                    wkt=wkt,
                )
            else:
                record.name = name
                record.relation_bid = relation_bid
                record.std_tag = std_tag
                record.click_pv = click_pv
                record.wkt = wkt

    proceed()


@desc()
def fill_basic_props(ctx: Context, proceed):
    """
    填充基础信息
    """
    sql = '''
        select name, relation_bid, click_pv, std_tag, st_astext(geometry) from poi where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in tqdm([x for x in ctx.records.values() if x.can_process and x.name is None]):
            row = stab.fetch_one(sql, (record.bid,))
            if row is None:
                record.can_process = False
                record.reason = 'bid not found'
                continue

            name, relation_bid, click_pv, std_tag, wkt = row
            record = ctx.records[record.bid]
            record.name = name
            record.relation_bid = relation_bid
            record.std_tag = std_tag
            record.click_pv = click_pv
            record.wkt = wkt

    proceed()


@desc()
def fill_navi_pv(ctx: Context, proceed):
    """
    填充导航 pv
    """
    sql = '''
        select pv from poi_nav_500m_pv where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in tqdm([x for x in ctx.records.values() if x.can_process and x.navi_pv == -1]):
            row = stab.fetch_one(sql, (record.bid,))
            if row is None:
                continue

            record = ctx.records[record.bid]
            record.navi_pv = row[0]

    proceed()


@desc()
def fill_new_std_tag(ctx: Context, proceed):
    """
    填充 new_std_tag
    """
    sql = '''
        select new_std_tag from poi_new_std_tag where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in tqdm([x for x in ctx.records.values() if x.can_process and x.new_std_tag is None]):
            row = stab.fetch_one(sql, (record.bid,))
            if row is None:
                continue

            record = ctx.records[record.bid]
            record.new_std_tag = row[0]

    proceed()


@desc()
def filter_records(ctx: Context, proceed):
    """
    过滤记录
    """
    for record in tqdm([x for x in ctx.records.values() if x.can_process]):
        if record.std_tag not in tags.ACCURATE_ALL and record.new_std_tag not in tags.ACCURATE_ALL:
            record.can_process = False
            record.reason = 'not accurate tag'
            continue

    proceed()


@desc()
def save_records_to_db(ctx: Context, proceed):
    """
    保存记录到数据库
    """
    batch_size = 1000

    with Backup(config=pgsql.POI_CONFIG, table_name="aoi_accurate_list") as backup:
        backup_table_name = backup.execute()
        insert_sql = f'''
            insert into {backup_table_name} (bid, name, relation_bid, std_tag, new_std_tag, click_pv, navi_pv, geom)    
            values (%s, %s, %s, %s, %s, %s, %s, st_geomfromtext(%s, 4326));
        '''

    records = [x for x in ctx.records.values() if x.can_process]

    for i in tqdm(range(0, len(records), batch_size)):
        with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab:
            stab.connection.autocommit = False
            with stab.connection.cursor() as cursor:
                try:
                    for record in records[i:i + batch_size]:
                        cursor.execute(insert_sql, [
                            record.bid,
                            record.name,
                            record.relation_bid,
                            record.std_tag,
                            record.new_std_tag,
                            record.click_pv,
                            record.navi_pv,
                            record.wkt,
                        ])

                    stab.connection.commit()
                except Exception as e:
                    stab.connection.rollback()
                    print(e)

    proceed()


@desc()
def save_records_to_file(ctx: Context, proceed):
    """
    保存记录到文件
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.bid,
                x.name,
                x.relation_bid,
                x.std_tag,
                x.new_std_tag,
                x.click_pv,
                x.navi_pv,
                x.wkt,
            ]
            for x in ctx.records.values() if x.can_process
        ]
    )

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_high_navi_pv_records,
        load_high_click_pv_records,
        fill_basic_props,
        fill_navi_pv,
        filter_records,
        save_records_to_db,
        save_records_to_file,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_fetch_accurate_list"),
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main()
