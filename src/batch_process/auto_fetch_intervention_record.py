# !/usr/bin/env python3
"""
例行获取 UGC 干预记录
"""
import json
from dataclasses import dataclass, field
from datetime import timedelta, datetime
from pathlib import Path

import requests
from retrying import retry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv, notice_tool

DESIRED_FIELD_NAMES = {'name', 'pointx', 'pointy', 'std_tag', 'status'}
UGC_API = 'http://lbs-poi-platform-dev-01.bcc-bdbl.baidu.com:8001/zebrago/poi/ugcPoiAction'
SEARCH_HOURS = 24

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    干预记录
    """
    bid: str
    field_name: str
    old_value: str
    new_value: str
    online_time: datetime
    can_process: bool = True
    reason: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    records: list[Record] = field(default_factory=list)
    extremely_high_heat_bids: set[str] = field(default_factory=set)
    high_heat_bids: set[str] = field(default_factory=set)
    potential_heat_bids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def is_desired_bid(self, bid):
        """
        是否是需要的 bid
        """
        return (
            bid in self.extremely_high_heat_bids or
            bid in self.high_heat_bids or
            bid in self.potential_heat_bids
        )


def generate_hourly_pairs():
    """
    生成时间序列
    """
    now = datetime.now()
    current_hour = now.replace(minute=0, second=0, microsecond=0)
    hours = [
        (current_hour + timedelta(hours=i)).strftime("%Y%m%d%H")
        for i in range(-SEARCH_HOURS, 2)
    ]

    return list(zip(hours[::2], hours[1::2]))


def get_changes(start, end):
    """
    获取变化记录
    """

    @retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
    def __get_response(params):
        return requests.get(UGC_API, params)

    print(start, end)
    response = __get_response({
        'start_time': start,
        'end_time': end,
    })

    if not response.ok:
        print('error status code', response.status_code)
        return []

    result = response.json()
    if result['code'] != 0:
        print('error code', result['message'])
        return []

    changes = result['result']['changes']
    return changes if changes is not None else []


@desc()
def load_desired_bids(ctx: Context, proceed):
    """
    加载需要处理的 bid
    """
    get_extremely_high_heat_bids_sql = '''
        select aoi_bid from over_heat_protection_aoi;
    '''
    get_high_heat_bids_sql = '''
        select aoi_bid from high_heat_protection_aoi;
    '''
    get_potential_heat_bids_sql = '''
        select aoi_bid from potential_heat_protection_aoi;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ctx.extremely_high_heat_bids = set(x[0] for x in stab.fetch_all(get_extremely_high_heat_bids_sql))
        ctx.high_heat_bids = set(x[0] for x in stab.fetch_all(get_high_heat_bids_sql))
        ctx.potential_heat_bids = set(x[0] for x in stab.fetch_all(get_potential_heat_bids_sql))

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载干预记录
    """
    for start, end in tqdm(generate_hourly_pairs()):
        for change in get_changes(start, end):
            change_type = change['type']
            if change_type != 'modify':
                continue

            fields = change['fields']
            bid = fields['bid']
            if not ctx.is_desired_bid(bid):
                continue

            online_time = datetime.fromtimestamp(int(fields['online_time']))
            for field_name in change['fields']:
                if field_name not in DESIRED_FIELD_NAMES:
                    continue

                value = json.loads(fields[field_name])
                old_value = value['old']
                new_value = value['new']
                ctx.records.append(Record(
                    bid=bid,
                    field_name=field_name,
                    old_value=old_value,
                    new_value=new_value,
                    online_time=online_time,
                ))

    proceed()


@desc()
def filter_records(ctx: Context, proceed):
    """
    过滤干预记录
    """
    sql = '''
        select 1 
        from aoi_intervention
        where bid = %s and 
              field_name = %s and 
              create_time = %s
        limit 1;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in ctx.records:
            if stab.fetch_one(sql, [record.bid, record.field_name, record.online_time]) is None:
                continue

            record.can_process = False
            record.reason = 'exists record'

    proceed()


@desc()
def save_records_to_db(ctx: Context, proceed):
    """
    保存干预记录到数据库
    """
    batch_size = 1000
    records = [x for x in ctx.records if x.can_process]
    sql = '''
        insert into aoi_intervention(bid, field_name, old_value, new_value, create_time)
        values (%s, %s, %s, %s, %s)
        on conflict(bid, field_name, create_time) do nothing;
    '''

    for i in tqdm(range(0, len(records), batch_size)):
        with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab:
            stab.connection.autocommit = False
            with stab.connection.cursor() as cursor:
                try:
                    for record in records[i:i + batch_size]:
                        cursor.execute(
                            sql,
                            [
                                record.bid,
                                record.field_name,
                                record.old_value,
                                record.new_value,
                                record.online_time,
                            ]
                        )

                    stab.connection.commit()
                except Exception as e:
                    stab.connection.rollback()
                    print(e)

    proceed()


@desc()
def save_records_to_file(ctx: Context, proceed):
    """
    保存干预记录到文件
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.bid,
                x.field_name,
                x.old_value,
                x.new_value,
                x.online_time.strftime("%Y-%m-%d %H:%M:%S"),
            ]
            for x in ctx.records if x.can_process
        ]
    )

    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行获取 UGC 干预记录脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d5070dd11c100081e2110cb89f9e71680'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_desired_bids,
        load_records,
        filter_records,
        save_records_to_db,
        save_records_to_file,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_fetch_intervention_record"),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == "__main__":
    main()
