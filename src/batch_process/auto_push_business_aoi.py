# !/usr/bin/env python3
"""
例行推送商圈边框
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.afs_tool import AfsTool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    推送记录
    """
    bid: str
    face_id: str
    wkt: str
    overlap_bids: list[str] = field(default_factory=list)
    can_process: bool = True
    reason: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    afs_path: str
    date: datetime.date
    data_path: Path = None
    bids: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载 bid 集合
    """
    sql = '''
        select bid from poi where std_tag = '行政地标;商圈';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ctx.bids = [bid for bid, in stab.fetch_all(sql)]

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载推送记录
    """
    sql = '''
        select a.face_id, st_astext(a.geom)
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s and
              a.aoi_level = 2
        limit 1;
    '''

    with PgsqlStabilizer(pgsql.BACK_CONFIG) as stab:
        for bid in tqdm(ctx.bids):
            row = stab.fetch_one(sql, (bid,))
            if row is None:
                continue

            face_id, wkt = row
            ctx.records.append(Record(
                bid=bid,
                face_id=face_id,
                wkt=wkt,
            ))

    proceed()


@desc()
def filter_records(ctx: Context, proceed):
    """
    过滤推送记录
    """
    sql = '''
        select 1 from aoi_repeat_bid where invalid_bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in tqdm([x for x in ctx.records if x.can_process]):
            row = stab.fetch_one(sql, (record.bid,))
            if row is not None:
                record.reason = "this bid is redundant"
                record.can_process = False

    proceed()


@desc()
def fill_overlap_bids(ctx: Context, proceed):
    """
    填充压盖 bid 集合
    """
    sql = '''
        select bid
        from poi
        where st_contains(st_geomfromtext(%s, 4326), geometry);
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in tqdm([x for x in ctx.records if x.can_process]):
            record.overlap_bids = [x[0] for x in stab.fetch_all(sql, (record.wkt,))]

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    output_items = []

    for record in tqdm([x for x in ctx.records if x.can_process]):
        for bid in record.overlap_bids:
            output_items.append([bid, record.face_id, record.bid])

    ctx.data_path = ctx.work_dir / f"{ctx.date.strftime('%Y%m%d')}.csv"
    tsv.write_tsv(ctx.data_path, output_items)
    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传记录
    """
    afs = AfsTool()
    afs.set_shell('/home/<USER>/afs/bin/afsshell')

    afs.put(str(ctx.data_path), ctx.afs_path)
    rm_path = f'{ctx.afs_path}/{(ctx.date - datetime.timedelta(days=5)).strftime("%Y%m%d")}.csv'
    afs.rm(rm_path)

    proceed()


@desc()
def delete_cache_data(ctx: Context, proceed):
    """
    删除缓存数据
    """
    ctx.data_path.unlink(missing_ok=True)
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行推送商圈边框脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d5070dd11c100081e2110cb89f9e71680'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_bids,
        load_records,
        filter_records,
        fill_overlap_bids,
        save_records,
        upload_records,
        delete_cache_data,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_push_business_aoi"),
        afs_path='/user/map-data-streeview/aoi-ml/business_aoi',
        date=datetime.date.today(),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == "__main__":
    main()
