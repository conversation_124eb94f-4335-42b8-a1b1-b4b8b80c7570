# !/usr/bin/env python3
"""
例行推送无主点边框数据
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv
from src.tools.afs_tool import AfsTool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    推送记录
    """
    face_id: str
    wkt: str
    src: str
    aoi_level: int
    kind: str
    city: str


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    afs_path: str
    date: datetime.date
    data_path: Path = None
    records: list[Record] = field(default_factory=list)
    face_ids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def add_record(self, record: Record):
        """
        添加推送记录
        """
        if record.face_id not in self.face_ids:
            self.face_ids.add(record.face_id)
            self.records.append(record)


def load_records(ctx: Context, sql):
    """
    加载推送记录
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as stab:
        for face_id, src, aoi_level, kind, city, wkt in tqdm(stab.fetch_all(sql)):
            ctx.add_record(Record(
                face_id=face_id,
                src=src,
                aoi_level=aoi_level,
                kind=kind,
                wkt=wkt,
                city=city,
            ))


@desc()
def load_to_b_records(ctx: Context, proceed):
    """
    加载 b 端边框记录
    """
    sql = '''
        select face_id, src, aoi_level, kind, city_name, st_astext(geom) from blu_face where src = 'SD';
    '''

    load_records(ctx, sql)
    proceed()


@desc()
def load_no_main_bid_records(ctx: Context, proceed):
    """
    加载无主点边框记录
    """
    sql = '''
        select a.face_id, a.src, a.aoi_level, a.kind, a.city_name, st_astext(a.geom) 
        from blu_face a
        left join blu_face_poi b
        on a.face_id = b.face_id
        where a.src != 'SD' and
              b.poi_bid is null;
    '''

    load_records(ctx, sql)
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存推送记录
    """
    ctx.data_path = ctx.work_dir / f"{ctx.date.strftime('%Y%m%d')}.csv"
    tsv.write_tsv(ctx.data_path, [['face_id', 'wkt', 'src', 'aoi_level', 'kind', 'city']])
    tsv.write_tsv(
        ctx.data_path,
        [
            [
                x.face_id,
                x.wkt,
                x.src,
                x.aoi_level,
                x.kind,
                x.city,
            ]
            for x in ctx.records
        ], mode='a'
    )
    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传记录
    """
    afs = AfsTool()
    afs.set_shell('/home/<USER>/afs/bin/afsshell')

    afs.put(str(ctx.data_path), ctx.afs_path)
    rm_path = f'{ctx.afs_path}/{(ctx.date - datetime.timedelta(days=5)).strftime("%Y%m%d")}.csv'
    afs.rm(rm_path)

    proceed()


@desc()
def delete_cache_data(ctx: Context, proceed):
    """
    删除缓存数据
    """
    ctx.data_path.unlink(missing_ok=True)
    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_to_b_records,
        load_no_main_bid_records,
        save_records,
        upload_records,
        delete_cache_data,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_push_no_main_bid_aoi"),
        afs_path='/user/map-data-streeview/aoi-ml/no_main_bid',
        date=datetime.date.today(),
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main()
