# !/usr/bin/env python3
"""
例行化主点重绑定
和失效恢复重绑定（auto_recover_invalid_aoi）不同的是，此脚本针对的是正常边框，非主点失效边框。
"""
from dataclasses import dataclass, field
from pathlib import Path
import datetime
from tqdm import tqdm

from src.batch_process.batch_helper import get_all_aoi_mesh_ids, batch_process
from src.batch_process.flow_process_aoi import update_main_poi_of_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv
from src.tools.mysql_tool import MysqlTool

MIN_SIMILARITY = 0.6
GET_AOI_BY_MESH_SQL = '''
    select a.face_id, b.poi_bid, a.src
    from blu_face a
    inner join blu_face_poi b
    on a.face_id = b.face_id
    where a.mesh_id = %s and
          a.src != 'SD';
'''
GET_AOI_BY_ID_SQL = '''
    select st_astext(geom) from blu_face where face_id = %s;
'''
GET_AOI_BY_BID_SQL = '''
    select 1
    from blu_face a
    inner join blu_face_poi b
    on a.face_id = b.face_id
    where b.poi_bid = %s
    limit 1;
'''
GET_POI_BY_BID_SQL = '''
    select name, click_pv, std_tag, mid from poi where bid = %s;
'''
GET_POI_BY_WKT_SQL = '''
    select bid, name, click_pv, std_tag, mid from poi where st_contains(st_geomfromtext(%s, 4326), geometry);
'''
GET_AOI_COMPLETE_SQL = '''
    select aoi_complete from blu_face_complete where face_id = %s;
'''
IGNORED_TAGS_LEVEL_1 = [
    '行政区划',
    '行政地标',
    '出入口',
]
IGNORED_TAGS_LEVEL_2 = [
    '交通设施;停车场',
]

desc = pipeline.get_desc()


@dataclass
class Poi:
    """
    POI 信息
    """
    bid: str
    mid: str
    name: str
    pv: int
    tag: str


@dataclass
class Record:
    """
    批处理记录
    """
    face_id: str
    main_poi: Poi
    src: str
    similar_poi: Poi = None
    name_similarity: float = 0.0
    relation_type: str = ''
    can_process: bool = True
    reason: str = ''


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    mesh_ids: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)


@desc()
def load_mesh_ids(ctx: Context, proceed):
    """
    加载图幅集合
    """
    # ctx.mesh_ids = ['384710']
    ctx.mesh_ids = get_all_aoi_mesh_ids()
    proceed()


def is_valid_tag(tag: str):
    """
    判断当前的 tag 是否是有效标签
    """
    return not (any(x for x in IGNORED_TAGS_LEVEL_1 if x in tag) or
                any(x for x in IGNORED_TAGS_LEVEL_2 if x == tag))


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理记录
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer, PgsqlStabilizer(
            pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        for mesh_id in tqdm(ctx.mesh_ids):
            for face_id, bid, src in back_stabilizer.fetch_all(GET_AOI_BY_MESH_SQL, (mesh_id,)):
                row = poi_stabilizer.fetch_one(GET_POI_BY_BID_SQL, (bid,))
                if row is None:
                    continue

                name, pv, tag, mid = row
                if not is_valid_tag(tag):
                    continue

                ctx.records.append(Record(
                    face_id=face_id,
                    src=src,
                    main_poi=Poi(
                        bid=bid,
                        mid=mid,
                        name=name,
                        pv=pv,
                        tag=tag,
                    ),
                ))

    proceed()


@desc()
def find_similar_poi(ctx: Context, proceed):
    """
    找到相似 POI
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer, PgsqlStabilizer(
            pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        def process(record: Record):
            row = back_stabilizer.fetch_one(GET_AOI_BY_ID_SQL, (record.face_id,))
            if row is None:
                record.can_process = False
                record.reason = 'face not found'
                return

            wkt, = row

            for bid, name, pv, tag, mid in poi_stabilizer.fetch_all(GET_POI_BY_WKT_SQL, (wkt,)):
                if bid == record.main_poi.bid or name != record.main_poi.name or tag != record.main_poi.tag:
                    continue

                record.similar_poi = Poi(
                    bid=bid,
                    mid=mid,
                    name=name,
                    pv=pv,
                    tag=tag,
                )
                break

            if record.similar_poi is None:
                record.can_process = False
                record.reason = 'no similar poi found'

        batch_process(ctx.records, process)

    proceed()


@desc()
def filter_records_by_stability(ctx: Context, proceed):
    """
    通过稳定性筛选记录
    """
    min_pv_diff = 100
    min_aoi_complete = 3

    with PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stab:

        def process(record: Record):
            pv_diff = record.similar_poi.pv - record.main_poi.pv
            if pv_diff >= min_pv_diff:
                # pv 差值足够大，直接换绑。
                return

            if pv_diff <= 0:
                # pv 差值过小，无需换绑。
                record.can_process = False
                record.reason = 'pv 差值过小无需换绑'
                return

            row = aoi_stab.fetch_one(GET_AOI_COMPLETE_SQL, (record.face_id,))
            if row is not None:
                aoi_complete, = row
                if aoi_complete >= min_aoi_complete:
                    # pv 差值在 0 - min_pv_diff 之间，但旧 aoi 已经是精准 aoi，无需换绑。
                    record.can_process = False
                    record.reason = '已经是精准 aoi 无需换绑'

        batch_process(ctx.records, process)

    proceed()


@desc()
def filter_records_by_ugc_qb(ctx: Context, proceed):
    """
    在ugc列表中并且近期上报过，不可以进行换绑
    """
    face_id_map = {}
    day_30_before = datetime.datetime.now() - datetime.timedelta(days=20)
    with MysqlTool(name='beeflow') as mysql:
        sql = 'select bid, face_id,created_at from aoi_ugc_main_geom_change_info where created_at >= %s order by id asc'
        with mysql.connection.cursor() as cursor:
            cursor.execute(sql, [day_30_before.strftime('%Y-%m-%d')])
            records = cursor.fetchall()
            for bid, face_id, created_at in records:
                if face_id == '':
                    continue
                face_id_map[face_id] = {
                    'bid': bid,
                    'created_at': created_at
                }
    print("在ugc列表中的数据：", face_id_map)

    def process(record: Record):
        """
        处理策略, 如果在上报集合中，近期不进行主点处理
        """
        info = face_id_map.get(record.face_id, None)
        if info:
            record.can_process = False
            record.reason = 'filter_records_by_by_ugc, has changed in last 20 days'

    batch_process(ctx.records, process, can_process=lambda x: x.can_process)
    proceed()


@desc()
def filter_records_by_status(ctx: Context, proceed):
    """
    通过状态筛选记录
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stab:
        def process(record: Record):
            row = aoi_stab.fetch_one(GET_AOI_BY_BID_SQL, (record.similar_poi.bid,))
            if row is not None:
                record.can_process = False
                record.reason = '换绑 bid 已存在边框'

        batch_process(ctx.records, process)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    now = datetime.datetime.now().strftime('%Y%m%d%H%M')
    tsv.write_tsv(
        ctx.work_dir / f'output_{now}.csv',
        [
            [
                x.reason,
                x.main_poi.bid,
                x.main_poi.name,
                x.main_poi.pv,
                x.main_poi.tag,
                x.similar_poi.bid if x.similar_poi else '',
                x.similar_poi.name if x.similar_poi else '',
                x.similar_poi.pv if x.similar_poi else 0,
                x.similar_poi.tag if x.similar_poi else '',
            ]
            for x in ctx.records
        ]
    )

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_stab:
        def process(record: Record):
            update_main_poi_of_aoi(
                back_stabilizer=aoi_stab,
                face_id=record.face_id,
                src=record.src,
                bid=record.similar_poi.bid,
                mid=record.similar_poi.mid,
            )

        batch_process(ctx.records, process)

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_mesh_ids,
        load_records,
        find_similar_poi,
        filter_records_by_stability,
        filter_records_by_status,
        filter_records_by_ugc_qb,
        # save_records,
        execute_batch,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/auto_reconnect_main_poi'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
