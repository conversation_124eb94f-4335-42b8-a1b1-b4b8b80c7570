# !/usr/bin/env python3
"""
例行化删除 bid 重复的 AOI 边框
"""
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

import shapely.wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.batch_process.remove_aoi import run

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    批处理记录
    """
    bid: str
    face_id: str
    geom: BaseGeometry
    src: str
    tag: str = ''
    last_manual_work_time: datetime = datetime.min
    last_auto_work_time: datetime = datetime.min
    manual_worked: bool = False
    competitor_iou: float = 0
    has_competitor: bool = False
    competitor_wkt: str = ''
    retained: bool = True
    reason: str = ''


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    repeated_bids: list[str] = field(default_factory=list)
    repeated_face_ids: dict[str, list[Record]] = field(default_factory=dict)
    pending_remove_face_ids: list[str] = field(default_factory=list)
    pending_remove_toC_count: int = 0
    pending_remove_toB_count: int = 0


@desc()
def load_repeated_bids(ctx: Context, proceed):
    """
    加载重复的 bid
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select poi_bid 
            from blu_face_poi  a inner join blu_face b on a.face_id = b.face_id
            where a.poi_bid != '' 
            group by a.poi_bid 
            having count(*) > 1;
        '''
        ctx.repeated_bids = [x[0] for x in pgsql.fetch_all(conn, sql)]

    proceed()


@desc()
def load_repeated_records(ctx: Context, proceed):
    """
    加载重复的 AOI 边框
    """
    get_face_id_sql = '''select face_id from blu_face_poi where poi_bid = %s;'''
    get_face_src_sql = '''select src, st_astext(geom) from blu_face where face_id = %s;'''

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for bid in tqdm(ctx.repeated_bids):
            records = []
            for face_id in set(x[0] for x in pgsql.fetch_all(conn, get_face_id_sql, (bid,))):
                row = pgsql.fetch_one(conn, get_face_src_sql, (face_id,))
                if row is None:
                    continue

                src, wkt = row

                records.append(Record(
                    bid=bid,
                    face_id=face_id,
                    src=src,
                    geom=shapely.wkt.loads(wkt),
                ))

            ctx.repeated_face_ids[bid] = records

    proceed()


def __fill_work_time(resume_conn, records):
    """
    填充记录的最后一次人工和自动化更新时间
    """
    get_last_manual_work_time_sql = '''
        select create_time 
        from blu_record 
        where face_id = %s and 
              task_id != '' 
        order by create_time desc 
        limit 1;
    '''
    get_last_auto_work_time_sql = '''
        select create_time 
        from blu_record 
        where face_id = %s and 
              task_id = '' 
        order by create_time desc 
        limit 1;
    '''

    for record in records:
        manual_row = pgsql.fetch_one(resume_conn, get_last_manual_work_time_sql, (record.face_id,))
        if manual_row is not None:
            record.last_manual_work_time = manual_row[0]
            record.manual_worked = True

        auto_row = pgsql.fetch_one(resume_conn, get_last_auto_work_time_sql, (record.face_id,))
        if auto_row is not None:
            record.last_auto_work_time = auto_row[0]


@desc()
def fill_work_time(ctx: Context, proceed):
    """
    填充记录的最后一次人工和自动化更新时间
    """
    with pgsql.get_connection(pgsql.AOI_RESUME_SLAVER_CONFIG) as conn:
        for records in tqdm(ctx.repeated_face_ids.values()):
            __fill_work_time(conn, records)

    proceed()


def get_geom_iou(geom1: BaseGeometry, geom2: BaseGeometry):
    """
    计算两个多边形的交并比
    """
    intersection = geom1.intersection(geom2).area
    union = geom1.union(geom2).area
    if union == 0:
        return 0.0

    return intersection / union


def fill_competitor_properties(poi_conn, records):
    """
    填充竞争信息
    """
    sql = '''
        select st_astext(geom)
        from aoi_intelligence_history
        where bid = %s
        order by create_time desc
        limit 1;
    '''

    for record in records:
        row = poi_conn.fetch_one(sql, (record.bid,))
        if row is None:
            continue

        competitor_wkt, = row
        competitor_geom = shapely.wkt.loads(competitor_wkt)
        record.has_competitor = True
        record.competitor_iou = get_geom_iou(record.geom, competitor_geom)
        record.competitor_wkt = competitor_wkt


def judge_retention_by_work_time(records):
    """
    根据人工作业时间判断是否保留
    """
    manual_worked_records = [x for x in records if x.manual_worked]
    auto_worked_records = [x for x in records if not x.manual_worked]

    if any(manual_worked_records):
        for record in auto_worked_records:
            record.retained = False
            record.reason = '人工和策略同时更新过，需要删除策略的，本记录是策略更新的。'

        last_worked_record = sorted(manual_worked_records, key=lambda x: x.last_manual_work_time)[-1]
        order_worked_records = [x for x in manual_worked_records if x.face_id != last_worked_record.face_id]
        for record in order_worked_records:
            record.retained = False
            record.reason = '都是人工更新的，但本记录时间较旧，故需要删除。'


def judge_retention_by_tag(records):
    """
    根据 tag 判断是否保留
    """
    desired_tags = ['酒店', '美食']
    desired_tags_records = [x for x in records if any(tag in x.tag for tag in desired_tags)]

    if not any(desired_tags_records):
        return

    # 在 desired_tags 中，index 越小优先级越高。
    desired_tags_record_index_map = {}
    for index, tag in enumerate(desired_tags):
        for record in desired_tags_records:
            if tag in record.tag:
                desired_tags_record_index_map[record.face_id] = index

    retention_record = sorted(desired_tags_records, key=lambda x: desired_tags_record_index_map[x.face_id])[0]
    remove_records = [x for x in desired_tags_records if x.face_id != retention_record.face_id]
    for record in remove_records:
        record.retained = False
        record.reason = 'tag 优先级较低，故需要删除。'


def judge_retention_by_auto_work_time(records):
    """
    根据例行化更新时间判断是否保留
    """
    auto_worked_records = [x for x in records if not x.manual_worked]
    if not any(auto_worked_records):
        return

    last_worked_record = sorted(auto_worked_records, key=lambda x: x.last_auto_work_time)[-1]
    order_worked_records = [x for x in auto_worked_records if x.face_id != last_worked_record.face_id]
    for record in order_worked_records:
        record.retained = False
        record.reason = '都是策略更新的，但本记录时间较旧，故需要删除。'


def judge_retention_by_competitor(records):
    """
    根据竞品信息判断是否保留
    """
    has_competitor_records = [x for x in records if x.has_competitor]
    no_competitor_records = [x for x in records if not x.has_competitor]

    if any(has_competitor_records):
        for record in no_competitor_records:
            record.retained = False
            record.reason = '压盖和不压盖竞品的记录都存在，需要删除不压盖的，本记录不压盖。'

        max_iou_record = sorted(has_competitor_records, key=lambda x: x.competitor_iou)[-1]
        smaller_iou_records = [x for x in has_competitor_records if x.face_id != max_iou_record.face_id]
        for record in smaller_iou_records:
            record.retained = False
            record.reason = '都压盖了竞品，但本记录交并比较小，故需要删除。'
    else:
        judge_retention_by_auto_work_time(no_competitor_records)


def __judge_retention(poi_conn, records):
    """
    判断是否保留
    """
    to_b_src = 'SD'

    def get_retained_records(original_records):
        return [x for x in original_records if x.retained]

    to_c_records = [x for x in records if x.src != to_b_src]
    to_b_records = [x for x in records if x.src == to_b_src]

    if any(to_c_records):
        for record in to_b_records:
            record.retained = False
            record.reason = 'B 端和 C 端边框都存在，需要删除 B 端的，本记录是 B 端。'

        judge_retention_by_tag(to_c_records)
        judge_retention_by_work_time(get_retained_records(to_c_records))
        judge_retention_by_auto_work_time(get_retained_records(to_c_records))
    elif any(to_b_records):
        fill_competitor_properties(poi_conn, to_b_records)
        judge_retention_by_tag(to_b_records)
        judge_retention_by_work_time(get_retained_records(to_b_records))
        judge_retention_by_competitor(get_retained_records(to_b_records))


@desc()
def judge_retention(ctx: Context, proceed):
    """
    判断是否保留
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for records in tqdm(ctx.repeated_face_ids.values()):
            __judge_retention(poi_conn, records)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存结果
    """
    output_items = []
    for records in tqdm(ctx.repeated_face_ids.values()):
        for record in records:
            output_items.append([
                record.retained,
                record.reason,
                record.bid,
                record.face_id,
                record.geom.wkt,
                record.src,
                record.has_competitor,
                record.competitor_wkt,
                record.competitor_iou,
                record.last_auto_work_time,
                record.manual_worked,
                record.last_manual_work_time,
            ])

    tsv.write_tsv(ctx.work_dir / 'output.tsv', output_items)
    proceed()


@desc()
def remove_aoi(ctx: Context, proceed):
    """
    删除边框
    """
    for records in tqdm(ctx.repeated_face_ids.values()):
        for record in records:
            if not record.retained:
                ctx.pending_remove_face_ids.append(record.face_id)
                if record.src == 'SD':
                    ctx.pending_remove_toB_count += 1
                else:
                    ctx.pending_remove_toC_count += 1
    print('待删除的aoi的face_id', ctx.pending_remove_face_ids)
    run(ctx.pending_remove_face_ids, '例行化删除重复 bid 边框')
    proceed()


@desc()
def send_result_to_infoflow(ctx: Context, proceed):
    """
    发送如流消息
    """
    notice_tool.send_hi(
        f'''通过自动化途径发现 bid 重复的边框，具体情况如下:
总量：{len(ctx.pending_remove_face_ids)}
C 端：{ctx.pending_remove_toC_count}
B 端：{ctx.pending_remove_toB_count}
请及时确认，谢谢。
''',
        atuserids=['chenjie02_cd', 'hexinyi_cd'],
        token='d83586c9c29feea30d4fbe3da7edc2669'
    )
    proceed()


def get_pending_remove_aoi(poi_conn, resume_conn, records):
    """
    获取可以删除的边框
    """
    __fill_work_time(resume_conn, records)
    __judge_retention(poi_conn, records)

    for record in records:
        if not record.retained:
            yield record


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_repeated_bids,
        load_repeated_records,
        fill_work_time,
        judge_retention,
        save_records,
        remove_aoi,
        send_result_to_infoflow,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/auto_remove_aoi_with_repeated_bid'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
