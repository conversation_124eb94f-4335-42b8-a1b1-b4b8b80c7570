# !/usr/bin/env python3
"""
例行回捞图灵作业成果
"""
import argparse
import hashlib
import json
import time
from dataclasses import dataclass, field
from pathlib import Path

import mapio.utils.coord
import requests
import shapely.wkt
from shapely import Polygon
from tqdm import tqdm

from src.batch_process.flow_process_aoi import add_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.helper import get_nested_value
from src.seg_post_process.smooth_point_string import smooth_polygon
from src.tools import pipeline, pgsql, tsv, notice_tool

BATCH_SIZE = 64
SOURCE_KEY = 'aoi_'
PLATFORMS = ['practice', 'formal']
# noinspection SpellCheckingInspection
CONFIGS = {
    'practice': {
        'webservice': '************:8585',
        'user': 'shaomingrui',
        'pipe': 'shaomingrui3-pipe',
        'token': '$1$5dHdV.SL$TDjLR2E6wRV91WgnyxsQJ0',
        'pipe_cur': {
            46: 1237478,
        }
    },
    'formal': {
        'webservice': 'group.opera-gz01-bigpipeGW-all-gzdisk2.Bigpipe.all.serv:8080',
        'user': 'map-link-platform-sub',
        'pipe': 'qingbao-async-intelligence-push',
        'token': '$1$Eo37VDEY$HGEpvQ0cpgtbxNmxfrRX00',
        'pipe_cur': {
            1: 6700000,
            2: 6700000,
            3: 6826639,
            4: 6700000,
            5: 6700000,
            6: 6700000,
            7: 6700000,
            8: 6700000,
            9: 6835047,
            10: 6700000,
        }
    }
}

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    回捞记录
    """
    message_id: int
    status: int
    result: int
    aoi_geom: Polygon
    source_id: str
    bid: str
    name: str
    city: str
    pipelet: int = 0
    platform: str = ''
    mid: str = ''

    @property
    def can_push_aoi(self):
        """
        判断是否可以推送 AOI
        """
        valid_status = 32
        valid_result = 1

        return (
            self.status == valid_status and
            self.result == valid_result and
            self.bid != '' and
            self.mid != '' and
            self.aoi_geom is not None
        )


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    platform: str
    message_id: int
    webservice: str
    user: str
    pipe: str
    token: str
    pipe_cur: dict[int, int] = field(default_factory=dict)
    records: list[Record] = field(default_factory=list)
    source_ids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


def trans_bd09_coords_to_gcj02(coords):
    """
    将百度坐标系转换为国测局坐标系
    """
    trans_line_points = []

    for coord in coords:
        gcj_lon, gcj_lat = mapio.utils.coord.bd09_to_gcj02(coord[0], coord[1])
        trans_line_points.append((gcj_lon, gcj_lat))

    return trans_line_points


def make_valid(geom):
    """
    将几何对象转换为有效几何对象
    """
    if not geom:
        return None

    geom = geom.buffer(0)

    if not geom.is_valid:
        return None

    if geom.geom_type != 'Polygon':
        return None

    if any(geom.interiors):
        geom = Polygon(geom.exterior)

    if geom.is_empty:
        return None

    return geom


def smooth_geom(geom):
    """
    平滑几何对象
    """
    simplify_tolerance = 5e-5
    min_area = 1600e-10
    precision = 1e-7

    geom = geom.simplify(simplify_tolerance, preserve_topology=True)
    geom = make_valid(geom)
    if geom is None:
        return None

    if geom.area >= min_area:
        geom = shapely.wkt.loads(smooth_polygon(geom.wkt))
        geom = make_valid(geom)
        if geom is None:
            return None

    return shapely.set_precision(geom, precision)


def parse_response(response):
    """
    解析响应
    """
    response_json = response.json()
    messages = get_nested_value(response_json, ['messages'])
    if messages is None:
        return []

    if len(messages) == 0:
        return []

    for i in range(len(messages)):
        message_id = get_nested_value(response_json, ['messages', i, 'id'])
        message = get_nested_value(response_json, ['messages', i, 'data'])
        message_json = json.loads(message)

        source_id = get_nested_value(message_json, ['sourceid'])
        status = get_nested_value(message_json, ['status'])
        result = get_nested_value(message_json, ['result'])

        yield message_id, source_id, status, result, message_json


def parse_message(message_json):
    """
    解析作业成果
    """
    bid = get_nested_value(message_json, ['original_info', 'properties', 'poi_bid'])
    name = get_nested_value(message_json, ['original_info', 'properties', 'poi_name'])
    city = get_nested_value(message_json, ['original_info', 'cityname'])
    coordinates = get_nested_value(message_json, ['edit_commit', 'geo', 0, 'geometry', 'coordinates', 0])

    if coordinates is None:
        aoi_geom = None
    else:
        aoi_geom = Polygon(trans_bd09_coords_to_gcj02(coordinates))
        aoi_geom = smooth_geom(aoi_geom)

    return bid, name, city, aoi_geom


@desc()
def fill_message_id(ctx: Context, proceed):
    """
    填充消息ID
    """
    sql = '''
        select current_id
        from turing_pipelet_info
        where type = 'aoi' and
              pipelet = %s;
    '''

    if ctx.message_id > 0:
        for pipelet in ctx.pipe_cur:
            ctx.pipe_cur[pipelet] = ctx.message_id
    else:
        with PgsqlStabilizer(pgsql.POI_CONFIG) as stab:
            for pipelet in ctx.pipe_cur:
                min_message_id = CONFIGS[ctx.platform]['pipe_cur'][pipelet]
                row = stab.fetch_one(sql, [pipelet])
                max_message_id = row[0] if row is not None else 0
                max_message_id = max_message_id if max_message_id is not None else 0
                ctx.pipe_cur[pipelet] = max(min_message_id, max_message_id)

    proceed()


def get_response(ctx: Context, pipelet, message_id, batch_size):
    """
    获取响应
    """
    url = f'http://{ctx.webservice}/rest/pipe/{ctx.pipe}?method=fetch'
    expires = int(time.time()) + 300
    sign_data = f"{expires}{ctx.token}"
    sign = hashlib.md5(sign_data.encode()).hexdigest()

    return requests.post(
        url,
        headers={
            'Expect': ''
        },
        files={
            'expires': expires,
            'username': ctx.user,
            'sign': sign,
            'pipelet': pipelet,
            'id': message_id,
            'batch': batch_size
        }
    )


def create_records(response):
    """
    根据响应创建记录
    """
    for message_id, source_id, status, result, message_json in parse_response(response):
        print(message_id, source_id)

        if source_id is None or not source_id.startswith(SOURCE_KEY):
            continue

        bid, name, city, aoi_geom = parse_message(message_json)
        yield Record(
            message_id=message_id,
            source_id=source_id,
            status=status,
            result=result,
            bid=bid,
            name=name,
            city=city,
            aoi_geom=aoi_geom,
        )


@desc()
def load_new_records(ctx: Context, proceed):
    """
    加载新记录
    """
    for pipelet in ctx.pipe_cur:
        while True:
            time.sleep(0.5)
            response = get_response(ctx, pipelet, ctx.pipe_cur[pipelet], BATCH_SIZE)
            ctx.pipe_cur[pipelet] += BATCH_SIZE

            if response.status_code == 404:
                break

            for record in create_records(response):
                record.pipelet = pipelet
                record.platform = ctx.platform
                ctx.records.append(record)

    proceed()


@desc()
def save_turing_pipelet_info(ctx: Context, proceed):
    """
    保存 bigpipe 游标
    """
    sql = '''
        update turing_pipelet_info
        set current_id = %s, 
            updated_at = now()
        where type = 'aoi' and
              pipelet = %s;
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for pipelet in ctx.pipe_cur:
                cur.execute(sql, [
                    ctx.pipe_cur[pipelet],
                    pipelet,
                ])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


@desc()
def save_tickets_to_db(ctx: Context, proceed):
    """
    保存工单到数据库
    """
    sql = '''
        update business_district_ticket
        set status = %s,
            result = %s,
            geom = st_geomfromtext(%s, 4326),
            message_id = %s,
            pipelet = %s,
            finish_time = now()
        where source_id = %s;
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for record in tqdm(ctx.records):
                cur.execute(sql, [
                    record.status,
                    record.result,
                    record.aoi_geom.wkt if record.aoi_geom is not None else None,
                    record.message_id,
                    record.pipelet,
                    record.source_id,
                ])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 POI 属性
    """
    sql = '''
        select mid from poi where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in tqdm(ctx.records):
            row = stab.fetch_one(sql, [record.bid])
            if row is None:
                continue

            record.mid = row[0]

    proceed()


@desc()
def push_aoi(ctx: Context, proceed):
    """
    推送 AOI
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as stab:
        for record in tqdm([x for x in ctx.records if x.can_push_aoi]):
            result = add_aoi(
                stab,
                add_data={
                    'kind': '52',
                    'src': 'SQ',
                    'aoi_level': 2,
                    'name': record.name,
                    'bid': record.bid,
                    'mid': record.mid,
                    'wkt': record.aoi_geom.wkt,
                    'city': record.city,
                    'memo': '图灵人工作业商圈',
                },
                platform='formal' if record.platform == 'formal' else 'practice',
            )
            if not result.success:
                print(result.msg)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存回捞记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.can_push_aoi,
                x.bid,
                x.message_id,
                x.status,
                x.result,
                x.aoi_geom.wkt if x.aoi_geom is not None else '',
                x.platform,
                x.source_id,
            ]
            for x in ctx.records
        ]
    )
    proceed()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        fill_message_id,
        load_new_records,
        save_turing_pipelet_info,
        save_tickets_to_db,
        fill_poi_properties,
    ]

    if args.push:
        pipes.extend([
            push_aoi,
        ])

    pipes.append(save_records)

    return pipeline.Pipeline(*pipes)


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--message-id',
        dest='message_id',
        type=int,
        default=0,
        required=False,
    )
    parser.add_argument(
        '--platform',
        dest='platform',
        type=str,
        choices=PLATFORMS,
        default='practice',
        required=False,
    )
    parser.add_argument(
        '--push',
        dest='push',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d5070dd11c100081e2110cb89f9e71680'
    )


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_retrieve_turing_result"),
        platform=args.platform,
        message_id=args.message_id,
        webservice=CONFIGS[args.platform]['webservice'],
        token=CONFIGS[args.platform]['token'],
        user=CONFIGS[args.platform]['user'],
        pipe=CONFIGS[args.platform]['pipe'],
        pipe_cur=CONFIGS[args.platform]['pipe_cur'],
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(f'例行回捞图灵作业成果脚本异常！{e}')


if __name__ == "__main__":
    main(parse_args())
