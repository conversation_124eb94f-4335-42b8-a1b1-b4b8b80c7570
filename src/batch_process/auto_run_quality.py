# !/usr/bin/env python3
"""
例行化运行质量清查
"""
import os
import sys
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[2]
print(root_path)
sys.path.insert(0, root_path.as_posix())

import argparse
import datetime
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.aoi_cleanup_strategy.run_all import find_strategies
from src.batch_process.batch_helper import get_all_poi_mesh_ids, get_all_aoi_mesh_ids
from src.tools import pipeline, pgsql

MIN_PV = 180
DESIRED_STRATEGIES = [
    # 'dest_track',
    'competitor_diff_v2',
    'multi_inner_road_group',
    'child_poi_out_of_aoi',
]

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    bid_list_path: Path
    mode: str
    mesh_ids: list[str] = field(default_factory=list)
    bids: list[str] = field(default_factory=list)


@desc()
def load_mesh_ids(ctx: Context, proceed):
    """
    加载图幅集合
    """
    ctx.mesh_ids = get_all_aoi_mesh_ids()
    proceed()


@desc()
def load_records_by_mesh_ids(ctx: Context, proceed):
    """
    加载批处理记录（数据库图幅模式）
    """
    with (
        pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
    ):
        query_poi_sql = '''select bid from poi where mesh_id = %s and click_pv >= %s;'''
        query_aoi_sql = '''
            select 1 
            from blu_face a
            inner join blu_face_poi b
            on a.face_id = b.face_id
            where b.poi_bid = %s and
                  a.src != 'SD' and
                  a.kind != '52';
        '''

        for mesh_id in tqdm(ctx.mesh_ids):
            for bid, in pgsql.fetch_all(poi_conn, query_poi_sql, (mesh_id, MIN_PV)):
                if pgsql.fetch_one(aoi_conn, query_aoi_sql, (bid,)):
                    ctx.bids.append(bid)

    proceed()


@desc()
def load_records_by_file(ctx: Context, proceed):
    """
    加载批处理记录（本地文件模式）
    """
    with open(ctx.bid_list_path, 'r', encoding='utf-8') as f:
        ctx.bids = f.read().splitlines()

    proceed()


@desc()
def run_quality(ctx: Context, proceed):
    """
    执行质量清查
    """
    now = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
    prefix = 'manual-' if ctx.mode != 'lib' else ''
    batch = f'{prefix}quality-statistics-{now}'

    for name, _, runner in find_strategies():
        if name not in DESIRED_STRATEGIES:
            continue

        try:
            runner(ctx.bids, batch)
        except Exception as e:
            print(e)

    proceed()


def create_pipeline(mode):
    """
    创建策略管道
    """
    if mode == 'lib':
        return pipeline.Pipeline(
            load_mesh_ids,
            load_records_by_mesh_ids,
            run_quality,
        )
    elif mode == 'file':
        return pipeline.Pipeline(
            load_records_by_file,
            run_quality,
        )

    raise ValueError(f'Unknown mode: {mode}')


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args.mode)
    desc.attach(main_pipe)
    ctx = Context(
        bid_list_path=Path(args.bid_list_path),
        mode=args.mode,
    )
    main_pipe(ctx)


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        required=False,
        default='',
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        required=False,
        default='lib',
    )
    return parser.parse_args()


if __name__ == '__main__':
    main(parse_args())
