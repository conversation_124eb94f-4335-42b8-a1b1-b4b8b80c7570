# !/usr/bin/env python3
"""
例行化更新边框层级。
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from shapely import Polygon
from tqdm import tqdm

from src.batch_process.batch_helper import contains_majority
from src.batch_process.flow_process_aoi import update_level_of_aoi
from src.model.aoi_model import AoiModel
from src.tools import pipeline, pgsql, tsv

CLUSTER_AOI = 1
BASIC_AOI = 2
INNER_AOI = 3
MIN_HIGH_LEVEL_ROAD_RATIO = 0.05
MAX_OPEN_TYPE_ROAD_RATIO = 0.95
MIN_OPEN_TYPE_ROAD_RATIO = 0.50

desc = pipeline.get_desc()


@dataclass
class BatchAoiLevelRecord:
    """
    层级批处理信息。
    """
    face_id: str
    bid: str
    mesh_id: str
    original_level: int
    level: int
    memo: str
    geom: Polygon
    name: str = ''
    can_process: bool = True
    reason: str = ''


@dataclass
class AoiRecord:
    face_id: str
    bid: str
    level: int
    mesh_id: str
    geom: shapely.geometry.Polygon
    batch_record: BatchAoiLevelRecord = field(init=False)


@dataclass
class Record:
    """
    存储边框信息。
    """
    face_id: str
    bid: str
    level: int
    geom: Polygon
    mesh_id: str
    open_type_road_length = 0
    high_level_road_length = 0
    total_road_length = 0
    intersects_aois: list[AoiRecord] = field(default_factory=list)
    batch_record: BatchAoiLevelRecord = None


@dataclass
class Context:
    """
    脚本执行上下文。
    """
    work_dir: Path
    bid_list_path: Path
    mode: str
    mesh_ids: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)


def load_mesh_ids(ctx: Context):
    """
    加载所有图幅。
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select distinct mesh_id from blu_face;
        '''
        ctx.mesh_ids = [x[0] for x in pgsql.fetch_all(conn, sql)]


def load_records_by_master_lib(ctx: Context):
    """
    通过数据库加载记录。
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        load_mesh_ids(ctx)
        for mesh_id in tqdm(ctx.mesh_ids):
            sql = '''
                select a.face_id, b.poi_bid, st_astext(a.geom), a.aoi_level
                from blu_face a

                inner join blu_face_poi b
                on a.face_id = b.face_id
                
                where a.mesh_id = %s and
                      a.src != 'SD' and
                      a.kind != '52';
            '''
            for face_id, bid, wkt, aoi_level in pgsql.fetch_all(conn, sql, (mesh_id,)):
                ctx.records.append(Record(
                    face_id=face_id,
                    bid=bid,
                    geom=shapely.wkt.loads(wkt),
                    level=aoi_level,
                    mesh_id=mesh_id,
                ))


def load_records_by_file(ctx: Context):
    """
    通过文件加载记录。
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        open(ctx.bid_list_path, 'r') as f,
    ):
        bids = [x.strip() for x in f.readlines()]
        for bid in tqdm(bids):
            sql = '''
                select a.face_id, st_astext(a.geom), a.aoi_level, a.mesh_id
                from blu_face a
                
                inner join blu_face_poi b
                on a.face_id = b.face_id
                
                where b.poi_bid = %s
                limit 1;
            '''
            row = pgsql.fetch_one(aoi_conn, sql, (bid,))
            if not row:
                continue

            face_id, wkt, aoi_level, mesh_id = row

            ctx.records.append(Record(
                face_id=face_id,
                bid=bid,
                geom=shapely.wkt.loads(wkt),
                level=aoi_level,
                mesh_id=mesh_id,
            ))


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    if ctx.mode == 'lib':
        load_records_by_master_lib(ctx)
    elif ctx.mode == 'file':
        load_records_by_file(ctx)

    proceed()


@desc()
def fill_road_length(ctx: Context, proceed):
    """
    填充聚合院落内道路数量
    """
    min_kind = 7  # 数值越小，道路等级越大，所以这里命名是 min_kind。
    inner_form = '52'

    with AoiModel() as aoi_model:
        for record in tqdm(ctx.records):
            if record.level != CLUSTER_AOI:
                continue

            sql = '''
                select kind, form, st_astext(geom) 
                from nav_link 
                where st_intersects(st_geomfromtext(%s, 4326), geom);
            '''
            for kind, form, wkt in pgsql.fetch_all(aoi_model.conn_road, sql, (record.geom.wkt,)):
                road_geom = shapely.wkt.loads(wkt)
                intersection_road_length = record.geom.intersection(road_geom).length

                record.total_road_length += intersection_road_length
                if kind <= min_kind:
                    record.high_level_road_length += intersection_road_length
                if inner_form not in form:
                    record.open_type_road_length += intersection_road_length

    proceed()


@desc()
def fill_intersects_aois(ctx: Context, proceed):
    """
    统计聚合院落内部是否包含基础院落
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn:
        for record in tqdm(ctx.records):
            if record.level != CLUSTER_AOI:
                continue

            sql = '''
                select a.face_id, b.poi_bid, a.aoi_level, st_astext(a.geom), a.mesh_id
                from blu_face a
                left join blu_face_poi b
                on a.face_id = b.face_id
                where st_intersects(st_geomfromtext(%s, 4326), geom) and
                      a.src != 'SD' and
                      a.aoi_level = 2 and
                      a.kind != '52' and
                      a.face_id != %s;
            '''
            for item in pgsql.fetch_all(aoi_conn, sql, (record.geom.wkt, record.face_id)):
                face_id, bid, aoi_level, wkt, mesh_id = item
                aoi_geom = shapely.wkt.loads(wkt)
                if contains_majority(record.geom, aoi_geom):
                    record.intersects_aois.append(AoiRecord(
                        face_id=face_id,
                        bid=bid,
                        level=aoi_level,
                        geom=aoi_geom,
                        mesh_id=mesh_id,
                    ))

    proceed()


@desc()
def generate_batch_records_without_basic_aoi(ctx: Context, proceed):
    """
    生成聚合院落下无基础院落的批处理记录。
    """
    for record in tqdm(ctx.records):
        if record.batch_record or record.level != CLUSTER_AOI or any(record.intersects_aois):
            continue

        memo = None

        if record.total_road_length == 0:
            memo = '无基础院落 + 无道路'
        else:
            high_level_road_ratio = record.high_level_road_length / record.total_road_length
            open_type_road_ratio = record.open_type_road_length / record.total_road_length

            if high_level_road_ratio <= MIN_HIGH_LEVEL_ROAD_RATIO:
                if open_type_road_ratio > MAX_OPEN_TYPE_ROAD_RATIO:
                    memo = '无基础院落 + 未压盖高等级道路 + 大部分都是内部路'
            elif open_type_road_ratio > MIN_OPEN_TYPE_ROAD_RATIO:
                memo = '无基础院落 + 压盖高等级道路 + 50% 以上开放式区域'

        if memo:
            record.batch_record = BatchAoiLevelRecord(
                face_id=record.face_id,
                bid=record.bid,
                original_level=CLUSTER_AOI,
                level=BASIC_AOI,
                memo=memo,
                geom=record.geom,
                mesh_id=record.mesh_id,
            )

    proceed()


def degrade_aoi_level(aoi_level: int):
    """
    降低边框等级
    """
    if aoi_level == CLUSTER_AOI:
        return BASIC_AOI
    elif aoi_level == BASIC_AOI:
        return INNER_AOI
    else:
        return aoi_level


@desc()
def generate_batch_records_with_basic_aoi(ctx: Context, proceed):
    """
    生成聚合院落下有基础院落的批处理记录。
    """
    for record in tqdm(ctx.records):
        if record.batch_record or record.level != CLUSTER_AOI or not any(record.intersects_aois):
            continue

        memo = None

        if record.total_road_length == 0:
            memo = '有基础院落 + 无道路'
        else:
            high_level_road_ratio = record.high_level_road_length / record.total_road_length
            open_type_road_ratio = record.open_type_road_length / record.total_road_length

            if high_level_road_ratio <= MIN_HIGH_LEVEL_ROAD_RATIO:
                if open_type_road_ratio > MAX_OPEN_TYPE_ROAD_RATIO:
                    memo = '有基础院落 + 未压盖高等级道路 + 大部分都是内部路'
            elif open_type_road_ratio > MIN_OPEN_TYPE_ROAD_RATIO:
                memo = '有基础院落 + 压盖高等级道路 + 50% 以上开放式区域'

        if memo:
            record.batch_record = BatchAoiLevelRecord(
                face_id=record.face_id,
                bid=record.bid,
                original_level=CLUSTER_AOI,
                level=BASIC_AOI,
                memo=memo,
                geom=record.geom,
                mesh_id=record.mesh_id,
            )
            for aoi in record.intersects_aois:
                new_level = degrade_aoi_level(aoi.level)
                if new_level == aoi.level:
                    continue
                aoi.batch_record = BatchAoiLevelRecord(
                    face_id=aoi.face_id,
                    bid=aoi.bid,
                    original_level=aoi.level,
                    level=degrade_aoi_level(aoi.level),
                    memo=memo,
                    geom=aoi.geom,
                    mesh_id=aoi.mesh_id,
                )

    proceed()


@desc()
def fill_name_of_batch_record(ctx: Context, proceed):
    """
    填充批处理记录的 name 信息。
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        for record in ctx.records:
            if not record.batch_record:
                continue

            sql = '''select name from poi where bid = %s;'''
            row = pgsql.fetch_one(conn, sql, (record.batch_record.bid,))
            if not row:
                record.batch_record = None
                continue

            record.batch_record.name, = row

            for aoi in record.intersects_aois:
                if not aoi.batch_record:
                    continue

                row = pgsql.fetch_one(conn, sql, (aoi.batch_record.bid,))
                if not row:
                    aoi.batch_record = None
                    continue

                aoi.batch_record.name, = row

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    for record in tqdm(ctx.records):
        if not record.batch_record:
            continue

        result = update_level_of_aoi(record.batch_record.face_id, record.batch_record.level)
        if not result.success:
            print(result.message)

        for aoi in record.intersects_aois:
            if not aoi.batch_record:
                continue

            result = update_level_of_aoi(aoi.batch_record.face_id, aoi.batch_record.level)
            if not result.success:
                print(result.message)

    proceed()


@desc()
def save_batch_records(ctx: Context, proceed):
    """
    保存批处理记录。
    """
    output_items = []

    for record in ctx.records:
        if not record.batch_record:
            continue

        for aoi in record.intersects_aois:
            if not aoi.batch_record:
                continue

            output_items.append([
                record.batch_record.bid,
                record.batch_record.name,
                record.batch_record.geom.wkt,
                record.batch_record.original_level,
                record.batch_record.level,
                aoi.batch_record.bid,
                aoi.batch_record.name,
                aoi.batch_record.geom.wkt,
                aoi.batch_record.original_level,
                aoi.batch_record.level,
                record.batch_record.memo,
            ])

    tsv.write_tsv(ctx.work_dir / 'output.tsv', output_items)
    proceed()


def parse_args():
    """
    解析参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        default='file',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_road_length,
        fill_intersects_aois,
        generate_batch_records_without_basic_aoi,
        generate_batch_records_with_basic_aoi,
        fill_name_of_batch_record,
        save_batch_records,
        execute_batch,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/recall_error_level_aoi'),
        bid_list_path=Path(args.bid_list_path),
        mode=args.mode,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
