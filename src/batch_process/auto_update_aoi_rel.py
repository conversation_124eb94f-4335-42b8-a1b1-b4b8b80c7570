# !/usr/bin/env python3
"""
边框层级例行化批处理
"""
import argparse
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.batch_process.flow_process_aoi import add_aoi_rel
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv

desc = pipeline.get_desc()


@dataclass
class BatchAoiRelRecord:
    bid: str
    bid2: str
    face_id: str
    face_id2: str
    mesh_id: str


@dataclass
class ChildRecord:
    """
    基础院落信息
    """
    geom: BaseGeometry
    bid: str
    face_id: str
    src: str
    name: str = ''
    name_iou: float = 0.0
    tag: str = ''
    overlap_type: str = ''
    can_output: bool = True
    reason: str = ''


@dataclass
class ParentRecord:
    """
    聚合院落信息
    """
    face_id: str
    geom: BaseGeometry
    bid: str
    name: str = ''
    tag: str = ''
    children: list[ChildRecord] = field(default_factory=list)
    can_output: bool = True
    reason: str = ''


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    bid_list_path: Path
    mode: str
    parent_records: list[ParentRecord] = field(default_factory=list)
    batch_records: list[BatchAoiRelRecord] = field(default_factory=list)


def batch_process_aoi(aois, process, use_tqdm=True):
    """
    封装统一的批处理过滤逻辑
    """
    for aoi in tqdm(aois) if use_tqdm else aois:
        if not aoi.can_output:
            continue

        process(aoi)


def is_valid_tag(tag):
    """
    判断 tag 是否有效
    """
    return tag == '房地产' or tag == '房地产;住宅区'


def load_records_by_master_lib(ctx: Context):
    """
    通过母库加载需要批处理的聚合院落
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select a.face_id, b.poi_bid, st_astext(a.geom)
            from blu_face a
            inner join blu_face_poi b
            on a.face_id = b.face_id
            where a.src != 'SD' and
                  a.aoi_level = 1 and
                  a.kind != '52'
        '''
        for face_id, poi_bid, wkt in pgsql.fetch_all(conn, sql):
            ctx.parent_records.append(ParentRecord(
                face_id=face_id,
                bid=poi_bid,
                geom=shapely.wkt.loads(wkt),
            ))


def load_records_by_file(ctx: Context):
    """
    通过文件加载需要批处理的聚合院落
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as conn,
        open(ctx.bid_list_path, 'r') as f,
    ):
        for bid in tqdm([x.strip() for x in f.readlines()]):
            sql = '''
                select a.face_id, b.poi_bid, st_astext(a.geom)
                from blu_face a
                inner join blu_face_poi b
                on a.face_id = b.face_id
                where b.poi_bid = %s and 
                      a.src != 'SD' and
                      a.aoi_level = 1 and
                      a.kind != '52'
            '''
            row = pgsql.fetch_one(conn, sql, (bid,))
            if not row:
                continue

            face_id, poi_bid, wkt = row
            ctx.parent_records.append(ParentRecord(
                face_id=face_id,
                bid=poi_bid,
                geom=shapely.wkt.loads(wkt),
            ))


@desc()
def load_records(ctx: Context, proceed):
    """
    加载需要批处理的聚合院落
    """
    if ctx.mode == 'lib':
        load_records_by_master_lib(ctx)
    elif ctx.mode == 'file':
        load_records_by_file(ctx)

    proceed()


@desc()
def filter_parents_by_complete(ctx: Context, proceed):
    """
    过滤掉非精准的聚合院落
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process(parent: ParentRecord):
            sql = f'''
                select * from blu_face_complete where face_id = %s and aoi_complete >= 3;
            '''
            if pgsql.fetch_one(conn, sql, (parent.face_id,)):
                parent.can_output = False
                parent.reason = 'is completed'

    batch_process_aoi(ctx.parent_records, process)
    proceed()


@desc()
def filter_parents_by_area(ctx: Context, proceed):
    """
    使用面积过滤聚合院落
    """
    max_area = 2 * 1000 * 1000e-10

    def process(parent: ParentRecord):
        if parent.geom.area > max_area:
            parent.can_output = False
            parent.reason = f'parent aoi area too large'

    batch_process_aoi(ctx.parent_records, process)
    proceed()


@desc()
def filter_parents_by_invalidation(ctx: Context, proceed):
    """
    过滤掉无效的聚合院落
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        def process(parent: ParentRecord):
            sql = '''
                select name, std_tag from poi where bid = %s;
            '''
            row = pgsql.fetch_one(conn, sql, (parent.bid,))
            if not row:
                parent.can_output = False
                parent.reason = 'parent_bid is invalid'
                return

            parent.name, parent.tag = row
            if not is_valid_tag(parent.tag):
                parent.can_output = False
                parent.reason = 'not realty'

        batch_process_aoi(ctx.parent_records, process)

    proceed()


@desc()
def find_children_aois(ctx: Context, proceed):
    """
    查找聚合院落下的基础院落
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process(parent: ParentRecord):
            sql = '''
                select b.poi_bid, a.face_id, a.src, st_astext(a.geom)
                from blu_face a              
                inner join blu_face_poi b
                on a.face_id = b.face_id              
                where st_intersects(st_geomfromtext(%s, 4326), a.geom) and
                      a.src != 'SD' and
                      a.aoi_level = 2
            '''
            for child_bid, face_id, src, wkt in pgsql.fetch_all(conn, sql, (parent.geom.wkt,)):
                parent.children.append(ChildRecord(
                    geom=shapely.wkt.loads(wkt),
                    bid=child_bid,
                    face_id=face_id,
                    src=src,
                ))

        batch_process_aoi(ctx.parent_records, process)

    proceed()


@desc()
def filter_children_by_area(ctx: Context, proceed):
    """
    过滤掉面积过小的基础院落
    """
    min_area = 2000e-10

    def process_child(child: ChildRecord):
        if child.geom.area < min_area:
            child.can_output = False
            child.reason = 'child aoi area too small'

    batch_process_aoi(
        ctx.parent_records,
        lambda parent_aoi: batch_process_aoi(parent_aoi.children, process_child, False)
    )
    proceed()


@desc()
def filter_children_by_inner_aoi(ctx: Context, proceed):
    """
    过滤掉包含内部院落的基础院落
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        def process_child(child: ChildRecord):
            sql = '''
                select b.poi_bid
                from blu_face a
                inner join blu_face_poi b
                on a.face_id = b.face_id
                where st_contains(st_geomfromtext(%s, 4326), a.geom) and
                      a.src != 'SD' and
                      a.kind != '52' and
                      a.aoi_level = 3;
            '''
            for bid, in pgsql.fetch_all(aoi_conn, sql, (child.geom.wkt,)):
                sql = '''
                    select 1 from poi where bid = %s;
                '''
                if pgsql.fetch_one(poi_conn, sql, (bid,)):
                    child.can_output = False
                    child.reason = 'has inner aoi'

        batch_process_aoi(
            ctx.parent_records,
            lambda parent_aoi: batch_process_aoi(parent_aoi.children, process_child, False)
        )

    proceed()


@desc()
def filter_children_by_aoi_relation(ctx: Context, proceed):
    """
    仅保留未建立层级关联的基础院落
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process_child(child: ChildRecord):
            sql = '''
                select 1 from blu_aoi_rel where face_id2 = %s limit 1;
            '''
            if pgsql.fetch_one(conn, sql, (child.face_id,)):
                child.can_output = False
                child.reason = 'aoi_relation exists'

        batch_process_aoi(
            ctx.parent_records,
            lambda parent_aoi: batch_process_aoi(parent_aoi.children, process_child, False)
        )

    proceed()


@desc()
def filter_children_by_invalidation(ctx: Context, proceed):
    """
    过滤掉无效的基础院落
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        def process(parent: ParentRecord):
            for child in parent.children:
                if not child.can_output:
                    continue

                sql = '''
                    select std_tag, name from poi where bid = %s;
                '''
                row = pgsql.fetch_one(conn, sql, (child.bid,))
                if not row:
                    child.can_output = False
                    child.reason = 'child_bid is invalid'
                    return

                child.tag, child.name = row
                if not is_valid_tag(child.tag):
                    child.can_output = False
                    child.reason = 'tag is not same'

        batch_process_aoi(ctx.parent_records, process)

    proceed()


@desc()
def filter_children_by_name(ctx: Context, proceed):
    """
    过滤掉名称不相似的基础院落
    """
    min_name_iou = 0.333

    def process(parent: ParentRecord):
        for child in parent.children:
            if not child.can_output:
                continue

            parent_name_set = set(parent.name)
            child_name_set = set(child.name)
            intersection_length = len(parent_name_set.intersection(child_name_set))
            union_length = len(parent_name_set.union(child_name_set))
            if union_length > 0:
                child.name_iou = intersection_length / union_length

            if child.name_iou < min_name_iou:
                child.can_output = False
                child.reason = f'name_iou {child.name_iou:.3f} < min_name_iou {min_name_iou:.3f}'

    batch_process_aoi(ctx.parent_records, process)
    proceed()


@desc()
def fill_children_overlap_type(ctx: Context, proceed):
    """
    仅保留被聚合院落完全包含的基础院落
    """

    def process(parent: ParentRecord):
        for child in parent.children:
            if not child.can_output:
                continue

            if parent.geom.contains(child.geom):
                child.overlap_type = 'contains'
            elif parent.geom.intersects(child.geom):
                child.overlap_type = 'intersects'
            else:
                child.overlap_type = 'none'

            if child.overlap_type != 'contains':
                child.can_output = False
                child.reason = f'overlap_type {child.overlap_type}'

    batch_process_aoi(ctx.parent_records, process)
    proceed()


@desc()
def fill_batch_records(ctx: Context, proceed):
    """
    生成批处理记录
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process(parent: ParentRecord):
            sql = f'''
                select a.face_id, a.mesh_id
                from blu_face a
                inner join blu_face_poi b
                on a.face_id = b.face_id
                where b.poi_bid = %s
                limit 1;
            '''

            parent_row = pgsql.fetch_one(conn, sql, (parent.bid,))
            if not parent_row:
                return

            face_id1, mesh_id = parent_row

            for child in [x for x in parent.children if x.can_output]:
                child_row = pgsql.fetch_one(conn, sql, (child.bid,))
                if not child_row:
                    return

                face_id2, _ = child_row
                ctx.batch_records.append(BatchAoiRelRecord(
                    bid=parent.bid,
                    bid2=child.bid,
                    face_id=face_id1,
                    face_id2=face_id2,
                    mesh_id=mesh_id
                ))

        batch_process_aoi(ctx.parent_records, process)

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
        for record in tqdm(ctx.batch_records):
            result = add_aoi_rel(
                back_stabilizer,
                face_id1=record.face_id,
                face_id2=record.face_id2,
                mesh_id=record.mesh_id
            )
            if not result.success:
                print(result.msg)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存批处理结果
    """
    output_items = []

    def process(parent: ParentRecord):
        for child in parent.children:
            if not child.can_output:
                continue

            output_items.append(
                [
                    parent.bid,
                    parent.name,
                    child.name,
                    child.name_iou,
                    parent.tag,
                    child.bid,
                    child.src,
                    child.tag,
                    child.overlap_type,
                ]
            )

    batch_process_aoi(ctx.parent_records, process)
    tsv.write_tsv(
        ctx.work_dir / f"output_{datetime.datetime.now().strftime('%Y%m%d')}.tsv",
        output_items,
    )
    proceed()


@desc()
def save_debug_records(ctx: Context, proceed):
    """
    保存批处理结果
    """
    output_items = []

    for parent in ctx.parent_records:
        for child in parent.children:
            output_items.append(
                [
                    child.reason,
                    parent.reason,
                    parent.bid,
                    parent.name,
                    child.name,
                    child.name_iou,
                    parent.tag,
                    child.bid,
                    child.src,
                    child.tag,
                    child.overlap_type,
                ]
            )

    tsv.write_tsv(
        ctx.work_dir / f"output_{datetime.datetime.now().strftime('%Y%m%d')}_debug.tsv",
        output_items,
    )
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        default='lib',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        filter_parents_by_complete,
        filter_parents_by_area,
        filter_parents_by_invalidation,
        find_children_aois,
        filter_children_by_area,
        filter_children_by_inner_aoi,
        filter_children_by_aoi_relation,
        filter_children_by_invalidation,
        filter_children_by_name,
        fill_children_overlap_type,
        fill_batch_records,
        execute_batch,
        save_records,
        # save_debug_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/auto_update_aoi_rel'),
        bid_list_path=Path(args.bid_list_path),
        mode=args.mode,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
