# !/usr/bin/env python3
"""
本类封装了在批处理时常用的帮助方法。
"""

import json
from dataclasses import dataclass, field

import pymysql
import pymysql.cursors
from shapely import Polygon
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.fix_geom_process.tools import multipolygon_to_single
from src.tools import pgsql
from src.tools.conf_tools import get_mysql_conf
from src.tools.connect_aoi_to_poi import get_face_ids_in_batch_processing, get_bids_in_working


@dataclass
class BatchRecord:
    """
    批处理记录
    """
    action: str = field(init=False)
    description: str = field(init=False)
    face_id: str = field(init=False)
    bid: str = field(init=False)
    mesh_id: str = field(init=False)
    flow_state: str = field(init=False)
    priority: int = field(init=False)
    can_process: bool = field(init=False)
    extra: dict = field(init=False)
    reason: str = field(init=False)

    def __post_init__(self):
        self.flow_state = '待入库'
        self.priority = 0
        self.can_process = True
        self.extra = field(default_factory=dict)
        self.reason = ''

    def get_batch_info(self):
        """
        获取批处理信息
        """
        return {
            'bid': self.bid,
            'face_id': self.face_id,
            'action': self.action,
            'imp_state': 0,
            'src': self.description,
            'flow_state': self.flow_state,
            'mesh_id': self.mesh_id,
            'resource': 6,
            'pri': self.priority,
            'extra': json.dumps(self.extra)
        }


def batch_process(records, process, use_tqdm=True, can_process=None):
    """
    对指定集合进行批处理动作
    """
    valid_count = 0

    for record in tqdm(records) if use_tqdm else records:
        if not can_process and not record.can_process:
            continue
        elif can_process and not can_process(record):
            continue

        valid_count += 1
        process(record)

    if can_process:
        print(f'valid count: {valid_count}')


def post_batch_records(records: list[BatchRecord],
                       ignore_processing_records: bool = False,
                       ignore_working_records: bool = False):
    """
    执行批处理
    """
    with(
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
    ):
        ignored_face_ids = set(get_face_ids_in_batch_processing()) if ignore_processing_records else None
        ignored_bids = set(get_bids_in_working()) if ignore_working_records else None

        try:
            for record in tqdm(records):
                if not record.can_process:
                    continue

                if ignored_face_ids and record.face_id in ignored_face_ids:
                    continue

                if ignored_bids and record.bid in ignored_bids:
                    continue

                batch_info = record.get_batch_info()

                sql = (
                        "insert into gate_bid_change_history(%s)" % ','.join(batch_info.keys()) +
                        "values (%s)" % ','.join(len(batch_info) * ['%s'])
                )
                cursor.execute(sql, (*batch_info.values(),))

            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e


def make_competitor_valid(geom):
    """
    确保竞品多边形有效。
    """
    if not geom:
        return None

    geom = geom.buffer(0)

    if not geom.is_valid:
        return None

    if geom.geom_type == 'MultiPolygon':
        geom = multipolygon_to_single(geom, 0)

    if geom.geom_type != 'Polygon':
        return None

    if any(geom.interiors):
        geom = Polygon(geom.exterior)

    if geom.is_empty:
        return None

    return geom


def contains_majority(geom1: BaseGeometry, geom2: BaseGeometry, ratio: float = 0.8):
    intersections = geom1.intersection(geom2)
    return intersections.area / geom2.area >= ratio


def get_geom_iou(geom1: BaseGeometry, geom2: BaseGeometry):
    """
    计算两个多边形的交并比
    """
    intersection = geom1.intersection(geom2).area
    union = geom1.union(geom2).area
    if union == 0:
        return 0.0

    return intersection / union


def get_str_iou(str1, str2):
    """
    计算两个字符串的相似度
    """
    set1 = set(str1)
    set2 = set(str2)
    intersection_length = len(set1.intersection(set2))
    union_length = len(set1.union(set2))
    return intersection_length / union_length if union_length > 0 else 0.0


def get_all_poi_mesh_ids():
    """
    获取所有 poi 的图幅号
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = '''
            select distinct mesh_id from poi;
        '''
        return [x[0] for x in pgsql.fetch_all(conn, sql)]


def get_all_aoi_mesh_ids():
    """
    获取所有 aoi 的图幅号
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select distinct mesh_id from blu_face;
        '''
        return [x[0] for x in pgsql.fetch_all(conn, sql)]


def get_mysql_connection(name, cursor_class=pymysql.cursors.Cursor):
    """
    获取 mysql 连接
    """
    host, port, user, pwd, database = get_mysql_conf(name)
    return pymysql.connect(
        host=host,
        port=int(port),
        user=user,
        password=pwd,
        db=database,
        charset="utf8mb4",
        cursorclass=cursor_class,
    )
