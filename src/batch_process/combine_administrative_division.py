# !/usr/bin/env python3
"""
合并行政区划，例如将镇合并为县，将县合并为市。
"""
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from shapely import Polygon, MultiPolygon
from tqdm import tqdm

from src.tools import pipeline, pgsql

ADMIN_CONFIG = {
    'host': '**************',
    'db': 'business_order',
    'port': '7432',
    'user': 'business_order_se_rw',
    'pwd': 'kpigzear',
}

desc = pipeline.get_desc()


@dataclass
class AdminRecord:
    """
    行政区划记录
    """
    code: str
    name: str
    level: int
    geom: Polygon = Polygon()
    children: list = field(default_factory=list)

    def get_or_create_child(self, name, code):
        """
        获取或创建子区域
        """
        for child in self.children:
            if child.code == code and child.name == name:
                return child
        child = AdminRecord(name=name, code=code, level=self.level + 1)
        self.children.append(child)
        return child

    def get_children_count(self, level):
        """
        获取子区域数量
        """
        if level > self.level:
            return sum(child.get_children_count(level) for child in self.children)
        elif level == self.level:
            return 1
        else:
            return 0


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    country: AdminRecord

    def __post_init__(self):
        with open(self.work_dir / 'admins.tsv', 'r', encoding='utf8') as f:
            lines = f.readlines()
            for x in tqdm(lines, total=len(lines), desc='加载行政区划名'):
                province_name, province_code, city_name, city_code, county_name, county_code, town_name, town_code = \
                    tuple(x.strip().split('\t'))
                self.country \
                    .get_or_create_child(province_name, province_code) \
                    .get_or_create_child(city_name, city_code) \
                    .get_or_create_child(county_name, county_code) \
                    .get_or_create_child(town_name, town_code)


def clean_polygon(geometry):
    """
    清理多边形
    """
    min_area = 1e-10
    geometry_type = geometry.geom_type

    if geometry_type == 'Polygon':
        return Polygon(geometry.exterior)
    elif geometry_type == 'GeometryCollection':
        multi_polygon = []
        geoms = list(geometry.geoms)
        polygon_count = len(geoms)
        # 删除 GEOMETRYCOLLECTION 中的 LINESTRING
        for geometry_index in range(polygon_count):
            child_geometry = geoms[geometry_index]
            if child_geometry.geom_type == 'Polygon':
                multi_polygon.append(child_geometry)
        return clean_polygon(MultiPolygon(multi_polygon))
    elif geometry_type == 'MultiPolygon':
        geoms = list(geometry.geoms)
        polygon_count = len(geoms)
        if polygon_count == 1:
            return clean_polygon(geoms[0])

        cleaned_multi_polygon = []
        polygons = []

        for polygon_index in range(polygon_count):
            polygon = geoms[polygon_index]
            if polygon.area > min_area:
                polygons.append(clean_polygon(polygon))

        for polygon_a in polygons:
            add = True
            for polygon_b in polygons:
                if polygon_a == polygon_b:
                    continue
                if polygon_a.within(polygon_b):
                    add = False
                    break
            if add:
                cleaned_multi_polygon.append(polygon_a)

        return MultiPolygon(cleaned_multi_polygon)

    return geometry


@desc()
def combine_towns(ctx: Context, proceed):
    """
    合并镇
    """
    with tqdm(total=ctx.country.get_children_count(3), desc='合并镇') as bar:
        with pgsql.get_connection(ADMIN_CONFIG) as conn:
            for province in ctx.country.children:
                for city in province.children:
                    for county in city.children:
                        sql = f'''
                            select st_astext(geom)
                            from bad_admin_town 
                            where pro_name = '{province.name}' and
                                  city_name = '{city.name}' and
                                  county_name = '{county.name}' and
                                  not st_isempty(geom)
                        '''
                        geoms = [shapely.wkt.loads(x[0]) for x in pgsql.fetch_all(conn, sql)]
                        union_geom = shapely.union_all(geoms)
                        parent_geom = shapely.set_precision(clean_polygon(union_geom), 1e-10)
                        county.geom = parent_geom
                        bar.update(1)

    proceed()


@desc()
def combine_counties(ctx: Context, proceed):
    """
    合并县
    """
    with tqdm(total=ctx.country.get_children_count(2), desc='合并县') as bar:
        with pgsql.get_connection(ADMIN_CONFIG) as conn:
            for province in ctx.country.children:
                for city in province.children:
                    sql = f'''
                        select st_astext(location)
                        from bad_city 
                        where province_name = '{province.name}' and
                              city_name = '{city.name}' and
                              level = '3' and
                              not st_isempty(location)
                    '''
                    geoms = [shapely.wkt.loads(x[0]) for x in pgsql.fetch_all(conn, sql)]
                    union_geom = shapely.union_all(geoms)
                    parent_geom = shapely.set_precision(clean_polygon(union_geom), 1e-10)
                    city.geom = parent_geom
                    bar.update(1)

    proceed()


@desc()
def combine_cities(ctx: Context, proceed):
    """
    合并市
    """
    with tqdm(total=ctx.country.get_children_count(1), desc='合并市') as bar:
        with pgsql.get_connection(ADMIN_CONFIG) as conn:
            for province in ctx.country.children:
                sql = f'''
                    select st_astext(location)
                    from bad_city 
                    where province_name = '{province.name}' and
                          level = '2' and
                          not st_isempty(location)
                '''
                geoms = [shapely.wkt.loads(x[0]) for x in pgsql.fetch_all(conn, sql)]
                union_geom = shapely.union_all(geoms)
                parent_geom = shapely.set_precision(clean_polygon(union_geom), 1e-10)
                province.geom = parent_geom
                bar.update(1)

    proceed()


@desc()
def upload_counties_to_db(ctx: Context, proceed):
    """
    上传县到数据库
    """
    with tqdm(total=ctx.country.get_children_count(3), desc='入库县') as bar:
        with pgsql.get_connection(ADMIN_CONFIG) as conn:
            for province in ctx.country.children:
                for city in province.children:
                    for county in city.children:
                        sql = f'''
                            insert into bad_city (
                                province_id,
                                province_name,
                                city_id,
                                city_name,
                                county_id,
                                county_name,
                                location,
                                level
                            )
                            values (
                                %s, %s, %s, %s,
                                %s, %s, st_geomfromtext(%s, 4326), %s
                            )
                        '''
                        values = tuple([
                            province.code,
                            province.name,
                            city.code,
                            city.name,
                            county.code,
                            county.name,
                            county.geom.wkt,
                            county.level
                        ])
                        pgsql.execute(conn, sql, values)
                        bar.update(1)

    proceed()


@desc()
def upload_cities_to_db(ctx: Context, proceed):
    """
    上传市到数据库
    """
    with tqdm(total=ctx.country.get_children_count(2), desc='入库市') as bar:
        with pgsql.get_connection(ADMIN_CONFIG) as conn:
            for province in ctx.country.children:
                for city in province.children:
                    sql = f'''
                        insert into bad_city (
                            province_id,
                            province_name,
                            city_id,
                            city_name,
                            location,
                            level
                        )
                        values (
                            %s, %s, %s, %s,
                            st_geomfromtext(%s, 4326), %s
                        )
                    '''
                    values = tuple([
                        province.code,
                        province.name,
                        city.code,
                        city.name,
                        city.geom.wkt,
                        city.level
                    ])
                    pgsql.execute(conn, sql, values)
                    bar.update(1)

    proceed()


@desc()
def upload_provinces_to_db(ctx: Context, proceed):
    """
    上传省到数据库
    """
    with tqdm(total=ctx.country.get_children_count(1), desc='入库省') as bar:
        with pgsql.get_connection(ADMIN_CONFIG) as conn:
            for province in ctx.country.children:
                sql = f'''
                    insert into bad_city (
                        province_id,
                        province_name,
                        location,
                        level
                    )
                    values (
                        %s, %s,
                        st_geomfromtext(%s, 4326), %s
                    )
                                    '''
                values = tuple([
                    province.code,
                    province.name,
                    province.geom.wkt,
                    province.level
                ])
                pgsql.execute(conn, sql, values)
                bar.update(1)

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        combine_towns,
        upload_counties_to_db,
        combine_counties,
        upload_cities_to_db,
        combine_cities,
        upload_provinces_to_db,
    )
    desc.attach(main_pipe)

    ctx = Context(
        work_dir=Path('/home/<USER>/dingping/combine_administrative_division'),
        country=AdminRecord('0', '中国', 0),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
