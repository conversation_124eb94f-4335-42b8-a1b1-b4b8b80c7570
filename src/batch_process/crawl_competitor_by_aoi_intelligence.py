# !/usr/bin/env python3
"""
根据新增的 poi 抓取竞品
"""
import argparse
import datetime
import subprocess
from dataclasses import dataclass, field
from pathlib import Path

import pymysql
from tqdm import tqdm

from src.batch_process.batch_helper import batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv
from src.tools.conf_tools import get_mysql_conf

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    新增 poi 信息
    """
    bid: str
    name: str = ''
    relation_bid: str = ''
    std_tag: str = ''
    pv: int = -1
    reason: str = ''
    can_process: bool = True
    intelligence_id: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    crawl_script: Path
    src: str
    output_path: Path = field(init=False)
    record_ids: set[str] = field(default_factory=set)
    records: list[Record] = field(default_factory=list)

    def add_record(self, record: Record):
        if record.bid in self.record_ids:
            return

        self.record_ids.add(record.bid)
        self.records.append(record)


def get_connection():
    """
    获取 mysql 连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


@desc()
def load_records_by_poi_adding(ctx: Context, proceed):
    """
    加载新增的 poi
    """
    with (
        get_connection() as mysql_conn,
        mysql_conn.cursor() as cursor,
    ):
        sql = '''
            select bid 
            from poi_realtime_intelligence 
            where create_time >= date_sub(curdate(), interval 1 day) and
                  create_time < curdate();
        '''
        cursor.execute(sql)
        for bid, in tqdm(cursor.fetchall()):
            ctx.add_record(Record(
                bid=bid,
            ))

    proceed()


@desc()
def load_records_by_intelligence(ctx: Context, proceed):
    """
    根据情报加载 bid 集合
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        sql = '''
            select main_poi_bid, ref_qb_id
            from integration_qb 
            where work_type = 1 and  -- 自动任务
                  status = 0 and  -- 待创建工单
                  src = 4 and  -- 需要制作边框
                  strategy_type = '';  -- 计划类型（计划类型空的默认需要自动化上量边框）
        '''
        for bid, ref_qb_id in stabilizer.fetch_all(sql):
            ctx.add_record(Record(
                bid=bid,
                intelligence_id=ref_qb_id,
            ))

    proceed()


def fill_poi_properties(ctx: Context, proceed):
    """
    填充 poi 相关的信息
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        def process(record: Record):
            sql = f'''
                select name, relation_bid, std_tag, click_pv from poi where bid = %s;
            '''
            row = stabilizer.fetch_one(sql, (record.bid,))
            if row is None:
                record.can_process = False
                record.reason = 'poi not found'
                return

            record.name, record.relation_bid, record.std_tag, record.click_pv = row

        batch_process(ctx.records, process)

    proceed()


@desc()
def filter_records_by_history(ctx: Context, proceed):
    """
    过滤历史抓取过的 bid
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        for record in tqdm(ctx.records):
            sql = '''
                select bid 
                from aoi_intelligence_history 
                where bid = %s and 
                      create_time > (now() - interval '6 month')
                limit 1;
            '''
            if stabilizer.fetch_one(sql, (record.bid,)):
                record.can_process = False
                record.reason = 'is new'

    proceed()


@desc()
def filter_records_by_competitor_id(ctx: Context, proceed):
    """
    仅抓取有三方 id 的 bid
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        for record in tqdm(ctx.records):
            if not record.can_process:
                continue

            sql = '''
                select bid, competitor_id from competitor_id where bid = %s and src = %s;
            '''
            if not stabilizer.fetch_one(sql, (record.bid, ctx.src)):
                record.can_process = False
                record.reason = 'competitor_id not found'

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存需要抓取的 bid
    """
    ctx.output_path = ctx.work_dir / f"output_{datetime.datetime.now().strftime('%Y%m%d')}.tsv"
    tsv.write_tsv(
        ctx.output_path,
        [
            [
                x.reason,
                x.bid,
            ]
            for x in ctx.records
        ]
    )

    proceed()


@desc()
def execute_crawl(ctx: Context, proceed):
    """
    执行抓取
    """
    crawl_competitor_process = subprocess.Popen(
        [
            'py39',
            str(ctx.crawl_script),
            '--region',
            str(ctx.output_path),
            '--priority',
            '1'  # 优先级默认是 0，设置为 1 保证先执行。
        ], stdout=subprocess.PIPE
    )
    crawl_competitor_process.wait()
    proceed()


@desc()
def mark_tickets_status(ctx: Context, proceed):
    """
    标记情报状态
    """
    status_work_doing = 1
    status_work_error = 5

    with PgsqlStabilizer(pgsql.POI_CONFIG) as stabilizer:
        for record in tqdm(ctx.records):
            if record.intelligence_id == '':
                continue

            status = status_work_error if not record.can_process else status_work_doing
            sql = '''
                update integration_qb set status = %s where ref_qb_id = %s;
            '''
            stabilizer.execute(sql, (status, record.intelligence_id))

    proceed()


def parse_args():
    """
    解析参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--src',
        dest='src',
        help='竞品源 {dtc | (sjc)}',
        type=str,
        default='sjc',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records_by_poi_adding,
        load_records_by_intelligence,
        fill_poi_properties,
        # filter_records_by_history,
        filter_records_by_competitor_id,
        save_records,
        execute_crawl,
        mark_tickets_status,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/crawl_competitor_by_aoi_intelligence'),
        crawl_script=Path('/home/<USER>/chenjie/product/src/aoi_cleanup_strategy/create_crawl_competitor_aoi_task.py'),
        src=args.src,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
