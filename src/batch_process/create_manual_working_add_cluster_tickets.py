# !/usr/bin/env python3
"""
使用 poi 提取待建聚合院落边框。
REF: https://ku.baidu-int.com/d/Ve9IIF8L7B3iJP
"""
from dataclasses import dataclass, field
from pathlib import Path

import rtree
import shapely.wkt
from shapely import Point, LineString
from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.create_manual_working_tickets_helper import Context, TicketRecord
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.model.aoi_model import AoiModel
from src.tools import pipeline, pgsql, tsv

PROVIDER_NAME = 'add_cluster'
PROVIDER_VERSION = '1.0.0'
PROVIDER_PRIORITY = 99

PREFIX_SERIAL = [
    '**********',
    '一二三四五六七八九十',
    'ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ',
    'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    'abcdefghijklmnopqrstuvwxyz',
    '东南西北',
]
POSTFIX_SERIAL = [
    '期',
    '区',
    '片区',
    '号片区',
    '块',
    '地块',
    '号地块',
    '院',
    '号院',
]
IGNORED_POSTFIX = [
    '号',
    '号楼',
    '栋',
    '幢',
    '楼',
    '门',
]
IGNORED_NAMES = [
    '停车场',
    '停车区',
]
IGNORED_TAGS = [
    '行政区划',
    '行政地标',
]

desc = pipeline.get_desc()


@dataclass
class ChildPoi:
    """
    子点 POI 信息
    """
    bid: str
    full_name: str
    prefix_name: str
    geom: Point
    std_tag: str
    pv: int


@dataclass
class Record:
    """
    清单记录
    """
    bid: str
    child_bid: str
    prefix_name: str = ''
    name: str = ''
    std_tag: str = ''
    pv: int = -1
    can_post: bool = True
    reason: str = ''
    wkt: str = ''
    src: str = ''


@dataclass
class InnerContext:
    """
    脚本执行上下文
    """
    work_dir: Path
    poi_min_distance: float
    min_pv: int
    mesh_ids: list[str]
    poi_postfix: set[str] = field(default_factory=set)
    records: list[Record] = field(default_factory=list)
    children_pois: dict[str, list[ChildPoi]] = field(default_factory=dict)
    serial_numbers: set[str] = field(default_factory=set)
    tickets: list[TicketRecord] = field(default_factory=list)
    desired_bids: set[str] = field(default_factory=set)

    def __post_init__(self):
        for prefix_items in PREFIX_SERIAL:
            for prefix_item in prefix_items:
                for postfix_item in POSTFIX_SERIAL:
                    self.serial_numbers.add(f'{prefix_item}{postfix_item}')

    def insert_record(self, child_poi: ChildPoi):
        """
        插入清单记录
        """
        self.records.append(Record(
            bid=child_poi.bid,
            child_bid='',
            prefix_name=child_poi.prefix_name,
            name=child_poi.full_name,
            std_tag=child_poi.std_tag,
            pv=child_poi.pv,
            wkt=child_poi.geom.wkt,
        ))


def get_str_iou(str1, str2):
    """
    计算两个字符串的相似度
    """
    set1 = set(str1)
    set2 = set(str2)
    intersection_length = len(set1.intersection(set2))
    union_length = len(set1.union(set2))
    return intersection_length / union_length if union_length > 0 else 0.0


def get_similarity_of_parent_and_son(conn, parent_bid, child_name, child_tag):
    """
    判断 poi 父子点是否相似
    """
    min_name_iou = 0.33333

    if child_tag not in tags.ACCURATE_WITHOUT_SCENIC:
        return False

    # 使用 tag、名称相似度找到聚合院落
    sql = '''
        select name from poi where bid = %s and std_tag = %s;
    '''
    row = conn.fetch_one(sql, (parent_bid, child_tag))
    if not row:
        return False

    parent_name, = row
    name_iou = get_str_iou(child_name, parent_name)
    return name_iou >= min_name_iou


def get_record_src(ctx: InnerContext, conn, parent_bid, child_name, child_tag):
    """
    获取记录生成渠道
    """
    if any(child_name.endswith(x) for x in ctx.serial_numbers) and all(x not in child_name for x in IGNORED_NAMES):
        return 'postfix'

    if get_similarity_of_parent_and_son(conn, parent_bid, child_name, child_tag):
        return 'similarity'

    return None


def load_parent_bids_by_mesh_id(ctx: InnerContext):
    """
    遍历图幅，加载符合要求的父点 bid
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        for mesh_id in tqdm(ctx.mesh_ids):
            sql = f'''
                select relation_bid, bid, name, std_tag, st_astext(geometry)
                from poi 
                where relation_bid != '0' and 
                      relation_bid != '' and
                      mesh_id = %s;
            '''
            for parent_bid, child_bid, child_name, child_tag in conn.fetch_all(sql, (mesh_id,)):
                record_src = get_record_src(ctx, conn, parent_bid, child_name, child_tag)
                if not record_src:
                    continue

                ctx.records.append(Record(
                    bid=parent_bid,
                    child_bid=child_bid,
                    name=child_name,
                    src=record_src,
                ))


def load_parent_bids_by_bid(ctx: InnerContext):
    """
    加载指定 bid 的父点
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        for parent_bid in tqdm(ctx.desired_bids):
            sql = f'''
                select bid, name, std_tag
                from poi 
                where relation_bid = %s;
            '''
            for child_bid, child_name, child_tag in conn.fetch_all(sql, (parent_bid,)):
                record_src = get_record_src(ctx, conn, parent_bid, child_name, child_tag)
                if not record_src:
                    continue

                ctx.records.append(Record(
                    bid=parent_bid,
                    child_bid=child_bid,
                    name=child_name,
                    src=record_src,
                ))


@desc()
def load_parent_bids(ctx: InnerContext, proceed):
    """
    加载符合要求的父点 bid
    """
    if not any(ctx.desired_bids):
        load_parent_bids_by_mesh_id(ctx)
    else:
        load_parent_bids_by_bid(ctx)

    proceed()


@desc()
def load_children_pois(ctx: InnerContext, proceed):
    """
    加载符合要求的子点 bid
    """
    min_name_count = 2

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        for mesh_id in tqdm(ctx.mesh_ids):
            sql = f'''
                select bid, name, st_astext(geometry), std_tag, click_pv, relation_bid
                from poi 
                where mesh_id = %s;
            '''
            for bid, name, wkt, std_tag, click_pv, relation_bid in conn.fetch_all(sql, (mesh_id,)):
                if relation_bid == '0' or relation_bid == '':
                    continue

                names = name.split('-')
                if len(names) < min_name_count:
                    continue

                prefix_name = names[0]

                children = ctx.children_pois.get(prefix_name, list())
                children.append(ChildPoi(
                    bid=bid,
                    full_name=name,
                    prefix_name=prefix_name,
                    geom=shapely.wkt.loads(wkt),
                    std_tag=std_tag,
                    pv=click_pv,
                ))
                ctx.children_pois[prefix_name] = children

    proceed()


def remove_pois(ctx: InnerContext, children: list[ChildPoi]):
    """
    删除子点
    """
    pending_remove_parent_names = []
    for child in children:
        ctx.children_pois[child.prefix_name].remove(child)
        if not any(ctx.children_pois[child.prefix_name]):
            pending_remove_parent_names.append(child.prefix_name)

    for name in pending_remove_parent_names:
        del ctx.children_pois[name]


def filter_pois(ctx: InnerContext, poi_filter):
    """
    过滤子点
    """
    pending_remove_pois = []

    for parent_name, children in tqdm(ctx.children_pois.items(), total=len(ctx.children_pois)):
        for child in children:
            if not poi_filter(child):
                pending_remove_pois.append(child)

    remove_pois(ctx, pending_remove_pois)


@desc()
def filter_pois_by_postfix(ctx: InnerContext, proceed):
    """
    使用后缀过滤子点
    """
    filter_pois(ctx, lambda child: any(child.full_name.endswith(x) for x in ctx.serial_numbers))
    proceed()


@desc()
def filter_pois_by_name(ctx: InnerContext, proceed):
    """
    使用名称过滤子点
    """
    filter_pois(ctx, lambda child: all(x not in child.full_name for x in IGNORED_NAMES))
    proceed()


@desc()
def filter_pois_by_overlap_aois(ctx: InnerContext, proceed):
    """
    通过压盖逻辑过滤子点
    """
    business_area_kind = '52'
    cluster_aoi = 1
    basic_aoi = 2
    internal_aoi = 3
    pending_remove_pois = []

    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for parent_name, children in tqdm(ctx.children_pois.items(), total=len(ctx.children_pois)):
            for child in children:
                sql = f'''
                    select a.aoi_level, b.poi_bid
                    from blu_face a
                    inner join blu_face_poi b
                    on a.face_id = b.face_id
                    where st_intersects(geom, st_geomfromtext(%s, 4326)) and
                          kind != %s;
                '''
                # 将行政区划排除。
                aoi_levels = set()
                for aoi_level, poi_bid in pgsql.fetch_all(aoi_conn, sql, (child.geom.wkt, business_area_kind)):
                    sql = f'''
                        select std_tag from poi where bid = %s;
                    '''
                    row = poi_conn.fetch_one(sql, (poi_bid,))
                    tag = row[0] if row else ''
                    if all(x not in tag for x in IGNORED_TAGS):
                        aoi_levels.add(aoi_level)

                if (
                        (cluster_aoi in aoi_levels and basic_aoi in aoi_levels) or
                        (basic_aoi in aoi_levels and internal_aoi in aoi_levels)
                ):
                    pending_remove_pois.append(child)

    remove_pois(ctx, pending_remove_pois)
    proceed()


def cluster_pois(min_distance, children: list[ChildPoi]):
    """
    将子点聚类
    """
    rtree_index = rtree.index.Index(interleaved=True)
    clustered_pois = []

    for current_child in children:
        if len(clustered_pois) == 0:
            clustered_pois.append(current_child)
            rtree_index.insert(len(clustered_pois), current_child.geom.bounds)
        else:
            if not any(rtree_index.intersection(current_child.geom.buffer(min_distance).bounds)):
                clustered_pois.append(current_child)
                rtree_index.insert(len(clustered_pois), current_child.geom.bounds)

    return clustered_pois


@desc()
def group_pois(ctx: InnerContext, proceed):
    """
    对聚类后的子点进行分组
    """
    for parent_name, children in tqdm(ctx.children_pois.items(), total=len(ctx.children_pois)):
        if len(children) == 1:
            ctx.insert_record(children[0])
        else:
            for child in cluster_pois(ctx.poi_min_distance, children):
                ctx.insert_record(child)

    proceed()


def group_records_by_parent_bid(records: list[Record]) -> dict[str, list[Record]]:
    """
    使用父点 bid 对清单进行分组
    """
    children_map = {}
    for record in records:
        children = children_map.get(record.bid, [])
        children.append(record)
        children_map[record.bid] = children

    return children_map


def is_child_names_all_ignored(children: list[Record]):
    """
    判断子点名称是否全部为忽略的名称
    """
    for child in children:
        if not any(x in child.name for x in IGNORED_NAMES):
            return False

    return True


@desc()
def filter_records_by_child_name(ctx: InnerContext, proceed):
    """
    使用子点名称过滤掉不合理的记录
    """
    similarity_records = [x for x in ctx.records if x.can_post and x.src == 'similarity']
    children_map = group_records_by_parent_bid(similarity_records)
    invalid_parent_bids = {
        parent_bid for parent_bid, children in children_map.items()
        if is_child_names_all_ignored(children)
    }

    for record in similarity_records:
        if record.bid in invalid_parent_bids:
            record.can_post = False
            record.reason = '子点均包含停车场'
            continue

        if any(record.name.endswith(x) for x in IGNORED_POSTFIX):
            record.can_post = False
            record.reason = '子点后缀非预期'

    proceed()


def get_poi_wkt(poi_conn, bid):
    """
    获取 poi 的 wkt
    """
    sql = '''
        select st_astext(geometry) from poi where bid = %s;
    '''
    row = poi_conn.fetch_one(sql, (bid,))
    return shapely.wkt.loads(row[0]) if row else None


def is_cross_high_level_road(poi_conn, road_conn, parent_geom, children: list[Record]):
    min_kind = 7  # 数值越小，道路等级越大，所以这里命名是 min_kind。

    for child in children:
        child_geom = get_poi_wkt(poi_conn, child.child_bid)
        if not child_geom:
            continue

        line = LineString([parent_geom, child_geom])
        sql = '''
            select 1
            from nav_link
            where st_intersects(st_geomfromtext(%s, 4326), geom) and
                  kind <= %s
            limit 1;
        '''
        if pgsql.fetch_one(road_conn, sql, (line.wkt, min_kind)):
            return True

    return False


@desc()
def filter_records_by_child_position(ctx: InnerContext, proceed):
    """
    使用子点位置过滤掉不合理的记录
    """
    similarity_records = [x for x in ctx.records if x.can_post and x.src == 'similarity']
    children_map = group_records_by_parent_bid(similarity_records)
    invalid_parent_bids = set()

    with (
        AoiModel() as aoi_model,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for parent_bid, children in tqdm(children_map.items()):
            parent_geom = get_poi_wkt(poi_conn, parent_bid)
            if not parent_geom:
                invalid_parent_bids.add(parent_bid)
                continue

            if not is_cross_high_level_road(poi_conn, aoi_model.conn_road, parent_geom, children):
                invalid_parent_bids.add(parent_bid)

    for record in similarity_records:
        if record.bid in invalid_parent_bids:
            record.can_post = False
            record.reason = '父子连线均未跨高等级道路'

    proceed()


@desc()
def filter_records_by_child_count(ctx: InnerContext, proceed):
    """
    使用子点数量过滤掉不合理的记录
    """
    min_child_count = 2
    similarity_records = [x for x in ctx.records if x.can_post and x.src == 'similarity']
    children_map = group_records_by_parent_bid(similarity_records)
    invalid_parent_bids = set()

    for parent_bid, children in children_map.items():
        if len(children) < min_child_count:
            invalid_parent_bids.add(parent_bid)

    for record in similarity_records:
        if record.bid in invalid_parent_bids:
            record.can_post = False
            record.reason = f'子点数量小于 {min_child_count}'

    proceed()


@desc()
def filter_records_by_online_aois(ctx: InnerContext, proceed):
    """
    过滤掉已经存在 AOI 的记录
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for record in tqdm(ctx.records):
            if not record.can_post:
                continue

            sql = f'''
                select 1
                from blu_face a
                inner join blu_face_poi b
                on a.face_id = b.face_id
                where b.poi_bid = %s and
                      a.src != 'SD';
            '''
            if pgsql.fetch_one(conn, sql, (record.bid,)):
                record.can_post = False
                record.reason = 'C 端已存在边框'

    proceed()


@desc()
def filter_records_by_bid(ctx: InnerContext, proceed):
    """
    过滤掉无效的 bid
    """
    children_map = group_records_by_parent_bid([x for x in ctx.records if x.can_post])

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        for record in tqdm(ctx.records):
            if not record.can_post:
                continue

            sql = f'''
                select click_pv from poi where bid = %s;
            '''
            row = conn.fetch_one(sql, (record.bid,))
            if not row:
                record.can_post = False
                record.reason = 'poi 不存在'
                continue

            click_pv, = row
            has_valid_pv_children = any(x for x in children_map[record.bid] if x.pv >= ctx.min_pv)
            if click_pv < ctx.min_pv and not has_valid_pv_children:
                record.can_post = False
                record.reason = f'poi 小于 {ctx.min_pv}'

    proceed()


@desc()
def fill_poi_properties(ctx: InnerContext, proceed):
    """
    填充 poi 属性信息
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        for record in tqdm(ctx.records):
            if not record.can_post:
                continue

            sql = f'''
                select name, std_tag, click_pv from poi where bid = %s;
            '''
            row = conn.fetch_one(sql, (record.child_bid,))
            if not row:
                record.can_post = False
                record.reason = 'poi 不存在'
                continue

            record.name, record.std_tag, record.pv = row
            if record.std_tag not in tags.ACCURATE_WITHOUT_SCENIC:
                record.can_post = False
                record.reason = '不满足精准 AOI 垂类要求'

    proceed()


@desc()
def filter_records_by_street(ctx: InnerContext, proceed):
    """
    根据和街区的空间关系过滤记录
    """
    min_street_count = 2

    # 同组内的 poi 必须跨越街区。
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        pending_remove_parent_names = []

        for parent_name, children in tqdm(ctx.children_pois.items(), total=len(ctx.children_pois)):
            street_ids = set()
            for child in children:
                sql = f'''
                    select face_id 
                    from street_region 
                    where st_contains(geom, st_geomfromtext(%s, 4326))
                    limit 1;
                '''
                row = conn.fetch_one(sql, (child.geom.wkt,))
                if not row:
                    continue

                face_id, = row
                street_ids.add(face_id)

            if len(street_ids) < min_street_count:
                pending_remove_parent_names.append(parent_name)

    for name in pending_remove_parent_names:
        del ctx.children_pois[name]

    proceed()


@desc()
def save_records(ctx: InnerContext, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / f'{PROVIDER_NAME}_records.tsv',
        [
            [
                x.prefix_name,
                x.bid,
                x.name,
                x.std_tag,
                x.pv,
                x.wkt,
            ]
            for x in ctx.records if x.can_post
        ]
    )

    proceed()


@desc()
def save_debug_records(ctx: InnerContext, proceed):
    """
    保存调试记录
    """
    tsv.write_tsv(
        ctx.work_dir / f'{PROVIDER_NAME}_records_debug.tsv',
        [
            [
                x.reason,
                x.src,
                x.prefix_name,
                x.bid,
                x.name,
                x.std_tag,
                x.pv,
                x.wkt,
            ]
            for x in ctx.records
        ]
    )

    proceed()


@desc()
def create_tickets(ctx: InnerContext, proceed):
    """
    生成作业工单
    """
    parent_bids = set()

    for record in ctx.records:
        if not record.can_post:
            continue

        if record.bid in parent_bids:
            continue

        parent_bids.add(record.bid)
        ctx.tickets.append(TicketRecord(
            bid=record.bid,
            name=record.name,
            std_tag=record.std_tag,
            pv=record.pv,
            src=PROVIDER_NAME,
            plan_type='37',
            memo='聚合院落补框',
            geom=shapely.wkt.loads(record.wkt),
        ))

    proceed()


def run(outer_ctx: Context):
    """
    运行
    """
    ctx = InnerContext(
        work_dir=outer_ctx.work_dir,
        mesh_ids=outer_ctx.mesh_ids,
        poi_min_distance=500e-5,
        min_pv=outer_ctx.min_pv,
        desired_bids=outer_ctx.desired_bids,
    )

    main_pipe = pipeline.Pipeline(
        # 需求 1
        load_parent_bids,
        filter_records_by_child_name,
        filter_records_by_child_position,
        filter_records_by_child_count,
        filter_records_by_online_aois,
        filter_records_by_bid,
        fill_poi_properties,

        # 需求 2
        # load_children_pois,
        # filter_pois_by_postfix,
        # filter_pois_by_name,
        # filter_pois_by_overlap_aois,
        # group_pois,

        # 需求 3
        # load_children_pois,
        # filter_pois_by_name,
        # filter_records_by_street,
        # group_pois,

        save_records,
        save_debug_records,
        create_tickets,
    )
    desc.attach(main_pipe)
    main_pipe(ctx)
    return ctx.tickets
