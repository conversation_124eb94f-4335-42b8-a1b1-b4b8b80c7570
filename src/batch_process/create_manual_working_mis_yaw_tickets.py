# !/usr/bin/env python3
"""
封装了创建 MIS 偏航人工作业工单的逻辑
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.auto_create_manual_working_tickets import can_update_aoi
from src.batch_process.batch_helper import batch_process
from src.batch_process.create_manual_working_tickets_helper import Context, TicketRecord
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql

PROVIDER_NAME = 'mis_yaw'
PROVIDER_VERSION = '1.0.0'
PROVIDER_PRIORITY = 1
DESIRED_STRATEGIES = [
    'competitor_diff',
    'multi_inner_road_group',
    'child_poi_out_of_aoi',
]

desc = pipeline.get_desc()


@dataclass
class InnerContext:
    """
    上下文
    """
    work_dir: Path
    min_pv: int
    tickets: list[TicketRecord] = field(default_factory=list)


def get_latest_quality_batch():
    """
    获取最新的质量清查批次
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = '''
            select batch 
            from strategy_result 
            where batch like '%quality-statistics%' 
            order by created_at 
            desc limit 1;
        '''
        row = pgsql.fetch_one(conn, sql)
        return None if not row else row[0]


@desc()
def create_tickets(ctx: InnerContext, proceed):
    """
    创建作业工单
    """
    bids = set()

    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = '''
            select strategy_name, case_id 
            from strategy_result 
            where batch = %s and 
                  result = 'fail';
        '''
        for strategy_name, bid in tqdm(pgsql.fetch_all(conn, sql, (get_latest_quality_batch(),))):
            if strategy_name not in DESIRED_STRATEGIES or bid in bids:
                continue

            ctx.tickets.append(TicketRecord(
                bid=bid,
                src=PROVIDER_NAME,
            ))

    proceed()


@desc()
def filter_tickets_by_yaw_rate(ctx: InnerContext, proceed):
    """
    使用 mis 偏航率过滤工单
    """
    mid_yaw_rate = 0.8

    with pgsql.get_connection(pgsql.TRAJECTORY_CONFIG) as conn:
        def process(ticket: TicketRecord):
            sql = '''select yaw_rate from poi_yaw_rate where bid = %s order by create_time desc limit 1;'''
            row = pgsql.fetch_one(conn, sql, (ticket.bid,))

            if not row:
                ticket.can_process = False
                ticket.reason = 'yaw_rate not found'
                return

            yaw_rate = float(row[0])
            if yaw_rate < mid_yaw_rate:
                ticket.can_process = False
                ticket.reason = f'yaw_rate={yaw_rate} < {mid_yaw_rate}'

        batch_process(ctx.tickets, process)

    proceed()


def get_aoi_level(aoi_conn, main_bid):
    """
    判断一个边框是否是指定的等级
    """
    sql = '''
        select a.aoi_level 
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s
        limit 1;
    '''
    row = pgsql.fetch_one(aoi_conn, sql, (main_bid,))
    return None if not row else row[0]


def fill_plan_properties_of_quality_ticket(aoi_conn, ticket: TicketRecord):
    """
    填充工单的计划属性
    """
    if ticket.std_tag in tags.ACCURATE_SCENIC:
        ticket.memo = '质量清查-旅游景点'
        ticket.plan_type = '304'
    else:
        aoi_level = get_aoi_level(aoi_conn, ticket.bid)
        if not aoi_level:
            ticket.can_process = False
            ticket.reason = 'aoi level not found'
        elif ticket.std_tag in tags.ONLY_BUILD_BORDER:
            if aoi_level == 1:
                ticket.memo = '质量清查-仅建边框-聚合院落'
                ticket.plan_type = '37'
            elif ticket.std_tag in tags.ACCURATE_PRIMARY_OTHER_TAGS:
                ticket.memo = '质量清查-仅建边框-重点垂类基础院落'
                ticket.plan_type = '301'
            elif ticket.std_tag in tags.ACCURATE_COMMON_TAGS:
                ticket.memo = '质量清查-仅建边框-普通垂类基础院落'
                ticket.plan_type = '302'
            else:
                ticket.can_process = False
                ticket.reason = 'invalid tag'
        elif ticket.std_tag in tags.SHOULD_CONNECT_GATE:
            if aoi_level == 1:
                ticket.memo = '质量清查-需关联大门-聚合院落'
                ticket.plan_type = '37'
            elif ticket.std_tag in tags.ACCURATE_PRIMARY_OTHER_TAGS:
                ticket.memo = '质量清查-需关联大门-重点垂类基础院落'
                ticket.plan_type = '301'
            elif ticket.std_tag in tags.ACCURATE_COMMON_TAGS:
                ticket.memo = '质量清查-需关联大门-普通垂类基础院落'
                ticket.plan_type = '302'
            else:
                ticket.can_process = False
                ticket.reason = 'invalid tag'
        else:
            ticket.can_process = False
            ticket.reason = 'invalid tag'


@desc()
def fill_properties(ctx: InnerContext, proceed):
    """
    填充工单信息
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        def process(ticket: TicketRecord):
            sql = '''select name, std_tag, click_pv from poi where bid = %s and click_pv >= 210;'''
            row = poi_conn.fetch_one(sql, (ticket.bid,))
            if not row:
                ticket.can_process = False
                ticket.reason = 'poi not found'
                return

            ticket.name, ticket.std_tag, ticket.pv = row
            if not can_update_aoi(ticket):
                ticket.can_process = False
                ticket.reason = 'ignored tag'
                return

            fill_plan_properties_of_quality_ticket(aoi_conn, ticket)

        batch_process(ctx.tickets, process)

    proceed()


def run(outer_ctx: Context):
    """
    运行
    """
    main_pipe = pipeline.Pipeline(
        create_tickets,
        fill_properties,
        filter_tickets_by_yaw_rate,
    )
    desc.attach(main_pipe)
    ctx = InnerContext(
        work_dir=outer_ctx.work_dir,
        min_pv=outer_ctx.min_pv,
    )
    main_pipe(ctx)

    return ctx.tickets
