# !/usr/bin/env python3
"""
封装了创建 POI 新增人工作业工单的逻辑
"""
from dataclasses import dataclass, field

import pymysql
import shapely.wkt
from shapely import Point
from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.auto_create_manual_working_tickets import exists_customer_aoi
from src.batch_process.batch_helper import batch_process
from src.batch_process.create_manual_working_tickets_helper import TicketRecord, Context
from src.tools import pgsql, pipeline
from src.tools.conf_tools import get_mysql_conf
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer

PROVIDER_NAME = 'poi_adding'
PROVIDER_VERSION = '1.0.0'
PROVIDER_PRIORITY = 1

desc = pipeline.get_desc()


@dataclass
class PoiAddingRecord:
    """
    POI 新增记录
    """
    bid: str
    name: str
    relation_bid: str
    std_tag: str
    pv: int
    geom: Point
    relation_tag: str = ''
    plan_type: str = ''
    memo: str = None
    reason: str = ''
    can_process: bool = True


@dataclass
class InnerContext:
    """
    上下文
    """
    min_pv: int
    poi_adding_records: list[PoiAddingRecord] = field(default_factory=list)
    tickets: list[TicketRecord] = field(default_factory=list)


def get_beeflow_connection():
    """
    获取 beeflow 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def can_ignore_relation(record: PoiAddingRecord):
    """
    判断一个 poi 新增记录在投产时是否可以忽略父子关系
    """

    def is_house(tag: str):
        return tag == '房地产;住宅区' or tag == '房地产'

    return is_house(record.std_tag) and is_house(record.relation_tag)


@desc()
def load_records(ctx: InnerContext, proceed):
    """
    加载 poi 新增记录
    """
    with (
        get_beeflow_connection() as beeflow_conn,
        beeflow_conn.cursor() as cursor,
        # pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        sql = '''
            select distinct bid
            from poi_realtime_intelligence
            where create_time >= date_sub(curdate(), interval 7 day) and
                  create_time < curdate();
        '''
        cursor.execute(sql)
        rows = cursor.fetchall()
        total = len(rows)
        none_count = 0
        invalid_pv_count = 0
        invalid_tag_count = 0

        for bid, in tqdm(rows):
            sql = '''select name, relation_bid, std_tag, click_pv, st_astext(geometry) from poi where bid = %s;'''
            poi = poi_conn.fetch_one(sql, (bid,))
            if not poi:
                none_count += 1
                continue

            name, relation_bid, std_tag, click_pv, wkt = poi

            if click_pv < ctx.min_pv:
                invalid_pv_count += 1
                continue

            if std_tag not in tags.ACCURATE_ALL:
                invalid_tag_count += 1
                continue

            ctx.poi_adding_records.append(PoiAddingRecord(
                bid=bid,
                name=name,
                relation_bid=relation_bid,
                std_tag=std_tag,
                pv=click_pv,
                geom=shapely.wkt.loads(wkt),
            ))

    print(f'total: {total}')
    print(f'none_count: {none_count}')
    print(f'invalid_pv_count: {invalid_pv_count}')
    print(f'invalid_tag_count: {invalid_tag_count}')
    print(f'valid_count: {len(ctx.poi_adding_records)}')
    proceed()


@desc()
def fill_plan_type(ctx: InnerContext, proceed):
    """
    为 poi 新增记录填充 plan_type
    """

    def process(record: PoiAddingRecord):
        if record.std_tag in tags.PLAN_TYPE_301:
            record.plan_type = '301'
        elif record.std_tag in tags.PLAN_TYPE_302:
            record.plan_type = '302'
        elif record.std_tag in tags.PLAN_TYPE_304:
            record.plan_type = '304'
        else:
            record.can_process = False
            record.reason = 'std_tag not in PLAN_TYPE'

    batch_process(ctx.poi_adding_records, process)
    proceed()


@desc()
def fill_relation_tag(ctx: InnerContext, proceed):
    """
    为 poi 新增记录填充 relation_tag
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        def process(record: PoiAddingRecord):
            sql = '''select std_tag from poi where bid = %s;'''
            row = pgsql.fetch_one(poi_conn, sql, (record.relation_bid,))
            if not row:
                return

            record.relation_tag, = row

        batch_process(ctx.poi_adding_records, process)

    proceed()


@desc()
def filter_records_by_online_aoi(ctx: InnerContext, proceed):
    """
    如果线上已经有符合要求的边框，则过滤。
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn:
        def process(record: PoiAddingRecord):
            if exists_customer_aoi(aoi_conn, record.bid):
                record.can_process = False
                record.reason = 'exists customer aoi'

        batch_process(ctx.poi_adding_records, process)

    proceed()


@desc()
def filter_records_by_relation(ctx: InnerContext, proceed):
    """
    借助父子关系过滤 poi 新增记录
    """

    def process(record: PoiAddingRecord):
        if can_ignore_relation(record):
            return

        if record.relation_bid != '0' and record.relation_bid != '':
            record.can_process = False
            record.reason = 'is child poi'

    batch_process(ctx.poi_adding_records, process)
    proceed()


@desc()
def create_tickets(ctx: InnerContext, proceed):
    """
    创建作业工单
    """

    def process(record: PoiAddingRecord):
        ctx.tickets.append(TicketRecord(
            bid=record.bid,
            src=PROVIDER_NAME,
            plan_type=record.plan_type,
            name=record.name,
            relation_bid=record.relation_bid,
            std_tag=record.std_tag,
            pv=record.pv,
            memo=record.memo,
            geom=shapely.wkt.loads(record.geom.wkt),
        ))

    batch_process(ctx.poi_adding_records, process)
    proceed()


def run(outer_ctx: Context):
    """
    运行
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_plan_type,
        fill_relation_tag,
        filter_records_by_online_aoi,
        filter_records_by_relation,
        create_tickets,
    )
    desc.attach(main_pipe)
    ctx = InnerContext(
        min_pv=outer_ctx.min_pv,
    )
    main_pipe(ctx)

    return ctx.tickets
