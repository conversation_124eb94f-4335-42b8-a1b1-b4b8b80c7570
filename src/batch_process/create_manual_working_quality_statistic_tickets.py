# !/usr/bin/env python3
"""
封装了创建质量清查人工作业工单的逻辑
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.auto_create_manual_working_tickets import can_update_aoi
from src.batch_process.create_manual_working_tickets_helper import TicketRecord, Context
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pgsql, pipeline, tsv

PROVIDER_NAME = 'test'  # 正式上线的话这里改成：quality_statistic
PROVIDER_VERSION = '1.0.0'
PROVIDER_PRIORITY = 0
MIN_FACTOR = 4
STRATEGY_FACTOR_MAP = {
    'dest_track': 4,
    'competitor_diff_v2': 4,
    'multi_inner_road_group': 1,
    'child_poi_out_of_aoi': 1,
}

desc = pipeline.get_desc()


@dataclass
class QualityRecord:
    """
    质量记录
    """
    bid: str
    strategy_name: str


@dataclass
class QualityStatisticRecord:
    """
    质量统计记录
    """
    bid: str
    strategy_names: set[str] = field(default_factory=set)
    total_factor: int = 0


@dataclass
class InnerContext:
    """
    上下文
    """
    work_dir: Path = None
    batch: str = ''
    quality_records: list[QualityRecord] = field(default_factory=list)
    quality_statistic_records: dict[str, QualityStatisticRecord] = field(default_factory=dict)
    tickets: list[TicketRecord] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


def is_desired_recall_count(record: QualityStatisticRecord, min_recall_count, max_recall_count):
    """
    判断单个质检统计记录是否满足召回量
    """
    recall_count = len(record.strategy_names)
    if recall_count < min_recall_count:
        return False
    if 0 < max_recall_count <= recall_count:
        return False
    if not (min_recall_count <= recall_count < max_recall_count):
        return False

    return True


def get_poi_properties(poi_conn, bid, desired_tags):
    """
    获取 poi 属性
    """
    sql = '''select name, std_tag, click_pv from poi where bid = %s;'''
    row = poi_conn.fetch_one(sql, (bid,))
    if not row:
        return None
    name, std_tag, click_pv = row
    if std_tag not in desired_tags:
        return None
    return name, std_tag, click_pv


def get_aoi_level(aoi_conn, main_bid):
    """
    判断一个边框是否是指定的等级
    """
    sql = '''
        select a.aoi_level 
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s
        limit 1;
    '''
    row = pgsql.fetch_one(aoi_conn, sql, (main_bid,))
    return None if not row else row[0]


def get_plan_type_of_quality_ticket(aoi_conn, bid):
    """
    获取质检工单需要下发的计划类型
    """
    aoi_level = get_aoi_level(aoi_conn, bid)
    if not aoi_level:
        return None

    if aoi_level == 2:
        return '302'
    if aoi_level == 1:
        return '37'

    return None


@desc()
def get_latest_quality_batch(ctx: InnerContext, proceed):
    """
    获取最新的质量清查批次
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = '''
            select batch 
            from strategy_result 
            where batch like 'quality-statistics%' 
            order by created_at 
            desc limit 1;
        '''
        row = pgsql.fetch_one(conn, sql)
        ctx.batch = None if not row else row[0]

    proceed()


@desc()
def load_quality_records(ctx: InnerContext, proceed):
    """
    加载质量清查清单
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = '''
            select strategy_name, case_id from strategy_result where batch = %s and result = 'fail';
        '''
        for strategy_name, bid in tqdm(pgsql.fetch_all(conn, sql, (ctx.batch,))):
            ctx.quality_records.append(QualityRecord(
                bid=bid,
                strategy_name=strategy_name,
            ))

    proceed()


@desc()
def create_quality_statistic_records(ctx: InnerContext, proceed):
    """
    生成质量清查统计清单
    """
    for quality_record in tqdm(ctx.quality_records):
        record = ctx.quality_statistic_records.get(quality_record.bid, QualityStatisticRecord(quality_record.bid))
        record.strategy_names.add(quality_record.strategy_name)
        ctx.quality_statistic_records[quality_record.bid] = record

    proceed()


def generate_quality_tickets_with_condition(ctx: InnerContext, recall_count_range, desired_tags, memo):
    """
    根据条件生成质量清查作业工单
    """
    min_recall_count, max_recall_count = recall_count_range

    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for bid, quality_statistic_record in tqdm(ctx.quality_statistic_records.items()):
            if not is_desired_recall_count(quality_statistic_record, min_recall_count, max_recall_count):
                continue
            poi_properties = get_poi_properties(poi_conn, bid, desired_tags)
            if not poi_properties:
                continue
            plan_type = get_plan_type_of_quality_ticket(aoi_conn, bid)
            if not plan_type:
                continue
            name, std_tag, click_pv = poi_properties
            if not can_update_aoi(std_tag):
                continue

            ctx.tickets.append(TicketRecord(
                bid=bid,
                name=name,
                std_tag=std_tag,
                pv=click_pv,
                src=PROVIDER_NAME,
                plan_type=plan_type,
                memo=memo,
            ))


@desc()
def create_tickets_by_count(ctx: InnerContext, proceed):
    """
    创建作业工单
    """
    generate_quality_tickets_with_condition(
        ctx, recall_count_range=(3, -1), desired_tags=tags.ONLY_BUILD_BORDER, memo='仅建边框')
    generate_quality_tickets_with_condition(
        ctx, recall_count_range=(3, -1), desired_tags=tags.SHOULD_CONNECT_GATE, memo='需建大门关联')

    # 暂时注释，等工艺通知。
    # generate_quality_tickets_with_condition(
    #     ctx, recall_count_range=(2, 3), desired_tags=tags.ONLY_BUILD_BORDER, memo='仅建边框')
    # generate_quality_tickets_with_condition(
    #     ctx, recall_count_range=(2, 3), desired_tags=tags.SHOULD_CONNECT_GATE, memo='需建大门关联')
    #
    # generate_quality_tickets_with_condition(
    #     ctx, recall_count_range=(1, 2), desired_tags=tags.ONLY_BUILD_BORDER, memo='仅建边框')
    # generate_quality_tickets_with_condition(
    #     ctx, recall_count_range=(1, 2), desired_tags=tags.SHOULD_CONNECT_GATE, memo='需建大门关联')

    proceed()


@desc()
def create_tickets_by_factor(ctx: InnerContext, proceed):
    """
    通过因子和创建作业工单
    """
    for bid, quality_statistic_record in tqdm(ctx.quality_statistic_records.items()):

        total_factor = 0
        for strategy_name in quality_statistic_record.strategy_names:
            total_factor += STRATEGY_FACTOR_MAP.get(strategy_name, 0)

        quality_statistic_record.total_factor = total_factor

    proceed()


@desc()
def save_tickets(ctx: InnerContext, proceed):
    """
    保存清单
    """
    output_items = []
    get_aoi_sql = '''
        select a.aoi_level, a.src
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s
        limit 1;
    '''
    get_poi_sql = '''
        select name, std_tag, click_pv from poi where bid = %s;
    '''

    with (
        PgsqlStabilizer(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for bid, quality_statistic_record in tqdm(ctx.quality_statistic_records.items()):
            row = aoi_conn.fetch_one(get_aoi_sql, (bid,))
            if row is None:
                continue

            aoi_level, src = row
            row = poi_conn.fetch_one(get_poi_sql, (bid,))
            if row is None:
                continue

            name, std_tag, click_pv = row

            output_items.append([
                bid,
                quality_statistic_record.total_factor,
                name,
                click_pv,
                std_tag,
                aoi_level,
                src,
                len(quality_statistic_record.strategy_names),
                ','.join(quality_statistic_record.strategy_names),
            ])

    tsv.write_tsv(ctx.work_dir / 'output.tsv', output_items)
    proceed()


def run(_: Context):
    """
    运行
    """
    main_pipe = create_pipeline('lib')
    desc.attach(main_pipe)
    ctx = InnerContext()
    main_pipe(ctx)

    return ctx.tickets


def create_pipeline(mode):
    """
    创建处理流水线
    """
    if mode == 'lib':
        return pipeline.Pipeline(
            get_latest_quality_batch,
            load_quality_records,
            create_quality_statistic_records,
            create_tickets_by_count,
        )
    elif mode == 'batch':
        return pipeline.Pipeline(
            load_quality_records,
            create_quality_statistic_records,
            create_tickets_by_factor,
            save_tickets,
        )

    raise ValueError(f'Unknown mode: {mode}')


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args.mode)
    desc.attach(main_pipe)
    ctx = InnerContext(
        batch=args.batch,
        work_dir=Path('cache/create_manual_working_quality_statistic_tickets'),
    )
    main_pipe(ctx)


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--batch',
        dest='batch',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        required=False,
        default='lib'
    )
    return parser.parse_args()


if __name__ == '__main__':
    main(parse_args())
