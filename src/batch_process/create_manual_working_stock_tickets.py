# !/usr/bin/env python3
"""
封装了创建存量边框人工作业工单的逻辑
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.auto_create_manual_working_tickets import can_add_basic_aoi
from src.batch_process.create_manual_working_tickets_helper import TicketRecord, Context
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pgsql, pipeline

PROVIDER_NAME = 'stock'
PROVIDER_VERSION = '1.0.0'
PROVIDER_PRIORITY = 2

desc = pipeline.get_desc()


@dataclass
class TicketBriefInfo:
    bid: str
    qb_id: str = ''


@dataclass
class InnerContext:
    """
    上下文
    """
    min_pv: int
    mesh_ids: list[str]
    common_infos: list[TicketBriefInfo] = field(default_factory=list)
    primary_infos: list[TicketBriefInfo] = field(default_factory=list)
    tickets: list[TicketRecord] = field(default_factory=list)
    poi_cache: dict[str, tuple] = field(default_factory=dict)


def exists_cluster_aoi(aoi_conn, bid):
    """
    判断是否存在聚合院落边框
    """
    sql = '''
        select a.face_id
        from blu_face a            
        inner join blu_face_poi b
        on a.face_id = b.face_id         
        where b.poi_bid = %s and
              a.aoi_level = 1 and
              a.src != 'SQ'
        limit 1;
    '''
    return pgsql.fetch_one(aoi_conn, sql, (bid,))


def is_accurate_aoi(aoi_conn, face_id):
    """
    判断一个边框是否是精准 AOI
    """
    sql = '''
        select * from blu_face_complete where face_id = %s and aoi_complete >= 3 limit 1;
    '''
    return pgsql.fetch_one(aoi_conn, sql, (face_id,))


def get_poi(ctx: InnerContext, poi_conn, bid):
    """
    获取 poi 信息
    """
    poi_cache = ctx.poi_cache.get(bid, None)
    if poi_cache is not None:
        return poi_cache

    sql = '''
        select bid, name, relation_bid, std_tag, click_pv, st_astext(geometry)
        from poi
        where bid = %s;
    '''
    row = poi_conn.fetch_one(sql, (bid,))
    ctx.poi_cache[bid] = row if row is not None else ('', '', '', '', 0, '')
    return ctx.poi_cache[bid]


def get_overlap_customer_bid(aoi_conn, bid, wkt):
    """
    获取压盖的 C 端边框
    """
    sql = '''
        select b.poi_bid
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where st_intersects(a.geom, st_geomfromtext(%s, 4326)) and
              a.src != 'SD' and
              a.src != 'SQ' and
              a.kind != '52' and
              a.aoi_level = 2 and
              b.poi_bid != %s;
    '''

    for overlap_bid, in pgsql.fetch_all(aoi_conn, sql, (wkt, bid)):
        return overlap_bid

    return None


def generate_common_basic_aoi_tickets(ctx: InnerContext, desired_tags, plan_type, memo=None):
    """
    生成普通优先级基础院落作业工单
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for info in tqdm(ctx.common_infos):
            _, name, relation_bid, std_tag, pv, wkt = get_poi(ctx, poi_conn, info.bid)

            if relation_bid != '0' and relation_bid != '':
                continue
            if std_tag not in desired_tags:
                continue

            ticket = TicketRecord(
                bid=info.bid,
                name=name,
                std_tag=std_tag,
                pv=pv,
                src=PROVIDER_NAME,
                plan_type=plan_type,
                memo=memo,
                geom=shapely.wkt.loads(wkt),
            )

            if not can_add_basic_aoi(aoi_conn, poi_conn, ticket):
                continue

            ctx.tickets.append(ticket)


def generate_sam_common_basic_aoi_tickets(ctx: InnerContext, plan_type, memo=None):
    """
    生成普通优先级山姆超市基础院落作业工单
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for info in tqdm(ctx.common_infos):
            _, name, relation_bid, std_tag, pv, wkt = get_poi(ctx, poi_conn, info.bid)

            if relation_bid != '0' and relation_bid != '':
                continue

            if std_tag != '购物;超市':
                continue

            if '山姆' not in name:
                continue

            ticket = TicketRecord(
                bid=info.bid,
                name=name,
                std_tag=std_tag,
                pv=pv,
                src=PROVIDER_NAME,
                plan_type=plan_type,
                memo=memo,
                geom=shapely.wkt.loads(wkt),
                ignore_tag=True,
            )

            if not can_add_basic_aoi(aoi_conn, poi_conn, ticket):
                continue

            ctx.tickets.append(ticket)


def generate_high_pv_common_basic_aoi_tickets(ctx: InnerContext, plan_type, memo=None):
    """
    生成普通优先级高 PV 基础院落作业工单
    """
    min_pv = 1000

    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for info in tqdm(ctx.common_infos):
            _, name, relation_bid, std_tag, pv, wkt = get_poi(ctx, poi_conn, info.bid)

            if relation_bid != '0' and relation_bid != '':
                continue

            if pv < min_pv:
                continue

            ticket = TicketRecord(
                bid=info.bid,
                name=name,
                std_tag=std_tag,
                pv=pv,
                src=PROVIDER_NAME,
                plan_type=plan_type,
                memo=memo,
                geom=shapely.wkt.loads(wkt),
                ignore_tag=True,
            )

            if not can_add_basic_aoi(aoi_conn, poi_conn, ticket):
                continue

            ctx.tickets.append(ticket)


def generate_tag_similar_common_basic_aoi_tickets(ctx: InnerContext, plan_type, memo=None):
    """
    生成普通优先级 TAG 相似基础院落作业工单
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for info in tqdm(ctx.common_infos):
            _, name, relation_bid, std_tag, pv, wkt = get_poi(ctx, poi_conn, info.bid)
            overlap_bid = get_overlap_customer_bid(aoi_conn, info.bid, wkt)
            if overlap_bid is None:
                continue

            _, _, _, overlap_std_tag, overlap_pv, _ = get_poi(ctx, poi_conn, overlap_bid)
            if std_tag == overlap_std_tag and int(pv) > int(overlap_pv):
                # 和压盖的 tag 一致，则下发作业无需过滤。
                ticket = TicketRecord(
                    bid=info.bid,
                    name=name,
                    std_tag=std_tag,
                    pv=pv,
                    src=PROVIDER_NAME,
                    plan_type=plan_type,
                    memo=memo,
                    geom=shapely.wkt.loads(wkt),
                    ignore_tag=True,
                )

                if not can_add_basic_aoi(aoi_conn, poi_conn, ticket):
                    continue

                ctx.tickets.append(ticket)


def generate_special_common_basic_aoi_tickets(ctx: InnerContext, plan_type, memo=None):
    """
    生成普通优先级特殊基础院落作业工单
    """
    generate_sam_common_basic_aoi_tickets(ctx, plan_type, memo)
    generate_high_pv_common_basic_aoi_tickets(ctx, plan_type, memo)
    generate_tag_similar_common_basic_aoi_tickets(ctx, plan_type, memo)


def generate_common_cluster_aoi_tickets(ctx: InnerContext, desired_tags, plan_type, memo=None):
    """
    生成普通优先级聚合院落作业工单
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for info in tqdm(ctx.common_infos):
            _, name, _, std_tag, pv, wkt = get_poi(ctx, poi_conn, info.bid)

            if std_tag not in desired_tags:
                continue
            record = exists_cluster_aoi(aoi_conn, info.bid)
            if not record:
                continue
            face_id, = record
            if is_accurate_aoi(aoi_conn, face_id):
                continue

            ctx.tickets.append(TicketRecord(
                bid=info.bid,
                qb_id=info.qb_id,
                name=name,
                std_tag=std_tag,
                pv=pv,
                src=PROVIDER_NAME,
                plan_type=plan_type,
                memo=memo,
                geom=shapely.wkt.loads(wkt),
            ))


def generate_primary_tickets(ctx: InnerContext, desired_tags, plan_type, memo=None):
    """
    生成高优先级作业工单
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for info in tqdm(ctx.primary_infos):
            _, name, _, std_tag, pv, wkt = get_poi(ctx, poi_conn, info.bid)
            if std_tag not in desired_tags:
                continue

            ctx.tickets.append(TicketRecord(
                bid=info.bid,
                qb_id=info.qb_id,
                name=name,
                std_tag=std_tag,
                pv=pv,
                src=PROVIDER_NAME,
                plan_type=plan_type,
                memo=memo,
                geom=shapely.wkt.loads(wkt),
                primary=True,
            ))


@desc()
def fill_common_bids(ctx: InnerContext, proceed):
    """
    填充普通优先级的 bid 集合
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        sql = '''select bid from poi where mesh_id = %s and click_pv >= %s;'''
        for mesh_id in tqdm(ctx.mesh_ids):
            for bid, in poi_conn.fetch_all(sql, (mesh_id, ctx.min_pv)):
                ctx.common_infos.append(TicketBriefInfo(bid=bid))

    proceed()


@desc()
def fill_primary_auto_b2c_bids(ctx: InnerContext, proceed):
    """
    填充高优先级的 bid 集合
    """
    # 例行化 B 转 C 的人工作业清单是目前的高优集合。
    ticket_dir = Path('/home/<USER>/chenjie/auto_business_to_customer')
    date = datetime.date.today().strftime('%Y%m%d')
    prefix = 'manual_working_tickets'
    ticket_files = ticket_dir.glob(f'{prefix}_{date}*.tsv')
    sql = '''select 1 from poi where bid = %s and click_pv >= %s;'''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for ticket_file in ticket_files:
            with open(ticket_file, 'r', encoding='utf-8') as f:
                for line in [x.rstrip('\n') for x in f.readlines()]:
                    bid, _, _, _ = tuple(line.split('\t'))
                    row = poi_conn.fetch_one(sql, (bid, ctx.min_pv))
                    if row is None:
                        continue
                    ctx.primary_infos.append(TicketBriefInfo(bid=bid))

    proceed()


@desc()
def fill_primary_fail_auto_task_bids(ctx: InnerContext, proceed):
    """
    自动任务失败的需要投放人工，所以需要收集一下失败任务关联的 bid 集合
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stabilizer:
        sql = '''
            select main_poi_bid, extra -> 'qb_id'
            from integration_qb
            where work_type = 1 and  -- 自动化任务
                  status = 5 and  -- 工单无法完成           
                  src = 4;  -- 需要制作边框
        '''
        for bid, qb_id in stabilizer.fetch_all(sql):
            ctx.primary_infos.append(TicketBriefInfo(bid=bid, qb_id=qb_id))

    proceed()


@desc()
def create_common_tickets(ctx: InnerContext, proceed):
    """
    生成普通优先级作业工单
    """
    generate_common_basic_aoi_tickets(
        ctx, desired_tags=tags.ACCURATE_COMMON_TAGS, plan_type='302', memo='普通垂类基础院落')
    generate_common_basic_aoi_tickets(
        ctx, desired_tags=tags.ACCURATE_PRIMARY_OTHER_TAGS, plan_type='301', memo='重点垂类基础院落')
    generate_common_basic_aoi_tickets(
        ctx, desired_tags=tags.ACCURATE_PRIMARY_TAGS, plan_type='304', memo='旅游景点')
    generate_special_common_basic_aoi_tickets(
        ctx, plan_type='301', memo='特殊类型基础院落')
    # generate_common_cluster_aoi_tickets(
    #     ctx, desired_tags=tags.ACCURATE_COMMON_TAGS, plan_type='37', memo='普通垂类聚合院落')
    # generate_common_cluster_aoi_tickets(
    #     ctx, desired_tags=tags.ACCURATE_PRIMARY_OTHER_TAGS_FOR_CLUSTER, plan_type='37', memo='重点垂类聚合院落')

    proceed()


@desc()
def create_primary_tickets(ctx: InnerContext, proceed):
    """
    生成高优先级作业工单
    """
    generate_primary_tickets(
        ctx, desired_tags=tags.ACCURATE_COMMON_TAGS, plan_type='302', memo='普通垂类基础院落')
    generate_primary_tickets(
        ctx, desired_tags=tags.ACCURATE_PRIMARY_OTHER_TAGS, plan_type='301', memo='重点垂类基础院落')
    generate_primary_tickets(
        ctx, desired_tags=tags.ACCURATE_PRIMARY_TAGS, plan_type='304', memo='旅游景点')

    proceed()


def run(outer_ctx: Context):
    """
    运行
    """
    main_pipe = pipeline.Pipeline(
        fill_common_bids,
        fill_primary_auto_b2c_bids,
        fill_primary_fail_auto_task_bids,
        create_common_tickets,
        create_primary_tickets,
    )
    desc.attach(main_pipe)
    ctx = InnerContext(
        min_pv=outer_ctx.min_pv,
        mesh_ids=outer_ctx.mesh_ids,
    )
    main_pipe(ctx)

    return ctx.tickets
