# !/usr/bin/env python3
"""
封装了创建存量边框人工作业工单的逻辑（v2）
和 v1 相比，v2 引入了新 tag 体系和导航 pv
"""
from dataclasses import dataclass, field

import shapely.wkt
from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.auto_create_manual_working_tickets import can_add_basic_aoi
from src.batch_process.create_manual_working_tickets_helper import TicketRecord, Context
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql

PROVIDER_NAME = 'stock_v2'
PROVIDER_VERSION = '1.0.0'
PROVIDER_PRIORITY = 3

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    工单记录
    """
    bid: str
    name: str
    relation_bid: str
    std_tag: str
    new_std_tag: str
    click_pv: int
    navi_pv: int
    wkt: str
    relation_std_tag: str = ''


@dataclass
class InnerContext:
    """
    上下文
    """
    mesh_ids: list[str]
    records: list[Record] = field(default_factory=list)
    tickets: list[TicketRecord] = field(default_factory=list)


@desc()
def load_records(ctx: InnerContext, proceed):
    """
    加载工单记录
    """
    sql = '''
        select bid, name, relation_bid, std_tag, new_std_tag, click_pv, navi_pv, st_astext(geom) 
        from aoi_accurate_list;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for bid, name, relation_bid, std_tag, new_std_tag, click_pv, navi_pv, wkt in tqdm(stab.fetch_all(sql)):
            ctx.records.append(Record(
                bid=bid,
                name=name,
                relation_bid=relation_bid,
                std_tag=std_tag,
                new_std_tag=new_std_tag,
                click_pv=click_pv,
                navi_pv=navi_pv,
                wkt=wkt,
            ))

    proceed()


@desc()
def fill_relation_std_tag(ctx: InnerContext, proceed):
    """
    填充关系标签
    """
    sql = '''
        select std_tag from poi where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in tqdm(ctx.records):
            if record.relation_bid == '0' or record.relation_bid == '':
                continue

            row = stab.fetch_one(sql, (record.relation_bid,))
            if row is None:
                continue

            record.relation_std_tag, = row

    proceed()


@desc()
def create_tickets(ctx: InnerContext, proceed):
    """
    生成作业工单
    """
    generate_common_basic_aoi_tickets(
        ctx, desired_tags=tags.ACCURATE_COMMON_TAGS, plan_type='302', memo='普通垂类基础院落')
    generate_common_basic_aoi_tickets(
        ctx, desired_tags=tags.ACCURATE_PRIMARY_OTHER_TAGS, plan_type='301', memo='重点垂类基础院落')
    generate_common_basic_aoi_tickets(
        ctx, desired_tags=tags.ACCURATE_PRIMARY_TAGS, plan_type='304', memo='旅游景点')

    proceed()


# -----------------------------------
# Helper functions
# -----------------------------------


def check_relation(record: Record):
    """
    检查父子关系是否满足下发条件
    """
    # 非子点可下发
    if record.relation_bid == '0' or record.relation_bid == '' or record.relation_std_tag == '':
        return True

    # 父点是购物中心或住宅区可下发
    return record.relation_std_tag == '购物;购物中心' or record.relation_std_tag == '房地产;住宅区'


def check_tag(record: Record, desired_tags):
    """
    检查 tag 是否满足下发条件
    """
    return record.std_tag in desired_tags or record.new_std_tag in desired_tags


def generate_common_basic_aoi_tickets(ctx: InnerContext, desired_tags, plan_type, memo=None):
    """
    生成普通优先级基础院落作业工单
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn,
    ):
        for record in tqdm(ctx.records):
            if not check_relation(record):
                continue
            if not check_tag(record, desired_tags):
                continue

            ticket = TicketRecord(
                bid=record.bid,
                name=record.name,
                std_tag=record.std_tag,
                relation_std_tag=record.relation_std_tag,
                pv=record.click_pv,
                src=PROVIDER_NAME,
                plan_type=plan_type,
                memo=memo,
                geom=shapely.wkt.loads(record.wkt),
            )

            if not can_add_basic_aoi(aoi_conn, poi_conn, ticket):
                continue

            ctx.tickets.append(ticket)


def run(outer_ctx: Context):
    """
    运行
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_relation_std_tag,
        create_tickets,
    )
    desc.attach(main_pipe)
    ctx = InnerContext(
        mesh_ids=outer_ctx.mesh_ids,
    )
    main_pipe(ctx)

    return ctx.tickets
