# !/usr/bin/env python3
"""
封装了创建人工作业工单的常用类和帮助方法
"""
from dataclasses import dataclass, field
from pathlib import Path
from typing import Callable

from shapely import Point

from src.batch_process import tags


@dataclass
class TicketRecord:
    """
    人工作业清单记录
    """
    bid: str
    src: str
    geom: Point = None
    plan_type: str = ''
    name: str = ''
    relation_bid: str = ''
    relation_std_tag: str = ''
    std_tag: str = ''
    pv: int = -1
    memo: str = None
    reason: str = ''
    can_process: bool = True
    primary: bool = False  # 非常重要的工单，这些工单不需要走全局过滤。
    intelligence_id: str = ''  # 作业工单的 ID
    qb_id: str = ''  # 作业工单对应的挖掘情报 ID
    ignore_tag: bool = False  # 是否忽略 tag（比如必须在精准建设清单内）


@dataclass
class TicketProviderDescription:
    """
    用于描述一个人工作业工单生成器信息
    """
    name: str
    version: str
    priority: int
    runner: Callable[[object], list[TicketRecord]]


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    bid_list_path: Path
    min_pv: int
    mesh_ids: list[str] = field(default_factory=list)
    ticket_records: dict[str, TicketRecord] = field(default_factory=dict)
    providers: list[TicketProviderDescription] = field(default_factory=list)
    desired_bids: set[str] = field(default_factory=set)

    def add_tickets(self, tickets: list[TicketRecord]):
        """
        添加人工作业工单
        """
        for ticket in tickets:
            if ticket.primary:
                # 如果是 primary 的工单，则不需要过滤，而且直接覆盖重复的工单
                self.ticket_records[ticket.bid] = ticket
                continue

            if ticket.bid in self.ticket_records:
                # 工单重复
                continue

            if not ticket.ignore_tag and ticket.std_tag not in tags.ACCURATE_ALL:
                # 如果不是精准建设清单内的工单，则直接跳过
                continue

            self.ticket_records[ticket.bid] = ticket

    def get_valid_tickets_count(self, memo=None, src=None, plan_type=None):
        """
        获取可下发的人工作业工单数量
        """
        desired_tickets = [x for x in self.ticket_records.values() if x.can_process]
        desired_tickets = (
            desired_tickets if not memo
            else [x for x in desired_tickets if x.memo == memo]
        )
        desired_tickets = (
            desired_tickets if not src
            else [x for x in desired_tickets if x.src == src]
        )
        desired_tickets = (
            desired_tickets if not plan_type
            else [x for x in desired_tickets if x.plan_type == plan_type]
        )
        return len(desired_tickets)
