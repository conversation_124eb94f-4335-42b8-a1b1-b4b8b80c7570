# !/usr/bin/env python3
"""
根据作业结论统计作业有效率
"""
import argparse
import json
from dataclasses import dataclass, field

import pymysql
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.tools import pipeline, notice_tool
from src.tools.conf_tools import get_mysql_conf

VALID_WORK_CONCLUSIONS = {
    31: [2, 4],  # AOI 清洗竞品对比
    33: [1, 2],  # AOI 主点不在 AOI 范围内
    35: [1],  # 特定垂类补充
    36: [1],  # 情报新增
    37: [1],  # 聚合院落新增
    301: [1],  # AOI 重要垂类清洗
    302: [1],  # AOI 常规清洗高功效
    303: [1],  # AOI 无定额
    304: [1],  # AOI 常规清洗低功效
}
MINOR_POI_CONFIG = {
    'host': '**************',
    'db': 'poi_online',
    'port': '8532',
    'user': 'poi_aoi_r',
    'pwd': 'poi_aoi_r',
}
GET_WPF_IDS_SQL = '''
    select wpf.wpf_prev_id
    from work_package_flow as wpf 
    
    left join task_record tr 
    on wpf.wpf_task_id = tr.task_id
    
    left join task_plan tp 
    on tr.plan_id = tp.plan_id
    
    where wpf.wpf_section = %s and
          wpf.wpf_delete_flag != %s and
          tp.plan_project_id = %s and
          tp.plan_status != %s and 
          tp.plan_id = %s;
'''
GET_WORK_INFO_SQL = f'''
    select plan_type, work_result 
    from unify_work_history 
    where wpf_id = %s;
'''

desc = pipeline.get_desc()


@dataclass
class WorkRecord:
    """
    作业记录
    """
    plan_type: int
    work_conclusion: int


@dataclass
class Context:
    """
    脚本执行上下文
    """
    plan_id: int
    effective_rate: float = 0
    wpf_ids: list[str] = field(default_factory=list)
    work_records: list[WorkRecord] = field(default_factory=list)


@desc()
def load_wpf_ids(ctx: Context, proceed):
    """
    加载工单集合
    """
    auto_merge_section = 50
    invalid_delete_flag = 1
    aoi_cleaning_project = 30002
    invalid_plan_status = 4

    host, port, user, pwd, database = get_mysql_conf('beeflow')
    with (
        pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4") as conn,
        conn.cursor() as cursor,
    ):
        args = (
            auto_merge_section,
            invalid_delete_flag,
            aoi_cleaning_project,
            invalid_plan_status,
            ctx.plan_id
        )
        cursor.execute(GET_WPF_IDS_SQL, args)
        ctx.wpf_ids = [x[0] for x in cursor.fetchall()]

    proceed()


@desc()
def load_work_records(ctx: Context, proceed):
    """
    加载作业记录
    """
    with (
        get_mysql_connection('beeflow') as conn,
        conn.cursor() as cursor,
    ):
        for wpf_id in tqdm(ctx.wpf_ids):
            cursor.execute(GET_WORK_INFO_SQL, (wpf_id,))
            for item in cursor.fetchall():
                plan_type = int(item[0])
                work_result = json.loads(item[1])
                work_conclusion = int(work_result['work_conclusion'])
                ctx.work_records.append(WorkRecord(plan_type, work_conclusion))

    proceed()


@desc()
def compute_effective_rate(ctx: Context, proceed):
    """
    计算有效率
    """
    total_count = len(ctx.work_records)
    if total_count == 0:
        ctx.effective_rate = 0
        proceed()
        return

    effective_count = len([x for x in ctx.work_records if x.work_conclusion in VALID_WORK_CONCLUSIONS[x.plan_type]])
    ctx.effective_rate = effective_count / len(ctx.work_records)

    proceed()


@desc()
def send_to_infoflow(ctx: Context, proceed):
    """
    如流通知
    """
    msg = f'计划 ID {ctx.plan_id}，有效率为 {ctx.effective_rate:.1%}。'
    print(msg)
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d83586c9c29feea30d4fbe3da7edc2669'
    )
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser('effective rate statistics')
    parser.add_argument(
        '--plan-id',
        dest='plan_id',
        help='计划 id',
        type=int,
        required=True,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_wpf_ids,
        load_work_records,
        compute_effective_rate,
        send_to_infoflow,
    )
    desc.attach(main_pipe)

    ctx = Context(
        plan_id=args.plan_id,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
