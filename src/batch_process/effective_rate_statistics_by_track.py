# !/usr/bin/env python3
"""
根据埋点统计作业有效率
"""
import argparse
import re
from dataclasses import dataclass, field
from pathlib import Path
from typing import Union

import shapely.wkt
from tqdm import tqdm

from src.batch_process.batch_helper import batch_process, get_geom_iou, get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, tsv, pgsql

GET_TASK_ID_SQL = '''
    select distinct task_id 
    from strategy_feature_list 
    where batch_id = %s;
'''
GET_WORK_ID_SQL = '''
    select distinct work_id  
    from unify_work_history 
    where task_id = %s;
'''
GET_WORK_INFO_SQL = '''
    select wpf_id, work_resource
    from unify_work_history 
    where work_id = %s
    order by created_at desc 
    limit 1;
'''
GET_RESUME_BY_BID_SQL = '''
    select face_id
    from blu_record
    where bid = %s;
'''
GET_CURRENT_RESUME_SQL = '''
    select id, src, aoi_level, st_astext(geom), bid
    from blu_record
    where face_id = %s and
          task_id = %s
    order by id desc
    limit 1;
'''
GET_ORIGINAL_RESUME_SQL = '''
    select id, src, aoi_level, st_astext(geom), bid
    from blu_record
    where face_id = %s and
          id < %s and
          task_id != %s
    order by id desc
    limit 1;
'''
GET_MODIFIED_TABLE_SQL = '''
    select distinct data_table, data_status
    from unify_work_relate_history
    where wpf_id = %s and
          work_id = %s;
'''
GET_STRATEGY_NAME_SQL = '''
    select distinct strategy_name
    from strategy_result
    where case_id = %s and
          batch = %s and
          result = 'fail';
'''
GET_STRATEGY_CASE_COUNT_SQL = '''
    select strategy_name, count(distinct case_id)
    from strategy_result
    where batch = %s and
          result = 'fail'
    group by strategy_name;
'''

desc = pipeline.get_desc()


@dataclass
class ResumeRecord:
    """
    履历记录
    """
    id: int
    face_id: str
    src: str
    aoi_level: int
    wkt: str
    main_bid: str
    iou: float = 0.0

    def is_difference_from(self, other):
        """
        判断两个履历记录是否不同
        """
        min_iou = 0.9
        self.iou = get_geom_iou(shapely.wkt.loads(self.wkt), shapely.wkt.loads(other.wkt))
        other.iou = self.iou

        return (
                self.aoi_level != other.aoi_level or
                self.main_bid != other.main_bid or
                self.iou < min_iou
        )


@dataclass
class Record:
    """
    有效率记录
    """
    task_id: str
    work_id: str
    wpf_id: str = ''
    bid: str = ''
    strategy_names: set[str] = field(default_factory=set)
    original_resume: Union[ResumeRecord, None] = None
    current_resume: Union[ResumeRecord, None] = None
    is_relation_changed: bool = False
    is_aoi_level_changed: bool = False
    is_aoi_rel_changed: bool = False
    is_aoi_wkt_changed: bool = False
    is_aoi_src_changed: bool = False
    can_process: bool = True
    reason: str = ''

    @property
    def is_resume_different(self):
        """
        判断前后两个履历是否不同
        """
        return (
                (self.original_resume.src == 'SD' and self.current_resume.src == 'CD') or
                self.original_resume.is_difference_from(self.current_resume)
        )

    @property
    def is_valid_ticket(self):
        """
        判断是否是有效记录
        """
        return self.can_process and (self.is_relation_changed or self.is_resume_different)


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    plan_batch: str
    plan_batch_list_path: Path
    strategy_batch: str
    strategy_weights: dict[str, float] = field(default_factory=dict)
    task_ids: set[int] = field(default_factory=set)
    records: list[Record] = field(default_factory=list)


@desc()
def load_task_ids(ctx: Context, proceed):
    """
    加载任务 id 集合
    """
    with (
        get_mysql_connection('beeflow') as conn,
        conn.cursor() as cursor,
    ):
        if ctx.plan_batch != '':
            cursor.execute(GET_TASK_ID_SQL, (ctx.plan_batch,))
            ctx.task_ids = set([row[0] for row in cursor.fetchall()])
        else:
            with open(ctx.plan_batch_list_path, encoding='utf-8') as f:
                for plan_batch in tqdm([line.strip('\n') for line in f]):
                    cursor.execute(GET_TASK_ID_SQL, (plan_batch,))
                    ctx.task_ids |= {row[0] for row in cursor.fetchall()}

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载任务记录
    """
    with (
        get_mysql_connection('beeflow') as conn,
        conn.cursor() as cursor,
    ):
        for task_id in tqdm(ctx.task_ids):
            cursor.execute(GET_WORK_ID_SQL, (task_id,))
            for work_id, in cursor.fetchall():
                ctx.records.append(Record(
                    task_id=str(task_id),
                    work_id=work_id,
                ))

    proceed()


@desc()
def fill_work_info(ctx: Context, proceed):
    """
    填充作业信息
    """
    with (
        get_mysql_connection('beeflow') as conn,
        conn.cursor() as cursor,
    ):
        def process(record: Record):
            cursor.execute(GET_WORK_INFO_SQL, (record.work_id,))
            row = cursor.fetchone()
            if row is None:
                record.can_process = False
                record.reason = 'work_id not found'
                return

            wpf_id, work_resource_str = row
            record.wpf_id = wpf_id

            match = re.search(r'"bd_poi":\{"bid":"(\d+)"', work_resource_str)
            if match:
                record.bid = match.group(1)
            else:
                raise Exception('no bid in work_resource_str')

        batch_process(ctx.records, process)

    proceed()


@desc()
def fill_is_relation_changed(ctx: Context, proceed):
    """
    填充关联关系的变化性
    """
    with (
        get_mysql_connection('beeflow') as conn,
        conn.cursor() as cursor,
    ):
        def process(record: Record):
            cursor.execute(GET_MODIFIED_TABLE_SQL, (record.wpf_id, record.work_id))
            for table_name, status in cursor.fetchall():
                if table_name == 'blu_aoi_rel':
                    record.is_relation_changed = True
                    break
                if table_name == 'blu_face' or table_name == 'blu_face_poi':
                    record.is_relation_changed = True
                    break

        batch_process(ctx.records, process)

    proceed()


@desc()
def fill_current_aoi_resume(ctx: Context, proceed):
    """
    填充当前履历
    """
    with PgsqlStabilizer(pgsql.AOI_RESUME_SLAVER_CONFIG) as resume_stabilizer:
        def process(record: Record):
            # 在履历库中寻找所有和 bid 相关联的 face id 集合
            related_face_ids = [x[0] for x in resume_stabilizer.fetch_all(GET_RESUME_BY_BID_SQL, (record.bid,))]

            # 再通过 face id 找到所有关联履历
            for face_id in related_face_ids:
                row = resume_stabilizer.fetch_one(GET_CURRENT_RESUME_SQL, (face_id, record.task_id))
                if row is None:
                    continue

                resume_id, src, aoi_level, wkt, bid = row
                record.current_resume = ResumeRecord(
                    id=resume_id,
                    face_id=face_id,
                    src=src,
                    aoi_level=aoi_level,
                    wkt=wkt,
                    main_bid=bid,
                )
                # 只需要一条履历
                break

            if record.current_resume is None:
                record.can_process = False
                record.reason = f'no current resume'

        batch_process(ctx.records, process)

    proceed()


@desc()
def fill_original_aoi_resume(ctx: Context, proceed):
    """
    填充原始履历
    """
    with PgsqlStabilizer(pgsql.AOI_RESUME_SLAVER_CONFIG) as resume_stabilizer:
        def process(record: Record):
            args = (
                record.current_resume.face_id,
                record.current_resume.id,
                record.task_id
            )
            row = resume_stabilizer.fetch_one(GET_ORIGINAL_RESUME_SQL, args)
            if row is None:
                record.can_process = False
                record.reason = f'no original resume'
                return

            resume_id, src, aoi_level, wkt, bid = row
            record.original_resume = ResumeRecord(
                id=resume_id,
                face_id=record.current_resume.face_id,
                src=src,
                aoi_level=aoi_level,
                wkt=wkt,
                main_bid=bid,
            )

        batch_process(ctx.records, process)

    proceed()


@desc()
def print_report(ctx: Context, proceed):
    """
    打印报告
    """
    effective_count = len([x for x in ctx.records if x.is_valid_ticket])
    effective_rate = effective_count / len(ctx.records)
    print(f'有效率为 {effective_rate:.1%}。')
    proceed()


@desc()
def fill_strategy_names(ctx: Context, proceed):
    """
    填充召回策略名称
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        def process(record: Record):
            args = (record.bid, ctx.strategy_batch)
            record.strategy_names = set([x[0] for x in poi_stabilizer.fetch_all(GET_STRATEGY_NAME_SQL, args)])

        batch_process(ctx.records, process, can_process=lambda x: x.is_valid_ticket)

    proceed()


@desc()
def fill_strategy_weights(ctx: Context, proceed):
    """
    填充召回策略权重
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        total_case_count_map = dict()
        valid_case_count_map = dict()

        for strategy_name, count in poi_stabilizer.fetch_all(GET_STRATEGY_CASE_COUNT_SQL, (ctx.strategy_batch,)):
            total_case_count_map[strategy_name] = count

        for strategy_name in total_case_count_map.keys():
            valid_count = len([x for x in ctx.records if strategy_name in x.strategy_names])
            valid_case_count_map[strategy_name] = valid_count
            ctx.strategy_weights[strategy_name] = valid_count / total_case_count_map[strategy_name] * 100

        for strategy_name, weight in sorted(ctx.strategy_weights.items(), key=lambda x: x[1], reverse=True):
            print(f'{strategy_name}:')
            print(f'total {total_case_count_map[strategy_name]}')
            print(f'valid {valid_case_count_map[strategy_name]}')
            print(f'ratio {valid_case_count_map[strategy_name] / total_case_count_map[strategy_name]: .1%}')
            print(f'weight {weight: .1f}')
            print('----------------------------------------------------------------------')

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.bid,
                x.task_id,
                x.wpf_id,
                x.work_id,
                x.original_resume.face_id if x.original_resume else '',
                x.is_valid_ticket,
                x.is_relation_changed,
                x.original_resume.src if x.original_resume else '',
                x.current_resume.src if x.current_resume else '',
                x.original_resume.aoi_level if x.original_resume else '',
                x.current_resume.aoi_level if x.current_resume else '',
                x.original_resume.wkt if x.original_resume else '',
                x.current_resume.wkt if x.current_resume else '',
                x.original_resume.iou if x.original_resume else 0,
                x.original_resume.main_bid if x.original_resume else '',
                x.current_resume.main_bid if x.current_resume else '',
            ]
            for x in ctx.records
        ]
    )
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--plan-batch',
        dest='plan_batch',
        type=str,
        required=False,
        default='',
    )
    parser.add_argument(
        '--plan-batch-list-path',
        dest='plan_batch_list_path',
        type=str,
        required=False,
        default='',
    )
    parser.add_argument(
        '--strategy-batch',
        dest='strategy_batch',
        type=str,
        required=False,
        default='quality-statistics-2024-q1-v2',
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_task_ids,
        load_records,
        fill_work_info,
        fill_is_relation_changed,
        fill_current_aoi_resume,
        fill_original_aoi_resume,
        print_report,
        fill_strategy_names,
        fill_strategy_weights,
        save_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/effective_rate_statistics_by_track'),
        plan_batch=args.plan_batch,
        plan_batch_list_path=Path(args.plan_batch_list_path),
        strategy_batch=args.strategy_batch,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
