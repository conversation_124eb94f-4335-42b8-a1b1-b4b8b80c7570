# !/usr/bin/env python3
"""
导出聚合院落下 POI 缺失情报
"""
import argparse
import datetime
import json
from dataclasses import dataclass, field
from pathlib import Path

import requests
import shapely.wkt
from retrying import retry
from shapely import Polygon, MultiPolygon
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.model.aoi_model import AoiModel
from src.tools import pipeline, pgsql, tsv, notice_tool

desc = pipeline.get_desc()

VALID_TAGS = [
    '房地产',
]
MIN_PV = 210
MIN_RECORD_COUNT = 1000


@dataclass
class BasicAoi:
    """
    表示一份基础院落信息
    """
    bid: str
    geom: BaseGeometry
    can_output: bool = True
    reason: str = ''


@dataclass
class Record:
    """
    表示清单中的一条记录
    """
    bid: str
    pv: int = 0
    aoi_id: str = ''
    geom: BaseGeometry = field(init=False)
    cropped_geom: BaseGeometry = field(init=False)
    built_aois: list[BasicAoi] = field(default_factory=list)
    missing_aois: list[BasicAoi] = field(default_factory=list)
    can_output: bool = True
    reason: str = ''

    def get_output_line(self):
        """
        获取输出行
        """
        bids = [x.bid for x in self.built_aois]

        if self.cropped_geom.is_empty:
            yield [
                self.bid,
                self.geom.wkt,
                ','.join(bids),
                self.pv,
            ]
        elif isinstance(self.cropped_geom, Polygon):
            yield [
                self.bid,
                self.cropped_geom.wkt,
                ','.join(bids),
                self.pv,
            ]
        elif isinstance(self.cropped_geom, MultiPolygon):
            for polygon in self.cropped_geom.geoms:
                yield [
                    self.bid,
                    polygon.wkt,
                    ','.join(bids),
                    self.pv,
                ]


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    bid_list_path: Path
    mode: str
    upload_file_url: str
    download_file_url: str
    remote_records_path: str = ''
    cluster_aois: list[Record] = field(default_factory=list)
    output_path: Path = field(init=False)
    send_path: Path = field(init=False)
    closed_ids: list[int] = field(default_factory=list)


def batch_process_aoi(aois, process, use_tqdm=True):
    """
    批量处理 AOI
    """
    for aoi in tqdm(aois) if use_tqdm else aois:
        if not aoi.can_output:
            continue

        process(aoi)


def load_records_by_master_lib(ctx: Context):
    """
    从主库加载记录
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn:
        sql = '''
            select b.poi_bid
            from blu_face a
            inner join blu_face_poi b
            on a.face_id = b.face_id
            where a.src != 'SD' and
                  a.aoi_level = 1 and
                  a.kind != '52'
        '''
        for bid, in pgsql.fetch_all(aoi_conn, sql):
            ctx.cluster_aois.append(Record(
                bid=bid,
            ))


@desc()
def load_records_by_file(ctx: Context, proceed):
    """
    从文件加载记录
    """
    with open(ctx.bid_list_path, 'r') as f:
        for bid in [x.strip() for x in f.readlines()]:
            ctx.cluster_aois.append(Record(
                bid=bid,
            ))

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    if ctx.mode == 'lib':
        load_records_by_master_lib(ctx)
    elif ctx.mode == 'file':
        load_records_by_file(ctx)

    proceed()


def get_history_bids():
    """
    获取历史的情报记录
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = '''
            select memo->'cluster_aoi_bid' from pending_build_poi where created_at > now() - interval '30 day';
        '''
        return set(x[0] for x in pgsql.fetch_all(conn, sql))


@desc()
def filter_records_by_history_bids(ctx: Context, proceed):
    """
    过滤掉历史记录
    """
    history_bids = get_history_bids()

    def process(cluster_aoi: Record):
        if cluster_aoi.bid in history_bids:
            cluster_aoi.can_output = False
            cluster_aoi.reason = 'in history'

    batch_process_aoi(ctx.cluster_aois, process)
    proceed()


@desc()
def filter_records_by_poi_validation(ctx: Context, proceed):
    """
    过滤掉无效的聚合院落边框
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        def process(cluster_aoi: Record):
            sql = '''
                select std_tag, click_pv from poi where bid = %s;
            '''
            row = pgsql.fetch_one(conn, sql, (cluster_aoi.bid,))
            if not row:
                cluster_aoi.can_output = False
                cluster_aoi.reason = 'poi not found'
                return

            std_tag, click_pv = row
            if not any([x for x in VALID_TAGS if x in std_tag]):
                cluster_aoi.can_output = False
                cluster_aoi.reason = f'invalid tag: {std_tag}'
                return

            if click_pv < MIN_PV:
                cluster_aoi.can_output = False
                cluster_aoi.reason = f'click pv < {MIN_PV}'
                return

            cluster_aoi.pv = click_pv

        batch_process_aoi(ctx.cluster_aois, process)

    proceed()


@desc()
def filter_records_by_aoi_validation(ctx: Context, proceed):
    """
    过滤掉不存在的聚合院落边框
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process(cluster_aoi: Record):
            sql = '''
                select a.face_id, st_astext(a.geom)
                from blu_face a
                inner join blu_face_poi b 
                on a.face_id = b.face_id
                where b.poi_bid = %s and
                      a.aoi_level = 1 and
                      a.src != 'SD' and
                      a.kind != '52'
                limit 1;
            '''
            row = pgsql.fetch_one(conn, sql, (cluster_aoi.bid,))
            if not row:
                cluster_aoi.can_output = False
                cluster_aoi.reason = 'aoi not found'
                return

            aoi_id, wkt = row
            cluster_aoi.aoi_id = aoi_id
            cluster_aoi.geom = shapely.wkt.loads(wkt)

        batch_process_aoi(ctx.cluster_aois, process)

    proceed()


@desc()
def filter_records_by_road(ctx: Context, proceed):
    """
    通过道路长度过滤聚合院落边框
    """
    min_road_length = 80e-5

    with AoiModel() as aoi_model:
        def process(cluster_aoi: Record):
            sql = '''
                select st_astext(geom) 
                from nav_link 
                where st_intersects(st_geomfromtext(%s, 4326), geom) and
                      kind < 8;
            '''

            total_length = 0

            for wkt, in pgsql.fetch_all(aoi_model.conn_road, sql, (cluster_aoi.geom.wkt,)):
                road_geom = shapely.wkt.loads(wkt)
                total_length += cluster_aoi.geom.intersection(road_geom).length

            if total_length < min_road_length:
                cluster_aoi.can_output = False
                cluster_aoi.reason = f'road length: {total_length:.2f}'

        batch_process_aoi(ctx.cluster_aois, process)

    proceed()


@desc()
def load_basic_aois(ctx: Context, proceed):
    """
    加载聚合院落下的基础院落
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process(cluster_aoi: Record):
            sql = '''
                select c.poi_bid, st_astext(b.geom) 
                from blu_aoi_rel a

                inner join blu_face b
                on a.face_id2 = b.face_id

                inner join blu_face_poi c
                on b.face_id = c.face_id

                where a.face_id1 = %s and
                      b.aoi_level = 2 and
                      b.src != 'SD' and
                      b.kind != '52';
            '''
            for bid, wkt in pgsql.fetch_all(conn, sql, (cluster_aoi.aoi_id,)):
                cluster_aoi.built_aois.append(BasicAoi(
                    bid=bid,
                    geom=shapely.wkt.loads(wkt),
                ))

        batch_process_aoi(ctx.cluster_aois, process)

    proceed()


@desc()
def filter_records_by_basic_aoi_validation(ctx: Context, proceed):
    """
    排除失效的基础院落
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        def process_basic_aoi(basic_aoi: BasicAoi):
            sql = '''
                select 1 from poi where bid = %s;
            '''
            if not pgsql.fetch_one(conn, sql, (basic_aoi.bid,)):
                basic_aoi.can_output = False
                basic_aoi.reason = 'child poi not found'

        batch_process_aoi(ctx.cluster_aois, lambda x: batch_process_aoi(x.built_aois, process_basic_aoi, False))

    proceed()


@desc()
def filter_records_by_basic_aoi_aggregate_area(ctx: Context, proceed):
    """
    通过基础院落总面积过滤聚合院落
    """
    min_child_area_ratio = 0.7

    def process_cluster_aoi(cluster_aoi: Record):
        child_total_area = sum(x.geom.area for x in cluster_aoi.built_aois if x.can_output)
        if child_total_area / cluster_aoi.geom.area >= min_child_area_ratio:
            cluster_aoi.can_output = False
            cluster_aoi.reason = 'basic aoi aggregate area is too large'

    batch_process_aoi(ctx.cluster_aois, process_cluster_aoi)
    proceed()


@desc()
def crop_cluster_aoi_by_basic_aoi(ctx: Context, proceed):
    """
    使用基础院落裁剪聚合院落边框
    """
    def process_cluster_aoi(cluster_aoi: Record):
        cluster_aoi.cropped_geom = cluster_aoi.geom

        try:
            for aoi in cluster_aoi.built_aois:
                if not aoi.can_output:
                    continue

                cluster_aoi.cropped_geom = cluster_aoi.cropped_geom.difference(aoi.geom)
        except Exception as e:
            print(e)
            cluster_aoi.can_output = False
            cluster_aoi.reason = 'crop_cluster_aois_by_basic_aois: error'

        cluster_aoi.cropped_geom = cluster_aoi.cropped_geom.buffer(0)

    batch_process_aoi(ctx.cluster_aois, process_cluster_aoi)
    proceed()


@desc()
def clear_slivers(ctx: Context, proceed):
    """
    清理裁剪后的碎面
    """
    radius = 30e-5

    def process(cluster_aoi: Record):
        if not any(cluster_aoi.built_aois):
            return

        cluster_aoi.cropped_geom = (
            cluster_aoi.cropped_geom
            .buffer(-radius, join_style=shapely.BufferJoinStyle.mitre)
            .buffer(0)
            .buffer(radius, join_style=shapely.BufferJoinStyle.mitre)
        )

    batch_process_aoi(ctx.cluster_aois, process)
    proceed()


@desc()
def find_poi_with_same_tag(ctx: Context, proceed):
    """
    查找 tag 相同的 poi
    """
    with (
        pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
    ):
        def process(cluster_aoi: Record):
            sql = '''
                select std_tag from poi where bid = %s;
            '''
            std_tag, = pgsql.fetch_one(poi_conn, sql, (cluster_aoi.bid,))
            sql = '''
                select bid
                from poi 
                where st_contains(st_geomfromtext(%s, 4326), geometry) and 
                      std_tag = %s;
            '''
            for bid, in pgsql.fetch_all(poi_conn, sql, (cluster_aoi.cropped_geom.wkt, std_tag)):
                sql = '''
                    select 1
                    from blu_face a
                    inner join blu_face_poi b
                    on a.face_id = b.face_id
                    where b.poi_bid = %s and
                          a.src != 'SD';
                '''
                if not any(pgsql.fetch_all(aoi_conn, sql, (bid,))):
                    cluster_aoi.can_output = False
                    cluster_aoi.reason = 'poi with same tag'
                    break

        batch_process_aoi(ctx.cluster_aois, process)

    proceed()


@desc()
def save_records_to_file(ctx: Context, proceed):
    """
    保存记录到文件
    """
    output_items = []
    batch_process_aoi(ctx.cluster_aois, lambda x: output_items.extend(x.get_output_line()))
    ctx.output_path = ctx.work_dir / f"output_{datetime.datetime.now().strftime('%Y%m%d')}.tsv"
    tsv.write_tsv(ctx.output_path, output_items)
    proceed()


@desc()
def save_records_to_lib(ctx: Context, proceed):
    """
    保存记录母库
    """
    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
    ):
        try:
            for cluster_aoi_bid, wkt, basic_aoi_bids, pv in tsv.read_tsv(ctx.output_path):
                memo = json.dumps({
                    'cluster_aoi_bid': cluster_aoi_bid,
                    'basic_aoi_bids': str(basic_aoi_bids).split(','),
                    'pv': pv,
                })
                sql = '''
                    insert into pending_build_poi (geom, memo)
                    values (st_geomfromtext(%s, 4326), %s);
                '''
                cursor.execute(sql, (wkt, memo))

            conn.commit()
        except Exception as e:
            print(e)
            conn.rollback()

    proceed()


@desc()
def try_pack_history_records(ctx: Context, proceed):
    """
    尝试打包历史记录
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = '''
            select id, memo->'cluster_aoi_bid', st_astext(geom) , memo->'pv'
            from pending_build_poi 
            where status = 0;
        '''
        rows = pgsql.fetch_all(conn, sql)
        if len(rows) < MIN_RECORD_COUNT:
            return

        send_data = []
        for row in rows:
            record_id, cluster_aoi_bid, wkt, pv = row
            ctx.closed_ids.append(record_id)
            send_data.append([cluster_aoi_bid, wkt, pv])

        ctx.send_path = ctx.work_dir / f"send_{datetime.datetime.now().strftime('%Y%m%d')}.tsv"
        tsv.write_tsv(ctx.send_path, send_data)

    proceed()


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(upload_file_url, local_file_path):
    """
    上传文件
    """
    files = {'file': open(local_file_path, 'r', encoding='utf-8')}
    file_uuid = requests.post(upload_file_url, files=files).text
    if len(file_uuid) != 32:
        raise Exception(f'upload file failed')

    return file_uuid


@desc()
def upload_online_records(ctx: Context, proceed):
    """
    上传需要推送的记录
    """
    file_uuid = upload_file(ctx.upload_file_url, ctx.send_path)
    ctx.remote_records_path = f'{ctx.download_file_url}&uuid={file_uuid}'
    print(ctx.remote_records_path)

    proceed()


# noinspection SpellCheckingInspection
@desc()
def send_to_infoflow(ctx: Context, proceed):
    """
    发送如流通知
    """
    notice_tool.send_hi(
        f'聚合院落下 POI 缺失情报: {ctx.remote_records_path}',
        atuserids=['zhangchenni', 'chenjie02_cd'],
        token='d83586c9c29feea30d4fbe3da7edc2669'
    )
    proceed()


@desc()
def closed_history_records(ctx: Context, proceed):
    """
    闭环历史记录
    """
    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
    ):
        try:
            for record_id in tqdm(ctx.closed_ids):
                sql = '''
                    update pending_build_poi set status = 1 where id = %s;
                '''
                cursor.execute(sql, (record_id,))

            conn.commit()
        except Exception as e:
            print(e)
            conn.rollback()

    proceed()


def parse_args():
    """
    解析参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        default='lib',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        filter_records_by_history_bids,
        filter_records_by_poi_validation,
        filter_records_by_aoi_validation,
        filter_records_by_road,
        load_basic_aois,
        filter_records_by_basic_aoi_validation,
        filter_records_by_basic_aoi_aggregate_area,
        crop_cluster_aoi_by_basic_aoi,
        clear_slivers,
        find_poi_with_same_tag,
        save_records_to_file,
        save_records_to_lib,
        try_pack_history_records,
        upload_online_records,
        send_to_infoflow,
        closed_history_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/export_missing_basic_aoi_in_cluster_aoi'),
        bid_list_path=Path(args.bid_list_path),
        mode=args.mode,
        upload_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei',
        download_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile',
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
