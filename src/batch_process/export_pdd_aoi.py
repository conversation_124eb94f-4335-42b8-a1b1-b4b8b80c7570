# !/usr/bin/env python3
"""
用于导出拼多多商单数据
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv

# noinspection SpellCheckingInspection
PDD_DB_CONFIG = {
    "host": "**************",
    "db": "pdd_sd",
    "port": "6432",
    "user": "pdd_sd_se_rw",
    "pwd": "iayydyva",
}

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    商单数据
    """
    face_id: str
    area: float
    perimeter: float
    aoi_level: int
    wkt: str
    poi_bid: str
    mesh_id: str
    kind: str = ''
    name_ch: str = ''
    name_ph: str = ''
    name_en: str = ''
    admin_id: str = ''
    city_name: str = ''
    poi_id: str = ''
    can_output: bool = True
    reason: str = ''

    def to_insert_data(self):
        """
        获取插入数据
        """
        return (
            self.face_id,
            self.kind,
            self.area,
            self.perimeter,
            self.name_ch,
            self.admin_id,
            self.aoi_level,
            self.city_name,
            self.poi_id,
            self.poi_bid,
            self.wkt,
            self.mesh_id,
        )


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    records: list[Record] = field(default_factory=list)
    mesh_ids: list[str] = field(default_factory=list)


@desc()
def load_mesh_ids(ctx: Context, proceed):
    """
    加载 mesh_id
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = '''
            select distinct mesh_id from blu_face;
        '''
        ctx.mesh_ids = [x[0] for x in pgsql.fetch_all(conn, sql)]

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载商单数据
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for mesh_id in tqdm(ctx.mesh_ids):
            sql = '''
                select a.face_id, a.area, a.perimeter, a.aoi_level, st_astext(a.geom), b.poi_bid
                from blu_face a
                inner join blu_face_poi b
                on a.face_id = b.face_id
                where a.mesh_id = %s and
                      a.src != 'SD' and
                      a.kind != '52';
            '''
            for face_id, area, perimeter, aoi_level, wkt, poi_bid in pgsql.fetch_all(conn, sql, (mesh_id,)):
                ctx.records.append(Record(
                    face_id=face_id,
                    area=area,
                    perimeter=perimeter,
                    aoi_level=aoi_level,
                    wkt=wkt,
                    poi_bid=poi_bid,
                    mesh_id=mesh_id
                ))

    proceed()


@desc()
def load_hongkong_records(ctx: Context, proceed):
    """
    加载香港商单数据
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for mesh_id in tqdm(ctx.mesh_ids):
            sql = '''
                select face_id, area, perimeter, aoi_level, st_astext(geom)
                from blu_face
                where mesh_id = %s and
                      city_name = '香港' and
                      src != 'SD' and
                      kind != '52';
            '''
            for face_id, area, perimeter, aoi_level, wkt in pgsql.fetch_all(conn, sql, (mesh_id,)):
                ctx.records.append(Record(
                    face_id=face_id,
                    area=area,
                    perimeter=perimeter,
                    aoi_level=aoi_level,
                    wkt=wkt,
                    poi_bid='',
                    mesh_id=mesh_id
                ))

    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 poi 属性
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        for record in tqdm(ctx.records):
            sql = '''
                select std_tag, name, city, mid, admin_code from poi where bid = %s;
            '''
            row = conn.fetch_one(sql, (record.poi_bid,))
            if not row:
                record.can_output = False
                record.reason = 'poi_not_found'
                continue

            record.kind, record.name_ch, record.city_name, record.poi_id, record.admin_id = row
            if '行政区划' in record.kind or '行政地标' in record.kind:
                record.can_output = False
                record.reason = 'poi_is_admin'

    proceed()


@desc()
def save_records_to_db(ctx: Context, proceed):
    """
    保存商单到数据库
    """
    with pgsql.get_connection(PDD_DB_CONFIG) as conn:
        try:
            for record in tqdm(ctx.records):
                if not record.can_output:
                    continue

                sql = '''
                    insert into blu_face(
                        face_id, kind, area, perimeter, name_ch, admin_id, aoi_level, 
                        city_name, poi_id, poi_bid, geom, mesh_id
                    )
                    values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, st_geomfromtext(%s, 4326), %s)
                '''
                pgsql.execute(conn, sql, record.to_insert_data())
            conn.commit()
        except Exception as e:
            conn.rollback()
            print(e)

    proceed()


@desc()
def save_records_to_file(ctx: Context, proceed):
    """
    保存商单到文件
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.tsv',
        [
            [
                x.face_id,
                x.kind,
                x.area,
                x.perimeter,
                x.name_ch,
                x.admin_id,
                x.aoi_level,
                x.city_name,
                x.poi_id,
                x.poi_bid,
                x.wkt,
            ]
            for x in ctx.records if x.can_output
        ]
    )

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_mesh_ids,
        load_records,
        # load_hongkong_records,
        fill_poi_properties,
        save_records_to_db,
        # save_records_to_file,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/export_pdd_aoi'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
