# !/usr/bin/env python3
"""
封装了 AOI 流式入库相关的帮助方法
"""
import uuid
from datetime import datetime
from typing import Literal

import shapely.wkt
from shapely import Point

from src.aoi_cleanup_strategy.mesh_tools import get_mesh_id
from src.tools.flow_process_data_helper import Result, FlowProcess
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer


def add_aoi_rel(
    back_stabilizer: PgsqlStabilizer,
    face_id1,
    face_id2,
    mesh_id,
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    新增 AOI 的层级关系
    """
    try:
        if __exists_aoi_rel(back_stabilizer, face_id1, face_id2):
            return Result.fail('层级关系已存在，请勿重复添加。')
    except Exception as e:
        return Result.fail(str(e))

    return FlowProcess(platform).add(list(__generate_add_aoi_rel_data(face_id1, face_id2, mesh_id))).execute()


def recover_aoi(
    back_stabilizer: PgsqlStabilizer,
    recover_data,
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    恢复指定的 AOI
    """
    try:
        if __exists_face(back_stabilizer, recover_data['face_id']):
            return Result.fail('AOI 已存在，无需恢复。')

        if __exists_related_face(back_stabilizer, recover_data['bid']):
            return Result.fail('指定 bid 已存在边框，无法恢复。')

        return FlowProcess(platform).add(list(__generate_recover_face_data(back_stabilizer, recover_data))).execute()
    except Exception as e:
        return Result.fail(str(e))


def add_aoi(
    back_stabilizer: PgsqlStabilizer,
    add_data,
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    新增 AOI
    """
    try:
        bid = add_data['bid']
        if bid is not None and bid != '' and __exists_related_face(back_stabilizer, bid):
            return Result.fail('指定 bid 已存在边框，无法新增。')
    except Exception as e:
        return Result.fail(str(e))

    return FlowProcess(platform).add([list(__generate_add_aoi_data(back_stabilizer, add_data))]).execute()


def delete_aoi(
    back_stabilizer: PgsqlStabilizer,
    face_id,
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    删除 AOI
    """
    try:
        all_links_of_face = __get_all_links_of_face(back_stabilizer, face_id)
        unshared_links = __get_unshared_links(back_stabilizer, all_links_of_face)
        all_nodes_of_links = __get_all_nodes_of_links(back_stabilizer, unshared_links)
        unshared_nodes = __get_unshared_nodes(back_stabilizer, all_nodes_of_links)

        data_list = []
        data_list.extend(__generate_delete_blu_face_data(face_id))
        data_list.extend(__generate_delete_blu_face_topo_data(back_stabilizer, face_id))
        data_list.extend(__generate_delete_blu_face_poi_data(back_stabilizer, face_id))
        data_list.extend(__generate_delete_blu_link_data(back_stabilizer, unshared_links))
        data_list.extend(__generate_delete_blu_node_data(unshared_nodes))
        aoi_rel_ids = __get_all_aoi_rels_of_face(back_stabilizer, face_id)
        data_list.extend(__generate_delete_aoi_rel_data(aoi_rel_ids))

        # 出入口相关的会有其它例行化脚本删除，这里不做处理。
        return FlowProcess(platform).delete(data_list).execute()
    except Exception as e:
        return Result.fail(str(e))


def delete_aoi_rel(
    back_stabilizer: PgsqlStabilizer,
    face_id1,
    face_id2,
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    删除 face_id1 和 face_id2 之间的层级关系
    """
    try:
        aoi_rel_ids = __get_aoi_rels(back_stabilizer, face_id1, face_id2)
        data_list = __generate_delete_aoi_rel_data(aoi_rel_ids)

        return FlowProcess(platform).delete(data_list).execute()
    except Exception as e:
        return Result.fail(str(e))


def delete_main_poi_of_aoi(
    back_stabilizer: PgsqlStabilizer,
    face_id, src='SD',
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    删除 AOI 的主点
    """
    try:
        delete_data_list = __generate_delete_blu_face_poi_data(back_stabilizer, face_id)
        update_data_list = __generate_update_src_of_face_data(face_id, src)

        return FlowProcess(platform).delete(delete_data_list).update(update_data_list).execute()
    except Exception as e:
        return Result.fail(str(e))


def update_geom_of_aoi(
    back_stabilizer: PgsqlStabilizer,
    face_id,
    wkt,
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    更新 AOI 的 geom
    """
    try:
        all_links_of_face = __get_all_links_of_face(back_stabilizer, face_id)
        if len(all_links_of_face) == 0:
            return Result.fail('face 未关联 link')

        if len(__get_all_faces_of_links(back_stabilizer, all_links_of_face)) > 1:
            return Result.fail('存在共边')

        all_nodes_set = __get_all_nodes_of_links(back_stabilizer, all_links_of_face)
        if len(all_nodes_set) == 0:
            return Result.fail('link 未关联 node')

        all_links_set = __get_all_links_of_nodes(back_stabilizer, all_nodes_set)
        all_faces = __get_all_faces_of_links(back_stabilizer, all_links_set)
        if len(all_faces) > 1:
            return Result.fail('存在共点')

        all_links = list(all_links_set)
        all_nodes = list(all_nodes_set)

        # 构造删除请求数据
        delete_data_list = []
        delete_data_list.extend(__generate_delete_blu_link_data(back_stabilizer, all_links[1:]))
        delete_data_list.extend(__generate_delete_blu_node_data(all_nodes[1:]))

        # 构造更新请求数据
        update_data_list = []
        link_id = all_links[0]
        node_id = all_nodes[0]
        update_data_list.extend(__generate_update_node_of_link_data(link_id, node_id, node_id))
        update_data_list.extend(__generate_update_geom_of_face_data(back_stabilizer, face_id, wkt))
        aoi_geom = shapely.wkt.loads(wkt)
        link_geom = aoi_geom.boundary
        update_data_list.extend(__generate_update_geom_of_link_data(back_stabilizer, link_id, link_geom.wkt))
        update_data_list.extend(__generate_update_geom_of_node_data(node_id, Point(link_geom.coords[0]).wkt))

        return FlowProcess(platform).delete(delete_data_list).update(update_data_list).execute()
    except Exception as e:
        return Result.fail(str(e))


def update_main_poi_of_aoi(
    back_stabilizer: PgsqlStabilizer,
    face_id,
    src,
    bid,
    mid,
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    更新 AOI 的主点
    """
    try:
        blu_face_poi_ids = __get_all_face_poi_of_face(back_stabilizer, face_id)
    except Exception as e:
        return Result.fail(str(e))

    data_list = []
    data_list.extend(__generate_update_face_poi_data(blu_face_poi_ids, bid, mid))
    data_list.extend(__generate_update_src_of_face_data(face_id, src))

    return FlowProcess(platform).update(data_list).execute()


def update_src_of_aoi(
    back_stabilizer: PgsqlStabilizer,
    face_id,
    src,
    bid='',
    mid='',
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    更新 AOI 的 src
    """
    data_list = []
    data_list.extend(__generate_update_src_of_face_data(face_id, src))

    if bid != '' and mid != '':
        blu_face_poi_ids = __get_all_face_poi_of_face(back_stabilizer, face_id)
        data_list.extend(__generate_update_face_poi_data(blu_face_poi_ids, bid, mid))

    return FlowProcess(platform).update(data_list).execute()


def update_level_of_aoi(
    face_id,
    level,
    platform: Literal['formal', 'practice'] = 'formal'
):
    """
    更新 AOI 的层级
    """
    return FlowProcess(platform).update(list(__generate_update_aoi_level_of_face_data(face_id, level))).execute()


# ------------------------------------------------------------------------------------------------------------------- #
#                                                 HELP METHODS                                                        #
# ------------------------------------------------------------------------------------------------------------------- #
def __generate_add_aoi_rel_data(face_id1, face_id2, mesh_id):
    """
    构造用于新增 blu_aoi_rel 的请求数据
    """
    yield {
        'table_name': 'blu_aoi_rel',
        'face_id1': face_id1,
        'face_id2': face_id2,
        'rel_type': 1,  # 这里的值参考背景规格文档
        'mesh_id': mesh_id,
    }


def __generate_add_aoi_data(back_stabilizer, add_data):
    """
    构造用于新增 blu_aoi 的请求数据
    """
    kind = add_data.get('kind', '38')
    src = add_data.get('src', 'SD')
    name = add_data.get('name', '')
    memo = add_data.get('memo', '')

    city = add_data['city']
    aoi_level = add_data['aoi_level']
    aoi_wkt = add_data['wkt']
    bid = add_data['bid']
    mid = add_data['mid']

    aoi_geom = shapely.wkt.loads(aoi_wkt)
    link_geom = aoi_geom.boundary
    node_geom = Point(link_geom.coords[0])
    face_id = uuid.uuid4().hex
    link_id = uuid.uuid4().hex
    node_id = uuid.uuid4().hex
    mesh_id = str(get_mesh_id(node_geom.x, node_geom.y))

    yield {
        'table_name': 'blu_node',
        'node_id': node_id,
        'form': 0,
        'geom': node_geom.wkt,
        'mesh_id': mesh_id,
        'adj_meshid': '',
        'adj_nid': '',
        'edit_flag': 0,
        'memo': '',
    }

    yield {
        'table_name': 'blu_link',
        'link_id': link_id,
        's_nid': node_id,
        'e_nid': node_id,
        'kind': '0',
        'mesh_id': mesh_id,
        'geom': link_geom.wkt,
        'length': __get_length(back_stabilizer, link_geom.wkt),
        'edit_flag': 0,
        'memo': '',
    }

    yield {
        'table_name': 'blu_face',
        'face_id': face_id,
        'geom': aoi_geom.wkt,
        'kind': kind,
        'mesh_id': mesh_id,
        'area': __get_area(back_stabilizer, aoi_geom.wkt),
        'perimeter': __get_perimeter(back_stabilizer, aoi_geom.wkt),
        'name_ch': name,
        'name_ph': '',
        'name_en': '',
        'name_po': '',
        'admin_id': '',
        'edit_flag': 0,
        'feature_id': face_id,
        'src': src,
        'aoi_level': aoi_level,
        'aoi_level_source': 2,
        'scene_id': '',
        'display_flag': 0,
        'label_flag': 0,
        'access_complete': 0,
        'proj_flag': 0,
        'memo': memo,
        'extra_field1': '',
        'extra_field2': '',
        'city_name': city,
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    }

    yield {
        'table_name': 'blu_face_topo',
        'id': uuid.uuid4().hex,
        'face_id': face_id,
        'link_id': link_id,
        'mesh_id': mesh_id,
        'memo': '',
    }

    yield {
        'table_name': 'blu_face_poi',
        'id': uuid.uuid4().hex,
        'face_id': face_id,
        'poi_id': mid,
        'mesh_id': mesh_id,
        'poi_bid': bid,
        'memo': '',
    }


def __generate_recover_face_data(back_stabilizer: PgsqlStabilizer, recover_data):
    """
    构造用于恢复 blu_face 的请求数据。
    """
    aoi_wkt = recover_data['wkt']
    face_id = recover_data['face_id']

    yield {
        'table_name': 'blu_face',
        'face_id': face_id,
        'geom': recover_data['wkt'],
        'kind': recover_data['kind'],
        'mesh_id': recover_data['mesh_id'],
        'area': __get_area(back_stabilizer, aoi_wkt),
        'perimeter': __get_perimeter(back_stabilizer, aoi_wkt),
        'name_ch': recover_data['name_ch'],
        'name_ph': '',
        'name_en': '',
        'name_po': '',
        'admin_id': '',
        'edit_flag': 0,
        'feature_id': recover_data['face_id'],
        'src': recover_data['src'],
        'aoi_level': recover_data['aoi_level'],
        'aoi_level_source': recover_data['aoi_level_source'],
        'scene_id': '0',
        'display_flag': 0,
        'label_flag': recover_data['label_flag'],
        'access_complete': recover_data['access_complete'],
        'proj_flag': recover_data['proj_flag'],
        'memo': '',
        'extra_field1': '',
        'extra_field2': '',
        'city_name': recover_data['city_name'],
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    }

    link_geom = shapely.wkt.loads(aoi_wkt).boundary
    link_wkt = link_geom.wkt
    link_id = uuid.uuid4().hex
    node_id = uuid.uuid4().hex

    yield {
        'table_name': 'blu_link',
        'link_id': link_id,
        's_nid': node_id,
        'e_nid': node_id,
        'kind': '0',
        'mesh_id': recover_data['mesh_id'],
        'geom': link_wkt,
        'length': __get_length(back_stabilizer, link_wkt),
        'edit_flag': 0,
        'memo': '',
    }

    yield {
        'table_name': 'blu_face_topo',
        'id': uuid.uuid4().hex,
        'face_id': face_id,
        'link_id': link_id,
        'mesh_id': recover_data['mesh_id'],
        'memo': '',
    }

    yield {
        'table_name': 'blu_node',
        'node_id': node_id,
        'form': 0,
        'geom': Point(link_geom.coords[0]).wkt,
        'mesh_id': recover_data['mesh_id'],
        'adj_meshid': '',
        'adj_nid': '',
        'edit_flag': 0,
        'memo': '',
    }

    yield {
        'table_name': 'blu_face_poi',
        'id': uuid.uuid4().hex,
        'face_id': face_id,
        'poi_id': recover_data['mid'],
        'poi_bid': recover_data['bid'],
        'mesh_id': recover_data['mesh_id'],
        'memo': '',
    }


def __generate_delete_blu_face_data(face_id):
    """
    构造用于删除 blu_face 的请求数据
    """
    yield {
        'table_name': 'blu_face',
        'face_id': face_id,
    }


def __generate_delete_blu_face_poi_data(back_stabilizer: PgsqlStabilizer, face_id):
    """
    构造用于删除 blu_face_poi 的请求数据
    """
    blu_face_poi_ids = __get_all_face_poi_of_face(back_stabilizer, face_id)
    for blu_face_poi_id in blu_face_poi_ids:
        yield {
            'table_name': 'blu_face_poi',
            'id': blu_face_poi_id,
        }


def __generate_delete_blu_face_topo_data(back_stabilizer: PgsqlStabilizer, face_id):
    """
    构造用于删除 blu_face_topo 的请求数据
    """
    blu_face_topo_ids = __get_all_face_topo_of_face(back_stabilizer, face_id)
    for blu_face_topo_id in blu_face_topo_ids:
        yield {
            'table_name': 'blu_face_topo',
            'id': blu_face_topo_id,
        }


def __generate_delete_blu_link_data(back_stabilizer: PgsqlStabilizer, link_ids):
    """
    构造用于删除 blu_link 的请求数据
    """
    face_topo_ids = __get_all_face_topo_of_links(back_stabilizer, link_ids)
    for face_topo_id in face_topo_ids:
        yield {
            'table_name': 'blu_face_topo',
            'id': face_topo_id,
        }

    for link_id in link_ids:
        yield {
            'table_name': 'blu_link',
            'link_id': link_id,
        }


def __generate_delete_blu_node_data(node_ids):
    """
    构造用于删除 blu_node 的请求数据
    """
    for node_id in node_ids:
        yield {
            'table_name': 'blu_node',
            'node_id': node_id,
        }


def __generate_delete_aoi_rel_data(aoi_rel_ids):
    """
    构造用于删除 blu_aoi_rel 的请求数据
    """
    for aoi_rel_id in aoi_rel_ids:
        yield {
            'table_name': 'blu_aoi_rel',
            'aoi_rel_id': aoi_rel_id,
        }


def __generate_update_face_poi_data(face_poi_ids, bid, mid):
    """
    构造用于更新 blu_face_poi 的请求数据
    """
    # 理论上边框只能有一个主点，如果有多个主点，则全部更新。
    for face_poi_id in face_poi_ids:
        yield {
            'table_name': 'blu_face_poi',
            'id': face_poi_id,
            'poi_bid': bid,
            'poi_id': mid,
        }


def __generate_update_src_of_face_data(face_id, src):
    """
    构造用于更新 blu_face.src 的请求数据
    """
    yield {
        'table_name': 'blu_face',
        'face_id': face_id,
        'src': src,
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    }


def __generate_update_aoi_level_of_face_data(face_id, level):
    """
    构造用于更新 blu_face.aoi_level 的请求数据
    """
    yield {
        'table_name': 'blu_face',
        'face_id': face_id,
        'aoi_level': level,
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    }


def __generate_update_geom_of_face_data(back_stabilizer: PgsqlStabilizer, face_id, wkt):
    """
    构造用于更新 blu_face.geom 的请求数据
    """
    yield {
        'table_name': 'blu_face',
        'face_id': face_id,
        'geom': wkt,
        'perimeter': __get_perimeter(back_stabilizer, wkt),
        'area': __get_area(back_stabilizer, wkt),
        'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    }


def __generate_update_geom_of_link_data(back_stabilizer: PgsqlStabilizer, link_id, wkt):
    """
    构造用于更新 blu_link.geom 的请求数据
    """
    yield {
        'table_name': 'blu_link',
        'link_id': link_id,
        'geom': wkt,
        'length': __get_length(back_stabilizer, wkt)
    }


def __generate_update_geom_of_node_data(node_id, wkt):
    """
    构造用于更新 blu_node.geom 的请求数据
    """
    yield {
        'table_name': 'blu_node',
        'node_id': node_id,
        'geom': wkt,
    }


def __generate_update_node_of_link_data(link_id, start_node_id, end_node_id):
    """
    构造用于更新 blu_link.s_nid 和 blu_link.e_nid 的请求数据
    """
    yield {
        'table_name': 'blu_link',
        'link_id': link_id,
        's_nid': start_node_id,
        'e_nid': end_node_id,
    }


def __get_all_face_poi_of_face(back_stabilizer: PgsqlStabilizer, face_id):
    """
    获取 face 关联的所有 blu_face_poi
    """
    sql = '''
        select id from blu_face_poi where face_id = %s;
    '''
    return [x[0] for x in back_stabilizer.fetch_all(sql, (face_id,))]


def __get_all_face_topo_of_face(back_stabilizer: PgsqlStabilizer, face_id):
    """
    获取 face 关联的所有 blu_face_topo
    """
    sql = '''
        select id from blu_face_topo where face_id = %s;
    '''
    return [x[0] for x in back_stabilizer.fetch_all(sql, (face_id,))]


def __get_all_face_topo_of_links(back_stabilizer: PgsqlStabilizer, link_ids):
    """
    获取 link 关联的所有 blu_face_topo
    """
    all_face_topo_ids = set()
    sql = '''
        select id from blu_face_topo where link_id = %s;
    '''
    for link_id in link_ids:
        for face_topo_id, in back_stabilizer.fetch_all(sql, (link_id,)):
            all_face_topo_ids.add(face_topo_id)

    return all_face_topo_ids


def __get_all_aoi_rels_of_face(back_stabilizer: PgsqlStabilizer, face_id):
    """
    获取 face 关联的所有 aoi_rel
    """
    sql = '''
        select aoi_rel_id 
        from blu_aoi_rel
        where face_id1 = %s or
              face_id2 = %s;
    '''
    return set(aoi_rel_id for aoi_rel_id, in back_stabilizer.fetch_all(sql, (face_id, face_id)))


def __get_aoi_rels(back_stabilizer: PgsqlStabilizer, face_id1, face_id2):
    """
    获取 face_id1 和 face_id2 的关联关系
    """
    sql = '''
        select aoi_rel_id 
        from blu_aoi_rel
        where face_id1 = %s and
              face_id2 = %s;
    '''
    return set(aoi_rel_id for aoi_rel_id, in back_stabilizer.fetch_all(sql, (face_id1, face_id2)))


def __get_all_links_of_face(back_stabilizer: PgsqlStabilizer, face_id):
    """
    获取 face 关联的所有 link
    """
    sql = '''
        select link_id from blu_face_topo where face_id = %s;
    '''
    return set(link_id for link_id, in back_stabilizer.fetch_all(sql, (face_id,)))


def __get_all_links_of_nodes(back_stabilizer: PgsqlStabilizer, node_ids):
    """
    获取 node 关联的所有 link
    """
    all_link_ids = set()
    sql = '''
        select distinct link_id from blu_link where s_nid = %s or e_nid = %s;
    '''
    for node_id in node_ids:
        for link_id, in back_stabilizer.fetch_all(sql, (node_id, node_id)):
            all_link_ids.add(link_id)

    return all_link_ids


def __get_all_faces_of_links(back_stabilizer: PgsqlStabilizer, link_ids):
    """
    获取 link 关联的所有 face
    """
    sql = '''
        select distinct face_id from blu_face_topo where link_id = %s;
    '''

    all_face_ids = set()
    for link_id in link_ids:
        for face_id, in back_stabilizer.fetch_all(sql, (link_id,)):
            all_face_ids.add(face_id)

    return all_face_ids


def __get_all_nodes_of_links(back_stabilizer: PgsqlStabilizer, link_ids):
    """
    获取 link 关联的所有 node
    """
    sql = '''
        select s_nid, e_nid from blu_link where link_id = %s;
    '''
    all_node_ids = set()
    for link_id in link_ids:
        for s_nid, e_nid in back_stabilizer.fetch_all(sql, (link_id,)):
            all_node_ids.add(s_nid)
            all_node_ids.add(e_nid)

    return all_node_ids


def __get_unshared_links(back_stabilizer: PgsqlStabilizer, link_ids):
    """
    获取非共享（仅关联一个 face）link
    """
    unshared_links = set()
    for link_id in link_ids:
        if len(__get_all_faces_of_links(back_stabilizer, [link_id])) == 1:
            unshared_links.add(link_id)

    return unshared_links


def __get_unshared_nodes(back_stabilizer: PgsqlStabilizer, node_ids):
    """
    获取非共享（仅关联一个 link）node
    """
    unshared_nodes = set()
    for node_id in node_ids:
        all_links_of_node = __get_all_links_of_nodes(back_stabilizer, [node_id])
        if len(__get_all_faces_of_links(back_stabilizer, all_links_of_node)) == 1:
            unshared_nodes.add(node_id)

    return unshared_nodes


def __exists_aoi_rel(back_stabilizer: PgsqlStabilizer, face_id1, face_id2):
    """
    判断 AOI 层级关系是否存在
    """
    sql = '''
        select aoi_rel_id 
        from blu_aoi_rel
        where face_id1 = %s and
              face_id2 = %s
        limit 1;
    '''
    return back_stabilizer.fetch_one(sql, (face_id1, face_id2)) is not None


def __exists_face(back_stabilizer: PgsqlStabilizer, face_id):
    """
    判断 face 是否存在
    """
    sql = '''
        select 1 from blu_face where face_id = %s;
    '''
    return back_stabilizer.fetch_one(sql, (face_id,)) is not None


def __exists_related_face(back_stabilizer: PgsqlStabilizer, bid):
    """
    给定主点 bid，判断是否存在相关的 face。
    """
    sql = '''
        select 1
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s
        limit 1;
    '''
    return back_stabilizer.fetch_one(sql, (bid,)) is not None


def __get_perimeter(back_stabilizer: PgsqlStabilizer, wkt):
    """
    获取几何图形的周长
    """
    sql = '''
        select st_perimeter(st_geomfromtext(%s, 4326)::geography);
    '''
    return back_stabilizer.fetch_one(sql, (wkt,))[0]


def __get_area(back_stabilizer: PgsqlStabilizer, wkt):
    """
    获取几何图形的面积
    """
    sql = '''
        select st_area(st_geomfromtext(%s, 4326)::geography);
    '''
    return back_stabilizer.fetch_one(sql, (wkt,))[0]


def __get_length(back_stabilizer: PgsqlStabilizer, wkt):
    """
    获取几何图形的长度
    """
    sql = '''
        select st_length(st_geomfromtext(%s, 4326)::geography);
    '''
    return back_stabilizer.fetch_one(sql, (wkt,))[0]
