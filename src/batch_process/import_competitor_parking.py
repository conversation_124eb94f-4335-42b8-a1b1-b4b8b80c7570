# !/usr/bin/env python3
"""
入库竞品停车场
"""
import argparse
import json
import re
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from shapely import Polygon
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, tsv, pgsql

KIND_TO_TAG_MAP = {
    'ground': '地上停车场',
    'underground': '地下停车场',
    '3d': '立体停车场',
    'all': '停车场'
}

desc = pipeline.get_desc()


@dataclass
class PendingPoi:
    """
    待匹配的 POI
    """
    bid: str
    name: str
    wkt: str
    distance: float = 0
    similarity: float = 0
    tag: str = ''
    show_tag: str = ''


@dataclass
class Parking:
    """
    停车场信息
    """
    intel_id: str
    src: str
    main_bid: str = ''
    parent_bid: str = ''
    poi_wkt: str = ''
    name: str = ''
    full_name: str = ''
    poi_name: str = ''
    fee: str = ''
    intel_parent_id: str = ''
    display_wkt: str = ''
    navigate_wkt: str = ''
    aoi_wkt: str = ''
    parent_aoi_geom: Polygon = None
    capacity: int = 0
    kind: str = ''
    bd_kind: str = ''
    raw_content: str = ''
    tag: str = ''
    other_raw_content: str = ''
    diff_props: set[str] = field(default_factory=set)
    pending_pois: list[PendingPoi] = field(default_factory=list)
    best_pending_poi: PendingPoi = None
    can_process: bool = True
    reason: str = ''
    is_kind_conflict: bool = False
    is_main_id_invalid: bool = False

    def get_main_id(self):
        """
        获取停车场合适的主点
        """
        return self.main_bid if self.best_pending_poi is None else self.best_pending_poi.bid

    def get_poi_name(self):
        """
        获取停车场合适的名称
        """
        return self.poi_name if self.best_pending_poi is None else self.best_pending_poi.name


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    competitor_path: Path
    parking_dict: dict[str, Parking] = field(default_factory=dict)

    def add_or_update_parking(self, parking):
        """
        添加或更新停车场
        """
        if parking.intel_id in self.parking_dict:
            self.update_parking(parking)
        else:
            self.parking_dict[parking.intel_id] = parking

    def update_parking(self, parking):
        """
        更新停车场
        """
        old_parking = self.parking_dict.get(parking.intel_id, None)
        if old_parking is None or old_parking.src == parking.src:
            return

        if old_parking.navigate_wkt != parking.navigate_wkt and parking.navigate_wkt != '':
            old_parking.navigate_wkt = parking.navigate_wkt
        if old_parking.aoi_wkt != parking.aoi_wkt and parking.aoi_wkt != '':
            old_parking.aoi_wkt = parking.aoi_wkt
        if old_parking.tag != parking.tag and parking.tag != '':
            old_parking.tag = parking.tag
        if old_parking.intel_parent_id != parking.intel_parent_id and parking.intel_parent_id != '':
            old_parking.intel_parent_id = parking.intel_parent_id

        exists_diff = False
        if old_parking.display_wkt != parking.display_wkt:
            old_parking.display_wkt = parking.display_wkt
            old_parking.diff_props.add('display_wkt')
            exists_diff = True
        if old_parking.fee != parking.fee and parking.fee != '':
            old_parking.fee = parking.fee
            old_parking.diff_props.add('fee')
            exists_diff = True
        if old_parking.capacity != parking.capacity and parking.capacity > 0:
            old_parking.capacity = parking.capacity
            old_parking.diff_props.add('capacity')
            exists_diff = True
        if old_parking.kind != parking.kind and parking.kind != '':
            old_parking.kind = parking.kind
            old_parking.diff_props.add('kind')
            exists_diff = True

        if exists_diff:
            old_parking.other_raw_content = parking.raw_content

    def get_all_parking(self):
        """
        获取所有停车场
        """
        return self.parking_dict.values()

    def get_all_parking_count(self):
        """
        获取所有停车场数量
        """
        return len(self.parking_dict)


def get_line_count(path: Path):
    """
    获取文件行数
    """
    with open(path, 'r', encoding='utf-8') as f:
        return sum(1 for _ in f)


def get_value(data, json_path):
    """
    给定 json path 获取对应的值
    """
    current = data
    partial_paths = []

    for partial_path in json_path.split('.'):
        partial_paths.append(partial_path)
        if current is not None and partial_path in current:
            current = current[partial_path]
        else:
            return None

    return current


def parse_aos_park_data_fee(data):
    """
    解析 aos park 数据中的费用
    """
    for label in data['labelList']:
        label_text = label['text']
        if '元' in label_text:
            return label_text

    return ''


def parse_aos_park_data_capacity(data):
    """
    解析 aos park 数据中的车位数
    """
    for label in data['labelList']:
        label_text = label['text']
        if '车位' in label_text:
            for num in re.findall(r'\d+', label_text):
                return int(num)

    return 0


def parse_aos_park_data(json_value):
    """
    解析 aos park 数据
    """
    json_path = 'ditu_endpoi.aosParkData.tabList'
    tab_list = get_value(json_value, json_path)
    if tab_list is None:
        return

    for tab in tab_list:
        data_list = tab['dataList']
        for data in data_list:
            if '停车' not in data['fullName']:
                continue

            yield Parking(
                name=data['name'],
                full_name=data['fullName'],
                fee=parse_aos_park_data_fee(data),
                intel_id=data['poiId'],
                intel_parent_id=data['parentPoiId'],
                display_wkt=f"POINT({data['x']} {data['y']})" if data['x'] != '' else 'POINT EMPTY',
                capacity=parse_aos_park_data_capacity(data),
                raw_content=json.dumps(data, ensure_ascii=False),
                src=json_path,
            )


def parse_end_parking_capacity(data):
    """
    解析 end parking 数据中的车位数
    """
    capacity = get_value(data, 'deep.sum')
    if capacity is not None and capacity != 'null':
        return int(capacity)

    return 0


# noinspection SpellCheckingInspection
def parse_end_parking(json_value):
    """
    解析 end parking 数据
    """
    json_path = 'ditu_endpoi.endParking.aoi.childlist'
    child_list = get_value(json_value, json_path)
    if child_list is None:
        return

    for child in child_list:
        if '停车' not in child['name']:
            continue

        yield Parking(
            name=child['shortname'],
            full_name=child['name'],
            fee='',
            intel_id=child['poiid'],
            display_wkt=f"POINT({child['lon']} {child['lat']})" if child['lon'] != '' else 'POINT EMPTY',
            navigate_wkt=f"POINT({child['x_entr']} {child['x_entr']})" if child['x_entr'] != '' else 'POINT EMPTY',
            capacity=parse_end_parking_capacity(child),
            raw_content=json.dumps(child, ensure_ascii=False),
            src=json_path,
        )


def parse_radar_park_fee(data):
    """
    解析 radar park 数据中的费用
    """
    info = json.loads(data['info'])
    park_price = get_value(info, 'parkPrice')
    return park_price if park_price is not None else ''


def parse_radar_park_capacity(data):
    """
    解析 radar park 数据中的车位数
    """
    info = json.loads(data['info'])
    capacity = get_value(info, 'numSpace')
    return int(capacity) if capacity is not None else 0


def parse_radar_park_tag(data):
    """
    解析 radar park 数据中的标签
    """
    info = json.loads(data['info'])
    tag = get_value(info, 'tagTxt')
    return tag if tag is not None else ''


def parse_radar_park_wkt(data):
    """
    解析 radar park 数据中的 wkt
    """
    raw_wkt = data['aoiInfo']['shape']
    if raw_wkt == '':
        return raw_wkt

    return f"POLYGON(({raw_wkt.replace(',', ' ').replace('_', ',')}))"


def parse_radar_park(json_value):
    """
    解析 radar park 数据
    """
    json_path = 'ditu_endpoi.radarPark.parkList'
    park_list = get_value(json_value, json_path)
    if park_list is None:
        return

    for park in park_list:
        if '停车' not in park['name']:
            continue

        yield Parking(
            name=park['shortName'],
            full_name=park['name'],
            fee=parse_radar_park_fee(park),
            intel_id=park['poiId'],
            intel_parent_id=park['parentPoiId'],
            display_wkt=f"POINT({park['lon']} {park['lat']})" if park['lon'] != '' else 'POINT EMPTY',
            navigate_wkt=f"POINT({park['xEntr']} {park['yEntr']})" if park['xEntr'] != '' else 'POINT EMPTY',
            capacity=parse_radar_park_capacity(park),
            raw_content=json.dumps(park, ensure_ascii=False),
            tag=parse_radar_park_tag(park),
            src=json_path,
            aoi_wkt=parse_radar_park_wkt(park),
        )


def load_records_from_file(ctx: Context, f, row_count, load_record):
    """
    从文件中加载记录
    """
    with tqdm(total=row_count) as bar:
        f.seek(0)
        for line in f:
            bar.update()
            try:
                data = json.loads(line)
                for parking in load_record(data):
                    ctx.add_or_update_parking(parking)
            except Exception as e:
                print(e)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    row_count = get_line_count(ctx.competitor_path)

    with open(ctx.competitor_path, 'r', encoding='utf-8') as f:
        load_records_from_file(ctx, f, row_count, parse_aos_park_data)
        load_records_from_file(ctx, f, row_count, parse_end_parking)
        load_records_from_file(ctx, f, row_count, parse_radar_park)

    proceed()


def get_kind(park_name):
    """
    获取停车场的种类
    """
    def is_underground(content):
        return '地下' in content or '室内' in content

    def is_ground(content):
        return '地上' in content or '室外' in content or '地面' in content

    def is_3d(content):
        return '立体' in content

    if is_3d(park_name):
        return '3d'

    if is_ground(park_name) and not is_underground(park_name):
        return 'ground'

    if is_underground(park_name) and not is_ground(park_name):
        return 'underground'

    return 'ground'


@desc()
def fill_kind(ctx: Context, proceed):
    """
    填充停车场的种类
    """
    for parking in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
        # 以下情况直接判断为不匹配：
        # 1. 长名称和短名称存在冲突
        # 2. 空间属性和名称存在冲突

        kind_from_full_name = get_kind(parking.full_name)
        kind_from_name = get_kind(parking.name)

        if kind_from_name == '' or kind_from_name != kind_from_full_name:
            parking.can_process = False
            parking.reason = 'name conflict'
            continue

        parking.kind = kind_from_name

    proceed()


def get_bid_from_competitor_id(poi_conn, competitor_id):
    """
    获取三方 id 对应的 bid
    """
    get_bid_sql = '''select bid from competitor_id where competitor_id = %s;'''
    if competitor_id == '':
        return ''

    row = poi_conn.fetch_one(get_bid_sql, (competitor_id,))
    return '' if row is None else row[0]


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 poi 属性
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for parking in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
            if not parking.can_process:
                continue

            parking.main_bid = get_bid_from_competitor_id(poi_conn, parking.intel_id)
            parking.parent_bid = get_parent_bid(poi_conn, parking.main_bid)

            if parking.parent_bid == '' or parking.parent_bid == '0':
                parking.parent_bid = get_bid_from_competitor_id(poi_conn, parking.intel_parent_id)

            if parking.parent_bid == '' or parking.parent_bid == '0':
                parking.can_process = False
                parking.reason = 'parent_id_invalid'

    proceed()


def get_str_iou(str1, str2):
    """
    计算两个字符串的相似度
    """
    set1 = set(str1)
    set2 = set(str2)
    intersection_length = len(set1.intersection(set2))
    union_length = len(set1.union(set2))
    return intersection_length / union_length if union_length > 0 else 0.0


def get_parent_bid(poi_conn, bid):
    """
    获取指定 bid 的父点
    """
    sql = '''
        select relation_bid from poi where bid = %s;
    '''

    if bid == '':
        return ''

    row = poi_conn.fetch_one(sql, (bid,))
    if row is None:
        return ''

    relation_bid, = row
    return relation_bid


def get_aoi_wkt(back_conn, bid):
    """
    获取指定 bid 的边框
    """
    sql = '''
        select st_astext(a.geom)
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s and
              a.src != 'SD'
        limit 1;
    '''
    if bid == '':
        return None

    row = back_conn.fetch_one(sql, (bid,))
    return None if row is None else row[0]


def get_inner_parking_pois(poi_conn, parking: Parking):
    """
    获取停车场内部的 poi
    """
    desired_tags = [
        '交通设施;路侧停车位',
        '交通设施;停车场'
    ]

    sql = '''
        select bid, name, st_astext(geometry), std_tag, status
        from poi 
        where relation_bid = %s;
    '''

    # 考虑 2 级 poi 关联关系
    sons = [
        PendingPoi(bid=bid, name=name, wkt=wkt, tag=std_tag)
        for bid, name, wkt, std_tag, status in poi_conn.fetch_all(sql, (parking.parent_bid,)) if status == 1
    ]

    grandsons = []
    for son in sons:
        for bid, name, wkt, std_tag, status in poi_conn.fetch_all(sql, (son.bid,)):
            if status != 1 or std_tag not in desired_tags:
                continue
            grandsons.append(PendingPoi(bid=bid, name=name, wkt=wkt, tag=std_tag))

    return [x for x in sons if x.tag in desired_tags] + grandsons


def get_show_tag(poi_conn, bid):
    """
    获取指定 bid 的停车场 show_tag
    """
    sql = '''
        select show_tag
        from park_online_data_20240730
        where bid = %s
        limit 1;
    '''
    row = poi_conn.fetch_one(sql, (bid,))
    return '' if row is None else row[0]


def filter_parking_pois_by_kind(poi_conn, parking_pois, kind):
    """
    根据停车场类型筛选 poi
    """
    desired_show_tag = KIND_TO_TAG_MAP[kind]

    for pending_poi in parking_pois:
        pending_poi.show_tag = get_show_tag(poi_conn, pending_poi.bid)

    desired_pois = [x for x in parking_pois if desired_show_tag in x.show_tag]
    if any(desired_pois):
        return desired_pois

    return []


@desc()
def fill_pending_pois(ctx: Context, proceed):
    """
    填充待匹配的停车场 poi
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for parking in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
            if not parking.can_process:
                continue

            parking_pois = get_inner_parking_pois(poi_conn, parking)
            parking.pending_pois = filter_parking_pois_by_kind(poi_conn, parking_pois, parking.kind)

    proceed()


@desc()
def fill_parking_poi_similarity(ctx: Context, proceed):
    """
    为待匹配的停车场 poi 填充相似度
    """
    def clean_name(name):
        return name.rstrip('停车场').rstrip('内部停车场').strip('-')

    for parking in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
        if not parking.can_process:
            continue

        for pending_poi in parking.pending_pois:
            pending_poi.similarity = get_str_iou(clean_name(pending_poi.name), clean_name(parking.full_name))

    proceed()


@desc()
def fill_parking_poi_distance(ctx: Context, proceed):
    """
    为待匹配的停车场 poi 填充距离
    """
    for parking in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
        if not parking.can_process:
            continue

        poi_wkt = parking.poi_wkt if parking.poi_wkt != '' else parking.display_wkt
        parking_poi_geom = shapely.wkt.loads(poi_wkt)
        for pending_poi in parking.pending_pois:
            pending_poi.distance = parking_poi_geom.distance(shapely.wkt.loads(pending_poi.wkt)) * 1e5

    proceed()


@desc()
def fill_best_pending_poi(ctx: Context, proceed):
    """
    匹配最合适的停车场 poi
    """
    distance_level_1 = 50
    distance_level_2 = 100
    distance_max = 200
    similarity_level_1 = 0.8
    similarity_level_2 = 0.5

    def process_pois(pois, filter_method, sort_method, reverse=False):
        temp_pois = [x for x in pois if filter_method(x)]
        temp_pois.sort(key=sort_method, reverse=reverse)
        return temp_pois

    for parking in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
        if not parking.can_process:
            continue

        if not any(parking.pending_pois):
            continue

        similarity_level_1_pois = process_pois(
            parking.pending_pois,
            lambda x: x.similarity >= similarity_level_1,
            lambda x: x.similarity,
            reverse=True,
        )
        similarity_level_2_pois = process_pois(
            parking.pending_pois,
            lambda x: similarity_level_2 <= x.similarity < similarity_level_1,
            lambda x: x.similarity,
            reverse=True,
        )
        similarity_level_3_pois = process_pois(
            parking.pending_pois,
            lambda x: x.similarity < similarity_level_2,
            lambda x: x.similarity,
            reverse=True,
        )
        distance_level_1_pois = process_pois(
            parking.pending_pois,
            lambda x: x.distance < distance_level_1,
            lambda x: x.distance,
        )
        distance_level_2_pois = process_pois(
            parking.pending_pois,
            lambda x: distance_level_1 <= x.distance < distance_level_2,
            lambda x: x.distance,
        )
        distance_level_3_pois = process_pois(
            parking.pending_pois,
            lambda x: distance_level_2 <= x.distance < distance_max,
            lambda x: x.distance,
        )

        if any(distance_level_1_pois):
            parking.best_pending_poi = distance_level_1_pois[0]
        elif any(similarity_level_1_pois):
            parking.best_pending_poi = similarity_level_1_pois[0]
        elif any(distance_level_2_pois):
            parking.best_pending_poi = distance_level_2_pois[0]
        elif any(similarity_level_2_pois):
            parking.best_pending_poi = similarity_level_2_pois[0]
        elif any(distance_level_3_pois):
            parking.best_pending_poi = distance_level_3_pois[0]
        elif any(similarity_level_3_pois):
            parking.best_pending_poi = similarity_level_3_pois[0]

    proceed()


@desc()
def filter_records_by_kind(ctx: Context, proceed):
    """
    通过空间属性过滤记录
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for parking in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
            if not parking.can_process:
                continue

            # 当匹配的 show_tag 是 ‘停车场’ 时，kind 可以是任意的，除此之外需要一一对应。
            show_tag = get_show_tag(poi_conn, parking.get_main_id())
            if show_tag == '停车场':
                continue

            desired_show_tag = KIND_TO_TAG_MAP[parking.kind]
            if desired_show_tag != show_tag:
                parking.can_process = False
                parking.reason = 'conflict kind'

    proceed()


@desc()
def filter_records_by_poi_valid(ctx: Context, proceed):
    """
    将 poi 失效的记录过滤
    """
    sql = '''select 1 from poi where bid = %s;'''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn:
        for x in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
            if not x.can_process:
                continue

            row = poi_conn.fetch_one(sql, (x.get_main_id(),))
            if row is None:
                x.can_process = False
                x.reason = 'poi not exist'

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存结果
    """
    output_items = []

    for x in tqdm(ctx.get_all_parking(), total=ctx.get_all_parking_count()):
        if not x.can_process:
            continue

        output_items.append(
            [
                # x.reason,  # 被过滤原因
                x.intel_id,  # 三方 id
                x.intel_parent_id,  # 三方父 id
                x.main_bid,  # 原始 bid
                x.get_main_id(),  # 修正 bid
                x.name,  # 名称
                x.full_name,  # 完整名称
                x.get_poi_name(),  # poi 名称
                x.fee,  # 收费信息
                x.capacity,  # 车位数
                x.kind,  # 忽略，调试用
                x.tag,  # tagTxt，标签
                x.display_wkt,  # 显示坐标
                x.navigate_wkt,  # 导航坐标
                x.aoi_wkt,  # 竞品停车场面
            ]
        )

    tsv.write_tsv(ctx.work_dir / 'output.tsv', output_items)
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    # noinspection SpellCheckingInspection
    parser.add_argument(
        '--competitor-path',
        dest='competitor_path',
        help='竞品数据路径',
        type=str,
        default='/home/<USER>/aoi-strategy/online_door_semantization_test_1101/src/aoi_v3/parking_content/152w.pvsort'
                '.20240417.rst',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_kind,
        fill_poi_properties,
        fill_pending_pois,
        fill_parking_poi_similarity,
        fill_parking_poi_distance,
        fill_best_pending_poi,
        filter_records_by_kind,
        filter_records_by_poi_valid,
        save_records
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/import_competitor_parking'),
        competitor_path=Path(args.competitor_path),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
