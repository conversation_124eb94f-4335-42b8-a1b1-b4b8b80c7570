# !/usr/bin/env python3
"""
手动推送图灵工单
"""
import argparse
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

import mapio.utils.coord
import requests
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.auto_repair_mixin import get_pois_by_bid
from src.charging_station.helper import get_nested_value
from src.tools import pipeline, tsv, pgsql

PLATFORMS = ['practice', 'formal']
API_MAP = {
    'practice': 'http://*************:80/stargaze/api/addintelligence',
    'formal': 'http://*************:80/stargaze/api/addintelligence',
}

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    推送记录
    """
    id: str
    bid: str
    name: str
    city: str
    wkt: str
    bd_x: float
    bd_y: float
    work_messages: str
    ticket_id: str = ''
    success: bool = True
    message: str = ''

    @property
    def bd_point(self):
        return f'{self.bd_x},{self.bd_y}'

    @property
    def source_id(self):
        return f'aoi_{self.id}'

    @property
    def formatted_json(self):
        """
        可用于推送的 json
        """
        # REF: https://ku.baidu-int.com/d/nhrxP_rUnyzMRx
        current_datetime = datetime.now()
        current_timestamp = current_datetime.timestamp()
        return {
            'platform': 0,
            'source': 1217,
            'sourceid': self.source_id,
            'subbusinesstype': 1505,
            'cityname': self.city,
            'content': self.work_messages,
            'problem': 0,
            'occurtime': int(current_timestamp),
            'coordsys': 'bd09ll',
            'refgeo': [
                {
                    'type': 'Feature',
                    'geometry': {
                        'type': 'Point',
                        'coordinates': [self.bd_x, self.bd_y]
                    },
                    'properties': []
                }
            ],
            'sign': '96190ff43e1005b2b4a56e6a02124b99',
            '_signkeys': 'platform,source',
            'properties': {
                'poi_bid': self.bid,
                'poi_name': self.name,
                'coordinates': self.bd_point,
                'need_giving_back': {
                    'data_type': 'add_new_business_district_area'
                }
            },
        }


@dataclass
class Context:
    """
    策略执行上下文
    """
    api: str
    work_dir: Path
    bid_path: str
    platform: str
    work_messages: str
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    bids = [x[0] for x in tsv.read_tsv(ctx.bid_path)]
    pois = get_pois_by_bid(bids)

    for bid in tqdm(pois):
        poi = pois[bid]
        bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(poi.geom.x, poi.geom.y)

        ctx.records.append(Record(
            id=uuid.uuid4().hex,
            bid=bid,
            name=poi.name,
            city=poi.city,
            wkt=poi.geom.wkt,
            bd_x=bd_x,
            bd_y=bd_y,
            work_messages=ctx.work_messages,
        ))

    proceed()


@desc()
def push_tickets(ctx: Context, proceed):
    """
    推送工单
    """
    success_code = 200

    for record in tqdm(ctx.records):
        try:
            print(record.formatted_json)
            response = requests.post(ctx.api, json=record.formatted_json)
            if response.status_code != success_code:
                record.success = False
                record.message = response.text
                continue

            response_json = response.json()
            print(response_json)
            errno = get_nested_value(response_json, ['errno'])
            ticket_id = get_nested_value(response_json, ['data', 'id'])

            if errno is None or errno != 0 or ticket_id is None:
                record.success = False
                record.message = errno
                continue

            record.ticket_id = ticket_id
        except Exception as e:
            record.success = False
            record.message = str(e)
        finally:
            time.sleep(0.5)

    proceed()


@desc()
def save_tickets_to_db(ctx: Context, proceed):
    """
    保存工单到数据库
    """
    sql = '''
        insert into business_district_ticket(source_id, bid, ticket_id, platform)
        values(%s, %s, %s, %s);
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for record in tqdm(ctx.records):
                cur.execute(sql, [
                    record.source_id,
                    record.bid,
                    record.ticket_id,
                    ctx.platform,
                ])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存工单
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.source_id,
                x.bid,
                x.name,
                x.city,
                x.wkt,
                x.bd_x,
                x.bd_y,
                x.success,
                x.message,

                ctx.platform,
                ctx.api,
            ]
            for x in ctx.records
        ]
    )

    proceed()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_records,
    ]

    if args.push:
        pipes.extend([
            push_tickets,
            save_tickets_to_db,
        ])

    pipes.append(save_records)

    return pipeline.Pipeline(*pipes)


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-path',
        dest='bid_path',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--platform',
        dest='platform',
        type=str,
        choices=PLATFORMS,
        default='practice',
        required=False,
    )
    parser.add_argument(
        '--work-messages',
        dest='work_messages',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--push',
        dest='push',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        api=API_MAP[args.platform],
        work_dir=Path("cache/manual_push_turing_ticket"),
        bid_path=args.bid_path,
        platform=args.platform,
        work_messages=args.work_messages,
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
