# !/usr/bin/env python3
"""
用于标记边框的上线来源。
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

import pymysql
from tqdm import tqdm

from src.tools import pipeline, pgsql, tsv
from src.tools.conf_tools import get_mysql_conf
from src.tools.file_downloader import download_file_by_http, download_file_by_afs

desc = pipeline.get_desc()


@dataclass
class OnlineRecord:
    """
    存储上线任务的相关信息。
    """
    batch: str
    src: str
    url: str
    path: Path = field(init=False)


@dataclass
class OnlineAoi:
    """
    存储上线 AOI 信息。
    """
    face_id: str
    is_valid: bool = True


@dataclass
class Context:
    """
    脚本执行上下文。
    """
    work_dir: Path
    source: str
    mode: str
    url: str
    file_path: Path
    batch_filter_predicate: str
    online_records: list[OnlineRecord] = field(default_factory=list)
    online_aois: list[OnlineAoi] = field(default_factory=list)

    def read_face_ids_from_local_file(self, file_path: Path):
        """
        读取本地文件，并解析出 face_id。
        """
        latest_line_length = 7
        q2_line_length = 8
        q3_line_length = 9

        for item in tsv.read_tsv(file_path):
            if len(item) == latest_line_length:
                bid, mid, name, city, wkt, aoi_id, remark = item
                self.online_aois.append(OnlineAoi(face_id=aoi_id))
            elif len(item) == q2_line_length:
                bid, mid, name, city, _, wkt, aoi_id, remark = item
                self.online_aois.append(OnlineAoi(face_id=aoi_id))
            elif len(item) == q3_line_length:
                bid, mid, name, city, _, wkt, aoi_id, remark, _ = item
                self.online_aois.append(OnlineAoi(face_id=aoi_id))

    def read_face_ids_from_remote_file(self, url: str, save_path: Path = None):
        """
        下载远程文件，并解析出 face_id。
        """
        save_path = save_path or self.work_dir / 'temp.tsv'
        download_file(url, save_path)
        self.read_face_ids_from_local_file(save_path)

    def load_face_ids_by_history(self):
        """
        从历史记录中读取 face_id。
        """
        with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
            sql = '''
                select face_id from blu_face where src = 'IR';
            '''
            for face_id, in tqdm(pgsql.fetch_all(conn, sql)):
                self.online_aois.append(OnlineAoi(face_id=face_id))

    def load_face_ids_by_lib(self):
        """
        从库中读取 face_id。
        """
        with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
            sql = f'''
                select batch, src, url
                from online_aoi_task
                where batch {self.batch_filter_predicate};
            '''
            for batch, src, url in tqdm(pgsql.fetch_all(conn, sql)):
                self.read_face_ids_from_remote_file(url, self.work_dir / f'{batch}.tsv')


# noinspection SpellCheckingInspection
def get_connection():
    """
    获取 mysql 连接。
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow_rw')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def download_file(url, save_path: Path):
    """
    下载文件。
    """
    if url.startswith('http'):
        download_file_by_http(url, save_path)
    elif url.startswith('/user/map-data-streeview'):
        download_file_by_afs(url, save_path)


@desc()
def load_face_ids(ctx: Context, proceed):
    """
    加载 face_id 集合。
    """
    if ctx.mode == 'lib':
        ctx.load_face_ids_by_lib()
    elif ctx.mode == 'url':
        ctx.read_face_ids_from_remote_file(ctx.url)
    elif ctx.mode == 'file':
        ctx.read_face_ids_from_local_file(ctx.file_path)
    elif ctx.mode == 'history':
        ctx.load_face_ids_by_history()

    proceed()


@desc()
def filter_face_ids_by_status(ctx: Context, proceed):
    """
    过滤过滤掉不存在的 face_id。
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for aoi in tqdm(ctx.online_aois):
            sql = '''
                select 1 from blu_face where face_id = %s limit 1;
            '''
            if not pgsql.fetch_one(conn, sql, (aoi.face_id,)):
                aoi.is_valid = False

    proceed()


@desc()
def insert_aoi_source_records(ctx: Context, proceed):
    """
    插入 aoi_source 记录。
    """
    with (
        get_connection() as conn,
        conn.cursor() as cursor,
    ):
        sql = '''
            insert into aoi_source (face_id, source)
            values (%s, %s)
            on duplicate key update face_id = face_id;
        '''

        try:
            for aoi in tqdm([x for x in ctx.online_aois if x.is_valid]):
                cursor.execute(sql, (aoi.face_id, ctx.source))

            conn.commit()
        except:
            conn.rollback()

    proceed()


def parse_args():
    """
    解析命令行参数。
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        default='lib',
        required=False,
    )
    parser.add_argument(
        '--batch-filter',
        dest='batch_filter',
        type=str,
        default="like '%online_competitor%'",
        required=False,
    )
    parser.add_argument(
        '--url',
        dest='url',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--file-path',
        dest='file_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--source',
        dest='source',
        type=str,
        default='competitor',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数。
    """
    main_pipe = pipeline.Pipeline(
        load_face_ids,
        filter_face_ids_by_status,
        insert_aoi_source_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/mark_aoi_source'),
        mode=args.mode,
        batch_filter_predicate=args.batch_filter,
        url=args.url,
        file_path=Path(args.file_path),
        source=args.source,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
