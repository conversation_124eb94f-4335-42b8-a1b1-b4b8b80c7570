# !/usr/bin/env python3
"""
pg 从库在长链接时会断开，本类是对 pgsql 类的包装，并支持自动重连。
"""
import time
from datetime import datetime, timedelta
from typing import Callable
from typing import Mapping
from typing import Sequence
from typing import Union

import psycopg2


class PgsqlStabilizer:
    """
    pgsql 稳定器
    """

    def __init__(self, config: Union[dict, Callable[[], dict]], timeout_minutes=10, cursor_factory=None, init=False):
        self.config = config
        self.__timeout_minutes = timeout_minutes
        self.__cursor_factory = cursor_factory
        self.__start = datetime.now()
        self.connection = None
        if init:
            self.__ensure_connection()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            self.connection.close()
        except:
            pass

    def __loop_execute(self, process):
        """
        循环执行，直到成功。
        """
        while True:
            try:
                return process()
            except Exception as e:
                print(e)
                time.sleep(8)
                self.__ensure_connection()

    def fetch_one(self, sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
        """
        获取一条记录。
        """

        def execute():
            self.__ensure_connection()
            with self.connection.cursor(cursor_factory=self.__cursor_factory) as c:
                c.execute(sql, args)
                return c.fetchone()

        return self.__loop_execute(execute)

    def fetch_all(self, sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
        """
        获取多条记录。
        """

        def execute():
            self.__ensure_connection()
            with self.connection.cursor(cursor_factory=self.__cursor_factory) as c:
                c.execute(sql, args)
                return c.fetchall()

        return self.__loop_execute(execute)

    def execute(self, sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
        """
        执行 sql 语句。
        """

        def execute():
            self.__ensure_connection()
            with self.connection.cursor(cursor_factory=self.__cursor_factory) as c:
                c.execute(sql, args)
                self.connection.commit()

        self.__loop_execute(execute)

    def __create_connection(self):
        """
        创建连接。
        """
        current_config = self.config if type(self.config) is dict else self.config()
        return psycopg2.connect(
            database=current_config["db"],
            user=current_config["user"],
            password=current_config["pwd"],
            host=current_config["host"],
            port=current_config["port"],
        )

    def __ensure_connection(self):
        """
        确保连接存活。
        """
        if self.connection is None or self.connection.closed:
            self.connection = self.__create_connection()
            return

        if datetime.now() - self.__start > timedelta(minutes=self.__timeout_minutes):
            self.__start = datetime.now()
            self.connection.close()
            self.connection = self.__create_connection()
