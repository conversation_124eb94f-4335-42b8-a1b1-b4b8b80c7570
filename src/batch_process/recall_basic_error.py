# !/usr/bin/env python3
"""
召回基础属性错误的边框
"""
from dataclasses import dataclass, field
from pathlib import Path

import numpy as np
import shapely.wkt
from shapely import MultiPoint, Polygon, Point
from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.batch_helper import batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.batch_process.remove_aoi import run
from src.tools import pipeline, pgsql, tsv
from src.tools.connect_aoi_to_poi import BatchMainPoiTool

GET_FACE_ID_SQL = '''
    select distinct face_id from blu_face;
'''
GET_MESH_ID_SQL = '''
    select distinct mesh_id from blu_face;
'''
GET_AOI_PROPERTIES_SQL = '''
    select a.kind, a.src, a.area, b.poi_bid, st_astext(a.geom)
    from blu_face a
    left join blu_face_poi b
    on a.face_id = b.face_id
    where a.face_id = %s;
'''
GET_AOI_PROPERTIES_SQL_V2 = '''
    select a.face_id, a.mesh_id, a.kind, a.src, a.area, b.poi_bid, st_astext(a.geom)
    from blu_face a
    left join blu_face_poi b
    on a.face_id = b.face_id
    where a.mesh_id = %s;
'''
GET_POI_PROPERTIES_SQL = '''
    select std_tag, click_pv, name, st_astext(geometry) from poi where bid = %s;
'''
IGNORED_NAMES = [
    '经济开发区',
    '工业',
    '软件园',
    '科技园',
    '产业园',
    '物流园',
    '大学城',
    '工业园',
    '开发区',
    '创意园',
    '创业园',
    '示范中心',
    '保税港区',
    '基地',
    '广场',
    '孵化',
    '智慧谷',
    '工贸园',
    '园区',
    '部件园',
    '智造园',
    '电商园',
    '科创园',
    '科技城',
    '物流城',
    '科技生态谷',
    '文创园',
    '创新园',
    '农科园',
]

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    批处理记录
    """
    face_id: str
    bid: str
    geom: Polygon
    pv: int = 0
    std_tag: str = ''
    name: str = ''
    main_poi_geom: Point = None
    kind: str = ''
    src: str = ''
    mesh_id: str = ''
    area: float = 0.0
    can_process: bool = True
    action: str = ''
    reason: str = ''
    distance: float = 0.0
    small_angle: str = ''


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    mesh_ids: list[str] = field(default_factory=list)
    face_ids: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def get_pending_remove_aoi_records(self):
        """
        获取待删除的 aoi 记录
        """
        return [x for x in self.records if not x.can_process and x.action == 'remove_aoi']

    def get_pending_remove_main_poi_records(self):
        """
        获取待删除主点的 aoi 记录
        """
        return [x for x in self.records if not x.can_process and x.action == 'remove_main_poi']


@desc()
def load_mesh_ids(ctx: Context, proceed):
    """
    加载 mesh_id
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        ctx.mesh_ids = [x[0] for x in pgsql.fetch_all(conn, GET_MESH_ID_SQL)]

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理信息
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for mesh_id in tqdm(ctx.mesh_ids):
            for row in pgsql.fetch_all(conn, GET_AOI_PROPERTIES_SQL_V2, (mesh_id,)):
                face_id, mesh_id, kind, src, area, bid, wkt = row
                ctx.records.append(Record(
                    face_id=face_id,
                    geom=shapely.wkt.loads(wkt),
                    kind=kind,
                    src=src,
                    area=area,
                    bid=bid,
                    mesh_id=mesh_id,
                ))

    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 poi 属性
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        for record in tqdm(ctx.records):
            if record.bid == '':
                continue

            row = conn.fetch_one(GET_POI_PROPERTIES_SQL, (record.bid,))
            if row is None:
                continue

            record.std_tag, record.pv, record.name, wkt = row
            record.main_poi_geom = shapely.wkt.loads(wkt)

    proceed()


@desc()
def recall_business_aoi(ctx: Context, proceed):
    """
    商圈例行化下线
    """
    business_tag = '行政地标;商圈'
    business_src = 'SQ'

    def process(record: Record):
        is_business_tag = record.std_tag == business_tag
        is_business_src = record.src == business_src

        # 1、POI 的 std_tag != 行政地标;商圈，SRC = SQ（商圈），直接删除边框，且 PV >= 210 的需要下发人工作业；
        # 2、POI 的 std_tag = 行政地标;商圈，SRC = SQ（商圈），保留，不好验证范围。
        # 3、POI 的 std_tag != 行政地标;商圈，SRC != SQ（商圈），保留。
        # 4、POI 的 std_tag = 行政地标;商圈，SRC != SQ（商圈），下线。
        # 5、POI 已失效或删除，SRC = SQ（商圈），下线。

        if not is_business_tag and is_business_src:
            # 此脚本仅负责错误边框下线，下发人工的逻辑会在 auto_business_to_customer 中处理。
            record.reason = '商圈例行化下线 (reason 1)'
        # 条件 4 暂时注释
        # elif is_business_tag and not is_business_src:
        #     record.reason = '商圈例行化下线 (reason 4)'
        elif record.bid == '' and is_business_tag:
            record.reason = '商圈例行化下线 (reason 5)'

        if record.reason != '':
            record.can_process = False
            record.action = 'remove_aoi'

    batch_process(ctx.records, process)
    proceed()


@desc()
def recall_tiny_area_aoi(ctx: Context, proceed):
    """
    小面积例行化下线
    """
    min_area = 100
    min_pv = 210

    def process(record: Record):
        if record.area < min_area:
            if record.std_tag in tags.ACCURATE_SCENIC and record.pv >= min_pv:
                return

            record.can_process = False
            record.reason = 'area is tiny'
            record.action = 'remove_aoi'

    batch_process(ctx.records, process)
    proceed()


@desc()
def recall_huge_area_aoi(ctx: Context, proceed):
    """
    大面积例行化下线
    """
    max_ratio = 0.97
    record_tag_map = {}

    # 按垂类分组
    for record in (x for x in ctx.records if x.can_process):
        grouped_records = record_tag_map.get(record.std_tag, [])
        grouped_records.append(record)
        record_tag_map[record.std_tag] = grouped_records

    # 按面积排序
    for grouped_records in record_tag_map.values():
        grouped_records.sort(key=lambda x: x.area)

    # 选取面积过大的记录
    for sorted_records in record_tag_map.values():
        skip_count = int(len(sorted_records) * max_ratio)
        if skip_count == 0:
            continue

        for huge_record in sorted_records[skip_count:]:
            huge_record.can_process = False
            huge_record.reason = 'area is huge'

    proceed()


@desc()
def recall_admin_aoi(ctx: Context, proceed):
    """
    行政区划例行化下线
    """

    def process(record: Record):
        # 非行政区划数据，不用下线。
        if record.std_tag not in tags.ADMIN:
            return

        # 普通等级行政区划名字在 IGNORED_NAMES 中的，不下线。
        if record.std_tag not in tags.TOP_ADMIN and any(x for x in IGNORED_NAMES if x in record.name):
            return

        # 不符合上述条件的，全部下线。
        record.can_process = False
        record.reason = '行政区划例行化删除'
        record.action = 'remove_aoi'

    batch_process(ctx.records, process)
    proceed()


@desc()
def recall_tollbooth_aoi(ctx: Context, proceed):
    """
    收费站例行化下线
    """

    def process(record: Record):
        if record.std_tag == '交通设施;收费站':
            record.can_process = False
            record.reason = '收费站例行化下线'
            record.action = 'remove_aoi'

    batch_process(ctx.records, process)
    proceed()


@desc()
def recall_access_aoi(ctx: Context, proceed):
    """
    出入口例行化下线
    """

    def process(record: Record):
        if '出入口' in record.std_tag:
            record.can_process = False
            record.reason = '出入口例行化删除主点'
            record.action = 'remove_main_poi'

    batch_process(ctx.records, process)
    proceed()


@desc()
def recall_aoi_with_external_main_poi(ctx: Context, proceed):
    """
    主点在边框外例行化下线
    """
    min_pv = 210
    max_distance = 10

    def process(record: Record):
        if record.main_poi_geom is None:
            return

        if not record.geom.contains(record.main_poi_geom):
            record.distance = record.geom.distance(record.main_poi_geom) * 1e5
            if record.distance > max_distance and record.pv < min_pv:
                record.can_process = False
                record.reason = '主点在边框外例行化下线'
                record.action = 'remove_main_poi'

    batch_process(ctx.records, process)
    proceed()


def angle_between(v1, v2):
    """
    计算两个向量之间的夹角（角度制）
    """
    # 通过点积和向量长度计算夹角
    dot_product = np.dot(v1, v2)
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)

    # 如果向量长度过小，直接返回 None，表示无法计算有效的角度
    if norm_v1 < 1e-6 or norm_v2 < 1e-6:
        return None

    # 计算夹角的余弦值，并通过反余弦函数求角度
    cos_theta = dot_product / (norm_v1 * norm_v2)
    angle_radians = np.arccos(np.clip(cos_theta, -1.0, 1.0))  # 限制 cos 值在 [-1, 1] 范围内
    angle_degrees = np.degrees(angle_radians)

    return angle_degrees


def find_small_angles(polygon, threshold_angle=20):
    """
    查找多边形中所有角度小于指定阈值（例如 20 度）的顶点
    """
    coords = np.array(polygon.exterior.coords)
    n = len(coords) - 1  # 忽略最后一个重复的点

    small_angle_indices = []

    for i in range(1, n - 1):  # 从第二个点到倒数第二个点计算角度
        # 计算两条边的向量
        v1 = coords[i - 1] - coords[i]  # 向量 P_i -> P_{i-1}
        v2 = coords[i + 1] - coords[i]  # 向量 P_i -> P_{i+1}

        # 计算角度
        angle = angle_between(v1, v2)

        # 如果计算的角度存在且小于阈值，则记录该顶点
        if angle is not None and angle < threshold_angle:
            small_angle_indices.append(coords[i])

    return small_angle_indices


@desc()
def recall_abnormal_aoi(ctx: Context, proceed):
    """
    畸形边框例行化下线
    """

    def process(record: Record):
        small_angle_vertices = find_small_angles(record.geom)
        if len(small_angle_vertices) > 0:
            record.can_process = False
            record.reason = '畸形边框例行化下线'
            record.action = 'update aoi geom'
            record.small_angle = MultiPoint(small_angle_vertices).wkt

    batch_process(ctx.records, process)
    proceed()


@desc()
def save_debug_records(ctx: Context, proceed):
    """
    保存 debug 记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.reason,
                x.face_id,
                x.bid,
                x.name,
                x.area,
                x.kind,
                x.pv,
                x.std_tag,
                x.src,
                x.distance,
                x.small_angle,
                x.action,
                # x.geom.wkt,
                # x.main_poi_geom.wkt if x.main_poi_geom is not None else '',
            ]
            for x in ctx.records if not x.can_process
        ]
    )
    proceed()


@desc()
def execute_remove_aoi(ctx: Context, proceed):
    """
    执行删除 aoi
    """
    pending_remove_face_ids = [x.face_id for x in ctx.get_pending_remove_aoi_records()]
    run(
        face_ids=pending_remove_face_ids,
        memo='例行化删除错误 tag 边框',
        immediately_execute=False,
    )
    proceed()


@desc()
def execute_remove_main_poi(ctx: Context, proceed):
    """
    执行删除主点
    """
    batch_tool = BatchMainPoiTool()

    for record in ctx.get_pending_remove_main_poi_records():
        batch_tool.insert_record(
            '',
            record.face_id,
            'SD',
            record.mesh_id,
            '',
            '例行化删除错误边框主点',
        )

    batch_tool.execute()
    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_mesh_ids,
        load_records,
        fill_poi_properties,
        # 行政区划例行化下线
        # recall_admin_aoi,
        # 商圈例行化下线
        recall_business_aoi,
        # 小面积例行化下线
        recall_tiny_area_aoi,
        # 大面积例行化下线
        # recall_huge_area_aoi,
        # 收费站例行化下线
        recall_tollbooth_aoi,
        # 出入口例行化删除主点
        recall_access_aoi,
        # 主点在边框外例行化下线
        recall_aoi_with_external_main_poi,
        # 畸形边框例行化下线
        # recall_abnormal_aoi,
        # 保存记录
        save_debug_records,
        # 执行批处理
        execute_remove_aoi,
        execute_remove_main_poi,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/recall_basic_error'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
