# !/usr/bin/env python3
"""
召回层级错误的边框
"""
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.batch_helper import contains_majority
from src.model.aoi_model import AoiModel
from src.tools import pipeline, pgsql, tsv

CLUSTER_LEVEL = 1
BASIC_LEVEL = 2
INNER_LEVEL = 3
GET_FACE_ID_SQL = '''
    select distinct face_id 
    from blu_face 
    where aoi_level = %s and 
          src != 'SD';
'''
GET_OVERLAP_FACE_SQL = '''
    select b.face_id
    from blu_face a, blu_face b
    where a.face_id = %s and 
          st_intersects(a.geom, b.geom) and
          a.face_id != b.face_id and
          b.aoi_level = %s and
          a.src != 'SD';
'''
GET_FACE_WKT_SQL = '''
    select st_astext(geom) from blu_face where face_id = %s;
'''
GET_AOI_PROPERTIES_SQL = '''
    select b.poi_bid, a.src, st_astext(a.geom)
    from blu_face a
    left join blu_face_poi b
    on a.face_id = b.face_id
    where a.face_id = %s;
'''
GET_POI_PROPERTIES_SQL = '''
    select std_tag, click_pv, name from poi where bid = %s;
'''
GET_OVERLAP_ROAD_SQL = '''
    select form
    from nav_link 
    where st_intersects(st_geomfromtext(%s, 4326), geom) and
          kind < 8;
'''

desc = pipeline.get_desc()


@dataclass
class FaceRecord:
    """
    边框信息
    """
    face_id: str
    aoi_level: int
    src: str = ''
    bid: str = ''
    tag: str = ''
    pv: float = 0
    name: str = ''
    wkt: str = ''


@dataclass
class NestedRecord:
    """
    边框嵌套信息
    """
    face1: FaceRecord
    face2: FaceRecord


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    cluster_face_ids: list[str] = field(default_factory=list)
    basic_face_ids: list[str] = field(default_factory=list)
    inner_face_ids: list[str] = field(default_factory=list)
    nested_records: list[NestedRecord] = field(default_factory=list)
    cross_road_basic_records: list[FaceRecord] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def get_face_ids(self, aoi_level):
        """
        根据边框层级获得符合要求的边框 id 集合
        """
        if aoi_level == CLUSTER_LEVEL:
            return self.cluster_face_ids
        elif aoi_level == BASIC_LEVEL:
            return self.basic_face_ids
        elif aoi_level == INNER_LEVEL:
            return self.inner_face_ids
        else:
            raise ValueError(f'unknown aoi_level: {aoi_level}')


@desc()
def load_face_ids(ctx: Context, proceed):
    """
    加载边框 id 集合
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        ctx.cluster_face_ids = [x[0] for x in pgsql.fetch_all(conn, GET_FACE_ID_SQL, (CLUSTER_LEVEL,))]
        ctx.basic_face_ids = [x[0] for x in pgsql.fetch_all(conn, GET_FACE_ID_SQL, (BASIC_LEVEL,))]
        ctx.inner_face_ids = [x[0] for x in pgsql.fetch_all(conn, GET_FACE_ID_SQL, (INNER_LEVEL,))]

    proceed()


def get_geom(back_conn, face_id):
    """
    获取边框形状
    """
    row = pgsql.fetch_one(back_conn, GET_FACE_WKT_SQL, (face_id,))
    if row is None:
        return None

    current_wkt, = row
    return shapely.wkt.loads(current_wkt)


def __load_nested_records(ctx: Context, current_level, overlap_level):
    """
    加载嵌套边框
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for face_id in tqdm(ctx.get_face_ids(current_level)):
            overlap_face_ids = [x[0] for x in pgsql.fetch_all(conn, GET_OVERLAP_FACE_SQL, (face_id, overlap_level))]
            if not any(overlap_face_ids):
                continue

            current_geom = get_geom(conn, face_id)
            if current_geom is None:
                continue

            for overlap_face_id in overlap_face_ids:
                overlap_geom = get_geom(conn, overlap_face_id)
                if overlap_geom is None:
                    continue

                if contains_majority(current_geom, overlap_geom):
                    yield NestedRecord(
                        FaceRecord(face_id, current_level),
                        FaceRecord(overlap_face_id, overlap_level)
                    )


@desc()
def load_nested_records(ctx: Context, proceed):
    """
    加载嵌套边框
    """
    ctx.nested_records.extend(__load_nested_records(ctx, CLUSTER_LEVEL, CLUSTER_LEVEL))

    ctx.nested_records.extend(__load_nested_records(ctx, BASIC_LEVEL, CLUSTER_LEVEL))
    ctx.nested_records.extend(__load_nested_records(ctx, BASIC_LEVEL, BASIC_LEVEL))

    ctx.nested_records.extend(__load_nested_records(ctx, INNER_LEVEL, CLUSTER_LEVEL))
    ctx.nested_records.extend(__load_nested_records(ctx, INNER_LEVEL, BASIC_LEVEL))
    ctx.nested_records.extend(__load_nested_records(ctx, INNER_LEVEL, INNER_LEVEL))

    proceed()


def fill_aoi_properties(records: list[FaceRecord]):
    """
    填充边框属性
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for record in tqdm(records):
            row = pgsql.fetch_one(conn, GET_AOI_PROPERTIES_SQL, (record.face_id,))
            if row is None:
                return

            record.bid, record.src, record.wkt = row


def fill_poi_properties(records: list[FaceRecord]):
    """
    填充 POI 属性
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        for record in tqdm(records):
            if record.bid == '':
                return

            row = pgsql.fetch_one(conn, GET_POI_PROPERTIES_SQL, (record.bid,))
            if row is None:
                return

            record.tag, record.pv, record.name = row


@desc()
def fill_nested_record_aoi_properties(ctx: Context, proceed):
    """
    填充嵌套边框属性
    """
    fill_aoi_properties([x.face1 for x in ctx.nested_records] + [x.face2 for x in ctx.nested_records])
    proceed()


@desc()
def fill_nested_record_poi_properties(ctx: Context, proceed):
    """
    填充嵌套边框 POI 属性
    """
    fill_poi_properties([x.face1 for x in ctx.nested_records] + [x.face2 for x in ctx.nested_records])
    proceed()


@desc()
def load_cross_road_basic_records(ctx: Context, proceed):
    """
    加载压路的边框
    """
    with AoiModel() as aoi_model:
        for face_id in tqdm(ctx.get_face_ids(BASIC_LEVEL)):
            rows = pgsql.fetch_all(aoi_model.conn_road, GET_OVERLAP_ROAD_SQL, (face_id,))
            if not any(rows):
                continue

            ctx.cross_road_basic_records.append(FaceRecord(
                face_id=face_id,
                aoi_level=BASIC_LEVEL,
            ))

    proceed()


@desc()
def fill_cross_road_basic_record_aoi_properties(ctx: Context, proceed):
    """
    填充压路边框属性
    """
    fill_aoi_properties(ctx.cross_road_basic_records)
    proceed()


@desc()
def fill_cross_road_basic_record_poi_properties(ctx: Context, proceed):
    """
    填充压路边框的 POI 属性
    """
    fill_poi_properties(ctx.cross_road_basic_records)
    proceed()


@desc()
def save_debug_records(ctx: Context, proceed):
    """
    保存调试记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output_debug.csv',
        [
            [
                x.face1.face_id,
                x.face2.face_id,
                x.face1.src,
                x.face2.src,
                x.face1.bid,
                x.face2.bid,
                x.face1.tag,
                x.face2.tag,
                x.face1.pv,
                x.face2.pv,
                x.face1.name,
                x.face2.name,
                x.face1.aoi_level,
                x.face2.aoi_level,
                x.face1.wkt,
                x.face2.wkt,
            ]
            for x in ctx.nested_records
        ]
    )
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存结果
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x
            ]
            for x in set(x.face1.face_id for x in ctx.nested_records)
        ]
    )
    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_face_ids,

        # poiinshanghai-12016 2.1
        load_nested_records,
        fill_nested_record_aoi_properties,
        fill_nested_record_poi_properties,

        # poiinshanghai-12016 2.2
        # load_cross_road_basic_records,
        # fill_cross_road_basic_record_aoi_properties,
        # fill_cross_road_basic_record_poi_properties,

        save_debug_records,
        # save_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/recall_error_aoi_level'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
