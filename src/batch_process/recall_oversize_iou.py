# !/usr/bin/env python3
"""
召回交并比过高的边框（冗余边框例行化下线）
"""
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

import shapely.wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.batch_process.auto_remove_aoi_with_repeated_bid import get_pending_remove_aoi
from src.batch_process.batch_helper import batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.batch_process.remove_aoi import run
from src.tools import pipeline, pgsql, tsv, notice_tool

MAX_IOU = 0.8
GET_FACE_ID_SQL = '''
    select distinct face_id from blu_face where aoi_level = 2;
'''
GET_OVERLAP_AOI_SQL = '''
    select face_id, st_astext(geom) 
    from blu_face 
    where st_intersects(geom, st_geomfromtext(%s, 4326)) and
          aoi_level = 2 and
          face_id != %s;
'''
GET_AOI_WKT_SQL = '''
    select st_astext(geom) from blu_face where face_id = %s;
'''
GET_AOI_PROPERTIES_SQL = '''
    select a.src, a.aoi_level, b.poi_bid
    from blu_face a
    inner join blu_face_poi b
    on a.face_id = b.face_id
    where a.face_id = %s;
'''
GET_POI_PROPERTIES_SQL = '''
    select std_tag, click_pv, name from poi where bid = %s;
'''

GET_PROTECT_AOI_SQL = '''
    select main_bid from aoi_boundary_protect where key in ('recall_oversize_iou');
'''

desc = pipeline.get_desc()


@dataclass
class OverlapRecord:
    """
    压盖信息
    """
    kind: str
    face_id: str
    geom: BaseGeometry
    src: str = ''
    tag: str = ''
    bid: str = ''
    name: str = ''
    aoi_level: int = 0
    pv: int = 0
    iou: float = 0

    last_manual_work_time: datetime = datetime.min
    last_auto_work_time: datetime = datetime.min
    manual_worked: bool = False
    competitor_iou: float = 0
    has_competitor: bool = False
    competitor_wkt: str = ''
    retained: bool = True

    can_process: bool = True
    ignored: bool = False
    remove: bool = False
    reason: str = ''


@dataclass
class Record(OverlapRecord):
    """
    冗余记录
    """
    overlap_records: list[OverlapRecord] = field(default_factory=list)


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    face_ids: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    loaded_face_ids: set[str] = field(default_factory=set)
    pending_remove_face_ids: list[str] = field(default_factory=list)
    pending_remove_toC_count: int = 0
    pending_remove_toB_count: int = 0

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def add_record(self, record: Record):
        """
        添加冗余记录
        """
        if record.face_id in self.loaded_face_ids:
            return

        self.loaded_face_ids.add(record.face_id)

        # 嵌套压盖比较复杂，不适合单次处理，可通过多次 1 对 1 压盖解决。
        for overlap_record in record.overlap_records:
            if overlap_record.face_id in self.loaded_face_ids:
                return

        for overlap_record in record.overlap_records:
            self.loaded_face_ids.add(overlap_record.face_id)

        self.records.append(record)


@desc()
def load_face_ids(ctx: Context, proceed):
    """
    加载待处理的 face_id 集合
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        ctx.face_ids = [x[0] for x in pgsql.fetch_all(conn, GET_FACE_ID_SQL)]

    proceed()


def get_geom_iou(geom1: BaseGeometry, geom2: BaseGeometry):
    """
    计算两个多边形的交并比
    """
    intersection = geom1.intersection(geom2).area
    union = geom1.union(geom2).area
    if union == 0:
        return 0.0

    return intersection / union


@desc()
def load_records(ctx: Context, proceed):
    """
    加载所有冗余记录
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for face_id in tqdm(ctx.face_ids):
            row = pgsql.fetch_one(conn, GET_AOI_WKT_SQL, (face_id,))
            if row is None:
                continue

            aoi_wkt, = row
            current_aoi_geom = shapely.wkt.loads(aoi_wkt)
            overlap_records = []
            for overlap_face_id, overlap_wkt in pgsql.fetch_all(conn, GET_OVERLAP_AOI_SQL, (aoi_wkt, face_id)):
                overlap_geom = shapely.wkt.loads(overlap_wkt)
                iou = get_geom_iou(current_aoi_geom, overlap_geom)
                if iou > MAX_IOU:
                    overlap_records.append(OverlapRecord(
                        kind='overlap',
                        face_id=overlap_face_id,
                        geom=shapely.wkt.loads(overlap_wkt),
                        iou=iou,
                    ))

            if not any(overlap_records):
                continue

            ctx.add_record(Record(
                kind='current',
                face_id=face_id,
                geom=shapely.wkt.loads(aoi_wkt),
                overlap_records=overlap_records,
            ))

    proceed()


@desc()
def fill_aoi_properties(ctx: Context, proceed):
    """
    填充 aoi 属性
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process(record: Record):
            row = pgsql.fetch_one(conn, GET_AOI_PROPERTIES_SQL, (record.face_id,))
            if row is None:
                record.can_process = False
                record.reason = 'no aoi'
                return

            record.src, record.aoi_level, record.bid = row

        batch_process(ctx.records, process)
        batch_process(ctx.records, lambda record: batch_process(record.overlap_records, process, use_tqdm=False))

    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 poi 属性
    """
    ignored_tags = [
        '购物;购物中心',
        '购物;超市',
        '购物',
        '购物;百货商场',
    ]
    ignored_pois = []

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as conn:
        # 加载需要保护的bid
        protected_rows = conn.fetch_all(GET_PROTECT_AOI_SQL)
        for protected_row in protected_rows:
            ignored_pois.append(protected_row[0])
        print(f"==== 需要保护的bid: {ignored_pois}")

        def process(record: Record):
            # 需要保护的 bid
            if record.bid in ignored_pois:
                record.can_process = False
                record.reason = 'ignored bid'
                print(f"protected bid {record.bid} continue")
            row = conn.fetch_one(GET_POI_PROPERTIES_SQL, (record.bid,))
            if row is None:
                return

            record.tag, record.pv, record.name = row
            if record.tag in ignored_tags:
                record.can_process = False
                record.reason = 'ignored tag'

        batch_process(ctx.records, process)
        batch_process(ctx.records, lambda record: batch_process(record.overlap_records, process, use_tqdm=False))

    proceed()


@desc()
def judge_retention(ctx: Context, proceed):
    """
    判断是否保留
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_conn, \
            pgsql.get_connection(pgsql.AOI_RESUME_SLAVER_CONFIG) as resume_conn:
        def process(record: Record):
            pending_records = [x for x in (record.overlap_records + [record]) if x.can_process]
            remove_records = get_pending_remove_aoi(poi_conn, resume_conn, pending_records)

            for remove_record in remove_records:
                remove_record.remove = True

        batch_process(ctx.records, process)

    proceed()


@desc()
def save_debug_records(ctx: Context, proceed):
    """
    保存 debug 记录
    """
    output_items = []

    for record in ctx.records:
        output_records: list[OverlapRecord] = [record]
        output_records.extend(record.overlap_records)

        for output_record in output_records:
            output_items.append([
                output_record.kind,
                output_record.remove,
                output_record.face_id,
                output_record.bid,
                output_record.name,
                output_record.geom.wkt,
                output_record.iou,
                output_record.src,
                output_record.pv,
                output_record.tag,
                output_record.aoi_level,
                output_record.has_competitor,
                output_record.competitor_wkt,
                output_record.competitor_iou,
                output_record.last_auto_work_time,
                output_record.manual_worked,
                output_record.last_manual_work_time,
            ])

    tsv.write_tsv(ctx.work_dir / 'output_debug.csv', output_items)
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    output_items = []

    for record in ctx.records:
        output_records: list[OverlapRecord] = [record]
        output_records.extend(record.overlap_records)

        for output_record in output_records:
            if not output_record.remove:
                continue

            output_items.append([
                output_record.face_id,
            ])

    tsv.write_tsv(ctx.work_dir / 'output.csv', output_items)
    proceed()


@desc()
def remove_aoi(ctx: Context, proceed):
    """
    删除边框
    """
    for record in ctx.records:
        output_records: list[OverlapRecord] = [record]
        output_records.extend(record.overlap_records)

        for output_record in output_records:
            if not output_record.remove:
                continue

            ctx.pending_remove_face_ids.append(output_record.face_id)
            if output_record.src == 'SD':
                ctx.pending_remove_toB_count += 1
            else:
                ctx.pending_remove_toC_count += 1

    run(ctx.pending_remove_face_ids, '例行化删除冗余边框')
    proceed()


@desc()
def send_result_to_infoflow(ctx: Context, proceed):
    """
    发送结果到如流
    """
    if any(ctx.pending_remove_face_ids):
        notice_tool.send_hi(
            f'''通过自动化途径发现冗余边框，具体情况如下:
    总量：{len(ctx.pending_remove_face_ids)}
    C 端：{ctx.pending_remove_toC_count}
    B 端：{ctx.pending_remove_toB_count}
    请及时确认，谢谢。
    ''',
            atuserids=['chenjie02_cd', 'hexinyi_cd'],
            token='d83586c9c29feea30d4fbe3da7edc2669'
        )

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_face_ids,
        load_records,
        fill_aoi_properties,
        fill_poi_properties,
        judge_retention,
        save_records,
        remove_aoi,
        send_result_to_infoflow,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/recall_oversize_iou'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
