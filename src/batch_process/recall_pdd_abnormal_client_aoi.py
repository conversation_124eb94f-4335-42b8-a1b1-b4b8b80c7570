# !/usr/bin/env python3
"""
召回 PDD 数据集中的异常边框
"""
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process import tags
from src.tools import pipeline, pgsql, tsv

PDD_DB_CONFIG = {
    "host": "**************",
    "db": "pdd_sd",
    "port": "6432",
    "user": "pdd_sd_se_rw",
    "pwd": "iayydyva",
}

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    商单数据
    """
    face_id: str
    wkt: str
    strange_ratio: float = -1
    strange_ratio_quadratic: float = -1
    name: str = ''
    area: float = -1
    pv: float = -1
    tag: str = ''
    aoi_level: int = -1
    reason: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    records: dict[str, Record] = field(default_factory=dict)
    mesh_ids: list[str] = field(default_factory=list)


@desc()
def load_mesh_ids(ctx: Context, proceed):
    """
     加载 mesh_id
    """
    with pgsql.get_connection(PDD_DB_CONFIG) as conn:
        sql = '''
            select distinct mesh_id from blu_face;
        '''
        ctx.mesh_ids = [x[0] for x in pgsql.fetch_all(conn, sql)]

    proceed()


@desc()
def check_strange_ratio(ctx: Context, proceed):
    """
    计算边框畸形度
    """
    min_strange_ratio = 10
    min_strange_ratio_quadratic = 0.001

    with pgsql.get_connection(PDD_DB_CONFIG) as conn:
        for mesh_id in tqdm(ctx.mesh_ids):
            sql = '''
                select face_id, st_astext(geom) 
                from blu_face 
                where mesh_id = %s;
            '''
            for face_id, wkt, in pgsql.fetch_all(conn, sql, (mesh_id,)):
                geom = shapely.wkt.loads(wkt)
                strange_ratio = geom.area / geom.length
                strange_ratio_quadratic = strange_ratio / geom.length
                if strange_ratio < min_strange_ratio or strange_ratio_quadratic < min_strange_ratio_quadratic:
                    ctx.records[face_id] = Record(
                        face_id=face_id,
                        wkt=wkt,
                        strange_ratio=strange_ratio,
                        strange_ratio_quadratic=strange_ratio_quadratic,
                    )

    proceed()


@desc()
def check_overlap_tag(ctx: Context, proceed):
    """
    检查压盖标签
    """
    with (
        pgsql.get_connection(PDD_DB_CONFIG) as pdd_conn,
        pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn
    ):
        for mesh_id in tqdm(ctx.mesh_ids):
            sql = '''
                select face_id, kind, poi_bid, aoi_level, name_ch, st_astext(geom)
                from blu_face 
                where mesh_id = %s;
            '''
            for face_id, kind, poi_bid, aoi_level, name_ch, wkt, in pgsql.fetch_all(pdd_conn, sql, (mesh_id,)):
                geom = shapely.wkt.loads(wkt)

                sql = '''
                    select click_pv from poi where bid = %s;
                '''
                row = pgsql.fetch_one(poi_conn, sql, (poi_bid,))
                pv = 1 if not row else row[0]
                condition1 = False
                condition2 = False

                if kind not in tags.ACCURATE_PRIMARY_TAGS:
                    # 场景 1
                    if geom.area >= 4000000e-10:
                        if kind != '交通设施;飞机场' and kind != '教育培训;高等院校':
                            condition1 = True
                    elif geom.area >= 1000000e-10:
                        if kind == '房地产;写字楼' or kind == '购物;商铺':
                            condition1 = True
                    if condition1:
                        ctx.records[face_id] = Record(
                            face_id=face_id,
                            name=name_ch,
                            area=geom.area * 1e10,
                            aoi_level=aoi_level,
                            pv=pv,
                            tag=kind,
                            wkt=wkt,
                            reason='场景 1',
                        )
                else:
                    # 场景 2
                    if geom.area >= 4000000e-10 and pv < 100:
                        condition2 = True
                        ctx.records[face_id] = Record(
                            face_id=face_id,
                            name=name_ch,
                            area=geom.area * 1e10,
                            aoi_level=aoi_level,
                            pv=pv,
                            tag=kind,
                            wkt=wkt,
                            reason='场景 2',
                        )

                # 场景 3
                if not condition1 and not condition2:
                    sql = '''
                        select bid from poi where relation_bid = %s;
                    '''
                    for bid, in pgsql.fetch_all(poi_conn, sql, (poi_bid,)):
                        sql = '''
                            select st_area(a.geom)
                            from blu_face a
                            inner join blu_face_poi b
                            on a.face_id = b.face_id
                            where b.poi_bid = %s
                            limit 1;
                        '''
                        row = pgsql.fetch_one(aoi_conn, sql, (bid,))
                        if not row:
                            continue
                        area, = row
                        if area > geom.area:
                            ctx.records[face_id] = Record(
                                face_id=face_id,
                                name=name_ch,
                                area=geom.area * 1e10,
                                aoi_level=aoi_level,
                                pv=pv,
                                tag=kind,
                                wkt=wkt,
                                reason='场景 3',
                            )

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存结果
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.tsv',
        [
            [
                x.face_id,
                x.wkt,
                x.strange_ratio,
            ]
            for x in ctx.records.values()
        ]
    )

    proceed()


@desc()
def save_records_detail(ctx: Context, proceed):
    """
    保存详细结果
    """
    tsv.write_tsv(
        ctx.work_dir / 'output_detail.tsv',
        [
            [
                x.reason,
                x.face_id,
                x.name,
                x.area,
                x.pv,
                x.tag,
                x.aoi_level,
                x.wkt,
            ]
            for x in ctx.records.values()
        ]
    )

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_mesh_ids,
        check_strange_ratio,
        save_records,
        check_overlap_tag,
        save_records,
        save_records_detail,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/recall_abnormal_client_aoi'),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main()
