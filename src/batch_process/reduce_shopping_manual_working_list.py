# !/usr/bin/env python3
"""
购物中心人工作业清单减量
"""
import argparse
import json
import math
from dataclasses import dataclass, field
from pathlib import Path

import numpy as np
import shapely.wkt
from shapely import Point, Polygon, MultiPoint
from sklearn.cluster import DBSCAN
from tqdm import tqdm
import rtree

from src.batch_process.batch_helper import get_mysql_connection, batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv
from src.tools.track_provider import get_provider

IGNORED_KIND_BATCHES = [
    # '20240702001',
    # '20240618001',
    # '2024060402_test',
]
BATCHES = [
    'build_gate_2024_07_10_01',
    '建筑门创建_072502.json',
    # '建筑门人工作业数据_正式_20240924',
    '建筑门人工作业数据_正式_20240918',
    # '建筑门人工作业数据_正式_20240912',

    # '建筑门创建_0813',
    # '建筑门创建_0816',
    # '建筑门创建_0821',
    # '建筑门创建_0830',
    # '建筑门创建_0904',
]
SHOPPING_LIST_PATH = '/home/<USER>/chenjie/shopping_list.csv'
COMPETITOR_INTELLIGENCE_PATH = '/home/<USER>/chenjie/data/building_access.csv'

desc = pipeline.get_desc()


@dataclass
class Intelligence:
    """
    表示一份情报
    """
    src: str
    geom: Point
    cluster_num: int = 0
    can_process: bool = True
    reason: str = ''


@dataclass
class Record:
    """
    批处理记录
    """
    bid: str
    aoi_level: int
    src: str
    geom: Polygon
    area: float
    name: str = ''
    tag: str = ''
    pv: int = -1
    work_status: int = 0
    batch: str = ''
    work_record_id: int = 0
    has_access: bool = False
    access_count: int = 0
    old_intelligences: list[Intelligence] = field(default_factory=list)
    current_intelligences: list[Intelligence] = field(default_factory=list)
    can_process: bool = True
    reason: str = ''

    @property
    def in_storage(self):
        return self.work_status == 3


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    bid_list_path: Path
    invalid_bids: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理记录
    """
    sql = '''
        select a.aoi_level, a.src, st_astext(a.geom), a.area
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s and
              a.aoi_level > 1;
    '''

    with (
        open(ctx.bid_list_path, 'r', encoding='utf-8') as f,
        PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stab,
    ):
        bids = set(f.read().splitlines())
        for bid in tqdm(bids):
            row = back_stab.fetch_one(sql, (bid,))
            if row is None:
                ctx.invalid_bids.append(bid)
                continue

            aoi_level, src, wkt, area = row
            ctx.records.append(Record(
                bid=bid,
                aoi_level=aoi_level,
                src=src,
                geom=shapely.wkt.loads(wkt),
                area=area,
            ))

    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 POI 属性
    """
    sql = '''
        select name, std_tag, click_pv from poi where bid=%s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm(ctx.records):
            row = poi_stab.fetch_one(sql, (record.bid,))
            if row is None:
                continue

            record.name, record.tag, record.pv = row

    proceed()


@desc()
def fill_work_info(ctx: Context, proceed):
    """
    填充作业资料信息
    """
    sql = '''
        select id, bid, work_status
        from strategy_feature_list 
        where batch_id=%s;
    '''

    with (
        get_mysql_connection('beeflow') as conn,
        conn.cursor() as cursor,
    ):
        work_records = dict()
        for batch in tqdm(BATCHES + IGNORED_KIND_BATCHES):
            cursor.execute(sql, (batch,))
            for record_id, bid, work_status in cursor.fetchall():
                work_records[bid] = (record_id, work_status, batch)

        print(len(work_records))

    for record in ctx.records:
        work_record = work_records.get(record.bid, None)
        if work_record is not None:
            record.work_record_id, record.work_status, record.batch = work_record
        else:
            record.can_process = False
            record.reason = '不在目标批次中'

    proceed()


@desc()
def find_access(ctx: Context, proceed):
    """
    查找关联出入口
    """
    valid_kinds = [5, 7]
    sql = '''
        select kind
        from blu_access
        where main_bid=%s;
    '''

    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stab:
        def process(record: Record):
            kinds = [int(x[0]) for x in back_stab.fetch_all(sql, (record.bid,))]

            if record.batch in IGNORED_KIND_BATCHES:
                record.has_access = True
                record.access_count = len(kinds)
            else:
                filtered_kinds = [kind for kind in kinds if kind in valid_kinds]
                record.has_access = any(filtered_kinds)
                record.access_count = len(filtered_kinds)

        batch_process(ctx.records, process)

    proceed()


@desc()
def fill_old_intelligences(ctx: Context, proceed):
    """
    加载历史情报数据
    """
    sql = '''
        select extra_data from strategy_feature_list where id=%s;
    '''

    with (
        get_mysql_connection('beeflow') as conn,
        conn.cursor() as cursor,
    ):
        def process(record: Record):
            cursor.execute(sql, (record.work_record_id,))
            row = cursor.fetchone()
            if row is None:
                return

            extra_data, = row
            extra_data_items = json.loads(extra_data)
            for extra_data_item in extra_data_items:
                src = extra_data_item.get('building_gate_src', None)
                if src is None:
                    continue

                wkt = extra_data_item.get('building_gate_wkt', None)
                wkt = extra_data_item.get('geom', None) if wkt is None else wkt

                record.old_intelligences.append(Intelligence(
                    src=extra_data_item['building_gate_src'],
                    geom=shapely.wkt.loads(wkt),
                    cluster_num=-1,
                ))

        batch_process(ctx.records, process)

    proceed()


@desc()
def fill_competitor_intelligences(ctx: Context, proceed):
    """
    填充竞品情报
    """
    competitor_src = '竞品建筑物门'

    with open(COMPETITOR_INTELLIGENCE_PATH, 'r', encoding='utf-8') as f:
        intelligence_map = {}
        for line in tqdm(f.read().splitlines()):
            bid, wkt = tuple(line.split('\t'))
            intelligences = intelligence_map.get(bid, [])
            intelligences.append((wkt, competitor_src))
            intelligence_map[bid] = intelligences

    def process(record: Record):
        for intelligence in intelligence_map.get(record.bid, []):
            intelligence_wkt, intelligence_src = intelligence
            record.current_intelligences.append(Intelligence(
                src=intelligence_src,
                geom=shapely.wkt.loads(intelligence_wkt),
                cluster_num=-1,
            ))

    batch_process(ctx.records, process)
    proceed()


@desc()
def fill_current_intelligences(ctx: Context, proceed):
    """
    填充最新情报数据
    """
    with open(SHOPPING_LIST_PATH, 'r', encoding='utf-8') as f:
        intelligence_map = {}
        for line in tqdm(f.read().splitlines()):
            bid, src, name, wkt, access_id, cluster_num = tuple(line.split('\t'))
            intelligences = intelligence_map.get(bid, [])
            intelligences.append((wkt, src))
            intelligence_map[bid] = intelligences

    def process(record: Record):
        for intelligence in intelligence_map.get(record.bid, []):
            intelligence_wkt, intelligence_src = intelligence
            record.current_intelligences.append(Intelligence(
                src=intelligence_src,
                geom=shapely.wkt.loads(intelligence_wkt),
                # cluster_num=int(float(intelligence_cluster_num)) if intelligence_cluster_num != '' else -1,
            ))

            if not any(record.current_intelligences):
                record.reason = '无情报'

    batch_process(ctx.records, process)
    proceed()


def calculate_angle(v1, v2):
    """
    计算两个向量之间的夹角（以角度为单位）
    """
    dot_product = np.dot(v1, v2)
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)

    # 通过余弦定理计算夹角
    cos_theta = dot_product / (norm_v1 * norm_v2)
    cos_theta = np.clip(cos_theta, -1.0, 1.0)  # 确保余弦值在 [-1, 1] 范围内
    angle_radians = np.arccos(cos_theta)

    # 将弧度转换为角度
    angle_degrees = np.degrees(angle_radians)
    return angle_degrees


def get_tangent_direction_at_point(geometry, point, buffer_dist=1e-6):
    """
    获取线在某个点附近的切线方向
    """
    # 找到点在几何对象上的投影位置（从起点开始的距离）
    proj_distance = geometry.project(point)

    # 在投影位置稍微前后取一个小的 buffer 区域来获取相邻的两个点
    before_point = geometry.interpolate(proj_distance - buffer_dist)
    after_point = geometry.interpolate(proj_distance + buffer_dist)

    # 返回切线方向的向量
    tangent_vector = np.array(after_point.coords[0]) - np.array(before_point.coords[0])
    return tangent_vector


def get_normal_direction(tangent_vector):
    """
    获取一个向量的法线方向（顺时针90度旋转）
    """
    # 旋转90度：将 (x, y) 变为 (-y, x) 或 (y, -x) 可以获得垂直方向
    normal_vector = np.array([-tangent_vector[1], tangent_vector[0]])
    return normal_vector


def get_intersections_with_small_normal_angles(polygon, linestring, threshold_angle=30):
    """
    获取多边形边界和线相交的点，筛选出与法线夹角小于 threshold_angle 的点
    """
    # 计算 Polygon 边界与 LineString 的交点
    intersection_points = polygon.boundary.intersection(linestring)
    small_angle_points = []

    # 如果没有交点，直接返回空列表
    if intersection_points.is_empty:
        return []

    # 将交点处理为列表，支持 Point 和 MultiPoint 等情况
    if isinstance(intersection_points, Point):
        intersection_points = [intersection_points]
    elif hasattr(intersection_points, "geoms"):
        intersection_points = list(intersection_points.geoms)

    # 对每个交点进行处理
    for point in intersection_points:
        # 计算 LineString 在交点处的切线方向
        line_tangent = get_tangent_direction_at_point(linestring, point)

        # 计算 Polygon 边界在交点处的切线方向
        polygon_tangent = get_tangent_direction_at_point(polygon.boundary, point)

        # 计算 Polygon 边界的法线方向（垂线方向）
        polygon_normal = get_normal_direction(polygon_tangent)

        # 计算 LineString 的切线方向与 Polygon 法线方向的夹角
        angle = calculate_angle(line_tangent, polygon_normal)

        # 只保留夹角绝对值小于阈值的交点
        if abs(angle) < threshold_angle:
            small_angle_points.append(point)

    return small_angle_points


def cluster_points(points):
    """
    对点集进行聚类
    """
    points_arr = np.array([point.coords[0] for point in points])

    # noinspection PyUnresolvedReferences
    labels = DBSCAN(eps=10e-5, min_samples=5).fit(points_arr).labels_
    clustered_points = {}
    for label, point in zip(labels, points):
        if label == -1:
            continue
        points_group = clustered_points.get(label, [])
        points_group.append(point)
        clustered_points[label] = points_group

    return clustered_points.values()

    # 选择聚类中最多的点集
    # max_count = 0
    # max_count_label = -1
    # if not any(clustered_points):
    #     return []
    #
    # for key in clustered_points.keys():
    #     count = len(clustered_points[key])
    #     if count > max_count:
    #         max_count_label = key
    #         max_count = count
    #
    # return clustered_points[max_count_label]


@desc()
def fill_track_intelligences(ctx: Context, proceed):
    """
    填充轨迹相关的情报信息
    """
    with get_provider('dest') as track_provider:
        def process(record: Record):
            intersection_points = []

            for track in track_provider.get_tracks(record.bid, 'bid', [])[:1000]:
                track_geom = shapely.wkt.loads(track['geom'])
                points = get_intersections_with_small_normal_angles(record.geom, track_geom, threshold_angle=45)
                intersection_points.extend(points)

            if not any(intersection_points):
                return

            clustered_groups = cluster_points(intersection_points)
            for clustered_points in clustered_groups:
                center = MultiPoint(clustered_points).centroid
                record.current_intelligences.append(Intelligence(
                    src='终点轨迹聚合',
                    geom=center,
                    cluster_num=len(clustered_points),
                ))

        batch_process(ctx.records, process)

    proceed()


def filter_too_close_to_each_other_intelligences(record: Record):
    """
    过滤掉距离太近的情报
    """
    competitor_src = '竞品建筑物门'
    access_src = '出入口数据'
    indoor_src = '智能空间POI数据'
    track_src = '终点轨迹聚合'
    max_distance = 50e-5

    # competitor_intelligences = [x for x in record.current_intelligences if x.src == competitor_src and x.can_process]
    # our_intelligences = [x for x in record.current_intelligences if x.src != competitor_src and x.can_process]
    track_intelligences = [x for x in record.current_intelligences if x.src == track_src and x.can_process]
    other_intelligences = [x for x in record.current_intelligences if x.src != track_src and x.can_process]
    access_intelligences = [x for x in record.current_intelligences if x.src == access_src and x.can_process]
    indoor_intelligences = [x for x in record.current_intelligences if x.src == indoor_src and x.can_process]

    # # 优先保留竞品
    # for competitor_intelligence in competitor_intelligences:
    #     for our_intelligence in our_intelligences:
    #         if competitor_intelligence.geom.distance(our_intelligence.geom) <= max_distance:
    #             our_intelligence.can_process = False
    #             our_intelligence.reason = 'too close to competitor intelligence'

    # 优先保留轨迹
    for competitor_intelligence in track_intelligences:
        for our_intelligence in other_intelligences:
            if competitor_intelligence.geom.distance(our_intelligence.geom) <= max_distance:
                our_intelligence.can_process = False
                our_intelligence.reason = 'too close to track intelligence'

    # 其次保留出入口
    for access_intelligence in access_intelligences:
        if access_intelligence.can_process:
            continue
        for indoor_intelligence in indoor_intelligences:
            if indoor_intelligence.can_process:
                continue
            if access_intelligence.geom.distance(indoor_intelligence.geom) > max_distance:
                indoor_intelligence.can_process = False
                indoor_intelligence.reason = 'too close to intelligence access'


def distance_based_simplify_multipoint(multipoint, distance_threshold):
    """
    简化多点，只保留距离大于阈值的点。
    """
    index = rtree.index.Index(interleaved=False)
    geoms = list(multipoint.geoms)

    for i, point in enumerate(geoms):
        min_x, min_y, max_x, max_y = point.bounds
        index.insert(i, (min_x, max_x, min_y, max_y))

    ignored_ids = set()
    for i, point in enumerate(geoms):
        if i in ignored_ids:
            continue

        min_x, min_y, max_x, max_y = point.buffer(distance_threshold).bounds
        intersection_ids = index.intersection((min_x, max_x, min_y, max_y))
        for intersection_id in intersection_ids:
            if intersection_id != i and geoms[intersection_id].distance(point) < distance_threshold:
                ignored_ids.add(intersection_id)

    return MultiPoint([x for i, x in enumerate(geoms) if i not in ignored_ids])


def simplify_intelligences(record: Record):
    """
    对情报进行抽稀
    """
    max_distance = 50e-5
    access_src = '出入口数据'
    intelligence_map = {}

    valid_intelligences = [x for x in record.current_intelligences if x.src != access_src and x.can_process]
    for intelligence in valid_intelligences:
        intelligence_map[intelligence.geom.wkt] = intelligence
    valid_geoms = [x.geom for x in valid_intelligences]
    multipoint = shapely.geometry.MultiPoint(valid_geoms)
    simplified_geom = distance_based_simplify_multipoint(multipoint, max_distance)

    retained_geoms = set()
    if simplified_geom.geom_type == 'MultiPoint':
        for geom in simplified_geom.geoms:
            retained_geoms.add(geom.wkt)
    else:
        retained_geoms.add(simplified_geom.wkt)

    for wkt, intelligence in intelligence_map.items():
        if wkt not in retained_geoms:
            intelligence.can_process = False
            intelligence.reason = 'too close to other intelligence'


def filter_too_far_away_from_aoi_intelligences(record: Record):
    """
    过滤掉距离 AOI 太远的情报
    """
    access_src = '出入口数据'
    indoor_src = '智能空间POI数据'
    max_distance = 50e-5

    for intelligence in record.current_intelligences:
        if not intelligence.can_process:
            continue

        if intelligence.src == access_src or intelligence.src == indoor_src:
            continue

        if record.geom.distance(intelligence.geom) > max_distance:
            intelligence.can_process = False
            intelligence.reason = 'too far away from aoi'


def filter_too_close_to_old_intelligences(record: Record):
    """
    过滤掉距离旧情报太近的情报
    """
    min_distance = 10e-5

    for current_intelligence in record.current_intelligences:
        if not current_intelligence.can_process:
            continue

        for old_intelligence in record.old_intelligences:
            if current_intelligence.geom.distance(old_intelligence.geom) < min_distance:
                current_intelligence.can_process = False
                current_intelligence.reason = 'too close to old intelligence'


def filter_too_close_to_access_intelligences(back_stab, record: Record):
    """
    过滤掉距离出入口太近的情报
    """
    min_distance = 50e-5
    valid_kinds = [5, 7]
    sql = '''
        select kind, st_astext(geom)
        from blu_access
        where main_bid = %s;
    '''

    for intelligence in record.current_intelligences:
        if not intelligence.can_process:
            continue

        for kind, wkt in back_stab.fetch_all(sql, (record.bid,)):
            geom = shapely.wkt.loads(wkt)
            distance = geom.distance(intelligence.geom)
            if distance < min_distance and (record.batch in IGNORED_KIND_BATCHES or int(kind) in valid_kinds):
                intelligence.can_process = False
                intelligence.reason = 'too close to back access'


def filter_too_low_cluster_num_updown_intelligences(record: Record):
    """
    过滤掉聚类数量太低的上下车点情报
    """
    updown_src = '上下车点'
    min_cluster_num_ratio = 0.1
    min_cluster_num = 3

    updown_intelligences = [x for x in record.current_intelligences if x.src == updown_src and x.can_process]
    if not any(updown_intelligences):
        return

    max_cluster_num = max(int(x.cluster_num) for x in updown_intelligences)
    threshold_cluster_num = max(max_cluster_num * min_cluster_num_ratio, min_cluster_num)

    for intelligence in updown_intelligences:
        if intelligence.cluster_num < threshold_cluster_num:
            intelligence.can_process = False
            intelligence.reason = f'cluster num too low'


def filter_too_low_cluster_num_track_intelligences(record: Record):
    """
    过滤掉聚类数量太低的轨迹点情报
    """
    track_src = '情报点数据'
    min_cluster_num_ratio = 0.1
    min_cluster_num = 1

    track_intelligences = [x for x in record.current_intelligences if x.src == track_src and x.can_process]
    if not any(track_intelligences):
        return

    max_cluster_num = max(int(x.cluster_num) for x in track_intelligences)
    threshold_cluster_num = max(max_cluster_num * min_cluster_num_ratio, min_cluster_num)

    for intelligence in track_intelligences:
        if intelligence.cluster_num < threshold_cluster_num:
            intelligence.can_process = False
            intelligence.reason = f'cluster num too low'


def filter_access_enough_intelligences(record: Record):
    """
    过滤掉出入口量级足够的情报
    """
    ratio = 10000

    desired_access_count = math.ceil(record.area / ratio)
    if record.access_count < desired_access_count:
        return

    for intelligence in record.current_intelligences:
        if not intelligence.can_process:
            continue

        intelligence.can_process = False
        intelligence.reason = f'access count is enough'


@desc()
def filter_current_intelligences(ctx: Context, proceed):
    """
    对最新情报进行过滤
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stab:
        def process(record: Record):
            filter_too_close_to_each_other_intelligences(record)
            simplify_intelligences(record)
            filter_too_far_away_from_aoi_intelligences(record)
            filter_too_close_to_old_intelligences(record)
            filter_too_close_to_access_intelligences(back_stab, record)
            filter_too_low_cluster_num_updown_intelligences(record)
            filter_too_low_cluster_num_track_intelligences(record)
            filter_access_enough_intelligences(record)

        batch_process(ctx.records, process)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存结果
    """
    output_items = []

    for record in ctx.records:
        if any(record.current_intelligences):
            for intelligence in record.current_intelligences:
                output_items.append([
                    record.bid,
                    record.name,
                    record.tag,
                    record.pv,
                    record.aoi_level,
                    record.src,
                    record.in_storage,
                    record.has_access,
                    record.access_count,
                    record.batch in IGNORED_KIND_BATCHES,
                    intelligence.src,
                    intelligence.cluster_num,
                    intelligence.geom.wkt,
                    intelligence.reason,
                ])
        else:
            output_items.append([
                record.bid,
                record.name,
                record.tag,
                record.pv,
                record.aoi_level,
                record.src,
                record.in_storage,
                record.has_access,
                record.access_count,
                record.batch in IGNORED_KIND_BATCHES,
                '',
                '',
                '',
                record.reason,
            ])

    tsv.write_tsv(ctx.work_dir / 'output.csv', output_items)
    tsv.write_tsv(ctx.work_dir / 'invalid_bids.csv', [*ctx.invalid_bids])
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_poi_properties,
        fill_work_info,
        find_access,
        fill_old_intelligences,
        # 竞品暂时不使用
        # fill_competitor_intelligences,
        # fill_track_intelligences,
        fill_current_intelligences,
        filter_current_intelligences,
        save_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/reduce_shopping_manual_working_list'),
        bid_list_path=Path(args.bid_list_path),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
