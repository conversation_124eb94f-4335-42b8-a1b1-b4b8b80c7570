# !/usr/bin/env python3
"""
批量删除 AOI
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.batch_helper import batch_process
from src.batch_process.flow_process_aoi import delete_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, tsv, pgsql

desc = pipeline.get_desc()


@dataclass
class BatchRemoveAoiRecord:
    """
    批量删除 AOI 记录
    """
    face_id: str = ''
    mesh_id: str = ''
    bid: str = ''
    description: str = ''
    immediately_execute: bool = True
    src: str = ''
    tag: str = ''
    can_process: bool = True


@dataclass
class Context:
    """
    批处理上下文
    """
    memo: str
    work_dir: Path
    immediately_execute: bool
    face_ids: list[str] = field(default_factory=list)
    bids: list[str] = field(default_factory=list)
    records: list[BatchRemoveAoiRecord] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    ctx.records = [
        BatchRemoveAoiRecord(
            face_id=face_id,
            description=ctx.memo,
            immediately_execute=ctx.immediately_execute,
        )
        for face_id in ctx.face_ids
    ]
    proceed()


@desc()
def fill_aoi_properties(ctx: Context, proceed):
    """
    填充 AOI 属性
    """
    sql = '''
        select a.mesh_id, a.src, b.poi_bid
        from blu_face a
        left join blu_face_poi b
        on a.face_id = b.face_id
        where a.face_id = %s;
    '''

    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
        def process(record: BatchRemoveAoiRecord):
            row = back_stabilizer.fetch_one(sql, (record.face_id,))
            if not row:
                record.reason = 'no face'
                record.can_process = False
                return

            record.mesh_id, record.src, record.bid = row
            if record.bid is None:
                record.bid = ''

        batch_process(ctx.records, process)

    print(len([x for x in ctx.records if x.can_process]))
    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 POI 属性
    """
    sql = '''select std_tag from poi where bid = %s;'''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stabilizer:
        def process(record: BatchRemoveAoiRecord):
            if record.bid == '':
                return

            row = poi_stabilizer.fetch_one(sql, (record.bid,))
            if not row:
                return

            record.tag, = row

        batch_process(ctx.records, process)

    proceed()


@desc()
def filter_records(ctx: Context, proceed):
    """
    过滤记录
    """
    business_tag = '行政地标;商圈'
    business_src = 'SQ'

    def process(record: BatchRemoveAoiRecord):
        if record.tag == business_tag and record.src == business_src:
            record.can_process = False
            record.reason = '商圈'

    batch_process(ctx.records, process)
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.face_id,
            ]
            for x in ctx.records if x.can_process
        ]
    )
    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
        for record in tqdm(ctx.records):
            if not record.can_process:
                continue

            result = delete_aoi(back_stabilizer, record.face_id)
            if not result.success:
                print(result.msg)

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--face-list-path',
        dest='face_list_path',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--memo',
        dest='memo',
        type=str,
        default='删除 AOI',
        required=True,
    )
    parser.add_argument(
        '--immediately_execute',
        dest='immediately_execute',
        help='是否立即执行批处理。',
        default=True,
        action='store_true',
    )
    return parser.parse_args()


def run(face_ids, memo, immediately_execute=True):
    """
    执行批量删除 AOI
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_aoi_properties,
        fill_poi_properties,
        filter_records,
        save_records,
        execute_batch,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/remove_aoi'),
        face_ids=face_ids,
        memo=memo,
        immediately_execute=immediately_execute,
    )
    main_pipe(ctx)


def main(args):
    """
    主函数
    """
    with open(Path(args.face_list_path)) as f:
        face_ids = [x.strip() for x in f.readlines()]

    run(
        face_ids=face_ids,
        memo=args.memo,
        immediately_execute=args.immediately_execute,
    )


if __name__ == '__main__':
    main(parse_args())
