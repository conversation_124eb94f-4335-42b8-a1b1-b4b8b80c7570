"""
删除tag错误的边框
"""
import os
import sys
from pathlib import Path

root_path = Path(os.path.abspath(__file__)).parents[2]
sys.path.insert(0, root_path.as_posix())
import pandas as pd
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.batch_process.flow_process_aoi import update_src_of_aoi
from src.tools import pgsql

INVALID_STD_TAG = [
    '交通设施;桥',
    '其他线要素;桥梁',
]


def run():
    """
    无效tag类型的任务例行删除
    :return:
    """
    lines_all = []
    with PgsqlStabilizer(config=pgsql.BACK_CONFIG) as db:
        sql = 'select b.poi_bid, c.std_tag, st_astext(a.geom) as geom, a.src, a.face_id ' \
              ' from blu_face a' \
              ' inner join blu_face_poi b on a.face_id=b.face_id' \
              ' inner join poi_std_tag c on b.poi_bid=c.bid ' \
              ' where c.std_tag in %(std_tag)s and a.src !=%(src)s'
        ret = db.fetch_all(sql, {'std_tag': tuple(INVALID_STD_TAG), 'src': 'SD'})
        for (poi_bid, std_tag, geom, src, face_id) in ret:
            lines_all.append({
                'poi_bid': poi_bid,
                'std_tag': std_tag,
                'src': src,
                'geom': geom,
                'face_id': face_id,
            })
            # 删除aoi
            print(f'delete aoi: {face_id}')
            update_src_of_aoi(db, face_id, 'SD')
    pd.DataFrame(lines_all).to_csv('./invalid_std_tag.csv', index=False, sep='\t')


if __name__ == '__main__':
    run()
