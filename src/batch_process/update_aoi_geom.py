# !/usr/bin/env python3
"""
用于批量更新 AOI 形状
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.batch_helper import batch_process
from src.batch_process.flow_process_aoi import update_geom_of_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql

desc = pipeline.get_desc()


@dataclass
class BatchAoiGeomRecord:
    """
    批处理记录
    """
    face_id: str = ''
    wkt: str = ''
    mesh_id: str = ''
    bid: str = ''
    can_process: bool = True


@dataclass
class Context:
    """
    上下文
    """
    work_dir: Path
    bid_list_path: Path
    records: list[BatchAoiGeomRecord] = field(default_factory=list)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    with open(ctx.bid_list_path) as f:
        for line in tqdm(f.readlines()):
            face_id, wkt = tuple(line.strip().split('\t'))
            ctx.records.append(BatchAoiGeomRecord(
                face_id=face_id,
                wkt=wkt,
            ))

    proceed()


@desc()
def fill_properties(ctx: Context, proceed):
    """
    填充属性
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process(record: BatchAoiGeomRecord):
            sql = f'''
                select mesh_id from blu_face where face_id = %s;
            '''
            row = pgsql.fetch_one(conn, sql, (record.face_id,))
            if not row:
                record.reason = 'no face'
                record.can_process = False
                return

            record.mesh_id, = row

        batch_process(ctx.records, process)

    print(len([x for x in ctx.records if x.can_process]))
    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
        for record in tqdm(ctx.records):
            if not record.can_process:
                continue

            result = update_geom_of_aoi(back_stabilizer, record.face_id, record.wkt)
            if not result.success:
                print(result.msg)

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser('update aoi geom')
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        required=True,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_properties,
        execute_batch,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/update_aoi_geom'),
        bid_list_path=Path(args.bid_list_path),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
