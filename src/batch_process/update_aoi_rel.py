# !/usr/bin/env python3
"""
更新 AOI 边框的层级关系。
"""
import argparse
import json
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.tools import pipeline, pgsql

desc = pipeline.get_desc()


@dataclass
class BatchRecord:
    """
    批处理记录
    """
    bid1: str
    bid2: str
    face_id1: str = ''
    face_id2: str = ''
    mesh_id: str = ''
    reason: str = ''
    can_update: bool = True

    def get_batch_info(self):
        """
        获取批处理信息
        """
        return {
            'bid': self.bid1,
            'face_id': self.face_id1,
            'action': 'update_aoi_rel',
            'imp_state': 0,
            'src': '基于空间关系自动关联',
            'flow_state': '',
            'mesh_id': self.mesh_id,
            'resource': 6,
            'pri': 9000,
            'extra': json.dumps(
                {
                    'face_id1': self.face_id1,
                    'face_id2': self.face_id2,
                }
            )
        }


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    bid_list_path: Path = Path()
    records: list[BatchRecord] = field(default_factory=list)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理记录
    """
    with open(ctx.bid_list_path) as f:
        for line in [x.strip().split('\t') for x in f.readlines()]:
            bid1 = line[0]
            bid2 = line[1]

            ctx.records.append(BatchRecord(bid1, bid2))

    proceed()


@desc()
def fill_properties(ctx: Context, proceed):
    """
    填充属性
    """
    sql = f'''
        select a.face_id, a.mesh_id
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s
        limit 1;
    '''

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        for record in tqdm(ctx.records):
            if not record.can_update:
                continue
            row = pgsql.fetch_one(conn, sql, (record.bid1,))
            if not row:
                record.reason = 'no face1'
                record.can_update = False
                return

            record.face_id1, record.mesh_id = row

            row = pgsql.fetch_one(conn, sql, (record.bid2,))
            if not row:
                record.reason = 'no face2'
                record.can_update = False
                return

            record.face_id2, _ = row

    print(len([x for x in ctx.records if x.can_update]))
    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
    ):
        try:
            for record in tqdm(ctx.records):
                if not record.can_update:
                    continue

                batch_info = record.get_batch_info()

                sql = (
                    "insert into gate_bid_change_history(%s)" % ','.join(batch_info.keys()) +
                    "values (%s)" % ','.join(len(batch_info) * ['%s'])
                )
                cursor.execute(sql, (*batch_info.values(),))

            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser('update aoi rel')
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        required=True,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_properties,
        execute_batch,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/update_aoi_rel'),
        bid_list_path=Path(args.bid_list_path),
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
