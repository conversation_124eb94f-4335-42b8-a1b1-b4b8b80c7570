# !/usr/bin/env python3
"""
更新 AOI 边框的 SRC 字段。
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.batch_helper import batch_process
from src.batch_process.flow_process_aoi import update_src_of_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv

desc = pipeline.get_desc()


@dataclass
class BatchAoiSrcRecord:
    """
    批处理记录
    """
    bid: str = ''
    src: str = ''
    description: str = ''
    face_id: str = ''
    mesh_id: str = ''
    mid: str = ''
    can_process: bool = True


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    memo: str
    bid_list_path: Path = Path()
    desired_src: str = ''
    records: list[BatchAoiSrcRecord] = field(default_factory=list)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    with open(ctx.bid_list_path) as f:
        ctx.records = [
            BatchAoiSrcRecord(bid=x.strip(), description=ctx.memo, src=ctx.desired_src)
            for x in f.readlines()
        ]

    proceed()


@desc()
def fill_aoi_properties(ctx: Context, proceed):
    """
    填充 aoi 属性
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        def process(record: BatchAoiSrcRecord):
            sql = f'''
                select a.face_id, a.mesh_id
                from blu_face a
                inner join blu_face_poi b
                on a.face_id = b.face_id
                where b.poi_bid = %s
                limit 1;
            '''
            row = pgsql.fetch_one(conn, sql, (record.bid,))
            if not row:
                record.reason = 'no face'
                record.can_process = False
                return

            record.face_id, record.mesh_id = row

        batch_process(ctx.records, process)

    print(len([x for x in ctx.records if x.can_process]))
    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 poi 属性
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        def process(record: BatchAoiSrcRecord):
            sql = '''
                select mid from poi where bid = %s;
            '''
            row = pgsql.fetch_one(conn, sql, (record.bid,))
            if not row:
                record.reason = 'no poi'
                record.can_process = False
                return

            record.mid, = row

        batch_process(ctx.records, process)

    print(len([x for x in ctx.records if x.can_process]))
    proceed()


@desc()
def filter_records_by_tag(ctx: Context, proceed):
    """
    通过 tag 过滤记录
    """
    if ctx.desired_src == 'SD':
        proceed()
        return

    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        def process(record: BatchAoiSrcRecord):
            sql = '''
                select std_tag from poi where bid = %s;
            '''
            row = pgsql.fetch_one(conn, sql, (record.bid,))
            if not row:
                record.reason = 'no poi'
                record.can_process = False
                return

            std_tag, = row
            if std_tag in tags.ADMIN:
                record.reason = 'poi is admin'
                record.can_process = False

        batch_process(ctx.records, process)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.bid,
                x.src,
            ]
            for x in ctx.records if x.can_process
        ]
    )

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
        for record in tqdm(ctx.records):
            if not record.can_process:
                continue

            result = update_src_of_aoi(back_stabilizer, record.face_id, record.src)
            if not result.success:
                print(result.msg)

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser('update aoi src')
    parser.add_argument(
        '--bid-list-path',
        dest='bid_list_path',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--src',
        dest='src',
        type=str,
        default='CD',
        required=False,
    )
    parser.add_argument(
        '--memo',
        dest='memo',
        type=str,
        default='AOI B 转 C',
        required=True,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_aoi_properties,
        fill_poi_properties,
        filter_records_by_tag,
        save_records,
        execute_batch,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('/home/<USER>/chenjie/update_aoi_src'),
        bid_list_path=Path(args.bid_list_path),
        desired_src=args.src,
        memo=args.memo,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
