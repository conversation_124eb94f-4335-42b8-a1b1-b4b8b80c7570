# !/usr/bin/env python3
"""
建筑物自动识别数据集产出
"""
import argparse
import glob
import json
import logging
import os
import random
import shutil
import subprocess
import sys
import uuid
from pathlib import Path
from typing import Union

from dataclasses import dataclass, field

from PIL import Image
import shapely.wkt

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

import src.model_mysql.beeflow_model as beeflow_model
import src.model.poi_online as poi_model
import src.tools.function as F
import src.tools.afs_tool as afs_tool
import src.dataset.building_dataset as building_dataset
import src.build.img_cropper as img_cropper
import src.tools.redis_tool as rt
import src.tools.locker as locker
from src.tools.utils import get_newest_subdir_name

# 预览计划状态
from src.tools import utils

PRE_PLAN_STATUS_INIT = 'INIT'
PRE_PLAN_STATUS_PROGRESSING = 'PROGRESSING'
PRE_PLAN_STATUS_STREET_SPLIT_BEGIN = 'STREET_SPLIT'
PRE_PLAN_STATUS_STREET_SPLIT_END = 'TILE_MERGED'
PRE_PLAN_STATUS_FAILED = 'FAILED'
PRE_PLAN_STATUS_END = 'END'

DATASET_CACHE_DIR = "/home/<USER>/cache"

MODEL_TYPE_VIS = "vis_v1"

BASE_DIR = Path("/home/<USER>/aoiMl/build/")
AFS_HOST = "afs://aries.afs.baidu.com:9902"
UPLOAD_AFS_PATH = Path("/user/map-data-streeview/aoi-ml/build/tile_map")
bf_model = beeflow_model.BFQuery()
poi_model = poi_model.PoiOnlineModel()
afs = afs_tool.AfsTool()

DATASET_VERSION_NEWEST = 'newest'
DATASET_VERSION_NEXT = 'next'
DATASET_VERSION_NULL = 'null'


@dataclass
class Context:
    """
    构建数据集上下文
    """
    batch_id: str  # 计划id
    model_id: str  # 模型id
    origin_geom_file: str  # 原始坐标文件
    output_dir: Path  # 输出成果目录
    result_dir: Path  # 成果打包目录
    region_file_path: Path  # 街区文件路径
    street_split_img_dir: str  # 街区切割影像图目录
    all_tile_map_afs_path: str  # 全量瓦片图AFS路径
    street_split_result_afs_dir: str  # 街区切割成果上传afs目录
    max_tile_size: int  # 瓦片最大尺寸
    batch_no: str  # 批次号
    geom_guid: str  # 范围对应的唯一 id


def progress(ctx: Context):
    """
    建筑物数据集产出
    """
    geom_text = get_geom_by_file(ctx.origin_geom_file)

    # 创建街区切割文件
    # region_dict = [(ctx.geom_guid, geom_text)]
    region_dict = gen_region_dict(ctx, geom_text)
    region_json = create_region_file(ctx, region_dict)

    # 产数据集
    tile_map_size_limit = ctx.max_tile_size
    dataset_version = get_dataset_version(ctx)
    if dataset_version == DATASET_VERSION_NULL:
        remark = f"产影像图次数用尽，任务结束"
        print(remark)
        bf_model.update_pre_plan_status_and_remark(ctx.batch_id, PRE_PLAN_STATUS_END, remark)
        return

    if MODEL_TYPE_VIS in ctx.model_id:
        create_vis_model_dataset(ctx, dataset_version)
        data_pack_path = f"{ctx.output_dir}/dataset_{ctx.batch_id}"
        image_path = f"{ctx.street_split_img_dir}/*"
    else:
        tile_map_size_limit = ctx.max_tile_size / 2
        create_self_develop_dataset_by_geom(ctx, region_dict, dataset_version)
        data_pack_path = f"{ctx.output_dir}"
        image_path = f"{ctx.output_dir}/images/"
        image_path = os.path.join(image_path, get_newest_subdir_name(image_path))
        image_path = f"{image_path}/*"

    print(data_pack_path, ctx.model_id, tile_map_size_limit)

    try:
        if not img_cropper.crop_bud_tile(data_pack_path, model=ctx.model_id, pixel_limit=tile_map_size_limit):
            print(f"影像图裁切失败")
            bf_model.update_pre_plan_status_and_result_url_by_id(ctx.batch_id, PRE_PLAN_STATUS_FAILED,
                                                                 ctx.street_split_result_afs_dir,
                                                                 f"影像图裁切失败")
            return
    except Exception as e:
        print(f"影像图裁切异常:{str(e)}")
        bf_model.update_pre_plan_status_and_result_url_by_id(ctx.batch_id, PRE_PLAN_STATUS_FAILED,
                                                             ctx.street_split_result_afs_dir,
                                                             f"影像图裁切异常：{str(e)}")
        return

    delete_file_number = 0
    files = glob.glob(image_path)
    print(f"识别结果:{len(files)};{image_path}")
    # for file in files:
    #     print(f"文件名:{file.title()}")
    #     if not file.endswith(".jpg"):
    #         continue
    #     with Image.open(file) as img:
    #         if img.width >= tile_map_size_limit:
    #             delete_file_number += 1
    #             os.remove(file)
    # print(f"删除超过限制数量：{delete_file_number}")
    if delete_file_number == len(files):
        remark = f"没有符合要求的街区信息：{delete_file_number}"
        if len(files) == 0:
            remark = f"影像图产出失败；{image_path}"
        print(remark)

        bf_model.update_pre_plan_status_and_remark(ctx.batch_id, PRE_PLAN_STATUS_FAILED, remark)
        return
    result_file_path = f"{ctx.result_dir}/{ctx.batch_id}.tar.gz"
    if MODEL_TYPE_VIS in ctx.model_id:
        F.exec_shell_cmd(f"tar -czf {result_file_path} -C {ctx.output_dir}/dataset_{ctx.batch_id} .")
    else:
        F.exec_shell_cmd(f"tar -czf {result_file_path} -C {ctx.output_dir} .")
    afs.put(result_file_path, ctx.street_split_result_afs_dir)
    os.remove(result_file_path)
    # 更新预览计划状态成果
    bf_model.update_pre_plan_status_and_result_url_by_id(ctx.batch_id, PRE_PLAN_STATUS_STREET_SPLIT_END,
                                                         ctx.street_split_result_afs_dir, region_json)


def get_geom_by_file(geom_file: str):
    """
    根据文件获取几何信息
    :param geom_file: 几何文件路径
    :return geom_str: 几何信息
    """
    with open(geom_file, 'r') as f:
        geom_str = f.read()
        return geom_str


def gen_region_dict(ctx: Context, geom_text: str) -> dict:
    """
    生成范围列表；如果范围是 multiPolygon, 一个 item 是一个 polygon
    """
    geo = shapely.wkt.loads(geom_text)
    if geo.geom_type not in ['Polygon', 'MultiPolygon']:
        raise Exception(f"产数据集范围的范围不支持：{geo.geom_type}")
    if geo.geom_type == 'Polygon':
        geos = [geo]
    else:
        geos = list(geo.geoms)

    region_dict = {}
    for _idx, _geo in enumerate(geos):
        region_dict[f"{ctx.geom_guid}_{_idx}"] = _geo.wkt
    return region_dict


def get_suitable_street(street_data_list, identify_wkt: str) -> Union[dict, None]:
    """
    挑选合适的街区，选择占比最大，且面积最小的街区
    """
    if street_data_list is None or len(street_data_list) == 0:
        return None

    identify_geo = shapely.wkt.loads(identify_wkt)

    max_rate_streets = []
    max_rate = 0
    for _street in street_data_list:
        _street_geo = shapely.wkt.loads(_street[1])
        _street_int = _street_geo.intersection(identify_geo)

        cur_rate = _street_int.area / identify_geo.area
        if cur_rate < max_rate:
            continue

        if cur_rate == max_rate:
            max_rate_streets.append(_street)
        else:
            max_rate = cur_rate
            max_rate_streets = [_street]

    min_area_street = None
    min_area = sys.maxsize
    for _street in max_rate_streets:
        _geo = shapely.wkt.loads(_street[1])
        if _geo.area < min_area:
            min_area = _geo.area
            min_area_street = _street
    return min_area_street


def create_region_file(ctx: Context, region_dict: dict) -> str:
    """
    根据街区信息创建识别文件
    """
    data_list = []
    for uid, wkt in region_dict.items():
        data_list.append({
            "uuid": uid,
            "poly_geom": wkt,
            "city": [""]
        })

    region_json_str = json.dumps(data_list, ensure_ascii=False)
    with open(ctx.region_file_path, 'w+', encoding='utf-8') as f:
        f.write(region_json_str)
    return region_json_str


def create_vis_model_dataset(ctx: Context, version: str):
    """
    数据集产出
    """
    args = [
        '/home/<USER>/py39/py39',
        '../pre_process/create_od_product_dataset.py',
        '--id',
        f'dataset_{ctx.batch_id}',
        '--region-file',
        str(ctx.region_file_path),
        '--output-path',
        str(ctx.output_dir),
        '--cache-path',
        f"{DATASET_CACHE_DIR}/{ctx.batch_id}",
        '--level',
        '20',
        '--no-clip',
    ]
    if version == DATASET_VERSION_NEWEST:
        version = 'Google2023'
        src = 'afs2023'
        args += [
            '--raster-type',
            version,
            '--raster-src',
            src,
            '--raster-host',
            str(ctx.all_tile_map_afs_path),
        ]
    else:
        version = 'Google2022'
        src = 'http'
        args += [
            '--raster-type',
            version,
            '--raster-src',
            src,
        ]

    download_raster_process = subprocess.Popen(args, stdout=subprocess.PIPE)
    download_raster_process.wait()


def create_self_develop_dataset_by_geom(ctx: Context, guid2geom: dict, version: str):
    """
    自有模型数据集产出
    @param ctx:
    @param guid2geom: 唯一id 对应的范围
    @param version: 数据集版本号
    """
    if version == DATASET_VERSION_NEWEST:
        version = 'google-2023'
    else:
        version = 'google-2022'
    building_dataset.create_dataset_by_geom(guid2geom, ctx.output_dir, [version])


def get_dataset_version(ctx: Context) -> str:
    """
    获取数据集（影像图）的版本；优先获取最新的（23 年），若多次获取最新的都失败，那么降级获取次新的（22 年的）
    获取的次数存放在 redis 中
    """
    with rt.RedisTool() as rds:
        rds_key = f"bud:dataset:newest:{ctx.batch_id}"
        rds_val = rds.redis_conn.get(rds_key)
        if rds_val is None or rds_val == "":
            rds_val = 0

        rds_val = int(rds_val) + 1
        rds.redis_conn.set(rds_key, rds_val, 24 * 3600 * 7)

        next_times, max_times = get_gen_dataset_limit_times()
        print(f"{rds_key} 第 {rds_val} 次产数据集; next_times: {next_times}; max_times:{max_times}")

        if rds_val > max_times:
            return DATASET_VERSION_NULL
        if rds_val > next_times:
            return DATASET_VERSION_NEXT
        return DATASET_VERSION_NEWEST  # 获取最新的


def get_gen_dataset_limit_times() -> tuple:
    """
    获取产数据集次数限制
    返回 (next_times, max_times) 降级次数，最大次数
    若值是 (5, 10) 那么表示 <=5 那么产最新的数据集，若大于 next_times and <= max_times，那么降级产数据；
    若大于 max_times 那么不再产，需要结束
    """
    if ARGS is None:
        return 5, 15
    return int(ARGS.next_times), int(ARGS.max_times)


def get_plans_with_db_lock(max_num: int = 30) -> list:
    """
    获取计划，获取计划之后, db 加锁（状态改为中间态）
    max_num: 一次获取的最大数量；基于以下几点考虑
    1、若一次都获取完所有计划，那么其他机器就不能处理了，不能并发消费，肯定不行
    2、若一次取太少，那么就需要频繁从库里拿，性能不是最佳的
    3、特殊情况，若计划总数少于 max_num, 那么就变成了情况 1，也不太行
    4、为了避免情况 3，且能并发；就定义如下：real_num = min (max_num, plan_num / 3)
    """
    pre_plans = bf_model.get_bud_pre_plan_by_status(PRE_PLAN_STATUS_INIT, limit_number=max_num * 3)
    if pre_plans is None or len(pre_plans) == 0:
        return []

    real_num = min(max_num, int(len(pre_plans) / 3) + 1)
    response = []
    for pre_plan in pre_plans:
        plan_id = str(pre_plan[0])
        bf_model.update_pre_plan_status_and_result_url_by_id(plan_id, PRE_PLAN_STATUS_PROGRESSING, '')
        response.append(pre_plan)

        real_num -= 1
        if real_num <= 0:
            break
    return response


def get_batch_plans_with_db_lock(max_num: int = 30) -> dict:
    """
    根据批次获取任务
    """
    pre_plans = bf_model.get_bud_pre_plan_by_status(PRE_PLAN_STATUS_INIT, limit_number=-1)
    # pre_plans = bf_model.get_bud_pre_plan_by_status('INIT_TEST', limit_number=-1)
    if pre_plans is None or not pre_plans or len(pre_plans) == 0:
        return {}

    no2plans = {}
    for _plan in pre_plans:
        no = _plan[4]
        if no == '':
            continue

        if no not in no2plans:
            no2plans[no] = []
        no2plans[no].append(_plan)

    real_num = min(max_num, int(len(pre_plans) / 3) + 1)
    response = {}
    for no, plans in no2plans.items():
        for plan in plans:
            plan_id = str(plan[0])
            bf_model.update_pre_plan_status_and_result_url_by_id(plan_id, PRE_PLAN_STATUS_PROGRESSING, '')

        real_num -= len(plans)
        response[no] = plans
        if real_num <= 0:
            break
    return response


def get_batch_plans_guardedly(limit: int) -> dict:
    """
    根据批次获取任务
    """
    rds_locker = locker.RedisLocker(
        rds=rt.RedisTool().redis_conn,
        key='bud:create:dataset:fetcher:plans',
        ex=60 * 30,
    )
    sleep_time = random.randint(1, 10)  # 睡眠调整为随机
    plans = locker.lock_guardian_with_retry(
        locker=rds_locker, times=10, sleep_time=sleep_time,
        fun=get_batch_plans_with_db_lock, max_num=limit,
    )
    return plans


def get_plans_guardedly(limit: int) -> list:
    """
    加锁获取任务
    """
    rds_locker = locker.RedisLocker(
        rds=rt.RedisTool().redis_conn,
        key='bud:create:dataset:fetcher:plans',
        ex=60 * 30,
    )
    sleep_time = random.randint(1, 10)  # 睡眠调整为随机
    plans = locker.lock_guardian_with_retry(
        locker=rds_locker, times=10, sleep_time=sleep_time,
        fun=get_plans_with_db_lock, max_num=limit,
    )
    return plans


def gen_ctx(pre_plan: tuple) -> Context:
    """
    生成上下文
    """
    batch_id = str(pre_plan[0])
    batch_no = str(pre_plan[4])
    geom_guid = batch_no.split('-')[1]
    ctx = Context(
        batch_id=batch_id,
        model_id=pre_plan[3],
        origin_geom_file=BASE_DIR / batch_id / "origin_geom.txt",
        output_dir=BASE_DIR / batch_id,
        result_dir=BASE_DIR / "result",
        region_file_path=BASE_DIR / batch_id / "region.json",
        street_split_img_dir=BASE_DIR / batch_id / f'dataset_{batch_id}' / "images",
        all_tile_map_afs_path="/user/map-data-streeview/map_yingxiangtu/2023/done",
        street_split_result_afs_dir=UPLOAD_AFS_PATH / f"{batch_id}.tar.gz",
        max_tile_size=4096,
        batch_no=batch_no,
        geom_guid=geom_guid
    )
    return ctx


def gen_plan_dataset(pre_plan: tuple):
    """
    生成计划的数据集
    """
    ctx = gen_ctx(pre_plan)
    try:
        utils.ensure_dir(ctx.output_dir)
        utils.ensure_dir(ctx.result_dir)
        utils.ensure_dir(Path(f"{DATASET_CACHE_DIR}/{ctx.batch_id}"))
        F.exec_shell_cmd(f"wget --no-check-certificate -O {ctx.origin_geom_file} '{pre_plan[1]}'")

        progress(ctx)

        shutil.rmtree(ctx.output_dir)
        shutil.rmtree(f"{DATASET_CACHE_DIR}/{ctx.batch_id}")
    except Exception as e:
        logging.exception(e)
        bf_model.update_pre_plan_status_and_remark(ctx.batch_id, PRE_PLAN_STATUS_FAILED,
                                                   f"数据集产出错误:{str(e)}")


def gen_batch_plans_dataset(plans: list):
    """
    生成一批计划的数据集
    一批计划中，分 vis 模型和自有模型；其中多个自有模型的数据集是一样的，那么数据集应该复用
    """
    src_plan = None
    for plan in plans:
        ctx = gen_ctx(plan)
        if ctx.model_id == MODEL_TYPE_VIS:
            gen_plan_dataset(plan)
            continue

        if src_plan is None:
            src_plan = get_copy_src_plan(ctx.batch_no)

        if src_plan is None or not can_copy(ctx):  # 还是不存在，那么生成
            gen_plan_dataset(plan)
            src_plan = plan
            continue

        # 后面计划开始 copy 数据集，不再重新创建
        # 特殊情况，若 src_plan 数据集为空或者失败，那么 copy 也会失败
        copy_plan_dataset(ctx, dst_plan=plan, src_plan=src_plan)


def get_copy_src_plan(batch_no: str):
    """
    获取某个批次，可以 copy 的计划
    """
    # return None

    sql = (f"select id,plan_name, result_url, task_data from basic_feature_pre_plan where batch_no = '{batch_no}' "
           f"and result_url like '%tar.gz%' and outside_id not in ('chat_v1', 'vis_v1') "
           f"and status not in ('INIT', 'PROGRESSING', 'FAILED')")
    res = bf_model.queryone(sql)
    if res is None or not res:
        return None
    return res


def can_copy(ctx: Context) -> bool:
    """
    能否 copy，若 copy 次数超过限制，那么就不能再 copy
    """
    with rt.RedisTool() as rds:
        rds_key = f"bud:dataset:newest:{ctx.batch_id}:copy"
        rds_val = rds.redis_conn.get(rds_key)
        if rds_val is None or rds_val == "":
            rds_val = 0

        rds_val = int(rds_val) + 1
        rds.redis_conn.set(rds_key, rds_val, 24 * 3600 * 7)

        copy_times, _ = get_gen_dataset_limit_times()
        print(f"{rds_key} 第 {rds_val} 次产数据集; copy_times: {copy_times}")

        if rds_val > copy_times:
            return False
        return True


def copy_plan_dataset(ctx: Context, dst_plan: tuple, src_plan: tuple):
    """
    copy 数据集，从 src copy to dst
    """
    src_id = src_plan[0]
    src_plan = bf_model.get_basic_pre_plan_by_id(src_id)  # 获取数据中最新的计划信息
    src_result, src_task_data = src_plan[2], src_plan[3]
    if src_result == '':  # 源生成失败，那么 copy 失败
        remark = f"影像图产出失败；copy 失败, 源不存在：{src_id}"
        print(remark)
        bf_model.update_pre_plan_status_and_remark(ctx.batch_id, PRE_PLAN_STATUS_FAILED, remark)
        return

    local_dir = './tmp/copy'
    afs.get(src_result, local_dir)

    src_file = os.path.join(local_dir, f'{src_id}.tar.gz')
    if not os.path.exists(src_file):
        remark = f"影像图产出失败；copy 失败，afs 下载失败: {src_id}"
        print(remark)
        bf_model.update_pre_plan_status_and_remark(ctx.batch_id, PRE_PLAN_STATUS_FAILED, remark)
        return

    dst_file = os.path.join(local_dir, f"{dst_plan[0]}.tar.gz")
    os.rename(src_file, dst_file)

    if not os.path.exists(dst_file):
        os.remove(src_file)
        remark = f"影像图产出失败；copy 失败，rename: {src_id}"
        print(remark)
        bf_model.update_pre_plan_status_and_remark(ctx.batch_id, PRE_PLAN_STATUS_FAILED, remark)
        return

    print(f"{ctx.batch_id} copy 数据集成功")
    afs.put(dst_file, ctx.street_split_result_afs_dir)
    os.remove(dst_file)
    # 更新预览计划状态成果
    bf_model.update_pre_plan_status_and_result_url_by_id(ctx.batch_id, PRE_PLAN_STATUS_STREET_SPLIT_END,
                                                         ctx.street_split_result_afs_dir, src_task_data)


def main():
    """
    主函数
    """
    times = 0
    while times < int(ARGS.times):  # 一次启动限制执行次数，避免日志过大
        times += 1

        # pre_plans = get_plans_guardedly(limit=int(ARGS.limit))
        # print(f"第 {times} 次执行；计划数：{len(pre_plans)}")
        # if len(pre_plans) == 0:
        #     print("没有可处理的预览计划")
        #     break
        #
        # for pre_plan in pre_plans:
        #     gen_plan_dataset(pre_plan)

        no2plans = get_batch_plans_guardedly(limit=int(ARGS.limit))
        print(f"第 {times} 次执行；计划数：{len(no2plans)}")
        if no2plans is None or len(no2plans) == 0:
            print("没有可处理的预览计划")
            break

        for plans in no2plans.values():
            gen_batch_plans_dataset(plans)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='manual to this script; python3 **.py --env=test ')
    parser.add_argument('--times', type=int, default=10)
    parser.add_argument('--limit', type=int, default=30)
    parser.add_argument('--max_times', type=int, default=15)
    parser.add_argument('--next_times', type=int, default=5)

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()
