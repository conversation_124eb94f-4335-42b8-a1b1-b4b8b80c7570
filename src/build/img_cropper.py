# !/usr/bin/env python3
# coding=utf-8

"""
图片裁切
"""
import abc
import glob
import json
import math
import os
import uuid
from typing import List, Union
import dataclasses

from PIL import Image
import shapely.wkt

from src.tools.utils import get_newest_subdir_name

Image.MAX_IMAGE_PIXELS = 3933120000


@dataclasses.dataclass
class Point:
    """
    点
    """
    x: float
    y: float


@dataclasses.dataclass
class Bounds:
    """
    框
    """
    left: float
    top: float
    right: float
    bottom: float

    def width(self) -> float:
        """
        宽
        """
        return self.right - self.left

    def height(self) -> float:
        """
        高
        """
        return self.top - self.bottom

    def box(self) -> tuple:
        """
        box 点
        """
        return self.left, self.top, self.right, self.bottom

    def dict(self) -> dict:
        """
        转换为字典
        """
        return {
            'left': self.left,
            'top': self.top,
            'right': self.right,
            'bottom': self.bottom,
        }


@dataclasses.dataclass
class CoordBounds(Bounds):
    """
    位置框
    """

    def wkt(self) -> str:
        """
        转换为 wkt
        """
        return (f"POLYGON(({self.left} {self.top}, {self.right} {self.top}, {self.right} {self.bottom}, "
                f"{self.left} {self.bottom}, {self.left} {self.top}))")


@dataclasses.dataclass
class Tile:
    """
    瓦片图
    """
    filepath: str
    width: float
    height: float
    gcj_coord_bounds: CoordBounds
    wgs_coord_bounds: CoordBounds = None

    def __post_init__(self):
        self.pixel_bounds = Bounds(left=0, top=0, right=self.width, bottom=self.height)
        self.root, full_file_name = os.path.split(self.filepath)
        self.id, self.file_ext = os.path.splitext(full_file_name)

    def get_coord_bounds_by_pixel_bounds(self, pixel_bounds: Bounds, coord_bounds: CoordBounds) \
            -> Union[CoordBounds, None]:
        """
        根据像素框获取坐标框
        """
        if coord_bounds is None:
            return None
        left_top = pixel_to_coord(
            coord_bounds=coord_bounds,
            pixel_point=(pixel_bounds.left, pixel_bounds.top),
            pixel_bounds=self.pixel_bounds,
        )
        right_bottom = pixel_to_coord(
            coord_bounds=coord_bounds,
            pixel_point=(pixel_bounds.right, pixel_bounds.bottom),
            pixel_bounds=self.pixel_bounds,
        )
        return CoordBounds(left=left_top.x, top=left_top.y, right=right_bottom.x, bottom=right_bottom.y)

    def json(self) -> str:
        """
        返回格式化的 json 信息
        """
        info = {
            'id': self.id,
            'width': self.width,
            'height': self.height,
            'region': self.gcj_coord_bounds.dict(),
            'geom': self.gcj_coord_bounds.wkt(),
        }
        if self.wgs_coord_bounds is not None:
            info['wgs_geom'] = self.wgs_coord_bounds.wkt()
        return json.dumps(info)


class BudTilePackage:
    """
    建筑物瓦片图包
    """
    root: str  # 根目录

    def __init__(self, root: str):
        self.root = root
        self.images_path = ''
        self.json_info_path = ''
        self.images_path_latest = ''  # 最新影像图作业包
        self._init_path_info()

    @abc.abstractmethod
    def _init_path_info(self):
        """
        初始化路径信息
        """
        pass

    def get_wait_cropped_tiles(self, pixel_limit) -> List[Tile]:
        """
        获取待裁切的瓦片图信息
        """
        files = glob.glob(f"{self.images_path_latest}/*")
        tiles = []
        for _file in files:
            _, filename = os.path.split(_file)
            name, _ = os.path.splitext(filename)
            info = json.loads(read_file(os.path.join(self.json_info_path, f"{name}.json")))

            if info['width'] < pixel_limit and info['height'] < pixel_limit:
                continue

            tile = Tile(
                filepath=_file,
                width=info['width'],
                height=info['height'],
                gcj_coord_bounds=CoordBounds(
                    left=info['region']['left'],
                    top=info['region']['top'],
                    right=info['region']['right'],
                    bottom=info['region']['bottom'],
                ),
            )
            if 'wgs_geom' in info:
                wgs_bounds = shapely.wkt.loads(info['wgs_geom']).bounds
                tile.wgs_coord_bounds = CoordBounds(
                    left=wgs_bounds[0],
                    top=wgs_bounds[3],
                    right=wgs_bounds[2],
                    bottom=wgs_bounds[1],
                )
            tiles.append(tile)
        return tiles

    def add_cropped_tiles(self, tiles: List[Tile]) -> bool:
        """
        添加裁切过的瓦片图，添加成功返回 True
        主要是完善 json 信息，裁切的瓦片图已经在当前包里面了
        """
        for _tile in tiles:
            with open(f"{self.json_info_path}/{_tile.id}.json", 'w') as handler:
                handler.write(_tile.json())
        return True


class SelfModelBudTilePackage(BudTilePackage):
    """
    自有模型影像图包
    """

    def _init_path_info(self):
        self.images_path = f"{self.root}/images"
        self.json_info_path = f"{self.root}/json_info/image"
        self.images_path_latest = f"{self.images_path}/{get_newest_subdir_name(self.images_path)}"  # 最新的影像图


class VisModelBudTilePackage(BudTilePackage):
    """
    vis 模型影像图包
    """

    def _init_path_info(self):
        self.images_path = f"{self.root}/images"
        self.json_info_path = f"{self.root}/json_info/image"
        self.images_path_latest = self.images_path  # 最新的影像图


def read_file(file: str):
    """
    根据文件获取几何信息
    :param file: 文件路径
    """
    with open(file, 'r') as f:
        return f.read()


def pixel_to_coord(coord_bounds: CoordBounds, pixel_bounds: Bounds, pixel_point: tuple) -> Point:
    """
    像素转坐标
    """
    x, y = pixel_point
    return Point(
        x=coord_bounds.left + ((x / pixel_bounds.width()) * coord_bounds.width()),
        y=coord_bounds.top + ((y / pixel_bounds.height()) * coord_bounds.height()),
    )


def get_crop_pixel_bounds(width: int, height: int, limit: int) -> List[Bounds]:
    """
    获取裁切的像素框位置
    width, height 图像的宽高
    limit 需要限制的大小
    """
    w_num = math.ceil(width / limit)
    h_num = math.ceil(height / limit)

    tops = []
    for i in range(0, w_num):
        for j in range(0, h_num):
            tops.append((i, j))

    bounds = []
    w_step = math.ceil(width / w_num)
    h_step = math.ceil(height / h_num)
    for _top in tops:
        x1 = _top[0] * w_step
        y1 = _top[1] * h_step
        x2 = min(x1 + w_step, width)
        y2 = min(y1 + h_step, height)
        bounds.append(Bounds(left=x1, top=y1, right=x2, bottom=y2))
    return bounds


def crop_tile(tile: Tile, pixel_limit: int) -> List[Tile]:
    """
    根据限制的像素大小裁切图片，返回多张图片
    """
    images = []
    with Image.open(tile.filepath) as image:
        pixel_bounds = get_crop_pixel_bounds(width=image.width, height=image.height, limit=pixel_limit)
        for _idx, _bound in enumerate(pixel_bounds):
            # 保存新图片
            print(_bound.box())
            roi = image.crop(_bound.box())

            new_file = os.path.join(tile.root, f"{uuid.uuid4().hex}{tile.file_ext}")
            print(new_file)
            roi.save(new_file)

            # 计算新图片的坐标范围
            gcj_coord_bounds = tile.get_coord_bounds_by_pixel_bounds(_bound, tile.gcj_coord_bounds)
            wgs_coord_bounds = tile.get_coord_bounds_by_pixel_bounds(_bound, tile.wgs_coord_bounds)
            images.append(Tile(
                filepath=new_file,
                width=roi.width,
                height=roi.height,
                gcj_coord_bounds=gcj_coord_bounds,
                wgs_coord_bounds=wgs_coord_bounds,
            ))

    # 删除掉已有的图片
    os.remove(tile.filepath)
    return images


def crop_bud_tile(bud_tile_package_path: str, model: str, pixel_limit: int) -> bool:
    """
    裁切建筑物的瓦片图, 若不需要裁切或者裁切成功，返回 True, 失败返回 False; 若异常会直接抛出
    :bud_tile_package_path: 建筑物影像图包的路径
    :pixel_limit: 影像图限制的像素大小
    """
    if 'vis' in model:
        package = VisModelBudTilePackage(root=bud_tile_package_path)
    else:
        package = SelfModelBudTilePackage(root=bud_tile_package_path)
    for _image in package.get_wait_cropped_tiles(pixel_limit=pixel_limit):
        if not package.add_cropped_tiles(crop_tile(tile=_image, pixel_limit=pixel_limit)):
            return False
    return True
