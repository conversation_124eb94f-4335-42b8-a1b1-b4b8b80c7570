# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置信息
Descripttion: Created by liuaonan
version: 1.0
Author: l<PERSON><PERSON><PERSON>@cd.baidu.com
Date: 2023-12-06 14:39:33
LastEditors: liuaonan
LastEditTime: 2023-12-06 14:39:33
"""
# 测试库 mysql
# mysql -h************ -uroot -pmapSH!@# -P3306 bee_flow -A
T_MY_DB = {
    'host': '************',
    'db': 'bee_flow',
    'port': 3306,
    'user': 'root',
    'password': 'mapSH!@#',
}
# 试生产 mysql
PRE_MY_DB = {
    'host': '*************',
    'db': 'bee_flow_pre',
    'port': 5730,
    'user': 'bee_flow_pre_rw',
    'password': 'bee_flow_pre_rw_2021',
}
# 线上库 mysql
ONLINE_MY_DB = {
    'host': '*************',
    'db': 'bee_flow',
    'port': 5730,
    'user': 'bee_flow',
    'password': 'nl4c/mqeTcsgpH',
}

# poi_online
# 测试库 pgsql
T_POI_ONLINE = {
    'host': '',
    'dbname': 'poi_online',
    'port': '',
    'user': '',
    'password': '',
}
# 试生产 pgsql
# psql -Upoi_aoi_rw -Wpoi_aoi_rw -p9432 -h************** -dpoi_online
PRE_POI_ONLINE = {
    'host': '**************',
    'dbname': 'poi_online',
    'port': '9432',
    'user': 'poi_aoi_rw',
    'password': 'poi_aoi_rw',
}
# 线上库 pgsql
# PGPASSWORK=poi_aoi_rw psql -h gzbh-ns-map-de16.gzbh.baidu.com -p 8532 -U poi_aoi_rw -d poi_online
ONLINE_POI_ONLINE = {
    'host': 'gzbh-ns-map-de16.gzbh.baidu.com',
    'dbname': 'poi_online',
    'port': '8532',
    'user': 'poi_aoi_rw',
    'password': 'poi_aoi_rw',
}

# 背景母库 mask_back
# 测试库 pgsql
# PGPASSWORK=dbuser psql -h ************ -p 8432 -U dbuser -d backref_test
T_MASK_BACK = {
    'host': '************',
    'dbname': 'backref_test',
    'port': '8432',
    'user': 'dbuser',
    'password': 'dbuser',
}
# 试生产 pgsql
#
PRE_MASK_BACK = {
    'host': '',
    'dbname': '',
    'port': '',
    'user': '',
    'password': '',
}
# 线上库 pgsql
# PGPASSWORD=mapread psql -h ************** -p 5432 -U master_back_se_ro -d master_back (只读)
ONLINE_MASK_BACK = {
    'host': '**************',
    'dbname': 'master_back',
    'port': '5432',
    'user': 'master_back_se_ro',
    'password': 'mapread',
}
# psql -h ************** -p 5432 -U master_back_se_rw -d master_back -Wcwduumoj
ONLINE_MASTER_BACK_RW = {
    'host': '**************',
    'dbname': 'master_back',
    'port': '5432',
    'user': 'master_back_se_rw',
    'password': 'cwduumoj',
}



# 智能空间
# 生产环境：psql -Upoi_aoi_rw -Wpoi_aoi_rw -p 8532 -h gzbh-ns-map-de16.gzbh.baidu.com -dindoor
SMARTSPACE_ONLINE_PG = {
    'host': 'gzbh-ns-map-de16.gzbh.baidu.com',
    'dbname': 'indoor',
    'port': '8532',
    'user': 'poi_aoi_rw',
    'password': 'poi_aoi_rw',
}
# 办公网测试：psql -h ************ -d indoor -U dbuser -p 8432
# 办公网测试：mysql mysql -u root -h ************ bee_flow -p
# 道路库 python ~/leon/getPgInfo.py /muku/road/master_road_ml
ROAD_PG = {
    'host': '**************',
    'dbname': 'mukucache_6147288adc7311ee919d6c92bfb4ccf6',
    'port': '5432',
    'user': 'mukucache_6147288adc7311ee919d6c92bfb4ccf6_se_rw',
    'password': 'sbfuolfu',
}

# de16
ONLINE_REDIS = {
    'host':'127.0.0.1', 
    'port':'6379', 
    'decode_responses':'True', 
    'db':'0'
}

#opera -> beeflow
OPERA_REDIS = {
    'host':'*************', 
    'port':'9000', 
    'decode_responses':'True', 
    'db':'0'
}

#建筑物上量 pg
ONLINE_BUD_PG = {
    'host': '**************',
    'dbname': 'aoi_back',
    'port': '7432',
    'user': 'aoi_back_se_rw',
    'password': 'gzoksgyf',
}