import logging


class Logger4common(object):
    def __init__(self, file_name, output_level='info', write_level='info', fmt='%(asctime)s - %(levelname)s: %(message)s'):
        self.logger = logging.getLogger(file_name)
        self.file_name = file_name

        if output_level not in ['debug', 'info', 'warning', 'error', 'critical']:
            raise ValueError("日志等级格式错误")

        if output_level == 'debug':
            l = 0
            level = logging.DEBUG
        elif output_level == 'info':
            l = 1
            level = logging.INFO
        elif output_level == 'warning':
            l = 2
            level = logging.WARNING
        elif output_level == 'error':
            l = 3
            level = logging.ERROR
        elif output_level == 'critical':
            l = 4
            level = logging.CRITICAL

        if write_level == 'debug':
            l2 = 0
            w_level = logging.DEBUG
        elif write_level == 'info':
            l2 = 1
            w_level = logging.INFO
        elif write_level == 'warning':
            l2 = 2
            w_level = logging.WARNING
        elif write_level == 'error':
            l2 = 3
            w_level = logging.ERROR
        elif write_level == 'critical':
            l2 = 4
            w_level = logging.CRITICAL

        # 设置日志格式
        self.format_str = logging.Formatter(fmt)

        if l < l2:
            min_level = level
        else:
            min_level = w_level

        # 设置日志级别
        self.logger.setLevel(min_level)

        # 命令行输出
        sh = logging.StreamHandler()
        sh.setLevel(level)
        sh.setFormatter(self.format_str)
        self.logger.addHandler(sh)
        self.console_handler = sh

        # 日志文件输出
        th = logging.FileHandler(self.file_name, encoding='utf-8')
        th.setLevel(w_level)
        th.setFormatter(self.format_str)
        self.logger.addHandler(th)
        self.file_handler = th

    def __call__(self, level, message):
        if level not in ['debug', 'info', 'warning', 'error', 'critical']:
            raise ValueError("日志等级格式错误")

        if level == 'debug':
            self.logger.debug(message)
        elif level == 'info':
            self.logger.info(message)
        elif level == 'warning':
            self.logger.warning(message)
        elif level == 'error':
            self.logger.error(message)
        elif level == 'critical':
            self.logger.critical(message)

    def remove_handlers(self):
        self.logger.removeHandler(self.console_handler)
        self.logger.removeHandler(self.file_handler)

# 装饰器类log
def logger4makeup():
    pass

if __name__ == '__main__':
    log = Logger4common('./test.log', output_level='info', write_level='info')

    for i in range(10):
        log('info', f'EPOCH {i}: loss:{i * 0.1}, acc:{i}')
        log('debug', f'222 EPOCH {i}: loss:{i * 0.1}, acc:{i}')

    log.remove_handlers()
