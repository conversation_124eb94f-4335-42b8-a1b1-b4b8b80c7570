"""
语义分割调用类
"""

import json
import pymysql
import os
import sys
import toml

from pathlib import Path

sys.path.append(os.path.abspath((os.path.dirname(__file__)) + "/../"))
from config.db_config import PRE_MY_DB, ONLINE_MY_DB
from config.afs_config import AFS_CONFIG

#MYSQL = PRE_MY_DB
MYSQL = ONLINE_MY_DB

# AFS配置
VECTORIZE_TYPE = {
    'vis': "building_vectorize_v2",
    'seg': "channel_2"
}

class CommonMulti:
    """
    语义分割调用类
    """
    def __init__(self, model_name, model_path, gpu, task_group_num, work_path):

        self.MODEL_NAME = model_name
        self.MODEL_PATH = model_path
        self.GPU = gpu
        self.TASK_GROUP_NUM = task_group_num
        self.WORK_PATH = work_path

        self.BEFORE_STATUE = "TILE_MERGED"
        self.PLAN_TYPE = 47
        self.TABLE_NAME = "basic_feature_pre_plan"

        self.mysql_conn = pymysql.connect(**MYSQL)
        self.mysql_cur = self.mysql_conn.cursor()

        self._get_task()


    def end(self):
        """
        手动中止
        """
        self.mysql_conn.close()

    def process(self):
        """
        入口主函数
        """
        try:
            self._download_list_from_afs()
            self._get_imgs_path_list()
            self._process_model()
            self._upload_afs()
        except Exception as e:
            print(e)

    def notask(self):
        """
        判断是否获取到了待处理的task
        """
        if len(self.task_list) == 0:
            return True
        else:
            return False

    def _get_task(self):
        """
        获取[task_id, url]
        :return:
        """
        self.task_list = []
        try:
            sql = f"select id, result_url from {self.TABLE_NAME} where status='{self.BEFORE_STATUE}' and plan_type={self.PLAN_TYPE} and outside_id = '{self.MODEL_NAME}' order by batch_no asc limit {self.TASK_GROUP_NUM};"
            self.mysql_cur.execute(sql)
            ret = self.mysql_cur.fetchall()
        except Exception as e:
            raise Exception(f"get task failed:{e}")
        for item in ret:
            self.task_list.append(item)
        print(self.MODEL_NAME, len(self.task_list))

    def _download_list_from_afs(self):
        """
        从AFS下载list中数据
        err:
            1、AFS上无数据
        :return:
        """
        err_list = []
        for item in self.task_list:
            url = item[1]
            image_name = os.path.basename(url)
            image_hash = image_name.split(".")[0]
            file_path = os.path.join(self.WORK_PATH, image_hash)
            gz_path = os.path.join(self.WORK_PATH, image_name)
            if os.path.exists(file_path):
                os.system(f'rm -rf {file_path}')
            if os.path.exists(gz_path):
                os.remove(gz_path)
            os.system(f'mkdir {file_path}')
            res = os.system(f'/home/<USER>/afs/bin/afsshell --username={AFS_CONFIG["username"]} '
                            f'--password={AFS_CONFIG["password"]} get afs://aries.afs.baidu.com:9902{url} {self.WORK_PATH}')

            try:
                if res != 0:
                    raise Exception("no file in afs")
            except Exception as e:
                self._update_table(item[0], "FAILED", e)
                err_list.append(item)
                continue
            # 删除task_list中err的list
            self.task_list = [item for item in self.task_list if item not in err_list]

            os.system(f'tar -zxf {gz_path} -C {file_path}')

    def _set_config(self, output_path, model_name, vectorize_type):
        """
        记录模型版本和矢量化版本
        """
        config = {
            'model_type': model_name,
            'vectorize_type': vectorize_type,
        }
        config_out = os.path.join(output_path, "config.json")
        with open(config_out, "w") as f:
            json.dump(config, f)

    def _get_imgs_path_list(self):
        """
        获取待处理的img的完整路径
        :return:
        """
        self.imgs_list = []
        self.output_path_list = []
        for item in self.task_list:
            file_path = os.path.join(self.WORK_PATH, item[0])
            imgs_path = os.path.join(file_path, "images")
            files_list = os.listdir(imgs_path)
            filter_files_list = [os.path.join(imgs_path, file) for file in files_list if file.endswith('.jpg')]
            if len(filter_files_list) == 0:
                raise Exception("no img in file:{}")
            self.imgs_list.extend(filter_files_list)
        return self.imgs_list, self.output_path_list

    def _process_model(self):
        """
        处理模型虚函数
        """
        pass

    def _upload_afs(self):
        """
        上传AFS, 更新 table
            压缩
            检测不存在，压缩文件夹
            检测AFS中不存在，上传文件夹
            删除 zip, tar.gz
        :return:
        """
        for item in self.task_list:
            # 压缩
            zip_file = str(item[0]) + "_" + self.MODEL_NAME + ".zip"
            os.system(f'cd {self.WORK_PATH}; zip -r {zip_file} {str(item[0])}')
            os.system(f'cd {self.WORK_PATH}; rm -rf {str(item[0])}')
            zip_path = os.path.join(self.WORK_PATH, zip_file)
            #
            res = os.system(f'/home/<USER>/afs/bin/afsshell --username={AFS_CONFIG["username"]} '
                            f'--password={AFS_CONFIG["password"]} ls afs://aries.afs.baidu.com:9902'
                            f'/user/map-data-streeview/aoi-ml/build/tile_map/ | grep {zip_file}')
            if res == 0:
                # 存在文件夹
                os.system(f'/home/<USER>/afs/bin/afsshell --username={AFS_CONFIG["username"]} '
                          f'--password={AFS_CONFIG["password"]} rm afs://aries.afs.baidu.com:'
                          f'9902/user/map-data-streeview/aoi-ml/build/tile_map/{zip_file}')
            res = os.system(
                f'/home/<USER>/afs/bin/afsshell --username={AFS_CONFIG["username"]} --password={AFS_CONFIG["password"]} '
                f'put {zip_path}  '
                f'afs://aries.afs.baidu.com:9902/user/map-data-streeview/aoi-ml/build/tile_map/')
            if res == 0:
                os.system(f'rm {zip_path}')
                gz_path = os.path.join(self.WORK_PATH, str(item[0]) + ".tar.gz")
                os.system(f'rm {gz_path}')
            self._update_table(item[0], "IDENTIFIED", None)

    def _update_table(self, tid, status, err):
        """
        更新mysql
        """

        try:
            self.mysql_conn.close()
        except Exception as e:
            pass

        self.mysql_conn = pymysql.connect(**MYSQL)
        self.mysql_cur = self.mysql_conn.cursor()

        if status == "FAILED":
            sql = f"update {self.TABLE_NAME} set status = '{status}', remark = 'Identify:{err}' where id = {tid}"
        else:
            sql = f"update {self.TABLE_NAME} set status = '{status}' where id = {tid}"
        self.mysql_cur.execute(sql)
        self.mysql_conn.commit()


class VisMulti(CommonMulti):
    """
    处理vis模型
    """
    def _get_imgs_path_list(self):
        """
        获取待处理的img的完整路径
        :return:
        """
        self.img_files_list = []
        err_list = []
        for item in self.task_list:
            file_path = os.path.join(self.WORK_PATH, str(item[0]))
            imgs_path = os.path.join(file_path, "images")
            files_list = os.listdir(imgs_path)
            filter_files_list = [os.path.join(imgs_path, file) for file in files_list if file.endswith('.jpg')]
            try:
                if len(filter_files_list) == 0:
                    raise Exception(f"no img in file {item[0]}")
            except Exception as e:
                self._update_table(item[0], "FAILED", e)
                err_list.append(err_list)
                continue
            list_file_path = self._save_as_file(filter_files_list, file_path)
            self.img_files_list.append(list_file_path)
            self._set_config(file_path, self.MODEL_NAME, VECTORIZE_TYPE['vis'])
        # 删除task_list中err的list
        self.task_list = [item for item in self.task_list if item not in err_list]
        if len(self.img_files_list) != len(self.task_list):
            raise Exception("list_files_file and task_list not equal")
        return self.img_files_list


    def _save_as_file(self, file_list, path):
        """
        保存为文件
        """
        list_file_path = os.path.join(path, "img_list")
        with open(list_file_path, "w") as f:
            for idx in range(len(file_list)):
                info_path = os.path.join(path, os.path.basename(file_list[idx])[:-4])
                if not os.path.exists(info_path):
                    os.makedirs(info_path)
                f.write(file_list[idx] + "\t" + path + "\n")
        return list_file_path


    def set_gpu_for_vis_multi(self):
        """
        设置vis的gpu - 废弃
        """
        def _set_toml(path):
            with open(path, "r") as f:
                config = toml.load(f)
            config["x86_roof_segmentation"]["gpu_device_id"] = self.GPU

            with open(path, "w") as f:
                toml.dump(config, f)

        rd_path = Path(self.MODEL_PATH) / "config" / "roof_detect" / "config.toml"
        _set_toml(rd_path)
        rs_path = Path(self.MODEL_PATH) / "config" / "roof_segmentation" / "config.toml"
        _set_toml(rs_path)

    def _process_model_bak(self):
        """
        处理模型 - 废弃
        """
        sh_path = os.path.join(self.MODEL_PATH, "run.sh")
        self.set_gpu_for_vis_multi()
        res = os.system(f'sh {sh_path} 0 {self.imgs_list} {self.output_path_list}')
        if res != 0:
            raise Exception("vis_multi roof detect failed")
        res = os.system(f'sh {sh_path} 1 {self.imgs_list} {self.output_path_list}')
        if res != 0:
            raise Exception("vis_multi roof segment failed")
    
    def set_gpu_for_vis2(self, path):
        """
        设置vis的gpu
        """
        def _set_toml(path, type1):
            with open(path, "r") as f:
                config = toml.load(f)
            if type1 == 'rd':
                config["x86_roof_detect"]["gpu_device_id"] = self.GPU
            else:
                config["x86_roof_segmentation"]["gpu_device_id"] = self.GPU

            with open(path, "w") as f:
                toml.dump(config, f)

        rd_path = Path(path) / "config" / "roof_detect" / "config.toml"
        _set_toml(rd_path, 'rd')
        rs_path = Path(path) / "config" / "roof_segmentation" / "config.toml"
        _set_toml(rs_path, 'rs')

    def process_bak(self):
        """
        处理入口函数
        """
        path = "/home/<USER>/liuaonan/roof-recognition-sdk-v1.0"
        self.set_gpu_for_vis2(path)
        res = os.system(f"python /home/<USER>/liuaonan/aoi-ml/src/building_segmentation2/multi-model-vis2.py")
        if res != 0:
            print("run multi-model-vis2.py failed")

    def _process_modeli_single(self):
        """
        处理模型
        """
        err_list = []
        sh_path = os.path.join(self.MODEL_PATH, "run.sh")
        self.set_gpu_for_vis2(self.MODEL_PATH)
        for idx in range(len(self.img_files_list)):
            try:
                res = os.system(f'sh {sh_path} 0 {self.img_files_list[idx]} {self.MODEL_PATH}')
                if res != 0:
                    raise Exception("vis_multi roof detect failed")
                
                res = os.system(f'sh {sh_path} 1 {self.img_files_list[idx]} {self.MODEL_PATH}')
                if res != 0:
                    raise Exception("vis_multi roof segment failed")
            except Exception as e:
                self._update_table(self.task_list[idx][0] ,"FAILED", e)
                err_list.append(self.task_list[idx])
                continue
        # 删除task_list中err的list
        self.task_list = [item for item in self.task_list if item not in err_list]

    def _process_model(self):
        sh_path = os.path.join(self.MODEL_PATH, "run.sh")
        self.set_gpu_for_vis2(self.MODEL_PATH)

        group_list_path = os.path.join(self.WORK_PATH, str(self.task_list[0][0]), "group_img_list")
        group_list = []
        for idx in range(len(self.img_files_list)):
            with open(self.img_files_list[idx], "r") as f:
                data = f.readlines()
                group_list.extend(data)
        with open(group_list_path, "w") as f:
            for item in group_list:
                f.write(item)
        
        try:
            res = os.system(f'sh {sh_path} 0 {group_list_path} {self.MODEL_PATH}')
            if res != 0:
                raise Exception("vis_multi roof detect failed")
            
            res = os.system(f'sh {sh_path} 1 {group_list_path} {self.MODEL_PATH}')
            if res != 0:
                raise Exception("vis_multi roof segment failed")
        except Exception as e:
            self._update_table(self.task_list[idx][0] ,"FAILED", e)


class SegMulti(CommonMulti):
    """
    分割模型
    """
    def _get_imgs_path_list(self):
        """
        获取待处理的img的完整路径
        :return:
        """
        self.SEG_IMG_DIR = "google-2022"
        self.imgs_list = []
        self.output_path_list = []
        err_list = []
        for item in self.task_list:
            file_path = os.path.join(self.WORK_PATH, str(item[0]))
            imgs_path = os.path.join(os.path.join(file_path, "images"), self.SEG_IMG_DIR)
            try:
                if not os.path.exists(imgs_path):
                    raise Exception(f"no img file")
            except Exception as e:
                self._update_table(item[0], "FAILED", e)
                err_list.append(item)
                continue
            files_list = os.listdir(imgs_path)
            filter_files_list = [os.path.join(imgs_path, file) for file in files_list if file.endswith('.jpg')]
            try:
                if len(filter_files_list) == 0:
                    raise Exception(f"no img in file")
            except Exception as e:
                print(e)
                self._update_table(item[0], "FAILED", e)
                err_list.append(item)
                continue
            self.imgs_list.extend(filter_files_list)
            self._set_config(file_path, self.MODEL_NAME, VECTORIZE_TYPE['seg'])
        # 删除task_list中err的list
        self.task_list = [item for item in self.task_list if item not in err_list]
        print(self.task_list)
        if len(self.task_list) == 0:
            raise Exception(f"file_list is none")
        for item in self.imgs_list:
            self.output_path_list.append(os.path.dirname(os.path.dirname(item)))
        self._save_img_list_as_txt()
        return self.imgs_list, self.output_path_list

    def _process_model(self):
        """
        调用函数
        """
        seg_log_path = Path(self.WORK_PATH) / "seg_multi.log"
        seg_log_path = Path(self.WORK_PATH) / "seg_multi.log"
        res = os.system(
            f"export CUDA_VISIBLE_DEVICES={self.GPU};cd {self.MODEL_PATH};sh {self.MODEL_PATH}/auto-{self.MODEL_NAME}.sh {self.imgs_list_txt_path} {self.common_save_path} >> {seg_log_path}")
        if res != 0:
            for item in self.task_list:
                e = "identify:group err:seg_multi failed"
                self._update_table(item[0], "FAILED", e)
            raise Exception("identify:group err:seg_multi failed")    
        self._deliever_img_to_file()

    def _save_img_list_as_txt(self):
        """
        保存图片列表到txt中
        :return:
        """
        key_item = self.task_list[0]
        self.common_save_path = os.path.join(self.WORK_PATH, str(key_item[0])+"_common")
        if os.path.exists(self.common_save_path):
            os.system(f"rm -rf {self.common_save_path}")
        os.system(f"mkdir {self.common_save_path}")
        self.imgs_list_txt_path = os.path.join(self.common_save_path, "imgs_list.txt")
        with open(self.imgs_list_txt_path, "w") as f:
            for item in self.imgs_list:
                f.write(item+"\n") 

    def _deliever_img_to_file(self):
        """
        分发图片
        """
        for i in range(len(self.imgs_list)):
            seg_img_path = Path(self.common_save_path) / "heatmap_prediction/2"
            # seg_img_path = Path(self.common_save_path) / "heatmap_prediction/2" / self.imgs_list[i]
            output_path = Path(os.path.dirname(self.output_path_list[i])) / "heatmap_prediction/2/images/google-2022" 
            if not os.path.exists(output_path):
                os.system(f"mkdir {Path(os.path.dirname(self.output_path_list[i])) / 'heatmap_prediction/'}")
                os.system(f"mkdir {Path(os.path.dirname(self.output_path_list[i])) / 'heatmap_prediction/2/'}")
                os.system(f"mkdir {Path(os.path.dirname(self.output_path_list[i])) / 'heatmap_prediction/2/images'}")
                os.system(f"mkdir {output_path}")
            img_name = os.path.basename(self.imgs_list[i]).replace("jpg", "png")
            os.system(f"cd {seg_img_path};cd ./{os.path.dirname(self.imgs_list[i])};mv {img_name} {output_path}")

            seg_img_path = Path(self.common_save_path) / "heatmap_prediction/1"
            # seg_img_path = Path(self.common_save_path) / "heatmap_prediction/2" / self.imgs_list[i]
            output_path = Path(os.path.dirname(self.output_path_list[i])) / "heatmap_prediction/1/images/google-2022" 
            if not os.path.exists(output_path):
                #os.system(f"mkdir {Path(os.path.dirname(self.output_path_list[i])) / 'heatmap_prediction/'}")
                os.system(f"mkdir {Path(os.path.dirname(self.output_path_list[i])) / 'heatmap_prediction/1/'}")
                os.system(f"mkdir {Path(os.path.dirname(self.output_path_list[i])) / 'heatmap_prediction/1/images'}")
                os.system(f"mkdir {output_path}")
            img_name = os.path.basename(self.imgs_list[i]).replace("jpg", "png")
            os.system(f"cd {seg_img_path};cd ./{os.path.dirname(self.imgs_list[i])};mv {img_name} {output_path}")
            
        os.system(f"rm -rf {self.common_save_path}")
            

def main():
    """
    测试类主函数
    """
 #   model_name = 'segformer_dv2'
 #   model_path = '/home/<USER>/liuaonan/aoi-ml/src/building_segmentation2/segment_model/zyxpaddleseg'
 #   gpu = 2
 #   task_group_num = 20
 #   work_path = '/home/<USER>/liuaonan/roof-recognition-sdk-v1.0'
 #   vis = SegMulti(model_name, model_path, gpu, task_group_num, work_path)
 #   vis.process()
 #   vis.end()

    model_name = 'vis_v1'
    model_path = '/home/<USER>/liuaonan/roof-recognition-sdk-v1.0'
    gpu = 2
    task_group_num = 20
    work_path = '/home/<USER>/liuaonan/roof-recognition-sdk-v1.0'
    vis = VisMulti(model_name, model_path, gpu, task_group_num, work_path)
    vis.process()
    vis.end()

if __name__ == "__main__":
    main()


