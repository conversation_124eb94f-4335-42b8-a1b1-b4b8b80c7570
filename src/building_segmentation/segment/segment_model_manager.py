"""
语义分割调度类
"""

import multiprocessing
import os
import re

from multiprocessing import Pool

from segment_model import VisMulti, SegMulti

class ModelManager:
    """
    语义分割调度类
    """
    def __init__(self, seg_model_path="/home/<USER>/liuaonan/aoi-ml/src/building_segmentation2/segment_model",
                 vis_model_path="/home/<USER>/liuaonan/roof-recognition-sdk-v1.0",
                 work_path="/home/<USER>/liuaonan/roof-recognition-sdk-v1.0",
                 task_group_num=20):
        # 每次运行task数量
        self.TASK_GROUP_NUM = task_group_num
        self.WORK_PATH = work_path

        # 初始化 模型类别 队列
        self.VIS_MODEL_PATH = vis_model_path
        self.model_type = [["vis_v1", self.VIS_MODEL_PATH]]
        self._search_for_auto_model(seg_model_path)
        manager= multiprocessing.Manager()
        self.model_queue = manager.Queue()
        for item in self.model_type:
            self.model_queue.put(item)

        # 初始化 卡号 列表
        self.gpu_list = [1, 2]
        self.NUM_PROCESS = len(self.gpu_list)

        # 初始化多线程参数
        self.params = []
        for i in range(len(self.gpu_list)):
            self.params.append([self.gpu_list[i], self.model_queue])

    def process(self):
        """
        入口主函数
        """
        with Pool(self.NUM_PROCESS) as pool:
            pool.map(self._segment_model_per_gpu, self.params)


    def _search_for_auto_model(self, seg_model_path):
        """
        模型搜索函数
        """
        pattern = re.compile(r'^auto-.+\.sh$')
        for root, dirs, files in os.walk(seg_model_path, followlinks=True):
            for file in files:
                if pattern.match(file):
                    model_name = file.replace("auto-", "").replace(".sh", "")
                    self.model_type.append([model_name, root])


    def _segment_model_per_gpu(self, params):
        """
        以GPU为维度，启动多线程
        """
        num_gpu = params[0]
        while params[1].qsize() > 0:
            item = params[1].get()
            print(num_gpu, item[0])
            if item[0] == "vis_v1":
                model = VisMulti(item[0], item[1], num_gpu, self.TASK_GROUP_NUM, self.WORK_PATH)
            else:
                model = SegMulti(item[0], item[1], num_gpu, self.TASK_GROUP_NUM, self.WORK_PATH)
            if model.notask():
                continue
            else:
                model.process()
                model.end()
                params[1].put(item)

def main():
    """
    语义分割主函数
    """
    mm = ModelManager()
    mm.process()


if __name__ == "__main__":
    main()
