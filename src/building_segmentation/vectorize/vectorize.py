"""
矢量化-矢量化处理类
"""
import json
import psycopg2
import pymysql
import os
import sys
import uuid

from multiprocessing import Pool
from pathlib import Path
from PIL import Image
from osgeo import ogr
from tqdm import tqdm

from vectorize_util import bgr2tag, building_segmentation_vectorize, delete_DN
from vectorize_util import trans_detect, trans_2_gcj, merge_segment, get_block_str, get_config_json

sys.path.append(os.path.abspath((os.path.dirname(__file__)) + "/../../../"))
from src.seg_post_process import contexts
from src.seg_post_process.main import execute as seg_post_process
from src.building_segmentation.config.db_config import PRE_MY_DB, PRE_POI_ONLINE, ONLINE_MY_DB, ONLINE_POI_ONLINE
from src.building_segmentation.config.db_config import ONLINE_BUD_PG
from src.building_segmentation.config.afs_config import AFS_CONFIG
from src.tools.utils import get_newest_subdir_name


#MYSQL = PRE_MY_DB
#PGSQL = PRE_POI_ONLINE

MYSQL = ONLINE_MY_DB
PGSQL = ONLINE_BUD_PG

OPERA_AFS_PATH = '/home/<USER>/aoiMl/afs/output/bin/afsshell'
#DE16_AFS_PATH = '/home/<USER>/afs-api/bin/afsshell'

AFS_PATH = OPERA_AFS_PATH

def check_oversize_image(img_list):
    """
    检查过大图片
    """
    max_size = 4096 
    for item in img_list:
        try:
            image = Image.open(item)
        except Exception as e:
            raise Exception(f"img oversize:{e}")
        if image.size[0] > max_size or image.size[1] > max_size:
            return False
    return True

class VecTask:
    """
    矢量化基类
    """
    def __init__(self, work_path="/home/<USER>/liuaonan/roof_segment"):
        self.WORK_PATH = work_path

        self.PLAN_TYPE = 47
        self.TABLE_NAME = "basic_feature_pre_plan"

        self.mysql_conn = pymysql.connect(**MYSQL)
        self.mysql_cur = self.mysql_conn.cursor()

        self.pg_conn = psycopg2.connect(**PGSQL)
        self.pg_cur = self.pg_conn.cursor()

        self.img_list = []

    def end(self):
        """
        结束链接
        """
        self.mysql_conn.close()
        self.pg_conn.close()

    def process(self, tid, result_url, outside_id):
        """
        主要处理函数
        """
        try:
            self.dir_path = self._download_from_afs(result_url, outside_id)
            self._vectorize()
            for img in self.img_list:
                image_hash = img.split(".")[0]
                json_path = os.path.join(self.WORK_PATH, image_hash, "gray_roof_segmentation_result_filtered_gcj.json")
                block_json_path = os.path.join(self.WORK_PATH, f"json_info/image/{image_hash}.json")
                block_str = get_block_str(block_json_path)
                config_path = os.path.join(self.WORK_PATH, image_hash, "config.json")
                config_json = get_config_json(config_path)
                self._upload_pg(tid, json_path, block_str, config_json)
        except Exception as e:
            self._update_table(tid, "FAILED", e)
        else:
            self._update_table(tid, "VECTORED", None)


    def _download_from_afs(self, url, model_name):
        """
        从AFS下载数据
        """
        image_name = os.path.basename(url)
        image_hash = image_name.split(".")[0]
        url_zip = url.replace(".tar.gz", f"_{model_name}.zip")
        self.url_zip = url_zip
        file_path = os.path.join(self.WORK_PATH, image_hash)
        zip_path = os.path.join(self.WORK_PATH, os.path.basename(url_zip))
        if os.path.exists(file_path):
            os.system(f'rm -rf {file_path}')
            os.remove(zip_path)
        os.system(f'{AFS_PATH} --username={AFS_CONFIG["username"]} '
                  f'--password={AFS_CONFIG["password"]} get afs://aries.afs.baidu.com:9902{url_zip} {self.WORK_PATH}')
        os.system(f'unzip {zip_path} -d {self.WORK_PATH}')
        return file_path

    def _vectorize(self):
        """
        执行矢量化
        """
        pass

    def _upload_pg(self, tid, js_path, block_str, config_json):
        """
        上传PG
        """
        pass

    def _update_table(self, tid, status, err):
        """
        更新mysql表
        """
        try:
            self.mysql_conn.close()
        except Exception as e:
            pass

        self.mysql_conn = pymysql.connect(**MYSQL)
        self.mysql_cur = self.mysql_conn.cursor()
        err = str(err)
        err = err.replace("'", " ")
        if status == "FAILED":
            sql = f"update {self.TABLE_NAME} set status = '{status}', remark = 'Vectored:{err}' where id = {tid}"
        else:
            sql = f"update {self.TABLE_NAME} set status = '{status}' where id = {tid}"
        self.mysql_cur.execute(sql)
        self.mysql_conn.commit()

def _upload_pg(PGSQL, tid, js_path, block_str, config_json):
    """
    vis 上传pg
    """
    pg_conn = psycopg2.connect(**PGSQL)
    pg_cur = pg_conn.cursor()

    with open(js_path, 'r') as f:
        features = json.load(f)['features']

    if len(features) == 0:
        return
    sql = (f'insert into bud_identify_res (guid, batchno, geom, roof_style, step, create_time,'
            f' update_time, block, model_type, version) '
            f'values ')
    for geom in tqdm(features, desc='upload pg'):
        g = ogr.CreateGeometryFromJson(json.dumps(geom['geometry']))
        uuid_hex = uuid.uuid4().hex
        sql = sql + f"""('{uuid_hex}', {tid}, st_makevalid(st_setsrid(st_geomfromtext('{g}'), 4326)),
                        {geom["properties"]["DN"]} , 'VECTORED', now(), now(), '{block_str}', 
                        '{config_json["model_type"]}', '{config_json["vectorize_type"]}'),"""
    pg_cur.execute(sql[:-1])
    pg_conn.commit()


def vectorize_vis(params):
    """
    vis多线程矢量化
    """
    dir_path, tid, pgsql, img = params
    res_dir = os.path.join(dir_path, img[:-4])
    out_img_pth = os.path.join(res_dir, "roof_segmentation_result.png")
    gray_pth = bgr2tag(out_img_pth)
    if gray_pth == 0:
        return
    json_path = os.path.join(dir_path, f"json_info/image/{img.split('.')[0]}.json")
    with open(json_path, 'r') as f:
        origin_wkt = json.load(f)['wgs_geom']
    js_path, trans = building_segmentation_vectorize(gray_pth, origin_wkt, res_dir)
    delete_DN(js_path, res_dir)
    trans_2_gcj(res_dir)

    image_hash = img.split(".")[0]
    json_path = os.path.join(dir_path, image_hash, "gray_roof_segmentation_result_filtered_gcj.json")
    block_json_path = os.path.join(dir_path, f"json_info/image/{image_hash}.json")
    block_str = get_block_str(block_json_path)
    config_json = get_config_json(dir_path)
    _upload_pg(pgsql, tid, json_path, block_str, config_json)


class VecVis(VecTask):
    """
    vis矢量化
    """
    def _vectorize(self):
        """
        vis矢量化
        """
        #for img in self.imgs_list:
        for img in tqdm(self.imgs_list):
            res_dir = os.path.join(self.dir_path, img[:-4])
            out_img_pth = os.path.join(res_dir, "roof_segmentation_result.png")
            gray_pth = bgr2tag(out_img_pth)
            if gray_pth == 0:
                continue
            json_path = os.path.join(self.dir_path, f"json_info/image/{img.split('.')[0]}.json")
            with open(json_path, 'r') as f:
                origin_wkt = json.load(f)['wgs_geom']
            js_path, trans = building_segmentation_vectorize(gray_pth, origin_wkt, res_dir)
            delete_DN(js_path, res_dir)

            # detect_out = os.path.join(res_dir, "roof_detect_result.out")
            # detect_trans_out = os.path.join(res_dir, "roof_detect_result_trans.out")
            # trans_detect(detect_out, detect_trans_out, trans)
            trans_2_gcj(res_dir)
            #img_path = os.path.join(os.path.join(self.dir_path, "images"), img)
            #merge_segment(img_path, res_dir)

    def _upload_pg(self, tid, js_path, block_str, config_json):
        """
        上传PG
        """
        try:
            self.pg_conn.close()
        except Exception as e:
            1

        self.pg_conn = psycopg2.connect(**PGSQL)
        self.pg_cur = self.pg_conn.cursor()

        with open(js_path, 'r') as f:
            features = json.load(f)['features']
        #for geom in features:
        #    g = ogr.CreateGeometryFromJson(json.dumps(geom['geometry']))
        #    uuid_hex = uuid.uuid4().hex
        #    sql = (f'insert into bud_identify_res (guid, batchno, geom, roof_style, step, create_time,'
        #           f' update_time, block, model_type, version) '
        #           f'values (\'{uuid_hex}\', '
        #           f'{tid}, st_makevalid(st_setsrid(st_geomfromtext(\'{g}\'), 4326)), {geom["properties"]["DN"]}'
        #           f', \'VECTORED\', now(), now(), \'{block_str}\', '
        #           f'\'{config_json["model_type"]}\', \'{config_json["vectorize_type"]}\')')
        #    self.pg_cur.execute(sql)
        if len(features) == 0:
            return
        sql = (f'insert into bud_identify_res (guid, batchno, geom, roof_style, step, create_time,'
               f' update_time, block, model_type, version) '
               f'values ')
        for geom in tqdm(features, desc='upload pg'):
            g = ogr.CreateGeometryFromJson(json.dumps(geom['geometry']))
            uuid_hex = uuid.uuid4().hex
            sql = sql + f"""('{uuid_hex}', {tid}, st_makevalid(st_setsrid(st_geomfromtext('{g}'), 4326)),
                            {geom["properties"]["DN"]} , 'VECTORED', now(), now(), '{block_str}', 
                            '{config_json["model_type"]}', '{config_json["vectorize_type"]}'),"""
        self.pg_cur.execute(sql[:-1])
        self.pg_conn.commit()


    def _get_imgs_list(self):
        """
        获取图片列表
        """
        imgs_path = os.path.join(self.dir_path, "images")
        files_list = os.listdir(imgs_path)
        filter_files_list = [file for file in files_list if file.endswith('.jpg')]
        return filter_files_list

    def process_single(self, tid, result_url, outside_id):
        """
        主处理函数
        """
        try:
            self.dir_path = self._download_from_afs(result_url, outside_id)
            #self.dir_path = '/home/<USER>/liuaonan/roof_segment/58917'
            self.imgs_list = self._get_imgs_list()
            self._vectorize()
            for img in self.imgs_list:
                image_hash = img.split(".")[0]
                json_path = os.path.join(self.dir_path, image_hash, "gray_roof_segmentation_result_filtered_gcj.json")
                block_json_path = os.path.join(self.dir_path, f"json_info/image/{image_hash}.json")
                block_str = get_block_str(block_json_path)
                config_json = get_config_json(self.dir_path)
                self._upload_pg(tid, json_path, block_str, config_json)
            # os.system(f"rm -rf {self.dir_path}")
            # os.system(f"rm -rf {self.dir_path}_vis_v1.zip")
        except Exception as e:
            print(e)
            self._update_table(tid, "FAILED", e)
        else:
            self._update_table(tid, "VECTORED", None)
        finally:
            if os.path.exists(self.dir_path):
                os.system(f"rm -rf {self.dir_path}")
                os.system(f"rm -rf {self.dir_path}_vis_v1.zip")

    def process(self, tid, result_url, outside_id):
        process_num = 4
        try:
            process_params = []
            self.dir_path = self._download_from_afs(result_url, outside_id)
            self.imgs_list = self._get_imgs_list()
            for img in self.imgs_list:
                process_params.append([self.dir_path, tid, PGSQL, img])
            with Pool(process_num) as pool:
                pool.map(vectorize_vis, process_params)
        except Exception as e:
            print(e)
            self._update_table(tid, "FAILED", e)
        else:
            self._update_table(tid, "VECTORED", None)
        finally:
            if os.path.exists(self.dir_path):
                os.system(f"rm -rf {self.dir_path}")
                os.system(f"rm -rf {self.dir_path}_vis_v1.zip")


def _vectorize_single(img, dir_path):
    """
    用于多线程的单处理函数
    """
    try:
        path_info = new_path_info(dir_path, img)
        seg_post_process(path_info, image_type="heatmap-adaptive")
    except Exception as e:
        #self._update_table(self.tid, "FAILED", e)
        #raise Exception(e)
        return e
    return None


def new_path_info(dir_path, img) -> contexts.PathInfo:
    """
    初始化路径信息，若目录下的照片信息不符合要求，会对外抛出异常
    """
    model_img_dir = dir_path + "/heatmap_prediction/2"

    self_model_img_dir = os.path.join(model_img_dir, "images")
    if os.path.exists(self_model_img_dir):
        model_img_dir = os.path.join(self_model_img_dir, get_newest_subdir_name(self_model_img_dir))

    path = [Path(model_img_dir) / img]
    for i in range(len(path)):
        path[i] = str(path[i]).replace("jpg", "png")
    if not check_oversize_image(path):
        raise Exception("img oversize 4096")

    path_info = contexts.PathInfo(
        image_dir=Path(model_img_dir),
        image_info_dir=Path(dir_path + "/json_info/image"),
        output_dir=Path(dir_path + "/output_vectorize"),
    )
    return path_info


class VecSeg(VecTask):
    """
    seg矢量化
    """
    def _vectorize_multi(self):
        """
        多进程矢量化
        """
        pnum = 5
        self._get_img_list()
        with Pool(pnum) as p:
            ret = p.starmap(_vectorize_single, [(img, self.dir_path) for img in self.imgs_list])
            for res in ret:
                if res is not None:
                    self._update_table(self.tid, "FAILED", res)

    def _vectorize_single(self, img):
        """
        更新
        """
        try:
            path_info = new_path_info(self.dir_path, img)
            seg_post_process(path_info, image_type="heatmap-adaptive")
        except Exception as e:
            self._update_table(self.tid, "FAILED", e)
            raise Exception(e)
        
    def _vectorize(self):
        """
        单进程矢量化
        """
        self._get_img_list()
        for img in self.imgs_list:
            try:
                path_info = new_path_info(self.dir_path, img)
            except Exception as e:
                self._update_table(self.tid, "FAILED", e)
                raise Exception(e)
            seg_post_process(path_info, image_type="heatmap-adaptive")
            break  # 只需要跑一次，目录下的图片就都可以矢量化好

    def _upload_pg(self, tid, js_path, block_str, config_json):
        """
        上传PG
        """
        try:
            self.pg_conn.close()
        except Exception as e:
            1
            
        self.pg_conn = psycopg2.connect(**PGSQL)
        self.pg_cur = self.pg_conn.cursor()

        with open(js_path, 'r') as f:
            features = json.load(f)['features']
        #for geom in tqdm(features, desc='upload pg'):
        #    g = ogr.CreateGeometryFromWkt(geom['geom'])
        #    uuid_hex = uuid.uuid4().hex
        #    sql = (f'insert into bud_identify_res (guid, batchno, geom, step, create_time,'
        #           f' update_time, block, model_type, version) '
        #           f'values (\'{uuid_hex}\', '
        #           f'{tid}, st_makevalid(st_setsrid(st_geomfromtext(\'{g}\'), 4326)), '
        #           f'\'VECTORED\', now(), now(), \'{block_str}\', '
        #           f'\'{config_json["model_type"]}\', \'{config_json["vectorize_type"]}\')')
        #    self.pg_cur.execute(sql)
        if len(features) == 0:
            return
        sql = (f'insert into bud_identify_res (guid, batchno, geom, step, create_time,'
               f' update_time, block, model_type, version) '
               f'values ')
        for geom in tqdm(features, desc='upload pg'):
            g = ogr.CreateGeometryFromWkt(geom['geom'])
            uuid_hex = uuid.uuid4().hex
            sql = sql + f"""('{uuid_hex}', {tid}, st_makevalid(st_setsrid(st_geomfromtext('{g}'), 4326)),
                            'VECTORED', now(), now(), '{block_str}', 
                            '{config_json["model_type"]}', '{config_json["vectorize_type"]}'),"""
        self.pg_cur.execute(sql[:-1])
        self.pg_conn.commit()

    def _get_img_list(self):
        """
        获取图片列表
        """
        imgs_root = os.path.join(self.dir_path, "images")
        imgs_path = os.path.join(imgs_root, get_newest_subdir_name(imgs_root))
        files_list = os.listdir(imgs_path)
        filter_files_list = [file for file in files_list if file.endswith('.jpg')]
        self.imgs_list = filter_files_list

    def process(self, tid, result_url, outside_id):
        """
        主处理函数
        """
        try:
            self.tid = tid
            self.dir_path = self._download_from_afs(result_url, outside_id)
            self._vectorize()
            #self._vectorize_multi()
            for img in self.imgs_list:
                image_hash = img.split(".")[0]
                json_path = os.path.join(self.dir_path, "output_vectorize", "vectorize_info", f"{image_hash}.json")
                if not os.path.exists(json_path):
                    raise Exception("failed to create json")
                block_json_path = os.path.join(self.dir_path, f"json_info/image/{image_hash}.json")
                block_str = get_block_str(block_json_path)
                config_json = get_config_json(self.dir_path)
                self._upload_pg(tid, json_path, block_str, config_json)
        except Exception as e:
            print(e)
            self._update_table(tid, "FAILED", e)
        else:
            self._update_table(tid, "VECTORED", None)
        finally:
            if os.path.exists(self.dir_path):
                dir_zip = Path(os.path.dirname(self.dir_path)) / os.path.basename(self.url_zip)
                os.system(f"rm -rf {self.dir_path}")
                os.system(f"rm -rf {dir_zip}")


def main():
    """
    用于类测试的主函数
    """
    tid = '10497'
    result_url = f'/user/map-data-streeview/aoi-ml/build/tile_map/{tid}.tar.gz'
    outside_id = 'segformer_dv2'
    vec = VecSeg()
    vec.process(tid, result_url, outside_id)

 #   tid = '6768'
 #   result_url = '/user/map-data-streeview/aoi-ml/build/tile_map/6768.tar.gz'
 #   outside_id = 'vis_v1'
 #   vec = VecVis()
 #   vec.process(tid, result_url, outside_id)


if __name__ == "__main__":
    main()

