"""
矢量化-多进程调度
"""
import os
import time

import psycopg2
import pymysql
import random
import redis
import sys
import uuid

from multiprocessing import Pool
from pathlib import Path

from vectorize import VecVis, VecSeg
sys.path.append(os.path.abspath((os.path.dirname(__file__)) + "/../../"))
from building_segmentation.config.db_config import PRE_MY_DB, ONLINE_MY_DB, ONLINE_REDIS, OPERA_REDIS
from tools.locker import lock_guardian_with_retry, RedisLocker, lock_guardian
from tools.utils import ensure_dir

#MYSQL = PRE_MY_DB
MYSQL = ONLINE_MY_DB

#REDIS = ONLINE_REDIS
REDIS = OPERA_REDIS

DE16_WORK_PATH = '/home/<USER>/liuaonan/roof_segment'
OPERA_WORK_PATH = '/home/<USER>/aoiMl/src/building_segmentation/vectorize/data'

WORK_PATH = OPERA_WORK_PATH

def _search_for_update_fun(mysql_conf, search_sql, update_sql):
    """
    查询更新操作
    """
    conn = pymysql.connect(**mysql_conf)
    cur = conn.cursor()
    cur.execute(search_sql)
    res = cur.fetchall()
    for item in res:
        sql1 = update_sql + str(item[0])
        cur.execute(sql1)
    conn.commit()
    cur.close()
    conn.close()
    return res

def check_params(mysql_conf, params):
    """
    查询更新操作
    """
    sql = "update basic_feature_pre_plan set status = '{}', remark = '{}' where plan_type = 47 and id = {};"
    conn = pymysql.connect(**mysql_conf)
    cur = conn.cursor()
    cur.execute(sql.format("FAILED", "result_url or outside_id is null", params[0]))
    conn.commit()
    cur.close()
    conn.close()


class VecManager:
    """
    矢量化-多进程调度
    """
    def __init__(self, work_path=WORK_PATH):

        self.WORK_PATH = work_path
        ensure_dir(Path(self.WORK_PATH))

        self.PLAN_TYPE = 47
        self.TABLE_NAME = "basic_feature_pre_plan"
        self.BEFORE_STATUS = 'IDENTIFIED'
        self.WORKING_STATUS = 'VECTORING'
        self.NUM_GROUP = 20
        self.NUM_PROCESS = 4
        
        self.REDIS_KEY = 'BUILDING_SEGMENT_VECTORING'
        self.REDIS_EXPIRE = 600

        self.task_list = []

    def process(self):
        """
        多进程类运行主函数
        """
        self._search_for_update()
        #self._search_for_update_lock()
        with Pool(self.NUM_PROCESS) as pool:
            pool.map(self._vectorize_for_per_task, self.task_list)

    def process_sig(self):
        """
        单进程
        """
        #self._search_for_update()
        self._search_for_update_lock()
        for item in self.task_list:
            if item[1] == "" or item[1] is None or item[2] == "" or item[2] is None:
                check_params(MYSQL, item)
                continue 
            self._vectorize_for_per_task(item)

    def _search_for_update_sql(self):
        sql = f"""select id,result_url,outside_id from {self.TABLE_NAME}
            where status='{self.BEFORE_STATUS}' and plan_type = {self.PLAN_TYPE} limit {self.NUM_GROUP};"""
        return sql

    def _set_for_update_sql(self):
        sql = f"""update {self.TABLE_NAME} set status = '{self.WORKING_STATUS}' 
                where id = """
        return sql

    def _search_for_update_lock(self):
        """
        搜索数据库中需要更新的数据 - 多进程锁
        """

        redis_conn = redis.Redis(**REDIS)
        redis_locker = RedisLocker(redis_conn, self.REDIS_KEY, self.REDIS_EXPIRE)

        retry_times = 10
        sleep_time = random.randint(0,200)/10.0
        search_sql = self._search_for_update_sql()
        set_sql = self._set_for_update_sql()
        self.task_list = lock_guardian_with_retry(redis_locker, retry_times, sleep_time, _search_for_update_fun, 
            MYSQL, search_sql, set_sql)
        redis_conn.close()


    def _search_for_update(self):
        """
        搜索数据库中需要更新的数据
        :return:
        """
        conn = pymysql.connect(**MYSQL)
        cur = conn.cursor()
        sql = f"""select id,result_url,outside_id from {self.TABLE_NAME} 
            where status='{self.BEFORE_STATUS}' and plan_type = {self.PLAN_TYPE};"""
            #where status='{self.BEFORE_STATUS}' and plan_type = {self.PLAN_TYPE} limit {self.NUM_GROUP};"""
        cur.execute(sql)
        res = cur.fetchall()
        cur.close()
        conn.close()
        self.task_list = res

    def _vectorize_for_per_task(self, params):
        """
        单元处理函数
        """
        if params[2] == 'vis_v1':
            vectorize_task = VecVis(self.WORK_PATH)
        else:
            vectorize_task = VecSeg(self.WORK_PATH)
        vectorize_task.process(params[0], params[1], params[2])
        vectorize_task.end()


def main():
    """
    调用主函数
    """
    times = 0
    while True:
        times += 1
        print(f"开始第{times}次执行")

        vm = VecManager()
        vm.process_sig()

        secs = random.randint(1, 10)
        print(f"开始第{times}次休眠; {secs}")
        time.sleep(secs)
        print(f"结束第{times}次休眠")

        # 超过一定次数后，为了避免日志爆炸，主动停止
        # 但这依赖，外部有再次启动的功能（启动后日志可能会重写），否则为了服务的可用性，不建议停止
        # 根据具体情况选择是否打开
        if times > 20:
            print(f"执行次数达到了:{times}; 避免日志爆炸，主动停止")
            break


if __name__ == "__main__":
    main()

