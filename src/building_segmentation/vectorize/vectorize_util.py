"""
矢量化工具
"""

import cv2
import json
import numpy as np
import os

from coord_convert.transform import wgs2gcj
from osgeo import ogr, gdal, gdalconst
from shapely import wkt

# 灰度转换关系
dict_gray = {
    38: 0,
    75: 1,
    113: 2,
    15: 3,
    53: 4,
    90: 5,
    128: 6,
    19: 7,
    57: 8
}

#像素参数
PIXEL = {
    'width': 0.000002682209014892578,
    'height': -0.000002682209014892578,
}


def bgr2tag(image_filepath, res_image_filepath=None):
    """
    彩色图像还原为标签灰度
    :param image_filepath:
    :param res_image_filepath:
    :return:
    """
    img = cv2.imread(image_filepath)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    tag_image = np.zeros(gray.shape, dtype=np.uint8)
    # 遍历像素
    #for row in range(gray.shape[0]):
    #    for col in range(gray.shape[1]):
    #        if gray[row, col] not in dict_gray:
    #            print(gray[row, col])
    #            return
    #        tag_image[row, col] = dict_gray[gray[row, col]]
    
    for pixel_value, tag_value in dict_gray.items():
        tag_image[gray == pixel_value] = tag_value

    if res_image_filepath is None:
        image_name = os.path.basename(image_filepath)
        image_hash = image_name.split(".")[0]
        res_image_filepath = os.path.join(os.path.dirname(image_filepath), f"gray_{image_hash}.png")
    cv2.imwrite(res_image_filepath, tag_image)
    return res_image_filepath

def building_segmentation_vectorize(image_filepath, origin_wkt, vertorize_result_dir,
                                    pixel_width=PIXEL['width'], pixel_height=PIXEL['height']):
    """
    建筑物矢量化
    :param image_filepath:  图片地址
    :param origin_wkt:  坐标
    :param vertorize_result_dir:  输出地址
    :return: 矢量化成果文件
    """
    gdal.AllRegister()
    ds = gdal.Open(image_filepath, gdalconst.GA_ReadOnly)
    # 设置地理坐标
    geom = wkt.loads(origin_wkt)
    minx, miny, maxx, maxy = geom.bounds
    # 左上角横坐标； 像元宽度/水平空间分辨率； 行旋转； 左上角纵坐标； 列旋转； 像元高度/垂直空间分辨率 * -1
    ds.SetGeoTransform((minx,
                        pixel_width,
                        0,
                        maxy,
                        0,
                        pixel_height))
    # 初始化
    image_name = os.path.basename(image_filepath)
    image_hash = image_name.split(".")[0]
    geojson_file = os.path.join(vertorize_result_dir, f"{image_hash}.json")
    if os.path.exists(geojson_file):
        os.remove(geojson_file)
    drv = ogr.GetDriverByName("GeoJSON")
    dst_ds = drv.CreateDataSource(geojson_file)
    # 创建图层和索引关键字
    fld = ogr.FieldDefn("DN", ogr.OFTInteger)
    dst_layer = dst_ds.CreateLayer(vertorize_result_dir, srs=None)
    dst_layer.CreateField(fld)
    dst_field = dst_layer.GetLayerDefn().GetFieldIndex("DN")
    # 波段，从1开始
    src_band = ds.GetRasterBand(1)
    options = ["8CONNECTED=8"]
    # 矢量化
    result = gdal.Polygonize(src_band, None, dst_layer, dst_field, options, callback=None)
    if result is None:
        raise ValueError(f'矢量化失败: {image_filepath}')
    return geojson_file, ds.GetGeoTransform()

def delete_DN(file_path, output_path):
    """
    筛选过滤标签为背景的图层
    :param file_path:
    :param output_path:
    :return:
    """
    # 筛选输入图层
    input_ds = ogr.Open(file_path)
    input_layer = input_ds.GetLayer()

    select_cond = "DN != '0'"
    input_layer.SetAttributeFilter(select_cond)

    # 初始化output
    drv = ogr.GetDriverByName("GeoJSON")
    image_name = os.path.basename(file_path)
    image_hash = image_name.split(".")[0]
    geojson_file = os.path.join(output_path, f"{image_hash}_filtered.json")
    if os.path.exists(geojson_file):
        os.remove(geojson_file)
    output_ds = drv.CreateDataSource(geojson_file)
    # 拷贝输出图层
    output_layer = output_ds.CopyLayer(input_layer, "output_layer")
    if output_layer is None:
        raise ValueError(f'拷贝输出图层失败: {geojson_file}')
    output_layer.SetAttributeFilter(None)
    output_layer.SyncToDisk()
    input_ds = None
    output_ds = None
    return geojson_file

def trans_detect(file_path, result_path, trans=[]):
    """
    检测框转换为地理坐标
    :param file_path:
    :param result_path:
    :param trans:
    :return:
    """
    data = []
    with open(file_path, 'r') as f:
        for l in f:
            data.append(l.strip().split(','))

    with open(result_path, 'w') as f:
        for d in data:
            px1 = trans[0] + float(d[2]) * trans[1] + float(d[3]) * trans[2]
            py1 = trans[3] + float(d[2]) * trans[4] + float(d[3]) * trans[5]
            px2 = trans[0] + float(d[4]) * trans[1] + float(d[5]) * trans[2]
            py2 = trans[3] + float(d[4]) * trans[4] + float(d[5]) * trans[5]

            output = "{},{},{},{},{},{}\n".format(d[0], d[1], px1, py1, px2, py2)
            f.write(output)

def trans_2_gcj(res_dir):
    """
    转换seg结果和detect结果为gcj坐标系
    :param res_dir:
    :return:
    """
    segment_detect_out = os.path.join(res_dir, "gray_roof_segmentation_result_filtered.json")
    detect_trans_out = os.path.join(res_dir, "roof_detect_result_trans.out")

    trans_segment_detect_out = os.path.join(res_dir, "gray_roof_segmentation_result_filtered_gcj.json")
    trans_detect_trans_out = os.path.join(res_dir, "roof_detect_result_trans_gcj.out")

    # 转换seg结果
    with open(segment_detect_out, 'r') as f:
        features = json.load(f)

    try:
        for i in range(0, len(features['features'])):
            for j in range(0, len(features['features'][i]['geometry']['coordinates'])):
                geom = features['features'][i]['geometry']['coordinates'][j]
                for k in range(0, len(geom)):
                    geom[k][0], geom[k][1] = wgs2gcj(geom[k][0], geom[k][1])
    except Exception as e:
        print(e)
        return False

    with open(trans_segment_detect_out, 'w') as f:
        f.write(json.dumps(features))

    # 转换detect
    # trans_detects = []
    # with open(detect_trans_out, 'r') as f:
    #     detects = f.readlines()

    # try:
    #     for detect in detects:
    #         data = [float(v) for v in detect[:-1].split(',')]
    #         data[2], data[3] = wgs2gcj(data[2], data[3])
    #         data[4], data[5] = wgs2gcj(data[4], data[5])
    #         trans_detects.append(data)
    # except Exception as e:
    #     print(e)
    #     return False

    # with open(trans_detect_trans_out, 'w') as f:
    #     for i in trans_detects:
    #         trans_line = ','.join(map(str, i))
    #         f.write(trans_line + "\n")
    return True


def merge_segment(origin_image_path, file_path):
    """
    合并segment结果和原始文件
    """
    segment_file = os.path.join(file_path, "roof_segmentation_result.png")
    output_file = os.path.join(file_path, "roof_segmentation_result_merge.png")
    segment_img = cv2.imread(segment_file)
    origin_img = cv2.imread(origin_image_path)
    merge_img = np.zeros(origin_img.shape, dtype=np.uint8)
    for row in range(origin_img.shape[0]):
        for col in range(origin_img.shape[1]):
            if (segment_img[row, col] == np.array([0, 0, 128])).all():
                merge_img[row, col] = origin_img[row, col]
            else:
                merge_img[row, col] = origin_img[row, col] * 0.4 + np.array([255, 0, 0]) * 0.6
    cv2.imwrite(output_file, merge_img)

def get_block_str(block_json_path):
    """
    获取block配置
    """
    with open(block_json_path, 'r') as f:
        data = json.load(f)['id']
    return data

def get_config_json(file_path):
    """
    获取模型配置
    """
    with open(os.path.join(file_path, "config.json"), 'r') as f:
        data = f.read()
        if '}{' in data:
            config_json = json.loads(data[:len(data) // 2])
        else:
            config_json = json.loads(data)
    return config_json

