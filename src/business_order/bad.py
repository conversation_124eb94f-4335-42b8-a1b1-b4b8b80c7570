"""
行政区划商单
"""
import os
import shapely.wkt
from shapely.geometry import Polygon
from dataclasses import asdict
from src.business_order.base import Context, Gis
from src.business_order import helper, enums
from src.business_order.quality import DataQ<PERSON><PERSON><PERSON><PERSON><PERSON>, QualityErrNo, TableQualityChecker
from src.business_order.query import OrderQuery, OrderWriter
from src.tools import pgsql


BAD_MIN_ANGLE = 10
SOURCE_VIL = 'bad_admin_vil_gcj'
SOURCE_TOWN = 'bad_admin_town_mzb'
SD_VIL = 'sd_bad_village'
SD_TOWN = 'sd_bad_town'


def get_source_level(ctx: Context):
    """ 获取数据源级别 """

    data_source = ctx.order.cond[enums.ExportCondKey.BAD.value].data_src[0]
    if data_source == enums.DivisionDataSource.SOURCE_B:
        return 'village'
    if data_source == enums.DivisionDataSource.SOURCE_C:
        return 'town'

    raise ValueError(f'不支持的数据源: {data_source}')


def get_sd_table(ctx: Context):
    """ 获取商单底层表 """
    data_source = ctx.order.cond[enums.ExportCondKey.BAD.value].data_src[0]
    if data_source == enums.DivisionDataSource.SOURCE_B:
        return SD_VIL
    elif data_source == enums.DivisionDataSource.SOURCE_C:
        return SD_TOWN
    else:
        raise ValueError(f'不支持的数据源: {data_source}')


def get_qkey(table):
    """ 获取表类型 """
    return 'village' if table == SD_VIL else 'town'


def _process_fix(sd_conf, table, start_id, end_id):
    """ 行政区划精度处理 """
    delete_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select guid from {table}
            where guid>=%s and guid<=%s order by guid;
        """
        segs = OrderQuery.generate_segments(
            curs, table, 'guid', f"where guid>='{start_id}' and guid<='{end_id}'", 2000)

        for seg in segs:
            bads = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            guids = [x[0] for x in bads]
            OrderWriter.fix_snap(conn, table, 'and guid=any(%s)', [guids])
            OrderWriter.fix_repeated_points(conn, table, 'and guid=any(%s)', [guids])
            OrderWriter.fix_valid(conn, table, 'and guid=any(%s)', [guids])
            OrderWriter.fix_geometry_collection(conn, table, 'and guid=any(%s)', [guids])

    return delete_logs


def _process_quality_check(sd_conf, table, start_id, end_id):
    """ 行政区划质检处理 """
    delete_logs = []

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        q_key = get_qkey(table)
        sql = f"""
            select guid as face_id,{q_key}_code as admin_id,{q_key}_name as name_ch,st_astext(geom) as geom 
            from {table} where guid>=%s and guid<=%s order by guid;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'guid', f"where guid>='{start_id}' and guid<='{end_id}'", 2000)

        for seg in segs:
            bads = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for _bad in bads:
                # 校验 face_id 是否包含特殊字符
                if DataQualityChecker.check_contains_special_characters(_bad[0]):
                    delete_logs.append(dict(errno=QualityErrNo.D0005, face_id=_bad[0]))

                # 校验 admin_id 是否有空
                if DataQualityChecker.check_is_empty(_bad[1]):
                    delete_logs.append(dict(errno=QualityErrNo.D0012, face_id=_bad[0]))

                # 检验 name_ch 是否有空值
                if DataQualityChecker.check_is_empty(_bad[2]):
                    delete_logs.append(dict(errno=QualityErrNo.D0016, face_id=_bad[0]))

                # 重复点检测
                _wkt = shapely.wkt.loads(_bad[3])
                if helper.Geoser.check_repeated_points(_wkt):
                    delete_logs.append(dict(errno=QualityErrNo.D0021, face_id=_bad[0]))

                # 自接触检测
                if helper.Geoser.check_contact(_wkt):
                    delete_logs.append(dict(errno=QualityErrNo.D0019, delid=_bad[0]))

                # 尖角检测
                try:
                    angles = helper.Geoser.check_angle(_wkt, BAD_MIN_ANGLE)
                    for angle in angles:
                        angle_size, angle_point, angle_points = angle
                        delete_logs.append(
                            dict(errno=QualityErrNo.D0023, face_id=_bad[0], angle_size=angle_size,
                                 point=angle_point, geom=Polygon(angle_points).wkt))
                except Exception as e:
                    delete_logs.append(dict(errno=QualityErrNo.D0023, face_id=_bad[0], angle_err=str(e)))

                # 压盖检测
                lap_sql = f"""
                    select guid as face_id, st_astext(geom) as geom from {table} where st_intersects(geom, %s) 
                    and guid<>%s;
                """
                laps = pgsql.fetch_all(conn, lap_sql, [f'SRID=4326;{_bad[3]}', _bad[0]])
                if len(laps) == 0:
                    continue
                for lap in laps:
                    lap_wkt = shapely.wkt.loads(lap[1])
                    if lap_wkt.equals(_wkt):
                        delete_logs.append(dict(errno=QualityErrNo.D0029, face_id=_bad[0], repeat_id=lap[0]))
                        continue

                    if lap_wkt.overlaps(_wkt):
                        intersects = lap_wkt.intersection(_wkt)
                        delete_logs.append(dict(errno=QualityErrNo.D0030,
                                           face_id=_bad[0], overlap_id=lap[0],
                                           overlap_area=intersects.area, geom=intersects.wkt))
                        continue

                    if not (lap_wkt.is_empty or _wkt.is_empty) and (lap_wkt.covers(_wkt) or _wkt.covers(lap_wkt)):
                        delete_logs.append(dict(errno=QualityErrNo.D0041, face_id=_bad[0], face_id2=lap[0]))
                        continue

    return delete_logs


def process_fix(sd_conf, table):
    """
    多进程质检
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'guid')
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_logs = helper.multi_run(segments, _process_fix, 16)
    _del_logs = sum(del_logs, [])

    # with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
    #     curs.execute(f'VACUUM ANALYZE {table};')

    return _del_logs


def process_quality_check(sd_conf, table):
    """
    多进程质检
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'guid', '', 10000)
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_logs = helper.multi_run(segments, _process_quality_check, 32)
    return sum(del_logs, [])


class BadGis(Gis):
    """ 行政区划处理类 """
    log_type = enums.ExportCondKey.BAD.value

    @classmethod
    def extract_values(cls, item, in_keys):
        """ 从字典中提取指定键对应的值，并返回一个元组。 """
        return tuple(item[key] for key in in_keys)

    def create_table(self, ctx: Context):
        """ 创建数据表 """
        with pgsql.get_connection(ctx.delivery_conf) as conn:
            OrderWriter.create_bad_table(conn)

    def filter_data(self, ctx: Context):
        """ 筛选数据入库 """

        _cond = ctx.order.cond[enums.ExportCondKey.BAD.value]
        where, where_params = self.build_where(ctx=ctx, cond=_cond, is_division=True)

        bottom_division = get_source_level(ctx)
        if bottom_division == 'village':
            bad_csv = OrderQuery.copy_vils_from_order(ctx.order_conf, ctx.export_id, where, where_params)
            copy_path = OrderQuery.convert_coord_csv(bad_csv, ctx.order.export_coord, 0)
            in_sql = f"copy sd_bad_village(geom,guid,village_code,village_name,town_code,town_name," + \
                "county_code,county_name,city_code,city_name,pro_code,pro_name)"
            helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

        if bottom_division == 'town':
            bad_csv = OrderQuery.copy_towns_from_order(ctx.order_conf, ctx.export_id, where, where_params)
            copy_path = OrderQuery.convert_coord_csv(bad_csv, ctx.order.export_coord, 0)
            in_sql = f"copy sd_bad_town(geom,guid,town_code,town_name,county_code,county_name," + \
                "city_code,city_name,pro_code,pro_name)"
            helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

    def export_data(self, ctx: Context):
        """ 导出数据 """
        field = ctx.get_export_geom_field()
        export_dir = ctx.get_export_dest_name()
        exporter = helper.JExporter(ctx.delivery_conf, ctx.order.export_format, ctx.order.export_encode)
        bad_level = ctx.order.cond[enums.ExportCondKey.BAD.value].bad_level
        if '5' in bad_level:
            exporter.export(os.path.join(export_dir, enums.DeliveryDirs.VILLAGE),
                            'division_village', enums.ExportSQL.VILLAGE.geom(field))
        if '4' in bad_level:
            exporter.export(os.path.join(export_dir, enums.DeliveryDirs.TOWN),
                            'division_town', enums.ExportSQL.TOWN.geom(field))
        if '3' in bad_level:
            exporter.export(os.path.join(export_dir, enums.DeliveryDirs.COUNTY),
                            'division_county', enums.ExportSQL.COUNTY.geom(field))
        if '2' in bad_level:
            exporter.export(os.path.join(export_dir, enums.DeliveryDirs.CITY),
                            'division_city', enums.ExportSQL.CITY.geom(field))
        if '1' in bad_level:
            exporter.export(os.path.join(export_dir, enums.DeliveryDirs.PRO),
                            'division_province', enums.ExportSQL.PRO.geom(field))

        dict_data = [asdict(record) for record in ctx.bad_quality_log]
        exporter.to_csv_by_data(export_dir, 'division_quality', None, dict_data)

    def quality_deal(self, ctx: Context):
        """ 质量处理 """
        process_fix(ctx.delivery_conf, get_sd_table(ctx))
        # 聚合表
        bottom_division = get_source_level(ctx)
        wait_union_divisions = enums.DivisionKey.get_higher_division(bottom_division)
        with pgsql.get_connection(ctx.delivery_conf) as conn, conn.cursor() as curs:
            for item in wait_union_divisions:
                if item == 'town':
                    OrderWriter.sync_town(conn)
                if item == 'county':
                    OrderWriter.sync_county(conn)
                if item == 'city':
                    OrderWriter.sync_city(conn)
                if item == 'pro':
                    OrderWriter.sync_pro(conn)

    def quality_check(self, ctx: Context):
        """ 质量检测 """

        bottom_division = get_source_level(ctx)
        table = SD_VIL if bottom_division == 'village' else SD_TOWN
        with pgsql.get_connection(ctx.delivery_conf) as sd_conn, sd_conn.cursor() as sd_curs:
            table_checks = [
                ['guid', 'varchar', 64, QualityErrNo.D0003],
                [f'{bottom_division}_code', 'varchar', 24, QualityErrNo.D0011],
                [f'{bottom_division}_name', 'varchar', 255, QualityErrNo.D0015],
            ]

            for _item in table_checks:
                _item_info = TableQualityChecker.get_column_info(sd_curs, table, _item[0])
                if not (TableQualityChecker.check_column_is_varchar(_item_info) and
                        TableQualityChecker.check_column_len(_item_info, _item[2])):
                    ctx.append_bad_logs([dict(errno=_item[3])])

            # 记录检测
            ctx.append_bad_logs(process_quality_check(ctx.delivery_conf, table))
