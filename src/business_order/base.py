"""
基础类，基础Step结构、上下文结构等
"""

import functools
import json
import os
import typing
import traceback
import shapely.wkt
from abc import ABC, abstractmethod
from dataclasses import dataclass, field, asdict

from src.business_order import enums, helper
from src.business_order.quality import QualityErrNo, QualityLogItem
from src.business_order.query import OrderQuery, OrderWriter
from src.tools import conf_tools, pgsql


@dataclass
class OrderCond:
    """ 商单交付物条件结构 """
    bad_level: typing.List[str] = field(default_factory=list)
    data_src: typing.List[str] = field(default_factory=list)
    bids: typing.List[str] = field(default_factory=list)
    cities: typing.List[str] = field(default_factory=list)
    counties: typing.List[str] = field(default_factory=list)
    delivery_spec: typing.List[str] = field(default_factory=list)
    fids: typing.List[str] = field(default_factory=list)
    meshes: typing.List[str] = field(default_factory=list)
    provinces: typing.List[str] = field(default_factory=list)
    query_table: typing.List[str] = field(default_factory=list)
    towns: typing.List[str] = field(default_factory=list)
    villages: typing.List[str] = field(default_factory=list)
    wkt: typing.List[str] = field(default_factory=list)
    bad_export_range: typing.List[str] = field(default_factory=list)
    quanguo_type: typing.List[str] = field(default_factory=list)


@dataclass
class BusinessOrderInfo:
    """ 商单参数结构 """
    project_name: str
    amount: str
    output_name: str
    need_safe: str
    export_coord: str
    export_encode: str
    export_format: str
    cond: typing.Dict[str, OrderCond] = None


class Context:
    """ 商单处理上下文 """

    def __init__(self, export_id, req: json):
        """ 初始化商单参数到上下文 """
        req_param = json.loads(req)
        self.req = req_param
        self.export_id = export_id
        if req_param:
            self.order = self.set_conditions(req_param)
        else:
            self.order = None
        self.delivery_conf = {}
        self.export_root_dir = os.path.abspath(os.path.join(
            os.path.dirname(os.path.abspath(__file__)), '../../data/auto_shangdan'))
        self.logger = ''

        self.aoi_quality_log = []
        self.bud_quality_log = []
        self.water_quality_log = []
        self.green_quality_log = []
        self.bad_quality_log = []
        self.brw_quality_log = []

        self.data_range = {}

        self.lock_file = f'./data/auto_shangdan/lock/{export_id}.lock'

    def __enter__(self):
        """ 自动注册商单PG连接、背景库PG连接 """
        order_conf = conf_tools.get_pg_conf("order")
        if not order_conf.keys().__contains__('host'):
            raise ("数据库信息没有 order 配置")

        master_conf = conf_tools.get_pg_conf("master")
        if not master_conf.keys().__contains__('host'):
            raise ("数据库信息没有 master 配置")

        poi_conf = conf_tools.get_pg_conf("poi")
        if not poi_conf.keys().__contains__('host'):
            raise ("数据库信息没有 poi_online 配置")

        self.order_conf = order_conf
        self.master_conf = master_conf
        self.poi_conf = poi_conf

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """ 注销PG库连接 """
        pass

    def set_delivery_conf(self, data):
        """ 设置成果交付库配置 """
        conf = {
            'host': data.get('host'),
            'db': data.get('db'),
            'pwd': data.get('passwd'),
            'user': data.get('user'),
            'port': data.get('port')
        }
        self.delivery_conf = conf

    def set_conditions(self, conditions) -> BusinessOrderInfo:
        """ 解析商单参数 """
        order_info = conditions.get("order_info")
        order = BusinessOrderInfo(
            project_name=order_info.get("order_project_name"),
            amount=order_info.get("order_amount"),
            output_name=order_info.get("order_output_name").strip(),
            need_safe=order_info.get("order_need_safe"),
            export_coord=order_info.get("order_export_coord").strip(),
            export_encode=order_info.get("order_export_encode").strip(),
            export_format=order_info.get("order_export_format").strip()
        )
        filter_conditions = order_info.get("order_filter_conditions")
        order.cond = {}
        for item in filter_conditions:
            cond_key = item.get("query_table")
            order.cond[cond_key[0]] = OrderCond(**item)

        # 同时支持下省市的图幅、几何范围、全国范围
        order_conditions = order_info.get("order_condition_list")
        for item in order_conditions:
            cond_key = item.get("delivery_data_range")  # 原始条件
            order.cond[cond_key].bad_export_range = [item.get('BAD_EXPORT_RANGE_TYPE', 'BAD_GEOM_RANGE')]
            order.cond[cond_key].quanguo_type = [item.get('QUANGUO_TYPE', '')]
            # QUANGUO_WITH_GANGAO QUANGUO_NO_GANGAO
        return order

    def set_export_id(self, export_id):
        """ 设置商单 ID """
        self.export_id = export_id

    @classmethod
    def build_log(cls, *args, **kwargs):
        """ 质检记录写入 """

        _log = QualityLogItem()
        for key, value in kwargs.items():

            if key == 'errno':
                _log.errno = value.name
                _log.msg += f"{value.value}."
            elif key == 'geom':
                _log.geom = value
            else:
                _log.msg += f" {key}: {value}."

        return _log

    def append_bud_logs(self, logs):
        """ 建筑质检报告"""
        for _log in logs:
            self.bud_quality_log.append(self.build_log(**_log))

    def append_aoi_logs(self, logs):
        """ AOI质检报告"""
        for _log in logs:
            self.aoi_quality_log.append(self.build_log(**_log))

    def append_water_logs(self, logs):
        """ 水系质检报告"""
        for _log in logs:
            self.water_quality_log.append(self.build_log(**_log))

    def append_green_logs(self, logs):
        """ 绿地质检报告"""
        for _log in logs:
            self.green_quality_log.append(self.build_log(**_log))

    def append_bad_logs(self, logs):
        """ 绿地质检报告"""
        for _log in logs:
            self.bad_quality_log.append(self.build_log(**_log))

    def append_brw_logs(self, logs):
        """ 绿地质检报告"""
        for _log in logs:
            self.brw_quality_log.append(self.build_log(**_log))

    def get_export_geom_field(self):
        """ 获取输出用的 geom key """
        return enums.DeliveryFormat.export_geom_key(self.order.export_format)

    def get_export_dest_name(self):
        """ 获取项目导出根路径 """
        return f'{self.export_root_dir}/{self.export_id}/{self.order.output_name}'

    def export_order_info(self):
        """ 导出商单基础信息 """
        export_dir = self.get_export_dest_name()
        json_string = json.dumps(self.req['order_info'])
        json_data = json.loads(json_string)

        # 格式化并写入到 txt 文件
        with open(f"{export_dir}/order_param.txt", "w", encoding="utf-8") as file:
            # 格式化为缩进的 JSON 字符串，缩进4个空格
            formatted_json = json.dumps(json_data, indent=4, ensure_ascii=False)
            file.write(formatted_json)

        print("JSON 写入完成！")

    def set_gg(self, tp, gg, coord):
        """ 设置边界 """
        if tp in [enums.ExportCondKey.BUD.value, enums.ExportCondKey.AOI.value] and gg:
            convert_geom = helper.transform_geom(gg, coord).wkt
            self.data_range[tp] = convert_geom
        else:
            self.data_range[tp] = None

    def get_gg(self, tp):
        """ 获取边界 """
        if tp in self.data_range:
            return self.data_range[tp]

        return None


def _get_exception_msg(e):
    """ 获取异常位置 """
    tb = traceback.extract_tb(e.__traceback__)  # 提取回溯信息
    root_cause = tb[0]  # 追踪到错误的最早调用点

    filename = root_cause.filename
    line_number = root_cause.lineno
    function_name = root_cause.name

    error_msg = f"错误发生在文件: {filename}, 行号: {line_number}, 函数: {function_name}, 错误: {str(e)}"

    print(traceback.format_exc())

    return error_msg


def log_decorator(func):
    """ 装饰器，用于记录函数的执行状态和错误信息。 """

    @functools.wraps(func)
    def wrapper(self, context):
        try:
            func(self, context)
            self.log(context, status_code=0)
        except Exception as e:
            # tb = traceback.extract_tb(e.__traceback__)
            # last_call = tb[-1]
            # filename = last_call.filename
            # line_number = last_call.lineno
            # error_msg = f"错误发生在文件: {filename}, 行号: {line_number}, {str(e)}"
            err_msg = _get_exception_msg(e)
            self.log(context, status_code=1, error_message=err_msg)

            helper.NoticeHelper.send_notice_msg(
                f"【商单平台】导出异常通知：{context.export_id}. {context.order.project_name} {err_msg}",
                helper.NoticeHelper.err_receivers())

            # 执行回滚操作
            if hasattr(self, 'rollback'):
                try:
                    self.rollback(context)
                except Exception as rollback_error:
                    self.log(context, status_code=1, error_message=f"Rollback failed: {rollback_error}")

            raise

    return wrapper


class Step:
    """ 步骤基类 """

    def __init__(self, name):
        """ 初始化变量 """
        self.name = name
        self.remark = ''

    def handle(self, context):
        """ 处理方法 """
        self._log_and_handle(context)

    @log_decorator
    def _log_and_handle(self, context):
        """ 由装饰器控制的实际处理逻辑 """
        self._handle_impl(context)

    def _handle_impl(self, context: Context):
        """ 处理步骤的逻辑，供子类重写 """
        raise NotImplementedError("Subclasses must implement '_handle_impl' method")

    def rollback(self, context):
        """ 一些异常需要进行回滚操作 """
        pass

    def log(self, context, status_code, error_message=""):
        """ 商单导出日志记录 """
        log_entry = {
            'export_id': context.export_id,
            'step_name': self.name,
            'status_code': status_code,
            'error_message': error_message,
            'remark': self.remark
        }
        OrderWriter.insert_step_log(log_entry)
        if status_code == 1:
            OrderWriter.update_export_order(context.export_id, enums.CommonExportStatus.FAILED, error_message)


class ChainExecutor:
    """
    context = Context()
    steps = [Step1(), Step2(), Step3()]
    chain_executor = ChainExecutor(steps)
    chain_executor.execute(context)
    """

    def __init__(self, steps):
        """ 初始化要运行的步骤 """
        self.steps = steps

    def execute(self, context):
        """ 运行 """
        for step in self.steps:
            try:
                step.handle(context)
            except Exception as e:
                print(f"【商单导出】停止运行在 {step.name}，错误内容为：{e}")
                break


class Gis(ABC):
    """数据处理器基类，定义通用接口"""

    def __init__(self):
        """ 初始化 """
        pass

    @classmethod
    def is_mesh_range(cls, cond):
        """判断图幅还是范围"""
        export_range = cond.get("bad_export_range", None)
        return (export_range and export_range[0] == 'BAD_MESH_RANGE')

    @classmethod
    def is_mzb(cls, cond):
        """是否需要民政部源"""
        mzb_src = cond.get("data_src", None)
        return (mzb_src and mzb_src[0] == 'FROM_C')

    @classmethod
    def is_quanguo(cls, cond):
        """ 是否是全国 """
        export_range = cond.get("quanguo_type", None)
        return (export_range and export_range[0] in ['BAD_EXPORT_RANGE_TYPE', 'QUANGUO_NO_GANGAO'])

    def build_where(self, ctx: Context, cond: OrderCond, ad='', is_division=False):
        """ 查询条件构建方法 """
        _cond = asdict(cond)
        where = ''
        where_params = []
        cond_key = cond.query_table[0]

        for key, value in _cond.items():
            if not value:
                continue

            if key == 'wkt':
                gg = value[0].strip()
                geom_column = 'a.geom' if ad == 'a.' else 'geom'
                where = f' and st_intersects({geom_column},St_GeomFromText(%s, 4326))'
                where_params.append(gg)
                ctx.set_gg(cond_key, gg, ctx.order.export_coord)

            if key == 'meshes':
                where = ' and a.mesh_id=any(%s)' if ad == 'a.' else ' and mesh_id=any(%s)'
                where_params.append(value)

            if key == 'fids':
                where = ' and a.face_id=any(%s)' if ad == 'a.' else ' and face_id=any(%s)'
                where_params.append(value)

            if key == 'bids':
                where += f' and b.poi_bid = ANY(%s)'
                where_params.append(value)

            if key == 'data_src' and value[0] == 'FROM_C':
                if cond.query_table[0] == enums.ExportCondKey.AOI.value:
                    where = " and a.src !='SD'" if ad == 'a.' else " and src !='SD'"

            if not self.is_quanguo(_cond) and key.upper() in enums.DivisionReqKey.__members__ and not is_division:
                # 非行政区划
                with pgsql.get_connection(ctx.order_conf) as order_conn, \
                        pgsql.get_connection(ctx.master_conf) as master_conn:
                    division_type = enums.DivisionReqKey.get_value(key)
                    if self.is_mesh_range(_cond) and division_type in ['pro', 'city']:
                        bad_names = OrderQuery.get_bad_name(
                            order_conn, 'bad_admin_vil_gcj', division_type, value)
                        mesh_ids = OrderQuery.get_mesh_ids(
                            master_conn, division_type, bad_names)

                        where = "and a.mesh_id=any(%s)" if ad == 'a.' else f" and mesh_id=any(%s)"
                        where_params.append(mesh_ids)
                    else:
                        if division_type in ['pro', 'city']:
                            gg2 = OrderQuery.get_division_geom_from_pddlastest(
                                order_conn, 'bad_admin_vil_gcj', division_type, value)
                        else:
                            gg2 = OrderQuery.get_division_geom(
                                order_conn, 'bad_admin_vil_gcj', division_type, value)
                        geom_column = 'a.geom' if ad == 'a.' else 'geom'
                        where += f" and st_intersects({geom_column},St_GeomFromText(%s, 4326))"
                        where_params.append(gg2)
                        ctx.set_gg(cond_key, gg2, ctx.order.export_coord)

            if not self.is_quanguo(_cond) and key.upper() in enums.DivisionReqKey.__members__ and is_division:
                # 行政区划
                bad_level = enums.DivisionReqKey.get_value(key)
                where += f" and {enums.DivisionReqKey.get_value(key)}_code = ANY(%s)"
                if self.is_mzb(_cond) and bad_level == 'town':
                    where_params.append([x[0:9] for x in value])
                elif self.is_mzb(_cond) and bad_level != 'town':
                    where_params.append([x[0:6] for x in value])
                else:
                    where_params.append(value)

        return where, where_params

    @abstractmethod
    def create_table(self, ctx: Context):
        """ 创建数据表 """
        pass

    @abstractmethod
    def filter_data(self, ctx: Context):
        """ 筛选数据 """
        pass

    @abstractmethod
    def export_data(self, ctx: Context):
        """ 数据导出 """
        pass

    @abstractmethod
    def quality_deal(self, ctx: Context):
        """ 数据质量处理 """
        pass

    @abstractmethod
    def quality_check(self, ctx: Context):
        """ 数据质量报告 """
        pass
