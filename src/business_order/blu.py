"""
AOI商单
"""
import shapely.wkt
from dataclasses import asdict
from mapio.utils import bid2uid
from shapely.geometry import Polygon

from src.business_order import enums, helper
from src.business_order.base import Context, Gis
from src.business_order.quality import DataQual<PERSON><PERSON><PERSON><PERSON>, QualityErrNo, \
    TableQualityChecker, process_repeated_points
from src.business_order.query import OrderWriter, OrderQuery
from src.tools import pgsql


AOI_MIN_AREA = 100  # AOI 最小面积阈值，低于的需要删除
AOI_NORMAL_MAX = 10000000  # AOI 常规垂类最大面积
AOI_MAX_AREA = 5000000000  # AOI 景区等垂类最大面积
AOI_MIN_ANGLE = 20  # AOI 尖角阈值
AOI_BIG_TAG = ['交通设施;飞机场', '自然地物;其他', '休闲娱乐;度假村', '行政地标;商圈', '旅游景点;风景区']


def _process_aoi_batch_tag(sd_conf, poi_conf, table, start_id, end_id):
    """
    建筑物接触检测
    """

    delete_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, poi_bid, area from {table} where face_id>=%s 
            and face_id<=%s order by face_id;
        """
        update_sql = f'update {table} set name_ch=%s,std_tag=%s,admin_code=%s,poi_id=%s where face_id=%s;'

        poi_sql = f'select bid, std_tag, name, admin_code from poi where bid=any(%s);'

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            wait_delete = []
            wait_update = []
            aois = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            bids = [x[1] for x in aois if x[1] != '']

            with pgsql.get_connection(poi_conf) as poi_conn:
                pois = pgsql.fetch_all(poi_conn, poi_sql, [bids])
                pois_set = {x[0]: x for x in pois}

            for aoi in aois:
                if aoi[1] not in pois_set:
                    delete_logs.append(dict(errno=QualityErrNo.A0029, delid=aoi[0], poi_bid=aoi[1]))
                    wait_delete.append(aoi[0])
                    continue

                _poi = pois_set[aoi[1]]
                if "行政区划" in _poi[1] or "行政地标" in _poi[1]:
                    delete_logs.append(dict(errno=QualityErrNo.A0051, delid=aoi[0]))
                    wait_delete.append(aoi[0])
                    continue

                if '交通设施;桥' == _poi[1]:
                    delete_logs.append(dict(errno=QualityErrNo.A0052, delid=aoi[0]))
                    wait_delete.append(aoi[0])
                    continue

                wait_update.append((_poi[2], _poi[1], _poi[3], bid2uid(_poi[0]), aoi[0]))

            pgsql.execute_many(conn, update_sql, wait_update)
            OrderWriter.fix_delete(conn, table, 'face_id', wait_delete)
    return delete_logs


def _process_contact_check(sd_conf, table, start_id, end_id, division):
    """
    建筑物接触检测
    """

    delete_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, area, st_astext(geom) as geom from {table} where face_id>=%s 
            and face_id<=%s order by face_id;
        """

        division_wkt = shapely.wkt.loads(division) if division else None

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            wait_delete = []
            aois = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for aoi in aois:
                if aoi[1] < AOI_MIN_AREA:
                    wait_delete.append(aoi[0])
                    delete_logs.append(dict(errno=QualityErrNo.A0024, delid=aoi[0]))
                    continue

                aoi_wkt = shapely.wkt.loads(aoi[2])
                if helper.Geoser.check_contact(aoi_wkt):
                    wait_delete.append(aoi[0])
                    delete_logs.append(dict(errno=QualityErrNo.A0034, delid=aoi[0]))
                    continue

                if division_wkt and not division_wkt.covers(aoi_wkt):
                    wait_delete.append(aoi[0])
                    delete_logs.append(dict(errno=QualityErrNo.A0042, delid=aoi[0]))
                    continue

            OrderWriter.fix_delete(conn, table, 'face_id', wait_delete)
    return delete_logs


def _process_repeate_check(sd_conf, table, start_id, end_id):
    """
    AOI 重复检测
    """

    overlaps = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, st_astext(geom) as geom from {table} where face_id>=%s 
            and face_id<=%s order by face_id;
        """

        lap_sql = f"""
            select face_id, st_astext(geom) as geom from {table} where st_intersects(geom, %s) 
            and face_id<>%s;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            aois = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for aoi in aois:
                laps = pgsql.fetch_all(conn, lap_sql, [f'SRID=4326;{aoi[1]}', aoi[0]])
                aoi_wkt = shapely.wkt.loads(aoi[1])
                for lap in laps:
                    lap_wkt = shapely.wkt.loads(lap[1])
                    if aoi_wkt.equals(lap_wkt):
                        overlaps.append((aoi[0], lap[0]))

    return overlaps


def _process_quality_check(sd_conf, table, start_id, end_id):
    """
    质量检测
    """

    delete_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id,poi_id,name_ch,std_tag,area,st_astext(geom) as geom 
            from {table} where face_id>=%s and face_id<=%s order by face_id;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            aois = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for _aoi in aois:
                # 校验 face_id 是否包含特殊字符
                if DataQualityChecker.check_contains_special_characters(_aoi[0]):
                    delete_logs.append(dict(errno=QualityErrNo.A0005, face_id=_aoi[0]))

                # 校验 poi_id 是否包含特殊字符
                if DataQualityChecker.check_contains_special_characters(_aoi[1]):
                    delete_logs.append(dict(errno=QualityErrNo.A0013, face_id=_aoi[0], poi_id=_aoi[1]))

                # 校验 poi_id 是否有空字符串
                if DataQualityChecker.check_is_empty(_aoi[1]):
                    delete_logs.append(dict(errno=QualityErrNo.A0012, face_id=_aoi[0]))

                # 检验 name 是否有空值
                if DataQualityChecker.check_is_empty(_aoi[2]):
                    delete_logs.append(dict(errno=QualityErrNo.A0029, face_id=_aoi[0]))

                # 检测面积 > 50亿的
                if _aoi[3] in AOI_BIG_TAG:
                    if _aoi[4] > AOI_MAX_AREA:
                        delete_logs.append(dict(errno=QualityErrNo.A0022, face_id=_aoi[0], area=_aoi[4]))
                        # self._delete_aoi_by_face_id(_aoi['face_id']) TODO 待稳定后删除

                # 检测面积 > 1000w
                if _aoi[3] not in AOI_BIG_TAG:
                    if _aoi[4] > AOI_NORMAL_MAX:
                        delete_logs.append(dict(errno=QualityErrNo.A0023, face_id=_aoi[0], area=_aoi[4]))

                _wkt = shapely.wkt.loads(_aoi[5])
                try:
                    angles = helper.Geoser.check_angle(_wkt, AOI_MIN_ANGLE)
                    for angle in angles:
                        angle_size, angle_point, angle_points = angle
                        delete_logs.append(
                            dict(errno=QualityErrNo.A0038, face_id=_aoi[0], angle_size=angle_size,
                                 point=angle_point, geom=Polygon(angle_points).wkt))
                except Exception as e:
                    delete_logs.append(dict(errno=QualityErrNo.A0038, face_id=_aoi[0], angle_err=str(e)))

    return delete_logs


def process_aoi_tag(sd_conf, poi_conf, table):
    """ 
    多进程 AOI 数据赋值批处理
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id', '', 10000)
        segments = [dict(sd_conf=sd_conf, poi_conf=poi_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_logs = helper.multi_run(segments, _process_aoi_batch_tag, 32)
    return sum(del_logs, [])


def process_contact_check(sd_conf, table, division):
    """ 
    多进程自接触检测
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id', '', 10000)
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1], division=division) for x in _segs]

    del_logs = helper.multi_run(segments, _process_contact_check, 32)
    return sum(del_logs, [])


def process_repeate_check(sd_conf, table):
    """ 
    多进程重复检测
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id', '', 10000)
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_gets = helper.multi_run(segments, _process_repeate_check, 32)
    del_ids = sum(del_gets, [])
    overlaps_delids = list({tuple(sorted(x)) for x in del_ids})

    wait_logs = []
    wait_dele = []
    with pgsql.get_connection(sd_conf) as conn:
        for rep in overlaps_delids:
            wait_logs.append(dict(errno=QualityErrNo.A0041, face_id=rep[0], del_id=rep[1]))
            wait_dele.append(rep[1])

        OrderWriter.fix_delete(conn, table, 'face_id', wait_dele)

    return wait_logs


def _process_poi_id(sd_conf, table, start_id, end_id):
    """
    POI ID 重复检测
    """
    wait_delete = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id,poi_id from {table} where face_id>=%s 
            and face_id<=%s order by face_id;
        """

        lap_sql = f"""
            select face_id,poi_id from {table} where poi_id=%s
            and face_id<>%s;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            aois = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for aoi in aois:
                laps = pgsql.fetch_all(conn, lap_sql, [aoi[1], aoi[0]])
                for lap in laps:
                    wait_delete.append(lap[1])

    return wait_delete


def process_repeate_poi_id(sd_conf, table):
    """ 
    多进程重复检测
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id', '', 10000)
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_ids_gets = helper.multi_run(segments, _process_poi_id, 32)
    del_ids = sum(del_ids_gets, [])
    wait_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        aoi_ids = list(set(del_ids))
        wait_del = []
        for aoi_id in aoi_ids:
            sql = f'select face_id,poi_id from {table} where poi_id=%s;'
            reps = pgsql.fetch_all(conn, sql, [aoi_id])
            if len(reps) > 1:
                for idx, rep in enumerate(reps):
                    if idx > 0:
                        wait_logs.append(dict(errno=QualityErrNo.A0016, face_id=rep[0], poi_id=rep[1]))
                        wait_del.append(rep[0])
        OrderWriter.fix_delete(conn, table, 'face_id', wait_del)

    return wait_logs


def process_quality_check(sd_conf, table):
    """
    多进程质检
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id', '', 10000)
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_logs = helper.multi_run(segments, _process_quality_check, 32)
    return sum(del_logs, [])


class AoiGis(Gis):
    """ AOI处理类 """

    TABLE = 'sd_blu_face'
    log_type = enums.ExportCondKey.AOI.value

    def create_table(self, ctx: Context):
        """
        创建数据表
        """

        with pgsql.get_connection(ctx.delivery_conf) as conn:
            OrderWriter.create_aoi_table(conn, self.TABLE)

    def filter_data(self, ctx: Context):
        """ 筛选数据入库 """

        # 数据写入到文件
        where, where_params = self.build_where(ctx, ctx.order.cond[enums.ExportCondKey.AOI.value], 'a.')
        aoi_csv = OrderQuery.copy_aoi_from_master(ctx.master_conf, ctx.export_id, where, where_params)

        # 转坐标系
        copy_path = OrderQuery.convert_coord_csv(aoi_csv, ctx.order.export_coord, 8)

        # 写入成果库
        in_sql = f"copy sd_blu_face(face_id,poi_bid,name_ch,area,perimeter,mesh_id,kind,aoi_level,geom) "
        helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

    def export_data(self, ctx: Context):
        """ 导出数据 """
        field = ctx.get_export_geom_field()
        export_dir = ctx.get_export_dest_name()
        dest = f'{export_dir}/{enums.DeliveryDirs.AOI}'
        exporter = helper.JExporter(ctx.delivery_conf, ctx.order.export_format, ctx.order.export_encode)
        exporter.export(dest, 'blu_face', enums.ExportSQL.AOI.geom(field))

        dict_data = [asdict(record) for record in ctx.aoi_quality_log]
        exporter.to_csv_by_data(export_dir, 'aoi_quality', None, dict_data)

    def quality_deal(self, ctx: Context):
        """ 质量处理 """
        process_repeated_points(ctx.delivery_conf, self.TABLE)
        ctx.append_aoi_logs(process_repeate_check(ctx.delivery_conf, self.TABLE))
        ctx.append_aoi_logs(process_aoi_tag(ctx.delivery_conf, ctx.poi_conf, self.TABLE))
        ctx.append_aoi_logs(process_contact_check(ctx.delivery_conf, self.TABLE,
                            ctx.get_gg(enums.ExportCondKey.AOI.value)))
        ctx.append_aoi_logs(process_repeate_poi_id(ctx.delivery_conf, self.TABLE))

    def quality_check(self, ctx: Context):
        """ 质量检测 """
        with pgsql.get_connection(ctx.delivery_conf) as sd_conn, sd_conn.cursor() as sd_curs:
            faceid_info = TableQualityChecker.get_column_info(sd_curs, self.TABLE, 'face_id')
            poiid_info = TableQualityChecker.get_column_info(sd_curs, self.TABLE, 'poi_id')
            name_info = TableQualityChecker.get_column_info(sd_curs, self.TABLE, 'name_ch')

            # 检测 face_id 是否符合长度要求
            if not (TableQualityChecker.check_column_is_varchar(faceid_info)
                    and TableQualityChecker.check_column_len(faceid_info, 128)):
                ctx.append_aoi_logs([dict(errno=QualityErrNo.A0003)])

            # 检测 poi_id 是否符合长度要求
            if not (TableQualityChecker.check_column_is_varchar(poiid_info)
                    and TableQualityChecker.check_column_len(poiid_info, 128)):
                ctx.append_aoi_logs([dict(errno=QualityErrNo.A0010)])

            # 检测 name 是否符合长度要求
            if not (TableQualityChecker.check_column_is_varchar(name_info)
                    and TableQualityChecker.check_column_len(name_info, 120)):
                ctx.append_aoi_logs([dict(errno=QualityErrNo.A0031)])

            # 记录检测
            ctx.append_aoi_logs(process_quality_check(ctx.delivery_conf, self.TABLE))
