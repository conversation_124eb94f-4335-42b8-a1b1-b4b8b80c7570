"""
铁道线商单
"""
from dataclasses import asdict
from src.business_order import enums, helper
from src.business_order.base import Context, Gis
from src.business_order.quality import DataQuality<PERSON>hecker, QualityErrNo, TableQualityChecker, process_repeated_points
from src.business_order.query import OrderQuery, OrderWriter
from src.tools import pgsql
import shapely.wkt
import shapely.geometry

BRW_LINK_KINDS = [1, 2, 3, 4, 5]  # 1普通铁路 2磁悬浮 3地铁 4高铁 5轻轨
BRW_LINK_FORMS = [0, 1, 2]  # 0无 1桥 2隧道

BRW_NODE_KINDS = [1, 2]  # 1平面交叉点 2Link属性变化点
BRW_NODE_FORMS = [0, 1, 2, 3, 4, 5, 6]  # 0	无 1铁路道口 2桥 3隧道 4图廓点 5有人看守铁路道口 6无人看守铁路道口

BRW_MIN_ANGLE = 10


class BrwGis(Gis):
    """ 铁路处理类 """

    log_type = enums.ExportCondKey.BRW.value
    LINK = 'sd_brw_link'
    NODE = 'sd_brw_node'

    def create_table(self, ctx: Context):
        """ 创建数据表 """
        with pgsql.get_connection(ctx.delivery_conf) as conn:
            OrderWriter.create_brw_table(conn)

    def _filter_link(self, ctx: Context):
        """ 筛选 brw_link 入库 """

        # 数据写入到文件
        where, where_params = self.build_where(ctx, ctx.order.cond[enums.ExportCondKey.BRW.value], '')
        link_csv = OrderQuery.copy_brw_link_from_master(ctx.master_conf, ctx.export_id, where, where_params)

        # 转坐标系
        copy_path = OrderQuery.convert_coord_csv(link_csv, ctx.order.export_coord, 0)

        # 写入成果库
        in_sql = f"copy {self.LINK}(geom,link_id,s_nid,e_nid,kind,form,length,name_ch,name_ph,name_en,z_level,mesh_id) "
        helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

    def _filter_node(self, ctx: Context):
        """ 筛选 brw_node 入库 """

        # 数据写入到文件
        where, where_params = self.build_where(ctx, ctx.order.cond[enums.ExportCondKey.BRW.value], '')
        link_csv = OrderQuery.copy_brw_node_from_master(ctx.master_conf, ctx.export_id, where, where_params)

        # 转坐标系
        copy_path = OrderQuery.convert_coord_csv(link_csv, ctx.order.export_coord, 0)

        # 写入成果库
        in_sql = f"copy {self.NODE}(geom,node_id,kind,form,mesh_id) "
        helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

    def filter_data(self, ctx: Context):
        """ 创建数据表 """
        self._filter_link(ctx)
        self._filter_node(ctx)

    def export_data(self, ctx: Context):
        """ 导出数据"""

        field = ctx.get_export_geom_field()
        export_dir = ctx.get_export_dest_name()
        dest = f'{export_dir}/{enums.DeliveryDirs.BRW}'
        exporter = helper.JExporter(ctx.delivery_conf, ctx.order.export_format, ctx.order.export_encode)
        exporter.export(dest, 'brw_node', enums.ExportSQL.BRW_NODE.geom(field))
        exporter.export(dest, 'brw_link', enums.ExportSQL.BRW_LINK.geom(field))

        dict_data = [asdict(record) for record in ctx.brw_quality_log]
        exporter.to_csv_by_data(export_dir, 'brw_quality', None, dict_data)

    def _deal_link(self, ctx: Context):
        """ 处理铁路 """
        with pgsql.get_connection(ctx.delivery_conf) as sd_conn, sd_conn.cursor() as sd_curs:
            # 去除重复
            repeat_sql = f"""
                select string_agg(link_id,',') as link_ids from {self.LINK}
                group by geom having count(*)>1;
            """
            repeat_links = pgsql.fetch_all(sd_conn, repeat_sql)
            wait_delete = []
            for repeat_link in repeat_links:
                wait_delete.extend(repeat_link[0].split(',')[1:])
            OrderWriter.fix_delete(sd_conn, self.LINK, 'link_id', wait_delete)

            # 重复点处理
            process_repeated_points(ctx.delivery_conf, self.LINK, 'link_id')

            # 其他处理
            segs = OrderQuery.generate_segments(
                sd_curs, self.LINK, 'link_id', "", 2000)
            for seg in segs:
                wait_update = []
                links = OrderQuery.query_brw_link(sd_conn, 'and link_id>=%s and link_id<=%s', seg)
                for link in links:
                    # 中文不为空，但拼音为空
                    if link[7] != '' and link[8] == '':
                        pinyin_txt = DataQualityChecker.fix_pinyin(link[7])
                        wait_update.append(tuple(pinyin_txt, link[1]))
                OrderWriter.fix_brw_link_ph(sd_conn, wait_update)

    def _deal_node(self, ctx: Context):
        """ 处理节点数据 """
        pass

    def quality_deal(self, ctx: Context):
        """ 创建数据表 """
        self._deal_link(ctx)
        self._deal_node(ctx)

    def _check_link(self, ctx: Context):
        """ 质检铁路 """
        with pgsql.get_connection(ctx.delivery_conf) as sd_conn, sd_conn.cursor() as sd_curs:
            table_checks = [
                ['link_id', 'varchar', 128, QualityErrNo.L0002],
                ['s_nid', 'varchar', 128, QualityErrNo.L0011],
                ['e_nid', 'varchar', 128, QualityErrNo.L0019],
                ['name_ch', 'varchar', 120, QualityErrNo.L0038],
                ['name_ph', 'varchar', 1000, QualityErrNo.L0042],
                ['name_en', 'varchar', 1000, QualityErrNo.L0046],
                ['z_level', 'varchar', 1000, QualityErrNo.L0049],
            ]

            for _item in table_checks:
                _item_info = TableQualityChecker.get_column_info(sd_curs, self.LINK, _item[0])
                if not (TableQualityChecker.check_column_is_varchar(_item_info) and
                        TableQualityChecker.check_column_len(_item_info, _item[2])):
                    ctx.append_brw_logs([dict(errno=_item[3])])

            # name_ch 不能全部为空
            if OrderQuery.get_count(sd_conn, self.LINK, "and name_ch<>''") == 0:
                ctx.append_brw_logs([dict(errno=QualityErrNo.L0039)])

            # name_ph 不能全部为空
            if OrderQuery.get_count(sd_conn, self.LINK, "and name_ph<>''") == 0:
                ctx.append_brw_logs([dict(errno=QualityErrNo.L0043)])

            links = pgsql.fetch_all(
                sd_conn, f'select link_id,s_nid,e_nid,kind,form,length,st_astext(geom) as geom from {self.LINK}')
            for link in links:
                if DataQualityChecker.check_contains_special_characters(link[0]):
                    ctx.append_brw_logs([dict(errno=QualityErrNo.L0005, face_id=link[0])])
                if DataQualityChecker.check_contains_special_characters(link[1]):
                    ctx.append_brw_logs([dict(errno=QualityErrNo.L0013, face_id=link[0], snid=link[1])])
                if DataQualityChecker.check_contains_special_characters(link[2]):
                    ctx.append_brw_logs([dict(errno=QualityErrNo.L0021, face_id=link[0], enid=link[2])])
                if link[3] not in BRW_LINK_KINDS:
                    ctx.append_brw_logs([dict(errno=QualityErrNo.L0027, face_id=link[0], kind=link[3])])
                if link[4] not in BRW_LINK_FORMS:
                    ctx.append_brw_logs([dict(errno=QualityErrNo.L0032, face_id=link[0], form=link[4])])

                _wkt = shapely.wkt.loads(link[6])
                try:
                    angles = helper.Geoser.check_angle(_wkt, BRW_MIN_ANGLE)
                    for angle in angles:
                        angle_size, angle_point, angle_points = angle
                        ctx.append_brw_logs([
                            dict(errno=QualityErrNo.L0060, link_id=link[0], angle_size=angle_size,
                                 point=angle_point, geom=shapely.geometry.Polygon(angle_points).wkt)
                        ])
                except Exception as e:
                    ctx.append_brw_logs([dict(errno=QualityErrNo.L0060, link_id=link[0], angle_err=str(e))])

    def _check_node(self, ctx: Context):
        """ 质检铁路节点 """
        with pgsql.get_connection(ctx.delivery_conf) as sd_conn, sd_conn.cursor() as sd_curs:
            table_checks = [
                ['node_id', 'varchar', 128, QualityErrNo.N0002],
            ]

            for _item in table_checks:
                _item_info = TableQualityChecker.get_column_info(sd_curs, self.NODE, _item[0])
                if not (TableQualityChecker.check_column_is_varchar(_item_info) and
                        TableQualityChecker.check_column_len(_item_info, _item[2])):
                    ctx.append_brw_logs([dict(errno=_item[3])])

            links = pgsql.fetch_all(sd_conn, f'select node_id,kind,form from {self.NODE}')
            for link in links:
                if DataQualityChecker.check_contains_special_characters(link[0]):
                    ctx.append_brw_logs([dict(errno=QualityErrNo.N0005, face_id=link[0])])
                if link[1] not in BRW_NODE_KINDS:
                    ctx.append_brw_logs([dict(errno=QualityErrNo.N0027, face_id=link[0], kind=link[1])])
                if link[2] not in BRW_NODE_FORMS:
                    ctx.append_brw_logs([dict(errno=QualityErrNo.N0032, face_id=link[0], form=link[2])])

    def quality_check(self, ctx: Context):
        """ 质量检测 """
        self._check_link(ctx)
        self._check_node(ctx)
