"""
建筑物商单
"""
import shapely.wkt
from shapely.geometry import Polygon
from dataclasses import asdict
from src.business_order import enums, helper
from src.business_order.base import Context, Gis
from src.business_order.query import OrderQuery, OrderWriter
from src.business_order.quality import DataQ<PERSON><PERSON><PERSON><PERSON><PERSON>, QualityErrNo, \
    TableQualityChecker, process_repeated_points
from src.tools import pgsql


BUD_MIN_AREA = 2  # 建筑物最小面积
BUD_MIN_ANGLE = 10  # 建筑物最小角度
QUANGUO_AOI = 'sd_blu_face_quanguo'  # 全国 AOI
QUANGUO_WATER = 'sd_blc_water_quanguo'  # 全国 Water


def _process_overlap_check(sd_conf, table, start_id, end_id):
    """
    建筑物压盖检测
    """

    overlaps = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, st_astext(geom) as geom from {table} where face_id>=%s 
            and face_id<=%s order by face_id;
        """

        lap_sql = f"""
            select face_id, st_astext(geom) as geom from {table} where st_intersects(geom, %s) 
            and face_id<>%s;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            buds = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for bud in buds:
                laps = pgsql.fetch_all(conn, lap_sql, [f'SRID=4326;{bud[1]}', bud[0]])
                if len(laps) == 0:
                    continue

                bud_wkt = shapely.wkt.loads(bud[1])
                for lap in laps:
                    lap_wkt = shapely.wkt.loads(lap[1])
                    if bud_wkt.overlaps(lap_wkt) or bud_wkt.covers(lap_wkt) or lap_wkt.covers(bud_wkt):
                        overlaps.append((bud[0], lap[0]))

    return overlaps


def _process_contact_check(sd_conf, table, start_id, end_id, division):
    """
    建筑物接触检测
    """

    delete_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, area, st_astext(geom) as geom from {table} where face_id>=%s 
            and face_id<=%s order by face_id;
        """

        division_wkt = shapely.wkt.loads(division) if division else None

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            wait_delete = []
            buds = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for bud in buds:
                if bud[1] < BUD_MIN_AREA:
                    wait_delete.append(bud[0])
                    delete_logs.append(dict(errno=QualityErrNo.B0030, delid=bud[0]))
                    continue

                bud_wkt = shapely.wkt.loads(bud[2])
                if bud_wkt.geom_type != 'Polygon':
                    wait_delete.append(bud[0])
                    delete_logs.append(dict(errno=QualityErrNo.B0038, delid=bud[0]))
                    continue

                if helper.Geoser.check_contact(bud_wkt):
                    wait_delete.append(bud[0])
                    delete_logs.append(dict(errno=QualityErrNo.B0039, delid=bud[0]))
                    continue

                if division_wkt and not division_wkt.covers(bud_wkt):
                    wait_delete.append(bud[0])
                    delete_logs.append(dict(errno=QualityErrNo.B0052, delid=bud[0]))
                    continue

            OrderWriter.fix_delete(conn, table, 'face_id', wait_delete)
    return delete_logs


def _process_quality_check(sd_conf, table, start_id, end_id):
    """
    建筑物质量检测
    """

    delete_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, struct_id, name_ch, aoi_id, height, area, perimeter, st_astext(geom) as geom 
            from {table} where face_id>=%s and face_id<=%s order by face_id;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            buds = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for bud in buds:
                if DataQualityChecker.check_contains_special_characters(bud[0]):
                    delete_logs.append(dict(errno=QualityErrNo.B0005, face_id=bud[0]))

                if DataQualityChecker.check_contains_special_characters(bud[1]):
                    delete_logs.append(dict(errno=QualityErrNo.B0013, face_id=bud[0], struct_id=bud[1]))

                if DataQualityChecker.check_is_empty(bud[1]):
                    delete_logs.append(dict(errno=QualityErrNo.B0012, face_id=bud[0], struct_id=bud[1]))

                _wkt = shapely.wkt.loads(bud[7])
                try:
                    angles = helper.Geoser.check_angle(_wkt, BUD_MIN_ANGLE)
                    for angle in angles:
                        angle_size, angle_point, angle_points = angle
                        delete_logs.append(
                            dict(errno=QualityErrNo.B0043, face_id=bud[0], angle_size=angle_size,
                                 point=angle_point, geom=Polygon(angle_points).wkt))
                except Exception as e:
                    delete_logs.append(dict(errno=QualityErrNo.B0043, face_id=bud[0], angle_err=str(e)))

    return delete_logs


def process_contact_check(sd_conf, table, division):
    """ 
    多进程建筑物压盖检测
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id')
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1], division=division) for x in _segs]

    del_logs = helper.multi_run(segments, _process_contact_check, 32)
    return sum(del_logs, [])


def process_overlap_check(sd_conf, table):
    """ 
    多进程建筑物压盖检测
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id')
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_gets = helper.multi_run(segments, _process_overlap_check, 32)
    del_ids = sum(del_gets, [])
    overlaps_delids = list({tuple(sorted(x)) for x in del_ids})

    wait_del = []
    wait_logs = []
    with pgsql.get_connection(sd_conf) as conn:
        query_sql = f"select face_id,src,create_time from {table} where face_id=%s;"
        for overlap in overlaps_delids:
            bud1 = pgsql.fetch_one(conn, query_sql, [overlap[0]])
            bud2 = pgsql.fetch_one(conn, query_sql, [overlap[1]])
            budinfo = "b1:{} {} {}, b2:{} {} {}.".format(
                bud1[0], bud1[1], bud1[2],
                bud2[0], bud2[1], bud2[2],
            )
            if bud1[1] == 'IR' and bud2[1] != 'IR':
                wait_del.append(bud1[0])
                wait_logs.append(dict(errno=QualityErrNo.B0050, budinfo=budinfo, delid=bud1[0]))
                continue
            elif bud1[1] != 'IR' and bud2[1] == 'IR':
                wait_del.append(bud2[0])
                wait_logs.append(dict(errno=QualityErrNo.B0050, budinfo=budinfo, delid=bud2[0]))
                continue
            elif bud1[2] < bud2[2]:
                wait_del.append(bud1[0])
                wait_logs.append(dict(errno=QualityErrNo.B0050, budinfo=budinfo, delid=bud1[0]))
                continue
            else:
                wait_del.append(bud2[0])
                wait_logs.append(dict(errno=QualityErrNo.B0050, budinfo=budinfo, delid=bud2[0]))
                continue
        OrderWriter.fix_delete(conn, table, 'face_id', [wait_del])
    return wait_logs


def process_quality_check(sd_conf, table):
    """ 多进程质检 """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id')
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_logs = helper.multi_run(segments, _process_quality_check, 32)
    return sum(del_logs, [])


def _process_overlap_by_aoi_water(sd_conf, table, start_id, end_id):
    """ 质检建筑物与水系、AOI 的压盖 """
    overlaps = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, st_astext(geom) as geom from {table} where face_id>=%s and face_id<=%s 
            order by face_id;
        """

        lap_sql = f"""
            select face_id, st_astext(geom) as geom from sd_bud_face where st_intersects(geom, %s);
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        is_water = True if table == QUANGUO_WATER else False

        for seg in segs:
            aois = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for aoi in aois:
                laps = pgsql.fetch_all(conn, lap_sql, [f'SRID=4326;{aoi[1]}'])
                aoi_wkt = shapely.wkt.loads(aoi[1])

                for lap in laps:
                    lap_wkt = shapely.wkt.loads(lap[1])
                    if is_water:
                        if aoi_wkt.overlaps(lap_wkt) or aoi_wkt.covers(lap_wkt) or lap_wkt.covers(aoi_wkt):
                            overlaps.append(lap[0])
                    else:
                        if aoi_wkt.overlaps(lap_wkt):
                            overlaps.append(lap[0])

    return overlaps


def _process_overlap_by_bud(sd_conf, table, start_id, end_id):
    """ 质检建筑物与水系、AOI 的压盖 """
    overlaps = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, st_astext(geom) as geom from {table} where face_id>=%s and face_id<=%s 
            order by face_id;
        """

        lap_sql = f"""
            select face_id, st_astext(geom) as geom from {QUANGUO_AOI} where st_intersects(geom, %s);
        """
        lap_sql2 = f"""
            select face_id, st_astext(geom) as geom from {QUANGUO_WATER} where st_intersects(geom, %s);
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            buds = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for bud in buds:
                laps = pgsql.fetch_all(conn, lap_sql, [f'SRID=4326;{bud[1]}'])
                laps2 = pgsql.fetch_all(conn, lap_sql2, [f'SRID=4326;{bud[1]}'])
                bud_wkt = shapely.wkt.loads(bud[1])

                for lap in laps:
                    lap_wkt = shapely.wkt.loads(lap[1])
                    if bud_wkt.overlaps(lap_wkt):
                        overlaps.append(bud[0])

                for lap in laps2:
                    lap_wkt = shapely.wkt.loads(lap[1])
                    if bud_wkt.overlaps(lap_wkt) or bud_wkt.covers(lap_wkt) or lap_wkt.covers(bud_wkt):
                        overlaps.append(lap[0])

    return overlaps


def process_overlap_aoi_water(sd_conf, table, uqkey='face_id'):
    """ 
    通用多进程处理建筑物与水系、AOI 的压盖
    """
    with pgsql.get_connection(sd_conf) as conn:
        bud_count = OrderQuery.get_count(conn, table, '', [])
        if bud_count > 5000000:
            _aoi_segs = OrderQuery.generate_segments(conn.cursor(), QUANGUO_AOI, uqkey)
            _water_segs = OrderQuery.generate_segments(conn.cursor(), QUANGUO_WATER, uqkey)
        else:
            _bud_segs = OrderQuery.generate_segments(conn.cursor(), table, uqkey)

    wait_delete = []
    if bud_count > 5000000:
        _dels = helper.multi_run([{'sd_conf': sd_conf, 'table': QUANGUO_AOI, 'start_id': x[0], 'end_id': x[1]}
                                  for x in _aoi_segs], _process_overlap_by_aoi_water, 32)
        aoi_overlaps_del_ids = sum(_dels, [])
        wait_delete.extend(aoi_overlaps_del_ids)

        _dels2 = helper.multi_run([{'sd_conf': sd_conf, 'table': QUANGUO_WATER, 'start_id': x[0], 'end_id': x[1]}
                                   for x in _water_segs], _process_overlap_by_aoi_water, 32)
        water_overlaps_del_ids = sum(_dels2, [])
        wait_delete.extend(water_overlaps_del_ids)
    else:
        _dels3 = helper.multi_run([{'sd_conf': sd_conf, 'table': table, 'start_id': x[0], 'end_id': x[1]}
                                   for x in _bud_segs], _process_overlap_by_bud, 32)
        water_aoi_overlaps_del_ids = sum(_dels3, [])
        wait_delete.extend(water_aoi_overlaps_del_ids)

    with pgsql.get_connection(sd_conf) as conn:
        OrderWriter.fix_delete(conn, table, uqkey, wait_delete)


class BudGis(Gis):
    """
    建筑处理类
    """

    TABLE = 'sd_bud_face'

    log_type = enums.ExportCondKey.BUD.value

    def create_table(self, ctx: Context):
        """ 创建数据表 """
        with pgsql.get_connection(ctx.delivery_conf) as conn:
            OrderWriter.create_bud_table(conn, self.TABLE)

    def _filter_quanguo_aoi(self, ctx: Context):
        """ 筛选全国 AOI 入库 """
        with pgsql.get_connection(ctx.delivery_conf) as conn:
            OrderWriter.create_quanguo_aoi_table(conn, QUANGUO_AOI)

        aoi_csv = OrderQuery.copy_quanguo_aoi_from_master(ctx.master_conf, ctx.export_id)

        copy_path = OrderQuery.convert_coord_csv(aoi_csv, ctx.order.export_coord, 3)

        # 写入成果库
        in_sql = f"copy {QUANGUO_AOI}(face_id,kind,mesh_id,geom)"
        helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

    def _filter_quanguo_water(self, ctx: Context):
        """ 筛选全国 Water 入库 """
        with pgsql.get_connection(ctx.delivery_conf) as conn:
            OrderWriter.create_quanguo_water_table(conn, QUANGUO_WATER)

        water_csv = OrderQuery.copy_quanguo_water_from_master(ctx.master_conf, ctx.export_id)

        copy_path = OrderQuery.convert_coord_csv(water_csv, ctx.order.export_coord, 4)

        # 写入成果库
        in_sql = f"copy {QUANGUO_WATER}(face_id,name_ch,kind,mesh_id,geom)"
        helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

    def filter_data(self, ctx: Context):
        """ 筛选数据入库 """

        # 数据写入到文件
        where, where_params = self.build_where(ctx, ctx.order.cond[enums.ExportCondKey.BUD.value], 'a.')
        bud_csv = OrderQuery.copy_bud_from_master(ctx.master_conf, ctx.export_id, where, where_params)

        # 转坐标系
        copy_path = OrderQuery.convert_coord_csv(bud_csv, ctx.order.export_coord, 10)

        # 写入成果库
        in_sql = f"copy sd_bud_face(face_id,struct_id,name_ch,aoi_id,height,area," + \
            "perimeter,mesh_id,src,create_time,geom)"
        helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

        self._filter_quanguo_aoi(ctx)
        self._filter_quanguo_water(ctx)

    def export_data(self, ctx: Context):
        """ 导出数据 """
        field = ctx.get_export_geom_field()
        export_dir = ctx.get_export_dest_name()
        dest = f'{export_dir}/{enums.DeliveryDirs.BUD}'
        exporter = helper.JExporter(ctx.delivery_conf, ctx.order.export_format, ctx.order.export_encode)
        exporter.export(dest, 'bud_face', enums.ExportSQL.BUD.geom(field))

        dict_data = [asdict(record) for record in ctx.bud_quality_log]
        exporter.to_csv_by_data(export_dir, 'bud_quality', None, dict_data)

    def quality_deal(self, ctx: Context):
        """ 数据质量处理 """

        process_repeated_points(ctx.delivery_conf, self.TABLE)
        process_overlap_aoi_water(ctx.delivery_conf, self.TABLE)
        ctx.append_bud_logs(process_contact_check(ctx.delivery_conf, self.TABLE,
                            ctx.get_gg(enums.ExportCondKey.BUD.value)))
        ctx.append_bud_logs(process_overlap_check(ctx.delivery_conf, self.TABLE))

    def quality_check(self, ctx: Context):
        """ 质量检测 """

        with pgsql.get_connection(ctx.delivery_conf) as sd_conn, sd_conn.cursor() as sd_curs:
            faceid_info = TableQualityChecker.get_column_info(sd_curs, self.TABLE, 'face_id')
            struct_info = TableQualityChecker.get_column_info(sd_curs, self.TABLE, 'struct_id')
            namech_info = TableQualityChecker.get_column_info(sd_curs, self.TABLE, 'name_ch')
            aoiid_info = TableQualityChecker.get_column_info(sd_curs, self.TABLE, 'aoi_id')

            if not (TableQualityChecker.check_column_is_varchar(faceid_info)
                    and TableQualityChecker.check_column_len(faceid_info, 128)):
                ctx.append_bud_logs([dict(errno=QualityErrNo.B0003)])

            if not (TableQualityChecker.check_column_is_varchar(struct_info)
                    and TableQualityChecker.check_column_len(struct_info, 128)):
                ctx.append_bud_logs([dict(errno=QualityErrNo.B0011)])

            if not (TableQualityChecker.check_column_is_varchar(namech_info)
                    and TableQualityChecker.check_column_len(namech_info, 120)):
                ctx.append_bud_logs([dict(errno=QualityErrNo.B0018)])

            if not (TableQualityChecker.check_column_is_varchar(aoiid_info)
                    and TableQualityChecker.check_column_len(aoiid_info, 128)):
                ctx.append_bud_logs([dict(errno=QualityErrNo.B0022)])

            if OrderQuery.get_count(sd_conn, self.TABLE, "and aoi_id<>''") == 0:
                ctx.append_bud_logs([dict(errno=QualityErrNo.B0023)])

            if OrderQuery.get_count(sd_conn, self.TABLE, "and name_ch<>''") == 0:
                ctx.append_bud_logs([dict(errno=QualityErrNo.B0019)])

        ctx.append_bud_logs(process_quality_check(ctx.delivery_conf, self.TABLE))
