""" 商单交付的枚举 """

# coding=utf-8
from enum import Enum
from dataclasses import dataclass


class StepName:
    """商单处理步骤管理"""

    ReadyInitDeliveryDB = "data_ready_init_delivery_db"
    ReadyCreateTable = "data_ready_create_table"
    ReadyFilterDataToDB = "data_ready_filter_data_to_db"
    ProcessingDataQualityDeal = "data_processing_data_quality_deal"
    ProcessingDataExport = "data_processing_data_export"
    ProcessingDataZip = "data_processing_data_zip"
    ProcessingDataUpload = "data_processing_data_upload"


class DBConf(Enum):
    """数据库连接配置"""

    def psql(self):
        """根据配置生成 psql -xx 连接模式"""
        return self.to_psql(self.value)

    def host(self):
        """根据配置生成 host -xx 模式"""
        return self.to_host(self.value)

    @staticmethod
    def to_psql(conf):
        """根据配置生成 psql -xx 连接模式"""
        return f"psql -h {conf['host']} -U{conf['user']} -W{conf['pwd']} -p{conf['port']} -d{conf['db']}"

    @staticmethod
    def to_host(conf):
        """根据配置生成 host -xx 模式"""
        return f"host={conf['host']} dbname={conf['db']} port={conf['port']} user={conf['user']} password={conf['pwd']}"


class ExportCondKey(Enum):
    """导出数据类型枚举"""

    BUD = "BUD"
    AOI = "BLU"
    BRW = "BRW"
    BAD = "BAD"
    BLC_WATER = "BLC_WATER"
    BLC_GREEN = "BLC_GREEN"


class DeliveryFormat(Enum):
    """导出格式枚举"""

    TAB = "TAB"
    SHP = "SHP"
    MID_MIF = "MIDMIF"
    TXT = "TXT"
    CSV = "CSV"
    GEOJSON = "GEOJSON"

    @classmethod
    def export_geom_key(cls, member):
        """不同格式，不同的输出 geom"""
        if member in [cls.TXT.value, cls.CSV.value]:
            return "st_astext(geom) as geom"
            # return "(case when St_IsEmpty(geom)='t' then Null else st_astext(geom) end) as geom"
        if member in [cls.GEOJSON, cls.SHP.value, cls.TAB.value]:
            return "geom"
        return "(case when St_IsEmpty(geom)='t' then Null else geom end) as geom"


class DeliveryCoord:
    """导出坐标系枚举"""

    GCJ02 = "GCJ02"
    BD09 = "BD09"
    WSG84 = "WGS84"
    BD09MC = "BD09MC"


class DeliveryEncode(Enum):
    """导出格式枚举"""

    UTF8 = "UTF-8"
    GBK = "GBK"
    GB18030 = "GB18030"


@dataclass
class DeliveryDBConfig:
    """创建成果库配置"""
    url: str
    domain: str
    create_action: str
    keep_days: int


@dataclass
class DeliveryFTPConfig:
    """交付FTP配置"""
    host: str
    port: int
    user: str
    pwd: str
    upload_dir: str


@dataclass
class DeliveryOtherConfig:
    """交付 ChenXi 配置"""
    upload_url_dev: str
    upload_url_prod: str
    download_url_dev: str
    download_url_prod: str


class DivisionDataSource:
    """数据源枚举"""
    SOURCE_B = "FROM_B"
    SOURCE_C = "FROM_C"


class CommonExportStatus:
    """商单状态枚举"""

    INIT = "INIT"
    PROGRESSING = "PROGRESSING"
    FAILED = "FAILED"
    END = "END"


class DivisionReqKey(Enum):
    """行政区划分类枚举"""

    VILLAGES = "village"
    TOWNS = "town"
    COUNTIES = "county"
    CITIES = "city"
    PROVINCES = "pro"

    def lower(self):
        """转小写"""
        return self.name.lower()

    @classmethod
    def lower_tuple(cls):
        """转tuple"""
        return tuple(member.name.lower() for member in cls)

    @classmethod
    def get_value(cls, key):
        """根据大写Key获取值"""
        return getattr(cls, key.upper(), None).value


class DivisionKey(Enum):
    """行政区划分类枚举2"""

    VILLAGE = "village"
    TOWN = "town"
    COUNTY = "county"
    CITY = "city"
    PRO = "pro"

    @classmethod
    def to_tuple(cls):
        """转 tuple"""
        return tuple(member.value for member in cls)

    @classmethod
    def get_division_fields(cls, division):
        """获取当前行政区划 field 及所有上级 field"""
        dd = cls.to_tuple()
        index = dd.index(division)
        fields = [f"{d}_code,{d}_name" for d in dd[index:]]
        return ",".join(fields)

    @classmethod
    def get_higher_division(cls, division):
        """获取上一级标识"""
        dd = cls.to_tuple()
        index = dd.index(division) + 1
        return [d for d in dd[index:]]

    @classmethod
    def get_last_division(cls, division):
        """获取下一级标识"""
        tb = cls.to_tuple()
        p_div_index = tb.index(division) - 1
        return tb[p_div_index]


class BadFields:
    """ 行政区划字段 """

    pro = ['pro_code', 'pro_name']
    city = ['city_code', 'city_name']
    county = ['county_code', 'county_name']
    town = ['town_code', 'town_name']
    village = ['village_code', 'village_name']

    @classmethod
    def vils_full(cls):
        """ 村级完整字段 """
        return cls.village + cls.town + cls.county + cls.city + cls.pro

    @classmethod
    def town_full(cls):
        """ 镇级完整字段 """
        return cls.town + cls.county + cls.city + cls.pro

    @classmethod
    def coun_full(cls):
        """ 区县级完整字段 """
        return cls.county + cls.city + cls.pro

    @classmethod
    def city_full(cls):
        """ 市级完整字段 """
        return cls.city + cls.pro

    @classmethod
    def pro_full(cls):
        """ 省级字段 """
        return cls.pro


class DeliveryDirs:
    """交付数据目录"""

    BUD = "00-building"
    AOI = "01-landutil"
    WATER = "02-landcover"
    GREEN = "02-landcover"
    BRW = "03-railway"
    VILLAGE = "04-division/village"
    TOWN = "04-division/town"
    COUNTY = "04-division/county"
    CITY = "04-division/city"
    PRO = "04-division/province"


class DeliveryTables(Enum):
    """交付表名称"""

    BUD = "sd_bud_face"
    AOI = "sd_blu_face"
    WATER = "sd_blc_water"
    GREEN = "sd_blc_green"
    BRW_NODE = "sd_brw_node"
    BRW_LINK = "sd_brw_link"
    VILLAGE = "sd_bad_village"
    TOWN = "sd_bad_town"
    COUNTY = "sd_bad_county"
    CITY = "sd_bad_city"
    PRO = "sd_bad_pro"


class TableInsertSQL(Enum):
    """数据筛选入库 insert sql"""

    BUD = """
        insert into sd_bud_face(face_id, struct_id, name_ch, aoi_id, height, area, perimeter, geom, mesh_id) 
        values(%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    AOI = """
        insert into sd_blu_face(face_id,poi_bid,name_ch,area,perimeter,geom,mesh_id) 
        values(%s, %s, %s, %s, %s, %s, %s)
    """
    WATER = """
        insert into sd_blc_water(face_id,name_ch,name_ph,kind,admin_id,area,perimeter,geom,mesh_id) 
        values(%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    GREEN = """
        insert into sd_blc_green(face_id,name_ch,name_ph,kind,admin_id,area,perimeter,geom,mesh_id) 
        values(%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    BRW_NODE = """
        insert into sd_brw_node(node_id,kind,form,mesh_id,geom) 
        values(%s, %s, %s, %s, %s)
    """
    BRW_LINK = """
        insert into sd_brw_link(
            link_id, s_nid, e_nid, kind, form, length, 
            name_ch, name_ph, name_en, z_level, mesh_id, geom
        ) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    VILLAGE = """
        insert into sd_bad_village(
            guid,village_code,village_name,town_code,town_name,
            county_code,county_name,city_code,city_name,pro_code,pro_name,geom
        ) 
        values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    TOWN = """
        insert into sd_bad_town(
            guid,town_code,town_name,county_code,county_name,
            city_code,city_name,pro_code,pro_name,geom
        ) 
        values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    COUNTY = """
        insert into sd_bad_county(
            guid, county_code, county_name, city_code, city_name, pro_code, pro_name, geom
        ) 
        values(%s, %s, %s, %s, %s, %s, %s, %s)
    """
    CITY = """
        insert into sd_bad_city(guid, city_code, city_name, pro_code, pro_name, geom) 
        values(%s, %s, %s, %s, %s, %s)
    """
    PRO = """
        insert into sd_bad_pro(guid, pro_code, pro_name, geom) 
        values(%s, %s, %s, %s)
    """

    @classmethod
    def get_value(cls, key):
        """根据值获取 sql"""
        return getattr(TableInsertSQL, key.upper(), None).value


class ExportSQL(Enum):
    """导出 sql 枚举"""

    BUD = "select face_id,struct_id,name_ch,aoi_id,height,area,perimeter,{geom} from sd_bud_face"
    AOI = "select face_id,poi_id,area,perimeter,name_ch as name,{geom} from sd_blu_face"
    WATER = "select face_id,name_ch,name_ph,kind,admin_id,area,perimeter,{geom} from sd_blc_water"
    GREEN = "select face_id,name_ch,name_ph,kind,admin_id,area,perimeter,{geom} from sd_blc_green"
    BRW_LINK = "select link_id,s_nid,e_nid,kind,form,length,name_ch,name_ph,name_en,z_level,{geom} from sd_brw_link"
    BRW_NODE = "select node_id,kind,form from sd_brw_node"
    VILLAGE = "select guid as face_id,village_code as admin_id,REPLACE(REPLACE(village_name,'\"',''),'''','') as name_ch,{geom} from sd_bad_village"
    TOWN = "select guid as face_id,town_code as admin_id,town_name as name_ch,{geom} from sd_bad_town"
    COUNTY = "select guid as face_id,county_code as admin_id,county_name as name_ch,{geom} from sd_bad_county"
    CITY = "select guid as face_id,city_code as admin_id,city_name as name_ch,{geom} from sd_bad_city"
    PRO = "select guid as face_id,pro_code as admin_id,pro_name as name_ch,{geom} from sd_bad_pro"

    def trip(self):
        """去换行，便于linux执行导出"""
        return self.value.strip().replace("\n", " ")

    def geom(self, field):
        """替换 geom key ，不同格式要求不同的 geom 查询方式"""
        return self.value.format(geom=field)
