"""
绿地水系商单
"""
import shapely.wkt
from shapely.geometry import Polygon
from dataclasses import asdict
from src.business_order import enums, helper
from src.business_order.base import Context, Gis
from src.business_order.quality import DataQuality<PERSON>he<PERSON>,  \
    QualityErrNo, TableQualityChecker, process_repeated_points
from src.business_order.query import OrderWriter, OrderQuery
from src.tools import pgsql

GREEN_ANGLE_SIZE = 10
GREEN_MIN_AREA = 10
GREEN_KINDS = ['11', '12', '14', '15', '16', '21', '25']


def _process_contact_check(sd_conf, table, start_id, end_id):
    """
    建筑物接触检测
    """

    delete_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, area, name_ch, name_ph, st_astext(geom) as geom from {table}
            where face_id>=%s and face_id<=%s order by face_id;
        """
        update_sql = f"""
            update {table} set name_ph=%s where face_id=%s;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            wait_delete = []
            wait_update = []
            greens = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for green in greens:
                if green[1] < GREEN_MIN_AREA:
                    wait_delete.append(green[0])
                    delete_logs.append(dict(errno=QualityErrNo.G0030, delid=green[0]))
                    continue

                green_wkt = shapely.wkt.loads(green[4])
                if helper.Geoser.check_contact(green_wkt):
                    wait_delete.append(green[0])
                    delete_logs.append(dict(errno=QualityErrNo.G0039, delid=green[0]))
                    continue

                # 中文名为空但英文名不为空，需要删除
                if green[2] == '' and green[3] != '':
                    wait_delete.append(green[0])
                    delete_logs.append(dict(errno=QualityErrNo.G0061, delid=green[0]))
                    continue

                # 中文名不为空但英文名为空，需要更新
                if green[2] != '' and green[3] == '':
                    pinyin_txt = DataQualityChecker.fix_pinyin(green[2])
                    wait_update.append((pinyin_txt, green[0]))

            OrderWriter.fix_delete(conn, table, 'face_id', wait_delete)
            pgsql.execute_many(conn, update_sql, wait_update)

    return delete_logs


def _process_quality_check(sd_conf, table, start_id, end_id):
    """
    质量检测
    """

    delete_logs = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id,name_ch,name_ph,kind,admin_id,area,perimeter,st_astext(geom) as geom 
            from {table} where face_id>=%s and face_id<=%s order by face_id;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            greens = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for _green in greens:
                if DataQualityChecker.check_contains_special_characters(_green[0]):
                    delete_logs.append(dict(errno=QualityErrNo.G0005, face_id=_green[0]))

                if DataQualityChecker.check_is_empty(_green[3]):
                    delete_logs.append(dict(errno=QualityErrNo.G0021, face_id=_green[0], kind=_green[3]))

                if DataQualityChecker.check_is_empty(_green[4]):
                    delete_logs.append(dict(errno=QualityErrNo.G0025, face_id=_green[0], admin_id=_green[4]))

                if _green[3] not in GREEN_KINDS:
                    delete_logs.append(dict(errno=QualityErrNo.G0020, face_id=_green[0], admin_id=_green[4]))

                _wkt = shapely.wkt.loads(_green[7])
                try:
                    angles = helper.Geoser.check_angle(_wkt, GREEN_ANGLE_SIZE)
                    for angle in angles:
                        angle_size, angle_point, angle_points = angle
                        delete_logs.append(
                            dict(errno=QualityErrNo.G0043, face_id=_green[0], angle_size=angle_size,
                                 point=angle_point, geom=Polygon(angle_points).wkt))
                except Exception as e:
                    delete_logs.append(dict(errno=QualityErrNo.G0043, face_id=_green[0], angle_err=str(e)))

    return delete_logs


def _process_overlap(sd_conf, table, start_id, end_id):
    """
    AOI 重复检测
    """

    overlaps = []
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        sql = f"""
            select face_id, st_astext(geom) as geom from {table} where face_id>=%s
            and face_id<=%s order by face_id;
        """

        lap_sql = f"""
            select face_id, st_astext(geom) as geom from {table} where st_intersects(geom, %s)
            and face_id<>%s;
        """

        segs = OrderQuery.generate_segments(
            curs, table, 'face_id', f"where face_id>='{start_id}' and face_id<='{end_id}'", 2000)

        for seg in segs:
            aois = pgsql.fetch_all(conn, sql, [seg[0], seg[1]])
            for aoi in aois:
                laps = pgsql.fetch_all(conn, lap_sql, [f'SRID=4326;{aoi[1]}', aoi[0]])
                aoi_wkt = shapely.wkt.loads(aoi[1])
                for lap in laps:
                    lap_wkt = shapely.wkt.loads(lap[1])
                    if aoi_wkt.overlaps(lap_wkt) or aoi_wkt.covers(lap_wkt) or lap_wkt.covers(aoi_wkt):
                        overlaps.append((aoi[0], lap[0]))
    return overlaps


def process_overlap(sd_conf, table):
    """
    多进程压盖检测
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id')
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    _overlaps = helper.multi_run(segments, _process_overlap, 32)
    _overlap_ids = sum(_overlaps, [])

    overlaps_delids = list({tuple(sorted(x)) for x in _overlap_ids})
    wait_dels = []
    wait_logs = []
    with pgsql.get_connection(sd_conf) as conn:
        query_sql = f"""
            select face_id,area from {table} where face_id=%s;
        """
        for overlap in overlaps_delids:
            green1 = pgsql.fetch_one(conn, query_sql, [overlap[0]])
            green2 = pgsql.fetch_one(conn, query_sql, [overlap[1]])
            if green1[1] < green2[1]:
                wait_dels.append(green1[0])
                wait_logs.append(dict(errno=QualityErrNo.G0049, reserve=green2[0], del_id=green1[0]))
            else:
                wait_dels.append(green2[0])
                wait_logs.append(dict(errno=QualityErrNo.G0049, reserve=green1[0], del_id=green2[0]))

        OrderWriter.fix_delete(conn, table, 'face_id', wait_dels)

    return wait_logs


def process_contact(sd_conf, table):
    """
    多进程质检
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id')
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_logs = helper.multi_run(segments, _process_contact_check, 32)
    return sum(del_logs, [])


def process_quality_check(sd_conf, table):
    """
    多进程质检
    """

    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        _segs = OrderQuery.generate_segments(curs, table, 'face_id')
        segments = [dict(sd_conf=sd_conf, table=table, start_id=x[0], end_id=x[1]) for x in _segs]

    del_logs = helper.multi_run(segments, _process_quality_check, 32)
    return sum(del_logs, [])


class BlcGreenGis(Gis):
    """ 绿地水系处理类 """
    TABLE = 'sd_blc_green'

    def create_table(self, ctx: Context):
        """ 创建数据表 """
        with pgsql.get_connection(ctx.delivery_conf) as conn:
            OrderWriter.create_green_table(conn, self.TABLE)

    def filter_data(self, ctx: Context):
        """ 筛选数据入库 """

        # 数据写入到文件
        where, where_params = self.build_where(ctx, ctx.order.cond[enums.ExportCondKey.BLC_GREEN.value])
        aoi_csv = OrderQuery.copy_green_from_master(ctx.master_conf, ctx.export_id, where, where_params)

        # 转坐标系
        copy_path = OrderQuery.convert_coord_csv(aoi_csv, ctx.order.export_coord, 8)

        # 写入成果库
        in_sql = f"copy sd_blc_green(face_id,name_ch,name_ph,kind,admin_id,area,perimeter,mesh_id,geom)"
        helper.copy_from_table(ctx.delivery_conf, copy_path, in_sql)

    def export_data(self, ctx: Context):
        """ 导出数据 """
        field = ctx.get_export_geom_field()
        export_dir = ctx.get_export_dest_name()
        dest = f'{export_dir}/{enums.DeliveryDirs.GREEN}'
        exporter = helper.JExporter(ctx.delivery_conf, ctx.order.export_format, ctx.order.export_encode)
        exporter.export(dest, 'blc_face_shuixi', enums.ExportSQL.GREEN.geom(field))

        dict_data = [asdict(record) for record in ctx.green_quality_log]
        exporter.to_csv_by_data(export_dir, 'green_quality', None, dict_data)

    def quality_deal(self, ctx: Context):
        """ 质量处理 """
        process_repeated_points(ctx.delivery_conf, self.TABLE)
        process_contact(ctx.delivery_conf, self.TABLE)
        process_overlap(ctx.delivery_conf, self.TABLE)

    def quality_check(self, ctx: Context):
        """ 质量检测 """

        with pgsql.get_connection(ctx.delivery_conf) as sd_conn, sd_conn.cursor() as sd_curs:
            table_checks = [
                ['face_id', 'varchar', 128, QualityErrNo.G0003],
                ['name_ch', 'varchar', 120, QualityErrNo.G0011],
                ['name_ph', 'varchar', 1000, QualityErrNo.G0015],
                ['kind', 'varchar', 6, QualityErrNo.G0019],
                ['admin_id', 'varchar', 6, QualityErrNo.G0024],
            ]
            for _item in table_checks:
                _item_info = TableQualityChecker.get_column_info(sd_curs, self.TABLE, _item[0])
                if not (TableQualityChecker.check_column_is_varchar(_item_info) and
                        TableQualityChecker.check_column_len(_item_info, _item[2])):
                    ctx.append_green_logs([dict(errno=_item[3])])

            if OrderQuery.get_count(sd_conn, self.TABLE, "and name_ch<>''") == 0:
                ctx.append_green_logs([dict(errno=QualityErrNo.G0012)])

            if OrderQuery.get_count(sd_conn, self.TABLE, "and name_ph<>''") == 0:
                ctx.append_green_logs([dict(errno=QualityErrNo.G0016)])

        ctx.append_green_logs(process_quality_check(ctx.delivery_conf, self.TABLE))
