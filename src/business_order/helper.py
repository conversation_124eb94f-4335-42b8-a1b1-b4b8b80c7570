""" 商单助手函数 """
import csv
import math
import os
import sys
import requests
import shapely.wkt
import pymysql
import pymysql.cursors
import subprocess
from tqdm import tqdm
from multiprocessing import Pool
from shapely.geometry import Polygon, MultiPolygon, LineString, MultiLineString, Point
from mapio.utils.coord import gcj02_to_bd09, gcj02_to_wgs84, bd09_to_mercator, wgs84_to_gcj02
from src.tools import conf_tools


def _transform_polygon(poly, transform_func) -> Polygon:
    """polygon 转坐标"""
    # 转换外边界
    new_exterior = [transform_func(x, y) for x, y in poly.exterior.coords]

    # 转换内洞
    new_holes = [
        [transform_func(x, y) for x, y in hole.coords]
        for hole in poly.interiors
    ]

    return Polygon(new_exterior, new_holes)


def _transform_geom(poly, tran_func):
    """坐标转化"""
    if poly.geom_type == 'Polygon':
        res = _transform_polygon(poly, tran_func)
        return res

    if poly.geom_type == 'MultiPolygon':
        return MultiPolygon([_transform_polygon(_poly, tran_func) for _poly in poly.geoms])

    if poly.geom_type == 'LineString':
        return LineString([tran_func(x, y) for x, y in poly.coords])

    if poly.geom_type == 'MultiLineString':
        return MultiLineString([
            LineString([tran_func(x, y) for x, y in line.coords])
            for line in poly.geoms
        ])

    if poly.geom_type == 'Point':
        return Point(*tran_func(poly.x, poly.y))

    raise Exception('不支持的几何类型')


def transform_geom(poly, tran_type):
    """坐标转化,基于不同转化方法"""
    poly_wkt = shapely.wkt.loads(poly)

    if poly_wkt.is_empty:
        return poly_wkt
    if tran_type == 'GCJ02':
        return poly_wkt
    if tran_type == 'BD09':
        return _transform_geom(poly_wkt, gcj02_to_bd09)
    if tran_type == 'WGS84':
        return _transform_geom(poly_wkt, gcj02_to_wgs84)
    if tran_type == 'BD09MC':
        bd09_geom = _transform_geom(poly_wkt, gcj02_to_bd09)
        return _transform_geom(bd09_geom, bd09_to_mercator)
    if tran_type == 'WGS2GCJ':
        return _transform_geom(poly_wkt, wgs84_to_gcj02)

    raise Exception('不支持的转化类型')


def fill_key(keys: list, items: list):
    """key 填充"""
    if len(items) == 0:
        return None

    if isinstance(items, list) and isinstance(items[0], dict):
        return items

    def fill_item(item, _keys):
        _res = {}
        for _idx, _key in enumerate(_keys):
            _res[alias(_key)] = item[_idx]

        return _res

    if isinstance(items[0], dict):
        return items

    if not isinstance(items[0], list) and not isinstance(items[0], tuple):
        return fill_item(items, keys)

    resp = []
    for _item in items:
        resp.append(fill_item(_item, keys))
    return resp


def alias(key):
    """别名"""
    vals = [" as ", " "]
    for _val in vals:
        if _val not in key:
            continue

        fields = str(key).split(_val)
        key = fields[-1]
        break
    return key


def get_root_data_path():
    """获取项目根路径"""
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'data', 'order_temp_data')


def clear_order_tmp_file(export_id):
    """清理临时文件"""
    clear_path = os.path.join(get_root_data_path(), str(export_id))
    os.system(f"rm -r {clear_path}/*.csv")


def create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = conf_tools.get_mysql_conf('beeflow_rw')
    # host, port, user, pwd, database = conf_tools.get_mysql_conf('beeflow_test')
    return pymysql.connect(
        host=host, port=int(port), user=user, password=pwd, db=database,
        charset="utf8mb4", cursorclass=pymysql.cursors.DictCursor
    )


def to_psql(conf):
    """
    根据配置生成 psql
    """
    # -W{conf['pwd']}
    return f"psql -h {conf['host']} -U{conf['user']} -p{conf['port']} -d{conf['db']}"


def to_host(conf):
    """
    根据配置生成 host
    """
    return f"host={conf['host']} dbname={conf['db']} port={conf['port']} user={conf['user']} password={conf['pwd']}"


def run_cmd(cmd: list):
    """执行脚本"""
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()
    if process.returncode == 0:
        print("Command executed successfully.")
        print(stdout.decode())
    else:
        print(f"Error: {stderr.decode()}")


def _update_process(tqdm_bar, value):
    """ 更新进度条的值 """
    tqdm_bar.update(1)  # 每次调用 callback，进度条 +1


def multi_run(segs, process_func, multi):
    """ 并发执行 """

    results = []
    desc = process_func.__name__
    with tqdm(total=len(segs), desc=desc, mininterval=5) as tqdm_bar:
        p = Pool(multi)
        for _seg in segs:
            res = p.apply_async(process_func, kwds={**_seg},
                                callback=lambda value: _update_process(tqdm_bar, value))
            results.append(res)

        print("Waiting for all subprocesses done...")
        p.close()
        p.join()
        print("All subprocesses done.")

    return [r.get() for r in results]


def get_data_from_csv(cls, file_path, delimiter='\t', encode='utf-8'):
    """ 读取 txt 文件中的所有数据 """
    # PS: 一个常见编码是 iso-8859-1
    csv.field_size_limit(sys.maxsize)
    with open(file_path, 'r', newline='', encoding=encode) as file:
        reader = csv.DictReader(file, delimiter=delimiter)
        for row in reader:
            yield row


def copy_from_table(sd_conf, source_path, sql):
    """成果库写入方法"""
    psql_cmd = to_psql(sd_conf)

    print("copy from table")
    cmd = f"""
    export PGPASSWORD={sd_conf['pwd']}
    {psql_cmd} -c "\\{sql} FROM '{source_path}' WITH (FORMAT csv, DELIMITER '\t', HEADER true);"
    """
    os.system(cmd)


def copy_to_file(output_path, conf, sql):
    """ 筛选数据写入文件 """
    psql_cmd = to_psql(conf)

    print("copy data to file")
    cmd = f"""
    export PGPASSWORD={conf['pwd']}
    {psql_cmd} -c "\\{sql} TO '{output_path}' WITH CSV DELIMITER E'\t' HEADER;"
    """
    os.system(cmd)


def percent_s_string(n):
    """直接返回 `n` 个 '%s' 组成的字符串"""
    return ", ".join(["%s"] * n)


class NoticeHelper:
    """
    通知类
    """
    ShangdanGroupToken = 'd448a218ab0fdb2775dfc896dbac1f27e'
    ShangdanGroupId = 10928309

    @classmethod
    def receivers(cls):
        """导出结果通知人"""
        return ['v_rendandan02', 'v_chengxingjie']

    @classmethod
    def err_receivers(cls):
        """错误结果通知人"""
        return ['v_chengxingjie']

    @classmethod
    def send_notice_msg(cls, msg, users=None):
        """发送消息"""

        if users:
            send_users = users
        else:
            send_users = cls.receivers()

        cls._send_ruliu(
            msg,
            atuserids=send_users,
            token=cls.ShangdanGroupToken,
            group_id=cls.ShangdanGroupId
        )

    @classmethod
    def _send_ruliu(cls, msg, atuserids, token, group_id):
        """发送如流消息
        Args:
            msg：消息内容
            atuserids: at群的某个人
            token: 群机器人token
            group_id: 必须是int形式, 群号,如果为空则忽略
        """
        url = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=" + token
        # 如流消息的header信息
        header = {}
        # 如果的body信息
        if atuserids is not None and len(atuserids) != 0:
            body = [
                {"type": "TEXT", "content": msg},
                {"atuserids": atuserids, "atall": False, "type": "AT"},
            ]
        else:
            body = [{"type": "TEXT", "content": msg}]
        # 指定群号发送
        if group_id is not None:
            header["toid"] = [group_id]
        data = {
            "message": {
                "header": header,
                "body": body,
            }
        }
        requests.post(url, json=data)


class JExporter:
    """
    导出方法。示例如下：
    exporter = JExporter(JConf.pg.order, DeliveryFormat.MID_MIF.value, encoding="UTF-8")
    exporter.export(dest, 'division_town', sql)
    """

    def __init__(self, pg_conf, format, encoding):
        """ 初始化 """
        self.format = format
        self.pg_conf = pg_conf
        self.encode = encoding
        self.enstr = self._get_enstr()

    def _check_dir(self, dest):
        """ 检查输出目录是否存在，不存在则创建 """
        if not os.path.exists(dest):
            os.makedirs(dest)

    def _get_enstr(self):
        """ 确认输出时编码 enstr """
        if self.format in ['SHP']:
            return "-lco encoding=UTF-8" if self.encode in ["UTF-8", "UTF8"] else f"-lco encoding={self.encode}"
        elif self.format in ['MIDMIF']:
            return "encoding=UTF-8" if self.encode in ["UTF-8", "UTF8"] else f"ENCODING '{self.encode}'"
        elif self.format in ['TAB']:
            # TAB格式对GBK编码支持有问题，强制使用UTF-8
            return ""
        else:
            return "" if self.encode in ["UTF-8", "UTF8"] else f"-lco encoding={self.encode}"

    def export(self, dest, name, sql):
        """统一导出方法"""
        if self.format == 'CSV':
            return self.to_csv(dest, name, sql)
        if self.format == 'MIDMIF':
            return self.to_mif(dest, name, sql)
        if self.format == 'SHP':
            return self.to_shp(dest, name, sql)
        if self.format == 'TXT':
            return self.to_txt(dest, name, sql)
        if self.format == 'TAB':
            return self.to_tab(dest, name, sql)
        if self.format == 'GEOJSONs':
            return self.to_geojson(dest, name, sql)
        else:
            raise ValueError('其他格式暂未支持')

    def to_mif(self, dest, name, sql):
        """ 导出为 midmif """

        self._check_dir(dest)
        pg_url = to_host(self.pg_conf)
        cmd = f"""
            rm {dest}/{name}.mif
            rm {dest}/{name}.mid
            ogr2ogr -f 'MapInfo File' {dest}/{name}.mif -dsco format=mif PG:"{pg_url}" -a_srs wgs84 -sql "{sql}" {self.enstr}
        """
        os.system(cmd)

    def to_geojson(self, dest, name, sql):
        """ 导出为 tab 格式 """

        self._check_dir(dest)
        pg_url = to_host(self.pg_conf)
        cmd = f"""
            rm {dest}/{name}.geojson
            ogr2ogr -f 'GeoJSON' {dest}/{name}.geojson PG:"{pg_url}" -a_srs wgs84 -sql "{sql}" {self.enstr}
        """
        os.system(cmd)

    def to_tab(self, dest, name, sql):
        """ 导出为 tab 格式 """
        self._check_dir(dest)
        pg_url = to_host(self.pg_conf)

        cmd = f"""
            rm {dest}/{name}.tab && rm {dest}/{name}.id && rm {dest}/{name}.dat && rm {dest}/{name}.map
            ogr2ogr -f 'MapInfo File' {dest}/{name}.tab -dsco format=tab PG:"{pg_url}" -a_srs wgs84 -sql "{sql}" {self.enstr}
        """
        print(f"TAB导出命令: {cmd.strip()}")
        result = os.system(cmd)
        print(f"TAB导出结果: {result}, 文件: {dest}/{name}.tab")
        if result != 0:
            print(f"TAB导出失败，退出码: {result}")

        # 检查生成的文件
        tab_file = f"{dest}/{name}.tab"
        if os.path.exists(tab_file):
            file_size = os.path.getsize(tab_file)
            print(f"TAB文件大小: {file_size} bytes")
        else:
            print("TAB文件未生成")

    def to_csv(self, dest, name, sql):
        """ 导出为 csv """

        self._check_dir(dest)
        pg_url = to_psql(self.pg_conf)

        ex_sql = sql.replace("\n", " ")
        cmd = f"""
        export PGPASSWORD={self.pg_conf['pwd']}
        rm {dest}/{name}.csv
        {pg_url} -c "\\copy ({ex_sql}) TO '{dest}/{name}.csv' WITH CSV HEADER {self.enstr};"
        """
        os.system(cmd)

    def to_txt(self, dest, name, sql):
        """ 导出为 txt """

        self._check_dir(dest)
        pg_url = to_psql(self.pg_conf)
        cmd = f"""
        export PGPASSWORD={self.pg_conf['pwd']}
        rm {dest}/{name}.txt
        {pg_url} -c "\\copy ({sql}) TO '{dest}/{name}.txt' WITH CSV DELIMITER E'\t' HEADER {self.enstr};"
        """
        os.system(cmd)

    def to_shp(self, dest, name, sql):
        """ 导出为shp """

        self._check_dir(dest)
        pg_url = to_host(self.pg_conf)
        cmd = f"""
            rm {dest}/{name}.dbf
            rm {dest}/{name}.cpg
            rm {dest}/{name}.prj
            rm {dest}/{name}.shp
            rm {dest}/{name}.shx
            ogr2ogr -f 'ESRI Shapefile' {dest}/{name}.shp PG:"{pg_url}" -a_srs wgs84 -nln {name} -sql "{sql}" {self.enstr}
        """
        os.system(cmd)

    def to_csv_by_data(self, dest, name, headers, datas):
        """导出为 csv 格式，使用传入的数据"""

        if not datas:
            print("数据为空，无法写入 CSV")
            return

        self._check_dir(dest)
        os.system(f'rm {dest}/{name}.csv')

        if not headers:
            headers = datas[0].keys()
        with open(f'{dest}/{name}.csv', mode='w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=headers)
            writer.writeheader()
            writer.writerows(datas)


class Geoser:
    """几何检测"""

    @classmethod
    def calculate_distance(cls, p1, p2):
        """计算点距离"""
        return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

    @classmethod
    def _calculate_angle(cls, p1, p2, p3):
        """ 计算角度 """
        v1 = (p1[0] - p2[0], p1[1] - p2[1])
        v2 = (p3[0] - p2[0], p3[1] - p2[1])

        angle = math.atan2(v2[1], v2[0]) - math.atan2(v1[1], v1[0])
        angle = abs(math.degrees(angle))
        if angle > 180:
            angle = 360 - angle
        return angle

    @classmethod
    def get_polygon_angles(cls, polygon, angle_size) -> list:
        """ 获取几何面尖角 """
        coords = list(polygon.exterior.coords)
        res = []
        for i in range(0, len(coords) - 1):
            if i == 0:
                p1, p2, p3 = coords[-2], coords[i], coords[i + 1]  # 因为 coords 首尾是一个点，所以需要倒数第二个点
            else:
                p1, p2, p3 = coords[i - 1], coords[i], coords[i + 1]
            angle = cls._calculate_angle(p1, p2, p3)
            if 0 < angle <= angle_size:
                res.append((angle, coords[i], [p1, p2, p3]))
        return res

    @classmethod
    def get_polygon_angles_but_line(cls, line, angle_size) -> list:
        """ 获取 line 尖角 """
        coords = list(line.coords)
        res = []
        if len(coords) < 3:
            return []

        for i in range(0, len(coords) - 3):
            p1, p2, p3 = coords[i], coords[i + 1], coords[i + 2]
            angle = cls._calculate_angle(p1, p2, p3)
            if 0 < angle <= angle_size:
                res.append((angle, coords[i + 1], [p1, p2, p3]))
        return res

    @classmethod
    def check_angle(cls, poly, angle_size=10):
        """小角检测"""

        if poly.geom_type == "Polygon":
            return cls.get_polygon_angles(poly, angle_size)

        if poly.geom_type == "MultiPolygon":
            res = []
            for polygon in poly.geoms:
                res.extend(cls.get_polygon_angles(polygon, angle_size))
            return res

        if poly.geom_type == "LineString":
            return cls.get_polygon_angles_but_line(poly, angle_size)

        if poly.geom_type == "MultiLineString":
            res = []
            for polygon in poly.geoms:
                res.extend(cls.get_polygon_angles_but_line(polygon, angle_size))
            return res

        return []

    @classmethod
    def _cal_self_intersections(cls, poly, tolerance=1.01e-8):
        """ 自接触检测 """
        coords = poly.exterior.coords
        clen = len(coords)
        lines = []
        points = []
        for i in range(0, clen - 1):
            points.append(shapely.geometry.Point(coords[i]))
            lines.append(shapely.geometry.LineString([coords[i], coords[i + 1]]))

        for i in range(0, clen - 1):
            for j in range(0, clen - 1):
                if i > 0 and (i - j == 0 or i - j == 1):
                    continue
                if i == 0 and (j == clen - 2):
                    continue
                if 0 < points[i].distance(lines[j]) < tolerance:
                    print(points[i].distance(lines[j]))
                    return [coords[i]]
        return []

    @classmethod
    def _cal_self_intersections_math(cls, poly, tolerance=1.01e-8):
        """自接触检测"""
        coords = poly.exterior.coords
        clen = len(coords)

        for i in range(0, clen - 1):
            for j in range(0, clen - 1):
                if i > 0 and (i - j == 0 or i - j == 1):
                    continue
                if i == 0 and (j == clen - 2):
                    continue
                if 0 < cls._cal_point_to_seg_distance(coords[i], coords[j], coords[j + 1]) < tolerance:
                    print(cls._cal_point_to_seg_distance(coords[i], coords[j], coords[j + 1]))
                    return [coords[i]]
        return []

    @classmethod
    def _cal_point_to_seg_distance(cls, p, a, b):
        """
        计算点 P 到线段 AB 的最短距离。

        参数:
            p (tuple): 点 P 的坐标 (x_p, y_p)。
            a (tuple): 点 A 的坐标 (x_a, y_a)。
            b (tuple): 点 B 的坐标 (x_b, y_b)。

        返回:
            float: 点 P 到线段 AB 的最短距离。
        """
        # 解构坐标
        x_p, y_p = p
        x_a, y_a = a
        x_b, y_b = b

        # 计算向量 AB 和 AP
        ab_x = x_b - x_a
        ab_y = y_b - y_a
        ap_x = x_p - x_a
        ap_y = y_p - y_a

        # 计算点积 AB · AP
        dot_product = ab_x * ap_x + ab_y * ap_y

        # 计算 AB 的长度的平方
        ab_length_squared = ab_x**2 + ab_y**2

        # 计算投影长度 t
        if ab_length_squared == 0:  # 如果 A 和 B 重合
            t = 0
        else:
            t = dot_product / ab_length_squared

        # 根据 t 的值确定最近点
        if t < 0:
            closest_x, closest_y = x_a, y_a  # 最近点是 A
        elif t > 1:
            closest_x, closest_y = x_b, y_b  # 最近点是 B
        else:
            closest_x = x_a + t * ab_x
            closest_y = y_a + t * ab_y

        # 计算点 P 到最近点的距离
        distance = math.sqrt((x_p - closest_x)**2 + (y_p - closest_y)**2)
        return distance

    @classmethod
    def check_contact(cls, poly):
        """自接触检测"""
        res = []
        if poly.geom_type == "Polygon":
            res.extend(cls._cal_self_intersections_math(poly))

        if poly.geom_type == "MultiPolygon":
            for polygon in poly.geoms:
                res.extend(cls._cal_self_intersections_math(polygon))

        return res

    @classmethod
    def _check_repeated_points(cls, _poly, tolerance):
        """ 重复点检测 """
        coords = list(_poly.exterior.coords)
        for i in range(0, len(coords) - 1):
            _distance = cls.calculate_distance(coords[i], coords[i + 1])
            if _distance < tolerance:
                return (coords[i], coords[i + 1])
        return None

    @classmethod
    def _check_repeated_points_but_line(cls, _line, tolerance):
        """ 重复点检测 """
        coords = list(_line.coords)
        for i in range(0, len(coords) - 2):
            _distance = cls.calculate_distance(coords[i], coords[i + 1])
            if _distance < tolerance:
                return (coords[i], coords[i + 1])
        return None

    @classmethod
    def check_repeated_points(cls, poly, tolerance=1.01e-8):
        """ 检测多边形顶点重复情况 """

        if poly.geom_type == "Polygon":
            return cls._check_repeated_points(poly, tolerance)

        if poly.geom_type == "MultiPolygon":
            for cpoly in poly.geoms:
                rr = cls._check_repeated_points(cpoly, tolerance)
                if rr:
                    return rr

        if poly.geom_type == 'LineString':
            return cls._check_repeated_points_but_line(poly, tolerance)

        if poly.geom_type == "MultiLineString":
            for cpoly in poly.geoms:
                rr = cls._check_repeated_points_but_line(cpoly, tolerance)
                if rr:
                    return rr

        return None
