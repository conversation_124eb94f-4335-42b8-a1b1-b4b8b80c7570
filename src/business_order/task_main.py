# coding=utf-8
"""
主运行器
"""
import fcntl
import json
import os.path
import sys

from src.business_order import task_step
from src.business_order.base import Context, ChainExecutor
from src.business_order.query import OrderQuery, OrderWriter


class StepContrlEnum:
    """ 步骤控制枚举 """

    IsOpenUpload = True  # 是否开启上传


def run_single_order(obj):
    """ 单个商单处理方法 """

    print(f"开始处理商单 {obj['id']} .")
    with Context(obj["id"], obj["req"]) as ctx:
        # 创建步骤对象
        steps = [
            task_step.StepInitOrderDB(),
            task_step.StepGenTable(),
            task_step.StepFilterDataToTable(),
            task_step.StepQualityDeal(),
            task_step.StepQualityCheck(),
            task_step.StepExportDeliveryData(),
            task_step.StepZipDeliveryData(),
        ]
        if StepContrlEnum.IsOpenUpload:
            steps.append(task_step.StepUploadDeliveryData())

        chain_executor = ChainExecutor(steps)
        chain_executor.execute(ctx)


def run_script():
    """ 获取未处理商单清单，并进行处理 """
    work_list = OrderQuery.get_export_order_list()

    if len(work_list) == 0:
        return

    for item in work_list[0:1]:
        if item['conditions'] == '':
            req_param = json.loads(item['req'])
            condition = req_param.get("order_info")
            OrderWriter.update_export_order_condition(item['id'], condition)
        run_single_order(item)


def business_order_run_main(args):
    """ 开始执行商单输出操作 """
    lock_dir = './data/shangdan/lock'
    lock_file = 'business_order_run_main.lock'
    try:
        if not os.path.exists(lock_dir):
            os.makedirs(lock_dir)
        with open(f"{lock_dir}/{lock_file}", 'w') as file:
            # 尝试获取文件锁
            fcntl.flock(file, fcntl.LOCK_EX | fcntl.LOCK_NB)
            print("\n【商单】lock. Running script...")
            run_script()
    except BlockingIOError:
        print("【商单】Another instance is already running.")
        sys.exit(1)

# ========================== 其他方法 ==========================


def run_fill_condition():
    """ 填充条件信息 """
    work_list1 = OrderQuery.get_export_order_list('FAILED')
    for item in work_list1:
        if item['conditions']:
            req_param = json.loads(item['req'])
            condition = req_param.get("order_info")
            OrderWriter.update_export_order_condition(item['id'], json.dumps(condition))

    work_list2 = OrderQuery.get_export_order_list('END')
    for item2 in work_list2:
        if item2['conditions'] == '' or item2['conditions'] == '{}':
            req_param = json.loads(item['req'])
            condition = req_param.get("order_info")
            OrderWriter.update_export_order_condition(item2['id'], json.dumps(condition))


def rerun(args):
    """ 重新导出 """
    export_id = args.id
    status = args.status if args.status else 'END'

    work_list = OrderQuery.get_export_order_list(status, export_id)
    for item in work_list:
        if item['conditions'] == '':
            req_param = json.loads(item['req'])
            condition = req_param.get("order_info")
            OrderWriter.update_export_order_condition(item['id'], condition)
        run_single_order(item)


def requality_check(args):
    """ 质量检测 """
    export_id = args.id
    status = args.status if args.status else 'END'
    work_list = OrderQuery.get_export_order_list(status, export_id)
    for obj in work_list:
        with Context(obj["id"], obj["req"]) as ctx:
            steps = [
                task_step.StepInitOrderDB(),
                task_step.StepQualityCheck(),
                task_step.StepExportDeliveryData()
            ]
            chain_executor = ChainExecutor(steps)
            chain_executor.execute(ctx)


def reexport(args):
    """ 重新导出 """
    export_id = args.id
    status = args.status if args.status else 'END'
    work_list = OrderQuery.get_export_order_list(status, export_id)
    for obj in work_list:
        with Context(obj["id"], obj["req"]) as ctx:
            steps = [
                task_step.StepInitOrderDB(),
                task_step.StepExportDeliveryData()
            ]
            chain_executor = ChainExecutor(steps)
            chain_executor.execute(ctx)


def rezip_upload(args):
    """ 重新压缩上传 """
    export_id = args.id
    status = args.status if args.status else 'END'
    work_list = OrderQuery.get_export_order_list(status, export_id)
    for obj in work_list:
        with Context(obj["id"], obj["req"]) as ctx:
            db_row = OrderQuery.get_sdconf(export_id)
            ctx.set_delivery_conf(json.loads(db_row['remark']))
            steps = [
                task_step.StepZipDeliveryData(),
                task_step.StepUploadDeliveryData()
            ]
            chain_executor = ChainExecutor(steps)
            chain_executor.execute(ctx)


def get_db_path(args):
    """ 获取成果库连接信息 """
    export_id = args.id
    db_row = OrderQuery.get_sdconf(export_id)
    db_con = json.loads(db_row['remark'])
    db_conf = {
        "host": db_con.get('host'),
        "database": db_con.get('db'),
        "port": db_con.get('port'),
        "user": db_con.get('user'),
        "password": db_con.get('passwd'),
    }
    print("========== Postgre Sql ===========")
    print("【PG_CONF】{}".format(db_conf))
    print("【PG_URL】psql -h {} -U{} -W{} -p{} -d{}".format(
        db_conf['host'],
        db_conf['user'],
        db_conf['password'],
        db_conf['port'],
        db_conf['database']
    ))
