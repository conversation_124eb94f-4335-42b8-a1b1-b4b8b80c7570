# coding=utf-8
"""
商单自助交付步骤管理，包括但不限于创建成果库、筛选入库、数据质检、数据清洗、导出等
"""

import ftplib
import json
import os
import subprocess
import requests

from src.business_order.base import Step, Context
from src.business_order.bud import BudGis
from src.business_order.blu import AoiGis
from src.business_order.water import Blc<PERSON>aterGis
from src.business_order.green import BlcGreenGis
from src.business_order.bad import BadGis
from src.business_order.brw import BrwGis
from src.business_order import enums, helper
from src.business_order.query import OrderQuery, OrderWriter
from src.tools import conf_tools, pgsql
from src.tools import deoss


class StepInitOrderDB(Step):
    """ 建表步骤，创建成果库 """

    def __init__(self, is_truncate=True):
        """ 初始化函数，用于初始化类成员变量。 """
        super().__init__(enums.StepName.ReadyInitDeliveryDB)
        self.is_truncate = is_truncate

    def _create_delivery_db(self, context: Context):
        """ 创建交付成果库 """
        bconf = conf_tools.get_config("delivery")
        delivery_conf = enums.DeliveryDBConfig(**bconf)
        database_name = f"business_order_{context.export_id}"
        data = {
            "action": delivery_conf.create_action,
            "domain": delivery_conf.domain,
            "name": database_name,
            "keepdays": delivery_conf.keep_days,
            "produce_line": "sd"
        }
        response = requests.post(delivery_conf.url, json=data, timeout=300)
        response.raise_for_status()
        json_data = response.json()
        status = json_data.get('status')
        message = json_data.get('message')
        data = json_data.get('data')

        return (status, message, data)

    def _handle_impl(self, context: Context):
        """ 初始化商单信息和上下文信息、商单库连接等 """
        OrderWriter.update_export_order(context.export_id, enums.CommonExportStatus.PROGRESSING, '', '')

        # 读取交付库配置
        db_row = OrderQuery.get_sdconf(context.export_id)

        # 不存在交付库去创建
        if not db_row:
            print("创建商单成果库")
            _status, _msg, _dbconf = self._create_delivery_db(context)
            if _status == 0:
                context.set_delivery_conf(_dbconf)
                self.remark = json.dumps(_dbconf)
            else:
                raise Exception("创建商单成果库失败:{}".format(_msg))
            return

        # 存在交付库，直接读取历史配置，存在过期情况，需要支持重新创建一次
        try:
            context.set_delivery_conf(json.loads(db_row['remark']))
            if self.is_truncate:
                with pgsql.get_connection(context.delivery_conf) as conn:
                    OrderWriter.drop_table(conn)
        except Exception as e:
            _status, _msg, _dbconf = self._create_delivery_db(context)  # 在创建一次，存在过期的情况
            if _status == 0:
                context.set_delivery_conf(_dbconf)
                self.remark = json.dumps(_dbconf)
            else:
                raise Exception("创建二次商单成果库失败:{}".format(_msg))


class StepGenTable(Step):
    """ 创建成果表 """

    def __init__(self):
        """ 初始化函数，用于初始化类成员变量。 """
        super().__init__(enums.StepName.ReadyCreateTable)

    def _handle_impl(self, context: Context):
        """ 处理实现方法，根据条件创建对应的数据表。"""
        if enums.ExportCondKey.BUD.value in context.order.cond:
            BudGis().create_table(context)
        if enums.ExportCondKey.AOI.value in context.order.cond:
            AoiGis().create_table(context)
        if enums.ExportCondKey.BRW.value in context.order.cond:
            BrwGis().create_table(context)
        if enums.ExportCondKey.BLC_WATER.value in context.order.cond:
            BlcWaterGis().create_table(context)
        if enums.ExportCondKey.BLC_GREEN.value in context.order.cond:
            BlcGreenGis().create_table(context)
        if enums.ExportCondKey.BAD.value in context.order.cond:
            BadGis().create_table(context)


class StepFilterDataToTable(Step):
    """ 筛选数据入库入口 """

    def __init__(self):
        """ 初始化函数，用于初始化类成员变量。 """
        super().__init__(enums.StepName.ReadyFilterDataToDB)

    def _handle_impl(self, context: Context):
        """ 实现下数据筛选入库 """
        if enums.ExportCondKey.BUD.value in context.order.cond:
            BudGis().filter_data(context)
        if enums.ExportCondKey.AOI.value in context.order.cond:
            AoiGis().filter_data(context)
        if enums.ExportCondKey.BLC_GREEN.value in context.order.cond:
            BlcGreenGis().filter_data(context)
        if enums.ExportCondKey.BLC_WATER.value in context.order.cond:
            BlcWaterGis().filter_data(context)
        if enums.ExportCondKey.BRW.value in context.order.cond:
            BrwGis().filter_data(context)
        if enums.ExportCondKey.BAD.value in context.order.cond:
            BadGis().filter_data(context)

        # 清理转坐标文件
        helper.clear_order_tmp_file(context.export_id)


class StepQualityDeal(Step):
    """ 数据质量处理 """

    def __init__(self):
        """ 初始化函数，用于初始化类成员变量。 """
        super().__init__(enums.StepName.ProcessingDataQualityDeal)

    def _handle_impl(self, context: Context):
        """ 批处理数据，去除重复点，重复 geom 等 """
        if enums.ExportCondKey.BUD.value in context.order.cond:
            BudGis().quality_deal(context)
        if enums.ExportCondKey.AOI.value in context.order.cond:
            AoiGis().quality_deal(context)
        if enums.ExportCondKey.BLC_GREEN.value in context.order.cond:
            BlcGreenGis().quality_deal(context)
        if enums.ExportCondKey.BLC_WATER.value in context.order.cond:
            BlcWaterGis().quality_deal(context)
        if enums.ExportCondKey.BRW.value in context.order.cond:
            BrwGis().quality_deal(context)
        if enums.ExportCondKey.BAD.value in context.order.cond:
            BadGis().quality_deal(context)


class StepQualityCheck(Step):
    """ 数据质量检测，输出质检报告 """

    def __init__(self):
        """ 初始化函数，用于初始化类成员变量。 """
        super().__init__(enums.StepName.ProcessingDataQualityDeal)

    def _handle_impl(self, context: Context):
        """ 数据质量检测 """
        if enums.ExportCondKey.BUD.value in context.order.cond:
            BudGis().quality_check(context)
        if enums.ExportCondKey.AOI.value in context.order.cond:
            AoiGis().quality_check(context)
        if enums.ExportCondKey.BLC_GREEN.value in context.order.cond:
            BlcGreenGis().quality_check(context)
        if enums.ExportCondKey.BLC_WATER.value in context.order.cond:
            BlcWaterGis().quality_check(context)
        if enums.ExportCondKey.BRW.value in context.order.cond:
            BrwGis().quality_check(context)
        if enums.ExportCondKey.BAD.value in context.order.cond:
            BadGis().quality_check(context)


class StepExportDeliveryData(Step):
    """ 导出交付数据到文件 """

    def __init__(self):
        """ 初始化函数，用于初始化类成员变量。 """
        super().__init__(enums.StepName.ProcessingDataExport)

    def _handle_impl(self, context: Context):
        """
        处理导出任务，将数据导出到指定目录中。
        该方法会根据导出格式、编码和配置信息生成相应的数据并导出到文件系统中。
        """
        if enums.ExportCondKey.BUD.value in context.order.cond:
            BudGis().export_data(context)
        if enums.ExportCondKey.AOI.value in context.order.cond:
            AoiGis().export_data(context)
        if enums.ExportCondKey.BLC_WATER.value in context.order.cond:
            BlcWaterGis().export_data(context)
        if enums.ExportCondKey.BLC_GREEN.value in context.order.cond:
            BlcGreenGis().export_data(context)
        if enums.ExportCondKey.BRW.value in context.order.cond:
            BrwGis().export_data(context)
        if enums.ExportCondKey.BAD.value in context.order.cond:
            BadGis().export_data(context)

        # 导出下商单信息
        context.export_order_info()


def upload_to_ftp(ftp_conf: enums.DeliveryFTPConfig, local_file_path: str, remote_dir: str):
    """ 
    上传文件到 FTP 

    examle:
        ftp_conf = DeliveryFTPConfig(**bconf['delivery_ftp'])
        upload_to_ftp(ftp_conf, zip_name, ftp_conf.upload_dir)
        res_path = f'{ftp_conf.upload_dir}/{zip_name}' #FTP路径
    """

    # 连接到 FTP 服务器
    ftp = ftplib.FTP()
    ftp.connect(ftp_conf.host, ftp_conf.port)
    ftp.login(ftp_conf.user, ftp_conf.pwd)

    # 切换到目标远程目录
    try:
        ftp.cwd(remote_dir)
    except ftplib.error_perm as e:
        if '550' in str(e):
            try:
                ftp.mkd(remote_dir)
                ftp.cwd(remote_dir)
            except ftplib.error_perm as mkd_e:
                raise Exception(f"【FTP】Failed to create directory '{remote_dir}': {mkd_e}")

    with open(local_file_path, 'rb') as file:
        ftp.storbinary(f'STOR {os.path.basename(local_file_path)}', file)
        print(f"Uploaded {local_file_path} to {remote_dir}")

    # 关闭 FTP 连接
    ftp.quit()
    print("FTP connection closed.")


def upload_to_chenxi(up_conf: enums.DeliveryOtherConfig, local_file_path: str):
    """上传到 chenxi"""
    curl_command = [
        "curl",
        "-X", "POST",
        "-F", f"file=@{local_file_path}",
        up_conf.upload_url_prod,
    ]
    try:

        result = subprocess.run(curl_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        if result.returncode == 0:
            res = result.stdout.strip()
            if len(res) == 32:
                print("Upload Success:", res)
                return res
            else:
                raise Exception(f"Upload Failed: {result.stdout}")
        else:
            raise Exception(f"Upload Failed: {result.stderr}")
    except Exception as e:
        raise e


def upload_deoss(up_conf: enums.DeliveryOtherConfig, local_file_path: str):
    """上传到 deoss"""
    _deoss = deoss.DeossTool(False)
    return _deoss.upload(local_file_path, '', 'application/zip')


class StepZipDeliveryData(Step):
    """ 压缩文件为 Zip """

    def __init__(self):
        """初始化"""
        super().__init__(enums.StepName.ProcessingDataZip)
        self.original_directory = None

    def _handle_impl(self, context: Context):
        """ 压缩 """
        self.original_directory = os.getcwd()

        zip_dir = f'{context.export_root_dir}/{context.export_id}'
        file_name = context.order.output_name
        zip_name = f"{file_name}.zip"

        os.chdir(zip_dir)

        # 压缩
        cmd1 = [
            "zip", "-r", f"{zip_name}", f"{file_name}"
        ]
        helper.run_cmd(cmd1)

    def rollback(self, context):
        """ 工作目录切换 """
        if self.original_directory is not None:
            os.chdir(self.original_directory)


class StepUploadDeliveryData(Step):
    """ 上传到 FTP """

    def __init__(self):
        """初始化"""
        super().__init__(enums.StepName.ProcessingDataUpload)
        self.original_directory = None

    def _handle_impl(self, context: Context):
        """ 压缩和上传 """

        bconf = conf_tools.get_config("other")

        # 获取本地文件路径
        local_zip_file = f'{context.export_root_dir}/{context.export_id}/{context.order.output_name}.zip'

        # 上传到 chenxi
        # up_conf = enums.DeliveryOtherConfig(**bconf)
        up_conf = enums.DeliveryOtherConfig(
            upload_url_dev=bconf['upload_url_dev'],
            upload_url_prod=bconf['upload_url_prod'],
            download_url_dev=bconf['download_url_dev'],
            download_url_prod=bconf['download_url_prod'],
        )
        # res_path = upload_to_chenxi(up_conf, local_zip_file)
        res_path = upload_deoss(up_conf, local_zip_file)
        print("res_path", res_path)

        # 更新商单状态
        self.remark = res_path
        OrderWriter.update_export_order(context.export_id, enums.CommonExportStatus.END, '', res_path)

        # 删除压缩包
        cmd3 = ["rm", f"{local_zip_file}"]
        helper.run_cmd(cmd3)

        # 发送如流通知
        msg = f"【商单平台】导出结果通知：{context.export_id}. {context.order.project_name} 导出完毕，下载地址：{res_path}"
        helper.NoticeHelper.send_notice_msg(msg)
