""" PDD商单上下文及开关 """
import os
from dataclasses import dataclass
from datetime import datetime
from src.tools import conf_tools


@dataclass
class BudSwitch:
    """ 创建表结构相关开关 """
    CreateBud: bool = False
    FilterBud: bool = False
    ProcessRepatedPoints: bool = False
    ProcessBudOverlap: bool = False
    ProcessBudAttr: bool = False
    ProcessSafeEncrypt: bool = False
    ExportRelease: bool = False
    ExportSafe: bool = False


@dataclass
class AoiSwitch:
    """ 创建表结构相关开关 """
    CreateAoi: bool = False
    CreatePoi: bool = False
    CreateMesh: bool = False
    FilterAoi: bool = False
    FilterPoi: bool = False
    FilterMesh: bool = False
    ProcessRepeatedPoints: bool = False
    ProcessOverlap: bool = False
    ProcessAoiAttr: bool = False
    ProcessComplGangao: bool = False
    ProcessPh: bool = False
    ProcessSafeEncrypt: bool = False
    ExportRelease: bool = False
    ExportSafe: bool = False


@dataclass
class WaterSwitch:
    """ 水系相关开关 """
    CreateWater: bool = False
    FilterWater: bool = False
    ProcessRepeatedPoints: bool = False
    ProcessOverlap: bool = False
    ProcessComplSea: bool = False
    ProcessPh: bool = False
    ProcessSafeEncrypt: bool = False
    ExportRelease: bool = False
    ExportSafe: bool = False


@dataclass
class GreenSwitch:
    """ 绿化相关开关 """
    CreateGreen: bool = False
    FilterGreen: bool = False
    ProcessRepeatedPoints: bool = False
    ProcessOverlap: bool = False
    ProcessPh: bool = False
    ProcessSafeEncrypt: bool = False
    ExportRelease: bool = False
    ExportSafe: bool = False


@dataclass
class StaticSwitch:
    """ 统计相关开关 """
    BudStatic: bool = False
    AoiStatic: bool = False
    WaterStatic: bool = False
    GreenStatic: bool = False
    DeliveryStatistic: bool = False


@dataclass
class QingchaSwitch:
    """ 清查相关开关 """
    Xitiao: bool = False
    AllFeidi: bool = False


class PddContext:
    """ 上下文 """
    SD_MESH = 'sd_mesh_conf'
    SD_POI = 'sd_poi'
    SD_BUD = 'sd_pdd_bud_face'
    SD_AOI = 'sd_pdd_blu_face'
    SD_WATER = 'sd_pdd_blc_water'
    SD_GREEN = 'sd_pdd_blc_green'
    SD_VIL = 'sd_pdd_bad_village'
    SD_TOWN = 'sd_pdd_bad_town'
    SOURCE_VIL = 'bad_admin_vil_gcj'
    SOURCE_MESH = 'mesh_conf'
    SOURCE_SEA = 'pdd_water_sea_supple'
    SOURCE_AOI_GANGAO = 'pdd_blu_face_xianggangaomen'

    DELIVER_PROVINCES = [
        "anhui", "aomen", "beijing", "fujian", "gansu", "guangdong", "guangxi", "guizhou",
        "hainan", "hebei", "henan", "heilongjiang", "hubei", "hunan", "jilin", "jiangsu",
        "jiangxi", "liaoning", "neimenggu", "ningxia", "qinghai", "shandong", "shanxi", "shaanxi",
        "shanghai", "sichuan", "tianjin", "xizang", "xianggang", "xinjiang", "yunnan", "zhejiang",
        "chongqing"
    ]

    ProSets = {
        'aomen': '澳门', 'jilin': '吉林省', 'gansu': '甘肃省', 'jiangxi': '江西省', 'shandong': '山东省', 
        'shaanxi': '陕西省', 'liaoning': '辽宁省', 'hainan': '海南省', 'sichuan': '四川省', 
        'guangxi': '广西壮族自治区', 'shanxi': '山西省', 'shanghai': '上海市', 'zhejiang': '浙江省', 
        'anhui': '安徽省', 'yunnan': '云南省', 'guangdong': '广东省', 'hubei': '湖北省', 'beijing': '北京市', 
        'hebei': '河北省', 'xinjiang': '新疆维吾尔自治区', 'heilongjiang': '黑龙江省', 'xizang': '西藏自治区', 
        'neimenggu': '内蒙古自治区', 'ningxia': '宁夏回族自治区', 'tianjin': '天津市', 'xianggang': '香港', 
        'qinghai': '青海省', 'henan': '河南省', 'guizhou': '贵州省', 'hunan': '湖南省', 'fujian': '福建省', 
        'jiangsu': '江苏省', 'chongqing': '重庆市'
    }

    HAD_ZILIAO_COUNTY = None

    BUD_DIR = "background/00-building"
    AOI_DIR = "background/01-landutil"
    BLC_DIR = "background/02-landcover"
    BRW_DIR = "background/03-railway"
    COUNTY_DIR = "background/04-divison/county"
    TOWN_DIR = "background/04-divison/town"

    RELEASE_FMT = 'MIDMIF'
    RELEASE_ENC = 'UTF-8'
    SAFE_FMT = 'TAB'
    SAFE_ENC = 'UTF-8'
    VER_FMT = 'SHP'
    VER_ENC = 'UTF-8'

    def __init__(self, sd_conf, project_name):
        """ 初始化上下文对象 """
        self.sd_conf = sd_conf
        self.bud_switch = BudSwitch()
        self.aoi_switch = AoiSwitch()
        self.water_switch = WaterSwitch()
        self.green_switch = GreenSwitch()
        self.stat_switch = StaticSwitch()
        self.qingcha_switch = QingchaSwitch()
        self.project_name = project_name
        self.appoint = None
        self.safe_paths = {}
        self.set_ziliao()

    def __enter__(self):
        """ 进入上下文 """
        order_conf = conf_tools.get_pg_conf("order")
        if not order_conf.keys().__contains__('host'):
            raise ("数据库信息没有 order 配置")

        master_conf = conf_tools.get_pg_conf("master")
        if not master_conf.keys().__contains__('host'):
            raise ("数据库信息没有 master 配置")

        poi_conf = conf_tools.get_pg_conf("poi")
        if not poi_conf.keys().__contains__('host'):
            raise ("数据库信息没有 poi_online 配置")

        self.order_conf = order_conf
        self.master_conf = master_conf
        self.poi_conf = poi_conf
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """ 退出上下文 """
        pass

    def set_ziliao(self):
        """ 初始化资料清单 """
        ziliao_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "..",
                                      "..", "resources", "division_ziliao.txt"))
        with open(ziliao_path, 'r', encoding='utf-8') as file:
            first_line = file.readline().strip()
        self.HAD_ZILIAO_COUNTY = first_line.split(',')

    def dest_temp_copy(self):
        """ 获取临时拷贝目录路径 """
        return os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "data")),
                            'temp_copy', self.project_name)

    def dest_process(self):
        """ 获取处理结果存储目录路径 """
        return os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "data")),
                            'upload', f"{self.project_name}_process" + datetime.now().strftime("%Y%m%d"))

    def dest_release(self):
        """ 获取发布结果存储目录路径 """
        return os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "data")),
                            f"{self.project_name}_release" + datetime.now().strftime("%Y%m%d"))

    def set_release_where(self, where, where_params):
        """ 设置发布条件和参数 """
        self.release_where = where
        self.release_where_params = where_params

    def set_process_where(self, where, where_params, format='SHP'):
        """ 设置处理条件和参数 """
        self.process_where = where
        self.process_where_params = where_params
        self.process_format = format

    def set_appoint(self, appoint: list):
        """ 设置指定区域 """
        self.appoint = appoint

    def set_safe_encrypt_path(self, tp, path):
        """ 设置加密文件保存路径 """
        self.safe_paths[tp] = path

    def get_safe_encrypt_path(self, tp):
        """ 获取加密文件保存路径 """
        return self.safe_paths.get(tp, None)
