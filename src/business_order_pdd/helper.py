""" PDD 助手方法 """
import csv
import sys
from pypinyin import lazy_pinyin


def get_pinyin(text):
    """ 获取拼音 """
    pinyin_result = lazy_pinyin(text)
    capitalized_pinyin = ["".join(word).capitalize() for word in pinyin_result]
    return " ".join(capitalized_pinyin)


def get_data_from_txt(file_path, delimiter='\t', fieldnames=None, encode='utf-8') -> list:
    """ 读取 txt 文件中的所有数据 """
    csv.field_size_limit(sys.maxsize)
    try:
        with open(file_path, mode="r", newline='', encoding=encode) as file:
            reader = csv.DictReader(file, delimiter=delimiter, fieldnames=fieldnames)
            res = []
            for row in reader:
                res.append(row)
            return res
    except Exception as e:
        raise e
