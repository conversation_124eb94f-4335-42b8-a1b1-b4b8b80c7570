""" PDD 接口 """
from abc import ABC
from abc import abstractmethod
from src.business_order_pdd import base
from src.business_order_pdd.internal import AoiLogic, BudLogic, GreenLogic, WaterLogic, \
    update_geom_by_safe, update_pinyin
from src.business_order_pdd.statistic import StatisticLogic


class PDDInterface(ABC):
    """ PDD提供对外接口 """
    @abstractmethod
    def create_table(self, ctx: base.PddContext):
        """创建表"""
        pass

    @abstractmethod
    def filter_data(self, ctx: base.PddContext):
        """过滤数据"""
        pass

    @abstractmethod
    def process_data(self, ctx: base.PddContext):
        """处理数据"""
        pass

    @abstractmethod
    def export_data(self, ctx: base.PddContext):
        """导出数据"""
        pass


class PddActualService(PDDInterface):
    """ 开平实现逻辑 """

    def create_table(self, ctx: base.PddContext):
        """创建表"""
        if ctx.bud_switch.CreateBud:
            BudLogic.create_bud(ctx)
        if ctx.aoi_switch.CreateAoi:
            AoiLogic.create_aoi(ctx)
        if ctx.aoi_switch.CreatePoi:
            AoiLogic.create_poi(ctx)
        if ctx.aoi_switch.CreateMesh:
            AoiLogic.create_mesh(ctx)
        if ctx.water_switch.CreateWater:
            WaterLogic.create_water(ctx)
        if ctx.green_switch.CreateGreen:
            GreenLogic.create_green(ctx)

    def filter_data(self, ctx: base.PddContext):
        """过滤数据入库"""
        if ctx.bud_switch.FilterBud:
            BudLogic.filter_data(ctx)
        if ctx.aoi_switch.FilterMesh:
            AoiLogic.filter_mesh(ctx)
        if ctx.aoi_switch.FilterAoi:
            AoiLogic.filter_aoi(ctx)
        if ctx.aoi_switch.FilterPoi:
            pass
        if ctx.water_switch.FilterWater:
            WaterLogic.filter_water(ctx)
        if ctx.green_switch.FilterGreen:
            GreenLogic.filter_green(ctx)

    def process_data(self, ctx: base.PddContext):
        """处理数据"""
        if ctx.bud_switch.ProcessRepatedPoints:
            BudLogic.process_bud_repeated_points(ctx)
        if ctx.bud_switch.ProcessBudOverlap:
            BudLogic.process_bud_bud_overlap(ctx)
        if ctx.bud_switch.ProcessBudAttr:
            BudLogic.process_bud_attr(ctx)
        if ctx.bud_switch.ProcessSafeEncrypt:
            update_geom_by_safe(ctx.sd_conf, ctx.SD_BUD, ctx.get_safe_encrypt_path(ctx.SD_BUD))
        if ctx.aoi_switch.ProcessRepeatedPoints:
            AoiLogic.process_aoi_repeated_points(ctx)
        if ctx.aoi_switch.ProcessOverlap:
            AoiLogic.process_aoi_aoi_overlap(ctx)
        if ctx.aoi_switch.ProcessAoiAttr:
            AoiLogic.process_aoi_attr(ctx)
        if ctx.aoi_switch.ProcessComplGangao:
            AoiLogic.comple_gangao(ctx)
        if ctx.aoi_switch.ProcessPh:
            update_pinyin(ctx.sd_conf, ctx.SD_AOI, 'face_id', 'name_ch', 'name_ph')
        if ctx.aoi_switch.ProcessSafeEncrypt:
            update_geom_by_safe(ctx.sd_conf, ctx.SD_AOI, ctx.get_safe_encrypt_path(ctx.SD_AOI))
        if ctx.water_switch.ProcessRepeatedPoints:
            WaterLogic.process_water_repeated_points(ctx)
        if ctx.water_switch.ProcessOverlap:
            WaterLogic.process_water_overlap(ctx)
        if ctx.water_switch.ProcessComplSea:
            WaterLogic.comple_sea(ctx)
        if ctx.water_switch.ProcessPh:
            update_pinyin(ctx.sd_conf, ctx.SD_WATER, 'face_id', 'name_ch', 'name_ph')
        if ctx.water_switch.ProcessSafeEncrypt:
            update_geom_by_safe(ctx.sd_conf, ctx.SD_WATER, ctx.get_safe_encrypt_path(ctx.SD_WATER))
        if ctx.green_switch.ProcessRepeatedPoints:
            GreenLogic.process_green_repeated_points(ctx)
        if ctx.green_switch.ProcessOverlap:
            GreenLogic.process_green_overlap(ctx)
        if ctx.green_switch.ProcessPh:
            update_pinyin(ctx.sd_conf, ctx.SD_GREEN, 'face_id', 'name_ch', 'name_ph')
        if ctx.green_switch.ProcessSafeEncrypt:
            update_geom_by_safe(ctx.sd_conf, ctx.SD_GREEN, ctx.get_safe_encrypt_path(ctx.SD_GREEN))

    def export_data(self, ctx: base.PddContext):
        """导出数据"""
        if ctx.bud_switch.ExportRelease:
            BudLogic.export_release(ctx)
        if ctx.bud_switch.ExportSafe:
            BudLogic.export_safe(ctx)
        if ctx.aoi_switch.ExportRelease:
            AoiLogic.export_release(ctx)
        if ctx.aoi_switch.ExportSafe:
            AoiLogic.export_safe(ctx)
        if ctx.water_switch.ExportRelease:
            WaterLogic.export_release(ctx)
        if ctx.water_switch.ExportSafe:
            WaterLogic.export_safe(ctx)
        if ctx.green_switch.ExportRelease:
            GreenLogic.export_release(ctx)
        if ctx.green_switch.ExportSafe:
            GreenLogic.export_safe(ctx)

    def statistic_data(self, ctx: base.PddContext):
        """统计数据"""
        if ctx.stat_switch.DeliveryStatistic:
            StatisticLogic.delivery_statistic(ctx)
