""" PDD交付表结构 """
class PddSchema:
    """PDD交付表结构"""
    Bud = f"""
create table sd_pdd_bud_face(
    face_id         character varying(128)      not null primary key,
    struct_id       character varying(512)      not null default ''::character varying,
    poi_id          character varying(128)      not null default ''::character varying,
    poi_bid         character varying(128)      not null default ''::character varying,
    aoi_id          character varying(128)      not null default ''::character varying,
    roof_style      smallint                    default 0,
    wall_material   smallint                    default 1,
    mesh_id         character varying(6)        default ''::character varying,
    area            double precision            default 0,
    perimeter       double precision            default 0,
    geom            geometry(Geometry,4326),
    src             character varying(64),
    create_time     timestamp without time zone default '1970-01-01 00:00:00'::timestamp without time zone
);
CREATE INDEX idx_pdd_bud_mesh ON sd_pdd_bud_face(mesh_id);
CREATE INDEX idx_pdd_bud_geom ON sd_pdd_bud_face USING gist(geom);
    """

    Aoi = f"""
create table sd_pdd_blu_face(
    face_id         character varying(128)      not null primary key,
    poi_id          character varying(128)      not null default ''::character varying,
    poi_bid         character varying(128)      not null default ''::character varying,
    kind            character varying(64)       not null default ''::character varying,
    std_tag         character varying(64)       not null default ''::character varying,
    area            double precision            default 0,
    perimeter       double precision            default 0,
    aoi_level       integer                     default 0,
    name_ch         character varying(120)      not null default ''::character varying,
    name_ph         character varying(1000)     not null default ''::character varying,
    name_en         character varying(1000)     not null default ''::character varying,
    city_name       character varying(128)      not null default ''::character varying,
    admin_id        character varying(6)        not null default ''::character varying,
    mesh_id         character varying(6)        default ''::character varying,
    geom            geometry(Geometry,4326)
);
CREATE INDEX idx_pddblu_mesh ON sd_pdd_blu_face(mesh_id);
CREATE INDEX idx_pddblu_geom ON sd_pdd_blu_face USING gist(geom);
    """

    Water = f"""
create table sd_pdd_blc_water(
    face_id   character varying(128) not null PRIMARY KEY,
    name_ch   character varying(128)              ,
    name_ph   character varying(1000)             ,
    kind      character varying(6)                ,
    admin_id  character varying(6)                ,
    area      double precision not null default 0 ,
    perimeter double precision not null default 0 ,
    form      integer not null default 0,
    dis_class integer not null default 0,
    mesh_id   character varying(128)              ,
    geom      geometry
);
CREATE INDEX idx_pdd_water_mesh ON sd_pdd_blc_water(mesh_id);
CREATE INDEX idx_pdd_water_geom ON sd_pdd_blc_water USING gist(geom);
    """

    Green = f"""
create table sd_pdd_blc_green(
    face_id   character varying(128) not null PRIMARY KEY,
    name_ch   character varying(128)              ,
    name_ph   character varying(1000)             ,
    kind      character varying(6)                ,
    admin_id  character varying(6)                ,
    area      double precision not null default 0 ,
    perimeter double precision not null default 0 ,
    form      integer not null default 0,
    dis_class integer not null default 0,
    mesh_id   character varying(128)              ,
    geom      geometry
);
CREATE INDEX idx_pdd_green_mesh ON sd_pdd_blc_green(mesh_id);
CREATE INDEX idx_pdd_green_geom ON sd_pdd_blc_green USING gist(geom);
    """

    Mesh = f"""
create table sd_mesh_conf(
    mesh_id     character varying(128)  not null default ''::character varying,
    province_ch character varying(128)  not null default ''::character varying,
    province_en character varying(128)  not null default ''::character varying,
    cityname    character varying(128)  not null default ''::character varying,
    cityname_ph character varying(128)  not null default ''::character varying,
    wkt         character varying(1000) not null default ''::character varying,
    geom        geometry(Geometry,4326)
);
    """

    Poi = f"""
create table sd_poi(
    bid            character varying(64)    not null PRIMARY KEY,
    mesh_id        character varying(6)     not null default ''::character varying,
    name           character varying(254)   not null default ''::character varying,
    address        character varying(1024)  not null default ''::character varying,
    kind           character varying(64)    not null default ''::character varying,
    tag            character varying(254)   not null default ''::character varying,
    std_tag        character varying(254)   not null default ''::character varying,
    status         integer                  not null default 1,
    relation_bid   character varying(64)    not null default ''::character varying,
    admin_code     character varying(10)    not null default ''::character varying,
    city           character varying(254)   not null default ''::character varying,
    click_pv       integer                  not null default 0,
    geometry       geometry(Geometry,4326)  
);
CREATE INDEX idx_sd_poi_bid ON sd_poi(bid);
CREATE INDEX idx_sd_poi_mesh ON sd_poi(mesh_id);
CREATE INDEX idx_sd_poi_geometry ON sd_poi USING gist(geometry);
    """
