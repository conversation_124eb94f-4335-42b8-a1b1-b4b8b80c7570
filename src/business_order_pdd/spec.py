""" PDD 一些数据结构 """
from dataclasses import dataclass, field
from typing import Optional
from enum import Enum


@dataclass
class ProcessCtx:
    """ 质检上下文 """
    Table: str
    PkKey: str
    PGConf: dict
    Where: str = ''
    Size: int = 20000
    Other: dict = field(default_factory=dict)


@dataclass
class QualityLogItem:
    """质检记录结构"""
    errno: str = ''
    msg: str = ''
    geom: Optional[str] = 'Polygon Empty'


class QualityErrNo(Enum):
    """ AOI 错误编码 """
    A0002 = 'aoi face_id 字段类型要求Varchar字符型'
    A0003 = 'aoi face_id 字段长度128'
    A0004 = 'aoi face_id 不能为空值'
    A0005 = 'aoi face_id 不能含有特殊符号'
    A0010 = 'aoi poi_id 字段类型要求Varchar字符型'
    A0011 = 'aoi poi_id 字段长度128'
    A0012 = 'aoi poi_id 不能为空值'
    A0013 = 'aoi poi_id 不能含有特殊符号'
    A0016 = 'aoi poi_id 值要唯一，不能重复'
    A0019 = 'aoi area 字段类型要求Double'
    A0022 = 'aoi area 景区等垂类需<50亿平方米'  # 超出输出报告
    A0023 = 'aoi area 常规垂类面积需<1000W平米'  # 超出输出报告
    A0024 = 'aoi area 整体面积需>100平'  # 最小值需要大于100平
    A0026 = 'aoi perimter 字段类型要求Double'
    A0029 = 'aoi poi_name 不能为空,关联主点POI缺失'  # 引用poi的name，存在关联主点POI缺失情况，质检报告输出
    A0031 = 'aoi name 字段类型要求Text'
    A0034 = 'aoi 不能存在自接触'
    A0038 = 'aoi 最小夹角20度'
    A0041 = 'aoi 不允许重复'
    A0042 = 'aoi 不能超出行政区划最外边界'
    A0051 = 'aoi 去除行政区划边界'
    A0052 = 'aoi 去除交通设施;桥'

    """ BUD 错误编码"""
    B0002 = 'bud face_id 字段类型要求Varchar字符型'
    B0003 = 'bud face_id 字段长度128'
    B0004 = 'bud face_id 不能为空值'
    B0005 = 'bud face_id 不能含有特殊符号'
    B0010 = 'bud struct_id 字段类型要求Varchar字符型'
    B0011 = 'bud struct_id 字段长度128'
    B0012 = 'bud struct_id 不能为空值'
    B0013 = 'bud struct_id 不能含有特殊符号'
    B0017 = 'bud name_ch 字段类型要求Varchar字符型'
    B0018 = 'bud name_ch 字段长度120'
    B0019 = 'bud name_ch 不能全部为空'
    B0021 = 'bud aoi_id 字段类型要求Varchar字符型'
    B0022 = 'bud aoi_id 字段长度128'
    B0023 = 'bud aoi_id 不能全部为空'
    B0024 = 'bud height 要求 float'
    B0027 = 'bud area 字段类型要求Double'
    B0030 = 'bud area 面积需>2平'  # 最小值需要大于2平
    B0031 = 'bud area 面积值与geo 实际值差异>100'  # 面积值与geo实际值差异>100平米质检报告报出
    B0032 = 'bud perimter 要求 double'
    B0036 = 'bud perimter 周长与geo 实际值差异>100'
    B0038 = 'bud 几何类型只能为polygon，不允许为multipolygon'
    B0039 = 'bud 不能存在自接触'
    B0043 = 'bud 最小夹角10度'
    B0050 = 'bud 不允许重叠'
    B0052 = 'bud 不允许超过行政区划边界'

    """ 绿地 """
    G0002 = 'green face_id 字段类型要求Varchar字符型'
    G0003 = 'green face_id 字段长度128'
    G0004 = 'green face_id 不能为空值'
    G0005 = 'green face_id 不能含有特殊符号'
    G0010 = 'green name_ch 字段类型要求Varchar字符型'
    G0011 = 'green name_ch 字段长度120'
    G0012 = 'green name_ch 不能全部为空'
    G0014 = 'green name_ph 字段类型要求Varchar字符型'
    G0015 = 'green name_ph 字段长度1000'
    G0016 = 'green name_ph 不能全部为空'
    G0018 = 'green kind 字段类型要求Varchar字符型'
    G0019 = 'green kind 字段长度6'
    G0020 = 'green kind 字段值只能为11、12、14、15、16、21、25'
    G0021 = 'green kind 不能为空值'
    G0023 = 'green admin_id 字段类型要求Varchar字符型'
    G0024 = 'green admin_id 字段长度6'
    G0025 = 'green admin_id 不能为空值'
    G0027 = 'green area 字段类型要求Double'
    G0030 = 'green area 面积需>10平'  # 最小值需要大于10平
    G0031 = 'green area 面积值与geo 实际值差异>100'  # 面积值与geo实际值差异>100平米质检报告报出
    G0035 = 'green perimter 要求 double'
    G0036 = 'green perimter 周长与geo 实际值差异>100'
    G0039 = 'green 不能存在自接触'
    G0043 = 'green 最小夹角10度'
    G0049 = 'green 不允许重叠'
    G0061 = 'green name_ch 为空但 name_ph 不为空'

    """ 水系 """
    W0002 = 'water face_id 字段类型要求Varchar字符型'
    W0003 = 'water face_id 字段长度128'
    W0004 = 'water face_id 不能为空值'
    W0005 = 'water face_id 不能含有特殊符号'
    W0010 = 'water name_ch 字段类型要求Varchar字符型'
    W0011 = 'water name_ch 字段长度120'
    W0012 = 'water name_ch 不能全部为空'
    W0014 = 'water name_ph 字段类型要求Varchar字符型'
    W0015 = 'water name_ph 字段长度1000'
    W0016 = 'water name_ph 不能全部为空'
    W0018 = 'water kind 字段类型要求Varchar字符型'
    W0019 = 'water kind 字段长度6'
    W0020 = 'water kind 字段值只能为1、2、3、4、5、6、31、32、33'
    W0021 = 'water kind 不能为空值'
    W0023 = 'water admin_id 字段类型要求Varchar字符型'
    W0024 = 'water admin_id 字段长度6'
    W0025 = 'water admin_id 不能为空值'
    W0027 = 'water area 字段类型要求Double'
    W0030 = 'water area 面积需>50平'
    W0031 = 'water area 面积值与geo 实际值差异>100'
    W0035 = 'water perimter 要求 double'
    W0036 = 'water perimter 周长与geo 实际值差异>100'
    W0039 = 'water 不能存在自接触'
    W0043 = 'water 最小夹角10度'
    W0049 = 'water 不允许重叠'
    W0061 = 'water name_ch 为空但 name_ph 不为空'

    """ 行政区划 """
    D0002 = 'division face_id 字段类型要求Varchar字符型'
    D0003 = 'division face_id 字段长度64'
    D0004 = 'division face_id 不能为空值'
    D0005 = 'division face_id 不能含有特殊符号'
    D0010 = 'division admin_id 字段类型要求Varchar字符型'
    D0011 = 'division admin_id 字段长度24'
    D0012 = 'division admin_id 不能为空'
    D0014 = 'division name_ch 字段类型要求Varchar字符型'
    D0015 = 'division name_ch 字段长度255'
    D0016 = 'division name_ch 不能为空'
    D0019 = 'division 不能存在自接触'
    D0021 = 'division 不能存在重复节点'
    D0023 = 'division 最小夹角10度'
    D0029 = 'division 不能有重复'
    D0030 = 'division 不能有重叠'
    D0031 = 'division 不能有空隙'
    D0041 = 'division 不能包含型'

    """ BRW """
    L0002 = 'brwlink face_id 字段类型要求Varchar字符型'
    L0003 = 'brwlink face_id 字段长度64'
    L0004 = 'brwlink face_id 不能为空值'
    L0005 = 'brwlink face_id 不能含有特殊符号'
    L0010 = 'brwlink s_nid 字段类型要求Varchar字符型'
    L0011 = 'brwlink s_nid 字段长度64'
    L0012 = 'brwlink s_nid 不能为空值'
    L0013 = 'brwlink s_nid 不能含有特殊符号'
    L0018 = 'brwlink e_nid 字段类型要求Varchar字符型'
    L0019 = 'brwlink e_nid 字段长度64'
    L0020 = 'brwlink e_nid 不能为空值'
    L0021 = 'brwlink e_nid 不能含有特殊符号'
    L0026 = 'brwlink kind 要求为 short'
    L0027 = 'brwlink kind 只能为 1、2、3、4、5'
    L0031 = 'brwlink form 要求为 short'
    L0032 = 'brwlink form 只能为 0、1、2'
    L0037 = 'brwlink name_ch 字段类型要求Varchar字符型'
    L0038 = 'brwlink name_ch 字段长度120'
    L0039 = 'brwlink name_ch 不能为全部为空'
    L0041 = 'brwlink name_ph 字段类型要求Varchar字符型'
    L0042 = 'brwlink name_ph 字段长度1000'
    L0043 = 'brwlink name_ph 不能为全部为空'
    L0045 = 'brwlink name_en 字段类型要求Varchar字符型'
    L0046 = 'brwlink name_en 字段长度1000'
    L0048 = 'brwlink z_level 字段类型要求Varchar字符型'
    L0049 = 'brwlink z_level 字段长度1000'
    L0060 = 'brwlink 最小夹角10度'

    N0002 = 'brwnode face_id 字段类型要求Varchar字符型'
    N0003 = 'brwnode face_id 字段长度64'
    N0004 = 'brwnode face_id 不能为空值'
    N0005 = 'brwnode face_id 不能含有特殊符号'
    N0026 = 'brwnode kind 要求为 short'
    N0027 = 'brwnode kind 只能为 1、2'
    N0031 = 'brwnode form 要求为 short'
    N0032 = 'brwnode form 只能为 0、1、2、3、4、5、6'

    def key(cls):
        """错误码"""
        return cls.key
