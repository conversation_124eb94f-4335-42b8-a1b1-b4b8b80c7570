""" PDD 交付统计 """

from tqdm import tqdm
from src.business_order_pdd import base
from src.business_order import helper
from src.tools import pgsql


def _get_mesh_ids(curs, table, where, where_params) -> list:
    """获取图幅ID列表"""
    curs.execute(f'select mesh_id from {table} where 1=1 {where}', where_params)
    res = curs.fetchall()

    return res


def _get_count_by_province(curs, table, province):
    """根据省份查询数量"""
    mesh_ids = _get_mesh_ids(curs, base.PddContext.SD_MESH, 'and province_en=%s', [province])
    curs.execute(f'select count(*) as co from {table} where 1=1 and mesh_id=any(%s)', [mesh_ids])
    row = curs.fetchone()
    if row:
        return row[0]
    return 0


def _get_count(curs, table, where='', where_params=None):
    """获取总数"""
    curs.execute(f'select count(*) as co from {table} where 1=1 {where}', where_params)
    row = curs.fetchone()
    if row:
        return row[0]

    return 0


def _bud_provicen_statistic_multi(province, sd_conf):
    """统计某个省的数据"""
    with pgsql.get_connection(sd_conf) as conn, conn.cursor() as curs:
        num = _get_count_by_province(curs, base.PddContext.SD_BUD, province)
        return ["建筑物", "bud_face", "建筑物面", base.PddContext.ProSets[province], num]


class StatisticLogic:
    """交付统计逻辑"""
    @staticmethod
    def delivery_statistic(ctx: base.PddContext):
        """ 交付统计 """
        res = []
        with pgsql.get_connection(ctx.sd_conf) as conn, conn.cursor() as curs:
            bud_segs = [{"province": pro, "sd_conf": ctx.sd_conf} for pro in ctx.DELIVER_PROVINCES]
            bud_res = helper.multi_run(bud_segs, _bud_provicen_statistic_multi, 16)
            for item in bud_res:
                res.append(item)

            for province in tqdm(ctx.DELIVER_PROVINCES, desc="按省统计 AOI"):
                num = _get_count_by_province(curs, ctx.SD_AOI, province)
                res.append(["aoi", "blu_face", "aoi面", ctx.ProSets[province], num])
            for province in tqdm(ctx.DELIVER_PROVINCES, desc="按省统计水系"):
                num = _get_count_by_province(curs, ctx.SD_WATER, province)
                res.append(["水系", "blc_face_shuixi", "水系面", ctx.ProSets[province], num])

            water_total_num = _get_count(curs, ctx.SD_WATER)
            res.append(["水系全量", "blc_face_shuixi", "水系面", '母库+海域', water_total_num])

            for province in tqdm(ctx.DELIVER_PROVINCES, desc="按省统计绿地"):
                num = _get_count_by_province(curs, ctx.SD_GREEN, province)
                res.append(["绿地", "blc_face_lvdi", "绿地面", ctx.ProSets[province], num])

            for province in tqdm(ctx.DELIVER_PROVINCES, desc="按省统计水系"):
                water_num = _get_count_by_province(curs, ctx.SD_WATER, province)
                green_num = _get_count_by_province(curs, ctx.SD_GREEN, province)
                res.append(["背景", "绿地+水系", "0", ctx.ProSets[province], water_num + green_num])

        with pgsql.get_connection(ctx.sd_conf) as conn, conn.cursor() as curs:
            for province in tqdm(ctx.DELIVER_PROVINCES, desc="统计行政区划区县"):
                num = _get_count(curs, 'bad_admin_town_pddv5', 'and province_py_name = %s', [province])
                res.append(["三级行政区划", "division_county", "0", ctx.ProSets[province], num])

            for province in tqdm(ctx.DELIVER_PROVINCES, desc="统计行政区划乡镇"):
                if province in ["xianggang", "aomen"]:
                    continue
                num = _get_count(curs, 'bad_admin_town_pddv5', 'and province_py_name = %s', [province])
                res.append(["四级行政区划", "division_town", "0", ctx.ProSets[province], num])

        last_res = [{'分类': x[0], '名称': x[1], '描述': x[2], '省份': x[3], '量级': x[4]} for x in res]
        exporter = helper.JExporter(None, 'D_CSV', 'UTF-8')
        exporter.to_csv_by_data(ctx.dest_process(), 'statistic', [], last_res)
