""" PDD table 方法"""
import os
from src.tools import pgsql


def to_psql(conf):
    """
    根据配置生成 psql
    """
    return f"psql -h {conf['host']} -U{conf['user']} -p{conf['port']} -d{conf['db']}"


def to_host(conf):
    """
    根据配置生成 host
    """
    return f"host={conf['host']} dbname={conf['db']} port={conf['port']} user={conf['user']} password={conf['pwd']}"


def create_table(pg_conf, table):
    """ 创建表 """
    with pgsql.get_connection(pg_conf) as conn:
        pgsql.execute(table)


def copy_table_to_file(pg_conf, dest, name, sql, where_params=None):
    """ 导出数据到文件 """
    if not os.path.exists(dest):
        os.makedirs(dest)

    file_path = os.path.join(dest, name + ".csv")
    if os.path.exists(file_path):
        os.remove(file_path)

    with pgsql.get_connection(pg_conf) as conn, conn.cursor() as curs:
        with open(file_path, "w") as f:
            final_query = curs.mogrify(sql, where_params).decode("utf-8")  # 安全拼接 SQL
            curs.copy_expert(f"COPY ({final_query}) TO STDOUT WITH CSV DELIMITER E'\t' HEADER", f)

    return file_path


def copy_data_to_table(pg_conf, source_path, sql):
    """ 导入数据到表 """

    psql_cmd = to_psql(pg_conf)

    cmd = f"""
    export PGPASSWORD={pg_conf['pwd']}
    {psql_cmd} -c "\\{sql} FROM '{source_path}' WITH (FORMAT csv, DELIMITER '\t', HEADER true);"
    """
    os.system(cmd)


def gen_segments(conn, tab, pk_key, where='', size=10000):
    """ 分段查询 """
    res = []

    # 第一个批次
    last_id = None
    first = pgsql.fetch_one(
        conn, f'select {pk_key} from {tab} where 1=1 {where} order by {pk_key} offset {size-1};')
    if first:
        last_id = first[0]
        res.append(('', last_id))
    else:
        last = pgsql.fetch_one(
            conn, f'select {pk_key} from {tab} where 1=1 {where} order by {pk_key} desc limit 1;')
        if last:
            res.append(('', last[0]))
            return res
        else:
            raise Exception('no data in table')

    # 后续批次
    while True:
        query = f"""
            select {pk_key} from {tab} where {pk_key} > %s {where} 
            order by {pk_key} limit 1 offset {size-1};
        """
        row = pgsql.fetch_one(conn, query, [last_id])
        if row:
            res.append((last_id, row[0]))
            last_id = row[0]
        else:
            break

    # 最后一批
    last = pgsql.fetch_one(
        conn, f'select {pk_key} from {tab} where 1=1 {where} order by {pk_key} desc limit 1;')
    if last[0] != last_id:
        res.append((last_id, last[0]))

    return res
