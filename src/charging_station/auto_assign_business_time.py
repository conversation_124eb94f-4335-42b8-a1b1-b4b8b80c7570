# !/usr/bin/env python3
"""
例行赋值营业时间
"""
import argparse
import random
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin as auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import TpRecord, Poi
from src.charging_station.formatters.business_time_formatter import format_business_time
from src.charging_station.tools.string_tool import clean_str
from src.tools import pipeline, tsv, notice_tool, pgsql

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    批处理记录
    """
    handled_business_time: str = ''

    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)

    can_process: bool = True
    reason: str = ''

    def __init__(self, tp, poi):
        self.tp = tp
        self.poi = poi


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
        ),
        common_poi_tp_min_name_iou=0,
    )
    ctx.records = [x for x in ctx.records if x.tp.business_hours != '']
    proceed()


@desc()
def fill_poi_business_time(ctx: Context, proceed):
    """
    填充 poi 的营业时间
    """
    sql = '''
        select business_time from cdz_history_20250303 where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for record in tqdm(ctx.records):
            row = stab.fetch_one(sql, (record.poi.bid,))
            if row is None:
                continue

            record.poi.business_time = row[0]

    proceed()


@desc()
def fill_handled_business_time(ctx: Context, proceed):
    """
    处理营业时间
    """
    for record in tqdm(ctx.records):
        handled_business_time, reason = format_business_time(
            business_hours=record.tp.business_hours,
            poi_business_time=record.poi.business_time,
        )

        if handled_business_time is None:
            record.can_process = False
            record.reason = reason
            continue

        record.handled_business_time = handled_business_time
        record.reason = reason

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    random.shuffle(ctx.records)

    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.poi.bid,
                x.tp.third_code,
                x.tp.third_name,
                x.tp.station_name,
                x.poi.name,
                clean_str(x.tp.business_hours),
                x.handled_business_time,
                x.poi.business_time,
                x.can_process,
                x.reason,
            ]
            for x in ctx.records
        ]
    )

    proceed()


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    如流通知结果
    """
    msg = f'''今日例行赋值营业时间 {len([x for x in ctx.records if x.can_process])} 条。'''
    print(msg)
    alert_to_infoflow(msg)
    proceed()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_tp_records,
        load_pois,
        retain_best_matched_tp,
        fill_poi_business_time,
        fill_handled_business_time,
        save_records,
    ]

    if args.mode == 'online':
        pipes.extend([
            send_records_to_infoflow,
        ])

    return pipeline.Pipeline(*pipes)


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['test', 'online'],
        default='test',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_assign_business_time"),
    )

    if args.mode == 'online':
        try:
            main_pipe(ctx)
        except Exception as e:
            alert_to_infoflow(f'例行赋值营业时间脚本异常！{e}')
    else:
        main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
