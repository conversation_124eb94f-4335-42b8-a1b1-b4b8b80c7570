# !/usr/bin/env python3
"""
例行赋值电话
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.charging_station import auto_repair_mixin as auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import Tp<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Competitor
from src.charging_station.online_prop import online_telephone
from src.charging_station.formatters.phone_fomatter import format_phone
from src.tools import pipeline, tsv, notice_tool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    批处理记录
    """
    handled_phone: str = ''
    phone_city: str = ''
    region_code: str = ''

    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)
    competitor: Competitor = field(init=False)

    can_process: bool = True
    reason: str = ''

    def __init__(self, tp, poi, competitor):
        self.tp = tp
        self.poi = poi
        self.competitor = competitor


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
            competitor=kwargs.get('competitor', None),
        ),
        mmatch_competitor=True,
        common_poi_tp_min_name_iou=0,
    )
    ctx.records = [x for x in ctx.records if x.tp.service_tel != '']
    proceed()


@desc()
def load_white_board_records(ctx: Context, proceed):
    """
    加载白板记录
    """
    auto_repair_mixin.load_white_board_records(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            poi=kwargs['poi'],
            tp=None,
            competitor=None,
        ),
    )
    proceed()


@desc()
def match_competitor_by_poi(ctx: Context, proceed):
    """
    根据 poi 匹配竞品
    """
    auto_repair_mixin.match_competitor_by_poi(ctx)
    proceed()


@desc()
def fill_handled_phone(ctx: Context, proceed):
    """
    处理电话号码
    """
    for record in tqdm(ctx.records):
        handled_phone, reason = format_phone(
            third_name=record.tp.third_name if record.tp is not None else '',
            service_tel=record.tp.service_tel if record.tp is not None else '',
            poi_name=record.poi.name,
            poi_phone=record.poi.phone,
            competitor_phone=record.competitor.phone if record.competitor is not None else '',
        )

        if handled_phone is None:
            record.can_process = False
            record.reason = reason
            continue

        record.handled_phone = handled_phone
        record.reason = reason

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    ctx.batch_records = [
        Poi(x.poi.bid, phone=x.handled_phone)
        for x in ctx.records if x.can_process
    ]

    online_telephone(ctx.batch_records)
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.poi.bid,
                x.tp.third_code if x.tp is not None else '',
                x.tp.third_name if x.tp is not None else '',
                x.tp.station_name if x.tp is not None else '',
                x.poi.name,
                x.competitor.name if x.competitor is not None else '',
                x.tp.service_tel if x.tp is not None else '',
                x.handled_phone,
                x.poi.phone,
                x.poi.city,
                x.phone_city,
                x.region_code,
                x.can_process,
                x.reason,
            ]
            for x in ctx.records
        ]
    )

    proceed()


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    如流通知结果
    """
    msg = f'''今日例行赋值电话 {len([x for x in ctx.records if x.can_process])} 条。'''
    print(msg)
    alert_to_infoflow(msg)
    proceed()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_tp_records,
        load_pois,
        retain_best_matched_tp,
        load_white_board_records,
        match_competitor_by_poi,
        fill_handled_phone,
        save_records,
    ]

    if args.mode == 'online':
        pipes.extend([
            execute_batch,
            send_records_to_infoflow,
        ])

    return pipeline.Pipeline(*pipes)


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['test', 'online'],
        default='test',
        required=False,
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_assign_phone'),
    )

    if args.mode == 'online':
        try:
            main_pipe(ctx)
        except Exception as e:
            alert_to_infoflow(f'例行赋值电话脚本异常！{e}')
    else:
        main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
