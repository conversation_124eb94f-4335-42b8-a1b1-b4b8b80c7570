# !/usr/bin/env python3
"""
例行赋值空间属性
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import Poi, SPACING_VALUE_MAP, \
    SPACING_UNKNOWN, SPACING_GROUND_DISPLAY_NAME, SPACING_UNDERGROUND_DISPLAY_NAME, SPACING_STRUCTURE_DISPLAY_NAME
from src.charging_station.helper import calc_spacing, get_manual_spacing_bids
from src.charging_station.online_prop import online_spacing
from src.tools import pipeline, pgsql, tsv, notice_tool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    充电站信息
    """
    bid: str
    name: str
    address: str
    show_tag: str
    desired_show_tag: str = ''
    can_process: bool = True
    reason: str = ''

    @property
    def desired_show_tag_display_name(self):
        """
        获取空间属性展示值
        """
        return SPACING_VALUE_MAP.get(self.desired_show_tag, SPACING_GROUND_DISPLAY_NAME)

    @property
    def can_auto_process(self):
        """
        是否可以自动处理
        """
        return self.can_process and self.show_tag != self.desired_show_tag_display_name


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    records: list[Record] = field(default_factory=list)
    batch_records: list[Poi] = field(default_factory=list)
    ignored_bids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fill_ignored_bids(ctx: Context, proceed):
    """
    加载需要忽略推送的 bid 集合
    """
    ctx.ignored_bids = get_manual_spacing_bids()
    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载充电站信息
    """
    sql = '''
        select bid, name, address, show_tag
        from poi
        where std_tag = '交通设施;充电站' and show_tag not in ('地上充电站', '地下充电站', '停车楼充电站');
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, name, address, show_tag in poi_stab.fetch_all(sql):
            if bid in ctx.ignored_bids:
                continue

            ctx.records.append(Record(
                bid=bid,
                name=name,
                address=address,
                show_tag=show_tag,
            ))

    proceed()


@desc()
def fill_desired_show_tag(ctx: Context, proceed):
    """
    填充期望空间属性
    """
    for record in tqdm(ctx.records):
        record.desired_show_tag = calc_spacing(record.name)
        if record.desired_show_tag == SPACING_UNKNOWN:
            record.desired_show_tag = calc_spacing(record.address)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.bid,
                x.name,
                x.address,
                x.show_tag,
                x.desired_show_tag_display_name,
            ]
            for x in ctx.records if x.can_auto_process
        ]
    )

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    ctx.batch_records = [
        Poi(x.bid, show_tag=x.desired_show_tag_display_name)
        for x in ctx.records if x.can_auto_process
    ]

    online_spacing(ctx.batch_records, force=True)
    proceed()


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    如流通知结果
    """
    total_records = [x for x in ctx.records if x.can_auto_process]

    msg = f'''今日例行赋值空间属性量级如下：
总量：{len(total_records)}
地上充电站：{len([x for x in total_records if x.desired_show_tag_display_name == SPACING_GROUND_DISPLAY_NAME])}
地下充电站：{len([x for x in total_records if x.desired_show_tag_display_name == SPACING_UNDERGROUND_DISPLAY_NAME])}
停车楼充电站：{len([x for x in total_records if x.desired_show_tag_display_name == SPACING_STRUCTURE_DISPLAY_NAME])}
'''
    print(msg)
    alert_to_infoflow(msg)
    proceed()


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['test', 'online'],
        default='test',
        required=False,
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        fill_ignored_bids,
        load_records,
        fill_desired_show_tag,
        save_records,
    ]

    if args.mode == 'online':
        pipes.extend([
            execute_batch,
            send_records_to_infoflow,
        ])

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_assign_spacing'),
    )

    if args.mode == 'online':
        try:
            main_pipe(ctx)
        except Exception as e:
            alert_to_infoflow(f'例行赋值空间属性脚本异常！{e}')
    else:
        main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
