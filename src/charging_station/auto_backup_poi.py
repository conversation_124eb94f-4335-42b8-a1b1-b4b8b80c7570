# !/usr/bin/env python3
"""
例行备份充电站成果
"""
import argparse
import datetime
import json
from dataclasses import dataclass, field
from multiprocessing import Pool
from pathlib import Path

import requests
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.helper import BnsExecutor, get_nested_value
from src.tools import pipeline, pgsql, notice_tool, tsv

desc = pipeline.get_desc()
bns_executor = BnsExecutor()


@dataclass
class Record:
    """
    备份记录
    """
    bid: str
    name: str
    address: str
    relation_bid: str
    show_tag: str
    status: int
    telephone: str
    wkb: str
    wkt: str
    online_type: int = 1
    business_time: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    file_name: str
    table_name: str
    records: dict[str, Record] = field(default_factory=dict)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    sql = '''
        select bid, name, address, relation_bid, show_tag, status, telephone, geometry, st_astext(geometry)
        from poi
        where std_tag = '交通设施;充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for item in tqdm(poi_stab.fetch_all(sql)):
            bid, name, address, relation_bid, show_tag, status, telephone, wkb, wkt = item
            ctx.records[bid] = Record(
                bid=bid,
                address=address,
                relation_bid=relation_bid,
                name=name,
                show_tag=show_tag,
                status=status,
                telephone=telephone,
                wkb=wkb,
                wkt=wkt,
            )

    proceed()


def __get_response(ip, port, data):
    """
    获取接口响应数据
    """
    bid, = data
    api = f'http://{ip}:{port}/richindex/2/detail'
    params = {
        'from': 'map-de-aoi',
        'clientip': '*************',
        'cuid': 'de16',
        'bid': bid,
    }
    return requests.get(api, params=params)


def get_response(record: Record):
    """
    获取接口响应数据
    """
    try:
        return record.bid, bns_executor.execute(__get_response, (record.bid,))
    except Exception as e:
        print(e)
        return record.bid, None


def fetch_status(response_json):
    """
    从返回的 json 中提取状态
    """
    keys = ['data', 'mohushangxiaxian', 'vs_content', 'poi_status', 'text']
    poi_status_text = get_nested_value(response_json, keys)
    return 0 if poi_status_text == '不对外开放' else 1


def extract_time(weeks):
    """
    从返回的 weeks 信息中提取营业时间
    """
    # 定义星期几的映射
    weekdays = {
        "1": "周一",
        "2": "周二",
        "3": "周三",
        "4": "周四",
        "5": "周五",
        "6": "周六",
        "7": "周日"
    }

    # 存储每天的时间段
    time_dict = {}
    for key, value in weeks.items():
        time_dict[weekdays[key]] = value[0]["time"]

    # 合并相同的时间段
    result = {}
    prev_time = None
    start_day = None
    prev_day = None

    for day, time in time_dict.items():
        if time != prev_time:
            if start_day:
                result[f"{start_day}至{prev_day}"] = prev_time
            start_day = day
            prev_time = time
        prev_day = day

    # 添加最后一个时间段
    if start_day:
        result[f"{start_day}至{prev_day}"] = prev_time

    # 格式化输出
    output = []
    for days, time in result.items():
        output.append(f"{days} {time}")

    return " ".join(output)


def fetch_business_time(response_json):
    """
    从返回的 json 中提取营业时间
    """
    poi_business_time_text = get_nested_value(response_json, ['data', 'base', 'business_time'])
    if poi_business_time_text is None or poi_business_time_text == '':
        return ""

    poi_business_time_json = json.loads(poi_business_time_text)
    weeks = get_nested_value(poi_business_time_json, ['data', 0, 'common', 0, 'weeks'])

    if weeks is None:
        return ""

    print(weeks)
    return extract_time(weeks)


@desc()
def fill_online_properties(ctx: Context, proceed):
    """
    填充开放属性
    """
    pool_size = 1

    with Pool(pool_size) as p:
        for bid, response in tqdm(p.imap_unordered(get_response, ctx.records.values()), total=len(ctx.records)):
            if response is None:
                continue

            if not response.ok:
                continue

            response_json = response.json()
            ctx.records[bid].online_type = fetch_status(response_json)
            ctx.records[bid].business_time = fetch_business_time(response_json)

    proceed()


def create_table(ctx: Context):
    """
    创建数据库表
    """
    create_table_sql = f'''
        create table if not exists {ctx.table_name}(
            bid VARCHAR(64) PRIMARY KEY,
            address VARCHAR(1024) NOT NULL DEFAULT '',
            relation_bid VARCHAR(64) NOT NULL DEFAULT '',
            name VARCHAR(254) NOT NULL DEFAULT '',
            show_tag VARCHAR(512) NOT NULL DEFAULT '',
            business_time VARCHAR(512) NOT NULL DEFAULT '',
            status INT NOT NULL DEFAULT 1,
            online_type INT NOT NULL DEFAULT 1,
            telephone VARCHAR(200) NOT NULL DEFAULT '',
            geometry geometry(POINT, 4326)
        );
    '''
    clear_table_sql = f'''delete from {ctx.table_name};'''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(create_table_sql)
        poi_stab.execute(clear_table_sql)


def update_view(ctx: Context):
    """
    更新数据库视图
    """
    check_view_sql = f'''
        (SELECT column_name, data_type
         FROM information_schema.columns
         WHERE table_name = '{ctx.table_name}')
        EXCEPT
        (SELECT column_name, data_type
         FROM information_schema.columns
         WHERE table_name = 'cdz_history');
    '''
    drop_view_sql = f'''
        drop view if exists cdz_history;
    '''
    create_view_sql = f'''
        create or replace view cdz_history as
        select * from {ctx.table_name};
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        if poi_stab.fetch_all(check_view_sql):
            poi_stab.execute(drop_view_sql)

        poi_stab.execute(create_view_sql)


def drop_table(table_prefix: str, expire_day: int):
    """
    删除过期数据库表
    """
    sql = f"""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name LIKE '{table_prefix}_2%';
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    tables = [(x[0], x[0].split("_")[-1]) for x in ret]
    tables = [(table_name, datetime.datetime.strptime(day, "%Y%m%d")) for table_name, day in tables]

    today = datetime.datetime.now()
    expire = today - datetime.timedelta(days=expire_day)
    expired_tables = [table_name for table_name, day in tables if day < expire]
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for table_name in expired_tables:
            # NOTE: 此处不要把 {table_name} 写成 %s 的形式，pgsql 会把它当成字符串，加上引号，导致 sql 语法错误
            sql_clear = f"""
                drop table if exists {table_name};
            """
            pgsql.execute(conn, sql_clear)

    return expired_tables


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / ctx.file_name,
        [
            [
                x.bid,
                x.address,
                x.relation_bid,
                x.name,
                x.show_tag,
                x.business_time,
                x.status,
                x.online_type,
                x.telephone,
                x.wkb,
            ]
            for x in ctx.records.values()
        ]
    )

    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传记录到数据库
    """
    create_table(ctx)

    with (
        open(ctx.work_dir / ctx.file_name, 'r', encoding='utf-8') as f,
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cur,
    ):
        # noinspection PyTypeChecker
        cur.copy_from(f, table=ctx.table_name, columns=(
            'bid',
            'address',
            'relation_bid',
            'name',
            'show_tag',
            'business_time',
            'status',
            'online_type',
            'telephone',
            'geometry',
        ))

    update_view(ctx)
    drop_table('cdz_history', 8)
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行备份充电站成果脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['test', 'online'],
        default='test',
        required=False,
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_records,
        fill_online_properties,
        save_records,
    ]

    if args.mode == 'online':
        pipes.append(upload_records)

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    today = datetime.date.today().strftime('%Y%m%d')
    ctx = Context(
        work_dir=Path('cache/auto_backup_poi'),
        file_name=f"output_{today}.csv",
        table_name=f'cdz_history_{today}',
    )

    if args.mode != 'test':
        try:
            main_pipe(ctx)
        except Exception as e:
            alert_to_infoflow(e)
    else:
        main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
