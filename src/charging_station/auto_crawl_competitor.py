# !/usr/bin/env python3
"""
例行抓取竞品充电站
"""
import argparse
import datetime
import random
import shutil
from dataclasses import dataclass, field
from pathlib import Path

import pandas as pd
import shapely.wkt
from psycopg2.extras import DictCursor
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.pgsql_table_backup_tool import Backup
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.afs_tool import AfsTool

MAX_CRAWL_BATCH_SIZE = 45000

desc = pipeline.get_desc()


@dataclass
class CrawlResult:
    """
    抓取结果
    """
    bid: str
    competitor_id: str
    name: str
    city: str
    tag: str
    address: str
    wkt: str
    telephone: str
    crawl_time: int
    wkb: str


@dataclass
class CrawlRequest:
    """
    抓取请求
    """
    id: int
    x: float
    y: float
    type: str
    name: str = "汽车充电站"
    city: str = ""
    tag: str = "交通设施;充电站"
    radius: int = 1000


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    table_name: str
    request_file_name: str
    result_file_name: str
    afs_path: str
    recently_crawl_ids: set = field(default_factory=set)
    crawl_request_records: list[CrawlRequest] = field(default_factory=list)
    crawl_result_records: list[CrawlResult] = field(default_factory=list)
    crawl_result_paths: list[Path] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fill_recently_crawl_ids(ctx: Context, proceed):
    """
    获取最近的抓取 id
    """
    sql = '''
        select crawl_id
        from cdz_crawl_history 
        where crawled_time >= now()::date - interval '7 days';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        ctx.recently_crawl_ids = {x[0] for x in poi_stab.fetch_all(sql)}

    proceed()


@desc()
def load_poi_crawl_request_records(ctx: Context, proceed):
    """
    获取以 poi 为抓取对象的抓取请求
    """
    sql = '''
        select bid, name, city, st_astext(geometry)
        from poi
        where std_tag = '交通设施;充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, name, city, wkt in tqdm(poi_stab.fetch_all(sql)):
            if bid in ctx.recently_crawl_ids:
                continue

            geom = shapely.wkt.loads(wkt)
            ctx.crawl_request_records.append(CrawlRequest(
                id=bid,
                city=city,
                x=geom.x,
                y=geom.y,
                type="poi",
            ))

    proceed()


@desc()
def load_tp_crawl_request_records(ctx: Context, proceed):
    """
    获取以 tp 为抓取对象的抓取请求
    """
    sql = '''
        select concat('tp_', id), bid, station_name, station_lng, station_lat 
        from charging_station;
    '''

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        crawl_poi_ids = {x.id for x in ctx.crawl_request_records}
        cur.execute(sql)

        for tid, bid, station_name, station_lng, station_lat in tqdm(cur.fetchall()):
            if bid in crawl_poi_ids or tid in ctx.recently_crawl_ids:
                continue

            ctx.crawl_request_records.append(CrawlRequest(
                id=tid,
                x=station_lng,
                y=station_lat,
                type="tp",
            ))

    proceed()


@desc()
def disarrange_crawl_request_records(ctx: Context, proceed):
    """
    整理抓取请求
    """
    poi_records = random.sample(
        [x for x in ctx.crawl_request_records if x.type == "poi"],
        MAX_CRAWL_BATCH_SIZE // 2
    )
    tp_records = random.sample(
        [x for x in ctx.crawl_request_records if x.type == "tp"],
        MAX_CRAWL_BATCH_SIZE - len(poi_records)
    )
    ctx.crawl_request_records = poi_records + tp_records
    proceed()


@desc()
def save_crawl_request_records(ctx: Context, proceed):
    """
    保存抓取请求
    """
    tsv.write_tsv(
        ctx.work_dir / ctx.request_file_name,
        [
            [
                x.id,
                x.radius,
                x.name,
                x.city,
                x.x,
                x.y,
                x.tag,
            ]
            for x in ctx.crawl_request_records
        ]
    )
    proceed()


@desc()
def upload_crawl_request_records(ctx: Context, proceed):
    """
    上传抓取请求
    """
    try:
        afs = AfsTool('aries_rpm01')
        afs.put(str(ctx.work_dir / ctx.request_file_name), ctx.afs_path)
    except Exception as e:
        print(e)
        raise e

    proceed()


@desc()
def refresh_crawl_history(ctx: Context, proceed):
    """
    刷新抓取历史记录
    """
    delete_sql = '''
        delete from cdz_crawl_history 
        where crawled_time < now()::date - interval '7 days';
    '''

    request_history_path = ctx.work_dir / f'{ctx.request_file_name}.history'
    tsv.write_tsv(request_history_path, [
        [
            x.id,
            datetime.datetime.now(),
        ]
        for x in ctx.crawl_request_records
    ])

    with (
        open(request_history_path, 'r', encoding='utf-8') as f,
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cur,
    ):
        cur.execute(delete_sql)
        # noinspection PyTypeChecker
        cur.copy_from(f, table='cdz_crawl_history', columns=(
            'crawl_id',
            'crawled_time',
        ))

    proceed()


@desc()
def download_crawl_result_records(ctx: Context, proceed):
    """
    下载抓取结果
    """
    try:
        afs = AfsTool('aries_rpm01')
        all_paths = afs.list(ctx.afs_path)
        done_names = {Path(x).stem for x in all_paths if x.endswith('.txt.done')}
        print(done_names)

        for result_path in [Path(x) for x in all_paths if x.endswith('.txt.csv')]:
            if result_path.stem not in done_names:
                print(ctx.work_dir / result_path.name)
                # 存在抓取结果，但是未标记完成的文件。
                afs.get(str(result_path), ctx.work_dir)
                ctx.crawl_result_paths.append(ctx.work_dir / result_path.name)
    except Exception as e:
        print(e)
        raise e

    proceed()


@desc()
def load_crawl_result_records(ctx: Context, proceed):
    """
    加载抓取结果
    """
    sql = '''
        select st_geomfromtext(%s, 4326);
    '''

    with PgsqlStabilizer(pgsql.COMPUTE_CONFIG) as compute_stab:
        for path in ctx.crawl_result_paths:
            df = pd.read_csv(path, sep='\t', encoding='utf-8', encoding_errors='ignore', header=1, low_memory=False)
            for line in tqdm(df.itertuples(index=False), total=len(df)):
                bid, competitor_id, name, city, tag, address, x, y, _, telephone, _, crawl_time = line
                if x == '' or y == '':
                    continue

                wkt = f"POINT({x} {y})"
                ctx.crawl_result_records.append(CrawlResult(
                    bid=bid,
                    competitor_id=competitor_id,
                    name=name,
                    city=city,
                    tag=tag,
                    address=address,
                    wkt=wkt,
                    wkb=compute_stab.fetch_one(sql, [wkt])[0],
                    telephone=telephone,
                    crawl_time=crawl_time,
                ))

    proceed()


def could_be_charging_station(name, tag):
    """
    判断是否可能是一个充电站
    """
    valid_name_key = '电动汽车充电站'
    valid_tags = {
        '汽车服务;充电站;充电站',
        '汽车服务;换电站;换电站',
        '汽车服务;充电站;专用充电站',
        '汽车服务;充电站;个人充电站',
    }

    if valid_name_key in name and tag in valid_tags:
        return True

    if any(valid_tag in tag for valid_tag in valid_tags):
        return True

    return False


def get_current_competitor_data():
    """
    获取当前的竞品数据
    """
    get_all_competitor_sql = '''
        select bid, competitor_id, name, city, tag, address, geom, telephone, crawl_time 
        from cdz_competitor
        order by id desc;
    '''
    competitor_map = dict()

    with (
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG, cursor_factory=DictCursor) as poi_stab,
    ):
        for data in poi_stab.fetch_all(get_all_competitor_sql):
            name = data['name']
            tag = data['tag']
            competitor_id = data['competitor_id']
            if not could_be_charging_station(name, tag):
                continue

            competitor_map[competitor_id] = data

    return competitor_map


@desc()
def combine_crawl_results(ctx: Context, proceed):
    """
    合并抓取结果
    """
    current_competitor_data = get_current_competitor_data()

    for record in tqdm(ctx.crawl_result_records):
        if not could_be_charging_station(record.name, record.tag):
            continue

        current_competitor_data[record.competitor_id] = [
            record.bid,
            record.competitor_id,
            record.name,
            record.city,
            record.tag,
            record.address,
            record.wkb,
            record.telephone,
            record.crawl_time,
        ]

    tsv.write_tsv(ctx.work_dir / ctx.result_file_name, current_competitor_data.values())
    proceed()


@desc()
def save_crawl_result_to_db(ctx: Context, proceed):
    """
    将抓取结果保存到数据库
    """
    with (
        Backup(config=pgsql.POI_CONFIG, table_name="cdz_competitor") as backup,
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab,
        open(ctx.work_dir / ctx.result_file_name, "r", encoding="utf-8") as f,
    ):
        backup_table_name = backup.execute()
        stab.connection.autocommit = False

        try:
            with stab.connection.cursor() as cur:
                cur.execute(f"delete from {backup_table_name};")
                cur.copy_from(f, table=backup_table_name, columns=(
                    'bid',
                    'competitor_id',
                    'name',
                    'city',
                    'tag',
                    'address',
                    'geom',
                    'telephone',
                    'crawl_time',
                ))
                stab.connection.commit()
        except Exception as e:
            print(e)
            stab.connection.rollback()

    proceed()


@desc()
def mark_crawl_task_finished(ctx: Context, proceed):
    """
    标记抓取任务完成
    """
    try:
        afs = AfsTool('aries_rpm01')

        for result_path in tqdm(ctx.crawl_result_paths):
            done_path = Path(ctx.work_dir / f'{result_path.stem}.done')
            done_path.touch()
            afs.put(str(done_path), ctx.afs_path)

    except Exception as e:
        print(e)
        raise e

    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行抓取竞品充电站脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--action',
        dest='action',
        type=str,
        choices=['all', 'upload', 'download'],
        default='all',
        required=False,
    )
    return parser.parse_args()


def create_pipeline(action):
    if action == 'all':
        return pipeline.Pipeline(
            fill_recently_crawl_ids,
            load_poi_crawl_request_records,
            load_tp_crawl_request_records,
            disarrange_crawl_request_records,
            save_crawl_request_records,
            upload_crawl_request_records,
            refresh_crawl_history,
            download_crawl_result_records,
            load_crawl_result_records,
            combine_crawl_results,
            save_crawl_result_to_db,
            mark_crawl_task_finished,
        )
    elif action == 'upload':
        return pipeline.Pipeline(
            fill_recently_crawl_ids,
            load_poi_crawl_request_records,
            load_tp_crawl_request_records,
            disarrange_crawl_request_records,
            save_crawl_request_records,
            upload_crawl_request_records,
            refresh_crawl_history,
        )
    elif action == 'download':
        return pipeline.Pipeline(
            download_crawl_result_records,
            load_crawl_result_records,
            combine_crawl_results,
            save_crawl_result_to_db,
            mark_crawl_task_finished,
        )

    raise ValueError(f'Unknown action: {action}')


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args.action)
    desc.attach(main_pipe)
    today = datetime.date.today()
    request_date = (today + datetime.timedelta(days=1)).strftime('%Y%m%d')
    ctx = Context(
        work_dir=Path('cache/auto_crawl_competitor'),
        table_name=f"cdz_competitor_{today.strftime('%Y%m%d')}",
        request_file_name=f"crawl_poi_list_{request_date}.txt",
        result_file_name=f"crawl_result_{today.strftime('%Y%m%d')}.txt",
        afs_path='user/map-data-streeview/aoi-ml/crawl_cdz',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)
    finally:
        shutil.rmtree(ctx.work_dir)


if __name__ == '__main__':
    main(parse_args())
