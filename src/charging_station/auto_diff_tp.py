# !/usr/bin/env python3
"""
例行 diff 充电站 tp 数据
"""
import csv
import datetime
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, tsv, pgsql, notice_tool

DESIRED_FIELDS = [
    'bid',
    'station_name',
    'address',
    'station_type',
    'station_status',
    'station_lng',
    'station_lat',
    'park_info',
    'site_guide'
]

desc = pipeline.get_desc()


@dataclass
class Diff:
    """
    数据差异
    """
    tp_id: int
    change_type: str
    field_name: str = ''
    old_value: object = None
    new_value: object = None


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    today_file_name: Path
    yesterday_file_name: Path
    today_data: dict[id, dict[str, object]] = field(default_factory=dict)
    yesterday_data: dict[id, dict[str, object]] = field(default_factory=dict)
    diff_data: list[Diff] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def export_today_data(ctx: Context, proceed):
    """
    导出今天的数据
    """
    sql = '''select * from charging_station;'''

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cursor,
        open(ctx.today_file_name, 'w', newline='', encoding='utf-8') as csvfile,
    ):
        cursor.execute(sql)
        columns = [x[0] for x in cursor.description]
        csv_writer = csv.writer(csvfile)
        csv_writer.writerow(columns)  # 写入列名
        csv_writer.writerows(cursor.fetchall())  # 写入数据

    proceed()


def read_data(file_name: Path):
    """
    读取数据
    """
    data_dict = {}
    if not file_name.exists():
        raise FileNotFoundError(f'{file_name} not found.')

    with open(file_name, 'r', newline='', encoding='utf-8') as csvfile:
        csv_reader = csv.reader(csvfile)
        headers = next(csv_reader)
        for row in csv_reader:
            tp_id = int(row[0])
            data_dict[tp_id] = {k: v for k, v in zip(headers, row)}

    return data_dict


@desc()
def read_today_data(ctx: Context, proceed):
    """
    读取今天的数据
    """
    ctx.today_data = read_data(ctx.today_file_name)
    proceed()


@desc()
def read_yesterday_data(ctx: Context, proceed):
    """
    读取昨天的数据
    """
    ctx.yesterday_data = read_data(ctx.yesterday_file_name)
    proceed()


@desc()
def generate_diff_data(ctx: Context, proceed):
    """
    生成差异数据
    """
    ctx.diff_data.extend([Diff(x, 'delete') for x in ctx.yesterday_data.keys() - ctx.today_data.keys()])
    ctx.diff_data.extend([Diff(x, 'add') for x in ctx.today_data.keys() - ctx.yesterday_data.keys()])

    for tp_id in ctx.today_data.keys() & ctx.yesterday_data.keys():
        for field_name in ctx.today_data[tp_id]:
            if field_name not in DESIRED_FIELDS:
                continue

            old_value = ctx.yesterday_data[tp_id][field_name]
            new_value = ctx.today_data[tp_id][field_name]
            if old_value != new_value:
                ctx.diff_data.append(Diff(
                    tp_id=tp_id,
                    field_name=field_name,
                    change_type='update',
                    old_value=old_value,
                    new_value=new_value,
                ))

    proceed()


@desc()
def upload_diff_data(ctx: Context, proceed):
    """
    上传差异数据
    """
    sql = '''
        insert into cdz_tp_resume (tp_id, change_type, field_name, old_value, new_value)
        values (%s, %s, %s, %s, %s);
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab:
        poi_stab.connection.autocommit = False
        with poi_stab.connection.cursor() as cursor:
            try:
                for diff in tqdm(ctx.diff_data):
                    cursor.execute(sql, (
                        diff.tp_id,
                        diff.change_type,
                        diff.field_name,
                        diff.old_value,
                        diff.new_value,
                    ))

                poi_stab.connection.commit()
            except Exception as e:
                print(e)
                poi_stab.connection.rollback()

    proceed()


@desc()
def delete_cache_data(ctx: Context, proceed):
    """
    删除缓存数据
    """
    target_day = datetime.date.today() - datetime.timedelta(days=2)
    target_filename = ctx.work_dir / f'backup_{target_day:%Y%m%d}.csv'
    target_filename.unlink(missing_ok=True)
    proceed()


@desc()
def save_diff_data(ctx: Context, proceed):
    """
    保存差异数据
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.tp_id,
                x.change_type,
                x.field_name,
                x.old_value,
                x.new_value,
            ]
            for x in ctx.diff_data
        ]
    )
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行 diff 充电站 tp 数据脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        export_today_data,
        read_yesterday_data,
        read_today_data,
        generate_diff_data,
        upload_diff_data,
        delete_cache_data,
        save_diff_data,
    )
    desc.attach(main_pipe)
    today = datetime.date.today()
    yesterday = today - datetime.timedelta(days=1)
    work_dir = Path('cache/auto_diff_tp')
    ctx = Context(
        work_dir=work_dir,
        today_file_name=work_dir / f'backup_{today:%Y%m%d}.csv',
        yesterday_file_name=work_dir / f'backup_{yesterday:%Y%m%d}.csv',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
