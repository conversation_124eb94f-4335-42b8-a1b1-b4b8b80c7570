# !/usr/bin/env python3
"""
充电桩评论例行获取
"""
from dataclasses import dataclass, field
from multiprocessing import Pool
from pathlib import Path

import requests
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.helper import get_hosts, BnsExecutor
from src.tools import pipeline, pgsql, tsv, notice_tool

desc = pipeline.get_desc()
bns_executor = BnsExecutor()


@dataclass
class Comment:
    """
    充电桩评论
    """
    bid: str
    uuid: str
    content: str
    created_at: str


@dataclass
class Record:
    """
    批处理记录
    """
    bid: int
    comments: list[Comment] = field(default_factory=list)


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    records: dict[str, Record] = field(default_factory=dict)
    hosts: list[tuple[str, int]] = field(default_factory=list)
    uuids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fill_hosts(ctx: Context, proceed):
    """
    获取可用的 ip 池
    """
    ctx.hosts.extend(get_hosts())
    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理记录
    """
    sql = '''
        select bid, relation_bid, name, show_tag, status, st_astext(geometry)
        from poi
        where std_tag = '交通设施;充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, relation_bid, name, show_tag, status, wkt in tqdm(poi_stab.fetch_all(sql)):
            ctx.records[bid] = Record(
                bid=bid,
            )

    proceed()


def __get_response(ip, port, data):
    """
    获取评论接口响应数据
    """
    bid, = data
    api = f'http://{ip}:{port}/richindex/2/richcomment'
    params = {
        'from': 'map-de-aoi',
        'clientip': '*************',
        'cuid': 'de16',
        'bid': bid,
    }
    return requests.get(api, params=params)


def get_response(record: Record):
    """
    获取接口响应数据
    """
    try:
        return bns_executor.execute(__get_response, (record.bid,))
    except Exception as e:
        print(e)
        return None


@desc()
def get_all_comment_uuids(ctx: Context, proceed):
    """
    获取所有评论的 uuid 集合
    """
    sql = '''
        select uuid from cdz_comment;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ctx.uuids = set(x[0] for x in stab.fetch_all(sql))

    proceed()


@desc()
def fill_comments(ctx: Context, proceed):
    """
    获取评论数据
    """
    pool_size = 4

    with Pool(pool_size) as p:
        for response in tqdm(p.imap_unordered(get_response, ctx.records.values()), total=len(ctx.records)):
            if response is None:
                continue

            if not response.ok:
                continue

            response_json = response.json()
            comments = response_json['data']['comment_list']
            for comment in comments:
                bid = comment['poi_id']
                content = comment['content']
                uuid = comment['uniqkey']

                if content == '' or uuid in ctx.uuids:
                    continue

                record = ctx.records.get(bid, None)
                if record is None:
                    continue

                record.comments.append(Comment(
                    bid=bid,
                    uuid=uuid,
                    content=content,
                    created_at=comment['date'],
                ))

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存批处理记录
    """
    output_items = []

    for record in ctx.records.values():
        for comment in record.comments:
            output_items.append([
                record.bid,
                comment.uuid,
                comment.content,
                comment.created_at,
            ])

    tsv.write_tsv(ctx.work_dir / 'output.csv', output_items)
    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传批处理记录
    """
    sql = '''
        insert into cdz_comment(uuid, bid, comment, created_time)
        values (%s, %s, %s, %s)
        on conflict (uuid) do nothing;
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for record in tqdm(ctx.records.values(), total=len(ctx.records)):
                for comment in record.comments:
                    cur.execute(sql, (
                        comment.uuid,
                        record.bid,
                        comment.content,
                        comment.created_at,
                    ))
            poi_stab.connection.commit()
        except Exception as e:
            print(e)
            poi_stab.connection.rollback()
            raise e

    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'充电桩评论例行获取脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        fill_hosts,
        load_records,
        get_all_comment_uuids,
        fill_comments,
        save_records,
        upload_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_fetch_comment'),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
