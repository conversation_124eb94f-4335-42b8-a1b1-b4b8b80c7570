# !/usr/bin/env python3
"""
例行获取充电站干预记录
"""
from dataclasses import dataclass, field
from datetime import datetime, date
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import UGC_SOURCES
from src.tools import pipeline, pgsql, tsv, notice_tool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    干预记录
    """
    record_id: int
    bid: str
    field_name: str
    old_value: str
    new_value: str
    update_time: datetime
    src_type: str
    sub_src: str


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    intervention_record_path: Path
    bids: set[str] = field(default_factory=set)
    intervention_ids: set[int] = field(default_factory=set)
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


def read_lines_from_end(file_path, buffer_size=4096, max_buffer_size=8192, encoding='utf-8'):
    """
    从文件末尾逐行读取数据
    """
    with open(file_path, 'rb') as file:
        # 将文件指针移动到文件末尾
        file.seek(0, 2)  # 2 表示从文件末尾开始
        file_size = file.tell()
        buffer = bytearray()

        # 从文件末尾逐块向前读取
        while file_size > 0:
            # 计算本次读取的大小
            read_size = min(buffer_size, file_size)
            file_size -= read_size
            file.seek(file_size)

            # 读取数据块
            chunk = file.read(read_size)
            buffer.extend(chunk)
            # 按行处理数据
            while b'\n' in buffer:
                # 找到最后一个换行符
                line_end = buffer.rfind(b'\n')
                if line_end == -1:
                    break

                # 提取一行数据
                line = buffer[line_end + 1:].decode(encoding, errors='ignore').strip()
                buffer = buffer[:line_end]  # 保留剩余数据
                yield line

            # 限制缓冲区大小
            if len(buffer) > max_buffer_size:
                buffer = buffer[-max_buffer_size:]

        # 处理最后一行（文件开头可能没有换行符）
        if buffer:
            line = buffer.decode(encoding, errors='ignore').strip()
            yield line


@desc()
def load_cdz_bids(ctx: Context, proceed):
    """
    加载充电站全量 bid 集合
    """
    sql = '''
        select bid from cdz_history;
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ctx.bids = set(x[0] for x in stab.fetch_all(sql))

    proceed()


@desc()
def load_intervention_ids(ctx: Context, proceed):
    """
    加载干预记录 id 集合
    """
    sql = '''
        select record_id from cdz_intervention;
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ctx.intervention_ids = set(x[0] for x in stab.fetch_all(sql))

    proceed()


def get_approximate_count(file_path):
    """
    获取文件的大致行数
    """
    for line in tqdm(read_lines_from_end(file_path, encoding='gbk')):
        if line == '':
            continue

        return int(line.split('\t')[0])


@desc()
def load_records(ctx: Context, proceed):
    """
    加载干预记录
    """
    valid_length = 11
    max_days = 10
    total_lines = get_approximate_count(ctx.intervention_record_path)

    for line in tqdm(read_lines_from_end(ctx.intervention_record_path, encoding='gbk'), total=total_lines):
        arr = line.split('\t')
        if len(arr) != valid_length:
            continue

        record_id, _, baidu_id, _, key_name, new_value, old_value, _, update_time_str, src_type, sub_src = tuple(arr)

        source = f'{src_type}.{sub_src}'
        if source not in UGC_SOURCES:
            continue

        update_time = datetime.fromtimestamp(float(update_time_str))
        days = (date.today() - update_time.date()).days
        if days > max_days or int(record_id) in ctx.intervention_ids:
            break

        if baidu_id not in ctx.bids:
            continue

        ctx.records.append(Record(
            record_id=int(record_id),
            bid=baidu_id,
            field_name=key_name,
            old_value=old_value,
            new_value=new_value,
            update_time=update_time,
            src_type=src_type,
            sub_src=sub_src,
        ))

        print(f'len: {len(ctx.records)}, days: {days}')

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存干预记录
    """
    tsv.write_tsv(
        ctx.work_dir / "output.csv",
        [
            [
                x.record_id,
                x.bid,
                x.field_name,
                x.old_value,
                x.new_value,
                x.update_time.strftime("%Y-%m-%d %H:%M:%S"),
                x.src_type,
                x.sub_src,
            ]
            for x in ctx.records
        ],
    )
    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传干预记录
    """
    sql = '''
        insert into cdz_intervention(
            record_id, 
            bid, 
            field_name, 
            old_value, 
            new_value, 
            src_type, 
            sub_src, 
            create_time
        )
        values (%s, %s, %s, %s, %s, %s, %s, %s)
        on conflict (record_id) do nothing;
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for record in tqdm(ctx.records):
                cur.execute(sql, (
                    record.record_id,
                    record.bid,
                    record.field_name,
                    record.old_value,
                    record.new_value,
                    record.src_type,
                    record.sub_src,
                    record.update_time,
                ))

            poi_stab.connection.commit()
        except Exception as e:
            print(e)
            poi_stab.connection.rollback()
            raise e

    proceed()


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_cdz_bids,
        load_intervention_ids,
        load_records,
        save_records,
        upload_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_fetch_intervention_record"),
        intervention_record_path=Path("/home/<USER>/cdz_mnt/data/t_inte")
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(f'例行获取充电站干预记录脚本异常！{e}')


if __name__ == "__main__":
    main()
