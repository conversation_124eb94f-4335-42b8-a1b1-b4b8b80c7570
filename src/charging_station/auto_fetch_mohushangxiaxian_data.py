# !/usr/bin/env python3
"""
例行获取模糊上下线数据
"""
import datetime
import json
from dataclasses import dataclass, field
from pathlib import Path

import requests
from retrying import retry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import UGC_SOURCES
from src.charging_station.helper import get_nested_value
from src.tools import pipeline, pgsql, tsv, notice_tool

API = 'http://************:8080/?user=map_content&password=map_content&database=mapContent'

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    干预记录
    """
    record_id: str
    bid: str
    online_type: int
    update_time: datetime
    src_type: str
    sub_src: str


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    bids: set[str] = field(default_factory=set)
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_cdz_bids(ctx: Context, proceed):
    """
    加载充电站全量 bid 集合
    """
    sql = '''
        select bid from cdz_history;
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ctx.bids = set(x[0] for x in stab.fetch_all(sql))

    proceed()


@retry(stop_max_attempt_number=8, wait_random_min=2 * 60 * 1000, wait_random_max=5 * 60 * 1000)
def get_response_text(sql):
    """
    获取响应
    """
    text = requests.post(API, data=sql).text
    if text.startswith('['):
        raise Exception('error response')

    return text


@desc()
def download_logs(ctx: Context, proceed):
    """
    下载日志
    """
    end_day = datetime.date.today()
    start_day = end_day - datetime.timedelta(days=10)
    end_str = end_day.strftime('%Y-%m-%d')
    start_str = start_day.strftime('%Y-%m-%d')

    # 不能使用三引号(不能使用换行符)，否则接口不会返回数据。
    sql = (
        "SELECT bid, source, query_key "
        "FROM baidu_map_poi_analysis "
        "WHERE logid GLOBAL IN ("
        "   SELECT DISTINCT logid "
        "   FROM baidu_map_poi_analysis "
        "   WHERE module_name IN ('detail-process') AND "
        f"        timestampInLog >= toDateTime('{start_str}') AND "
        f"        timestampInLog <= toDateTime('{end_str}') AND "
        "         updatecards='mohushangxiaxian'"
        ") AND "
        "module_name IN ('data-access') AND "
        f"timestampInLog >= toDateTime('{start_str}') AND "
        f"timestampInLog <= toDateTime('{end_str}');"
    )

    with open(ctx.work_dir / 'logs.csv', 'w') as f:
        f.write(get_response_text(sql))

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载干预记录
    """
    keys = ['mohushangxiaxian', 'info', 'vs_content', 'poi_status', 'text']

    for line in tqdm([x for x in tsv.read_tsv(ctx.work_dir / 'logs.csv')]):
        bid, _, query_key = line
        if query_key == '' or not query_key.endswith('}') or bid not in ctx.bids:
            continue

        try:
            record_json = json.loads(query_key.replace('\\"', '"').replace(r'\\\\u', r'\u'))
            src_type = record_json['src_type']
            sub_src = record_json['sub_src']
            source = f'{src_type}.{sub_src}'
            if source not in UGC_SOURCES:
                continue

            content = record_json['content']
            content_json = json.loads(content)
            poi_status_text = get_nested_value(content_json, keys)
            if poi_status_text is None:
                continue

            update_time = datetime.datetime.fromtimestamp(float(record_json['accessTime']) / 1000)
            online_type = 0 if poi_status_text == '不对外开放' else 1

            ctx.records.append(Record(
                record_id=record_json['_plog_id_'],
                bid=bid,
                src_type=record_json['src_type'],
                sub_src=record_json['sub_src'],
                online_type=online_type,
                update_time=update_time,
            ))
        except Exception as e:
            print(e)
            print(bid, query_key)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存干预记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.record_id,
                x.bid,
                x.online_type,
                x.update_time.strftime("%Y-%m-%d %H:%M:%S"),
                x.src_type,
                x.sub_src,
            ]
            for x in ctx.records
        ]
    )
    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传干预记录
    """
    sql = '''
        insert into cdz_mohushangxiaxian(
            record_id, 
            bid,
            online_type, 
            src_type, 
            sub_src, 
            create_time
        )
        values (%s, %s, %s, %s, %s, %s)
        on conflict (record_id) do nothing;
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for record in tqdm(ctx.records):
                cur.execute(sql, (
                    record.record_id,
                    record.bid,
                    record.online_type,
                    record.src_type,
                    record.sub_src,
                    record.update_time,
                ))

            poi_stab.connection.commit()
        except Exception as e:
            print(e)
            poi_stab.connection.rollback()
            raise e

    proceed()


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_cdz_bids,
        download_logs,
        load_records,
        save_records,
        upload_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_fetch_mohushangxiaxian_data"),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(f'例行获取模糊上下线数据脚本异常！{e}')


if __name__ == "__main__":
    main()
