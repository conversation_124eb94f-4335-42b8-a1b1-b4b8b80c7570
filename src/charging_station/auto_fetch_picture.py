# !/usr/bin/env python3
"""
充电桩图片例行获取
"""
import datetime
from dataclasses import dataclass, field
from multiprocessing import Pool
from pathlib import Path

import requests
from psycopg2.extras import DictCursor
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.helper import get_hosts, BnsExecutor
from src.charging_station.pgsql_table_backup_tool import Backup
from src.tools import pipeline, pgsql, tsv, notice_tool

desc = pipeline.get_desc()
bns_executor = BnsExecutor()


@dataclass
class Picture:
    """
    充电桩图片信息
    """
    bid: str
    uuid: str
    url: str
    created_at: str


@dataclass
class Record:
    """
    批处理记录
    """
    bid: str
    pictures: list[Picture] = field(default_factory=list)


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    file_name: str
    records: dict[str, Record] = field(default_factory=dict)
    hosts: list[tuple[str, int]] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fill_hosts(ctx: Context, proceed):
    """
    获取可用的 ip 池
    """
    ctx.hosts.extend(get_hosts())
    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理记录
    """
    sql = '''
        select bid, relation_bid, name, show_tag, status, st_astext(geometry)
        from poi
        where std_tag = '交通设施;充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, relation_bid, name, show_tag, status, wkt in tqdm(poi_stab.fetch_all(sql)):
            ctx.records[bid] = Record(
                bid=bid,
            )

    proceed()


def __get_response(ip, port, data):
    """
    获取接口响应数据
    """
    bid, = data
    api = f'http://{ip}:{port}/richindex/2/photo'
    params = {
        'from': 'map-de-aoi',
        'clientip': '*************',
        'cuid': 'de16',
        'bid': bid,
    }

    return requests.get(api, params=params)


def get_response(record: Record):
    """
    获取接口响应数据
    """
    try:
        return record.bid, bns_executor.execute(__get_response, (record.bid,))
    except Exception as e:
        print(e)
        return record.bid, None


def is_valid_datetime(datetime_str):
    """
    判断是否是有效的日期时间字符串
    """
    try:
        datetime.datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        return True
    except ValueError:
        return False


def parse_picture(bid, picture_json):
    """
    解析图片
    """
    url = picture_json.get('imgUrl', '')
    uuid = picture_json.get('uniqkey', '')
    created_at = picture_json.get('photo_uploadtime', '')

    if url == '' or uuid == '' or created_at == '':
        return None

    if not is_valid_datetime(created_at):
        return None

    return Picture(
        bid=bid,
        uuid=uuid,
        url=url,
        created_at=created_at,
    )


def fill_pictures(ctx: Context, proceed):
    """
    填充图片信息
    """
    pool_size = 4

    with Pool(pool_size) as p:
        for bid, response in tqdm(p.imap_unordered(get_response, ctx.records.values()), total=len(ctx.records)):
            if response is None or not response.ok:
                continue

            response_json = response.json()
            pictures = response_json['data']['photo_list']

            if pictures is None:
                continue

            for picture_json in pictures:
                picture = parse_picture(bid, picture_json)

                if picture is None:
                    continue

                ctx.records[bid].pictures.append(picture)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存批处理记录
    """
    output_items = []

    for record in ctx.records.values():
        for picture in record.pictures:
            output_items.append([
                record.bid,
                picture.uuid,
                picture.url,
                picture.created_at,
            ])

    tsv.write_tsv(ctx.work_dir / 'output.csv', output_items)
    proceed()


def get_current_pictures():
    """
    获取当前的图片数据
    """
    sql = '''
        select uuid, bid, url, created_time from cdz_picture;
    '''
    picture_map = dict()

    with (
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG, cursor_factory=DictCursor) as poi_stab,
    ):
        for data in poi_stab.fetch_all(sql):
            picture_map[data['uuid']] = data

    return picture_map


@desc()
def combine_pictures(ctx: Context, proceed):
    """
    合并图片数据
    """
    current_pictures = get_current_pictures()

    for record in tqdm(ctx.records.values(), total=len(ctx.records)):
        for picture in record.pictures:
            current_pictures[picture.uuid] = [
                picture.uuid,
                picture.bid,
                picture.url,
                picture.created_at,
            ]

    tsv.write_tsv(ctx.work_dir / ctx.file_name, current_pictures.values())
    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传批处理记录
    """
    with (
        Backup(config=pgsql.POI_CONFIG, table_name="cdz_picture") as backup,
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab,
        open(ctx.work_dir / ctx.file_name, 'r', encoding='utf-8') as f,
    ):
        backup_table_name = backup.execute()
        stab.connection.autocommit = False

        try:
            with stab.connection.cursor() as cur:
                cur.execute(f"delete from {backup_table_name};")
                cur.copy_from(f, table=backup_table_name, columns=(
                    'uuid',
                    'bid',
                    'url',
                    'created_time',
                ))
                stab.connection.commit()
        except Exception as e:
            print(e)
            stab.connection.rollback()

    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'充电桩图片例行获取脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        fill_hosts,
        load_records,
        fill_pictures,
        save_records,
        combine_pictures,
        upload_records,
    )
    desc.attach(main_pipe)
    today = datetime.date.today().strftime('%Y%m%d')
    ctx = Context(
        work_dir=Path('cache/auto_fetch_picture'),
        file_name=f"pictures_{today}.txt",
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
