# !/usr/bin/env python3
"""
例行获取充电站发布结果
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.pgsql_table_backup_tool import Backup
from src.tools import pipeline, pgsql
from src.tools.file_downloader import download_file_by_http

DOWNLOAD_URL_FORMAT = "http://filecenter.matrix.baidu.com/api/v1/file/caojunfeng_cd/tid2bid/{0}/download"

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    date: datetime.date
    release_result_path: Path = field(init=False)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def download_release_result(ctx: Context, proceed):
    """
    下载发布结果
    """
    download_url = DOWNLOAD_URL_FORMAT.format(ctx.date.strftime("%Y%m%d"))
    ctx.release_result_path = ctx.work_dir / f"result_{ctx.date}.csv"
    download_file_by_http(download_url, ctx.release_result_path)

    proceed()


@desc()
def save_records_to_db(ctx: Context, proceed):
    """
    保存记录到数据库
    """
    with (
        Backup(config=pgsql.POI_CONFIG, table_name="cdz_release_result") as backup,
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab,
        open(ctx.release_result_path, "r", encoding="utf-8") as f,
    ):
        backup_table_name = backup.execute()
        stab.connection.autocommit = False

        try:
            with stab.connection.cursor() as cur:
                cur.execute(f"delete from {backup_table_name};")
                cur.copy_from(f, table=backup_table_name, columns=(
                    "tid",
                    "bid",
                ))
                stab.connection.commit()
        except Exception as e:
            print(e)
            stab.connection.rollback()

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        download_release_result,
        save_records_to_db,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_fetch_release_result"),
        date=datetime.date.today(),
    )

    try:
        main_pipe(ctx)
    finally:
        ctx.release_result_path.unlink(missing_ok=True)


if __name__ == "__main__":
    main()
