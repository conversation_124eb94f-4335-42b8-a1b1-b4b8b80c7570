# !/usr/bin/env python3
"""
例行获取 tp 品牌
"""
from dataclasses import dataclass, field
from pathlib import Path

import requests
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.helper import BnsExecutor, get_nested_value
from src.charging_station.pgsql_table_backup_tool import Backup
from src.tools import pipeline, pgsql, tsv, notice_tool

BNS_HOST = 'group.opera-Online-NaviNewEnergyBP-NaviNewEnergyBP-all.map-navi.all'

desc = pipeline.get_desc()
bns_executor = BnsExecutor(bns_name=BNS_HOST, auto_retry=False)


@dataclass
class Record:
    """
    品牌记录
    """
    third_code: int
    third_name: str
    operator_id: str
    tag: int


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    records: list = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


def get_response(ip, port, _):
    """
    获取接口响应数据
    """
    return requests.get(f'http://{ip}:{port}/business_platform/inner/operators', headers={
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.e30.D6md94soUW7ty5zkMJq-29blP4ug4q7ixzMYShubk6E'
    })


@desc()
def load_records(ctx: Context, proceed):
    """
    获取品牌数据
    """
    response = bns_executor.execute(get_response, None)
    response_json = response.json()
    status = response_json['status']

    if status != 0:
        raise Exception('fetch tp brand failed')

    for item in get_nested_value(response_json, ['data', 'list']):
        tags = item['tags'].split(',')
        for tag in tags:
            ctx.records.append(Record(
                third_code=item['third_code'],
                third_name=item['third_name'],
                operator_id=item['operator_id'],
                tag=int(tag) if tag != '' else 0,
            ))

    proceed()


@desc()
def save_records_to_db(ctx: Context, proceed):
    """
    保存品牌数据到数据库
    """
    with (
        Backup(config=pgsql.POI_CONFIG, table_name="cdz_tp_brand") as backup,
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab,
    ):
        backup_table_name = backup.execute()
        delete_sql = f'''
            delete from {backup_table_name};
        '''
        insert_sql = f'''
            insert into {backup_table_name} (third_code, third_name, operator_id, tag) 
            values (%s, %s, %s, %s);
        '''

        stab.connection.autocommit = False

        try:
            with stab.connection.cursor() as cur:
                cur.execute(delete_sql)
                for record in tqdm(ctx.records):
                    cur.execute(insert_sql, (record.third_code, record.third_name, record.operator_id, record.tag))

                stab.connection.commit()
        except Exception as e:
            print(e)
            stab.connection.rollback()

    proceed()


@desc()
def save_records_to_file(ctx: Context, proceed):
    """
    保存品牌数据到文件
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.third_code,
                x.third_name,
                x.operator_id,
                x.tag,
            ]
            for x in ctx.records
        ]
    )
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行获取 tp 品牌脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        save_records_to_db,
        save_records_to_file,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_fetch_tp_brand'),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
