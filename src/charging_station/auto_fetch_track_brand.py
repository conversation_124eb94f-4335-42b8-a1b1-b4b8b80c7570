# !/usr/bin/env python3
"""
例行入库轨迹电车品牌映射
"""
import argparse
import datetime
import time
from dataclasses import dataclass, field
from pathlib import Path

import requests
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, notice_tool
from src.tools.afs_tool import AfsTool
from src.tools.file_downloader import download_file_by_afs
from src.tools.traj_helper import decrypt_user_id

ONLINE_DEVICE_API = 'http://gzxj-zhongyuan0004.gzxj:8888/trace/device'
OFFLINE_DEVICE_FILE_NAME = 'devices.csv'

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    afs_path: str
    history_brand_codes: set[str] = field(default_factory=list)
    device_to_brand_map: dict[str, str] = field(default_factory=dict)
    code_to_brand_map: dict[str, str] = field(default_factory=dict)
    encrypted_user_ids: list[str] = field(default_factory=list)
    decrypted_user_ids: list[str] = field(default_factory=list)
    online_device_infos: list[tuple[str, str]] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def try_download_offline_device_infos(ctx: Context, proceed):
    """
    尝试下载离线设备信息文件
    """
    file_path = ctx.work_dir / OFFLINE_DEVICE_FILE_NAME
    if not file_path.exists():
        print('Downloading offline device infos...')
        download_file_by_afs(f'{ctx.afs_path}/{OFFLINE_DEVICE_FILE_NAME}', file_path)
        print('Done.')

    if not file_path.exists():
        raise FileNotFoundError(f'Offline device infos file not found: {file_path}')

    proceed()


@desc()
def fill_device_to_brand_map(ctx: Context, proceed):
    """
    填充设备 id 到品牌映射关系
    """
    file_path = ctx.work_dir / OFFLINE_DEVICE_FILE_NAME

    with open(file_path, 'r') as f:
        for line in f:
            items = line.rstrip().split(',')
            device_id = items[0]
            brand = items[2]
            ctx.device_to_brand_map[device_id] = brand

    proceed()


@desc()
def fill_history_brand_codes(ctx: Context, proceed):
    """
    填充历史品牌编码
    """
    sql = '''
        select code from cdz_brand;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        ctx.history_brand_codes = set([str(x[0]) for x in poi_stab.fetch_all(sql)])

    print(len(ctx.history_brand_codes))
    proceed()


def get_user_id_count():
    """
    获取用户 id 数量
    """
    sql = '''
        SELECT reltuples::BIGINT AS approximate_row_count
        FROM pg_class
        WHERE relname = 'parking_points';
    '''

    with PgsqlStabilizer(pgsql.TRAJ_FEATURE) as stab:
        return int(stab.fetch_one(sql)[0])


@desc()
def fill_encrypted_user_ids(ctx: Context, proceed):
    """
    填充加密用户 id
    """
    batch_size = 10000
    last_id = 0
    sql = '''
        select id, user_id
        from parking_points
        where id > %s and 
        (
            (parking_type = 'start_car' AND ABS(CAST(parking_time AS FLOAT)) >= 0) OR
            (parking_type = 'end_car' AND ABS(CAST(parking_time AS FLOAT)) >= 30) OR
            (parking_type = 'mid_stop_car' AND ABS(CAST(parking_time AS FLOAT)) >= 5)
        )
        order by id
        limit %s;
    '''

    user_id_count = get_user_id_count()
    print(f'There are about {user_id_count} user ids.')
    user_ids = set()

    with (
        PgsqlStabilizer(pgsql.TRAJ_FEATURE, init=True) as stab,
        stab.connection.cursor() as cur,
        tqdm(total=user_id_count) as bar,
    ):
        while True:
            cur.execute(sql, [last_id, batch_size])
            rows = cur.fetchall()
            if not rows:
                break

            for row in rows:
                _, user_id = row
                user_ids.add(user_id)

            # 更新 last_id 为当前批次最后一条记录的 ID
            last_id = rows[-1][0]  # ID 在第 0 列
            bar.update(batch_size)

    ctx.encrypted_user_ids = list(user_ids)
    proceed()


@desc()
def decrypt_user_ids(ctx: Context, proceed):
    """
    解密用户 id
    """
    for user_id in tqdm(ctx.encrypted_user_ids):
        ctx.decrypted_user_ids.append(decrypt_user_id(user_id))

    ctx.encrypted_user_ids.clear()
    proceed()


def get_line_count(path):
    """
    获取文件行数
    """
    with open(path, 'r', encoding='utf-8') as f:
        return sum(1 for _ in f)


@desc()
def fill_code_to_brand_map_offline(ctx: Context, proceed):
    """
    离线填充 code 到 brand 映射关系
    """
    valid_user_id_length = 2

    for decrypted_user_id in tqdm(ctx.decrypted_user_ids):
        decrypted_items = decrypted_user_id.split('_')
        if len(decrypted_items) != valid_user_id_length:
            continue

        brand_code = decrypted_items[0]
        if brand_code in ctx.history_brand_codes:
            continue

        device_id = decrypted_items[1]

        if device_id not in ctx.device_to_brand_map:
            ctx.online_device_infos.append((brand_code, device_id))
        else:
            brand = ctx.device_to_brand_map[device_id]
            ctx.code_to_brand_map[brand_code] = brand

    proceed()


@desc()
def upload_code_to_brand_map(ctx: Context, proceed):
    """
    上传 code 到 brand 映射关系
    """
    sql = '''
        insert into cdz_brand(code, brand) 
        values(%s, %s)
        on conflict (code) do nothing;
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab,
        stab.connection.cursor() as cur,
    ):
        try:
            for code, brand in ctx.code_to_brand_map.items():
                cur.execute(sql, (code, brand))

            stab.connection.commit()
        except Exception as e:
            stab.connection.rollback()
            print(e)
            raise e

    proceed()


@desc()
def upload_online_device_infos(ctx: Context, proceed):
    """
    上传需要走 api 拉取详情的设备信息
    """
    if not any(ctx.online_device_infos):
        proceed()
        return

    now = datetime.datetime.now().strftime('%Y%m%d')
    online_device_infos_path = ctx.work_dir / f'online_device_infos_{now}.csv'
    with open(online_device_infos_path, 'w', encoding='utf-8') as f:
        for brand_code, device_id in ctx.online_device_infos:
            f.write(f'{brand_code}\t{device_id}\n')

    try:
        afs = AfsTool()
        afs.put(str(online_device_infos_path), ctx.afs_path)
        online_device_infos_path.unlink()
    except Exception as e:
        print(e)
        raise e

    proceed()


@desc()
def download_online_device_infos(ctx: Context, proceed):
    """
    下载需要走 api 拉取详情的设备信息
    """
    date = (datetime.date.today() - datetime.timedelta(days=0)).strftime('%Y%m%d')
    file_name = f'online_device_infos_{date}.csv'
    local_path = ctx.work_dir / file_name
    afs_path = f'{ctx.afs_path}/{file_name}'
    download_file_by_afs(afs_path, local_path, 'aries_rpm01')

    if not local_path.exists():
        raise Exception(f'{local_path} does not exist')

    with open(local_path, 'r', encoding='utf-8') as f:
        for line in f.read().splitlines():
            code, device_id = tuple(line.split('\t'))
            ctx.online_device_infos.append((code, device_id))

    local_path.unlink(missing_ok=True)
    proceed()


def parse_brand(response):
    """
    解析品牌
    """
    if response is None:
        return None

    if not response.ok:
        return None

    return response.json()['data']['sys_maker']


@desc()
def fill_code_to_brand_map_online(ctx: Context, proceed):
    """
    通过 api 拉取 code 到 brand 映射关系
    """
    sleep_secs = 0.5
    brand_codes = set()

    for brand_code, device_id in tqdm(ctx.online_device_infos):
        if brand_code in brand_codes:
            continue

        brand_codes.add(brand_code)
        params = {
            'device_no': device_id,
        }
        try:
            response = requests.get(ONLINE_DEVICE_API, params=params)
            brand = parse_brand(response)
            ctx.code_to_brand_map[brand_code] = brand
        except:
            pass
        finally:
            time.sleep(sleep_secs)

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        default='offline',
        required=False,
    )
    return parser.parse_args()


def create_pipeline(mode):
    """
    创建管道
    """
    if mode == 'offline':
        return pipeline.Pipeline(
            try_download_offline_device_infos,
            fill_device_to_brand_map,
            fill_history_brand_codes,
            fill_encrypted_user_ids,
            decrypt_user_ids,
            fill_code_to_brand_map_offline,
            upload_code_to_brand_map,
            upload_online_device_infos,
        )
    elif mode == 'online':
        return pipeline.Pipeline(
            download_online_device_infos,
            fill_code_to_brand_map_online,
            upload_code_to_brand_map,
        )

    raise ValueError(f'Unknown mode: {mode}')


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行入库轨迹电车品牌映射脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args.mode)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_fetch_track_brand'),
        afs_path='/user/map-data-streeview/aoi-ml/cdz',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main(parse_args())
