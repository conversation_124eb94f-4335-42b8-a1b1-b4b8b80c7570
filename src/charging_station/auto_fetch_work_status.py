# !/usr/bin/env python3
"""
例行获取人工作业状态
"""
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path

from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv, notice_tool

desc = pipeline.get_desc()


@dataclass
class UpdateRecord:
    """
    更新批次作业记录
    """
    id: int
    ptid: str
    is_finished: int = 0
    work_conclusion: int = -1


@dataclass
class CdcRecord:
    """
    cdc 批次作业记录
    """
    ptid: str
    batch_id: str
    is_finished: int
    work_conclusion: int
    start_time: datetime
    can_process: bool = True
    reason: str = ""


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    update_records: list[UpdateRecord] = field(default_factory=list)
    cdc_records: list[CdcRecord] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_update_records(ctx: Context, proceed):
    """
    加载更新批次作业记录
    """
    sql = '''
        select id, ptid 
        from cdz_ticket 
        where batch != '' and
              ptid is not null and
              mode = 'formal' and
              is_finished = 0;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for row in tqdm(stab.fetch_all(sql)):
            record_id, ptid = row
            ctx.update_records.append(UpdateRecord(
                id=record_id,
                ptid=ptid,
            ))

    proceed()


@desc()
def fill_update_work_status(ctx: Context, proceed):
    """
    填充更新批次作业状态
    """
    max_qps = 5
    batch_size = 1000
    total_records = len(ctx.update_records)
    sql = '''
        select wpi.is_finished, pa.work_conclusion
        from work_process_instance as wpi
        
        left join pro_achievement as pa
        on wpi.ptid = pa.ptid
        
        where wpi.ptid = %s;
    '''

    for i in range(0, total_records, batch_size):
        batch = ctx.update_records[i:i + batch_size]

        with (
            get_mysql_connection("poi_platform") as conn,
            conn.cursor() as cur,
        ):
            for record in tqdm(batch, desc=f"Batch {i // batch_size + 1}"):
                try:
                    cur.execute(sql, [int(record.ptid)])
                    if row := cur.fetchone():
                        record.is_finished, record.work_conclusion = row
                    time.sleep(1 / max_qps)
                except Exception as e:
                    print(f"Error on ptid {record.ptid}: {e}")

    proceed()


@desc()
def save_update_work_status_to_db(ctx: Context, proceed):
    """
    保存更新批次作业状态到数据库
    """
    sql = '''
        update cdz_ticket
        set is_finished = %s, work_conclusion = %s
        where id = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab:
        stab.connection.autocommit = False
        with stab.connection.cursor() as cur:
            try:
                for record in tqdm([x for x in ctx.update_records if x.is_finished == 1]):
                    cur.execute(sql, [
                        record.is_finished,
                        record.work_conclusion,
                        record.id,
                    ])

                stab.connection.commit()
            except Exception as e:
                print(e)
                stab.connection.rollback()
                raise e

    proceed()


@desc()
def save_update_records(ctx: Context, proceed):
    """
    保存更新批次作业记录
    """
    tsv.write_tsv(
        ctx.work_dir / "output_update.csv",
        [
            [
                x.id,
                x.ptid,
                x.is_finished,
                x.work_conclusion,
            ]
            for x in ctx.update_records
        ]
    )
    proceed()


@desc()
def load_cdc_records(ctx: Context, proceed):
    """
    加载 cdc 批次作业记录
    """
    max_days = 30
    max_qps = 5
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=max_days)
    sql = f"""
        select wpi.ptid, wpi.batch_id, wpi.start_time, wpi.is_finished, pa.work_conclusion
        from work_process_instance as wpi
        left join pro_achievement as pa
        on wpi.ptid = pa.ptid
        where wpi.start_time >= %s and
              wpi.start_time < %s and
              wpi.batch_id like '%%CDCJSK%%' and
              wpi.project = 'CDC';
    """

    with (
        get_mysql_connection("poi_platform") as conn,
        conn.cursor() as cur,
    ):
        for i in tqdm(range(max_days + 1)):
            current_date = start_date + timedelta(days=i)
            next_date = current_date + timedelta(days=1)
            cur.execute(sql, [current_date, next_date])

            for ptid, batch_id, start_time, is_finished, work_conclusion in cur.fetchall():
                ctx.cdc_records.append(CdcRecord(
                    ptid=ptid,
                    batch_id=batch_id,
                    start_time=start_time,
                    is_finished=is_finished,
                    work_conclusion=work_conclusion,
                ))

            time.sleep(1 / max_qps)

    proceed()


@desc()
def check_cdc_work_status(ctx: Context, proceed):
    """
    检查 cdc 批次作业状态
    """
    for record in tqdm(ctx.cdc_records):
        if record.is_finished == 1 and record.work_conclusion is None:
            record.can_process = False
            record.reason = 'finished while no conclusion'
            continue

        if record.is_finished == 0 and record.work_conclusion is not None:
            record.can_process = False
            record.reason = 'unfinished but has conclusion'
            continue

    proceed()


@desc()
def save_cdc_work_status_to_db(ctx: Context, proceed):
    """
    保存 cdc 批次作业状态到数据库
    """
    sql = '''
        insert into cdz_ticket (
            bid, batch, src, push_time, ptid,
            project, priority, method, message, mode,
            is_finished, work_conclusion
        )
        values (
            %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, %s, 
            %s, %s
        )
        on conflict (ptid) do nothing;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab:
        stab.connection.autocommit = False
        try:
            with stab.connection.cursor() as cur:
                for record in tqdm([x for x in ctx.cdc_records if x.can_process]):
                    data = [
                        '', record.batch_id, 'create_cdz', record.start_time, record.ptid,
                        'CDC', 5, 'new', '常规工艺', 'formal',
                        record.is_finished, record.work_conclusion if record.work_conclusion is not None else -1
                    ]
                    cur.execute(sql, data)
                stab.connection.commit()
        except Exception as e:
            print('error: ', e)
            stab.connection.rollback()
            raise e

    proceed()


@desc()
def save_cdc_records(ctx: Context, proceed):
    """
    保存 cdc 批次作业记录
    """
    tsv.write_tsv(
        ctx.work_dir / "output_cdc.csv",
        [
            [
                x.ptid,
                x.batch_id,
                x.start_time,
                x.is_finished,
                x.work_conclusion,
                x.can_process,
                x.reason,
            ]
            for x in ctx.cdc_records
        ]
    )
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行获取人工作业状态脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_update_records,
        fill_update_work_status,
        save_update_work_status_to_db,
        save_update_records,

        load_cdc_records,
        check_cdc_work_status,
        save_cdc_work_status_to_db,
        save_cdc_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_fetch_work_status"),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == "__main__":
    main()
