# !/usr/bin/env python3
"""
充电桩成果推送导航
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.afs_tool import AfsTool

SPACE_MAP = {
    '电动汽车充电站': 0,
    '地上充电站': 1,
    '地下充电站': 2,
}

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    推送记录
    """
    bid: str
    relation_bid: str
    name: str
    show_tag: str
    status: int
    wkt: str
    point_x_mercator: float = 0.0
    point_y_mercator: float = 0.0
    is_ground: int = 0
    is_accurate_parking: int = 0
    is_credible: int = 0
    is_public: int = 0
    power: int = -1


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    afs_path: str
    result_file_path: Path = None
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_records(ctx: Context, proceed):
    """
    记载记录
    """
    sql = '''
        select bid, relation_bid, name, show_tag, status, st_astext(geometry)
        from poi
        where std_tag = '交通设施;充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, relation_bid, name, show_tag, status, wkt in tqdm(poi_stab.fetch_all(sql)):
            ctx.records.append(Record(
                bid=bid,
                relation_bid=relation_bid,
                name=name,
                show_tag=show_tag,
                status=status,
                is_ground=SPACE_MAP.get(show_tag, 0),
                wkt=wkt,
            ))

    proceed()


@desc()
def fill_mercator_point(ctx: Context, proceed):
    """
    填充墨卡托坐标
    """
    sql = '''
        select st_astext(gcj2mc(st_geomfromtext(%s, 4326)));
    '''

    with PgsqlStabilizer(pgsql.COMPUTE_CONFIG) as compute_stab:
        for record in tqdm(ctx.records):
            row = compute_stab.fetch_one(sql, (record.wkt,))
            geom = shapely.wkt.loads(row[0])
            record.point_x_mercator = geom.x
            record.point_y_mercator = geom.y

    proceed()


@desc()
def fill_is_accurate_parking(ctx: Context, proceed):
    """
    将父点是精准停车场的记录标记
    """
    sql = '''
        select 1 from park_statistical_data where bid = %s and precise10 = 1;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm(ctx.records):
            if record.relation_bid == '0' or record.relation_bid == '':
                continue

            row = poi_stab.fetch_one(sql, (record.relation_bid,))
            record.is_accurate_parking = 1 if row is not None else 0

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    date = datetime.datetime.now().strftime('%Y%m%d')
    file_name = f'cdz.dat.{date}'
    ctx.result_file_path = ctx.work_dir / file_name

    tsv.write_tsv(
        ctx.result_file_path,
        [
            [
                x.bid,
                x.point_x_mercator,
                x.point_y_mercator,
                x.relation_bid,
                x.is_ground,
                x.is_accurate_parking,
                x.is_credible,
                x.is_public,
                x.power,
                x.status,
                0,
            ]
            for x in ctx.records
        ]
    )
    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传记录
    """
    afs = AfsTool('charging_station')
    afs.put(str(ctx.result_file_path), ctx.afs_path)

    # 暂时不清空
    # finally:
    #     ctx.result_file_path.unlink(missing_ok=True)

    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'充电桩成果推送导航脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        fill_mercator_point,
        fill_is_accurate_parking,
        save_records,
        upload_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_generate_navigation_risk'),
        afs_path='/user/lbs-poi/leida/ev_output/cdz_daily',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
