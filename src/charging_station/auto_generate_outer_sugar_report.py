# !/usr/bin/env python3
"""
充电站外部指标统计
"""
import argparse
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, notice_tool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    成果记录
    """
    bid: str
    name: str
    address: str
    relation_bid: str
    show_tag: str
    status: int
    telephone: str
    wkt: str
    online_type: int


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    today: datetime.date
    today_table_name: str
    yesterday_table_name: str
    yesterday_records: list[Record] = field(default_factory=list)
    today_records: list[Record] = field(default_factory=list)
    same_bids: set[str] = field(default_factory=set)
    statistics_data: dict[str, float] = field(default_factory=dict)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def __getitem__(self, key):
        return self.statistics_data.get(key, 0)

    def __setitem__(self, key, value):
        self.statistics_data[key] = value


def load_records(table_name):
    """
    加载成果
    """
    sql = f'''
        select bid, address, relation_bid, name, show_tag, status, online_type, telephone, st_astext(geometry)
        from {table_name};
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for item in tqdm(poi_stab.fetch_all(sql)):
            bid, address, relation_bid, name, show_tag, status, online_type, telephone, wkt = item
            yield Record(
                bid=bid,
                name=name,
                address=address,
                relation_bid=relation_bid,
                show_tag=show_tag,
                status=status,
                telephone=telephone,
                wkt=wkt,
                online_type=online_type,
            )


@desc()
def load_yesterday_records(ctx: Context, proceed):
    """
    加载昨天的成果
    """
    ctx.yesterday_records = list(load_records(ctx.yesterday_table_name))
    proceed()


@desc()
def load_today_records(ctx: Context, proceed):
    """
    加载今天的成果
    """
    ctx.today_records = list(load_records(ctx.today_table_name))
    proceed()


@desc()
def fill_new_and_delete_count(ctx: Context, proceed):
    """
    填充新增和删除数量
    """
    old_bids = set(record.bid for record in ctx.yesterday_records)
    new_bids = set(record.bid for record in ctx.today_records)

    ctx['new_count'] = len(new_bids - old_bids)
    ctx['delete_count'] = len(old_bids - new_bids)
    ctx.same_bids = old_bids & new_bids

    proceed()


@desc()
def fill_update_count(ctx: Context, proceed):
    """
    填充更新数量
    """
    today_records_map = {record.bid: record for record in ctx.today_records if record.bid in ctx.same_bids}
    yesterday_records_map = {record.bid: record for record in ctx.yesterday_records if record.bid in ctx.same_bids}

    for today_record in today_records_map.values():
        yesterday_record = yesterday_records_map[today_record.bid]

        is_same_name = today_record.name == yesterday_record.name
        is_same_address = today_record.address == yesterday_record.address
        is_same_wkt = shapely.wkt.loads(today_record.wkt) == shapely.wkt.loads(yesterday_record.wkt)
        is_same_status = today_record.status == yesterday_record.status
        is_same_show_tag = today_record.show_tag == yesterday_record.show_tag
        is_same_telephone = today_record.telephone == yesterday_record.telephone
        is_same_relation_bid = today_record.relation_bid == yesterday_record.relation_bid
        is_same_online_type = today_record.online_type == yesterday_record.online_type

        ctx['update_name_count'] += 0 if is_same_name else 1
        ctx['update_address_count'] += 0 if is_same_address else 1
        ctx['update_wkt_count'] += 0 if is_same_wkt else 1
        ctx['update_status_count'] += 0 if is_same_status else 1
        ctx['update_show_tag_count'] += 0 if is_same_show_tag else 1
        ctx['update_telephone_count'] += 0 if is_same_telephone else 1
        ctx['update_relation_bid_count'] += 0 if is_same_relation_bid else 1
        ctx['update_online_type_count'] += 0 if is_same_online_type else 1

    proceed()


@desc()
def print_sugar_data(ctx: Context, proceed):
    """
    打印统计数据
    """
    for name, value in ctx.statistics_data.items():
        print(f'{name}: {value}')

    proceed()


@desc()
def upload_sugar_data(ctx: Context, proceed):
    """
    上传统计数据
    """
    delete_sql = '''
        delete from cdz_statistic where date = (DATE %s) and name in %s;
    '''
    insert_sql = '''
        insert into cdz_statistic(name, value, date) values (%s, %s, %s);
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        today = ctx.today.strftime('%Y-%m-%d')
        poi_stab.execute(delete_sql, [today, tuple(ctx.statistics_data.keys())])

        try:
            for name, value in ctx.statistics_data.items():
                cur.execute(insert_sql, [name, value, today])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['normal', 'repeat'],
        default='normal',
        required=False,
    )
    return parser.parse_args()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'充电站外部指标统计脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_yesterday_records,
        load_today_records,
        fill_new_and_delete_count,
        fill_update_count,
        print_sugar_data,
        upload_sugar_data,
    )
    desc.attach(main_pipe)
    offset = 0 if args.mode == 'normal' else 1
    today = datetime.date.today() - datetime.timedelta(days=offset)
    yesterday = today - datetime.timedelta(days=1)
    ctx = Context(
        work_dir=Path('cache/auto_generate_outer_sugar_report'),
        today=today,
        today_table_name=f'cdz_history_{today:%Y%m%d}',
        yesterday_table_name=f'cdz_history_{yesterday:%Y%m%d}',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main(parse_args())
