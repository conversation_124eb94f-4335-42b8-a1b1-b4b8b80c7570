# !/usr/bin/env python3
"""
充电站指标统计
"""
import argparse
import datetime
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import POI_STATUS_MAP
from src.charging_station.helper import (
    BnsExecutor, TRUSTY_THIRD_NAMES,
    get_leading_users, get_small_and_medium_users, get_aggregation_users, get_automaker_users,
    get_paying_users, get_closed_users,
)
from src.tools import pipeline, pgsql, notice_tool

desc = pipeline.get_desc()
bns_executor = BnsExecutor()


@dataclass
class Record:
    """
    批处理记录
    """
    bid: str
    city: str
    std_tag: str
    click_pv: int
    relation_bid: str
    name: str
    address: str
    show_tag: str
    status: int
    status_display: str
    wkt: str


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    today: datetime.date
    result_file_path: Path = None
    records: list[Record] = field(default_factory=list)
    column_names: list[str] = field(default_factory=list)
    statistics_data: dict[str, float] = field(default_factory=dict)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        self.leading_users = get_leading_users()
        self.small_and_medium_users = get_small_and_medium_users()
        self.aggregation_users = get_aggregation_users()
        self.automaker_users = get_automaker_users()
        self.paying_users = get_paying_users()
        self.closed_users = get_closed_users()

    def __getitem__(self, key):
        return self.statistics_data.get(key, 0)

    def __setitem__(self, key, value):
        self.statistics_data[key] = value

    def get_users(self, user_type: str):
        if user_type == 'leading':
            return self.leading_users
        elif user_type == 'small_and_medium':
            return self.small_and_medium_users
        elif user_type == 'aggregation':
            return self.aggregation_users
        elif user_type == 'automaker':
            return self.automaker_users
        elif user_type == 'paying':
            return self.paying_users
        elif user_type == 'closed':
            return self.closed_users


@desc()
def load_records(ctx: Context, proceed):
    """
    加载批处理记录
    """
    sql = '''
        select bid, city, std_tag, address, click_pv, relation_bid, name, show_tag, status, st_astext(geometry)
        from poi
        where std_tag = '交通设施;充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for item in tqdm(poi_stab.fetch_all(sql)):
            bid, city, std_tag, address, click_pv, relation_bid, name, show_tag, status, wkt = item
            ctx.records.append(Record(
                bid=bid,
                city=city,
                std_tag=std_tag,
                address=address,
                click_pv=click_pv,
                relation_bid=relation_bid,
                name=name,
                show_tag=show_tag,
                status=status,
                status_display=POI_STATUS_MAP.get(status),
                wkt=wkt,
            ))

    proceed()


@desc()
def fill_spacing_scale_count(ctx: Context, proceed):
    """
    填充空间属性规模
    """
    ctx['total_count'] = len(ctx.records)
    ctx['spacing_ground_count'] = len([x for x in ctx.records if x.show_tag == '地上充电站'])
    ctx['spacing_underground_count'] = len([x for x in ctx.records if x.show_tag == '地下充电站'])
    ctx['spacing_structure_count'] = len([x for x in ctx.records if x.show_tag == '停车楼充电站'])
    ctx['spacing_others_count'] = (
            ctx['total_count'] -
            ctx['spacing_ground_count'] -
            ctx['spacing_underground_count'] -
            ctx['spacing_structure_count']
    )

    proceed()


@desc()
def fill_scenic_scale_count(ctx: Context, proceed):
    """
    填充场景规模
    """
    service_zone = "服务区"
    sql = """
        select thirdcode, station_name from charging_station where bid != ''; 
    """

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql)

        for third_code, station_name in tqdm(cur.fetchall()):
            if third_code in ctx.leading_users:
                ctx['scenic_leading_count'] += 1
            if third_code in ctx.small_and_medium_users:
                ctx['scenic_small_and_medium_count'] += 1
            if third_code in ctx.aggregation_users:
                ctx['scenic_aggregation_count'] += 1
            if third_code in ctx.automaker_users:
                ctx['scenic_automaker_count'] += 1
            if third_code in ctx.paying_users:
                ctx['scenic_paying_count'] += 1
            if third_code in ctx.closed_users:
                ctx['scenic_closed_count'] += 1
            if service_zone in station_name:
                ctx['scenic_service_zone_count'] += 1

    proceed()


@desc()
def fill_feedback_count(ctx: Context, proceed):
    """
    填充用户反馈数量
    """
    low_stars = 1
    date = (ctx.today - datetime.timedelta(days=2)).strftime('%Y%m%d')  # 负反馈一般延迟 2 天
    sql = '''
        select stars, input_re
        from feedback_info
        where event_day = %s and
              tag_child = '充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        all_stars = [(stars, comment) for stars, comment in poi_stab.fetch_all(sql, [date])]
        negative_stars = [(stars, comment) for stars, comment in all_stars if stars == low_stars and comment != '']
        ctx['feedback_count'] = len(all_stars)
        ctx['negative_feedback_count'] = len(negative_stars)
        ctx['negative_feedback_ratio'] = safe_divide(len(negative_stars), len(all_stars))

    proceed()


@desc()
def fill_comment_count(ctx: Context, proceed):
    """
    填充用户评论数量
    """
    flow_result_negative = 1
    date = (ctx.today - datetime.timedelta(days=3)).strftime('%Y%m%d')
    sql = '''
        select flow_result
        from cdz_comment
        where created_time::DATE = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        all_results = [flow_result for flow_result, in poi_stab.fetch_all(sql, [date])]
        negative_results = [result for result in all_results if result == flow_result_negative]
        ctx['comment_count'] = len(all_results)
        ctx['negative_comment_count'] = len(negative_results)
        ctx['negative_comment_ratio'] = safe_divide(len(negative_results), len(all_results))

    proceed()


@desc()
def fill_trusty_count(ctx: Context, proceed):
    """
    填充高准 TP 数量
    """
    sql = """
        select bid, thirdname
        from charging_station;
    """

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql)
        trusty_count = 0
        trusty_online_count = 0

        for bid, third_name in cur.fetchall():
            if third_name not in TRUSTY_THIRD_NAMES:
                continue

            trusty_count += 1

            if bid != '':
                trusty_online_count += 1

        ctx['trusty_count'] = trusty_count
        ctx['trusty_online_count'] = trusty_online_count
        ctx['trusty_online_ratio'] = safe_divide(trusty_online_count, trusty_count)

    proceed()


def calculate_avg_duration(rows, users):
    """
    计算平均时长
    """
    durations = []

    for third_code, duration in rows:
        if third_code not in users:
            continue

        if duration < 0:
            continue

        durations.append(duration)

    return 0 if not durations else sum(durations) / len(durations)


@desc()
def fill_online_duration(ctx: Context, proceed):
    """
    填充上线时长
    """
    sql = """
        select thirdcode, TIMESTAMPDIFF(second, create_time, online_ctime) / 3600 / 24 
        from charging_station 
        where bid != '' and 
              create_time > DATE_SUB(%s, INTERVAL 7 DAY);
    """

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql, [ctx.today.strftime("%Y%m%d")])
        rows = cur.fetchall()

        ctx['scenic_leading_duration'] = calculate_avg_duration(rows, ctx.leading_users)
        ctx['scenic_small_and_medium_duration'] = calculate_avg_duration(rows, ctx.small_and_medium_users)
        ctx['scenic_aggregation_duration'] = calculate_avg_duration(rows, ctx.aggregation_users)
        ctx['scenic_automaker_duration'] = calculate_avg_duration(rows, ctx.automaker_users)
        ctx['scenic_paying_duration'] = calculate_avg_duration(rows, ctx.paying_users)
        ctx['scenic_closed_duration'] = calculate_avg_duration(rows, ctx.closed_users)

    proceed()


def fill_scenic_n_days_count(ctx: Context, rows, day_count, user_type):
    """
    填充指定用户类型的 T+n 统计数据
    """
    for bid, third_name, third_code in rows:
        if third_code not in ctx.get_users(user_type):
            continue

        ctx[f'scenic_{user_type}_{day_count}_days_count'] += 1

        if bid == '':
            continue

        ctx[f'scenic_{user_type}_{day_count}_days_online_count'] += 1

    ctx[f'scenic_{user_type}_{day_count}_days_online_ratio'] = safe_divide(
        ctx[f'scenic_{user_type}_{day_count}_days_online_count'],
        ctx[f'scenic_{user_type}_{day_count}_days_count']
    )


def fill_other_n_days_count(ctx: Context, rows, day_count):
    """
    填充其他用户类型的 T+n 统计数据
    """
    for bid, third_name, third_code in rows:
        ctx[f'total_{day_count}_days_count'] += 1
        ctx[f'total_{day_count}_days_online_count'] += 1 if bid != '' else 0

        ctx[f'trusty_{day_count}_days_count'] += 1 if third_name in TRUSTY_THIRD_NAMES else 0
        ctx[f'trusty_{day_count}_days_online_count'] += 1 if third_name in TRUSTY_THIRD_NAMES and bid != '' else 0

    ctx[f'total_{day_count}_days_online_ratio'] = safe_divide(
        ctx[f'total_{day_count}_days_online_count'],
        ctx[f'total_{day_count}_days_count']
    )
    ctx[f'trusty_{day_count}_days_online_ratio'] = safe_divide(
        ctx[f'trusty_{day_count}_days_online_count'],
        ctx[f'trusty_{day_count}_days_count']
    )


def __fill_n_days_count(ctx: Context, day_count):
    """
    填充 T+n 统计数据
    """
    sql = f'''
        select bid, thirdname, thirdcode
        from charging_station
        where DATE(create_time) = DATE_SUB(%s, INTERVAL {day_count + 1} DAY);
    '''

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql, [ctx.today.strftime("%Y%m%d")])
        rows = cur.fetchall()

        fill_scenic_n_days_count(ctx, rows, day_count, 'leading')
        fill_scenic_n_days_count(ctx, rows, day_count, 'small_and_medium')
        fill_scenic_n_days_count(ctx, rows, day_count, 'aggregation')
        fill_scenic_n_days_count(ctx, rows, day_count, 'automaker')
        fill_scenic_n_days_count(ctx, rows, day_count, 'paying')
        fill_scenic_n_days_count(ctx, rows, day_count, 'closed')
        fill_other_n_days_count(ctx, rows, day_count)


@desc()
def fill_n_days_count(ctx: Context, proceed):
    """
    填充 T+n 统计数据
    """
    __fill_n_days_count(ctx, 3)
    __fill_n_days_count(ctx, 7)

    proceed()


@desc()
def fill_competitor_count(ctx: Context, proceed):
    """
    填充竞品抓取数据
    """
    request_date = (ctx.today - datetime.timedelta(days=2)).strftime('%Y%m%d')
    download_date = (ctx.today - datetime.timedelta(days=1)).strftime('%Y%m%d')
    get_total_count_sql = '''
        select count(distinct competitor_id) 
        from cdz_competitor
        where crawl_time >= extract(epoch from (now() - interval '1 months'))::integer;
    '''
    get_crawl_count_sql = '''
        select count(*) from cdz_crawl_history where crawled_time::DATE = %s;
    '''
    get_result_count_sql = '''
        select count(distinct competitor_id) 
        from cdz_competitor 
        where to_char(to_timestamp(crawl_time), 'yyyymmdd') = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        ctx['competitor_count'] = poi_stab.fetch_one(get_total_count_sql)[0]
        ctx['competitor_crawl_request_count'] = poi_stab.fetch_one(get_crawl_count_sql, [request_date])[0]
        ctx['competitor_crawl_result_count'] = poi_stab.fetch_one(get_result_count_sql, [download_date])[0]

    proceed()


@desc()
def fill_other_data(ctx: Context, proceed):
    """
    填充统计数据
    """
    get_zhongyuan_cluster_count_sql = '''
        select count(*) from cdz_zhongyuan_cluster_polygon;
    '''
    get_picture_sql = '''
        select 1 from cdz_picture where bid = %s limit 1;
    '''

    ctx['total_click_pv'] = sum([x.click_pv for x in ctx.records])

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        ctx['zhongyuan_cluster_count'] = poi_stab.fetch_one(get_zhongyuan_cluster_count_sql)[0]

        for record in tqdm(ctx.records):
            row = poi_stab.fetch_one(get_picture_sql, [record.bid])
            if row is not None:
                ctx['picture_count'] += 1

    proceed()


@desc()
def print_sugar_data(ctx: Context, proceed):
    """
    打印统计数据
    """
    for name, value in ctx.statistics_data.items():
        print(f'{name}: {value}')

    proceed()


@desc()
def upload_sugar_data(ctx: Context, proceed):
    """
    上传统计数据
    """
    delete_sql = '''
        delete from cdz_statistic where date = (DATE %s) and name in %s;
    '''
    insert_sql = '''
        insert into cdz_statistic(name, value, date) values (%s, %s, %s);
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        today = ctx.today.strftime('%Y-%m-%d')
        poi_stab.execute(delete_sql, [today, tuple(ctx.statistics_data.keys())])

        try:
            for name, value in ctx.statistics_data.items():
                cur.execute(insert_sql, [name, value, today])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


def safe_divide(numerator, denominator):
    """
    安全除法
    """
    if denominator == 0:
        return 0
    else:
        return numerator / denominator


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'充电站指标统计脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['normal', 'repeat'],
        default='normal',
        required=False,
    )
    parser.add_argument(
        '--upload',
        dest='upload',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_records,
        fill_spacing_scale_count,
        fill_scenic_scale_count,
        fill_feedback_count,
        fill_comment_count,
        fill_trusty_count,
        fill_online_duration,
        fill_n_days_count,
        fill_competitor_count,
        fill_other_data,
        print_sugar_data,
    ]

    if args.upload:
        pipes.append(upload_sugar_data)

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    offset = 0 if args.mode == 'normal' else 1
    ctx = Context(
        work_dir=Path('cache/auto_generate_sugar_report'),
        today=datetime.date.today() - datetime.timedelta(days=offset),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main(parse_args())
