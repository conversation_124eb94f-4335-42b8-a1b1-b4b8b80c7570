# !/usr/bin/env python3
"""
例行入库众源聚面
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import numpy as np
import shapely.wkt
from shapely import MultiPoint, GeometryCollection, Polygon
from sklearn.cluster import DBSCAN
from tqdm import tqdm

from src.batch_process.batch_helper import batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.fix_geom_process import tools
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.traj_helper import decrypt_user_id

DESIRED_BRANDS = {'t3', 'caocao'}

desc = pipeline.get_desc()


@dataclass
class ClusterRecord:
    """
    聚面信息
    """
    geom: Polygon
    overlap_road_ratio: float = 0
    total_user_count: int = 0
    new_energy_user_count: int = 0
    new_energy_user_ratio: float = 0
    can_process: bool = True
    reason: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    table_name: str
    new_energy_points: list[str] = field(default_factory=list)
    cluster_records: list[ClusterRecord] = field(default_factory=list)
    brands: dict[int, str] = field(default_factory=dict)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fill_brands(ctx: Context, proceed):
    """
    填充品牌信息
    """
    sql = '''
        select code, brand from cdz_brand;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for code, brand in stab.fetch_all(sql):
            ctx.brands[code] = brand

    proceed()


def is_new_energy_brand(ctx, decrypted_user_id):
    """
    判断是否是新能源品牌
    """
    valid_user_id_length = 2

    decrypted_items = decrypted_user_id.split('_')
    if len(decrypted_items) != valid_user_id_length:
        return False

    brand_code = decrypted_items[0]
    if not str.isdigit(brand_code):
        return False

    brand = ctx.brands.get(int(brand_code), None)
    if brand is None:
        return False

    if brand not in DESIRED_BRANDS:
        return False

    return True


def extract_new_energy_points(ctx, rows):
    """
    提取新能源轨迹点
    """
    encrypted_data_map = {}
    new_energy_points = []

    for _, encrypted_user_id, wkt in rows:
        encrypted_data = encrypted_data_map.get(encrypted_user_id, [])
        encrypted_data.append(wkt)
        encrypted_data_map[encrypted_user_id] = encrypted_data

    for encrypted_user_id in encrypted_data_map.keys():
        decrypted_user_id = decrypt_user_id(encrypted_user_id)

        if not is_new_energy_brand(ctx, decrypted_user_id):
            continue

        new_energy_points.extend(encrypted_data_map[encrypted_user_id])

    return new_energy_points


def get_user_id_count():
    """
    获取用户 id 数量
    """
    sql = '''
        SELECT reltuples::BIGINT AS approximate_row_count
        FROM pg_class
        WHERE relname = 'parking_points';
    '''

    with PgsqlStabilizer(pgsql.TRAJ_FEATURE) as stab:
        return int(stab.fetch_one(sql)[0])


@desc()
def load_new_energy_points(ctx: Context, proceed):
    """
    加载新能源轨迹点
    """
    batch_size = 1000000
    last_id = 0
    sql = '''
        select id, user_id, st_astext(geom)
        from parking_points
        where id > %s and 
        (
            (parking_type = 'start_car' AND ABS(CAST(parking_time AS FLOAT)) >= 0) OR
            (parking_type = 'end_car' AND ABS(CAST(parking_time AS FLOAT)) >= 30) OR
            (parking_type = 'mid_stop_car' AND ABS(CAST(parking_time AS FLOAT)) >= 5)
        )
        order by id
        limit %s;
    '''

    user_id_count = get_user_id_count()
    print(f'There are about {user_id_count} user ids.')

    with (
        PgsqlStabilizer(pgsql.TRAJ_FEATURE, init=True) as stab,
        stab.connection.cursor() as cur,
        tqdm(total=user_id_count) as bar,
    ):
        while True:
            cur.execute(sql, [last_id, batch_size])
            rows = cur.fetchall()
            if not rows:
                break

            ctx.new_energy_points.extend(extract_new_energy_points(ctx, rows))
            # 更新 last_id 为当前批次最后一条记录的 ID
            last_id = rows[-1][0]  # ID 在第 0 列
            bar.update(batch_size)

    if not ctx.new_energy_points:
        print('no new energy points found')
        return

    print(f'total count: {len(ctx.new_energy_points)}')
    proceed()


@desc()
def cluster_points(ctx: Context, proceed):
    """
    聚合轨迹点
    """
    min_area = 20e-10
    multipoint = MultiPoint([shapely.wkt.loads(x) for x in ctx.new_energy_points])
    points_arr = np.array([point.coords[0] for point in multipoint.geoms])
    # noinspection PyUnresolvedReferences
    labels = DBSCAN(eps=10e-5, min_samples=5, algorithm='kd_tree').fit(points_arr).labels_
    clustered_points = {}

    for label, point in zip(labels, multipoint.geoms):
        if label == -1:
            continue

        points_group = clustered_points.get(label, [])
        points_group.append(point)
        clustered_points[label] = points_group

    for label, points in tqdm(clustered_points.items(), total=len(clustered_points)):
        clustered_geom = MultiPoint(points).convex_hull

        if clustered_geom.area < min_area or clustered_geom.geom_type != 'Polygon':
            continue

        ctx.cluster_records.append(ClusterRecord(
            geom=clustered_geom,
        ))

    proceed()


def get_outer_roads(road_stab, wkt):
    """
    获取高等级道路
    """
    sql = '''
        select link_id, dir, kind, lane_l, lane_r, form, st_astext(geom) 
        from nav_link
        where kind < 8 and 
              st_intersects(st_buffer(st_geomfromtext(%s, 4326), 20e-5), geom);
    '''
    ret = road_stab.fetch_all(sql, (wkt,))
    return [(*x[:-1], shapely.wkt.loads(x[-1])) for x in ret]


def buffer_roads(roads):
    """
    对道路线进行外扩形成面
    """
    buffered_roads = []
    for _, direction, kind, lane_l, lane_r, form, geom in roads:
        line_width = tools.get_road_width(direction, kind, lane_l, lane_r, form)
        line_buffer = line_width * 1e-5 / 2
        line = geom.buffer(line_buffer)
        buffered_roads.append(line)

    return GeometryCollection(buffered_roads).buffer(0)


@desc()
def filter_records_by_road(ctx: Context, proceed):
    """
    通过品牌过滤记录
    """
    max_ratio = 0.5

    with PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab:
        def process(record: ClusterRecord):
            roads = get_outer_roads(road_stab, record.geom.wkt)
            buffered_roads = buffer_roads(roads)
            diff_geom = record.geom.difference(buffered_roads)
            record.overlap_road_ratio = 1 - diff_geom.area / record.geom.area
            if record.overlap_road_ratio > max_ratio:
                record.can_process = False
                record.reason = f'overlap road too much'

        batch_process(ctx.cluster_records, process)

    proceed()


@desc()
def filter_records_by_user(ctx: Context, proceed):
    """
    通过用户量过滤记录
    """
    min_user_id_count = 3
    min_new_energy_ratio = 0.5
    sql = '''
        select user_id
        from parking_points
        where st_intersects(st_geomfromtext(%s, 4326), geom)
    '''

    with PgsqlStabilizer(pgsql.TRAJ_FEATURE) as traj_stab:
        def process(record: ClusterRecord):
            encrypted_user_ids = set(x[0] for x in traj_stab.fetch_all(sql, (record.geom.wkt,)))
            record.total_user_count = len(encrypted_user_ids)

            if len(encrypted_user_ids) < min_user_id_count:
                record.can_process = False
                record.reason = f'not enough users'
                return

            new_energy_count = 0
            for encrypted_user_id in encrypted_user_ids:
                decrypted_user_id = decrypt_user_id(encrypted_user_id)
                if is_new_energy_brand(ctx, decrypted_user_id):
                    new_energy_count += 1

            record.new_energy_user_count = new_energy_count
            record.new_energy_user_ratio = new_energy_count / len(encrypted_user_ids)
            if record.new_energy_user_ratio < min_new_energy_ratio:
                record.can_process = False
                record.reason = f'not enough new energy brand'

        batch_process(ctx.cluster_records, process)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存批处理记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.geom.wkt,
                x.new_energy_user_count,
                x.total_user_count,
                f'{x.overlap_road_ratio:.2f}',
                f'{x.new_energy_user_ratio:.2f}',
            ]
            for x in ctx.cluster_records if x.can_process
        ]
    )

    proceed()


def create_table(ctx: Context):
    """
    创建数据库表
    """
    create_table_sql = f'''
        create table if not exists {ctx.table_name}(
            id SERIAL PRIMARY KEY,
            center_geom geometry(POINT, 4326),
            region_geom geometry(POLYGON, 4326)
        );
    '''
    clear_table_sql = f'''delete from {ctx.table_name};'''
    create_index_sql = f'''
        create index if not exists {ctx.table_name}_center_geom_index 
        on {ctx.table_name} 
        using gist (center_geom);
        
        create index if not exists {ctx.table_name}_region_geom_index 
        on {ctx.table_name} 
        using gist (region_geom);
        
        analyze {ctx.table_name};
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(create_table_sql)
        poi_stab.execute(clear_table_sql)
        poi_stab.execute(create_index_sql)


def update_view(ctx: Context):
    """
    更新数据库视图
    """
    create_view_sql = f'''
        create or replace view cdz_zhongyuan_cluster_polygon as
        select * from {ctx.table_name};
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(create_view_sql)


def drop_table(table_prefix: str, expire_day: int):
    """
    删除过期数据库表
    """
    sql = f"""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name LIKE '{table_prefix}_%';
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    tables = [(x[0], x[0].split("_")[-1]) for x in ret]
    tables = [(table_name, datetime.datetime.strptime(day, "%Y%m%d")) for table_name, day in tables]

    today = datetime.datetime.now()
    expire = today - datetime.timedelta(days=expire_day)
    expired_tables = [table_name for table_name, day in tables if day < expire]
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for table_name in expired_tables:
            # NOTE: 此处不要把 {table_name} 写成 %s 的形式，pgsql 会把它当成字符串，加上引号，导致 sql 语法错误
            sql_clear = f"""
                drop table if exists {table_name};
            """
            pgsql.execute(conn, sql_clear)

    return expired_tables


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传批处理记录到数据库
    """
    sql = f'''
        insert into {ctx.table_name}(center_geom, region_geom)
        values(st_geomfromtext(%s, 4326), st_geomfromtext(%s, 4326));
    '''

    create_table(ctx)

    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab:
        poi_stab.connection.autocommit = False

        try:
            with poi_stab.connection.cursor() as cur:
                for record in tqdm([x for x in ctx.cluster_records if x.can_process]):
                    center = record.geom.centroid
                    cur.execute(sql, (center.wkt, record.geom.wkt))

                poi_stab.connection.commit()
        except Exception as e:
            print(e)
            poi_stab.connection.rollback()
            raise e

    update_view(ctx)
    drop_table('cdz_zhongyuan_cluster_polygon', 8)
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行入库众源聚面脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        fill_brands,
        load_new_energy_points,
        cluster_points,
        filter_records_by_road,
        filter_records_by_user,
        save_records,
        upload_records,
    )
    desc.attach(main_pipe)
    date = datetime.date.today().strftime('%Y%m%d')
    ctx = Context(
        work_dir=Path('cache/auto_generate_zhongyuan_cluster_polygon'),
        table_name=f'cdz_zhongyuan_cluster_polygon_{date}',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
