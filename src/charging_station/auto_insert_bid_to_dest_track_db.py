# !/usr/bin/env python3
"""
例行将 bid 插入终点轨迹库
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.helper import alert_to_infoflow
from src.tools import pipeline, pgsql

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    records: list = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载记录
    """
    sql = '''
        select bid, click_pv, std_tag
        from poi
        where std_tag = '交通设施;充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for bid, click_pv, std_tag in tqdm(stab.fetch_all(sql)):
            ctx.records.append((bid, click_pv, std_tag))

    proceed()


@desc()
def save_records_to_db(ctx: Context, proceed):
    """
    保存记录到数据库
    """
    sql = '''
        insert into sync_traj_bid_record (
            bid,
            batch,
            cal_pv,
            std_tag
        )
        values (%s, %s, %s, %s)
        on conflict do nothing;
    '''
    today = datetime.date.today().strftime('%Y%m%d')
    batch = f'charging_station_{today}'

    with pgsql.get_connection(pgsql.TRAJECTORY_CONFIG) as conn:
        conn.autocommit = False
        with conn.cursor() as cur:
            try:
                for bid, click_pv, std_tag in tqdm(ctx.records):
                    cur.execute(sql, (bid, batch, click_pv, std_tag))

                cur.connection.commit()
            except Exception as e:
                cur.connection.rollback()
                print(e)

    proceed()


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        save_records_to_db,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_insert_bid_to_dest_track_db"),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(f'例行将 bid 插入终点轨迹库脚本异常！{e}')


if __name__ == "__main__":
    main()
