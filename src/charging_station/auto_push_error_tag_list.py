# !/usr/bin/env python3
"""
例行推送错误 tag 清单供下游修复
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.tools import pipeline, tsv, notice_tool
from src.tools.afs_tool import AfsTool
from src.tools.file_downloader import download_file_by_http

DOWNLOAD_URL_FORMAT = ('http://***********:8008/offline_quality_check_result/{0}'
                       '/charging_statistic/charging_not_valid.txt')

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    afs_path: str
    date: datetime.date
    check_result_path: Path = None
    intelligence_path: Path = None
    bids: list[str] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def download_check_result(ctx: Context, proceed):
    """
    下载质检结果
    """
    download_url = DOWNLOAD_URL_FORMAT.format(ctx.date.strftime('%Y-%m-%d'))
    ctx.check_result_path = ctx.work_dir / f'result_{ctx.date}.txt'
    download_file_by_http(download_url, ctx.check_result_path)

    proceed()


@desc()
def load_check_result(ctx: Context, proceed):
    """
    加载质检结果
    """
    desired_error_messages = {
        '【充电站】【1】名称和tag一致',
        '【充电站】【2】tag体系一致',
    }

    for data in tqdm(list(tsv.read_tsv(ctx.check_result_path, skip_header=True))):
        bid, charging, click_pv, std_tag, status_code, err_type, err_msg, err_ext, status_message, raw_result = data
        if err_type not in desired_error_messages:
            continue

        ctx.bids.append(bid)

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    ctx.intelligence_path = ctx.work_dir / f"{ctx.date.strftime('%Y%m%d')}.input"
    tsv.write_tsv(ctx.intelligence_path, [[x] for x in ctx.bids])
    proceed()


@desc()
def upload_records(ctx: Context, proceed):
    """
    上传记录
    """
    afs = AfsTool('fenghuang')
    afs.set_shell('/home/<USER>/afs/bin/afsshell')
    afs.put(str(ctx.intelligence_path), ctx.afs_path)

    proceed()


@desc()
def delete_cache_data(ctx: Context, proceed):
    """
    删除缓存数据
    """
    ctx.check_result_path.unlink(missing_ok=True)
    ctx.intelligence_path.unlink(missing_ok=True)
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行推送错误 tag 清单脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        download_check_result,
        load_check_result,
        save_records,
        upload_records,
        delete_cache_data,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_push_error_tag_list'),
        afs_path='/user/map_data_aoi/cdz/qa/tag',
        date=datetime.date.today(),
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
