# !/usr/bin/env python3
"""
例行推送充电站图片
"""
import argparse
import json
import time
import uuid
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

import requests
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection, batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin as auto_repair_mixin
from src.charging_station.data import TpRecord, Poi
from src.charging_station.formatters.picture_formatter import format_picture
from src.charging_station.helper import BnsExecutor
from src.charging_station.picture_filter import filter_before_online
from src.tools import pipeline, pgsql, tsv, notice_tool

BNS_HOST = 'group.opera-online-PoiRichDataProcess-RichDataProcess-all.map-poi.all'
MAX_BATCH_SIZE = 1000

desc = pipeline.get_desc()
bns_executor = BnsExecutor(bns_name=BNS_HOST, auto_retry=False)


@dataclass
class Record:
    """
    推送记录
    """
    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)

    # 业务逻辑
    picture_text: str = ''
    picture_urls: list[str] = field(default_factory=list)
    can_process: bool = True
    reason: str = ''
    response: str = ''

    def __init__(self, tp: TpRecord, poi: Poi):
        self.tp = tp
        self.poi = poi


@dataclass
class Context(auto_repair_mixin.Context):
    """
    脚本执行上下文
    """
    data_path: str = ''
    valid_bids: set[str] = field(default_factory=set)
    recognition_results: list[list] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
        ),
    )
    proceed()


@desc()
def filter_records_by_basic_prop(ctx: Context, proceed):
    """
    通过一些基础属性过滤推送记录
    """
    sql = '''
        select 1 from cdz_picture where bid = %s limit 1;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        def process(record: Record):
            if any(char.isspace() for char in record.tp.tid):
                record.can_process = False
                record.reason = 'invalid tid'
                return

            row = poi_stab.fetch_one(sql, [record.tp.bid])
            if row is not None:
                record.can_process = False
                record.reason = 'has pictures'

        batch_process(ctx.records, process)

    proceed()


@desc()
def load_online_pictures(ctx: Context, proceed):
    """
    加载线上图片
    """
    sql = '''
        select pictures from charging_station where id = %s;
    '''

    with (
        get_mysql_connection('charging_station') as conn,
        conn.cursor() as cur,
    ):
        def process(record: Record):
            cur.execute(sql, [record.tp.id])
            row = cur.fetchone()
            if row is None:
                record.can_process = False
                record.reason = 'no tp'
                return

            record.picture_text, = row

        batch_process(ctx.records, process)

    proceed()


@desc()
def parse_pictures(ctx: Context, proceed):
    """
    解析图片地址
    """

    def process(record: Record):
        picture_urls, reason = format_picture(picture_text=record.picture_text)

        if not picture_urls:
            record.can_process = False
            record.reason = reason
            return

        record.picture_urls = picture_urls

    batch_process(ctx.records, process)
    proceed()


@desc()
def load_manual_records(ctx: Context, proceed):
    """
    加载手动推送记录
    """
    ctx.valid_bids = set(x.tp.bid for x in ctx.records if x.can_process)
    picture_map = defaultdict(set)

    for item in tsv.read_tsv(ctx.data_path):
        station_id, bid, tid, station_name, third_code, third_name, picture_url, _ = item
        if bid not in ctx.valid_bids or picture_url == '' or 'http' not in picture_url:
            continue

        picture_map[bid].add(picture_url)

    for record in ctx.records:
        picture_urls = picture_map.get(record.tp.bid, set())
        record.picture_urls = list(picture_urls)
        if not picture_urls:
            record.can_process = False
            record.reason = 'no picture'

    proceed()


def create_request(record: Record):
    """
    创建推送请求
    """
    photo_other = []
    for picture_url in record.picture_urls:
        photo_other.append({
            "imgUrl": picture_url,
            "src": "chongdianzhan",
            "photo_uploadtime": int(time.time())
        })

    return {
        "nid": str(uuid.uuid4()),
        "bid": int(record.tp.bid),
        "origin_bid": 0,
        "type": "photo_other",
        "details": [{
            "tid": record.tp.tid,
            "src": "chongdianzhan",
            "status": 1,
            "detail": json.dumps({"photo_other": photo_other}),
        }]
    }


def __get_response(ip, port, request):
    """
    获取接口响应数据
    """
    api = f"http://{ip}:{port}/rich-data/tp/access"
    return requests.post(api, json=request)


def get_response(record: Record):
    """
    获取接口响应数据
    """
    try:
        return bns_executor.execute(__get_response, create_request(record))
    except Exception as e:
        print(e)
        return None


@desc()
def restrict_record_count(ctx: Context, proceed):
    """
    限制推送数量
    """
    # 受限于服务限制，需要限制每天推送数量，否则会导致识别积压。
    # 目前的策略是：
    #   1. 假设每天推送数量为 7000 个，周一推送 1-1000，周二 1001-2000，周三 2001-3000，以此类推。
    #   2. 如果当天推送数量超过了 1000 个，则只推送前 1000 个。
    # 因为充电站缓存库存在 3 天延迟，上述策略可以尽量避免重复推送。
    success_records = [x for x in ctx.records if x.can_process]
    today = datetime.today().weekday()  # 获取当前星期几（0 表示周一，6 表示周日）
    chunk_size = len(success_records) // 7
    start = today * chunk_size
    end = start + chunk_size if today < 6 else len(success_records)  # 周日获取剩余元素

    for record in success_records[:start] + success_records[end:]:
        record.can_process = False
        record.reason = 'too many records to push'

    for record in [x for x in ctx.records if x.can_process][MAX_BATCH_SIZE:]:
        record.can_process = False
        record.reason = 'too many records to push'

    proceed()


@desc()
def filter_pictures(ctx: Context, proceed):
    """
    过滤推送图片
    """
    for record in tqdm([x for x in ctx.records if x.can_process]):
        success_picture_urls = []

        for picture_url in record.picture_urls:
            success, msg = filter_before_online(picture_url)
            ctx.recognition_results.append([
                record.tp.bid,
                record.tp.third_code,
                record.tp.third_name,
                record.tp.station_id,
                record.tp.station_name,
                picture_url,
                msg,
            ])
            time.sleep(1)

            if success:
                success_picture_urls.append(picture_url)

        record.picture_urls = success_picture_urls
        if not record.picture_urls:
            record.can_process = False
            record.reason = 'all pictures are filtered'

    tsv.write_tsv(ctx.work_dir / "recognition_results.csv", ctx.recognition_results)
    proceed()


@desc()
def upload_recognition_results(ctx: Context, proceed):
    """
    上传识别结果
    """
    sql = '''
        insert into cdz_picture_recognition(
            bid, third_code, third_name, station_id, station_name, url, result
        )
        values(%s, %s, %s, %s, %s, %s, %s)
        on conflict(bid, third_code, station_id, url)
        do update set result = excluded.result;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab:
        poi_stab.connection.autocommit = False
        with poi_stab.connection.cursor() as cursor:
            try:
                for result in tqdm(ctx.recognition_results):
                    cursor.execute(sql, result)

                poi_stab.connection.commit()
            except Exception as e:
                poi_stab.connection.rollback()
                print(e)

    proceed()


@desc()
def push_pictures(ctx: Context, proceed):
    """
    执行推送
    """
    for record in tqdm([x for x in ctx.records if x.can_process]):
        try:
            response = get_response(record)

            if response is None:
                record.response = 'response is none'
                continue

            if not response.ok:
                record.response = f'{response.status_code} {response.reason}'
                continue

            response_json = response.json()
            record.response = f"{response_json['_error_no']}: {response_json['_transid']}"
        except Exception as e:
            record.response = str(e)

    proceed()


@desc()
def save_success_records(ctx: Context, proceed):
    """
    保存成功的推送记录
    """
    output_items = []

    for record in tqdm([x for x in ctx.records if x.can_process]):
        if ctx.valid_bids and record.tp.bid not in ctx.valid_bids:
            continue

        for picture_url in record.picture_urls:
            output_items.append([
                record.tp.station_id,
                record.tp.bid,
                record.tp.tid,
                record.tp.station_name,
                record.tp.third_code,
                record.tp.third_name,
                picture_url,
                record.response,
            ])

    tsv.write_tsv(ctx.work_dir / "output.csv", output_items)
    proceed()


@desc()
def save_failed_records(ctx: Context, proceed):
    """
    保存失败的推送记录
    """
    output_items = []

    for record in tqdm([x for x in ctx.records if not x.can_process]):
        if ctx.valid_bids and record.tp.bid not in ctx.valid_bids:
            continue

        output_items.append([
            record.tp.station_id,
            record.tp.bid,
            record.tp.tid,
            record.tp.station_name,
            record.tp.third_code,
            record.tp.third_name,
            record.response,
            record.reason,
        ])

    tsv.write_tsv(ctx.work_dir / "output_failed.csv", output_items)
    proceed()


def alert_to_infoflow(msg):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg=msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    如流通知结果
    """
    msg = f'''今日例行推送图片量级： {len([x for x in ctx.records if x.can_process])} '''
    print(msg)
    alert_to_infoflow(msg)
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['test', 'output', 'manual', 'auto'],
        default='test',
        required=False,
    )
    parser.add_argument(
        '--data-path',
        dest='data_path',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--upload-reg-result',
        dest='upload_reg_result',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建管道
    """
    pipes = [
        load_tp_records,
        load_pois,
        retain_best_matched_tp,
        filter_records_by_basic_prop,
    ]

    if args.mode == 'test':
        pipes.extend([
            load_manual_records,
            restrict_record_count,
            filter_pictures,
        ])
    elif args.mode == 'output':
        pipes.extend([
            load_online_pictures,
            parse_pictures,
            restrict_record_count,
            filter_pictures,
        ])
    elif args.mode == 'auto':
        pipes.extend([
            load_online_pictures,
            parse_pictures,
            restrict_record_count,
            filter_pictures,
            push_pictures,
            send_records_to_infoflow,
        ])
    elif args.mode == 'manual':
        pipes.extend([
            load_manual_records,
            push_pictures,
        ])

    if args.upload_reg_result:
        pipes.append(upload_recognition_results)

    pipes.extend([
        save_success_records,
        save_failed_records,
    ])

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_push_picture'),
        data_path=args.data_path,
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(f'例行推送充电站图片脚本异常！{e}')


if __name__ == '__main__':
    main(parse_args())
