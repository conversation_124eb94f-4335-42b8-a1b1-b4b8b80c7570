# !/usr/bin/env python3
"""
例行发布充电站成果
"""
import argparse
import csv
import datetime
import random
import time
from dataclasses import dataclass, field
from pathlib import Path

from pymysql import cursors
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection, batch_process
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import TAG_CLOSED_USER
from src.charging_station.helper import TRUSTY_THIRD_NAMES, get_closed_users, get_city, get_tp_users
from src.charging_station.formatters.phone_fomatter import format_phone
from src.tools import pipeline, pgsql, notice_tool, tsv
from src.tools.afs_tool import AfsTool

# 以下字段顺序不可变，已和下游约定好了。
QUERY_FIELDS = [
    'id', 'uid', 'bid', 'thirdcode', 'thirdname', 'station_id', 'operator_id', 'equipment_owner_id',
    'station_name', 'address', 'station_tel', 'service_tel', 'station_type', 'station_status',
    'park_nums', 'station_lng', 'station_lat', 'site_guide', 'construction', 'park_info',
    'busine_hours', 'electricity_fee', 'service_fee', 'market_electricity_fee', 'market_service_fee',
    'parking_tag', 'bdmap_charging', 'policy_infos', 'extra', 'online_ctime', 'online_mtime',
    'online_status', 'online_info', 'create_time', 'update_time', 'is_deleted'
]
RELEASE_FIELDS = QUERY_FIELDS + ['city', 'auto_release', 'msg', 'show_tag']

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    上线记录
    """
    tid: str
    x: float
    y: float
    wkt: str
    src: str
    city: str = ''
    data: dict = field(default_factory=dict)
    can_auto_online: bool = True
    can_process: bool = True
    reason: str = ''
    is_match_competitor: bool = False
    competitor_name: str = ''
    competitor_address: str = ''
    handled_phone: str = ''

    @property
    def auto_release(self):
        """
        是否可自动上线
        """
        return 1 if self.can_auto_online else 0

    @property
    def release_data(self):
        """
        推送数据
        """
        extra_data = [self.city, self.auto_release, self.reason, '充电站']  # 这里必须是充电站，已和下游约定好了。
        self.data['station_lng'] = self.x
        self.data['station_lat'] = self.y
        self.data['bid'] = ''
        self.data['service_tel'] = self.handled_phone

        return [self.data[field_name] for field_name in QUERY_FIELDS] + extra_data


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    date: str
    save_path: Path
    afs_path: str
    data_path: str
    all_tp_users: dict = field(default_factory=dict)
    records: list[Record] = field(default_factory=list)
    record_ids: set[str] = field(default_factory=set)
    delayed_third_codes: set[int] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def extend_records(self, records):
        """
        添加记录
        """
        for record in records:
            if record.tid in self.record_ids:
                continue

            self.records.append(record)
            self.record_ids.add(record.tid)


@desc()
def fill_all_tp_users(ctx: Context, proceed):
    """
    填充所有 tp 用户
    """
    ctx.all_tp_users = get_tp_users()
    proceed()


@desc()
def fill_delayed_third_codes(ctx: Context, proceed):
    """
    填充延迟上线的第三方编码
    """
    closed_users = get_closed_users()
    sql = '''
        select third_code
        from cdz_tp_brand 
        where third_code != %s and
              third_name = %s and      
              tag != %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for user in tqdm(closed_users.items(), total=len(closed_users)):
            for third_code, in stab.fetch_all(sql, [user[0], user[1], TAG_CLOSED_USER]):
                if third_code in closed_users:
                    continue

                ctx.delayed_third_codes.add(third_code)

    print(ctx.delayed_third_codes)

    proceed()


def parse_record(data, src=""):
    """
    将数据库记录解析为 Record 对象
    """
    tid = f"{data['thirdcode']}_{data['station_id']}"
    x = float(data['station_lng'])
    y = float(data['station_lat'])
    wkt = f"POINT({x} {y})"

    return Record(
        tid=tid,
        wkt=wkt,
        data=data,
        x=x,
        y=y,
        src=src,
    )


def load_delayed_db_records(ctx: Context):
    """
    加载需要延迟上线的记录
    """
    sql = f'''
        select {', '.join(QUERY_FIELDS)}
        from charging_station
        where DATE(create_time) = (%s - interval 5 day) and
              bid = '' and
              thirdcode in %s;
    '''

    with (
        get_mysql_connection("charging_station", cursor_class=cursors.DictCursor) as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql, [ctx.date, ctx.delayed_third_codes])
        records = [parse_record(x, src="delayed") for x in tqdm(cur.fetchall())]
        print("delayed count", len(records))
        ctx.extend_records(records)


def load_realtime_db_records(ctx: Context):
    """
    加载需要尽快上线的记录
    """
    sql = f'''
        select {', '.join(QUERY_FIELDS)}
        from charging_station
        where DATE(create_time) = %s and
              bid = '' and
              thirdcode not in %s;
    '''

    with (
        get_mysql_connection("charging_station", cursor_class=cursors.DictCursor) as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql, [ctx.date, ctx.delayed_third_codes])
        records = [parse_record(x, src="realtime") for x in tqdm(cur.fetchall())]
        print("realtime count", len(records))
        ctx.extend_records(records)


def load_temp_db_records(ctx: Context, tp_ids):
    """
    加载需要临时上线的记录
    """
    sql = f'''
        select {', '.join(QUERY_FIELDS)}
        from charging_station
        where thirdcode = %s and
              station_id = %s
        limit 1;
    '''
    with (
        get_mysql_connection("charging_station", cursor_class=cursors.DictCursor) as conn,
        conn.cursor() as cur,
    ):
        records = []

        for tp_id in tqdm(tp_ids):
            cur.execute(sql, tp_id)
            row = cur.fetchone()
            if row is None:
                continue

            records.append(parse_record(row, src="temp"))

        print("temp count", len(records))
        ctx.extend_records(records)


def load_temp_db_records_by_dict(ctx: Context):
    """
    加载需要临时上线的记录
    """
    tp_ids = [
        # ('1020', '10000100057'),
        # ('162', '00000000000000188025'),
        # 这里可以添加需要临时上线的 tp id，上面 2 个是示例。
    ]
    load_temp_db_records(ctx, tp_ids)


def load_temp_db_records_by_file(ctx: Context):
    """
    加载需要临时上线的记录
    """
    temp_file = ctx.work_dir / 'temp.csv'
    if not temp_file.exists():
        return

    tp_ids = [x for x in tsv.read_tsv(temp_file)]
    load_temp_db_records(ctx, tp_ids)
    temp_file.unlink(missing_ok=True)


def get_released_ids():
    """
    获取已上线的 id 集合
    """
    sql = '''
        select tid from cdz_release_result;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        return set(x[0] for x in stab.fetch_all(sql))


def load_failed_db_records(ctx: Context):
    """
    加载历史上线失败的记录
    """
    sql = f'''
        select {', '.join(QUERY_FIELDS)}
        from charging_station
        where DATE(create_time) < (%s - interval 7 day) and
              create_time >= '2025-05-01' and
              bid = '';
    '''
    released_ids = get_released_ids()

    with (
        get_mysql_connection("charging_station", cursor_class=cursors.DictCursor) as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql, [ctx.date])
        original_records = []
        for item in tqdm(cur.fetchall()):
            record = parse_record(item, src="failed")
            original_records.append(record)

        print("original failed count", len(original_records))
        actual_records = [x for x in original_records if x.tid not in released_ids]
        print("actual failed count", len(actual_records))
        ctx.extend_records(actual_records)

    pass


@desc()
def load_db_records(ctx: Context, proceed):
    """
    加载记录（数据库模式）
    """
    load_delayed_db_records(ctx)
    load_realtime_db_records(ctx)
    load_temp_db_records_by_dict(ctx)
    load_temp_db_records_by_file(ctx)

    if datetime.datetime.today().weekday() == 0:  # 仅周一执行
        load_failed_db_records(ctx)

    proceed()


@desc()
def load_file_records(ctx: Context, proceed):
    """
    加载记录（文件模式）
    """
    sql = f'''
        select {', '.join(QUERY_FIELDS)}
        from charging_station
        where id = %s;
    '''

    with (
        get_mysql_connection("charging_station", cursor_class=cursors.DictCursor) as conn,
        conn.cursor() as cur,
    ):
        for record_id in tqdm({int(x) for x, in tsv.read_tsv(ctx.data_path)}):
            cur.execute(sql, [record_id])
            row = cur.fetchone()
            if row is None:
                continue

            ctx.records.append(parse_record(row))

    proceed()


@desc()
def load_all_records(ctx: Context, proceed):
    """
    加载记录（全量模式）
    """
    sql = f'''select {', '.join(QUERY_FIELDS)} from charging_station;'''

    with (
        get_mysql_connection("charging_station", cursor_class=cursors.DictCursor) as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql)
        ctx.records.extend([parse_record(x) for x in tqdm(cur.fetchall())])

    proceed()


@desc()
def load_date_records(ctx: Context, proceed):
    """
    加载记录（日期模式）
    """
    sql = f'''
        select {', '.join(QUERY_FIELDS)}
        from charging_station
        where id = %s;
    '''

    with (
        get_mysql_connection("charging_station", cursor_class=cursors.DictCursor) as conn,
        conn.cursor() as cur,
    ):
        for record_id in tqdm([int(x) for x, in tsv.read_tsv(ctx.data_path)]):
            cur.execute(sql, [record_id])
            row = cur.fetchone()
            if row is None:
                continue

            ctx.records.append(parse_record(row))

    proceed()


def shake_record_geom(record: Record):
    """
    对 tp 坐标进行随机抖动
    """
    tolerance = 0.1e-5

    x = record.x
    y = record.y

    x = round(x + random.choice([-tolerance, tolerance]), 7)
    y = round(y + random.choice([-tolerance, tolerance]), 7)
    wkt = f"POINT({x} {y})"

    record.x = x
    record.y = y
    record.wkt = wkt


@desc()
def shake_geom(ctx: Context, proceed):
    """
    对 tp 坐标进行随机抖动

    二次推送时，如果前后两次坐标一致会被过滤，所以需要抖动坐标。
    """
    for record in tqdm(ctx.records):
        shake_record_geom(record)

    proceed()


@desc()
def fill_city(ctx: Context, proceed):
    """
    填充城市信息
    """
    qps = 20

    for record in tqdm(ctx.records):
        city = get_city(record.x, record.y)
        time.sleep(1 / qps)
        if city is None:
            record.can_process = False
            record.reason = 'can not find city'
            continue

        record.city = city

    proceed()


def within_boshi(poi_stab, record: Record):
    """
    是否落面博士
    """
    buffer = 30e-5
    sql = '''
        select 1 
        from cluster_aoi
        where st_dwithin(st_geomfromtext(%s, 4326), region_geom, %s)
        limit 1;
    '''

    return poi_stab.fetch_one(sql, [record.wkt, buffer]) is not None


def within_zhongyuan(poi_stab, record: Record):
    """
    是否落面众源
    """
    buffer = 30e-5
    sql = '''
        select 1 
        from cdz_zhongyuan_cluster_polygon
        where st_dwithin(st_geomfromtext(%s, 4326), region_geom, %s)
        limit 1;
    '''

    return poi_stab.fetch_one(sql, [record.wkt, buffer]) is not None


@desc()
def match_competitor(ctx: Context, proceed):
    """
    匹配竞品
    """
    buffer = 5e-5
    sql = '''
        select name, address
        from cdz_competitor
        where st_dwithin(geom, st_geomfromtext(%s, 4326), %s) and
              crawl_time >= extract(epoch from (now() - interval '1 months'))::integer
        limit 1;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        def process(record: Record):
            row = poi_stab.fetch_one(sql, [record.wkt, buffer])
            if row is None:
                return

            record.is_match_competitor = True
            record.competitor_name, record.competitor_address = row

        batch_process(ctx.records, process)

    proceed()


@desc()
def filter_records(ctx: Context, proceed):
    """
    过滤上线记录
    """
    undesired_keys = {'私人'}

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        def process(record: Record):
            if record.data['thirdname'] in TRUSTY_THIRD_NAMES:
                record.reason = '高准 TP'
                return

            if record.data['thirdcode'] not in ctx.all_tp_users:
                record.can_process = False
                record.reason = '非合作 TP'
                return

            if any(key for key in undesired_keys if key in record.data['station_name']):
                record.can_auto_online = False
                record.reason = '名称包含特殊字符'
                return

            if within_boshi(poi_stab, record):
                record.reason = '落面博士'
            elif within_zhongyuan(poi_stab, record):
                record.reason = '落面众源'
            elif record.is_match_competitor:
                record.reason = '匹配竞品'
            else:
                record.can_auto_online = False
                record.reason = '博士不落面、竞品不匹配。'

        batch_process(ctx.records, process)

    proceed()


@desc()
def process_phone(ctx: Context, proceed):
    """
    填充处理后的电话号码
    """
    for record in tqdm([x for x in ctx.records if x.can_process]):
        service_tel = record.data['service_tel']
        handled_phone, reason = format_phone(
            third_name=record.data['thirdname'],
            service_tel=service_tel,
        )

        record.handled_phone = handled_phone if handled_phone is not None else service_tel

    proceed()


def save_data(save_path, records):
    """
    保存上线记录
    """
    with open(save_path, 'w', newline='', encoding='utf-8-sig') as f:
        csv_writer = csv.writer(f)
        csv_writer.writerow(RELEASE_FIELDS)

        for record in tqdm(records):
            if not record.can_process:
                continue

            csv_writer.writerow(record.release_data)


@desc()
def save_release_data(ctx: Context, proceed):
    """
    保存上线记录（正式发布数据）
    """
    save_data(ctx.save_path, ctx.records)
    proceed()


@desc()
def save_debug_data(ctx: Context, proceed):
    """
    保存上线记录（调试数据）
    """
    save_data(f'{ctx.save_path}.debug', ctx.records)
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.tid,
                x.wkt,
                x.data['station_name'],
                x.data['address'],
                x.city,
                x.can_auto_online,
                x.can_process,
                x.reason,
                x.is_match_competitor,
                x.competitor_name,
                x.competitor_address,
                x.src,
            ]
            for x in ctx.records
        ]
    )

    proceed()


@desc()
def upload_release_data(ctx: Context, proceed):
    """
    上传记录
    """
    afs = AfsTool('aries_rpm01')
    afs.put(str(ctx.save_path), ctx.afs_path)
    ctx.save_path.unlink(missing_ok=True)

    proceed()


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    如流通知结果
    """
    total_count = len(ctx.records)
    auto_count = len([x for x in ctx.records if x.auto_release])
    manual_count = total_count - auto_count
    msg = f'''今日推送总量：{len(ctx.records)}
自动上线量：{auto_count}
人工核实量：{manual_count}
'''
    print(msg)
    alert_to_infoflow(msg)
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['db', 'file', 'all'],
        default='db',
        required=False,
    )
    parser.add_argument(
        '--data-path',
        dest='data_path',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--date',
        dest='date',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--shake',
        dest='shake',
        required=False,
        default=False,
        action='store_true',
    )
    parser.add_argument(
        '--push',
        dest='push',
        required=False,
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = []

    if args.mode == 'db':
        pipes.extend([
            fill_all_tp_users,
            fill_delayed_third_codes,
            load_db_records,
            fill_city,
            match_competitor,
            filter_records,
            process_phone,
        ])
    elif args.mode == 'file':
        pipes.extend([
            fill_all_tp_users,
            fill_delayed_third_codes,
            load_file_records,
            fill_city,
            match_competitor,
            filter_records,
            process_phone,
        ])
    elif args.mode == 'all':
        pipes.append(load_all_records)

    if args.shake:
        pipes.append(shake_geom)

    if args.push:
        pipes.extend([
            save_release_data,
            upload_release_data,
            send_records_to_infoflow,
        ])
    else:
        pipes.append(save_debug_data)

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)

    date_format = '%Y%m%d'
    target_date = datetime.datetime.strptime(args.date, date_format).strftime(date_format) if args.date else ''
    yesterday = (datetime.date.today() - datetime.timedelta(days=1)).strftime(date_format)
    date = target_date if target_date != '' and target_date != yesterday else yesterday

    work_dir = Path('cache/auto_release')
    ctx = Context(
        work_dir=work_dir,
        date=date,
        save_path=work_dir / f'all_tp_data_{date}.csv',
        afs_path='/user/map-data-streeview/aoi-ml/tp_data/tp_auto_release',
        data_path=args.data_path,
    )

    if args.push:
        try:
            main_pipe(ctx)
        except Exception as e:
            alert_to_infoflow(f'例行发布充电站成果脚本异常！{e}')
    else:
        main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
