# !/usr/bin/env python3
"""
例行修正充电站属性
"""
import argparse
import importlib
import os
import time
from collections import defaultdict
from dataclasses import dataclass, field
from pathlib import Path

import requests
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin
from src.charging_station.data import (
    <PERSON>le,
    Ticket,
    TicketProviderDescription,
    TicketBatch,
    TicketPushResult,
    TpRecord,
    Poi,
    RepairPropContext,
)
from src.charging_station.ticket_organizer import TicketOrganizer
from src.tools import pipeline, pgsql, tsv, notice_tool

API_MAP = {
    'test': 'http://xiangmeng02-dev01.bcc-gzhxy.baidu.com:8905/api/v1/workflow/process/start',
    'practice': 'http://gzdt-map-poi-plat03.gzdt.baidu.com:8906/api/v1/workflow/process/start',
    'formal': 'http://*************:8905/api/v1/workflow/process/start',
}
REGISTERED_PROVIDERS = {
    'update_tesla': 'auto',
    'update_status': 'all',
    'update_location': 'all',
    'update_ownership': 'all',
    'update_spacing': 'all',
    'update_prop': 'manual',
}
RESPONSE_ERROR_MAP = {
    403: 'creator 不在用户组中',
    404: '传入的工艺 proc_def_id 不存在。',
    500: '服务端处理异常，将错误信息反馈给平台组相关 RD。',
}
EMAIL = '<EMAIL>'
BATCH_SIZE = 1

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    api: str
    mode: str
    work_dir: Path
    bid_path: str
    piles: defaultdict[str, list[Pile]] = field(default_factory=lambda: defaultdict(list))
    tp_records: defaultdict[str, list[TpRecord]] = field(default_factory=lambda: defaultdict(list))
    pois: dict[str, Poi] = field(default_factory=dict)
    tickets: list[Ticket] = field(default_factory=list)
    batch_statistics: dict[str, int] = field(default_factory=dict)
    providers: list[TicketProviderDescription] = field(default_factory=list)
    statistics_msg: str = ''

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def add_ticket(self, ticket: Ticket):
        ticket.creator = EMAIL
        ticket.mode = self.mode
        self.tickets.append(ticket)


@desc()
def load_piles(ctx: Context, proceed):
    """
    加载充电站桩头信息
    """
    auto_repair_mixin.load_piles(ctx)
    proceed()


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def find_providers(ctx: Context, proceed):
    """
    查询所有工单提供器
    """
    provider_dir = Path(os.path.dirname(__file__))
    module_names = [x.stem for x in provider_dir.glob('auto_repair_*.py')]
    full_module_names = [f'src.charging_station.{x}' for x in module_names]
    modules = [importlib.import_module(x) for x in full_module_names]
    provider_modules = [
        x for x in modules
        if (
            hasattr(x, 'NAME') and
            hasattr(x, 'PROJECT') and
            hasattr(x, 'PRIORITY') and
            hasattr(x, 'BATCH') and
            hasattr(x, 'METHOD') and
            hasattr(x, 'run')
        )
    ]
    for module in provider_modules:
        if module.NAME not in REGISTERED_PROVIDERS:
            continue

        ctx.providers.append(TicketProviderDescription(
            name=module.NAME,
            runner=module.run,
        ))

    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    provider_count = len(ctx.providers)

    for i, provider in enumerate(ctx.providers):
        print(f'=============================================================')
        print(f'provider ({i + 1:0{len(str(provider_count))}}/{provider_count}): {provider.name}')

        repair_context = RepairPropContext(
            piles=ctx.piles,
            tp_records=ctx.tp_records,
            pois=ctx.pois,
            mode=REGISTERED_PROVIDERS[provider.name] if ctx.mode != 'test' else 'manual',
        )
        result = provider.runner(repair_context)
        ctx.batch_statistics[result.batch_name] = len(result.batch_records)
        for ticket in result.tickets:
            ctx.add_ticket(ticket)

    ctx.tickets = TicketOrganizer(ctx).arrange()
    proceed()


def extract_push_result_from_response(ticket_batch: TicketBatch, response):
    """
    从响应中提取工单推送结果
    """
    success_code = 200
    params_error_code = 400

    if response.status_code in RESPONSE_ERROR_MAP:
        ticket_batch.set_push_result(code=response.status_code, msg=RESPONSE_ERROR_MAP[response.status_code])
        return

    if response.status_code != success_code and response.status_code != params_error_code:
        ticket_batch.set_push_result(code=response.status_code, msg=f'undesired status code: {response.status_code}')
        return

    ticket_batch.set_push_result(results=[TicketPushResult(x) for x in response.json().get('data')])


@desc()
def push_tickets(ctx: Context, proceed):
    """
    推送工单
    """
    tickets = [x for x in ctx.tickets if x.can_process]
    batches = [TicketBatch(tickets[i:i + BATCH_SIZE]) for i in range(0, len(tickets), BATCH_SIZE)]

    for batch in tqdm(batches):
        try:
            response = requests.post(ctx.api, json=batch.formatted_json)
            extract_push_result_from_response(batch, response)
        except Exception as e:
            batch.set_push_result(code=-1, msg=str(e))
        finally:
            time.sleep(1)  # 下游扛不住，qps 限制到 1。

    proceed()


@desc()
def save_tickets_to_db(ctx: Context, proceed):
    """
    保存工单到数据库
    """
    sql = '''
        insert into cdz_ticket(bid, ptid, batch, src, project, priority, method, message, mode)
        values(%s, %s, %s, %s, %s, %s, %s, %s, %s);
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for ticket in [x for x in ctx.tickets if x.push_success]:
                cur.execute(sql, [
                    ticket.bid,
                    ticket.ptid,
                    ticket.batch_id,
                    ticket.src,
                    ticket.project,
                    ticket.priority,
                    ticket.method,
                    ticket.message,
                    ticket.mode,
                ])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


@desc()
def save_tickets_to_file(ctx: Context, proceed):
    """
    保存工单到文件
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.bid,
                x.ptid,
                x.res_code,
                x.res_msg,
                x.project,
                x.priority,
                x.batch_id,
                x.method,
                x.message,
                x.creator,
                x.reason,
            ]
            for x in ctx.tickets if x.can_process
        ]
    )
    proceed()


def create_manual_statistics_msg(ctx: Context):
    """
    生成人工统计信息
    """
    ticket_map = defaultdict(list)
    for ticket in [x for x in ctx.tickets if x.can_process]:
        ticket_map[ticket.batch_name].append(ticket)

    total_count = sum(len(group) for group in ticket_map.values())
    count_map = {key: len(group) for key, group in ticket_map.items()}
    lines = [
        '今日属性例行推送人工情况如下：',
        f'总量：{total_count}'
    ]
    lines.extend([f'{key}：{value}' for key, value in count_map.items()])
    return "\n".join(lines)


def create_batch_statistics_msg(ctx: Context):
    """
    生成批处理统计信息
    """
    total_count = sum(ctx.batch_statistics.values())
    lines = [
        '今日属性例行批处理情况如下：',
        f'总量：{total_count}'
    ]
    lines.extend([f'{key}：{value}' for key, value in ctx.batch_statistics.items()])
    return "\n".join(lines)


@desc()
def fill_statistics_msg(ctx: Context, proceed):
    """
    填充统计信息
    """
    manual_msg = create_manual_statistics_msg(ctx)
    batch_msg = create_batch_statistics_msg(ctx)
    ctx.statistics_msg = f'{manual_msg}\n\n{batch_msg}'
    print(ctx.statistics_msg)
    proceed()


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    如流通知结果
    """
    alert_to_infoflow(ctx.statistics_msg)
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['test', 'practice', 'formal'],
        default='test',
        required=False,
    )
    parser.add_argument(
        '--bid-path',
        dest='bid_path',
        type=str,
        required=False,
    )
    return parser.parse_args()


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_piles,
        load_tp_records,
        load_pois,
        find_providers,
        create_tickets,
        fill_statistics_msg,
    ]

    if args.mode == 'test':
        pipes.extend([
            save_tickets_to_file,
        ])
    else:
        pipes.extend([
            push_tickets,
            save_tickets_to_db,
            save_tickets_to_file,
            send_records_to_infoflow,
        ])

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_repair'),
        bid_path=args.bid_path,
        api=API_MAP[args.mode],
        mode=args.mode,
    )

    if args.mode != 'test':
        try:
            main_pipe(ctx)
        except Exception as e:
            alert_to_infoflow(f'例行修正充电站属性脚本异常！{e}')
    else:
        main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
