# !/usr/bin/env python3
"""
例行修正充电站坐标
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.charging_station import auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import <PERSON><PERSON>, RepairResult, RepairPropContext, TpRecord, TicketConfig, Competitor
from src.charging_station.helper import get_manual_location_bids, get_all_white_list_bids
from src.charging_station.online_prop import online_gcj_location
from src.tools import pipeline, tsv

NAME = 'update_location'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = 'CDXJSK20241023005'
BATCH_NAME = '坐标'
METHOD = 'edit'

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    充电站信息
    """
    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)
    competitor: Competitor = field(init=False)

    # 业务逻辑
    distance: float = 0.0
    is_too_far_away: bool = False
    is_too_close: bool = False
    is_manual_worked: bool = False
    in_white_list: bool = False

    def __init__(self, tp: TpRecord, poi: Poi, competitor: Competitor):
        self.tp = tp
        self.poi = poi
        self.competitor = competitor
        self.distance = self.poi.geom.distance(self.tp.station_geom)

    @property
    def work_message(self):
        return 'tp 和 poi 距离过远，请核实。'

    @property
    def can_eval(self):
        return not self.in_white_list and not self.is_manual_worked and self.distance > 0

    @property
    def can_auto_process(self):
        return (
            self.can_eval and
            self.tp.src != 'manual' and
            not self.is_too_close
        )

    @property
    def can_manual_process(self):
        return (
            self.can_eval and
            not self.can_auto_process and
            self.is_too_far_away
        )


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
            competitor=kwargs.get('competitor', None)
        ),
        match_competitor=True,
    )
    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    auto_repair_mixin.fill_poi_mc_wkt(ctx)
    proceed()


@desc()
def fill_distance_status(ctx: Context, proceed):
    """
    填充距离状态
    """
    max_distance = 15e-5
    min_distance = 10e-5

    for record in tqdm(ctx.records):
        record.is_too_far_away = record.distance > max_distance
        record.is_too_close = record.distance < min_distance

    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    auto_repair_mixin.create_tickets(ctx, TicketConfig(
        project=PROJECT,
        priority=PRIORITY,
        batch_id=BATCH,
        method=METHOD,
        src=NAME,
        batch_name=BATCH_NAME,
    ))
    proceed()


@desc()
def fill_batch_conditions(ctx: Context, proceed):
    """
    填充批处理条件
    """
    manual_bids = get_manual_location_bids()
    white_list_bids = get_all_white_list_bids()

    for record in tqdm(ctx.records):
        if record.poi.bid in manual_bids:
            record.is_manual_worked = True

        if record.poi.bid in white_list_bids:
            record.in_white_list = True

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    ctx.batch_records = [
        Poi(bid=x.poi.bid, geom=x.tp.station_geom)
        for x in ctx.records if x.can_auto_process
    ]
    online_gcj_location(ctx.batch_records)
    proceed()


def save_records(file_name, records: list[Record]):
    """
    保存记录
    """
    tsv.write_tsv(
        file_name,
        [
            [
                x.tp.id,
                x.tp.station_id,
                x.tp.station_name,
                x.tp.station_geom.wkt,
                x.tp.third_code,
                x.tp.third_name,
                x.poi.bid,
                x.tp.address,

                x.poi.name,
                x.poi.alias,
                x.poi.address,
                x.poi.phone,
                x.poi.status,
                x.poi.tag,
                x.poi.geom.wkt,
                x.poi.mc_wkt,

                x.competitor.name if x.competitor else '',
                x.competitor.address if x.competitor else '',
                x.competitor.geom.wkt if x.competitor else '',
                x.competitor.address_iou if x.competitor else 0,
                x.competitor.name_iou if x.competitor else 0,

                x.distance * 1e5,
                x.is_too_far_away,
                x.is_too_close,
                x.tp.src,
                x.is_manual_worked,
                x.in_white_list,
            ]
            for x in records
        ]
    )


@desc()
def save_all_records(ctx: Context, proceed):
    """
    保存所有记录
    """
    save_records(ctx.work_dir / "all.csv", ctx.records)
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    save_records(ctx.work_dir / "eval.csv", [x for x in ctx.records if x.can_eval])
    proceed()


@desc()
def save_auto_records(ctx: Context, proceed):
    """
    保存自动处理记录
    """
    save_records(ctx.work_dir / "auto.csv", [x for x in ctx.records if x.can_auto_process])
    proceed()


@desc()
def save_manual_records(ctx: Context, proceed):
    """
    保存人工处理记录
    """
    save_records(ctx.work_dir / "manual.csv", [x for x in ctx.records if x.can_manual_process])
    proceed()


def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_records=ctx.batch_records,
        batch_name=BATCH_NAME,
    )


def create_pipeline(args, repair_context: RepairPropContext):
    """
    创建策略执行管道
    """
    mode = args.mode if args is not None else repair_context.mode
    print(mode)
    pipes = [
        load_tp_records,
        load_pois,
    ] if repair_context is None else []

    pipes.extend([
        retain_best_matched_tp,
        fill_distance_status,
        fill_batch_conditions,
    ])

    if mode == 'manual':
        pipes += [fill_poi_mc_wkt, create_tickets]
    elif mode == 'auto':
        pipes += [fill_poi_mc_wkt, execute_batch]
    elif mode == 'all':
        pipes += [fill_poi_mc_wkt, create_tickets, execute_batch]

    pipes.extend([
        save_all_records,
        save_eval_records,
        save_auto_records,
        save_manual_records,
    ])

    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """
    主函数
    """
    main_pipe = create_pipeline(args, repair_context)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_repair_location'),
        bid_path=args.bid_path if args is not None else None,
    )

    if repair_context is not None:
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois

    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())
