# !/usr/bin/env python3
"""
包含一些例行属性修正的复用逻辑
"""
import argparse
import datetime
from collections import defaultdict
from dataclasses import dataclass, field
from pathlib import Path
from typing import Callable, Any

import mapio.utils.coord
import pymysql.cursors
import shapely.wkt
from shapely import Point, LineString
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection, get_str_iou
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import TpRecord, Poi, Ticket, TicketConfig, Competitor, Pile
from src.charging_station.formatters.picture_formatter import format_picture
from src.charging_station.helper import get_desired_poi_name, TRUSTY_THIRD_NAMES
from src.tools import pgsql, tsv


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    bid_path: str = ''
    piles: defaultdict[str, list[<PERSON>le]] = field(default_factory=lambda: defaultdict(list))
    tp_records: defaultdict[str, list[TpRecord]] = field(default_factory=lambda: defaultdict(list))
    pois: dict[str, Poi] = field(default_factory=dict)
    records: list = field(default_factory=list)
    tickets: list[Ticket] = field(default_factory=list)
    batch_records: list[Poi] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def get_unmatched_tp_records(self):
        """
        获取暂未匹配的 tp 记录
        """
        matched_bids = set(record.tp.bid for record in self.records)
        return {
            bid: records
            for bid, records in self.tp_records.items()
            if bid in self.pois and bid not in matched_bids
        }


def get_all_charging_station_bids():
    """
    获取充电站所有 bid
    """
    sql = '''
        select bid from poi where std_tag = '交通设施;充电站';
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        return [row[0] for row in poi_stab.fetch_all(sql)]


def get_all_private_charging_station_bids():
    """
    获取所有不开放的充电站 bid
    """
    sql = '''
        select bid from cdz_history where online_type = 0;
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        return [row[0] for row in poi_stab.fetch_all(sql)]


def get_all_public_charging_station_bids():
    """
    获取所有开放的充电站 bid
    """
    sql = '''
        select bid from cdz_history where online_type = 1;
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        return [row[0] for row in poi_stab.fetch_all(sql)]


def get_all_offline_charging_station_bids():
    """
    获取所有离线状态的充电站 bid
    """
    sql = '''
        select bid from cdz_history where status != 1;
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        return [row[0] for row in poi_stab.fetch_all(sql)]


def get_all_online_charging_station_bids():
    """
    获取所有在线状态的充电站 bid
    """
    sql = '''
        select bid from cdz_history where status = 1;
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        return [row[0] for row in poi_stab.fetch_all(sql)]


def get_all_tp_bids():
    """
    获取所有和 tp 存在挂接关系的 bid
    """
    sql = '''
        select distinct bid from charging_station where bid != '';
    '''

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql)
        return [row[0] for row in cur.fetchall()]


def get_white_board_bids():
    """
    获取所有白板 bid
    """
    charging_station_bids = get_all_charging_station_bids()
    tp_bids = get_all_tp_bids()
    return list(set(charging_station_bids) - set(tp_bids))


def get_piles(tp_cur, bid):
    """
    获取充电站桩头
    """
    get_tp_sql = '''
        select thirdcode, station_id from charging_station where bid = %s;
    '''
    get_update_time_sql = '''
        select status, update_time from charging_equipment where thirdcode = %s and station_id = %s;
    '''
    piles = []

    tp_cur.execute(get_tp_sql, (bid,))
    for third_code, station_id in tp_cur.fetchall():
        tp_cur.execute(get_update_time_sql, (third_code, station_id))
        for status, update_time in tp_cur.fetchall():
            piles.append((status, update_time))

    return piles


def __create_tp_record(dict_row):
    return TpRecord(
        id=dict_row['id'],
        bid=dict_row['bid'],

        station_id=dict_row['station_id'],
        station_name=dict_row['station_name'],
        station_type=dict_row['station_type'],
        station_status=dict_row['station_status'],
        station_geom=Point((float(dict_row['station_lng']), float(dict_row['station_lat']))),

        third_code=dict_row['thirdcode'],
        third_name=dict_row['thirdname'],

        address=dict_row['address'],
        park_info=dict_row['park_info'],
        site_guide=dict_row['site_guide'],
        service_tel=dict_row['service_tel'],
        business_hours=dict_row['busine_hours'],
    )


def get_tp_records(tp_cur, bid):
    """
    获取充电站
    """
    sql = '''
        select id, bid,
               station_id, station_name, station_type, station_lng, station_lat, station_status,
               thirdcode, thirdname,
               address, park_info, site_guide, service_tel, busine_hours
        from charging_station
        where bid = %s;
    '''

    tp_cur.execute(sql, (bid,))
    return [__create_tp_record(data) for data in tp_cur.fetchall()]


def get_pile_last_update_time(tp_cur, bid):
    """
    获取充电站桩头最后更新时间
    """
    all_update_time = [update_time for _, update_time in get_piles(tp_cur, bid)]
    return max(all_update_time) if all_update_time else datetime.datetime.min


def get_tp_last_pull_time(tp_cur, bid):
    """
    获取充电站 tp 最后拉取时间
    """
    sql = '''
        select pull_mtime from charging_station where bid = %s;
    '''
    all_pull_time = []

    tp_cur.execute(sql, (bid,))
    for pull_time, in tp_cur.fetchall():
        all_pull_time.append(pull_time)

    return max(all_pull_time) if all_pull_time else datetime.datetime.min


def load_piles(ctx):
    """
    获取充电站桩头信息
    """
    sql = '''
        select station_id, thirdcode, status
        from charging_equipment;
    '''

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql)

        for station_id, thirdcode, status in tqdm(cur.fetchall()):
            pile = Pile(
                third_code=thirdcode,
                station_id=station_id,
                status=status,
            )
            ctx.piles[pile.tid].append(pile)


def __load_all_tp_records(ctx):
    """
    获取所有充电站 tp 记录
    """
    sql = '''
        select id, bid,
               station_id, station_name, station_type, station_lng, station_lat, station_status,
               thirdcode, thirdname,
               address, park_info, site_guide, service_tel, busine_hours
        from charging_station
        where bid != '';
    '''

    with (
        get_mysql_connection("charging_station", cursor_class=pymysql.cursors.DictCursor) as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql)

        for data in tqdm(cur.fetchall()):
            ctx.tp_records[data['bid']].append(__create_tp_record(data))


def load_tp_records(ctx):
    """
    获取充电站 tp 记录
    """
    __load_all_tp_records(ctx)

    if ctx.bid_path is None or ctx.bid_path == '':
        return

    valid_bids = set(x[0] for x in tsv.read_tsv(ctx.bid_path))
    del_bids = [bid for bid in ctx.tp_records if bid not in valid_bids]
    for bid in del_bids:
        del ctx.tp_records[bid]


def load_pois(ctx):
    """
    获取 poi 信息
    """
    ctx.pois |= get_pois_by_bid(list(ctx.tp_records.keys()))


def get_pois_by_bid(bids):
    """
    获取指定 bid 的 poi 信息
    """
    pois = {}

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid in tqdm(bids):
            poi = get_poi_by_bid(poi_stab, bid)
            if poi is None:
                continue

            pois[bid] = poi

    return pois


def get_poi_by_bid(poi_stab, bid):
    """
    获取指定 bid 的 poi 信息
    """
    sql = '''
        select name, 
               alias, 
               address, 
               st_astext(geometry), 
               telephone, 
               status, 
               std_tag, 
               show_tag, 
               click_pv, 
               city,
               arrive_x,
               arrive_y
        from poi 
        where bid = %s;
    '''

    row = poi_stab.fetch_one(sql, [bid])
    if row is None:
        return None

    name, alias, address, wkt, telephone, status, std_tag, show_tag, click_pv, city, arrive_x, arrive_y = row
    return Poi(
        bid=bid,
        name=name,
        alias=alias,
        address=address,
        geom=shapely.wkt.loads(wkt),
        arrive_geom=Point(arrive_x, arrive_y),
        phone=telephone,
        status=status,
        tag=std_tag,
        show_tag=show_tag,
        click_pv=click_pv,
        city=city,
    )


def __approach_to_competitor_address(address: str):
    """
    拟合竞品地址

    对于直辖市我们和竞品的命名方式有所区别：
    我们：北京市xx区xx路xx号
    竞品：北京市北京市xx区xx路xx号
    """
    municipal_cities = {
        '北京市',
        '天津市',
        '上海市',
        '重庆市',
    }

    for city in municipal_cities:
        if address.startswith(city):
            return f'{city * 2}{address.lstrip(city)}'

    return address


def __approach_to_competitor_name(name: str):
    """
    拟合竞品名称

    对于充电站我们和竞品的命名方式有所区别：
    我们：xx充电站
    竞品：xx汽车充电站
    """
    return name.replace("充电站", "汽车充电站", 1)


def __get_competitors_in_range(poi_stab, station_geom, search_radius):
    """
    获取指定范围内的竞品
    """
    sql = '''
        select competitor_id, tag, name, address, telephone, st_astext(geom)
        from cdz_competitor
        where st_contains(st_geomfromtext(%s, 4326), geom) and
              crawl_time >= extract(epoch from (now() - interval '1 months'))::integer;
    '''
    buffered_geom = station_geom.buffer(search_radius)

    return [
        Competitor(
            id=competitor_id,
            tag=tag,
            name=name,
            address=address,
            phone=telephone,
            geom=shapely.wkt.loads(wkt),
        )
        for competitor_id, tag, name, address, telephone, wkt in poi_stab.fetch_all(sql, [buffered_geom.wkt])
    ]


def __get_competitors_in_range_with_service_area_in_mind(poi_stab, road_stab, station_geom, search_radius):
    """
    获取指定范围内的竞品（附带考虑到服务区的一些逻辑）
    """
    sql = '''
        select 1
        from nav_link 
        where st_intersects(st_geomfromtext(%s, 4326), geom) and
              kind = 1;
    '''
    valid_competitors = []

    for competitor in __get_competitors_in_range(poi_stab, station_geom, search_radius):
        link = LineString([station_geom, competitor.geom])
        row = road_stab.fetch_one(sql, [link.wkt])
        if row is not None:
            # 跨高速的竞品需要忽略
            continue

        valid_competitors.append(competitor)

    return valid_competitors


def __get_max_address_iou_competitor(poi_stab, road_stab, records, competitor_search_radius):
    """
    获取地址相似度最大的竞品
    """
    max_address_iou = 0
    matched_record = None
    matched_competitor = None

    for record in records:
        competitors = __get_competitors_in_range_with_service_area_in_mind(
            poi_stab,
            road_stab,
            record.station_geom,
            competitor_search_radius
        ) if '服务区' in record.station_name else __get_competitors_in_range(
            poi_stab,
            record.station_geom,
            competitor_search_radius
        )

        for competitor in competitors:
            tp_address = __approach_to_competitor_address(record.address)
            address_iou = get_str_iou(tp_address, competitor.address)

            if address_iou > max_address_iou:
                matched_record = record
                matched_competitor = competitor
                max_address_iou = address_iou

    return matched_record, matched_competitor, max_address_iou


def __match_tp_by_competitor_address(poi_stab, road_stab, records, kwargs):
    """
    通过竞品地址匹配最佳 tp
    """
    competitor_tp_min_address_iou = kwargs.get('competitor_tp_min_address_iou', 0.99)
    competitor_tp_max_distance = kwargs.get('competitor_tp_max_distance', 5e-5)
    competitor_search_radius = kwargs.get('competitor_search_radius', 1000e-5)

    matched_record, matched_competitor, max_address_iou = __get_max_address_iou_competitor(
        poi_stab,
        road_stab,
        records,
        competitor_search_radius
    )

    if max_address_iou < competitor_tp_min_address_iou:
        return None, None

    if matched_record.station_geom.distance(matched_competitor.geom) > competitor_tp_max_distance:
        return None, None

    matched_record.src, matched_competitor.name_iou = 'competitor-tp address', max_address_iou
    return matched_record, matched_competitor


def __get_max_name_iou_competitor_by_tp(poi_stab, road_stab, records, competitor_search_radius):
    """
    获取和 tp 名称相似度最大的竞品
    """
    max_name_iou = 0
    matched_record = None
    matched_competitor = None

    for record in records:
        competitors = __get_competitors_in_range_with_service_area_in_mind(
            poi_stab,
            road_stab,
            record.station_geom,
            competitor_search_radius
        ) if '服务区' in record.station_name else __get_competitors_in_range(
            poi_stab,
            record.station_geom,
            competitor_search_radius
        )

        for competitor in competitors:
            tp_name = __approach_to_competitor_name(get_desired_poi_name(record.third_name, record.station_name))
            name_iou = get_str_iou(tp_name, competitor.name)

            if name_iou > max_name_iou:
                matched_record = record
                matched_competitor = competitor
                max_name_iou = name_iou

    return matched_record, matched_competitor, max_name_iou


def get_max_name_iou_competitor_by_prop(poi_stab, road_stab, name, geom, competitor_search_radius):
    """
    获取名称相似度最大的竞品
    """
    max_name_iou = 0
    matched_competitor = None

    competitors = __get_competitors_in_range_with_service_area_in_mind(
        poi_stab,
        road_stab,
        geom,
        competitor_search_radius
    ) if '服务区' in name else __get_competitors_in_range(
        poi_stab,
        geom,
        competitor_search_radius
    )

    for competitor in competitors:
        poi_name = __approach_to_competitor_name(name)
        name_iou = get_str_iou(poi_name, competitor.name)

        if name_iou > max_name_iou:
            matched_competitor = competitor
            max_name_iou = name_iou

    return matched_competitor, max_name_iou


def get_max_name_iou_competitor_by_poi(poi_stab, road_stab, poi, competitor_search_radius):
    """
    获取和 poi 名称相似度最大的竞品
    """
    return get_max_name_iou_competitor_by_prop(poi_stab, road_stab, poi.name, poi.geom, competitor_search_radius)


def __match_tp_by_competitor_name(poi_stab, road_stab, records, kwargs):
    """
    通过竞品名称匹配最佳 tp
    """
    competitor_tp_min_name_iou = kwargs.get('competitor_tp_min_name_iou', 0.8)
    competitor_tp_max_distance = kwargs.get('competitor_tp_max_distance', 5e-5)
    competitor_search_radius = kwargs.get('competitor_search_radius', 1000e-5)

    matched_record, matched_competitor, max_name_iou = __get_max_name_iou_competitor_by_tp(
        poi_stab,
        road_stab,
        records,
        competitor_search_radius
    )

    if max_name_iou < competitor_tp_min_name_iou:
        return None, None

    if matched_record.station_geom.distance(matched_competitor.geom) > competitor_tp_max_distance:
        return None, None

    matched_record.src, matched_competitor.name_iou = 'competitor-tp name', max_name_iou
    return matched_record, matched_competitor


def __get_matched_name_iou_tp_records(poi: Poi, records, min_name_iou):
    """
    获取名称相似度大于阈值的 tp 记录
    """
    matched_iou_records = []

    for record in records:
        desired_name = get_desired_poi_name(record.third_name, record.station_name)
        name_iou = get_str_iou(poi.name, desired_name)
        if name_iou >= min_name_iou:
            matched_iou_records.append(record)

    return matched_iou_records


def __get_max_name_iou_tp_record(poi: Poi, records):
    """
    获取名称相似度最大的 tp 记录
    """
    max_name_iou = 0
    matched_record = None

    for record in records:
        desired_name = get_desired_poi_name(record.third_name, record.station_name)
        name_iou = get_str_iou(poi.name, desired_name)
        if name_iou > max_name_iou:
            matched_record = record
            max_name_iou = name_iou

    return matched_record, max_name_iou


def __match_tp_by_competitor(ctx: Context, create_record: Callable[..., Any], kwargs):
    """
    通过竞品匹配最佳 tp
    """
    competitor_tp_min_name_iou = kwargs.get('competitor_tp_min_name_iou', 0.6)

    with (
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab,
        PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab,
    ):
        for bid, records in tqdm(list(ctx.get_unmatched_tp_records().items())):
            poi = ctx.pois[bid]
            matched_iou_records = __get_matched_name_iou_tp_records(poi, records, competitor_tp_min_name_iou)

            matched_record, matched_competitor = __match_tp_by_competitor_address(
                poi_stab,
                road_stab,
                matched_iou_records,
                kwargs
            )
            if matched_record is not None:
                ctx.records.append(create_record(tp=matched_record, poi=ctx.pois[bid], competitor=matched_competitor))
                continue

            matched_record, matched_competitor = __match_tp_by_competitor_name(
                poi_stab,
                road_stab,
                matched_iou_records,
                kwargs
            )
            if matched_record is not None:
                ctx.records.append(create_record(tp=matched_record, poi=ctx.pois[bid], competitor=matched_competitor))


def __is_same_tp_records(records: list[TpRecord]):
    """
    判断给定的 tp 属性是否一致
    """
    is_same_third_name = len(set(x.third_name for x in records)) == 1
    is_same_station_name = len(set(x.station_name for x in records)) == 1
    is_same_address = len(set(x.address for x in records)) == 1
    is_same_geom = len(set(x.station_geom.wkt for x in records)) == 1

    return is_same_third_name and is_same_station_name and is_same_address and is_same_geom


def __match_trusty_tp_by_name(ctx: Context, create_record: Callable[..., Any], kwargs):
    """
    通过名称相似度匹配最佳高准 tp
    """
    trusty_poi_tp_min_name_iou = kwargs.get('trusty_poi_tp_min_name_iou', 0.6)

    for bid, records in tqdm(list(ctx.get_unmatched_tp_records().items())):
        poi = ctx.pois[bid]

        # 如果 tp 记录相同，则直接选择第一个。（暂时注释，待工艺评估。）
        # if __is_same_tp_records(records):
        #     matched_record = records[0]
        #     matched_record.src = 'same tp'
        #     ctx.records.append(create_record(tp=matched_record, poi=poi))
        #     continue

        matched_record, max_name_iou = __get_max_name_iou_tp_record(poi, records)
        if max_name_iou < trusty_poi_tp_min_name_iou or matched_record.third_name not in TRUSTY_THIRD_NAMES:
            continue

        matched_record.src = 'poi-tp name iou'
        ctx.records.append(create_record(tp=matched_record, poi=poi))


def __match_common_tp_by_name(ctx: Context, create_record: Callable[..., Any], kwargs):
    """
    通过名称相似度匹配最佳普通 tp
    """
    common_poi_tp_min_name_iou = kwargs.get('common_poi_tp_min_name_iou', 0.80)

    for bid, records in tqdm(list(ctx.get_unmatched_tp_records().items())):
        poi = ctx.pois[bid]

        # 如果 tp 记录相同，则直接选择第一个。（暂时注释，待工艺评估。）
        # if __is_same_tp_records(records):
        #     matched_record = records[0]
        #     matched_record.src = 'same tp'
        #     ctx.records.append(create_record(tp=matched_record, poi=poi))
        #     continue

        matched_record, max_name_iou = __get_max_name_iou_tp_record(poi, records)
        if matched_record is None or max_name_iou < common_poi_tp_min_name_iou:
            continue

        matched_record.src = 'manual'
        ctx.records.append(create_record(tp=matched_record, poi=poi))


def __match_single_tp(ctx: Context, create_record: Callable[..., Any]):
    """
    匹配单个 tp
    """
    for bid, records in tqdm(list(ctx.get_unmatched_tp_records().items())):
        if len(records) != 1:
            continue

        poi = ctx.pois[bid]
        matched_record = records[0]
        matched_record.src = 'manual'
        ctx.records.append(create_record(tp=matched_record, poi=poi))


def retain_best_matched_tp(ctx: Context, create_record: Callable[..., Any], **kwargs):
    """
    保留最佳匹配的 tp
    """
    if kwargs.get('match_trusty_tp_by_name', True):
        __match_trusty_tp_by_name(ctx, create_record, kwargs)

    if kwargs.get('match_competitor', False):
        __match_tp_by_competitor(ctx, create_record, kwargs)

    if kwargs.get('match_common_tp_by_name', True):
        __match_common_tp_by_name(ctx, create_record, kwargs)

    if kwargs.get('match_single_tp', False):
        __match_single_tp(ctx, create_record)


def load_white_board_records(ctx: Context, create_record: Callable[..., Any]):
    """
    加载白板记录
    """
    all_bids = set(get_all_charging_station_bids())
    binding_tp_bids = set(x.poi.bid for x in ctx.records)
    white_board_bids = all_bids - binding_tp_bids

    for poi in get_pois_by_bid(list(white_board_bids)).values():
        ctx.records.append(create_record(poi=poi))


def match_competitor_by_poi(ctx: Context, **kwargs):
    """
    根据 poi 匹配竞品
    """
    competitor_poi_min_name_iou = kwargs.get('competitor_poi_min_name_iou', 0.8)
    competitor_search_radius = kwargs.get('competitor_search_radius', 1000e-5)

    with (
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab,
        PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab,
    ):
        for record in tqdm(ctx.records):
            if record.competitor is not None:
                continue

            matched_competitor, max_name_iou = get_max_name_iou_competitor_by_poi(
                poi_stab,
                road_stab,
                record.poi,
                competitor_search_radius,
            )

            if max_name_iou < competitor_poi_min_name_iou:
                continue

            record.competitor = matched_competitor


def fill_poi_mc_wkt(ctx: Context):
    """
    填充 poi 墨卡托坐标
    """
    for record in tqdm([x for x in ctx.records if x.can_eval]):
        bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(record.poi.geom.x, record.poi.geom.y)
        mc_x, mc_y = mapio.utils.coord.bd09_to_mercator(bd_x, bd_y)
        record.poi.mc_wkt = f'POINT({mc_x} {mc_y})'


def fill_ticket_ref_urls(ctx):
    """
    填充工单的参考图片地址
    """
    sql = '''
        select pictures from charging_station where bid = %s; 
    '''

    with (
        get_mysql_connection('charging_station') as conn,
        conn.cursor() as cur,
    ):
        for ticket in tqdm([x for x in ctx.tickets if x.can_process]):
            if ticket.bid is None or ticket.bid == '':
                continue

            all_picture_urls = set()
            cur.execute(sql, (ticket.bid,))

            for picture_text, in cur.fetchall():
                picture_urls, reason = format_picture(picture_text=picture_text)
                all_picture_urls.update(picture_urls)

            ticket.ref_urls = list(all_picture_urls)


def create_tickets(ctx: Context, config: TicketConfig):
    """
    创建工单
    """
    for record in tqdm([x for x in ctx.records if x.can_manual_process]):
        ctx.tickets.append(Ticket(
            project=config.project,
            priority=config.priority,
            batch_id=config.batch_id,
            bid=record.poi.bid,
            method=config.method,
            message=record.work_message,
            src=config.src,
            suggest_poi=Poi(
                name=record.poi.name,
                alias=record.poi.alias,
                address=record.poi.address,
                geom=record.poi.geom,
                mc_geom=shapely.wkt.loads(record.poi.mc_wkt),
                phone=record.poi.phone,
                status=record.poi.status,
                tag=record.poi.tag,
            ),
            batch_name=config.batch_name,
        ))


def parse_args(mode_choices=None):
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['eval', 'manual', 'auto', 'all'] if mode_choices is None else mode_choices,
        default='eval',
        required=False,
    )
    parser.add_argument(
        '--bid-path',
        dest='bid_path',
        type=str,
        required=False,
    )
    return parser.parse_args()
