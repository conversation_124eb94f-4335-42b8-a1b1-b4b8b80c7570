# !/usr/bin/env python3
"""
例行修正充电站开放属性
"""
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin as auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import (
    Poi,
    RepairResult,
    TpRecord,
    RepairPropContext,
    TicketConfig,
    Competitor,

    OWNERSHIP_PRIVATE,
    OWNERSHIP_PUBLIC,
    OWNERSHIP_UNKNOWN,
    OWNERSHIP_REASON_TP_CONTAINS_PRIVATE_KEYS,
    OWNERSHIP_REASON_TP_CONTAINS_PUBLIC_KEYS,
    OWNERSHIP_REASON_POI_CONTAINS_PRIVATE_KEYS,
    OWNERSHIP_REASON_POI_CONTAINS_PUBLIC_KEYS,
    OWNERSHIP_REASON_TP_IS_PRIVATE_TYPE,
    OWNERSHIP_REASON_TP_IS_PUBLIC_TYPE,
)
from src.charging_station.helper import (
    get_manual_ownership_bids,
    get_ownership,
    get_ownership_by_station_name,
    get_all_white_list_bids,
)
from src.charging_station.online_prop import online_ownership
from src.tools import pipeline, tsv, pgsql

NAME = 'update_ownership'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = 'CDXJSK20241023004'
BATCH_NAME = '开放属性'
METHOD = 'edit'

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    充电站信息
    """
    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)
    competitor: Competitor = field(init=False)

    # 业务逻辑
    tp_ownership: str = ''
    poi_ownership: str = ''
    competitor_ownership: str = ''
    competitor_distance: float = 0.0
    fast_pile_count: int = 0
    slow_pile_count: int = 0
    pile_count: int = 0
    contains_fast_pile_key: bool = False
    contains_slow_pile_key: bool = False
    is_manual_worked: bool = False
    in_white_list: bool = False
    is_must_be_public: bool = False
    reason: str = ''

    def __init__(self, tp, poi, competitor):
        self.tp = tp
        self.poi = poi
        self.competitor = competitor

        if competitor is not None:
            self.competitor_distance = self.poi.geom.distance(self.competitor.geom)

    @property
    def work_message(self):
        return '疑似实际开放，请核实。' if self.tp_ownership == OWNERSHIP_PUBLIC else '疑似实际不开放，请核实。'

    @property
    def actual_ownership(self):
        """
        实际开放属性
        """
        private_tag = '不对外开放'
        public_tag = ''

        if self.is_must_be_public:
            return public_tag
        elif self.tp_ownership == OWNERSHIP_PRIVATE:
            return private_tag
        elif self.tp_ownership == OWNERSHIP_PUBLIC:
            return public_tag

        return public_tag

    @property
    def can_eval(self):
        return not self.in_white_list

    def can_auto_process_ignored_work_result(self):
        """
        是否可以在忽略作业成果的前提下自动处理
        """
        # 通过关键字匹配，这种可以无视作业成果。
        reasons_by_key = {
            OWNERSHIP_REASON_TP_CONTAINS_PRIVATE_KEYS,
            OWNERSHIP_REASON_TP_CONTAINS_PUBLIC_KEYS,
            OWNERSHIP_REASON_POI_CONTAINS_PRIVATE_KEYS,
            OWNERSHIP_REASON_POI_CONTAINS_PUBLIC_KEYS,
        }
        reasons_by_type = {
            OWNERSHIP_REASON_TP_IS_PRIVATE_TYPE,
            OWNERSHIP_REASON_TP_IS_PUBLIC_TYPE,
        }
        trusty_brands = {'依威能源'}
        contains_key = self.reason in reasons_by_key
        is_trusty_brand = self.tp is not None and self.tp.third_name in trusty_brands and self.reason in reasons_by_type

        return (
            (contains_key or is_trusty_brand) and
            self.tp_ownership != self.poi_ownership and
            self.tp_ownership != OWNERSHIP_UNKNOWN and
            self.poi_ownership != OWNERSHIP_UNKNOWN
        )

    def can_auto_process_considered_work_result(self):
        """
        是否可以在考虑作业成果的前提下自动处理
        """
        # 通过 station_type + 竞品匹配，这种需要过滤掉人工干预过的数据。
        max_competitor_distance = 100e-5
        valid_reasons = {
            OWNERSHIP_REASON_TP_IS_PRIVATE_TYPE,
            OWNERSHIP_REASON_TP_IS_PUBLIC_TYPE,
        }

        return (
            self.reason in valid_reasons and
            self.tp_ownership != self.poi_ownership and
            self.tp_ownership != OWNERSHIP_UNKNOWN and
            self.tp_ownership == self.competitor_ownership and
            self.competitor_distance < max_competitor_distance and
            self.poi_ownership != OWNERSHIP_UNKNOWN and
            not self.is_manual_worked
        )

    @property
    def can_auto_process(self):
        """
        是否可以自动处理
        """
        if not self.can_eval:
            return False

        return (
            (self.is_must_be_public and self.poi_ownership == OWNERSHIP_PRIVATE) or
            self.can_auto_process_ignored_work_result() or
            self.can_auto_process_considered_work_result()
        )

    @property
    def is_all_fast_pile(self):
        """
        是否都是快充
        """
        return self.fast_pile_count > 0 and self.slow_pile_count == 0

    @property
    def is_fast_pile_enough(self):
        """
        快充数量是否足够
        """
        min_fast_pile_count = 10

        return self.fast_pile_count >= min_fast_pile_count

    @property
    def can_manual_process(self):
        """
        是否可以人工处理
        """
        if not self.can_eval:
            return False

        if self.can_auto_process:
            return False

        # 倾向于从对内做成开放
        is_competitor_tp_different = (
            self.poi_ownership == OWNERSHIP_PRIVATE and
            self.competitor_ownership == OWNERSHIP_PUBLIC and
            (
                self.tp_ownership == OWNERSHIP_PRIVATE or
                self.tp_ownership == OWNERSHIP_UNKNOWN
            )
        )
        is_pile_count_strange = (
            self.poi_ownership == OWNERSHIP_PRIVATE and
            self.is_all_fast_pile and
            self.is_fast_pile_enough
        )

        return is_competitor_tp_different or is_pile_count_strange


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
            competitor=kwargs.get('competitor', None),
        ),
        match_competitor=True,
        match_single_tp=True,
        common_poi_tp_min_name_iou=0.6,
    )
    proceed()


@desc()
def load_white_board_records(ctx: Context, proceed):
    """
    加载白板记录
    """
    auto_repair_mixin.load_white_board_records(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            poi=kwargs['poi'],
            tp=None,
            competitor=None,
        ),
    )
    proceed()


@desc()
def match_competitor_by_poi(ctx: Context, proceed):
    """
    根据 poi 匹配竞品
    """
    auto_repair_mixin.match_competitor_by_poi(ctx)
    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    auto_repair_mixin.fill_poi_mc_wkt(ctx)
    proceed()


@desc()
def fill_tp_ownership(ctx: Context, proceed):
    """
    填充 tp 的开放属性
    """
    for record in tqdm(ctx.records):
        ownership = get_ownership_by_station_name(record.poi.name)
        if ownership == OWNERSHIP_PRIVATE:
            record.tp_ownership = ownership
            record.reason = OWNERSHIP_REASON_POI_CONTAINS_PRIVATE_KEYS
            continue

        if ownership == OWNERSHIP_PUBLIC:
            record.tp_ownership = ownership
            record.reason = OWNERSHIP_REASON_POI_CONTAINS_PUBLIC_KEYS
            continue

        if record.tp is None:
            record.tp_ownership = OWNERSHIP_UNKNOWN
            continue

        record.tp_ownership, record.reason = get_ownership(record.tp.station_name, record.tp.station_type)

    proceed()


@desc()
def fill_poi_ownership(ctx: Context, proceed):
    """
    填充 poi 的开放属性
    """
    public_type = 1

    sql = '''
        select online_type from cdz_history where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm(ctx.records):
            row = poi_stab.fetch_one(sql, [record.poi.bid])
            if row is None:
                record.poi_ownership = OWNERSHIP_UNKNOWN
                continue

            if row[0] == public_type:
                record.poi_ownership = OWNERSHIP_PUBLIC
            else:
                record.poi_ownership = OWNERSHIP_PRIVATE

    proceed()


@desc()
def fill_competitor_ownership(ctx: Context, proceed):
    """
    填充竞品的开放属性
    """
    private_tag = '汽车服务;充电站;个人充电站'

    for record in tqdm(ctx.records):
        if record.competitor is None:
            record.competitor_ownership = OWNERSHIP_UNKNOWN
            continue

        ownership = get_ownership_by_station_name(record.competitor.name)
        if ownership != OWNERSHIP_UNKNOWN:
            record.competitor_ownership = ownership
            continue

        if private_tag in record.competitor.tag:
            record.competitor_ownership = OWNERSHIP_PRIVATE
        else:
            record.competitor_ownership = OWNERSHIP_PUBLIC

    proceed()


@desc()
def fill_is_must_be_public(ctx: Context, proceed):
    """
    判断是否必须是公开的
    """
    public_keys = {'服务区', '逸安启'}
    public_brands = {'逸安启'}
    error_brands = {'蒙马充电', '特来电'}
    type_bus = 100

    for record in tqdm(ctx.records):
        record.is_must_be_public = (
            any(key in record.poi.name for key in public_keys) or
            any(brand in record.poi.name for brand in public_brands) or
            (record.tp is not None and any(brand == record.tp.third_name for brand in public_brands))
        )

        if record.tp is not None and record.tp.third_name in error_brands and record.tp.station_type == type_bus:
            record.is_must_be_public = record.competitor_ownership != OWNERSHIP_PRIVATE

    proceed()


@desc()
def fill_pile_count(ctx: Context, proceed):
    connector_type_fast = 4
    fast_keys = {'快充'}
    slow_keys = {'慢充'}
    sql = '''
        select connector_type 
        from charging_equipment
        where thirdcode = %s and
              station_id = %s;
    '''

    if datetime.today().weekday() != 5:  # 仅周六执行
        proceed()
        return

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        for record in tqdm(ctx.records):
            if record.tp is None:
                continue

            record.contains_fast_pile_key = any(key in record.tp.station_name for key in fast_keys)
            record.contains_slow_pile_key = any(key in record.tp.station_name for key in slow_keys)

            cur.execute(sql, [record.tp.third_code, record.tp.station_id])
            for connector_type, in cur.fetchall():
                record.pile_count += 1
                if connector_type == connector_type_fast:
                    record.fast_pile_count += 1
                else:
                    record.slow_pile_count += 1

    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    auto_repair_mixin.create_tickets(ctx, TicketConfig(
        project=PROJECT,
        priority=PRIORITY,
        batch_id=BATCH,
        method=METHOD,
        src=NAME,
        batch_name=BATCH_NAME,
    ))
    proceed()


@desc()
def fill_batch_conditions(ctx: Context, proceed):
    """
    填充批处理条件
    """
    manual_bids = get_manual_ownership_bids()
    white_list_bids = get_all_white_list_bids()

    for record in tqdm(ctx.records):
        if record.poi.bid in manual_bids:
            record.is_manual_worked = True

        if record.poi.bid in white_list_bids:
            record.in_white_list = True

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    ctx.batch_records = [
        Poi(bid=x.poi.bid, ownership=x.actual_ownership)
        for x in ctx.records if x.can_auto_process
    ]
    online_ownership(ctx.batch_records)
    proceed()


def save_records(file_path, records: list[Record]):
    """
    保存记录
    """
    tsv.write_tsv(
        file_path,
        [
            [
                x.tp.id if x.tp is not None else "",
                x.tp.station_id if x.tp is not None else "",
                x.tp.station_name if x.tp is not None else "",
                x.tp.station_type if x.tp is not None else "",

                x.tp.third_code if x.tp is not None else "",
                x.tp.third_name if x.tp is not None else "",

                x.poi.bid,
                x.poi.name,
                x.poi.alias,
                x.poi.address,
                x.poi.phone,
                x.poi.status,
                x.poi.tag,
                x.poi.wkt,
                x.poi.mc_wkt,

                x.reason,
                x.tp_ownership,
                x.competitor_ownership,
                x.competitor_distance * 1e5,
                x.poi_ownership,
                x.pile_count,
                x.fast_pile_count,
                x.slow_pile_count,
                x.contains_fast_pile_key,
                x.contains_slow_pile_key,
                x.tp.src if x.tp is not None else "",
                x.is_manual_worked,
                x.in_white_list,
            ]
            for x in records
        ]
    )


@desc()
def save_all_records(ctx: Context, proceed):
    """
    保存所有记录
    """
    save_records(ctx.work_dir / "all.csv", ctx.records)
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    save_records(ctx.work_dir / "eval.csv", [x for x in ctx.records if x.can_eval])
    proceed()


@desc()
def save_auto_records(ctx: Context, proceed):
    """
    保存自动处理记录
    """
    save_records(ctx.work_dir / "auto.csv", [x for x in ctx.records if x.can_auto_process])
    proceed()


@desc()
def save_manual_records(ctx: Context, proceed):
    """
    保存人工处理记录
    """
    save_records(ctx.work_dir / "manual.csv", [x for x in ctx.records if x.can_manual_process])
    proceed()


def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_records=ctx.batch_records,
        batch_name=BATCH_NAME,
    )


def create_pipeline(args, repair_context: RepairPropContext):
    """
    创建策略执行管道
    """
    mode = args.mode if args is not None else repair_context.mode
    print(mode)
    pipes = [
        load_tp_records,
        load_pois,
    ] if repair_context is None else []

    pipes.extend([
        retain_best_matched_tp,
        load_white_board_records,
        match_competitor_by_poi,
        fill_tp_ownership,
        fill_poi_ownership,
        fill_competitor_ownership,
        fill_is_must_be_public,
        fill_pile_count,
        fill_batch_conditions,
    ])

    if mode == 'manual':
        pipes += [fill_poi_mc_wkt, create_tickets]
    elif mode == 'auto':
        pipes += [fill_poi_mc_wkt, execute_batch]
    elif mode == 'all':
        pipes += [fill_poi_mc_wkt, create_tickets, execute_batch]

    pipes.extend([
        save_all_records,
        save_eval_records,
        save_auto_records,
        save_manual_records,
    ])

    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """
    主函数
    """
    main_pipe = create_pipeline(args, repair_context)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_repair_ownership'),
        bid_path=args.bid_path if args is not None else None,
    )

    if repair_context is not None:
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois

    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())
