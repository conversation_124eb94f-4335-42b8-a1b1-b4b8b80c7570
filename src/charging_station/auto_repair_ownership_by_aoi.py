# !/usr/bin/env python3
"""
例行修正充电站开放属性
"""
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import time
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin as auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import (
    Poi,
    RepairResult,
    TpRecord,
    RepairPropContext,
    TicketConfig,
    Competitor,

    OWNERSHIP_PRIVATE,
    OWNERSHIP_PUBLIC,
    OWNERSHIP_UNKNOWN,
    OWNERSHIP_REASON_POI_CONTAINS_PRIVATE_KEYS,
    OWNERSHIP_REASON_POI_CONTAINS_PUBLIC_KEYS,
    OWNERSHIP_REASON_AOI_CONTAINS_PUBLIC_TAGS,
    OWNERSHIP_REASON_AOI_CONTAINS_PRIVATE_TAGS,
    OWNERSHIP_REASON_TP_EXTRA_INFO
)
from src.charging_station.helper import (
    get_manual_ownership_bids,
    get_ownership,
    get_ownership_by_station_name,
    get_all_white_list_bids,
)
from src.charging_station.online_prop import online_ownership
from src.tools import pipeline, tsv, pgsql, notice_tool
from src.charging_station import find_aoi_cover_kind
from src.charging_station.find_aoi_cover_kind import Aoi
from src.charging_station.check_tp_ownership_by_ai import check_tp_information_by_ai
import re

NAME = 'update_ownership'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = 'CDXJSK20241023004'
BATCH_NAME = '开放属性'
METHOD = 'edit'

desc = pipeline.get_desc()

OWNERSHIP_REASON_AOI_EDUCATION_FAR = 'aoi education far from boundary'
OWNERSHIP_REASON_AOI_EDUCATION_NEAR = 'aoi education near from boundary'


@dataclass
class Record:
    """
    充电站信息
    """
    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)
    competitor: Competitor = field(init=False)
    aois: []

    # 业务逻辑
    tp_ownership: str = ''
    poi_ownership: str = ''
    competitor_ownership: str = ''
    competitor_distance: float = 0.0
    fast_pile_count: int = 0
    slow_pile_count: int = 0
    pile_count: int = 0
    aoi_kind: str = ''
    contains_fast_pile_key: bool = False
    contains_slow_pile_key: bool = False
    is_manual_worked: bool = False
    in_white_list: bool = False
    is_must_be_public: bool = False
    reason: str = ''
    flow_result: int = -1

    def __init__(self, tp, poi, competitor, aois):
        self.tp = tp
        self.poi = poi
        self.competitor = competitor
        self.aois = []

        if competitor is not None:
            self.competitor_distance = self.poi.geom.distance(self.competitor.geom)

    @property
    def work_message(self):
        """
        作业提示
        """
        return '疑似实际开放，请核实。' if self.tp_ownership == OWNERSHIP_PUBLIC else '疑似实际不开放，请核实。'

    @property
    def actual_ownership(self):
        """
        实际开放属性
        """
        private_tag = '不对外开放'
        public_tag = ''

        if self.is_must_be_public:
            return public_tag
        elif self.tp_ownership == OWNERSHIP_PRIVATE:
            return private_tag
        elif self.tp_ownership == OWNERSHIP_PUBLIC:
            return public_tag

        return public_tag

    @property
    def can_eval(self):
        """
        是否可以评估
        """
        return not self.in_white_list
        # return self.reason == OWNERSHIP_REASON_TP_EXTRA_INFO and self.poi_ownership != OWNERSHIP_PRIVATE

    def can_auto_process_ignored_work_result(self):
        """
        是否可以在忽略作业成果的前提下自动处理
        通过关键字匹配，这种可以无视作业成果。
        """
        return False

    def can_auto_process_considered_work_result(self):
        """
        是否可以在考虑作业成果的前提下自动处理
        通过 station_type + 竞品匹配，这种需要过滤掉人工干预过的数据。
        """
        max_competitor_distance = 100e-5
        aoi_competitor_public = (
                self.reason == OWNERSHIP_REASON_AOI_CONTAINS_PUBLIC_TAGS and
                self.competitor_ownership == OWNERSHIP_PUBLIC and
                self.competitor_distance < max_competitor_distance and
                self.poi_ownership == OWNERSHIP_PRIVATE
        )
        tp_aoi_private = (self.reason == OWNERSHIP_REASON_TP_EXTRA_INFO and
                          self.poi_ownership == OWNERSHIP_PUBLIC
                          )
        aoi_education_far = (self.reason == OWNERSHIP_REASON_AOI_EDUCATION_FAR)
        return (aoi_competitor_public or tp_aoi_private or aoi_education_far) and not self.is_manual_worked

    @property
    def can_auto_process(self):
        """
        是否可以自动处理
        """
        if not self.can_eval:
            return False

        return (
            self.can_auto_process_considered_work_result()
        )

    @property
    def can_manual_process(self):
        """
        是否可以人工处理
        """
        if not self.can_eval:
            return False

        if self.can_auto_process:
            return False

        is_aoi_public = (
                self.reason == OWNERSHIP_REASON_AOI_CONTAINS_PUBLIC_TAGS and
                self.poi_ownership == OWNERSHIP_PRIVATE and
                self.competitor_ownership != OWNERSHIP_PUBLIC
        )
        is_aoi_private = (
                self.reason == OWNERSHIP_REASON_AOI_CONTAINS_PRIVATE_TAGS and
                self.poi_ownership == OWNERSHIP_PUBLIC and
                self.competitor_ownership == OWNERSHIP_PRIVATE
        )
        aoi_education_near = (self.reason == OWNERSHIP_REASON_AOI_EDUCATION_NEAR)
        return is_aoi_public or is_aoi_private or aoi_education_near


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
            competitor=kwargs.get('competitor', None),
            aois=[]
        ),
        match_competitor=True,
        match_single_tp=True,
        common_poi_tp_min_name_iou=0.6,
    )
    proceed()


@desc()
def load_white_board_records(ctx: Context, proceed):
    """
    加载白板记录
    """
    auto_repair_mixin.load_white_board_records(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            poi=kwargs['poi'],
            tp=None,
            competitor=None,
            aois=[]
        ),
    )
    proceed()


@desc()
def match_competitor_by_poi(ctx: Context, proceed):
    """
    根据 poi 匹配竞品
    """
    auto_repair_mixin.match_competitor_by_poi(ctx)
    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    auto_repair_mixin.fill_poi_mc_wkt(ctx)
    proceed()


@desc()
def fill_tp_ownership(ctx: Context, proceed):
    """
    填充 tp 的开放属性
    """
    for record in tqdm(ctx.records):
        ownership = get_ownership_by_station_name(record.poi.name)
        if ownership == OWNERSHIP_PRIVATE:
            record.tp_ownership = ownership
            record.reason = OWNERSHIP_REASON_POI_CONTAINS_PRIVATE_KEYS
            continue

        if ownership == OWNERSHIP_PUBLIC:
            record.tp_ownership = ownership
            record.reason = OWNERSHIP_REASON_POI_CONTAINS_PUBLIC_KEYS
            continue

        if record.tp is None:
            record.tp_ownership = OWNERSHIP_UNKNOWN
            continue

        record.tp_ownership, record.reason = get_ownership(record.tp.station_name, record.tp.station_type)

    proceed()


@desc()
def fill_poi_ownership(ctx: Context, proceed):
    """
    填充 poi 的开放属性
    """
    public_type = 1

    sql = '''
        select online_type from cdz_history where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm(ctx.records):
            row = poi_stab.fetch_one(sql, [record.poi.bid])
            if row is None:
                record.poi_ownership = OWNERSHIP_UNKNOWN
                continue

            if row[0] == public_type:
                record.poi_ownership = OWNERSHIP_PUBLIC
            else:
                record.poi_ownership = OWNERSHIP_PRIVATE

    proceed()


@desc()
def fill_competitor_ownership(ctx: Context, proceed):
    """
    填充竞品的开放属性
    """
    private_tag = '汽车服务;充电站;个人充电站'

    for record in tqdm(ctx.records):
        if record.competitor is None:
            record.competitor_ownership = OWNERSHIP_UNKNOWN
            continue

        ownership = get_ownership_by_station_name(record.competitor.name)
        if ownership != OWNERSHIP_UNKNOWN:
            record.competitor_ownership = ownership
            continue

        if private_tag in record.competitor.tag:
            record.competitor_ownership = OWNERSHIP_PRIVATE
        else:
            record.competitor_ownership = OWNERSHIP_PUBLIC

    proceed()


@desc()
def fill_is_must_be_public(ctx: Context, proceed):
    """
    判断是否必须是公开的
    """
    public_key = '服务区'
    error_brands = {'蒙马充电', '特来电'}
    type_bus = 100

    for record in tqdm(ctx.records):
        record.is_must_be_public = public_key in record.poi.name

        if record.tp is not None and record.tp.third_name in error_brands and record.tp.station_type == type_bus:
            record.is_must_be_public = record.competitor_ownership != OWNERSHIP_PRIVATE

    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    auto_repair_mixin.create_tickets(ctx, TicketConfig(
        project=PROJECT,
        priority=PRIORITY,
        batch_id=BATCH,
        method=METHOD,
        src=NAME,
        batch_name=BATCH_NAME,
    ))
    proceed()


@desc()
def fill_batch_conditions(ctx: Context, proceed):
    """
    填充批处理条件
    """
    manual_bids = get_manual_ownership_bids()
    white_list_bids = get_all_white_list_bids()

    for record in tqdm(ctx.records):
        if record.poi.bid in manual_bids:
            record.is_manual_worked = True

        if record.poi.bid in white_list_bids:
            record.in_white_list = True

    proceed()


@desc()
def fill_aoi_kind(ctx: Context, proceed):
    """
    填充aoi类型
    """
    public_tag = ['交通设施', '旅游景点', '购物', '酒店']
    public_tag_not_include = ['公交车', '长途汽车', '停车']
    private_tag = ['政府机构', '房地产;住宅区', '公司企业;园区', '公司企业;公司']
    education_tag = ['教育培训;高等院校', '教育培训;中学', '教育培训;小学']
    BOUNDARY_DIS = 30
    for record in tqdm(ctx.records):
        poi = record.poi
        aois = find_aoi_cover_kind.find_aoi_by_poi(poi)
        if len(aois) == 0:
            continue
        record.aois.extend(aois)
        for aoi in aois:
            if aoi.dis != 0.0:  # 只考虑压盖的情况
                continue
            # 教育垂类
            for tag in education_tag:
                if tag in aoi.std_tag:
                    record.aoi_kind = tag
                    if record.reason == '':
                        if aoi.boundary_dis >= BOUNDARY_DIS:
                            record.reason = OWNERSHIP_REASON_AOI_EDUCATION_FAR
                        else:
                            record.reason = OWNERSHIP_REASON_AOI_EDUCATION_NEAR
                        break
            if record.reason != '':
                break
            # 垂类倾向对内
            for tag in private_tag:
                if tag in aoi.std_tag:
                    record.aoi_kind = tag
                    if record.reason == '':
                        record.reason = OWNERSHIP_REASON_AOI_CONTAINS_PRIVATE_TAGS
                    break
            if record.reason != '':
                break
            # 垂类倾向开放
            for tag in public_tag:
                if tag in aoi.std_tag:
                    if not any(item in aoi.std_tag for item in public_tag_not_include):
                        record.aoi_kind = tag
                        if record.reason == '':
                            record.reason = OWNERSHIP_REASON_AOI_CONTAINS_PUBLIC_TAGS
            if record.reason != '':
                break
    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    ctx.batch_records = [
        Poi(bid=x.poi.bid, ownership=x.actual_ownership)
        for x in ctx.records if x.can_auto_process
    ]
    online_ownership(ctx.batch_records)
    proceed()


def save_records(file_path, records: list[Record]):
    """
    保存记录
    """
    tsv.write_tsv(
        file_path,
        [
            [
                x.tp.id if x.tp is not None else "",
                x.tp.station_id if x.tp is not None else "",
                x.tp.station_name if x.tp is not None else "",
                x.tp.station_type if x.tp is not None else "",

                x.tp.third_code if x.tp is not None else "",
                x.tp.third_name if x.tp is not None else "",
                # x.tp.business_hours if x.tp is not None else "",
                # x.tp.park_info if x.tp is not None else "",
                # x.tp.park_fee if x.tp is not None else "",
                # x.tp.site_guide if x.tp is not None else "",
                x.flow_result,
                "'" + x.poi.bid,
                x.poi.name,
                x.poi.address,
                x.poi.status,
                # x.poi.mc_wkt,

                x.reason,
                x.tp_ownership,
                x.competitor_ownership,
                x.competitor_distance * 1e5,
                x.poi_ownership,
                x.aoi_kind,
                x.tp.src if x.tp is not None else "",
                x.is_manual_worked,
                x.in_white_list,
                x.aois[0].aoi_bid if len(x.aois) > 0 else '',
                x.aois[0].std_tag if len(x.aois) > 0 else '',
                x.aois[0].show_tag if len(x.aois) > 0 else '',
                x.aois[0].dis if len(x.aois) > 0 else '',
                x.aois[0].boundary_dis if len(x.aois) > 0 else '',
            ]
            for x in records
        ], mode="a"
    )


@desc()
def check_tp_information(ctx: Context, proceed):
    """
    检查TP信息
    """
    for record in tqdm(ctx.records):
        if record.tp is None:
            continue
        record.tp.description = ''
        key_str = ['不对外开放', '全天不对外', '不对外', '不对外充电', '不对社会车辆开放']
        key_word = ['内部站点', '内部专用']
        check_words = ['不对外', '不开放', '内部站点', '内部专用', '仅对内']
        if any(key in record.tp.business_hours for key in key_word) or any(
                str == record.tp.business_hours for str in key_str):
            record.reason = OWNERSHIP_REASON_TP_EXTRA_INFO
            continue

        extra_info = [record.tp.park_info, record.tp.park_fee, record.tp.site_guide]
        for word in extra_info:
            if any(key in word for key in check_words):
                record.tp.description = word
                record.reason = OWNERSHIP_REASON_TP_EXTRA_INFO
                break

    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行化推送负面评论清单脚本异常！{e}',
        atuserids=['zengjia01'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def send_to_ruliu(ctx, proceed):
    """
    如流通知
    """
    count = len(list(filter(lambda r: r.can_auto_process, ctx.records)))
    notice_tool.send_hi(
        f'例行化推送开放属性！数量{count}',
        atuserids=['zengjia01'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


@desc()
def save_all_records(ctx: Context, proceed):
    """
    保存所有记录
    """
    save_records(ctx.work_dir / ("all" + datetime.now().strftime("%Y-%m-%d_%H-%M") + ".tsv"), ctx.records)
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    save_records(ctx.work_dir / ("eval" + datetime.now().strftime("%Y-%m-%d_%H-%M") + ".tsv"),
                 [x for x in ctx.records if x.can_eval])
    proceed()


@desc()
def save_auto_records(ctx: Context, proceed):
    """
    保存自动处理记录
    """
    save_records(ctx.work_dir / ("auto" + datetime.now().strftime("%Y-%m-%d_%H-%M") + ".tsv"),
                 [x for x in ctx.records if x.can_auto_process])
    proceed()


@desc()
def save_manual_records(ctx: Context, proceed):
    """
    保存人工处理记录
    """
    save_records(ctx.work_dir / ("manual" + datetime.now().strftime("%Y-%m-%d_%H-%M") + ".tsv"),
                 [x for x in ctx.records if x.can_manual_process])
    proceed()


def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_records=ctx.batch_records,
        batch_name=BATCH_NAME,
    )


def create_pipeline(args, repair_context: RepairPropContext):
    """
    创建策略执行管道
    """
    mode = args.mode if args is not None else repair_context.mode
    print(mode)
    pipes = [
        load_tp_records,
        load_pois,
    ] if repair_context is None else []

    pipes.extend([
        retain_best_matched_tp,
        load_white_board_records,
        match_competitor_by_poi,
        fill_tp_ownership,
        fill_poi_ownership,
        fill_competitor_ownership,
        fill_is_must_be_public,
        fill_batch_conditions,
        fill_aoi_kind,
        check_tp_information_by_ai
    ])

    if mode == 'manual':
        pipes += [fill_poi_mc_wkt, create_tickets]
    elif mode == 'auto':
        pipes += [fill_poi_mc_wkt, execute_batch]
    elif mode == 'all':
        pipes += [fill_poi_mc_wkt, create_tickets, execute_batch]

    pipes.extend([
        save_all_records,
        save_eval_records,
        save_auto_records,
        save_manual_records,
        # send_to_ruliu,
    ])

    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """
    主函数
    """
    main_pipe = create_pipeline(args, repair_context)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_repair_ownership'),
        bid_path=args.bid_path,
    )

    if repair_context is not None:
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois

    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())
