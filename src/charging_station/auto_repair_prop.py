# !/usr/bin/env python3
"""
例行修正充电站任意属性
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin
from src.charging_station.data import Ticket, Poi, RepairResult, RepairPropContext
from src.tools import pipeline, pgsql, tsv

NAME = 'update_prop'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = 'CDXJSK20241023006'
BATCH_NAME = '全属性'
METHOD = 'edit'

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    充电站信息
    """
    bid: str
    work_message: str
    poi_name: str = ''
    poi_alias: str = ''
    poi_address: str = ''
    poi_wkt: str = ''
    poi_mc_wkt: str = ''
    poi_phone: str = ''
    poi_status: int = 1
    poi_tag: str = ''

    # 业务逻辑
    can_process: bool = True


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    date: datetime.date
    records: list[Record] = field(default_factory=list)
    tickets: list[Ticket] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_negative_feedback_records(ctx: Context, proceed):
    """
    加载负反馈记录
    """
    offset_days = 2
    date = (ctx.date - datetime.timedelta(days=offset_days)).strftime('%Y%m%d')
    sql = '''
        select end_bid, input_re
        from feedback_info
        where event_day = %s and
              stars = 1 and
              input_re is not null and
              input_re != '' and
              tag_child = '充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, description in tqdm(poi_stab.fetch_all(sql, [date])):
            ctx.records.append(Record(
                bid=bid,
                work_message=description,
            ))

    proceed()


@desc()
def load_negative_comment_records(ctx: Context, proceed):
    """
    加载负面评论
    """
    offset_days = 3
    date = (ctx.date - datetime.timedelta(days=offset_days)).strftime('%Y%m%d')
    sql = '''
        select bid, comment
        from cdz_comment
        where created_time::DATE = %s and
              flow_result = 1;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, comment in tqdm(poi_stab.fetch_all(sql, [date])):
            ctx.records.append(Record(
                bid=bid,
                work_message=comment,
            ))

    proceed()


@desc()
def fill_poi_properties(ctx: Context, proceed):
    """
    填充 poi 信息
    """
    sql = '''
        select name, alias, address, st_astext(geometry), telephone, status, std_tag from poi where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm(ctx.records):
            row = poi_stab.fetch_one(sql, [record.bid])
            if row is None:
                record.can_process = False
                continue

            name, alias, address, wkt, telephone, status, std_tag = row
            record.poi_name = name
            record.poi_alias = alias
            record.poi_address = address
            record.poi_wkt = wkt
            record.poi_phone = telephone
            record.poi_status = status
            record.poi_tag = std_tag

    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    sql = '''
        select st_astext(gcj2mc(st_geomfromtext(%s, 4326))); 
    '''

    with PgsqlStabilizer(pgsql.COMPUTE_CONFIG) as stab:
        for record in tqdm(ctx.records):
            if not record.can_process:
                continue

            record.poi_mc_wkt = stab.fetch_one(sql, [record.poi_wkt])[0]

    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    for record in tqdm([x for x in ctx.records if x.can_process]):
        ctx.tickets.append(Ticket(
            project=PROJECT,
            priority=PRIORITY,
            batch_id=BATCH,
            bid=record.bid,
            method=METHOD,
            message=record.work_message,
            src=NAME,
            suggest_poi=Poi(
                name=record.poi_name,
                alias=record.poi_alias,
                address=record.poi_address,
                geom=shapely.wkt.loads(record.poi_wkt),
                mc_geom=shapely.wkt.loads(record.poi_mc_wkt),
                phone=record.poi_phone,
                status=record.poi_status,
                tag=record.poi_tag,
            ),
            batch_name=BATCH_NAME,
        ))

    proceed()


def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / "manual.csv",
        [
            [
                x.bid,
                x.poi_name,
                x.poi_alias,
                x.poi_address,
                x.poi_phone,
                x.poi_status,
                x.poi_tag,
                x.poi_wkt,
                x.poi_mc_wkt,
                x.work_message,
            ]
            for x in ctx.records if x.can_process
        ]
    )
    proceed()


def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_name=BATCH_NAME,
    )


def create_pipeline(args, repair_context: RepairPropContext):
    """
    创建策略执行管道
    """
    mode = args.mode if args is not None else repair_context.mode
    print(mode)
    pipes = [
        load_negative_feedback_records,
        load_negative_comment_records,
        fill_poi_properties,
        fill_poi_mc_wkt,
        save_records,
    ]

    if mode == 'manual':
        pipes += [create_tickets]

    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """
    主函数
    """
    main_pipe = create_pipeline(args, repair_context)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_repair_prop'),
        date=datetime.date.today(),
    )
    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args(['eval', 'manual']))
