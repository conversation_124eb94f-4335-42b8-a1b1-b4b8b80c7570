# !/usr/bin/env python3
"""
例行修正充电站空间属性
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin as auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import (
    Poi,
    RepairResult,
    TpRecord,
    RepairPropContext,
    TicketConfig,

    SPACING_GROUND,
    SPACING_UNDERGROUND,
    SPACING_STRUCTURE,
    SPACING_UNKNOWN,
    SPACING_NAME_MAP,
    SPACING_VALUE_MAP,
)
from src.charging_station.helper import (
    is_structure,
    is_ground,
    is_underground,
    get_manual_spacing_bids,
    get_all_white_list_bids,
)
from src.charging_station.online_prop import online_spacing
from src.tools import pipeline, tsv, pgsql

NAME = 'update_spacing'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = 'CDXJSK20241023002'
BATCH_NAME = '空间属性'
METHOD = 'edit'

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    充电站信息
    """
    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)

    # 业务逻辑
    tp_spacing: str = SPACING_UNKNOWN
    poi_spacing: str = SPACING_UNKNOWN
    recognition_spacing: str = SPACING_UNKNOWN
    is_manual_worked: bool = False
    in_white_list: bool = False

    def __init__(self, tp, poi):
        self.tp = tp
        self.poi = poi
        self.poi_spacing = SPACING_NAME_MAP.get(poi.show_tag, SPACING_GROUND)

    @property
    def work_message(self):
        return '空间属性疑似错误，请核实。'

    @property
    def actual_spacing(self):
        """
        实际空间属性
        """
        return SPACING_VALUE_MAP[self.tp_spacing]

    @property
    def can_eval(self):
        if self.in_white_list or self.is_manual_worked:
            return False

        return (
            (
                self.poi_spacing != self.tp_spacing and
                self.tp_spacing != SPACING_UNKNOWN
            ) or
            (
                self.poi_spacing != self.recognition_spacing and
                self.recognition_spacing != SPACING_UNKNOWN
            )
        )

    @property
    def can_auto_process(self):
        return (
            self.can_eval and
            self.poi_spacing != SPACING_UNDERGROUND and  # 暂时只开放从非地下批处理成其它类型的。
            self.poi_spacing != self.tp_spacing and
            self.tp_spacing != SPACING_UNKNOWN and
            self.tp.src != 'manual'
        )

    @property
    def can_manual_process(self):
        return (
            self.can_eval and
            not self.can_auto_process and
            self.recognition_spacing != SPACING_UNKNOWN
        )


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
        ),
    )
    proceed()


@desc()
def load_white_board_records(ctx: Context, proceed):
    """
    加载白板记录
    """
    auto_repair_mixin.load_white_board_records(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            poi=kwargs['poi'],
            tp=None,
        ),
    )
    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    auto_repair_mixin.fill_poi_mc_wkt(ctx)
    proceed()


@desc()
def fill_tp_spacing(ctx: Context, proceed):
    """
    填充 tp 空间属性
    """
    for record in tqdm(ctx.records):
        if record.tp is None:
            continue

        values = [record.tp.station_name, record.tp.address, record.tp.park_info, record.tp.site_guide]
        spacing_map = dict()

        spacing_map[SPACING_STRUCTURE] = any(is_structure(value) for value in values)
        spacing_map[SPACING_GROUND] = any(is_ground(value) for value in values)
        spacing_map[SPACING_UNDERGROUND] = any(is_underground(value) for value in values)

        # 空间属性必须是互斥的，否则需要人工判断。
        if len([x for x in spacing_map.values() if x]) == 1:
            record.tp_spacing = list(spacing_map.keys())[list(spacing_map.values()).index(True)]
        else:
            record.tp_spacing = SPACING_GROUND

    proceed()


def fetch_underground_recognition_bids():
    """
    获取识别结果为地下的 bid 集合
    """
    sql = """
        select distinct bid 
        from cdz_picture_recognition 
        where result like '%地下停车场%';
    """

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        return [x[0] for x in stab.fetch_all(sql)]


@desc()
def fill_recognition_spacing(ctx: Context, proceed):
    """
    填充识别结果
    """
    underground_bids = set(fetch_underground_recognition_bids())

    for record in tqdm(ctx.records):
        if record.poi.bid in underground_bids:
            record.recognition_spacing = SPACING_UNDERGROUND

    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    auto_repair_mixin.create_tickets(ctx, TicketConfig(
        project=PROJECT,
        priority=PRIORITY,
        batch_id=BATCH,
        method=METHOD,
        src=NAME,
        batch_name=BATCH_NAME,
    ))
    proceed()


@desc()
def fill_batch_conditions(ctx: Context, proceed):
    """
    填充批处理条件
    """
    manual_bids = get_manual_spacing_bids()
    white_list_bids = get_all_white_list_bids()

    for record in tqdm(ctx.records):
        if record.poi.bid in manual_bids:
            record.is_manual_worked = True

        if record.poi.bid in white_list_bids:
            record.in_white_list = True

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    ctx.batch_records = [
        Poi(x.poi.bid, show_tag=x.actual_spacing)
        for x in ctx.records if x.can_auto_process
    ]
    online_spacing(ctx.batch_records)
    proceed()


def save_records(file_name, records: list[Record]):
    """
    保存记录
    """
    tsv.write_tsv(
        file_name,
        [
            [
                x.tp.id if x.tp is not None else "",
                x.tp.station_id if x.tp is not None else "",
                x.tp.station_name if x.tp is not None else "",
                x.tp.station_type if x.tp is not None else "",

                x.tp.third_code if x.tp is not None else "",
                x.tp.third_name if x.tp is not None else "",

                x.poi.bid,
                x.poi.name,
                x.poi.alias,
                x.poi.address,
                x.poi.phone,
                x.poi.status,
                x.poi.tag,
                x.poi.show_tag,
                x.poi.wkt,
                x.poi.mc_wkt,

                x.tp_spacing,
                x.poi_spacing,
                x.recognition_spacing,
                x.tp.src if x.tp is not None else "",
                x.is_manual_worked,
                x.in_white_list,
            ]
            for x in records
        ]
    )


@desc()
def save_all_records(ctx: Context, proceed):
    """
    保存所有记录
    """
    save_records(ctx.work_dir / "all.csv", ctx.records)
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    save_records(ctx.work_dir / "eval.csv", [x for x in ctx.records if x.can_eval])
    proceed()


@desc()
def save_auto_records(ctx: Context, proceed):
    """
    保存自动处理记录
    """
    save_records(ctx.work_dir / "auto.csv", [x for x in ctx.records if x.can_auto_process])
    proceed()


@desc()
def save_manual_records(ctx: Context, proceed):
    """
    保存人工处理记录
    """
    save_records(ctx.work_dir / "manual.csv", [x for x in ctx.records if x.can_manual_process])
    proceed()


def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_records=ctx.batch_records,
        batch_name=BATCH_NAME,
    )


def create_pipeline(args, repair_context: RepairPropContext):
    """
    创建策略执行管道
    """
    mode = args.mode if args is not None else repair_context.mode
    print(mode)
    pipes = [
        load_tp_records,
        load_pois,
    ] if repair_context is None else []

    pipes.extend([
        retain_best_matched_tp,
        load_white_board_records,
        fill_tp_spacing,
        fill_recognition_spacing,
        fill_batch_conditions,
    ])

    if mode == 'manual':
        pipes += [fill_poi_mc_wkt, create_tickets]
    elif mode == 'auto':
        pipes += [fill_poi_mc_wkt, execute_batch]
    elif mode == 'all':
        pipes += [fill_poi_mc_wkt, create_tickets, execute_batch]

    pipes.extend([
        save_all_records,
        save_eval_records,
        save_auto_records,
        save_manual_records,
    ])

    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """
    主函数
    """
    main_pipe = create_pipeline(args, repair_context)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_repair_spacing'),
        bid_path=args.bid_path if args is not None else None,
    )

    if repair_context is not None:
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois

    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())
