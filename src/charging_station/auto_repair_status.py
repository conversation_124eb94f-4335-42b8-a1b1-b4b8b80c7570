# !/usr/bin/env python3
"""
例行修正充电站状态
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.charging_station import auto_repair_mixin as auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import (
    Poi,
    RepairResult,
    TpRecord,
    RepairPropContext,
    TicketConfig,
    Competitor,

    STATUS_OFFLINE,
    STATUS_ONLINE,
    STATUS_UNKNOWN,
    STATUS_REASON_TP_CONTAINS_OFFLINE_KEYS,
    STATUS_REASON_TP_IS_OFFLINE_TYPE,
    STATUS_REASON_TP_IS_ONLINE_TYPE,
    STATUS_REASON_PIPE_IS_OFFLINE_TYPE,
    STATUS_REASON_PIPE_IS_ONLINE_TYPE,
)
from src.charging_station.helper import (
    get_manual_status_bids,
    get_all_white_list_bids,
    get_status_by_pipe,
    get_status_by_station_name,
    get_status_by_station_status,
)
from src.charging_station.online_prop import online_status
from src.tools import pipeline, tsv

NAME = 'update_status'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = 'CDXJSK20241023003'
BATCH_NAME = '状态'
METHOD = 'edit'

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    充电站信息
    """
    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)
    competitor: Competitor = field(init=False)

    # 业务逻辑
    tp_status: str = STATUS_UNKNOWN
    tp_status_reason: str = ''
    pile_status: str = STATUS_UNKNOWN
    pile_status_reason: str = ''
    poi_status: str = STATUS_UNKNOWN
    competitor_status: str = STATUS_ONLINE
    is_manual_worked: bool = False
    in_white_list: bool = False
    reason: str = ''

    def __init__(self, tp, poi, competitor):
        self.tp = tp
        self.poi = poi
        self.competitor = competitor
        self.poi_status = STATUS_ONLINE if poi.status == 1 else STATUS_OFFLINE

    @property
    def work_message(self):
        return '疑似实际状态正常，请核实。' if self.tp_status == STATUS_ONLINE else '疑似实际状态不在线，请核实。'

    @property
    def actual_status(self):
        """
        实际状态
        """
        status_open = 1
        status_close = 3

        return status_close if self.poi_status == STATUS_ONLINE else status_open

    @property
    def can_eval(self):
        if self.in_white_list or self.is_manual_worked:
            return False

        should_be_offline = (
            self.poi_status == STATUS_ONLINE and
            self.pile_status == STATUS_OFFLINE and
            (self.competitor_status == STATUS_UNKNOWN or self.competitor_status == STATUS_OFFLINE)
        )
        should_be_online = (
            self.poi_status == STATUS_OFFLINE and
            self.pile_status == STATUS_ONLINE and
            self.competitor_status == STATUS_ONLINE
        )

        return should_be_offline or should_be_online

    @property
    def can_auto_process(self):
        online_keys = {
            'e充电',
            '国家电网',
            '小鹏汽车',
            '小鹏',
            '开迈斯',
            '蒙马充电',
            '云快充',
        }

        if not self.can_eval:
            return False

        should_be_offline = (
            self.poi_status == STATUS_ONLINE and
            (
                self.tp_status == STATUS_OFFLINE or
                (
                    self.tp_status == STATUS_ONLINE and
                    (
                        any(x in self.poi.name for x in online_keys) or
                        (self.tp is not None and any(x in self.tp.third_name for x in online_keys))
                    )
                )
            )
        )
        should_be_online = (
            self.poi_status == STATUS_OFFLINE and
            self.tp_status == STATUS_ONLINE
        )

        return should_be_offline or should_be_online

    @property
    def can_manual_process(self):
        return self.can_eval and not self.can_auto_process


@desc()
def load_piles(ctx: Context, proceed):
    """
    加载充电站桩头信息
    """
    auto_repair_mixin.load_piles(ctx)
    proceed()


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
            competitor=kwargs.get('competitor', None),
        ),
        match_competitor=True,
        common_poi_tp_min_name_iou=0.6,
    )
    proceed()


@desc()
def load_white_board_records(ctx: Context, proceed):
    """
    加载白板记录
    """
    auto_repair_mixin.load_white_board_records(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            poi=kwargs['poi'],
            tp=None,
            competitor=None,
        ),
    )
    proceed()


@desc()
def match_competitor_by_poi(ctx: Context, proceed):
    """
    根据 poi 匹配竞品
    """
    auto_repair_mixin.match_competitor_by_poi(ctx, competitor_search_radius=100e-5)
    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    auto_repair_mixin.fill_poi_mc_wkt(ctx)
    proceed()


def estimate_pile_status(ctx: Context, record: Record):
    """
    根据桩的状态判断充电站状态
    """
    if record.tp is None:
        record.pile_status = STATUS_UNKNOWN
        return

    status_by_pipe = get_status_by_pipe(ctx.piles.get(record.tp.tid, []))

    if status_by_pipe == STATUS_ONLINE:
        record.pile_status = STATUS_ONLINE
        record.pile_status_reason = STATUS_REASON_PIPE_IS_ONLINE_TYPE
    elif status_by_pipe == STATUS_OFFLINE:
        record.pile_status = STATUS_OFFLINE
        record.pile_status_reason = STATUS_REASON_PIPE_IS_OFFLINE_TYPE


def estimate_tp_status(record: Record):
    """
    根据 tp 状态判断充电站状态
    """
    if record.tp is None:
        record.tp_status = STATUS_UNKNOWN
        return

    status_by_station_name = get_status_by_station_name(record.tp.station_name)
    status_by_station_status = get_status_by_station_status(record.tp.station_status)

    if status_by_station_name == STATUS_OFFLINE:
        record.tp_status = STATUS_OFFLINE
        record.tp_status_reason = STATUS_REASON_TP_CONTAINS_OFFLINE_KEYS
    elif status_by_station_status == STATUS_OFFLINE:
        record.tp_status = STATUS_OFFLINE
        record.tp_status_reason = STATUS_REASON_TP_IS_OFFLINE_TYPE
    elif status_by_station_status == STATUS_ONLINE:
        record.tp_status = STATUS_ONLINE
        record.tp_status_reason = STATUS_REASON_TP_IS_ONLINE_TYPE


@desc()
def fill_tp_status(ctx: Context, proceed):
    """
    填充 tp 状态
    """
    for record in tqdm(ctx.records):
        estimate_pile_status(ctx, record)
        estimate_tp_status(record)

    proceed()


@desc()
def fill_competitor_status(ctx: Context, proceed):
    """
    填充竞品的状态
    """
    for record in tqdm(ctx.records):
        if record.competitor is None:
            record.competitor_status = STATUS_UNKNOWN
            continue

        status = get_status_by_station_name(record.competitor.name)
        if status != STATUS_UNKNOWN:
            record.competitor_status = status

    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    auto_repair_mixin.create_tickets(ctx, TicketConfig(
        project=PROJECT,
        priority=PRIORITY,
        batch_id=BATCH,
        method=METHOD,
        src=NAME,
        batch_name=BATCH_NAME,
    ))
    proceed()


@desc()
def fill_batch_conditions(ctx: Context, proceed):
    """
    填充批处理条件
    """
    manual_bids = get_manual_status_bids()
    white_list_bids = get_all_white_list_bids()

    for record in tqdm(ctx.records):
        if record.poi.bid in manual_bids:
            record.is_manual_worked = True

        if record.poi.bid in white_list_bids:
            record.in_white_list = True

    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    ctx.batch_records = [
        Poi(bid=x.poi.bid, status=x.actual_status)
        for x in ctx.records if x.can_auto_process
    ]
    online_status(ctx.batch_records)
    proceed()


def save_records(file_path, records: list[Record]):
    """
    保存记录
    """
    tsv.write_tsv(
        file_path,
        [
            [
                x.tp.id if x.tp is not None else "",
                x.tp.station_id if x.tp is not None else "",
                x.tp.station_name if x.tp is not None else "",
                x.tp.station_status if x.tp is not None else "",

                x.tp.third_code if x.tp is not None else "",
                x.tp.third_name if x.tp is not None else "",

                x.poi.bid,
                x.poi.name,
                x.poi.alias,
                x.poi.address,
                x.poi.phone,
                x.poi.status,
                x.poi.tag,
                x.poi.wkt,
                x.poi.mc_wkt,

                x.poi_status,
                x.tp_status,
                x.tp_status_reason,
                x.pile_status,
                x.pile_status_reason,
                x.competitor_status,
                x.tp.src if x.tp is not None else "",
                x.is_manual_worked,
                x.in_white_list,
            ]
            for x in records
        ]
    )


@desc()
def save_all_records(ctx: Context, proceed):
    """
    保存所有记录
    """
    save_records(ctx.work_dir / "all.csv", ctx.records)
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    save_records(ctx.work_dir / "eval.csv", [x for x in ctx.records if x.can_eval])
    proceed()


@desc()
def save_auto_records(ctx: Context, proceed):
    """
    保存自动处理记录
    """
    save_records(ctx.work_dir / "auto.csv", [x for x in ctx.records if x.can_auto_process])
    proceed()


@desc()
def save_manual_records(ctx: Context, proceed):
    """
    保存人工处理记录
    """
    save_records(ctx.work_dir / "manual.csv", [x for x in ctx.records if x.can_manual_process])
    proceed()


def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_records=ctx.batch_records,
        batch_name=BATCH_NAME,
    )


def create_pipeline(args, repair_context: RepairPropContext):
    """
    创建策略执行管道
    """
    mode = args.mode if args is not None else repair_context.mode
    print(mode)
    pipes = [
        load_piles,
        load_tp_records,
        load_pois,
    ] if repair_context is None else []

    pipes.extend([
        retain_best_matched_tp,
        load_white_board_records,
        match_competitor_by_poi,
        fill_tp_status,
        fill_competitor_status,
        fill_batch_conditions,
    ])

    if mode == 'manual':
        pipes += [fill_poi_mc_wkt, create_tickets]
    elif mode == 'auto':
        pipes += [fill_poi_mc_wkt, execute_batch]
    elif mode == 'all':
        pipes += [fill_poi_mc_wkt, create_tickets, execute_batch]

    pipes.extend([
        save_all_records,
        save_eval_records,
        save_auto_records,
        save_manual_records,
    ])

    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """
    主函数
    """
    main_pipe = create_pipeline(args, repair_context)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_repair_status'),
        bid_path=args.bid_path if args is not None else None,
    )

    if repair_context is not None:
        ctx.piles = repair_context.piles
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois

    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())
