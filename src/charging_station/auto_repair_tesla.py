# !/usr/bin/env python3
"""
例行修正 tesla 充电站
"""
from dataclasses import dataclass, field
from pathlib import Path

from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import (
    RepairPropContext,
    TpRecord,
    Poi,
    RepairResult,

    STATUS_UNKNOWN,
    STATUS_OFFLINE,
    STATUS_ONLINE,
    STATUS_REASON_TP_CONTAINS_OFFLINE_KEYS,
    STATUS_REASON_TP_IS_OFFLINE_TYPE,
    STATUS_REASON_TP_IS_ONLINE_TYPE,

    SPACING_UNKNOWN,
    SPACING_STRUCTURE,
    SPACING_GROUND,
    SPACING_UNDERGROUND,
    SPACING_NAME_MAP,
    SPACING_VALUE_MAP,
)
from src.charging_station.helper import (
    get_all_white_list_bids,
    get_status_by_station_name,
    get_status_by_station_status,

    is_structure,
    is_ground,
    is_underground,
)
from src.charging_station.online_prop import online_gcj_location, online_status, online_spacing
from src.tools import pipeline, tsv, pgsql

NAME = 'update_tesla'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = ''
BATCH_NAME = '特斯拉'
METHOD = ''

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    充电站信息
    """
    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)

    # 业务逻辑
    # location
    distance: float = 0.0
    is_too_far_away: bool = False
    is_too_close: bool = False

    # status
    tp_status: str = STATUS_UNKNOWN
    tp_status_reason: str = ''
    poi_status: str = STATUS_UNKNOWN

    # spacing
    tp_spacing: str = SPACING_UNKNOWN
    poi_spacing: str = SPACING_UNKNOWN
    recognition_spacing: str = SPACING_UNKNOWN

    # other
    in_white_list: bool = False

    def __init__(self, tp: TpRecord, poi: Poi):
        self.tp = tp
        self.poi = poi
        self.distance = self.poi.geom.distance(self.tp.station_geom)
        self.poi_status = STATUS_ONLINE if poi.status == 1 else STATUS_OFFLINE
        self.poi_spacing = SPACING_NAME_MAP.get(poi.show_tag, SPACING_GROUND)

    @property
    def can_eval_location(self):
        """
        是否可以评估位置
        """
        return not self.in_white_list and self.distance > 0

    @property
    def can_eval_status(self):
        """
        是否可以评估状态
        """
        if self.in_white_list:
            return False

        return self.poi_status != self.tp_status and self.tp_status != STATUS_UNKNOWN

    @property
    def can_eval_spacing(self):
        """
        是否可以评估空间属性
        """
        if self.in_white_list:
            return False

        return (
            (
                self.poi_spacing != self.tp_spacing and
                self.tp_spacing != SPACING_UNKNOWN
            ) or
            (
                self.poi_spacing != self.recognition_spacing and
                self.recognition_spacing != SPACING_UNKNOWN
            )
        )

    @property
    def can_eval(self):
        """
        是否可以评估
        """
        return self.can_eval_location or self.can_eval_status or self.can_eval_spacing

    @property
    def can_auto_process_location(self):
        """
        是否可以自动处理位置
        """
        return (
            self.can_eval_location and
            self.tp.src != 'manual' and
            not self.is_too_close
        )

    @property
    def can_auto_process_status(self):
        """
        是否可以自动处理状态
        """
        return (
            self.can_eval_status and
            self.tp.src != 'manual'
        )

    @property
    def can_auto_process_spacing(self):
        """
        是否可以自动处理空间属性
        """
        return (
            self.can_eval_spacing and
            self.poi_spacing != SPACING_UNDERGROUND and  # 暂时只开放从非地下批处理成其它类型的。
            self.poi_spacing != self.tp_spacing and
            self.tp_spacing != SPACING_UNKNOWN and
            self.tp.src != 'manual'
        )

    @property
    def can_auto_process(self):
        """
        是否可以自动处理
        """
        return self.can_auto_process_location or self.can_auto_process_status or self.can_auto_process_spacing

    @property
    def actual_spacing(self):
        """
        实际空间属性
        """
        return SPACING_VALUE_MAP.get(self.tp_spacing, SPACING_UNKNOWN)

    @property
    def actual_status(self):
        """
        实际状态
        """
        status_open = 1
        status_close = 3

        return status_close if self.poi_status == STATUS_ONLINE else status_open


@desc()
def load_tp_records(ctx: Context, proceed):
    """
    加载充电站 tp 记录
    """
    auto_repair_mixin.load_tp_records(ctx)
    proceed()


@desc()
def load_pois(ctx: Context, proceed):
    """
    加载 poi 信息
    """
    auto_repair_mixin.load_pois(ctx)
    proceed()


@desc()
def filter_records(ctx: Context, proceed):
    """
    过滤批处理记录
    """
    del_bids = []
    for bid in ctx.tp_records:
        tp_records = ctx.tp_records[bid]
        if not any(x.third_name == '特斯拉' for x in tp_records):
            del_bids.append(bid)

    for bid in del_bids:
        ctx.tp_records.pop(bid, None)
        ctx.pois.pop(bid, None)

    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
        ),
        trusty_poi_tp_min_name_iou=0.8,
        match_common_tp_by_name=False,
        match_single_tp=True,
    )
    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    auto_repair_mixin.fill_poi_mc_wkt(ctx)
    proceed()


@desc()
def fill_distance_status(ctx: Context, proceed):
    """
    填充距离状态
    """
    max_distance = 15e-5
    min_distance = 10e-5

    for record in tqdm(ctx.records):
        record.is_too_far_away = record.distance > max_distance
        record.is_too_close = record.distance < min_distance

    proceed()


def estimate_tp_status(record: Record):
    """
    根据 tp 状态判断充电站状态
    """
    if record.tp is None:
        record.tp_status = STATUS_UNKNOWN
        return

    status_by_station_name = get_status_by_station_name(record.tp.station_name)
    status_by_station_status = get_status_by_station_status(record.tp.station_status)

    if status_by_station_name == STATUS_OFFLINE:
        record.tp_status = STATUS_OFFLINE
        record.tp_status_reason = STATUS_REASON_TP_CONTAINS_OFFLINE_KEYS
    elif status_by_station_status == STATUS_OFFLINE:
        record.tp_status = STATUS_OFFLINE
        record.tp_status_reason = STATUS_REASON_TP_IS_OFFLINE_TYPE
    elif status_by_station_status == STATUS_ONLINE:
        record.tp_status = STATUS_ONLINE
        record.tp_status_reason = STATUS_REASON_TP_IS_ONLINE_TYPE


@desc()
def fill_tp_status(ctx: Context, proceed):
    """
    填充 tp 状态
    """
    for record in tqdm(ctx.records):
        estimate_tp_status(record)

    proceed()


@desc()
def fill_tp_spacing(ctx: Context, proceed):
    """
    填充 tp 空间属性
    """
    for record in tqdm(ctx.records):
        if record.tp is None:
            continue

        values = [record.tp.station_name, record.tp.address, record.tp.park_info, record.tp.site_guide]
        spacing_map = dict()

        spacing_map[SPACING_STRUCTURE] = any(is_structure(value) for value in values)
        spacing_map[SPACING_GROUND] = any(is_ground(value) for value in values)
        spacing_map[SPACING_UNDERGROUND] = any(is_underground(value) for value in values)

        # 空间属性必须是互斥的，否则需要人工判断。
        if len([x for x in spacing_map.values() if x]) == 1:
            record.tp_spacing = list(spacing_map.keys())[list(spacing_map.values()).index(True)]
        else:
            record.tp_spacing = SPACING_UNKNOWN

    proceed()


def fetch_underground_recognition_bids():
    """
    获取识别结果为地下的 bid 集合
    """
    sql = """
        select distinct bid 
        from cdz_picture_recognition 
        where result like '%地下停车场%';
    """

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        return [x[0] for x in stab.fetch_all(sql)]


@desc()
def fill_recognition_spacing(ctx: Context, proceed):
    """
    填充识别结果
    """
    underground_bids = set(fetch_underground_recognition_bids())

    for record in tqdm(ctx.records):
        if record.poi.bid in underground_bids:
            record.recognition_spacing = SPACING_UNDERGROUND

    proceed()


@desc()
def fill_batch_conditions(ctx: Context, proceed):
    """
    填充批处理条件
    """
    white_list_bids = get_all_white_list_bids()

    for record in tqdm(ctx.records):
        if record.poi.bid in white_list_bids:
            record.in_white_list = True

    proceed()


@desc()
def execute_batch_location(ctx: Context, proceed):
    """
    执行位置批处理
    """
    batch_records = [
        Poi(bid=x.poi.bid, geom=x.tp.station_geom)
        for x in ctx.records if x.can_auto_process_location
    ]
    online_gcj_location(batch_records)
    ctx.batch_records.extend(batch_records)
    proceed()


@desc()
def execute_batch_status(ctx: Context, proceed):
    """
    执行状态批处理
    """
    batch_records = [
        Poi(bid=x.poi.bid, status=x.actual_status)
        for x in ctx.records if x.can_auto_process_status
    ]
    online_status(batch_records)
    ctx.batch_records.extend(batch_records)
    proceed()


@desc()
def execute_batch_spacing(ctx: Context, proceed):
    """
    执行批处理
    """
    batch_records = [
        Poi(x.poi.bid, show_tag=x.actual_spacing)
        for x in ctx.records if x.can_auto_process_spacing
    ]
    online_spacing(batch_records)
    ctx.batch_records.extend(batch_records)
    proceed()


def save_records(file_name, records: list[Record]):
    """
    保存记录
    """
    tsv.write_tsv(
        file_name,
        [
            [
                x.tp.id,
                x.tp.station_id,
                x.tp.station_name,
                x.tp.station_geom.wkt,
                x.tp.third_code,
                x.tp.third_name,
                x.poi.bid,
                x.tp.address,

                x.poi.name,
                x.poi.alias,
                x.poi.address,
                x.poi.phone,
                x.poi.status,
                x.poi.tag,
                x.poi.geom.wkt,
                x.poi.mc_wkt,

                x.distance * 1e5,
                x.is_too_far_away,
                x.is_too_close,

                x.poi_status,
                x.tp_status,
                x.tp_status_reason,

                x.poi_spacing,
                x.tp_spacing,
                x.recognition_spacing,

                x.tp.src,
                x.in_white_list,

                x.can_auto_process_location,
                x.tp.station_geom.wkt,
                x.can_auto_process_status,
                x.actual_status,
                x.can_auto_process_spacing,
                x.actual_spacing,
            ]
            for x in records
        ]
    )


@desc()
def save_all_records(ctx: Context, proceed):
    """
    保存所有记录
    """
    save_records(ctx.work_dir / "all.csv", ctx.records)
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    save_records(ctx.work_dir / "eval.csv", [x for x in ctx.records if x.can_eval])
    proceed()


@desc()
def save_auto_records(ctx: Context, proceed):
    """
    保存自动处理记录
    """
    save_records(ctx.work_dir / "auto.csv", [x for x in ctx.records if x.can_auto_process])
    proceed()


def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_records=ctx.batch_records,
        batch_name=BATCH_NAME,
    )


def create_pipeline(args, repair_context: RepairPropContext):
    """
    创建策略执行管道
    """
    mode = args.mode if args is not None else repair_context.mode
    print(mode)
    pipes = [
        load_tp_records,
        load_pois,
    ] if repair_context is None else []

    pipes.extend([
        filter_records,
        retain_best_matched_tp,

        fill_distance_status,
        fill_tp_status,
        fill_tp_spacing,
        fill_recognition_spacing,

        fill_batch_conditions,
        fill_poi_mc_wkt,
    ])

    if mode == 'auto':
        pipes.extend([
            execute_batch_location,
            execute_batch_status,
            execute_batch_spacing,
        ])

    pipes.extend([
        save_all_records,
        save_eval_records,
        save_auto_records,
    ])

    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """
    主函数
    """
    main_pipe = create_pipeline(args, repair_context)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/auto_repair_tesla"),
        bid_path=args.bid_path if args is not None else None,
    )

    if repair_context is not None:
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois

    main_pipe(ctx)
    return ctx


if __name__ == "__main__":
    main(args=auto_repair_mixin.parse_args())
