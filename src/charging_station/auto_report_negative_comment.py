# !/usr/bin/env python3
"""
例行化推送负面评论清单
"""
import datetime
import time
from dataclasses import dataclass, field
from pathlib import Path

import requests
from retrying import retry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv, notice_tool

FLOW_RESULT_NEGATIVE = '1'

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    批处理记录
    """
    uuid: str
    bid: str
    comment: str
    flow_result: str = ''


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    date: str
    save_path: Path
    upload_file_url: str
    download_file_url: str
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载负面评论记录
    """
    sql = '''
        select uuid, bid, comment
        from cdz_comment
        where created_time::DATE = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for uuid, bid, comment in poi_stab.fetch_all(sql, [ctx.date]):
            ctx.records.append(Record(
                uuid=uuid,
                bid=bid,
                comment=comment
            ))

    proceed()


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def __check_comment_by_flow(comment):
    """
    使用大模型对评论进行分类
    """
    flow_id = 8031
    api_url = f"http://iplayground-dev.cloudapi.baidu-int.com/api/rest/v1/flow/{flow_id}/build/predict"
    payload = {"message": comment}
    headers = {"Content-Type": "application/json", "X-release": "false"}
    response = requests.post(api_url, json=payload, headers=headers)
    response_json = response.json()

    return response_json['result']


@desc()
def check_comment_by_flow(ctx: Context, proceed):
    """
    使用大模型对评论进行分类
    """
    max_token_count = 100000
    token_count = sum(len(x.comment) for x in ctx.records)

    if token_count > max_token_count:
        raise Exception('token count too large')

    for record in tqdm(ctx.records):
        try:
            record.flow_result = __check_comment_by_flow(record.comment)
        except Exception as e:
            print(e)
        finally:
            time.sleep(1)

    proceed()


@desc()
def upload_flow_check_result(ctx: Context, proceed):
    """
    上传大模型分类结果
    """
    sql = '''
        update cdz_comment
        set flow_result = %s
        where uuid = %s;
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for record in tqdm(ctx.records):
                if record.flow_result != FLOW_RESULT_NEGATIVE:
                    continue

                cur.execute(sql, [1, record.uuid])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(upload_file_url, local_file_path):
    """
    上传文件
    """
    files = {'file': open(local_file_path, 'r', encoding='utf-8')}
    file_uuid = requests.post(upload_file_url, files=files).text
    if len(file_uuid) != 32:
        raise Exception(f'upload file failed')

    return file_uuid


@desc()
def save_records(ctx: Context, proceed):
    """
    保存负面评论记录
    """
    pending_save_records = [x for x in ctx.records if x.flow_result == FLOW_RESULT_NEGATIVE]
    if not pending_save_records:
        print('no records to process')
        return

    tsv.write_tsv(
        ctx.save_path,
        [
            [
                x.bid,
                x.flow_result,
                x.comment,
            ]
            for x in pending_save_records
        ]
    )
    proceed()


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    推送负面评论记录到如流
    """

    def upload_records(local_path: Path):
        if not local_path.exists():
            return None

        file_uuid = upload_file(ctx.upload_file_url, local_path)
        return f'{ctx.download_file_url}&uuid={file_uuid}'

    remote_url = upload_records(ctx.save_path)
    if remote_url is None:
        proceed()
        return

    total_count = len([x for x in ctx.records if x.flow_result == FLOW_RESULT_NEGATIVE])
    notice_tool.send_hi(
        f'充电桩 {ctx.date} 负面评论清单，量级 {total_count}，请抽空核实：{remote_url}',
        atuserids=['chenjie02_cd', 'songhuiling', 'linbin_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行化推送负面评论清单脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        check_comment_by_flow,
        upload_flow_check_result,
        save_records,
        send_records_to_infoflow,
    )
    desc.attach(main_pipe)
    date = (datetime.date.today() - datetime.timedelta(days=3)).strftime('%Y%m%d')
    work_dir = Path('cache/auto_report_negative_comment')
    ctx = Context(
        work_dir=work_dir,
        date=date,
        save_path=work_dir / f'output_{date}.csv',
        upload_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei',
        download_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
