# !/usr/bin/env python3
"""
例行化推送负反馈清单
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import requests
from retrying import retry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, notice_tool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    批处理记录
    """
    bid: str
    description: str


@dataclass
class Context:
    """
    批处理上下文
    """
    work_dir: Path
    date: str
    save_path: Path
    upload_file_url: str
    download_file_url: str
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_records(ctx: Context, proceed):
    """
    加载负反馈记录
    """
    sql = '''
        select end_bid, input_re
        from feedback_info
        where event_day = %s and
              stars = 1 and
              input_re is not null and
              input_re != '' and
              tag_child = '充电站';
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, description in tqdm(poi_stab.fetch_all(sql, [ctx.date])):
            ctx.records.append(Record(
                bid=bid,
                description=description,
            ))

    if not any(ctx.records):
        print('no records to process')
        return

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存负反馈记录
    """
    with open(ctx.save_path, 'w', encoding='utf-8') as f:
        for record in ctx.records:
            f.write(f"{record.bid}\t{record.description}\n")

    proceed()


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(upload_file_url, local_file_path):
    """
    上传文件
    """
    files = {'file': open(local_file_path, 'r', encoding='utf-8')}
    file_uuid = requests.post(upload_file_url, files=files).text
    if len(file_uuid) != 32:
        raise Exception(f'upload file failed')

    return file_uuid


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    推送负反馈记录到如流
    """
    def upload_records(local_path: Path):
        if not local_path.exists():
            return None

        file_uuid = upload_file(ctx.upload_file_url, local_path)
        return f'{ctx.download_file_url}&uuid={file_uuid}'

    remote_url = upload_records(ctx.save_path)
    if remote_url is None:
        proceed()
        return

    notice_tool.send_hi(
        f'充电桩 {ctx.date} 负反馈清单，量级 {len(ctx.records)}，请抽空核实：{remote_url}',
        atuserids=['chenjie02_cd', 'songhuiling', 'linbin_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )
    proceed()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行化推送负反馈清单脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        load_records,
        save_records,
        send_records_to_infoflow,
    )
    desc.attach(main_pipe)
    date = (datetime.date.today() - datetime.timedelta(days=2)).strftime('%Y%m%d')  # 负反馈一般延迟 2 天
    work_dir = Path('cache/auto_report_negative_feedback')
    ctx = Context(
        work_dir=work_dir,
        date=date,
        save_path=work_dir / f'output_{date}.csv',
        upload_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei',
        download_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main()
