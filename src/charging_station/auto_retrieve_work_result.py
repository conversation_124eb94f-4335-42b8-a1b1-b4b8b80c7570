# !/usr/bin/env python3
"""
例行回捞人工作业成果
"""
import argparse
import json
import re
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import Poi
from src.charging_station.helper import get_nested_value
from src.charging_station.online_prop import online_relation, online_spacing, online_ownership
from src.tools import pipeline, pgsql, tsv, notice_tool

VALID_CONCLUSIONS = {3, 7}
UGC_PROJECTS = {'CGU', 'CZX'}
DESIRED_PROJECTS = {'CDC', 'CDX', 'CHZ', 'CDK'} | UGC_PROJECTS

desc = pipeline.get_desc()


@dataclass
class Ticket:
    """
    存储工单信息
    """
    # 工单属性
    source_id: str
    mark_id: str
    ptid: str
    project: str
    batch: str
    work_conclusion: int
    item_content: str
    update_time: str

    # poi 属性
    bid: str = ''
    name: str = ''
    click_pv: int = 0
    show_tag: str = ''
    relation_bid: str = ''
    ownership: str = ''

    # 业务逻辑
    work_relation_bid: str = None
    work_show_tag: str = None
    work_ownership: str = None
    work_location: str = None
    work_status: int = None

    original_relation_bid: str = None
    original_show_tag: str = None
    original_location: str = None
    original_status: int = None

    distance: float = 0
    is_valid: bool = True
    reason: str = ''
    valid: int = 0

    @property
    def retrieved_relation(self):
        """
        是否回捞父子关系
        """
        return (
            self.is_valid and
            # 父子关系仅回捞 cdc
            self.project.upper() == 'CDC' and
            self.work_conclusion in VALID_CONCLUSIONS and
            self.work_relation_bid is not None and
            self.work_relation_bid != '' and
            self.work_relation_bid != '0' and
            self.work_relation_bid != self.relation_bid
        )

    @property
    def retrieved_spacing(self):
        """
        是否回捞空间属性
        """
        return (
            self.is_valid and
            # 空间属性仅回捞 cdc
            self.project.upper() == 'CDC' and
            self.work_conclusion in VALID_CONCLUSIONS and
            self.work_show_tag is not None and
            self.work_show_tag != self.show_tag
        )

    @property
    def retrieved_ownership(self):
        """
        是否回捞开放属性
        """
        return (
            self.is_valid and
            self.work_ownership is not None and
            self.work_ownership != self.ownership
        )

    @property
    def relation_changed(self):
        """
        是否有父子关系变更
        """
        return self.work_relation_bid != self.original_relation_bid

    @property
    def spacing_changed(self):
        """
        是否有空间属性变更
        """
        return self.work_show_tag != self.original_show_tag

    @property
    def ownership_changed(self):
        """
        是否有开放属性变更
        """
        return self.work_ownership is not None

    @property
    def location_changed(self):
        """
        是否有位置变更
        """
        return self.work_location != self.original_location

    @property
    def status_changed(self):
        """
        是否有状态变更
        """
        return self.work_status != self.original_status


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    ptid_path: str
    tickets: list[Ticket] = field(default_factory=list)

    relation_data: list[Poi] = field(default_factory=list)
    spacing_data: list[Poi] = field(default_factory=list)
    ownership_data: list[Poi] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


def load_finished_tickets_by_db(ctx: Context):
    """
    通过数据库加载已完成工单
    """
    sql = '''
        select wpi.ptid, wpi.source_id, pd.mark_id, pa.work_conclusion, pa.item_content, wpi.update_time, wpi.batch_id
        from work_process_instance as wpi

        left join pro_achievement as pa
        on wpi.ptid = pa.ptid

        left join poi_detail as pd
        on wpi.ptid = pd.ptid

        where wpi.project = %s and
              wpi.is_finished = 1 and
              wpi.start_time >= CURDATE() - interval 30 day;
    '''

    with (
        get_mysql_connection("poi_platform") as conn,
        conn.cursor() as cur,
    ):
        for project in tqdm(DESIRED_PROJECTS):
            cur.execute(sql, [project])
            for ptid, source_id, mark_id, work_conclusion, item_content, update_time, batch_id in cur.fetchall():
                if item_content is None:
                    continue

                ctx.tickets.append(Ticket(
                    ptid=ptid,
                    source_id=source_id,
                    mark_id=mark_id,
                    work_conclusion=work_conclusion,
                    batch=batch_id,
                    project=project,
                    item_content=item_content,
                    update_time=update_time,
                ))


def load_finished_tickets_by_file(ctx: Context):
    """
    通过文件加载已完成工单
    """
    sql = '''
        select wpi.ptid, wpi.source_id, pd.mark_id, pa.work_conclusion, pa.item_content, 
            wpi.update_time, wpi.batch_id, wpi.project
        from work_process_instance as wpi

        left join pro_achievement as pa
        on wpi.ptid = pa.ptid

        left join poi_detail as pd
        on wpi.ptid = pd.ptid

        where wpi.ptid = %s;
    '''
    ptids = set(x[0] for x in tsv.read_tsv(ctx.ptid_path))

    with (
        get_mysql_connection("poi_platform") as conn,
        conn.cursor() as cur,
    ):
        for ptid in tqdm(ptids):
            cur.execute(sql, [ptid])
            row = cur.fetchone()
            if row is None:
                continue

            ptid, source_id, mark_id, work_conclusion, item_content, update_time, batch_id, project = row
            if item_content is None:
                continue

            ctx.tickets.append(Ticket(
                ptid=ptid,
                source_id=source_id,
                mark_id=mark_id,
                work_conclusion=work_conclusion,
                batch=batch_id,
                project=project,
                item_content=item_content,
                update_time=update_time,
            ))


@desc()
def load_finished_tickets(ctx: Context, proceed):
    """
    加载已完成工单
    """
    if ctx.ptid_path is None:
        load_finished_tickets_by_db(ctx)
    else:
        load_finished_tickets_by_file(ctx)

    proceed()


def parse_bid_by_mark_id(mark_id):
    """
    解析 mark_id 中的 bid
    """
    return re.search(r'<(.*?)>', mark_id).group(1) if mark_id.startswith('bid') else None


@desc()
def fill_ticket_bid(ctx: Context, proceed):
    """
    填充工单 bid
    """
    sql = '''
        select bid from charging_station where thirdcode = %s and station_id = %s limit 1;
    '''

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        for ticket in tqdm(ctx.tickets):
            bid = parse_bid_by_mark_id(ticket.mark_id)
            if bid is not None:
                ticket.bid = bid
                continue

            if ticket.source_id == '':
                continue

            tid = ticket.source_id.split(':')[-1]
            third_code, station_id = tid.split('_', 1)
            cur.execute(sql, (third_code, station_id))
            row = cur.fetchone()
            if row is None:
                ticket.is_valid = False
                ticket.reason = 'bid not found'
                continue

            ticket.bid = row[0]

    proceed()


@desc()
def fill_poi_basic_value(ctx: Context, proceed):
    """
    填充 poi 的基础属性
    """
    sql = '''
        select name, click_pv, show_tag, relation_bid, status, st_astext(geometry) from poi where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for ticket in tqdm(ctx.tickets):
            if ticket.bid == '':
                ticket.is_valid = False
                ticket.reason = 'poi not found'
                continue

            row = poi_stab.fetch_one(sql, (ticket.bid,))
            if row is None:
                ticket.is_valid = False
                ticket.reason = 'poi not found'
                continue

            ticket.name, ticket.click_pv, ticket.show_tag, ticket.relation_bid, ticket.status, wkt = row
            ticket.geom = shapely.wkt.loads(wkt)

    proceed()


@desc()
def fill_poi_ownership(ctx: Context, proceed):
    """
    填充 poi 的开放属性
    """
    public_type = 1
    private_tag = '不对外开放'
    public_tag = ''

    sql = '''
        select online_type from cdz_history where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for ticket in tqdm(ctx.tickets):
            if not ticket.is_valid:
                continue

            row = poi_stab.fetch_one(sql, (ticket.bid,))
            if row is None:
                ticket.is_valid = False
                ticket.reason = 'history not found'
                continue

            ticket.ownership = public_tag if int(row[0]) == public_type else private_tag

    proceed()


@desc()
def retrieve_work_result(ctx: Context, proceed):
    """
    回捞作业成果
    """
    for ticket in tqdm(ctx.tickets):
        item_json = json.loads(ticket.item_content)
        retrieve_relation_result(ticket, item_json)
        retrieve_spacing_result(ticket, item_json)
        retrieve_ownership_result(ticket, item_json)
        retrieve_status_result(ticket, item_json)
        retrieve_location_result(ticket, item_json)

    proceed()


def retrieve_relation_result(ticket: Ticket, result_json):
    """
    回捞父子关系成果
    """
    work_result = get_nested_value(result_json[0], ['answer', 'poi', 'basic', 'parent_id'])
    ticket.work_relation_bid = str(work_result) if work_result is not None else None

    origin_result = get_nested_value(result_json[0], ['questions', 0, 'before', 'basic', 'parent_id'])
    ticket.original_relation_bid = str(origin_result) if origin_result is not None else None


def retrieve_spacing_result(ticket: Ticket, result_json):
    """
    回捞空间属性成果
    """
    default_value = '地上充电站'
    valid_values = [
        default_value,
        '地下充电站',
        '停车楼充电站',
    ]

    work_result = get_nested_value(result_json[0], ['answer', 'poi', 'basic', 'classify_show_tag'])
    if work_result is not None:
        ticket.work_show_tag = str(work_result)
        ticket.work_show_tag = (
            ticket.work_show_tag if ticket.work_show_tag in valid_values else default_value
        )

    origin_result = get_nested_value(result_json[0], ['questions', 0, 'before', 'basic', 'classify_show_tag'])
    if origin_result is not None:
        ticket.original_show_tag = str(origin_result)
        ticket.original_show_tag = (
            ticket.original_show_tag if ticket.original_show_tag in valid_values else default_value
        )


def retrieve_ownership_result(ticket: Ticket, result_json):
    """
    回捞开放属性成果
    """
    private_tag = '不对外开放'
    public_tag = ''
    public_type = '1'

    work_result = get_nested_value(result_json[0], ['answer', 'is_open'])
    if work_result is not None and work_result != '':
        ticket.work_ownership = public_tag if str(work_result) == public_type else private_tag


def retrieve_status_result(ticket: Ticket, result_json):
    """
    回捞状态成果
    """
    work_result = get_nested_value(result_json[0], ['answer', 'poi', 'basic', 'status'])
    ticket.work_status = int(work_result) if work_result is not None else None

    original_result = get_nested_value(result_json[0], ['questions', 0, 'before', 'basic', 'status'])
    ticket.original_status = int(original_result) if original_result is not None else None


def retrieve_location_result(ticket: Ticket, result_json):
    """
    回捞位置成果
    """
    work_result = get_nested_value(result_json[0], ['answer', 'poi', 'basic', 'point'])
    ticket.work_location = str(work_result) if work_result is not None else None

    original_result = get_nested_value(result_json[0], ['questions', 0, 'before', 'basic', 'point'])
    ticket.original_location = str(original_result) if original_result is not None else None


@desc()
def fill_online_data(ctx: Context, proceed):
    """
    填充人工作业成果
    """
    ctx.relation_data = [
        Poi(bid=x.bid, relation_bid=x.work_relation_bid)
        for x in ctx.tickets if x.retrieved_relation
    ]
    ctx.spacing_data = [
        Poi(bid=x.bid, show_tag=x.work_show_tag)
        for x in ctx.tickets if x.retrieved_spacing
    ]
    ctx.ownership_data = [
        Poi(bid=x.bid, ownership=x.work_ownership)
        for x in ctx.tickets if x.retrieved_ownership
    ]

    proceed()


@desc()
def online_work_result(ctx: Context, proceed):
    """
    上传人工作业成果
    """
    online_relation(ctx.relation_data)
    online_spacing(ctx.spacing_data, force=True)
    online_ownership(ctx.ownership_data)

    proceed()


@desc()
def online_intervention_records(ctx: Context, proceed):
    """
    上传干预记录
    """
    sql = '''
        insert into cdz_mohushangxiaxian(
            ptid, 
            bid,
            online_type, 
            src_type, 
            sub_src, 
            create_time
        )
        values (%s, %s, %s, %s, %s, %s)
        on conflict (ptid) do update set
            online_type = excluded.online_type;
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for ticket in tqdm([x for x in ctx.tickets if x.project in UGC_PROJECTS and x.ownership_changed]):
                cur.execute(sql, (
                    ticket.ptid,
                    ticket.bid,
                    1 if ticket.work_ownership == '' else 0,
                    'cdz',
                    'celue',
                    ticket.update_time,
                ))

            poi_stab.connection.commit()
        except Exception as e:
            print(e)
            poi_stab.connection.rollback()
            raise e

    proceed()


@desc()
def save_changed_data(ctx: Context, proceed):
    """
    保存变更数据
    """
    batch_size = 1000
    get_ptid_sql = '''
        select ptid from cdz_work_changed;
    '''
    sql = '''
        insert into cdz_work_changed (ptid, spacing, relation, ownership, location, status, finished_time) 
        values (%s, %s, %s, %s, %s, %s, %s)
        on conflict (ptid) do update set
            spacing = excluded.spacing,
            relation = excluded.relation,
            ownership = excluded.ownership,
            location = excluded.location,
            status = excluded.status;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ptids = set(x[0] for x in stab.fetch_all(get_ptid_sql))

    valid_tickets = [x for x in ctx.tickets if x.ptid not in ptids]
    for i in range(0, len(valid_tickets), batch_size):
        with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab:
            stab.connection.autocommit = False
            with stab.connection.cursor() as cursor:
                try:
                    for ticket in tqdm(valid_tickets[i:i + batch_size], desc=f"Batch {i // batch_size + 1}"):
                        cursor.execute(sql, [
                            ticket.ptid,
                            1 if ticket.spacing_changed else 0,
                            1 if ticket.relation_changed else 0,
                            1 if ticket.ownership_changed else 0,
                            1 if ticket.location_changed else 0,
                            1 if ticket.status_changed else 0,
                            ticket.update_time,
                        ])

                    stab.connection.commit()
                except Exception as e:
                    stab.connection.rollback()
                    raise e

    proceed()


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    如流通知结果
    """
    msg = f'''今日例行回捞情况如下：
父子关系：{len(ctx.relation_data)}
空间属性：{len(ctx.spacing_data)}
开放属性：{len(ctx.ownership_data)}
'''
    print(msg)
    alert_to_infoflow(msg)
    proceed()


@desc()
def save_online_data(ctx: Context, proceed):
    """
    输出需要上传的人工作业成果
    """
    tsv.write_tsv(ctx.work_dir / 'relation.csv', [[x.bid, x.relation_bid] for x in ctx.relation_data])
    tsv.write_tsv(ctx.work_dir / 'spacing.csv', [[x.bid, x.show_tag] for x in ctx.spacing_data])
    tsv.write_tsv(ctx.work_dir / 'ownership.csv', [[x.bid, x.ownership] for x in ctx.ownership_data])
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    输出记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.project,
                x.bid,
                x.name,
                x.ptid,
                x.work_conclusion,

                x.spacing_changed,
                x.ownership_changed,
                x.relation_changed,
                x.location_changed,
                x.status_changed,

                x.work_show_tag,
                x.show_tag,
                x.retrieved_spacing,

                x.work_ownership,
                x.ownership,
                x.retrieved_ownership,

                x.work_relation_bid,
                x.relation_bid,
                x.retrieved_relation,

                x.click_pv,
                x.batch,
                x.update_time,
                x.is_valid,
                x.reason,
            ]
            for x in ctx.tickets
        ]
    )

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['offline', 'online'],
        default='offline',
        required=False,
    )
    parser.add_argument(
        '--ptid-path',
        dest='ptid_path',
        type=str,
        required=False,
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_finished_tickets,
        fill_ticket_bid,
        fill_poi_basic_value,
        fill_poi_ownership,
        retrieve_work_result,
        fill_online_data,
    ]

    if args.mode == 'online':
        pipes.extend([
            online_work_result,
            send_records_to_infoflow,
        ])

    pipes.extend([
        save_changed_data,
        online_intervention_records,
        save_online_data,
        save_records,
    ])
    return pipeline.Pipeline(*pipes)


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_retrieve_work_result'),
        ptid_path=args.ptid_path,
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(f'例行回捞人工作业成果脚本异常！{e}')


if __name__ == '__main__':
    main(parse_args())
