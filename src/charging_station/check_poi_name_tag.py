import datetime
import re
import time
from dataclasses import dataclass, field
from pathlib import Path

import requests
from retrying import retry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import Poi
from src.charging_station.helper import get_tp_users
from src.charging_station.online_prop import online_spacing
from src.tools import pgsql
from src.tools import pipeline, tsv, notice_tool
from src.tools.file_downloader import download_file_by_http

DOWNLOAD_URL_FORMAT = ('http://***********:8008/offline_quality_check_result/{0}'
                       '/charging_statistic/charging_not_valid.txt')

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    afs_path: str
    date: datetime.date
    intelligence_path: Path = None
    white_list: set[str] = field(default_factory=set)
    records: list = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        self.white_list = set(x[0] for x in tsv.read_tsv('all_white_list.csv'))


@dataclass
class Record:
    """
    充电站信息
    """
    bid: str
    name: str
    std_tag: str
    err_type: str
    err_msg: str
    flow_result: str = '-1'
    is_charging_station: bool = False
    reason: str = ''

    @property
    def can_eval(self):
        return True

    @property
    def can_auto_process(self):
        return self.is_charging_station


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def check_name_by_flow(name):
    """
    调用大模型接口判断名称是否为充电站
    """
    flow_id = 8539
    api_url = f'http://iplayground-dev.cloudapi.baidu-int.com/api/rest/v1/promptflow/{flow_id}/build/predict'

    payload = {'inputs': {'name': name}}

    headers = {'Content-Type': 'application/json', 'X-release': 'false'}

    response = requests.post(api_url, json=payload, headers=headers)
    response_json = response.json()

    return response_json.get('result').get('result')


@desc()
def download_check_result(ctx: Context, proceed):
    """
    下载质检结果
    """
    download_url = DOWNLOAD_URL_FORMAT.format(ctx.date.strftime('%Y-%m-%d'))
    ctx.intelligence_path = ctx.work_dir / f'result_{ctx.date}.txt'
    download_file_by_http(download_url, ctx.intelligence_path)

    if not ctx.intelligence_path.exists():
        return

    proceed()


@desc()
def read_data(ctx, proceed):
    """
    读取质检数据
    """
    std_tag_not = (
        '交通设施;电动自行车充电站',
        '交通设施;萝卜快跑上下车点',
        '交通设施;停车场',
        '交通设施;普通停车位',
        '交通设施;路侧停车位',
        '出入口;高速公路出口',
        '出入口;停车场出入口',
        '出入口;门',
        '出入口',
        '行政区划',
        '运动健身;体育场馆',
    )
    err_msg_check = '【充电站】【2】名称和tag一致'

    for row in tqdm(list(tsv.read_tsv(ctx.intelligence_path))):
        bid, name, _, std_tag, status_code, err_type, err_msg = row[:7]
        if err_type != err_msg_check:
            continue

        if std_tag in std_tag_not:
            ctx.white_list.add(bid)
        else:
            ctx.records.append(Record(bid=bid, name=name, std_tag=std_tag, err_type='', err_msg=''))

    print(len(ctx.records))
    proceed()


@desc()
def check_name(ctx, proceed):
    """
    关键词检测
    """
    key_words_end = (
        '充电站', '充电桩', '超充站', '超充桩', '慢充站', '慢充桩', '换电站', '换电桩', '极充站', '极充桩'
    )
    key_word_in = {
        '理想5C超充站', '蔚来超充站', '蔚来换电站'
    }
    not_in_words = {
        '自行车', '电瓶车', '摩托车', '电单车', '二轮', '两轮', '三轮', '非机动', '手机充电站', '吉利'
    }
    brands = {
        '66快充', 'bp快充', 'EN智能充电', 'e充电', '国家电网', '万城万充', '万辉新能源', '万马爱充', '中石化',
        '中科智充', '中镉', '云南交投', '云快充', '云科领创', '享充充电', '任我充', '依威能源', '充充有电', '充电喵',
        '华润电力', '南方和顺', '南网电动', '南方电网', '顺易充', '卡泰驰', '均悦充', '塑云', '壳牌', '好充',
        '如约充电', '安徽省充换电', '安心充电', '安悦充电', '宿来电', '小桔充电', '小鹏', '川润环保能源', '帝能云',
        '广东天路', '广汽能源', '昊铂', '开迈斯', '快电', '快鳗充电', '思极星能', '恒昌', '拓邦云充', '捷电通',
        '新电途', '新疆公路服务', '旭电通', '时代安迅', '易事特', '易佳电', '星星充电', '春尚充电桩', '普天新能源',
        '中石油', '昆仑网电', '景德镇易停车', '智充', '极能超电', '极越', '氢积电', '河南交投', '河南静态', '济南能源',
        '海汇德', '润诚达', '湖南京能', '特斯拉', '特来电', '珠海驿联驿充电', '理想', '电能侠', '科陆充电', '绍兴越城',
        '绿侠充电', '联行', '聚合快充', '能佳科技', '能效电气', '苏州交投', '蒙马充电', '蔚景云', '蔚来', '超翔新能源',
        '车电网', '达克云', '逸安启', '铁塔能源', '闪得能源', '闪电客', '闪象智充', '阜阳充电', '阳光新能源', '驴充充',
        '驿通', '鼎晟', '龙电宝', '速停车', '中国铁塔', '吉利', '极氪', 'ZEEKER'
    }
    brackets = ['(', ')', '（', '）']

    for record in tqdm(ctx.records):
        if not any(brand in record.name for brand in brands):
            record.reason = 'poi name not contains brand'
            continue

        name = record.name
        for word in key_words_end:
            word_index = name.find(word)
            if word_index != -1:
                if any(word in name for word in not_in_words):
                    record.reason = 'poi name contain not_in_word'
                    break
                if word_index == len(name) - 3:
                    record.is_charging_station = True
                    break
                for bracket in brackets:
                    matches = [m.start() for m in re.finditer(re.escape(bracket), name)]
                    for bracket_index in matches:
                        if word_index == bracket_index - 3:
                            record.is_charging_station = True
                            break

        if any(word in name for word in key_word_in):
            record.is_charging_station = True

    proceed()


@desc()
def check_special_name(ctx, proceed):
    """
    特殊关键词检测
    """
    key_words = ['便民充电站', '惠民充电站']
    all_tp_brands = list(get_tp_users().values())

    for record in tqdm([x for x in ctx.records if not x.is_charging_station]):
        if all(x not in record.name for x in key_words):
            continue

        record.is_charging_station = any(x in record.name for x in all_tp_brands)

    proceed()


def save_records(file_name, records: list[Record]):
    """
    保存记录
    """
    tsv.write_tsv(file_name,
                  [[
                      record.bid,
                      record.name,
                      record.std_tag,
                      '换电站' if '换电站' in record.name else '地上充电站',
                      record.is_charging_station,
                      record.flow_result,
                      record.reason,
                      record.err_type,
                      record.err_msg
                  ]
                      for record in tqdm(records)],
                  )


def fetch_result_from_db(bid, name):
    """
    从db中读取已有的结果，没有返回-1
    """
    sql = '''
        select result from cdz_name_recognition where bid=%s and name =%s
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        ret = stab.fetch_one(sql, [bid, name])
        if ret is None or len(ret) == 0:
            return -1
        return ret[0]


def insert_result_to_db(bid, name, flow_result):
    """
    将结果插入数据库
    """
    sql = '''
    insert into cdz_name_recognition(bid,name,result) values(%s,%s,%s)
    ON CONFLICT (bid) DO UPDATE SET
    name = EXCLUDED.name,
    result = EXCLUDED.result;
    '''
    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            cur.execute(sql, [bid, name, flow_result])
            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e


@desc()
def check_by_ai(ctx, proceed):
    """
    大模型名称检测
    """
    for record in tqdm(ctx.records):
        name = record.name
        record.flow_result = fetch_result_from_db(record.bid, record.name)
        if record.flow_result == -1:
            try:
                flow_result = check_name_by_flow(name)
                record.flow_result = flow_result
                insert_result_to_db(record.bid, record.name, record.flow_result)
                print(name, ':', flow_result)
            except Exception as e:
                print(e)
                record.flow_result = -1
            finally:
                time.sleep(1)
    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    ctx.batch_records = [
        Poi(x.bid, show_tag='换电站' if '换电站' in x.name else '地上充电站')
        for x in ctx.records if x.can_auto_process
    ]

    online_spacing(ctx.batch_records, force=True)
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    save_records(ctx.work_dir / ('eval' + datetime.datetime.now().strftime('%Y-%m-%d') + '.tsv'),
                 [x for x in ctx.records if x.can_eval])
    proceed()


@desc()
def save_auto_records(ctx: Context, proceed):
    """
    保存自动处理记录
    """
    save_records(ctx.work_dir / ('auto' + datetime.datetime.now().strftime('%Y-%m-%d') + '.tsv'),
                 [x for x in ctx.records if x.can_auto_process])
    proceed()


@desc()
def save_white_list(ctx: Context, proceed):
    """
    保存白名单
    """
    ctx.white_list.update(x.bid for x in ctx.records if not x.can_auto_process)
    tsv.write_tsv(
        ctx.work_dir / 'whitelist.csv',
        [
            [
                bid,
            ]
            for bid in ctx.white_list
        ],
    )
    proceed()


def send_to_ruliu(ctx, proceed):
    """
    如流通知
    """
    count = len(list(filter(lambda r: r.can_auto_process, ctx.records)))
    if count == 0:
        return

    notice_tool.send_hi(
        f'例行化根据名称维护tag完成！数量{count}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )
    proceed()


def create_pipeline(args):
    """
    创建执行管道
    """
    mode = args.mode if args is not None else 'eval'

    pipes = [
        download_check_result,
        read_data,
        check_name,
        check_special_name,
        # check_by_ai,
        save_eval_records,
        save_auto_records,
        save_white_list,
    ]
    if mode == 'auto':
        pipes += [execute_batch, send_to_ruliu]
    return pipeline.Pipeline(*pipes)


def main(args=None):
    """
    主函数
    """
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('data/check_poi_name_tag'),
        afs_path='/user/map_data_aoi/cdz/qa/tag',
        date=datetime.date.today() - datetime.timedelta(days=2),
    )
    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())
