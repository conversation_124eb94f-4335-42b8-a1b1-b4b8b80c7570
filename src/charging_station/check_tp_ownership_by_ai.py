import requests
from retrying import retry
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import time
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin as auto_repair_mixin
from src.charging_station.auto_repair_mixin import Context
from src.parking.recognition import dbutils
from src.tools import pgsql

from src.charging_station.data import (
    OWNERSHIP_PRIVATE,
    OWNERSHIP_REASON_TP_EXTRA_INFO
)

def fetch_result_from_db(bid):
    """
    从db中读取已有的结果，没有返回-1
    """
    sql="""
        select result from cdz_tp_ownership_recognition where bid=%s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [bid])
    if ret is None or len(ret) == 0:
        return -1
    return ret[0]

def insert_result_to_db(record):
    """
    将结果插入数据库
    """
    sql="""
    INSERT INTO cdz_tp_ownership_recognition (
        bid, tp_id, third_code, third_name, station_id, station_name, 
        result, business_hours, park_info, park_fee, site_guide
    ) 
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ON CONFLICT (bid) DO UPDATE SET
        tp_id = EXCLUDED.tp_id,
        third_code = EXCLUDED.third_code,
        third_name = EXCLUDED.third_name,
        station_id = EXCLUDED.station_id,
        station_name = EXCLUDED.station_name,
        result = EXCLUDED.result,
        business_hours = EXCLUDED.business_hours,
        park_info = EXCLUDED.park_info,
        park_fee = EXCLUDED.park_fee,
        site_guide = EXCLUDED.site_guide;
    """
    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            cur.execute(sql, [record.poi.bid,record.tp.id, record.tp.third_code, record.tp.third_name, record.tp.station_id, 
                record.tp.station_name,record.flow_result,record.tp.business_hours,record.tp.park_info,record.tp.park_fee,record.tp.site_guide])
            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e
   

def check_tp_information_by_ai(ctx: Context, proceed):
    """
    通过AI检查TP信息
    """
    PUBLIC_NUM = 1
    PRIVATE_NUM = 0
    UNKNOWN_NUM = 2
    check_words = ['不对外','不开放','内部','对内','私']
    for record in tqdm(ctx.records):
        if record.tp is None:
            continue
        record.flow_result = fetch_result_from_db(record.poi.bid)
        
        if record.flow_result == -1:
            record.tp.description = ''
            extra_info = [record.tp.business_hours,record.tp.park_info,record.tp.park_fee,record.tp.site_guide]
            for word in extra_info:
                if any(key in word for key in check_words):
                    try:
                        public_res = ask_is_public(word)
                        record.flow_result = public_res
                        print(f"{word}: {public_res}")
                        insert_result_to_db(record)
                        if record.flow_result == PRIVATE_NUM and record.poi_ownership != OWNERSHIP_PRIVATE:
                            break
                    except Exception as e:
                        print(e)
                    finally:
                        time.sleep(1)
        if record.flow_result == PRIVATE_NUM and record.poi_ownership != OWNERSHIP_PRIVATE :
            record.reason = OWNERSHIP_REASON_TP_EXTRA_INFO
    proceed()    
    
@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def ask_is_public(message: str) -> dict:
    """
    判断输入的字符串描述是否对外开放
    """
    FLOW_ID = 8535
    api_url = f"http://iplayground-dev.cloudapi.baidu-int.com/api/rest/v1/promptflow/{FLOW_ID}/build/predict"

    payload = {"inputs": {"description": message}}

    headers = {"Content-Type": "application/json", "X-release": "false"}

    response = requests.post(api_url, json=payload, headers=headers)
    return response.json().get('result').get('result')