# !/usr/bin/env python3
"""
包含充电站所需的一些数据结构
"""
import uuid
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from typing import Callable, Protocol

import mapio.utils.coord
from shapely import Point

SPACING_GROUND = 'ground'
SPACING_UNDERGROUND = 'underground'
SPACING_STRUCTURE = 'structure'
SPACING_UNKNOWN = 'unknown'

SPACING_GROUND_DISPLAY_NAME = '地上充电站'
SPACING_UNDERGROUND_DISPLAY_NAME = '地下充电站'
SPACING_STRUCTURE_DISPLAY_NAME = '停车楼充电站'

SPACING_NAME_MAP = {
    SPACING_GROUND_DISPLAY_NAME: SPACING_GROUND,
    SPACING_UNDERGROUND_DISPLAY_NAME: SPACING_UNDERGROUND,
    SPACING_STRUCTURE_DISPLAY_NAME: SPACING_STRUCTURE,
}
SPACING_VALUE_MAP = {
    SPACING_GROUND: SPACING_GROUND_DISPLAY_NAME,
    SPACING_UNDERGROUND: SPACING_UNDERGROUND_DISPLAY_NAME,
    SPACING_STRUCTURE: SPACING_STRUCTURE_DISPLAY_NAME,
}

OWNERSHIP_PRIVATE = 'private'
OWNERSHIP_PUBLIC = 'public'
OWNERSHIP_UNKNOWN = 'unknown'

OWNERSHIP_REASON_TP_CONTAINS_PRIVATE_KEYS = 'station name contains private keys'
OWNERSHIP_REASON_TP_CONTAINS_PUBLIC_KEYS = 'station name contains public keys'
OWNERSHIP_REASON_POI_CONTAINS_PRIVATE_KEYS = 'poi name contains private keys'
OWNERSHIP_REASON_POI_CONTAINS_PUBLIC_KEYS = 'poi name contains public keys'
OWNERSHIP_REASON_TP_IS_PRIVATE_TYPE = 'station type is private'
OWNERSHIP_REASON_TP_IS_PUBLIC_TYPE = 'station type is public'

STATUS_ONLINE = 'online'
STATUS_OFFLINE = 'offline'
STATUS_UNKNOWN = 'unknown'

STATUS_REASON_TP_CONTAINS_OFFLINE_KEYS = 'station name contains offline keys'
STATUS_REASON_TP_IS_OFFLINE_TYPE = 'station status is offline'
STATUS_REASON_TP_IS_ONLINE_TYPE = 'station status is online'
STATUS_REASON_PIPE_IS_OFFLINE_TYPE = 'pipe status is offline'
STATUS_REASON_PIPE_IS_ONLINE_TYPE = 'pipe status is online'

TAG_LEADING_USER = 1
TAG_SMALL_AND_MEDIUM_USER = 2
TAG_AGGREGATION_USER = 4
TAG_AUTOMAKER_USER = 8
TAG_PAYING_USER = 16
TAG_CLOSED_USER = 32
TP_USER_TAGS = {
    TAG_LEADING_USER: '头部',
    TAG_SMALL_AND_MEDIUM_USER: '中小',
    TAG_AGGREGATION_USER: '聚合',
    TAG_AUTOMAKER_USER: '车厂',
    TAG_PAYING_USER: '付费',
    TAG_CLOSED_USER: '闭环',
}

POI_STATUS_MAP = {
    1: '正常营业',
    2: '删除',
    3: '暂停营业',
    5: '尚未开业',
    9: '停止营业',
    15: '模糊上线',
    16: '模糊下线',
    17: '失效',
    18: '非状态问题删除'
}

# noinspection SpellCheckingInspection
UGC_SOURCES = {
    'basic.ganyu',
    'basic.security',
    'layer.vip_ganyu',
    'jinji.ganyu',
    'dataop.photo',
    'wanneng_touchuan.mingdeng_bianji',
    'dataop.bianji_ugc_xiugai_auto',
    'dataop.bianji_ugc_shangxian_auto',
    'dataop.ugc_phone_sh',
    'dataop.bianji_ugc_delete_auto',
    'dataop.bianji_ugc_new_auto',
    'dataop.ugc_tag_auto',
    'dataop.bianji_ugc_revise_auto',
    'dataop.ugc_phone_auto',
    'dataop.bianji_ugc_xiaxian_auto',
    'dataop.ugc_dianhua_auto',
    'dataop.bianji_ugc_revise',
    'dataop.bgc_tag',
    'dataop.ugc_phone_bai',
    'dataop.bianji_ugc_revise_light',
    'dataop.bianji_ugc_delete',
    'dataop.bianji_ugc_new_light',
    'dataop.bianji_ugc_new',
    'dataop.ugc_tag',
    'lbc.brand_normal_bid',
    'dataop.bianji_ugc_revise_important',
    'dataop.bianji_ugc_new_important',
    'dataop.bianji_ugc_del_important',
    'dataop.ugc_phone_rg',
    'special.bianji_ugc_new_special',
    'special.bianji_ugc_delete_special',
    'special.bianji_ugc_revise_special',
    'lbc.brand_normal',
    'dataop.bianji_ugc_delete_light',
    'dataop.bianji_ugc_new_direct',
    'dataop.bianji_ugc_delete_direct',
    'dataop.bianji_ugc_revise_direct',
    'lbc.new_biaozhu_bid',
    'lbc.new_biaozhu',
}


@dataclass
class TicketPushResult:
    """
    表示一份人工作业推送结果
    """
    batch_id: str
    mark_id: str
    ptid: str
    code: int
    result: str

    def __init__(self, data):
        self.batch_id = data['batch_id']
        self.mark_id = data['detail']['mark_id']
        self.ptid = data['ptid']
        self.code = data['code']
        self.msg = data['msg']

    @property
    def uuid(self):
        """
        唯一标识
        """
        return f'{self.batch_id}_{self.mark_id}'


@dataclass
class Poi:
    """
    poi 信息
    """
    bid: str = ''
    click_pv: int = 0
    relation_bid: str = ''
    name: str = ''
    alias: str = ''
    address: str = ''
    wkt: str = ''
    geom: Point = None
    arrive_geom: Point = None
    mc_wkt: str = ''
    mc_geom: Point = None
    phone: str = ''
    business_time: str = ''
    status: int = 1
    tag: str = ''
    show_tag: str = ''
    classify_tag: str = ''
    ownership: str = ''
    city: str = ''
    create_time: int = 0
    update_time: int = 0


@dataclass
class Pile:
    """
    充电站桩头信息
    """
    third_code: int
    station_id: str
    status: int

    @property
    def tid(self):
        """
        充电站 tid
        """
        return f'{self.third_code}_{self.station_id}'


@dataclass
class TpRecord:
    """
    充电站 tp 信息
    """
    id: int
    bid: str
    station_id: str
    station_name: str
    station_type: int
    station_status: int
    station_geom: Point
    third_code: int
    third_name: str
    address: str
    park_info: str
    site_guide: str
    service_tel: str = ''
    business_hours: str = ''
    src: str = ''

    @property
    def tid(self):
        """
        充电站 tid
        """
        return f'{self.third_code}_{self.station_id}'


@dataclass
class RepairPropContext:
    """
    属性修复上下文
    """
    piles: defaultdict[str, list[Pile]] = field(default_factory=lambda: defaultdict(list))
    tp_records: defaultdict[str, list[TpRecord]] = field(default_factory=lambda: defaultdict(list))
    pois: dict[str, Poi] = field(default_factory=dict)
    mode: str = ''


@dataclass
class Ticket:
    """
    人工作业清单记录
    """
    # 推送信息
    project: str
    priority: int
    batch_id: str
    batch_name: str
    bid: str
    method: str
    message: str
    src: str
    creator: str = ''

    # poi 参考信息
    suggest_poi: Poi = Poi()

    # 参考图片地址
    ref_urls: list[str] = field(default_factory=list)

    # 业务信息
    mode: str = ''
    can_process: bool = True
    reason: str = ''
    res_code: int = 0
    res_msg: str = ''
    ptid: str = ''

    @property
    def mark_id(self):
        """
        格式化的 bid
        """
        return f'bid:<{self.bid}>'

    @property
    def suggest_point(self):
        """
        格式化的坐标
        """
        if self.suggest_poi.mc_geom is None:
            return ''
        else:
            return f'{self.suggest_poi.mc_geom.x},{self.suggest_poi.mc_geom.y}'

    @property
    def uuid(self):
        """
        唯一标识
        """
        return f'{self.batch_id}_{self.mark_id}'

    @property
    def push_success(self):
        """
        是否推送成功
        """
        return self.ptid != ''

    @property
    def image_item_content(self):
        """
        参考图片
        """
        items = []
        bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(self.suggest_poi.geom.x, self.suggest_poi.geom.y)

        for url in self.ref_urls:
            items.append({
                'img_url': url,
                'x': bd_x,
                'y': bd_y,
                'north': 0,
            })

        return items

    @property
    def formatted_json(self):
        """
        可用于推送的 json
        """
        # REF: https://ku.baidu-int.com/d/0c5e648d84484e
        return {
            'project': self.project,
            'creator': self.creator,
            'priority': self.priority,
            'batch_id': self.batch_id,
            'detail': {
                'mark_id': self.mark_id,
                'online_source': 'zt_life.cdz_rengong',
                'force_add': True,
                'method': self.method,
                'items': {
                    'item_type': 4,  # 4-素材、问题答案描述（标注、新桩点）
                    'contents': [
                        {
                            'data': {
                                'suggest': {
                                    'bid': self.bid,
                                    'basic': {
                                        'name': self.suggest_poi.name,
                                        'alias': self.suggest_poi.alias,
                                        'address': self.suggest_poi.address,
                                        'point': self.suggest_point,
                                        'phone': self.suggest_poi.phone,
                                        'status': self.suggest_poi.status,
                                        'std_tag': self.suggest_poi.tag,
                                    }
                                }
                            }
                        }
                    ],
                    'reference': {
                        'message': {
                            'content': self.message
                        }
                    }
                },
                'info': [
                    {
                        'mark_id': self.mark_id,
                        'info_type': 9,  # 9-淘金图片
                        'item_content': self.image_item_content,
                        'collect_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    }
                ]
            }
        }


@dataclass
class CreatePoiTicket(Ticket):
    """
    新增人工作业清单记录
    """

    def __post_init__(self):
        self.guid = f'guid:<{uuid.uuid4()}>'

    @property
    def mark_id(self):
        """
        格式化的 bid
        """
        return self.guid

    @property
    def formatted_json(self):
        """
        可用于推送的 json
        """
        # REF: https://ku.baidu-int.com/d/0c5e648d84484e
        return {
            'project': self.project,
            'creator': self.creator,
            'priority': self.priority,
            'batch_id': self.batch_id,
            'detail': {
                'mark_id': self.mark_id,
                'online_source': 'zt_life.cdz_rengong',
                'force_add': True,
                'method': self.method,
                'items': {
                    'item_type': 4,  # 0-未知, 1-基础字段加工（桩点）, 2-单元格描述, 4-素材、问题答案描述（标注、新桩点）
                    'contents': [
                        {
                            'data': {
                                'suggest': {
                                    'basic': {
                                        'name': self.suggest_poi.name,
                                        'address': self.suggest_poi.address,
                                        'point': self.suggest_point,
                                        'phone': self.suggest_poi.phone,
                                        'status': self.suggest_poi.status,
                                        'std_tag': self.suggest_poi.tag,
                                    }
                                }
                            }
                        }
                    ],
                    'reference': {
                        'message': {
                            'content': self.message
                        }
                    }
                },
            }
        }


@dataclass
class TicketBatch:
    """
    表示一批人工作业清单记录
    """
    tickets: list[Ticket]

    def __init__(self, tickets: list[Ticket]):
        self.tickets = tickets
        self.ticket_map = {x.uuid: x for x in tickets}

    def set_push_result(self, results: list[TicketPushResult] = None, code=None, msg=None):
        """
        设置推送结果
        """
        if results is not None:
            for result in results:
                ticket = self.ticket_map[result.uuid]
                ticket.res_code = result.code
                ticket.res_msg = result.msg
                ticket.ptid = result.ptid
        elif code is not None and msg is not None:
            for ticket in self.tickets:
                ticket.res_code = code
                ticket.res_msg = msg

    @property
    def formatted_json(self):
        """
        可用于推送的 json
        """
        return {
            'inst_list': [x.formatted_json for x in self.tickets]
        }


@dataclass
class RepairResult:
    """
    充电站属性修复结果
    """
    batch_name: str
    tickets: list[Ticket] = field(default_factory=list)
    batch_records: list[Poi] = field(default_factory=list)


@dataclass
class TicketProviderDescription:
    """
    用于描述一个人工作业工单生成器信息
    """
    name: str
    runner: Callable[[RepairPropContext], RepairResult]


@dataclass
class TicketConfig:
    """
    作业清单配置
    """
    src: str
    project: str
    priority: int
    batch_id: str
    batch_name: str
    method: str
    platform: str = ''
    mode: str = ''


@dataclass
class Competitor:
    """
    充电站竞品数据
    """
    id: str
    name: str
    address: str
    geom: Point
    tag: str = ''
    phone: str = ''
    name_iou: float = 0.0
    address_iou: float = 0.0


@dataclass
class CompetitorDiff:
    """
    竞品 Diff
    """
    bid: str = ''
    prop_type: str = ''
    bd_value: str = ''
    gd_value: str = ''


@dataclass
class TicketOrganizerContext(Protocol):
    """
    工单整理器上下文
    """
    tp_records: defaultdict[str, list[TpRecord]] = field(default_factory=lambda: defaultdict(list))
    tickets: list[Ticket] = field(default_factory=list)
