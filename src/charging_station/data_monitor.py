# !/usr/bin/env python3
"""
充电站数据监控
"""
import datetime
import re
import zipfile
from dataclasses import dataclass, field
from pathlib import Path
from typing import Literal

import requests
from retrying import retry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import Poi
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.file_downloader import download_file_by_http
from src.tools.icafe_tool import create_card

DOWNLOAD_URL_FORMAT = ('http://poi-data-subscription.baidu-int.com/data_warehouse/{0}'
                       '/zengyini_307f7f5510a30ce0.tsv_dir/xaa.zip')

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    execution_time: datetime.datetime
    upload_file_url: str
    download_file_url: str
    file_name: str = ''
    data_zip_path = None
    data_path = None
    closest_time: datetime.datetime = None
    should_alert: bool = False
    statistics_data: dict[str, object] = field(default_factory=dict)

    latest_records: list[Poi] = field(default_factory=list)
    old_records: list[Poi] = field(default_factory=list)

    one_hour_new_records: list[Poi] = field(default_factory=list)
    one_hour_deleted_records: list[Poi] = field(default_factory=list)

    one_day_new_records: list[Poi] = field(default_factory=list)
    one_day_deleted_records: list[Poi] = field(default_factory=list)

    one_day_new_data_download_url: str = ''
    one_day_deleted_data_download_url: str = ''

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        self.file_name = f'{self.execution_time.date()}_{self.execution_time.hour:02d}'


@desc()
def download_latest_data(ctx: Context, proceed):
    """
    下载最新数据
    """
    zip_file_path = ctx.work_dir / f'{ctx.file_name}.zip'
    if (ctx.work_dir / f'{ctx.file_name}.csv').exists():
        ctx.data_zip_path = zip_file_path
        print(f'{ctx.file_name}.csv 已存在，跳过下载。')
        proceed()
        return

    ctx.data_zip_path = zip_file_path
    download_url = DOWNLOAD_URL_FORMAT.format(ctx.execution_time.strftime('%Y-%m-%d'))
    download_file_by_http(download_url, ctx.data_zip_path)

    proceed()


@desc()
def unzip_latest_data(ctx: Context, proceed):
    """
    解压最新数据
    """
    data_path = ctx.work_dir / f'{ctx.file_name}.csv'
    if not ctx.data_zip_path.exists():
        ctx.data_path = data_path
        proceed()
        return

    try:
        with zipfile.ZipFile(ctx.data_zip_path, 'r') as zf:
            # 尝试解压
            zf.extractall(path=ctx.work_dir, pwd='9fff2a13'.encode('utf-8'))
            ctx.data_path = (ctx.work_dir / 'xaa').rename(data_path)
            ctx.data_zip_path.unlink()
            print(f"文件解压成功，存放于: {ctx.work_dir}")
    except zipfile.BadZipFile:
        print("错误：不是有效的 ZIP 文件！")
    except RuntimeError as e:
        print(f"解压失败，可能密码错误：{e}")
    except Exception as e:
        print(f"发生错误：{e}")

    proceed()


def load_records(data_path):
    """
    加载数据
    """
    records = []

    with open(data_path, 'r', encoding='utf-8') as f:
        for line in tqdm(f.readlines()[1:]):
            bid, name, status, create_time, update_time = tuple(line.strip().split('\t'))
            records.append(Poi(
                bid=bid,
                name=name,
                status=int(status),
                create_time=int(create_time),
                update_time=int(update_time),
            ))

    return records


@desc()
def load_latest_records(ctx: Context, proceed):
    """
    加载最新数据
    """
    ctx.latest_records = load_records(ctx.data_path)
    ctx.statistics_data['latest_total_count'] = len(ctx.latest_records)
    proceed()


def find_latest_file(file_paths, current_time):
    """
    找到最近的文件
    """
    closest_file = None
    closest_time = None
    smallest_diff = float('inf')  # 初始化为无限大

    # 使用正则过滤符合格式的文件路径
    valid_files = [
        file_path for file_path in file_paths
        if re.match(r'^\d{4}-\d{2}-\d{2}_\d{1,2}\.csv$', file_path.name)
    ]

    for file_path in valid_files:
        try:
            # 从文件名提取时间
            file_time_str = file_path.stem  # 获取文件名（不含后缀）
            file_time = datetime.datetime.strptime(file_time_str, '%Y-%m-%d_%H')

            # 计算时间差
            time_diff = abs((file_time - current_time).total_seconds())

            # 更新最近文件
            if time_diff < smallest_diff:
                smallest_diff = time_diff
                closest_file = file_path
                closest_time = file_time
        except ValueError:
            print(f"无法解析文件名: {file_path}")

    return closest_file, closest_time


@desc()
def load_one_hour_ago_records(ctx: Context, proceed):
    """
    加载一小时前的数据
    """
    closest_file, closest_time = find_latest_file(ctx.work_dir.glob('*.csv'), ctx.execution_time)

    if closest_file is None:
        raise FileNotFoundError()

    ctx.closest_time = closest_time
    ctx.old_records = load_records(closest_file)
    ctx.statistics_data['old_total_count'] = len(ctx.old_records)
    proceed()


def diff_data(latest_records, old_records):
    """
    比较两份记录返回新增、删除记录
    """
    latest_bids = set(record.bid for record in latest_records)
    old_bids = set(record.bid for record in old_records)

    new_bids = latest_bids - old_bids
    deleted_bids = old_bids - latest_bids

    new_records = [record for record in latest_records if record.bid in new_bids]
    deleted_records = [record for record in old_records if record.bid in deleted_bids]

    return new_records, deleted_records


@desc()
def diff_one_hour_data(ctx: Context, proceed):
    """
    比较一小时前后的数据
    """
    ctx.one_hour_new_records, ctx.one_hour_deleted_records = diff_data(ctx.latest_records, ctx.old_records)

    ctx.statistics_data['new_count'] = len(ctx.one_hour_new_records)
    ctx.statistics_data['deleted_count'] = len(ctx.one_hour_deleted_records)

    if not ctx.one_hour_new_records and not ctx.one_hour_deleted_records:
        ctx.data_path.unlink()

    proceed()


def save_records(path, records: list[Poi]):
    """
    保存数据
    """
    if not records:
        return

    tsv.write_tsv(
        path,
        [
            [
                x.bid,
                x.name,
                x.status,
                datetime.datetime.fromtimestamp(x.create_time).strftime('%Y-%m-%d %H:%M:%S'),
                datetime.datetime.fromtimestamp(x.update_time).strftime('%Y-%m-%d %H:%M:%S'),
            ]
            for x in records
        ]
    )


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(upload_file_url, local_file_path):
    """
    上传文件
    """
    files = {'file': open(local_file_path, 'r', encoding='utf-8')}
    file_uuid = requests.post(upload_file_url, files=files).text
    if len(file_uuid) != 32:
        raise Exception(f'upload file failed')

    return file_uuid


def upload_records(ctx: Context, local_path: Path):
    """
    上传记录
    """
    if not local_path.exists():
        return ''

    file_uuid = upload_file(ctx.upload_file_url, local_path)
    local_path.unlink()
    return f'{ctx.download_file_url}&uuid={file_uuid}'


@desc()
def upload_one_hour_diff_data(ctx: Context, proceed):
    """
    上传差异数据
    """
    new_data_path = ctx.work_dir / f'{ctx.file_name}_new.csv'
    deleted_data_path = ctx.work_dir / f'{ctx.file_name}_deleted.csv'

    save_records(new_data_path, ctx.one_hour_new_records)
    save_records(deleted_data_path, ctx.one_hour_deleted_records)

    ctx.statistics_data['new_data_download_url'] = upload_records(ctx, new_data_path)
    ctx.statistics_data['deleted_data_download_url'] = upload_records(ctx, deleted_data_path)

    proceed()


@desc()
def print_one_hour_statistics_data(ctx: Context, proceed):
    """
    打印一小时内的统计数据
    """
    for name, value in ctx.statistics_data.items():
        print(f'{name}: {value}')

    proceed()


@desc()
def send_one_hour_statistics_data_to_infoflow(ctx: Context, proceed):
    """
    发送一小时内的统计数据到如流
    """
    msg = f'''【充电站小时级监控】
相对 {ctx.closest_time:%Y-%m-%d %H:%M:%S} 的数据量对比:
新增充电站: {ctx.statistics_data['new_count']}, 下载地址: {ctx.statistics_data['new_data_download_url']}.
删除充电站: {ctx.statistics_data['deleted_count']}, 下载地址：{ctx.statistics_data['deleted_data_download_url']}.
'''
    print(msg)
    notice_tool.send_hi(msg, atuserids=['chenjie02_cd'], token='d2ab0b311ae2d9a6faa0d0a4e79100707')
    proceed()


def get_changed_count_today(ctx: Context, action_type: Literal['new', 'deleted']):
    """
    获取今天删除的数量
    """
    action_name = f'{action_type}_count'
    sql = '''
        select sum(value::numeric) 
        from cdz_monitor 
        where create_time::date=%s and 
              name = %s;
    '''
    today = ctx.execution_time.strftime('%Y-%m-%d')

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        row = poi_stab.fetch_one(sql, [today, action_name])
        return 0 if (row is None or row[0] is None) else int(row[0])


def mark_alerted(ctx: Context):
    """
    标记已报警
    """
    with open(ctx.work_dir / f'{ctx.execution_time.date()}_alerted', 'w') as f:
        f.write('')


def is_alerted(ctx: Context):
    """
    是否已报警
    """
    return (ctx.work_dir / f'{ctx.execution_time.date()}_alerted').exists()


def alert_if_changed_too_much(msg_items):
    """
    如果变化量级过多，则报警。
    """
    msg = f'''【充电站小时级监控】
{''.join(msg_items)}
请打开 sugar 平台查看详情：
https://sugar.baidu-int.com/group/aoi_monitor/report/r_1013e-2wnemic6-o5eqdp?__scp__=Baidu
'''
    print(msg)
    notice_tool.send_hi(msg, atuserids=['chenjie02_cd', 'songhuiling'], token='dc355cf9f3076cd43f93069a5c00ac6ce')
    notice_tool.send_hi(msg, atuserids=['chenjie02_cd', 'songhuiling'], token='d2ab0b311ae2d9a6faa0d0a4e79100707')

    # noinspection SpellCheckingInspection
    # 暂时不创建卡片了
    # create_card(
    #     space='park-case-closed',
    #     title='充电站变化量级超过阈值',
    #     owner='chenjie02_cd',
    #     detail=msg,
    #     username='chenjie02_cd',
    #     type='Case',
    #     password='VVVwBXdRLQoS4rSKRnD0b2TXRXUGyldPioO',
    #     notify_emails=['<EMAIL>', '<EMAIL>'],
    # )


@desc()
def alert_if_changed_too_much_one_hour(ctx: Context, proceed):
    """
    如果 1 小时内变化量级过多，则报警。
    """
    max_delete_count = 100
    max_add_count = 1000
    msg_items = []

    delete_count_one_hour = int(str(ctx.statistics_data['deleted_count']))
    if delete_count_one_hour > max_delete_count:
        msg_items.append(f'一小时内删除量级: {delete_count_one_hour}，超过阈值: {max_delete_count}。')

    new_count_one_hour = int(str(ctx.statistics_data['new_count']))
    if new_count_one_hour > max_add_count:
        msg_items.append(f'一小时内新增量级: {new_count_one_hour}，超过阈值: {max_add_count}。')

    if not msg_items:
        proceed()
        return

    alert_if_changed_too_much(msg_items)
    proceed()


@desc()
def alert_if_changed_too_much_one_day(ctx: Context, proceed):
    """
    如果 1 天内变化量级过多，则报警。
    """
    max_delete_count = 100
    max_add_count = 1000
    msg_items = []

    if is_alerted(ctx):
        proceed()
        return

    deleted_count_today = get_changed_count_today(ctx, 'deleted')
    if deleted_count_today > max_delete_count:
        msg_items.append(f'今日删除量级: {deleted_count_today}，超过阈值: {max_delete_count}。')

    new_count_today = get_changed_count_today(ctx, 'new')
    if new_count_today > max_add_count:
        msg_items.append(f'今日新增量级: {new_count_today}，超过阈值: {max_add_count}。')

    if not msg_items:
        proceed()
        return

    alert_if_changed_too_much(msg_items)
    mark_alerted(ctx)
    proceed()


@desc()
def save_one_hour_statistics_data_to_db(ctx: Context, proceed):
    """
    保存一小时内的统计数据到数据库
    """
    delete_sql = '''
        delete from cdz_monitor
        where date(create_time) = (DATE %s) and 
              extract(hour from create_time) = %s;
    '''
    insert_sql = '''
        insert into cdz_monitor(name, value) values (%s, %s);
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        today = ctx.execution_time.strftime('%Y-%m-%d')
        poi_stab.execute(delete_sql, [today, ctx.execution_time.hour])

        try:
            for name, value in ctx.statistics_data.items():
                cur.execute(insert_sql, [name, value])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


@desc()
def load_one_day_ago_records(ctx: Context, proceed):
    """
    加载一天内的记录
    """
    if ctx.execution_time.hour != 21:  # 只在晚上 21 点执行
        proceed()
        return

    start_time = ctx.execution_time - datetime.timedelta(hours=24)
    closest_file, closest_time = find_latest_file(ctx.work_dir.glob('*.csv'), start_time)
    if closest_file is None:
        raise FileNotFoundError()

    one_day_ago_records = load_records(closest_file)
    ctx.one_day_new_records, ctx.one_day_deleted_records = diff_data(ctx.latest_records, one_day_ago_records)

    new_data_path = ctx.work_dir / f'{ctx.file_name}_new_all.csv'
    deleted_data_path = ctx.work_dir / f'{ctx.file_name}_deleted_all.csv'

    save_records(new_data_path, ctx.one_day_new_records)
    save_records(deleted_data_path, ctx.one_day_deleted_records)

    ctx.one_day_new_data_download_url = upload_records(ctx, new_data_path)
    ctx.one_day_deleted_data_download_url = upload_records(ctx, deleted_data_path)

    proceed()


@desc()
def send_one_day_statistics_data_to_infoflow(ctx: Context, proceed):
    """
    发送一小时内的统计数据到如流
    """
    if ctx.execution_time.hour != 21:  # 只在晚上 21 点执行
        proceed()
        return

    msg = f'''【充电站小时级监控】
过去 24 小时量级变化如下:
新增充电站: {len(ctx.one_day_new_records)}, 下载地址: {ctx.one_day_new_data_download_url}.
删除充电站: {len(ctx.one_day_deleted_records)}, 下载地址：{ctx.one_day_deleted_data_download_url}.
'''
    print(msg)
    notice_tool.send_hi(msg, atuserids=['chenjie02_cd'], token='dc355cf9f3076cd43f93069a5c00ac6ce')
    notice_tool.send_hi(msg, atuserids=['chenjie02_cd'], token='d2ab0b311ae2d9a6faa0d0a4e79100707')
    proceed()


@desc()
def clear_cache(ctx: Context, proceed):
    """
    清理缓存
    """
    for path in ctx.work_dir.glob(f'{(ctx.execution_time - datetime.timedelta(days=3)):%Y-%m-%d}*'):
        print(path)
        path.unlink()

    proceed()


def alert_exception_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'充电站数据监控脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        # one hour
        load_one_hour_ago_records,
        download_latest_data,
        unzip_latest_data,
        load_latest_records,
        diff_one_hour_data,
        upload_one_hour_diff_data,
        print_one_hour_statistics_data,
        save_one_hour_statistics_data_to_db,
        send_one_hour_statistics_data_to_infoflow,
        alert_if_changed_too_much_one_hour,

        # one day
        load_one_day_ago_records,
        send_one_day_statistics_data_to_infoflow,
        alert_if_changed_too_much_one_day,

        clear_cache,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/data_monitor'),
        execution_time=datetime.datetime.now(),
        upload_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei',
        download_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_exception_to_infoflow(e)


if __name__ == '__main__':
    main()
