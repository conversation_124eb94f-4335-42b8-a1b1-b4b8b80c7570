"""
数据监控例行工具
"""
import subprocess
import requests
import json
from retrying import retry
from datetime import date, timedelta
from dataclasses import dataclass, field
from src.charging_station.service_zone import auto_find_service_zone
from pathlib import Path
from src.tools import utils, tsv
from src.parking.recognition import dbutils
from multiprocessing.pool import Pool
from src.tools import pgsql, notice_tool


@dataclass
class MonitorTool:
    """
    上下文
    """
    afsshell = "/home/<USER>/afs/bin/afsshell"
    afsshell_de16 = '/home/<USER>/chenbaojun/scripts/afs/bin/afsshell'
    AFS_BASE = "/user/map-data-streeview/aoi-ml/charging_station/data_monitor/"
    local_base_path = "cache/data_monitor/"
    upload_file_url = 'http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei'
    download_file_url = 'http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile'
    web_hook = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=" + 'd2ab0b311ae2d9a6faa0d0a4e79100707'
    ruliu_hook = 'd2ab0b311ae2d9a6faa0d0a4e79100707'
    yesterday_bids = []
    today_bids = []
    today_str = date.today().strftime("%Y-%m-%d")
    yesterday_str = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
    return_new_download_url = ''
    return_change_download_url = ''
    new_cdz_msg = ''
    change_cdz_msg = ''
    today_file_name = ''
    yesterday_file_name = ''
    def __init__(self, local_base_path,today_file_name,yesterday_file_name, AFS_BASE, afsshell, ruliu_hook):
        self.local_base_path = local_base_path
        self.today_file_name = today_file_name
        self.yesterday_file_name = yesterday_file_name
        self.AFS_BASE = AFS_BASE
        self.afsshell = afsshell
        self.ruliu_hook = ruliu_hook

    @retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
    def upload_file(self, upload_file_url, local_file_path):
        """
        上传文件
        """
        files = {'file': open(local_file_path, 'r', encoding='utf-8')}
        file_uuid = requests.post(upload_file_url, files=files).text
        if len(file_uuid) != 32:
            raise Exception(f'upload file failed')

        return file_uuid


    def upload_records(self, local_path: Path):
        """
        生成上传记录
        """
        if not local_path.exists():
            return ''

        file_uuid = self.upload_file(self.upload_file_url, local_path)
        print(f'{self.download_file_url}&uuid={file_uuid}')
        return f'{self.download_file_url}&uuid={file_uuid}'

def afs_upload_afs(monitor_tool:MonitorTool, local_file_path: str):
    """上传本地文件路径到AFS目标路径"""
    afs_target_path = monitor_tool.AFS_BASE
    try:
        subprocess.run([monitor_tool.afsshell, 'put', local_file_path, afs_target_path], check=True)
        print("文件成功上传到AFS。")
    except subprocess.CalledProcessError:
        print("上传文件到AFS时发生错误。")

def afs_download_file(monitor_tool:MonitorTool, download_path: str):
    """下载文件到本地"""
    try:
        subprocess.run([monitor_tool.afsshell, 'get', download_path, monitor_tool.local_base_path], check=True)
        print("文件成功下载到本地。")
    except subprocess.CalledProcessError:
        print("下载文件时发生错误。")

    

def alert_to_infoflow(msg,atuserids,token):
    """
    如流通知
    """
    notice_tool.send_hi(
        msg,
        atuserids=[atuserids],
        token=token
    )