"""
找充电站压盖aoi类型
"""
from pathlib import Path
from src.tools import pipeline, tsv, utils
from shapely import Point
from shapely import wkt
from tqdm import tqdm
from datetime import datetime
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from dataclasses import dataclass, field
from src.parking.recognition import dbutils
from src.charging_station.auto_repair_mixin import Context
from src.tools import pgsql
from src.charging_station import auto_repair_mixin
from src.charging_station.data import Poi, RepairResult, TicketConfig, RepairPropContext, Ticket

desc = pipeline.get_desc()
METER = 1e-5


@dataclass
class Aoi:
    """
    压盖aoi信息
    """
    aoi_bid: str
    face_id: str
    kind: str
    name: str
    name_ch: str
    std_tag: str
    show_tag: str
    dis: float = 1000
    boundary_dis: float = 0.0


@dataclass(init=True)
class Record:
    """
    充电站信息
    """
    aoi_bid: str = ''
    poi: Poi = field(default_factory=False)
    aoi_kind: str = ''
    aois: list[Aoi] = field(default_factory=list)
    reason: str = ''
    poi_ownership: str = 'unknown'


@desc()
def load_pois(ctx, proceed):
    """
    获取 poi 信息
    """
    sql = '''
        select bid,name, relation_bid, address, st_astext(geometry), telephone, status, std_tag, show_tag, click_pv
        from poi 
        where std_tag = '交通设施;充电站' ;
    '''
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    for row in tqdm(res):
        bid, name, relation_bid, address, geom_wkt, telephone, status, std_tag, show_tag, click_pv = row
        ctx.pois[bid] = Poi(
            bid=bid,
            name=name,
            relation_bid=relation_bid,
            address=address,
            geom=wkt.loads(geom_wkt),
            phone=telephone,
            status=status,
            tag=std_tag,
            show_tag=show_tag,
            click_pv=click_pv,
        )
    proceed()


@desc()
def fill_poi_ownership(ctx: Context, proceed):
    """
    填充 poi 的开放属性
    """
    public_type = 1

    sql = '''
        select online_type from cdz_history where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm(ctx.records):
            row = poi_stab.fetch_one(sql, [record.poi.bid])
            if row is None:
                record.poi_ownership = 'unknown'
                continue

            if row[0] == public_type:
                record.poi_ownership = 'public'
            else:
                record.poi_ownership = 'private'

    proceed()


@desc()
def load_pois_in_file(ctx, proceed):
    """
    获取 poi 信息
    """
    bids = [x[0] for x in tsv.read_tsv('data/aoi_study_bids.tsv')]
    sql = '''
        select bid,name, relation_bid, address, st_astext(geometry), telephone, status, std_tag, show_tag, click_pv
        from poi 
        where std_tag = '交通设施;充电站' and bid in %s;
    '''
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (tuple(bids),))
    for row in tqdm(res):
        bid, name, relation_bid, address, geom_wkt, telephone, status, std_tag, show_tag, click_pv = row
        ctx.records.append(Record(poi=Poi(
            bid=bid,
            name=name,
            relation_bid=relation_bid,
            address=address,
            geom=wkt.loads(geom_wkt),
            phone=telephone,
            status=status,
            tag=std_tag,
            show_tag=show_tag,
            click_pv=click_pv,
        )))
    proceed()


def search_poi(bid):
    """
    找到对应的poi
    """
    sql = '''
        select name,std_tag, show_tag
        from poi 
        where bid = %s;
    '''
    res = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [bid])
    return res


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    out_name = 'output' + datetime.now().strftime("%Y-%m-%d_%H-%M") + '.tsv'
    out_path = utils.ensure_path(Path(ctx.work_dir / out_name), cleanup=True)
    tsv.write_tsv(out_path,
        [[
            'p.bid', 'p.name', 'p.address', 'p.ownership',
            'aoi.bid',  'aoi.std_tag', 'aoi.show_tag', 'aoi.dis', 'aoi.boundary_dis'
        ]],
        mode="a")
    for x in tqdm(ctx.records):
        p = x.poi
        aoi = x.aois[0]
        tsv.write_tsv(out_path,
            [[
                "'" + p.bid, p.name, p.address, x.poi_ownership,
                    "'" + aoi.aoi_bid if len(x.aois) > 0 else '',
                    aoi.std_tag if len(x.aois) > 0 else '',
                    aoi.show_tag if len(x.aois) > 0 else '',
                    aoi.dis if len(x.aois) > 0 else '',
                    aoi.boundary_dis if len(x.aois) > 0 else '',
            ]],
            mode="a")


def fetch_online_aoi(geom_wkt: str):
    """
    找压盖的aoi
    """
    sql = """
        select face_id,kind,ST_ASTEXT(geom) as geom_wkt,name_ch 
        from blu_face 
        where (ST_Intersects(st_geomfromText(%s, 4326), geom)) and src != 'SQ' and
              kind != '52' ; 
    """
    res = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [geom_wkt])
    return res


def fetch_bid_by_faceid(face_id: str):
    """
    找bid对应的face_id
    """
    sql = """
        select poi_bid from blu_face_poi
        where face_id = %s
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, [face_id])
    if ret is None:
        return ret
    return ret[0]


def search_repeate_bid(bid):
    """
    查询重复的bid
    """
    sql = """
        select release_bid from aoi_repeat_bid where invalid_bid = %s;
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql, [bid])
    return res


def fetch_aoi_information(face_id, kind, geom_wkt, name_ch):
    """
    查询aoi信息
    """
    if face_id is None:
        return None
    aoi_bid = fetch_bid_by_faceid(face_id)
    if aoi_bid is None:
        aoi = Aoi(aoi_bid='', face_id=face_id, kind=kind, name='', name_ch=name_ch, std_tag='', show_tag='')
        return aoi
    res = search_poi(aoi_bid)
    if res is None:
        res = search_repeate_bid(aoi_bid)
        if res is None:
            aoi = Aoi(aoi_bid=aoi_bid, face_id=face_id, kind=kind, name='', name_ch=name_ch, std_tag='', show_tag='')
            return aoi
        aoi_bid = res[0]
        res = search_poi(aoi_bid)
        if res is None:
            aoi = Aoi(aoi_bid=aoi_bid, face_id=face_id, kind=kind, name='', name_ch=name_ch, std_tag='', show_tag='')
            return aoi
    name, std_tag, show_tag = res
    aoi = Aoi(aoi_bid=aoi_bid, face_id=face_id, kind=kind, name=name, name_ch=name_ch, std_tag=std_tag,
                show_tag=show_tag)
    return aoi


def find_aoi_by_poi(poi):
    """
    找poi压盖aoi
    """
    geom_wkt = poi.geom.wkt
    res = fetch_online_aoi(geom_wkt)
    aois = []
    for row in res:
        face_id, kind, geom_wkt, name_ch = row
        aoi_res = fetch_aoi_information(face_id, kind, geom_wkt, name_ch)
        if aoi_res is None:
            continue
        aoi_res.dis = 0.0
        aoi_res.boundary_dis = poi.geom.distance(wkt.loads(geom_wkt).boundary) / METER
        aois.append(aoi_res)

        break

    return aois


def find_nearest_aoi(poi):
    """
    找不压盖但最近的aoi
    """
    aois = []
    buffer_width = 1000 * METER
    geom_wkt = poi.geom.buffer(buffer_width).wkt
    res = fetch_online_aoi(geom_wkt)
    if res is None or len(res) == 0:
        return aois
    near_aoi_res = res[0]
    if len(res) > 1:
        near_dis = buffer_width + 1
        for row in res:
            face_id, kind, aoi_geom_wkt, name_ch = row
            aoi_dis = poi.geom.distance(wkt.loads(aoi_geom_wkt))
            if aoi_dis < near_dis:
                near_aoi_res = row
                near_dis = aoi_dis
    if near_aoi_res is not None:
        face_id, kind, aoi_geom_wkt, name_ch = near_aoi_res
        aoi_res = fetch_aoi_information(face_id, kind, aoi_geom_wkt, name_ch)
        if aoi_res is not None:
            aoi_res.dis = poi.geom.distance(wkt.loads(aoi_geom_wkt)) / METER
            aoi_res.boundary_dis = poi.geom.distance(wkt.loads(aoi_geom_wkt).boundary) / METER
            aois.append(aoi_res)
    return aois


def check_aoi(ctx, proceed):
    """
    检查压盖aoi类型
    """
    buffer_width = 0
    for bid, poi in tqdm(ctx.pois.items()):
        aois = find_aoi_by_poi(poi)
        if len(aois) == 0:
            continue
        for aoi in aois:
            record = Record(poi=poi, aoi=aoi, )
            ctx.records.append(record)
    proceed()


@desc()
def fill_aoi_kind(ctx: Context, proceed):
    """
    填充aoi类型
    """
    public_tag = ['交通设施', '旅游景点', '购物', '酒店']
    public_tag_not_include = ['公交车', '长途汽车', '停车']
    private_tag = ['政府机构', '房地产;住宅区', '公司企业;园区', '公司企业;公司', '教育培训;高等院校', '教育培训;中学',
                   '教育培训;小学', '医疗;疗养院']
    for record in tqdm(ctx.records):
        poi = record.poi
        aois = find_aoi_by_poi(poi)
        if len(aois) == 0:
            continue
        record.aois.extend(aois)
        for aoi in aois:
            if aoi.dis != 0.0:
                continue
            for tag in private_tag:
                if tag in aoi.std_tag:
                    record.aoi_kind = tag
                    if record.reason != '':
                        record.reason = 'OWNERSHIP_REASON_AOI_CONTAINS_PRIVATE_TAGS'
                    break
            if record.reason != '':
                break
            for tag in public_tag:
                if tag in aoi.std_tag:
                    if not any(item in aoi.std_tag for item in public_tag_not_include):
                        record.aoi_kind = tag
                        if record.reason != '':
                            record.reason = 'OWNERSHIP_REASON_AOI_CONTAINS_PUBLIC_TAGS'
            if record.reason != '':
                break
    proceed()



def create_pipeline(args):
    """创建执行管道"""
    mode = args.mode if args is not None else 'eval'
    print(f"Running in {mode} mode")
    pipes = [
        load_pois_in_file,
        fill_aoi_kind,
        fill_poi_ownership,
    ]
    pipes.extend([
        save_records
    ])
    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """主函数"""
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('data/aoi_cover'),
    )
    if repair_context is not None:
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois
    main_pipe(ctx)
    print(ctx.records)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())