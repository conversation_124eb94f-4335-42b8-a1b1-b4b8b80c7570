"""
找距离父点aoi过远的充电站
"""
from pathlib import Path
from src.tools import pipeline, tsv, utils
from shapely import Point
from shapely import wkt
from tqdm import tqdm
from datetime import datetime
from dataclasses import dataclass, field
from src.parking.recognition import dbutils
from src.charging_station.auto_repair_mixin import Context
from src.tools import pgsql
from src.charging_station import auto_repair_mixin
from src.charging_station.data import Poi, RepairResult, TicketConfig, RepairPropContext, Ticket

desc = pipeline.get_desc()

METER = 1e-5

NAME = 'check_aoi_distance'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = 'CDXJSK202402072031'
BATCH_NAME = '坐标'
METHOD = 'edit'



@dataclass
class Record:
    """
    充电站信息
    """
    poi: Poi = field(default_factory=False)
    distance: float = 0.0
    aoi_bid: str = ''

    @property
    def can_eval(self):
        """
        是否可以评估
        """
        return True

    @property
    def can_manual_process(self):
        """是否给人工作业"""
        return self.can_eval

    @property
    def work_message(self):
        """作业提示"""
        return '疑似父点aoi距离充电站距离过大，请核实。'



def create_tickets(ctx: Context, config: TicketConfig):
    """
    创建工单 没有tp所以重新写了
    """
    for record in tqdm([x for x in ctx.records if x.can_manual_process]):
        ctx.tickets.append(Ticket(
            project=config.project,
            priority=config.priority,
            batch_id=config.batch_id,
            bid=record.poi.bid,
            method=config.method,
            message=record.work_message,
            src=config.src,
            suggest_poi=Poi(
                name=record.poi.name,
                alias=record.poi.alias,
                address=record.poi.address,
                mc_geom=wkt.loads(record.poi.mc_wkt),
                phone=record.poi.phone,
                status=record.poi.status,
                tag=record.poi.tag,
            ),
            batch_name=config.batch_name,
        ))


@desc()
def load_pois(ctx, proceed):
    """
    获取 poi 信息
    """
    sql = '''
        select bid,name, relation_bid, address, st_astext(geometry), telephone, status, std_tag, show_tag, click_pv
        from poi 
        where std_tag = '交通设施;充电站' and status = 1;
    '''
    res = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    for row in tqdm(res):
        bid, name, relation_bid, address, geom_wkt, telephone, status, std_tag, show_tag, click_pv = row
        ctx.pois[bid] = Poi(
            bid=bid,
            name=name,
            relation_bid=relation_bid,
            address=address,
            geom=wkt.loads(geom_wkt),
            phone=telephone,
            status=status,
            tag=std_tag,
            show_tag=show_tag,
            click_pv=click_pv,
        )
    proceed()

def fetch_face_id_by_bid(poi_bid: str):
    """
    找bid对应的face_id
    """
    sql = """
        select face_id from blu_face_poi
        where poi_bid = %s
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, [poi_bid])
    return ret


def fetch_geom_by_face_id(face_id: str):
    """
    找face_id对应的area
    """
    sql = f'''select st_astext(geom) from blu_face where face_id = '{face_id}';'''
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql)
    return ret


@desc()
def check_aoi_distance(ctx, proceed):
    """检查到aoi的距离"""
    AOI_DISTANCE = 200
    for poi in tqdm(ctx.pois.values(), total=len(ctx.pois)):
        relation_bid = poi.relation_bid
        if relation_bid is None or relation_bid == '' or relation_bid == '0':
            continue
        face_id = fetch_face_id_by_bid(relation_bid)
        if face_id is None or len(face_id) == 0:
            continue
        geom_wkt = fetch_geom_by_face_id(face_id[0])
        if geom_wkt is None:
            continue
        geom = wkt.loads(geom_wkt[0])
        dis = poi.geom.distance(geom) / METER
        if dis > AOI_DISTANCE:
            ctx.records.append(Record(poi=poi, distance=dis, aoi_bid=relation_bid))
    print(len(ctx.records))
    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    auto_repair_mixin.fill_poi_mc_wkt(ctx)
    proceed()


@desc()
def create_tickets_run(ctx: Context, proceed):
    """
    创建工单
    """
    create_tickets(ctx, TicketConfig(
        project=PROJECT,
        priority=PRIORITY,
        batch_id=BATCH,
        method=METHOD,
        src=NAME,
        batch_name=BATCH_NAME,
    ))
    proceed()


@desc()
def save_records(file_path, records: list[Record]):
    """
    保存记录
    'poi.bid','poi.name','poi.address','poi.geom','distance','aoi_bid'
    """
    for record in tqdm(records):
        poi = record.poi
        out_item = [
            poi.bid, poi.name, poi.address, poi.geom.wkt, record.distance, record.aoi_bid,
        ]
        tsv.write_tsv(file_path,
              [out_item],
              mode="a")


@desc()
def save_manual_records(ctx: Context, proceed):
    """
    保存人工处理记录
    """
    out_name = 'manual_' + datetime.now().strftime("%Y-%m-%d") + '.tsv'
    save_records(ctx.work_dir / out_name, [x for x in ctx.records if x.can_manual_process])
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    out_name = 'eval' + datetime.now().strftime("%Y-%m-%d") + '.tsv'
    save_records(ctx.work_dir / out_name, [x for x in ctx.records if x.can_eval])
    proceed()

def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_records=ctx.batch_records,
        batch_name=BATCH_NAME,
    )

def create_pipeline(args):
    """创建执行管道"""
    mode = args.mode if args is not None else 'eval'
    print(f"Running in {mode} mode")
    pipes = [
        load_pois,
        check_aoi_distance
    ]
    if mode == 'manual':
        pipes += [fill_poi_mc_wkt, create_tickets_run]
    pipes.extend([
        save_eval_records,
        save_manual_records,
    ])
    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """主函数"""
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('data/aoi_too_far'),
    )
    if repair_context is not None:
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois
    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())