"""
找竞品Diff
"""
import random
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

from shapely import Point
from shapely import wkt
from tqdm import tqdm

from src.batch_process import batch_helper
from src.charging_station import auto_repair_mixin
from src.charging_station.data import Poi, SPACING_UNKNOWN, CompetitorDiff, SPACING_VALUE_MAP
from src.charging_station.helper import calc_spacing
from src.parking.recognition import dbutils
from src.tools import pgsql
from src.tools import pipeline, tsv, utils

desc = pipeline.get_desc()

METER = 1e-5

field_names = {
    'LOCATION': 'location'
}


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    pois: dict[str, Poi] = field(default_factory=dict)
    records: list = field(default_factory=list)
    batch_records: list[Poi] = field(default_factory=list)
    diff_data: list[CompetitorDiff] = field(default_factory=list)


@dataclass
class Poi:
    """
    poi 信息
    """
    bid: str = ''
    click_pv: int = 0
    relation_bid: str = ''
    name: str = ''
    alias: str = ''
    address: str = ''
    wkt: str = ''
    geom: Point = None
    mc_wkt: str = ''
    mc_geom: Point = None
    phone: str = ''
    status: int = 1
    tag: str = ''
    show_tag: str = ''
    classify_tag: str = ''
    ownership: str = ''
    city: str = ''
    is_online: bool = None
    is_public: bool = True
    create_time: int = 0
    update_time: int = 0


@dataclass
class Competitor:
    """
    充电站竞品数据
    """
    id: str
    name: str
    address: str
    geom: Point
    name_iou: float = 0.0
    address_iou: float = 0.0
    tag: str = ''
    telephone: str = ''
    is_public: bool = True
    space_tag: str = ''
    status: str = '1'
    is_online: bool = True


@dataclass
class Record:
    """
    记录
    """
    poi: Poi = field(init=False)
    competitor: Competitor = field(init=False)

    name_iou: float = 0.0
    competitor_distance: float = 0.0
    diff_props: list = field(default_factory=list)

    def __init__(self, poi, competitor, name_iou, distance):
        self.poi = poi
        self.competitor = competitor
        self.diff_props = []
        self.name_iou = name_iou
        self.competitor_distance = distance
        if competitor is not None:
            self.competitor_distance = self.poi.geom.distance(self.competitor.geom) / METER

    @property
    def can_eval(self):
        """
        是否可以评估
        """
        return len(self.diff_props) > 0


def fetch_cp_in_range(geom_wkt: str):
    """
    找范围内的充电站poi
    """
    sql = """
        select id,name, tag, address, st_astext(geom), telephone
        from cdz_competitor
        where ST_Contains(st_geomfromText(%s, 4326), geom) and
              crawl_time >= extract(epoch from (now() - interval '1 months'))::integer;
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [geom_wkt])
    result_cp = []
    for row in ret:
        id, name, tag, address, geom_wkt, telephone = row
        result_cp.append(Competitor(
            id=id,
            name=name,
            tag=tag,
            address=address,
            geom=wkt.loads(geom_wkt),
            telephone=telephone
        ))
    return result_cp


def find_max_cp(poi):
    """
    找到最相似的竞品
    """
    name_min_iou = 0.8
    buffer_width = 100 * METER
    buffer_geom = poi.geom.buffer(buffer_width)
    cp_reuslt = fetch_cp_in_range(buffer_geom.wkt)
    if cp_reuslt is None:
        return []
    if len(cp_reuslt) > 0:
        name_iou = 0
        max_iou = 0.0
        max_cp = None
        for p in cp_reuslt:
            # 匹配名称
            name_iou = batch_helper.get_str_iou(poi.name, p.name)
            if name_iou > name_min_iou and name_iou > max_iou:
                max_iou = name_iou
                max_cp = p
        if max_cp is not None:
            dis = poi.geom.distance(max_cp.geom) / METER
            return max_cp, max_iou, dis
    return None, 0.0, 0.0


@desc()
def match_competitor_from_db(ctx, proceed):
    """
    db匹配竞品
    """
    sql = """
    select
        b.id, a.bid, b.name, b.tag, b.address, st_astext(b.geom), b.telephone
    from competitor_charge a inner join cdz_competitor b on a.gid = b.competitor_id
    where
        gid != ''
    """
    bid_competitor_dic = {}
    for id, bid, gd_name, tag, address, gd_wkt, telephone in dbutils.fetch_all(pgsql.POI_CONFIG, sql):
        bid_competitor_dic[bid] = Competitor(
            id=id,
            name=gd_name,
            tag=tag,
            address=address,
            geom=Point(wkt.loads(gd_wkt)),
            telephone=telephone
        )

    for bid, poi in tqdm(ctx.pois.items()):
        max_cp = bid_competitor_dic.get(bid)
        if max_cp is None:
            continue
        dis = poi.geom.distance(max_cp.geom) / METER
        ctx.records.append(Record(poi=poi, competitor=max_cp, name_iou=0, distance=dis))
    proceed()


def fetch_random(source, num: int):
    """
    随机取数据
    """
    sampled_data = random.sample(source, num)
    return sampled_data


@desc()
def load_pois(ctx, proceed):
    """
    获取 poi 信息
    """
    sql = '''
        select bid,name, alias, address, st_astext(geometry), telephone, status, std_tag, show_tag, click_pv, city
        from poi 
        where std_tag = '交通设施;充电站' ;
    '''
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    # 随机抽取数据
    # random_data = fetch_random(res, 10000)
    for row in tqdm(res):
        bid, name, alias, address, geom_wkt, telephone, status, std_tag, show_tag, click_pv, city = row
        ctx.pois[bid] = Poi(
            bid=bid,
            name=name,
            alias=alias,
            address=address,
            geom=wkt.loads(geom_wkt),
            phone=telephone,
            status=status,
            tag=std_tag,
            show_tag=show_tag,
            click_pv=click_pv,
            city=city
        )
    proceed()


def check_is_offline(name: str):
    """
    通过名字检查是否是离线状态
    """
    offline_keys = {
        '暂停营业',
        '暂不营业',
        '已下线',
        '关闭',
    }
    for k in offline_keys:
        if k in name:
            return k
    return None


def check_cp_public(competitor: Competitor):
    """
    通过名字检查竞品开放属性
    """
    private_keys = {'内部使用', '内部专用', '(内部)', '不对外', '仅内', '私人'}
    public_keys = {'公共', '公用', '对外'}
    public_keys_not_include = {'公交', '公共交通', '公共汽车'}
    private_tags = {
        '个人充电站'
    }
    cdz_name = competitor.name
    cdz_tag = competitor.tag
    for key in private_tags:
        if key in cdz_tag:
            return False
    # for key in public_keys:
    #     if key in cdz_tag:
    #         return True
    if any(key in cdz_name for key in private_keys):
        return False
    return True
    # if any(key in cdz_name for key in public_keys):
    #     if not any(key in cdz_name for key in public_keys_not_include):
    #         return True


def fetch_online_type(bid):
    """
    找开放类型
    """
    sql = """
        select online_type from cdz_history where bid=%s; 
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql, [bid])
    return res


@desc()
def fill_cp_status(ctx, proceed):
    """
    填充竞品状态
    """
    for record in tqdm(ctx.records):
        if record.competitor is None:
            continue
        # 检查是否是离线状态
        name_check = check_is_offline(record.competitor.name)
        if name_check is not None:
            record.competitor.status = name_check
            record.competitor.is_online = False
        # 检查开放属性
        record.competitor.is_public = check_cp_public(record.competitor)
        # 检查空间属性
        record.competitor.space_tag = calc_spacing(record.competitor.name)
        if record.competitor.space_tag == SPACING_UNKNOWN:
            record.competitor.space_tag = calc_spacing(record.competitor.address)
        if record.competitor.space_tag != SPACING_UNKNOWN:
            record.competitor.space_tag = SPACING_VALUE_MAP[record.competitor.space_tag]
    proceed()


def fill_poi_status(ctx: Context, proceed):
    """
    填充 poi 状态
    """
    for record in tqdm(ctx.records):
        if record.poi is None:
            continue
        # 检查是否是离线状态
        record.poi.is_online = True if record.poi.status == 1 else False
    proceed()


@desc()
def fill_poi_public(ctx: Context, proceed):
    """
    填充开放属性
    """
    public_type = 1
    for record in tqdm(ctx.records):
        bid = record.poi.bid
        if bid is None:
            continue
        res = fetch_online_type(bid)
        if res is not None:
            if res[0] == public_type:
                record.poi.is_public = True
            else:
                record.poi.is_public = False
    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    out_name = 'output' + datetime.now().strftime("%Y-%m-%d_%H-%M") + '.tsv'
    out_path = utils.ensure_path(Path(ctx.work_dir / out_name), cleanup=True)
    tsv.write_tsv(out_path,
                  [[
                      'cp.name', 'cp.address', 'cp.tag', 'cp.geom', 'cp.status', 'cp.space_tag', 'cp.telephone',
                      'cp.is_public',
                      'p.bid', 'p.name', 'p.address', 'p.city', 'p.geom', 'p.status', 'p.show_tag', 'p.telephone',
                      'p.is_public',
                      'distance', 'diff_str'
                  ]],
                  mode="a")
    for record in tqdm(ctx.records):
        if record.can_eval:
            cp = record.competitor
            p = record.poi
            tsv.write_tsv(out_path,
                          [[
                              cp.name, cp.address, cp.tag, cp.geom, cp.status, cp.space_tag, cp.telephone, cp.is_public,
                              p.bid, p.name, p.address, p.city, p.geom, p.status, p.show_tag, p.phone, p.is_public,
                              record.competitor_distance, ",".join(record.diff_props)
                          ]],
                          mode="a")
    proceed()


def check_position(ctx: Context, proceed):
    """
    检查坐标距离
    """
    max_distance_limit = 30
    for record in tqdm(ctx.records):
        if record.competitor is None or record.poi is None:
            continue
        if record.competitor.geom is None or record.poi.geom is None:
            continue
        record.competitor_distance = record.poi.geom.distance(record.competitor.geom) / METER
        if record.competitor_distance > max_distance_limit:
            ctx.diff_data.append(
                CompetitorDiff(bid=record.poi.bid, prop_type=field_names['LOCATION'],
                               bd_value=str(record.poi.geom), gd_value=str(record.competitor.geom))
            )
            record.diff_props.append(field_names['LOCATION'])
    proceed()


def check_prop(ctx: Context, record: Record, prop_type: str, bd_value: str, gd_value: str):
    """
    检查属性
    """
    if record.competitor is None or record.poi is None:
        return
    if gd_value != bd_value:
        ctx.diff_data.append(
            CompetitorDiff(bid=record.poi.bid, prop_type=prop_type, bd_value=bd_value, gd_value=gd_value))
        record.diff_props.append(prop_type)


def check_prop_diff(ctx: Context, proceed):
    """
    检查属性Diff: 电话、开放属性、空间属性、状态
    """
    for record in tqdm(ctx.records):
        check_prop(ctx, record, 'telephone', record.poi.phone, record.competitor.telephone)
        check_prop(ctx, record, 'ownership', record.poi.is_public, record.competitor.is_public)
        if record.competitor.space_tag != SPACING_UNKNOWN:
            check_prop(ctx, record, 'spacing', record.poi.show_tag, record.competitor.space_tag)
        check_prop(ctx, record, 'status', record.poi.is_online, record.competitor.is_online)
    proceed()


competitor_diff_pipe = [
    load_pois,
    match_competitor_from_db,
    fill_poi_public,
    fill_cp_status,
    fill_poi_status,
    check_position,
    check_prop_diff,
    save_records
]


def create_pipeline(args):
    """创建管道"""
    mode = args.mode if args is not None else None
    pipes = competitor_diff_pipe
    return pipeline.Pipeline(*pipes)


def main(args=None):
    """主函数"""
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('data/competitor_diff'),
    )

    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())
