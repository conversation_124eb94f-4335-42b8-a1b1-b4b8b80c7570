"""
找状态不一致的充电站Poi
"""
from tqdm import tqdm
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict
from dataclasses import dataclass, field
from src.charging_station import auto_repair_mixin
from src.charging_station import auto_repair_location
from src.charging_station import auto_repair_status
from src.charging_station import find_competitor_diff
from src.charging_station.auto_repair_mixin import Context
from src.charging_station.data import Poi, RepairResult, RepairPropContext, TpRecord, Pile, Competitor, TicketConfig
from src.tools import pipeline, tsv, utils
from src.charging_station.helper import get_manual_status_bids
from shapely import Point
from src.charging_station.online_prop import online_status
from src.parking.recognition import dbutils
from src.tools import pgsql
from shapely import wkt
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer

desc = pipeline.get_desc()

NAME = 'update_status'
PROJECT = 'CDX'
PRIORITY = 4
BATCH = 'CDXJSK20250210003'  # 待更改
BATCH_NAME = '状态'
METHOD = 'edit'


@dataclass
class Record:
    """
    充电站信息
    """
    tp: TpRecord = field(init=False)
    poi: Poi = field(init=False)
    competitor: Competitor = field(init=False)
    is_tp_pile_online: bool = None
    is_tp_station_online: bool = None
    is_cp_online: bool = None
    has_manual: bool = None
    crawl_duration: float = None
    reason: str = ''

    def __init__(self, tp: TpRecord = None, poi: Poi = None, competitor: Competitor = None, reason: str = ''):
        self.tp = tp
        self.poi = poi
        self.competitor = competitor
        self.reason = reason

    @property
    def work_message(self):
        """作业提示"""
        return '疑似实际状态正常，请核实。' if self.poi.status != 1 else '疑似实际状态不在线，请核实。'

    @property
    def can_eval(self):
        """是否可以评估"""
        if self.poi.status == 1 and not self.is_tp_pile_online and not self.is_cp_online:
            return True
        if self.poi.status != 1 and self.is_tp_pile_online and self.is_cp_online:
            return True
        return False

    @property
    def can_auto_process(self):
        """是否自动化"""
        key_word = '国家电网'
        if self.tp.src == 'manual':
            return False
        if (self.poi.status == 1 and not self.is_tp_pile_online and not self.is_tp_station_online and
                self.is_tp_station_online is not None and not self.is_cp_online):
            return True
        if self.poi.status != 1 and self.is_tp_pile_online and self.is_tp_station_online and self.is_cp_online:
            return True
        if (self.poi.status == 1 and not self.is_tp_pile_online and self.is_tp_pile_online is not None and
                not self.is_cp_online and key_word in self.poi.name):
            return True

    @property
    def can_manual_process(self):
        """是否给人工作业"""
        return self.can_eval and not self.can_auto_process


@desc()
def load_pois(ctx, proceed):
    """
    获取 poi 信息
    """
    sql = '''
        select name, alias, address, st_astext(geometry), telephone, status, std_tag, show_tag, click_pv
        from poi 
        where bid = %s ;
    '''
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid in tqdm(list(ctx.tp_records.keys())):  # list(ctx.tp_records.keys())
            row = poi_stab.fetch_one(sql, [bid])
            if row is None:
                continue

            name, alias, address, geom_wkt, telephone, status, std_tag, show_tag, click_pv = row
            ctx.pois[bid] = Poi(
                bid=bid,
                name=name,
                alias=alias,
                address=address,
                geom=wkt.loads(geom_wkt),
                phone=telephone,
                status=status,
                tag=std_tag,
                show_tag=show_tag,
                click_pv=click_pv,
            )
    proceed()


@desc()
def retain_best_matched_tp(ctx: Context, proceed):
    """
    保留最佳匹配的 tp
    """
    auto_repair_mixin.retain_best_matched_tp(
        ctx=ctx,
        create_record=lambda **kwargs: Record(
            tp=kwargs['tp'],
            poi=kwargs['poi'],
            competitor=kwargs.get('competitor', None)
        ),
        match_competitor=True,
        common_poi_tp_min_name_iou=0.6
    )
    proceed()


def estimate_tp_status_if_pile_offline(ctx: Context, record: Record):
    """
    根据桩头状态判断 tp 是否下线
    """
    error_statuses = {0, 255}
    piles = ctx.piles.get(record.tp.tid, [])
    if len(piles) == 0:
        return
    if all(x.status in error_statuses for x in piles):
        record.reason = 'pile status is offline'
        record.is_tp_pile_online = False
        return
    if len(piles) > 0:
        record.is_tp_pile_online = True
        return


def check_has_manual(bid: str):
    """
    检查是否有人工作业记录
    """
    sql = """
        select * from cdz_ticket where bid=%s;
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [bid])
    if ret is None or len(ret) == 0:
        return False
    return True


@desc()
def fill_has_manual(ctx: Context, proceed):
    """
    填充是否有人工作业记录
    """
    for record in tqdm(ctx.records):
        record.has_manual = check_has_manual(record.poi.bid)
    proceed()


def estimate_tp_status_if_station_offline(record: Record):
    """
    根据站点状态判断 tp 是否下线
    """
    offline_keys = {
        '暂停营业',
        '暂不营业',
        '已下线',
        '关闭',
    }
    offline_statuses = {1, 5, 6}
    online_statuses = {50}

    if record.tp.station_status in online_statuses:
        record.is_tp_station_online = True
        record.reason = 'station status is online'
        return

    if any(key in record.tp.station_name for key in offline_keys):
        record.reason = 'name contains offline keys'
        record.is_tp_station_online = False
        return

    if record.tp.station_status in offline_statuses:
        record.reason = 'station status is offline'
        record.is_tp_station_online = False
        return


def check_is_offline(name: str):
    """
    检查是否离线
    """
    offline_keys = {
        '暂停营业',
        '暂不营业',
        '已下线',
        '关闭',
    }
    for k in offline_keys:
        if k in name:
            return False
    return True


def match_competitor_by_poi_name(ctx: Context, proceed):
    """
    找100米内的竞品，名称iou>0.8
    """
    for item in tqdm(ctx.records):
        if item.competitor is None:
            match_competitor = find_competitor_diff.find_max_cp(item.poi)
            if match_competitor is not None:
                item.competitor = match_competitor[0]
    proceed()


@desc()
def save_records(file_path, records: list[Record]):
    """
    保存记录
    'bid','name','address','status','competitor.name',
    'is_tp_pile_online','is_tp_station_online','is_cp_online','has_manual'
    """
    for record in tqdm(records):
        if record.competitor is not None:
            out_item = [
                record.tp.bid, record.poi.name, record.poi.address, record.poi.status, record.competitor.name,
                record.is_tp_pile_online, record.is_tp_station_online, record.is_cp_online, record.has_manual
            ]
            tsv.write_tsv(file_path,
                          [out_item],
                          mode="a")
        else:
            out_item = [
                record.tp.bid, record.poi.name, record.poi.address, record.poi.status, None,
                record.is_tp_pile_online, record.is_tp_station_online, record.is_cp_online, record.has_manual
            ]
            tsv.write_tsv(file_path,
                          [out_item],
                          mode="a")


@desc()
def fill_tp_status(ctx: Context, proceed):
    """
    填充 tp 状态
    """
    for record in tqdm(ctx.records):
        estimate_tp_status_if_pile_offline(ctx, record)
        estimate_tp_status_if_station_offline(record)

    proceed()


@desc()
def fill_cp_status(ctx: Context, proceed):
    """
    填充 cp 状态
    """
    for record in tqdm(ctx.records):
        if record.competitor is not None:
            record.is_cp_online = check_is_offline(record.competitor.name)
    proceed()


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    auto_repair_mixin.create_tickets(ctx, TicketConfig(
        project=PROJECT,
        priority=PRIORITY,
        batch_id=BATCH,
        method=METHOD,
        src=NAME,
        batch_name=BATCH_NAME,
    ))
    proceed()


@desc()
def execute_batch(ctx: Context, proceed):
    """
    执行批处理
    """
    status_open = 1
    status_close = 3

    manual_bids = get_manual_status_bids()
    ctx.batch_records = [
        Poi(bid=x.tp.bid, status=status_open if x.is_tp_online else status_close)
        for x in ctx.records if x.can_auto_process and x.tp.bid not in manual_bids
    ]
    online_status(ctx.batch_records)
    proceed()


@desc()
def fill_poi_mc_wkt(ctx: Context, proceed):
    """
    填充 poi 墨卡托坐标
    """
    auto_repair_mixin.fill_poi_mc_wkt(ctx)
    proceed()


@desc()
def save_eval_records(ctx: Context, proceed):
    """
    保存评估记录
    """
    out_name = 'eval' + datetime.now().strftime("%Y-%m-%d_%H-%M") + '.tsv'
    save_records(ctx.work_dir / out_name, [x for x in ctx.records if x.can_eval])
    proceed()


@desc()
def save_auto_records(ctx: Context, proceed):
    """
    保存自动处理记录
    """
    out_name = 'auto' + datetime.now().strftime("%Y-%m-%d_%H-%M") + '.tsv'
    save_records(ctx.work_dir / out_name, [x for x in ctx.records if x.can_auto_process])
    proceed()


@desc()
def save_manual_records(ctx: Context, proceed):
    """
    保存人工处理记录
    """
    out_name = 'manual' + datetime.now().strftime("%Y-%m-%d_%H-%M") + '.tsv'
    save_records(ctx.work_dir / out_name, [x for x in ctx.records if x.can_manual_process])
    proceed()


def run(repair_context: RepairPropContext):
    """
    以方法模式运行脚本
    """
    ctx = main(repair_context=repair_context)
    return RepairResult(
        tickets=ctx.tickets,
        batch_records=ctx.batch_records,
        batch_name=BATCH_NAME,
    )


def create_pipeline(args, repair_context: RepairPropContext):
    """创建执行管道"""
    mode = args.mode if args is not None else 'eval'
    print(f"Running in {mode} mode")
    pipes = [
        auto_repair_location.load_tp_records,
        load_pois,
        auto_repair_status.load_piles,
        retain_best_matched_tp,
        match_competitor_by_poi_name,
        fill_tp_status,
        fill_cp_status,
        fill_has_manual
    ]
    if mode == 'manual':
        pipes += [fill_poi_mc_wkt, create_tickets]
    elif mode == 'auto':
        pipes += [fill_poi_mc_wkt, execute_batch]
    elif mode == 'all':
        pipes += [fill_poi_mc_wkt, create_tickets, execute_batch]

    pipes.extend([
        save_eval_records,
        save_auto_records,
        save_manual_records,
    ])
    return pipeline.Pipeline(*pipes)


def main(args=None, repair_context: RepairPropContext = None):
    """主函数"""
    main_pipe = create_pipeline(args, repair_context)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('data/status_not_same'),
    )
    if repair_context is not None:
        ctx.tp_records = repair_context.tp_records
        ctx.pois = repair_context.pois
    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())