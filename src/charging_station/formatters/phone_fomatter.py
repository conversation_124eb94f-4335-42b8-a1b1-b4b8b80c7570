# !/usr/bin/env python3
"""
电话号码格式化工具
"""
import re

import phonenumbers

# 无视 tp 强制刷新
FORCE_TEL = {
    '普天新能源': '95700',
    'e充电': '95598',
    '国家电网': '95598',
    '中石化': '95105888',
    '中国石化': '95105888',
    '特来电': '4001300001',
    '南网电动': '4006895598',
    '南方电网': '4006895598',
    '快电': '4000826699',
    '云快充': '4008288517',
    '小桔充电': '4000610999',
    '驴充充': '0797966999',
    '铁塔能源': '10096',
}
# 优先 tp，tp 没有的话，用默认的
DEFAULT_TEL = FORCE_TEL | {
    '新电途': '4001100835',
    '星星充电': '4008280768',
    '依威能源': '4001800910',
    '达克云': '4006717555',
    '蔚来能源': '02167099903',
    '蔚来': '02167099903',
    '特斯拉': '4009100707',
    '润诚达': '4000961518',
    '小鹏汽车': '4007836688',
    '壳牌': '4007160699',
    '理想': '4006860400',
    '广汽能源': '4006665330',
    '极越': '4006201688',
    '开迈斯': '4006657666',
    '中国铁塔': '10096',
}
# 无视 poi 强制刷新
REPAIR_BRAND = {
    '驴充充': {'4009155388'},
    '铁塔能源': {''},
    '中国铁塔': {''},
}
CITY_TO_REGION_CODE = {
    '北京市': '010', '天津市': '022', '上海市': '021', '重庆市': '023', '石家庄市': '0311', '唐山市': '0315',
    '秦皇岛市': '0335', '邯郸市': '0310', '邢台市': '0319', '保定市': '0312', '张家口市': '0313', '承德市': '0314',
    '沧州市': '0317', '廊坊市': '0316', '衡水市': '0318', '太原市': '0351', '大同市': '0352', '阳泉市': '0353',
    '长治市': '0355', '晋城市': '0356', '朔州市': '0349', '晋中市': '0354', '运城市': '0359', '忻州市': '0350',
    '临汾市': '0357', '吕梁市': '0358', '呼和浩特市': '0471', '包头市': '0472', '乌海市': '0473', '赤峰市': '0476',
    '通辽市': '0475', '鄂尔多斯市': '0477', '呼伦贝尔市': '0470', '巴彦淖尔市': '0478', '乌兰察布市': '0474',
    '兴安盟': '0482', '锡林郭勒盟': '0479', '阿拉善盟': '0483', '沈阳市': '024', '大连市': '0411', '鞍山市': '0412',
    '抚顺市': '0413', '本溪市': '0414', '丹东市': '0415', '锦州市': '0416', '营口市': '0417', '阜新市': '0418',
    '辽阳市': '0419', '盘锦市': '0427', '铁岭市': '0410', '朝阳市': '0421', '葫芦岛市': '0429', '长春市': '0431',
    '吉林市': '0432', '四平市': '0434', '辽源市': '0437', '通化市': '0435', '白山市': '0439', '松原市': '0438',
    '白城市': '0436', '延边朝鲜族自治州': '0433', '哈尔滨市': '0451', '齐齐哈尔市': '0452', '鸡西市': '0467',
    '鹤岗市': '0468', '双鸭山市': '0469', '大庆市': '0459', '伊春市': '0458', '佳木斯市': '0454', '七台河市': '0464',
    '牡丹江市': '0453', '黑河市': '0456', '绥化市': '0455', '大兴安岭地区': '0457', '南京市': '025', '无锡市': '0510',
    '徐州市': '0516', '常州市': '0519', '苏州市': '0512', '南通市': '0513', '连云港市': '0518', '淮安市': '0517',
    '盐城市': '0515', '扬州市': '0514', '镇江市': '0511', '泰州市': '0523', '宿迁市': '0527', '杭州市': '0571',
    '宁波市': '0574', '温州市': '0577', '嘉兴市': '0573', '湖州市': '0572', '绍兴市': '0575', '金华市': '0579',
    '衢州市': '0570', '舟山市': '0580', '台州市': '0576', '丽水市': '0578', '合肥市': '0551', '芜湖市': '0553',
    '蚌埠市': '0552', '淮南市': '0554', '马鞍山市': '0555', '淮北市': '0561', '铜陵市': '0562', '安庆市': '0556',
    '黄山市': '0559', '滁州市': '0550', '阜阳市': '0558', '宿州市': '0557', '六安市': '0564', '亳州市': '0558',
    '池州市': '0566', '宣城市': '0563', '福州市': '0591', '厦门市': '0592', '莆田市': '0594', '三明市': '0598',
    '泉州市': '0595', '漳州市': '0596', '南平市': '0599', '龙岩市': '0597', '宁德市': '0593', '南昌市': '0791',
    '景德镇市': '0798', '萍乡市': '0799', '九江市': '0792', '新余市': '0790', '鹰潭市': '0701', '赣州市': '0797',
    '吉安市': '0796', '宜春市': '0795', '抚州市': '0794', '上饶市': '0793', '济南市': '0531', '青岛市': '0532',
    '淄博市': '0533', '枣庄市': '0632', '东营市': '0546', '烟台市': '0535', '潍坊市': '0536', '济宁市': '0537',
    '泰安市': '0538', '威海市': '0631', '日照市': '0633', '莱芜市': '0634', '临沂市': '0539', '德州市': '0534',
    '聊城市': '0635', '滨州市': '0543', '菏泽市': '0530', '郑州市': '0371', '开封市': '0378', '洛阳市': '0379',
    '平顶山市': '0375', '安阳市': '0372', '鹤壁市': '0392', '新乡市': '0373', '焦作市': '0391', '濮阳市': '0393',
    '许昌市': '0374', '漯河市': '0395', '三门峡市': '0398', '南阳市': '0377', '商丘市': '0370', '信阳市': '0376',
    '周口市': '0394', '驻马店市': '0396', '武汉市': '027', '黄石市': '0714', '十堰市': '0719', '宜昌市': '0717',
    '襄阳市': '0710', '鄂州市': '0711', '荆门市': '0724', '孝感市': '0712', '荆州市': '0716', '黄冈市': '0713',
    '咸宁市': '0715', '随州市': '0722', '恩施土家族苗族自治州': '0718', '长沙市': '0731', '株洲市': '0733',
    '湘潭市': '0732', '衡阳市': '0734', '邵阳市': '0739', '岳阳市': '0730', '常德市': '0736', '张家界市': '0744',
    '益阳市': '0737', '郴州市': '0735', '永州市': '0746', '怀化市': '0745', '娄底市': '0738',
    '湘西土家族苗族自治州': '0743',
    '广州市': '020', '韶关市': '0751', '深圳市': '0755', '珠海市': '0756', '汕头市': '0754', '佛山市': '0757',
    '江门市': '0750', '湛江市': '0759', '茂名市': '0668', '肇庆市': '0758', '惠州市': '0752', '梅州市': '0753',
    '汕尾市': '0660', '河源市': '0762', '阳江市': '0662', '清远市': '0763', '东莞市': '0769', '中山市': '0760',
    '潮州市': '0768', '揭阳市': '0663', '云浮市': '0766', '南宁市': '0771', '柳州市': '0772', '桂林市': '0773',
    '梧州市': '0774', '北海市': '0779', '防城港市': '0770', '钦州市': '0777', '贵港市': '0775', '玉林市': '0775',
    '百色市': '0776', '贺州市': '0774', '河池市': '0778', '来宾市': '0772', '崇左市': '0771', '海口市': '0898',
    '三亚市': '0899', '三沙市': '0898', '儋州市': '0898', '成都市': '028', '自贡市': '0813', '攀枝花市': '0812',
    '泸州市': '0830', '德阳市': '0838', '绵阳市': '0816', '广元市': '0839', '遂宁市': '0825', '内江市': '0832',
    '乐山市': '0833', '南充市': '0817', '眉山市': '028', '宜宾市': '0831', '广安市': '0826', '达州市': '0818',
    '雅安市': '0835', '巴中市': '0827', '资阳市': '028', '阿坝藏族羌族自治州': '0837', '甘孜藏族自治州': '0836',
    '凉山彝族自治州': '0834', '贵阳市': '0851', '六盘水市': '0858', '遵义市': '0852', '安顺市': '0853',
    '毕节市': '0857', '铜仁市': '0856', '黔西南布依族苗族自治州': '0859', '黔东南苗族侗族自治州': '0855',
    '黔南布依族苗族自治州': '0854', '昆明市': '0871', '曲靖市': '0874', '玉溪市': '0877', '保山市': '0875',
    '昭通市': '0870', '丽江市': '0888', '普洱市': '0879', '临沧市': '0883', '楚雄彝族自治州': '0878',
    '红河哈尼族彝族自治州': '0873', '文山壮族苗族自治州': '0876', '西双版纳傣族自治州': '0691',
    '大理白族自治州': '0872',
    '德宏傣族景颇族自治州': '0692', '怒江傈僳族自治州': '0886', '迪庆藏族自治州': '0887', '拉萨市': '0891',
    '日喀则市': '0892', '昌都市': '0895', '林芝市': '0894', '山南市': '0893', '那曲市': '0896', '阿里地区': '0897',
    '西安市': '029', '铜川市': '0919', '宝鸡市': '0917', '咸阳市': '029', '渭南市': '0913', '延安市': '0911',
    '汉中市': '0916', '榆林市': '0912', '安康市': '0915', '商洛市': '0914', '兰州市': '0931', '嘉峪关市': '0937',
    '金昌市': '0935', '白银市': '0943', '天水市': '0938', '武威市': '0935', '张掖市': '0936', '平凉市': '0933',
    '酒泉市': '0937', '庆阳市': '0934', '定西市': '0932', '陇南市': '0939', '临夏回族自治州': '0930',
    '甘南藏族自治州': '0941', '西宁市': '0971', '海东市': '0972', '海北藏族自治州': '0970', '黄南藏族自治州': '0973',
    '海南藏族自治州': '0974', '果洛藏族自治州': '0975', '玉树藏族自治州': '0976', '海西蒙古族藏族自治州': '0977',
    '银川市': '0951', '石嘴山市': '0952', '吴忠市': '0953', '固原市': '0954', '中卫市': '0955', '乌鲁木齐市': '0991',
    '克拉玛依市': '0990', '吐鲁番市': '0995', '哈密市': '0902', '昌吉回族自治州': '0994', '博尔塔拉蒙古自治州': '0909',
    '巴音郭楞蒙古自治州': '0996', '阿克苏地区': '0997', '克孜勒苏柯尔克孜自治州': '0908', '喀什地区': '0998',
    '和田地区': '0903', '伊犁哈萨克自治州': '0999', '塔城地区': '0901', '阿勒泰地区': '0906'
}
REGION_CODE_TO_CITY = {v: k for k, v in CITY_TO_REGION_CODE.items()}
REGION_CODE = set(CITY_TO_REGION_CODE.values())
# 定义手机号的正则表达式，允许空格或连字符作为分隔符
MOBILE_PATTERN = re.compile(r'(1[3-9]\d[\s-]?\d{4}[\s-]?\d{4})|(1[3-9]\d[\s-]?\d{3}[\s-]?\d{5})')
# 定义座机号的正则表达式，包括400开头的号码
LANDLINE_PATTERN = re.compile(r'((?<!\d)\b0\d{2,3}-?\d{7,8}\b)|((?<!\d)\b0\d{2,3}\d{7,8}\b)|'
                              r'((?<!\d)\b0\d{2,3} \d{7,8}\b)|'
                              r'((?<!\d)\b400[\s-]?\d{3,4}[\s-]?\d{3,4}\b)|'
                              r'((?<!\d)\b440[\s-]?\d{3,4}[\s-]?\d{3,4}\b)|'
                              r'((?<!\d)\b400[1-9][\s-]?\d{3,4}[\s-]?\d{3,4}\b)|'
                              r'(\(0\d{2,3}\)[\s-]?\d{3,4}[\s-]?\d{3,4})|'
                              r'((?<!\d)\b0\d{2,3}[\s-]?\d{3,4}[\s-]?\d{3,5}\b)|'
                              r'((?<!\d)\b95\d{3,8})|(\b96\d{3,8}\b)')
# 部分电话号码需要特殊处理
IGNORE_TEL = {'10096'}


# noinspection SpellCheckingInspection
def __fullwidth_to_halfwidth(s):
    """
    将全角字符串转换为半角字符串
    """
    result = []
    for char in s:
        # 全角空格为 12288，半角空格为 32
        # 其余全角字符从 65281 到 65374，减去 65248 就是对应的半角字符。
        code = ord(char)
        if code == 12288:  # 全角空格
            result.append(chr(32))
        elif 65281 <= code <= 65374:  # 其他全角字符
            result.append(chr(code - 65248))
        else:  # 非全角字符
            result.append(char)
    return ''.join(result)


def __clean_phone_str(phone_str):
    """
    清理电话号码字符串
    """
    return (
        __fullwidth_to_halfwidth(phone_str)
        .replace('\t', '')
        .replace('\n', '')
        .replace('\r', '')
        .replace("（", "(")
        .replace("）", ")")
        .replace("–", "-")
        .replace("--", "-")
        .replace("––", "-")
        .replace('——', '-')
        .replace("~", "-")
        .replace('－', '-')
        .replace('—', '-')
    )


def __parse_phone_str(third_name, phone_str):
    """
    解析电话号码字符串
    """
    if third_name in FORCE_TEL:
        return True, {FORCE_TEL[third_name]}

    if phone_str in FORCE_TEL.values():
        return True, {phone_str}

    numbers = set()
    for match in phonenumbers.PhoneNumberMatcher(phone_str, 'CN'):
        numbers.add(f'{match.raw_string}')

    if not numbers and third_name in DEFAULT_TEL:
        return True, {DEFAULT_TEL[third_name]}

    return any(numbers), numbers


def __clean_phone_numbers(numbers):
    """
    清理电话号码
    """
    cleaned_numbers = set()

    for number in numbers:
        cleaned_number = re.sub(r'\D', '', number)
        cleaned_number = cleaned_number.lstrip('+86').lstrip('86')
        cleaned_numbers.add(cleaned_number)

    return cleaned_numbers


def __regionalize_phone_numbers(numbers):
    """
    将电话号码区域化
    """
    valid_region_code_length = [3, 4]
    cleaned_numbers = set()

    for number in numbers:
        for region_code_length in valid_region_code_length:
            if number[:region_code_length] in REGION_CODE:
                cleaned_numbers.add(f'({number[:region_code_length]}){number[region_code_length:]}')
                break
        else:
            cleaned_numbers.add(number)

    return cleaned_numbers


def __extract_phone_numbers(third_name, phone_str):
    """
    提取电话号码
    """
    if phone_str is None or phone_str == '':
        return False, set()

    success, handled_phone_numbers = __parse_phone_str(third_name, phone_str)
    handled_phone_numbers = __clean_phone_numbers(handled_phone_numbers)
    handled_phone_numbers = __regionalize_phone_numbers(handled_phone_numbers)

    if not success:
        return False, set()

    return success, handled_phone_numbers


def __get_region_code(phone_number):
    """
    获取电话号码的区域代码
    """
    result = re.search(r'\((\d{3,4})\)', phone_number)
    return result.group(1) if result else None


def __check_phone_region(phone_numbers, desired_city):
    """
    检查区域代码是否和期望的城市匹配
    """
    for phone_number in phone_numbers:
        region_code = __get_region_code(phone_number)
        if region_code is None:
            continue

        phone_city = REGION_CODE_TO_CITY.get(region_code, None)
        if phone_city != desired_city:
            return False

    return True


def __filter_valid_phone_numbers(phone_numbers):
    """
    过滤出有效的电话号码
    """
    valid_phone_numbers = []

    for phone_number in phone_numbers:
        if (
            phone_number not in IGNORE_TEL and
            not MOBILE_PATTERN.match(phone_number) and
            not LANDLINE_PATTERN.match(phone_number)
        ):
            continue

        valid_phone_numbers.append(phone_number)

    return valid_phone_numbers


def __try_get_brand_of_poi(poi_name):
    """
    尝试获取 poi 的品牌
    """
    if poi_name is None or poi_name == '':
        return None

    return next((brand for brand in DEFAULT_TEL if brand in poi_name), None)


def __get_configured_phone(poi_name):
    """
    获取配置好的电话号码
    """
    brand = __try_get_brand_of_poi(poi_name)
    if brand is None:
        return False, []

    original_phone = __clean_phone_str(DEFAULT_TEL[brand])
    success, handled_phone_numbers = __extract_phone_numbers(brand, original_phone)

    if not success:
        return False, []

    return True, handled_phone_numbers


def __remove_duplicates(handled_phone, third_name, poi_name, poi_phone):
    """
    去重
    """
    if third_name in DEFAULT_TEL:
        brand = third_name
    else:
        poi_name_without_brackets = poi_name.split('(')[0]
        brand = __try_get_brand_of_poi(poi_name_without_brackets)

    should_force_update_brand = brand in REPAIR_BRAND
    desired_phones = REPAIR_BRAND.get(brand, {})
    should_force_update_phone = any(x in poi_phone for x in desired_phones)
    should_force_update = should_force_update_brand and should_force_update_phone

    if not should_force_update and poi_phone != '':
        return False, 'has phone'

    if handled_phone == poi_phone:
        return False, 'same phone'

    return True, ''


def format_phone(**kwargs):
    """
    格式化电话号码
    """
    third_name = kwargs.get('third_name', '')
    service_tel = kwargs.get('service_tel', '')
    poi_name = kwargs.get('poi_name', '')
    poi_city = kwargs.get('poi_city', '')
    poi_phone = kwargs.get('poi_phone', '')
    competitor_phone = kwargs.get('competitor_phone', '')
    check_region = kwargs.get('check_region', False)

    original_phone = __clean_phone_str(service_tel)
    success, handled_phone_numbers = __extract_phone_numbers(third_name, original_phone)
    extract_method = 'common'

    if not success:
        success, handled_phone_numbers = __get_configured_phone(poi_name)
        extract_method = 'default brand'

    if not success:
        success, handled_phone_numbers = __extract_phone_numbers('', competitor_phone)
        extract_method = 'competitor'

    if not success:
        return None, 'extract phone numbers failed'

    handled_phone_numbers = __filter_valid_phone_numbers(handled_phone_numbers)
    if not handled_phone_numbers:
        return None, 'no valid phone number'

    if check_region and not __check_phone_region(handled_phone_numbers, poi_city):
        return None, 'city mismatch'

    handled_phone = ','.join(handled_phone_numbers)
    success, reason = __remove_duplicates(handled_phone, third_name, poi_name, poi_phone)
    if not success:
        return None, reason

    return handled_phone, extract_method
