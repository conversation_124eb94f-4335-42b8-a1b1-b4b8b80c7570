# !/usr/bin/env python3
"""
图片地址格式化器
"""
import json


def format_picture(**kwargs):
    """
    格式化图片地址
    """
    picture_text = kwargs.get('picture_text', '')

    if picture_text == '[]' or picture_text == '{}' or picture_text == '':
        return [], 'no picture'

    try:
        picture_urls = list(set(
            x for x in json.loads(picture_text)
            if x is not None and x != '' and x.startswith('http')
        ))

        if not picture_urls:
            return [], 'no picture'

        return picture_urls, 'success'
    except:
        return [], 'json parse error'
