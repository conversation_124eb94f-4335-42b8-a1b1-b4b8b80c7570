# !/usr/bin/env python3
"""
包含一些充电桩相关的帮助方法
"""
import multiprocessing
import os
import random
import re
import subprocess
import uuid

import requests
from cachetools import TTLCache, cached
from retrying import retry
from urllib3.exceptions import NewConnectionError

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import (
    SPACING_GROUND,
    SPACING_UNDERGROUND,
    SPACING_STRUCTURE,
    SPACING_UNKNOWN,

    TAG_LEADING_USER,
    TAG_SMALL_AND_MEDIUM_USER,
    TAG_AGGREGATION_USER,
    TAG_AUTOMAKER_USER,
    TAG_PAYING_USER,
    TAG_CLOSED_USER,

    OWNERSHIP_PRIVATE,
    OWNERSHIP_PUBLIC,
    OWNERSHIP_UNKNOWN,
    OWNERSHIP_REASON_TP_CONTAINS_PRIVATE_KEYS,
    OWNERSHIP_REASON_TP_CONTAINS_PUBLIC_KEYS,
    OWNERSHIP_REASON_TP_IS_PRIVATE_TYPE,
    OWNERSHIP_REASON_TP_IS_PUBLIC_TYPE,

    STATUS_OFFLINE,
    STATUS_ONLINE,
    STATUS_UNKNOWN,
)
from src.tools import tsv, pgsql, notice_tool

BNS_HOST = "group.mappoi-PoiRichinfoOdp-envconf.map-poi.all"
MUTEX_NAMES = {
    '特来电': '特来电',
    'e充电': '国家电网',
    '星星': '星星',
    '南网电动': '南方电网',
    '依威能源': '依威能源',
    '中石化': '中国石化',
    '润诚达': '润诚达',
    '铁塔能源': '铁塔能源',
    '普天新能源': '普天新能源',
    '壳牌': '壳牌',
    '均悦充': '均悦充',
    '蔚来': '蔚来',
    '特斯拉': '特斯拉',
    '小鹏': '小鹏',
    '理想': '理想',
    '广汽能源': '广汽能源',
}
SELF_OWNED_NAMES = {
    '蔚来能源',
    '特斯拉',
    '理想',
    '小鹏汽车',
    '广汽能源',
    '中石化',
    '中国石化销售股份有限公司',
    '普天新能源',
    '中石油昆仑网电',
    'e充电',
    '南网电动',
    '开迈斯',
    '蒙马充电',
}
TRUSTY_THIRD_NAME_FORMATS = {
    'e充电': '国家电网充电站({})',
    '特来电': '特来电充电站({})',
    '小桔充电': '小桔充电充电站({})',
    '蔚来能源': '蔚来能源充电站({})',
    '小鹏汽车': '小鹏充电站({})',
    '特斯拉': '特斯拉充电站({})',
    '理想': '理想充电站({})',
    '开迈斯': '开迈斯充电站({})',
    '蒙马充电': '蒙马充电充电站({})',
    '南网电动': '南网电动充电站({})',
    '普天新能源': '普天新能源充电站({})',
    '广汽能源': '广汽能源充电站({})',
    'bp快充': 'bp快充充电站({})',
    '依威能源': '依威能源充电站({})',
}
TRUSTY_THIRD_NAMES = set(TRUSTY_THIRD_NAME_FORMATS.keys())


def __get_tp_users(tag):
    """
    获取 tp 用户信息
    """
    sql = '''
        select third_code, third_name from cdz_tp_brand where tag = %s;
    '''
    users = {}

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for third_code, third_name in stab.fetch_all(sql, (tag,)):
            users[third_code] = third_name

    return users


def get_tp_users():
    """
    获取 tp 用户信息
    """
    sql = '''
        select third_code, third_name from cdz_tp_brand where tag != 0;
    '''
    users = {}

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for third_code, third_name in stab.fetch_all(sql):
            users[third_code] = third_name

    return users


@cached(TTLCache(maxsize=1, ttl=3600))
def get_leading_users():
    """
    获取头部用户信息
    """
    return __get_tp_users(TAG_LEADING_USER)


@cached(TTLCache(maxsize=1, ttl=3600))
def get_small_and_medium_users():
    """
    获取中小用户信息
    """
    return __get_tp_users(TAG_SMALL_AND_MEDIUM_USER)


@cached(TTLCache(maxsize=1, ttl=3600))
def get_aggregation_users():
    """
    获取聚合用户信息
    """
    return __get_tp_users(TAG_AGGREGATION_USER)


@cached(TTLCache(maxsize=1, ttl=3600))
def get_automaker_users():
    """
    获取车厂用户信息
    """
    return __get_tp_users(TAG_AUTOMAKER_USER)


@cached(TTLCache(maxsize=1, ttl=3600))
def get_paying_users():
    """
    获取付费用户信息
    """
    return __get_tp_users(TAG_PAYING_USER)


@cached(TTLCache(maxsize=1, ttl=3600))
def get_closed_users():
    """
    获取闭环用户信息
    """
    return __get_tp_users(TAG_CLOSED_USER)


def get_hosts(bns_name: str = BNS_HOST):
    """
    获取 bns ip 池，作者：chenbaojun，代码没动过，直接抄的。
    """
    ret = subprocess.run(
        "get_instance_by_service -ips {}".format(bns_name) + " | awk '{print $2,$3,$4}'",
        shell=True,
        stdout=subprocess.PIPE
    )
    code = ret.returncode
    if code == 0:
        stdout = ret.stdout
        host_result = stdout.decode('UTF-8').split("\n")
        if len(host_result) < 1:
            return
        for i in host_result:
            item_list = i.split(" ")
            if len(item_list) < 3:
                continue
            if item_list[2] != '0':
                # 状态不是0，实例有问题
                continue

            ip, port = item_list[0], int(item_list[1])
            if port == 0:
                continue

            yield ip, port
    else:
        print("获取bns失败{}, 状态码{}, 返回{}".format(BNS_HOST, code, ret.stdout))


class BnsExecutor:
    """
    bns 执行器
    """

    def __init__(self, bns_name: str = BNS_HOST, auto_retry: bool = True):
        self.auto_retry = auto_retry
        self.bns_name = bns_name
        self.hosts = list(get_hosts(bns_name))
        self.lock = multiprocessing.Lock()

    def __execute_once(self, process, data):
        """
        单次执行
        """
        ip, port = random.choice(self.hosts)

        try:
            return process(ip, port, data)
        except NewConnectionError:
            self.__refresh_hosts()
            raise Exception("bns 池 ip 出现失效。")

    @retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
    def __execute_with_retrying(self, process, data):
        """
        带重试机制的执行
        """
        return self.__execute_once(process, data)

    def execute(self, process, data):
        """
        执行
        """
        return self.__execute_with_retrying(process, data) if self.auto_retry else self.__execute_once(process, data)

    def __refresh_hosts(self):
        """
        刷新 hosts
        """
        with self.lock:
            self.hosts = list(get_hosts(self.bns_name))


def get_nested_value(data, keys):
    """
    获取 json 嵌套值
    """
    for key in keys:
        try:
            if isinstance(data, dict):
                data = data.get(key, None)
            elif isinstance(data, list) and isinstance(key, int):
                data = data[key] if 0 <= key < len(data) else None
            else:
                return None

            if data is None:
                return None
        except:
            return None

    return data


def is_structure(text):
    """
    判断给定的文本是否是停车楼停车场
    """
    return re.search(r'(UG|停车楼|楼顶|立体停车)', text) is not None


def is_ground(text):
    """
    判断给定的文本是否是地上停车场
    """
    return re.search(r'(地面|地上|交叉|加油站|服务区|中石化|\w+侧约\d+米)', text) is not None


def is_underground(text):
    """
    判断给定的文本是否是地下停车场
    """
    return re.search(r'(LG|B[1-9]([楼层])|负([1-9]|[一二三四五六七八九])[楼层]|地下|地库)', text) is not None


def get_ownership_by_station_name(station_name):
    """
    根据 station_name 判断充电站开放属性
    """
    private_keys = {'内部使用', '内部专用', '(内部)', '不对外', '仅内', '私人'}
    public_keys = {'公共', '公用', '对外'}
    public_keys_not_include = {'公交', '公共交通', '公共汽车'}

    if any(key in station_name for key in private_keys):
        return OWNERSHIP_PRIVATE

    contains_public_keys = any(key in station_name for key in public_keys)
    contains_public_keys_not_include = any(key in station_name for key in public_keys_not_include)

    if contains_public_keys and not contains_public_keys_not_include:
        return OWNERSHIP_PUBLIC

    return OWNERSHIP_UNKNOWN


def get_ownership_by_station_type(station_type):
    """
    根据 station_name 判断充电站开放属性
    """
    private_types = {50, 100, 101, 102, 103}
    public_types = {1, 255}

    if station_type in private_types:
        return OWNERSHIP_PRIVATE

    if station_type in public_types:
        return OWNERSHIP_PUBLIC

    return OWNERSHIP_UNKNOWN


def get_ownership(station_name, station_type):
    """
    判断给定的充电站是否对外开放
    """
    ownership_by_station_name = get_ownership_by_station_name(station_name)
    ownership_by_station_type = get_ownership_by_station_type(station_type)

    if ownership_by_station_name == OWNERSHIP_PRIVATE:
        return OWNERSHIP_PRIVATE, OWNERSHIP_REASON_TP_CONTAINS_PRIVATE_KEYS

    if ownership_by_station_type == OWNERSHIP_PRIVATE:
        return OWNERSHIP_PRIVATE, OWNERSHIP_REASON_TP_IS_PRIVATE_TYPE

    if ownership_by_station_name == OWNERSHIP_PUBLIC:
        return OWNERSHIP_PUBLIC, OWNERSHIP_REASON_TP_CONTAINS_PUBLIC_KEYS

    if ownership_by_station_type == OWNERSHIP_PUBLIC:
        return OWNERSHIP_PUBLIC, OWNERSHIP_REASON_TP_IS_PUBLIC_TYPE

    return OWNERSHIP_UNKNOWN, ''


def get_status_by_station_name(station_name):
    """
    根据 station_name 判断充电站状态
    """
    offline_keys = {
        '暂停营业',
        '暂不营业',
        '已下线',
        '关闭',
    }

    return STATUS_OFFLINE if any(key in station_name for key in offline_keys) else STATUS_UNKNOWN


def get_status_by_station_status(station_status):
    """
    根据 station_status 判断充电站状态
    """
    offline_statuses = {1, 5, 6}
    online_statuses = {50}

    if station_status in offline_statuses:
        return STATUS_OFFLINE

    if station_status in online_statuses:
        return STATUS_ONLINE

    return STATUS_UNKNOWN


def get_status_by_pipe(piles):
    """
    根据桩的状态判断充电站状态
    """
    error_statuses = {0, 255}

    if not piles:
        return STATUS_UNKNOWN

    return STATUS_OFFLINE if all(x.status in error_statuses for x in piles) else STATUS_ONLINE


def get_desired_poi_name(third_name, station_name):
    """
    获取期望的 poi name
    """
    desired_format = TRUSTY_THIRD_NAME_FORMATS.get(third_name, f'{third_name}充电站({{}})')
    return desired_format.format(station_name)


def calc_spacing(text):
    """
    计算指定文本的空间属性
    """
    if is_structure(text):
        return SPACING_STRUCTURE
    elif is_underground(text):
        return SPACING_UNDERGROUND
    elif is_ground(text):
        return SPACING_GROUND
    else:
        return SPACING_UNKNOWN


def get_bids_by_ptids(ptids):
    """
    获取指定 ptids 对应的 bid 集合
    """
    sql = '''
        select bid from cdz_ticket where ptid = %s and bid != '' limit 1;
    '''
    bids = set()

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for ptid in ptids:
            row = poi_stab.fetch_one(sql, [ptid])
            if row is None:
                continue

            bids.add(row[0])

    return bids


def __get_work_changed_bids(sql):
    """
    获取人工变更过的 bid 集合
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        ptids = {x[0] for x in poi_stab.fetch_all(sql)}

    return get_bids_by_ptids(ptids)


def get_work_changed_spacing_bids():
    """
    获取空间属性人工变更过的 bid 集合
    """
    sql = '''
        select ptid 
        from cdz_work_changed 
        where spacing = 1 and
              finished_time > now() - interval '180 days';
    '''

    return __get_work_changed_bids(sql)


def get_work_changed_status_bids():
    """
    获取状态人工变更过的 bid 集合
    """
    sql = '''
        select ptid 
        from cdz_work_changed 
        where status = 1 and
              finished_time > now() - interval '90 days';
    '''

    return __get_work_changed_bids(sql)


def get_work_changed_ownership_bids():
    """
    获取开放属性人工变更过的 bid 集合
    """
    sql = '''
        select ptid 
        from cdz_work_changed 
        where ownership = 1 and
              finished_time > now() - interval '180 days' and
              finished_time >= '2025-06-15';
    '''

    return __get_work_changed_bids(sql)


def get_work_changed_location_bids():
    """
    获取位置人工变更过的 bid 集合
    """
    sql = '''
        select ptid 
        from cdz_work_changed 
        where location = 1 and
              finished_time > now() - interval '180 days';
    '''

    return __get_work_changed_bids(sql)


def get_work_changed_prop_bids():
    """
    获取全属性人工变更过的 bid 集合
    """
    sql = '''
        select bid 
        from cdz_ticket 
        where src = 'update_prop' and 
              mode = 'formal' and
              (
                  (project = 'CDX' and work_conclusion in (3, 7)) or
                  (project = 'CHZ' and work_conclusion in (3, 7))
              ) and
              push_time > now() - interval '180 days';
    '''

    return __get_work_changed_bids(sql)


def get_manual_bids(src):
    """
    获取人工作业过的 bid 集合
    """
    if src == 'update_location':
        return get_work_changed_location_bids()
    elif src == 'update_status':
        return get_work_changed_status_bids()
    elif src == 'update_ownership':
        return get_work_changed_ownership_bids()
    elif src == 'update_spacing':
        return get_work_changed_spacing_bids()
    elif src == 'update_prop':
        return get_work_changed_prop_bids()
    else:
        return set()


def get_manual_spacing_bids():
    """
    获取人工作业过空间属性的 bid 集合
    """
    return get_manual_bids('update_spacing')


def get_manual_status_bids():
    """
    获取人工作业过状态的 bid 集合
    """
    return get_manual_bids('update_status')


def get_manual_ownership_bids():
    """
    获取人工作业过开放属性的 bid 集合
    """
    return get_manual_bids('update_ownership')


def get_manual_location_bids():
    """
    获取人工作业过位置的 bid 集合
    """
    return get_manual_bids('update_location')


def get_all_white_list_bids():
    """
    获取白名单 bid 集合
    """
    sql = '''
        select bid from cdz_white_list;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        return {x[0] for x in poi_stab.fetch_all(sql)}


def exists_spacing_ticket(stab, bid):
    """
    判断指定 bid 是否存在空间属性工单
    """
    sql = '''
        select 1 
        from cdz_ticket 
        where bid = %s and
              (src = 'update_spacing' or src = 'update_prop') and 
              mode = 'formal' and
              is_finished != -1 and
              push_time > now() - interval '180 days'
        limit 1;
    '''

    return stab.fetch_one(sql, [bid]) is not None


def exists_status_ticket(stab, bid):
    """
    判断指定 bid 是否存在状态工单
    """
    sql = '''
        select 1 
        from cdz_ticket 
        where bid = %s and
              (src = 'update_status' or src = 'update_prop') and 
              mode = 'formal' and
              is_finished != -1 and
              push_time > now() - interval '90 days';
    '''

    return stab.fetch_one(sql, [bid]) is not None


def exists_ownership_ticket(stab, bid):
    """
    判断指定 bid 是否存在开放属性工单
    """
    sql = '''
        select 1 
        from cdz_ticket 
        where bid = %s and
              (src = 'update_ownership' or src = 'update_prop') and 
              mode = 'formal' and
              is_finished != -1 and
              push_time > now() - interval '180 days' and
              push_time >= '2025-06-15'
        limit 1;
    '''

    return stab.fetch_one(sql, [bid]) is not None


def exists_location_ticket(stab, bid):
    """
    判断指定 bid 是否存在位置工单
    """
    sql = '''
        select 1 
        from cdz_ticket 
        where bid = %s and
              (src = 'update_location' or src = 'update_prop') and 
              mode = 'formal' and
              is_finished != -1 and
              push_time > now() - interval '180 days'
        limit 1;
    '''

    return stab.fetch_one(sql, [bid]) is not None


def exists_prop_ticket(stab, bid):
    """
    判断指定 bid 是否存在全属性工单
    """
    sql = '''
        select 1 
        from cdz_ticket 
        where bid = %s and
              src = 'update_prop' and 
              mode = 'formal' and
              is_finished != -1 and
              push_time > now() - interval '180 days'
        limit 1;
    '''

    return stab.fetch_one(sql, [bid]) is not None


def exists_ticket(stab, src, bid):
    """
    判断指定 bid 是否存在指定 src 的工单
    """
    if src == 'update_location':
        return exists_location_ticket(stab, bid)
    elif src == 'update_status':
        return exists_status_ticket(stab, bid)
    elif src == 'update_ownership':
        return exists_ownership_ticket(stab, bid)
    elif src == 'update_spacing':
        return exists_spacing_ticket(stab, bid)
    elif src == 'update_prop':
        return exists_prop_ticket(stab, bid)
    else:
        return False


def get_white_list_bids(src):
    """
    获取指定 src 的白名单 bid 集合
    """
    sql = '''
        select bid from cdz_white_list where src = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        return {x[0] for x in poi_stab.fetch_all(sql, [src])}


def get_white_list_spacing_bids():
    """
    获取白名单空间属性的 bid 集合
    """
    return get_white_list_bids('update_spacing')


def get_white_list_status_bids():
    """
    获取白名单状态的 bid 集合
    """
    return get_white_list_bids('update_status')


def get_white_list_ownership_bids():
    """
    获取白名单开放属性的 bid 集合
    """
    return get_white_list_bids('update_ownership')


def get_white_list_location_bids():
    """
    获取白名单位置的 bid 集合
    """
    return get_white_list_bids('update_location')


def get_ugc_intervene_bids(sql):
    """
    获取 UGC 干预的 bid 集合
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        return {x[0] for x in poi_stab.fetch_all(sql)}


def get_ugc_intervene_relation_bids():
    """
    获取 UGC 干预父子关系的 bid 集合
    """
    return get_ugc_intervene_bids('''
        select distinct bid 
        from cdz_intervention 
        where field_name = 'relation_bid' and
              create_time >= NOW() - INTERVAL '6 months';
    ''')


def get_ugc_intervene_phone_bids():
    """
    获取 UGC 干预电话的 bid 集合
    """
    return get_ugc_intervene_bids('''
        select distinct bid 
        from cdz_intervention 
        where field_name = 'phone' and
              create_time >= NOW() - INTERVAL '6 months';
    ''')


def get_ugc_intervene_spacing_bids():
    """
    获取 UGC 干预空间属性的 bid 集合
    """
    return get_ugc_intervene_bids('''
        select distinct bid 
        from cdz_intervention 
        where field_name = 'classify_tag' and
              create_time >= NOW() - INTERVAL '6 months';
    ''')


def get_ugc_intervene_ownership_bids():
    """
    获取 UGC 干预开放属性的 bid 集合
    """
    return get_ugc_intervene_bids('''
        select distinct bid 
        from cdz_mohushangxiaxian
        where create_time >= NOW() - INTERVAL '6 months';
    ''')


def get_ugc_intervene_status_bids():
    """
    获取 UGC 干预状态的 bid 集合
    """
    return get_ugc_intervene_bids('''
        select distinct bid 
        from cdz_intervention 
        where field_name = 'status' and
              create_time >= NOW() - INTERVAL '6 months';
    ''')


def get_ugc_intervene_location_bids():
    """
    获取 UGC 干预位置的 bid 集合
    """
    return get_ugc_intervene_bids('''
        select distinct bid 
        from cdz_intervention 
        where field_name in ('pointx', 'pointy') and
              create_time >= NOW() - INTERVAL '6 months';
    ''')


def copy_data_to_db(conn, data: list, table_name, columns):
    """
    将数据拷贝到数据库
    """
    if not data:
        return

    temp_file_name = f'temp_{uuid.uuid4().hex}'
    tsv.write_tsv(temp_file_name, data)

    with (
        conn.cursor() as cur,
        open(temp_file_name, 'r', encoding='utf-8') as f,
    ):
        try:
            cur.copy_from(f, table=table_name, columns=columns)
        except Exception as e:
            print(e)
            conn.rollback()
        finally:
            os.remove(temp_file_name)


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def get_city(x, y):
    """
    获取指定坐标所属城市
    """
    api = 'http://api.map.sdns.baidu.com/reverse_geocoding/v3'
    # noinspection SpellCheckingInspection
    params = {
        'ak': 'YC4iEtl2kLGLGNRZVsLnCqXkNFBEpP3W',
        'extensions_poi': '0',
        'coordtype': 'gcj02ll',
        'output': 'json',
        'location': f'{y}, {x}',
    }
    response = requests.get(api, params=params)
    address = response.json()['result']['addressComponent']
    city = address['city']
    district = address['district']

    return city or district


def alert_to_infoflow(msg):
    """
    如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        msg,
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )
