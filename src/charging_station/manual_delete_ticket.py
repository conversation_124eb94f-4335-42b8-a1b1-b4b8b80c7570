# !/usr/bin/env python3
"""
手动删除人工作业清单
"""
import argparse
import time
from dataclasses import dataclass, field
from pathlib import Path

import requests
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import Ticket
from src.tools import pipeline, tsv, pgsql

PLATFORMS = ['test', 'practice', 'formal']
SRCS = ['create', 'update_spacing', 'update_status', 'update_ownership', 'update_location', 'update_prop']
EMAIL = '<EMAIL>'
API_MAP = {
    'test': 'http://xiangmeng02-dev01.bcc-gzhxy.baidu.com:8905/api/v1/workflow/process/delete',
    'practice': 'http://gzdt-map-poi-plat03.gzdt.baidu.com:8906/api/v1/workflow/process/delete',
    'formal': 'http://*************:8905/api/v1/workflow/process/delete',
}

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    platform: str
    ptid_path: str
    bid_path: str
    api: str
    src: str
    batch_id: str
    memo: str
    bids: set[str] = field(default_factory=set)
    tickets: list[Ticket] = field(default_factory=list)
    deleted_ptids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载 bid 集合
    """
    if ctx.bid_path is not None:
        ctx.bids = {x[0] for x in tsv.read_tsv(ctx.bid_path)}

    proceed()


@desc()
def load_tickets(ctx: Context, proceed):
    """
    加载工单列表
    """
    sql = '''
        select bid, ptid, batch, src, project, priority, method, message, mode
        from cdz_ticket
        where ptid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for ptid in tqdm({x[0] for x in tsv.read_tsv(ctx.ptid_path)}):
            row = stab.fetch_one(sql, (ptid,))
            if row is None:
                continue

            bid, ptid, batch, src, project, priority, method, message, mode = row
            if ctx.bids and bid not in ctx.bids:
                continue

            ctx.tickets.append(Ticket(
                bid=bid,
                ptid=ptid,
                batch_id=batch,
                batch_name='',
                src=src,
                project=project,
                priority=priority,
                method=method,
                message=message,
                mode=mode,
            ))

    proceed()


@desc()
def delete_tickets(ctx: Context, proceed):
    """
    删除工单
    """
    for ticket in tqdm(ctx.tickets):
        try:
            requests.get(url=ctx.api, params={'ptid': ticket.ptid, 'user': EMAIL})
            ctx.deleted_ptids.add(ticket.ptid)
            time.sleep(1)
        except Exception as e:
            ticket.can_process = False
            ticket.reason = str(e)
            print(e)

    proceed()


@desc()
def update_status(ctx: Context, proceed):
    """
    更新工单状态
    """
    sql = '''
        update cdz_ticket 
        set is_finished = -1, memo = %s
        where ptid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab:
        poi_stab.connection.autocommit = False
        with poi_stab.connection.cursor() as cursor:
            try:
                for ticket in tqdm([x for x in ctx.tickets if x.can_process and x.ptid in ctx.deleted_ptids]):
                    cursor.execute(sql, (ctx.memo, ticket.ptid))

                poi_stab.connection.commit()
            except Exception as e:
                poi_stab.connection.rollback()
                print(e)

    proceed()


@desc()
def save_tickets(ctx: Context, proceed):
    """
    保存工单列表
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.ptid,
                x.batch_id,
                x.bid,
                x.project,
                x.priority,
                x.method,
                x.message,
                x.src,
                x.creator,
                f'{ctx.api}?ptid={x.ptid}&user={EMAIL}',
                x.can_process,
                x.reason,
            ]
            for x in ctx.tickets
        ]
    )
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--platform',
        dest='platform',
        type=str,
        choices=PLATFORMS,
        default='test',
        required=True,
    )
    parser.add_argument(
        '--bid-path',
        dest='bid_path',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--ptid-path',
        dest='ptid_path',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--src',
        dest='src',
        type=str,
        choices=SRCS,
        required=False,
    )
    parser.add_argument(
        '--memo',
        dest='memo',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--batch-id',
        dest='batch_id',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--execute',
        dest='execute',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_bids,
        load_tickets,
    ]

    if args.execute:
        pipes.extend([
            delete_tickets,
            update_status,
        ])

    pipes.append(save_tickets)

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/manual_delete_ticket"),
        platform=args.platform,
        ptid_path=args.ptid_path,
        bid_path=args.bid_path,
        api=API_MAP[args.platform],
        src=args.src,
        batch_id=args.batch_id,
        memo=args.memo,
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
