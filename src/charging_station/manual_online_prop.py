# !/usr/bin/env python3
"""
充电站属性手动推送上线
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import Poi
from src.charging_station.helper import get_manual_bids, get_white_list_bids
from src.charging_station.online_prop import (
    online_ownership,
    online_relation,
    online_gcj_location,
    online_spacing,
    online_telephone,
    online_status,
    online_tag,
)
from src.tools import pipeline, tsv, pgsql

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    data_path: str
    mode: str
    force: bool
    source: str
    ignored_bids: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def check_bid(ctx: Context, proceed):
    """
    检查 bid 是否是充电站
    """
    cdz_tag = '交通设施;充电站'
    sql = '''
        select std_tag from poi where bid = %s;
    '''
    src_map = {
        'spacing': 'update_spacing',
        'private': 'update_ownership',
        'public': 'update_ownership',
        'location': 'update_location',
    }

    if ctx.force:
        proceed()
        return

    src = src_map.get(ctx.mode, None)
    ctx.ignored_bids.update(get_manual_bids(src) if src is not None else set())
    ctx.ignored_bids.update(get_white_list_bids(src) if src is not None else set())

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for data in tqdm(list(tsv.read_tsv(ctx.data_path))):
            bid = data[0]
            row = poi_stab.fetch_one(sql, [bid])
            if row is None:
                ctx.ignored_bids.add(bid)
                continue

            tag, = row
            if tag != cdz_tag:
                ctx.ignored_bids.add(bid)

    proceed()


@desc()
def __online_relation(ctx: Context, proceed):
    """
    推送父子关系
    """
    online_relation([
        Poi(bid=bid, relation_bid=relation_bid)
        for bid, relation_bid in tsv.read_tsv(ctx.data_path) if bid not in ctx.ignored_bids
    ], source='zt_life.cdz_rengong' if ctx.source is None else ctx.source)
    proceed()


@desc()
def __online_telephone(ctx: Context, proceed):
    """
    推送联系方式
    """
    online_telephone([
        Poi(bid=bid, phone=phone)
        for bid, phone in tsv.read_tsv(ctx.data_path)
    ], source='phone.chongdianzhanjieru' if ctx.source is None else ctx.source)

    proceed()


@desc()
def __online_spacing(ctx: Context, proceed):
    """
    推送空间属性
    """
    online_spacing([
        Poi(bid=bid, show_tag=show_tag)
        for bid, show_tag in tsv.read_tsv(ctx.data_path) if bid not in ctx.ignored_bids
    ], source='zt_life.cdz_rengong' if ctx.source is None else ctx.source, force=ctx.force)

    proceed()


@desc()
def __online_tag(ctx: Context, proceed):
    """
    推送 tag
    """
    online_tag([
        Poi(bid=bid, tag=std_tag, show_tag=show_tag)
        for bid, std_tag, show_tag in tsv.read_tsv(ctx.data_path) if bid not in ctx.ignored_bids
    ], source='zt_life.cdz_rengong' if ctx.source is None else ctx.source)

    proceed()


@desc()
def __online_private_ownership(ctx: Context, proceed):
    """
    推送对内开放属性
    """
    online_ownership([
        Poi(bid=bid, ownership='不对外开放')
        for bid, in tsv.read_tsv(ctx.data_path) if bid not in ctx.ignored_bids
    ], source='chuilei_scope.rg' if ctx.source is None else ctx.source)
    proceed()


@desc()
def __online_public_ownership(ctx: Context, proceed):
    """
    推送对外开放属性
    """
    online_ownership([
        Poi(bid=bid)
        for bid, in tsv.read_tsv(ctx.data_path) if bid not in ctx.ignored_bids
    ], source='chuilei_scope.rg' if ctx.source is None else ctx.source)
    proceed()


@desc()
def __online_location(ctx: Context, proceed):
    """
    推送坐标
    """
    online_gcj_location([
        Poi(bid=bid, geom=shapely.wkt.loads(wkt))
        for bid, wkt in tsv.read_tsv(ctx.data_path) if bid not in ctx.ignored_bids
    ], source='chuilei_scope.rg' if ctx.source is None else ctx.source)
    proceed()


def __online_status(ctx: Context, desired_status):
    """
    推送状态
    """
    online_status([
        Poi(bid=bid, status=desired_status)
        for bid, in tsv.read_tsv(ctx.data_path) if bid not in ctx.ignored_bids
    ], source='chuilei_scope.rg' if ctx.source is None else ctx.source)


@desc()
def __online_common_status(ctx: Context, proceed):
    """
    推送正常状态
    """
    __online_status(ctx, 1)
    proceed()


@desc()
def __online_closed_status(ctx: Context, proceed):
    """
    推送暂停营业状态
    """
    __online_status(ctx, 3)
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['relation', 'spacing', 'tag', 'private', 'public', 'location', 'phone', 'common', 'closed'],
        required=True,
    )
    parser.add_argument(
        '--data-path',
        dest='data_path',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--source',
        dest='source',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--force',
        dest='force',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def create_pipeline(mode):
    """
    创建脚本执行管道
    """
    pipes = [check_bid]

    if mode == 'relation':
        pipes.append(__online_relation)
    elif mode == 'spacing':
        pipes.append(__online_spacing)
    elif mode == 'tag':
        pipes.append(__online_tag)
    elif mode == 'private':
        pipes.append(__online_private_ownership)
    elif mode == 'public':
        pipes.append(__online_public_ownership)
    elif mode == 'location':
        pipes.append(__online_location)
    if mode == 'phone':
        pipes.append(__online_telephone)
    elif mode == 'common':
        pipes.append(__online_common_status)
    elif mode == 'closed':
        pipes.append(__online_closed_status)

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args.mode)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/manual_online_prop'),
        data_path=args.data_path,
        mode=args.mode,
        force=args.force,
        source=args.source,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
