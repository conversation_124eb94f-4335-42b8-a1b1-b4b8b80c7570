# !/usr/bin/env python3
"""
手动推送人工作业清单
"""
import argparse
import random
import time
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

import mapio.utils.coord
import requests
import shapely.wkt
from shapely import Point
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station import auto_repair_mixin
from src.charging_station.auto_repair import extract_push_result_from_response, API_MAP
from src.charging_station.data import TicketConfig, Ticket, TicketBatch, Poi, CreatePoiTicket
from src.charging_station.helper import get_desired_poi_name, get_city, exists_ticket
from src.tools import pipeline, tsv, pgsql

PLATFORMS = ['test', 'practice', 'formal']
MODES = ['practice', 'exam', 'common']
METHODS = ['edit', 'new']
SRCS = [
    'create',
    'update_name',
    'update_spacing',
    'update_status',
    'update_ownership',
    'update_location',
    'update_prop',
]
EMAIL = '<EMAIL>'

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    推送记录
    """
    poi: Poi
    work_message: str
    bid: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    api: str
    data_path: str
    cache_path: str
    bid_path: str
    work_messages: str
    src: str
    platform: str
    mode: str
    method: str
    batch_id: str
    batch_count: int
    ticket_count: int
    limit: int
    push_batch_size: int
    force: bool
    cities: set[str] = field(default_factory=set)
    undesired_cities: set[Poi] = field(default_factory=set)
    configs: list[TicketConfig] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    tickets: list[Ticket] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fill_practice_create_configs(ctx: Context, proceed):
    """
    填充练习新增配置
    """
    batch_prefix = f'CDCPXZ{datetime.now().strftime("%Y%m%d")}'

    for index in range(1, ctx.batch_count + 1):
        batch_id = f'{batch_prefix}{index:03}'
        config = TicketConfig(
            project=batch_id[:3],
            priority=5,
            batch_id=batch_id,
            method='new',
            src='create',
            batch_name='练习新增',
            platform='test',
            mode='practice',
        )
        ctx.configs.append(config)

    proceed()


@desc()
def fill_practice_update_status_configs(ctx: Context, proceed):
    """
    填充练习更新状态配置
    """
    batch_prefix = f'CDXPZT{datetime.now().strftime("%Y%m%d")}'

    for index in range(1, ctx.batch_count + 1):
        batch_id = f'{batch_prefix}{index:03}'
        config = TicketConfig(
            project=batch_id[:3],
            priority=4,
            batch_id=batch_id,
            method='edit',
            src='update_status',
            batch_name='练习更新状态',
            platform='test',
            mode='practice',
        )
        ctx.configs.append(config)

    proceed()


@desc()
def fill_practice_update_location_configs(ctx: Context, proceed):
    """
    填充练习更新坐标配置
    """
    batch_prefix = f'CDXPZB{datetime.now().strftime("%Y%m%d")}'

    for index in range(1, ctx.batch_count + 1):
        batch_id = f'{batch_prefix}{index:03}'
        config = TicketConfig(
            project=batch_id[:3],
            priority=4,
            batch_id=batch_id,
            method='edit',
            src='update_location',
            batch_name='练习更新坐标',
            platform='test',
            mode='practice',
        )
        ctx.configs.append(config)

    proceed()


@desc()
def fill_practice_update_spacing_configs(ctx: Context, proceed):
    """
    填充练习更新空间属性配置
    """
    batch_prefix = f'CDXPKJ{datetime.now().strftime("%Y%m%d")}'

    for index in range(1, ctx.batch_count + 1):
        batch_id = f'{batch_prefix}{index:03}'
        config = TicketConfig(
            project=batch_id[:3],
            priority=4,
            batch_id=batch_id,
            method='edit',
            src='update_spacing',
            batch_name='练习更新空间属性',
            platform='test',
            mode='practice',
        )
        ctx.configs.append(config)

    proceed()


@desc()
def fill_examine_create_configs(ctx: Context, proceed):
    """
    填充考核新增配置
    """
    batch_prefix = f'CDCKXZ{datetime.now().strftime("%Y%m%d")}'

    for index in range(1, ctx.batch_count + 1):
        batch_id = f'{batch_prefix}{index:03}'
        config = TicketConfig(
            project=batch_id[:3],
            priority=5,
            batch_id=batch_id,
            method='new',
            src='create',
            batch_name='考核新增',
            platform='test',
            mode='exam',
        )
        ctx.configs.append(config)

    proceed()


@desc()
def fill_examine_update_status_configs(ctx: Context, proceed):
    """
    填充考核更新状态配置
    """
    batch_prefix = f'CDXKZT{datetime.now().strftime("%Y%m%d")}'

    for index in range(1, ctx.batch_count + 1):
        batch_id = f'{batch_prefix}{index:03}'
        config = TicketConfig(
            project=batch_id[:3],
            priority=4,
            batch_id=batch_id,
            method='edit',
            src='update_status',
            batch_name='考核更新状态',
            platform='test',
            mode='exam',
        )
        ctx.configs.append(config)

    proceed()


@desc()
def fill_examine_update_location_configs(ctx: Context, proceed):
    """
    填充考核更新坐标配置
    """
    batch_prefix = f'CDXKZB{datetime.now().strftime("%Y%m%d")}'

    for index in range(1, ctx.batch_count + 1):
        batch_id = f'{batch_prefix}{index:03}'
        config = TicketConfig(
            project=batch_id[:3],
            priority=4,
            batch_id=batch_id,
            method='edit',
            src='update_location',
            batch_name='考核更新坐标',
            platform='test',
            mode='exam',
        )
        ctx.configs.append(config)

    proceed()


@desc()
def fill_examine_update_spacing_configs(ctx: Context, proceed):
    """
    填充考核更新空间属性配置
    """
    batch_prefix = f'CDXKKJ{datetime.now().strftime("%Y%m%d")}'

    for index in range(1, ctx.batch_count + 1):
        batch_id = f'{batch_prefix}{index:03}'
        config = TicketConfig(
            project=batch_id[:3],
            priority=4,
            batch_id=batch_id,
            method='edit',
            src='update_spacing',
            batch_name='考核更新空间属性',
            platform='test',
            mode='exam',
        )
        ctx.configs.append(config)

    proceed()


@desc()
def fill_formal_update_spacing_configs(ctx: Context, proceed):
    """
    填充正式更新空间属性配置
    """
    batch_id = 'CDXCDZ20241023002'

    ctx.configs.append(TicketConfig(
        project=batch_id[:3],
        priority=4,
        batch_id=batch_id,
        method='edit',
        src='update_spacing',
        batch_name='正式更新空间属性',
        platform='formal',
        mode='common',
    ))
    proceed()


@desc()
def fill_formal_update_status_configs(ctx: Context, proceed):
    """
    填充正式更新状态配置
    """
    batch_id = 'CDXCDZ20241023003'

    ctx.configs.append(TicketConfig(
        project=batch_id[:3],
        priority=4,
        batch_id=batch_id,
        method='edit',
        src='update_status',
        batch_name='正式更新状态',
        platform='formal',
        mode='common',
    ))
    proceed()


@desc()
def fill_formal_update_ownership_configs(ctx: Context, proceed):
    """
    填充正式更新开放属性配置
    """
    batch_id = 'CDXCDZ20241023004'

    ctx.configs.append(TicketConfig(
        project=batch_id[:3],
        priority=4,
        batch_id=batch_id,
        method='edit',
        src='update_ownership',
        batch_name='正式更新开放属性',
        platform='formal',
        mode='common',
    ))
    proceed()


@desc()
def fill_formal_update_location_configs(ctx: Context, proceed):
    """
    填充正式更新坐标配置
    """
    batch_id = 'CDXCDZ20241023005'

    ctx.configs.append(TicketConfig(
        project=batch_id[:3],
        priority=4,
        batch_id=batch_id,
        method='edit',
        src='update_location',
        batch_name='正式更新坐标',
        platform='formal',
        mode='common',
    ))
    proceed()


@desc()
def fill_formal_update_prop_configs(ctx: Context, proceed):
    """
    填充正式更新所有属性配置
    """
    batch_id = 'CDXCDZ20241023006'

    ctx.configs.append(TicketConfig(
        project=batch_id[:3],
        priority=4,
        batch_id=batch_id,
        method='edit',
        src='update_prop',
        batch_name='正式更新所有属性',
        platform='formal',
        mode='common',
    ))
    proceed()


@desc()
def filter_configs(ctx: Context, proceed):
    """
    过滤配置
    """
    ctx.configs = [
        config
        for config in ctx.configs
        if (
            config.platform == ctx.platform and
            config.mode == ctx.mode and
            config.method == ctx.method and
            config.src == ctx.src
        )
    ]
    if not ctx.configs:
        raise ValueError('config is not found')

    proceed()


@desc()
def create_config(ctx: Context, proceed):
    """
    创建配置
    """
    ctx.configs = [
        TicketConfig(
            project=ctx.batch_id[:3],
            priority=4,
            batch_id=ctx.batch_id,
            method=ctx.method,
            src=ctx.src,
            batch_name='手动下发批次' if ctx.work_messages is None else ctx.work_messages,
            platform=ctx.platform,
            mode=ctx.mode,
        )
    ]
    proceed()


def load_create_records_by_cache(ctx: Context):
    """
    加载新增记录（通过 auto_release 的缓存文件）
    """
    for data in tsv.read_tsv(ctx.cache_path, skip_header=True, splitter=','):
        (
            row_id, uid, bid, thirdcode, thirdname, station_id, operator_id, equipment_owner_id, station_name, address,
            station_tel, service_tel, station_type, station_status, park_nums, station_lng, station_lat, site_guide,
            construction, park_info, busine_hours, electricity_fee, service_fee, market_electricity_fee,
            market_service_fee, parking_tag, bdmap_charging, policy_infos, extra, online_ctime, online_mtime,
            online_status, online_info, create_time, update_time, is_deleted, city, auto_release, msg, show_tag
        ) = data
        # 过滤掉自动发布的记录
        if int(auto_release) == 1:
            continue

        bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(float(station_lng), float(station_lat))
        mc_x, mc_y = mapio.utils.coord.bd09_to_mercator(bd_x, bd_y)
        ctx.records.append(Record(
            work_message='常规工艺',
            poi=Poi(
                name=get_desired_poi_name(thirdname, station_name),
                address=address,
                mc_wkt=f'POINT({mc_x} {mc_y})',
                phone='',
                status=1,
                tag='',
            ),
        ))


def create_update_records_by_cache(src, count):
    """
    创建更新记录
    """
    get_ticket_sql = '''
        select bid, message 
        from cdz_ticket 
        where src = %s and 
              mode = 'formal' and
              is_finished = 0 
        order by random()
        limit %s;
    '''
    get_poi_sql = '''
        select name, alias, address, st_astext(geometry), telephone, status, std_tag 
        from poi 
        where bid = %s;
    '''
    take_count = count * 2  # 有些 poi 会失效，故取两倍以保证取到足够的记录。

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for bid, message in stab.fetch_all(get_ticket_sql, [src, take_count]):
            row = stab.fetch_one(get_poi_sql, [bid])
            if row is None:
                continue

            name, alias, address, wkt, telephone, status, std_tag = row
            geom: Point = shapely.wkt.loads(wkt)
            bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(geom.x, geom.y)
            mc_x, mc_y = mapio.utils.coord.bd09_to_mercator(bd_x, bd_y)

            yield Record(
                bid=bid,
                work_message=message,
                poi=Poi(
                    name=name,
                    alias=alias,
                    address=address,
                    wkt=wkt,
                    mc_wkt=f'POINT({mc_x} {mc_y})',
                    phone=telephone,
                    status=int(status),
                    tag=std_tag,
                ),
            )


def load_records_by_cache(ctx: Context):
    """
    加载推送记录（通过其它脚本的缓存文件）
    """
    if ctx.src == 'create':
        load_create_records_by_cache(ctx)
    else:
        ctx.records.extend(create_update_records_by_cache(ctx.src, ctx.ticket_count))


def load_records_by_bid(ctx: Context):
    """
    加载推送记录（通过 bid 文件）
    """
    sql = '''
        select name, alias, address, telephone, status, std_tag, st_astext(geometry)
        from poi
        where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid in tqdm({bid for bid, in tsv.read_tsv(ctx.bid_path)}):
            row = poi_stab.fetch_one(sql, [bid])
            if row is None:
                print(bid)
                continue

            name, alias, address, phone, status, tag, wkt = row
            geom: Point = shapely.wkt.loads(wkt)
            bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(geom.x, geom.y)
            mc_x, mc_y = mapio.utils.coord.bd09_to_mercator(bd_x, bd_y)
            ctx.records.append(Record(
                bid=bid,
                work_message=ctx.work_messages,
                poi=Poi(
                    name=name,
                    alias=alias,
                    address=address,
                    wkt=wkt,
                    mc_wkt=f'POINT({mc_x} {mc_y})',
                    phone=phone,
                    status=status,
                    tag=tag,
                ),
            ))

            if 0 < ctx.limit <= len(ctx.records):
                break


def load_records_by_data(ctx: Context):
    """
    加载推送记录（通过数据文件）
    """
    sql = '''
        select name, alias, address, telephone, status, std_tag
        from poi
        where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for item in tqdm([x for x in tsv.read_tsv(ctx.data_path)]):
            bid, wkt = item

            row = poi_stab.fetch_one(sql, [bid])
            if row is None:
                print(bid)
                continue

            name, alias, address, phone, status, tag = row
            geom: Point = shapely.wkt.loads(wkt)
            bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(geom.x, geom.y)
            mc_x, mc_y = mapio.utils.coord.bd09_to_mercator(bd_x, bd_y)
            ctx.records.append(Record(
                bid=bid,
                work_message=ctx.work_messages,
                poi=Poi(
                    name=name,
                    alias=alias,
                    address=address,
                    wkt=wkt,
                    mc_wkt=f'POINT({mc_x} {mc_y})',
                    phone=phone,
                    status=status,
                    tag=tag,
                ),
            ))

            if 0 < ctx.limit <= len(ctx.records):
                break


@desc()
def load_records(ctx: Context, proceed):
    """
    加载推送记录
    """
    if ctx.cache_path is not None:
        load_records_by_cache(ctx)
        ctx.records = random.sample(ctx.records, ctx.ticket_count)
    elif ctx.bid_path is not None:
        load_records_by_bid(ctx)
    elif ctx.data_path is not None:
        load_records_by_data(ctx)
    else:
        raise ValueError('no valid path')

    if len(ctx.records) < ctx.ticket_count:
        raise RuntimeError('records not enough')

    proceed()


def create_tickets_for_updating_poi(ctx: Context):
    """
    创建更新 poi 的工单
    """
    for config in ctx.configs:
        for record in ctx.records:
            ctx.tickets.append(Ticket(
                creator=EMAIL,
                mode=ctx.platform,
                project=config.project,
                priority=config.priority,
                batch_id=config.batch_id,
                bid=record.bid,
                method=config.method,
                message=record.work_message,
                src=config.src,
                suggest_poi=Poi(
                    name=record.poi.name,
                    alias=record.poi.alias,
                    address=record.poi.address,
                    geom=shapely.wkt.loads(record.poi.wkt),
                    mc_geom=shapely.wkt.loads(record.poi.mc_wkt),
                    phone=record.poi.phone,
                    status=record.poi.status,
                    tag=record.poi.tag,
                ),
                batch_name=config.batch_name,
            ))


def create_tickets_for_creating_poi(ctx: Context):
    """
    创建新增 poi 的工单
    """
    for config in ctx.configs:
        for record in ctx.records:
            ctx.tickets.append(CreatePoiTicket(
                creator=EMAIL,
                mode=ctx.platform,
                project=config.project,
                priority=config.priority,
                batch_id=config.batch_id,
                bid=record.bid,
                method=config.method,
                message=record.work_message,
                src=config.src,
                suggest_poi=Poi(
                    name=record.poi.name,
                    address=record.poi.address,
                    mc_geom=shapely.wkt.loads(record.poi.mc_wkt),
                ),
                batch_name=config.batch_name,
            ))


@desc()
def create_tickets(ctx: Context, proceed):
    """
    创建工单
    """
    if ctx.method == 'edit':
        create_tickets_for_updating_poi(ctx)
    elif ctx.method == 'new':
        create_tickets_for_creating_poi(ctx)
    else:
        raise ValueError('unknown method')

    proceed()


@desc()
def filter_tickets_by_history(ctx: Context, proceed):
    """
    过滤掉最近下发过的工单
    """
    if ctx.platform != 'formal' or ctx.force:
        proceed()
        return

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for ticket in tqdm(ctx.tickets):
            if exists_ticket(poi_stab, ticket.src, ticket.bid):
                ticket.can_process = False
                ticket.reason = 'is work changed'

    proceed()


@desc()
def filter_tickets_by_city(ctx: Context, proceed):
    """
    根据城市过滤工单
    """
    if ctx.cities:
        for ticket in tqdm(ctx.tickets):
            city = get_city(ticket.suggest_poi.geom.x, ticket.suggest_poi.geom.y)
            if city not in ctx.cities:
                ticket.can_process = False
                ticket.reason = 'city mismatch'
    elif ctx.undesired_cities:
        for ticket in tqdm(ctx.tickets):
            city = get_city(ticket.suggest_poi.geom.x, ticket.suggest_poi.geom.y)
            if city in ctx.undesired_cities:
                ticket.can_process = False
                ticket.reason = 'city mismatch'

    proceed()


@desc()
def fill_ticket_ref_urls(ctx: Context, proceed):
    """
    填充工单的参考图片地址
    """
    auto_repair_mixin.fill_ticket_ref_urls(ctx)
    proceed()


@desc()
def push_tickets(ctx: Context, proceed):
    """
    推送工单
    """
    batch_size = ctx.push_batch_size if ctx.platform != 'formal' else 1
    tickets = [x for x in ctx.tickets if x.can_process]
    for batch in tqdm([TicketBatch(tickets[i:i + batch_size]) for i in range(0, len(tickets), batch_size)]):
        try:
            response = requests.post(ctx.api, json=batch.formatted_json)
            extract_push_result_from_response(batch, response)
        except Exception as e:
            print(e)
            batch.set_push_result(code=-1, msg=str(e))
        finally:
            time.sleep(1)  # 正式环境下游扛不住，qps 限制到 1。

    proceed()


@desc()
def save_tickets_to_db(ctx: Context, proceed):
    """
    保存工单到数据库
    """
    sql = '''
        insert into cdz_ticket(bid, ptid, batch, src, project, priority, method, message, mode)
        values(%s, %s, %s, %s, %s, %s, %s, %s, %s);
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for ticket in [x for x in ctx.tickets if x.push_success]:
                cur.execute(sql, [
                    ticket.bid,
                    ticket.ptid,
                    ticket.batch_id,
                    ticket.src,
                    ticket.project,
                    ticket.priority,
                    ticket.method,
                    ticket.message,
                    ticket.mode,
                ])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()


@desc()
def save_tickets(ctx: Context, proceed):
    """
    保存工单
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.ptid,
                x.res_msg,
                x.reason,
                x.project,
                x.priority,
                x.batch_id,
                x.bid,
                x.method,
                x.message,
                x.src,
                x.suggest_poi.name,
                x.suggest_poi.alias,
                x.suggest_poi.city,
                x.suggest_poi.address,
                x.suggest_poi.geom.wkt if x.suggest_poi.geom else '',
                x.suggest_poi.mc_geom.wkt,
                x.suggest_poi.phone,
                x.suggest_poi.status,
                x.suggest_poi.tag,
                x.batch_name,
                ','.join(x.ref_urls),

                ctx.platform,
                ctx.mode,
                ctx.method,
                ctx.src,
                ctx.work_messages,
                ctx.batch_count,
                ctx.ticket_count,
                ctx.api,
            ]
            for x in ctx.tickets if x.can_process
        ]
    )

    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--cache-path',
        dest='cache_path',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--data-path',
        dest='data_path',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--bid-path',
        dest='bid_path',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--work-messages',
        dest='work_messages',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--src',
        dest='src',
        type=str,
        choices=SRCS,
        required=True,
    )
    parser.add_argument(
        '--platform',
        dest='platform',
        type=str,
        choices=PLATFORMS,
        default='test',
        required=True,
    )
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=MODES,
        required=True,
    )
    parser.add_argument(
        '--method',
        dest='method',
        type=str,
        choices=METHODS,
        required=True,
    )
    parser.add_argument(
        '--batch-id',
        dest='batch_id',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--batch-count',
        dest='batch_count',
        type=int,
        default=0,
        required=False,
    )
    parser.add_argument(
        '--limit',
        dest='limit',
        type=int,
        default=-1,
        required=False,
    )
    parser.add_argument(
        '--push-batch-size',
        dest='push_batch_size',
        type=int,
        default=1,
        required=False,
    )
    parser.add_argument(
        '--ticket-count',
        dest='ticket_count',
        type=int,
        default=0,
        required=False,
    )
    parser.add_argument(
        '--cities',
        dest='cities',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--undesired-cities',
        dest='undesired_cities',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--push',
        dest='push',
        default=False,
        action='store_true',
    )
    parser.add_argument(
        '--force',
        dest='force',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = []

    if args.batch_id is None:
        pipes.extend([
            fill_practice_create_configs,
            fill_practice_update_status_configs,
            fill_practice_update_location_configs,
            fill_practice_update_spacing_configs,

            fill_examine_create_configs,
            fill_examine_update_status_configs,
            fill_examine_update_location_configs,
            fill_examine_update_spacing_configs,

            fill_formal_update_spacing_configs,
            fill_formal_update_status_configs,
            fill_formal_update_ownership_configs,
            fill_formal_update_location_configs,
            fill_formal_update_prop_configs,

            filter_configs,
        ])
    else:
        pipes.append(create_config)

    pipes.extend([
        load_records,
        create_tickets,
        filter_tickets_by_history,
        filter_tickets_by_city,
    ])

    if args.push:
        pipes.extend([
            fill_ticket_ref_urls,
            push_tickets,
            save_tickets_to_db,
        ])

    pipes.append(save_tickets)

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/manual_push_ticket'),
        api=API_MAP[args.platform],
        data_path=args.data_path,
        cache_path=args.cache_path,
        bid_path=args.bid_path,
        work_messages=args.work_messages,
        src=args.src,
        cities=args.cities,
        undesired_cities=args.undesired_cities,
        platform=args.platform,
        mode=args.mode,
        method=args.method,
        batch_id=args.batch_id,
        batch_count=args.batch_count,
        ticket_count=args.ticket_count,
        limit=args.limit,
        push_batch_size=args.push_batch_size,
        force=args.force,
    )
    main_pipe(ctx)


if __name__ == '__main__':
    main(parse_args())
