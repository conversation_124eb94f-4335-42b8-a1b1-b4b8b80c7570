"""
监控竞品Diff的Diff
"""
from pathlib import Path
from src.tools import pipeline, tsv, utils
from shapely import Point
from shapely import wkt
from tqdm import tqdm
import csv
from dataclasses import dataclass, field
from src.parking.recognition import dbutils
from src.tools import pgsql
from src.batch_process import batch_helper

from datetime import datetime, timedelta
from src.charging_station.data import Poi, SPACING_UNKNOWN, CompetitorDiff, SPACING_VALUE_MAP
from src.charging_station.helper import calc_spacing
from src.charging_station import auto_repair_mixin

from src.charging_station.find_competitor_diff import Context
from src.charging_station import find_competitor_diff
from src.charging_station import data_monitor_tool
from src.charging_station.data_monitor_tool import MonitorTool

from src.charging_station.pgsql_table_backup_tool import Backup
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer

desc = pipeline.get_desc()
monitor_tool = MonitorTool(local_base_path='data/competitor_diff_diff/',
                           today_file_name='output' + datetime.now().strftime("%Y-%m-%d") + '.tsv',
                           yesterday_file_name='output' + (datetime.now() - timedelta(days=1)).strftime(
                               "%Y-%m-%d") + '.tsv',
                           AFS_BASE='/user/map-data-streeview/aoi-ml/charging_station/data_monitor/competitor_diff/',
                           afsshell='/home/<USER>/chenbaojun/scripts/afs/bin/afsshell',
                           ruliu_hook='d2ab0b311ae2d9a6faa0d0a4e79100707')
key_word = ['北京市', '上海市']


def save_diff_records(ctx, proceed):
    """
    保存记录
    """
    out_name = 'output' + datetime.now().strftime("%Y-%m-%d") + '.tsv'
    out_path = utils.ensure_path(Path(ctx.work_dir / out_name), cleanup=True)
    for item in tqdm(ctx.diff_data):
        tsv.write_tsv(out_path,
                      [[
                          item.bid, item.prop_type, item.bd_value, item.gd_value
                      ]],
                      mode="a")
    proceed()


def check_poi_key(bid):
    sql = '''
        select city
        from poi 
        where bid = %s ;
    '''
    res = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [bid])
    if res is None:
        return
    if res[0] in key_word:
        return True


def check_beijing_shanghai(ctx):
    key_num = 0
    for record in tqdm(ctx.records):
        if record.competitor is not None and len(record.diff_props) > 0:
            if record.poi.city in key_word:
                key_num += 1
    return key_num


def check_beijing_shanghai_by_poi(ctx):
    """
    检查重点区域poi
    """
    key_num = 0
    for bid in tqdm(ctx.today_bids):
        if check_poi_key(bid):
            key_num += 1
    return key_num


def make_file_link():
    """
    上传文件生成链接
    """
    file_path = monitor_tool.local_base_path + monitor_tool.today_file_name
    print(file_path)
    file_link = monitor_tool.upload_records(Path(file_path))

    return file_link


def parse_file_to_bids(local_file_path: str):
    """
    解析文件
    """
    records = []
    records = [x for x in tsv.read_tsv(local_file_path, skip_header=True)]
    bids = [x[0] for x in records]
    bids = set(bids)
    return bids


def find_diff(ctx, proceed):
    """
    查找Diff
    """
    yesterday_file_path = monitor_tool.local_base_path + monitor_tool.yesterday_file_name
    yesterday_diff_bids = parse_file_to_bids(yesterday_file_path)
    today_file_path = monitor_tool.local_base_path + monitor_tool.today_file_name
    ctx.today_bids = parse_file_to_bids(today_file_path)
    ctx.today_num = len(ctx.today_bids)
    ctx.new_bids = ctx.today_bids - yesterday_diff_bids
    ctx.decrease_bids = yesterday_diff_bids - ctx.today_bids
    proceed()


def make_send_msg(ctx, proceed):
    """
    发送如流消息
    """
    file_link = make_file_link()
    key_num = check_beijing_shanghai(ctx)
    send_msg = f"""【充电站竞品Diff天级监控】{monitor_tool.today_str} 
    今日Diff总数：{ctx.today_num} 
    北上Diff数量：{key_num} 
    相对于昨日新增：{len(ctx.new_bids)} 
    相对于昨日减少：{len(ctx.decrease_bids)} 
    全量属性Diff详情：{file_link}
    """
    print(send_msg)
    data_monitor_tool.alert_to_infoflow(send_msg, 'zengjia01', monitor_tool.ruliu_hook)
    # 'd0d3f0d70c4cd4d88a962090908aeb6fa' z+
    # 'd2ab0b311ae2d9a6faa0d0a4e79100707' qunliao
    proceed()


def upload_download_file(ctx, proceed):
    """
    上传下载文件
    """
    file_path = monitor_tool.local_base_path + monitor_tool.today_file_name
    data_monitor_tool.afs_upload_afs(monitor_tool, file_path)
    file_path = monitor_tool.local_base_path + monitor_tool.yesterday_file_name
    data_monitor_tool.afs_download_file(monitor_tool, file_path)
    proceed()


def create_pipeline(args):
    """创建管道"""
    mode = args.mode if args is not None else None
    pipes = find_competitor_diff.competitor_diff_pipe
    pipes += [
        save_diff_records,
        # upload_download_file,
        find_diff,
        make_send_msg,
        save_diff_records_to_db
    ]
    return pipeline.Pipeline(*pipes)


@desc()
def save_diff_records_to_db(ctx, proceed):
    """
    将抓取结果保存到数据库
    """
    with (
        Backup(config=pgsql.POI_CONFIG, table_name="cdz_competitor_diff") as backup,
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as stab,
    ):
        backup_table_name = backup.execute()
        today_file_path = monitor_tool.local_base_path + monitor_tool.today_file_name
        # noinspection PyTypeChecker
        with (
            open(today_file_path, 'r', encoding='utf-8') as f,
            stab.connection.cursor() as cur,
        ):
            try:
                cur.copy_from(f, table=backup_table_name, columns=(
                    'bid',
                    'field_name',
                    'bd_value',
                    'gd_value'
                ))
                stab.connection.commit()
                msg = '竞品Diff数据入库成功！'
                data_monitor_tool.alert_to_infoflow(msg, 'zengjia01', data_monitor_tool.ruliu_hook)
            except Exception as e:
                print(e)
                stab.connection.rollback()
                msg = '竞品Diff数据入库异常'
                data_monitor_tool.alert_to_infoflow(msg, 'zengjia01', data_monitor_tool.ruliu_hook)


def main(args=None):
    """主函数"""
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('data/competitor_diff_diff'),
    )

    main_pipe(ctx)
    return ctx


if __name__ == '__main__':
    main(args=auto_repair_mixin.parse_args())
