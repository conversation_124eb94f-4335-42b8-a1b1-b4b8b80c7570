# !/usr/bin/env python3
"""
封装了充电站属性推送逻辑
"""
import json

import requests
import shapely.wkt
from mapio.poi.model import Value
from mapio.poi.output.data_access import DataAccess
from mapio.poi.poi_io import load_online_poi
from mapio.utils import coord
from tqdm import tqdm

from src.charging_station.data import Poi
from src.charging_station.helper import (
    get_nested_value,
    get_all_white_list_bids,
    get_ugc_intervene_spacing_bids,
    get_ugc_intervene_status_bids,
    get_ugc_intervene_location_bids,
    get_ugc_intervene_phone_bids,
    get_ugc_intervene_relation_bids,
    get_ugc_intervene_ownership_bids,
)


def __post_data(api, data):
    """
    推送上线数据
    """
    try:
        response = requests.post(api, json=data)
        if response.status_code != 200:
            print("error:", data['bid'])
    except:
        print('error:', data['bid'])


def __generate_api(source):
    """
    生成推送 api
    """
    source_items = source.split('.')
    src_type = source_items[0]
    sub_src = source_items[1]
    return f'http://mapde-poi.baidu-int.com/prod/api/cdzDataPush?src_type={src_type}&sub_src={sub_src}'


def online_relation(pois: list[Poi], source='cdz.celue'):
    """
    推送父子关系
    """
    ignored_bids = get_all_white_list_bids() | get_ugc_intervene_relation_bids()
    api = __generate_api(source)

    if not pois:
        return

    for poi in tqdm(pois):
        if poi.bid in ignored_bids:
            continue

        __post_data(api, {
            'bid': poi.bid,
            'relation_bid': poi.relation_bid,
        })


def online_telephone(pois: list[Poi], source='phone.chongdianzhanjieru'):
    """
    推送联系方式
    """
    ignored_bids = get_all_white_list_bids() | get_ugc_intervene_phone_bids()
    data_access = DataAccess(source=source)

    if not pois:
        return

    for poi in tqdm(pois):
        if poi.bid in ignored_bids:
            continue

        try:
            online_poi_info = load_online_poi(poi.bid, ["basic"], source=source)
            old_value = str(online_poi_info.basic.phone)
            new_value = str(poi.phone)
            if old_value == new_value:
                continue

            data_access.update_by_bid(poi.bid, [
                Value(key="phone", old_value=old_value, new_value=new_value),
            ])
        except Exception as e:
            print(e)
            print('error:', poi.bid)


def online_spacing(pois: list[Poi], force=False, source='cdz.celue'):
    """
    推送空间属性
    """
    ignored_bids = get_all_white_list_bids() | get_ugc_intervene_spacing_bids()
    data_access = DataAccess(source=source)

    if not pois:
        return

    for poi in tqdm(pois):
        if not force and poi.bid in ignored_bids:
            continue

        try:
            online_poi_info = load_online_poi(poi.bid, ["basic"], source=source)
            tag_info = json.loads(online_poi_info.basic.tag_info)
            old_value_std_tag = str(online_poi_info.basic.std_tag)
            old_value_classify_tag = get_nested_value(tag_info, ['classification', 'show_tag'])
            old_value_classify_tag = '' if old_value_classify_tag is None else old_value_classify_tag

            if poi.show_tag != '' and (force or old_value_classify_tag != poi.show_tag):
                # WARNING: std_tag 必须要一起更新，哪怕和线上的值一致也需要更新，这里写死 '交通设施;充电站' 防止外部没有传参。
                values = [
                    Value(key="std_tag", old_value=old_value_std_tag, new_value='交通设施;充电站'),
                    Value(key="classify_tag", old_value=old_value_classify_tag, new_value=poi.show_tag),
                ]
                data_access.update_by_bid(poi.bid, values)
        except Exception as e:
            print(e)
            print('error:', poi.bid)


def online_tag(pois: list[Poi], source='zt_life.cdz_rengong'):
    """
    推送 tag
    """
    data_access = DataAccess(source=source)

    if not pois:
        return

    for poi in tqdm(pois):
        try:
            online_poi_info = load_online_poi(poi.bid, ["basic"], source=source)
            tag_info = json.loads(online_poi_info.basic.tag_info)
            old_value_std_tag = str(online_poi_info.basic.std_tag)
            old_value_classify_tag = get_nested_value(tag_info, ['classification', 'show_tag'])
            old_value_classify_tag = '' if old_value_classify_tag is None else old_value_classify_tag

            if poi.tag != '' and poi.show_tag != '':
                values = [
                    Value(key="std_tag", old_value=old_value_std_tag, new_value=poi.tag),
                    Value(key="classify_tag", old_value=old_value_classify_tag, new_value=poi.show_tag),
                ]
                data_access.update_by_bid(poi.bid, values)
        except Exception as e:
            print(e)
            print('error:', poi.bid)


def online_ownership(pois: list[Poi], source='cdz.celue'):
    """
    推送开放属性
    """
    ignored_bids = get_all_white_list_bids() | get_ugc_intervene_ownership_bids()
    api = __generate_api(source)

    if not pois:
        return

    for poi in tqdm(pois):
        if poi.bid in ignored_bids:
            continue

        __post_data(api, {
            'bid': poi.bid,
            'open_limit': poi.ownership,
        })


def online_status(pois: list[Poi], source='cdz.celue'):
    """
    推送状态
    """
    ignored_bids = get_all_white_list_bids() | get_ugc_intervene_status_bids()
    data_access = DataAccess(source=source)

    if not pois:
        return

    for poi in tqdm(pois):
        if poi.bid in ignored_bids:
            continue

        try:
            online_poi_info = load_online_poi(poi.bid, ["basic"], source=source)
            old_value = str(online_poi_info.basic.status)
            new_value = str(poi.status)

            if old_value == new_value:
                continue

            data_access.update_by_bid(poi.bid, [
                Value(key="status", old_value=old_value, new_value=new_value),
            ])
        except:
            print('error:', poi.bid)


def online_gcj_location(pois: list[Poi], source='cdz.celue'):
    """
    推送国测局坐标
    """
    mc_pois = []

    if not pois:
        return

    for poi in tqdm(pois):
        mc_x, mc_y = coord.bd09_to_mercator(*coord.gcj02_to_bd09(poi.geom.x, poi.geom.y))
        mc_pois.append(Poi(bid=poi.bid, mc_geom=shapely.wkt.loads(f"POINT({mc_x} {mc_y})")))

    online_mc_location(mc_pois, source)


def online_mc_location(pois: list[Poi], source='cdz.celue'):
    """
    推送墨卡托坐标
    """
    ignored_bids = get_all_white_list_bids() | get_ugc_intervene_location_bids()
    data_access = DataAccess(source=source)

    if not pois:
        return

    for poi in tqdm(pois):
        if poi.bid in ignored_bids:
            continue

        try:
            online_poi_info = load_online_poi(poi.bid, ["basic"], source=source)
            # 因为涉及到坐标转换，所以和线上不可能完全相同，1m 的误差是可以接受的。
            old_value_point_x = str(int(online_poi_info.basic.point_x))
            old_value_point_y = str(int(online_poi_info.basic.point_y))
            new_value_point_x = str(int(poi.mc_geom.x))
            new_value_point_y = str(int(poi.mc_geom.y))

            if old_value_point_x == new_value_point_x and old_value_point_y == new_value_point_y:
                continue

            data_access.update_by_bid(poi.bid, [
                Value(key="point_x", old_value=old_value_point_x, new_value=str(poi.mc_geom.x)),
                Value(key="point_y", old_value=old_value_point_y, new_value=str(poi.mc_geom.y)),
            ])
        except:
            print('error:', poi.bid)
