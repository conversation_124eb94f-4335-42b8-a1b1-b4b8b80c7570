# !/usr/bin/env python3
"""
停车场数据监控
"""
import os
import sys
import datetime
import re
import zipfile
from dataclasses import dataclass, field
from pathlib import Path
from typing import Literal

import requests
from retrying import retry
from tqdm import tqdm
root_path = Path(os.path.abspath(__file__)).parents[2]
sys.path.insert(0, root_path.as_posix())

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import Poi
from src.tools import pipeline, pgsql, tsv, notice_tool
from src.tools.file_downloader import download_file_by_http
from src.tools.icafe_tool import create_card

DOWNLOAD_URL_FORMAT = ('http://poi-data-subscription.baidu-int.com/data_warehouse/{0}'
                       '/zhaoguanghui03_0802cb3885b92631.tsv_dir/')

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    execution_time: datetime.datetime
    upload_file_url: str
    download_file_url: str
    file_name: str = ''
    data_path = None
    closest_time: datetime.datetime = None
    should_alert: bool = False
    statistics_data: dict[str, object] = field(default_factory=dict)
    day_statistics_data: dict[str, object] = field(default_factory=dict)
    
    latest_park_records: list[Poi] = field(default_factory=list)
    old_park_records: list[Poi] = field(default_factory=list)
    one_hour_new_park_records: list[Poi] = field(default_factory=list)
    one_hour_delete_park_records: list[Poi] = field(default_factory=list)
    one_hour_new_high_pv_park_records: list[Poi] = field(default_factory=list)
    one_hour_delete_high_pv_park_records: list[Poi] = field(default_factory=list)
    one_hour_delete_4categories_park_records: list[Poi] = field(default_factory=list)
    one_day_new_park_records: list[Poi] = field(default_factory=list)
    one_day_delete_park_records: list[Poi] = field(default_factory=list)
    one_day_new_high_pv_park_records: list[Poi] = field(default_factory=list)
    one_day_delete_high_pv_park_records: list[Poi] = field(default_factory=list)
    one_day_delete_4categories_park_records: list[Poi] = field(default_factory=list)

    latest_gate_records: list[Poi] = field(default_factory=list)
    old_gate_records: list[Poi] = field(default_factory=list)
    one_hour_new_gate_records: list[Poi] = field(default_factory=list)
    one_hour_delete_gate_records: list[Poi] = field(default_factory=list)
    one_hour_new_high_pv_gate_records: list[Poi] = field(default_factory=list)
    one_hour_delete_high_pv_gate_records: list[Poi] = field(default_factory=list)
    one_day_new_gate_records: list[Poi] = field(default_factory=list)
    one_day_delete_gate_records: list[Poi] = field(default_factory=list)
    one_day_new_high_pv_gate_records: list[Poi] = field(default_factory=list)
    one_day_delete_high_pv_gate_records: list[Poi] = field(default_factory=list)

    def __post_init__(self):
        '''
        初始化工作目录和文件名
        '''
        self.work_dir.mkdir(parents=True, exist_ok=True)
        self.file_name = f'{self.execution_time.date()}_{self.execution_time.hour:02d}'

@desc()
def download_latest_data(ctx: Context, proceed):
    """
    下载最新数据
    """
    data_path = ctx.work_dir / f'{ctx.file_name}.csv'
    if data_path.exists():
        ctx.data_path = data_path
        print(f'{ctx.file_name}.csv 已存在，跳过下载。')
        proceed()
        return

    base_url = DOWNLOAD_URL_FORMAT.format(ctx.execution_time.strftime('%Y-%m-%d'))
    try:
        response = requests.get(base_url)
        response.raise_for_status()
        html_content = response.text.splitlines()
        html_string = ''.join(html_content)
        print(f'html_string: {html_string}')

        if parse_download_data(base_url, html_string, ctx.work_dir, data_path):
            ctx.data_path = data_path
        else:
            if os.path.exists(data_path):
                os.remove(data_path)
            ctx.data_path = ''

        delete_old_file(ctx.work_dir.glob('*.csv'))
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败：{e}")
        return False
        
    proceed()

def parse_download_data(base_url, html_string, work_dir, data_path):
    '''
    下载数据，解压数据，合并数据
    '''
    zip_file_paths = []
    unzip_file_paths = []
    try:
        href_links = re.findall(r'href="([^"]+)"', html_string)
        for link in href_links:
            if not link.startswith('xa') or not link.endswith('.zip'):
                print(f'下载异常，数据有延迟{html_string}')
                return False

        # 提取所有 zip 文件名
        zip_file_names = re.findall(r'href="([^"]+\.zip)"', html_string)
        if len(zip_file_names) == 0:
            print(f'下载异常，数据有延迟{html_string}')
            return False

        for zip_name in zip_file_names:
            if not zip_name.startswith('xa'):
                print(f'下载异常，数据有延迟{html_string}')
                return False
                
        for zip_name in zip_file_names:
            zip_file_url = base_url + zip_name
            print(f'下载文件：{zip_file_url}')
            # 下载文件
            zip_file_path = os.path.join(work_dir, zip_name)
            zip_file_paths.append(zip_file_path)
            with open(zip_file_path, 'wb') as f:
                file_response = requests.get(zip_file_url)
                f.write(file_response.content)
            if not os.path.exists(zip_file_path):
                print(f'下载异常，{zip_file_path}')
                return False
            print(f'下载完成：{zip_file_path}')
        
        # 解压文件
        pwd='e9a16cc8'        
        for zip_file in zip_file_paths:
            print(f'解压文件：{zip_file}')
            file_name = os.path.splitext(os.path.basename(zip_file))[0]
            unzip_file = work_dir / f'{file_name}'
            unzip_file_paths.append(unzip_file)
            os.system(f'unzip -P {pwd} {zip_file} -d {work_dir}')
            if not os.path.exists(unzip_file):
                print(f'解压异常，{unzip_file}')
                return False
            print(f'解压完成：{unzip_file}')         
        
        # 合并文件
        with open(data_path, 'w') as output_file:
            for file in unzip_file_paths:
                print(f'合并文件: {file}')
                with open(file, 'r') as data_file:
                    output_file.write(data_file.read())
        if not os.path.exists(data_path):
            print(f'合并异常，{data_path}')
            return False
        
        return True

    except Exception as e:
        print(f'下载异常，{e}')
        return False
    finally:
        # 清理工作
        for file in zip_file_paths:
            if os.path.exists(file):
                os.remove(file)
        for file in unzip_file_paths:
            if os.path.exists(file):
                os.remove(file)

def delete_old_file(file_paths):
    '''
    删除历史文件
    '''
    valid_files = [
        file_path for file_path in file_paths
        if re.match(r'^\d{4}-\d{2}-\d{2}_\d{1,2}\.csv$', file_path.name)
    ]
    if len(valid_files) <= 24:
        return

    sorted_files = sorted(valid_files, key=lambda x: datetime.datetime.strptime(x.stem, "%Y-%m-%d_%H"))
    oldest_file = sorted_files[0]
    if os.path.exists(oldest_file):
        oldest_file.unlink()
        print(f'删除旧文件：{oldest_file}')

def load_records(data_path):
    """
    加载数据
    """
    park_records = []
    gate_records = []
    count = 0
    with open(data_path, 'r', encoding='utf-8') as f:
        f.readline() # 跳过第一行标题
        for line in f:
            count += 1
            parts = line.strip().split('\t')
            if len(parts) < 10:
                print(f'Invalid record: {line}')
                continue
            
            bid = parts[0]
            name = parts[1]
            std_tag_1 = parts[5]
            std_tag_2 = parts[6]
            status = parts[9]
            if std_tag_1 == '交通设施' and std_tag_2 in ['路侧停车位', '停车场']:
                park_records.append(Poi(
                bid=bid,
                name=name
                ))
            if std_tag_1 == '出入口' and std_tag_2 in ['停车场出入口', '停车场出口', '停车场入口']:
                gate_records.append(Poi(
                bid=bid,
                name=name
                ))
    print(f'park_records: {len(park_records)},gate_records: {len(gate_records)}, total_count: {count}')
    return park_records, gate_records

@desc()
def load_latest_records(ctx: Context, proceed):
    """
    加载最新数据
    """
    if ctx.data_path == '':
        proceed()
        return

    ctx.latest_park_records, ctx.latest_gate_records = load_records(ctx.data_path)
    ctx.statistics_data['latest_park_total_count'] = len(ctx.latest_park_records)
    ctx.statistics_data['latest_gate_total_count'] = len(ctx.latest_gate_records)
    proceed()

def find_latest_file(file_paths, current_time):
    """
    找到最近的文件
    """
    closest_file = None
    closest_time = None
    smallest_diff = float('inf')  # 初始化为无限大

    # 使用正则过滤符合格式的文件路径
    valid_files = [
        file_path for file_path in file_paths
        if re.match(r'^\d{4}-\d{2}-\d{2}_\d{1,2}\.csv$', file_path.name)
    ]

    for file_path in valid_files:
        try:
            # 从文件名提取时间
            file_time_str = file_path.stem  # 获取文件名（不含后缀）
            file_time = datetime.datetime.strptime(file_time_str, '%Y-%m-%d_%H')

            # 计算时间差
            time_diff = abs((file_time - current_time).total_seconds())

            # 更新最近文件
            if time_diff < smallest_diff:
                smallest_diff = time_diff
                closest_file = file_path
                closest_time = file_time
        except ValueError:
            print(f"无法解析文件名: {file_path}")

    return closest_file, closest_time

@desc()
def load_one_hour_ago_records(ctx: Context, proceed):
    """
    加载一小时前的数据
    """
    closest_file, closest_time = find_latest_file(ctx.work_dir.glob('*.csv'), ctx.execution_time)
    print(f'最近的文件是: {closest_file}')

    if closest_file is None:
        raise FileNotFoundError()

    ctx.closest_time = closest_time
    ctx.old_park_records, ctx.old_gate_records = load_records(closest_file)
    ctx.statistics_data['old_park_total_count'] = len(ctx.old_park_records)
    ctx.statistics_data['old_gate_total_count'] = len(ctx.old_gate_records)
    proceed()

def diff_data(latest_records, old_records):
    """
    比较两份记录返回新增、删除记录
    """
    latest_bids = set(record.bid for record in latest_records)
    old_bids = set(record.bid for record in old_records)

    new_bids = latest_bids - old_bids
    delete_bids = old_bids - latest_bids

    new_records = [record for record in latest_records if record.bid in new_bids]
    delete_records = [record for record in old_records if record.bid in delete_bids]

    return new_records, delete_records

def get_high_pv_park(park_records):
    '''
    获取高pv停车场
    停车场父点的算路PV 大于1000
    '''
    high_pv_records = []
    if len(park_records) == 0:
        return high_pv_records

    bids = []
    for record in park_records:
        bids.append(f"'{record.bid}'")
    bids_str = ','.join(bids)
    
    sql = f'''
        select t1.bid, t1.name, t2.click_pv from park_online_data t1 left join poi t2 on t1.parent_id = t2.bid 
        where t1.bid in ({bids_str}) and t1.parent_id !='0' and t2.click_pv is not null and t2.click_pv >= 1000;
    '''
    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab:
        rows = poi_stab.fetch_all(sql, [])
        if rows is not None and len(rows) > 0:
            for row in rows:
                bid = row[0]
                name = row[1]
                click_pv = row[2]
                if not str(click_pv).isdigit():
                    continue
                high_pv_records.append(Poi(
                bid=bid,
                name=name,
                click_pv=int(click_pv)
                ))
    return high_pv_records

def get_4categories_parking(park_records):
    '''
    获取属于四大垂类的停车场
    '''
    parking_4categories_records = []
    if len(park_records) == 0:
        return parking_4categories_records

    bids = []
    for record in park_records:
        bids.append(f"'{record.bid}'")
    bids_str = ','.join(bids)
    std_tag_list = [
        '购物;购物中心', '购物;百货商场', '购物;超市', 
        '交通设施;飞机场', '交通设施;火车站', '交通设施;港口',
        '医疗;综合医院', '医疗;专科医院']
    std_tag_str = ','.join([f"'{tag}'" for tag in std_tag_list])
    sql = f'''
        select t1.bid, t1.name, t2.std_tag, t2.show_tag from park_online_data t1 
        left join poi t2 on t1.parent_id = t2.bid 
        where t1.bid in ({bids_str}) and t1.parent_id !='0' and 
        (t2.std_tag in ({std_tag_str}) or t2.std_tag like '旅游景点;%%' or 
        (t2.std_tag like '交通设施;%%' and t2.show_tag = '航站楼'));
    '''
    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab:
        rows = poi_stab.fetch_all(sql, [])
        if rows is not None and len(rows) > 0:
            for row in rows:
                bid = row[0]
                name = row[1]
                std_tag = row[2]
                show_tag = row[3]
                parking_4categories_records.append(Poi(
                bid=bid,
                name=name,
                show_tag=f'{std_tag}|{show_tag}'
                ))
    return parking_4categories_records

def get_high_pv_gate(gate_records):
    '''
    获取高pv出入口
    停车场父点的算路PV 大于1000
    '''
    high_pv_records = []
    if len(gate_records) == 0:
        return high_pv_records

    bids = []
    for record in gate_records:
        bids.append(f"'{record.bid}'")
    bids_str = ','.join(bids)
    
    sql = f'''
        select t1.bid, t1.name, t3.click_pv from park_online_data t1 
        left join park_online_data t2 on t1.parent_id = t2.bid 
        left join poi t3 on t2.parent_id = t3.bid 
        where t1.bid in ({bids_str}) and t1.parent_id !='0' and t2.parent_id != '0' and 
        t3.click_pv is not null and t3.click_pv >= 1000;
    '''
    with PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab:
        rows = poi_stab.fetch_all(sql, [])
        if rows is not None and len(rows) > 0:
            for row in rows:
                bid = row[0]
                name = row[1]
                click_pv = row[2]
                if not str(click_pv).isdigit():
                    continue
                high_pv_records.append(Poi(
                bid=bid,
                name=name,
                click_pv=int(click_pv)
                ))
    return high_pv_records

@desc()
def diff_one_hour_data(ctx: Context, proceed):
    """
    比较一小时前后的数据
    """
    if ctx.data_path == '':
        proceed()
        return

    ctx.one_hour_new_park_records, ctx.one_hour_delete_park_records = \
    diff_data(ctx.latest_park_records, ctx.old_park_records)
    ctx.one_hour_new_high_pv_park_records = get_high_pv_park(ctx.one_hour_new_park_records)
    ctx.one_hour_delete_high_pv_park_records = get_high_pv_park(ctx.one_hour_delete_park_records)
    ctx.one_hour_delete_4categories_park_records = get_4categories_parking(ctx.one_hour_delete_park_records)

    ctx.statistics_data['new_park_count'] = len(ctx.one_hour_new_park_records)
    ctx.statistics_data['delete_park_count'] = len(ctx.one_hour_delete_park_records)
    ctx.statistics_data['new_high_pv_park_count'] = len(ctx.one_hour_new_high_pv_park_records)
    ctx.statistics_data['delete_high_pv_park_count'] = len(ctx.one_hour_delete_high_pv_park_records)
    ctx.statistics_data['delete_4categories_park_count'] = len(ctx.one_hour_delete_4categories_park_records)

    ctx.one_hour_new_gate_records, ctx.one_hour_delete_gate_records = \
    diff_data(ctx.latest_gate_records, ctx.old_gate_records)
    ctx.one_hour_new_high_pv_gate_records = get_high_pv_gate(ctx.one_hour_new_gate_records)
    ctx.one_hour_delete_high_pv_gate_records = get_high_pv_gate(ctx.one_hour_delete_gate_records)

    ctx.statistics_data['new_gate_count'] = len(ctx.one_hour_new_gate_records)
    ctx.statistics_data['delete_gate_count'] = len(ctx.one_hour_delete_gate_records)
    ctx.statistics_data['new_high_pv_gate_count'] = len(ctx.one_hour_new_high_pv_gate_records)
    ctx.statistics_data['delete_high_pv_gate_count'] = len(ctx.one_hour_delete_high_pv_gate_records)

    proceed()

def save_records(path, records: list[Poi]):
    """
    保存数据
    """
    if not records:
        return

    tsv.write_tsv(
        path,
        [
            [
                x.bid,
                x.name,
            ]
            for x in records
        ]
    )

def save_high_pv_records(path, records: list[Poi]):
    """
    保存高pv数据
    """
    if not records:
        return

    tsv.write_tsv(
        path,
        [
            [
                x.bid,
                x.name,
                x.click_pv,
            ]
            for x in records
        ]
    )

def save_4categories_records(path, records: list[Poi]):
    """
    保存四大垂类数据
    """
    if not records:
        return

    tsv.write_tsv(
        path,
        [
            [
                x.bid,
                x.name,
                x.show_tag,
            ]
            for x in records
        ]
    )

@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(upload_file_url, local_file_path):
    """
    上传文件
    """
    files = {'file': open(local_file_path, 'r', encoding='utf-8')}
    file_uuid = requests.post(upload_file_url, files=files).text
    if len(file_uuid) != 32:
        raise Exception(f'upload file failed')

    return file_uuid


def upload_records(ctx: Context, local_path: Path):
    """
    上传记录
    """
    if not local_path.exists():
        return ''

    file_uuid = upload_file(ctx.upload_file_url, local_path)
    local_path.unlink()
    return f'{ctx.download_file_url}&uuid={file_uuid}'

@desc()
def upload_one_hour_diff_data(ctx: Context, proceed):
    """
    上传差异数据
    """
    if ctx.data_path == '':
        proceed()
        return

    new_park_path = ctx.work_dir / f'{ctx.file_name}_new_park.csv'
    delete_park_path = ctx.work_dir / f'{ctx.file_name}_delete_park.csv'
    new_high_pv_park_path = ctx.work_dir / f'{ctx.file_name}_new_high_pv_park.csv'
    delete_high_pv_park_path = ctx.work_dir / f'{ctx.file_name}_delete_high_pv_park.csv'
    delete_4categories_park_path = ctx.work_dir / f'{ctx.file_name}_delete_4categories_park.csv'

    save_records(new_park_path, ctx.one_hour_new_park_records)
    save_records(delete_park_path, ctx.one_hour_delete_park_records)
    save_high_pv_records(new_high_pv_park_path, ctx.one_hour_new_high_pv_park_records)
    save_high_pv_records(delete_high_pv_park_path, ctx.one_hour_delete_high_pv_park_records)
    save_4categories_records(delete_4categories_park_path, ctx.one_hour_delete_4categories_park_records)

    ctx.statistics_data['new_park_download_url'] = upload_records(ctx, new_park_path)
    ctx.statistics_data['delete_park_download_url'] = upload_records(ctx, delete_park_path)
    ctx.statistics_data['new_high_pv_park_download_url'] = upload_records(ctx, new_high_pv_park_path)
    ctx.statistics_data['delete_high_pv_park_download_url'] = upload_records(ctx, delete_high_pv_park_path)
    ctx.statistics_data['delete_4categories_park_download_url'] = upload_records(ctx, delete_4categories_park_path)

    new_gate_path = ctx.work_dir / f'{ctx.file_name}_new_gate.csv'
    delete_gate_path = ctx.work_dir / f'{ctx.file_name}_delete_gate.csv'
    new_high_pv_gate_path = ctx.work_dir / f'{ctx.file_name}_new_high_pv_gate.csv'
    delete_high_pv_gate_path = ctx.work_dir / f'{ctx.file_name}_delete_high_pv_gatek.csv'    

    save_records(new_gate_path, ctx.one_hour_new_gate_records)
    save_records(delete_gate_path, ctx.one_hour_delete_gate_records)
    save_high_pv_records(new_high_pv_gate_path, ctx.one_hour_new_high_pv_gate_records)
    save_high_pv_records(delete_high_pv_gate_path, ctx.one_hour_delete_high_pv_gate_records)    

    ctx.statistics_data['new_gate_download_url'] = upload_records(ctx, new_gate_path)
    ctx.statistics_data['delete_gate_download_url'] = upload_records(ctx, delete_gate_path)
    ctx.statistics_data['new_high_pv_gate_download_url'] = upload_records(ctx, new_high_pv_gate_path)
    ctx.statistics_data['delete_high_pv_gate_download_url'] = upload_records(ctx, delete_high_pv_gate_path)

    proceed()

@desc()
def print_one_hour_statistics_data(ctx: Context, proceed):
    """
    打印一小时内的统计数据
    """
    if ctx.data_path == '':
        proceed()
        return

    for name, value in ctx.statistics_data.items():
        print(f'{name}: {value}')

    proceed()

@desc()
def send_one_hour_statistics_data_to_infoflow(ctx: Context, proceed):
    """
    发送一小时内的统计数据到如流
    """
    if ctx.data_path == '':
        proceed()
        return

    msg = f'''【停车场小时级监控】
相对 {ctx.closest_time:%Y-%m-%d %H:%M:%S} 的数据量对比:
新增停车场: {ctx.statistics_data['new_park_count']}, 下载地址: {ctx.statistics_data['new_park_download_url']}

删除停车场: {ctx.statistics_data['delete_park_count']}, 下载地址：{ctx.statistics_data['delete_park_download_url']}

新增高pv停车场: {ctx.statistics_data['new_high_pv_park_count']}, 
下载地址: {ctx.statistics_data['new_high_pv_park_download_url']}

删除高pv停车场: {ctx.statistics_data['delete_high_pv_park_count']}, 
下载地址: {ctx.statistics_data['delete_high_pv_park_download_url']}

删除四大垂类停车场: {ctx.statistics_data['delete_4categories_park_count']}, 
下载地址: {ctx.statistics_data['delete_4categories_park_download_url']}

新增出入口: {ctx.statistics_data['new_gate_count']}, 下载地址: {ctx.statistics_data['new_gate_download_url']}

删除出入口: {ctx.statistics_data['delete_gate_count']}, 下载地址: {ctx.statistics_data['delete_gate_download_url']}

新增高pv出入口: {ctx.statistics_data['new_high_pv_gate_count']}, 
下载地址: {ctx.statistics_data['new_high_pv_gate_download_url']}

删除高pv出入口: {ctx.statistics_data['delete_high_pv_gate_count']}, 
下载地址: {ctx.statistics_data['delete_high_pv_gate_download_url']}
'''
    print(msg)
    user_names, passwords, emails = get_at_user_name_passwork()
    notice_tool.send_hi(msg, atuserids=user_names, token='d8d8bfd0c21fa61a90b63fdc88dc909a8')
    notice_tool.send_hi(msg, None, token='df2c29952bfc58250be8df29534603d77')
    proceed()

def alert_if_changed_too_much(msg_items):
    """
    如果变化量级过多，则报警。
    """
    msg = f'''【停车场小时级监控】
{''.join(msg_items)}
请打开 sugar 平台查看详情：
https://sugar.baidu-int.com/group/aoi_monitor/report/r_1013e-3xzp5syz-k4v5pb?__scp__=Baidu
'''
    print(msg)
    user_names, passwords, emails = get_at_user_name_passwork()
    print(f'user_names: {user_names}; passwords: {passwords}; emails: {emails}')
    notice_tool.send_hi(msg, atuserids=user_names, token='d8d8bfd0c21fa61a90b63fdc88dc909a8')
    notice_tool.send_hi(msg, None, token='df2c29952bfc58250be8df29534603d77')
    '''
    response = create_card(
        space='park-case-closed',
        title='停车场变化量级超过阈值',
        owner=user_names[0],
        detail=msg,
        username=user_names[0],
        type='Case',
        password=passwords[0],
        notify_emails=emails,
    )
    print(f'response: {response}')
    '''

@desc()
def alert_if_changed_too_much_one_hour(ctx: Context, proceed):
    """
    如果 1 小时内变化量级过多，则报警。
    """
    if ctx.data_path == '':
        proceed()
        return

    max_delete_count = 100
    max_add_count = 1000
    max_high_pv = 5
    msg_items = []

    delete_park_count = int(str(ctx.statistics_data['delete_park_count']))
    if delete_park_count > max_delete_count:
        msg_items.append(f'停车场一小时内删除量级: {delete_park_count}，超过阈值: {max_delete_count}。')

    new_park_count = int(str(ctx.statistics_data['new_park_count']))
    if new_park_count > max_add_count:
        msg_items.append(f'停车场一小时内新增量级: {new_park_count}，超过阈值: {max_add_count}。')

    delete_high_pv_park_count = int(str(ctx.statistics_data['delete_high_pv_park_count']))
    if delete_high_pv_park_count > max_high_pv:
        msg_items.append(f'停车场一小时内删除高pv量级: {delete_high_pv_park_count}，超过阈值: {max_high_pv}。')

    new_high_pv_park_count = int(str(ctx.statistics_data['new_high_pv_park_count']))
    if new_high_pv_park_count > max_high_pv:
        msg_items.append(f'停车场一小时内新增高pv量级: {new_high_pv_park_count}，超过阈值: {max_high_pv}。')

    delete_gate_count = int(str(ctx.statistics_data['delete_gate_count']))
    if delete_gate_count > max_delete_count:
        msg_items.append(f'出入口一小时内删除量级: {delete_gate_count}，超过阈值: {max_delete_count}。')

    new_gate_count = int(str(ctx.statistics_data['new_gate_count']))
    if new_gate_count > max_add_count:
        msg_items.append(f'出入口一小时内新增量级: {new_gate_count}，超过阈值: {max_add_count}。')
    
    delete_high_pv_gate_count = int(str(ctx.statistics_data['delete_high_pv_gate_count']))
    if delete_high_pv_gate_count > max_high_pv:
        msg_items.append(f'出入口一小时内删除高pv量级: {delete_high_pv_gate_count}，超过阈值: {max_high_pv}。')

    new_high_pv_gate_count = int(str(ctx.statistics_data['new_high_pv_gate_count']))
    if new_high_pv_gate_count > max_high_pv:
        msg_items.append(f'出入口一小时内新增高pv量级: {new_high_pv_gate_count}，超过阈值: {max_high_pv}。')

    if not msg_items:
        proceed()
        return

    alert_if_changed_too_much(msg_items)
    proceed()

@desc()
def save_one_hour_statistics_data_to_db(ctx: Context, proceed):
    """
    保存一小时内的统计数据到数据库
    """
    if ctx.data_path == '':
        proceed()
        return

    delete_sql = '''
        delete from park_gate_data_monitor
        where date(create_time) = (DATE %s) and 
              extract(hour from create_time) = %s;
    '''
    insert_sql = '''
        insert into park_gate_data_monitor(name, value, create_time) values (%s, %s, %s);
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        today = ctx.execution_time.strftime('%Y-%m-%d')
        poi_stab.execute(delete_sql, [today, ctx.execution_time.hour])

        try:
            for name, value in ctx.statistics_data.items():
                cur.execute(insert_sql, [name, value, datetime.datetime.now()])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()

@desc()
def load_one_day_ago_records(ctx: Context, proceed):
    """
    加载一天内的记录
    """
    if ctx.data_path == '' or check_is_day_statistics(ctx):
        proceed()
        return

    if ctx.execution_time.hour not in (20, 21):  # 只在晚上 20、21 点执行
        proceed()
        return

    start_time = ctx.execution_time - datetime.timedelta(hours=24)
    closest_file, closest_time = find_latest_file(ctx.work_dir.glob('*.csv'), start_time)
    print(f'最近的文件是: {closest_file}')
    if closest_file is None:
        raise FileNotFoundError()

    one_day_ago_park_records, one_day_ago_gate_records = load_records(closest_file)

    ctx.one_day_new_park_records, ctx.one_day_delete_park_records = \
    diff_data(ctx.latest_park_records, one_day_ago_park_records)
    ctx.one_day_new_high_pv_park_records = get_high_pv_park(ctx.one_day_new_park_records)
    ctx.one_day_delete_high_pv_park_records = get_high_pv_park(ctx.one_day_delete_park_records)
    ctx.one_day_delete_4categories_park_records = get_4categories_parking(ctx.one_day_delete_park_records)
    ctx.day_statistics_data['one_day_new_park_count'] = len(ctx.one_day_new_park_records)
    ctx.day_statistics_data['one_day_delete_park_count'] = len(ctx.one_day_delete_park_records)
    ctx.day_statistics_data['one_day_new_high_pv_park_count'] = len(ctx.one_day_new_high_pv_park_records)
    ctx.day_statistics_data['one_day_delete_high_pv_park_count'] = len(ctx.one_day_delete_high_pv_park_records)
    ctx.day_statistics_data['one_day_delete_4categories_park_count'] = len(ctx.one_day_delete_4categories_park_records)

    new_park_path = ctx.work_dir / f'{ctx.file_name}_new_park_all.csv'
    delete_park_path = ctx.work_dir / f'{ctx.file_name}_delete_park_all.csv'
    new_high_pv_park_path = ctx.work_dir / f'{ctx.file_name}_new_high_pv_park_all.csv'
    delete_high_pv_park_path = ctx.work_dir / f'{ctx.file_name}_delete_high_pv_park_all.csv'
    delete_4categories_park_path = ctx.work_dir / f'{ctx.file_name}_delete_4categories_park_all.csv'

    save_records(new_park_path, ctx.one_day_new_park_records)
    save_records(delete_park_path, ctx.one_day_delete_park_records)
    save_high_pv_records(new_high_pv_park_path, ctx.one_day_new_high_pv_park_records)
    save_high_pv_records(delete_high_pv_park_path, ctx.one_day_delete_high_pv_park_records)
    save_4categories_records(delete_4categories_park_path, ctx.one_day_delete_4categories_park_records)

    ctx.day_statistics_data['one_day_new_park_download_url'] = upload_records(ctx, new_park_path)
    ctx.day_statistics_data['one_day_delete_park_download_url'] = upload_records(ctx, delete_park_path)
    ctx.day_statistics_data['one_day_new_high_pv_park_download_url'] = upload_records(ctx, new_high_pv_park_path)
    ctx.day_statistics_data['one_day_delete_high_pv_park_download_url'] = upload_records(ctx, delete_high_pv_park_path)
    ctx.day_statistics_data['one_day_delete_4categories_park_download_url'] = \
    upload_records(ctx, delete_4categories_park_path)

    ctx.one_day_new_gate_records, ctx.one_day_delete_gate_records = \
    diff_data(ctx.latest_gate_records, one_day_ago_gate_records)
    ctx.one_day_new_high_pv_gate_records = get_high_pv_gate(ctx.one_day_new_gate_records)
    ctx.one_day_delete_high_pv_gate_records = get_high_pv_gate(ctx.one_day_delete_gate_records)
    ctx.day_statistics_data['one_day_new_gate_count'] = len(ctx.one_day_new_gate_records)
    ctx.day_statistics_data['one_day_delete_gate_count'] = len(ctx.one_day_delete_gate_records)
    ctx.day_statistics_data['one_day_new_high_pv_gate_count'] = len(ctx.one_day_new_high_pv_gate_records)
    ctx.day_statistics_data['one_day_delete_high_pv_gate_count'] = len(ctx.one_day_delete_high_pv_gate_records)

    new_gate_path = ctx.work_dir / f'{ctx.file_name}_new_gate_all.csv'
    delete_gate_path = ctx.work_dir / f'{ctx.file_name}_delete_gate_all.csv'
    new_high_pv_gate_path = ctx.work_dir / f'{ctx.file_name}_new_high_pv_gate_all.csv'
    delete_high_pv_gatek_path = ctx.work_dir / f'{ctx.file_name}_delete_high_pv_gate_all.csv'

    save_records(new_gate_path, ctx.one_day_new_gate_records)
    save_records(delete_gate_path, ctx.one_day_delete_gate_records)
    save_high_pv_records(new_high_pv_gate_path, ctx.one_day_new_high_pv_gate_records)
    save_high_pv_records(delete_high_pv_gatek_path, ctx.one_day_delete_high_pv_gate_records)

    ctx.day_statistics_data['one_day_new_gate_download_url'] = upload_records(ctx, new_gate_path)
    ctx.day_statistics_data['one_day_delete_gate_download_url'] = upload_records(ctx, delete_gate_path)
    ctx.day_statistics_data['one_day_new_high_pv_gate_download_url'] = upload_records(ctx, new_high_pv_gate_path)
    ctx.day_statistics_data['one_day_delete_high_pv_gate_download_url'] = \
    upload_records(ctx, delete_high_pv_gatek_path)

    proceed()

@desc()
def print_one_day_statistics_data(ctx: Context, proceed):
    """
    打印一天内的统计数据
    """
    if ctx.data_path == '' or check_is_day_statistics(ctx):
        proceed()
        return

    if ctx.execution_time.hour not in (20, 21):  # 只在晚上 20、21 点执行
        proceed()
        return

    for name, value in ctx.day_statistics_data.items():
        print(f'{name}: {value}')

    proceed()

@desc()
def send_one_day_statistics_data_to_infoflow(ctx: Context, proceed):
    """
    发送一天的统计数据到如流
    """
    if ctx.data_path == '' or check_is_day_statistics(ctx):
        proceed()
        return

    if ctx.execution_time.hour not in (20, 21):  # 只在晚上 20、21 点执行
        proceed()
        return

    msg = f'''【停车场小时级监控】
过去 24 小时量级变化如下:
新增停车场: {ctx.day_statistics_data['one_day_new_park_count']}, 
下载地址: {ctx.day_statistics_data['one_day_new_park_download_url']}

删除停车场: {ctx.day_statistics_data['one_day_delete_park_count']}, 
下载地址：{ctx.day_statistics_data['one_day_delete_park_download_url']}

新增高pv停车场: {ctx.day_statistics_data['one_day_new_high_pv_park_count']}, 
下载地址: {ctx.day_statistics_data['one_day_new_high_pv_park_download_url']}

删除高pv停车场: {ctx.day_statistics_data['one_day_delete_high_pv_park_count']}, 
下载地址: {ctx.day_statistics_data['one_day_delete_high_pv_park_download_url']}

删除四大类停车场: {ctx.day_statistics_data['one_day_delete_4categories_park_count']}, 
下载地址: {ctx.day_statistics_data['one_day_delete_4categories_park_download_url']}

新增出入口：{ctx.day_statistics_data['one_day_new_gate_count']}, 
下载地址: {ctx.day_statistics_data['one_day_new_gate_download_url']}

删除出入口：{ctx.day_statistics_data['one_day_delete_gate_count']}, 
下载地址: {ctx.day_statistics_data['one_day_delete_gate_download_url']}

新增高pv出入口: {ctx.day_statistics_data['one_day_new_high_pv_gate_count']}, 
下载地址: {ctx.day_statistics_data['one_day_new_high_pv_gate_download_url']}

删除高pv出入口: {ctx.day_statistics_data['one_day_delete_high_pv_gate_count']}, 
下载地址: {ctx.day_statistics_data['one_day_delete_high_pv_gate_download_url']}
'''
    print(msg)
    user_names, passwords, emails = get_at_user_name_passwork()
    notice_tool.send_hi(msg, atuserids=user_names, token='d8d8bfd0c21fa61a90b63fdc88dc909a8')
    notice_tool.send_hi(msg, None, token='df2c29952bfc58250be8df29534603d77')
    proceed()

@desc()
def save_one_day_statistics_data_to_db(ctx: Context, proceed):
    """
    保存一天内的统计数据到数据库
    """
    if ctx.data_path == '' or check_is_day_statistics(ctx):
        proceed()
        return
    
    insert_sql = '''
        insert into park_gate_data_monitor(name, value, create_time) values (%s, %s, %s);
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        try:
            for name, value in ctx.day_statistics_data.items():
                cur.execute(insert_sql, [name, value, datetime.datetime.now()])

            poi_stab.connection.commit()
        except Exception as e:
            poi_stab.connection.rollback()
            print(e)
            raise e

    proceed()

def check_is_day_statistics(ctx: Context):
    '''
    检查是否执行过天级统计
    '''
    select_sql = '''
        select * from park_gate_data_monitor
        where date(create_time) = (DATE %s) and 
        name = 'one_day_new_park_count';
    '''

    with (
        PgsqlStabilizer(pgsql.POI_CONFIG, init=True) as poi_stab,
        poi_stab.connection.cursor() as cur,
    ):
        today = ctx.execution_time.strftime('%Y-%m-%d')
        result = poi_stab.fetch_one(select_sql, [today,])
        if result and len(result) > 0:
            print(f'reault: {result}')
            return True
        else:
            return False


def alert_exception_to_infoflow(e):
    """
    异常信息如流通知
    """
    notice_tool.send_hi(
        f'停车场数据监控脚本异常！{e}',
        atuserids=['chenjunna',],
        token='df2c29952bfc58250be8df29534603d77'
    )

def get_day_of_year():
    """
    获取当前日期是当年的第几天，返回3天周期内的余数
    """
    today = datetime.date.today()
    day_of_year = today.timetuple().tm_yday
    remainder = day_of_year % 3
    return remainder

def get_at_user_name_passwork():
    """
    获取需要 @ 的人名
    """
    remainder = get_day_of_year()
    if remainder == 0:
        return ['chenjunna',], ['df2c29952bfc58250be8df29534603d77',], ['<EMAIL>',]
    elif remainder == 1:
        return ['qianguoming',], ['VVVUv5mFJ07Sc5hS%2B5IYhSjzpIVVWNYQiyL',], ['<EMAIL>',]
    else:
        return ['liuqingsong_cd',], ['VVVACpgA3wGmT4na1ylamGLkTyNpSXGV%2B0x',], ['<EMAIL>',]

def main():
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        # one hour
        load_one_hour_ago_records,
        download_latest_data,
        load_latest_records,
        diff_one_hour_data,
        upload_one_hour_diff_data,
        print_one_hour_statistics_data,
        save_one_hour_statistics_data_to_db,
        send_one_hour_statistics_data_to_infoflow,
        alert_if_changed_too_much_one_hour,

        # one day
        load_one_day_ago_records,
        print_one_day_statistics_data,
        send_one_day_statistics_data_to_infoflow,
        save_one_day_statistics_data_to_db,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/parking_data_monitor'),
        execution_time=datetime.datetime.now(),
        upload_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei',
        download_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        data_path = ctx.work_dir / f'{ctx.file_name}.csv'
        if os.path.exists(data_path):
            os.remove(data_path)
        alert_exception_to_infoflow(e)

if __name__ == '__main__':
    main()
