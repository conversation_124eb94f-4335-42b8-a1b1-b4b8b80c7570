# !/usr/bin/env python3
"""
封装了 pg 库数据表的备份逻辑
"""
import datetime
import subprocess
from pathlib import Path

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer


class Backup:
    """
    备份表的逻辑
    """

    def __init__(self, **kwargs):
        config = kwargs.get('config')
        table_name = kwargs.get('table_name')

        self.__pg_bin_path = kwargs.get('pg_bin_path', '/home/<USER>/chenbaojun/postgres13/packages/bin')
        self.__pg_config = config
        self.__pg_stab = PgsqlStabilizer(config)
        self.__is_never_backup = True
        self.__expire_day = kwargs.get('expire_day', 8)

        self.__table_name = table_name
        self.__view_name = table_name
        self.__backup_table_name = f"{table_name}_{datetime.date.today().strftime('%Y%m%d')}"

        self.__work_dir = Path('cache/auto_backup_poi')
        self.__work_dir.mkdir(parents=True, exist_ok=True)
        self.__schema_dump_file = self.__work_dir / f'{table_name}_dump.sql'

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.__clear_cache()
        self.__pg_stab.__exit__(exc_type, exc_val, exc_tb)
        pass

    def execute(self):
        """
        执行备份逻辑
        """
        if self.__exists_table(self.__backup_table_name):
            print(f"{self.__backup_table_name} already exists.")
            self.__clear_table(self.__backup_table_name)
            self.__fill_backup_tables()
            self.__update_view()
            self.__drop_expired_table()
            return self.__backup_table_name

        self.__fill_backup_tables()
        self.__dump_schema()
        self.__replace_table_name_in_dump()
        self.__drop_original_table()
        self.__create_new_backup_table()
        self.__analyze_table()
        self.__update_view()
        self.__drop_expired_table()

        return self.__backup_table_name

    def __fill_backup_tables(self):
        """
        填充所有备份表信息
        """
        self.__all_backup_tables = self.__get_all_backup_tables()
        if not self.__all_backup_tables:
            # 当前表从未备份过，则直接使用当前表的规格来创建新的备份表。
            self.__latest_backup_table_name = self.__table_name
        else:
            self.__is_never_backup = False
            self.__latest_backup_table_name = sorted(self.__all_backup_tables, key=lambda x: x[1])[-1][0]

    def __dump_schema(self):
        """
        导出表结构
        """
        command = [
            f"{self.__pg_bin_path}/pg_dump",
            "-h", self.__pg_config['host'],
            "-p", self.__pg_config['port'],
            "-U", self.__pg_config['user'],
            "-t", self.__latest_backup_table_name,
            "-f", str(self.__schema_dump_file),
            "--schema-only",
            self.__pg_config['db'],
        ]
        env = {"PGPASSWORD": self.__pg_config["pwd"]}

        # 执行命令
        try:
            subprocess.run(command, env=env, check=True)
            print(f"备份成功，已保存到 {self.__schema_dump_file}")
        except subprocess.CalledProcessError as e:
            print(f"备份失败: {e}")

    @staticmethod
    def __replace_in_file(file_path, old_str, new_str):
        """
        替换文件中的字符串
        """
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # 替换字符串
        updated_content = content.replace(old_str, new_str)

        # 将更新后的内容写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(updated_content)

    def __replace_table_name_in_dump(self):
        """
        替换文件中的表名
        """
        self.__replace_in_file(self.__schema_dump_file, self.__latest_backup_table_name, self.__backup_table_name)

    def __drop_original_table(self):
        """
        删除原始表
        """
        if not self.__is_never_backup:
            return

        self.__drop_table(self.__table_name)

    def __exists_table(self, table_name):
        """
        判断表是否存在
        """
        sql = f'''
            select exists (
                select 1
                from pg_catalog.pg_tables
                where schemaname = 'public'
                  and tablename = %s
            );
        '''

        return self.__pg_stab.fetch_one(sql, (table_name,))[0]

    def __create_new_backup_table(self):
        """
        创建新备份表
        """
        command = [
            f"{self.__pg_bin_path}/psql",
            "-h", self.__pg_config['host'],
            "-p", self.__pg_config['port'],
            "-U", self.__pg_config['user'],
            "-f", str(self.__schema_dump_file),
            "-d", self.__pg_config['db'],
        ]
        env = {"PGPASSWORD": self.__pg_config["pwd"]}

        # 执行命令
        try:
            subprocess.run(command, env=env, check=True)
            print(f"创建新备份表成功: {self.__backup_table_name}")
        except subprocess.CalledProcessError as e:
            print(f"创建新备份表失败: {e}")

    def __analyze_table(self):
        """
        分析新备份表（帮助 PG 查询优化器生成更高效的执行计划）
        """
        analyze_sql = f'''
            analyze {self.__backup_table_name};
        '''

        self.__pg_stab.execute(analyze_sql)

    def __check_view_table_consistency(self):
        """
        检查视图和表的规格是否一致
        """
        sql = f'''
            (SELECT column_name, data_type
             FROM information_schema.columns
             WHERE table_name = '{self.__backup_table_name}')
            EXCEPT
            (SELECT column_name, data_type
             FROM information_schema.columns
             WHERE table_name = '{self.__view_name}');
        '''

        return not self.__pg_stab.fetch_all(sql)

    def __update_view(self):
        """
        更新数据库视图
        """
        drop_view_sql = f'''
            drop view if exists {self.__view_name};
        '''
        create_view_sql = f'''
            create or replace view {self.__view_name} as
            select * from {self.__backup_table_name};
        '''

        if not self.__check_view_table_consistency():
            self.__pg_stab.execute(drop_view_sql)

        self.__pg_stab.execute(create_view_sql)

    def __get_all_backup_tables(self):
        """
        获取所有备份表信息
        """
        get_all_tables_sql = f'''
            select table_name 
            from information_schema.tables 
            where table_schema = 'public' and table_name like '{self.__table_name}_2%';
        '''

        tables = [(x[0], x[0].split("_")[-1]) for x in self.__pg_stab.fetch_all(get_all_tables_sql)]
        return [(table_name, datetime.datetime.strptime(day, "%Y%m%d")) for table_name, day in tables]

    def __drop_expired_table(self):
        """
        删除过期数据库表
        """
        today = datetime.datetime.now()
        expire = today - datetime.timedelta(days=self.__expire_day)
        expired_tables = [table_name for table_name, day in self.__all_backup_tables if day < expire]
        for table_name in expired_tables:
            self.__drop_table(table_name)

        return expired_tables

    def __drop_table(self, table_name):
        """
        删除数据库表
        """
        self.__pg_stab.execute(f"drop table if exists {table_name};")

    def __clear_table(self, table_name):
        """
        清空数据库表
        """
        self.__pg_stab.execute(f"delete from {table_name};")

    def __clear_cache(self):
        """
        清理缓存
        """
        self.__schema_dump_file.unlink(missing_ok=True)
