# !/usr/bin/env python3
"""
封装了图片过滤的逻辑
"""
import base64
import json
import os
import re
import uuid
from pathlib import Path
from urllib.parse import quote

import requests
from mapio.utils import bns
from retrying import retry

from src.charging_station.helper import get_nested_value
from src.tools import bos_tool
from src.tools.bos_tool import BosClient

BOS_CLIENT = BosClient('picture_server')
IGNORED_KEYS = {
    '地图',
    '图标',
    '设计效果图',
    '屏幕截图',
    '纸币-500欧元',
    '纸币-100欧元',
    '户型图',
    '香烟',
    '相册模板',
    '二维码',
    '矢量字体',
    '文字图片',
    '数学图形',
    '走势图',
    '口罩',
    '显示器屏幕',
    '简笔画',
    '卡通动漫',
    '宣传单',
    '公共标示',
    '圆形图案',
    '说明书',
    '艺术字',
    '手抄报',
    '模糊图片',
}
IGNORED_URLS = {
    'http://wx.evking.cn/assets/img/default-240-360.jpg',
    'https://oms-starcharge-com.oss-cn-qingdao.aliyuncs.com/source/oms/stationImage/rc-upload-1705394305853-28.png',
    'https://comppad.didistatic.com/static/comp-pad/f26b74aff97d9dbe.blob',
    'http://oos-cn.ctyunapi.cn/ptne/image/12345.jpg',
    'https://oms-starcharge-com.oss-cn-qingdao.aliyuncs.com/source/station/01fc991e-b3d8-45ef-a64b-81e644816160_9.jpg',
}


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def __get_response(url):
    """
    调用图片识别接口

    Note:
        For more details, visit https://ku.baidu-int.com/d/vRLdy3C-DuxHKl.
    """
    bns_host = 'group.opera-offline-PoiPicServer-PoiPicServer-all.map-poi.all'
    api = f'http://{bns.get_host_by_bns(bns_host)}/Odyssey2Calculator/calculator_service'
    value = {
        'query_features': ["MassiveClass"],
        'rowkey': 'test_xxx',
        'pic_url': url,
    }

    params = {
        'header': {
            'nid': 'OTU1NDA0ODYtYzc3Mi0xMWViLWE1NDEtN2NkMzBhYjA2ODYw',
            'logid': 2,  # 不确定这是啥意思，反正文档里是这么写的。
            'miner_name': base64.b64encode('MapPoiRichPhotoFeatureExtract'.encode('utf-8')).decode(),
        },
        'data': [
            {
                'parameter': base64.b64encode('base_feature_extract_input'.encode('utf-8')).decode(),
                'value': base64.b64encode(json.dumps(value).encode('utf-8')).decode(),
            },
        ],
    }

    return requests.post(api, json=params)


def __encode_chinese_chars(url):
    """
    将中文字符进行编码
    """
    # 匹配中文字符的正则表达式
    pattern = re.compile(r'[\u4e00-\u9fff]+')
    # 对匹配到的中文字符进行编码
    encoded_url = pattern.sub(lambda x: quote(x.group()), url)
    return encoded_url


def __recognize(url, threshold=0.65):
    """
    识别图片中的元素
    """
    try:
        recognize_types = set()
        response = __get_response(__encode_chinese_chars(url))
        response_json = response.json()

        result = get_nested_value(response_json, ['data', 0, 'value'])
        result_json = json.loads(base64.b64decode(result))
        massive_class_str = get_nested_value(result_json, ['features', 'MassiveClass'])
        for item in json.loads(massive_class_str).get('result', []):
            if item.get("class_score", 0) >= threshold:
                recognize_types.add(item['class_name'])

        return True, recognize_types
    except Exception as e:
        print(url)
        return False, {str(e)}


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def __calc_size(url):
    """
    计算图片大小
    """
    try:
        response = requests.get(f'http://10.212.41.171:9000/picture/size?url={url}')
        response_json = response.json()
        code = response_json['code']
        if code != 0:
            return -1, -1, -1

        data = response_json['data']

        return data['width'], data['height'], data['size']
    except Exception as e:
        print(e)
        return -1, -1, -1


def __upload_file_to_bos(content, mime):
    """
    将图片上传到 BOS
    """
    temp_folder_path = Path(os.getcwd()) / 'cache' / 'picture_filter'
    temp_folder_path.mkdir(parents=True, exist_ok=True)
    temp_file_path = temp_folder_path / f'{uuid.uuid4().hex}.{mime}'
    temp_file_name = temp_file_path.name
    temp_file_path.write_bytes(content)
    url = BOS_CLIENT.put_object_from_file(
        key=temp_file_name,
        file_path=str(temp_file_path),
    )
    temp_file_path.unlink(missing_ok=True)
    return url, temp_file_name


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def __compress(url):
    """
    压缩图片
    """
    try:
        response = requests.get(f'http://10.212.41.171:9000/picture/compress?url={url}')
        content_type = response.headers.get('Content-Type')
        if 'image' not in content_type:
            return None, ''

        mime = content_type.split('/')[1]
        return response.content, mime
    except Exception as e:
        print(e)
        return None, ''


def __ensure_valid_size(url):
    """
    确保图片大小不超过阈值
    """
    max_size = 2 * 1024 * 1024

    width, height, size = __calc_size(url)
    if size <= max_size:
        return True, url, None

    compressed_content, mime = __compress(url)
    if compressed_content is not None and len(compressed_content) <= max_size:
        compressed_url, key = __upload_file_to_bos(compressed_content, mime)
        return True, compressed_url, key

    return False, url, None


def __try_delete_picture_from_bos(url, key):
    """
    从 BOS 中删除指定图片
    """
    if bos_tool.API not in url:
        return

    BOS_CLIENT.delete_object(key=key)


def filter_before_online(url, force=False):
    """
    判断图片是否可以推送上线
    """
    if url in IGNORED_URLS:
        return False, 'url ignored'

    valid, valid_url, key = __ensure_valid_size(url)
    if not valid:
        return False, 'invalid image size'

    success, recognize_results = __recognize(valid_url)
    __try_delete_picture_from_bos(valid_url, key)
    if not success:
        return False, f'error during recognition: {list(recognize_results)[0]}'

    if not recognize_results:
        return False, 'no results after recognition'

    if not force and any([x for x in IGNORED_KEYS if x in recognize_results]):
        return False, 'ignored keys found during recognition'

    return True, ';'.join(recognize_results)
