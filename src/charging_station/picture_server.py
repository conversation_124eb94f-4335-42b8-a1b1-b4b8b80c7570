# !/usr/bin/env python3
"""
图片服务
"""
import urllib.request
import uuid
from pathlib import Path

import requests

from PIL import Image
from io import BytesIO

from PIL.Image import Resampling
from flask import Flask, request, jsonify, send_file
from retrying import retry

HOST = '0.0.0.0'
PORT = 8999
CACHE_DIR = Path('cache/picture_server')

app = Flask(__name__)


def error(msg):
    """
    错误响应
    """
    return jsonify({
        'code': -1,
        'msg': msg,
        'data': None,
    })


def success(data):
    """
    成功响应
    """
    return jsonify({
        'code': 0,
        'msg': '',
        'data': data,
    })


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def download_file(url, save_path: Path):
    """
    使用 http 协议下载文件。
    """
    urllib.request.urlretrieve(url, save_path)


@app.route('/')
def api_default():
    """
    默认路由
    """
    return 'Hello, World!'


@app.route('/picture/size')
def api_picture_size():
    """
    计算图片大小接口
    """
    try:
        url = request.args.get('url', None)
        if url is None:
            return error("url is required")

        response = requests.get(url)
        img = Image.open(BytesIO(response.content))
        return success({'size': len(response.content), 'width': img.width, 'height': img.height})
    except Exception as e:
        print(e)
        return error(str(e))


def compress_picture(picture_path):
    """
    压缩图片
    """
    quality = 85
    png_compress_level = 6
    optimize = True
    max_width = 1280

    if picture_path is None or not picture_path.exists():
        return None

    with Image.open(picture_path) as img:
        original_format = img.format
        original_width, original_height = img.size

        if original_width > max_width:
            new_height = int((max_width / original_width) * original_height)
            img = img.resize((max_width, new_height), Resampling.LANCZOS)

        if original_format == 'JPEG':
            # 对于 JPG 格式，使用质量参数进行压缩
            output_path = CACHE_DIR / f'{picture_path.name}.jpg'
            img.save(output_path, 'JPEG', quality=quality)
            return output_path

        if original_format == 'PNG':
            # 对于 PNG 格式，使用压缩级别和优化选项进行压缩
            output_path = CACHE_DIR / f'{picture_path.name}.png'
            img = img.convert('RGB')
            img.save(output_path, 'PNG', compress_level=png_compress_level, optimize=optimize)
            return output_path

    return picture_path


@app.route('/picture/compress')
def api_picture_compress():
    """
    压缩图片接口
    """
    original_path = CACHE_DIR / uuid.uuid4().hex
    compressed_path = None

    def delete_files():
        if original_path is not None:
            original_path.unlink(missing_ok=True)
        if compressed_path is not None:
            compressed_path.unlink(missing_ok=True)

    try:
        url = request.args.get('url', None)
        if url is None:
            return error("url is required")

        download_file(url, original_path)
        compressed_path = compress_picture(original_path)

        if compressed_path is None:
            return error("unsupported image format")

        return send_file(compressed_path, as_attachment=True)
    except Exception as e:
        print(e)
        return error(str(e))
    finally:
        delete_files()


if __name__ == '__main__':
    CACHE_DIR.mkdir(parents=True, exist_ok=True)
    app.run(host=HOST, port=PORT)
