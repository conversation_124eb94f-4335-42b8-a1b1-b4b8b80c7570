# !/usr/bin/env python3
"""
随机抽取充电站
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

import requests
from retrying import retry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv, notice_tool

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    充电站信息
    """
    bid: str
    address: str
    relation_bid: int
    name: str
    show_tag: bool
    status: int
    online_type: int
    wkt: str
    relation_name: str = ''


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    count: int
    upload_file_url: str
    download_file_url: str
    records: list[Record] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)


@desc()
def check_args(ctx: Context, proceed):
    """
    检查参数
    """
    if ctx.count <= 0:
        raise ValueError("count must be positive")

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载充电站信息
    """
    sql = '''
        select bid, address, relation_bid, name, show_tag, status, online_type, st_astext(geometry)
        from cdz_history
        order by random()
        limit %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for item in tqdm(poi_stab.fetch_all(sql, [ctx.count])):
            bid, address, relation_bid, name, show_tag, status, online_type, wkt = item
            ctx.records.append(Record(
                bid=bid,
                address=address,
                relation_bid=relation_bid,
                name=name,
                show_tag=show_tag,
                status=status,
                online_type=online_type,
                wkt=wkt,
            ))

    proceed()


@desc()
def fill_relation_name(ctx: Context, proceed):
    """
    填充父点名称
    """
    sql = '''
        select name from poi where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm(ctx.records):
            row = poi_stab.fetch_one(sql, [record.relation_bid])
            if row is None:
                continue

            record.relation_name = row[0]

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            [
                x.bid,
                x.name,
                x.wkt,
                x.show_tag,
                x.relation_bid,
                x.relation_name,
                x.status,
                '' if x.online_type == 1 else '不对外开放',
            ]
            for x in ctx.records
        ]
    )
    proceed()


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(upload_file_url, local_file_path):
    """
    上传文件
    """
    files = {'file': open(local_file_path, 'r', encoding='utf-8')}
    file_uuid = requests.post(upload_file_url, files=files).text
    if len(file_uuid) != 32:
        raise Exception(f'upload file failed')

    return file_uuid


@desc()
def send_records_to_infoflow(ctx: Context, proceed):
    """
    推送记录到如流
    """

    def upload_records(local_path: Path):
        if not local_path.exists():
            return None

        file_uuid = upload_file(ctx.upload_file_url, local_path)
        return f'{ctx.download_file_url}&uuid={file_uuid}'

    remote_url = upload_records(ctx.work_dir / 'output.csv')
    if remote_url is None:
        proceed()
        return

    notice_tool.send_hi(
        f'充电桩随机 {ctx.count} 条数据：{remote_url}',
        atuserids=['songhuiling'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )
    proceed()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--count',
        dest='count',
        type=int,
        required=True,
    )
    return parser.parse_args()


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'随机抽取充电站脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d2ab0b311ae2d9a6faa0d0a4e79100707'
    )


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        check_args,
        load_records,
        fill_relation_name,
        save_records,
        send_records_to_infoflow,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/random_sample'),
        count=args.count,
        upload_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei',
        download_file_url='http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile',
    )

    try:
        main_pipe(ctx)
    except Exception as e:
        alert_to_infoflow(e)


if __name__ == '__main__':
    main(parse_args())
