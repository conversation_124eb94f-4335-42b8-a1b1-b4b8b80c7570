# !/usr/bin/env python3
"""
包含一些召回策略的复用逻辑
"""
import argparse
import datetime
from functools import wraps

import numpy as np
import shapely.wkt
from matplotlib import pyplot as plt
from scipy.stats import gaussian_kde
from shapely import GeometryCollection, MultiPoint, Point, Polygon
from sklearn.cluster import DBSCAN

from src.charging_station.auto_repair_mixin import get_piles
from src.fix_geom_process import tools


class ContourAnalyzer:
    """
    用于分析密度分布的类
    """

    def __init__(self, multipoint):
        """
        初始化并预处理数据
        """
        self.points = self._convert_multipoint(multipoint)

        try:
            self.kde = self._calculate_kde()
            self.X, self.Y, self.Z = self._create_density_grid()
        except:
            self.kde = None

    @staticmethod
    def _convert_multipoint(multipoint):
        """
        将 MultiPoint 转换为 numpy 数组
        """
        return np.array([[p.x, p.y] for p in multipoint.geoms])

    def _calculate_kde(self):
        """
        计算核密度估计
        """
        return gaussian_kde(self.points.T, bw_method='scott')

    # noinspection SpellCheckingInspection,PyPep8Naming
    def _create_density_grid(self, grid_size=200, buffer_ratio=0.1):
        """
        创建密度评估网格
        """
        xmin, ymin = self.points.min(axis=0)
        xmax, ymax = self.points.max(axis=0)

        xbuffer = (xmax - xmin) * buffer_ratio
        ybuffer = (ymax - ymin) * buffer_ratio

        xgrid = np.linspace(xmin - xbuffer, xmax + xbuffer, grid_size)
        ygrid = np.linspace(ymin - ybuffer, ymax + ybuffer, grid_size)

        X, Y = np.meshgrid(xgrid, ygrid)
        Z = self.kde(np.vstack([X.ravel(), Y.ravel()])).reshape(X.shape)

        return X, Y, Z

    def _generate_contour_levels(self, levels):
        """
        生成等高线层级
        """
        if isinstance(levels, int):
            percentiles = np.linspace(0, 100, levels + 2)[1:-1]
            return np.percentile(self.Z, percentiles)

        return np.array(levels)

    def _find_target_level(self, target, contour_levels):
        """
        定位目标点所在的等高线层级
        """
        target_lon, target_lat = target
        sorted_levels = np.sort(contour_levels)[::-1]  # 从高到低检查

        for index, level in enumerate(sorted_levels):
            contours = plt.contour(self.X, self.Y, self.Z, levels=[level])
            plt.close()

            if self._check_point_in_contours(contours, target_lon, target_lat):
                return index, level == sorted_levels[0]

        return len(contour_levels), False

    def _get_contour_polygons(self, levels: np.ndarray):
        """
        获取所有等高线的多边形集合
        """
        contours = plt.contour(self.X, self.Y, self.Z, levels=levels)
        plt.close()
        polygons = []

        for level, collection in zip(contours.levels, contours.collections):
            for path in collection.get_paths():
                for coords in path.to_polygons():
                    if len(coords) >= 3:
                        polygons.append(Polygon(coords))

        return polygons

    @staticmethod
    def _check_point_in_contours(contours, lon, lat):
        """
        检查点是否在等高线集合内
        """
        for collection in contours.collections:
            for path in collection.get_paths():
                if path.contains_point([lon, lat]):
                    return True

        return False

    def find_contour_level_for_target(self, target_point, levels=10):
        """
        查找目标点所在的等高线层级
        """
        if self.kde is None:
            return levels, {}

        try:
            contour_levels = self._generate_contour_levels(levels)
            all_polygons = self._get_contour_polygons(contour_levels)
            x, y = target_point.x, target_point.y
            target_level, is_highest = self._find_target_level((x, y), contour_levels)
        except:
            return levels, {}

        contour_info = {
            'levels': contour_levels,
            'min_density': float(self.Z.min()),
            'max_density': float(self.Z.max()),
            'target_in_highest': is_highest,
            'all_polygons': [x.wkt for x in all_polygons],
        }

        return target_level, contour_info


def feature(feature_id):
    """
    标记特征 id，用于过滤。
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            ctx, proceed = args
            feature_list = ctx.feature_list
            if not feature_list:
                func(*args, **kwargs)
                return

            if feature_id not in feature_list:
                proceed()
                return

            func(*args, **kwargs)

        return wrapper

    return decorator


def get_outer_roads(road_stab, wkt):
    """
    获取外部路
    """
    sql = '''
        select link_id, dir, kind, lane_l, lane_r, form, st_astext(geom) 
        from nav_link
        where kind < 8 and 
              st_intersects(st_buffer(st_geomfromtext(%s, 4326), 20e-5), geom);
    '''
    ret = road_stab.fetch_all(sql, (wkt,))
    return [(*x[:-1], shapely.wkt.loads(x[-1])) for x in ret]


def buffer_roads(roads):
    """
    对道路线进行外扩形成面
    """
    buffered_roads = []
    for _, direction, kind, lane_l, lane_r, form, geom in roads:
        line_width = tools.get_road_width(direction, kind, lane_l, lane_r, form)
        line_buffer = line_width * 1e-5 / 2
        line = geom.buffer(line_buffer)
        buffered_roads.append(line)

    return GeometryCollection(buffered_roads).buffer(0)


def cluster_points(multipoint: MultiPoint, eps=10e-5, min_samples=1):
    """
    对点进行聚类
    """
    if len(multipoint.geoms) == 1:
        return list(multipoint.geoms)  # 直接返回唯一的点

    points_arr = np.array([point.coords[0] for point in multipoint.geoms])
    # noinspection PyUnresolvedReferences
    labels = DBSCAN(eps=eps, min_samples=min_samples).fit(points_arr).labels_
    clustered_points = {}
    for label, point in zip(labels, multipoint.geoms):
        if label == -1:
            continue
        points_group = clustered_points.get(label, [])
        points_group.append(point)
        clustered_points[label] = points_group

    max_count = 0
    max_count_label = -1
    if not any(clustered_points):
        return []

    for key in clustered_points.keys():
        count = len(clustered_points[key])
        if count > max_count:
            max_count_label = key
            max_count = count

    return clustered_points[max_count_label]


def filter_dest_tracks_by_road(road_stab, links=None, points=None):
    """
    过滤掉压路的终点轨迹
    """
    valid_points = []
    actual_points = [Point(link.coords[-1]) for link in links] if links is not None else points
    if actual_points is None:
        return valid_points

    for point in actual_points:
        roads = get_outer_roads(road_stab, point.wkt)
        buffered_roads = buffer_roads(roads)
        if buffered_roads.intersects(point):
            continue

        valid_points.append(point)

    return valid_points


def get_pile_status(tp_cur, bid):
    """
    获取桩状态
    """
    all_piles = get_piles(tp_cur, bid)
    all_update_time = [update_time for _, update_time in all_piles]
    all_status = [status for status, _ in all_piles]
    last_update_time = max(all_update_time) if all_update_time else datetime.datetime.min
    is_all_pile_broken = all(x == 0 or x == 255 for x in all_status) if all_status else True
    frozen_days = (datetime.date.today() - last_update_time.date()).days

    return is_all_pile_broken, frozen_days, last_update_time


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['db', 'file', 'bid'],
        default='bid',
        required=False,
    )
    parser.add_argument(
        '--method',
        dest='method',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--data-type',
        dest='data_type',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--data-path',
        dest='data_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--bids',
        dest='bids',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--wkt-list',
        dest='wkt_list',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--feature-list',
        dest='feature_list',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--cities',
        dest='cities',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--debug',
        dest='debug',
        default=False,
        action='store_true',
    )
    return parser.parse_args()
