# !/usr/bin/env python3
"""
召回充电站坐标错误情报
"""
from dataclasses import dataclass, field
from pathlib import Path

import mapio.utils.coord
import mcpack
import shapely.wkt
from mapio.utils import bns
from nshead import nshead
from retrying import retry
from shapely import MultiPoint
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.auto_repair_mixin import get_all_charging_station_bids, get_pois_by_bid
from src.charging_station.data import Poi
from src.charging_station.recall_error_mixin import (
    cluster_points,
    parse_args,
    filter_dest_tracks_by_road,
    feature,
    ContourAnalyzer
)
from src.tools import pipeline, pgsql, tsv
from src.tools.track_provider import get_provider

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    召回记录
    """
    poi: Poi

    # 轨迹聚合
    track_points: list = field(default_factory=list)
    track_clustered_geom: BaseGeometry = None
    track_clustered_distance: float = 0.0
    track_contour_level: int = 10

    # 熄火点相关
    parking_points: list = field(default_factory=list)
    parking_contour_level: int = 10

    # 业务逻辑
    can_process: bool = True
    reason: str = ''
    recalled: bool = False
    src: str = ''

    @property
    def can_recall(self):
        return self.can_process and not self.recalled


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    mode: str
    data_path: Path
    debug: bool
    bids: list[str] = field(default_factory=list)
    wkt_list: list[str] = field(default_factory=list)
    feature_list: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    cities: set[str] = field(default_factory=set)
    dest_track_type: str = 'dest'

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        (self.work_dir / 'output.csv').unlink(missing_ok=True)


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载充电站 bid 集合
    """
    if ctx.mode == 'file':
        ctx.bids = list(set([x[0] for x in tsv.read_tsv(ctx.data_path)]))
    elif ctx.mode == 'db':
        ctx.bids = get_all_charging_station_bids()

    if not ctx.bids:
        raise ValueError("no bids")

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载召回记录
    """
    if not ctx.wkt_list:
        ctx.records = [
            Record(poi=x)
            for x in get_pois_by_bid(ctx.bids).values() if filter_poi(ctx, x)
        ]
    else:
        ctx.records = [
            Record(poi=Poi(bid=bid, geom=shapely.wkt.loads(wkt)))
            for bid, wkt in zip(ctx.bids, ctx.wkt_list)
        ]

    proceed()


@desc()
def load_track_points(ctx: Context, proceed):
    """
    加载数据库终点轨迹
    """
    with (
        get_provider(ctx.dest_track_type) as track_provider,
        PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab,
    ):
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            tracks = track_provider.get_tracks(record.poi.bid)
            geoms = [shapely.wkt.loads(x['geom']) for x in tracks]
            record.track_points = filter_dest_tracks_by_road(road_stab, links=geoms)

    proceed()


@desc()
def cluster_track_points(ctx: Context, proceed):
    """
    对轨迹点进行聚类
    """
    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if not record.track_points:
            continue

        all_points = MultiPoint(record.track_points)
        clustered_geom = MultiPoint(cluster_points(all_points)).convex_hull
        record.track_clustered_geom = clustered_geom

    proceed()


@desc()
def load_parking_points(ctx: Context, proceed):
    """
    加载熄火点
    """
    radius = 50e-5
    sql = '''
        select st_astext(geom)
        from parking_points
        where st_dwithin(st_geomfromtext(%s, 4326), geom, %s)
    '''

    with (
        PgsqlStabilizer(pgsql.TRAJ_FEATURE) as traj_stab,
        PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab,
    ):
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            geoms = []

            for wkt, in traj_stab.fetch_all(sql, (record.poi.geom.wkt, radius)):
                geoms.append(shapely.wkt.loads(wkt))

            record.parking_points = filter_dest_tracks_by_road(road_stab, points=geoms)

    proceed()


@desc()
def cluster_parking_points(ctx: Context, proceed):
    """
    对熄火点进行聚类
    """
    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if not record.parking_points:
            continue

        all_points = MultiPoint(record.parking_points)
        target_level, info = ContourAnalyzer(all_points).find_contour_level_for_target(record.poi.geom)
        record.parking_contour_level = target_level

    proceed()


@desc()
def rollback_to_api_dest_track(ctx: Context, proceed):
    """
    回滚到 api 端轨迹
    """
    ctx.dest_track_type = 'dest-api'
    proceed()


@desc()
def output_failed_records(ctx: Context, proceed):
    """
    输出召回失败的记录
    """
    for record in tqdm([x for x in ctx.records if not x.recalled]):
        print(record.poi.bid)

    proceed()


@feature('location_01')
@desc()
def recall_error_01(ctx: Context, proceed):
    """
    召回 【location_01】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    min_distance = 30
    max_distance = 100
    parking_min_contour_level = 2

    for record in tqdm([x for x in ctx.records if x.can_recall]):
        # 轨迹聚合相关逻辑
        if record.track_clustered_geom is None or record.track_clustered_geom.is_empty:
            continue
        if record.track_clustered_geom.geom_type != "Polygon":
            continue
        if record.track_clustered_geom.contains(record.poi.geom):
            continue

        clustered_centroid_geom = record.track_clustered_geom.centroid
        record.track_clustered_distance = record.poi.geom.distance(clustered_centroid_geom) * 1.1e5
        if record.track_clustered_distance < min_distance or record.track_clustered_distance > max_distance:
            continue

        # 热力等高线相关逻辑
        if record.parking_contour_level < parking_min_contour_level:
            if record.track_contour_level > record.parking_contour_level:
                continue

        record.recalled, record.src = True, 'location_01'
        output_record(ctx, record)

    proceed()


@feature('location_02')
@desc()
def recall_error_02(ctx: Context, proceed):
    """
    召回 【location_02】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    min_distance = 100

    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if record.track_clustered_geom is None or record.track_clustered_geom.is_empty:
            continue

        if record.track_clustered_geom.geom_type != "Polygon":
            continue

        if record.track_clustered_geom.contains(record.poi.geom):
            continue

        clustered_centroid_geom = record.track_clustered_geom.centroid
        record.track_clustered_distance = record.poi.geom.distance(clustered_centroid_geom) * 1.1e5
        if record.track_clustered_distance <= min_distance:
            continue

        record.recalled, record.src = True, 'location_02'
        output_record(ctx, record)

    proceed()


@feature('location_03')
@desc()
def recall_error_03(ctx: Context, proceed):
    """
    召回 【location_03】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    min_distance = 30
    max_distance = 500
    parking_min_contour_level = 2

    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if record.track_clustered_geom is None or record.track_clustered_geom.is_empty:
            continue

        if record.track_clustered_geom.geom_type == "Polygon":
            continue

        clustered_centroid_geom = record.track_clustered_geom.centroid
        record.track_clustered_distance = record.poi.geom.distance(clustered_centroid_geom) * 1.1e5
        if record.track_clustered_distance <= min_distance or record.track_clustered_distance > max_distance:
            continue

        if record.parking_contour_level < parking_min_contour_level:
            continue

        record.recalled, record.src = True, 'location_03'
        output_record(ctx, record)

    proceed()


@feature('location_04')
@desc()
def recall_error_04(ctx: Context, proceed):
    """
    召回 【location_04】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    min_distance = 30

    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if record.track_clustered_geom is None or record.track_clustered_geom.is_empty:
            continue

        if record.track_clustered_geom.geom_type != "Polygon":
            continue

        if not record.track_clustered_geom.contains(record.poi.geom):
            continue

        clustered_centroid_geom = record.track_clustered_geom.centroid
        record.track_clustered_distance = record.poi.geom.distance(clustered_centroid_geom) * 1.1e5
        if record.track_clustered_distance <= min_distance:
            continue

        record.recalled, record.src = True, 'location_04'
        output_record(ctx, record)

    proceed()


@feature('location_05')
@desc()
def recall_error_05(ctx: Context, proceed):
    """
    召回 【location_05】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    track_min_distance = 30
    parking_min_contour_level = 2
    parking_max_contour_level = 10

    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if record.track_clustered_geom is None or record.track_clustered_geom.is_empty:
            continue

        track_clustered_centroid_geom = record.track_clustered_geom.centroid
        record.track_clustered_distance = record.poi.geom.distance(track_clustered_centroid_geom) * 1.1e5

        if (
            record.parking_contour_level < parking_max_contour_level and
            record.track_clustered_distance <= track_min_distance
        ):
            continue

        if record.parking_contour_level < parking_min_contour_level:
            continue

        record.recalled, record.src = True, 'location_05'
        output_record(ctx, record)

    proceed()


@feature('location_06')
@desc()
def recall_error_06(ctx: Context, proceed):
    """
    召回 【location_06】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    # todo: 特征不聚集，暂不开发。
    proceed()


@feature('location_07')
@desc()
def recall_error_07(ctx: Context, proceed):
    """
    召回 【location_07】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    # todo: 特征不聚集，暂不开发。
    proceed()


@feature('location_08')
@desc()
def recall_error_08(ctx: Context, proceed):
    """
    召回 【location_08】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    for record in tqdm([x for x in ctx.records if x.can_recall]):
        bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(record.poi.geom.x, record.poi.geom.y)
        mc_x, mc_y = mapio.utils.coord.bd09_to_mercator(bd_x, bd_y)

        try:
            overlap, _ = check_on_road(mc_x, mc_y)
        except:
            overlap = False

        if not overlap:
            continue

        record.recalled, record.src = True, 'location_08'
        output_record(ctx, record)

    proceed()


@feature('location_09')
@desc()
def recall_error_09(ctx: Context, proceed):
    """
    召回 【location_09】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    # todo: 特征不聚集，暂不开发。
    proceed()


# -----------------------------------
# Helper functions
# -----------------------------------


def filter_poi(ctx: Context, poi: Poi):
    """
    过滤 poi
    """
    if ctx.cities and poi.city not in ctx.cities:
        return False

    return True


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def check_on_road(x, y):
    """
    压路落水检测服务调用（异常后重试8次）
    :param: x, y (float): 百度墨卡托坐标
    :return 压路落水: True, new_point; 不压路落水: False, None
    """
    bns_str = "group.opera-online-PoiStreetService-ArchServiceAoi-all.map-poi.all"
    query = {
        "type": 0,
        "x": x,
        "y": y
    }
    ip, port = bns.get_host_by_bns(bns_str).split(':')
    nc_client = nshead.NsheadClient(ip, int(port))
    nc_client.send(mcpack.dumps(query, 'v2', 'gbk'))
    recv_data = nc_client.recv()
    result = mcpack.loads(recv_data, 'v2', 'gbk')

    if result.get("errcode") == 1 or result.get("errcode") == -1:
        return False, None
    if result.get("x") and result["x"] == 0 and result["y"] == 0:  # 不压路落水情况
        return False, None
    else:
        new_point = float(result["x"]), float(result["y"])
        return True, new_point


def output_record(ctx: Context, record: Record):
    """
    输出记录
    """
    result = [
        record.poi.bid,
        record.poi.geom.wkt,

        record.track_clustered_geom.wkt if record.track_clustered_geom is not None else '',
        record.track_clustered_geom.centroid.wkt if record.track_clustered_geom is not None else '',
        record.track_clustered_distance,
        record.track_contour_level,

        record.parking_contour_level,

        record.src,
    ]

    if ctx.debug:
        print('\n'.join([str(x) for x in result]))
    else:
        tsv.write_tsv(ctx.work_dir / 'output.csv', [result], mode='a')


def create_pipeline(args):
    """
    创建策略执行管道
    """
    # 各特征召回手段
    recall_methods = [
        recall_error_01,
        recall_error_02,
        recall_error_03,
        recall_error_04,
        recall_error_05,
        recall_error_06,
        recall_error_08,
        recall_error_09,
    ]

    pipes = [
        load_bids,
        load_records,
        load_track_points,
        cluster_track_points,
        load_parking_points,
        cluster_parking_points,

        *recall_methods,

        rollback_to_api_dest_track,
        load_track_points,
        cluster_track_points,

        *recall_methods,
    ]

    if args.debug:
        pipes.append(output_failed_records)

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/recall_location_error"),
        mode=args.mode,
        data_path=Path(args.data_path),
        bids=args.bids,
        wkt_list=args.wkt_list,
        feature_list=args.feature_list,
        cities=args.cities,
        debug=args.debug,
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
