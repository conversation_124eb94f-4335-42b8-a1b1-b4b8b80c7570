# !/usr/bin/env python3
"""
召回充电站缺失错误情报
"""
import argparse
from dataclasses import dataclass, field
from pathlib import Path

import rtree
import shapely.wkt
from rtree import Index
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pipeline, pgsql, tsv

SEARCH_RADIUS = 100e-5

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    召回记录
    """
    competitor: tuple = None
    can_process: bool = True
    reason: str = ''
    recalled: bool = False
    src: str = ''

    @property
    def can_recall(self):
        """
        是否可以召回
        """
        return self.can_process and not self.recalled


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    mode: str
    data_path: Path
    debug: bool
    competitor_ids: list[str] = field(default_factory=list)
    wkt_list: list[str] = field(default_factory=list)
    feature_list: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    cities: set[str] = field(default_factory=set)
    rtree_index: Index = rtree.index.Index(interleaved=True)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        (self.work_dir / 'output.csv').unlink(missing_ok=True)


@desc()
def create_cdz_rtree(ctx: Context, proceed):
    """
    创建充电站 rtree
    """
    sql = '''
        select st_astext(geometry) from cdz_history;
    '''

    count = 0
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for wkt, in tqdm(stab.fetch_all(sql)):
            geom = shapely.wkt.loads(wkt)
            ctx.rtree_index.insert(count, geom.bounds, obj=(geom,))
            count += 1

    proceed()


@desc()
def load_competitor_ids(ctx: Context, proceed):
    """
    加载竞品 id 集合
    """
    if ctx.mode == 'file':
        ctx.competitor_ids = list(set([x[0] for x in tsv.read_tsv(ctx.data_path)]))
    elif ctx.mode == 'db':
        ctx.competitor_ids = get_all_competitor_ids()

    if not ctx.competitor_ids:
        raise ValueError("no competitor ids")

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载召回记录
    """
    sql = '''
        select name, address, city, st_astext(geom)
        from cdz_competitor 
        where competitor_id = %s and
              crawl_time >= extract(epoch from (now() - interval '1 months'))::integer
        order by crawl_time desc
        limit 1;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for competitor_id in tqdm(ctx.competitor_ids):
            row = stab.fetch_one(sql, (competitor_id,))
            if row is None:
                continue

            name, address, city, wkt = row
            competitor_geom = shapely.wkt.loads(wkt)
            if ctx.cities and city not in ctx.cities:
                continue

            if not intersects_cdz(ctx.rtree_index, competitor_geom):
                ctx.records.append(Record(competitor=(competitor_id, name, address, city, wkt)))

    proceed()


@desc()
def save_records(ctx: Context, proceed):
    """
    保存召回记录
    """
    tsv.write_tsv(
        ctx.work_dir / 'output.csv',
        [
            x.competitor
            for x in ctx.records
        ]
    )

    proceed()


# -----------------------------------
# Helper functions
# -----------------------------------


def intersects_cdz(cdz_rtree, competitor_geom):
    """
    判断竞品是否与充电站相交
    """
    competitor_buffered_geom = competitor_geom.buffer(SEARCH_RADIUS)
    intersection_items = list(cdz_rtree.intersection(competitor_buffered_geom.bounds, objects=True))
    if not any(intersection_items):
        return False

    contains = False

    for item in intersection_items:
        cdz_geom, = item.object
        if competitor_buffered_geom.contains(cdz_geom):
            contains = True
            break

    return contains


def get_all_competitor_ids():
    """
    获取所有竞品 id
    """
    sql = '''
        select distinct competitor_id 
        from cdz_competitor
        where crawl_time >= extract(epoch from (now() - interval '1 months'))::integer;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        return [x[0] for x in stab.fetch_all(sql)]


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['db', 'file', 'competitor_id'],
        default='competitor_id',
        required=False,
    )
    parser.add_argument(
        '--data-path',
        dest='data_path',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--competitor_ids',
        dest='competitor_ids',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--cities',
        dest='cities',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--feature-list',
        dest='feature_list',
        nargs='+',
        type=str,
        required=False,
    )
    parser.add_argument(
        '--debug',
        dest='debug',
        default=False,
        action='store_true',
    )
    return parser.parse_args()


def main(args):
    """
    主函数
    """
    main_pipe = pipeline.Pipeline(
        create_cdz_rtree,
        load_competitor_ids,
        load_records,
        save_records,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/recall_missing_error"),
        mode=args.mode,
        data_path=Path(args.data_path),
        competitor_ids=args.competitor_ids,
        feature_list=args.feature_list,
        cities=args.cities,
        debug=args.debug,
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
