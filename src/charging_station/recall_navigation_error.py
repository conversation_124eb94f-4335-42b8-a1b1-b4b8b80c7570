# !/usr/bin/env python3
"""
召回充电站引导错误情报
"""
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from shapely import affinity, Point, LineString, Polygon
from shapely.ops import linemerge
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.auto_repair_mixin import get_all_charging_station_bids, get_pois_by_bid, get_poi_by_bid
from src.charging_station.data import Poi
from src.charging_station.recall_error_mixin import parse_args, feature
from src.tools import pipeline, pgsql, tsv
from src.tools.track_provider import get_provider

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    召回记录
    """
    poi: Poi
    overlap_aoi_geom: shapely.geometry.Polygon = None
    navigation_geom: shapely.geometry.linestring = None
    navigation_last_point: Point = None
    has_end_walk: bool = False
    inner_navigation_geom: shapely.geometry.linestring = None
    can_process: bool = True
    reason: str = ''
    recalled: bool = False
    src: str = ''

    @property
    def can_recall(self):
        """
        是否可以召回
        """
        return self.can_process and not self.recalled


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    mode: str
    data_path: Path
    data_type: str
    debug: bool
    bids: list[str] = field(default_factory=list)
    wkt_list: list[str] = field(default_factory=list)
    feature_list: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    cities: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        (self.work_dir / 'output.csv').unlink(missing_ok=True)


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载充电站 bid 集合
    """
    if ctx.mode == 'file':
        ctx.bids = list(set([x[0] for x in tsv.read_tsv(ctx.data_path)]))
    elif ctx.mode == 'db':
        ctx.bids = get_all_charging_station_bids()

    if not ctx.bids:
        print('no bid')
        return

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载召回记录
    """
    if not ctx.wkt_list:
        ctx.records = [
            Record(poi=x)
            for x in get_pois_by_bid(ctx.bids).values() if filter_poi(ctx, x)
        ]
    else:
        ctx.records = [
            Record(poi=Poi(bid=bid, geom=shapely.wkt.loads(wkt)))
            for bid, wkt in zip(ctx.bids, ctx.wkt_list)
        ]

    proceed()


@desc()
def load_navigation_geom_by_api(ctx: Context, proceed):
    """
    通过 API 加载导航轨迹
    """
    offset = 500e-5

    with get_provider('nav-drive') as track_provider:
        for record in tqdm(ctx.records):
            offset_geom = affinity.translate(record.poi.geom, xoff=offset, yoff=offset)
            track_info = track_provider.get_tracks(
                region=record.poi.bid,
                origin_wkt=offset_geom.wkt,
            )

            if track_info is None:
                record.can_process = False
                record.reason = 'no navigation track'
                continue

            wkt, has_end_walk = track_info['wkt'], track_info['has_end_walk']
            record.navigation_geom = shapely.wkt.loads(wkt)
            record.navigation_last_point = Point(record.navigation_geom.coords[-1])
            record.has_end_walk = has_end_walk

    proceed()


@desc()
def load_navigation_geom_by_file(ctx: Context, proceed):
    """
    通过文件加载导航轨迹
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for bid, wkt in tqdm([x for x in tsv.read_tsv(ctx.data_path)]):
            poi = get_poi_by_bid(poi_stab, bid)
            if poi is None:
                continue

            record = Record(poi=poi, navigation_geom=shapely.wkt.loads(wkt))
            record.navigation_last_point = Point(record.navigation_geom.coords[-1])
            ctx.records.append(record)

    proceed()


@desc()
def load_overlap_aoi(ctx: Context, proceed):
    """
    加载压盖的 AOI
    """
    sql = '''
        select b.poi_bid, st_astext(a.geom)
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where st_intersects(a.geom, st_geomfromtext(%s, 4326)) and
              a.src != 'SQ' and
              a.kind != '52' and
              a.aoi_level = 2
        limit 1;
    '''

    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stab:
        for record in tqdm([x for x in ctx.records if x.can_process]):
            row = back_stab.fetch_one(sql, [record.poi.geom.wkt])
            if row is None:
                record.can_process = False
                record.reason = 'no overlap aoi'
            else:
                _, wkt = row
                record.overlap_aoi_geom = shapely.wkt.loads(wkt)

    proceed()


@feature('navigation_01')
@desc()
def recall_error_01(ctx: Context, proceed):
    """
    召回 【navigation_01】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if not record.has_end_walk:
            continue

        record.recalled, record.src = True, 'navigation_01'
        output_record(ctx, record)

    proceed()


@feature('navigation_02')
@desc()
def recall_error_02(ctx: Context, proceed):
    """
    召回 【navigation_02】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    with PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab:
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            if get_overlap_road_face(road_stab, record.poi.geom) is not None:
                record.recalled, record.src = True, 'navigation_02'
                output_record(ctx, record)

    proceed()


@feature('navigation_03')
@desc()
def recall_error_03(ctx: Context, proceed):
    """
    召回 【navigation_03】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    for record in tqdm([x for x in ctx.records if x.can_recall]):
        inner_navigation_geom = record.navigation_geom.intersection(record.overlap_aoi_geom)

        if inner_navigation_geom.geom_type != 'LineString':
            continue

        if inner_navigation_geom.is_simple:
            continue

        record.recalled, record.src = True, 'navigation_03'
        output_record(ctx, record)

    proceed()


# -----------------------------------
# Helper functions
# -----------------------------------

def filter_poi(ctx: Context, poi: Poi):
    """
    过滤 poi
    """
    if ctx.cities and poi.city not in ctx.cities:
        return False

    return True


def enclose_marking_pl_list(links):
    """
    将多条 marking_pl 围合成一个多边形
    """
    link1 = links[0]
    link2 = LineString([x.coords[0] for x in links])
    link3 = links[-1]
    link4 = LineString([x.coords[-1] for x in links])

    return Polygon(linemerge([link1, link2, link3, link4]).coords)


def get_overlap_nav_lane_face(road_stab, poi_geom):
    """
    获取点压盖的 nav_lane 构成的面
    """
    search_radius = 1.1 * 10e-5
    get_lane_group_id_sql = '''
        select distinct lane_group_id 
        from nav_lane_link
        where st_dwithin(st_geomfromtext(%s, 4326), geom, %s);
    '''
    get_boundary_list_sql = '''
        select boundary_list
        from nav_lane_group
        where lane_group_id = %s;
    '''
    get_lane_marking_pl_wkt_sql = '''
        select st_astext(geom)
        from nav_lane_marking_pl
        where marking_pl_id in %s
        order by group_num_seq;
    '''

    for lane_group_id, in road_stab.fetch_all(get_lane_group_id_sql, [poi_geom.wkt, search_radius]):
        for boundary_list, in road_stab.fetch_all(get_boundary_list_sql, [lane_group_id]):
            links = []
            for wkt, in road_stab.fetch_all(get_lane_marking_pl_wkt_sql, [tuple(boundary_list.split(','))]):
                links.append(LineString([(x, y) for x, y, _ in shapely.wkt.loads(wkt).coords]))

            face = enclose_marking_pl_list(links)
            if face.contains(poi_geom):
                return face

    return None


def get_overlap_nav_lane_road_pg_face(road_stab, poi_geom):
    """
    获取点压盖的 nav_lane_road_pg 构成的面
    """
    search_radius = 1.1 * 10e-5
    sql = '''
        select st_astext(geom)
        from nav_lane_road_pg
        where st_dwithin(st_geomfromtext(%s, 4326), geom, %s);
    '''

    for wkt, in road_stab.fetch_all(sql, [poi_geom.wkt, search_radius]):
        face = shapely.wkt.loads(wkt)
        if face.contains(poi_geom):
            return face

    return None


def get_overlap_road_face(road_stab, poi_geom):
    """
    获取点压盖的道路面
    """
    face = get_overlap_nav_lane_face(road_stab, poi_geom)
    return face if face is not None else get_overlap_nav_lane_road_pg_face(road_stab, poi_geom)


def output_record(ctx: Context, record: Record):
    """
    输出记录
    """
    result = [
        record.poi.bid,
        record.poi.name,
        record.poi.show_tag,
        record.poi.geom.wkt,
        record.navigation_geom.wkt if record.navigation_geom else '',
        record.navigation_last_point.wkt if record.navigation_last_point else '',
        record.navigation_last_point.distance(record.poi.geom) * 1e5 if record.navigation_last_point else '',
        record.poi.arrive_geom.wkt,

        record.src,
    ]

    if ctx.debug:
        print('\n'.join([str(x) for x in result]))
    else:
        tsv.write_tsv(ctx.work_dir / 'output.csv', [result], mode='a')


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = []

    if args.data_type == 'bid':
        pipes.extend([
            load_bids,
            load_records,
            load_navigation_geom_by_api,
        ])
    elif args.data_type == 'track':
        pipes.extend([
            load_navigation_geom_by_file,
        ])

    pipes.extend([
        load_overlap_aoi,

        recall_error_01,
        recall_error_02,
        recall_error_03,
    ])

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/recall_navigation_error"),
        mode=args.mode,
        data_path=Path(args.data_path),
        data_type=args.data_type,
        bids=args.bids,
        wkt_list=args.wkt_list,
        cities=args.cities,
        debug=args.debug,
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
