# !/usr/bin/env python3
"""
召回充电站开放属性错误情报
"""
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from shapely import Point, unary_union
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.auto_repair_mixin import get_pois_by_bid, get_all_charging_station_bids
from src.charging_station.data import Poi
from src.charging_station.recall_error_mixin import parse_args, feature
from src.tools import pipeline, tsv, pgsql
from src.tools.track_provider import get_provider

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    召回记录
    """
    poi: Poi
    is_public: bool = False
    overlap_aoi: tuple = None
    inner_buffered_poi_geom: BaseGeometry = None
    related_link_ids: list[str] = field(default_factory=list)
    didi_track_info: tuple = None
    dest_track_info: tuple = None
    can_process: bool = True
    reason: str = ''
    recalled: bool = False
    src: str = ''

    @property
    def can_recall(self):
        return self.can_process and not self.recalled


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    mode: str
    data_path: Path
    debug: bool
    data_type: str = ''
    bids: list[str] = field(default_factory=list)
    wkt_list: list[str] = field(default_factory=list)
    feature_list: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    cities: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        (self.work_dir / 'output.csv').unlink(missing_ok=True)


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载充电站 bid 集合
    """
    if ctx.mode == 'file':
        ctx.bids = list(set([x[0] for x in tsv.read_tsv(ctx.data_path)]))
    elif ctx.mode == 'db':
        ctx.bids = get_all_charging_station_bids()

    if not ctx.bids:
        raise ValueError("no bids")

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载召回记录
    """
    if not ctx.wkt_list:
        ctx.records = [
            Record(poi=x)
            for x in get_pois_by_bid(ctx.bids).values() if filter_poi(ctx, x)
        ]
    else:
        ctx.records = [
            Record(poi=Poi(bid=bid, geom=shapely.wkt.loads(wkt)))
            for bid, wkt in zip(ctx.bids, ctx.wkt_list)
        ]

    proceed()


@desc()
def fill_ownership(ctx: Context, proceed):
    """
    填充开放属性
    """
    if ctx.data_type != '':
        for record in ctx.records:
            record.is_public = True if ctx.data_type == 'public' else False
    else:
        sql = '''
            select online_type from cdz_history where bid = %s;
        '''
        with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
            for record in tqdm(ctx.records):
                row = stab.fetch_one(sql, [record.poi.bid])
                if row is None:
                    record.can_process = False
                    record.reason = 'bid not found'
                    continue

                online_type, = row
                record.is_public = True if online_type == 1 else False

    proceed()


@desc()
def fill_overlap_aoi(ctx: Context, proceed):
    """
    填充压盖的 AOI
    """
    buffer_radius = 30e-5

    with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stab:
        for record in tqdm([x for x in ctx.records if x.can_process]):
            record.overlap_aoi = get_overlap_aoi(back_stab, record.poi.geom.wkt)
            buffered_poi_geom = record.poi.geom.buffer(buffer_radius)

            if record.overlap_aoi is None:
                record.overlap_aoi = '', buffered_poi_geom

            _, aoi_geom = record.overlap_aoi
            record.inner_buffered_poi_geom = buffered_poi_geom.intersection(aoi_geom)

    proceed()


@desc()
def fill_related_link_ids(ctx: Context, proceed):
    """
    填充关联道路 link ids
    """
    with PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab:
        for record in tqdm([x for x in ctx.records if x.can_process]):
            _, aoi_geom = record.overlap_aoi
            record.related_link_ids = get_related_nav_link_ids(road_stab, aoi_geom, record.poi.geom)

    proceed()


@feature('ownership_01')
@desc()
def recall_error_01(ctx: Context, proceed):
    """
    召回 【ownership_01】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    proceed()


@feature('ownership_02')
@desc()
def recall_error_02(ctx: Context, proceed):
    """
    召回 【ownership_02】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    didi_min_count = 5

    with get_provider('drive') as track_provider:
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            if record.is_public:
                continue

            ensure_didi_track_info(record, track_provider)
            didi_arrival_count, _, didi_through_count, _ = record.didi_track_info
            if didi_arrival_count < didi_min_count and didi_through_count < didi_min_count:
                continue

            ensure_dest_track_info(record, track_provider)
            dest_arrival_count, _, dest_through_count, _ = record.dest_track_info
            if dest_arrival_count == 0 or dest_through_count == 0:
                continue

            record.recalled, record.src = True, 'ownership_02'
            output_record(ctx, record)

    proceed()


@feature('ownership_03')
@desc()
def recall_error_03(ctx: Context, proceed):
    """
    召回 【ownership_03】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    min_ratio = 10

    def divide(numerator, denominator):
        epsilon = 1e-10
        if denominator < epsilon:
            if numerator < epsilon:
                return 0
            else:
                return min_ratio
        else:
            return numerator / denominator

    with get_provider('drive') as track_provider:
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            if not record.is_public:
                continue

            ensure_didi_track_info(record, track_provider)
            ensure_dest_track_info(record, track_provider)
            didi_arrival_count, didi_arrival_area, didi_through_count, didi_through_area = record.didi_track_info
            dest_arrival_count, dest_arrival_area, dest_through_count, dest_through_area = record.dest_track_info
            arrival_count_ratio = divide(dest_arrival_count, didi_arrival_count)
            arrival_area_ratio = divide(dest_arrival_area, didi_arrival_area)
            through_count_ratio = divide(dest_through_count, didi_through_count)
            through_area_ratio = divide(dest_through_area, didi_through_area)

            if arrival_count_ratio < min_ratio and through_count_ratio < min_ratio:
                continue

            if arrival_area_ratio < min_ratio and through_area_ratio < min_ratio:
                continue

            record.recalled, record.src = True, 'ownership_03'
            output_record(ctx, record)

    proceed()


@feature('ownership_04')
@desc()
def recall_error_04(ctx: Context, proceed):
    """
    召回 【ownership_04】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    min_track_count = 5

    with get_provider('dest') as track_provider:
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            ensure_didi_track_info(record, track_provider)
            ensure_dest_track_info(record, track_provider)
            didi_arrival_count, didi_arrival_area, didi_through_count, didi_through_area = record.didi_track_info
            dest_arrival_count, dest_arrival_area, dest_through_count, dest_through_area = record.dest_track_info

            if didi_arrival_area > 0 or didi_through_count > 0 or dest_arrival_count > 0 or dest_through_count > 0:
                continue

            tracks = track_provider.get_tracks(record.poi.bid)
            if len(tracks) < min_track_count:
                continue

            record.recalled, record.src = True, 'ownership_04'
            output_record(ctx, record)

    proceed()


@feature('ownership_05')
@desc()
def recall_error_05(ctx: Context, proceed):
    """
    召回 【ownership_05】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    proceed()


@feature('ownership_06')
@desc()
def recall_error_06(ctx: Context, proceed):
    """
    召回 【ownership_06】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    proceed()


@feature('ownership_07')
@desc()
def recall_error_07(ctx: Context, proceed):
    """
    召回 【ownership_07】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    proceed()


@feature('ownership_08')
@desc()
def recall_error_08(ctx: Context, proceed):
    """
    召回 【ownership_08】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    proceed()


# -----------------------------------
# Helper functions
# -----------------------------------


def filter_poi(ctx: Context, poi: Poi):
    """
    过滤 poi
    """
    if '服务区' in poi.name:
        return False

    if ctx.cities and poi.city not in ctx.cities:
        return False

    return True


def output_record(ctx: Context, record: Record):
    """
    输出记录
    """
    didi_arrival_count, didi_arrival_area, didi_through_count, didi_through_area = record.didi_track_info
    dest_arrival_count, dest_arrival_area, dest_through_count, dest_through_area = record.dest_track_info

    result = [
        record.poi.bid,
        didi_arrival_count,
        dest_arrival_count,
        didi_through_count,
        dest_through_count,
        round(didi_arrival_area * 1e10, 2),
        round(dest_arrival_area * 1e10, 2),
        round(didi_through_area * 1e10, 2),
        round(dest_through_area * 1e10, 2),
        round(record.inner_buffered_poi_geom.area * 1e10, 2) if record.inner_buffered_poi_geom is not None else 0,
        record.src,
    ]

    if ctx.debug:
        print('\n'.join([str(x) for x in result]))
    else:
        tsv.write_tsv(ctx.work_dir / 'output.csv', [result], mode='a')


def get_overlap_aoi(back_stab, wkt):
    """
    获取压盖的 AOI
    """
    sql = '''
        select b.poi_bid, st_astext(a.geom)
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where st_intersects(a.geom, st_geomfromtext(%s, 4326)) and
              a.src != 'SQ' and
              a.kind != '52' and
              a.aoi_level = 2
        limit 1;
    '''

    row = back_stab.fetch_one(sql, [wkt])
    if row is None:
        return None

    bid, wkt = row
    return bid, shapely.wkt.loads(wkt)


def get_related_nav_link_ids(road_stab, aoi_geom, target_point):
    """
    获取关联道路 link ids
    """
    sql = '''
        select link_id
        from nav_link
        where st_intersects(st_buffer(st_geomfromtext(%s, 4326) , 100e-5), geom) and
              st_intersects(st_geomfromtext(%s, 4326), geom);
    '''

    return [x[0] for x in road_stab.fetch_all(sql, [target_point.wkt, aoi_geom.wkt])]


def get_track_geoms(track_provider, src, link_ids):
    """
    获取轨迹
    """
    user_ids = set()
    track_geoms = []

    for link_id in link_ids:
        for track in track_provider.get_tracks(link_id, src=src):
            user_id = track['cuid']
            if user_id not in user_ids:
                user_ids.add(user_id)
                track_geoms.append(shapely.wkt.loads(track['wkt']))

    return track_geoms


def extract_valid_geoms(record: Record, track_geoms):
    """
    提取有效轨迹点
    """
    _, aoi_geom = record.overlap_aoi
    arrival_geoms = []
    through_geoms = []

    for track_geom in track_geoms:

        if not aoi_geom.contains(Point(track_geom.coords[0])) and not aoi_geom.contains(Point(track_geom.coords[-1])):
            continue

        if aoi_geom.contains(Point(track_geom.coords[0])) and aoi_geom.contains(Point(track_geom.coords[-1])):
            continue

        if record.inner_buffered_poi_geom.intersects(Point(track_geom.coords[-1])):
            arrival_geoms.append(track_geom)

        if record.inner_buffered_poi_geom.intersects(track_geom):
            through_geoms.append(track_geom)

    return arrival_geoms, through_geoms


def get_intersected_track_geom_area(track_geoms, inner_buffered_poi_geom):
    """
    计算轨迹相交面积
    """
    buffer_radius = 1e-5

    # 0. 提取凸多边形的外接矩形边界（快速预筛选）
    min_x, min_y, max_x, max_y = inner_buffered_poi_geom.bounds

    # 1. 直接遍历线段，用外接矩形快速过滤
    intersecting_lines = []
    for line in track_geoms:
        # 检查线段的外接矩形是否与多边形的外接矩形相交
        line_min_x, line_min_y, line_max_x, line_max_y = line.bounds
        if not (line_max_x < min_x or line_min_x > max_x or
                line_max_y < min_y or line_min_y > max_y):
            # 精确检查线段与凸多边形是否相交
            if line.intersects(inner_buffered_poi_geom):
                intersecting_lines.append(line)

    # 2. 对相交线段逐个缓冲并合并（非并行）
    buffers = [line.buffer(buffer_radius) for line in intersecting_lines]

    # 3. 分块合并结果（避免一次性合并大内存）
    chunk_size = 1000  # 根据内存调整
    combined_geom = unary_union(
        [unary_union(buffers[i:i + chunk_size])
         for i in range(0, len(buffers), chunk_size)]
    )

    # 4. 计算最终相交面积
    return combined_geom.intersection(inner_buffered_poi_geom).area


def get_track_info(track_provider, src, record: Record):
    """
    获取轨迹信息
    """
    track_geoms = get_track_geoms(track_provider, src, record.related_link_ids)
    arrival_geoms, through_geoms = extract_valid_geoms(record, track_geoms)

    intersected_arrival_geom_area = get_intersected_track_geom_area(arrival_geoms, record.inner_buffered_poi_geom)
    intersected_through_geom_area = get_intersected_track_geom_area(through_geoms, record.inner_buffered_poi_geom)

    return len(arrival_geoms), intersected_arrival_geom_area, len(through_geoms), intersected_through_geom_area


def ensure_didi_track_info(record: Record, track_provider):
    """
    确保存在滴滴轨迹信息
    """
    didi_src = 4

    if record.didi_track_info is not None:
        return

    if record.overlap_aoi is None:
        record.didi_track_info = 0, 0, 0, 0
        return

    try:
        record.didi_track_info = get_track_info(track_provider, didi_src, record)
    except Exception as e:
        print(e)
        record.didi_track_info = 0, 0, 0, 0


def ensure_dest_track_info(record: Record, track_provider):
    """
    确保存在终点轨迹信息
    """
    dest_src = 3

    if record.dest_track_info is not None:
        return

    if record.overlap_aoi is None:
        record.dest_track_info = 0, 0, 0, 0
        return

    try:
        record.dest_track_info = get_track_info(track_provider, dest_src, record)
    except Exception as e:
        print(e)
        record.dest_track_info = 0, 0, 0, 0


def create_pipeline():
    """
    创建策略执行管道
    """
    pipes = [
        load_bids,
        load_records,
        fill_ownership,
        fill_overlap_aoi,
        fill_related_link_ids,

        # 各特征召回手段
        recall_error_01,
        recall_error_02,
        recall_error_03,
        recall_error_04,
        recall_error_05,
        recall_error_06,
        recall_error_07,
        recall_error_08,
    ]

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline()
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/recall_ownership_error"),
        mode=args.mode,
        data_path=Path(args.data_path),
        bids=args.bids,
        wkt_list=args.wkt_list,
        feature_list=args.feature_list,
        cities=args.cities,
        debug=args.debug,
        data_type=args.data_type,
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
