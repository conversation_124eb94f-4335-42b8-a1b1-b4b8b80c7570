# !/usr/bin/env python3
"""
召回充电站冗余错误情报
"""
from dataclasses import dataclass, field
from pathlib import Path

import rtree
import shapely.wkt
from rtree import Index
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.auto_repair_mixin import (
    get_pois_by_bid,
    get_max_name_iou_competitor_by_poi,
    get_all_charging_station_bids,
    get_all_tp_bids,
)
from src.charging_station.data import Poi, SPACING_UNDERGROUND_DISPLAY_NAME
from src.charging_station.recall_error_mixin import parse_args, get_pile_status, feature
from src.tools import pipeline, tsv, pgsql
from src.tools.track_provider import get_provider

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    召回记录
    """
    poi: Poi
    is_connected: bool = False
    clustered_geom: BaseGeometry = None
    clustered_distance: float = 0
    competitor: tuple = None
    nearest_tp: tuple = None
    recognition_result: bool = False
    tracks: list = field(default_factory=list)
    pile_status: tuple = None
    can_process: bool = True
    reason: str = ''
    recalled: bool = False
    src: str = ''

    @property
    def can_recall(self):
        return self.can_process and not self.recalled


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    mode: str
    data_path: Path
    debug: bool
    bids: list[str] = field(default_factory=list)
    wkt_list: list[str] = field(default_factory=list)
    feature_list: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    cities: set[str] = field(default_factory=set)
    rtree_index: Index = field(default=None)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        (self.work_dir / 'output.csv').unlink(missing_ok=True)

    def is_all_connected(self):
        return all(x.is_connected for x in self.records)


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载充电站 bid 集合
    """
    if ctx.mode == 'file':
        ctx.bids = list(set([x[0] for x in tsv.read_tsv(ctx.data_path)]))
    elif ctx.mode == 'db':
        ctx.bids = get_all_charging_station_bids()

    if not ctx.bids:
        raise ValueError("no bids")

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载召回记录
    """
    if not ctx.wkt_list:
        ctx.records = [
            Record(poi=x)
            for x in get_pois_by_bid(ctx.bids).values() if filter_poi(ctx, x)
        ]
    else:
        ctx.records = [
            Record(poi=Poi(bid=bid, geom=shapely.wkt.loads(wkt)))
            for bid, wkt in zip(ctx.bids, ctx.wkt_list)
        ]

    proceed()


@desc()
def fill_is_connected(ctx: Context, proceed):
    """
    填充挂接状态
    """
    tp_bids = set(get_all_tp_bids())

    for record in tqdm(ctx.records):
        record.is_connected = record.poi.bid in tp_bids

    proceed()


@desc()
def create_tp_rtree(ctx: Context, proceed):
    """
    创建 tp 空间索引
    """
    if ctx.is_all_connected():
        proceed()
        return

    sql = '''
        select id, thirdcode, thirdname, station_lng, station_lat from charging_station;
    '''
    rtree_index = rtree.index.Index(interleaved=True)

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        cur.execute(sql)

        for tp_id, thirdcode, thirdname, station_lng, station_lat in tqdm(cur.fetchall()):
            wkt = f'POINT({station_lng} {station_lat})'
            geom = shapely.wkt.loads(wkt)
            rtree_index.insert(tp_id, geom.bounds, obj=(tp_id, thirdcode, thirdname, geom))

        ctx.rtree_index = rtree_index

    proceed()


@desc()
def fill_competitor(ctx: Context, proceed):
    """
    填充竞品
    """
    search_radius = 1000e-5

    with (
        PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab,
        PgsqlStabilizer(pgsql.ROAD_CONFIG) as road_stab,
    ):
        for record in tqdm([x for x in ctx.records if x.can_process]):
            matched_competitor, _ = get_max_name_iou_competitor_by_poi(
                poi_stab,
                road_stab,
                record.poi,
                search_radius,
            )

            if matched_competitor is not None:
                record.competitor = (
                    matched_competitor.name,
                    matched_competitor.geom.wkt,
                    matched_competitor.geom.distance(record.poi.geom) * 1.1e5,
                )
            else:
                record.competitor = '', '', search_radius * 1.1e5

    proceed()


@desc()
def fill_nearest_tp(ctx: Context, proceed):
    """
    填充最近的 tp
    """
    if ctx.is_all_connected():
        proceed()
        return

    search_radius = 1000e-5

    for record in tqdm([x for x in ctx.records if x.can_process]):
        distance = search_radius
        nearest_tp = '', '', '', '', search_radius * 1.1e5

        for item in ctx.rtree_index.intersection(record.poi.geom.buffer(search_radius).bounds, objects=True):
            tp_id, thirdcode, thirdname, tp_geom = item.object
            current_distance = tp_geom.distance(record.poi.geom)
            if current_distance < distance:
                distance = current_distance
                nearest_tp = (tp_id, thirdcode, thirdname, tp_geom.wkt, current_distance * 1.1e5)

        record.nearest_tp = nearest_tp

    proceed()


@desc()
def fill_recognition_result(ctx: Context, proceed):
    """
    加载图片识别结果
    """
    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm([x for x in ctx.records if x.can_process]):
            record.recognition_result = exists_picture_recognition_result(poi_stab, record.poi.bid)

    proceed()


@feature('redundancy_00')
@desc()
def recall_error_00(ctx: Context, proceed):
    """
    附近 100m 无竞品
    """
    for record in tqdm([x for x in ctx.records if x.can_recall]):
        record.recalled, record.src = True, 'redundancy_00'
        output_record(ctx, record)

    proceed()


@feature('redundancy_01')
@desc()
def recall_error_01(ctx: Context, proceed):
    """
    召回 【redundancy_01】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    distance_threshold = 100
    track_count_threshold = 5

    with get_provider('dest') as track_provider:
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            if record.is_connected:
                continue

            competitor_name, competitor_wkt, competitor_distance = record.competitor
            if competitor_distance < distance_threshold:
                continue

            tp_id, third_code, third_name, tp_wkt, tp_distance = record.nearest_tp
            if tp_distance < distance_threshold:
                continue

            ensure_tracks(record, track_provider)
            if len(record.tracks) >= track_count_threshold:
                continue

            record.recalled, record.src = True, 'redundancy_01'
            output_record(ctx, record)

    proceed()


@feature('redundancy_02')
@desc()
def recall_error_02(ctx: Context, proceed):
    """
    召回 【redundancy_02】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    track_count_threshold = 20
    days_threshold = 30

    with (
        get_provider('dest') as track_provider,
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            if not record.is_connected:
                continue

            if record.recognition_result:
                continue

            ensure_pile_status(record, cur)
            is_all_pile_broken, frozen_days, last_update_time = record.pile_status
            if frozen_days < days_threshold or not is_all_pile_broken:
                continue

            ensure_tracks(record, track_provider)
            if len(record.tracks) >= track_count_threshold:
                continue

            record.recalled, record.src = True, 'redundancy_02'
            output_record(ctx, record)

    proceed()


@feature('redundancy_03')
@desc()
def recall_error_03(ctx: Context, proceed):
    """
    召回 【redundancy_03】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    track_count_threshold = 20
    days_threshold = 80

    with (
        get_provider('dest') as track_provider,
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            if not record.is_connected:
                continue

            if record.recognition_result:
                continue

            ensure_pile_status(record, cur)
            is_all_pile_broken, frozen_days, last_update_time = record.pile_status
            if frozen_days < days_threshold:
                continue

            ensure_tracks(record, track_provider)
            if len(record.tracks) >= track_count_threshold:
                continue

            record.recalled, record.src = True, 'redundancy_03'
            output_record(ctx, record)

    proceed()


@feature('redundancy_05')
@desc()
def recall_error_05(ctx: Context, proceed):
    """
    召回 【redundancy_05】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    days_threshold = 10

    with (
        get_provider('dest') as track_provider,
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            if not record.is_connected:
                continue

            if record.poi.show_tag != SPACING_UNDERGROUND_DISPLAY_NAME:
                continue

            if record.recognition_result:
                continue

            ensure_pile_status(record, cur)
            is_all_pile_broken, frozen_days, last_update_time = record.pile_status
            if frozen_days < days_threshold:
                continue

            ensure_tracks(record, track_provider)
            if any(record.tracks):
                continue

            record.recalled, record.src = True, 'redundancy_05'
            output_record(ctx, record)

    proceed()


@feature('redundancy_06')
@desc()
def recall_error_06(ctx: Context, proceed):
    """
    召回 【redundancy_06】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    days_threshold = 80

    with (
        get_provider('dest') as track_provider,
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            if not record.is_connected:
                continue

            if record.poi.show_tag != SPACING_UNDERGROUND_DISPLAY_NAME:
                continue

            if record.recognition_result:
                continue

            ensure_pile_status(record, cur)
            is_all_pile_broken, frozen_days, last_update_time = record.pile_status
            if frozen_days < days_threshold:
                continue

            ensure_tracks(record, track_provider)
            if not any(record.tracks):
                continue

            record.recalled, record.src = True, 'redundancy_06'
            output_record(ctx, record)

    proceed()


@feature('redundancy_07')
@desc()
def recall_error_07(ctx: Context, proceed):
    """
    召回 【redundancy_07】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    distance_threshold = 10

    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if record.is_connected:
            continue

        competitor_name, competitor_wkt, competitor_distance = record.competitor
        if competitor_distance >= distance_threshold:
            continue

        tp_id, third_code, third_name, tp_wkt, tp_distance = record.nearest_tp
        if tp_distance >= distance_threshold:
            continue

        record.recalled, record.src = True, 'redundancy_07'
        output_record(ctx, record)

    proceed()


# -----------------------------------
# Helper functions
# -----------------------------------


def filter_poi(ctx: Context, poi: Poi):
    """
    过滤 poi
    """
    if '服务区' in poi.name:
        return False

    if ctx.cities and poi.city not in ctx.cities:
        return False

    return True


def output_record(ctx: Context, record: Record):
    """
    输出记录
    """
    if record.competitor is None:
        competitor_name, competitor_wkt, competitor_distance = '', '', ''
    else:
        competitor_name, competitor_wkt, competitor_distance = record.competitor

    if record.nearest_tp is None:
        tp_id, third_code, third_name, tp_wkt, tp_distance = '', '', '', '', ''
    else:
        tp_id, third_code, third_name, tp_wkt, tp_distance = record.nearest_tp

    result = [
        record.poi.bid,
        len(record.tracks),
        record.poi.geom.wkt,

        competitor_name,
        competitor_wkt,
        competitor_distance,

        tp_id,
        third_code,
        third_name,
        tp_wkt,
        tp_distance,

        record.src,
    ]

    if ctx.debug:
        print('\n'.join([str(x) for x in result]))
    else:
        tsv.write_tsv(ctx.work_dir / 'output.csv', [result], mode='a')


def exists_picture_recognition_result(poi_stab, bid):
    """
    判断是否存在图片识别结果
    """
    sql = '''
        select result from cdz_picture_recognition where bid = %s;
    '''
    ignored_keys = [
        'url ignored',
        'invalid image size',
        'error during recognition',
        'no results after recognition',
        'ignored keys found during recognition',
        'the JSON object must be str',
    ]

    for result, in poi_stab.fetch_all(sql, (bid,)):
        if all(key not in result for key in ignored_keys):
            return True

    return False


def ensure_tracks(record: Record, track_provider):
    """
    确保轨迹数据存在
    """
    if any(record.tracks):
        return

    record.tracks = track_provider.get_tracks(record.poi.bid)


def ensure_pile_status(record: Record, tp_cur):
    """
    确保桩状态存在
    """
    if record.pile_status is not None:
        return

    record.pile_status = get_pile_status(tp_cur, record.poi.bid)


def create_pipeline(args):
    """
    创建策略执行管道
    """
    pipes = [
        load_bids,
        load_records,
        fill_is_connected,
        fill_competitor,
        create_tp_rtree,
        fill_nearest_tp,
        fill_recognition_result,

        # 各特征召回手段
        recall_error_01,
        recall_error_02,
        recall_error_03,
        recall_error_05,
        recall_error_06,
        recall_error_07,
    ]

    if args.feature_list is not None and 'redundancy_00' in args.feature_list:
        pipes.append(recall_error_00)

    return pipeline.Pipeline(*pipes)


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = create_pipeline(args)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/recall_redundancy_error"),
        mode=args.mode,
        data_path=Path(args.data_path),
        bids=args.bids,
        wkt_list=args.wkt_list,
        feature_list=args.feature_list,
        cities=args.cities,
        debug=args.debug,
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
