# !/usr/bin/env python3
"""
召回充电站状态错误情报
"""
import datetime
from dataclasses import dataclass, field
from pathlib import Path

import shapely.wkt
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.auto_repair_mixin import (
    get_pois_by_bid,
    get_pile_last_update_time,
    get_tp_last_pull_time,
    get_all_charging_station_bids,
)
from src.charging_station.data import Poi
from src.charging_station.recall_error_mixin import parse_args, feature
from src.tools import pipeline, tsv, pgsql

desc = pipeline.get_desc()


@dataclass
class Record:
    """
    召回记录
    """
    poi: Poi
    is_online: bool = False
    track_points: list = field(default_factory=list)
    pile_last_update_time: datetime = None
    tp_last_pull_time: datetime = None
    can_process: bool = True
    reason: str = ''
    recalled: bool = False
    src: str = ''

    @property
    def can_recall(self):
        return self.can_process and not self.recalled


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    mode: str
    data_path: Path
    debug: bool
    bids: list[str] = field(default_factory=list)
    wkt_list: list[str] = field(default_factory=list)
    feature_list: list[str] = field(default_factory=list)
    records: list[Record] = field(default_factory=list)
    cities: set[str] = field(default_factory=set)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)
        (self.work_dir / 'output.csv').unlink(missing_ok=True)


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载充电站 bid 集合
    """
    if ctx.mode == 'file':
        ctx.bids = list(set([x[0] for x in tsv.read_tsv(ctx.data_path)]))
    elif ctx.mode == 'db':
        ctx.bids = get_all_charging_station_bids()

    if not ctx.bids:
        raise ValueError("no bids")

    proceed()


@desc()
def load_records(ctx: Context, proceed):
    """
    加载召回记录
    """
    if not ctx.wkt_list:
        ctx.records = [
            Record(poi=x)
            for x in get_pois_by_bid(ctx.bids).values() if filter_poi(ctx, x)
        ]
    else:
        ctx.records = [
            Record(poi=Poi(bid=bid, geom=shapely.wkt.loads(wkt)))
            for bid, wkt in zip(ctx.bids, ctx.wkt_list)
        ]

    proceed()


@desc()
def fill_status(ctx: Context, proceed):
    """
    填充状态
    """
    status_online = 1
    sql = '''
        select status from poi where bid = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        for record in tqdm(ctx.records):
            row = poi_stab.fetch_one(sql, (record.poi.bid,))
            if row is None:
                record.can_process = False
                record.reason = "bid not found"
                continue

            status, = row
            record.is_online = True if status == status_online else False

    proceed()


@desc()
def fill_time_props(ctx: Context, proceed):
    """
    填充时间属性
    """
    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        for record in tqdm([x for x in ctx.records if x.can_recall]):
            record.pile_last_update_time = get_pile_last_update_time(cur, record.poi.bid)
            record.tp_last_pull_time = get_tp_last_pull_time(cur, record.poi.bid)

    proceed()


@feature('status_01')
@desc()
def recall_error_01(ctx: Context, proceed):
    """
    召回 【status_01】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    days_threshold = 80

    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if not record.is_online:
            continue

        if record.pile_last_update_time == datetime.datetime.min:
            continue

        days = (datetime.date.today() - record.pile_last_update_time.date()).days
        if days <= days_threshold:
            continue

        record.recalled, record.src = True, 'status_01'
        output_record(ctx, record)

    proceed()


@feature('status_02')
@desc()
def recall_error_02(ctx: Context, proceed):
    """
    召回 【status_02】 特征，详见：https://ku.baidu-int.com/d/U6sqKlN-3LL5zL
    """
    days_threshold = 2

    for record in tqdm([x for x in ctx.records if x.can_recall]):
        if record.is_online:
            continue

        if record.pile_last_update_time == datetime.datetime.min:
            continue

        days = (datetime.date.today() - record.pile_last_update_time.date()).days
        if days > days_threshold:
            continue

        record.recalled, record.src = True, 'status_02'
        output_record(ctx, record)

    proceed()


# -----------------------------------
# Helper functions
# -----------------------------------


def filter_poi(ctx: Context, poi: Poi):
    """
    过滤 poi
    """
    if '服务区' in poi.name:
        return False

    if ctx.cities and poi.city not in ctx.cities:
        return False

    return True


def output_record(ctx: Context, record: Record):
    """
    输出记录
    """
    result = [
        record.poi.bid,
        (datetime.date.today() - record.pile_last_update_time.date()).days,
        record.poi.geom.wkt,
        record.pile_last_update_time,
        record.tp_last_pull_time,
        record.src,
    ]

    if ctx.debug:
        print('\n'.join([str(x) for x in result]))
    else:
        tsv.write_tsv(ctx.work_dir / 'output.csv', [result], mode='a')


def main(args):
    """
    主函数
    """
    print(args)
    main_pipe = pipeline.Pipeline(
        load_bids,
        load_records,
        fill_status,
        fill_time_props,

        # 各特征召回手段
        recall_error_01,
        recall_error_02,
    )
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path("cache/recall_status_error"),
        mode=args.mode,
        data_path=Path(args.data_path),
        bids=args.bids,
        wkt_list=args.wkt_list,
        feature_list=args.feature_list,
        cities=args.cities,
        debug=args.debug,
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main(parse_args())
