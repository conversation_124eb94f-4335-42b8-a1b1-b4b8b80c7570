"""
找全量的服务区充电站
"""
from dataclasses import dataclass, field
from tqdm import tqdm
from pathlib import Path
from src.parking.recognition import dbutils
from multiprocessing.pool import Pool
from src.tools import pgsql
from src.tools import utils, tsv
from shapely import wkt
from shapely.geometry import Polygon, Point

METER = 0.9e-5


@dataclass
class ChargingStation:
    """
    充电站类
    """
    bid: str
    show_tag: str
    geom: str = field(default=None)
    name: str = field(default=None)
    address: str = field(default=None)
    online_type: int = field(default=None)
    is_service: bool = field(default=False)
    aoi_cover: bool = field(default=False)
    aoi_bid: str = field(default=None)
    poi_cover: bool = field(default=None)
    poi_bid: str = field(default=None)
    high_road: bool = field(default=None)
    high_link_id: str = field(default=None)
    name_service: bool = field(default=None)

    def __init__(self, bid, show_tag, geom, name, address):
        self.bid = bid
        self.show_tag = show_tag
        self.geom = geom
        self.name = name
        self.address = address

    def fill_online_type(self):
        """
        开放属性
        """
        if not self.is_service:
            return
        res = fetch_online_type(self.bid)
        if res is not None:
            self.online_type = res[0]

    def check_aoi_cover(self):
        """
        检查是否在服务区内
        """
        buffer_width = 10 * METER
        buffer_geom = wkt.loads(self.geom).buffer(buffer_width)
        face_ids_res = fetch_face_id_by_geom(buffer_geom.wkt)
        if face_ids_res is None or len(face_ids_res) == 0:
            return
        bids_res = fetch_bids_by_face_ids(face_ids_res)
        if bids_res is None or len(bids_res) == 0:
            return
        service_bid = fetch_service_zones_by_bids(bids_res)
        if service_bid is not None and len(service_bid) > 0:
            self.is_service = True
            self.aoi_cover = True
            self.aoi_bid = service_bid[0][0]

    def check_name(self):
        """
        找名字里面有服务区的
        """
        service_name = '服务区'
        if self.is_service:
            return
        if self.name is not None:
            if service_name in self.name or service_name in self.address:
                self.name_service = True

    def check_exist_service_zone(self):
        """
        检查500米内是否存在服务区poi
        """
        if self.is_service or not self.name_service:
            return
        buffer_width = 500 * METER
        buffer_geom = wkt.loads(self.geom).buffer(buffer_width)
        res = fetch_service_pois(buffer_geom.wkt)
        if res is not None:
            self.poi_cover = True
            self.poi_bid = res[0]
            if self.name_service:
                self.is_service = True

    def check_high_level_road(self):
        """
        检查是否有高等级道路
        """
        if self.aoi_cover or not self.name_service:
            return
        buffer_width = 200 * METER
        buffer_geom = wkt.loads(self.geom).buffer(buffer_width)
        res = get_nav_link(buffer_geom.wkt)
        if res is not None:
            self.high_road = True
            self.high_link_id = res[0]


def fetch_cdzs():
    """
    找充电站
    """
    cdz_list = []
    sql = """
        select bid,show_tag,ST_ASTEXT(geometry) as geom,name,address from poi where std_tag='交通设施;充电站' and status=1; 
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql, )
    for bid, show_tag, geom, name, address in res:
        cdz_list.append(ChargingStation(bid, show_tag, geom, name, address))
    return cdz_list


def fetch_online_type(bid):
    """
    找开放类型
    """
    sql = """
        select online_type from cdz_history where bid=%s; 
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql, [bid])
    return res


def fetch_service_pois(geom_wkt):
    """
    找相交的服务区poi
    """
    sql = """
        select bid,ST_ASTEXT(geometry) as geom from poi where std_tag='交通设施;服务区' and 
        status=1 and ST_Intersects(st_geomfromText(%s, 4326), geometry); 
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql, [geom_wkt])
    return res


def fetch_service_zones_by_bids(bids: list[str]):
    """
    找所有服务区poi
    """
    sql = """
        select bid from poi where std_tag='交通设施;服务区' and bid in %s and status=1; 
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [tuple(bids)])
    return res


def fetch_bids_by_face_ids(face_ids: list[str]):
    """
    找服务区对应的face_id
    """
    sql = """
        select poi_bid from blu_face_poi
        where face_id in %s;
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [tuple(face_ids)])
    bids = [bid[0] for bid in ret]
    return bids


def fetch_face_id_by_geom(geom_wkt: str):
    """
    找服务区对应的area
    """
    sql = """
        select face_id from blu_face
        where ST_Intersects(st_geomfromText(%s, 4326), geom);
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [geom_wkt])
    ret = [item[0] for item in ret]
    return ret


def get_nav_link(geom_wkt: str):
    """
    查找相交的高等级道路
    """
    sql = f"""
      select link_id from nav_link where ST_Intersects(st_geomfromText(%s, 4326), geom) and (kind =1 or kind =2)
    """
    link_res = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [geom_wkt])
    return link_res


def check_cdz(cdz):
    """
    检查充电站
    1.是否被服务区aoi压盖
    2.500米内是否存在服务区poi
    3.200米是否有高等级道路
    """

    cdz.check_aoi_cover()
    cdz.check_name()
    cdz.check_exist_service_zone()
    cdz.check_high_level_road()
    cdz.fill_online_type()
    return cdz


def fetch_service_cdz(out_path):
    """
    找全量的服务区充电站
    """
    cdz_list = fetch_cdzs()
    for cdz in tqdm(cdz_list):
        result = check_cdz(cdz)
        if result.is_service:
            tsv.write_tsv(out_path,
                          [[result.bid]],
                          mode="a")


if __name__ == '__main__':
    out_path = utils.ensure_path(Path("cache/data_monitor/service_cdz_bid.tsv"), cleanup=True)
    fetch_service_cdz(out_path)