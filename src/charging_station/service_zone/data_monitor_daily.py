"""
天级监控：服务区充电站
"""
import subprocess
import requests
import json
from retrying import retry
from datetime import date, timedelta
from dataclasses import dataclass, field
from src.charging_station.service_zone import auto_find_service_zone
from pathlib import Path
from src.tools import utils, tsv
from src.parking.recognition import dbutils
from multiprocessing.pool import Pool
from src.tools import pgsql


@dataclass
class Context:
    """
    上下文
    """
    afsshell = "/home/<USER>/afs/bin/afsshell"
    afsshell_de16 = '/home/<USER>/chenbaojun/scripts/afs/bin/afsshell'
    AFS_BASE = "/user/map-data-streeview/aoi-ml/charging_station/data_monitor/"
    local_base_path = "cache/data_monitor/"
    upload_file_url = 'http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=postfile&space=fenglei'
    download_file_url = 'http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile'
    web_hook = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=" + 'd2ab0b311ae2d9a6faa0d0a4e79100707'
    # http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd28b25f60a98c5b682e0f625824d5781
    # 'd2ab0b311ae2d9a6faa0d0a4e79100707'
    yesterday_bids = []
    today_bids = []
    today_str = date.today().strftime("%Y-%m-%d")
    yesterday_str = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
    return_new_download_url = ''
    return_change_download_url = ''
    new_cdz_msg = ''
    change_cdz_msg = ''
    msg_info = f"【服务区充电站天级监控】\n"


def send_to_ruliu(ctx, msg, to_user_name):
    """
    发送消息到如流机器人
    """
    data = {
        "message": {
            "body": [{
                "type": "TEXT",
                "content": msg
            },
                {
                    "type": "AT",
                    "atuserids": [
                        to_user_name
                    ]
                }]
        }
    }
    headers = {
        "Content-Type": "application/json"
    }
    response = requests.post(ctx.web_hook, headers=headers, data=json.dumps(data))
    print(response.text)


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(upload_file_url, local_file_path):
    """
    上传文件
    """
    files = {'file': open(local_file_path, 'r', encoding='utf-8')}
    file_uuid = requests.post(upload_file_url, files=files).text
    if len(file_uuid) != 32:
        raise Exception(f'upload file failed')

    return file_uuid


def upload_records(ctx: Context, local_path: Path):
    """
    上传记录
    """
    if not local_path.exists():
        return ''

    file_uuid = upload_file(ctx.upload_file_url, local_path)
    local_path.unlink()
    return f'{ctx.download_file_url}&uuid={file_uuid}'


def find_poi_info(bids: list):
    """
    查找POI信息
    """
    sql = f"""
      select bid,name,address,show_tag,status,ST_ASTEXT(geometry) as geom from poi where bid in %s
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [tuple(bids)])
    return res


def download_file(ctx: Context, download_path: str):
    """下载文件到本地"""
    try:
        subprocess.run([ctx.afsshell_de16, 'get', download_path, ctx.local_base_path], check=True)
        print("文件成功下载到本地。")
    except subprocess.CalledProcessError:
        print("下载文件时发生错误。")


def parse_file(ctx: Context, local_file_path: str):
    """解析文件"""
    lines = []
    with open(local_file_path, "r", encoding="utf-8") as file:  # 如果有特殊编码，调整 encoding
        lines = file.readlines()
    return [line.strip() for line in lines if line.strip() != '']


def save_records(ctx: Context, records: list, out_path: str):
    """
    保存记录
    """
    out_path = utils.ensure_path(Path(out_path), cleanup=True)
    for record in records:
        tsv.write_tsv(out_path, [record], mode="a")


def find_diff(ctx: Context):
    """
    找今日和昨日差异
    """
    diff_today = list(set(ctx.today_bids).difference(set(ctx.yesterday_bids)))
    ctx.new_cdz_msg = f"新增的充电站：{len(diff_today)}\n"
    if len(diff_today) > 0:
        info_res = find_poi_info(diff_today)
        out_path_str = ctx.local_base_path + ctx.today_str + "new_service_cdzs.tsv"
        save_records(ctx, info_res, out_path_str)
        ctx.return_new_download_url = upload_records(ctx, Path(out_path_str))
        ctx.new_cdz_msg += f"{ctx.return_new_download_url}\n"
    diff_yesterday = list(set(ctx.yesterday_bids).difference(set(ctx.today_bids)))
    ctx.change_cdz_msg = f"变更的充电站：{len(diff_yesterday)}\n"
    if len(diff_yesterday) > 0:
        info_res = find_poi_info(diff_yesterday)
        out_path_str = ctx.local_base_path + ctx.today_str + "change_service_cdzs.tsv"
        save_records(ctx, info_res, out_path_str)
        ctx.return_change_download_url = upload_records(ctx, Path(out_path_str))
        ctx.change_cdz_msg += f"{ctx.return_change_download_url}\n"
    ctx.msg_info += f"{ctx.today_str} 相对于 {ctx.yesterday_str} 数量对比：\n"
    ctx.msg_info += ctx.new_cdz_msg
    ctx.msg_info += ctx.change_cdz_msg


def upload_afs(ctx: Context, local_file_path: str):
    """上传本地文件路径到AFS目标路径"""
    afs_target_path = ctx.AFS_BASE
    try:
        subprocess.run([ctx.afsshell_de16, 'put', local_file_path, afs_target_path], check=True)
        print("文件成功上传到AFS。")
    except subprocess.CalledProcessError:
        print("上传文件到AFS时发生错误。")


def main(ctx: Context):
    """主函数"""
    file_name = "service_cdz_bid.tsv"
    download_path = ctx.AFS_BASE + ctx.yesterday_str + file_name
    out_file_str = ctx.local_base_path + ctx.today_str + file_name
    out_path = utils.ensure_path(Path(out_file_str), cleanup=True)
    yesterday_loacal_path = ctx.local_base_path + ctx.yesterday_str + file_name
    download_file(ctx, download_path)
    ctx.yesterday_bids = parse_file(ctx, yesterday_loacal_path)
    auto_find_service_zone.fetch_service_cdz(out_path)
    upload_afs(ctx, out_file_str)
    ctx.today_bids = parse_file(ctx, out_file_str)
    find_diff(ctx)
    send_to_ruliu(ctx, ctx.msg_info, 'zengjia01')


if __name__ == "__main__":
    ctx = Context()
    main(ctx)