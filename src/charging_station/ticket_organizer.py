# !/usr/bin/env python3
"""
工单整理器
"""
import copy
from collections import defaultdict

import mapio.utils.coord
import shapely.wkt
from shapely import Point
from tqdm import tqdm

from src.batch_process.batch_helper import get_mysql_connection
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.data import Ticket, Poi, TicketOrganizerContext
from src.charging_station.helper import exists_ticket
from src.tools import pgsql

EMAIL = '<EMAIL>'
JWC_CITIES = {'北京市', '深圳市', '重庆市', '成都市'}
TOP_CITIES = {'上海市', '广州市'}
CONFIGS = {
    'diff_location': ('location', 'diff', 'prop', 'CDXDIF20241023005', '竞品 diff 坐标', 'update_location'),
    'diff_ownership': ('ownership', 'diff', 'prop', 'CDXDIF20241023004', '竞品 diff 开放属性', 'update_ownership'),
    'diff_status': ('status', 'diff', 'prop', 'CDXDIF20241023003', '竞品 diff 状态', 'update_status'),
    'diff_spacing': ('spacing', 'diff', 'prop', 'CDXDIF20241023002', '竞品 diff 空间属性', 'update_spacing'),

    'jwc_city_location': (JWC_CITIES, 'city', 'src', 'CDXJWC20241023005', 'JWC 重保城市坐标', 'update_location'),
    'jwc_city_ownership': (JWC_CITIES, 'city', 'src', 'CDXJWC20241023004', 'JWC 重保城市开放属性', 'update_ownership'),
    'jwc_city_status': (JWC_CITIES, 'city', 'src', 'CDXJWC20241023003', 'JWC 重保城市状态', 'update_status'),
    'jwc_city_spacing': (JWC_CITIES, 'city', 'src', 'CDXJWC20241023002', 'JWC 重保城市空间属性', 'update_spacing'),

    'top_city_location': (TOP_CITIES, 'city', 'src', 'CDXTOP20241023005', 'TOP 重保城市坐标', 'update_location'),
    'top_city_ownership': (TOP_CITIES, 'city', 'src', 'CDXTOP20241023004', 'TOP 重保城市开放属性', 'update_ownership'),
    'top_city_status': (TOP_CITIES, 'city', 'src', 'CDXTOP20241023003', 'TOP 重保城市状态', 'update_status'),
    'top_city_spacing': (TOP_CITIES, 'city', 'src', 'CDXTOP20241023002', 'TOP 重保城市空间属性', 'update_spacing'),
}
FILTERS_BRAND = {
    '特斯拉': {'update_location', 'update_ownership', 'update_spacing', 'update_status'},
    '依威能源': {'update_ownership'},
}


def _get_competitor_diff():
    """
    获取存在竞品 diff 的 bid 集合
    """
    field_names = (
        'location',
        'ownership',
        'spacing',
        'status',
    )
    competitor_diff = defaultdict(set)
    sql = '''
        select bid, field_name 
        from cdz_competitor_diff 
        where field_name in %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for bid, field_name in stab.fetch_all(sql, (field_names,)):
            competitor_diff[field_name].add(bid)

    return competitor_diff


def _get_bids_of_brand(brand):
    """
    获取指定品牌的 bid 集合
    """
    get_poi_bid = '''
        select bid
        from poi
        where std_tag = '交通设施;充电站' and
              (name like %s);
    '''
    get_tp_bid = '''
        select bid
        from charging_station
        where bid != '' and
              (thirdname = %s);
    '''
    bids = set()

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        bids.update(row[0] for row in poi_stab.fetch_all(get_poi_bid, [f'%{brand}%']))

    with (
        get_mysql_connection("charging_station") as conn,
        conn.cursor() as cur,
    ):
        cur.execute(get_tp_bid, [brand])
        bids.update(row[0] for row in cur.fetchall())

    return bids


def _get_city_map(bids):
    """
    获取城市
    """
    sql = '''
        select city from poi where bid = %s;
    '''
    city_map = {}

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as stab:
        for bid in bids:
            row = stab.fetch_one(sql, (bid,))
            if row is None:
                continue

            city_map[bid] = row[0]

    return city_map


def _copy_tickets(original_tickets: list[Ticket], config_key):
    """
    复制工单
    """
    _, _, _, batch_id, work_message, src = CONFIGS[config_key]
    tickets = []

    for original_ticket in original_tickets:
        ticket = copy.copy(original_ticket)
        ticket.project = batch_id[:3]
        ticket.batch_id = batch_id
        ticket.batch_name = work_message
        ticket.message = work_message
        ticket.src = src
        tickets.append(ticket)

    return tickets


def _get_poi(poi_stab, bid):
    """
    获取 poi
    """
    sql = '''
        select name, alias, address, telephone, status, std_tag, city, st_astext(geometry)
        from poi
        where bid = %s;
    '''

    row = poi_stab.fetch_one(sql, [bid])
    if row is None:
        return None

    name, alias, address, phone, status, tag, city, wkt = row
    geom: Point = shapely.wkt.loads(wkt)
    bd_x, bd_y = mapio.utils.coord.gcj02_to_bd09(geom.x, geom.y)
    mc_x, mc_y = mapio.utils.coord.bd09_to_mercator(bd_x, bd_y)

    return Poi(
        name=name,
        alias=alias,
        address=address,
        geom=geom,
        mc_geom=shapely.wkt.loads(f'POINT({mc_x} {mc_y})'),
        phone=phone,
        status=status,
        tag=tag,
        city=city,
    )


class TicketOrganizer:
    """
    工单整理器
    """

    def __init__(self, context: TicketOrganizerContext, attach=True):
        self.__attach = attach

        if attach:
            self.__competitor_diff = _get_competitor_diff()
            self.__competitor_diff_city_map = {}
            for bids in self.__competitor_diff.values():
                self.__competitor_diff_city_map.update(_get_city_map(bids))

        self.__context = context
        self.__pois = {}
        self.__all_tickets = []
        self.__all_ticket_uuids = set()

    def arrange(self):
        """
        整理工单
        """
        self.__add_tickets(self.__sort_tickets())

        if self.__attach:
            self.__add_tickets(self.__attach_tickets())

        self.__filter_tickets()
        return self.__all_tickets

    def __get_competitor_diff_bids(self, field_name=None):
        """
        获取竞品 diff bid 集合
        """
        competitor_diff_bids = set()

        if field_name is None:
            for bids in self.__competitor_diff.values():
                competitor_diff_bids.update(bids)
        else:
            competitor_diff_bids = self.__competitor_diff.get(field_name, {})

        return competitor_diff_bids

    def __add_tickets(self, tickets):
        """
        添加工单
        """
        for ticket in tickets:
            if ticket.uuid not in self.__all_ticket_uuids:
                self.__all_tickets.append(ticket)
                self.__all_ticket_uuids.add(ticket.uuid)

    def __create_tickets(self, bids, config_key):
        """
        创建工单
        """
        _, _, _, batch_id, work_message, src = CONFIGS[config_key]

        for bid in tqdm(bids):
            yield Ticket(
                creator=EMAIL,
                mode='formal',
                project=batch_id[:3],
                priority=4,
                batch_id=batch_id,
                bid=bid,
                method='edit',
                message=work_message,
                src=src,
                suggest_poi=self.__pois.get(bid),
                batch_name=work_message,
            )

    def __fill_pois(self, bids):
        """
        加载 poi
        """
        with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
            for bid in tqdm(bids):
                if bid in self.__pois:
                    continue

                poi = _get_poi(poi_stab, bid)
                if poi is None:
                    continue

                self.__pois[bid] = poi

    def __sort_tickets(self):
        """
        对工单排序
        """
        all_tickets = [x for x in self.__context.tickets if x.can_process]
        self.__fill_pois([x.bid for x in all_tickets])
        other_tickets = all_tickets
        sorted_tickets = []

        for key in CONFIGS:
            value, kind, sub_kind, batch_id, work_message, src = CONFIGS[key]
            target_tickets = []

            if kind == 'city' and sub_kind == 'src':
                target_tickets, other_tickets = self.__pick_tickets_by_city(other_tickets, value, src=src)

            sorted_tickets.extend(_copy_tickets(target_tickets, key))

        sorted_tickets.extend(other_tickets)
        return sorted_tickets

    def __attach_tickets(self):
        """
        附加工单
        """
        attached_tickets = []

        for key in CONFIGS:
            value, kind, sub_kind, batch_id, work_message, src = CONFIGS[key]
            target_competitor_diff_bids = set()

            if kind == 'diff' and sub_kind == 'prop':
                target_competitor_diff_bids = self.__get_competitor_diff_bids(value)

            self.__fill_pois(target_competitor_diff_bids)
            attached_tickets.extend(self.__create_tickets(target_competitor_diff_bids, key))

        return attached_tickets

    def __filter_tickets_by_time(self):
        """
        根据时间过滤工单
        """
        with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
            for ticket in tqdm([x for x in self.__all_tickets if x.can_process]):
                if exists_ticket(poi_stab, ticket.src, ticket.bid):
                    ticket.can_process = False
                    ticket.reason = 'is work changed'

    def __filter_tickets_of_diff_batch(self):
        """
        过滤竞品 diff 批次工单
        """
        sql = '''
            select 1 
            from cdz_ticket 
            where bid = %s and 
                  src = %s and
                  mode = 'formal' and
                  push_time > '2025-05-27' and
                  push_time > now() - interval '180 days';
        '''

        with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
            for ticket in tqdm([x for x in self.__all_tickets if x.can_process and 'CDXDIF' in x.batch_id]):
                row = poi_stab.fetch_one(sql, [ticket.bid, ticket.src])
                if row is not None:
                    ticket.can_process = False
                    ticket.reason = 'is diff work changed'

    def __filter_tickets_by_config(self):
        """
        根据配置过滤工单
        """
        for brand in FILTERS_BRAND:
            props = FILTERS_BRAND[brand]
            all_tickets = [x for x in self.__context.tickets if x.can_process]
            target_tickets, other_tickets = self.__pick_tickets_by_brand(all_tickets, brand)
            for ticket in target_tickets:
                if ticket.src in props:
                    ticket.can_process = False
                    ticket.reason = 'ignored brand'

    def __filter_tickets(self):
        """
        过滤工单
        """
        self.__filter_tickets_by_time()
        self.__filter_tickets_by_config()
        self.__filter_tickets_of_diff_batch()

    def __get_competitor_diff_bids_of_city(self, city):
        """
        获取某个城市的竞品 diff 的 bid 集合
        """
        return {
            bid for bid in self.__get_competitor_diff_bids()
            if self.__competitor_diff_city_map.get(bid, '') == city
        }

    def __pick_tickets_by_brand(self, original_tickets, brand):
        """
        根据品牌挑选工单
        """
        picked_tickets = []
        other_tickets = []

        for ticket in original_tickets:
            if ticket.bid not in self.__pois:
                continue

            if brand in self.__pois[ticket.bid].name:
                picked_tickets.append(ticket)
                continue

            tp_records = self.__context.tp_records.get(ticket.bid, [])
            if any(tp.third_name == brand for tp in tp_records):
                picked_tickets.append(ticket)
                continue

            other_tickets.append(ticket)

        return picked_tickets, other_tickets

    def __pick_tickets_by_city(self, original_tickets, cities, src=None):
        """
        根据城市挑选工单
        """
        picked_tickets = []
        other_tickets = []

        for ticket in original_tickets:
            if ticket.bid not in self.__pois:
                continue

            if self.__pois[ticket.bid].city not in cities:
                other_tickets.append(ticket)
                continue

            if src is not None and ticket.src != src:
                other_tickets.append(ticket)
                continue

            picked_tickets.append(ticket)

        return picked_tickets, other_tickets
