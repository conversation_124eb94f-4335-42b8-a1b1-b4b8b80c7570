# !/usr/bin/env python3
"""
任务流的常量
"""


class ConstWorkFlow:
    """
    任务流状态，可以自己定义，任务流转会根据任务状态确定下一步的任务
    """
    FLOW_STATUS_WAIT = 0  # 等待执行
    FLOW_STATUS_DOING = 1  # 正在执行
    FLOW_STATUS_SUCCESS = 2  # 任务成功
    FLOW_STATUS_FAIL = 3  # 任务执行失败
    FLOW_STATUS_ERROR = 4  # 出现error

    # 图片转存状态
    IMAGE_STATUS_WAIT = 0
    IMAGE_STATUS_DOING = 1
    IMAGE_STATUS_SUCCESS = 2
    IMAGE_STATUS_FAIL = 3

    """
    子任务，每个任务都需要单独写一个任务执行方法
    """
    # 任务开始
    FLOW_SUBTASK_START = "subtask_start"
    # 子任务：任务结束标志
    FLOW_SUBTASK_END = "subtask_end"
    # 子任务：线性化
    FLOW_SUBTASK_SEG_POST_JOB = "subtask_seg_post_job"
    # 子任务：策略后处理
    FLOW_SUBTASK_POST_JOB = "subtask_aoi_ml_post_job"
    # 子任务：处理语义分割
    FLOW_SUBTASK_EXEC_SEG = 'subtask_exec_seg'
    # 子任务，处理评估任务
    FLOW_SUBTASK_EXEC_MODEL_EVALUATE = 'subtask_exec_model_evaluate'

    """
    任务名称配置
    """
    # 识别后处理任务(常规)
    TASK_TYPE_POST_AFTER_SEG_SCAN = 'task_type_post_after_seg_scan'
    # 识别后处理(大街区)
    TASK_TYPE_POST_AFTER_SEG_SCAN_LARGE_STREET = "task_type_post_after_seg_scan_large_street"
    # 执行语义分割
    TASK_TYPE_EXEC_AOI_SEG = "task_type_exec_aoi_seg"
    # 评估任务
    TASK_TYPE_MODEL_EVALUATE = "task_type_model_evaluate"

    """
    任务流配置，配置当前任务完成后的下一步任务名称
    """
    TASK_FLOW_CONF = {
        # 识别后处理：1. 开始 -> 1 线性化 ->  2 后处理 ->  3 结束
        TASK_TYPE_POST_AFTER_SEG_SCAN: {
            FLOW_SUBTASK_START: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_SEG_POST_JOB,
            },
            FLOW_SUBTASK_SEG_POST_JOB: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_POST_JOB,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            },
            FLOW_SUBTASK_POST_JOB: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_END,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            }
        },
        TASK_TYPE_POST_AFTER_SEG_SCAN_LARGE_STREET: {
            # 大街区处理:暂时是: 开始 -> 矢量化 -> 结束
            FLOW_SUBTASK_START: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_SEG_POST_JOB,
            },
            FLOW_SUBTASK_SEG_POST_JOB: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_END,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            }
        },
        TASK_TYPE_EXEC_AOI_SEG: {
            # 语义分割处理:开始-> 执行语义分割 -> 矢量化 -> 策略后处理
            FLOW_SUBTASK_START: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_EXEC_SEG,
            },
            FLOW_SUBTASK_EXEC_SEG: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_SEG_POST_JOB,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            },
            FLOW_SUBTASK_SEG_POST_JOB: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_POST_JOB,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            },
            FLOW_SUBTASK_POST_JOB: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_END,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            }
        },
        TASK_TYPE_MODEL_EVALUATE: {
            # 模型评估:开始-> 执行语义分割 -> 矢量化 -> 策略后处理 ->  模型评估
            FLOW_SUBTASK_START: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_EXEC_SEG,
            },
            FLOW_SUBTASK_EXEC_SEG: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_SEG_POST_JOB,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            },
            FLOW_SUBTASK_SEG_POST_JOB: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_POST_JOB,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            },
            FLOW_SUBTASK_POST_JOB: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_EXEC_MODEL_EVALUATE,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            },
            FLOW_SUBTASK_EXEC_MODEL_EVALUATE: {
                FLOW_STATUS_SUCCESS: FLOW_SUBTASK_END,
                FLOW_STATUS_FAIL: FLOW_SUBTASK_END,
            }
        }
    }

    TASK_CONF = {
        TASK_TYPE_POST_AFTER_SEG_SCAN_LARGE_STREET: {
            # 不过滤小文件
            "filter_small": 0
        }
    }

    @staticmethod
    def get_next_subtask(task_type, current_subtask_name, current_status=FLOW_STATUS_SUCCESS) -> str:
        """
        获取下一步任务的名称
        :param task_name: 任务名称
        :param current_subtask_name: 当前子任务名称
        :param current_status: 当前任务状态
        :return: 下一步任务的名称
        """
        flow_cfg = ConstWorkFlow.TASK_FLOW_CONF.get(task_type, None)
        if not flow_cfg:
            return ConstWorkFlow.FLOW_SUBTASK_END
        sub_task_cfg = flow_cfg.get(current_subtask_name, None)
        if not sub_task_cfg:
            return ConstWorkFlow.FLOW_SUBTASK_END
        next_subtask = sub_task_cfg.get(current_status, None)
        if not next_subtask:
            return ConstWorkFlow.FLOW_SUBTASK_END
        return next_subtask

    @staticmethod
    def get_task_conf(task_type) -> dict:
        return ConstWorkFlow.TASK_CONF.get(task_type) if task_type in ConstWorkFlow.TASK_CONF else {}


if __name__ == '__main__':
    print(ConstWorkFlow.get_next_subtask(ConstWorkFlow.TASK_TYPE_POST_AFTER_SEG_SCAN,
                                         ConstWorkFlow.FLOW_SUBTASK_SEG_POST_JOB))
