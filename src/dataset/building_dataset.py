"""
建筑物数据集生成脚本，外部使用请调用 create_dataset_by_plan_id() 和 create_dataset_by_city() 函数
TODO: 这个文件中目前编写了大量没用的东西，比如：找街区、根据城市找街区、根据 plan_id 找街区，
TODO: 这些方便于具体业务的帮助函数，应当单独放到别的文件中。
TODO: 这个文件应当做成仅依赖 geom 产生数据集，仅提供最底层、最纯洁的功能。
"""

from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from pathlib import Path

import cv2
import pymysql
import requests
from shapely import wkt
from tqdm import tqdm

from src.aikit import boundary, satellite_imagery
from src.tools import pipeline, pgsql, utils

desc = pipeline.get_desc()

# TODO: 配置到 mysql.py 中，和 pgsql.py 保持一致，放入配置文件。
BEEFLOW_CONFIG = {
    "host": "*************",
    "port": 5730,
    "user": "bee_flow_r",
    "password": "bee_flow_r_2021",
    "db": "bee_flow",
    "charset": "utf8mb4",
}
BEEFLOW_PRE_CONFIG = {
    "host": "*************",
    "port": 5730,
    "user": "bee_flow_pre_rw",
    "password": "bee_flow_pre_rw_2021",
    "db": "bee_flow_pre",
    "charset": "utf8mb4",
}
IMAGE_VERSIONS = [
    # "google-2021",
    # "google-2022",
    "google-2023",
]


# export:


def create_dataset_by_plan_id(plan_ids: list[int], work_dir: Path):
    """
    根据计划 id 生成数据集
    @param plan_ids: 计划 id 列表
    @param work_dir: 输出目录（会自动创建）
    """
    with ThreadPoolExecutor(max_workers=2) as executor:
        tasks = executor.map(execute_plan_id, enumerate(create_tasks_by_plan_id(plan_ids, work_dir)))
        _ = list(tasks)


def create_dataset_by_city(cities: list[tuple[str, str]], work_dir: Path):
    """
    根据城市名生成数据集
    @param cities: [(city_zh_name, city_pinyin)]
    @param work_dir: 输出目录（会自动创建）
    """
    with ThreadPoolExecutor(max_workers=2) as executor:
        tasks = executor.map(execute_city, enumerate(create_tasks_by_city(cities, work_dir)))
        _ = list(tasks)


def create_dataset_by_street_region(street_region_ids: list[str], output_dir: Path):
    """
    根据街区 ID 生成数据集
    @param street_region_ids: 街区 face_id 列表
    @param output_dir: 输出目录（会自动创建）
    """
    with ThreadPoolExecutor(max_workers=2) as executor:
        tasks = executor.map(
            execute_street_region,
            enumerate(create_tasks_by_street_region(street_region_ids, output_dir)),
        )
        _ = list(tasks)


def create_dataset_by_geom(guid2geom: dict, output_dir: Path, versions: list = []):
    """
    根据街区 ID 生成数据集
    @param guid2geom: 唯一id 对应的范围
    @param output_dir: 输出目录（会自动创建）
    @param versions: 数据集的版本号
    """
    with ThreadPoolExecutor(max_workers=2) as executor:
        tasks = executor.map(
            execute_region,
            enumerate(create_tasks_by_guid2geom(guid2geom, output_dir, versions)),
        )
        _ = list(tasks)


# pipeline:


@dataclass
class Context:
    """
    管线上下文
    """

    work_dir: Path
    task_name: str
    position: int
    image_version: str
    # [(face_id, geom, bounds)]
    street_regions: list[tuple[str, str, str]] = field(default_factory=list)


def get_street_region_by_city(city: str):
    """
    根据城市名获取街道区域
    """

    def pipe(ctx: Context, proceed):
        with pymysql.connect(**BEEFLOW_CONFIG) as conn, conn.cursor() as cursor:
            sql = """
                select mesh_id from mesh_conf
                where cityname = %s;
            """
            cursor.execute(sql, [city])
            ret = cursor.fetchall()

        mesh_ids = [geom[0] for geom in ret]

        with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
            sql = """
                select face_id, ST_AsText(geom), ST_AsText(ST_Envelope(geom)) from street_region
                where mesh_id in %s;
            """
            ctx.street_regions = pgsql.fetch_all(conn, sql, [tuple(mesh_ids)])

        proceed()

    return pipe


def get_street_region_by_plan_id(plan_ids: list[str]):
    """
    根据计划 id 获取街道区域
    """

    def pipe(ctx: Context, proceed):
        with pymysql.connect(**BEEFLOW_PRE_CONFIG) as conn, conn.cursor() as cursor:
            sql = """
                select data_content from basic_feature_pre_plan
                where id in %s;
            """
            cursor.execute(sql, [plan_ids])
            ret = cursor.fetchall()

        urls = [x[0] for x in ret]
        geoms = [requests.get(x).text for x in urls]

        with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
            for geom in geoms:
                sql = """
                    select face_id, ST_AsText(geom), ST_AsText(ST_Envelope(geom)) from street_region
                    where ST_Intersects(ST_GeomFromText(%s, 4326), geom);
                """
                ctx.street_regions.extend(pgsql.fetch_all(conn, sql, [geom]))

        proceed()

    return pipe


def get_street_region_by_id(street_region_ids: list[str]):
    """
    根据街区 id 获取街道区域
    """

    def pipe(ctx: Context, proceed):
        with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
            sql = """
                select face_id, ST_AsText(geom), ST_AsText(ST_Envelope(geom)) from street_region
                where face_id in %s;
            """
            regions = pgsql.fetch_all(conn, sql, [tuple(street_region_ids)])
            ctx.street_regions.extend(regions)

        proceed()

    return pipe


def generate_building_cases(ctx: Context, proceed):
    """
    生成建筑物数据集
    """
    # generate dirs
    dataset_dir = ctx.work_dir
    json_info_dir = dataset_dir / "json_info"
    image_info_dir = json_info_dir / "image"
    image_info_dir.mkdir(parents=True, exist_ok=True)

    building_info_dir = json_info_dir / "building"
    building_info_dir.mkdir(parents=True, exist_ok=True)

    images_dir = dataset_dir / "images" / ctx.image_version
    images_dir.mkdir(parents=True, exist_ok=True)

    for face_id, geom, bounds_wkt in tqdm(
        ctx.street_regions,
        desc=f"{ctx.task_name} - {ctx.image_version}",
        position=ctx.position,
    ):
        image_path = images_dir / f"{face_id}.jpg"
        image_info_path = image_info_dir / f"{face_id}.json"
        building_info_path = building_info_dir / f"{face_id}.json"
        if image_path.exists() and image_info_path.exists() and building_info_path.exists():
            continue

        # generate building info
        # building_info = generate_building_info(face_id, bounds_wkt)
        # if not building_info:
        #     continue
        #
        # utils.write_json(building_info_path, building_info)

        # generate image
        image, image_info = generate_image_and_info(face_id, bounds_wkt, ctx.image_version)
        if not image_info:
            continue

        cv2.imwrite(str(image_path), image, [int(cv2.IMWRITE_JPEG_QUALITY), 75])
        utils.write_json(image_info_path, image_info)

    proceed()


# helpers:


def generate_image_and_info(block_id: str, geom: str, image_version: str):
    """
    数据集：生成图片和图片信息
    """
    bounds = boundary.from_wkt(geom)
    image = satellite_imagery.crop(bounds, image_version, level=19)
    if image is None:
        return None, None

    # generate image info
    height, width = image.shape[:2]
    left, top, right, bottom = bounds
    image_info = {
        "id": block_id,
        "width": width,
        "height": height,
        "region": {
            "left": left,
            "top": top,
            "right": right,
            "bottom": bottom,
        },
        "geom": geom,
    }
    return image, image_info


def generate_building_info(block_id: str, geom: str):
    """
    数据集：生成建筑物信息
    """
    bud_faces = get_bud_face_by_wkt(geom)
    if not bud_faces:
        return None

    building_info = {
        "id": block_id,
        "features": [
            {
                "face_id": building_id,
                "struct_id": struct_id,
                "geom": geom,
            }
            for struct_id, building_id, geom in bud_faces
        ],
    }
    return building_info


def get_bud_face_by_wkt(geom: str):
    """
    获取建筑物信息，根据指定的 wkt
    @param geom: wkt
    @return: [(struct_id, face_id, geom)]
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = f"""
            select struct_id, face_id, ST_AsText(geom) from bud_face
            where ST_Intersects(ST_GeomFromText(%s, 4326), geom);
        """
        return pgsql.fetch_all(conn, sql, [geom])


def execute_city(task: tuple[int, tuple[Path, str, str]]):
    """
    并发封装：执行城市任务
    """
    ctx = prepare_context(task)
    pipe = pipeline.Pipeline(
        get_street_region_by_city(ctx.task_name),
        generate_building_cases,
    )
    desc.attach(pipe)
    pipe(ctx)


def execute_plan_id(task: tuple[int, tuple[Path, str, str]]):
    """
    并发封装：执行计划 id 任务
    """
    ctx = prepare_context(task)
    pipe = pipeline.Pipeline(
        get_street_region_by_plan_id([ctx.task_name]),
        generate_building_cases,
    )
    desc.attach(pipe)
    pipe(ctx)


def execute_street_region(task: tuple[int, tuple[Path, str, list[str]]]):
    """
    并发封装：执行计划 id 任务
    """
    position, (output_dir, version, ids) = task
    ctx = prepare_context((position, (output_dir, version, "street region task")))
    pipe = pipeline.Pipeline(
        get_street_region_by_id(ids),
        generate_building_cases,
    )
    desc.attach(pipe)
    pipe(ctx)


def prepare_context(task: tuple[int, tuple[Path, str, str]]):
    """
    准备上下文
    """
    position, (work_dir, version, task_name) = task
    work_dir.mkdir(parents=True, exist_ok=True)
    ctx = Context(
        work_dir=work_dir,
        task_name=task_name,
        position=position,
        image_version=version,
    )
    return ctx


def execute_region(task: tuple[int, tuple[Path, str, dict]]):
    """
    并发封装：执行计划 id 任务
    """
    position, (output_dir, version, guid2geom) = task
    ctx = prepare_context((position, (output_dir, version, "region task")))
    pipe = pipeline.Pipeline(
        fill_regions(guid2geom),
        generate_building_cases,
    )
    desc.attach(pipe)
    pipe(ctx)


def fill_regions(guid2geom: dict):
    """
    填充需要生成数据集的范围
    """

    def pipe(ctx: Context, proceed):
        regions = []
        for guid, geom in guid2geom.items():
            bound_geom = wkt.loads(geom).envelope.wkt
            regions.append((guid, geom, bound_geom))
        ctx.street_regions.extend(regions)

        proceed()

    return pipe


def create_tasks_by_city(cities: list[tuple[str, str]], work_dir: Path):
    """
    根据城市列表，生成任务
    @param cities: [(城市中文名, city_pinyin)]
    @param work_dir: 任务目录
    @return: [(output_dir, image_version, task_name)]
    """
    tasks = [(work_dir / pinyin, version, city) for city, pinyin in cities for version in IMAGE_VERSIONS]
    return tasks


def create_tasks_by_plan_id(plan_ids: list[int], work_dir: Path):
    """
    根据计划 id 列表，生成任务
    @param plan_ids: 计划 id 列表
    @param work_dir: 任务目录
    @return: [(output_dir, image_version, [plan_id])]
    """
    tasks = [(work_dir / str(plan_id), version, [plan_id]) for plan_id in plan_ids for version in IMAGE_VERSIONS]
    return tasks


def create_tasks_by_street_region(street_region_ids: list[str], output_dir: Path):
    """
    根据城市列表，生成任务
    @param street_region_ids: 街区 face_id 列表
    @param output_dir: 任务目录
    @return: [(output_dir, image_version, [street_region_id])]
    """
    tasks = [(output_dir, version, street_region_ids) for version in IMAGE_VERSIONS]
    return tasks


def create_tasks_by_guid2geom(guid2geom: dict, output_dir: Path, versions: list = []) -> list:
    """
    根据范围创建任务
    根据城市列表，生成任务
    @param guid2geom: 唯一id对应的范围
    @param output_dir: 任务目录
    @param versions: 数据集版本好
    @return: [(output_dir, image_version, [guid2geom])]
    """
    if len(versions) == 0:
        versions = IMAGE_VERSIONS
    tasks = [(output_dir, version, guid2geom) for version in versions]
    return tasks


def get_eval_plan_ids() -> list[int]:
    """
    获取评估计划 id 列表
    """
    with pymysql.connect(**BEEFLOW_PRE_CONFIG) as conn, conn.cursor() as cursor:
        sql = """
            select id from basic_feature_pre_plan
            where plan_type = 47 and status='INIT';
        """
        cursor.execute(sql)
        ret = cursor.fetchall()
        return [x[0] for x in ret]


def main():
    """
    主函数：用于测试
    """
    cities = [
        ("北京市", "beijing"),
        ("上海市", "shanghai"),
        ("成都市", "chengdu"),
        ("杭州市", "hangzhou"),
    ]
    # plan_ids = [926, 927, 928, 932]
    plan_ids = get_eval_plan_ids()

    work_dir = Path("building_dataset_test")
    # create_dataset_by_city(cities, work_dir)
    create_dataset_by_plan_id(plan_ids, work_dir)


if __name__ == "__main__":
    main()
