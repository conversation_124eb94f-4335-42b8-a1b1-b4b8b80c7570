# !/usr/bin/env python3
"""
模型评估：该模块用于计算识别线的指标。
remarks: 计算样本点的召回指标，召回标准：识别框包含样本点。
"""
import json
import math
from functools import reduce
from pathlib import Path

import cv2
import numpy as np
from osgeo import ogr
from tqdm import tqdm

from src.tools import linq
from src.tools import pipeline
from src.tools import tsv
from src.tools import utils


class Indicator:
    model_id: str
    recall: float
    exclusive_recall: float
    extraction: float
    productivity: float
    exclusive_productivity: float


class Context:
    def __init__(self, work_dir=Path('.'), qualified_score=80):
        work_dir.mkdir(parents=True, exist_ok=True)
        self.dataset_dir = work_dir
        self.sample_bid_list_path = work_dir / 'bid2mid.tsv'
        self.result_json_path_list = list((work_dir / 'results' / 'unfiltered').glob('*.json'))
        self.qualified_score = qualified_score

        # [bid, mid, road_block_id, poi_geom]
        self.samples = []
        # {file_name: [road_block_id, score, aoi_geom]}
        self.recognitions = {}
        # [file_name, recalled_sample_bids, recalled_true_sample_bids]
        self.recalled_samples = []
        self.indicators = []


def fill_sample_list(ctx: Context, proceed):
    sample_items = tsv.read_tsv(ctx.sample_bid_list_path)
    poi_json_dir = ctx.dataset_dir / 'json_info' / 'poi'
    poi_table = {poi['id']: (poi_path.stem, poi['geom'])
                 for poi_path in tqdm(poi_json_dir.glob('*.json'))
                 for poi in get_poi_list(poi_path)}
    match_result = [[*item, *poi_table[item[1]]]
                    for item in sample_items if item[1] in poi_table]
    match_result = [[*x[:-1], ogr.CreateGeometryFromWkt(x[-1])] for x in match_result]

    ctx.samples = match_result
    proceed()


def fill_recognition_list(ctx: Context, proceed):
    for recognition_path in tqdm(ctx.result_json_path_list):
        results = json.loads(recognition_path.read_text(encoding='utf8'))
        if not results:
            continue

        results = [
            [get_road_block_id(x['imgLabel']), x['score'], ogr.CreateGeometryFromWkt(x['wkt'])]
            for x in results
        ]
        results = linq.group_by(results, lambda x: x[0])
        ctx.recognitions[recognition_path.stem] = results

    proceed()


def extract_recalled_samples(ctx: Context, proceed):
    # fetch recalled sample bids.
    samples = []
    for key, results in ctx.recognitions.items():
        sample_recognition_pairs = [(sample, results[sample[-2]]) for sample in ctx.samples if sample[-2] in results]
        recalled_samples = {sample[0]: (sample, aoi) for sample, aoi_list in sample_recognition_pairs
                            for aoi in aoi_list if aoi[-1].Contains(sample[-1])}
        recalled_true_samples = {bid: (sample, aoi) for bid, (sample, aoi) in recalled_samples.items()
                                 if aoi[1] >= ctx.qualified_score}
        samples.append((key, recalled_samples.keys(), recalled_true_samples.keys()))

    ctx.recalled_samples = samples
    proceed()


def calc_indicators(ctx: Context, proceed):
    recalled_samples = ctx.recalled_samples

    union_r_bids = reduce(lambda acc, x: acc | x, [r_bids for _, r_bids, _ in recalled_samples])
    union_r_bids_count = len(union_r_bids)

    union_r_true_bids = reduce(lambda acc, x: acc | x, [r_true_bids for _, _, r_true_bids in recalled_samples])
    union_r_true_bids_count = len(union_r_true_bids)

    r_bids_list = [(model, r_bids) for model, r_bids, _ in recalled_samples]
    r_true_bids_list = [(model, r_true_bids) for model, _, r_true_bids in recalled_samples]

    results = []
    for model, r_bids, r_true_bids in recalled_samples:
        exclusive_r_bids = get_exclusive_bids(model, r_bids, r_bids_list)
        exclusive_r_true_bids = get_exclusive_bids(model, r_true_bids, r_true_bids_list)

        result = Indicator()
        result.model_id = model
        result.recall = len(r_bids) / len(ctx.samples)
        result.exclusive_recall = len(exclusive_r_bids) / union_r_bids_count
        result.extraction = len(r_true_bids) / len(r_bids)
        result.productivity = result.recall * result.extraction
        result.exclusive_productivity = len(exclusive_r_true_bids) / union_r_true_bids_count
        results.append(result)

    result = Indicator()
    result.model_id = 'union'
    result.recall = union_r_bids_count / len(ctx.samples)
    result.exclusive_recall = math.nan
    result.extraction = union_r_true_bids_count / union_r_bids_count
    result.productivity = result.recall * result.extraction
    result.exclusive_productivity = math.nan
    results.append(result)

    ctx.indicators = results
    proceed()


def print_results(ctx: Context, proceed):
    headers = [
        'model',
        'recall (%)',
        'exclusive recall (%)',
        'extraction (%)',
        'productivity (%)',
        'exclusive productivity (%)',
    ]
    formatters = {
        'recall (%)': format_float,
        'exclusive recall (%)': format_float,
        'extraction (%)': format_float,
        'productivity (%)': format_float,
        'exclusive productivity (%)': format_float,
    }
    data = [[x.model_id,
             100 * x.recall,
             100 * x.exclusive_recall,
             100 * x.extraction,
             100 * x.productivity,
             100 * x.exclusive_productivity]
            for x in ctx.indicators]
    utils.print_table(data, headers, formatters)
    proceed()


def draw_sample(ctx: Context, proceed):
    for key, results in ctx.recognitions.items():
        hit_samples = [(sample, results[sample[-2]]) for sample in ctx.samples if sample[-2] in results]
        hit_samples = [(poi[0], poi[2], poi[-1], aoi[-1]) for poi, aoi_list in hit_samples for aoi in aoi_list if
                       aoi[1] >= 90 and aoi[-1].Contains(poi[-1])]

        output_dir = ctx.dataset_dir / 'render_results' / key
        output_dir.mkdir(exist_ok=True, parents=True)
        for bid, road_block_id, poi_geom, aoi_geom in tqdm(hit_samples, leave=False):
            output_path = output_dir / f'{bid}.jpg'
            image_path = ctx.dataset_dir / 'images' / f'{road_block_id}.jpg'
            image_info_path = ctx.dataset_dir / 'json_info' / 'image' / f'{road_block_id}.json'

            image_info = utils.read_json(image_info_path)
            transform_info = utils.get_transform_info_from_json(image_info)

            image = cv2.imread(str(image_path))
            px, py = utils.get_point(str(poi_geom), transform_info)
            cv2.ellipse(image,
                        center=(px, py),
                        axes=(10, 10),
                        angle=0,
                        startAngle=0,
                        endAngle=360,
                        color=(0, 255, 0),
                        thickness=-1)

            aoi_image = np.zeros(image.shape, dtype=image.dtype)
            points = utils.get_polygon_points(aoi_geom, transform_info)
            cv2.fillPoly(aoi_image, pts=[points], color=(0, 0, 255))
            result = cv2.addWeighted(image, 0.7, aoi_image, 0.3, 0)
            cv2.imwrite(str(output_path), result)

    proceed()


def get_exclusive_bids(key, bids, recalled_bids):
    union_other_bids = reduce(lambda acc, x: acc | x, [x for item_key, x in recalled_bids if item_key != key])
    exclusive_recall_bids = bids - union_other_bids
    return exclusive_recall_bids


def get_road_block_id(image_url: str):
    image_name = image_url.split('/')[-1]
    return image_name.split('.')[0]


def get_poi_list(poi_json_file: Path):
    def recurse_poi(poi):
        yield poi
        yield from poi['children']

    poi_info = json.loads(poi_json_file.read_text(encoding='utf8'))
    poi_list = [x for poi in poi_info['poi_list'] for x in recurse_poi(poi)]
    return poi_list


def format_float(value):
    return f'{value:0.2f}'


def main():
    pipe = pipeline.Pipeline(
        pipeline.print_desc('1. find road-block that match the poi...'),
        fill_sample_list,
        pipeline.print_desc('2. fill recognition list...'),
        fill_recognition_list,
        pipeline.print_desc('3. calculate recall...'),
        extract_recalled_samples,
        calc_indicators,
        print_results,
        # pipeline.print_desc('4. draw recall sample...'),
        # draw_sample,
        pipeline.print_desc('completed!'),
    )
    ctx = Context(work_dir=Path(r'C:\Users\<USER>\Downloads\q2_source_uncover_bids_1000'))
    pipe(ctx)


if __name__ == '__main__':
    main()
