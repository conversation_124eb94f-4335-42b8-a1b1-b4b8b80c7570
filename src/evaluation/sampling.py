# !/usr/bin/env python3
"""
模型评估：该模块用于获取评估样本。
"""
from pathlib import Path
from typing import List, Dict

from tqdm import tqdm

from src.tools import tsv, pipeline, pgsql

EXCLUDE_TAG_KEY = 'exclude'
SAMPLE_TAGS = {
    'company': ['公司企业'],
    'house': ['房地产;住宅区'],
    EXCLUDE_TAG_KEY: ['交通设施;加油加气站', '旅游景点'],
}
SELECT_BLU_FACE_SQL = r'''
  WITH result AS (
    SELECT 
      a.face_id AS fid, 
      a.src, 
      a.city_name, 
      b.face_id, 
      b.poi_bid 
    FROM 
      blu_face AS a 
      INNER JOIN blu_face_poi AS b ON a.src != 'IR' AND a.face_id = b.face_id
  ) 
  SELECT 
    fid,
    poi_bid,
    src,
    city_name
  FROM 
    result
  ORDER BY
    city_name;
'''

FACE_ID_INDEX = 0
BID_INDEX = 1
MID_INDEX = 2
SRC_INDEX = 3
CITY_INDEX = 4
TAG_INDEX = 5


class Context:
    def __init__(self, sample_tags: Dict[str, List[str]], sample_number=2000, work_dir=Path('.')):
        self.sample_tags = sample_tags if sample_tags else SAMPLE_TAGS
        self.sample_number = sample_number

        work_dir.mkdir(parents=True, exist_ok=True)
        self.work_dir = work_dir
        self.blu_face_path = work_dir / 'blu_face.tsv'
        self.blu_face_with_tag_path = work_dir / 'blu_face_with_tag.tsv'


def sampling_pipes():
    return pipeline.Pipeline(
        pipeline.print_desc('1. pull blu face population...'),
        pull_blu_face_population,
        pipeline.print_desc('2. fill tag to blu face population...'),
        fill_tag_to_blu_face_population,
        pipeline.print_desc('3. sampling from blu face population...'),
        sampling_from_blu_face_population,
        pipeline.print_desc('completed!'),
    )


def pull_blu_face_population(ctx: Context, proceed):
    if ctx.blu_face_path.exists():
        proceed()
        return

    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        items = pgsql.fetch_all(conn, SELECT_BLU_FACE_SQL)
        # 持久化查询成果，为了断点恢复计算。
        tsv.write_tsv(ctx.blu_face_path, items)

    proceed()


def fill_tag_to_blu_face_population(ctx: Context, proceed):
    def append_tag(c, item):
        face_id, bid, src, city = item
        sql = f'''
            SELECT bid, mid, std_tag FROM poi WHERE bid = '{bid}';
        '''
        result = pgsql.fetch_one(c, sql)
        result = result if result else ('', '', '')
        # 去 poi 库取 mid，而不是从 blu_face_poi, blu_face_poi 有时是空的。
        _, mid, tag = result
        return [face_id, bid, mid, src, city, tag]

    if ctx.blu_face_with_tag_path.exists():
        proceed()
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        blu_faces = list(tsv.read_tsv(ctx.blu_face_path))
        blu_face_tags = (append_tag(conn, x) for x in blu_faces)
        blu_face_tags = list(tqdm(blu_face_tags, total=len(blu_faces)))
        tsv.write_tsv(ctx.blu_face_with_tag_path, blu_face_tags)

    proceed()


def sampling_from_blu_face_population(ctx: Context, proceed):
    population = list(tsv.read_tsv(ctx.blu_face_with_tag_path))
    for name, tags in tqdm(ctx.sample_tags.items()):
        if name == EXCLUDE_TAG_KEY:
            name = 'other'
            exclude_tags = [tag for tag_list in ctx.sample_tags.values() for tag in tag_list]
            include_tag_filter = get_tag_filter(exclude_tags)
            samples = sampling(population, lambda tag: not include_tag_filter(tag), ctx.sample_number)
        else:
            tag_filter = get_tag_filter(tags)
            samples = sampling(population, tag_filter, ctx.sample_number)

        tsv.write_tsv(ctx.work_dir / f'sample.{name}.{ctx.sample_number}.tsv', samples)
        tsv.write_tsv(ctx.work_dir / f'sample_bid.{name}.{ctx.sample_number}.tsv', [[x[BID_INDEX]] for x in samples])

    proceed()


def sampling(items: List, tag_filter, sample_number: int, seed=0):
    filtered_items = [x for x in items if x[TAG_INDEX] and tag_filter(x[TAG_INDEX])]
    # 按城市分层抽样，因为最初导出数据时，使用了 ORDER BY city_name，所以 items 本就是有序的，不必再次排序。
    # filtered_items.sort(key=lambda x: x[CITY_INDEX])
    # 系统抽样
    sample_interval = len(filtered_items) // sample_number
    sample_items = [x for i, x in enumerate(filtered_items) if i % sample_interval == seed]
    return sample_items[:sample_number]


def get_tag_filter(tags: List[str]):
    def tag_filter(expected):
        return any(tag in expected for tag in tags)

    return tag_filter


def main():
    pipe = sampling_pipes()
    ctx = Context(sample_tags=SAMPLE_TAGS, sample_number=1000, work_dir=Path('./output/sampling'))
    pipe(ctx)


if __name__ == '__main__':
    main()
