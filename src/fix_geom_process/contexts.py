# !/usr/bin/env python3
from shapely.validation import make_valid

from src.model.aoi_model import AoiModel
from src.tools.pipeline import Pipeline
from shapely import wkt
from src.fix_geom_process.tools import multipolygon_to_single, get_inner_road, merge_line_helper, get_out_road, \
    get_count_inner_road, get_count_unknown_road, write_log
from src.tools.aoi_tools import get_area, calc_iou


class Context:
    def __init__(self):
        # 原始的geom
        self.origin_geom = ""
        # 街区id
        self.street_faceid = ""
        # 街区geom
        self.street_geom = ""
        # 修形状后完成geom
        self.fixed_geom = ""
        # 正在修形的shape
        self.fixing_shape = None
        # 唯一id
        self.uuid = ""
        # 存储日志的地址
        self.log_dir = ""
        # 错误信息记录
        self.error_dir = ""
        # 查询的aoi_model
        self.aoi_model: AoiModel = None
        # 是否debug模式
        self.is_debug = False
        # 是否处理聚合院落
        self.handle_group_street = False
        # 特殊说明
        self.memo = ""
        # 街区buffer值
        self.street_buffer_val = -0.00005
        # 日志地址
        self.log_file_path = ""
        # 是否有水系
        self.has_water = False
        # 是否过滤aoi
        self.filter_online_aoi = True


def init_street(ctx: Context, proceed):
    """
    初始化街区数据
    :param ctx:
    :param proceed:
    :return:
    """
    # print("init_street ")
    geom_shape = wkt.loads(ctx.origin_geom)
    ctx.fixing_shape = geom_shape
    if ctx.street_geom != "":
        return proceed()
    if ctx.street_faceid != "":
        street_wkt = ctx.aoi_model.get_street_by_file(ctx.street_faceid)
        street_shape = wkt.loads(street_wkt)

        if street_wkt != "":
            street_wkt = make_valid(wkt.loads(street_wkt)).buffer(-0.00005).simplify(0.00004,
                                                                                     preserve_topology=False).wkt
            # 如果街区面积太大，影响速度（会查询整个城市的路网）
            if street_shape.area / geom_shape.area < 10:
                ctx.street_geom = street_wkt
                return proceed()
    if ctx.street_geom == "":
        # 没有街区数据，以当前区域的600米做街区
        ctx.street_geom = geom_shape.buffer(0.006, join_style=1).wkt
        return proceed()


def cut_by_water(ctx: Context, proceed):
    """ 通过水系做切割
    """
    # print("cut_by_water ")
    shape = ctx.fixing_shape
    write_log("before cut_by_water ==" + shape.wkt, ctx.log_file_path)
    if shape is None:
        return proceed()
    water_list = ctx.aoi_model.get_intersection_water_by_geom(ctx.street_geom)
    for water_wkt in water_list:
        water_shape = wkt.loads(water_wkt).buffer(0.00002)
        if shape.intersects(water_shape):
            ctx.has_water = True
            # 水系切割不处理，只提供参考字段
            # shape = shape.difference(water_shape)
    # shape = multipolygon_to_single(shape, 0)
    ctx.fixing_shape = shape
    proceed()


def process_by_bud(ctx: Context, proceed):
    """ 通过楼块修形
    :return:
    """
    # print("process_by_bud")
    shape = ctx.fixing_shape
    write_log("before process by build ==" + shape.wkt, ctx.log_file_path)
    if shape is None:
        return proceed()
    bud_list = ctx.aoi_model.get_building_by_geom(shape)
    for bud in bud_list:
        bud_shape = wkt.loads(bud[1])
        if ctx.fixing_shape.overlaps(bud_shape):
            intersect = shape.intersection(bud_shape)
            if intersect.area / bud_shape.area < 0.3:  # 小于30%移除楼块
                # 交集小于80% 暂时可以压盖
                shape = ctx.fixing_shape.difference(bud_shape)
            elif intersect.area / bud_shape.area > 0.8:
                shape = ctx.fixing_shape.union(bud_shape)
            else:  # 否则需要包括在内
                pass
    ctx.fixing_shape = shape
    proceed()


def cut_by_other_aoi(ctx: Context, proceed):
    """
    通过其他aoi做切割
    :return:
    """
    # print("cut_by_other_aoi")
    if not ctx.filter_online_aoi:
        # print("不过滤在线aoi")
        return proceed()
    shape = ctx.fixing_shape
    write_log("before cut_by_other_aoi ==" + shape.wkt, ctx.log_file_path)
    if shape is None:
        return proceed()
    # 切割aoi时忽略聚合院落
    geom_list = ctx.aoi_model.get_intersection_list_by_geom(ctx.street_geom, 1, True)
    for geom_item in geom_list:
        geom_shape = wkt.loads(geom_item)
        if shape.intersects(geom_shape):
            write_log("== other aoi ==" + geom_item, ctx.log_file_path)
            shape = shape.difference(geom_shape)
    shape = multipolygon_to_single(shape, 0)
    for geom_item in geom_list:
        geom_shape = wkt.loads(geom_item)
        if shape.intersects(geom_shape):
            # 如果一个已经在线的aoi和当前边框是包含关系, 如果区域贴边，可以修一下
            check_shape = shape.difference(geom_shape.buffer(0.00006))
            check_shape = multipolygon_to_single(check_shape, 0)
            if check_shape.contains(geom_shape):
                ctx.fixing_shape = None
                ctx.memo = "区域内部存在wkt"
                return proceed()
            else:
                shape = check_shape
    ctx.fixing_shape = shape
    proceed()


def process_by_inner_road(ctx: Context, proceed):
    """ 通过内部路修形， 这里先使用大思哥的策略
    :return:
    """
    if ctx.fixing_shape is None:
        return proceed()
    write_log("before process_by_inner_road ==" + ctx.fixing_shape.wkt, ctx.log_file_path)
    # print("process_by_inner_road")
    polygon_road = ctx.fixing_shape
    # 使用街区的数据去计算太慢，拿当前区域去获取道路信息
    street_shape = polygon_road.buffer(0.001)
    inner_road_list = get_inner_road(street_shape, ctx.aoi_model)
    # print("inner_road list:", inner_road_list)
    memo = ""
    include_road_num = 0
    pending_list = []
    """
    1、大于80% 直接包含
    2、若没有大于80%的内部路，但有超过2条内部路的，选一条压盖最大的包含，其他小于30%且小于最大包含面积的直接移除
    3、若仅有一组内部路且小于80%，若道路不穿过aoi，则直接包含；若道路穿过aoi，则用道路分割aoi两部分，若分割一部分小于另一部分的10%,则直接移除
    4、若存在大于80%的，包含小于30%全部移除， 30%-80%之间的暂不处理（未发现具体case）
    """
    for line_item in inner_road_list:
        intersect = polygon_road.intersection(line_item['buffer_group'])
        buffer_road = line_item['buffer_group']
        # print("inner line wkt: ", buffer_road.wkt, "; area:", intersect.area, ";out line area:", buffer_road.area)
        if intersect.area / buffer_road.area < 0.8:  # 策略 1  小于80%,待定
            if intersect.area / buffer_road.area > 0:
                # 策略 3
                buffer_road = line_item['buffer_group']
                per = intersect.area / buffer_road.area
                test_split_road = buffer_road.difference(polygon_road)
                if hasattr(test_split_road, 'geoms') and len(test_split_road.geoms) > 1:
                    test_split_aoi = polygon_road.difference(buffer_road)
                    if not hasattr(test_split_aoi, 'geoms') or len(test_split_aoi.geoms) == 1:
                        polygon_road = test_split_aoi
                    else:
                        max_aoi = None
                        max_area = 0
                        need_split = True
                        for split_aoi_item in test_split_aoi.geoms:
                            if max_aoi is None:
                                max_aoi = split_aoi_item
                                max_area = split_aoi_item.area
                                continue
                            if split_aoi_item.area > max_area:
                                if max_area / split_aoi_item.area > 0.1:
                                    need_split = False
                                    break
                                max_area = split_aoi_item.area
                                max_aoi = split_aoi_item
                            elif split_aoi_item.area / max_area > 0.1:
                                need_split = False
                        if need_split:
                            polygon_road = max_aoi
                        # elif per > 0.6:
                        else:
                            pending_list.append({"per": per, "item": line_item, "area": get_area(intersect.wkt)})
                            # polygon_road = polygon_road.union(buffer_road)
                elif test_split_road.length > buffer_road.length:  # 仅压盖
                    polygon_road = polygon_road.difference(buffer_road)
                else:
                    pending_list.append({"per": per, "item": line_item, "area": get_area(intersect.wkt)})
        else:  # 否则需要包括在内
            tmp_geom = polygon_road.union(buffer_road)
            # 交并比>0.95 才进行合并
            if calc_iou(tmp_geom.wkt, polygon_road.wkt) > 0.95:
                polygon_road = tmp_geom
                include_road_num += 1
    # print("first match road:", include_road_num, "; pending list:", len(pending_list))
    if include_road_num == 0:
        if len(pending_list) > 1:  # 策略2
            max_per = 0
            max_item = None
            for line_item in pending_list:
                buffer_road = line_item["item"]['buffer_group']
                per = line_item["per"]
                # print("curr per:", per, "; buffer:", buffer_road.wkt)
                if per > max_per:
                    max_per = per
                    max_item = buffer_road
            # print("max per:", max_per, ";max_item:", max_item)
            polygon_road = polygon_road.union(max_item)
            for line_item in pending_list:
                buffer_road = line_item["item"]['buffer_group']
                per = line_item["per"]
                if per < max_per and per < 0.3:
                    polygon_road = polygon_road.difference(buffer_road)
        elif len(pending_list) == 1:  # 策略 3
            buffer_road = pending_list[0]["item"]['buffer_group']
            per = pending_list[0]["per"]
            # print("buffer_group, wkt", buffer_road.wkt, "; per:", per, "; area:", pending_list[0]["area"])
            if per > 0.6:
                tmp_geom = polygon_road.union(buffer_road)
                if calc_iou(tmp_geom.wkt, polygon_road.wkt) > 0.95:
                    polygon_road = tmp_geom
    else:  # 策略 4
        for line_item in pending_list:
            buffer_road = line_item["item"]['buffer_group']
            per = line_item["per"]
            if per < 0.3:
                polygon_road = polygon_road.difference(buffer_road)

    polygon_road = polygon_road.buffer(0.00005)
    polygon_road = polygon_road.buffer(-0.00005)

    polygon_road = multipolygon_to_single(polygon_road)
    include_road_num = 0
    count_inner_road = get_count_inner_road(polygon_road, ctx.aoi_model)
    for line_item in count_inner_road:
        if polygon_road.intersects(line_item):
            include_road_num += 1
    if include_road_num > 1:
        memo = "存在多条内部路组;"

    unknown_road = get_count_unknown_road(polygon_road, ctx.aoi_model)
    for line_item in unknown_road:
        if polygon_road.intersects(line_item):
            touch = False
            for inner_line_item in count_inner_road:
                if line_item.touches(inner_line_item):
                    touch = True
                    break
            if not touch:
                memo += "存在未知等级道路;"
            break
    if len(count_inner_road) == 0 and len(unknown_road) == 0:
        memo += "道路缺失;"
    ctx.memo = memo
    # print("== fixing_shape ==", ctx.fixing_shape.area)
    # print("== polygon_raad ==", polygon_road.area)
    ctx.fixing_shape = polygon_road
    proceed()


def process_by_out_road(ctx: Context, proceed):
    """ 通过外部路切割
    :return:
    """
    # print("process_by_out_road")
    write_log("before process_by_out_road ==" + ctx.fixing_shape.wkt, ctx.log_file_path)
    if ctx.fixing_shape is None:
        return proceed()
    write_log("before process_by_out_road ==" + ctx.fixing_shape.wkt, ctx.log_file_path)

    res_shape = ctx.fixing_shape
    shape_buffer = ctx.fixing_shape.buffer(0.0001)
    out_road = get_out_road(shape_buffer, ctx.aoi_model)
    # print("out_road list :", out_road)
    for line_item in out_road:
        res_shape = res_shape.difference(line_item["buffer_group"])
    res_shape = multipolygon_to_single(res_shape, 0)
    ctx.fixing_shape = res_shape
    proceed()


def cut_by_street(ctx: Context, proceed):
    """ 通过街区做切割
    :return:
    """
    # print("process_by_street_road")
    if ctx.fixing_shape is None:
        return proceed()
    write_log("before cut_by_street ==" + ctx.fixing_shape.wkt, ctx.log_file_path)
    street = wkt.loads(ctx.street_geom)
    shape = ctx.fixing_shape
    street_buffer = street.buffer(ctx.street_buffer_val)  # 道路是线，需要buffer
    if shape.intersects(street_buffer):
        shape = shape.intersection(street_buffer)
    ctx.fixing_shape = shape
    return proceed()


def after_process(ctx: Context, proceed):
    """
    修形完成后处理
    :param ctx:
    :param proceed:
    :return:
    """
    # print("after_process")
    if ctx.fixing_shape is None:
        return proceed()
    shape = ctx.fixing_shape
    shape = shape.buffer(-0.00005)
    shape = shape.buffer(+0.00005)

    shape = multipolygon_to_single(shape, 0)
    shape = shape.simplify(0.00001, preserve_topology=False)
    if not ctx.filter_online_aoi:
        ctx.fixing_shape = shape
        return proceed()
    # 检查完成后的区域是否压盖其他的基础院落aoi，压盖了过滤掉
    geom_list = ctx.aoi_model.get_intersection_list_by_geom(ctx.street_geom, 1, True)
    for geom_item in geom_list:
        geom_shape = wkt.loads(geom_item)
        if shape.buffer(-0.00005).intersects(geom_shape):
            ctx.memo += "修形完成后的区域压盖基础院落"
            ctx.fixing_shape = None
            return proceed
    ctx.fixing_shape = shape
    return proceed()


PIPELINE = Pipeline(
    init_street,
    cut_by_water,
    process_by_bud,
    cut_by_other_aoi,
    process_by_inner_road,
    process_by_out_road,
    cut_by_street,
    after_process,
)
