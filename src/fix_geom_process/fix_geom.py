# !/usr/bin/env python3
import os.path
import sys

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)
from src.fix_geom_process.contexts import Context, PIPELINE
from src.model.aoi_model import AoiModel


class FixGeom:
    def __init__(self, origin_geom, uuid, street_faceid="", street_geom="", log_dir="", aoi_model: AoiModel = None,
                 filter_online_aoi=True):
        """
        执行修形操作，导出一个方法，可以直接调用
        :param origin_geom: 语义分割后直接输出的原始的geom
        :param uuid: 唯一标识，用于数据跟踪
        :param street_faceid: 街区的face_id
        :param street_geom: 街区的id
        :param aoi_model: 完成实例化的aoi_model，可以共用之前的数据库连接，减少连接次数
        :param log_dir: 日志文件夹
        :param filter_online_aoi： 是否过滤在线的aoi
        """
        ctx = Context()
        ctx.origin_geom = origin_geom
        ctx.uuid = uuid
        ctx.street_faceid = street_faceid
        ctx.street_geom = street_geom
        if aoi_model is not None:
            ctx.aoi_model = aoi_model
        if log_dir == "":
            log_dir = ROOT_PATH + "/fix_geom_log"
        ctx.log_dir = log_dir + "/log"
        ctx.filter_online_aoi = filter_online_aoi
        ctx.log_file_path = ctx.log_dir + "/" + uuid + ".log"
        ctx.error_dir = log_dir + "/error"
        self.ctx = ctx
        os.makedirs(ctx.log_dir, exist_ok=True)
        os.makedirs(ctx.error_dir, exist_ok=True)

    def run(self):
        """
        :return:
        """
        ctx = self.ctx
        if ctx.aoi_model is None:
            with AoiModel() as aoi_model:
                ctx.aoi_model = aoi_model
                PIPELINE(ctx)
        else:
            PIPELINE(ctx)
        return {"wkt": ctx.fixing_shape.wkt if ctx.fixing_shape is not None else "", "memo": ctx.memo,
                "has_water": ctx.has_water}

    def __call__(self, *args, **kwargs):
        return self.run()


if __name__ == '__main__':
    geom = "POLYGON((115.84683343 28.691426774117645, 115.84680131500001 28.691405348235293, 115.846758495 28.691394635294117, 115.8467103225 28.69134642705882, 115.84669426500001 28.691341070588233, 115.84666215 28.691308931764706, 115.846651445 28.691276792941174, 115.84661397750001 28.69123929764706, 115.84659792000001 28.69123394117647, 115.8465604525 28.691164307058823, 115.84649622250001 28.691078603529412, 115.846458755 28.69099825647059, 115.8464319925 28.690971474117646, 115.84642664 28.690944691764706, 115.84638917250001 28.690891127058823, 115.8463784675 28.690848275294115, 115.84633564750001 28.69078399764706, 115.84631959000001 28.690730432941177, 115.84626071250001 28.690666155294117, 115.84621254000001 28.690564382352942, 115.8461536625 28.690478678823528, 115.8461536625 28.690462609411764, 115.84613225250001 28.690435827058824, 115.84611619500001 28.690392975294117, 115.84606267000001 28.69031798470588, 115.84603055500001 28.690184072941175, 115.8461215475 28.690189429411763, 115.84613760500001 28.69019478588235, 115.8461536625 28.690221568235295, 115.8462821225 28.690248350588234, 115.84621789250001 28.690119795294116, 115.84621254000001 28.690055517647057, 115.84616436750001 28.690007309411765, 115.846201835 28.689985883529413, 115.846373115 28.68993231882353, 115.84649087000001 28.68987339764706, 115.846501575 28.68983590235294, 115.84654439500001 28.689771624705884, 115.84653904250001 28.689643069411765, 115.8465283375 28.689589504705882, 115.8465551 28.689546652941175, 115.84659792000001 28.689525227058823, 115.8466782075 28.68950380117647, 115.8467317325 28.689466305882352, 115.84681202 28.68944488, 115.84683343 28.689423454117648, 115.84687089750001 28.689412741176472, 115.8469137175 28.689375245882353, 115.84694583250001 28.68935917647059, 115.847058235 28.68932168117647, 115.8471599325 28.689273472941178, 115.84725627750001 28.689246690588234, 115.84734727 28.689198482352943, 115.8475453125 28.68914491764706, 115.847614895 28.68914491764706, 115.84770588750001 28.689177056470587, 115.8477487075 28.689214551764707, 115.84776476500001 28.689246690588234, 115.84779688 28.689278829411766, 115.84792534 28.68950380117647, 115.84796280750001 28.689610930588234, 115.84798957000001 28.68965378235294, 115.8480056275 28.689712703529413, 115.8480056275 28.689766268235292, 115.84801633250001 28.689766268235292, 115.84803239 28.689787694117648, 115.84807521 28.689814476470588, 115.8481019725 28.689889467058823, 115.84811267750001 28.68989482352941, 115.84816620250001 28.690007309411765, 115.84821437500001 28.69006087411765, 115.8482518425 28.690119795294116, 115.8482625475 28.690119795294116, 115.8482839575 28.690178716470587, 115.84831607250001 28.69020549882353, 115.8483374825 28.69023763764706, 115.84835354 28.69029120235294, 115.84836959750001 28.690301915294118, 115.84837495000001 28.690328697647058, 115.84842312250001 28.690392975294117, 115.84843918 28.690425114117648, 115.84843918 28.69044654, 115.8484659425 28.690484035294116, 115.84846059 28.690612590588234, 115.84839100750001 28.690789354117648, 115.84831072 28.690880414117647, 115.848235785 28.690917909411763, 115.84820367 28.690950048235294, 115.8481769075 28.690950048235294, 115.8480056275 28.69102503882353, 115.8479199875 28.69104646470588, 115.847871815 28.691078603529412, 115.8478557575 28.691078603529412, 115.8477701175 28.691121455294116, 115.847700535 28.691142881176468, 115.84765771500001 28.69116966352941, 115.8476416575 28.69116966352941, 115.8475988375 28.691191089411763, 115.847572075 28.691191089411763, 115.8474917875 28.691228584705883, 115.847443615 28.69123929764706, 115.84735797500001 28.691282149411762, 115.84717599000001 28.691341070588233, 115.84714922750001 28.691357139999997, 115.84707429250001 28.691373209411765, 115.84705288250001 28.69138927882353, 115.84702076750001 28.69138927882353, 115.84700471000001 28.691405348235293, 115.8469833 28.69141070470588, 115.8468923075 28.691426774117645, 115.84683343 28.691426774117645))"
    f = FixGeom(geom, '8b612c95b2ecbbf4b34fa551dedad545')
    ret = f()
    print(ret)
