# !/usr/bin/env python3
from pathlib import Path
from typing import Any

from tqdm import tqdm
from osgeo import ogr

from src.tools import linq


class Aoi:
    def __init__(self, city: str, aoi_id: str, aoi_tag: str, geom: Any, useless=False):
        self.city: str = city
        self.aoi_id: str = aoi_id
        self.aoi_tag: str = aoi_tag
        self.geom: Any = geom
        self.useless: bool = useless


def read_aoi_items(file_path: Path):
    lines = file_path.read_text(encoding='utf8').split('\n')
    for line in lines:
        if not line:
            continue

        columns = [x for x in line.split('\t') if x]
        yield Aoi(
            columns[0],
            columns[2],
            columns[3],
            ogr.CreateGeometryFromWkt(columns[1])
        )


def get_intersection_aoi(aoi_list):
    for i in range(len(aoi_list)):
        for j in range(i + 1, len(aoi_list)):
            a = aoi_list[i]
            b = aoi_list[j]
            if a.geom.Intersects(b.geom):
                yield a, b


def normalize(geom):
    buffer = 3e-5
    return geom.Buffer(-buffer).Buffer(buffer)


def flatten(geom):
    count = geom.GetGeometryCount()
    if count < 2:
        return [geom]

    result = []
    for i in range(count):
        result.append(geom.GetGeometryRef(i))

    return result


def max_geom(geom_list):
    max_area_geom = None
    max_area = 0
    for geom in geom_list:
        if not max_area_geom:
            max_area_geom = geom
            max_area = geom.Area()
            continue

        area = geom.Area()
        if area > max_area:
            max_area_geom = geom
            max_area = area

    return ogr.CreateGeometryFromWkt(str(max_area_geom))


def get_iou(geom1, geom2):
    intersection_area = geom1.Intersection(geom2).Area()
    union_area = geom1.Union(geom2).Area()
    return intersection_area / union_area


def resolve_intersection(file_path: Path):
    count_dict = {
        'intersection': 0,
        'contain': 0,
        'too much change': 0,
        'multi polygon': 0,
    }
    aoi_group_dict = linq.group_by(read_aoi_items(file_path), lambda x: x.city)
    abnormal_cases = []
    for _, aoi_list in tqdm(aoi_group_dict.items()):
        for aoi1, aoi2 in get_intersection_aoi(aoi_list):
            count_dict['intersection'] += 1
            # 处理包含关系
            if aoi1.geom.Contains(aoi2.geom):
                count_dict['contain'] += 1
                aoi2.useless = True
                continue

            if aoi2.geom.Contains(aoi1.geom):
                count_dict['contain'] += 1
                aoi1.useless = True
                continue

            # 处理压盖关系
            area1 = aoi1.geom.Area() * 1e10
            area2 = aoi2.geom.Area() * 1e10

            mut_aoi, immut_aoi = (aoi1, aoi2) if area1 > area2 else (aoi2, aoi1)
            mut_aoi_geom = mut_aoi.geom
            mut_aoi.geom = mut_aoi.geom.Difference(immut_aoi.geom.Buffer(1e-6))

            # 处理多碎片问题
            if mut_aoi.geom.GetGeometryType() == ogr.wkbMultiPolygon:
                count_dict['multi polygon'] += 1
                geoms = flatten(mut_aoi.geom)
                mut_aoi.geom = max_geom(geoms)

            mut_aoi.geom = normalize(mut_aoi.geom)
            if get_iou(mut_aoi_geom, mut_aoi.geom) < 0.9:
                count_dict['too much change'] += 1
                abnormal_cases.append(mut_aoi)

    abnormal_cases_output_path = Path(file_path.parent / f'{file_path.stem}_abnormal_cases.txt')
    repaired_output_path = Path(file_path.parent / f'{file_path.stem}_repaired.txt')

    abnormal_cases_output_path.write_text('\n'.join([str(x.geom) for x in abnormal_cases]))

    lines = [f'\t\t\t{aoi.city}\t\t{aoi.geom}\t{aoi.aoi_id}\t{aoi.aoi_tag}'
             for aoi_list in aoi_group_dict.values()
             for aoi in aoi_list
             if not aoi.useless]
    repaired_output_path.write_text('\n'.join(lines), encoding='utf8')
    print('\n'.join([f'- {k}: {v}' for k, v in count_dict.items()]))


if __name__ == '__main__':
    # TODO: 替换文件路径，文件格式为 AOI 上线格式。
    resolve_intersection(Path(r'C:\Users\<USER>\Downloads\product_poi_tag_41_200.txt'))
