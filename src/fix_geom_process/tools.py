# !/usr/bin/env python3
from shapely import geometry, wkt
import datetime


def multipolygon_to_single(shape, buffer=0.00001, merge_first=1):
    """ multipolygon合并
        通过buffer再收缩，合并近的，如果仍然是多个取面积最大的
    :param shape:
    :param buffer:
    :param merge_first:
    :return:
    """
    if not (hasattr(shape, 'geoms') and len(shape.geoms) > 0) \
            and not (hasattr(shape, 'interiors') and len(shape.interiors) > 0):
        return shape
    if buffer > 0:
        if merge_first == 1:  # 部分场景需要合并，部分场景需要分割
            shape = shape.buffer(buffer)
            shape = shape.buffer(-buffer)
        else:
            shape = shape.buffer(-buffer)
            # print("buffer_res_1:", shape.wkt)
            shape = shape.buffer(buffer)
    # print("buffer_res_2:", shape.wkt)
    if not (hasattr(shape, 'geoms') and len(shape.geoms) > 0) \
            and not (hasattr(shape, 'interiors') and len(shape.interiors) > 0):
        return shape
    if hasattr(shape, 'geoms'):
        max_shape = shape.geoms[0]
        for shape_item in shape.geoms:
            if max_shape.area < shape_item.area:
                max_shape = shape_item
        shape = max_shape
    if hasattr(shape, 'interiors') and len(shape.interiors) > 0:
        shape = geometry.Polygon(shape.exterior.coords)
    return shape


def merge_line_helper(shape, link_group, line_dict):
    """ 内部路组合辅助函数
    :param shape:
    :param link_group:
    :param line_dict:
    :return:
    """
    for i, line in line_dict.items():
        if shape.touches(line["shape"]):
            link_group.append(line["link_id"])
            shape = shape.union(line["shape"])
            del line_dict[i]
            return merge_line_helper(shape, link_group, line_dict)
    return shape, link_group, line_dict


def merge_line_by_node_helper(link_group, link_node_list, line_dict):
    """ 通过node_id聚合内部路组辅助函数
    :param link_group:
    :param link_node_list:
    :param line_dict:
    :return:
    """
    for i, line in line_dict.items():
        node_id = ""
        if line["s_nid"] in link_node_list:
            node_id = line["s_nid"]
        elif line["e_nid"] in link_node_list:
            node_id = line["e_nid"]

        if node_id != "":
            diff_form = False  # 是否是不同形态
            for item in link_group:
                # 一边是内部路，另一边不是
                # 两边kind不一致， 则认为是不同的内部路组
                if (
                        item["s_nid"] == node_id or item["e_nid"] == node_id or
                        (item["s_adjoint_nid"] != "" and item["s_adjoint_nid"] == node_id) or
                        (item["e_adjoint_nid"] != "" and item["e_adjoint_nid"] == node_id)
                ) and \
                        (
                                ("52" in item["form"] and "52" not in line["form"])
                                or
                                ("52" not in item["form"] and "52" in line["form"])
                        ):
                    # print("diff_form: ", item['shape'].wkt, "line :", line['shape'].wkt)
                    diff_form = True
            if diff_form:
                continue
            link_node_list.append(line["s_nid"])
            link_node_list.append(line["e_nid"])
            if line["s_adjoint_nid"] != "":
                link_node_list.append(line["s_adjoint_nid"])
            if line["e_adjoint_nid"] != "":
                link_node_list.append(line["e_adjoint_nid"])
            link_group.append(line)
            del line_dict[i]
            return merge_line_by_node_helper(link_group, link_node_list, line_dict)

    return link_group, link_node_list, line_dict


def get_road_width(direction, kind, lane_l, lane_r, form):
    """ 获得道路路宽
    :param direction:
    :param kind:
    :param lane_l:
    :param lane_r:
    :param form:
    :return: float 路宽
    """
    width = 3
    form_list = form.split(",")
    is_jct = is_island = is_ic = False
    if "11" in form_list:  # junction 立交桥
        is_jct = True
    if "33" in form_list:  # 环岛
        is_island = True
    if "10" in form_list:  # interchange 高速-普通连接处
        is_ic = True

    if direction == 1:
        n_lane_num = lane_l + lane_r
        if kind == 1:
            if n_lane_num == 2:
                width = n_lane_num * 1.5 * 3
            elif n_lane_num > 3:
                width = n_lane_num * 1.0 * 3
        elif kind == 2:
            if n_lane_num == 2:
                width = n_lane_num * 1.5 * 3
            elif n_lane_num >= 3:
                width = n_lane_num * 1.0 * 3
        elif kind == 3:
            if n_lane_num == 2:
                width = n_lane_num * 1.85 * 3
            elif n_lane_num >= 3:
                width = n_lane_num * 1.0 * 3
        elif kind == 4:
            if n_lane_num == 2:
                width = n_lane_num * 1.85 * 3
            elif n_lane_num == 3:
                width = n_lane_num * 1.5 * 3
            elif n_lane_num >= 4:
                width = n_lane_num * 1.0 * 3
        elif kind == 6:
            if n_lane_num == 2:
                width = n_lane_num * 1.85 * 3
            elif n_lane_num == 3 or n_lane_num == 4:
                width = n_lane_num * 1.5 * 3
            elif n_lane_num >= 5:
                width = n_lane_num * 1.0 * 3
        elif kind == 7:
            if n_lane_num == 2:
                width = n_lane_num * 1.85 * 3
            elif n_lane_num == 3:
                width = n_lane_num * 1.5 * 3
            elif n_lane_num >= 4:
                width = n_lane_num * 1.0 * 3
        elif kind == 8:
            if n_lane_num >= 2:
                width = n_lane_num * 1.0 * 2
        elif kind == 9:
            if n_lane_num >= 2:
                width = n_lane_num * 1.0 * 3
        elif kind == 10:
            if n_lane_num >= 2:
                width = n_lane_num * 0.5 * 3
    else:
        n_lane_num = 0
        if direction == 2:
            n_lane_num = lane_r
        elif direction == 3:
            n_lane_num = lane_l

        if kind == 1:
            if n_lane_num == 1 and not is_jct and not is_ic:
                width = n_lane_num * 3.0 * 3
            elif n_lane_num == 1 and (is_jct or is_ic):
                width = n_lane_num * 2.2 * 3
            elif n_lane_num == 2 and not is_jct and not is_ic:
                width = n_lane_num * 1.5 * 3
            elif n_lane_num == 2 and (is_jct or is_ic):
                width = n_lane_num * 1.3 * 3
            elif n_lane_num == 3:
                width = n_lane_num * 1.3 * 3
            elif n_lane_num >= 4:
                width = n_lane_num * 1.0 * 3
        elif kind == 2:
            if n_lane_num == 1 and not is_jct and not is_ic:
                width = n_lane_num * 3.0 * 3
            elif n_lane_num == 1 and (is_jct or is_ic):
                width = n_lane_num * 2.2 * 3
            elif n_lane_num == 2 and not is_jct and not is_ic:
                width = n_lane_num * 1.5 * 3
            elif n_lane_num == 2 and (is_jct or is_ic):
                width = n_lane_num * 1.3 * 3
            elif n_lane_num == 3 and not is_jct and not is_ic:
                width = n_lane_num * 1.3 * 3
            elif n_lane_num == 3 and (is_jct or is_ic):
                width = n_lane_num * 1.1 * 3
            elif n_lane_num >= 4:
                width = n_lane_num * 1.0 * 3
        elif kind == 3 or kind == 4 or kind == 6:
            if n_lane_num == 1:
                width = n_lane_num * 2.0 * 3
            elif n_lane_num == 2:
                width = n_lane_num * 1.4 * 3
            elif n_lane_num >= 3:
                width = n_lane_num * 1.0 * 3
        elif kind == 7:
            if n_lane_num == 1:
                width = n_lane_num * 1.6 * 3
            elif n_lane_num >= 2:
                width = n_lane_num * 1.0 * 3
        elif kind == 8:
            if n_lane_num == 1:
                width = n_lane_num * 2.0 * 2
            if n_lane_num >= 2:
                width = n_lane_num * 1.0 * 2
        elif kind == 9:
            if n_lane_num == 1:
                width = n_lane_num * 1.5 * 3
            elif n_lane_num >= 2:
                width = n_lane_num * 1.0 * 3
        elif kind == 10:
            if n_lane_num >= 1:
                width = n_lane_num * 1.0 * 2
    if is_island and (kind == 8 or kind == 9 or kind == 10):
        width = width * 1.5
    elif is_island and (kind == 1 or kind == 2 or kind == 3 or kind == 4 or kind == 6 or kind == 7):
        width = width * 2.0

    return width


def get_out_road(shape, q):
    """ 获取外部路
    :param shape:
    :return:
    """
    line_list = q.get_out_line_by_geom_v2(shape.wkt)
    line_dict = {}
    line_list_filter = []
    n = 0
    for line_info in line_list:
        link_id = line_info[0]
        kind = line_info[1]
        form = line_info[2]
        road_dir = line_info[3]
        lane_l = line_info[4]
        lane_r = line_info[5]
        line_wkt = line_info[6]
        if kind >= 8:
            if road_dir == 1:  # kind 等于8 可以双向通行，算内部路
                continue
            name = q.get_name_by_link_id(link_id)  # kind 等于9， 名称有辅路才算外部路
            if "辅路" not in name:
                continue
        line_shape = wkt.loads(line_wkt)
        line_item = {
            "link_id": link_id,
            "kind": int(kind),
            "form": form,
            "dir": int(road_dir),
            "lane_l": int(lane_l),
            "lane_r": int(lane_r),
            "shape": line_shape
        }
        line_dict[n] = line_item
        line_list_filter.append(line_item)
        n += 1
    i = 0
    res_list = []
    link_group_list = []
    len_dict = len(line_dict)
    while i < len_dict:
        if i not in line_dict:
            i += 1
            continue
        line_item = line_dict[i]
        del line_dict[i]
        link_group = [line_item["link_id"]]
        res_shape, link_group, line_dict = merge_line_helper(line_item["shape"], link_group, line_dict)
        link_group_list.append(link_group)
        # res_list.append(res_shape)
        i += 1
    for link_group in link_group_list:
        buffer_link_shape = None
        multi_link_shape = None
        for line_item in line_list_filter:
            # line_item = line_list_filter[k]
            if line_item["link_id"] in link_group:
                if multi_link_shape is None:
                    multi_link_shape = line_item["shape"]
                else:
                    multi_link_shape = multi_link_shape.union(line_item["shape"])
                line_width = get_road_width(line_item['dir'], line_item['kind'], line_item["lane_l"],
                                            line_item["lane_r"], line_item["form"])
                buffer_len = 0.000006 * line_width + 0.000001
                buffer_road = line_item["shape"].buffer(buffer_len, 16, None, geometry.CAP_STYLE.flat)
                if buffer_link_shape is None:
                    buffer_link_shape = buffer_road
                else:
                    buffer_link_shape = buffer_link_shape.union(buffer_road)

        # 填补空隙
        buffer_link_shape = buffer_link_shape.buffer(0.00001)

        res_list.append({"buffer_group": buffer_link_shape, "multi_shape": multi_link_shape})

    return res_list


def get_inner_road(shape, q, out_road_shape=None):
    """ 获取内部路
    :param shape:
    :param out_road_shape:
    :return:
    """
    line_list = q.get_line_by_geom_v2(shape.wkt)
    line_dict = {}
    line_list_filter = []
    n = 0
    for line_info in line_list:
        link_id = line_info[0]
        s_nid = line_info[1]
        e_nid = line_info[2]
        s_adjoint_nid = line_info[3]
        e_adjoint_nid = line_info[4]
        kind = line_info[5]
        form = line_info[6]
        road_dir = line_info[7]
        lane_l = line_info[8]
        lane_r = line_info[9]
        line_wkt = line_info[10]

        if int(kind) >= 8:
            if "52" not in form and road_dir != 1:  # kind 等于8 单行道，算外部路
                continue
            name = q.get_name_by_link_id(link_id)  # kind 等于9， 名称有辅路算外部路
            if "辅路" in name:
                continue

        line_shape = wkt.loads(line_wkt)
        line_item = {
            "link_id": link_id,
            "kind": int(kind),
            "s_nid": s_nid,
            "e_nid": e_nid,
            "s_adjoint_nid": s_adjoint_nid,
            "e_adjoint_nid": e_adjoint_nid,
            "form": form,
            "dir": int(road_dir),
            "lane_l": int(lane_l),
            "lane_r": int(lane_r),
            "shape": line_shape
        }
        line_dict[n] = line_item
        line_list_filter.append(line_item)
        n += 1
    i = 0
    res_list = []
    link_group_list = []
    len_dict = len(line_dict)
    # print("line_list:", line_dict)
    while i < len_dict:
        if i not in line_dict:
            i += 1
            continue
        line_item = line_dict[i]
        del line_dict[i]
        link_node_group = [line_item["s_nid"], line_item["e_nid"]]
        if line_item["s_adjoint_nid"] != "":
            link_node_group.append(line_item["s_adjoint_nid"])
        if line_item["e_adjoint_nid"] != "":
            link_node_group.append(line_item["e_adjoint_nid"])

        link_group, link_node_group, line_dict = merge_line_by_node_helper([line_item], link_node_group, line_dict)
        # print("link_node_group: ", link_node_group)
        link_group_list.append(link_group)
        i += 1

    if out_road_shape is None:
        for i in get_out_road(shape, q):
            if out_road_shape is None:
                out_road_shape = i['buffer_group']
            else:
                out_road_shape = out_road_shape.union(i['buffer_group'])
    #    print("out_road_shape:", out_road_shape.wkt)

    for link_group in link_group_list:
        buffer_link_shape = None
        multi_link_shape = None
        for line_item in link_group:
            if multi_link_shape is None:
                multi_link_shape = line_item["shape"]
            else:
                multi_link_shape = multi_link_shape.union(line_item["shape"])
            line_width = get_road_width(line_item['dir'], line_item['kind'], line_item["lane_l"],
                                        line_item["lane_r"], line_item["form"])
            buffer_len = 0.000006 * line_width + 0.000001
            buffer_road = line_item["shape"].buffer(buffer_len, 16, None, geometry.CAP_STYLE.flat)
            if buffer_link_shape is None:
                buffer_link_shape = buffer_road
            else:
                buffer_link_shape = buffer_link_shape.union(buffer_road)

        # 填补空隙 圆滑
        buffer_link_shape = buffer_link_shape.buffer(0.00001)

        # print("inner origin:", multi_link_shape.wkt)
        # continue

        split = False
        if out_road_shape is not None and out_road_shape.intersects(buffer_link_shape):  # 在通过外部路切割一次
            buffer_link_shape = buffer_link_shape.difference(out_road_shape)
            multi_link_shape = multi_link_shape.difference(out_road_shape)
            if hasattr(buffer_link_shape, 'geoms') and len(buffer_link_shape.geoms) > 0 \
                    and hasattr(multi_link_shape, 'geoms') and len(multi_link_shape.geoms) > 0:
                split = True
                # print("need split ", buffer_link_shape.wkt)
                for polygon_item in buffer_link_shape.geoms:
                    # print("diff buffer_group:", polygon_item.wkt)
                    res_list.append({"buffer_group": polygon_item, "multi_shape": multi_link_shape})
            elif hasattr(buffer_link_shape, 'geoms') and len(buffer_link_shape.geoms) > 0 \
                    and not hasattr(multi_link_shape, 'geoms'):
                max_shape = buffer_link_shape.geoms[0]
                for shape_item in buffer_link_shape.geoms:
                    if max_shape.area < shape_item.area:
                        max_shape = shape_item
                # print("get max area, ", max_shape.wkt)
                buffer_link_shape = max_shape
            elif not hasattr(buffer_link_shape, 'geoms') \
                    and hasattr(multi_link_shape, 'geoms') and len(multi_link_shape.geoms) > 0:
                max_shape = multi_link_shape.geoms[0]
                for shape_item in multi_link_shape.geoms:
                    if max_shape.length < shape_item.length:
                        max_shape = shape_item
                multi_link_shape = max_shape

        if not split and buffer_link_shape.area > 0:
            res_list.append({"buffer_group": buffer_link_shape, "multi_shape": multi_link_shape})

    return res_list


def get_count_inner_road(shape, q):
    """ 获取内部路
    :param shape:
    :return:
    """
    line_list = q.get_inner_line_by_geom(shape.wkt)
    line_dict = {}
    n = 0
    for line_wkt in line_list:
        line_shape = wkt.loads(line_wkt)
        line_dict[n] = line_shape
        n += 1
    i = 0
    res_list = []
    len_dict = len(line_dict)
    while i < len_dict:
        if i not in line_dict:
            i += 1
            continue
        shape = line_dict[i]
        del line_dict[i]
        res_shape, line_dict = merge_line_helper_count(shape, line_dict)
        res_list.append(res_shape)
        i += 1
    return res_list


def get_count_unknown_road(shape, q):
    """ 获取8级路
    :param shape:
    :return:
    """
    # print("===== unknow road" + shape.wkt)
    line_list = q.get_unknown_road_by_geom(shape.wkt)
    # print(line_list)
    line_dict = {}
    n = 0
    for line_wkt in line_list:
        line_shape = wkt.loads(line_wkt)
        line_dict[n] = line_shape
        n += 1
    i = 0
    res_list = []
    len_dict = len(line_dict)
    while i < len_dict:
        if i not in line_dict:
            i += 1
            continue
        shape = line_dict[i]
        del line_dict[i]
        res_shape, line_dict = merge_line_helper_count(shape, line_dict)
        res_list.append(res_shape)
        i += 1

    return res_list


def merge_line_helper_count(shape, line_dict):
    """
    :param shape:
    :param line_dict:
    :return:
    """
    for i, line in line_dict.items():
        if shape.touches(line):
            shape = shape.union(line)
            del line_dict[i]
            return merge_line_helper_count(shape, line_dict)
    return shape, line_dict


def write_log(msg: str, log_file):
    """
    记录日志
    """
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    w_msg = f"{now}\t{msg}\n"
    with open(log_file, 'a+') as f:
        f.write(w_msg)
