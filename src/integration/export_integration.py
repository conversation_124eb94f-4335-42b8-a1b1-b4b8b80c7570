# !/usr/bin/env python3
"""
接入一体化情报模块
"""
from dataclasses import asdict, field, dataclass
import requests
from typing import Any, Iterable, Dict

from tqdm import tqdm

url_integration = "https://mapde-poi.baidu-int.com/prod/integration/qbSyncV2"

STATUS_PENDING = 0  # 任务待创建：情报生成的初始状态
STATUS_WORK_TODO = 1  # 任务已创建
STATUS_WORK_DONE = 2  # 制作完成 制作完成，成果待回库，其他环节不可用
STATUS_BACK_DB_DONE = 3  # 回库完成 制作完成，且成果回库，其他环节可拉取 为终态
STATUS_DATA_CORRECT = 4  # 数据正确：情报核实数据是正确的无需修改，为终态
STATUS_CANNOT_WORK = 5  # 无法制作 情报无效 当前工艺水平下无法判断或不需要制作 为终态
STATUS_CHECK_TODO = 6  # 无法核实-待采集 资料未覆盖，无法核实
STATUS_GATHER = 7  # 无法核实-投放采集 无法核实，已投放路淘、众源等采集资料 为终态

WORK_TYPE_UNKNOWN = 0  # 未知作业类型
WORK_TYPE_AUTO = 1  # 自动批处理作业
WORK_TYPE_MANUAL = 2  # 人工作业

CODE_SUCCESS = 0  # 成功
CODE_DATA_ERROR = -1  # 失败
CODE_NETWORK_ERROR = -2  # 网络错误

RETRY_COUNT = 3


@dataclass
class IntegrationIntelligence:
    #用户传入的唯一id 追踪case或去重使用
    ref_qb_id: str 

    #说明情报的处理方式以及需要处理的业务线
    src: int
    work_factors: str 
    work_type: int = WORK_TYPE_UNKNOWN 
    status: int = STATUS_PENDING
    strategy_type: str = '' #需要创建人工作业计划的类型，不是策略名称！！！
    
    #唯一定位情报关键信息的参数，如果都相等可能是情报重复或者元素发生多次变化
    main_poi_bid: str = ''
    ref_id: str = ''
    action: str = ''
    mark_geom: str = ''
    extra: Dict[str, Any] = field(default_factory=dict)

    #附加信息，如标记是否同一批创建计划等；
    ref_qb_batch_id: str = ''
    ref_qb_batch_name: str = ''
    memo: str = ''
    ref_factors: str = ''
    priority: int = 0
    nav_risk: int = 0

    
def to_service(intelligences: Iterable[IntegrationIntelligence]):
    """
    返回参数:
        - 成功: (CODE_SUCCESS, 一体化平台返回结果)
        - 数据错误: (CODE_DATA_ERROR, 一体化平台返回结果)
        - 网络错误: (CODE_NETWORK_ERROR, None)
    """

    intelligence_list = list(intelligences)
    for intel_data in tqdm(intelligence_list):
        req = None
        for _ in range(RETRY_COUNT):
            req = requests.post(url_integration, json=asdict(intel_data))
            if req.status_code == 200:
                break

        if req and req.status_code != 200:
            yield CODE_NETWORK_ERROR, None
