# !/usr/bin/env python3
"""
用于推送 AOI 常规清洗高功效作业清单。
"""
import datetime

from src.integration.tools import Intelligence
from src.tools import pgsql
from src.tools import aoi_tools

STRATEGY_NAME = "post aoi cleaning high effect"
STRATEGY_TYPE = "302"
VALID_TIMESPAN = datetime.timedelta(days=30)
SRC_LIST = ["mis_add_aoi", "poi_stock", "b_to_c"]


def filter_intelligences(intelligences: list[Intelligence]) -> list[Intelligence]:
    """过滤情报。

    Args:
        intelligences (list[Intelligence]): 情报列表。

    Returns:
        list[Intelligence]: 经过过滤后的情报列表。

    """
    # 可进一步自定义过滤逻辑，比如根据情报的置信度来决定是否需要下发。

    intelligences_res = []
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        for intelligences_tmp in intelligences:
            if intelligences_tmp.src == "mis_add_aoi":
                sql = "select std_tag from poi where bid = %s"
                std_tag = pgsql.fetch_one(conn, sql, [intelligences_tmp.element_id])
                if std_tag is None or std_tag[0] in aoi_tools.get_important_aoi_tag():
                    continue
            intelligences_res.append(intelligences_tmp)
    return intelligences_res
