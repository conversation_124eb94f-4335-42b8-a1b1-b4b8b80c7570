# !/usr/bin/env python3
"""
用于推送 “聚合院落新增” 作业工单。
"""
import datetime

from src.integration.tools import Intelligence

STRATEGY_NAME = 'post new poi'
STRATEGY_TYPE = '37'
VALID_TIMESPAN = datetime.timedelta(days=30)
SRC_LIST = [
    'cluster_yard_without_basic_aoi',
]


def filter_intelligences(intelligences: list[Intelligence]) -> list[Intelligence]:
    """过滤情报。

    Args:
        intelligences (list[Intelligence]): 情报列表。

    Returns:
        list[Intelligence]: 经过过滤后的情报列表。

    """
    return intelligences
