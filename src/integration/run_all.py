# !/usr/bin/env python3
"""
总执行脚本：根据给定的数据源，运行本模块下的所有子脚本。
Details: 本脚本可以自动发现本目录下的所有策略，并执行它。能够被发现的策略模块应当具备以下约定：
- 文件名前缀为 `filter_*.py`
- 文件中包含四个字段：`STRATEGY_NAME`、`STRATEGY_TYPE`、`VALID_TIMESPAN`、`SRC_LIST`
- 文件中包含一个函数：`filter(intelligences) -> list[Intelligence]`

see: https://ku.baidu-int.com/d/DxWwDc7CEx2JMs
"""
import datetime
import importlib
import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import Callable

from tqdm import tqdm

from src.integration.export_integration import to_service, IntegrationIntelligence
from src.integration.tools import Intelligence
from src.tools import pipeline, pgsql

MINOR_POI_CONFIG = {
    "host": "**************",
    "db": "poi_online",
    "port": "8532",
    "user": "poi_aoi_r",
    "pwd": "poi_aoi_r",
}
SRC_AOI = 4  # AOI边框产线
WORK_TYPE_MANUAL = 2  # 人工作业
WORK_FACTORS = "边框"  # 制作要素
ERROR_INTELLIGENCE_STATUS = -1
WAITING_INTELLIGENCE_STATUS = 0
POSTING_INTELLIGENCE_STATUS = 1
POSTED_INTELLIGENCE_STATUS = 2
INVALID_FLOW_STATUS = -1
# see: https://ku.baidu-int.com/d/PaU68SND-BrjBQ
WORKING_STATUS = (
    0,  # 任务待创建
    1,  # 待制作 任务未领取作业
    2,  # 制作完成 制作完成，成果待回库，其他环节不可用
    6,  # 无法核实-待采集 资料未覆盖，无法核实
)
COMPLETE_STATUS = (
    3,  # 回库完成 制作完成，且成果回库，其他环节可拉取
    4,  # 数据正确 情报无效 数据已存在，不需要制作
    5,  # 无法制作 情报无效 当前工艺水平下无法判断或不需要制作
    7,  # 无法核实-投放采集 无法核实，已投放路淘、众源等采集资料
)

desc = pipeline.get_desc()


@dataclass
class PostPackage:
    """情报推送包"""

    batch: str
    plan_type: str
    valid_timespan: datetime
    intelligences: list[Intelligence] = field(default_factory=list)


@dataclass
class StrategyDefinition:
    """推送策略定义"""

    name: str
    plan_type: str
    valid_timespan: datetime
    src_list: list[str]
    runner: Callable[[list[Intelligence]], list[Intelligence]]


@dataclass
class Context:
    """情报去重上下文"""

    batch_postfix: str
    mesh_ids: list[str] = field(default_factory=list)
    original_intelligences: list[Intelligence] = field(default_factory=list)
    distinct_intelligences: dict = field(default_factory=dict)
    post_packages: list[PostPackage] = field(default_factory=list)

    def update_intelligence_status(self):
        with (
            pgsql.get_connection(pgsql.POI_CONFIG) as conn,
            conn.cursor() as cur,
        ):
            try:
                for intelligence in tqdm(self.original_intelligences):
                    sql = (
                        f"""
                        update aoi_original_intelligence set status = %s where id = %s;
                    """
                        if intelligence.status != POSTED_INTELLIGENCE_STATUS
                        else f"""
                        update aoi_original_intelligence set status = %s, pushed_at = now() where id = %s;
                    """
                    )
                    cur.execute(sql, (intelligence.status, intelligence.id))

                conn.commit()
            except Exception as e:
                conn.rollback()
                raise e


def find_strategies() -> list[StrategyDefinition]:
    """找到与当前脚本在同目录下的、符合格式要求的子脚本集合"""
    strategy_dir = Path(os.path.dirname(__file__))
    module_names = [x.stem for x in strategy_dir.glob("filter_*.py")]
    full_module_names = [f"src.integration.{x}" for x in module_names]
    modules = [importlib.import_module(x) for x in full_module_names]
    strategy_modules = [
        x
        for x in modules
        if (
            hasattr(x, "STRATEGY_NAME")
            and hasattr(x, "STRATEGY_TYPE")
            and hasattr(x, "VALID_TIMESPAN")
            and hasattr(x, "SRC_LIST")
            and hasattr(x, "filter_intelligences")
        )
    ]
    return [
        StrategyDefinition(
            x.STRATEGY_NAME,
            x.STRATEGY_TYPE,
            x.VALID_TIMESPAN,
            x.SRC_LIST,
            x.filter_intelligences,
        )
        for x in strategy_modules
    ]


@desc()
def load_intelligences(ctx: Context, proceed):
    """加载一定时间范围内的情报集合"""
    with pgsql.get_connection(MINOR_POI_CONFIG) as conn:
        sql = f"""
            select id, 
                   batch, 
                   src, 
                   action, 
                   element_id, 
                   created_at, 
                   memo
            from aoi_original_intelligence
            where status not in %s;
        """
        invalid_status = (ERROR_INTELLIGENCE_STATUS, POSTED_INTELLIGENCE_STATUS)
        intelligences = pgsql.fetch_all(conn, sql, (invalid_status,))
        for (
            intelligence_id,
            batch,
            src,
            action,
            element_id,
            created_at,
            memo,
        ) in intelligences:
            ctx.original_intelligences.append(
                Intelligence(
                    id=intelligence_id,
                    batch=batch,
                    src=src,
                    action=action,
                    element_id=element_id,
                    created_at=created_at,
                    memo=memo,
                )
            )

    proceed()


@desc()
def init_intelligences_status(ctx: Context, proceed):
    """初始化情报状态"""
    for intelligence in ctx.original_intelligences:
        intelligence.status = POSTING_INTELLIGENCE_STATUS

    ctx.update_intelligence_status()
    proceed()


@desc()
def filter_duplicate_intelligences_in_global(ctx: Context, proceed):
    """过滤一些重复推送的情报"""
    for intelligence in tqdm(ctx.original_intelligences):
        intelligences_of_single_src = ctx.distinct_intelligences.get(
            intelligence.src, dict()
        )
        intelligences_of_single_action = intelligences_of_single_src.get(
            intelligence.action, dict()
        )
        if not intelligences_of_single_action.get(intelligence.element_id):
            intelligences_of_single_action[intelligence.element_id] = intelligence
        intelligences_of_single_src[
            intelligence.action
        ] = intelligences_of_single_action
        ctx.distinct_intelligences[intelligence.src] = intelligences_of_single_src

    proceed()


@desc()
def create_post_packages(ctx: Context, proceed):
    """创建情报推送包"""
    for definition in find_strategies():
        intelligences = []

        for src in ctx.distinct_intelligences.keys():
            if src in definition.src_list:
                intelligences_of_single_src = ctx.distinct_intelligences[src]
                for action in intelligences_of_single_src.keys():
                    intelligences_of_single_action = intelligences_of_single_src[action]
                    for intelligence in intelligences_of_single_action.values():
                        intelligences.append(intelligence)

        intelligences = definition.runner(intelligences)
        ctx.post_packages.append(
            PostPackage(
                batch=f"plan_type_{definition.plan_type}_{ctx.batch_postfix}",
                plan_type=definition.plan_type,
                valid_timespan=definition.valid_timespan,
                intelligences=definition.runner(intelligences),
            )
        )

    proceed()


@desc()
def filter_duplicate_intelligences_in_package(ctx: Context, proceed):
    """过滤每个推送包内的重复数据"""
    for post_package in ctx.post_packages:
        intelligences = []
        element_ids = set()

        for intelligence in tqdm(
            post_package.intelligences, desc=post_package.plan_type
        ):
            if intelligence.element_id in element_ids:
                continue
            element_ids.add(intelligence.element_id)
            intelligences.append(intelligence)

        post_package.intelligences = intelligences

    proceed()


@desc()
def filter_intelligences_by_status(ctx: Context, proceed):
    """结合一体化平台，忽略作业中和短期内已经入过库的情报。"""
    for post_package in ctx.post_packages:
        intelligences = []

        with pgsql.get_connection(MINOR_POI_CONFIG) as conn:
            for intelligence in tqdm(
                post_package.intelligences, desc=post_package.plan_type
            ):
                sql = f"""
                    select id
                    from integration_qb 
                    where main_poi_bid = %s and
                          flow_status != %s and
                          (
                              status in %s or
                              (status in %s and updated_at > %s)
                          );
                """
                args = (
                    intelligence.element_id,
                    INVALID_FLOW_STATUS,
                    WORKING_STATUS,
                    COMPLETE_STATUS,
                    datetime.datetime.now() - post_package.valid_timespan,
                )

                if pgsql.fetch_one(conn, sql, args):
                    continue
                intelligences.append(intelligence)

        post_package.intelligences = intelligences

    proceed()


@desc()
def filter_intelligences_by_pv(ctx: Context, proceed):
    """
    根据 pv 过滤情报
    """
    min_pv = 210
    sql = '''
        select click_pv from poi where bid = %s;
    '''

    for post_package in ctx.post_packages:
        intelligences = []

        with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
            for intelligence in tqdm(
                post_package.intelligences, desc=post_package.plan_type
            ):
                row = pgsql.fetch_one(conn, sql, (intelligence.element_id,))
                if row is None:
                    continue

                pv, = row
                if pv < min_pv:
                    continue

                intelligences.append(intelligence)

        post_package.intelligences = intelligences

    proceed()


@desc()
def post_intelligences_to_unity_platform(ctx: Context, proceed):
    """将情报推送至一体化平台"""

    success = 0

    for post_package in ctx.post_packages:
        integration_intelligences = []

        for intelligence in tqdm(
            post_package.intelligences, desc=post_package.plan_type
        ):
            integration_intelligences.append(
                IntegrationIntelligence(
                    ref_qb_id=intelligence.id,
                    src=SRC_AOI,
                    work_type=WORK_TYPE_MANUAL,
                    work_factors=WORK_FACTORS,
                    strategy_type=post_package.plan_type,
                    main_poi_bid=intelligence.element_id,
                    ref_qb_batch_id=post_package.batch,
                )
            )

        for result in tqdm(to_service(integration_intelligences)):
            code, record = result
            if code != success:
                print(code)
                print(record)

    proceed()


@desc()
def determine_intelligences_status(ctx: Context, proceed):
    """给定情报最终的推送状态"""
    with pgsql.get_connection(MINOR_POI_CONFIG) as conn:
        for intelligence in ctx.original_intelligences:
            sql = f"""
                select ref_qb_id from integration_qb where ref_qb_id = %s;
            """
            intelligence.status = (
                POSTED_INTELLIGENCE_STATUS
                if pgsql.fetch_one(conn, sql, (intelligence.id,))
                else ERROR_INTELLIGENCE_STATUS
            )

    ctx.update_intelligence_status()
    proceed()


def main():
    """情报去重模块主函数"""
    main_pipe = pipeline.Pipeline(
        load_intelligences,
        init_intelligences_status,
        filter_duplicate_intelligences_in_global,
        create_post_packages,
        filter_duplicate_intelligences_in_package,
        filter_intelligences_by_status,
        filter_intelligences_by_pv,
        post_intelligences_to_unity_platform,
        determine_intelligences_status,
    )
    desc.attach(main_pipe)
    ctx = Context(
        batch_postfix=datetime.datetime.now().strftime("%Y%m%d"),
    )
    main_pipe(ctx)


if __name__ == "__main__":
    main()
