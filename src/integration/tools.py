# !/usr/bin/env python3
"""
包含一些情报推送的帮助方法。
"""
import datetime
from dataclasses import dataclass

from tqdm import tqdm

from src.tools import pgsql


@dataclass
class Intelligence:
    id: str
    batch: str
    src: str
    action: str
    element_id: str
    created_at: datetime = datetime.datetime.min
    status: int = 0
    memo: str = ''


def post_intelligences(intelligences: list[Intelligence]):
    with(
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cur,
    ):
        try:
            for intelligence in tqdm(intelligences):
                sql = f'''
                    insert into aoi_original_intelligence(
                        id, 
                        batch, 
                        src, 
                        action, 
                        element_id, 
                        memo
                    )
                    values(%s, %s, %s, %s, %s, %s);
                '''
                cur.execute(sql, (
                    intelligence.id,
                    intelligence.batch,
                    intelligence.src,
                    intelligence.action,
                    intelligence.element_id,
                    intelligence.memo,
                ))

            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
