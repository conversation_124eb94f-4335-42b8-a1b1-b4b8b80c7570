# !/usr/bin/env python3
from .sample_scheduler import test_task
# from .sample_scheduler import task_with_process
from flask_apscheduler import APScheduler
from src.tools.console_transfer_log import ConsoleTransferLog
from .create_first_flow_job import CreateFirstFlowJobExecutor
from src.tools import function
from src.tools import machine_group
from src.tools.afs_tool import AfsTool
import traceback

try:
    from .aoi_seg_post_job import AoiSegPostJobExecutor
    from .aoi_post_job import PostJobExecutor
    from .crawler_res_import import info_crawler_import_handle
    from .aoi_model_evaluate_job import AoiModelEvaluateExecutor
    from .aoi_street_img_upload import StreetImgUploadExecutor
except Exception as e:
    if machine_group.machine_gpu_rpm01():
        pass
    else:
        raise e

from .aoi_seg_job import AoiSegJobExecutor


def register_scheduler(scheduler: APScheduler):
    """
    统一的任务注册
    :param scheduler:
    :return:
    """
    # 校验下afs是否存在
    _ = AfsTool()
    # 拦截print等信息，写到日志文件中
    ConsoleTransferLog()

    # 配置计划任务
    # @scheduler.task("cron", id="do_job_1", minute="*")
    # def do_job_1():
    #     test_task()

    # @scheduler.task("cron", id='do_job_2', minute="*")
    # def do_job_with_process():
    #     task_with_process()

    logger = function.get_logging_client('job_exec_fail')

    @scheduler.task("cron", id="StreetImgUploadExecutor", minute="*/5", max_instances=1)
    # 部署图片上传任务
    def create_street_img_upload_job():
        if not (machine_group.machine_de17()):
            # 目前只限制在de17上执行
            return
        try:
            c = StreetImgUploadExecutor()
            c()
        except Exception as e:
            logger.warning(
                f"执行任务失败:StreetImgUploadExecutor:{traceback.format_exc()}")

    @scheduler.task("cron", id="CreateFirstFlowJobExecutor", minute="*/2", max_instances=1)
    def create_first_flow_job():
        # 生成第一个任务
        if not (machine_group.machine_de16()):
            print("机器不匹配, 不执行:create_first_flow_job")
            return
        try:
            c = CreateFirstFlowJobExecutor()
            c()
        except Exception as e:
            logger.warning(
                f"执行任务失败:CreateFirstFlowJobExecutor:{e.args}:{traceback.format_exc()}")

    @scheduler.task("cron", id="AoiSegPostJobExecutor", minute="*/3", max_instances=1)
    def create_seg_post_job():
        # 语义分割后处理
        if not (machine_group.machine_de16() or machine_group.machine_opera_3()):
            return
        try:
            c = AoiSegPostJobExecutor()
            c()
        except Exception as e:
            logger.warning(
                f"执行任务失败:AoiSegPostJobExecutor:{e.args}-{traceback.format_exc()}")

    @scheduler.task("cron", id="PostJobExecutor", hour="9-21", minute="*/5", max_instances=1)
    def post_job():
        # 策略后处理，数据库同步，基本上是11点半更新到早上8点
        if not (machine_group.machine_de16()):
            return
        try:
            c = PostJobExecutor()
            c()
        except Exception as e:
            logger.warning(
                f"执行任务失败PostJobExecutor:{e.args}{traceback.format_exc()}")

    @scheduler.task("cron", id="AoiModelEvaluateExecutor", hour="9-21", minute="*/6", max_instances=1)
    def evaluate_job():
        # 评估脚本
        if not (machine_group.machine_de16()):
            return
        try:
            c = AoiModelEvaluateExecutor()
            c()
        except Exception as e:
            logger.warning(
                f"执行任务失败AoiModelEvaluateExecutor:{e.args}{traceback.format_exc()}")

    @scheduler.task("cron", id='info_crawler', hour="12", max_instances=1)
    def do_job_info_crawler():
        if not (machine_group.machine_opera()):
            print("机器不匹配,不执行:do_job_info_crawler")
            return
        info_crawler_import_handle()

    @scheduler.task("cron", id="AoiSegJobExecutor", minute="*/7", max_instances=1)
    def do_exec_seg_job():
        # 去除na02的机器配置，统一改成rpm01
        if not (machine_group.machine_gpu_rpm01()):
            return
        try:
            a = AoiSegJobExecutor()
            a()
        except Exception as e:
            logger.warning(
                f"执行任务失败:AoiSegJobExecutor:{e.args}{traceback.format_exc()}")
