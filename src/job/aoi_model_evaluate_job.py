# !/usr/bin/env python3
"""
评估脚本
"""

import glob
import json
import time
import traceback

from shapely import wkt
import tqdm
import sys
import os

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

tmp_dir = ROOT_PATH + "/../flow_data/aoi_model_evaluate_job"
from src.model.aoi_model import AoiModel
from src.tools.aoi_tools import calc_iou
from src.tools.afs_tool import AfsTool
from src.job.base.flow_job_base import FlowJobBase
from src.const.work_flow import ConstWorkFlow
from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.tools import function as F
from src.tools.health_check import check_pg_ok


class Evaluate:
    def __init__(self, bid_file, street_dir, vec_file, ml_post_file, out_path):
        if not os.path.isfile(bid_file):
            raise Exception(f"bid文件{bid_file}不存在")
        if not os.path.isdir(out_path):
            os.makedirs(out_path, exist_ok=True)
        self.out_path = out_path
        self.bid_str = str(list(str(bid_file).split("/"))[-1]).replace(".txt", "").replace("bid_list", "")
        self.street_dir = street_dir
        self.vec_file = vec_file
        self.bid_file = bid_file
        self.ml_post_file = ml_post_file
        self.bid_list = []
        self.get_bid_list()
        self.merged_json_file = out_path + "/" + f"{self.bid_str}_merged_evaluate.json"
        self.result_file = out_path + "/" + f"{self.bid_str}_evaluate_result.json"

        # 总数（有结果）
        self.all_data = 0
        # 矢量化 : iou> 80%
        self.vec_iou_80 = 0
        # 矢量化 : iou < 80
        self.vec_iou_less_80 = 0
        # 策略处理 :  iou > 80
        self.ml_iou_80 = 0
        # 策略处理： iou < 80
        self.ml_iou_less_80 = 0
        # 策略分
        self.score_80 = 0

    def get_wkt_item(self, wkt_str: str, image_info):
        """
        获取wkt数据
        :param image_info:
        :return:
        """
        buffer = 1.1
        if not wkt_str:
            return None

        region_info = image_info['region']
        left, top, right, bottom = region_info['left'], region_info['top'], region_info['right'], region_info['bottom']
        width, height = image_info['width'], image_info['height']
        max_size = max(width * buffer, height * buffer)
        n = 0
        scale = 1
        while max_size > 1024:
            max_size //= 2
            n += 1
            scale *= 2

        return {
            'wkt': wkt_str,
            'center_lng': (left + right) / 2,
            'center_lat': (top + bottom) / 2,
            'width': width // scale,
            'height': height // scale,
            'zoom': 19 - n,
        }

    def get_street_map(self):
        """
        获取街区文件
        :return:
        """
        m = {}
        dirs = [self.street_dir + "/*.json"]
        for json_dirs in dirs:
            for json_file in glob.glob(json_dirs):
                with open(json_file, "r") as f:
                    data = json.load(f)
                    m[data['id']] = data
        return m

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    def get_bid_list(self):
        with open(self.bid_file) as f:
            lines = f.read().split("\n")
            bid_list = [x.strip() for x in lines if x]
            self.bid_list = bid_list

    def get_vec_map(self):
        """
        获取矢量化文件
        :return:
        """
        m = {}
        with open(self.vec_file) as f:
            for item in json.load(f):
                m[item['uuid']] = item['geom']
        return m

    def get_table_header(self):
        return [
            {
                "title": "poi bid",
                "type": "text",
                "field": "id"
            },
            {
                "title": "名称",
                "type": "text",
                "field": "poi_name"
            },
            {
                "title": "城市",
                "type": "text",
                "field": "city"
            },
            {
                "title": "备注",
                "type": "text",
                "field": "memo"
            },
            {
                "title": "POI 点位",
                "type": "wkt",
                "field": "point_wkt"
            },
            {
                "title": "识别图像",
                "type": "image",
                "field": "img_label"
            },
            {
                "title": "策略后处理结果",
                "type": "wkt",
                "field": "wkt"
            },
            {
                "title": "矢量化结果",
                "type": "wkt",
                "field": "vec_wkt"
            },
            {
                "title": "百度在线数据",
                "type": "wkt",
                "field": "info_wkt"
            },
            {
                "title": "评分",
                "type": "text",
                "field": "score"
            }
        ]

    def get_all_ml_data(self):
        """
        读取策略提取文件
        :return:
        """
        all_json_list = []
        files = [self.ml_post_file]
        for json_file in files:
            with open(json_file, 'r') as f:
                json_list = json.load(f)
                for j in json_list:
                    all_json_list.append(j)
        return all_json_list

    def do_evaluate(self):
        with AoiModel() as aoi_model:
            street_list = self.get_street_map()
            ml_json_list = self.get_all_ml_data()

            # 导出的json
            export_json = []
            for i in tqdm.tqdm(self.bid_list):
                poi_name = ""
                poi_city = ""
                geom = aoi_model.get_geom_by_bid(i)
                conn = aoi_model.get_conn_poi()
                cursor = conn.cursor()
                sql = f"select name,city from poi where bid='{i}'"
                cursor.execute(sql)
                ret = cursor.fetchone()
                if ret:
                    poi_name = ret[0]
                    poi_city = ret[1]

                if not geom or geom == "":
                    continue
                aoi_street_id = ""
                street_info = {}
                # 查询街区id
                for street_id, street_data in street_list.items():
                    if wkt.loads(street_data['geom']).intersects(wkt.loads(geom)):
                        aoi_street_id = street_id
                        street_info = street_data
                        break

                if not aoi_street_id:
                    continue
                print(f"{i}:{aoi_street_id}")
                # 查询aoi
                aoi_wkt = ""
                aoi_info = aoi_model.get_aoi_by_bid(i)
                if aoi_info:
                    aoi_wkt = aoi_info[2]
                # print(f"{i}:{aoi_wkt}")
                ml_wkt = ""
                ml_score = 0
                ml_id = ""
                # 比对结果
                vec_wkt = ""
                vec_id = ""
                # 查询矢量化结果
                for t_vec_id, t_vec_geom in self.get_vec_map().items():
                    if wkt.loads(t_vec_geom).contains(wkt.loads(geom)):
                        vec_wkt = t_vec_geom
                        vec_id = t_vec_id
                        break
                # 查询策略修型结果
                for ml in ml_json_list:
                    if wkt.loads(ml['wkt']).contains(wkt.loads(geom)):
                        ml_wkt = ml['wkt']
                        ml_score = ml['score']
                        ml_id = ml['id']
                        break
                # 生成数据
                tmp = {
                    "id": i,
                    # 分数
                    "score": ml_score,
                    "memo": f"点:{geom}",
                    "point_wkt": self.get_wkt_item(geom, street_info),
                    # poi名称
                    "poi_name": poi_name,
                    # 城市
                    "city": poi_city,
                    "img_label": f"http://gzbh-ns-map-na002.gzbh.baidu.com:8090/aoi_ml"
                                 f"/chenbaojun/products/{self.bid_str}/added_prediction/{aoi_street_id}.jpg",
                    # 矢量化结果
                    "wkt": self.get_wkt_item(ml_wkt, street_info),
                    # 在线的原始值
                    "info_wkt": self.get_wkt_item(aoi_wkt, street_info),
                    "ml_id": ml_id,
                    # 矢量化wkt
                    "vec_wkt": self.get_wkt_item(vec_wkt, street_info),
                    "vec_id": vec_id,
                }
                export_json.append(tmp)
                if aoi_wkt != "":
                    self.all_data += 1
                    if calc_iou(vec_wkt, aoi_wkt) > 0.8:
                        self.vec_iou_80 += 1
                    else:
                        self.vec_iou_less_80 += 1
                    if calc_iou(ml_wkt, aoi_wkt) > 0.8:
                        self.ml_iou_80 += 1
                        if ml_score > 80:
                            self.score_80 += 1
                    else:
                        self.ml_iou_less_80 += 1

            with open(self.merged_json_file, 'w') as f:
                json.dump({
                    "headers": self.get_table_header(),
                    "body": export_json,
                }, f, ensure_ascii=False, indent=2)

            with open(self.result_file, 'w') as f:
                json.dump({
                    "data_all": len(self.bid_list),
                    "result_all": self.all_data,
                    "vec_iou_80": self.vec_iou_80,
                    "vec_iou_less_80": self.vec_iou_less_80,
                    "ml_auto": self.score_80,
                    "ml_iou_80": self.ml_iou_80,
                    "ml_iou_less_80": self.ml_iou_less_80,
                }, f,
                    ensure_ascii=False, indent=2)

    def __call__(self, *args, **kwargs):
        self.do_evaluate()


class AoiModelEvaluateExecutor(FlowJobBase):
    def __init__(self):
        if not check_pg_ok():
            print("pg检测失败，任务不执行")
            return
        name = ConstWorkFlow.FLOW_SUBTASK_EXEC_MODEL_EVALUATE
        FlowJobBase.__init__(self, flow_name=name)
        self.current_tmp_dir = ""
        self.afs_full_path = ""
        self.afs_output_dir = "/user/map-data-streeview/aoi-ml/flow_data/aoi_model_evaluate_job/"

    def call_with_taskid(self, task_id, uuid_str):
        """
        指定任务去评估数据
        :param task_id:
        :return:
        """
        self.current_tmp_dir = tmp_dir + "/" + uuid_str
        os.makedirs(self.current_tmp_dir, exist_ok=True)
        os.makedirs(self.current_tmp_dir + "/prepare", exist_ok=True)
        ## 从数据库里面查询必要的文件
        with AoiMlFlowModel() as a:
            list_data = a.get_subtasks_by_task_id(task_id)
            params = {}
            for i in list_data:
                wpf_section_name = i[2]
                wpf_flow_info = i[4]
                wpf_memo_info = i[5]
                in_data = json.loads(wpf_flow_info)
                out_data = json.loads(wpf_memo_info)
                if wpf_section_name == ConstWorkFlow.FLOW_SUBTASK_EXEC_SEG:
                    # 找出街区数据
                    params['dataset'] = in_data['afs_url']

                if wpf_section_name == ConstWorkFlow.FLOW_SUBTASK_POST_JOB:
                    # 矢量化数据
                    params['vec_data'] = in_data['afs_url']
                    # 策略结果
                    params['ml_data'] = out_data['afs_url']
            if "dataset" not in params or "vec_data" not in params or "ml_data" not in params:
                raise Exception("基础数据不全，无法评估")
        # 下载文件
        afs = AfsTool()
        afs.get(params['dataset'], self.current_tmp_dir + "/prepare")
        afs.get(params['vec_data'], self.current_tmp_dir + "/prepare")
        afs.get(params['ml_data'], self.current_tmp_dir + "/prepare")
        # 记录打包时的文件名称
        dataset_file_name = str(list(params['dataset'].split("/"))[-1]).replace(".tar", "")
        vec_data_filename = list(params['vec_data'].split("/"))[-1]
        ml_data_file_name = list(params['ml_data'].split("/"))[-1]
        F.exec_shell_cmd(
            f"cd {self.current_tmp_dir}/prepare/; tar -xf {self.current_tmp_dir}/prepare/{dataset_file_name}.tar")
        bid_file_list = glob.glob(f"{self.current_tmp_dir}/prepare/{dataset_file_name}/*bid_list.txt")
        if len(bid_file_list) < 1:
            raise Exception("未发现bid列表文件")
        bid_file_path = bid_file_list[0]
        street_dir = f"{self.current_tmp_dir}/prepare/{dataset_file_name}/json_info/image"
        vec_file = f"{self.current_tmp_dir}/prepare/{vec_data_filename}"
        ml_file = f"{self.current_tmp_dir}/prepare/{ml_data_file_name}"
        output_dir = f"{self.current_tmp_dir}/output"
        os.makedirs(output_dir, exist_ok=True)
        # 执行评估
        with Evaluate(bid_file_path, street_dir, vec_file, ml_file, output_dir) as e:
            e()
        out_file_list = glob.glob(f"{output_dir}/*.json")
        if len(out_file_list) < 1:
            raise Exception("未生成结果文件")
        afs_file_name = f"{uuid_str}_{dataset_file_name}.tar"
        F.exec_shell_cmd(f"cd {self.current_tmp_dir};tar -cf {afs_file_name} output")
        afs.put(f"{self.current_tmp_dir}/{afs_file_name}", self.afs_output_dir)
        self.afs_full_path = f"{self.afs_output_dir}/{afs_file_name}"

    def __call__(self, *args, **kwargs):
        t = self.get_can_exec_task()
        if not t.wpf_id or not t.uuid_str:
            return
        try:
            # 设置正在执行
            t.aoi_ml_flow_model.set_subtask_doing(t.wpf_id, t.task_id, tmp_dir + "/" + t.uuid_str)
            self.call_with_taskid(t.task_id, t.uuid_str)
            # 执行完成
            t.aoi_ml_flow_model.connection.ping()
            # 获取下一步计划
            next_task = ConstWorkFlow.get_next_subtask(t.task_type, t.section_name)
            t.wpf_flow_info['evaluate_afs_path'] = self.afs_full_path
            params = json.dumps(t.wpf_flow_info, ensure_ascii=False)
            # 设置当前任务成功
            t.aoi_ml_flow_model.set_subtask_success(t.wpf_id, t.task_id, params)
            # 创建下一步任务
            self.create_next_task(t.wpf_id, t.task_id, next_task, params)
        except Exception as e:
            t.aoi_ml_flow_model.connection.ping()
            t.aoi_ml_flow_model.set_subtask_fail(t.wpf_id, t.task_id,
                                                 "系统异常：" + str(e.args) + str(traceback.format_exc()))


if __name__ == '__main__':
    # a = Evaluate(
    #     "/home/<USER>/chenbaojun/statistics/wujing_20230213_49/wujing_20230213_49_new/wujing_20230213_49.txt_bid_list.txt",
    #     "/home/<USER>/chenbaojun/statistics/wujing_20230213_49/wujing_20230213_49_new/json_info/image",
    #     "/home/<USER>/chenbaojun/statistics/wujing_20230213_49/20230213144500_45_88_20230213143600_87_product_wujing_20230213_49_new.tar.json",
    #     "/home/<USER>/chenbaojun/statistics/wujing_20230213_49/20230213145000_45_89_sorted_ujing_20230213_49_new.tar.json",
    #     "/home/<USER>/chenbaojun/statistics/wujing_20230213_49/")
    # a.do_evaluate()
    a = AoiModelEvaluateExecutor()
    a.call_with_taskid(45, "aaa_" + time.strftime("%Y%m%d%H%M%S") + "_evaluate")
