# !/usr/bin/env python3
"""
aoi后处理策略
包括：
  1。生成唯一的uuid
  2。执行后处理
"""
import os.path
import sys
import uuid
import time
import json

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

tmp_dir = ROOT_PATH + "/../flow_data/aoi_post_job"
from src.tools.afs_tool import AfsTool
from script.inner_roads.post_process.seg_scan_main import run_main as post_run_main
from script.online.merge_json import run_main as merge_run_main
from src.tools import function as F
from src.const.work_flow import ConstWorkFlow
from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.tools.redis_tool import RedisTool
from src.tools.conf_tools import is_debug
import traceback
from src.model_mysql.aoi_ml_model import AoiMlModel
from src.tools.health_check import check_pg_ok
from src.tools import machine_group


class PostJob:
    def __init__(self, uuid_str="", json_file=""):
        """
        :param uuid_str: 唯一标识
        :param json_file: json afs文件地址
        """
        self.uuid_str = uuid_str
        self.json_file = json_file
        self.local_json_file = ""
        self.current_tmp_dir = tmp_dir + "/" + self.uuid_str
        os.makedirs(self.current_tmp_dir, exist_ok=True)
        self.afs_output_dir = "/user/map-data-streeview/aoi-ml/flow_data/aoi_post_job/"
        self.afs_output_full_path = ""
        self.exec_fail = False
        self.exec_fail_reason = False

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    def download_file(self):
        afs_tool = AfsTool()
        afs_tool.get(self.json_file, self.current_tmp_dir)
        self.local_json_file = self.current_tmp_dir + "/" + self.get_filename()
        print("本地文件地址:" + self.local_json_file)
        if not os.path.isfile(self.local_json_file):
            print("无json文件")
            self.exec_fail_reason = f"下载文件失败,无文件{self.local_json_file}"
            self.exec_fail = True

    def get_filename(self, last=0) -> str:
        """
        获取文件名称
        :param last: 取最后多少字符
        :return:
        """
        f_list = self.json_file.split("/")
        file_name = f_list[-1]
        if last == 0 or len(file_name) < last:
            return file_name
        else:
            file_name = file_name.replace(".tar", "").replace(".json", "").replace(".txt", "")
            if len(file_name) < last:
                return file_name
            return file_name[len(file_name) - last:len(file_name)]

    def __call__(self, filter_online_aoi=True):
        self.download_file()
        if self.exec_fail:
            return
        # 执行策略
        pool_size = 10
        if machine_group.machine_opera():
            pool_size = 4
        post_run_main(self.local_json_file, filter_online_aoi=filter_online_aoi, process_num=pool_size)
        out_put_dir = self.local_json_file + "_product/result"
        # 执行任务合并与排序
        out_json = merge_run_main(out_put_dir)
        if not os.path.isfile(out_json):
            print("未生成sorted.json文件")
            self.exec_fail = True
            self.exec_fail_reason = "未生成sorted.json文件"
            return
        new_file_name = f"{self.uuid_str}_sorted_{self.get_filename(last=30)}.json"
        new_file_full_name = f'{self.local_json_file}_product/{new_file_name}'
        F.exec_shell_cmd(f"cd {self.local_json_file}_product/; mv {out_json} {new_file_full_name}")
        # 上传到afs
        afs_tool = AfsTool()
        afs_tool.put(new_file_full_name, self.afs_output_dir)
        # 上传执行的过程文件
        F.exec_shell_cmd(f"cd {self.local_json_file}_product;"
                         f" tar -cf {new_file_name}.tar ./log ./result ./error ")
        afs_tool.put(f"{self.local_json_file}_product/{new_file_name}.tar", self.afs_output_dir)

        self.afs_output_full_path = self.afs_output_dir + "/" + new_file_name
        print(f"上传到afs文件夹:{self.afs_output_full_path}")


class PostJobExecutor:
    def __init__(self):
        self.name = ConstWorkFlow.FLOW_SUBTASK_POST_JOB
        self.logger = F.get_logging_client(self.name)

    def __del__(self):
        F.close_log_client(self.logger)

    def __call__(self):
        if not check_pg_ok():
            self.logger.info("检测pg失败，本次不执行，10分钟后再试")
            time.sleep(500)
            return
        with AoiMlFlowModel() as af:
            data_list = af.get_flow_subtask(ConstWorkFlow.FLOW_SUBTASK_POST_JOB, ConstWorkFlow.FLOW_STATUS_WAIT)
            if len(data_list) < 1:
                self.logger.info("没有任务需要处理")
                return
            with AoiMlModel() as rt:
                for i in data_list:
                    wpf_id, task_id, section_name, status, wpf_flow_info, task_name, task_type, task_content = i
                    wpf_flow_info = json.loads(wpf_flow_info)
                    if 'afs_url' not in wpf_flow_info:
                        af.set_subtask_fail(wpf_id, task_id, "无afs_url")
                        continue
                    # 锁定
                    key = self.name + ":" + str(wpf_id)
                    succ, lock_val = rt.set_lock(key)
                    if not succ:
                        self.logger.warning(f"任务被锁定,跳过{wpf_id}: key:{key}")
                        continue
                    uuid_str = f"{time.strftime('%Y%m%d%H%M%S')}{'_debug' if is_debug() else ''}_{wpf_id}"
                    with PostJob(uuid_str, wpf_flow_info.get("afs_url")) as job:
                        af.set_subtask_doing(wpf_id, task_id, job.current_tmp_dir)
                        try:
                            # 执行任务
                            # 是否过滤在线aoi，跑对照组不过滤
                            filter_online = True
                            if "filter_online_aoi" in wpf_flow_info and int(wpf_flow_info.get("filter_online_aoi")) < 1:
                                print("不过滤在线aoi")
                                filter_online = False
                            job(filter_online_aoi=filter_online)
                        except Exception as e:
                            af.set_subtask_fail(wpf_id, task_id, str(e.args) + str(traceback.format_exc()))
                            return
                        # mysql断线重新连接
                        af.connection.ping()
                        params = ""
                        if job.exec_fail:
                            af.set_subtask_fail(wpf_id, task_id, job.exec_fail_reason)
                            next_task = ConstWorkFlow.get_next_subtask(task_type, section_name,
                                                                       ConstWorkFlow.FLOW_STATUS_FAIL)
                        else:
                            afs_path = job.afs_output_full_path
                            next_task = ConstWorkFlow.get_next_subtask(task_type, section_name)
                            wpf_flow_info['afs_url'] = afs_path
                            params = json.dumps(wpf_flow_info, ensure_ascii=False)
                            af.set_subtask_success(wpf_id, task_id, params)
                        # 自动生成下一环节任务
                        af.create_next_subtask(wpf_id, task_id, next_task, params)
                        return


if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == 'auto':
            e = PostJobExecutor()
            e()
        else:
            raise Exception("请输入正确的参数的参数")
    else:
        pass
        # uuidStr = time.strftime("%Y%m%d") + "_" + str(uuid.uuid4()).replace("-", "")
        # file_path = "/user/map-data-streeview/aoi-ml/flow_data/aoi_seg_post_job/
        # 20230108_21e506d3eed14dc783824b59ce22133e_product_bad_review_model_2022.tar.json"
        # p = PostJob(uuidStr, file_path)
        # p(filter_online_aoi=False)
