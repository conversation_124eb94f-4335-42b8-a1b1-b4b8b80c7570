# !/usr/bin/env python3
"""
执行语义分割
包括：
  1。下载基础数据
  2。调用脚本执行
  3。 将结果上传到afs
"""
import glob
import os
import shutil
import sys
import traceback

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

tmp_dir = ROOT_PATH + "/../flow_data/aoi_seg_job"
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.pre_process import generate_feed_txt
from src.tools.city_tools import get_city_pinying
import argparse
import src.tools.machine_group as machine_group
from src.const.work_flow import ConstWorkFlow
import json
from src.job.base.flow_job_base import FlowJobBase
from src.model_mysql.aoi_ml_model import AoiMlModel
import time
from pathlib import Path

MODEL_MULTI_CHANNE = 'model_multi_channel'
MODEL_VIS = 'model_vis'
MODEL_zyxpaddleseg = 'zyxpaddleseg'
MODEL_paddleseg_vis_v3 = "paddleseg_vis_v3"
MODEL_border_recognition_sdk_v1_1 = 'border_recognition_sdk_v1_1'
MODEL_border_recognition_sdk_v2_1 = 'border_recognition_sdk_v2_1'

# 是否需要生成多通道数据
NEED_MULTI_CHANNEL_CFG = {
    MODEL_MULTI_CHANNE: True,
    MODEL_VIS: False,
    MODEL_zyxpaddleseg: False,
    MODEL_paddleseg_vis_v3: False,
    # 新交付的sdk模型
    MODEL_border_recognition_sdk_v1_1: True,
    MODEL_border_recognition_sdk_v2_1: True,
}
# vis sdk模型
VIS_SDK_MODEL = [MODEL_border_recognition_sdk_v1_1, MODEL_border_recognition_sdk_v2_1]

GPU_DEVICE = 0
MODEL_DIR = "/home/<USER>/models"


class SegJob:
    def __init__(self, uuid_str, feed_file, model_type=MODEL_VIS):
        self.uuid_str = uuid_str
        self.feed_file = feed_file
        self.current_tmp_dir = tmp_dir + "/" + self.uuid_str
        os.makedirs(self.current_tmp_dir, exist_ok=True)
        # afs保存的文件地址,文件夹已经创建好
        self.afs_output_dir = "/user/map-data-streeview/aoi-ml/flow_data/aoi_seg_job/"
        self.afs_output_full_path = ""
        # 合并后的文件地址
        self.merged_path = self.current_tmp_dir + "/merged"
        self.exec_fail = ""
        self.exec_fail_reason = ""
        # 识别输出路径
        self.seg_out_dir = self.current_tmp_dir + "/product"
        # afs文件名称
        self.afs_file_name = ""
        self.logger = F.get_logging_client("aoi_seg_job")
        self.model_type = model_type

    def merge_file(self, path):
        """
        合并文件夹
        :param path:
        :return:
        """
        print(f"剩余空间:{shutil.disk_usage(path).free / 1024.0 / 1024 / 1024} G")
        print(os.listdir(path))
        all_path = os.listdir(path)
        merge_path_list = []
        # /xxx/output/xxx_上海市/images ... 路径
        if 'output' in all_path:
            sub_path = os.listdir(path + "/output")
            for i in sub_path:
                sub_path_full = f"{path}/output/{i}"
                sub_path2 = os.listdir(sub_path_full)
                if "json_info" in sub_path2 and "images" in sub_path2 and "aux_images_inner_road" in sub_path2:
                    merge_path_list.append(sub_path_full)
        else:
            # /xxx/images ... 路径
            for i in all_path:
                full_path = self.current_tmp_dir + "/" + i
                if os.path.isfile(full_path):
                    continue
                if os.path.isdir(full_path):
                    sub_full = os.listdir(full_path)
                    if "json_info" in sub_full and "images" in sub_full and "aux_images_inner_road" in sub_full:
                        merge_path_list.append(full_path)
        if len(merge_path_list) < 1:
            raise Exception("未找到合适的文件")
        os.makedirs(self.merged_path, exist_ok=True)

        replace_map = {}
        real_merge_path_list = []
        for i in merge_path_list:
            # 复制json文件到合并的文件夹中
            os.makedirs(self.merged_path + "/json_info/image", exist_ok=True)
            for json_item in glob.glob(f"{i}/json_info/image/*.json"):
                shutil.copyfile(json_item,
                                json_item.replace(f"{i}/json_info/image", f"{self.merged_path}/json_info/image"))
            # 将中文替换为英文
            str_split_item = i.split("/")
            str_origin = str(str_split_item[-1])
            str2 = get_city_pinying(str_origin.replace("seg_scan_", ""))
            if not str2:
                real_merge_path_list.append(i)
                continue
            else:
                replace_map[i] = i.replace(str_origin, "seg_scan_" + str2)
        # 执行换文件夹
        for i, j in replace_map.items():
            shutil.move(i, j)
            real_merge_path_list.append(j)

        real_feeds_txt = []
        for j in real_merge_path_list:
            if not NEED_MULTI_CHANNEL_CFG.get(self.model_type, False):
                # vis模型不需要txt文件,直接给图片文件夹就行
                real_feeds_txt.append(j + "/images")
            else:
                print(f"开始执行 Split dataset {j}")
                parser = argparse.ArgumentParser('Split dataset')
                parser.add_argument('--dataset-dir', dest='dataset_dir', default=j, )
                parser.add_argument('--count', dest='count', default=1, )
                parser.add_argument('--image-size-limit', dest='image_size_limit',
                                    type=lambda x: [int(x) for x in x.split('*')], default='2048*2048', )
                # 生成文件类型, vis sdk的通道与之前的多通道不一致
                parser.add_argument('--legacy', dest='legacy',
                                    default=self.model_type not in VIS_SDK_MODEL)
                generate_feed_txt.main(parser.parse_args())
                real_feeds_txt.append(j + "/feed.txt")
        return real_feeds_txt
        # 合并文件

    def parse_file(self):
        """
        解析文件
        :return:
        """
        afs_tool = AfsTool()
        afs_tool.get(self.feed_file, self.current_tmp_dir)
        tar_file = self.current_tmp_dir + "/" + self.get_filename()
        # 下载
        print("本地文件地址:" + tar_file)
        if not os.path.isfile(tar_file):
            self.exec_fail_reason = f"下载文件失败,无文件{tar_file}"
            self.exec_fail = True
            raise Exception("下载文件失败")
        # 解压
        F.exec_shell_cmd(f"cd {self.current_tmp_dir}; tar -xf {tar_file}; rm {tar_file}")
        # 文件处理
        return self.merge_file(self.current_tmp_dir)

    def get_filename(self, last=0) -> str:
        """
        获取文件名称
        :param last: 取最后多少字符
        :return:
        """
        f_list = self.feed_file.split("/")
        file_name = f_list[-1]
        if last == 0 or len(file_name) < last:
            return file_name
        else:
            file_name = file_name.replace(".tar", "").replace(".json", "").replace(".txt", "")
            if len(file_name) < last:
                return file_name
            return file_name[len(file_name) - last:len(file_name)]

    def do_seg(self, feed_list):
        """
        执行语义分割任务
        :return:
        """
        for j in feed_list:
            cmd = self.get_seg_cmd(j)
            self.logger.info(cmd)
            F.exec_shell_cmd(cmd, timeout=86400 * 2)

    def get_seg_cmd(self, img_path):
        if self.model_type in VIS_SDK_MODEL:
            return self.get_cmd_vis_sdk(img_path)
        # 多通道的模型
        cmd_multi_channel = f"{machine_group.init_envirement_script()}export CUDA_VISIBLE_DEVICES={GPU_DEVICE};" \
                            f"cd {MODEL_DIR}/PaddleSeg_MultiChannel;" \
                            "python predict.py --config aoi_configs/segformer_b3_tag29.yml " \
                            "--model_path output/segformer_b3_v9_tag29/best_model/model.pdparams " \
                            f"--image_path  {img_path} " \
                            f"--save_dir {self.seg_out_dir} >> {self.current_tmp_dir}/seg_log.txt 2>&1 "
        # vis模型
        cmd_vis = f"{machine_group.init_envirement_script()}export CUDA_VISIBLE_DEVICES={GPU_DEVICE};" \
                  f"cd {MODEL_DIR}/PaddleSegZYX0129;" \
                  "python predict_zyx.py --config configs/quick_start/map_aoi_15_up_res50_resizeBorder.yaml " \
                  "--model_path output_r05_480_e20/best_model/model.pdparams " \
                  f"--image_path  {img_path} " \
                  f"--save_dir {self.seg_out_dir} >> {self.current_tmp_dir}/seg_log.txt 2>&1 "
        # 模型zyxpaddleseg
        cmd_vis_zyxpaddleseg = f"{machine_group.init_envirement_script()}export CUDA_VISIBLE_DEVICES={GPU_DEVICE};" \
                               f"cd {MODEL_DIR}/zyxpaddleseg;" \
                               "python predict_zyx.py --config configs/quick_start/cae.yaml " \
                               "--model_path output/best_model/model.pdparams " \
                               f"--image_path  {img_path} " \
                               f"--save_dir {self.seg_out_dir} >> {self.current_tmp_dir}/seg_log.txt 2>&1 "
        cmd_padddleseg_vis_v3 = f"{machine_group.init_envirement_script()}export CUDA_VISIBLE_DEVICES={GPU_DEVICE};" \
                                f"cd {MODEL_DIR}/paddleseg_vis_v3/;" \
                                "python predict_zyx.py --config configs/quick_start/res50.yaml " \
                                "--model_path output/best_model/model.pdparams " \
                                f"--image_path  {img_path} " \
                                f"--save_dir {self.seg_out_dir} >> {self.current_tmp_dir}/seg_log.txt 2>&1 "
        cmd_str = {
            MODEL_VIS: cmd_vis,
            MODEL_MULTI_CHANNE: cmd_multi_channel,
            MODEL_zyxpaddleseg: cmd_vis_zyxpaddleseg,
            MODEL_paddleseg_vis_v3: cmd_padddleseg_vis_v3,
        }
        return cmd_str.get(self.model_type)

    def get_cmd_vis_sdk(self, img_path):
        """
        vis模型的执行方式
        """
        img_dir = Path(img_path).parent
        export_cmd = "export LD_LIBRARY_PATH=/opt/compiler/gcc-8.2/lib64:/usr/lib64:" \
                     "/home/<USER>/chenlong22/cuda-11.2-runtime/lib64:/home/<USER>/chenlong22/cuda/lib64"
        os.makedirs(Path(self.seg_out_dir) / 'heatmap_prediction')
        cmd_str = {
            MODEL_border_recognition_sdk_v1_1: f"{export_cmd};cd {MODEL_DIR}/border-recognition-sdk-v1.1/;"
                                               f"./build/app/border_recognition/border_recognition_demo"
                                               f" ./config/border_recognition/"
                                               f" {str(img_dir)}"
                                               f" {img_path}"
                                               f" {self.seg_out_dir} >> {self.current_tmp_dir}/seg_log.txt 2>&1 ",
            MODEL_border_recognition_sdk_v2_1: f"{export_cmd};cd {MODEL_DIR}/border-recognition-sdk-v2.1/;"
                                               f"./build/app/border_recognition/border_recognition_demo"
                                               f" ./config/border_recognition/"
                                               f" {str(img_dir)}"
                                               f" {img_path}"
                                               f" {self.seg_out_dir} >> {self.current_tmp_dir}/seg_log.txt 2>&1 "
        }
        return cmd_str.get(self.model_type)

    def upload_product(self):
        """
        上传成果数据
        :return:
        """
        # 检测文件完整性
        if (
                self.model_type not in VIS_SDK_MODEL and
                not os.path.isdir(f"{self.current_tmp_dir}/product/added_prediction")
        ) or (
                self.model_type in VIS_SDK_MODEL and
                len(glob.glob(f"{self.current_tmp_dir}/product/heatmap_prediction/*.png")
                    ) < 1
        ):
            self.exec_fail = True
            self.exec_fail_reason = "未生成识别文件"
            return
        shutil.copytree(self.current_tmp_dir + "/merged/json_info", self.seg_out_dir + "/json_info")
        shutil.copyfile(self.current_tmp_dir + "/seg_log.txt", self.seg_out_dir + "/seg_log.txt")
        self.afs_file_name = self.uuid_str + "_product_" + self.get_filename(30)
        F.exec_shell_cmd(f"cd {self.current_tmp_dir};"
                         f"tar -cf {self.afs_file_name} product/seg_log.txt product/json_info"
                         f" product/pseudo_color_prediction product/heatmap_prediction;")
        if Path(f"{self.current_tmp_dir}/product/added_prediction").exists():
            # sdk模型只生成 heatmap_prediction, 不生成 added_prediction,pseudo_color_prediction
            F.exec_shell_cmd(f"tar -cf {self.afs_file_name}_pred_pic.tar product/added_prediction")
        if not os.path.exists(f"{self.current_tmp_dir}/{self.afs_file_name}"):
            print("文件打包失败")
            self.exec_fail = True
            self.exec_fail_reason = "未生成压缩包文件"
            return
        afs_tool = AfsTool()
        afs_tool.put(f"{self.current_tmp_dir}/{self.afs_file_name}", self.afs_output_dir)
        if Path(f"{self.current_tmp_dir}/{self.afs_file_name}_pred_pic.tar").exists():
            afs_tool.put(f"{self.current_tmp_dir}/{self.afs_file_name}_pred_pic.tar", self.afs_output_dir)
        self.afs_output_full_path = self.afs_output_dir + "/" + self.afs_file_name
        print(self.afs_output_full_path)

    def __call__(self, *args, **kwargs):
        feed_list = self.parse_file()
        self.do_seg(feed_list)
        self.upload_product()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.clean()
        try:
            F.close_log_client(self.logger)
        except Exception as e:
            pass

    def clean(self):
        """
        清理文件,执行失败时保留文件
        :return:
        """
        if self.exec_fail:
            print(self.exec_fail_reason)
            return
        file_list = os.listdir(self.current_tmp_dir)
        for i in file_list:
            full_path = self.current_tmp_dir + "/" + i
            if os.path.isfile(full_path):
                os.remove(full_path)
                continue
            if os.path.isdir(full_path):
                if i != "product":
                    shutil.rmtree(full_path)
                else:
                    shutil.rmtree(full_path + "/added_prediction", ignore_errors=True)


class AoiSegJobExecutor(FlowJobBase):
    def __init__(self):
        name = ConstWorkFlow.FLOW_SUBTASK_EXEC_SEG
        FlowJobBase.__init__(self, flow_name=name)

    def __call__(self, *args, **kwargs):
        # if not machine_group.machine_na002():
        #     self.logger.info("非na002机器，不执行")
        #     return
        t = self.get_can_exec_task(AoiMlModel)
        if not t.wpf_id or not t.uuid_str:
            return
        if "afs_url" not in t.wpf_flow_info:
            self.logger.warning("无afs_url， 结束")
            t.aoi_ml_flow_model.set_subtask_fail(t.wpf_id, t.task_id, "无afs_url")
            return
        try:
            # 执行任务
            with SegJob(t.uuid_str, t.wpf_flow_info['afs_url'],
                        t.wpf_flow_info.get("model_type", MODEL_MULTI_CHANNE)) as s:
                t.aoi_ml_flow_model.set_subtask_doing(t.wpf_id, t.task_id, s.current_tmp_dir)
                s()
                # mysql重新连接
                t.aoi_ml_flow_model.connection.ping()
                params = ""
                if s.exec_fail:
                    t.aoi_ml_flow_model.set_subtask_fail(t.wpf_id, t.task_id, s.exec_fail_reason)
                    next_task = ConstWorkFlow.get_next_subtask(t.task_type, t.section_name,
                                                               ConstWorkFlow.FLOW_STATUS_FAIL)
                else:
                    afs_path = s.afs_output_full_path
                    next_task = ConstWorkFlow.get_next_subtask(t.task_type, t.section_name)
                    t.wpf_flow_info['afs_url'] = afs_path
                    params = json.dumps(t.wpf_flow_info, ensure_ascii=False)
                    t.aoi_ml_flow_model.set_subtask_success(t.wpf_id, t.task_id, params)
                # 自动生成下一环节任务
                self.create_next_task(t.wpf_id, t.task_id, next_task, params)
        except Exception as e:
            t.aoi_ml_flow_model.connection.ping()
            t.aoi_ml_flow_model.set_subtask_fail(t.wpf_id, t.task_id,
                                                 "系统异常：" + str(e.args) + str(traceback.format_exc()))


if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == "auto":
            a = AoiSegJobExecutor()
            a()
        else:
            raise ValueError("参数错误")
    else:
        pass
        # 测试用
        # uuidStr = "aaa_" + time.strftime("%Y%m%d%H%M%S") + "_seg_job"
        # feed_file = "/user/map-data-streeview/aoi-ml/feeds/bad_review/village_bidlist_1000.tar"
        # with SegJob(uuidStr, feed_file, model_type=MODEL_border_recognition_sdk_v2_1) as s:
        #     s()
