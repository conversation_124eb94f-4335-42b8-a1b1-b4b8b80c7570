# !/usr/bin/env python3
"""
aoi识别后矢量化
1。 下载矢量化后的数据
2。 执行矢量化操作
3。 合并街区，生成json文件
"""
import json
import os.path
import sys
import shutil
import time
import uuid
from pathlib import Path

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.tools.afs_tool import AfsTool
from src.seg_post_process.main import run_main as seg_run_main
from script.inner_roads.pre_process.create_seg_uuid import run_main as uuid_run_main
from src.const.work_flow import ConstWorkFlow
from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.tools.redis_tool import RedisTool
from src.tools import function as F
from src.tools.conf_tools import is_debug
from src.model_mysql.aoi_ml_model import AoiMlModel
import glob

tmp_dir = ROOT_PATH + "/../flow_data/aoi_seg_post_job"


class AoiSegPostJob:
    """
    识别后生成的图片信息
    """

    def __init__(self, uuid_str, scan_file_path, is_filter_small=True, filter_area=4000):
        """
        初始化
        :param uuid_str: 生成的唯一编号
        :param scan_file_path: 语义分割后的文件地址,是afs的存储路径
        :param is_filter_small: 是否过滤小区域
        """
        self.uuid_str = uuid_str
        self.scan_file_path = scan_file_path
        self.current_tmp_dir = tmp_dir + "/" + self.uuid_str
        self.exec_fail = False
        self.exec_fail_reason = ""
        self.is_filter_small = is_filter_small
        self.street_dir = ""
        self.vec_dir = ""
        self.origin_file_name = ""
        os.makedirs(self.current_tmp_dir, exist_ok=True)
        self.afs_output_dir = "/user/map-data-streeview/aoi-ml/flow_data/aoi_seg_post_job/"
        self.afs_output_filename = ""
        self.afs_output_full_path = ""
        self.filter_area = filter_area

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.clean()

    def download_file(self):
        afs_tool = AfsTool()
        print("下载文件")
        afs_tool.get(self.scan_file_path, self.current_tmp_dir)
        file_full_name = self.current_tmp_dir + "/" + self.get_file_name()
        if not os.path.isfile(file_full_name):
            print("文件下载失败")
            self.exec_fail = True
            self.exec_fail_reason = f"下载文件失败,无{file_full_name}"
            return
        # 解压文件
        F.exec_shell_cmd(f"cd {self.current_tmp_dir}; tar -xf {file_full_name}")
        for i in os.listdir(self.current_tmp_dir):
            # 寻找可以进行识别的子文件夹，矢量化那边已经处理了文件夹问题
            if (Path(self.current_tmp_dir) / i / 'pseudo_color_prediction').is_dir() or \
                    (Path(self.current_tmp_dir) / i / 'heatmap_prediction').is_dir():
                current_dir = self.current_tmp_dir + "/" + i
                self.vec_dir = current_dir
                self.street_dir = current_dir
                break

    def get_file_name(self):
        name_list = str(self.scan_file_path).split("/")
        return name_list[-1]

    def __call__(self):
        self.download_file()
        # 执行main方法
        seg_run_main(self.street_dir, self.vec_dir)
        # 执行合并
        vec_dir_output = self.vec_dir + "/output_vectorize/vectorize_info"
        street_json_path = self.street_dir + "/json_info/image"
        uuid_run_main(vec_dir_output, street_json_path, is_filter=self.is_filter_small)
        # 合并后的文件名称
        merged_json_info = vec_dir_output + "_seg_uuid.json"
        print("merged_file:" + merged_json_info)
        if not os.path.isfile(merged_json_info):
            print("create merged_file error")
            self.exec_fail = True
            self.exec_fail_reason = f"未生成合并文件{merged_json_info}"
            return
        # 上传到afs
        self.afs_output_filename = f'{self.uuid_str}_{self.get_file_name()}.json'
        new_file_name = f"{self.vec_dir}/output_vectorize/{self.afs_output_filename}"
        F.exec_shell_cmd(
            f"cd {self.vec_dir}/output_vectorize; mv vectorize_info_seg_uuid.json {new_file_name}")
        afs_tool = AfsTool()
        afs_tool.put(new_file_name, self.afs_output_dir)
        # 上传tar文件到文件夹
        F.exec_shell_cmd(
            f"cd {self.vec_dir}/output_vectorize; "
            f"tar -cf {self.afs_output_filename}.tar  ./debug ./failed_cases ./vectorize_info")
        afs_tool.put(f"{self.vec_dir}/output_vectorize/{self.afs_output_filename}.tar", self.afs_output_dir)
        self.afs_output_full_path = self.afs_output_dir + "/" + self.afs_output_filename

    def clean(self):
        """
        清除一些缓存目录
        :return:
        """
        if not self.exec_fail:
            f_list = os.listdir(self.current_tmp_dir)
            for i in f_list:
                full_path = self.current_tmp_dir + "/" + i
                if os.path.isfile(full_path):
                    os.remove(full_path)


class AoiSegPostJobExecutor:
    def __init__(self):
        self.name = ConstWorkFlow.FLOW_SUBTASK_SEG_POST_JOB
        self.logger = F.get_logging_client(self.name)

    def __del__(self):
        F.close_log_client(self.logger)

    def __call__(self, *args, **kwargs):
        """
        执行任务
        :return:
        """
        with AoiMlFlowModel() as af:
            data_list = af.get_flow_subtask(ConstWorkFlow.FLOW_SUBTASK_SEG_POST_JOB, ConstWorkFlow.FLOW_STATUS_WAIT)
            if len(data_list) < 1:
                self.logger.info("没有任务需要处理")
                return
            with AoiMlModel() as rt:
                for i in data_list:
                    wpf_id, task_id, section_name, status, wpf_flow_info, task_name, task_type, task_content = i

                    wpf_flow_info = json.loads(wpf_flow_info)
                    wpf_flow_info = dict(ConstWorkFlow.get_task_conf(task_type), **wpf_flow_info)
                    if 'afs_url' not in wpf_flow_info or wpf_flow_info['afs_url'] == "":
                        af.set_subtask_fail(wpf_id, task_id, "无afs_url")
                        continue
                    # 锁定
                    filter_small = True
                    if 'filter_small' in wpf_flow_info and int(wpf_flow_info['filter_small']) < 1:
                        self.logger.info("不需要过滤小文件")
                        filter_small = False
                    key = self.name + ":" + str(wpf_id)
                    succ, lock_val = rt.set_lock(key)
                    if not succ:
                        self.logger.warning(f"任务被锁定,跳过{wpf_id}: key:{key}")
                        continue
                    uuid_str = f"{time.strftime('%Y%m%d%H%M%S')}{'_debug' if is_debug() else ''}_{wpf_id}"
                    with AoiSegPostJob(uuid_str, wpf_flow_info.get("afs_url"),
                                       is_filter_small=filter_small) as aoi_seg_post_job:
                        af.set_subtask_doing(wpf_id, task_id, aoi_seg_post_job.current_tmp_dir)
                        try:
                            # 执行任务
                            aoi_seg_post_job()
                        except Exception as e:
                            af.set_subtask_fail(wpf_id, task_id, str(e.args))
                            return
                        # mysql断线重新连接
                        af.connection.ping()
                        params = ""
                        if aoi_seg_post_job.exec_fail:
                            af.set_subtask_fail(wpf_id, task_id, aoi_seg_post_job.exec_fail_reason)
                            next_task = ConstWorkFlow.get_next_subtask(task_type, section_name,
                                                                       ConstWorkFlow.FLOW_STATUS_FAIL)
                        else:
                            afs_path = aoi_seg_post_job.afs_output_full_path
                            next_task = ConstWorkFlow.get_next_subtask(task_type, section_name)
                            # 参数传递到下一环节
                            wpf_flow_info['afs_url'] = afs_path
                            params = json.dumps(wpf_flow_info, ensure_ascii=False)
                            af.set_subtask_success(wpf_id, task_id, params)
                        # 自动生成下一环节任务
                        af.create_next_subtask(wpf_id, task_id, next_task, params)
                        return


if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == 'auto':
            a = AoiSegPostJobExecutor()
            a()
        else:
            raise ValueError("请输入正确的命令")
    else:
        pass
