# !/usr/bin/env python3
"""
识别图片自动上传
"""
import glob
import json
import os
import shutil
import sys
import traceback
import time
import tqdm

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.deoss import DeossTool
from src.model.aoi_model import AoiModel
from src.tools.conf_tools import is_debug
from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.model_mysql.aoi_ml_model import AoiMlModel
from src.const.redis_key import ConstRedisKey
from src.const.work_flow import ConstWorkFlow
from multiprocessing import Pool
import shapely.wkt
from pathlib import Path

tmp_dir = ROOT_PATH + "/../flow_data/street_pic_upload"


class UploadParam:
    def __init__(self):
        # 文件路径
        self.file_path = ""
        # 矢量化路径
        self.vec_path = ""
        # 模型类型
        self.model = ""
        # 任务id
        self.task_id = 0
        # 任务类型
        self.task_type = ""


def upload_multi(upload_param: UploadParam, deoss: DeossTool, log_client, aoi_model: AoiModel):
    """
    支持多进程上传
    :param upload_param: 上传基础信息
    :param deoss: deoss 库
    :param log_client: 日志工具
    :param aoi_model: aoi模型库
    :return:
    """
    file_path = upload_param.file_path
    vec_path = upload_param.vec_path
    model = upload_param.model
    task_id = int(upload_param.task_id)

    prediction_images_dir = vec_path + "/pseudo_color_prediction/images"
    f = open(file_path, 'r')
    json_data = json.load(f)
    f.close()

    id = json_data['id']
    img_file_path = f"{prediction_images_dir}/{id}.png"
    batch_info = json_data.get("batch_info", "google2021")
    if not os.path.isfile(img_file_path):
        print(f"文件不存在:{img_file_path}")
        return
    if aoi_model.check_pic_exists(id, model, batch_info):
        print(f"文件已经上传完成：{img_file_path}")
        return
    img_id = f"{'debug_' if is_debug() else ''}{model}_{id}_{batch_info}_pred.png"
    info = {
        "region": json_data['region'],
        "geom": json_data['geom'],
        "width": json_data['width'],
        "height": json_data['height'],
    }
    if info['width'] < 100 or info['height'] < 100:
        return

    try:
        ret = deoss.upload(img_file_path, img_id)
        if ret:
            pic_url = {
                "pred_pic_url": ret,
                "pred_pic_objid": img_id
            }
            geom = shapely.wkt.loads(info['geom']).envelope.wkt
            aoi_model.save_region_pic(id, json.dumps(info), json.dumps(pic_url), model,
                                      geom, task_id=task_id, batch_info=batch_info, task_type=upload_param.task_type)
            return True
        else:
            log_client.warning(f"上传文件失败, 无连接url：{img_id}")
        return False
    except Exception as e:
        log_client.warning(f"保存文件存在错误：{img_id}, {e.args}_{traceback.format_exc()}")
        return False


class StreetImgUpload:
    def __init__(self, uuid_str, afs_path, model="model_multi_channel", task_id=0):
        self.uuid_str = uuid_str
        self.scan_file_path = afs_path
        self.current_tmp_dir = tmp_dir + "/" + self.uuid_str
        self.vec_dir = ""
        self.exec_fail = False
        self.exec_fail_reason = ""
        self.model = model
        self.deoss = DeossTool()
        self.task_id = task_id
        self.log_client = F.get_logging_client(f"upload_street_pic_{task_id}")
        self.is_debug = is_debug()

    def get_file_name(self):
        """
        获取文件名称
        :return:
        """
        name_list = str(self.scan_file_path).split("/")
        return name_list[-1]

    def get_task_type(self):
        """
        确定任务类型, 暂时先根据文件名称区分：
             seg_scan: 扫街任务
             poi_tag： poi_tag
             bad_review 数据评估

        :return:
        """
        if 'seg_scan' in self.scan_file_path:
            return 'seg_scan_street'
        if 'poi_tag' in self.scan_file_path:
            return "seg_scan_poi_tag"
        if 'bad_review' in self.scan_file_path:
            return 'seg_scan_evaluate'
        return ""

    def download_file(self):
        """
        下载，解压，预处理文件
        :return:
        """
        afs_tool = AfsTool()
        afs_tool.get(self.scan_file_path, self.current_tmp_dir)
        file_full_name = self.current_tmp_dir + "/" + self.get_file_name()
        if not os.path.isfile(file_full_name):
            self.exec_fail = True
            self.exec_fail_reason = f"下载文件失败,无{file_full_name}"
            return
        # 解压文件
        F.exec_shell_cmd(f"cd {self.current_tmp_dir}; tar -xf {file_full_name}")
        for check_vec_path in Path(self.current_tmp_dir).rglob("**/pseudo_color_prediction"):
            pseudo_dir = check_vec_path.parent
            # 新模型不生成 images文件夹，统一处理
            for image in pseudo_dir.rglob("**/*.png"):
                if image.parent.name != "images":
                    os.makedirs(image.parent / 'images', exist_ok=True)
                    image.rename(image.parent / 'images' / image.name)
            self.vec_dir = str(pseudo_dir.absolute())

        # 检测下是否存在文件夹
        #  1. json_info/image/*.json 2. pseudo_color_prediction/images/*.png
        if not os.path.isdir(f"{self.vec_dir}/json_info/image") or not os.path.isdir(
                f"{self.vec_dir}/pseudo_color_prediction/images"):
            self.exec_fail = True
            self.exec_fail_reason = "文件格式错误，无法处理"

    def do_upload(self, vec_path):
        """
        执行上传
        :param vec_path: 模型识别输出的文件，格式是 .png结尾
        :return:
        """
        street_json_file = vec_path + "/json_info/image/*.json"
        json_files = glob.glob(street_json_file)
        param_list = []
        task_type = self.get_task_type()
        for i in json_files:
            param = UploadParam()
            param.file_path = i
            param.vec_path = vec_path
            param.model = self.model
            param.task_id = int(self.task_id)
            param.task_type = task_type
            param_list.append(param)

        deoss = DeossTool()
        # 多进程会导致文件上传失败（下游qps限制），暂时用单线程
        with AoiModel() as aoi_model:
            for i in tqdm.tqdm(param_list):
                upload_multi(i, deoss, self.log_client, aoi_model)

    def __call__(self, *args, **kwargs):
        self.download_file()
        if self.exec_fail:
            return
        # 执行上传
        self.do_upload(self.vec_dir)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if not self.exec_fail:
            shutil.rmtree(self.current_tmp_dir)


class StreetImgUploadExecutor:
    def __init__(self):
        self.name = "StreetImgUpload"
        self.log_client = F.get_logging_client(self.name)

    def __del__(self):
        F.close_log_client(self.log_client)

    def __call__(self, *args, **kwargs):
        with AoiMlFlowModel() as af:
            wait_upload_jobs = af.get_wait_upload_img_task(30)
            if len(wait_upload_jobs) < 1:
                self.log_client.warning("暂时没有任务需要处理")
                return
            with AoiMlModel() as al:
                for i in wait_upload_jobs:
                    task_id, image_status, wpf_status, wpf_flow_info, wpf_last_modify_time = i
                    wpf_flow_info_obj = json.loads(wpf_flow_info)
                    k = self.name + ":" + str(task_id)
                    succ, lock_val = al.set_lock(k)
                    if not succ:
                        self.log_client.warning(f"任务锁定。无法执行{task_id}, lock_key:[{ConstRedisKey.REDIS_LOCK_PREFIX}{k}]")
                        continue
                    af.update_task_by_id(task_id, {"image_status": ConstWorkFlow.FLOW_STATUS_DOING})
                    afs_path = wpf_flow_info_obj.get('afs_url', "")
                    model_name = wpf_flow_info_obj.get("model_type", "model_multi_channel")
                    uuid = f"{time.strftime('%Y%m%d%H%M')}_{task_id}"
                    try:
                        with StreetImgUpload(uuid, afs_path, model_name, int(task_id)) as si:
                            si()
                            af.connection.ping()
                            if si.exec_fail:
                                af.set_task_data(task_id, f"{self.name}_error", si.exec_fail_reason)
                                af.update_task_by_id(task_id, {"image_status": ConstWorkFlow.FLOW_STATUS_ERROR})
                            else:
                                af.update_task_by_id(task_id, {"image_status": ConstWorkFlow.FLOW_STATUS_SUCCESS})
                    except Exception as e:
                        af.connection.ping()
                        af.set_task_data(task_id, f"{self.name}_error", f"{e.args}, {traceback.format_exc()}")
                        af.update_task_by_id(task_id, {"image_status": ConstWorkFlow.FLOW_STATUS_ERROR})
                    # 一次执行一个
                    return


if __name__ == '__main__':
    s = StreetImgUploadExecutor()
    s()
    # uuid = "aaa_imgupload_" + time.strftime("%Y%m%d%H%M")
    # afs_path = '/user/map-data-streeview/aoi-ml/flow_data/aoi_seg_job/20230301094800_286_product_seg_scan_长春市.tar'
    # c = StreetImgUpload(uuid, afs_path)
    # c()
