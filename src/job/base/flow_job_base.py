# !/usr/bin/env python3
from typing import Dict

from src.tools import function as F
from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.const.work_flow import ConstWorkFlow
import time
import json
from src.tools.conf_tools import is_debug
from src.model_mysql.aoi_ml_model import AoiMlModel


class TaskInfo:
    def __init__(self):
        self.uuid_str = ""
        self.task_id: str = ""
        self.wpf_id: str = ""
        self.wpf_flow_info: Dict = {}
        self.section_name = ""
        self.task_type = ""
        self.aoi_ml_flow_model: AoiMlFlowModel = None


class FlowJobBase:
    def __init__(self, flow_name):
        self.name = flow_name
        self.logger = F.get_logging_client(self.name)

    def __del__(self):
        F.close_log_client(self.logger)

    def get_can_exec_task(self, lock_client=None) -> TaskInfo:
        t = TaskInfo()
        if not lock_client:
            lock_client = AoiMlModel
        with AoiMlFlowModel() as af:
            data_list = af.get_flow_subtask(self.name, ConstWorkFlow.FLOW_STATUS_WAIT, 20)
            if len(data_list) < 1:
                self.logger.info("没有任务需要处理")
                return t
            with lock_client() as rt:
                for i in data_list:
                    wpf_id, task_id, section_name, status, wpf_flow_info, task_name, task_type, task_content = i
                    wpf_flow_info = json.loads(wpf_flow_info)
                    # 合并配置文件
                    wpf_flow_info = dict(ConstWorkFlow.get_task_conf(task_type), **wpf_flow_info)
                    if 'exec_machine' in wpf_flow_info:
                        # 判断是否绑定执行的机器，不满足绑定的机器规则，不处理
                        if self.name in wpf_flow_info['exec_machine'] \
                                and wpf_flow_info['exec_machine'][self.name] != "*":
                            if wpf_flow_info['exec_machine'][self.name] not in F.get_host_name():
                                continue
                    # 锁定
                    key = self.name + ":" + str(wpf_id)
                    succ, lock_val = rt.set_lock(key)
                    if not succ:
                        self.logger.warning(f"任务被锁定,跳过{wpf_id}: key:{key}")
                        continue
                    uuid_str = f"{time.strftime('%Y%m%d%H%M%S')}{'_debug' if is_debug() else ''}_{wpf_id}"
                    t.task_id = task_id
                    t.wpf_id = wpf_id
                    t.wpf_flow_info = wpf_flow_info
                    t.uuid_str = uuid_str
                    t.aoi_ml_flow_model = af
                    t.section_name = section_name
                    t.task_type = task_type
                    return t
        return t

    def create_next_task(self, wpf_id, task_id, next_task, params):
        """
        生成下一环节的任务
        :param wpf_id: 流程id
        :param task_id:任务id
        :param next_task: 下一个环节的任务
        :param params:参数
        :return:
        """
        with AoiMlFlowModel() as af:
            # 自动生成下一环节任务
            af.create_next_subtask(wpf_id, task_id, next_task, params)
