# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
情报数据抓取入库
"""
import os
import subprocess
import uuid
import math
import sys
import time
from pathlib import Path
import src.tools.redis_tool as rt
import src.tools.conf_tools as ct
from src.model.poi_online import PoiOnlineModel
from src.model.aoi_model import AoiModel


def info_crawler_import_handle(date=None):
    """ 情报抓取入库入口
    :return:
    """
    with rt.RedisTool() as rt_instance:
        if date is None:
            date = time.strftime("%Y-%m-%d", time.localtime())
        key = "info_crawler_import_{}".format(date)
        if not rt_instance.redis_conn.set(key, 1, 1800, None, True):
            return
        curr_data = retrieve_data(date)
        print("get info file ", curr_data)
        if curr_data is not None:
            process_res_file(date, curr_data)
        rt_instance.redis_conn.delete(key)


def retrieve_data(date):
    """ 获取情报数据
    :return:
    """
    afs_list = ct.get_config('afs')
    if "fenghuang" not in afs_list:
        return
    afs_conf = afs_list['fenghuang']

    file_name = "sjc.{}".format(date.replace("-", ""))
    data_src = "afs://fenghuang.afs.baidu.com:9902/user/map_data_aoi/info/crawler/{}".format(file_name)
    data_dst = ct.get_config('tmp_dir') + "/" + uuid.uuid4().hex
    dst_file = data_dst + "/" + file_name
    Path(data_dst).mkdir(parents=True, exist_ok=True)

    process = subprocess.Popen(
        [afs_conf['shell'], '--username=' + afs_conf['username'], '--password=' + afs_conf['password'],
         'get', data_src, data_dst],
        stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    process.communicate()
    if os.path.exists(dst_file):
        return dst_file


def cal_mesh_id(x: float, y: float):
    """
        根据传入的坐标计算所在的图幅号，传入坐标为国测局经纬度信息
    Args:
        x: 经度坐标
        y: 纬度坐标
    Return:
        ret: 该经纬度对应的图幅号
    """
    intm1m2 = math.floor(y * 1.5)
    intj1 = math.floor(x)
    dblj2 = x - math.floor(x)
    intm3m4 = math.floor(intj1 - 60)
    intm5 = math.floor((y - intm1m2 / 1.5) * 12.0)
    intm6 = math.floor(dblj2 * 8.0)
    if (intm1m2 < 0 or intm1m2 > 99 or intm3m4 < 0 or intm3m4 > 99 or
            intm5 < 0 or intm5 > 7 or intm6 < 0 or intm6 > 7):
        return 0
    ret = int(intm1m2) * 10000 + int(intm3m4) * 100 + int(intm5) * 10 + int(intm6)
    return ret


def process_res_file(date: str, file_name: str):
    """
    读取竞品文件的aoi数据，判断是否与母库数据冲突，识别出新增和更新的结果保存至数据库
    """
    p = PoiOnlineModel()
    a = AoiModel()

    # 读取竞品抓取的aoi文件信息，并入库
    with open(file_name, "r") as f:
        lines = f.readlines()
        for line in lines:
            tid, bid, pid, name, city, category, address, location, aoi, rating, tel, open_hours, tag, status, \
            created_at, modifed_at = line.rstrip().split('\t')
            if tid == "tid":
                continue
            geom = "POLYGON((" + aoi.replace(',', ' ').replace(';', ',') + "))"
            point = location.split(',')
            display_x = float(point[1])
            display_y = float(point[0])
            mesh_id = cal_mesh_id(display_x, display_y)
            geom_error = 0
            if "|" in aoi or aoi.strip() == "-" or aoi.strip() == "":
                geom_error = 1
            elif aoi.split(';', 1)[0].strip() != aoi.rsplit(';', 1)[1].strip():
                geom_error = 1
            if geom_error != 0:  # 错误aoi 跳过
                continue
            try:
                swap_geom = p.swap_geom(geom)
                if len(swap_geom) == 0:
                    return
                geom = p.make_valid(swap_geom[0])
                print("curr geom is bid:{}, geom:{}".format(bid, geom))
                p.insert_aoi_intelligence_history([bid, tid, name, address, display_x, display_y,
                                                   str(mesh_id), geom, date, modifed_at])
            except Exception as e:
                return e

            union_geom = ""
            aoi_res = a.query_overlay_aoi(geom)
            if len(aoi_res) > 0:
                union_geom = aoi_res[0]

            if union_geom is not None and union_geom != "":
                area_qb, area_overlap = p.get_overlay_area(geom, union_geom)
                if area_overlap / area_qb > 0.7:  # 压盖过大，过滤
                    continue

            try:
                print("start insert bid:", bid)
                p.insert_aoi_intelligence([bid, tid, name, address, display_x, display_y,
                                           str(mesh_id), geom, date, 1, modifed_at])
            except Exception as e:
                return e

    p.commit()
    p.close()


if __name__ == '__main__':
    _date = sys.argv[1]
    print("date", _date)
    info_crawler_import_handle(_date)
