# !/usr/bin/env python3
"""
创建任务后生成第一步任务的脚本
"""
import os.path
import sys

ROOT_PATH = os.path.abspath(os.path.dirname(__file__) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.model_mysql.aoi_ml_flow_model import AoiMlFlowModel
from src.tools.redis_tool import RedisTool
from src.tools import function as F
from src.const.redis_key import ConstRedisKey
from src.const.work_flow import ConstWorkFlow
from src.model_mysql.aoi_ml_model import AoiMlModel


class CreateFirstFlowJobExecutor:
    def __init__(self):
        self.name = ConstWorkFlow.FLOW_SUBTASK_START
        self.log_client = F.get_logging_client("first_job_create")

    def get_wait_job(self, af: AoiMlFlowModel):
        data = af.get_wait_create_first_job_task(
            fields=['task_id', 'task_type', 'task_current_flow', 'task_status', 'task_content'])
        print(len(data))
        return data

    def __call__(self, *args, **kwargs):

        with AoiMlFlowModel() as af:
            waited_job = self.get_wait_job(af)
            if len(waited_job) < 1:
                self.log_client.warning("暂时没有任务需要处理")
                return
            with AoiMlModel() as rt:
                for i in waited_job:
                    task_id, task_type, task_current_flow, task_status, task_content = i
                    print(task_id, task_type, task_current_flow, task_status)
                    k = self.name + ":" + str(task_id)
                    succ, lock_val = rt.set_lock(k)
                    if not succ:
                        self.log_client.warning(f"任务锁定。无法执行{task_id}, lock_key:[{ConstRedisKey.REDIS_LOCK_PREFIX}{k}]")
                        continue
                    self.log_client.info(f"开始创建第一个子任务{task_id}")
                    next_task = ConstWorkFlow.get_next_subtask(task_type, task_current_flow)
                    if next_task == ConstWorkFlow.FLOW_SUBTASK_END:
                        self.log_client.error(f"无下一步任务，缺少任务类型配置{task_id}")
                        af.update_task_by_id(task_id, {
                            "task_current_flow": next_task,
                            "task_status": ConstWorkFlow.FLOW_STATUS_ERROR,
                            "task_error_desc": "无下一步任务，缺少任务类型配置"
                        })
                        continue
                    # 生成 第一个任务
                    subtask_data = {
                        "wpf_task_id": task_id,
                        "wpf_section_name": next_task,
                        "wpf_status": ConstWorkFlow.FLOW_STATUS_WAIT,
                        "wpf_flow_info": task_content
                    }
                    try:
                        if af.create_subtask(subtask_data):
                            af.update_task_by_id(task_id, {
                                "task_current_flow": next_task,
                                "task_status": ConstWorkFlow.FLOW_STATUS_WAIT,
                            })
                    except Exception as e:
                        af.update_task_by_id(task_id, {
                            "task_status": ConstWorkFlow.FLOW_STATUS_ERROR,
                            "task_error_desc": str(e.args),
                        })

    def __del__(self):
        F.close_log_client(self.log_client)


if __name__ == '__main__':
    # 测试用
    c = CreateFirstFlowJobExecutor()
    c()
