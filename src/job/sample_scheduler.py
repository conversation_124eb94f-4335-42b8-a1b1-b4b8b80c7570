# !/usr/bin/env python3
import datetime
import time
from concurrent.futures.process import ProcessPoolExecutor


def test_task():
    """
    常规任务
    :return:
    """
    print(" current is {}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")))


class TaskWithProces:
    """
    测试多进程调用方法
    """
    def __call__(self, *args, **kwargs):
        print("start process {} {}".format(args, kwargs))
        time.sleep(3)
        return "{} success" .format(args[0])


def task_with_process():
    """
    进程池任务
    :return:
    """
    t = TaskWithProces()
    # 使用线程池执行任务
    with ProcessPoolExecutor(3) as p:
        for i in p.map(t, [x for x in range(0, 9)]):
            print(i)
