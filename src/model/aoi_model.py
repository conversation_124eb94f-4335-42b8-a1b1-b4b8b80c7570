# !/usr/bin/env python3
"""
aoi相关查询接口
"""
from typing import List
from src.tools.pg_tool import PgTool


class AoiModel(PgTool):
    def __init__(self, road_use_conf=False):
        """
        初始化连接数据库
        :param road_use_conf: True 使用配置中的数据库，False 使用接口返回的数据库
        """
        PgTool.__init__(self, road_use_conf)

    def __enter__(self):
        """
        支持with
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        调用上级别的
        :param exc_type:
        :param exc_val:
        :param  exc_tb:
        :return: 无返回
        """
        PgTool.__exit__(self, exc_type, exc_val, exc_tb)

    def get_street_by_file(self, name, buffer=0) -> str:
        """ 获得街区范围
        :param name:  hash名称
        :param buffer:  buffer距离， 单位 米
        :return:  街区wkt
        """
        if buffer > 0:
            sql = "select st_astext(st_buffer(geometry::geography, {})) as geom " \
                  "from aoi_ml_region where face_id = '{}'".format(buffer, name)
        else:
            sql = "select st_astext(geometry) as geom from aoi_ml_region where face_id = '{}'".format(name)
        online_cur = self.conn_poi.cursor()
        online_cur.execute(sql)
        res = online_cur.fetchone()
        online_cur.close()
        self.conn_poi.commit()
        if res is not None:
            return res[0]
        else:
            return ""

    def get_poi_tag_by_geom(self, area_wkt) -> List:
        """ 获得poi_tag种类
        :param area_wkt:  面wkt
        :return:  tag数量
        """
        sql = "select distinct(std_tag) from poi " \
              "where st_contains(st_geomfromtext('{}', '4326'), geometry) and relation_bid = '0'; ".format(area_wkt)
        online_cur = self.conn_poi.cursor()
        online_cur.execute(sql)
        tag_list = []
        for tag in online_cur.fetchall():
            tag_list.append(tag[0])
        online_cur.close()
        self.conn_poi.commit()
        return tag_list

    def get_intersection_by_geom(self, street) -> str:
        """ 获得相交geom
        :param street:  街区wkt
        :return:  相交geom
        """
        sql = "with tmp as ( " \
              "select face_id, st_intersection(st_geomfromtext('{}', '4326'), geom) as geom_intersection " \
              "from blu_face " \
              "where ST_Intersects(st_geomfromtext('{}', '4326'), geom) ) " \
              "select st_astext(st_union(geom_intersection)) as geom_text " \
              "from tmp where not ST_Equals(geom_intersection, st_geomfromtext('{}', '4326')) ".format(street, street,
                                                                                                       street)

        back_cur = self.conn_back.cursor()
        back_cur.execute(sql)
        data = back_cur.fetchone()[0]
        self.conn_back.commit()
        return data

    def check_is_intersection_by_geom(self, geom) -> List:
        """
        获得相交geom
        :param geom:  街区wkt
        :return:  相交geom
        """
        if geom == "":
            return []
        sql = '''
            select aoi_level, st_astext(geom), face_id
            from blu_face 
            where ST_Intersects(st_geomfromtext('{}', '4326'), geom) 
            and src != 'SD'
            and kind != '52' '''.format(geom)
        # print('[check_is_intersection_by_geom_sql]', sql)
        res = []
        with self.conn_back.cursor() as back_cur:
            try:
                back_cur.execute(sql)
                for item in back_cur.fetchall():
                    res.append(item)
            except Exception as e:
                print("[check_is_intersection_by_geom]", e, sql)
            finally:
                back_cur.close()
        self.conn_back.commit()
        return res

    def get_intersection_water_by_geom(self, street) -> List:
        """ 获得街区水系
        :param street:
        :return:
        """
        sql = "with street as (" \
              " select st_geomfromtext('{}', '4326') as street_geom" \
              ")" \
              "select face_id, " \
              "st_astext(st_buffer(st_intersection(street_geom, geom)::geography, 1)) as geom_intersection " \
              "from blc_face, street " \
              "where st_intersects(street_geom, geom) " \
              "and kind in ('1', '2', '3', '4', '5', '6') ".format(street)
        back_cur = self.conn_back.cursor()
        back_cur.execute(sql)
        res_list = []
        for geom in back_cur.fetchall():
            res_list.append(geom[1])
        back_cur.close()
        self.conn_back.commit()
        return res_list

    def get_intersection_list_by_geom(self, street, buffer=5, group_filter=False) -> List:
        """ 获得相交aoi geom
        :param street:  街区wkt
        :param buffer 外扩范围
        :return:  相交geom
        """
        filter_sql = ""
        if group_filter:
            filter_sql = " and aoi_level != 1 "
        sql = '''
            with street as (
                select st_geomfromtext('{}', '4326') as street_geom 
            ),
            tmp as ( 
                select face_id, st_makevalid(st_intersection(street_geom, st_makevalid(geom))) as geom_intersection 
                from blu_face, street 
                where st_intersects(street_geom, geom)  {}
                and st_area(st_intersection(street_geom, st_makevalid(geom))) > st_area(st_makevalid(geom)) * 0.8 
            )
            select face_id, st_astext(st_buffer(geom_intersection::geography, {})) as geom_text 
            from tmp, street where not ST_Equals(geom_intersection, street_geom) '''.format(street, filter_sql, buffer)
        # ("[get_intersection_list_by_geom_sql]", sql)
        with self.conn_back.cursor() as back_cur:
            try:
                back_cur.execute(sql)
                res_list = []
                for geom in back_cur.fetchall():
                    res_list.append(geom[1])
                return res_list
            except Exception as e:
                print('[get_intersection_list_by_geom_error]', e, sql)
                return []
            finally:
                back_cur.close()
                self.conn_back.commit()

    def get_intersection_aoi(self, geom, filter_group=False) -> List:
        """
        获取在线的相交的aoi
        """
        filter_group_sql = ""
        if filter_group:
            filter_group_sql = " and  aoi_level> 1 "
        sql = "select st_astext(geom) as geom from blu_face where" \
              " st_intersects(st_geomfromtext('{}', '4326') , geom) and src != 'SD' and kind != '52' {} "\
            .format(geom, filter_group_sql)
        # print(['get_intersection_aoi_sql', sql])
        data = []
        with self.conn_back.cursor() as back_cur:
            try:
                back_cur.execute(sql)
                data = back_cur.fetchall()
            except Exception as e:
                print("[get_intersection_aoi_err]", sql, e)
        self.conn_back.commit()
        return data

    def get_aoi_group_poi(self, wkt: str):
        """
        检查聚合院落
        :return:
        """
        sql = "select b.poi_bid,a.face_id from blu_face a left join blu_face_poi b on a.face_id=b.face_id where " \
              "st_intersects(st_geomfromtext('{}', '4326') , a.geom)" \
              " and a.kind != '52' and a.src != 'SD' and a.aoi_level=1 ".format(wkt)
        with self.conn_back.cursor() as back_cur:
            back_cur.execute(sql)
            data = back_cur.fetchall()
        self.conn_back.commit()
        return data

    def multi_get_poi_by_bids(self, poi_ids: List[str]) -> List:
        if len(poi_ids) < 1:
            return []
        data = []
        multi_ids = "'" + "','".join(poi_ids) + "'"
        sql = f"select name,std_tag,bid,relation_bid,st_astext(geometry) as point,click_pv from poi where bid in ({multi_ids})"
        with self.conn_poi.cursor() as poi_cursor:
            poi_cursor.execute(sql)
            data = poi_cursor.fetchall()
        self.conn_poi.commit()
        return data

    def get_geom_by_bid(self, bid) -> str:
        """ 获取poi坐标
        :param bid: bid
        :return:
        """
        sql = "select st_astext(geometry) from poi " \
              "where bid = '{}'; ".format(bid)
        online_cur = self.conn_poi.cursor()
        online_cur.execute(sql)
        res = online_cur.fetchone()
        online_cur.close()
        self.conn_poi.commit()
        if res is not None:
            return res[0]
        else:
            return ""

    def get_street_id_by_geom(self, geom) -> str:
        """ 获得街区id
        :param geom: geom数据
        :return: 街区id
        """
        sql = "select face_id from aoi_ml_region " \
              "where st_contains(geometry,  st_geomfromtext('{}', '4326'))".format(geom)
        online_cur = self.conn_poi.cursor()
        online_cur.execute(sql)
        res = online_cur.fetchone()
        online_cur.close()
        self.conn_poi.commit()
        if res is not None:
            return res[0]
        else:
            return ""

    def get_poi_by_geom(self, geom) -> List:
        """ 通过geom获取poi列表
        :param geom:
        :return:
        """
        sql = "select bid, mid, name, mesh_id, std_tag, click_pv, city from poi " \
              "where st_intersects(st_geomfromtext('{}', 4326), geometry)".format(geom)
        online_cur = self.conn_poi.cursor()
        online_cur.execute(sql)
        res = []
        for item in online_cur.fetchall():
            res.append(item)
        online_cur.close()
        self.conn_poi.commit()
        return res

    def get_aoi_by_bid(self, bid) -> List:
        """ 通过bid获取aoi
        :param bid:
        :return:
        """
        sql = '''
            select a.face_id, b.poi_bid, st_astext(a.geom)
            from blu_face a 
            left join blu_face_poi b on a.face_id = b.face_id
            where
                b.poi_bid = '{}' '''.format(bid)
        back_cur = self.conn_back.cursor()
        back_cur.execute(sql)
        data = back_cur.fetchone()
        back_cur.close()
        self.conn_back.commit()
        return data

    def get_line_by_geom(self, geom) -> List:
        """ 获得相交的内部路
        :param geom:
        :return:
        """
        sql = "select st_asText(geom) from nav_link " \
              "where ST_Intersects(geom,  st_geomfromtext('{}', '4326')) " \
              "and form = '52' ".format(geom)
        road_cur = self.conn_road.cursor()
        road_cur.execute(sql)
        geom_list = []
        for geom in road_cur.fetchall():
            geom_list.append(geom[0])
        road_cur.close()
        self.conn_road.commit()
        return geom_list

    def get_line_by_geom_v2(self, geom):
        """ 获得相交的内部路
        :param geom:
        :return:
        """
        sql = """select 
                link_id, s_nid, e_nid, b.adjoin_nid as s_adjoint_nid, c.adjoin_nid as e_adjoint_nid,
                a.kind, a.form, dir, lane_l, lane_r, st_asText(a.geom) as geom_text 
            from nav_link a 
            left join nav_node b on a.s_nid = b.node_id 
            left join nav_node c on a.e_nid = c.node_id 
            where ST_Intersects(a.geom,  st_geomfromtext('{}', '4326')) 
            and (a.kind > 7 or a.form = '52') """.format(geom)
        cursor = self.conn_road.cursor()
        cursor.execute(sql)
        geom_list = []
        for geom in cursor.fetchall():
            geom_list.append(geom)
        cursor.close()
        self.conn_road.commit()
        return geom_list

    def get_out_line_by_geom(self, geom, level8=False):
        """ 获得外部路
        :param geom:
        :param level8 是否为八级以上的路
        :return:
        """
        if level8:
            kind = " kind >=8 and kind <= 10 "
        else:
            kind = "kind < 8 "
        sql = '''
            select link_id, kind, form, dir, st_asText(geom) as geom_text,lane_c
            from nav_link link 
            where 
                ST_Intersects(geom,  st_geomfromtext('{}', '4326')) 
                and {}
                and form not like '%52%' '''.format(geom, kind)
        road_cur = self.conn_road.cursor()
        road_cur.execute(sql)
        geom_list = []
        for geom in road_cur.fetchall():
            geom_list.append(geom)
        road_cur.close()
        self.conn_road.commit()
        return geom_list

    def get_out_line_by_geom_v2(self, geom):
        """ 获得外部路
       :param geom:
       :return:
       """
        sql = '''
    select link_id, kind, form, dir, lane_l, lane_r, st_asText(geom) as geom_text
    from nav_link link 
    where 
        ST_Intersects(geom,  st_geomfromtext('{}', '4326')) 
        and kind < 10
        and form not like '%52%' '''.format(geom)
        cursor = self.conn_road.cursor()
        cursor.execute(sql)
        geom_list = []
        for geom in cursor.fetchall():
            geom_list.append(geom)
        cursor.close()
        self.conn_road.commit()
        return geom_list

    def get_name_by_link_id(self, link_id) -> str:
        """ 获得路名
        :param link_id
        :return:
        """
        sql = '''
            select name_ch
            from nav_link_name map
            left join nav_name name on map.name_id = name.name_id
            where map.link_id = '{}' '''.format(link_id)
        road_cur = self.conn_road.cursor()
        road_cur.execute(sql)
        res = road_cur.fetchone()
        name = ""
        if res is not None and len(res) > 0:
            name = res[0]
        road_cur.close()
        self.conn_road.commit()
        return name

    def get_building_by_geom(self, street_geom):
        """ 获得楼块信息
        :param street_geom: 待定区域的楼块信息
        :return:
        """
        sql = "with street as (" \
              " select st_geomfromtext('{}', '4326') as street_geom" \
              ")," \
              "tmp as ( " \
              "select struct_id " \
              "from bud_face, street " \
              "where st_intersects(street_geom, geom)  " \
              ")" \
              "select bud_face.struct_id, st_astext(st_buffer(st_union(geom)::geography, 0.5)) as geom_text " \
              "from tmp, bud_face " \
              "where bud_face.struct_id = tmp.struct_id group by bud_face.struct_id ".format(street_geom)

        back_cur = self.conn_back.cursor()
        back_cur.execute(sql)
        data = back_cur.fetchall()
        back_cur.close()
        self.conn_back.commit()
        return data

    def get_competitor_geom(self, area: str, batch: str = '') -> List:
        """  获得竞品数据
        :param area: 待检测区域
        :param batch 批次
        :return:
        """
        if batch == '':
            sql = "select bid, st_astext(geom) from aoi_intelligence where" \
                  " st_intersects(st_geomfromtext('{}', 4326), geom)".format(area)
        else:
            sql = "select bid, st_astext(geom) from aoi_intelligence where " \
                  "st_intersects(st_geomfromtext('{}', 4326), geom) and batch='{}'".format(area, batch)
        online_cur = self.conn_poi.cursor()
        online_cur.execute(sql)
        data = online_cur.fetchall()
        online_cur.close()
        self.conn_poi.commit()
        return data

    def get_street_geoms_by_geom(self, area: str, buffer=0) -> List:
        """
        查询街区对应的geom
        """
        if buffer > 0:
            sql = "select face_id,st_astext(st_buffer(geometry::geography, {})) as geom " \
                  "from aoi_ml_region where ST_Intersects(geometry, st_geomfromtext('{}', 4326))".format(buffer, area)
        else:
            sql = "select face_id,st_astext(geometry) as geom from aoi_ml_region where" \
                  " ST_Intersects(geometry, st_geomfromtext('{}', 4326))".format(area)
        online_cur = self.conn_poi.cursor()
        online_cur.execute(sql)
        res = online_cur.fetchall()
        online_cur.close()
        self.conn_poi.commit()
        return res

    def search_poi(self, shape_wkt, filter_online=True) -> List:
        """ 查询poi点
        :param shape_wkt:
        :param filter_online: 是否过滤在线
        :return:
        """
        res_list = []
        poi_list = self.get_poi_by_geom(shape_wkt)
        for poi_item in poi_list:
            bid = poi_item[0]
            mid = poi_item[1]
            name = poi_item[2]
            mesh_id = poi_item[3]
            std_tag = poi_item[4]
            click_pv = poi_item[5]
            city = poi_item[6]
            if filter_online:
                aoi_res = self.get_aoi_by_bid(bid)
            else:
                aoi_res = None
            if aoi_res is not None and len(aoi_res) > 0:
                """
                根据aoi查询poi，如果已经上线了，忽略
                """
                continue
            res_list.append(
                {
                    "bid": bid,
                    "mid": mid,
                    "name": name,
                    "mesh_id": mesh_id,
                    "std_tag": std_tag,
                    "click_pv": click_pv,
                    "city": city
                }
            )
        return res_list

    def query_overlay_aoi(self, geom):
        """
        :return:
        """
        sql = """select st_astext(st_union(geom)) 
        from blu_face where 
        st_intersects(st_geomfromtext('%s', 4326), geom) and src != 'SD' and kind != '52'""" % geom
        cursor = self.conn_back.cursor()
        cursor.execute(sql)
        res = cursor.fetchone()
        self.conn_back.commit()
        return res

    def get_mesh_id(self, geom, buffer=1):
        """
        查询图幅号
        :param geom:
        :param buffer:
        :return:
        """
        sql = "select get_2d5w_mesh_internal_with_polygon(st_buffer('{}'::geography, {})::geometry)" \
            .format(geom, buffer)
        with self.conn_back.cursor() as cursor:
            cursor.execute(sql)
            return cursor.fetchall()

    def get_inner_line_by_geom(self, geom):
        """ 获得相交的内部路
        :param geom:
        :return:
        """
        # sql = "select st_asText(geom) from nav_link " \
        #       "where ST_Intersects(geom,  st_geomfromtext('{}', '4326')) " \
        #       "and form like '%52%' ".format(geom)
        # self.road_cur.execute(sql)
        # geom_list = []
        # for geom in self.road_cur.fetchall():
        #     geom_list.append(geom[0])
        return self.get_line_by_geom(geom)

    def get_unknown_road_by_geom(self, geom):
        """ 获得未知道路
        :param geom:
        :return:
        """
        sql = "select st_asText(geom) from nav_link " \
              "where ST_Intersects(geom,  st_geomfromtext('{}', '4326')) " \
              "and kind = 8 and form not like '%52%' ".format(geom)
        cursor = self.conn_road.cursor()
        cursor.execute(sql)
        geom_list = []
        for geom in cursor.fetchall():
            geom_list.append(geom[0])
        cursor.close()
        self.conn_road.commit()
        return geom_list

    def check_pic_exists(self, obj_id: str, resource, batch_info: str = "google2021"):
        """
        # 检测是否存在, 如果存在
        :param obj_id: id， 唯一标识
        :param resource: 资源类型
        :param batch_info: 批次
        :return:
        """
        with self.conn_poi.cursor() as cursor:
            try:
                sql = f"select object_id from aoi_ml_region_pic where object_id='{obj_id}' " \
                      f"and resource='{resource}' and batch_info='{batch_info}' limit 1"
                cursor.execute(sql)
                ret = cursor.fetchone()
                self.conn_poi.commit()
                return ret is not None
            except Exception as e:
                self.conn_poi.rollback()
                return False

    def save_region_pic(self, obj_id: str, info: str, pic_info: str, resource: str, geom: str,
                        task_id: int = 0, batch_info: str = "google2021", task_type=''):
        """
        保存街区图片数据
        :param obj_id: 街区id
        :param info: 街区详情
        :param pic_info: 街区图片详情
        :param resource: 资源类型
        :param geom: geom
        :param task_id 任务id
        :param batch_info: 任务批次
        :param task_type: 任务类型
        :return:
        """
        with self.conn_poi.cursor() as cursor:
            try:
                sql = f"insert into aoi_ml_region_pic (object_id, info, pic_info, resource, geom, task_id, batch_info, task_type)" \
                      f" values('{obj_id}', '{info}', '{pic_info}', '{resource}'," \
                      f" st_geomfromtext('{geom}', 4326), {task_id}, '{batch_info}', '{task_type}');"
                cursor.execute(sql)
                self.conn_poi.commit()
            except Exception as e:
                self.conn_poi.rollback()
                raise e
