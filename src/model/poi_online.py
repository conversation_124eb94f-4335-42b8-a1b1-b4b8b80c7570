# -*- coding: utf-8 -*-
"""
poi 线上库
"""

from src.tools.pg_tool import PgTool


class PoiOnlineModel(PgTool):
    """
    poi线上库model
    """
    def __init__(self):
        """
        初始化连接数据库
        """
        PgTool.__init__(self)

    def insert_aoi_intelligence_history(self, value_arr):
        """ 写入 history信息
        :param value_arr:
        :return:
        """
        sql = """insert into aoi_intelligence_history
        (bid, intel_id, name, address, display_x, display_y, mesh_id, geom, batch, crawl_time) values
        (%s, %s, %s, %s, %s, %s, %s, st_geomfromtext(%s, 4326), %s, extract(epoch from DATE %s));"""
        cursor = self.conn_poi.cursor()
        return cursor.execute(sql, value_arr)

    def insert_aoi_intelligence(self, value_arr):
        """ 写入情报信息
        :param value_arr:
        :return:
        """
        sql = """insert into aoi_intelligence
        (bid, intel_id, name, address, display_x, display_y, mesh_id, geom, batch, intel_src, crawl_time) values
        (%s, %s, %s, %s, %s, %s, %s, st_geomfromtext(%s, 4326), %s, %s, extract(epoch from DATE %s));"""
        cursor = self.conn_poi.cursor()
        return cursor.execute(sql, value_arr)

    def swap_geom(self, geom_str):
        """ 交换坐标 x, y
        :param geom_str:
        :return:
        """
        sql = """select st_astext(ST_FlipCoordinates(st_geomfromtext(%s, 4326))) """
        cursor = self.conn_poi.cursor()
        cursor.execute(sql, [geom_str])
        return cursor.fetchone()

    def make_valid(self, geom_str):
        """ 移除自相交
        :param geom_str:
        :return:
        """
        sql = """select
st_isvalid(
    st_geomfromtext(%s, '4326')::geometry
) as isvalid"""
        cursor = self.conn_poi.cursor()
        cursor.execute(sql, [geom_str])
        is_valid = cursor.fetchone()
        print("is_valid, ", is_valid)
        if is_valid[0]:
            return geom_str

        sql = """select
st_astext(
    ST_CollectionExtract(
        ST_MakeValid(
           st_geomfromtext(%s, '4326')::geometry
        ), 3
    )
) """
        cursor = self.conn_poi.cursor()
        cursor.execute(sql, [geom_str])
        res = cursor.fetchone()
        return res[0]

    def get_overlay_area(self, geom_str, back_geom_str):
        """ 获取压盖面积
        :param geom_str:   情报geom
        :param back_geom_str: 母库geom
        :return:
        """
        sql = """select st_area(st_geomfromtext(%s, 4326)::geography),
        st_area(st_intersection(st_geomfromtext(%s, 4326), st_geomfromtext(%s, 4326))::geography)"""

        cursor = self.conn_poi.cursor()
        cursor.execute(sql, [geom_str, back_geom_str, geom_str])
        res_area = cursor.fetchone()
        return res_area

    def commit(self):
        """ 事物提交
        :return:
        """
        return self.conn_poi.commit()

    def rollback(self):
        """ 事物回滚
        :return:
        """
        return self.conn_poi.rollback()

    def close(self):
        """ 关闭链接
        :return:
        """
        return self.conn_poi.close()

    def get_intersects_street_by_geom(self, geom: str):
        """
        获取与geom相交的街区
        :return:
        """
        query_sql = f"""
            with tmp as (select st_geomfromtext('{geom}',4326) as wkt) 
            select mesh_id, st_astext(geom),face_id from street_region,tmp 
            where st_intersects(geom,tmp.wkt) 
                and st_isvalid(geom)='t' 
                and st_area(st_intersection(geom,tmp.wkt))/st_area(tmp.wkt) > 0.6
        """
        cursor = self.conn_poi.cursor()
        cursor.execute(query_sql)
        data_list = cursor.fetchall()
        self.conn_poi.commit()
        return data_list
