# !/usr/bin/env python3
"""
任务流相关逻辑
"""
import json
import os
import sys

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.tools.mysql_tool import MysqlTool
from src.tools import function as F
from src.const.work_flow import ConstWorkFlow


class AoiMlFlowModel(MysqlTool):
    def __init__(self):
        MysqlTool.__init__(self)
        self.log_client = F.get_logging_client("sql_error")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        F.close_log_client(self.log_client)
        MysqlTool.__exit__(self, exc_type, exc_val, exc_tb)

    """
    task相关
    """

    def create_flow_task(self, task_record_dict: dict) -> bool:
        """
        创建任务
        :return:
        """
        with self.connection.cursor() as cursor:
            self.connection.begin()
            sql, args = MysqlTool.create_insert_sql("aoi_ml_task_record", task_record_dict)
            try:
                cursor.execute(sql, args=args)
                if cursor.lastrowid > 0:
                    self.connection.commit()
                    return True
                else:
                    self.log_client.warning(f"插入失败，未生成id:{MysqlTool.print_sql(sql, args)}")
                    self.connection.rollback()
                    return False
            except Exception as e:
                self.log_client.warning(f"生成任务失败: {e.args}:{MysqlTool.print_sql(sql, args)}")
                self.connection.rollback()
                return False

    def get_task_by_id(self, task_id: int, fields="*"):
        """
        根据id获取任务信息
        :param task_id:
        :param fields:
        :return:
        """
        with self.connection.cursor() as cursor:
            sql, args = self.create_query_sql("aoi_ml_task_record", fields=fields, where={"task_id=": task_id})
            cursor.execute(sql, args=args)
            data = cursor.fetchone()
            return data

    def get_wait_create_first_job_task(self, limit=10, fields='*'):
        """
        获取需要创建第一个子任务的task
        :return:
        """
        with self.connection.cursor() as cursor:
            where = {"task_status=": ConstWorkFlow.FLOW_STATUS_WAIT,
                     "task_current_flow=": ConstWorkFlow.FLOW_SUBTASK_START}
            sql, args = self.create_query_sql("aoi_ml_task_record", fields=fields, where=where,
                                              other=f"order by task_id asc limit {limit}")
            cursor.execute(sql, args=args)
            data = cursor.fetchall()
            return data

    def update_task_by_id(self, task_id, data) -> bool:
        with self.connection.cursor() as cursor:
            self.connection.begin()
            sql, args = self.create_update_sql("aoi_ml_task_record", data, {"task_id=": task_id}, "limit 1")
            MysqlTool.print_sql(sql, args)
            cursor.execute(sql, args)
            self.connection.commit()
            return True

    """
     work_package_flow 相关
    """

    def get_flow_subtask(self, section_name, status, limit=10):
        """
        获取子任务
        wpf_id, task_id, section_name, status, wpf_flow_info, task_name, task_type, task_content
        :return:
        """
        with self.connection.cursor() as cursor:
            fields = "a.wpf_id,a.wpf_task_id, a.wpf_section_name, a.wpf_status," \
                     "a.wpf_flow_info, b.task_name,b.task_type, b.task_content"
            table = "aoi_ml_work_package_flow a inner join aoi_ml_task_record b on a.wpf_task_id = b.task_id"
            where = {
                "a.wpf_status=": status,
                "a.wpf_section_name=": section_name,
            }
            sql, args = MysqlTool.create_query_sql(table, fields, where,
                                                   f"order by b.task_priority desc, a.wpf_id asc limit {limit}")
            # self.print_sql(sql, args)
            cursor.execute(sql, args=args)
            data_list = cursor.fetchall()
            return data_list

    def create_subtask(self, data: dict) -> bool:
        with self.connection.cursor() as cursor:
            self.connection.begin()
            sql, args = self.create_insert_sql("aoi_ml_work_package_flow", data)
            cursor.execute(sql, args=args)
            if cursor.lastrowid > 0:
                print("last_id", cursor.lastrowid)
                self.connection.commit()
                return True
            else:
                self.log_client.warning(f"生成subtask失败: {self.print_sql(sql, args)}")
                self.connection.rollback()
                raise Exception(f"生成subtask失败: {self.print_sql(sql, args)}")

    def update_subtask_by_wpfid(self, wpfid, data):
        with self.connection.cursor() as cursor:
            sql, args = self.create_update_sql("aoi_ml_work_package_flow", data, {"wpf_id=": wpfid})
            # self.print_sql(sql, args)
            cursor.execute(sql, args)
            self.connection.commit()

    def set_subtask_fail(self, wpf_id, task_id, msg):
        """
        设置执行失败
        :param wpf_id:
        :param task_id:
        :param msg:  备注信息
        :return:
        """
        self.update_subtask_by_wpfid(wpf_id, {"wpf_status": ConstWorkFlow.FLOW_STATUS_FAIL,
                                              "wpf_fail_reason": str(wpf_id) + msg})
        self.update_task_by_id(task_id, {"task_status": ConstWorkFlow.FLOW_STATUS_FAIL,
                                         "task_error_desc": msg})

    def get_subtasks_by_task_id(self, task_id):
        """
        获取子任务
        :param task_id:
        :return:
        """
        with self.connection.cursor() as cursor:
            sql, args = self.create_query_sql(
                "aoi_ml_work_package_flow",
                "wpf_id, wpf_task_id, wpf_section_name,wpf_status, wpf_flow_info, wpf_memo_info",
                {"wpf_task_id=": task_id,
                 "wpf_status=": ConstWorkFlow.FLOW_STATUS_SUCCESS})
            cursor.execute(sql, args)
            data_list = cursor.fetchall()
            return data_list

    def set_subtask_success(self, wpf_id, task_id, msg: str):
        """
        设置执行成功
        :param wpf_id:
        :param task_id:
        :param msg: 备注信息
        :return:
        """
        self.update_subtask_by_wpfid(wpf_id, {
            "wpf_status": ConstWorkFlow.FLOW_STATUS_SUCCESS,
            "wpf_memo_info": msg
        })
        self.update_task_by_id(task_id, {"task_status": ConstWorkFlow.FLOW_STATUS_SUCCESS})

    def set_subtask_doing(self, wpf_id, task_id, work_path):
        """
        设置正在执行
        :param wpf_id:
        :param task_id:
        :param work_path: 工作目录
        :return:
        """
        # 设置为正在执行
        up_data = {
            "wpf_status": ConstWorkFlow.FLOW_STATUS_DOING,
            "wpf_work_mechine": F.get_host_name(),
            "wpf_work_path": work_path
        }
        self.update_subtask_by_wpfid(wpf_id, up_data)
        self.update_task_by_id(task_id, {"task_status": ConstWorkFlow.FLOW_STATUS_DOING})

    def create_next_subtask(self, wpf_id, task_id, next_task, params):
        """
        生成下一环节任务
        :param wpf_id:
        :param task_id:
        :param next_task:
        :param params:
        :return:
        """
        task_map = {"task_current_flow": next_task}
        if next_task != ConstWorkFlow.FLOW_SUBTASK_END:
            # 任务不是结束状态,需要下一步子任务,并设置为待处理状态
            task_map['task_status'] = ConstWorkFlow.FLOW_STATUS_WAIT
            self.create_subtask({
                "wpf_task_id": task_id,
                "wpf_father_id": wpf_id,
                "wpf_section_name": next_task,
                "wpf_status": ConstWorkFlow.FLOW_STATUS_WAIT,
                "wpf_flow_info": params
            })
        self.update_task_by_id(task_id, task_map)

    def get_wait_upload_img_task(self, limit=30):
        table = "aoi_ml_task_record a inner join aoi_ml_work_package_flow b on a.task_id = b.wpf_task_id"
        fields = "a.task_id,a.image_status,b.wpf_status,b.wpf_flow_info,b.wpf_last_modify_time"
        where = {
            "a.image_status=": ConstWorkFlow.IMAGE_STATUS_WAIT,
            "b.wpf_status=": ConstWorkFlow.FLOW_STATUS_SUCCESS,
            "b.wpf_section_name=": ConstWorkFlow.FLOW_SUBTASK_SEG_POST_JOB,
        }
        sql, args = self.create_query_sql(table, fields, where, f"limit {limit}")
        with self.connection.cursor() as cursor:
            cursor.execute(sql, args)
            lists = cursor.fetchall()
            self.connection.commit()
            return lists

    def set_task_data(self, task_id, key, value):
        table = "aoi_ml_task_data"
        data = {
            "task_id": task_id,
            "taskd_key": key,
            "taskd_content": value
        }
        dup = {
            "taskd_content": value
        }
        sql, args = self.create_duplicate_insert_sql(table, data, dup)
        with self.connection.cursor() as cursor:
            cursor.execute(sql, args)
            self.connection.commit()


if __name__ == '__main__':
    with AoiMlFlowModel() as a:
        # 构造一个任务
        # task_dict = {
        #     "task_name": ConstWorkFlow.TASK_TYPE_POST_AFTER_SEG_SCAN_LARGE_STREET,
        #     "task_type": ConstWorkFlow.TASK_TYPE_POST_AFTER_SEG_SCAN_LARGE_STREET,
        #     "task_content": json.dumps({
        #         "afs_url": "/user/map-data-streeview/aoi-ml/products/seg_scan_large/product_vec_seg_large_scan_guangzhoushi.tar",
        #         "filter_small": 0,
        #     }),
        #     "task_current_flow": ConstWorkFlow.FLOW_SUBTASK_START,
        #     "task_status": ConstWorkFlow.FLOW_STATUS_WAIT,
        # }
        # task_dict = {
        #     "task_name": ConstWorkFlow.TASK_TYPE_EXEC_AOI_SEG,
        #     "task_type": ConstWorkFlow.TASK_TYPE_EXEC_AOI_SEG,
        #     "task_content": json.dumps({
        #         "afs_url": "/user/map-data-streeview/aoi-ml/feeds/bad_review/bad_review_4.tar",
        #     }),
        #     "task_current_flow": ConstWorkFlow.FLOW_SUBTASK_START,
        #     "task_status": ConstWorkFlow.FLOW_STATUS_WAIT,
        # }
        task_dict = {
            "task_name": ConstWorkFlow.TASK_TYPE_POST_AFTER_SEG_SCAN,
            "task_type": ConstWorkFlow.TASK_TYPE_POST_AFTER_SEG_SCAN,
            "task_content": json.dumps({
                "afs_url": "/user/map-data-streeview/aoi-ml/products/bad_review/poi_gd_tx_missing_large.tar",
                "filter_online_aoi": "0"
            }),
            "task_current_flow": ConstWorkFlow.FLOW_SUBTASK_START,
            "task_status": ConstWorkFlow.FLOW_STATUS_WAIT,
        }
        a.create_flow_task(task_dict)
