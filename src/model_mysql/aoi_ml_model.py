# !/usr/bin/env python3
"""
mysql操作
"""
import json

from src.tools.mysql_tool import MysqlTool
import time


class AoiMlModel(MysqlTool):
    def __init__(self):
        MysqlTool.__init__(self)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        MysqlTool.__exit__(self, exc_type, exc_val, exc_tb)

    def get_store(self, key: str) -> str:
        """
        获取配置的数据
        :param key:
        :return:
        """
        with self.connection.cursor() as cursor:
            current = time.time()
            sql, args = MysqlTool.create_query_sql("aoi_ml_store",
                                                   ["k", "v", "expire_dateline"],
                                                   {"k=": f'{key}', "expire_dateline>": current},
                                                   "limit 1"
                                                   )
            cursor.execute(
                sql, args=args)
            data = cursor.fetchone()
            if data is None:
                return ""
            return data[1]

    def set_store(self, key: str, value: str, expire_second=3153600000, as_lock=False) -> bool:
        """
        设置配置的数据
        :param expire_second: 缓存过期时间 默认100年（相当于永不过期）
        :param key: key
        :param value: 值
        :param as_lock: 作为分布式锁设置,  不会自动更新
        :return:
        """
        with self.connection.cursor() as cursor:
            expire_time = int(time.time()) + expire_second
            if as_lock:
                sql, args = MysqlTool.create_insert_sql(
                    'aoi_ml_store',
                    {"k": key, "v": value, "expire_dateline": expire_time})
            else:
                sql, args = MysqlTool.create_duplicate_insert_sql(
                    'aoi_ml_store',
                    {"k": key, "v": value, "expire_dateline": expire_time},
                    {"v": value, "expire_dateline": expire_time})
            cursor.execute(sql, args=args)
            self.connection.commit()
            return True

    def set_lock(self, key: str, expire_second=3600):
        """
        设置锁， 使用mysql
        :param expire_second: 过期时间
        :param key: key
        :return:
        """
        current = time.time()
        with self.connection.cursor() as cursor:
            sql, args = self.create_del_sql("aoi_ml_store", {"expire_dateline<": current})
            cursor.execute(sql, args=args)
            self.connection.commit()
        try:
            val = str(current) + "|as_lock"
            return self.set_store(key, val, expire_second, as_lock=True), val
        except Exception as e:
            return False, ""


if __name__ == '__main__':
    with AoiMlModel() as a:
        print(a.set_lock("cc3"))
