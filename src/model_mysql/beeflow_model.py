# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
beeflow 查询
"""
import os
import sys
import time

import pymysql

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)
from src.tools.conf_tools import get_mysql_conf


class BFQuery(object):
    """
    BeeFlow MySQL 查询
    """

    def __init__(self):
        host, port, user, pwd, database = get_mysql_conf("beeflow_rw")
        self.host = host
        self.user = user
        self.passwd = pwd
        self.db = database
        self.port = port
        self.charset = "utf8"
        self.conn = None
        self._conn()

    def _conn(self):
        try:
            self.conn = pymysql.connect(host=self.host, user=self.user, password=self.passwd, db=self.db,
                                        port=self.port, charset=self.charset)
            return True
        except:
            return False

    def _reConn(self, retry_num=10, sleep_time=3):
        _number = 0
        _status = True
        while _status and _number <= retry_num:
            try:
                self.conn.ping()
                _status = False
            except:
                if self._conn():
                    _status = False
                    break
                _number += 1
                time.sleep(sleep_time)

    def queryall(self, sql=''):
        """
        查询多条数据
        :param sql:
        :return:
        """
        try:
            self._reConn()
            self.cursor = self.conn.cursor()
            self.cursor.execute(sql)
            self.cursor.close()
            result = self.cursor.fetchall()
            return result
        except Exception as e:
            print(e)
            return False

    def queryone(self, sql=''):
        """
        单条数据查询
        :param sql:
        :return:
        """
        try:
            self._reConn()
            self.cursor = self.conn.cursor()
            self.cursor.execute(sql)
            self.cursor.close()
            result = self.cursor.fetchone()
            return result
        except Exception as e:
            print(e)
            return False

    def execute(self, sql=''):
        """
        执行sql
        :param sql:
        :return:
        """
        try:
            self._reConn()
            self.cursor = self.conn.cursor()
            self.cursor.execute("set names utf8")
            result = self.cursor.execute(sql)
            self.conn.commit()
            self.cursor.close()
            return result
        except Exception as e:
            print(e)
            return False

    def execute_return_id(self, sql=''):
        """
        执行sql
        :param sql:
        :return:
        """
        try:
            self._reConn()
            self.cursor = self.conn.cursor()
            self.cursor.execute("set names utf8")
            self.cursor.execute(sql)
            result = self.cursor.lastrowid
            self.conn.commit()
            self.cursor.close()
            return result
        except Exception as e:
            print(e)
            return False

    def get_bud_pre_plan_by_status(self, status: str, limit_number=1):
        """
        获取建筑物预览计划信息
        :param status: 预览计划状态
        """
        sql = f"""
            select id, data_content, remark, outside_id, batch_no from basic_feature_pre_plan
            where plan_type = 47 and status='{status}' 
            order by updated_at 
        """
        if limit_number >= 0:
            sql = f"{sql} limit {limit_number}"
        return self.queryall(sql)

    def get_basic_pre_plan_by_status(self, status):
        """
        获取基础预览计划信息
        :param status:
        :return:
        """
        sql = f"""
            select id,plan_name from basic_feature_pre_plan where status = '{status}' order by id limit 1
        """
        return self.queryone(sql)

    def get_basic_pre_plan_by_id(self, plan_id):
        """
        获取基础预览计划信息
        :param plan_id:
        :return:
        """
        sql = f"""
            select id,plan_name, result_url, task_data from basic_feature_pre_plan where id = {plan_id} 
        """
        return self.queryone(sql)

    def update_pre_plan_status_and_result_url_by_id(self, id, status, result_url, remark=''):
        """
        更新基础预览计划状态
        :param id:
        :param status:
        :param result_url:
        :return:
        """
        sql = f"""
            update basic_feature_pre_plan set status = '{status}', result_url='{result_url}', task_data='{remark}' where id = {id}
        """
        return self.execute(sql)

    def update_pre_plan_status_and_remark(self, id, status, remark=''):
        """
        更新基础预览计划状态
        :param id:
        :param status:
        :param remark:
        :return:
        """
        sql = f"""
            update basic_feature_pre_plan set status = '{status}', remark='{remark}' where id = {id}
        """
        return self.execute(sql)
