"""
复杂停车场的装饰线生成
技术文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/1FWaLzu6_F/7vD2ejOe54ODTG
"""
from collections import defaultdict
from dataclasses import dataclass, field

import shapely.ops
from mapio.utils import coord
from shapely import LineString, Point, Polygon, wkt

from src.aikit import algo
from src.parking.beautify import model
from src.parking.beautify.model import GraphNode
from src.parking.recognition import dbutils
from src.parking.storefront import verify
from src.parking.storefront.flow.model import road
from src.parking.storefront.post_process import central_line
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.tools import linq, turnoff_point, pgsql


@dataclass
class Feed:
    """
    代表一个装饰线候选线（未经过熄火点投影）
    """

    is_inner: bool
    geom: LineString
    resolved: bool = field(default=False)

    def __post_init__(self):
        self.geom = self.geom.simplify(0.1 * METER)


@dataclass
class Graph:
    """
    代表一个内部路组
    """

    nodes: list[GraphNode]
    feeds: list[Feed] = field(default_factory=list)
    lines: list[LineString] = field(default_factory=list)


@dataclass
class Context:
    """
    上下文
    """

    data: model.BasicInfo
    scene: str
    polygon: Polygon
    link_graphs: list[Graph] = field(default_factory=list)
    turnoff_pts: list[turnoff_point.TurnoffPoint] = field(default_factory=list)
    bud_faces: list[Polygon] = field(default_factory=list)
    debug_data: dict[str, list] = field(default_factory=lambda: defaultdict(list))
    error: str = field(default="")


def filter_by_smart_space_parking_lots(ctx: Context, proceed):
    """
    如果已经做过智能空间的停车场，就不需要推装饰线了
    """
    if model.has_smart_space_parking_lot(ctx.polygon.wkt):
        ctx.error = "line.empty.has_smart_space_parking_lot"
        return

    proceed()


def create_link_graphs(ctx: Context, proceed):
    """
    创建内部道路 Graph
    """
    lines = [x.geom for x in ctx.data.inner_links]
    ctx.link_graphs = [Graph(nodes=nodes) for nodes in model.create_graph_by_links(lines)]
    proceed()


def filter_data_by_contains_poi(ctx: Context, proceed, buffer: float):
    """
    性能优化：以是否包含停车场 POI 为依据，过滤掉距离过远的无用数据，以提高处理性能
    """
    link_groups = []
    turnoff_points = []
    bud_faces = geometric.flat_polygon(ctx.data.bud_face)
    faces = []
    for g in ctx.link_graphs:
        lines = [x.geom for x in g.nodes]
        lines = shapely.unary_union(lines)
        polygon = lines.buffer(buffer)
        polygon = Polygon(polygon.exterior)
        if polygon.contains(ctx.data.parking.poi_geom):
            link_groups.append(g)
            pts = [x for x in ctx.data.turnoff_pts if polygon.contains(x.point)]
            turnoff_points.extend(pts)
            buds = [x for x in bud_faces if polygon.intersects(x)]
            faces.extend(buds)

    ctx.link_graphs = link_groups
    ctx.turnoff_pts = turnoff_points
    ctx.bud_faces = faces
    proceed()


def generate_feeds(ctx: Context, proceed, min_tail_length: float, buffer: float):
    """
    筛选需要处理的
    """
    for g in ctx.link_graphs:
        nodes = [x for x in g.nodes if not _is_invalid_tail_node(x, min_tail_length)]
        lines = [x.geom for x in nodes]
        merged_lines = shapely.ops.linemerge(lines)
        buffer_lines = merged_lines.buffer(buffer)
        for polygon in geometric.flat_polygon(buffer_lines):
            g.feeds.append(Feed(is_inner=False, geom=LineString(polygon.exterior.coords)))
            g.feeds.extend(Feed(is_inner=True, geom=LineString(x.coords)) for x in polygon.interiors)

    proceed()


def resolve_slim_polygon(ctx: Context, proceed, threshold: float):
    """
    将 buffer 后细长的内部空洞处理为中心线，以供后续处理
    """

    def try_resolve(polygon: Polygon):
        # noinspection PyBroadException
        try:
            return central_line.get_central_line_irregular(polygon)
        except:
            return None

    for g in ctx.link_graphs:
        todo_feeds = [x for x in g.feeds if x.is_inner]

        todo_items = [(Polygon(x.geom.coords), x) for x in todo_feeds]
        todo_items = [(polygon, feed) for polygon, feed in todo_items if is_slim_polygon(polygon, threshold)]

        done_items = [(try_resolve(polygon), feed) for polygon, feed in todo_items]
        done_items = [(line, feed) for line, feed in done_items if line is not None]
        for line, feed in done_items:
            feed.geom = line

    proceed()


def make_decorative_line(ctx: Context, proceed, length_per_point: float, max_distance: float, nearby_distance: float):
    """
    通过投影的方式生成装饰线
    """
    if ctx.scene == model.SCENE_CENTRALIZED_HIGH:
        return proceed()

    def merge_nearby_ranges(ranges: list[(float, float)]):
        if len(ranges) < 2:
            return ranges

        ranges = sorted(ranges, key=lambda x: x[0])
        current_a, current_b = ranges[0]
        result = []
        for next_a, next_b in ranges[1:]:
            if next_a - current_b < nearby_distance:
                current_b = next_b
            else:
                result.append((current_a, current_b))
                current_a, current_b = next_a, next_b

        result.append((current_a, current_b))
        return result

    bud_face = shapely.unary_union(ctx.bud_faces)

    def project(ln: LineString, pts: list[Point]) -> list[LineString]:
        pts = [p for p in pts if geometric.get_foot_point(p, ln) is not None]
        dists = [(ln.project(p), p) for p in pts]
        dists = [(d, p) for d, p in dists if ln.distance(p) <= max_distance]
        dists = [(d, LineString([ln.interpolate(d), p])) for d, p in dists]
        dists = [(d, ln) for d, ln in dists if not ln.intersects(bud_face)]

        ctx.debug_data["project"].extend(ln for _, ln in dists)

        dists = [d for d, _ in dists]
        half_length = length_per_point / 2
        ranges = [(max(d - half_length, 0), min(d + half_length, ln.length)) for d in dists]
        overlap_ranges = algo.calc_overlap_ranges(ranges)
        overlap_ranges = [r for r, n in overlap_ranges]
        overlap_ranges = merge_nearby_ranges(overlap_ranges)
        project_lines = [shapely.ops.substring(ln, a, b) for a, b in overlap_ranges]
        project_lines = [ln for ln in project_lines if ln.length > 0]

        if len(project_lines) > 1:
            project_lines = shapely.ops.linemerge(project_lines)
            project_lines = geometric.flat_line(project_lines)

        return project_lines

    for g in ctx.link_graphs:
        unresolved_lines = [x.geom for x in g.feeds]
        ctx.debug_data["lines"].extend(unresolved_lines)
        tree = shapely.STRtree(unresolved_lines)
        point_line_pairs = [(tree.nearest(p.point), p.point) for p in ctx.turnoff_pts]
        point_line_pairs = [(i, p) for i, p in point_line_pairs if i is not None]
        line_groups = linq.group_by(point_line_pairs, key=lambda x: x[0], value=lambda x: x[1])

        projected_lines = [line for k, v in line_groups.items() for line in project(tree.geometries.take(k), v)]
        g.lines.extend(projected_lines)

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.make_decorative_line"
        return

    proceed()


def make_decorative_line_without_turnoff_point(ctx: Context, proceed):
    """
    直接使用内部路 buffer 的结果作为装饰线，不使用熄火点投影，专用于人工核实过的（高置信）集中可停场景
    """
    if ctx.scene != model.SCENE_CENTRALIZED_HIGH:
        return proceed()

    for g in ctx.link_graphs:
        lines = [ln for g in g.feeds for ln in geometric.flat_line(g.geom)]
        g.lines.extend(lines)

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.make_decorative_line_without_turnoff_point"
        return

    proceed()


def clip_by_tail_line(ctx: Context, proceed, buffer: float, min_length: float):
    """
    用长的断头路裁切圆末端
    """
    eps = 0.1 * METER
    for g in ctx.link_graphs:
        nodes = [x for x in g.nodes if x.is_tail_line() and x.geom.length >= min_length]
        for node in nodes:
            buffer_geom = node.geom.buffer(buffer - eps, cap_style="square")
            g.lines = [x for ln in g.lines for x in geometric.flat_line(ln.difference(buffer_geom))]

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.clip_by_tail_line"
        return

    proceed()


def clip_by_bud_face(ctx: Context, proceed, buffer: float):
    """
    不能压盖建筑
    """
    buffer_geom = ctx.data.bud_face.buffer(buffer)
    for g in ctx.link_graphs:
        lines = [x for ln in g.lines for x in geometric.flat_line(ln.difference(buffer_geom))]
        g.lines = [ln for ln in lines if not ln.is_empty and ln.length > 0]

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.clip_by_bud_face"
        return

    proceed()


def clip_by_blu_face(ctx: Context, proceed, buffer: float):
    """
    不能在 AOI 范围之外
    """
    polygon = ctx.data.blu_face.geom.buffer(buffer)
    for g in ctx.link_graphs:
        lines = [x for ln in g.lines for x in geometric.flat_line(ln.intersection(polygon))]
        g.lines = [ln for ln in lines if not ln.is_empty and ln.length > 0]

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.clip_by_blu_face"
        return

    proceed()


def clip_by_park_face(ctx: Context, proceed, buffer: float):
    """
    不能在 停车场面 范围之外
    """
    polygon = ctx.polygon.buffer(buffer)
    for g in ctx.link_graphs:
        lines = [x for ln in g.lines for x in geometric.flat_line(ln.intersection(polygon))]
        g.lines = [ln for ln in lines if not ln.is_empty and ln.length > 0]

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.clip_by_park_face"
        return

    proceed()


def clip_by_sd_line(ctx: Context, proceed, buffer: float):
    """
    不压盖道路
    """
    nav_links = model.get_nav_link(ctx.polygon.wkt)
    nav_geoms = [wkt.loads(x) for x in nav_links]
    union_nav_geom = shapely.unary_union(nav_geoms)
    buffer_nav_geom = union_nav_geom.buffer(buffer)
    for g in ctx.link_graphs:
        lines = [x.difference(buffer_nav_geom) for x in g.lines]
        g.lines = [ln for ln in lines if not ln.is_empty and ln.length > 0]

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.clip_by_sd_line"
        return

    proceed()


def clip_by_access_model(ctx: Context, proceed, search_buffer: float, clip_buffer: float):
    """
    根据出入口模型裁剪装饰线。
    """
    buffer_polygon = ctx.polygon.buffer(search_buffer)
    pts = _get_access_model_points(buffer_polygon)
    if not pts:
        return proceed()

    circles = [p.buffer(clip_buffer) for p in pts]
    circle_union = shapely.unary_union(circles)
    for g in ctx.link_graphs:
        lines = [x for ln in g.lines for x in geometric.flat_line(ln.difference(circle_union))]
        g.lines = [ln for ln in lines if not ln.is_empty and ln.length > 0]

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.clip_by_access_model"
        return

    proceed()


def clip_by_ld_face(ctx: Context, proceed, buffer: float):
    """
    过滤掉与 LD 压盖的装饰线
    """
    lines = [x for g in ctx.link_graphs for ln in g.lines for x in geometric.flat_line(ln)]
    union_lines = shapely.unary_union(lines)

    search_buffer = 50 * METER
    search_geom = union_lines.buffer(search_buffer)
    ld_2d_geom = road.get_full_ld_polygon(search_geom.wkt)
    ld_2d_geom = wkt.loads(ld_2d_geom)
    if ld_2d_geom.is_empty:
        return proceed()

    ld_2d_geom = ld_2d_geom.buffer(buffer)
    for g in ctx.link_graphs:
        lines = [x for ln in g.lines for x in geometric.flat_line(ln)]
        lines = [x for ln in lines for x in geometric.flat_line(ln.difference(ld_2d_geom))]
        g.lines = [ln for ln in lines if not ln.is_empty and ln.length > 0]

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.clip_by_ld_face"
        return

    proceed()


def filter_by_length(ctx: Context, proceed, min_length: float):
    """
    筛掉过短的装饰线
    """
    for g in ctx.link_graphs:
        lines = [x for ln in g.lines for x in geometric.flat_line(ln)]
        g.lines = [ln for ln in lines if ln.length > min_length]

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.filter_by_length"
        return

    proceed()


def filter_by_turning_point(ctx: Context, proceed, max_angle: float, min_length: float):
    """
    根据较大的累积拐弯角度打断折线段为直线段集合，并去除短的直线段
    """

    def resolve(ln: LineString):
        turning_points = list(verify.get_turning_points(ln, max_angle=max_angle))
        if not turning_points:
            return [ln]

        dists = sorted(ln.project(Point(p)) for p in turning_points)
        dists = [0, *dists, ln.length]
        valid_segments = [(a, b) for a, b in zip(dists[:-1], dists[1:]) if b - a > min_length]
        valid_lines = [shapely.ops.substring(ln, a, b) for a, b in valid_segments]
        merge_line = shapely.ops.linemerge(valid_lines)
        return geometric.flat_line(merge_line)

    for g in ctx.link_graphs:
        lines = [x for ln in g.lines for x in geometric.flat_line(ln)]
        lines = [x for ln in lines for x in resolve(ln)]
        g.lines = lines

    if not any(ln for g in ctx.link_graphs for ln in g.lines):
        ctx.error = "line.empty.filter_by_turning_point"
        return

    proceed()


def filter_by_few_lines(ctx: Context, proceed, min_area: float, min_count: int, min_length: float):
    """
    对于又少又短的场景，清空所有装饰线
    """
    # 太小的面应该尽量保留装饰线，不应用此策略
    if ctx.polygon.area < min_area:
        return proceed()

    lines = [x for g in ctx.link_graphs for ln in g.lines for x in geometric.flat_line(ln)]
    can_empty = len(lines) <= min_count and all(ln.length < min_length for ln in lines)
    if can_empty:
        for g in ctx.link_graphs:
            g.lines = []

    if can_empty:
        ctx.error = "line.empty.filter_by_few_lines"
        return

    proceed()


# helpers:


def is_slim_polygon(geom: Polygon, threshold: float):
    """
    判断是否是细长的多边形
    """
    # noinspection PyBroadException
    try:
        line = central_line.get_central_line_irregular(geom)
        if line is None:
            return False

        f = (2 * line.length) / geom.length
        return f > threshold
    except:
        return False


def _is_invalid_tail_node(node: GraphNode, min_length: float):
    return node.is_tail_line() and node.geom.length < min_length


def _get_access_model_points(geom: Polygon) -> list[Point]:
    sql = """
        select st_astext(gate_geom) from parking
        where 1 = 1
            and status = 1
            and std_tag = '出入口;停车场出入口'
            and show_tag = '停车场入口'
            and st_contains(%s, gate_geom)
    """
    polygon_pts_gcj02 = [(x, y) for x, y in geom.exterior.coords]
    polygon_pts_bd09 = [coord.gcj02_to_bd09(x, y) for x, y in polygon_pts_gcj02]
    polygon_pts_mc = [coord.bd09_to_mercator(x, y) for x, y in polygon_pts_bd09]
    polygon_mc = Polygon(polygon_pts_mc)

    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [f"SRID=3857;{polygon_mc.wkt}"])
    pts = [wkt.loads(x[0]) for x in ret]
    pts = [coord.mercator_to_gcj02(p.x, p.y) for p in pts]
    pts = [Point(x, y) for x, y in pts]
    return pts
