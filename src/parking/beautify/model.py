"""
停车场面及装饰线美化模块共用的数据模型
"""
from collections import defaultdict
from dataclasses import dataclass, field
from typing import Iterable

import shapely
import shapely.ops
from shapely import Point, Polygon, LineString, wkt
from shapely.geometry.base import BaseGeometry

from src.parking.recognition import dbutils
from src.parking.storefront.utils import geometric
from src.parking_production import query
from src.tools import pgsql, utils, turnoff_point

AREA_TYPES = ["smart_area", "show_area", "area"]

SCENE_SCATTER = "scatter"
SCENE_CENTRALIZED = "centralized"
SCENE_CENTRALIZED_HIGH = "centralized.high"
SCENE_UNLIMITED = "unlimited"
SCENE_UNKNOWN = "unknown"
SCENE_MANUAL_CHECK = "manual.check"


@dataclass
class Parking:
    """
    停车场信息
    """

    bid: str
    parent_bid: str
    root_bid: str
    name: str
    city: str
    poi_geom: Point
    geom_type: str
    geom: Polygon


@dataclass
class BluFace:
    """
    AOI信息
    """

    bid: str
    face_id: str
    aoi_level: int
    src: str
    std_tag: str
    geom: Polygon


@dataclass
class Link:
    """
    道路信息
    """

    link_id: str
    s_nid: str
    e_nid: str
    geom: LineString


@dataclass
class GraphNode:
    """
    图节点：构建内部路网 Graph 的节点
    """

    geom: LineString
    uuid: str = field(init=False)
    point1: str = field(init=False)
    point2: str = field(init=False)
    adjacent1_nodes: list["GraphNode"] = field(default_factory=list)
    adjacent2_nodes: list["GraphNode"] = field(default_factory=list)

    def __post_init__(self):
        def get_end_point_key(ln: LineString):
            s_pt, e_pt = ln.coords[0], ln.coords[-1]
            s_x, s_y = s_pt
            e_x, e_y = e_pt
            return f"POINT({s_x} {s_y})", f"POINT({e_x} {e_y})"

        self.uuid = utils.md5(self.geom.wkt)
        self.point1, self.point2 = get_end_point_key(self.geom)

    def __hash__(self):
        return hash(self.uuid)

    def __repr__(self):
        return f"Node ({self.uuid}, {len(self.adjacent1_nodes)}, {len(self.adjacent2_nodes)})"

    def is_tail_line(self):
        """
        判断是否为断头路
        """
        return len(self.adjacent1_nodes) == 0 or len(self.adjacent2_nodes) == 0

    def get_graph(self) -> list["GraphNode"]:
        """
        获取与该节点相连的所有节点
        """

        def search_node(seen_nodes: set[GraphNode], node: GraphNode):
            if node in seen_nodes:
                return

            seen_nodes.add(node)
            for adjacent_node in node.adjacent1_nodes:
                search_node(seen_nodes, adjacent_node)

            for adjacent_node in node.adjacent2_nodes:
                search_node(seen_nodes, adjacent_node)

        result = set()
        search_node(result, self)
        return list(result)


@dataclass
class BasicInfo:
    """
    基础信息
    """

    parking: Parking = field(default=None)
    blu_face: BluFace = field(default=None)
    bud_face: BaseGeometry = field(default=None)
    inner_links: list[Link] = field(default_factory=list)
    turnoff_pts: list[turnoff_point.TurnoffPoint] = field(default_factory=list)


def get_parking_bids(count: int = 0) -> list[str]:
    """
    获取所有停车场信息
    """
    if count > 0:
        sql = """
            select bid from park_important_list
            where show_tag = '地上停车场'
            limit %s
        """
        ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [count])
    else:
        sql = """
            select bid from park_important_list
            where show_tag = '地上停车场'
        """
        ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)

    bids = [x[0] for x in ret]
    return bids


def get_parkings_by_bids(bids: Iterable[str]):
    """
    获取指定 bids 的停车场信息
    """

    def first_not_none(*items: str):
        for i, it in enumerate(items):
            if it is not None:
                return AREA_TYPES[i], it

        return "empty", ""

    def get_gcj_geom(geom: str):
        return wkt.loads(query.mc2gcj(geom)) if geom else None

    def create_parking(row):
        bid, p_bid, r_bid, name, city, geom1, geom2, geom3 = row
        geom_type, geom = first_not_none(geom1, geom2, geom3)

        return Parking(
            bid=bid,
            parent_bid=p_bid,
            root_bid=r_bid,
            name=name,
            city=city,
            poi_geom=wkt.loads(get_position_by_bid(bid)),
            geom_type=geom_type,
            geom=get_gcj_geom(geom) if geom else geom,
        )

    sql = """
        select 
            a.bid, b.parent_id, a.root_bid, b.name, b.city_name, 
            st_astext(b.smart_area), st_astext(b.show_area), st_astext(b.area)
        from park_statistical_data a
        join park_online_data b on a.bid = b.bid
        where a.show_tag = '地上停车场' and a.bid in %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(bids)])
    return (create_parking(row) for row in ret)


def get_blu_face_by_bid(bid: str):
    """
    获取指定 bid 的 AOI 信息
    """
    sql = """
        select b.poi_bid, a.face_id, aoi_level, src, st_astext(geom)
        from blu_face a
        join blu_face_poi b on a.face_id = b.face_id
        where b.poi_bid = %s
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, [bid])
    if ret:
        p_bid, face_id, aoi_level, src, geom = ret
        return BluFace(
            bid=p_bid,
            face_id=face_id,
            aoi_level=aoi_level,
            src=src,
            std_tag=get_std_tag_by_bid(bid),
            geom=wkt.loads(geom),
        )
    else:
        return None


def get_blu_face_by_wkt(geom: str):
    """
    通过 wkt 查询 AOI 信息
    """
    sql = """
        select b.poi_bid, a.face_id, aoi_level, src, st_astext(geom)
        from blu_face a
        join blu_face_poi b on a.face_id = b.face_id
        where st_intersects(geom, %s)
            and aoi_level = 2
            and src != 'SD'
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [f"SRID=4326;{geom}"])
    return [
        BluFace(
            bid=p_bid,
            face_id=face_id,
            aoi_level=aoi_level,
            src=src,
            std_tag=get_std_tag_by_bid(p_bid),
            geom=wkt.loads(geom),
        )
        for p_bid, face_id, aoi_level, src, geom in ret
    ]


def get_std_tag_by_bid(bid: str):
    """
    获取指定 bid 的 std_tag
    """
    sql = """
        select std_tag from poi
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [bid])
    return ret[0] if ret else ""


def get_position_by_bid(bid: str):
    """
    获取指定 bid 的 POI 坐标
    """
    sql = """
        select st_astext(geometry)
        from poi
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [bid])
    return ret[0] if ret else None


def get_inner_road_by_wkt(geom: str):
    """
    通过 wkt 查询内部道路信息
    """
    sql = """
        select link_id, s_nid, e_nid, st_astext(geom) 
        from nav_link
        where st_intersects(geom, %s)
            and (form ~ '52' or kind = 8)
            -- and form !~ '53'  -- 停车场出入口连接路 (53)，有些连接路很长，占据了整个停车场，可能是 form 不准，不能过滤
            and kind != 10  -- 步行道路 (10)
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{geom}"])
    if ret:
        invalid_link_ids = get_indoor_link_by_id([x[0] for x in ret])
        return [
            Link(link_id=link_id, s_nid=s_nid, e_nid=e_nid, geom=wkt.loads(geom))
            for link_id, s_nid, e_nid, geom in ret
            if link_id not in invalid_link_ids
        ]
    else:
        return []


def get_nav_link(geom: str):
    """
    通过 wkt 查询所有的道路 LineString
    """
    sql = """
        select st_astext(geom) 
        from nav_link
        where st_intersects(geom, %s)
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{geom}"])
    return [x[0] for x in ret]


def get_indoor_link_by_id(link_id: Iterable[str]):
    """
    获取指定 id 的室内道路信息
    """
    sql = """
        select link_id from nav_link_scene
        where link_id in %s and theme = 4  -- 室内停车场道路
    """
    scenes = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [tuple(link_id)])
    return {x[0] for x in scenes}


def has_smart_space_parking_lot(geom: str):
    """
    判断给定的几何体是否与智能空间停车位相交
    """
    sql = """
        select count(*)
        from indoor_subregion 
        where st_intersects(%s, geom)
            and catalog = '130604'
            and floor = 'F1'
    """
    ret = dbutils.fetch_one(pgsql.INDOOR, sql, [f"SRID=4326;{geom}"])
    return ret[0] > 0


def get_bud_face_by_wkt(geom: str):
    """
    通过 wkt 查询建筑物信息
    """
    sql = """
        select st_astext(st_buffer(st_unaryunion(st_buffer(st_collect(geom), 1 * 1e-5)), -1 * 1e-5))
        from bud_face
        where st_intersects(%s, geom)
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [f"SRID=4326;{geom}"])
    return shapely.unary_union([wkt.loads(x[0]) for x in ret])


def create_graph_by_links(lines: Iterable[LineString]) -> list[list[GraphNode]]:
    """
    根据道路线段创建图形结构，返回一个列表，其中每个元素都是一个节点的列表，代表一个独立的连通图内的所有节点
    """
    union_links = shapely.ops.linemerge(lines)
    merged_links = geometric.flat_line(union_links)
    nodes = [GraphNode(geom=ln) for ln in merged_links]

    point_map = defaultdict(list)
    for node in nodes:
        point_map[node.point1].append(node)
        point_map[node.point2].append(node)

    for node in nodes:
        adjacent1_nodes = [x for x in point_map[node.point1] if x.uuid != node.uuid]
        adjacent2_nodes = [x for x in point_map[node.point2] if x.uuid != node.uuid]
        node.adjacent1_nodes.extend(adjacent1_nodes)
        node.adjacent2_nodes.extend(adjacent2_nodes)

    graphs = []
    node_set = set(nodes)
    while len(node_set) > 0:
        first_node = node_set.pop()
        nodes = first_node.get_graph()
        graphs.append(nodes)
        node_set = node_set - set(nodes)

    return graphs
