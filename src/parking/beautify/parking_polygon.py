"""
1. 分散/集中判定标准：
    a. AOI 内熄火点求凸包后，若包含建筑（以压盖建筑的面积占比为准，如：大于 50%），则按“分散可停”处理，否则：
    b. 对于（建筑面积 / AOI 面积）比值较大（如：大于 40%-50%），或 AOI 面积特别小（阈值待定），按“分散可停”处理，否则：
    c. 按“集中可停”处理。
2. “集中可停”面生成规则：
    a. 有 AOI 制作的面，则直接使用；
    b. 当前只处理内部路组“成环”的场景，即：对内部路组构建 Graph 结构，剪掉所有包含连通度为 1 节点的枝桠，对剩余的封闭环进行 buffer 生成面；
    c. 输出不可解场景的占比。

面生成补充：
1. 线上已经有面的（smart_area，show_area，area）：基于原有面压盖的内部路进行规整化处理。
2. 线上缺面的：使用熄火点聚类面进行内部路压盖，这里要处理的场景可能比较多，要调试一下。
    a. 熄火点比例太低不处理；
    b. 主要考虑熄火点不足的情况，比如：某 link 没有被压盖，但其两端的相邻 link 被压盖，则也要入选，否则面会出现塌陷的情况，之前有遇到过。
"""
from dataclasses import dataclass, field

import numpy as np
import shapely
from shapely import Polygon, wkt, LineString, Point, MultiPoint
from shapely.affinity import rotate, translate

from src.parking.beautify import model
from src.parking.beautify.model import GraphNode
from src.parking.recognition import dbutils
from src.parking.storefront import cluster_projection
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.seg_post_process import smooth_point_string
from src.tools import turnoff_point, pgsql


@dataclass
class Context:
    """
    上下文
    """

    bid: str
    data: model.BasicInfo
    scene: str
    turnoff_pts_clusters: list[list[turnoff_point.TurnoffPoint]]
    polygon: Polygon = field(default=None)
    polygon_source: str = field(default="unknown")
    error: str = field(default="")


def use_blu_face(ctx: Context, proceed):
    """
    pipe: 若已有 AOI 面，则直接使用
    """
    if ctx.polygon is not None:
        return proceed()

    sql = """
        select st_astext(geom), src
        from blu_face a
        inner join blu_face_poi b on a.face_id = b.face_id
        where b.poi_bid = %s
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, [ctx.bid])
    if ret:
        geom, src = ret
        ctx.polygon = wkt.loads(geom)
        ctx.polygon_source = f"blu_face.{src}"

    proceed()


def use_parent_blu_face(ctx: Context, proceed):
    """
    pipe: 对于分散可停场景，使用其父点 AOI 面
    """
    if ctx.scene != model.SCENE_SCATTER or ctx.polygon is not None:
        return proceed()

    blu_face = ctx.data.blu_face
    if blu_face.aoi_level == 1:
        ctx.error = "polygon.blu_face-aoi_level-is-1"
    else:
        ctx.polygon = ctx.data.blu_face.geom
        ctx.polygon_source = "blu_face.parent"

    proceed()


def use_ring_inner_link(ctx: Context, proceed, concave_ratio: float, buffer_link: float, buffer_gap: float):
    """
    pipe: 对于集中可停场景，使用成环的内部路进行 buffer
    """
    if ctx.scene != model.SCENE_CENTRALIZED or ctx.polygon is not None:
        return proceed()

    if len(ctx.turnoff_pts_clusters) > 1:
        ctx.error = "polygon.more-than-one-turnoff_point-cluster"
        return proceed()

    if len(ctx.turnoff_pts_clusters) == 0:
        ctx.error = "polygon.use_ring_inner_link.no-turnoff_point-cluster"
        return proceed()

    turnoff_pts = ctx.turnoff_pts_clusters[0]
    lines = [x.geom for x in ctx.data.inner_links]
    if len(lines) == 0:
        ctx.error = "polygon.no-inner_link"
        return proceed()

    cluster = shapely.unary_union([x.point for x in turnoff_pts])
    concave_hull = shapely.concave_hull(cluster, concave_ratio)
    link_graphs = [nodes for nodes in model.create_graph_by_links(lines)]
    node_groups = [nodes for nodes in link_graphs if any(node.geom.intersects(concave_hull) for node in nodes)]
    if len(node_groups) == 0:
        ctx.error = "polygon.no-ring-inner_link-intersects-turnoff_point"
        return proceed()

    if len(node_groups) > 1:
        node_items = [(items, shapely.unary_union([x.geom for x in items])) for items in node_groups]
        node_items = [(a, b.buffer(10 * METER)) for a, b in node_items]
        node_items = [(a, Polygon(b.exterior)) for a, b in node_items]
        node_groups = [a for a, b in node_items if b.contains(ctx.data.parking.poi_geom)]

    if len(node_groups) > 1:
        ctx.error = "polygon.more-than-one-ring-inner_link-intersects-turnoff_point"
        return proceed()

    if len(node_groups) != 1:
        ctx.error = "polygon.node-group-length-is-not-1"
        return proceed()

    nodes = node_groups[0]
    intersected_nodes = [node for node in nodes if node.geom.intersects(concave_hull)]
    no_intersected_nodes = [node for node in nodes if not node.geom.intersects(concave_hull)]
    valid_nodes = [
        node
        for node in no_intersected_nodes
        if any(x in intersected_nodes for x in node.adjacent1_nodes)
        and any(x in intersected_nodes for x in node.adjacent2_nodes)
    ]
    valid_nodes = valid_nodes + intersected_nodes
    clear_graph(valid_nodes)
    trim_graph(valid_nodes)
    union_links = shapely.unary_union([node.geom for node in valid_nodes])
    union_geom = union_links.buffer(buffer_link)

    polygon = Polygon(union_geom.exterior)
    polygon = polygon.intersection(ctx.data.blu_face.geom.buffer(-buffer_gap))
    if not polygon.is_empty:
        ctx.polygon = polygon
        ctx.polygon_source = "strategy.ring_inner_link"

    proceed()


def use_obb(ctx: Context, proceed, eps: float, min_samples: int, concave_ratio=0.2):
    """
    pipe: 对于集中可停场景，使用 OBB 算法生成面
    """
    if ctx.scene != model.SCENE_CENTRALIZED or ctx.polygon is not None:
        return proceed()

    # 准备熄火点：
    if ctx.turnoff_pts_clusters:
        turnoff_pts_clusters = ctx.turnoff_pts_clusters
    else:
        clusters = cluster_projection.cluster_by_dbscan(ctx.data.turnoff_pts, eps, min_samples, key=lambda i: i.point)
        hulls = [(shapely.unary_union([x.point for x in cluster]).convex_hull, cluster) for cluster in clusters]
        turnoff_pts_clusters = [
            cluster for hull, cluster in hulls if hull.buffer(10 * METER).contains(ctx.data.parking.poi_geom)
        ]

    pts = [x.point for cluster in turnoff_pts_clusters for x in cluster]
    pts = [p for p in pts if ctx.data.blu_face.geom.contains(p)]
    if not pts:
        ctx.error = "polygon.use_obb.no-turnoff_point-cluster"
        return proceed()

    turnoff_clusters = cluster_projection.cluster_by_dbscan(pts, eps=eps, min_samples=min_samples)
    if not turnoff_clusters:
        ctx.error = "polygon.use_obb.no-turnoff_point-cluster"
        return proceed()

    turnoff_pts = max(turnoff_clusters, key=lambda x: len(x))

    # 准备内部路线采样点
    lines = [x.geom for x in ctx.data.inner_links]
    if len(lines) == 0:
        ctx.error = "polygon.no-inner_link"
        return proceed()

    line_sampling_step: float = 2 * METER
    hull = shapely.concave_hull(MultiPoint(turnoff_pts), concave_ratio)
    lines = [x for ln in lines for x in geometric.flat_line(ln.intersection(hull))]
    buffered_lines = [ln.buffer(10 * METER, cap_style="square") for ln in lines]
    exterior_lines = [LineString(ln.exterior.coords) for ln in buffered_lines]
    link_pts = [
        ln.interpolate(i * line_sampling_step)
        for ln in exterior_lines
        for i in range(int(round(ln.length / line_sampling_step)))
    ]
    try:
        polygon = calc_obb(turnoff_pts + link_pts)
        polygon = polygon.intersection(ctx.data.blu_face.geom.buffer(-2 * METER))
        polygon_wkt = smooth_point_string.smooth_polygon(polygon.wkt)
        ctx.polygon = wkt.loads(polygon_wkt)
        ctx.polygon_source = "obb"
        ctx.error = ""
    except ValueError as e:
        ctx.error = f"calc_obb: '{e}'"

    proceed()


def filter_polygon_by_area(ctx: Context, proceed, min_area: float, max_area: float):
    """
    pipe: 筛掉面积过大/过小的面
    """
    if ctx.polygon is None:
        return proceed()

    area = ctx.polygon.area
    if area < min_area:
        ctx.error = "polygon.polygon-is-empty" if ctx.polygon.is_empty else "polygon.polygon-too-small"
        ctx.polygon = None

    if area > max_area:
        ctx.error = "polygon.polygon-too-large"
        ctx.polygon = None

    proceed()


def filter_polygon_by_multi_pois(ctx: Context, proceed):
    """
    pipe: 筛掉包含多个 poi 的面
    """
    if ctx.polygon is None:
        return proceed()

    sql = """
        select bid from park_online_data
        where st_contains(%s, gcj_geom)
            and show_tag = '地上停车场'
            and status = 1
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [f"SRID=4326;{ctx.polygon.wkt}"])
    if len(ret) > 1:
        ctx.error = "polygon.polygon-contains-more-than-one-poi"
        ctx.polygon = None

    proceed()


# helpers


def calc_obb(points: list[Point]):
    """
    计算给定点的 OBB
    """
    # 1. 解析输入为几何要素，并提取所有坐标
    coordinates = [(pt.x, pt.y) for pt in points]
    coordinates = np.array(coordinates)
    if coordinates.shape[0] == 0:
        raise ValueError(f"empty input: {points}")

    # 2. 计算质心并中心化坐标
    center = coordinates.mean(axis=0)
    coords_cent = coordinates - center

    # 3. 主成分分析 (PCA) 获得主方向角度
    cov = np.cov(coords_cent, rowvar=False)
    eig_vals, eig_vecs = np.linalg.eig(cov)
    # 最大特征值对应的特征向量方向
    principal = eig_vecs[:, np.argmax(eig_vals)]
    angle = np.arctan2(principal[1], principal[0])  # 弧度
    # 旋转坐标，使主方向与 x 轴平行
    cos_a, sin_a = np.cos(-angle), np.sin(-angle)
    r = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
    rotated = coords_cent @ r.T

    # 4. 计算约束边界
    xs, ys = rotated[:, 0], rotated[:, 1]
    min_x, max_x = xs.min(), xs.max()
    min_y, max_y = ys.min(), ys.max()
    l_45 = (xs + ys).min()
    u_45 = (xs + ys).max()
    l_135 = (ys - xs).min()
    u_135 = (ys - xs).max()
    # 5. 构造所有可能的约束直线 (a*x + b*y = c)
    lines_eq = [
        (1, 0, min_x),
        (1, 0, max_x),  # x = const
        (0, 1, min_y),
        (0, 1, max_y),  # y = const
        (1, 1, l_45),
        (1, 1, u_45),  # x+y = const (45° 线)
        (-1, 1, l_135),
        (-1, 1, u_135),  # y-x = const (135° 线)
    ]
    # 6. 计算交点并过滤出合法顶点
    pts_intersect = []
    for i in range(len(lines_eq)):
        a1, b1, c1 = lines_eq[i]
        for j in range(i + 1, len(lines_eq)):
            a2, b2, c2 = lines_eq[j]
            det = a1 * b2 - a2 * b1
            if abs(det) < 1e-9:
                continue  # 平行无交点
            x_int = (c1 * b2 - c2 * b1) / det
            y_int = (a1 * c2 - a2 * c1) / det
            # 检查是否满足所有约束
            if x_int < min_x - 1e-6 or x_int > max_x + 1e-6:
                continue
            if y_int < min_y - 1e-6 or y_int > max_y + 1e-6:
                continue
            if (x_int + y_int) < l_45 - 1e-6 or (x_int + y_int) > u_45 + 1e-6:
                continue
            if (y_int - x_int) < l_135 - 1e-6 or (y_int - x_int) > u_135 + 1e-6:
                continue
            pts_intersect.append((x_int, y_int))

    # 去重并排序
    # 注意浮点精度，做简单四舍五入去重
    uniq = list({(round(x, 6), round(y, 6)) for x, y in pts_intersect})
    if not uniq:
        raise ValueError("not found valid polygon points")

    # 计算质心以极角排序
    pts_arr = np.array(uniq)
    cen = pts_arr.mean(axis=0)
    angles = np.arctan2(pts_arr[:, 1] - cen[1], pts_arr[:, 0] - cen[0])
    order = np.argsort(angles)
    hull_pts = pts_arr[order].tolist()
    # 7. 构造多边形，并逆旋转回原始坐标
    poly_rot = Polygon(hull_pts)
    # shapely.rotate 默认绕多边形质心旋转，这里我们绕原点使用同样角度反向旋转
    poly_orig = rotate(poly_rot, np.degrees(angle), origin=(0, 0))
    # 平移回原始中心
    poly_orig = translate(poly_orig, xoff=center[0], yoff=center[1])
    return poly_orig


def clear_graph(nodes: list[GraphNode]):
    """
    清理图结果，把不属于 nodes 里的邻接 node 全部移除
    """
    for node in nodes:
        invalid_nodes1 = [x for x in node.adjacent1_nodes if x not in nodes]
        for to_remove_node in invalid_nodes1:
            node.adjacent1_nodes.remove(to_remove_node)

        invalid_nodes2 = [x for x in node.adjacent2_nodes if x not in nodes]
        for to_remove_node in invalid_nodes2:
            node.adjacent2_nodes.remove(to_remove_node)


def remove_node(nodes: list[GraphNode], target_node: GraphNode):
    """
    从图中移除 node，包括邻接列表里的
    """
    if target_node in nodes:
        nodes.remove(target_node)

    for node in target_node.adjacent1_nodes + target_node.adjacent2_nodes:
        if target_node in node.adjacent1_nodes:
            node.adjacent1_nodes.remove(target_node)

        if target_node in node.adjacent2_nodes:
            node.adjacent2_nodes.remove(target_node)


def trim_graph(nodes: list[GraphNode]):
    """
    去除图中所有枝桠，最终只留下环
    """
    while True:
        tail_nodes = [node for node in nodes if node.is_tail_line()]
        if len(tail_nodes) == 0:
            break

        for tail_node in tail_nodes:
            remove_node(nodes, tail_node)
