"""
停车场4.0面及装饰线生成流程
"""
import math
import random
import sys
from collections import defaultdict
from dataclasses import dataclass, field
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path

import cv2
import shapely.ops
from shapely import Polygon
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.aikit import boundary, satellite_imagery, preview_image
from src.parking.beautify import model, scene_classification as sc, parking_polygon as pp, decorative_line as dl
from src.parking.recognition import dbutils
from src.parking.storefront.utils.geometric import METER_2, METER
from src.tools import pipeline, turnoff_point, utils, tsv, pgsql

# VERSION = "1.0.0"  # 初始版本提交
# VERSION = "1.0.1"  # 修复内部路选取时，未成环枝桠没有修建干净的 bug
# VERSION = "1.0.2"  # 仅排除聚合院落，而非限定基础院落（即允许内部院落）
# VERSION = "1.1.0"  # 对细长空洞求中心线作为装饰线
# 1. 邻近装饰线连接：对端点距离 20m 内的装饰线进行连接延长
# 2. 短直线去除：以（累积）方向偏转 45° 以上的角（即夹角小于 135°）为折角，讲直线打断，去除短于 10m 的直线段
# 3. 裁切掉出入口模型
# 4. 景区：性能优化修复
# VERSION = "1.2.0"
# VERSION = "1.3.0"  # 景区：查找 blu_face 相关面提前到第一步，并提供特殊优先级，其装饰线生成不再依赖熄火点投影
# VERSION = "1.3.1"  # 景区：特殊优先级不卡内部路 form，断头路筛选改为 20m，修复一系列 bug
# VERSION = "1.3.2"  # 优化 AOI 获取逻辑
# VERSION = "1.3.3"  # 当面包含多个 POI 时，清空面，并记录完整的 error 链
# VERSION = "1.3.4"  # 允许停车场 POI 距离父 AOI 10 m 内
# VERSION = "1.3.5"  # 使用众源熄火点（与博士熄火点混合一起使用）
# VERSION = "1.3.6"  # 增加掘金轻核实图片渲染
# VERSION = "1.3.7"  # 对无内部路的场景生成面
# VERSION = "1.3.8"  # 修复掘金图片的尺寸限制问题，增加场景限制，增加断点恢复功能
# VERSION = "1.4.0"  # 内部路标准改为 form=52 or kind=8；重构掘金图片相关逻辑
# VERSION = "1.5.0"  # 增加改进型的 obb 面生成方案；增加对熄火点的抽稀逻辑，提升性能
# VERSION = "1.6.0"  # 1. 装饰线 LD 压盖裁切；2. 已包含智能空间停车位的不做装饰线
# VERSION = "1.6.1"  # 1w 平方米以下停车场的不应用 fewlines 策略
VERSION = "1.6.2"  # 步行路不能被用于生成停车场，装饰线不压任何 nav_link

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    上下文
    """

    bid: str
    data: model.BasicInfo = field(default_factory=model.BasicInfo)
    range: Polygon = field(default=None)
    scene: str = field(default=model.SCENE_UNKNOWN)
    scene_source: str = field(default=model.SCENE_UNKNOWN)
    turnoff_pts_clusters: list[list[turnoff_point.TurnoffPoint]] = field(default_factory=list)
    polygon: Polygon = field(default=None)
    polygon_source: str = field(default="unknown")
    line: BaseGeometry = field(default=None)
    error: list[str] = field(default_factory=list)


@desc()
def fetch_parking(ctx: Context, proceed):
    """
    获取停车场信息
    """
    ctx.data.parking = next(model.get_parkings_by_bids([ctx.bid]), None)
    if ctx.data.parking is None:
        ctx.error.append("fetch.no-parking")
        return

    proceed()


@desc()
def fetch_blu_faces(ctx: Context, proceed, max_dist: float):
    """
    获取 AOI
    """
    park = ctx.data.parking
    # 优先查找当前停车场自己有没有 AOI 的面
    blu_face = model.get_blu_face_by_bid(ctx.bid)
    if blu_face is None:
        blu_face = model.get_blu_face_by_bid(park.parent_bid)
    else:
        ctx.polygon = blu_face.geom
        ctx.polygon_source = f"blu_face.{blu_face.src}"
        ctx.scene = model.SCENE_CENTRALIZED_HIGH
        ctx.scene_source = f"blu_face.parking"

    if blu_face is None:
        blu_face = model.get_blu_face_by_bid(park.root_bid)

    if blu_face is None:
        blu_faces = model.get_blu_face_by_wkt(park.geom.wkt if park.geom else park.poi_geom.wkt)
        if len(blu_faces) > 1:
            ctx.error.append("fetch.multi-blu_face")
            return proceed()

        if len(blu_faces) == 1:
            blu_face = blu_faces[0]

    if blu_face is None:
        ctx.error.append("fetch.no-blu_face")
        return proceed()

    dist = blu_face.geom.distance(park.poi_geom)
    if dist > max_dist:
        ctx.error.append("fetch.blu_face-not-contains-poi")
        return proceed()

    ctx.data.blu_face = blu_face
    proceed()


@desc()
def make_range(ctx: Context, proceed, max_range: float):
    """
    计算要素的搜索范围
    """
    if ctx.data.blu_face:
        ctx.range = ctx.data.blu_face.geom
    else:
        ctx.scene_source = "manual"
        ctx.scene = model.SCENE_UNLIMITED
        ctx.range = ctx.data.parking.poi_geom.buffer(300 * METER)

    if ctx.range.area > max_range:
        max_length = math.sqrt(max_range)
        ctx.range = ctx.data.parking.poi_geom.buffer(max_length)

    proceed()


@desc()
def fetch_inner_links(ctx: Context, proceed, max_blu_area: float):
    """
    获取内部道路
    """
    inner_links = model.get_inner_road_by_wkt(ctx.range.wkt)
    if not inner_links:
        if ctx.data.blu_face and ctx.data.blu_face.geom.area <= max_blu_area:
            return proceed()
        else:
            ctx.error.append("fetch.no-inner_road")
            return proceed()

    ctx.data.inner_links = inner_links
    proceed()


@desc()
def fetch_turnoff_points(ctx: Context, proceed, simplify_count: int, simplify_dist: float, simplify_k: int):
    """
    获取熄火点
    """
    pts1 = turnoff_point.get_points_of_doctor(ctx.range.wkt)
    pts2 = turnoff_point.get_points_of_other(ctx.range.wkt)
    pts = pts1 + pts2
    if len(pts) > simplify_count:
        grid_map = defaultdict(list)
        simplify_dist /= 2 * math.sqrt(2)
        for pt in pts:
            key = (int(pt.point.x // simplify_dist), int(pt.point.y // simplify_dist))
            grid_map[key].append(pt)

        pts = []
        for grouped_pts in grid_map.values():
            if len(grouped_pts) > simplify_k:
                random.shuffle(grouped_pts)
                simple_pts = grouped_pts[:simplify_k]
            else:
                simple_pts = grouped_pts

            pts.extend(simple_pts)

    if not pts and ctx.scene != model.SCENE_CENTRALIZED_HIGH:
        ctx.error.append("fetch.no-turnoff_point")
        return proceed()

    ctx.data.turnoff_pts = pts
    proceed()


@desc()
def fetch_bud_faces(ctx: Context, proceed):
    """
    获取建筑物
    """
    ctx.data.bud_face = model.get_bud_face_by_wkt(ctx.range.wkt)
    proceed()


scene_pipe = pipeline.Pipeline(
    partial(sc.make_convex_hulls, eps=50 * METER, min_samples=5),
    partial(sc.judge_by_std_tag, std_tags=["旅游景点"], scene=model.SCENE_CENTRALIZED),
    partial(sc.judge_by_small_blu_face, small_threshold=8000 * METER_2),
    partial(sc.judge_by_building_ratio, building_ratio=0.4),
    partial(sc.judge_by_turnoff_point_convex_hull, ioa_bud_face=0.5),
)


@desc()
def judge_scene(ctx: Context, proceed):
    """
    判断场景类型
    """
    if ctx.error or ctx.scene != model.SCENE_UNKNOWN:
        return proceed()

    scene_ctx = sc.Context(ctx.data)
    scene_pipe(scene_ctx)

    ctx.scene = scene_ctx.scene
    ctx.scene_source = scene_ctx.scene_source
    ctx.turnoff_pts_clusters = [b for a, b in scene_ctx.convex_hulls]
    if scene_ctx.error:
        ctx.error.append(scene_ctx.error)

    proceed()


@desc()
def review_manual_scene(ctx: Context, proceed):
    """
    确认人工（掘金）可解的场景
    """
    specified_errors = {"scene.poi-not-in-turnoff_point-convex_hull"}
    specified_sources = {"turnoff_point.centralized"}

    if ctx.scene == model.SCENE_UNKNOWN and set(ctx.error) & specified_errors:
        ctx.scene = model.SCENE_MANUAL_CHECK
    elif ctx.scene == model.SCENE_CENTRALIZED and ctx.scene_source in specified_sources:
        ctx.scene = model.SCENE_MANUAL_CHECK

    proceed()


polygon_pipe = pipeline.Pipeline(
    pp.use_blu_face,
    pp.use_parent_blu_face,
    partial(pp.use_ring_inner_link, concave_ratio=0.2, buffer_link=10 * METER, buffer_gap=2 * METER),
    partial(pp.use_obb, eps=20 * METER, min_samples=10, concave_ratio=0.2),
    partial(pp.filter_polygon_by_area, min_area=200 * METER_2, max_area=50_0000 * METER_2),
    pp.filter_polygon_by_multi_pois,
)


@desc()
def make_polygon(ctx: Context, proceed):
    """
    生成停车场多边形
    """
    if ctx.error or ctx.polygon:
        return proceed()

    polygon_ctx = pp.Context(ctx.bid, ctx.data, ctx.scene, ctx.turnoff_pts_clusters)
    polygon_pipe(polygon_ctx)

    ctx.polygon = polygon_ctx.polygon
    ctx.polygon_source = polygon_ctx.polygon_source
    if polygon_ctx.error:
        ctx.error.append(polygon_ctx.error)

    proceed()


line_pipe = pipeline.Pipeline(
    dl.filter_by_smart_space_parking_lots,
    dl.create_link_graphs,
    partial(dl.filter_data_by_contains_poi, buffer=50 * METER),
    partial(dl.generate_feeds, buffer=6 * METER, min_tail_length=20 * METER),
    partial(dl.resolve_slim_polygon, threshold=0.7),
    partial(dl.make_decorative_line, length_per_point=8 * METER, max_distance=10 * METER, nearby_distance=20 * METER),
    dl.make_decorative_line_without_turnoff_point,
    partial(dl.clip_by_blu_face, buffer=0 * METER),
    partial(dl.clip_by_park_face, buffer=-3 * METER),
    partial(dl.clip_by_sd_line, buffer=2 * METER),
    # partial(dl.clip_by_bud_face, buffer=3 * METER),
    partial(dl.clip_by_tail_line, buffer=6 * METER, min_length=50 * METER),
    partial(dl.clip_by_access_model, search_buffer=50 * METER, clip_buffer=14 * METER),
    partial(dl.clip_by_ld_face, buffer=3 * METER),
    partial(dl.filter_by_turning_point, max_angle=45, min_length=11 * METER),
    partial(dl.filter_by_length, min_length=10 * METER),
    partial(dl.filter_by_few_lines, min_area=10_000 * METER_2, min_count=3, min_length=20 * METER),
)


@desc()
def make_line(ctx: Context, proceed):
    """
    生成装饰线
    """
    if ctx.error:
        return proceed()

    if ctx.polygon is None or ctx.polygon.is_empty:
        ctx.error.append("line.no-polygon")
        return proceed()

    line_ctx = dl.Context(ctx.data, ctx.scene, ctx.polygon)
    line_pipe(line_ctx)

    if line_ctx.error:
        ctx.error.append(line_ctx.error)
    else:
        lines = [ln for g in line_ctx.link_graphs for ln in g.lines]
        union_geom = shapely.ops.linemerge(lines) if lines else None
        if union_geom is None or union_geom.is_empty:
            ctx.error.append("line.empty.unknown")
        else:
            ctx.line = union_geom

    proceed()


@desc()
def draw_debug_image(ctx: Context, proceed, save_dir: Path):
    """
    绘制预览图
    """
    if ctx.polygon_source != "obb" or ctx.polygon is None:
        return proceed()

    data = ctx.data
    if ctx.polygon:
        bounds = boundary.from_wkt(ctx.polygon.wkt, buffer=30 * METER)
    else:
        bounds = boundary.from_wkt(data.parking.poi_geom.wkt, buffer=200 * METER)

    image = satellite_imagery.crop(bounds)

    # 绘制 AOI
    if data.blu_face:
        preview_image.draw_polygon(image, data.blu_face.geom.wkt, bounds, thickness=4, color=preview_image.COLOR_GREEN)

    # 绘制熄火点
    points = [p.point for p in data.turnoff_pts]
    points = [p for p in points if not data.bud_face.contains(p)]
    preview_image.draw_point(image, [p.wkt for p in points], bounds, radius=4, color=preview_image.COLOR_CYAN)

    # 绘制建筑物
    preview_image.fill_polygon(image, data.bud_face.wkt, bounds, color=preview_image.COLOR_WHITE, alpha=0.8)
    preview_image.draw_polygon(image, data.bud_face.wkt, bounds, thickness=1, color=preview_image.COLOR_WHITE)

    # 绘制停车场面
    if ctx.polygon:
        preview_image.fill_polygon(image, ctx.polygon.wkt, bounds, color=preview_image.COLOR_PURPLE, alpha=0.2)
        preview_image.draw_polygon(image, ctx.polygon.wkt, bounds, thickness=2, color=preview_image.COLOR_PURPLE)

    # 绘制装饰线
    if ctx.line:
        preview_image.draw_linestring(image, ctx.line.wkt, bounds, thickness=4, color=preview_image.COLOR_RED)

    # 绘制内部路
    if data.inner_links:
        lines = [x.geom.wkt for x in data.inner_links]
        preview_image.draw_linestring(image, lines, bounds, thickness=4, color=preview_image.COLOR_BLUE)

    preview_image.draw_point(image, data.parking.poi_geom.wkt, bounds, radius=8, color=preview_image.COLOR_RED)
    cv2.imwrite(str(utils.ensure_path(save_dir / f"{ctx.bid}.jpg")), image)
    proceed()


@desc()
def draw_juejin_image(ctx: Context, proceed, max_size: float, save_dir: Path):
    """
    绘制掘金核实图片
    """
    if ctx.scene != model.SCENE_MANUAL_CHECK:
        return proceed()

    data = ctx.data
    if ctx.data.blu_face and ctx.data.blu_face.geom.area <= max_size * max_size:
        bounds = boundary.from_wkt(data.blu_face.geom.wkt, buffer=10 * METER)
    else:
        bounds = boundary.from_wkt(data.parking.poi_geom.wkt, buffer=max_size / 2)

    image = satellite_imagery.crop(bounds)

    # 绘制 AOI
    if data.blu_face:
        preview_image.draw_polygon(image, data.blu_face.geom.wkt, bounds, thickness=4, color=preview_image.COLOR_GREEN)

    # 绘制熄火点
    points = [p for p in data.turnoff_pts if not data.bud_face.contains(p.point)]
    other_pts = [p.point.wkt for p in points if isinstance(p, turnoff_point.OtherTurnoffPoint)]
    preview_image.draw_point(image, other_pts, bounds, radius=4, color=preview_image.COLOR_YELLOW)
    doctor_pts = [p.point.wkt for p in points if isinstance(p, turnoff_point.DoctorTurnoffPoint)]
    preview_image.draw_point(image, doctor_pts, bounds, radius=4, color=preview_image.COLOR_CYAN)

    # 绘制建筑物
    preview_image.fill_polygon(image, data.bud_face.wkt, bounds, color=preview_image.COLOR_WHITE, alpha=0.8)
    preview_image.draw_polygon(image, data.bud_face.wkt, bounds, thickness=1, color=preview_image.COLOR_WHITE)

    # 绘制内部路
    if data.inner_links:
        lines = [x.geom.wkt for x in data.inner_links]
        preview_image.draw_linestring(image, lines, bounds, thickness=4, color=preview_image.COLOR_BLUE)

    preview_image.draw_point(image, data.parking.poi_geom.wkt, bounds, radius=8, color=preview_image.COLOR_RED)
    cv2.imwrite(str(utils.ensure_path(save_dir / "images" / f"{ctx.bid}.jpg")), image)
    proceed()


@desc()
def try_exception(ctx: Context, proceed):
    """
    作为第一个 pipe，捕获整个 pipeline 中的异常
    """
    try:
        proceed()
    except Exception as ex:
        ctx.error.append(str(ex).replace("\n", " "))


@desc()
def clear_contexts(ctx: Context, proceed):
    """
    作为最后一个 pipe，将图结构清楚掉，否则有些递归太深的 Graph，会导致多进程返回数据时 pickle 出错（递归太深）
    """
    try:
        proceed()
    finally:
        ctx.data.turnoff_pts.clear()
        ctx.turnoff_pts_clusters.clear()


def get_undone_bids(bids: list[str]):
    """
    获取未解的 bid
    """
    sql = """
        select bid
        from park_4_0_beautify
        where bid in %s 
            and result = 'success'
            or (result != 'success' and version = %s);
    """
    done_bids = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(bids), VERSION])
    done_bids = {x[0] for x in done_bids}
    return [bid for bid in bids if bid not in done_bids]


def get_file_saver(valid_path: Path, invalid_path: Path, error_path: Path):
    """
    获取保存函数
    """

    def save(ctx: Context):
        error = ",".join(ctx.error) if ctx.error else ""
        if error:
            row = [ctx.bid, error]
            tsv.write_tsv(error_path, [row], mode="a")

        polygon = ctx.polygon.wkt if ctx.polygon else "<empty>"
        line = ctx.line.wkt if ctx.line else "<empty>"
        name = ctx.data.parking.name if ctx.data.parking else "<empty>"
        city = ctx.data.parking.city if ctx.data.parking else "<empty>"
        std_tag = ctx.data.blu_face.std_tag if ctx.data.blu_face else "<empty>"
        row = [ctx.bid, name, city, std_tag, ctx.scene_source, ctx.scene, ctx.polygon_source, polygon, line]
        if not error and polygon != "<empty>" and line != "<empty>":
            tsv.write_tsv(valid_path, [row], mode="a")
        else:
            tsv.write_tsv(invalid_path, [[*row, error]], mode="a")

    return save


def execute(bids: list[str], output_dir: Path, overwrite: bool):
    """
    处理给定的 bids 列表，overwrite 为 True 时，会清空已完成的结果文件，重新开始
    """
    error_path = utils.ensure_path(output_dir / "error.tsv", cleanup=overwrite)
    valid_path = utils.ensure_path(output_dir / "result.valid.tsv", cleanup=overwrite)
    invalid_path = utils.ensure_path(output_dir / "result.invalid.tsv", cleanup=overwrite)

    fetch_pipe = pipeline.Pipeline(
        fetch_parking,
        partial(fetch_blu_faces, max_dist=10 * METER),
        partial(make_range, max_range=2000 * 2000 * METER_2),
        partial(fetch_inner_links, max_blu_area=100_000 * METER_2),
        partial(fetch_turnoff_points, simplify_count=200_000, simplify_dist=18 * METER, simplify_k=1),
        fetch_bud_faces,
    )
    pipe = pipeline.Pipeline(
        try_exception,
        clear_contexts,
        fetch_pipe,
        judge_scene,
        review_manual_scene,
        make_polygon,
        make_line,
        partial(draw_debug_image, save_dir=utils.ensure_dir(output_dir / "preview", overwrite)),
        partial(draw_juejin_image, max_size=1000 * METER, save_dir=utils.ensure_dir(output_dir / "juejin", overwrite)),
    )
    desc.attach(pipe)
    desc.disabled = True

    if overwrite:
        done_bids = set()
    else:
        valid_bids = {x[0] for x in tsv.read_tsv(valid_path)} if valid_path.exists() else set()
        invalid_bids = {x[0] for x in tsv.read_tsv(invalid_path)} if invalid_path.exists() else set()
        done_bids = valid_bids | invalid_bids

    doing_bids = [bid for bid in bids if bid not in done_bids]
    contexts = (Context(bid=bid) for bid in doing_bids)

    save_to_file = get_file_saver(valid_path, invalid_path, error_path)
    with Pool(16) as p:
        for ctx in tqdm(p.imap_unordered(pipe, contexts), total=len(doing_bids)):
            save_to_file(ctx)


def main(bid_path: Path, overwrite: bool):
    """
    主函数
    """
    bids = [x[0] for x in tsv.read_tsv(bid_path)]
    output_dir = Path("output") / f"{bid_path.stem}_{VERSION}"
    execute(bids, output_dir, overwrite)


if __name__ == "__main__":
    main(Path(sys.argv[1]), bool(int(sys.argv[2])) if len(sys.argv) > 2 else False)
