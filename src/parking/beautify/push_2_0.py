"""
推送停车场2.0数据
"""
import sys
from pathlib import Path
from typing import Iterable

from loguru import logger
from tqdm import tqdm

from src.parking.beautify import model, push_base, prod_flow
from src.parking.recognition import dbutils
from src.tools import tsv, utils, pgsql


def push_data_2_0(bid: str, face_id, polygon: str, source: str, batch: str):
    """
    推送停车场面
    """
    data = {
        "bid": bid,
        "force_update": 1,  # 强制更新
        "source": source,
        "source_id": face_id,
        "area": polygon,
        "area_batch": batch,
    }
    resp = push_base.push_parking_data(data)
    return resp


def get_rollback_infos(bids: Iterable[str]):
    """
    获取回滚信息
    """
    sql = """
        select bid, area_batch, st_astext(area)
        from parking
        where bid in %s
    """
    return dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [tuple(bids)])


def push(items: Iterable[tuple[str, str]]):
    """
    推送数据
    """
    for bid, polygon in items:
        if not push_base.is_empty_wkt(polygon) and not push_base.check_wkt(polygon, ["Polygon"]):
            yield "failed", (bid, "invalid-polygon", polygon)
            continue

        source = push_base.select_source(polygon, "")
        if source == push_base.SOURCE_POLYGON:
            face_id = utils.md5(polygon)
            polygon = polygon
            batch = f"{source}-{prod_flow.VERSION}-{push_base.TODAY}"
        elif source == push_base.SOURCE_EMPTY:
            face_id = ""
            polygon = ""
            batch = ""
        else:
            raise ValueError(f"invalid source: {source}")

        resp = push_data_2_0(bid, face_id, polygon, source, batch)
        if isinstance(resp, dict) and resp.get("errno", 1) == 0:
            yield "success", (bid, face_id)
        else:
            yield "failed", (bid, "push-failed", str(resp))


def main(work_dir: Path):
    """
    主函数：处理由 prod_flow.py 输出的 result.valid.tsv 文件
    """
    pushed_path = work_dir / "DONE.2_0.PUSHED"
    unpushed_path = work_dir / "DONE.2_0.UNPUSHED"
    rollback_path = work_dir / "DONE.2_0.ROLLBACK"

    file_paths = [work_dir / "result.valid.tsv", work_dir / "result.invalid.tsv"]
    to_push_scenes = {model.SCENE_SCATTER, model.SCENE_CENTRALIZED_HIGH, model.SCENE_CENTRALIZED}
    data = [
        (x[push_base.IDX_BID], x[push_base.IDX_POLYGON])
        for file_path in file_paths
        for x in tsv.read_tsv(file_path)
        if not push_base.is_empty_wkt(x[push_base.IDX_POLYGON]) and x[push_base.IDX_SCENE] in to_push_scenes
    ]
    bids = [x[0] for x in data]

    logger.info("start backup rollback infos...")
    infos = get_rollback_infos(bids)
    tsv.write_tsv(rollback_path, infos, mode="a")

    logger.info("start push data...")
    for status, payload in tqdm(push(data), total=len(data)):
        if status == "success":
            tsv.write_tsv(pushed_path, [payload], mode="a")
        elif status == "failed":
            tsv.write_tsv(unpushed_path, [payload], mode="a")


if __name__ == "__main__":
    main(Path(sys.argv[1]))
