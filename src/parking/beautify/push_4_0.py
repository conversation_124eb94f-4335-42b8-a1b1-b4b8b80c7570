"""
推送停车场4.0数据
"""
import sys
from pathlib import Path
from typing import Iterable

from loguru import logger
from tqdm import tqdm

from src.parking.beautify import model, push_base, prod_flow
from src.parking.recognition import dbutils
from src.tools import tsv, utils, pgsql


def push_data_4_0(bid: str, face_id, polygon: str, line: str, source: str, batch: str):
    """
    推送停车场面
    """
    data = {
        "bid": bid,
        "force_update": 1,  # 强制更新
        "source": source,
        "source_id": face_id,
        "show_area": polygon,
        "show_area_batch": batch,
        "cover_show_area_id": face_id,
        "central_line": line,
    }
    resp = push_base.push_parking_data(data)
    return resp


def get_rollback_infos(bids: Iterable[str]):
    """
    获取回滚信息
    """
    sql = """
        select bid, show_area_batch, st_astext(show_area), st_astext(central_line)
        from parking
        where bid in %s
    """
    return dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [tuple(bids)])


def push(items: Iterable[tuple[str, str, str]]):
    """
    推送数据
    """
    for bid, polygon, line in items:
        if not push_base.is_empty_wkt(polygon) and not push_base.check_wkt(polygon, ["Polygon"]):
            yield "failed", (bid, "invalid-polygon", polygon)
            continue

        if not push_base.is_empty_wkt(line) and not push_base.check_wkt(line, ["LineString", "MultiLineString"]):
            yield "failed", (bid, "invalid-line", line)
            continue

        source = push_base.select_source(polygon, line)
        if source == push_base.SOURCE:
            face_id = utils.md5(polygon)
            polygon = polygon
            line = line
            batch = f"{source}-{prod_flow.VERSION}-{push_base.TODAY}"
        elif source == push_base.SOURCE_POLYGON:
            face_id = utils.md5(polygon)
            polygon = polygon
            line = ""
            batch = f"{source}-{prod_flow.VERSION}-{push_base.TODAY}"
        elif source == push_base.SOURCE_EMPTY:
            face_id = ""
            polygon = ""
            line = ""
            batch = ""
        else:
            raise ValueError(f"invalid source: {source}")

        resp = push_data_4_0(bid, face_id, polygon, line, source, batch)
        if isinstance(resp, dict) and resp.get("errno", 1) == 0:
            yield "success", (bid, face_id)
        else:
            yield "failed", (bid, "push-failed", str(resp))


def push_valid(work_dir: Path):
    """
    主函数：处理由 prod_flow.py 输出的 result.valid.tsv 文件
    """
    pushed_path = work_dir / "DONE.4_0.PUSHED"
    unpushed_path = work_dir / "DONE.4_0.UNPUSHED"
    rollback_path = work_dir / "DONE.4_0.ROLLBACK"

    file_path = work_dir / "result.valid.tsv"
    to_push_scenes = {model.SCENE_SCATTER, model.SCENE_CENTRALIZED_HIGH}
    data = [
        (x[push_base.IDX_BID], x[push_base.IDX_POLYGON], x[push_base.IDX_LINE])
        for x in tsv.read_tsv(file_path)
        if x[push_base.IDX_SCENE] in to_push_scenes
    ]
    bids = [x[0] for x in data]

    logger.info("start backup rollback infos...")
    infos = get_rollback_infos(bids)
    tsv.write_tsv(rollback_path, infos, mode="a")

    logger.info("start push data...")
    for status, payload in tqdm(push(data), total=len(data)):
        if status == "success":
            tsv.write_tsv(pushed_path, [payload], mode="a")
        elif status == "failed":
            tsv.write_tsv(unpushed_path, [payload], mode="a")


def push_invalid(work_dir: Path):
    """
    主函数：处理由 prod_flow.py 输出的 result.valid.tsv 文件
    """
    pushed_path = work_dir / "DONE.4_0.PUSHED"
    unpushed_path = work_dir / "DONE.4_0.UNPUSHED"
    rollback_path = work_dir / "DONE.4_0.ROLLBACK"

    file_path = work_dir / "result.invalid.tsv"
    data = [(x[push_base.IDX_BID], x[push_base.IDX_POLYGON], x[push_base.IDX_LINE]) for x in tsv.read_tsv(file_path)]
    bids = [x[0] for x in data]

    logger.info("start backup rollback infos...")
    infos = get_rollback_infos(bids)
    tsv.write_tsv(rollback_path, infos, mode="a")

    logger.info("start push data...")
    for status, payload in tqdm(push(data), total=len(data)):
        if status == "success":
            tsv.write_tsv(pushed_path, [payload], mode="a")
        elif status == "failed":
            tsv.write_tsv(unpushed_path, [payload], mode="a")


def main(work_dir: Path, category: str):
    """
    主函数
    """
    if category == "valid":
        push_valid(work_dir)
    elif category == "invalid":
        push_invalid(work_dir)
    else:
        raise ValueError(f"invalid category: {category}")


if __name__ == "__main__":
    main(Path(sys.argv[1]), sys.argv[2])
