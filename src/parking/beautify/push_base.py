"""
推送停车场4.0数据
"""
from datetime import datetime

import requests
from shapely import wkt

from src.parking.beautify import prod_flow

PARK_RELEASE_URL = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"

SOURCE = "STRATEGY_UPDATE_BEAUTIFY_4_0"  # 面 & 线
SOURCE_POLYGON = "STRATEGY_POLYGON_BEAUTIFY_4_0"  # 仅有面（不可能仅有线，没有面就没有线）
SOURCE_EMPTY = "STRATEGY_EMPTY_BEAUTIFY_4_0"  # 清空线上数据
TODAY = datetime.now().strftime("%Y%m%d")
BATCH = f"{SOURCE}-{prod_flow.VERSION}-{TODAY}"

IDX_BID = 0
IDX_SCENE = 5
IDX_POLYGON = 7
IDX_LINE = 8


def push_parking_data(payload: dict):
    """
    停车场数据推送上线
    """
    resp = requests.post(url=PARK_RELEASE_URL, json=payload)
    return resp.json()


def check_wkt(geom: str, expected_type: list[str]):
    """
    检查 wkt 格式是否符合预期
    """
    # noinspection PyBroadException
    try:
        geom = wkt.loads(geom)
        assert geom.geom_type in expected_type
        return True
    except:
        return False


def is_empty_wkt(wkt_str: str):
    """
    检查 wkt 是否是空，兼容 '<empty>' 和空字符串
    """
    return wkt_str in ("", "<empty>")


def select_source(polygon: str, line: str):
    """
    选择推送的源
    """
    if is_empty_wkt(polygon):
        return SOURCE_EMPTY
    else:
        if is_empty_wkt(line):
            return SOURCE_POLYGON
        else:
            return SOURCE
