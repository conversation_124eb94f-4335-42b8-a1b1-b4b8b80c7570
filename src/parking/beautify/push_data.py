"""
推送停车场4.0数据
"""
import sys
from pathlib import Path

import requests
from loguru import logger
from shapely import wkt

from src.tools import tsv, utils

PARK_RELEASE_URL = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
SOURCE = "STRATEGY_UPDATE_BEAUTIFY_4_0"


def push_parking_data(payload: dict):
    """
    停车场数据推送上线
    """
    resp = requests.post(
        url=PARK_RELEASE_URL,
        json=payload,
    )
    return resp.json()


def push_line(bid: str, line: str):
    """
    更新数据
    """
    payload = {
        "bid": bid,
        "force_update": 1,  # 强制更新
        # "park_spec": 2,
        "central_line": line,
        "source": SOURCE,
    }
    resp = push_parking_data(payload)
    return resp


def push_show_area(bid: str, polygon: str):
    """
    推送停车场面
    """
    data = {
        "bid": bid,
        "force_update": 1,  # 强制更新
        # "park_spec": 2,
        "show_area": polygon,
        "show_area_batch": SOURCE,
        "cover_show_area_id": utils.md5(polygon),
        "source": SOURCE,
    }
    resp = push_parking_data(data)
    return resp


def push_area(bid: str, polygon: str):
    """
    推送停车场面
    """
    data = {
        "bid": bid,
        "force_update": 1,  # 强制更新
        "area": polygon,
        "area_batch": SOURCE,
        "source": SOURCE,
    }
    resp = push_parking_data(data)
    return resp


def check_wkt(geom: str, expected_type: list[str]):
    """
    检查 wkt 格式是否符合预期
    """
    # noinspection PyBroadException
    try:
        geom = wkt.loads(geom)
        assert geom.geom_type in expected_type
        return True
    except:
        return False


def main(data_type: str, file_path: Path):
    """
    主函数：处理由 prod_flow.py 输出的 result.valid.tsv 文件
    """
    idx_polygon = 7
    idx_line = 8

    log_dir = utils.ensure_dir("logs")
    logger.add(log_dir / f"{file_path.parent.name}_{file_path.stem}.{data_type}.log")
    if data_type == "line":
        data = [(x[0], x[idx_line]) for x in tsv.read_tsv(file_path)]
        for bid, line in data:
            if check_wkt(line, ["LineString", "MultiLineString"]):
                resp = push_line(bid, line)
                logger.info(f"{bid}, {resp}")
            else:
                logger.error(f"{bid}, {line}")
    elif data_type == "show_area":
        data = [(x[0], x[idx_polygon]) for x in tsv.read_tsv(file_path)]
        for bid, polygon in data:
            if check_wkt(polygon, ["Polygon"]):
                resp = push_show_area(bid, polygon)
                logger.info(f"{bid}, {resp}")
            else:
                logger.error(f"{bid}, {polygon}")
    elif data_type == "area":
        data = [(x[0], x[idx_polygon]) for x in tsv.read_tsv(file_path)]
        for bid, polygon in data:
            if check_wkt(polygon, ["Polygon"]):
                resp = push_area(bid, polygon)
                logger.info(f"{bid}, {resp}")
            else:
                logger.error(f"{bid}, {polygon}")


if __name__ == "__main__":
    main(sys.argv[1], Path(sys.argv[2]))
