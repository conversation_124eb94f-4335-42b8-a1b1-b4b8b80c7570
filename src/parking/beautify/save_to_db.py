"""
4.0面和装饰线数据入库
"""
import sys
from pathlib import Path
from typing import Optional

import psycopg2.extras
from loguru import logger

from src.parking.beautify import prod_flow
from src.tools import pgsql, tsv


def save(result_path: Path, batch: str):
    """
    保存 result.* 文件到数据库
    """
    idx_bid = 0
    idx_name = 1
    idx_city = 2
    idx_std_tag = 3
    idx_scene_source = 4
    idx_scene = 5
    idx_polygon_source = 6
    idx_polygon = 7
    idx_line = 8
    idx_error = 9

    def empty_value(val: str, default: Optional[str] = ""):
        return val if val != "<empty>" else default

    sql = """
        insert into park_4_0_beautify (
            bid, name, parent_std_tag, 
            scene_source, scene, polygon_source, polygon, central_line, 
            version, result, city, batch
        )
        values %s
    """
    values = [
        (
            x[idx_bid],
            empty_value(x[idx_name]),
            empty_value(x[idx_std_tag]),
            x[idx_scene_source],
            x[idx_scene],
            x[idx_polygon_source],
            empty_value(x[idx_polygon], None),
            empty_value(x[idx_line], None),
            prod_flow.VERSION,
            x[idx_error] if len(x) > idx_error else "success",
            empty_value(x[idx_city]),
            batch,
        )
        for x in tsv.read_tsv(result_path)
    ]
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cur:
        psycopg2.extras.execute_values(cur, sql, values, page_size=10000)

    logger.info(f"{len(values)} rows inserted.")


def main(work_dir: Path):
    """
    主函数
    """
    batch = work_dir.name
    valid_path = work_dir / "result.valid.tsv"
    save(valid_path, batch)

    invalid_path = work_dir / "result.invalid.tsv"
    save(invalid_path, batch)


if __name__ == "__main__":
    main(Path(sys.argv[1]))
