"""
场景判断策略
"""
from dataclasses import dataclass, field

import shapely
from shapely import Polygon

from src.parking.beautify import model
from src.parking.beautify.model import SCENE_UNKNOWN, SCENE_SCATTER, SCENE_CENTRALIZED
from src.parking.storefront import cluster_projection
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.tools import utils, turnoff_point


@dataclass
class Context:
    """
    上下文
    """

    data: model.BasicInfo
    convex_hulls: list[tuple[Polygon, list[turnoff_point.TurnoffPoint]]] = field(default_factory=list)
    scene: str = field(default=SCENE_UNKNOWN)
    scene_source: str = field(default=SCENE_UNKNOWN)
    error: str = field(default="")


def make_convex_hulls(ctx: Context, proceed, eps: float, min_samples: int):
    """
    制作熄火点凸包
    """
    poi_clusters = cluster_projection.cluster_by_dbscan(ctx.data.turnoff_pts, eps, min_samples, key=lambda i: i.point)
    convex_hulls = [(shapely.unary_union([x.point for x in cluster]).convex_hull, cluster) for cluster in poi_clusters]
    convex_hulls = [
        (hull, cluster) for hull, cluster in convex_hulls if hull.buffer(10 * METER).contains(ctx.data.parking.poi_geom)
    ]
    ctx.convex_hulls = convex_hulls
    proceed()


def judge_by_small_blu_face(ctx: Context, proceed, small_threshold: float):
    """
    若所在的 AOI 很小，则直接认为分散
    """
    if ctx.scene != SCENE_UNKNOWN:
        return proceed()

    if ctx.data.blu_face.geom.area < small_threshold:
        ctx.scene = SCENE_SCATTER
        ctx.scene_source = "blu_face.small_area"

    proceed()


def judge_by_building_ratio(ctx: Context, proceed, building_ratio: float):
    """
    若所在的 AOI 内，建筑物面积占比过高，则直接认为分散
    """
    if ctx.scene != SCENE_UNKNOWN:
        return proceed()

    ratio = ctx.data.bud_face.area / ctx.data.blu_face.geom.area
    if ratio > building_ratio:
        ctx.scene = SCENE_SCATTER
        ctx.scene_source = "bud_face.large_ratio"

    proceed()


def judge_by_std_tag(ctx: Context, proceed, std_tags: list[str], scene: str):
    """
    若父标签在指定标签中，则直接设置为指定标签
    """
    if ctx.scene != SCENE_UNKNOWN:
        return proceed()

    parent_tag = ""
    if ctx.data.parking.parent_bid:
        parent_tag = model.get_std_tag_by_bid(ctx.data.parking.parent_bid)

    if any(tag in parent_tag for tag in std_tags):
        ctx.scene = scene
        ctx.scene_source = "parent_std_tag"

    proceed()


def judge_by_turnoff_point_convex_hull(ctx: Context, proceed, ioa_bud_face: float):
    """
    根据熄火点凸包是否包含建筑物来判定场景
    """
    if ctx.scene != SCENE_UNKNOWN:
        return proceed()

    convex_hulls = ctx.convex_hulls
    if len(convex_hulls) == 0:
        ctx.error = "scene.poi-not-in-turnoff_point-convex_hull"
        return

    bud_faces = geometric.flat_polygon(ctx.data.bud_face)
    overlap_bud_face = sum(
        1 for bud in bud_faces if any(utils.calc_ioa(hull, bud) > ioa_bud_face for hull, cluster in convex_hulls)
    )
    if overlap_bud_face > 0:
        ctx.scene = SCENE_SCATTER
        ctx.scene_source = "turnoff_point.overlap_bud_face"
        return proceed()

    ctx.scene_source = "turnoff_point.centralized"
    ctx.scene = SCENE_CENTRALIZED
    proceed()
