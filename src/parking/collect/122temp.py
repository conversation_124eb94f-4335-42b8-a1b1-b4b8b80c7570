import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests
import csv
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
from shapely.ops import transform
from shapely.ops import unary_union
import pyp<PERSON>j
import random

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from shapely import Polygon, LineString

from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER



def create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def _create_vdust_connection():
    """
    创建 mysql 数据库连接
    """
    # host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host="***********", port=int(6082), user="zhaoxiaolong_r", password="offline_read", db="vdust", charset="utf8", autocommit=1)


def _get_all_task():
    sql = f"""
        select strategy_id,point_time,response_info from park_points_push where created_at>'2025-01-18' and created_at<'2025-01-19';
        """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_task_by_id():
    sql = f"""
        select strategy_id,point_time,response_info from park_points_push 
        where created_at>'2025-01-18' and created_at<'2025-01-19' and strategy_id in(141961, 100780, 141959, 100514, 116744, 145363, 142002, 142536, 99190, 98437);
        """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_push_count(strategy_id):
    sql = f"""
        select count(*) from park_points_push where created_at>'2025-01-18' and created_at<'2025-01-19' and strategy_id={strategy_id};
        """
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)[0]


def _get_push_data(strategy_id):
    sql = f"""
        select a.strategy_id,st_astext(b.geom),a.point_time,a.device_no,st_astext(a.point_geom),
            st_astext(a.parking_traj_geom),a.response_info 
            from park_points_push a left join park_storefront_post_zhongyuan b on a.strategy_id=b.strategy_id 
            where a.created_at>'2025-01-18' and a.created_at<'2025-01-19' and a.strategy_id={strategy_id};
        """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_zhongyuan_match_detail(match_id):
    # http://gzxj-zhongyuan0004.gzxj:8888/trace/match?match_id=7193446943
    url = f"http://gzxj-zhongyuan0004.gzxj:8888/trace/match?match_id={match_id}"
    response = requests.get(url)
    data = json.loads(response.content)
    return data


def _get_track_detail(url):
    response = requests.get(url)
    data = json.loads(response.content)
    return data



if __name__ == '__main__':

    conn = _create_vdust_connection()
    cursor = conn.cursor()

    export_file = []
    data = _get_all_task()
    total_match_cnt = 0
    strategy_id_list = []
    for item in data:
        strategy_id = item[0]
        strategy_id_list.append(strategy_id)

        all_push_data = _get_push_data(strategy_id)
        match_cnt = 0
        push_count = 0
        for it in all_push_data:
            strategy_id = it[0]
            parking_geom = it[1]
            point_time = it[2]
            dt = datetime.fromtimestamp(point_time)
            formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            device_no = it[3]
            point_geom = it[4]
            parking_traj_geom = it[5]
            response_info = it[6]
            response_info_dict = json.loads(response_info)
            task_id = response_info_dict['data']['data']['task_id']

            sql = f"""
                select m.update_time,m.match_id,m.match_status,m.match_cnt from task t, match_task mt, matches m where t.update_time>'2025-01-18' and t.task_source=45
                 and mt.task_id=t.task_id and mt.match_id=m.match_id and t.business_id='{task_id}'
                """
            # print(sql)
            cursor.execute(sql)
            ree = cursor.fetchone()
            # print(ree)
            collect_status = '已回收'
            long_geom = "LINESTRING EMPTY"
            pic_list = ""
            match_id = ""
            update_time = ""
            push_count = push_count + 1
            if ree is None:
                collect_status = '未回收'
            else:
                update_time = ree[0]
                match_id = ree[1]
                match_status = ree[2]
                match_cnt_temp = ree[3]
                if match_status >= 21:
                    match_cnt = match_cnt + match_cnt_temp
                    total_match_cnt = total_match_cnt + match_cnt_temp
                # try:
                #     match_detail = _get_zhongyuan_match_detail(match_id)
                #     if match_detail is None:
                #         long_geom = "LINESTRING EMPTY"
                #     if match_detail['status'] == 0 and len(match_detail['data']['pic_list']) == 0:
                #         pic_list = ""
                #     else:
                #         pic_list = f"http://gzxj-zhongyuan0004.gzxj:8888/trace/match?match_id={match_id}"
                #         track_list = _get_track_detail(match_detail['data']['geo_list'][0]['geo_url'])
                #         coordinates = [(track['longitude'], track['latitude']) for track in track_list['tracks']]
                #         line = LineString(coordinates)
                #         # 将 LineString 对象转换为 WKT 格式
                #         long_geom = line.wkt
                # except Exception as e:
                #     print(e)
                #     continue

            # business_id_str = f"`{task_id}"
            # line = (f"{strategy_id},{collect_status}, {device_no}, {parking_geom}, {formatted_time}, {parking_traj_geom}, "
            #         f"{long_geom}, {business_id_str}, {match_id}, {pic_list}")
            # print(line)
            # export_file.append([
            #     strategy_id, collect_status, formatted_time, update_time, device_no, parking_geom,
            #     parking_traj_geom, long_geom, business_id_str, match_id, pic_list
            # ])
            print(
                f"strategy_id: {strategy_id}, match_cnt: {match_cnt}, push_count: {push_count}, avg: {match_cnt / push_count}")
    unique_set = set(strategy_id_list)
    print(len(unique_set))


    # file_path = '/home/<USER>/fanjiabin/aoi-ml/output/src/parking/collect/random_collect0118_new1.csv'
    # # 使用 with 语句打开文件
    # with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
    #     writer = csv.writer(file, delimiter='\t')
    #     # 写入每一行数据
    #     for row in export_file:
    #         writer.writerow(row)
    #
    # print("数据已成功导出.")

    # sql = "select * from task limit 1"
    # cursor.execute(sql)
    # print(cursor.fetchall())
    # res = _get_all_task()
    # matched_count = 0
    # my_list = []
    # for r in res:
    #     strategy_id = r[0]
    #     point_time = r[1]
    #     data_dict = json.loads(r[2])
    #     task_id = data_dict['data']['data']['task_id']
    #
    #     sql = f"""
    #         select count(*) from task t, match_task mt, matches m where t.update_time>'2025-01-18' and t.task_source=45
    #          and mt.task_id=t.task_id and mt.match_id=m.match_id and m.match_status=21
    #          and t.business_id='{task_id}'
    #         """
    #     cursor.execute(sql)
    #     ree = cursor.fetchone()
    #     # print(ree)
    #     if ree[0] > 0:
    #         matched_count += 1
    #         my_list.append(strategy_id)
    #
    # unique_set = set(my_list)
    # print(len(unique_set))
    # print("matched_count: ", matched_count)
    #
    # random_sample = random.sample(unique_set, 10)
    # print(random_sample)