#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
Description
按link方式推送众源采集接口
"""
import jwt
import logging
import os

import json
import time
from datetime import datetime
import requests

from loguru import logger
from collections import defaultdict

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from typing import List, Dict, Tuple
from shapely.wkt import loads
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
import math

url = "http://dedc.baidu.com/vdust/main/createnewtask"


def get_jwt_token(account, token, service_list):
    """获取加密字符串

    Args:
        account (str): 授权账号
        token (str): 账号对应token
        service_list (list): 要访问的接口，对于路网服务，列表长度只能为1，第0个元素为要访问的接口名

    Returns:
        _type_: _description_
    """
    msg = {
        'exp': int(time.time()) + 260,
        'timeout': int(time.time()) + 3600 * 24 * 7,  # 有效期7天,
        'name': account,  # 账号名称
        'service': service_list # 服务名称
    }
    # 获取token
    ret = jwt.encode(msg, token, algorithm='HS256')
    return ret


def _get_parking_points(batch_src, geom):
    qry = (f"select id,user_id,traj_time,st_astext(geom),st_astext(parking_traj_geom) from parking_points"
           f" where batch_src ='{batch_src}'"
           f" and ST_Contains(ST_GeomFromText('{geom}', 4326), geom)")
    # print(qry)
    res = dbutils.fetch_one(pgsql.TRAJ_FEATURE, qry)
    return res


def _push_data(source_id, link_id_str, link_geom, dir):
    """主流程
    """
    # url = "http://*************:8891/de/servicehub/create_task"
    url = "http://service.mapde.baidu-int.com/de/servicehub/create_task"

    # account = 'parking'
    # token = 'c262159e031c40889226dd0d2aa04707'
    account = 'de_mid_shopping'
    token = '206281fd24024d6bace55a6b21342067'

    service_list = ['vdust']
    data = {
        # "deliver_source": "parking",
        # "deliver_source": "aoi",
        "deliver_source": "damen",
        # "task_type": "zy",
        "task_info": [{
            "geo_type": "link",
            "geom": link_geom,
            "link_id": link_id_str,
            "dir": dir,
            "job_distance_period": 3,
            "quality_filter": "000",
            # "nodegeom": "000",
            # "version": "17777",
            # "len": len,
            "collect_times": 2,
            # "city_code": city_code
        }]
    }
    print(data)
    key = get_jwt_token(account, token, service_list)
    headers = {'content-type': "application/json", 'Authorization': 'Bearer ' + key}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    logging.info(response.text)
    _insert_push_data(source_id, json.dumps(data), response.text)


def _insert_push_data(source_id, request_info, response_info):
    try:
        conn = pgsql.get_connection(pgsql.POI_CONFIG)
        cursor = conn.cursor()

        parsed_data = json.loads(response_info)
        nested_task_id = parsed_data.get("data", {}).get("data", {}).get("task_id")
        sql = f"""insert into parking_collect_push (source_id,request_info,response_info,task_id) 
        values({source_id}, '{request_info}','{response_info}', '{nested_task_id}')
            """
        print(sql)
        res = cursor.execute(sql)
        if res == 0:
            logger.error(f"插入失败, {sql}")
            conn.rollback()
            return

        conn.commit()
        logger.info(f"_insert_push_data success")
    except Exception as e:
        print(f"插入数据失败: {e}")


def _get_parking_data():

    sql = """
        -- select source_id,cityname from parking_collect_data where source='common' and tag='daily' limit 1000
        select source_id,cityname from parking_collect_data 
        where source_id not in(select source_id from parking_collect_push where created_at>='2025-06-13 10:00:00')
         limit 1000
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return res


def _get_road_relation_by_bid(bid):
    sql = f"""
    select road_relation_childrens from park_online_data where bid='{bid}';
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if not res:
        return []
    else:
        return res[0]


def _get_long_link_id(short_link_id):
    """
    获取长link_id
    """
    sql = """
        SELECT sid FROM image_r WHERE tid = %s
        """
    res = dbutils.fetch_one(pgsql.TRANS_ID, sql, (short_link_id,))
    if res is None:
        return None
    return res[0]


def _get_nav_link(long_link_id):
    """
    1-双方向
    2-顺方向
    3-逆方向
    """
    sql = f"""
      select st_astext(geom), dir from nav_link where link_id = %s
    """
    link_res = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [long_link_id])
    return link_res


def _get_merge_nav_link(long_link_id):
    sql = f"""
      select st_astext(st_linemerge(st_union(geom))) from nav_link where link_id = ANY(%s) 
    """
    link_res = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, (long_link_id,))
    return link_res


def _get_nav_link2(long_link_id):
    """
    1-双方向
    2-顺方向
    3-逆方向
    """
    sql = f"""
      select dir, link_id from nav_link where link_id = ANY(%s) 
    """
    link_res = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, (long_link_id,))
    return link_res


def _process_data(data):
    # 第一步：按第一列分组
    groups = defaultdict(list)
    for num, value in data:
        groups[num].append(value)

    # 第二步：处理特殊值1（分裂为2和3）
    if 1 in groups:
        values_for_1 = groups.pop(1)  # 取出所有值为1的条目
        for value in values_for_1:
            # 添加到2的组
            groups[2].append(value)
            # 添加到3的组
            groups[3].append(value)

    # 第三步：转换为最终要求的格式
    result = {
        "right": groups.get(2, []),
        "reverse": groups.get(3, [])
    }

    return result


def _get_link_dir_map_data(dir):
    if dir == 2:
        return ['right']
    elif dir == 3:
        return ['reverse']
    else:
        return ['right', 'reverse']


def _get_city_code(cityname):
    sql = f"""
        select provcode from mesh_conf_wkt where cityname='{cityname}'
    """
    res = dbutils.fetch_one(pgsql.BACK_CONFIG, sql)
    return res[0]



def main():
    """
    主函数
    :return:
    """
    collect_data = _get_parking_data()
    for collect_info in collect_data:
        try:
            source_id = collect_info[0]
            cityname = collect_info[1]

            city_code = _get_city_code(cityname)

            road_relations = _get_road_relation_by_bid(source_id)
            # {'link_info': [{'bid': '5356245649698265338', 'link_id': '1655021898', 'node_id': '1556280906',
            # 'orientation': 1, 'point': '12717657.793285,2582329.345521', 'type': 1, 'update_source': None,
            # 'update_time': None}]}
            # 提取所有 link_id
            short_link_ids = [item['link_id'] for item in road_relations['link_info']]
            print(short_link_ids)

            long_link_ids = []
            for link_id in short_link_ids:
                linkid = _get_long_link_id(link_id)
                print(linkid)
                long_link_ids.append(linkid)
            print(long_link_ids)

            all_link_data = _get_nav_link2(long_link_ids)
            format_data = _process_data(all_link_data)
            for dir_str, link_arr in format_data.items():
                if len(link_arr) > 0:
                    print(dir_str, link_arr)
                    geom = _get_merge_nav_link(link_arr)[0]
                    print(geom)
                    if geom.startswith('LINESTRING('):
                        link_str = ",".join(link_arr)
                        _push_data(source_id, link_str, geom, dir_str)
                    else:
                        logging.error(f"合并后的几何图形不是LINESTRING类型: {geom}")
        except Exception as e:
            print(f"Exception: {e}")


if __name__ == '__main__':
    """
    程序入口
    """
    start_time = time.time()
    formatted_start_time = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')
    print("start_time_main:", formatted_start_time)

    main()

    end_time = time.time()
    formatted_end_time = datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')
    print("end_time_main:", formatted_end_time)
    elapsed_time_seconds = end_time - start_time
    elapsed_time_minutes = elapsed_time_seconds / 60
    print(
        "process_time---:",
        "|",
        elapsed_time_seconds,
        "s",
        "|",
        elapsed_time_minutes,
        "min",
    )
