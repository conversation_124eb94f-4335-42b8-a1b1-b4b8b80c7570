"""
熄火点众源采集
"""
import json
import time
import os
import subprocess
import tempfile
import random
import copy
from typing import List, Dict, Tuple
from shapely.geometry import Point, LineString
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
import psutil
import time
import threading
import math
# import datetime

import src.tools.redis_tool as rt
import redis
import requests
# from datetime import datetime
import multiprocessing
from retrying import retry
from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from datetime import datetime, timedelta
from loguru import logger
from itertools import groupby
from src.trajectory.utils import coord_trans
from src.parking.collect.turnoff_point_calc.calc_for_caiji_v5 import run as run_point

# 记录最高资源使用
max_cpu_usage = 0
max_memory_usage = 0
running = True  # 控制监控线程的运行


@retry(stop_max_attempt_number=3, wait_random_min=1000, wait_random_max=5000)
def post_request(session, url, payload):
    """
    post请求
    :return:
    """
    # 设置请求头，告知服务器这是一个 JSON 请求
    try:
        response = session.post(url, json=payload, timeout=3)
        return json.loads(response.text)
    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return None


def _get_parkstore_parking(cityname):
    """
    获取待推送的数据
    """
    sql = f"select distinct strategy_id, ST_AsText(geom) from park_storefront_post_zhongyuan where city = '{cityname}'"
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_parkstore_parking_new(cityname):
    """
    获取待推送的数据
    """
    sql = f"select bid,st_astext(geom) from park_storefront_prod_parking where city='{cityname}' and bid!='';"
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _random_beijing_parking():
    sql = (f"select b.id,st_astext(b.geom) from park_storefront_task "
           f"a join park_storefront_strategy b on a.task_id = b.task_id "
           f"where a.batch in ('beijing_except_haidian_20241208', 'beijing_except_haidian_20241221_quanjing', "
           f"'shanghai_partial_20241114') and b.step = 'VERIFIED' and a.status!='END' order by random() limit 1000;")
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _import_data(source, source_id, parking_geom, batch):
    try:
        with pgsql.get_connection_ttl(pgsql.BACK_CONFIG) as aoi_conn:
            aoi_cursor = aoi_conn.cursor()

            sql1 = (f"SELECT mesh_id, cityname FROM mesh_conf_wkt "
                    f"WHERE ST_Intersects(wkt, ST_GeomFromText('{parking_geom}', 4326)) "
                    f"OR ST_Contains(wkt, ST_GeomFromText('{parking_geom}', 4326)) limit 1")
            aoi_cursor.execute(sql1)
            mesh_info = aoi_cursor.fetchone()
            if mesh_info is None:
                raise Exception(f"{parking_geom} not found in any mesh.")
        mesh_id, cityname = mesh_info[0], mesh_info[1]

        with pgsql.get_connection_ttl(pgsql.POI_CONFIG) as poi_conn:
            poi_cursor = poi_conn.cursor()

            sql = f"""insert into parking_collect_data (source, source_id, parking_geom, mesh_id, cityname, batch)
            values('{source}', '{source_id}', st_geomfromtext('{parking_geom}', 4326), '{mesh_id}', '{cityname}', '{batch}')
                """
            res = poi_cursor.execute(sql)
            logger.info(res)
    except Exception as e:
        logger.error(e)


def _get_parking_points_file(tracks, batch_src, device_no):
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json",
                                     prefix=f"{batch_src}_{device_no}_") as temp_file:
        json.dump(tracks, temp_file)  # 写入 JSON 数据
        temp_file.flush()  # 确保数据写入缓冲区
        os.fsync(temp_file.fileno())  # 强制写入磁盘
        file_path = temp_file.name
        logger.info(f"Processing {file_path}")

        cm = f"py39 calc_for_caji_v4.py"
        script_path = f"cd /home/<USER>/lifan14/parking_point_test/calc_parking_points_distributed_v5_laofan && {cm}"
        command = (f"source ~/fanjiabin/bashrc && {script_path} {file_path} {batch_src}")
        # print(f"Processing {command}")
        try:
            # 调用子进程运行脚本
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            # print(f"Processed {file_path}")
            # print(f"Processed Result: {result.stdout}")
            # 在完成后删除文件
        except Exception as e:
            print(f"Error processing : {e}")


def _cal_parking_points(tracks, batch_src, device_no):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    custom_dir = f"{current_dir}/tempfile"
    os.makedirs(custom_dir, exist_ok=True)
    with tempfile.NamedTemporaryFile(mode='w',
                                     delete=False,
                                     suffix=".json",
                                     prefix=f"{batch_src}_{device_no}_",
                                     dir=custom_dir
                                     ) as temp_file:
        json.dump(tracks, temp_file)  # 写入 JSON 数据
        temp_file.flush()  # 确保数据写入缓冲区
        os.fsync(temp_file.fileno())  # 强制写入磁盘
        file_path = temp_file.name

        try:
            run_point(file_path, batch_src)
        except Exception as e:
            print(f"Error processing : {e}")
        # cm = f"python3 calc_for_caji_v5.py"
        # script_path = f"cd {current_dir}/calc_parking_points_distributed_v5_laofan && {cm}"
        # command = (f"source ~/fanjiabin/bashrc && {script_path} {file_path} {batch_src}")
        # # print(f"Processing {command}")
        # try:
        #     # 调用子进程运行脚本
        #     result = subprocess.run(command, shell=True, capture_output=True, text=True)
        #     # print(f"Processed {file_path}")
        #     # print(f"Processed Result: {result.stdout}")
        # except Exception as e:
        #     print(f"Error processing : {e}")
        os.remove(file_path)


def _bd092gcj(geom):
    """
    将 gcj02 坐标转换为百度墨卡托坐标
    """
    qry = f"select st_astext(mc2gcj(st_geomfromtext('{geom}', 4326)))"
    res = dbutils.fetch_one(pgsql.COMPUTE_CONFIG, qry)

    # point_gcj = _mc2gcj(f'POINT({lon_mc} {lat_mc})')
    # point_gcj_geo = shapely.wkt.loads(point_gcj)
    # lon_gcj_tmp = point_gcj_geo.x
    # lat_gcj_tmp = point_gcj_geo.y

    return res[0]


def _get_tracks(session, device_no, start, end, need_coord_trans=True):
    # start_ms = start * 1000
    pst = time.perf_counter()
    sun = get_sun_angle_limit()
    end_ms = end * 1000
    payload = {"start_time": start, "end_time": end, "device_no": str(device_no)}
    post_result = post_request(session, "http://10.65.35.35:8010/tracksrv/TrackService/FetchTrack", payload)
    if post_result is not None and post_result['status'] == 0 and 'tracks' in post_result and len(
            post_result['tracks']) != 0:
        tracks = post_result['tracks']

        track_sun_e = generate_sun_elevation(tracks[0]['x'], tracks[0]['y'], tracks[0]['gpstime'])
        if track_sun_e < sun:
            logger.info(f"{device_no},{start},{end}, sun_elevation: {track_sun_e}")
            return [], []

        tracks.sort(key=lambda x: x["gpstime"])
        tracks_gcj = copy.deepcopy(tracks)

        for track in tracks:
            if track.get("x") is not None and track.get("y") is not None:
                if need_coord_trans:
                    point_gcj = coord_trans.gcj02_to_wgs84_wkt(f"POINT({track['x']} {track['y']})")
                    point_gcj_geo = shapely.wkt.loads(point_gcj)
                    track["x"], track["y"] = point_gcj_geo.x, point_gcj_geo.y

        filter_tracks_wgs84 = _filter_no_shutdow_track(tracks, end_ms, device_no)
        pet = time.perf_counter()
        logger.info(f"_get_tracks_time: {pet - pst:.4f} 秒")
        return filter_tracks_wgs84, tracks_gcj
    else:
        return [], []


def _add_log(device_no, start, end, remark, parking_collect_data_id=0, track='LINESTRING EMPTY'):
    try:
        with pgsql.get_connection_ttl(pgsql.POI_CONFIG) as poi_conn:
            poi_cursor = poi_conn.cursor()

            sql = f"""insert into parking_collect_log (device_no, start_time, end_time, remark, 
            parking_collect_data_id, track_geom) values('{device_no}', {start}, {end},
             '{remark}', {parking_collect_data_id}, st_geomfromtext('{track}', 4326))
                """
            logger.info(sql)
            res = poi_cursor.execute(sql)
        logger.info(res)
    except Exception as e:
        logger.error(e)


def _filter_mesh_id(mesh_id_keys, time_slice, machine_index, total_machines):
    """
    根据机器 ID 分配 mesh_id，并过滤出需要处理的 mesh_id。
    :param mesh_id_keys: list, 原始 mesh_id 列表
    :param time_slice: str, 时间片标识
    :param machine_id: str, 当前机器的标识（如 "de16"）
    :return: list, 当前机器需要处理的 mesh_id 列表
    """
    pst = time.perf_counter()
    # 提取 mesh_id
    mesh_ids = []
    for mesh_id in mesh_id_keys:
        parts = mesh_id.split('_')
        if len(parts) >= 4:
            mesh_ids.append(parts[3])  # 提取 mesh_id 部分
    print(f"{machine_index} before mesh_ids: {len(mesh_ids)}, total_machines: {total_machines}")
    # 筛选出当前机器需要处理的 mesh_id
    assigned_mesh_indices = [
        i for i in range(len(mesh_ids))
        if i % total_machines == machine_index
    ]
    assigned_mesh_ids = [mesh_ids[i] for i in assigned_mesh_indices]
    print(f"{machine_index} after mesh_ids: {len(assigned_mesh_ids)}, total_machines: {total_machines}")

    # 按批次查询数据库
    useful_mesh_ids = set()
    batch_size = 2000
    # lock_batch 锁定 batch, 后续在当前 batch 中查询
    lock_batch = f"{time_slice}_{machine_index}"

    with pgsql.get_connection_ttl(pgsql.POI_CONFIG) as conn:
        cursor = conn.cursor()
        for i in range(0, len(assigned_mesh_ids), batch_size):
            batch_mesh_ids = assigned_mesh_ids[i: i + batch_size]
            # update_sql = f"""UPDATE parking_collect_data SET batch = '{lock_batch}'
            #  WHERE status in('INIT') and mesh_id IN ({','.join(f"'{m}'" for m in batch_mesh_ids)})"""
            # update_sql = f"""UPDATE parking_collect_data SET batch = '{lock_batch}'
            #  WHERE status in('INIT') and mesh_id IN ({','.join(f"'{m}'" for m in batch_mesh_ids)})"""
            # cursor.execute(update_sql)

            select_sql = f"""
                SELECT id FROM parking_collect_data
                WHERE status = 'INIT' AND mesh_id IN ({','.join(f"'{m}'" for m in batch_mesh_ids)})
                FOR UPDATE SKIP LOCKED
            """
            cursor.execute(select_sql)
            rows = cursor.fetchall()
            ids_to_update = [str(row[0]) for row in rows]

            if ids_to_update:
                update_sql = f"""
                    UPDATE parking_collect_data
                    SET batch = '{lock_batch}'
                    WHERE id IN ({','.join(ids_to_update)})
                """
                cursor.execute(update_sql)

    # 记录查询结果
    sql = f"""SELECT mesh_id FROM parking_collect_data WHERE batch = '{lock_batch}'"""
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    for row in res:
        useful_mesh_ids.add(row[0])

    # 重新组装原始格式
    result = [
        f"zhongyuan_active_mesh_{mesh_ids[i]}_device_{mesh_id_keys[i].split('_')[-1]}"
        for i in assigned_mesh_indices
        if mesh_ids[i] in useful_mesh_ids
    ]
    pet = time.perf_counter()
    logger.info(f"__filter_mesh_id_time: {pet - pst:.4f} 秒")
    return result


def _filter_no_shutdow_track(tracks, end_ms, device_no):
    # 获取最后一个轨迹点的 gpstime
    last_gpstime = tracks[-1]["gpstime"]
    # 检查是否满足条件，如果最后一个点的时间与 end 相差不到 10 秒，丢弃该设备的所有轨迹
    # gap = (end_ms - last_gpstime)
    gap = (end_ms - last_gpstime) / 1000
    # logger.info(f"{device_no} time gap: {end_ms} , {last_gpstime}")
    if gap <= 10:
        logger.error(f"{device_no}: {end_ms} , {last_gpstime} , 丢弃, 相差：{gap}")
        return []  # 丢弃该设备的轨迹
    return tracks


def _get_device_no_by_mesh_id(mesh_id):
    with rt.RedisTool('aoi') as rt_client:
        try:
            device_no_list = rt_client.redis_conn.smembers(mesh_id)
            # logger.info(f"{mesh_id}：{len(device_no_list)}")
            return list(device_no_list)
        except (redis.ConnectionError, ConnectionResetError) as e:
            print(f"Error: {e}")
            return []


def _generate_linestring(track_data):
    """
    将轨迹数据转换为 LINESTRING 格式，并去重重复点
    :param track_data: list, 包含多个轨迹点的字典列表
    :return: WKT 格式的 LINESTRING 或 None
    """
    # 过滤掉没有坐标的数据
    # valid_points = [(t["x"], t["y"]) for t in track_data if t["x"] and t["y"]]
    # # 去重相同的 (x, y) 坐标点，保持顺序
    # unique_points = [key for key, _ in groupby(valid_points)]
    # if len(unique_points) < 2:  # LINESTRING 至少需要两个点
    #     return None
    # linestring = LineString(unique_points)
    # return linestring.wkt
    # 过滤掉没有坐标的数据
    valid_points = [(t["x"], t["y"]) for t in track_data if t["x"] and t["y"]]
    if len(valid_points) < 2:  # LINESTRING 至少需要两个点
        return None
    linestring = LineString(valid_points)
    return linestring.wkt


# 检查面和轨迹是否相交
def _check_track_intersection(tracks, time_slice):
    track_line_string = _generate_linestring(tracks)
    if track_line_string is None:
        return False, ''
    sql = (f"SELECT count(*) FROM parking_collect_data "
           f"WHERE status in('INIT') AND batch = '{time_slice}' "
           f"AND ST_Intersects(parking_geom, ST_GeomFromText('{track_line_string}', 4326));")
    if dbutils.fetch_all(pgsql.POI_CONFIG, sql)[0] > 0:
        # logger.info(f"{track_line_string}")
        return True, track_line_string
    else:
        return False, track_line_string


def _check_track_intersection_v2(tracks, time_slice, device_no, start, end):
    track_line_string = _generate_linestring(tracks)
    if track_line_string is None:
        # _add_log(device_no, start, end, "_generate_linestring返回空")
        return False, ''
    try:
        # conn = pgsql.get_connection(pgsql.POI_CONFIG)
        # cursor = conn.cursor()
        # sql = (f"SELECT id FROM parking_collect_data "
        #        f"WHERE status in('INIT') AND batch = '{time_slice}' "
        #        f"AND ST_Intersects(parking_geom, ST_GeomFromText('{track_line_string}', 4326));")
        sql = (f"SELECT id FROM parking_collect_data "
               f"WHERE status in('INIT') AND batch = '{time_slice}' "
               f"AND ST_Intersects(parking_geom, ST_GeomFromText('{track_line_string}', 4326));")
        ins_park_ids = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
        if len(ins_park_ids) <= 0:
            return False, ''
        for row in ins_park_ids:
            parking_collect_data_id = row[0]
            _add_log(device_no, start, end, "轨迹面相交", parking_collect_data_id, track_line_string)

        return True, track_line_string
    except Exception as e:
        print(f"插入数据失败: {e}")
        return False, ''


def _check_track_intersection_test(tracks, time_slice):
    track_line_string = _generate_linestring(tracks)
    if track_line_string is None:
        return False, ''
    sql = (f"SELECT count(*) FROM parking_collect_data "
           f"WHERE batch = '{time_slice}' "
           f"AND ST_Intersects(parking_geom, ST_GeomFromText('{track_line_string}', 4326));")
    if dbutils.fetch_one(pgsql.POI_CONFIG, sql)[0] > 0:
        # logger.info(f"{track_line_string}")
        return True, track_line_string
    else:
        return False, track_line_string


def monitor_resources():
    """
    持续监控 CPU 和内存使用率（包括所有子进程）
    """
    global running
    process = psutil.Process()  # 获取当前主进程
    max_cpu = 0
    max_memory = 0

    while running:
        # 获取当前进程及所有子进程
        cpu_usage = process.cpu_percent()  # 当前进程 CPU
        memory_usage = process.memory_info().rss / 1024 / 1024  # MB

        # 遍历所有子进程并累加资源占用
        for child in process.children(recursive=True):  # 递归获取子进程
            cpu_usage += child.cpu_percent()  # 子进程 CPU
            memory_usage += child.memory_info().rss / 1024 / 1024 / 1024  # 子进程内存

        max_cpu = max(max_cpu, cpu_usage)
        max_memory = max(max_memory, memory_usage)

        time.sleep(1)  # 每秒采样一次

    print(f"\nMax CPU Usage: {max_cpu}% | Max Memory Usage: {max_memory:.2f} GB")


# 进程池内处理单个 mesh_id 的任务
def _process_mesh_id(mesh_id, start, end, time_slice):
    device_no_list = _get_device_no_by_mesh_id(mesh_id)
    logger.info(f"图幅：{mesh_id}, 设备个数：{len(device_no_list)}")
    results = []
    req_session = requests.Session()
    for device_no in device_no_list:
        track_wgs84, track_gcj = _get_tracks(req_session, device_no, start, end)
        # logger.info(f"{device_no},{start},{end}")
        if len(track_wgs84) != 0 and len(track_gcj) != 0:
            # is_intersect, line_string = _check_track_intersection(track, time_slice)
            is_intersect, line_string = _check_track_intersection_v2(track_gcj, time_slice, device_no, start, end)
            # logger.info(f"{device_no}：{is_intersect}")
            if is_intersect:
                # batch_src = f"parking_{time_slice}"
                batch_src = f"{time_slice}_{start}_{end}"
                _cal_parking_points(track_wgs84, batch_src, device_no)
                # logger.info(f"{time_slice}, {device_no}, {batch_src}, {line_string}")
                logger.info(f"{time_slice},{start},{end},{device_no},{batch_src}")
                results.append(device_no)
            # else:
            #     logger.info(f"{device_no}, 无相交面")

    return results


def _process_all_mesh_ids(mesh_ids, start, end, time_slice):
    # 创建进程池
    with multiprocessing.Pool(processes=250) as pool:
        # 使用 starmap 并行处理多个 mesh_id
        results = pool.starmap(_process_mesh_id, [(mesh_id, start, end, time_slice) for mesh_id in mesh_ids])
    # 返回所有结果（按设备号和过滤后的轨迹）
    return [result for sublist in results for result in sublist]  # 将子进程结果扁平化


def _run_import():
    # city_name = '北京市'
    # data = _get_parkstore_parking_new(city_name)
    data = _random_beijing_parking()
    for item in data:
        _import_data("parkstore", item[0], item[1], "20250301")


def _get_mesh_list(time_slice: int) -> List[str]:
    """从 Redis 获取 mesh_list"""
    with rt.RedisTool('aoi') as rt_client:
        active_mesh_key = f"zhongyuan_active_mesh_{time_slice}"
        mesh_list = rt_client.redis_conn.smembers(active_mesh_key)
    return list(mesh_list)


def _add_push_batch(data):
    with rt.RedisTool('aoi') as rt_client:
        zhongyuan_batch_src_queue_key = f"zhongyuan_batch_src_queue"
        rt_client.redis_conn.lpush(zhongyuan_batch_src_queue_key, data)


def main():
    """
    主函数
    :return:
    """
    time_slice = int(time.time() / 600) - 1
    start_time = (int(time.time() / 600) - 2) * 600
    end_time = (int(time.time() / 600) - 1) * 600
    logger.info(f"{time_slice}：{start_time}, {end_time}")
    # active_mesh_key = f"zhongyuan_active_mesh_{time_slice}"
    hostname = os.uname().nodename
    logger.info(f"hostname: {hostname}")
    # 所有机器的标识
    # machines = ["gzbh-ns-map-de16.gzbh.baidu.com", "gzbh-ns-map-de20.gzbh.baidu.com",
    # "gzbh-ps-beehive-agent179840.gzbh.baidu.com"]
    # machines = ["gzbh-ns-map-de16.gzbh.baidu.com"]
    # machines = ["gzbh-ps-beehive-agent179840.gzbh.baidu.com"]
    machines = ["gzbh-sys-rpm1945896f5.gzbh.baidu.com", "gzbh-ps-beehive-agent179840.gzbh.baidu.com"]
    # 当前机器的索引
    machine_index = machines.index(hostname)
    # 机器总数
    total_machines = len(machines)

    try:
        # 获取 mesh_list
        mesh_list = _get_mesh_list(time_slice)
        # 过滤 mesh_list - > 不在投放池中的mesh_id过滤
        logger.info(f"mesh_count_before: {len(mesh_list)}")
        mesh_ids = _filter_mesh_id(mesh_list, time_slice, machine_index, total_machines)
        logger.info(f"mesh_count_after: {len(mesh_ids)}")

        # mesh_ids = random.sample(mesh_ids, 5)
        print(f"hostname:{hostname}({machine_index}), mesh_ids: {len(mesh_ids)}")
        new_time_slice = f"{time_slice}_{machine_index}"
        after_filtered_tracks = _process_all_mesh_ids(mesh_ids, start_time, end_time, new_time_slice)
        logger.info(f"after_filtered_tracks: {len(after_filtered_tracks)}")

        if len(after_filtered_tracks) > 0:
            batch_src = f"{new_time_slice}_{start_time}_{end_time}"
            _add_push_batch(batch_src)

    except (redis.ConnectionError, ConnectionResetError) as e:
        print(f"Error: {e}")


def _get_track(device_no, start_time_str, end_time_str):
    start_dt = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
    end_dt = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")

    current_time = start_dt

    while current_time < end_dt:
        start_time = int(current_time.timestamp())  # 转换为时间戳
        start_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
        end_time = int((current_time + timedelta(minutes=10)).timestamp())
        end_time_str = (current_time + timedelta(minutes=10)).strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"{device_no}：{start_time_str}, {end_time_str}")
        # tracks = _get_tracks(device_no, start_time, end_time, False)
        tracks = _get_tracks(device_no, start_time, end_time)
        if len(tracks) != 0:
            # is_intersect, line_string = _check_track_intersection_test(tracks, '2901199')
            track_line_string = _generate_linestring(tracks)
            if track_line_string is not None:
                logger.info(f"{device_no}：{track_line_string}")
                # _get_parking_points_file(tracks, 'test', device_no)

        # 递增 10 分钟
        current_time += timedelta(minutes=10)


def generate_sun_elevation(inputlon, inputlat, gpstime):
    """获取轨迹太阳高度角
    """
    lat = inputlat
    lon = inputlon

    timevalue = time.localtime(int(gpstime / 1000))

    year = timevalue[0]
    month = timevalue[1]
    day = timevalue[2]
    hour = timevalue[3]
    minute = timevalue[4]
    second = timevalue[5]

    daynum = (datetime(year, month, day) - datetime(year, 1, 1)).days
    # Bourges
    n0 = 78.801 + int(0.2422 * (year - 1969)) - int(0.25 * (year - 1969))
    t = daynum - 1 - n0
    w = 2 * math.pi / 365.2422
    dec = 0.3723 + 23.2567 * math.sin(w * t) + 0.1149 * math.sin(2 * w * t) - 0.1712 * math.sin(
        3 * w * t) - 0.7580 * math.cos(w * t) + 0.3656 * math.cos(2 * w * t) + 0.0201 * math.cos(3 * w * t)
    dec = dec * math.pi / 180
    # Wloof min
    x = 2 * math.pi * (daynum - 1) / 365.242
    eot = 0.258 * math.cos(x) - 7.416 * math.sin(x) - 3.648 * math.cos(2 * x) - 9.228 * math.sin(2 * x)
    # time_t = (hour * 60 + minute + second / 60) - eot + 12 * 60 / 180 * lon
    time_t = (hour * 60 + minute + second / 60) + eot + 4 * (lon - 120)
    if time_t > 24 * 60:
        time_t = time_t - 24 * 60
    # hourAngle = (time_t - 12 * 60) / (12 * 60) * math.pi - math.pi * 2 / 3
    hourAngle = ((time_t / 60 - 12) * 15) / 180 * math.pi

    lat = lat * math.pi / 180
    alt = math.sin(lat) * math.sin(dec) + math.cos(lat) * math.cos(dec) * math.cos(hourAngle)
    alt = math.asin(alt)
    alt = alt * 180 / math.pi

    return alt


def get_sun_angle_limit(cur_day=None, min_angle_limit=-3, max_angle_limit=3):
    """根据日期算出当天的太阳高度角限制, 夏至日-3度, 冬至日3度"""
    if not cur_day:
        cur_day = datetime.today()
    cur_year = cur_day.year
    last_year = cur_year - 1
    next_year = cur_year + 1
    # 获取今年的夏至, 今年的冬至, 去年的冬至, 明年的夏至
    cur_summer_solstice = datetime(year=cur_year, month=6, day=22)
    cur_winter_solstice = datetime(year=cur_year, month=12, day=22)
    last_winter_solstice = datetime(year=last_year, month=12, day=22)
    next_summer_solstice = datetime(year=next_year, month=6, day=22)

    if last_winter_solstice <= cur_day < cur_summer_solstice:
        # 今天位于今年夏至前
        day_num = (cur_summer_solstice - cur_day).days
        total_days = (cur_summer_solstice - last_winter_solstice).days
    elif cur_summer_solstice <= cur_day <= cur_winter_solstice:
        # 今天位于今年夏至-今年冬至
        day_num = (cur_day - cur_summer_solstice).days
        total_days = (cur_winter_solstice - cur_summer_solstice).days
    else:
        # 今天位于今年冬至之后
        day_num = (next_summer_solstice - cur_day).days
        total_days = (next_summer_solstice - cur_winter_solstice).days
    # 动态计算当天太阳角限制
    angle_limit = min_angle_limit + (max_angle_limit - min_angle_limit) * (day_num * 1.0 / total_days)
    return angle_limit


def filter_sun_elevation():
    """过滤太阳高度角
    """
    SUN_ELEVATION_PER = get_sun_angle_limit()

    payload = {"start_time": 1741739440, "end_time": 1741740040, "device_no": str('e22017f759ab7d327dc8ba7e39987c02')}
    pst = time.perf_counter()
    post_result = post_request("http://10.11.131.11:8456/tracksrv/TrackService/FetchTrack", payload)
    pet = time.perf_counter()
    logger.info(f"_get_tracks_api_time: {pet - pst:.4f} 秒")
    if post_result is not None and post_result['status'] == 0 and 'tracks' in post_result and len(
            post_result['tracks']) != 0:
        track = post_result['tracks'][0]
        track_sun_e = generate_sun_elevation(track['x'], track['y'], track['gpstime'])
        if track_sun_e < SUN_ELEVATION_PER:
            return False
        return True


if __name__ == "__main__":
    """
    主函数
    :return:
    """
    # _run_import()
    # mysql -h10.70.29.74 -P6082 -uzhaoxiaolong_r -poffline_read vdust --default-character-set=utf8
    # select count(*) from task t, match_task mt, matches m where t.update_time>'2025-01-18' and t.task_source=45
    # and mt.task_id=t.task_id and mt.match_id=m.match_id and m.match_status=21;
    # 45: 熄火点
    # 32: 面投放
    # psql -U traj_feature_rw -d traj_feature_db -W traj_feature_rw -p 9432 -h 10.56.135.223
    # psql -U dest_traj_se_rw -d dest_traj -W erwmbdpm -p 7432 -h 10.162.138.153
    # psql -U master_back_se_ro -d master_back -W mapread -p 5432 -h 10.162.197.147
    start_time = time.time()
    formatted_start_time = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')
    print("start_time_main:", formatted_start_time)

    main()

    end_time = time.time()
    formatted_end_time = datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')
    print("end_time_main:", formatted_end_time)
    elapsed_time_seconds = end_time - start_time
    elapsed_time_minutes = elapsed_time_seconds / 60
    print(
        "process_time---:",
        "|",
        elapsed_time_seconds,
        "s",
        "|",
        elapsed_time_minutes,
        "min",
    )
