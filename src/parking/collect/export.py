"""
根据差分结果回捞人工核实结果
"""
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests
import csv
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
from shapely.ops import transform
from shapely.ops import unary_union
import pyproj
import random

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from shapely import Polygon, LineString

from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER


def _get_total_push_count(batch):
    sql = f"""
    select count(*) from park_points_push where created_at>='2025-01-15 10:00:00' and batch = '{batch}';
    """
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)[0]


def _get_aoi_match_detail(business_id):
    sql = f"""
    select a.strategy_id,st_astext(b.geom),a.point_time,a.device_no,st_astext(a.point_geom),
    st_astext(a.parking_traj_geom),a.batch,a.bid,c.std_tag 
    from park_points_push a left join aoi_post_zhongyuan b on a.bid=b.bid left join poi c on a.bid=c.bid 
    where a.created_at>'2025-01-11 10:00:00' and a.response_info like '%{business_id}%';
    """

    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _get_park_match_detail_by_batch(business_id , batch):
    if batch == '20250110_bj_parkstore':
        sql = f"""
            select a.id,a.strategy_id,st_astext(b.geom),a.point_time,a.device_no,st_astext(a.point_geom),
            st_astext(a.parking_traj_geom),a.batch,a.bid
            from park_points_push a left join park_storefront_post_zhongyuan b on a.strategy_id=b.strategy_id 
            where a.batch='{batch}' and a.created_at>'2025-01-15 10:00:00' and a.response_info like '%{business_id}%';
            """
    elif batch == '20250110_bj_aoi':
        sql = f"""
        select a.id,a.strategy_id,st_astext(b.geom),a.point_time,a.device_no,st_astext(a.point_geom),
        st_astext(a.parking_traj_geom),a.batch,a.bid
        from park_points_push a left join park_storefront_post_zhongyuan b on a.strategy_id=b.strategy_id 
        where a.batch='{batch}' and a.created_at>'2025-01-15 10:00:00' and a.response_info like '%{business_id}%';
        """
    else:
        sql = f"""
        select a.id,a.strategy_id,st_astext(b.geom),a.point_time,a.device_no,st_astext(a.point_geom),
        st_astext(a.parking_traj_geom),a.batch,a.bid
        from park_points_push a left join park_storefront_post_zhongyuan b on a.strategy_id=b.strategy_id 
        where a.created_at>'2025-01-15 10:00:00' and a.response_info like '%{business_id}%';
        """
    # print(sql)
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _get_park_match_detail_by_id(id):
    sql = f"""
        select a.strategy_id,st_astext(b.geom),a.point_time,a.device_no,st_astext(a.point_geom),
        st_astext(a.parking_traj_geom),a.batch,a.bid
        from park_points_push a left join park_storefront_post_zhongyuan b on a.strategy_id=b.strategy_id 
        where a.id={id};
        """
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _get_park_match_detail(business_id):
    sql = f"""
    select a.strategy_id,st_astext(b.geom),a.point_time,a.device_no,st_astext(a.point_geom),
    st_astext(a.parking_traj_geom),a.batch,a.bid
    from park_points_push a left join park_storefront_post_zhongyuan b on a.strategy_id=b.strategy_id 
    where a.created_at>'2025-01-15 10:00:00' and a.response_info like '%{business_id}%';
    """
    # print(sql)
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _get_park_match_detail_all(business_id):
    sql = f"""
    select a.id,a.strategy_id,st_astext(b.geom),a.point_time,a.device_no,st_astext(a.point_geom),
    st_astext(a.parking_traj_geom),a.batch,a.bid
    from park_points_push a left join park_storefront_post_zhongyuan b on a.strategy_id=b.strategy_id 
    where a.created_at>'2025-01-11 10:00:00' and a.response_info like '%{business_id}%';
    """
    # print(sql)
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _get_zhongyuan_match_detail(match_id):
    # http://gzxj-zhongyuan0004.gzxj:8888/trace/match?match_id=7193446943
    url = f"http://gzxj-zhongyuan0004.gzxj:8888/trace/match?match_id={match_id}"
    response = requests.get(url)
    data = json.loads(response.content)
    return data


def _get_track_detail(url):
    response = requests.get(url)
    data = json.loads(response.content)
    return data


if __name__ == '__main__':
    park_total = _get_total_push_count('20250110_bj_parkstore')
    print("park_total: ", park_total)

    aoi_total = _get_total_push_count('20250110_bj_aoi')
    print("aoi_total: ", aoi_total)
    export_file = []
    park_total_zy = 0
    aoi_total_zy = 0

    park_match_zy = 0
    aoi_match_zy = 0

    all_push_data = []
    all_random_push_id = []
    with open('/home/<USER>/fanjiabin/aoi-ml/output/src/parking/collect/0118_collect_data.txt', 'r') as file:
        for line in file:
            business_id, match_id, status = line.strip().split(",")

            exist = _get_park_match_detail_by_batch(business_id, '20250110_bj_parkstore')
            if exist is None:
                continue

            temp_data = {
                'business_id': business_id,
                'match_id': match_id,
                'status': status,
            }
            all_push_data.append(temp_data)

    num_to_select = 20  # 要获取的数量
    assert isinstance(all_push_data, list), "all_push_data 必须是列表"
    assert all(isinstance(item, dict) for item in all_push_data), "all_push_data 的每个元素必须是字典"
    # 检查 num_to_select 是否合理
    num_to_select = min(num_to_select, len(all_push_data))
    # 生成随机索引并检查类型
    random_indices = random.sample(range(len(all_push_data)), num_to_select)
    assert all(isinstance(index, int) for index in random_indices), "随机索引必须是整数"
    # 获取结果
    random_items = [(index, all_push_data[index]) for index in random_indices]

    for index, item in random_items:
        business_id = item.get('business_id')
        match_id = item.get('match_id')
        status = item.get('status')

        detail = _get_park_match_detail(business_id)
        if detail is None:
            continue
        strategy_id = detail[0]
        parking_geom = detail[1]
        point_time = detail[2]
        device_no = detail[3]
        point_geom = detail[4]
        parking_traj_geom = detail[5]
        batch = detail[6]
        bid = detail[7]
        # std_tag = detail[8]
        if strategy_id <= 0:
            continue

        long_geom = "LINESTRING EMPTY"
        pic_list = ""
        try:
            match_detail = _get_zhongyuan_match_detail(match_id)
            if match_detail is None:
                long_geom = "LINESTRING EMPTY"
            if match_detail['status'] == 0 and len(match_detail['data']['pic_list']) == 0:
                pic_list = ""
            else:
                pic_list = f"http://gzxj-zhongyuan0004.gzxj:8888/trace/match?match_id={match_id}"
                track_list = _get_track_detail(match_detail['data']['geo_list'][0]['geo_url'])
                coordinates = [(track['longitude'], track['latitude']) for track in track_list['tracks']]
                line = LineString(coordinates)
                # 将 LineString 对象转换为 WKT 格式
                long_geom = line.wkt
        except Exception as e:
            print(e)
            continue

        business_id_str = f"`{business_id}"
        bid_str = f"`{bid}"
        export_file.append([
            index, device_no, parking_geom, point_time, parking_traj_geom, long_geom,
            status, business_id_str, match_id, pic_list
        ])
        # export_file.append([
        #     device_no, bid_str, std_tag, parking_geom, point_time, parking_traj_geom, long_geom, status,
        #     business_id_str, match_id, pic_list
        # ])
        line = (f"{index}, {device_no}, {parking_geom}, {point_time}, {parking_traj_geom}, "
                f"{long_geom}, {status}, {business_id_str}, {match_id}, {pic_list}")
        # line = (f"{device_no}, {bid_str}, {std_tag}, {parking_geom}, {point_time}, {parking_traj_geom}, "
        #         f"{long_geom}, {status}, {match_id}, {pic_list}")
        print(line)

    file_path = '/home/<USER>/fanjiabin/aoi-ml/output/src/parking/collect/random_collect0118_2.csv'
    # 使用 with 语句打开文件
    with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
        writer = csv.writer(file, delimiter='\t')
        # 写入每一行数据
        for row in export_file:
            writer.writerow(row)

    print("数据已成功导出.")

    # with open('/home/<USER>/fanjiabin/aoi-ml/output/src/parking/collect/0111_collect_data.txt', 'r') as file:
    #     # 逐行读取文件
    #     # zhaohui_total = 0
    #     # match_total = 0
    #     for line in file:
    #         # print(line)
    #         # 20250111071031135507007, 7193447242, 21
    #
    #         business_id, match_id, status = line.strip().split(",")
    #         # print(business_id, match_id, status)
    #         # zhaohui_total = zhaohui_total + 1
    #         # if int(status) >= 21:
    #         #     match_total = match_total + 1
    #         # cuid, 面, 时间戳， 熄火点最后一点轨迹串，小龙轨迹串，任务状态，任务ID，匹配ID, 图片串（没有的全部留空）,
    #         detail = _get_park_match_detail(business_id)
    #         # detail = _get_aoi_match_detail(business_id)
    #         if detail is None:
    #             continue
    #         strategy_id = detail[0]
    #         parking_geom = detail[1]
    #         point_time = detail[2]
    #         device_no = detail[3]
    #         point_geom = detail[4]
    #         parking_traj_geom = detail[5]
    #         batch = detail[6]
    #         bid = detail[7]
    #         # std_tag = detail[8]
    #         # if strategy_id <= 0:
    #         #     continue
    #         # if bid == '':
    #         #     continue
    #         if batch == '20250110_bj_parkstore':
    #             park_total_zy = park_total_zy + 1
    #             if int(status) >= 21:
    #                 park_match_zy = park_match_zy + 1
    #         if batch == '20250110_bj_aoi':
    #             aoi_total_zy = aoi_total_zy + 1
    #             if int(status) >= 21:
    #                 aoi_match_zy = aoi_match_zy + 1
    #
    #     print(f"停车场投放: {park_total} , 停车场回收: {park_total_zy}, park_match_zy: {park_match_zy}")
    #     print(f"aoi投放: {aoi_total} , aoi回收: {aoi_total_zy}, aoi_match_zy: {aoi_match_zy}")


