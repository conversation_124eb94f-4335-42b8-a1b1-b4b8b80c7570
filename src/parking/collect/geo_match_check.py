"""
根据差分结果回捞人工核实结果
"""
from shapely.wkt import loads
from scipy.spatial.distance import directed_hausdorff
import numpy as np


def hausdorff_distance(u, v):
    distance_ab = directed_hausdorff(u, v)[0]
    distance_ba = directed_hausdorff(v, u)[0]
    return max(distance_ab, distance_ba)


if __name__ == '__main__':
    # 定义两个 WKT LINESTRING
    # geo1_wkt = "LINESTRING(116.273634204162 39.8919901314158,116.273634188143 39.8918611253788,116.273643177351 39.8917311253837,116.273653167778 39.8916061262994,116.273653151635 39.8914761202042,116.273644130634 39.8913501081972,116.273644114987 39.8912241022822,116.273644099838 39.8911020965515,116.273654089645 39.8909720972145,116.273654074496 39.8908500914768,116.273643052429 39.890725078144,116.273643037652 39.8906060725406,116.273644024464 39.8904950679881,116.273644010805 39.8903850628027,116.273643997146 39.8902750576145,116.273643984108 39.8901700526596,116.27363396587 39.8900710412157,116.273633953701 39.8899730365865,116.273643949216 39.8898890393864,116.273643941145 39.8898240363135,116.27363392924 39.889776027274,116.273628920185 39.8897270215728,116.273598897789 39.8896899995401,116.273563874717 39.8896709750191,116.273513843861 39.8896599408286,116.273463813497 39.8896519068692,116.273408780831 39.889647869844,116.273339740676 39.8896478237844,116.273273702976 39.8896517800756,116.273193656956 39.8896517270745,116.273118613593 39.8896476774044,116.273043570126 39.8896406277935,116.272943513748 39.8896405624004,116.272853464037 39.8896455040893,116.272733398772 39.8896554269503,116.27263434492 39.8896593634981,116.272504275011 39.8896652807466,116.272378207926 39.8896702010818,116.272248139614 39.8896761195258,116.27212407537 39.8896830423609,116.272013018579 39.88968997379,116.271903964025 39.8897019071292,116.271813919547 39.8897128524625,116.271732880111 39.8897248036117,116.271683856873 39.8897357743519,116.271638834876 39.8897397472683,116.271607819934 39.8897437287121,116.271593812976 39.8897437202578,116.271569801941 39.8897507061124,116.27156379959 39.8897557027329,116.271543790938 39.8897656911623,116.271532786746 39.8897756850175,116.271523783547 39.8897856800791,116.271508777023 39.8897926713977,116.271493770634 39.8898006627718,116.27145375187 39.8898076391313,116.271433743076 39.8898156275455,116.27140973235 39.889823613586)"
    # geo2_wkt = "LINESTRING(116.273347 39.895374, 116.273367 39.895279, 116.2734 39.895178, 116.27343 39.895069, 116.273463 39.894951, 116.273495 39.894828, 116.273527 39.894706, 116.273562 39.894584, 116.273595 39.894459, 116.273626 39.894329, 116.273652 39.894199, 116.273669 39.894063, 116.273679 39.893918, 116.273681 39.893768, 116.273675 39.893625, 116.273675 39.893476, 116.273679 39.893316, 116.273681 39.893155, 116.273677 39.892996, 116.273675 39.892835, 116.273669 39.892682, 116.273659 39.892534, 116.273653 39.892379, 116.273653 39.892226, 116.273649 39.892081, 116.273643 39.891941, 116.273647 39.891812, 116.273657 39.891684, 116.273663 39.891557, 116.273659 39.891429, 116.273653 39.891303, 116.273653 39.891178, 116.273657 39.891053, 116.273663 39.890926, 116.273659 39.890803, 116.273653 39.890681, 116.273653 39.890565, 116.273653 39.890454, 116.273653 39.890344, 116.273653 39.890237, 116.273649 39.890134, 116.273643 39.890035, 116.273648 39.889943, 116.273653 39.889867, 116.273649 39.889809, 116.273641 39.889761, 116.273625 39.889721)"

    geo1_wkt = "LINESTRING(116.703696304366 39.9144367241727,116.703696303869 39.914432724001,116.703706330266 39.9144397409647,116.703716357281 39.9144517581412,116.703726385165 39.9144707756163,116.703726384544 39.9144657754016,116.703716358152 39.9144587584417,116.703706331012 39.9144457412223,116.703700314453 39.9144357307953,116.703695302684 39.9144437228069,116.703686279708 39.9144437078082,116.703696303869 39.914432724001,116.703696302502 39.9144217235286,116.703716351066 39.9144017559942,116.703726375841 39.9143957723959,116.703735398809 39.9143957873877,116.703726374598 39.9143857719664,116.703706326412 39.9144087396336,116.70371635032 39.9143957557365,116.703701315265 39.9144217318605,116.703686279708 39.9144437078082,116.703695304176 39.9144557233221,116.703706334119 39.9144707422957,116.703716360514 39.9144777592575,116.7037263879 39.9144927765608,116.703735412857 39.9145087922394,116.703762483613 39.9145238378493,116.703795569406 39.9145368933469,116.703845698639 39.9145509771499,116.703885801212 39.9145560438916,116.703945955809 39.9145701442256,116.704021147506 39.9145762690521,116.704090324306 39.9145863839889,116.704165517238 39.9146045091219,116.704225672433 39.9146266094759,116.704285829033 39.9146607102748,116.704315909913 39.9146987615488,116.70434198261 39.9147528068756,116.70434599935 39.9148058157644,116.704346006065 39.9148598180799,116.704342002008 39.9149088135648,116.70433599387 39.9149658060838,116.70431595194 39.9150367760404,116.704295909628 39.9151047458599,116.704280880002 39.9151727239471,116.704249810732 39.9152486758817,116.704215734439 39.9153296230416,116.704185668035 39.9154085767208,116.704165626904 39.9154865469139,116.704145585887 39.9155655171412,116.704119530599 39.9156524777524,116.70408946624 39.9157484320987,116.704056394226 39.9158443814478,116.704036356017 39.9159463526113,116.70400528955 39.9160463054157,116.703985251441 39.9161492765992,116.703960201087 39.9162562396376,116.703936153264 39.9163632043241,116.703916116366 39.9164761759028,116.703892069513 39.9165911409066,116.703855993054 39.9167140862793,116.703825930744 39.9168280412257,116.703785842534 39.9169389793791,116.703761794985 39.9170489441028,116.703735741946 39.9171559053548,116.703725727491 39.917244892483,116.70370568813 39.9173388631593,116.703685648014 39.9174268335717,116.703669614745 39.9174878094959,116.703645560665 39.9175457719488,116.703631533361 39.9176137514924,116.703605475393 39.9176817110154,116.703586433557 39.9177356816104,116.703570397516 39.9177746565678,116.703566390649 39.9178016510387,116.703546341391 39.9178166182939,116.703556368819 39.9178316356224,116.703565394931 39.9178566517045,116.703586450715 39.9178736874689,116.703615528543 39.9179037371208,116.703645608036 39.917926788127,116.703695738446 39.917948872406,116.703730829897 39.9179659314407,116.703765919206 39.9179659897308)"
    geo2_wkt = "LINESTRING (116.703678 39.914401, 116.703678 39.914401, 116.703679 39.914401, 116.703679 39.914401, 116.703687 39.914399, 116.703695 39.914398, 116.703704 39.914396, 116.703713 39.914394, 116.703721 39.914392, 116.70373 39.91439, 116.703739 39.914388, 116.703739 39.91439, 116.703738 39.914392, 116.703737 39.914395, 116.703736 39.914397, 116.703734 39.9144, 116.703733 39.914402, 116.703732 39.914405, 116.703731 39.914407, 116.70373 39.914409, 116.703729 39.914412, 116.703728 39.914414, 116.703727 39.914417, 116.703725 39.914419, 116.703724 39.914421, 116.703723 39.914424, 116.703722 39.914426, 116.703721 39.914429, 116.70372 39.914431, 116.703719 39.914433, 116.703717 39.914436, 116.703716 39.914438, 116.703715 39.914441, 116.703715 39.914441, 116.703715 39.914441, 116.703715 39.914441, 116.703714 39.914441, 116.703714 39.914441, 116.703714 39.914441, 116.703714 39.914441, 116.703714 39.914441, 116.703713 39.914441, 116.703713 39.914441, 116.703713 39.914441, 116.703713 39.914441, 116.703713 39.914441, 116.703713 39.914441, 116.703712 39.914441, 116.703712 39.914441, 116.703712 39.914441, 116.703712 39.914441, 116.703712 39.914441, 116.703712 39.914441, 116.703711 39.914441, 116.703711 39.914441, 116.703711 39.914441, 116.703711 39.914441, 116.703711 39.914441, 116.703711 39.914441, 116.70371 39.914441, 116.70371 39.914441, 116.70371 39.914441, 116.70371 39.914441, 116.70371 39.914441, 116.70371 39.914441, 116.703709 39.914441, 116.703709 39.914441, 116.703709 39.914441, 116.703709 39.914441, 116.703709 39.914441, 116.703708 39.914441, 116.703708 39.914441, 116.703708 39.914441, 116.703708 39.914441, 116.703708 39.914441, 116.703708 39.914441, 116.703707 39.914441, 116.703707 39.914441, 116.703707 39.914441, 116.703707 39.914441, 116.703707 39.914441, 116.703707 39.914441, 116.703706 39.914441, 116.703706 39.914441, 116.703706 39.914441, 116.703706 39.914441, 116.703706 39.914441, 116.703706 39.914441, 116.703705 39.914441, 116.703705 39.914441, 116.703705 39.914441, 116.703705 39.914441, 116.703705 39.914441, 116.703705 39.914441, 116.703705 39.914441, 116.703705 39.914441, 116.703704 39.914441, 116.703704 39.914442, 116.703704 39.914442, 116.703704 39.914442, 116.703704 39.914442, 116.703704 39.914442, 116.703704 39.914442, 116.703704 39.914442, 116.703704 39.914442, 116.703704 39.914442, 116.703703 39.914442, 116.703703 39.914442, 116.703703 39.914442, 116.703703 39.914443, 116.703703 39.914443, 116.703703 39.914443, 116.703703 39.914443, 116.703703 39.914443, 116.703703 39.914443, 116.703703 39.914443, 116.703702 39.914443, 116.703702 39.914443, 116.703702 39.914443, 116.703702 39.914443)"
    # 加载 WKT 为 Shapely 对象
    geo1 = loads(geo1_wkt)
    geo2 = loads(geo2_wkt)

    intersection = geo1.intersection(geo2)
    iou = intersection.length / max(geo1.length, geo2.length)
    print("轨迹交集覆盖率 (IoU):", iou)

    # 转换为点坐标数组
    # geo1_coords = np.array(geo1.coords)
    # geo2_coords = np.array(geo2.coords)
    #
    # # 计算双向 Hausdorff 距离
    # hausdorff_1_to_2 = directed_hausdorff(geo1_coords, geo2_coords)[0]
    # hausdorff_2_to_1 = directed_hausdorff(geo2_coords, geo1_coords)[0]
    #
    # # Hausdorff 距离
    # hausdorff_distance = max(hausdorff_1_to_2, hausdorff_2_to_1)
    #
    # # 输出结果
    # print(f"Geo1 到 Geo2 的 Hausdorff 距离: {hausdorff_1_to_2}")
    # print(f"Geo2 到 Geo1 的 Hausdorff 距离: {hausdorff_2_to_1}")
    # print(f"最终 Hausdorff 距离: {hausdorff_distance}")
