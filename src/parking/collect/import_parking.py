"""
根据差分结果回捞人工核实结果
"""
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests
import csv
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
from shapely.ops import transform
from shapely.ops import unary_union
import pyproj
from shapely.geometry import Polygon, LineString


from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from shapely import Polygon, LineString

from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER


def try_buffer(polygon: Polygon, baseline: LineString, buffer: float, extend_length=1 * METER) -> Polygon:
    """try_buffer
    计算缓冲区
    """
    try:
        sideline, i_line = geometric.get_sidelines(polygon, baseline, eps=1 * METER)

        sideline = geometric.trim_linestring(sideline, -0.5, 10 * METER)
        sideline = geometric.trim_linestring_for_buffer(sideline, (buffer + 1 * METER))
        sideline = geometric.trim_linestring_for_buffer(sideline, -(buffer + 1 * METER))
        sideline = geometric.extend_linestring(sideline, extend_length)

        center_point = i_line.interpolate(i_line.length / 2)
        is_left = geometric.is_left(center_point, sideline)
        buffer *= 1 if is_left else -1
        return sideline.buffer(buffer, cap_style="flat", single_sided=True)
    except Exception as e:
        print(e)
        return None


def _random_beijing_parking():
    sql = (f"select b.id,ST_AsText(b.geom),ST_AsText(b.baseline) from park_storefront_task "
           f"a join park_storefront_strategy b on a.task_id = b.task_id "
           f"where a.batch in ('beijing_except_haidian_20241208', 'beijing_except_haidian_20241221_quanjing', "
           f"'shanghai_partial_20241114') and b.step = 'VERIFIED' and a.status!='END' order by random() limit 5;")
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_louzhao_data():
    sql = """
    select b.id,ST_AsText(b.geom),ST_AsText(b.baseline)
from park_storefront_task a
join park_storefront_strategy b on a.task_id = b.task_id
where a.batch = 'shenzhen_patch_20250306' and b.step = 'PRIME' and b.status in('VERIFYING','VERIFIED');
    """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _test_face(id):
    sql = (f"select b.id,ST_AsText(b.geom),ST_AsText(b.baseline) from park_storefront_task "
           f"a join park_storefront_strategy b on a.task_id = b.task_id "
           f"where a.batch in ('beijing_except_haidian_20241208', 'beijing_except_haidian_20241221_quanjing', "
           f"'shanghai_partial_20241114') and b.step = 'VERIFIED' and a.status!='END' and b.id={id}")
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_parkstore_parking_prime_by_city_name(city_name):
    sql = f"select verify_id, st_astext(geom),prev_info_id from park_storefront_verify_pushed where city = '{city_name}'"
    data_list = dbutils.fetch_all(pgsql.POI_CONFIG, sql)

    return_data = []
    for item in data_list:
        verify_id = item[0]
        prev_info_id = item[2]
        # logger.info(f"prev_info_id: {prev_info_id}")
        sql2 = f"select prev_face_ids from park_storefront_strategy where id={prev_info_id}"
        prev_face_ids = dbutils.fetch_one(pgsql.POI_CONFIG, sql2)[0]
        # logger.info(f"prev_face_ids: {prev_face_ids}")
        # if len(prev_face_ids) > 1:
        #     logger.error(f"prev_face_ids: {prev_face_ids}")
            # for pf_id in prev_face_ids:
            #     verify_id = f"{verify_id}_1"
        # else:
        #     logger.info(f"prev_face_ids: {prev_face_ids}")
        # for prev_face_id in prev_face_ids.split(','):
        #     logger.info(f"prev_face_id: {prev_face_id}")

        # print(prev_face_ids[0])
        sql3 = f"select ST_AsText(geom), ST_AsText(baseline), id from park_storefront_strategy where face_id='{prev_face_ids[0]}' and step='PRIME' order by id desc limit 1"
        data2 = dbutils.fetch_one(pgsql.POI_CONFIG, sql3)
        temp = [verify_id, data2[0], data2[1], data2[2]]
        # print(temp)
        return_data.append(temp)
    return return_data


def _is_inst(source_id, new_polygon):
    sql = (f"SELECT id FROM park_storefront_strategy WHERE id={source_id} "
           f"AND ST_Intersects(baseline, ST_GeomFromText('{new_polygon}', 4326));")
    if dbutils.fetch_one(pgsql.POI_CONFIG, sql):
        return True
    else:
        return False


def _is_inst_by_sid(source_id, new_polygon):
    sql = (f"SELECT id FROM park_storefront_strategy WHERE id={source_id} "
           f"AND ST_Intersects(baseline, ST_GeomFromText('{new_polygon}', 4326));")
    if dbutils.fetch_one(pgsql.POI_CONFIG, sql):
        return True
    else:
        return False


def _run_import():
    # city_name = '北京市'
    # data = _get_parkstore_parking_new(city_name)
    # data = _random_beijing_parking()
    # data = _test_face(162336)
    data = _get_parkstore_parking_prime_by_city_name('绍兴市')
    for item in data:
        source_id = item[0]
        # parking_geom = item[1]
        # baseline = item[2]
        # try_buffer(Polygon(parking_geom), LineString(baseline), 10)
        # logger.info(f"parking_geom: {parking_geom}")
        parking_geom = wkt.loads(item[1])
        baseline = wkt.loads(item[2])
        sid = item[3]
        new_polygon = try_buffer(parking_geom, baseline, 30 * METER)
        if new_polygon is None:
            insert_parking_geom = parking_geom
        else :
            insert_parking_geom = new_polygon

        # 过滤面是否和主路相交
        is_inst = _is_inst_by_sid(sid, insert_parking_geom)
        if is_inst:
            logger.error(f"{source_id} is inst 过滤.")
            # print(item)
            # print(insert_parking_geom)
            continue
        else:
            logger.info(f"{source_id} is not inst.")
            # print(new_polygon)
            _import_data("parkstore", source_id, insert_parking_geom, "20250307")


def _run_import2():
    # city_name = '北京市'
    # data = _get_parkstore_parking_new(city_name)
    # data = _random_beijing_parking()
    # data = _test_face(162336)
    # data = _get_parkstore_parking_prime_by_city_name('绍兴市')
    data = _get_louzhao_data()
    for item in data:
        source_id = item[0]
        parking_geom = item[1]
        baseline = item[2]
        # try_buffer(Polygon(parking_geom), LineString(baseline), 10)
        # logger.info(f"parking_geom: {parking_geom}")

        parking_geom = wkt.loads(parking_geom)
        baseline = wkt.loads(baseline)
        new_polygon = try_buffer(parking_geom, baseline, 30 * METER)
        if new_polygon is None:
            insert_parking_geom = parking_geom
        else:
            insert_parking_geom = new_polygon

        # 过滤面是否和主路相交
        is_inst = _is_inst(source_id, insert_parking_geom)
        if is_inst:
            logger.error(f"{source_id} is inst 过滤.")
            # print(item)
            # print(insert_parking_geom)
            continue
        else:
            logger.info(f"{source_id} is not inst.")
            # print(new_polygon)
            _import_data("parkstore", source_id, insert_parking_geom, "20250308")


def _import_data(source, source_id, parking_geom, batch):
    try:
        poi_conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
        poi_cursor = poi_conn.cursor()

        aoi_conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
        aoi_cursor = aoi_conn.cursor()

        sql1 = (f"SELECT mesh_id, cityname FROM mesh_conf_wkt "
               f"WHERE ST_Intersects(wkt, ST_GeomFromText('{parking_geom}', 4326)) "
               f"OR ST_Contains(wkt, ST_GeomFromText('{parking_geom}', 4326)) limit 1")
        aoi_cursor.execute(sql1)
        mesh_info = aoi_cursor.fetchone()
        if mesh_info is None:
            raise Exception(f"{parking_geom} not found in any mesh.")
        mesh_id, cityname = mesh_info[0], mesh_info[1]

        sql = f"""insert into parking_collect_data (source, source_id, parking_geom, mesh_id, cityname, batch)
        values('{source}', '{source_id}', st_geomfromtext('{parking_geom}', 4326), '{mesh_id}', '{cityname}', '{batch}')
            """
        res = poi_cursor.execute(sql)
        logger.info(res)
    except Exception as e:
        logger.error(e)


def _check_parking_geom():
    # sql = f"select source_id,st_astext(parking_geom) from parking_collect_data where id = 51977"
    # res = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    # source_id = res[0]
    # parking_geom = res[1]
    # print(source_id)
    # print(parking_geom)

    sql = (f"SELECT st_astext(baseline),st_astext(geom) FROM park_storefront_strategy where id=140178")
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql)

    baseline = res[0]
    geom = res[1]
    print(baseline)
    print(geom)

    new_polygon = try_buffer(wkt.loads(geom), wkt.loads(baseline), 30 * METER)

    print(new_polygon)


if __name__ == "__main__":
    # _run_import()
    _run_import2()
    # _check_parking_geom()