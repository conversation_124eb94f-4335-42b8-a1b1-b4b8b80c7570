#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
Description
安轨迹下发众源任务
"""
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from datetime import datetime
from loguru import logger
from src.tools.afs_tool import AfsTool
from multiprocessing import Pool
import subprocess
import multiprocessing


# 定义处理单个文件的函数
def _process_file(file_path, batch_src):

    local_dir = os.getcwd()
    # /user/lbs-guiji/trajplat/mapmatch/ugc/20250112/beijing/1100/beijing/part-00099.gz
    split_path = file_path.split('/')
    date = split_path[6]
    city = split_path[7]
    hour = split_path[8]
    file_name = split_path[-1]

    # /home/<USER>/fanjiabin/aoi-ml/output/src/parking/collect/beijing/20250112/1100
    local_data_dir = f"{local_dir}/{city}/{date}/{hour}/{file_name}"

    cm = f"py39 calc_for_caiji_v2.py"
    script_path = f"cd /home/<USER>/lifan14/parking_point_test/calc_parking_points_distributed_v5_laofan && {cm}"
    command = f"source ~/fanjiabin/bashrc && {script_path} {local_data_dir} {batch_src}"
    print(f"Processing {command}")
    try:
        # 调用子进程运行脚本
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        print(f"Processed {file_path}")
        print(result.stdout)
    except Exception as e:
        print(f"Error processing {file_path}: {e}")


def process_files_in_parallel(file_list, batch_src, num_processes=4):
    """
    使用多进程处理文件列表。
    :param file_list: 要处理的文件列表
    :param batch_src: 传递给每个处理任务的参数
    :param num_processes: 并行进程数
    """
    with multiprocessing.Pool(num_processes) as pool:
        # 为每个文件分配一个任务
        results = pool.starmap(_process_file, [(file, batch_src) for file in file_list])
    return results


def _download_file(afs_data):
    try:
        print(f"Downloading {afs_data}")
        current_directory = os.getcwd()
        dat_split = afs_data.split('/')
        # file_name = dat_split[-1]
        date = dat_split[6]
        city = dat_split[7]
        time = dat_split[8]
        print(f"{city}/{date}/{time}")
        data_dir = f"{city}/{date}/{time}"

        dir_path = os.path.join(current_directory, data_dir)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

        afs = AfsTool('map_aoi_fenghuang')
        code, _ = afs.get(afs_data, dir_path)
        if code == 0:
            logger.info(f"download file to {data_dir} success")
        else:
            logger.error(f"download file to {data_dir} failed")
    except Exception as e:
        logger.error(f"download file Exception, {e}")


def _multi_process_download(flow_name, num_processes=4):
    """使用多进程并行下载文件"""
    if not _update_flow_status(flow_name, 'DOING'):
        logger.error("flow is doing")
        return

    data = _get_flow_data(flow_name, 'DOING')
    if data is None:
        logger.error("no such flow")
        return

    file_list = data['data']
    print(f"file_list: {file_list}")

    with Pool(num_processes) as pool:
        # 为每个文件分配任务
        pool.map(_download_file, file_list)

    # 所有下载任务完成后更新状态
    if not _update_flow_status(flow_name, 'DONE'):
        logger.error("Failed to update flow status to DONE")
    else:
        logger.info("Flow status updated to DONE")


def _get_downloaded_traj():
    sql = f"""select flow_data from park_collect_flow where flow_name = 'tracj" and status = 'downloaded'"""
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _get_flow_status(flow_name):
    sql = f"""select status from park_collect_flow where flow_name = '{flow_name}'"""
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _is_flow_doing(flow_name) -> bool:
    status = _get_flow_status(flow_name)
    if status is None or status[0] != 'DOING':
        return False
    else:
        return True


def _is_flow_done(flow_name) -> bool:
    status = _get_flow_status(flow_name)
    if status is None or status[0] != 'DONE':
        return False
    else:
        return True


def _update_flow_status(flow_name, status):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"update park_collect_flow set status = '{status}' where flow_name = '{flow_name}'"
    cursor.execute(sql)
    rowcount = cursor.rowcount
    return rowcount


def _get_flow_data(flow_name, status):
    sql = f"""select flow_data from park_collect_flow where flow_name = '{flow_name}' and status = '{status}'"""
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)[0]


def _get_wait_cal_data():
    sql = f"""select flow_name,flow_data from park_collect_flow where status = 'DONE' and id=2193"""
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _add_flow(flow_name, flow_data):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    flow_data_json = json.dumps(flow_data)
    try:
        sql = (f"insert into park_collect_flow(flow_name, flow_data, status) values ('{flow_name}', "
               f"'{flow_data_json}', 'INIT')")
        cursor.execute(sql)
        rowcount = cursor.rowcount
        return rowcount
    except Exception as e:
        print(f"Exception: {e}")
        return 0



if __name__ == '__main__':
    """
    根据众源轨迹计算熄火点
    """
    arguments = sys.argv[1:]
    print(arguments)
    if len(arguments) < 1:
        print("请输入参数: py39 parking_points_data.py download_data 20250111 1900 ")
        print("请输入参数: nohup py39 parking_points_data.py cal 20250111 1900 >"
              " parking_points_data_cal_20250111.log 2>&1 & ")
        exit(0)
    action = arguments[0]

    afs_path = '/user/lbs-guiji/trajplat/mapmatch/ugc'
    split_area = {
        'beijing': [
            'beijing', # 北京
        ],
        'shanghai': [
            'shanghai', # 上海
        ],
        'guangdong': [
            'guangdong', # 广州 深圳 中山 东莞 佛山
        ],
        'zhejiang': [
            'zhejiang', # 杭州 金华 温州 宁波
        ],
        'jiangsu': [
            'jiangsu', # 南京 苏州 无锡
        ],
        'huadong': [
            'fujian', # 福州 厦门
            'anhui', # 合肥
        ],
        'huanan_huabei': [
            'guangxi', # 南宁
            'hebei', # 石家庄
            'tianjin', # 天津
        ],
        'xinan': [
            'sichuan', # 成都
            'chongqing', # 重庆
            'yunnan', # 昆明
        ],
        'huazhong': [
            'hubei', # 武汉
            'henan', # 郑州
            'hunan', # 长沙
            'shandong', # 济南 青岛
        ],
        'dongbei_xibei': [
            'shan3xi', # 西安
        ]
    }
    # collect_city = ['beijing']
    collect_city = [
        'beijing',
        'shanghai',
        'guangdong',
        'zhejiang'
        'jiangsu',
        'huadong',
        'huanan_huabei',
        'xinan',
        'huazhong',
        'dongbei_xibei'
    ]
    # collect_hours = ['0600', '0700', '0800', '0900', '1000', '1100', '1200', '1300', '1400', '1500', '1600', '1700', '1800', '1900']
    collect_hours = ['1400']


    if action == 'download_data':
        # afsshell --username=map_aoi_fenghuang --password=map_aoi_fenghuang
        # ls afs://fenghuang.afs.baidu.com:9902/user/lbs-guiji/trajplat/mapmatch/ugc/20250123/guangdong
        today = datetime.now().strftime("%Y%m%d")
        print(today)
        today = '20250123'
        # date_arg = arguments[1]
        # time_arg = arguments[2]

        afs = AfsTool('map_aoi_fenghuang')
        _, split_area_list = afs.list(f"{afs_path}/{today}/")
        for file in split_area_list:
            split_file = file.split('/')
            split_area_name = split_file[-2]
            if split_area_name not in collect_city:
                continue

            logger.info(f"{split_area_name}")
            _, hour_list = afs.list(f"{afs_path}/{today}/{split_area_name}")
            for hour in hour_list:
                hour_split = hour.split('/')
                # print(hour_split)
                hour_d = hour_split[-2]
                # print(hour_d)
                if hour_d not in collect_hours:
                    continue

                logger.info(f"{split_area_name}, {hour_d}")

                _, success_list = afs.list(f"{afs_path}/{today}/{split_area_name}/{hour_d}")
                if len(success_list) >= 2:
                    # print(f"{success_list}")
                    success_file = f"{afs_path}/{today}/{split_area_name}/{hour_d}/_SUCCESS"
                    # # print(success_file)
                    done_file = f"{afs_path}/{today}/{split_area_name}/{hour_d}/done.txt"
                    # print(done_file)
                    # city_file = f"{afs_path}/{today}/{split_area_name}/{hour_d}/{city}/"
                    # print(city_file)

                    # print(success_file, done_file, city_file)

                    if success_file in success_list and done_file in success_list:
                        prov_list = split_area[split_area_name]
                        for prov in prov_list:
                            prov_file = f"{afs_path}/{today}/{split_area_name}/{hour_d}/{prov}/"
                            _, download_file_list = afs.list(f"{prov_file}/")
                            if len(download_file_list) > 0:
                                # _add_flow(f"{city}_{hour_d}", {'city': city, 'hour': hour_d})

                                flow_name = f"download_track_{prov}_{today}_{hour_d}"
                                if _is_flow_doing(flow_name):
                                    logger.info(f"{prov}_{hour_d} is doing")
                                    continue

                                if _is_flow_done(flow_name):
                                    logger.info(f"{prov}_{hour_d} is done")
                                    continue

                                res = _add_flow(flow_name, {'city': prov, 'date': today,
                                                            'hour': hour_d, 'data': download_file_list})
                                if res:
                                    logger.info(f"{prov}_{hour_d} add flow success")
                                    # _multi_process_download(download_file_list, num_processes=10)
                                    _multi_process_download(flow_name, num_processes=10)
                                else:
                                    logger.error(f"{prov}_{hour_d} add flow failed")

    if action == 'cal':
        data = _get_wait_cal_data()
        if data is None:
            logger.error("no wait cal data")
            exit(0)
        logger.info(f"data: {data}")
        flow_name = data[0]
        flow_data = data[1]
        city = flow_data['city']
        date_arg = flow_data['date']
        time_arg = flow_data['hour']
        batch_src = f"{city}_{date_arg}_{time_arg}"
        print(f"batch_src: {batch_src}")
        file_list = flow_data['data']

        results = process_files_in_parallel(file_list, batch_src, num_processes=20)
        print("Results:")
        for result in results:
            print(result)
        if not _update_flow_status(flow_name, 'CALLED'):
            logger.error("Failed to update flow status to CALLED")
        else:
            logger.info("Flow status updated to CALLED")


    # psql -U traj_feature_rw -d traj_feature_db -W traj_feature_rw -p 9432 -h 10.56.135.223
    # select count(*) from parking_points where batch_src='beijing_20250118_0600'; 1077441695
    if action == 'test':
        _multi_process_download('download_track_beijing_1100', num_processes=2)