"""
根据差分结果回捞人工核实结果
"""
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests
import csv
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
from shapely.ops import transform
from shapely.ops import unary_union
import pyproj

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from shapely import Polygon, LineString

from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER


def try_buffer(polygon: Polygon, baseline: LineString, buffer: float) -> Polygon:
    """try_buffer
    计算缓冲区
    """
    try:
        sideline, i_line = geometric.get_sidelines(polygon, baseline, eps=1 * METER)

        sideline = geometric.trim_linestring(sideline, -0.5, 10 * METER)
        sideline = geometric.trim_linestring_for_buffer(sideline, (buffer + 1 * METER))
        sideline = geometric.trim_linestring_for_buffer(sideline, -(buffer + 1 * METER))
        sideline = geometric.extend_linestring(sideline, 1 * METER)

        center_point = i_line.interpolate(i_line.length / 2)
        is_left = geometric.is_left(center_point, sideline)
        buffer *= 1 if is_left else -1
        return sideline.buffer(buffer, cap_style="flat", single_sided=True)
    except Exception as e:
        return None


def _get_link_info():
    """
    获取 link
    """
    sql = (f"select a.id,ST_AsText(b.geom),ST_AsText(b.baseline) from park_storefront_post_zhongyuan "
           f"a left join park_storefront_strategy b on a.strategy_id=b.id")
    # # sql = ("select b.id, st_astext(b.geom), st_astext(b.baseline) from park_storefront_task a
    # join park_storefront_strategy b "
    # #        "on a.task_id = b.task_id "
    # #        "where a.batch in ('beijing_except_haidian_20241208', 'beijing_except_haidian_20241221_quanjing') "
    # #        "and b.step = 'VERIFIED' and a.status!='END'")
    # print(sql)
    data = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return data


def _get_storefront_area_wait_push():
    """
    获取待推送的数据
    """
    # sql = f"select strategy_id, st_astext(geom), st_astext(new_geom), status from park_storefront_post_zhongyuan where status=1 limit 1"
    sql = f"select strategy_id, st_astext(geom), status from park_storefront_post_zhongyuan where status=0"
    # sql = f"select strategy_id, st_astext(new_geom), status from park_storefront_post_zhongyuan where status=1"
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _is_get_link(geom):
    """
    获取 link
    """
    qry = f"""SELECT
        nl.link_id,
        ST_AsText(nl.geom) AS link_geom_wkt
    FROM nav_link nl
    WHERE
        ST_Intersects(ST_GeomFromText('{geom}', 4326), nl.geom);"""
    res = dbutils.fetch_one(pgsql.ROAD_CONFIG, qry)
    if res is None:
        return False
    if len(res) > 0:
        return True
    else:
        return False


def push_zhongyuan(payload):
    """
    将指定的POI信息推送到POI接口
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/createCollectTask"

    print(f"payload : {payload}")
    headers = {
        'Content-Type': 'application/json'
    }
    response = requests.request("POST", url, headers=headers, json=payload)
    # response = requests.post(url, json={
    #     "bid": bid,
    #     "name": name,
    #     "wkt": wkt,
    #     "buffer": 1,
    # })
    print(response.text)
    # time.sleep(1)
    return json.loads(response.text)


def _get_blu_face():
    sql = ("select main_bid, st_astext(geom) from blu_face as a inner join "
           "blu_face_complete as b on a.face_id = b.face_id where b.aoi_complete >= 3")
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    return ret


def _get_bid_pv(main_bid):
    sql = f"select click_pv from poi where bid = '{main_bid}'"
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _is_poi_4categories_list_2024q4(main_bid):
    sql = f"select id from poi_4categories_list_2024q4 where bid = '{main_bid}';"
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _insert_aoi_post_zhongyuan(bid, geom, batch, issi):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"""insert into aoi_post_zhongyuan (bid,geom,batch,issi) values('{bid}',
     st_geomfromtext('{geom}', 4326), '{batch}', '{issi}')
        """
    cursor.execute(sql)


def _get_beijing_yessi():
    # 非景区和购物中心
    # sql = (f"select bid, st_astext(geom) from"
    #        f" aoi_post_zhongyuan where issi = 'yes' and status=0 and type not in(1,4)")

    # 景区和购物中心 - 查停车场面数据投放 ??
    # sql = (f"select bid, st_astext(geom) from"
    #        f" aoi_post_zhongyuan where issi = 'yes' and status=0 and type in(1,4)")

    sql = (f"select bid, st_astext(geom) from"
           f" aoi_post_zhongyuan where status=0 limit 80000")
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_yessi_jingqu_gouwu():
    sql = (f"select bid, st_astext(geom) from"
           f" aoi_post_zhongyuan where issi = 'yes' and status=0 and type in(1,4) limit 100")
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_beijing_nosi():
    sql = (f"select bid, st_astext(geom) from"
           f" aoi_post_zhongyuan where issi = 'no' and status=0 order by random() limit 999")
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _update_aoi_post_zhongyuan_status(bid, status):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"update aoi_post_zhongyuan set status = '{status}' where bid = '{bid}'"
    cursor.execute(sql)
    rowcount = cursor.rowcount
    return rowcount


def _get_all_data1():
    sql = """
    select bid from aoi_post_zhongyuan where issi = 'yes'
    """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_4categories_list_2024q4(bid):
    sql = f"""
    select type from poi_4categories_list_2024q4 where bid = '{bid}'
    """
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _update_issi_by_bid(bid, issi):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"update aoi_post_zhongyuan set issi = '{issi}' where bid = '{bid}'"
    cursor.execute(sql)


def _update_type_by_bid(bid, type):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"update aoi_post_zhongyuan set type = {type} where bid = '{bid}'"
    cursor.execute(sql)


def _get_parking_area(bid):
    sql = f"select bid, st_astext(area) from park_online_data where parent_id = '{bid}'"
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _insert_push_data(data, request_info, response_info):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    bid = data['bid']
    batch = data['batch']

    # park_points_push
    point_geom = 'POINT EMPTY'
    parking_traj_geom = 'LINESTRING EMPTY'
    sql = f"""insert into park_points_push (strategy_id,bid,park_points_id,point_time,
    device_no,point_geom,parking_traj_geom,request_info,response_info,
    batch) values(0,'{bid}', 0,
           0,'',st_geomfromtext('{point_geom}', 4326),
           st_geomfromtext('{parking_traj_geom}', 4326),
            '{request_info}','{response_info}','{batch}')
        """
    cursor.execute(sql)
    logger.info(f"insert park_points_push success")


def _mc2gcj(geom):
    """
    将 gcj02 坐标转换为百度墨卡托坐标
    """
    qry = f"select st_astext(mc2gcj(st_geomfromtext('{geom}', 4326)))"
    res = dbutils.fetch_one(pgsql.COMPUTE_CONFIG, qry)
    return res[0]

if __name__ == "__main__":
    """
    主函数
    type 4 景区 1购物
    """
    arguments = sys.argv[1:]
    print(arguments)
    if len(arguments) < 1:
        print("请输入参数===========================")
        exit(0)
    action = arguments[0]
    if action == "import_aoi_data":
        main_bid_list = _get_blu_face()
        for r in main_bid_list:
            bid = r[0]
            geom = r[1]
            pv = _get_bid_pv(bid)
            if pv is None:
                continue
            if pv[0] < 500:
                continue

            issss = _is_poi_4categories_list_2024q4(bid)
            print(issss)
            issi = 'yes'
            if issss is None:
                issi = 'no'
            else:
                issi = 'yes'

            logger.info(f"bid: {bid}, pv: {pv[0]}, issss: {issss}, issi: {issi}")

            _insert_aoi_post_zhongyuan(bid, geom, "20250111", issi)
        pass

    if action == "get_link_info":
        res = _get_link_info()
        print(len(res))
        data = []
        conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
        cursor = conn.cursor()

        try:
            for r in res:
                pz_id = r[0]
                geom = r[1]
                baseline = r[2]
                poly = wkt.loads(geom)
                line = wkt.loads(baseline)

                print('================')
                print(poly)
                print(line)
                buffer_geom = (poly, line, 30 * METER)
                print(buffer_geom)
                if buffer_geom is not None:
                    # buffer_geom = wkt.dumps(buffer_geom)
                    print(buffer_geom)
                    if _is_get_link(buffer_geom):
                        sql = f"""
                                 update park_storefront_post_zhongyuan
                                 set new_geom = st_geomfromtext('{buffer_geom}', 4326),
                                 status = 1 where id = {pz_id}
                             """
                        cursor.execute(sql)

                print('================')
                # data.append({'strategy_id': strategy_id, 'geom': geom, 'baseline': baseline,
                #              'buffer_geom': buffer_geom})

        except Exception as e:
            print(e)
    # 非景区和购物中心
    if action == "area_push_aoi_yessi":
        # 1.24号
        park_geom_list = _get_beijing_yessi()
        for r in park_geom_list:
            bid = r[0]
            geom = r[1].replace(" ((", "((").replace(", ", ",")
            push_data = {
                "bid": bid,
                "name": "store_front",
                "wkt": geom,
                "buffer": 1,
            }
            res = push_zhongyuan(push_data)
            print(res)
            res_json = json.dumps(res)
            if res['errno'] == 0:
                _update_aoi_post_zhongyuan_status(bid, 1)

    # 景区和购物中心 - 查停车场面数据投放
    # if action == "area_push_aoi_yessi_gouwu":
    #     aoi_geom_list = _get_yessi_jingqu_gouwu()
    #     for r in aoi_geom_list:
    #         aoi_bid = r[0]
    #         aoi_geom = r[1].replace(" ((", "((").replace(", ", ",")
    #         park_info_list = _get_parking_area(aoi_bid)
    #         try:
    #             if park_info_list is None or len(park_info_list) <= 0:
    #                 logger.error(f"aoi_bid: {aoi_bid}, park info: None")
    #                 # 如果没有停车场面，就用aoi面
    #                 push_data = {
    #                     "bid": aoi_bid,
    #                     "name": "store_front",
    #                     "wkt": aoi_geom,
    #                     "buffer": 1,
    #                 }
    #                 logger.info(f"aoi_info: {push_data}")
    #                 # res = push_zhongyuan(push_data)
    #                 # print(res)
    #                 # res_json = json.dumps(res)
    #                 # if res['errno'] == 0:
    #                 #     _insert_push_data({'bid': aoi_bid}, json.dumps(push_data), res_json)
    #                 #     _update_aoi_post_zhongyuan_status(bid, 1)
    #                 # else:
    #                 #     _update_aoi_post_zhongyuan_status(bid, 4)
    #                 # continue
    #             else:
    #                 for park_info in park_info_list:
    #                     park_bid = park_info[0]
    #                     if park_info[1] is None:
    #                         continue
    #                     logger.info(f"park_info_before: {park_info[1]}")
    #                     park_area_gcj = _mc2gcj(park_info[1])
    #                     park_area = park_area_gcj.replace(" ((", "((").replace(", ", ",")
    #                     push_data = {
    #                         "bid": park_bid,
    #                         "name": "aoi_park",
    #                         "wkt": park_area,
    #                         "buffer": 500,
    #                     }
    #                     logger.info(f"park_info_after: {push_data}")
    #                 #     res = push_zhongyuan(push_data)
    #                 #     print(res)
    #                 #     res_json = json.dumps(res)
    #                 #     if res['errno'] == 0:
    #                 #         _insert_push_data({'bid': aoi_bid}, json.dumps(push_data), res_json)
    #                 #         _update_aoi_post_zhongyuan_status(bid, 1)
    #                 #     else:
    #                 #         _update_aoi_post_zhongyuan_status(bid, 4)
    #
    #             # print(f"bid: {bid}, park info: {len(park_info)}")
    #         except Exception as e:
    #             logger.error(f"bid: {aoi_bid}, error: {e}")


    if action == "area_push_aoi_nosi":
        park_geom_list = _get_beijing_nosi()
        for r in park_geom_list:
            bid = r[0]
            geom = r[1].replace(" ((", "((").replace(", ", ",")
            push_data = {
                "bid": bid,
                "name": "store_front",
                "wkt": geom,
                "buffer": 1,
            }
            res = push_zhongyuan(push_data)
            print(res)
            res_json = json.dumps(res)
            if res['errno'] == 0:
                _update_aoi_post_zhongyuan_status(bid, 1)


    if action == "push_poi":
        # 1.27号
        # 创建之后 curl https://mapde-poi.baidu-int.com/prod/api/pushCollectTask
        res = _get_storefront_area_wait_push()
        conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
        cursor = conn.cursor()

        for r in res:
            strategy_id = r[0]
            geom = r[1].replace(" ((", "((").replace(", ", ",")
            # print(strategy_id, geom)
            push_data = {
                "bid": str(strategy_id),
                "name": "store_front",
                "wkt": geom,
                "buffer": 1,
            }
            # push_data_json = json.dumps(push_data)
            # print(push_data_json)
            res = push_zhongyuan(push_data)
            print(res)
            res_json = json.dumps(res)
            if res['errno'] == 0:
                sql = f"""
                         update park_storefront_post_zhongyuan set status = 3, response_info = '{res_json}' 
                          where strategy_id = {strategy_id}
                     """
                cursor.execute(sql)


