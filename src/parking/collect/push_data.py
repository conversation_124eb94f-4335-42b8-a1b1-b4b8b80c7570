#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
Description
安轨迹下发众源任务
"""
import jwt
import logging
import os

import json
import time
from datetime import datetime
import requests
import src.tools.redis_tool as rt
import redis

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
import subprocess
import multiprocessing
from typing import List, Dict, Tuple
from shapely.wkt import loads
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
import math

url = "http://dedc.baidu.com/vdust/main/createnewtask"


def get_jwt_token(account, token, service_list):
    """获取加密字符串

    Args:
        account (str): 授权账号
        token (str): 账号对应token
        service_list (list): 要访问的接口，对于路网服务，列表长度只能为1，第0个元素为要访问的接口名

    Returns:
        _type_: _description_
    """
    msg = {
        'exp': int(time.time()) + 260,  # 过期时间
        'timeout': int(time.time()) + 86400 * 5,  # 有效期5天,
        'name': account,  # 账号名称
        'service': service_list # 服务名称
    }
    # 获取token
    ret = jwt.encode(msg, token, algorithm='HS256')
    return ret



def _haversine(lon1, lat1, lon2, lat2):
    """
    使用 haversine 公式计算地球上两点之间的距离（单位：米）。

    参数:
        lon1 (float): 第一个点的经度。
        lat1 (float): 第一个点的纬度。
        lon2 (float): 第二个点的经度。
        lat2 (float): 第二个点的纬度。

    返回:
        float: 两点之间的距离（单位：米）。
    """
    # 地球半径（单位：米）
    R = 6371000

    # 将经纬度转换为弧度
    lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])

    # 计算差值
    dlon = lon2 - lon1
    dlat = lat2 - lat1

    # haversine 公式
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    # 计算距离
    distance = R * c
    return distance


def _calculate_geometry_distance(wkt_geometry):
    """
    计算几何对象（LINESTRING、MULTILINESTRING 或 POLYGON）的两两点之间的距离之和（单位：米）。

    参数:
        wkt_geometry (str): 几何对象的 WKT 字符串。

    返回:
        float: 两两点之间的距离之和（单位：米）。
    """
    # 将 WKT 字符串转换为 Shapely 几何对象
    geometry = loads(wkt_geometry)

    # 初始化总距离
    total_distance = 0

    # 处理 LINESTRING
    if geometry.geom_type == "LineString":
        coords = list(geometry.coords)
        for i in range(len(coords) - 1):
            lon1, lat1 = coords[i]  # 第一个点的经度和纬度
            lon2, lat2 = coords[i + 1]  # 第二个点的经度和纬度
            distance = _haversine(lon1, lat1, lon2, lat2)  # 使用 haversine 公式计算距离
            total_distance += distance

    # 处理 MULTILINESTRING
    elif geometry.geom_type == "MultiLineString":
        for linestring in geometry.geoms:  # 遍历每个 LINESTRING
            coords = list(linestring.coords)
            for i in range(len(coords) - 1):
                lon1, lat1 = coords[i]  # 第一个点的经度和纬度
                lon2, lat2 = coords[i + 1]  # 第二个点的经度和纬度
                distance = _haversine(lon1, lat1, lon2, lat2)  # 使用 haversine 公式计算距离
                total_distance += distance

    # 处理 POLYGON
    elif geometry.geom_type == "Polygon":
        coords = list(geometry.exterior.coords)
        for i in range(len(coords) - 1):
            lon1, lat1 = coords[i]  # 第一个点的经度和纬度
            lon2, lat2 = coords[i + 1]  # 第二个点的经度和纬度
            distance = _haversine(lon1, lat1, lon2, lat2)  # 使用 haversine 公式计算距离
            total_distance += distance

    else:
        raise ValueError("仅支持 LINESTRING、MULTILINESTRING 和 POLYGON 类型的几何对象。")

    return total_distance


def _get_parking_points(batch_src, geom):
    qry = (f"select id,user_id,traj_time,st_astext(geom),st_astext(parking_traj_geom) from parking_points"
           f" where batch_src ='{batch_src}'"
           f" and ST_Contains(ST_GeomFromText('{geom}', 4326), geom)")
    # print(qry)
    res = dbutils.fetch_one(pgsql.TRAJ_FEATURE_NEW, qry)
    return res


# def run(device_no):
#     """主流程
#     """
#     # url = "http://10.56.171.217:8891/de/servicehub/create_task"
#     url = "http://service.mapde.baidu-int.com/de/servicehub/create_task"
#
#     account = 'parking'
#     token = 'c262159e031c40889226dd0d2aa04707'
#
#     service_list = ['vdust']
#     data = {
#         "task_info": [{
#             "device_no": device_no,
#             # "geo_timestamp": *************,
#             "geo_timestamp": *************,
#             # 当前 前后100米
#             "geo_range": "100,100",
#             # "time_trace": geom_area,
#             # 传具体时间戳list 时 不用传 geo_timestamp  geo_range
#             # "timestamp_list": [*************, *************, *************, *************,
#             # *************, *************, *************, *************, *************, *************],
#             }],
#         "deliver_source": "parking"
#     }
#     #data = {'test':'a'}
#     key = get_jwt_token(account, token, service_list)
#     headers = {'content-type': "application/json", 'Authorization': 'Bearer ' + key}
#     response = requests.post(url, data=json.dumps(data), headers=headers)
#     print(response)
#     logging.info(response.text)


def _decrypt_user_id(encrypted_user_id):
    device_code_length = 6
    encrypted_prefix = 'trajplat_'

    encrypted_user_id_without_prefix = encrypted_user_id.replace(encrypted_prefix, '')
    index_of_first_underscore = encrypted_user_id_without_prefix.find('_')
    index_of_first_device_code_number = index_of_first_underscore - device_code_length
    decrypted_user_id_start = encrypted_user_id_without_prefix[index_of_first_device_code_number::]
    decrypted_user_id_end = encrypted_user_id_without_prefix[:index_of_first_device_code_number]
    return f"{decrypted_user_id_start}{decrypted_user_id_end}"


def _push_data(device_no, timestamp_list, extra_info):
    """主流程
    """
    # url = "http://10.56.171.217:8891/de/servicehub/create_task"
    url = "http://service.mapde.baidu-int.com/de/servicehub/create_task"

    account = 'parking'
    token = 'c262159e031c40889226dd0d2aa04707'

    service_list = ['vdust']
    data = {
        "task_info": [{
            "device_no": device_no,
            "timestamp_list": timestamp_list,
            }],
        "deliver_source": "parking"
    }
    logging.info(data)
    key = get_jwt_token(account, token, service_list)
    headers = {'content-type': "application/json", 'Authorization': 'Bearer ' + key}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    logging.info(response.text)

    # 记录当天推送次数
    _record_push_times()

    _insert_push_data(extra_info, json.dumps(data), response.text)
    # return json.loads(response.text)


def _insert_push_data(data, request_info, response_info):
    try:
        conn = pgsql.get_connection(pgsql.POI_CONFIG)
        cursor = conn.cursor()

        parking_collect_data_id = data['parking_collect_data_id']
        park_points_id = data['park_points_id']
        point_time = data['point_time']
        device_no = data['device_no']
        point_geom = data['point_geom']
        parking_traj_geom = data['parking_traj_geom']
        batch = data['batch']
        remark = data['remark']
        remark_json = json.dumps(remark)

        parsed_data = json.loads(response_info)
        nested_task_id = parsed_data.get("data", {}).get("data", {}).get("task_id")
        sql = f"""insert into parking_points_push (parking_collect_data_id,park_points_id,point_time,
        device_no,point_geom,parking_traj_geom,request_info,response_info,
        batch,task_id,remark) values({parking_collect_data_id},{park_points_id},
               {point_time},'{device_no}',st_geomfromtext('{point_geom}', 4326),
               st_geomfromtext('{parking_traj_geom}', 4326),
                '{request_info}','{response_info}','{batch}','{nested_task_id}','{remark_json}')
            """
        res = cursor.execute(sql)
        if res == 0:
            logger.error(f"插入失败, {sql}")
            conn.rollback()
            return

        # sql = f"""update parking_collect_data set collect_times = collect_times+1
        # where id = {parking_collect_data_id};"""
        # sql = f"""update parking_collect_data
        # set collect_times = collect_times-1 where id = {parking_collect_data_id};"""
        # res = cursor.execute(sql)
        # if res == 0:
        #     logger.error(f"更新失败, {sql}")
        #     conn.rollback()
        #     return
        conn.commit()
        logger.info(f"_insert_push_data success")
    except Exception as e:
        print(f"插入数据失败: {e}")


def _get_wait_cal_data_by_status(status):
    sql = f"""select flow_name,flow_data from park_collect_flow where status = '{status}'"""
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _update_flow_status(flow_name, status):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"update park_collect_flow set status = '{status}' where flow_name = '{flow_name}'"
    cursor.execute(sql)
    rowcount = cursor.rowcount
    return rowcount


def _get_push_batch() -> str:
    """从 Redis 获取 待推送批次（熄火点 batch_src）"""
    with rt.RedisTool('aoi') as rt_client:
        zhongyuan_batch_src_queue_key = f"zhongyuan_batch_src_queue"
        data_count = rt_client.redis_conn.llen(zhongyuan_batch_src_queue_key)
        if data_count == 0:
            return None
        else:
            batch_src = rt_client.redis_conn.rpop(zhongyuan_batch_src_queue_key)
            return batch_src


def _get_parking_points_by_batch_src(batch_src):
    sql = f"""select id,user_id,traj_time,st_astext(geom),st_astext(parking_traj_geom),parking_time,parking_type,batch_src 
    from parking_points where batch_src = '{batch_src}' and parking_type in('end_car','start_car')"""
    return dbutils.fetch_all(pgsql.TRAJ_FEATURE_NEW, sql)


def _get_parking_traj_geom_str(device_no, point_timestamp_ms):
    point_timestamp = point_timestamp_ms // 1000
    timestamp_str = point_timestamp.strftime("%Y-%m-%d %H:%M:%S")
    uid = f"{device_no}{timestamp_str}"
    sql = f"""SELECT time_str FROM parking_traj_time_str where uid='{uid}'"""
    data_str = dbutils.fetch_one(pgsql.DEST_TRAJ, sql)
    if not data_str:
        return None
    else:
        data_str_last_time = data_str[0].split('&')
        dt = datetime.strptime(data_str_last_time, "%Y-%m-%d %H:%M:%S")
        timestamp = dt.timestamp()
        return timestamp * 1000


# def _get_collect_data(parking_point: str) -> dict:
#     sql = f"""
#         SELECT id, batch FROM parking_collect_data
#         WHERE ST_Within(ST_GeomFromText('{parking_point}', 4326), parking_geom) and status = 'INIT' and collect_times<3
#         """
#     return dbutils.fetch_all(pgsql.POI_CONFIG, sql)
def _get_collect_data(parking_point: str, parking_traj: str) -> dict:
    """
    1、熄火点是否在面内
    :param parking_point:
    :return:
    """
    # sql = f"""
    #     SELECT id, batch, st_astext(parking_geom) FROM parking_collect_data
    #     WHERE ST_Within(ST_GeomFromText('{parking_point}', 4326), parking_geom)
    #      and ST_NPoints(ST_Intersection(parking_geom, ST_GeomFromText('{parking_traj}', 4326))) > 1
    #      and status = 'INIT' and collect_times<3
    #     """
    sql = f"""
            WITH filtered_data AS (
                SELECT id, batch, parking_geom, source, collect_type
                FROM parking_collect_data
                WHERE status = 'INIT'
                  AND collect_times > 0
            ),
            traj_geom AS (
                SELECT ST_GeomFromText('{parking_traj}', 4326) AS geom
            ),
            point_geom AS (
                SELECT ST_GeomFromText('{parking_point}', 4326) AS geom
            )
            SELECT 
                f.id,
                f.batch,
                ST_AsText(ST_Intersection(f.parking_geom, t.geom)) AS intersection_points,
                f.source,
                f.collect_type
            FROM 
                filtered_data f,
                traj_geom t,
                point_geom p
            WHERE 
                ST_Within(p.geom, f.parking_geom);
                """
    # print(sql)
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _check_total_push_times():
    with rt.RedisTool('aoi') as rt_client:
        try:
            current_date = datetime.now().strftime("%Y%m%d")
            key = f"zhongyuan_pushed_times_{current_date}"
            data = rt_client.redis_conn.get(key)
            if data is None or int(data) < 150000:
                return True
            else:
                return False
        except (redis.ConnectionError, ConnectionResetError) as e:
            logger.error(f"redis error: {e}")
            return False


def _record_push_times():
    with rt.RedisTool('aoi') as rt_client:
        try:
            current_date = datetime.now().strftime("%Y%m%d")
            key = f"zhongyuan_pushed_times_{current_date}"
            rt_client.redis_conn.incr(key)
        except (redis.ConnectionError, ConnectionResetError) as e:
            logger.error(f"redis error: {e}")


def _get_start_timestamp(start_car_timestamp_ms):
    # 获取后 2 分钟的起始时间戳
    start_timestamp_ms = start_car_timestamp_ms + (2 * 60 * 1000)
    # 生成毫秒级时间戳列表
    time_stamps_list = list(range(start_timestamp_ms, start_car_timestamp_ms, 1000))  # 每毫秒递增
    return time_stamps_list


def get_post_start_timestamps(start_car_timestamp, parking_time, is_point_within, device_no):
    """
    计算启动后 2 分钟的时间戳列表，并去除停车时间。
    - 第一个时间戳是启动时间。
    - 从第二个时间戳开始，跳过启动后的 parking_time 分钟，生成时间戳。
    - 总共生成 120 个时间戳（包括启动时间）。
    :param start_car_timestamp: int, 启动时间戳（毫秒）
    :param parking_time: int, 停车时间（分钟）
    :return: list, 时间戳列表（毫秒）
    """
    # 计算停车时间（单位：毫秒）
    parking_time_ms = int(parking_time * 60 * 1000)
    # 计算起始时间（启动时间 + 停车时间）
    start_timestamp = start_car_timestamp + parking_time_ms
    # 计算结束时间（起始时间 + 119 秒，因为第一个时间戳是启动时间）
    end_timestamp = start_timestamp + 119 * 1000  # 119 秒（毫秒）
    # 生成时间戳列表（每秒一个，共 119 个）
    timestamps = list(range(start_timestamp, end_timestamp + 1000, 1000))
    # 将启动时间插入到列表开头
    timestamps.insert(0, start_car_timestamp)

    if is_point_within:
        last_time_stamp = _get_parking_traj_geom_str(device_no, start_car_timestamp)
        if last_time_stamp is not None:
            # 1. 在 last_time_stamp 之前的时间点（不包括第一个时间点）中，每隔 2 个点抽取一个
            before_last = [timestamps[i] for i in range(1, len(timestamps)) if
                           timestamps[i] < last_time_stamp and (i - 1) % 2 == 0]
            # 2. 基于 last_time_stamp 时间点，往后累加 1 分钟的时间戳列表（每秒一个）
            after_last = list(range(last_time_stamp, last_time_stamp + 60 * 1000, 1000))  # 1 分钟，每秒一个

            # 合并结果：启动时间 + 抽取的 before_last + after_last
            timestamps = [timestamps[0]] + before_last + after_last
    filtered_timestamps = timestamps[::2]
    return filtered_timestamps


def get_pre_shutoff_timestamps(end_car_timestamp, parking_time):
    """
    计算熄火前 2 分钟的时间戳列表，并去除停车时间。
    - 起始时间去掉第一秒
    - 最后时间戳保留熄火时间
    :param end_car_timestamp: int, 熄火时间戳（毫秒）
    :param parking_time: int, 停车时间（分钟）
    :return: list, 时间戳列表（毫秒）
    """
    # 计算停车时间和熄火前 2 分钟的时间（单位：毫秒）
    parking_time_ms = int(parking_time * 60 * 1000)
    two_minutes_ms = 2 * 60 * 1000  # 2 分钟（毫秒）
    # 计算起始时间（去除停车时间 + 2 分钟）
    start_timestamp = end_car_timestamp - parking_time_ms - two_minutes_ms
    # 计算结束时间（熄火时间 - 停车时间）
    end_timestamp = end_car_timestamp - parking_time_ms
    # 确保时间范围合理
    if start_timestamp >= end_timestamp:
        return [end_car_timestamp]  # 如果时间无效，则仅返回熄火时间
    # 去掉第一秒（start_timestamp + 1000）
    timestamps = list(range(start_timestamp + 1000, end_timestamp, 1000))
    # 添加最后的熄火时间戳
    timestamps.append(end_car_timestamp)
    filtered_timestamps = timestamps[::2]
    return filtered_timestamps


def _check_point_is_within(parking_traj_geom, within_geom, parking_type):
    # 解析 WKT 数据
    within_geom = loads(within_geom)
    park_geom = loads(parking_traj_geom)

    point = None
    if parking_type == 'end_car':
        point = Point(park_geom.coords[0])

    if parking_type == 'start_car':
        point = Point(park_geom.coords[-1])

    if point is None:
        logger.error(f"parking_type: {parking_type}")
        return True

    # 判断最后一个点是否在 within_geom 上
    is_on_within_geom = within_geom.contains(point) or within_geom.touches(point)
    if is_on_within_geom:
        logger.info(f"点在 within_geom 上")
    else:
        logger.info(f"点不在 within_geom 上")
    return is_on_within_geom


def _process_parking_point(parking_point):
    try:
        park_points_id = parking_point[0]
        device_no = parking_point[1]
        traj_time = parking_point[2]
        point_geom = parking_point[3]
        parking_traj_geom = parking_point[4]
        # 停车场时间，分钟
        parking_time = parking_point[5]
        parking_type = parking_point[6]
        batch_src = parking_point[7]
        timestamp = int(traj_time.timestamp())

        logger.info(f"park_points_id: {park_points_id}, device_no:{device_no}, parking_type:{parking_type},"
                    f" timestamp: {timestamp}, parking_time: {parking_time}")
        collect_datas = _get_collect_data(point_geom, parking_traj_geom)
        for collect_data in collect_datas:
            # 超过总线推送次数上线检测
            if not _check_total_push_times():
                return

            parking_collect_data_id = collect_data[0]
            batch = collect_data[1]
            within_geom = collect_data[2]
            source = collect_data[3]
            collect_type = collect_data[4]

            within_distances = _calculate_geometry_distance(within_geom)
            # 落在面内的熄火轨迹，需要超过30米 - 门前最初定的值
            within_distances_len = 30
            if source == 'common':
                within_distances_len = 5
            if within_distances < within_distances_len:
                logger.info(f"park_points_id: {park_points_id}, within_distances: {within_distances}")
                continue

            is_point_within = _check_point_is_within(parking_traj_geom, within_geom, parking_type)
            logger.info(f"is_point_within: {is_point_within}")
            if is_point_within:
                logger.info(f"点在 within_geom 上, park_points_id: {park_points_id}, "
                            f"parking_collect_data_id: {parking_collect_data_id}")
                continue
            _do_collect(collect_type, parking_type, timestamp, parking_time, parking_collect_data_id,
                        park_points_id, device_no, point_geom, parking_traj_geom, batch, batch_src, is_point_within)
    except Exception as e:
        logger.error(f"error: {e}")


def _do_parkstore(parking_type, timestamp, parking_time, parking_collect_data_id,
                  park_points_id, uuid, point_geom, parking_traj_geom, batch, batch_src):
    # start_car 不处理
    if parking_type == 'start_car':
        return

    timestamp_ms = timestamp * 1000
    time_stamps_list = get_pre_shutoff_timestamps(timestamp_ms, parking_time)
    if timestamp > 0:
        logger.info(f"time_stamps_list: {time_stamps_list}")

    extra_info = {
        "parking_collect_data_id": parking_collect_data_id,
        "park_points_id": park_points_id,
        "point_time": timestamp,
        "device_no": uuid,
        "point_geom": point_geom,
        "parking_traj_geom": parking_traj_geom,
        "request_info": "",
        "batch": batch,
        "remark": {"batch_src": batch_src},
    }
    _push_data(uuid, time_stamps_list, extra_info)


def _do_collect(collect_type, parking_type, timestamp, parking_time, parking_collect_data_id,
                  park_points_id, device_no, point_geom, parking_traj_geom, batch, batch_src, is_point_within):
    timestamp_ms = timestamp * 1000
    time_stamps_list = []

    # 只采入口
    if collect_type == 0 and parking_type == 'end_car':
        time_stamps_list = get_pre_shutoff_timestamps(timestamp_ms, parking_time)
    # 只采出口
    if collect_type == 1 and parking_type == 'start_car':
        time_stamps_list = get_post_start_timestamps(timestamp_ms, parking_time, is_point_within, device_no)
    # 采出入口
    if collect_type == 2:
        if parking_type == 'start_car':
            time_stamps_list = get_post_start_timestamps(timestamp_ms, parking_time, is_point_within, device_no)
        if parking_type == 'end_car':
            time_stamps_list = get_pre_shutoff_timestamps(timestamp_ms, parking_time)

    if not time_stamps_list:
        return
    if timestamp > 0:
        logger.info(f"time_stamps_list: {time_stamps_list}")

    extra_info = {
        "parking_collect_data_id": parking_collect_data_id,
        "park_points_id": park_points_id,
        "point_time": timestamp,
        "device_no": device_no,
        "point_geom": point_geom,
        "parking_traj_geom": parking_traj_geom,
        "request_info": "",
        "batch": batch,
        "remark": {"batch_src": batch_src},
    }
    _push_data(device_no, time_stamps_list, extra_info)


def _do_common(parking_type, timestamp, parking_time, parking_collect_data_id,
                  park_points_id, uuid, point_geom, parking_traj_geom, batch, batch_src):

    timestamp_ms = timestamp * 1000
    time_stamps_list = []
    if parking_type == 'start_car':
        time_stamps_list = get_post_start_timestamps(timestamp_ms, parking_time)
    if parking_type == 'end_car':
        time_stamps_list = get_pre_shutoff_timestamps(timestamp_ms, parking_time)
    if not time_stamps_list:
        return
    if timestamp > 0:
        logger.info(f"time_stamps_list: {time_stamps_list}")

    extra_info = {
        "parking_collect_data_id": parking_collect_data_id,
        "park_points_id": park_points_id,
        "point_time": timestamp,
        "device_no": uuid,
        "point_geom": point_geom,
        "parking_traj_geom": parking_traj_geom,
        "request_info": "",
        "batch": batch,
        "remark": {"batch_src": batch_src},
    }
    _push_data(uuid, time_stamps_list, extra_info)


def main():
    batch_src = _get_push_batch()
    if not batch_src:
        logger.info(f"没有数据")
        return

    logger.info(f"批次 {batch_src} 开始处理")
    all_parking_points = _get_parking_points_by_batch_src(batch_src)
    if not all_parking_points:
        logging.info(f"批次 {batch_src} 没有熄火点点数据")
        return

    with multiprocessing.Pool(processes=10) as pool:
        results = pool.map(_process_parking_point, all_parking_points)

    logging.info(f"批次 {batch_src} 处理完成: {results}")


if __name__ == '__main__':
    start_time = time.time()
    formatted_start_time = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')
    print("start_time_main:", formatted_start_time)

    main()
    # _test_case()

    end_time = time.time()
    formatted_end_time = datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')
    print("end_time_main:", formatted_end_time)
    elapsed_time_seconds = end_time - start_time
    elapsed_time_minutes = elapsed_time_seconds / 60
    print(
        "process_time---:",
        "|",
        elapsed_time_seconds,
        "s",
        "|",
        elapsed_time_minutes,
        "min",
    )

