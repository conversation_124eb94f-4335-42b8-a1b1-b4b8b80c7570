"""
统计1
"""
import json
import time
import os
import subprocess
import tempfile
import random
from typing import List, Dict, Tuple
import csv

import src.tools.redis_tool as rt
import redis
import requests
from datetime import datetime
import multiprocessing
from multiprocessing import Pool
from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from datetime import datetime
from loguru import logger
from shapely.geometry import LineString
from itertools import groupby
import pymysql


def _create_vdust_connection():
    """
    创建 mysql 数据库连接
    """
    # host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host="***********", port=int(6082), user="zhaoxiaolong_r",
                           password="offline_read", db="vdust", charset="utf8", autocommit=1)


def _random_data(source):
    # sql = (f"select a.id,b.task_id,a.source,st_astext(a.parking_geom),st_astext(b.point_geom),"
    #        f" TO_CHAR(TO_TIMESTAMP(b.point_time), 'YYYY-MM-DD HH24:MI:SS') as point_time,"
    #        f" b.request_info, st_astext(b.parking_traj_geom) "
    #        f"from parking_collect_data a left join parking_points_push b on a.id=b.parking_collect_data_id "
    #        f"where a.status='MATCHED' and b.created_at>='2025-02-28 13:10:00' and source='{source}' "
    #        f"and b.task_id !='None' ORDER BY RANDOM() LIMIT 20;")
    sql = ("select * from parking_collect_data a left join parking_points_push b on a.id=b.parking_collect_data_id"
           " where a.cityname='绍兴市' and b.match_result=1")
    logger.info(sql)
    data = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    export_file = []
    for item in data:
        id, task_id, source, parking_geom, point_geom, point_time, request_info, parking_traj_geom = item
        logger.info(f"{id},{task_id},{source}")
        business_id_str = f"`{task_id}"
        point_time_str = f"`{point_time}"
        export_file.append([
            id, business_id_str, source, parking_geom, point_geom, point_time, request_info, parking_traj_geom
        ])
    file_path = f"/home/<USER>/fanjiabin/aoi-ml/output/src/parking/collect/random_0228_{source}_11.csv"
    # 使用 with 语句打开文件
    with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
        writer = csv.writer(file, delimiter='\t')
        # 写入每一行数据
        for row in export_file:
            writer.writerow(row)

    print("数据已成功导出.")


def _get_match_status(task_id):
    conn = _create_vdust_connection()
    cursor = conn.cursor()
    sql = f"""
           select m.update_time,m.match_id,m.match_status,m.match_cnt from task t, match_task mt, matches m where t.task_source=45
            and mt.task_id=t.task_id and mt.match_id=m.match_id and t.business_id='{task_id}';
           """
    # print(sql)
    cursor.execute(sql)
    ree = cursor.fetchall()
    # logger.info(f"匹配结果：{ree}")
    return ree


def main():
    """
    主函数
    :return:
    """
    # 召回率、准确率、众源轨迹覆盖率 本周产出；无效数据比例分析->原因、解决方案

    # 投放池面总量
    sql = f"select count(*) from parking_collect_data where source='parkstore'"
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    total = res[0][0]
    print(f"投放池面总量: {total}")

    # 熄火点覆盖个数
    sql = f"select count(*) from parking_points_push where created_at>='2025-03-03 00:00:00'"
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    zy_coverage = res[0][0]
    print(f"众源轨迹覆盖面: {zy_coverage}")
    # 众源轨迹覆盖率
    # print(f"总投放池：{total}")
    # print(f"众源轨迹覆盖面：{zy_coverage}")
    print("%.3f%%" % ((zy_coverage / total) * 100))

    # 总投放面
    sql = (f"select count(distinct parking_collect_data_id) from parking_points_push"
           f" where created_at>='2025-03-03 00:00:00' and task_id != 'None'")
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    total_push_parking = res[0][0]
    print(f"总投放面: {total_push_parking}")


    sql = """select distinct parking_collect_data_id from parking_points_push 
       where created_at>'2025-03-03 00:00:00' and task_id != 'None'"""
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    logger.info(f"{len(res)}")
    for item in res:
        # print(item[0])
        parking_collect_data_id = item[0]
        task_id = item[1]
    status = _get_match_status(task_id)
    if status is not None and len(status) > 0 and status[0][2] == 21:
        # _update_match_status(parking_collect_data_id, 'MATCHED')
        logger.info(f"{parking_collect_data_id}, {status[0][2]}")



    # 匹配面
    sql = f"select source, count(*) from parking_collect_data where status!='INIT' group by source;"
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    print(f"匹配面: {res}")
    # total_match = res[0]
    # 召回率
    # print(f"总投放面：{total_push}")
    # print(f"匹配面：{total_match}")
    # print("%.3f%%" % ((total_match / total_push) * 100))

    # 随机导出已召回面
    # _random_data('parkstore')
    # _random_data('common')


if __name__ == "__main__":
    main()