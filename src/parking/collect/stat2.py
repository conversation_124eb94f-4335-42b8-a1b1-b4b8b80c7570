#!/usr/bin/env python
# -*- coding: utf-8 -*-
########################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
########################################################################
import json
import time
import os
import subprocess
import tempfile
import random
from typing import List, Dict, Tuple
import csv

import src.tools.redis_tool as rt
import redis
import requests
from datetime import datetime
import multiprocessing
from multiprocessing import Pool
from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from datetime import datetime
from loguru import logger
from shapely.geometry import LineString
from itertools import groupby
import pymysql


def _create_vdust_connection():
    """
    创建 mysql 数据库连接
    """
    # host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host="***********", port=int(6082), user="zhaoxiaolong_r", password="offline_read", db="vdust", charset="utf8", autocommit=1)


def _random_data(source):
    sql = (f"select a.id,b.task_id,a.source,st_astext(a.parking_geom),st_astext(b.point_geom), TO_CHAR(TO_TIMESTAMP(b.point_time), 'YYYY-MM-DD HH24:MI:SS') as point_time, b.request_info, st_astext(b.parking_traj_geom) "
           f"from parking_collect_data a left join parking_points_push b on a.id=b.parking_collect_data_id "
           f"where a.status='MATCHED' and b.created_at>='2025-02-28 13:10:00' and source='{source}' and b.task_id !='None' ORDER BY RANDOM() LIMIT 20;")
    logger.info(sql)
    data = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    export_file = []
    for item in data:
        id, task_id, source, parking_geom, point_geom, point_time, request_info, parking_traj_geom = item
        logger.info(f"{id},{task_id},{source}")
        business_id_str = f"`{task_id}"
        point_time_str = f"`{point_time}"
        export_file.append([
            id, business_id_str, source, parking_geom, point_geom, point_time, request_info, parking_traj_geom
        ])
    file_path = f"/home/<USER>/fanjiabin/aoi-ml/output/src/parking/collect/random_0228_{source}_11.csv"
    # 使用 with 语句打开文件
    with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
        writer = csv.writer(file, delimiter='\t')
        # 写入每一行数据
        for row in export_file:
            writer.writerow(row)

    print("数据已成功导出.")


def main():
    # 召回率、准确率、众源轨迹覆盖率 本周产出；无效数据比例分析->原因、解决方案

    # 投放池面总量
    # sql = f"select source, count(*) from parking_collect_data group by source"
    # res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    # # total = res[0]
    # print(f"投放池面总量: {res}")
    #
    # # # 众源轨迹覆盖面
    # sql = f"select source, count(*) from parking_collect_data where batch!='20250220' group by source"
    # res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    # print(f"众源轨迹覆盖面: {res}")
    # # 众源轨迹覆盖率
    # # print(f"总投放池：{total}")
    # # print(f"众源轨迹覆盖面：{zy_coverage}")
    # # print("%.3f%%" % ((zy_coverage / total) * 100))
    #
    # # 总投放面
    # sql = f"select b.source, count(distinct a.parking_collect_data_id) from parking_points_push a left join parking_collect_data b on a.parking_collect_data_id=b.id where a.created_at>='2025-02-21 08:00:00' group by b.source;"
    # res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    # print(f"总投放面: {res}")
    #
    # # 匹配面
    # sql = f"select source, count(*) from parking_collect_data where status!='INIT' group by source;"
    # res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    # print(f"匹配面: {res}")
    # total_match = res[0]
    # 召回率
    # print(f"总投放面：{total_push}")
    # print(f"匹配面：{total_match}")
    # print("%.3f%%" % ((total_match / total_push) * 100))

    # sql = "select id,st_astext(parking_geom), batch from parking_collect_data where created_at >= '2025-03-01 00:00:00'"
    sql = "select id, st_astext(parking_geom), st_astext(ST_Buffer(parking_geom::geography, 20)), batch from parking_collect_data where created_at >= '2025-03-01 00:00:00'"
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    print(len(res))
    # time_slice = int(time.time() / 600) - 1
    # start_time = (int(time.time() / 600) - 2) * 600
    # end_time = (int(time.time() / 600) - 1) * 600
    # logger.info(f"{time_slice}：{start_time}, {end_time}")

    for item in res:
        id, old_geom, geom_buff, batch = item
        # logger.info(f"{id}, {old_geom}, {geom_buff}")
        batch_src = f"parking_{batch}"
        sql = (f"SELECT parking_type, count(*) FROM parking_points WHERE batch_src='{batch_src}' "
               f"and ST_Within(geom, ST_SetSRID(ST_GeomFromText('{geom_buff}'), 4326)) group by parking_type")
        track_info = dbutils.fetch_all(pgsql.TRAJ_FEATURE, sql)
        logger.info(f"{id}, {len(track_info)}, {track_info}")


if __name__ == "__main__":
    main()