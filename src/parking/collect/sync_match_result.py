#!/usr/bin/env python
# -*- coding: utf-8 -*-
########################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
########################################################################

"""
Description
同步众源采集结果，更新collect_times字段
"""

import json
import time
import os
import subprocess
import tempfile
import random
from typing import List, Dict, Tuple

import src.tools.redis_tool as rt
import redis
import requests
from datetime import datetime
import multiprocessing
from multiprocessing import Pool
from src.parking.recognition import dbutils
from src.tools.conf_tools import get_mysql_conf
from src.tools import pgsql, tsv
from datetime import datetime
from loguru import logger
from shapely.geometry import LineString
from itertools import groupby
import pymysql


def _create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def _create_vdust_connection():
    """
    创建 mysql 数据库连接
    """
    # host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host="***********",
                           port=int(6082),
                           user="zhaoxiaolong_r",
                           password="offline_read",
                           db="vdust",
                           charset="utf8",
                           autocommit=1)


def _update_match_result(push_id, parking_collect_data_id):
    conn = pgsql.get_connection(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"update parking_points_push set match_result = 1 where id = {push_id}"
    logger.info(f"{push_id}, {sql}")
    res = cursor.execute(sql)
    if res == 0:
        logger.error(f"更新parking_points_push失败, {sql}")
        conn.rollback()
        return

    sql = f"""update parking_collect_data
    set collect_times = 0 where id = {parking_collect_data_id};"""
    res = cursor.execute(sql)
    if res == 0:
        logger.error(f"更新parking_collect_data失败, {sql}")
        conn.rollback()
        return
    conn.commit()

    logger.info(f"{parking_collect_data_id}, {push_id}, match done.")


def _get_match_status(task_id):
    conn = _create_vdust_connection()
    cursor = conn.cursor()
    sql = f"""
           select m.update_time,m.match_id,m.match_status,m.match_cnt from task t, 
           match_task mt, matches m where t.task_source=45
            and mt.task_id=t.task_id and mt.match_id=m.match_id and t.business_id='{task_id}';
           """
    # print(sql)
    cursor.execute(sql)
    ree = cursor.fetchall()
    # logger.info(f"匹配结果：{ree}")
    return ree


def main():
    """
    主函数
    :return:
    """
    sql = """select parking_collect_data_id,task_id,id from parking_points_push 
       where created_at>'2025-03-06 00:00:00' and task_id != 'None' and match_result=0"""
    # sql = """select parking_collect_data_id,task_id,id from parking_points_push
    # where created_at>'2025-03-06 00:00:00' and task_id != 'None' and parking_collect_data_id in
    # (select id from parking_collect_data where source='common' and status='INIT' and collect_times>0)"""
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    logger.info(f"{len(res)}")
    for item in res:
        # print(item[0])
        parking_collect_data_id = item[0]
        task_id = item[1]
        push_id = item[2]

        status = _get_match_status(task_id)
        if status is not None and len(status) > 0 and status[0][2] == 21:
            _update_match_result(push_id, parking_collect_data_id)


if __name__ == "__main__":
    main()