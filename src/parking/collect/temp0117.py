"""
根据差分结果回捞人工核实结果
"""
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests
import csv
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
from shapely.ops import transform
from shapely.ops import unary_union
import pyproj
import random

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from shapely import Polygon, LineString

from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER


def _create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def _get_pv_by_bid(bid):
    sql = f"select click_pv from poi where bid='{bid}'"
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _get_bid_by_bid(bid):
    sql = f"select relation_bid from poi where bid='{bid}'"
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _get_gate_bid_by_main_bid(main_bid):
    beeflow_connection = _create_beeflow_connection()

    sql = f"select bid from parking where parent_id='{main_bid}'"
    data1 = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    if data1 is None:
        print(f"{main_bid}, 没有找到")
    for bid1 in data1:
        if bid1[0] is not None:
            # print(bid1[0])

            sql = f"select bid from parking where parent_id='{bid1[0]}'"
            data2 = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
            for bid2 in data2:
                if bid2[0] is not None:
                    # print(bid2[0])

                    sql = (f"select turing_qid,from_unixtime(create_time),from_unixtime(update_time) "
                           f"from parking_gate_push_turing where bid = '{bid2[0]}'")
                    with beeflow_connection.cursor() as cursor:
                        cursor.execute(sql)
                        res = cursor.fetchone()
                        if res is None:
                            # logger.info(f"main_bid: {main_bid} 没有找到")
                            continue
                        else:
                            print(f"{main_bid},{res[0]},{res[1]},{res[2]}")


if __name__ == "__main__":
    """
    主函数
    """
    bid_list = [
        '10090743682689156850',
        '10419865832154974327',
        '10932472610010064057',
        '11049728878029928214',
        '11179315608886422663',
        '11417372558015701590',
        '11985888464152509706',
        '12038288860015509780',
        '12279881115099938663',
        '12734289584485479930',
        '1278758607000969010',
        '12789880909821293291',
        '12790348321519639848',
        '13046064231642167299',
        '1307782066878474337',
        '13613089733564477978',
        '13785504969038995830',
        '13810665786392470723',
        '1455098202739099092',
        '14624492495218046494',
        '14964099656343544130',
        '15102316894202099619',
        '15109459631101039461',
        '15298298666890503892',
        '15598455422390480580',
        '15635787932389165838',
        '156473169141773554',
        '15835715712155676453',
        '15858567216139782911',
        '16092116919968017316',
        '16131779459448136169',
        '16242381866874059788',
        '16250018951192735106',
        '16615078856288398272',
        '17399224249447017850',
        '17625580836776574975',
        '18180980525571496033',
        '18418484555760227035',
        '18423837930184865620',
        '18433381391302098316',
        '2019939851885439140',
        '2222516326753994143',
        '2227626818536087682',
        '2418929898202234586',
        '2690046165323288455',
        '2844437304948271641',
        '2847122109945832045',
        '2872460979211548311',
        '297455777469779704',
        '3086032901321362492',
        '3272774853920051297',
        '3704916583429748651',
        '4125196165806342596',
        '4183330159621910199',
        '4481959160863864815',
        '4725112602258058418',
        '4754603237943359117',
        '5307034330727656194',
        '5993765983242898101',
        '6073983802283207604',
        '6144735268425139081',
        '6234383767448992464',
        '6423514468459114128',
        '668310593296217060',
        '6818136303395110015',
        '7062097901594564364',
        '7356820023501716213',
        '7378116787422716946',
        '7391480408273421925',
        '7503934453325645177',
        '7848866697214163236 ',
        '805941252464637105',
        '8218526770751048038',
        '8441530622990804924',
        '8957871464487757973',
        '9012574276973084025',
        '9396306666402048057',
        '9464422292543698083',
        '9532810740074299703',
        '9732415024590702698',
    ]
    for bid in bid_list:
        _get_gate_bid_by_main_bid(bid)

    # beeflow_connection = _create_beeflow_connection()
    # sql = "select bid,turing_qid,problem_id from parking_gate_push_turing
    # where create_time>'2025-01-10' and status=1 and bid!='';"
    # with beeflow_connection.cursor() as cursor:
    #     cursor.execute(sql)
    #     res = cursor.fetchall()
    #
    #     for row in res:
    #         bid = row[0]
    #         turing_qid = row[1]
    #         problem_id = row[2]
    #
    #         park_bid =_get_bid_by_bid(bid)
    #         if not park_bid:
    #             continue
    #         park_bid = park_bid[0]
    #         if not park_bid:
    #             continue
    #
    #         parent_bid = _get_bid_by_bid(park_bid)
    #         if not parent_bid:
    #             continue
    #         parent_bid = parent_bid[0]
    #         if not parent_bid:
    #             continue
    #
    #         pv_info = _get_pv_by_bid(parent_bid)
    #         if not pv_info:
    #             continue
    #         pv = pv_info[0]
    #         if pv > 10:
    #             print(turing_qid,problem_id,bid,pv)
    #         # print(f"{row[0]} {pv_info[0]}")
    # beeflow_connection.close()

