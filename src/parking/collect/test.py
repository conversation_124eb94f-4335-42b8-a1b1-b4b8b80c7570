# -*- coding: utf-8 -*-
"""
通过消息队列执行
"""
import json
import time
import os
import subprocess
import tempfile

import src.tools.redis_tool as rt
import redis
import requests
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Pool


def post_request(url, payload):
    # 设置请求头，告知服务器这是一个 JSON 请求
    headers = {
        'Content-Type': 'application/json'
    }
    try:
        # 发送 POST 请求
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        # print(type(json.loads(response.text)))
        return json.loads(response.text)
    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return None


def _cal_parking_points(file_path, batch_src):
    cm = f"py39 calc_for_caiji_v3.py"
    script_path = f"cd /home/<USER>/lifan14/parking_point_test/calc_parking_points_distributed_v5_laofan && {cm}"
    command = (f"source ~/fanjiabin/bashrc && {script_path} {file_path} {batch_src}")
    print(f"Processing {command}")
    try:
        # 调用子进程运行脚本
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        print(f"Processed {file_path}")
        print(f"Processed Result: {result.stdout}")
    except Exception as e:
        print(f"Error processing : {e}")


def run(file_path, batch_src):
    """
    run
    """
    start_time = time.time()
    formatted_time = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')
    print("start_time:", formatted_time)

    print("step_1: get_all_device_info")

    print("step_2: cal_parking_points")

    print("step_3: find_push_data")

    end_time = time.time()
    elapsed_time_seconds = end_time - start_time
    elapsed_time_minutes = elapsed_time_seconds / 60
    print(
        "process_time:",
        "|",
        elapsed_time_seconds,
        "s",
        "|",
        elapsed_time_minutes,
        "min",
    )

    # psql -U traj_feature_rw -d traj_feature_db -W traj_feature_rw -p 9432 -h 10.56.135.223
    #  select traj_time,user_id from parking_points where batch_src ='caiji_test' limit 1;;
    # start = 1739695800
    # end = 1739696400
    # device_no = 'ATMSW13H13K12N39'
    # post_result = post_request("http://10.11.131.11:8456/tracksrv/TrackService/FetchTrack",
    #                  {"start_time": start, "end_time": end, "device_no": str(device_no)})
    # if post_result['status'] == 0:
    #     track_json = post_result['tracks']
    #     line_string_dict = []
    #     for track in track_json:
    #         print(f"{track['x']} {track['y']}")
        #     line_string_dict = line_string_dict.append(line_string)
        # print(line_string_dict.join(','))
        # with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json") as temp_file:
        #     json.dump(post_result['tracks'], temp_file, indent=4)  # 写入 JSON 数据
        #     temp_filename = temp_file.name  # 获取文件路径
        #     print(temp_filename)
        #     _process_file(temp_filename, f"test_{device_no}")


def process_device_no(device_no, start, end):
    payload = {"start_time": start, "end_time": end, "device_no": str(device_no)}
    post_result = post_request("http://10.11.131.11:8456/tracksrv/TrackService/FetchTrack", payload)

    if post_result is not None and post_result['status'] == 0 and 'tracks' in post_result and len(post_result['tracks']) != 0:
        # 写入 JSON 数据到临时文件
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json") as temp_file:
            json.dump(post_result['tracks'], temp_file)  # 写入 JSON 数据
            temp_file.flush()  # 确保数据写入缓冲区
            os.fsync(temp_file.fileno())  # 强制写入磁盘
            temp_filename = temp_file.name
            _cal_parking_points(temp_filename, f"test_20250217_01")

def process_mesh_id(mesh_id, start, end):
    # 直接在主进程中处理
    with rt.RedisTool('aoi') as rt_client:
        try:
            device_no_list = rt_client.redis_conn.smembers(mesh_id)
            return device_no_list
        except (redis.ConnectionError, ConnectionResetError) as e:
            print(f"Error: {e}")
            return []

def main1():
    print("step_1: get_all_device_info")


def main():
    time_slice = int(time.time() / 600) - 1
    start = (int(time.time() / 600) - 2) * 600
    end = (int(time.time() / 600) - 1) * 600
    print(time_slice, start, end)

    active_mesh_key = f"zhongyuan_active_mesh_{time_slice}"

    try:
        # 在主进程中创建 Redis 连接
        with rt.RedisTool('aoi') as rt_client:
            res = rt_client.redis_conn.smembers(active_mesh_key)

            # 只在主进程中创建池并处理
            with Pool(processes=10) as pool:
                # 处理每个 mesh_id
                device_no_lists = pool.starmap(process_mesh_id, [(mesh_id, start, end) for mesh_id in res])

                # 针对每个 device_no_list 进行处理
                for device_no_list in device_no_lists:
                    with Pool(processes=10) as pool_device:
                        pool_device.starmap(process_device_no,
                                            [(device_no, start, end) for device_no in device_no_list])

    except (redis.ConnectionError, ConnectionResetError) as e:
        print(f"Error: {e}")


if __name__ == '__main__':
    # time_slice = int(time.time()/600) - 1
    # start = (int(time.time()/600) - 2)*600
    # end = (int(time.time()/600) - 1)*600
    # print(time_slice, start, end)
    # active_mesh_key = f"zhongyuan_active_mesh_{time_slice}"
    # with rt.RedisTool('aoi') as rt_client:
    #     try:
    #         res = rt_client.redis_conn.smembers(active_mesh_key)
    #         for mesh_id in res:
    #             device_no_list = rt_client.redis_conn.smembers(mesh_id)
    #             json_str = json.dumps(list(device_no_list))
    #             line = f"{mesh_id}:{json_str}"
    #             for device_no in device_no_list:
    #                 payload = {"start_time": start, "end_time": end, "device_no": str(device_no)}
    #                 post_result = post_request("http://10.11.131.11:8456/tracksrv/TrackService/FetchTrack", payload)
    #                 if post_result['status'] == 0 and 'tracks' in post_result and len(post_result['tracks']) != 0:
    #                     # track_json = json.dumps(post_result['tracks'])
    #                     with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json") as temp_file:
    #                         json.dump(post_result['tracks'], temp_file)  # 写入 JSON 数据
    #                         temp_file.flush()
    #                         os.fsync(temp_file.fileno())
    #                         temp_filename = temp_file.name
    #                         _cal_parking_points(temp_filename, f"test_{device_no}")
    #
    #     except (redis.ConnectionError, ConnectionResetError) as e:
    #         print(e)
    start_time = time.time()
    formatted_start_time = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')
    print("start_time_main:", formatted_start_time)

    main()

    end_time = time.time()
    formatted_end_time = datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')
    print("end_time_main:", formatted_end_time)
    elapsed_time_seconds = end_time - start_time
    elapsed_time_minutes = elapsed_time_seconds / 60
    print(
        "process_time---:",
        "|",
        elapsed_time_seconds,
        "s",
        "|",
        elapsed_time_minutes,
        "min",
    )

