#!/usr/bin/env python
# -*- coding: utf-8 -*-
########################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
########################################################################

import json
import time
import os
import subprocess
import tempfile
import random
from typing import List, Dict, Tuple

import src.tools.redis_tool as rt
import redis
import requests
from datetime import datetime
import multiprocessing
from multiprocessing import Pool
from src.parking.recognition import dbutils
from src.tools.conf_tools import get_mysql_conf
from src.tools import pgsql, tsv
from datetime import datetime
from loguru import logger
from shapely.geometry import LineString
from itertools import groupby
import pymysql


def _create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def _create_vdust_connection():
    """
    创建 mysql 数据库连接
    """
    # host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host="***********", port=int(6082), user="zhaoxiaolong_r", password="offline_read", db="vdust", charset="utf8", autocommit=1)


def _get_parkstore_parking(cityname):
    """
    获取待推送的数据
    """
    sql = f"select distinct strategy_id, ST_AsText(geom) from park_storefront_post_zhongyuan where city = '{cityname}'"
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _import_data(source, source_id, parking_geom, batch):
    try:
        poi_conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
        poi_cursor = poi_conn.cursor()

        aoi_conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
        aoi_cursor = aoi_conn.cursor()

        sql1 = (f"SELECT mesh_id, cityname FROM mesh_conf_wkt "
               f"WHERE ST_Intersects(wkt, ST_GeomFromText('{parking_geom}', 4326)) "
               f"OR ST_Contains(wkt, ST_GeomFromText('{parking_geom}', 4326)) limit 1")
        aoi_cursor.execute(sql1)
        mesh_info = aoi_cursor.fetchone()
        if mesh_info is None:
            raise Exception(f"{parking_geom} not found in any mesh.")
        mesh_id, cityname = mesh_info[0], mesh_info[1]

        if cityname not in ['上海市', '北京市']:
            raise Exception(f"{cityname} is not Shanghai.")


        sql = f"""insert into parking_collect_data (source, source_id, parking_geom, mesh_id, cityname, batch)
        values('{source}', '{source_id}', st_geomfromtext('{parking_geom}', 4326), '{mesh_id}', '{cityname}', '{batch}')
            """
        res = poi_cursor.execute(sql)
        logger.info(res)
    except Exception as e:
        logger.error(e)


# 用于启动多个进程
def process_data_in_parallel(data):
    # 这里 data 是你传入的数据，每个进程会处理其中的一项
    with multiprocessing.Pool(processes=20) as pool:
        results = pool.starmap(_import_data, data)
        return results

# def _run_import():
    # city_name = '上海市'
    # data = _get_parkstore_parking(city_name)
    # logger.info(f"{city_name}：{len(data)}")
    # for item in data:
    #     _import_data("parkstore", item[0], item[1], "20250219")

def _update_match_status(id, status):
    poi_conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    poi_cursor = poi_conn.cursor()
    # sql = f"update parking_collect_data set status = 'MATCHING' where id = {id} and collect_times>0 and collect_times<3"
    # res = poi_cursor.execute(sql)

    sql = f"update parking_collect_data set status = '{status}' where id = {id}"
    res = poi_cursor.execute(sql)
    logger.info(f"{id}, {res}")


def _update_match_times(id):
    poi_conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    poi_cursor = poi_conn.cursor()
    # sql = f"update parking_collect_data set status = 'MATCHING' where id = {id} and collect_times>0 and collect_times<3"
    # res = poi_cursor.execute(sql)

    sql = f"update parking_collect_data set match_times = match_times+1 where id = {id}"
    res = poi_cursor.execute(sql)
    logger.info(f"{id}, {res}")


def _get_data_info(info_id = 116185):
    sql = f"select id from parking_collect_data where source_id = '{info_id}'";
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if res is None:
        return None
    parking_collect_data_id = res[0]

    sql = f"select task_id from parking_points_push where parking_collect_data_id = {parking_collect_data_id} and match_result=1;"
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    if res is None or len(res) == 0:
        return None

    for item in res:
        task_id = item[0]
        print(task_id)

        callback_data = _get_callback_info(task_id)
        if callback_data is None:
            return None
        callback_data_dict = json.loads(callback_data[1])


def _update_match_result(push_id):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    sql = f"update parking_points_push set match_result = 1 where id = {push_id}"
    logger.info(f"{push_id}, {sql}")
    res = cursor.execute(sql)
    logger.info(f"{push_id}, {res}")


def _get_match_status(task_id):
    conn = _create_vdust_connection()
    cursor = conn.cursor()
    sql = f"""
           select m.update_time,m.match_id,m.match_status,m.match_cnt from task t, match_task mt, matches m where t.task_source=45
            and mt.task_id=t.task_id and mt.match_id=m.match_id and t.business_id='{task_id}';
           """
    # print(sql)
    cursor.execute(sql)
    ree = cursor.fetchall()
    # logger.info(f"匹配结果：{ree}")
    return ree


def _get_callback_info(task_id):
    beeflow_connection = _create_beeflow_connection()
    sql = f"select task_id, content from collect_task_callback_records where task_id='{task_id}' group by task_id"
    with beeflow_connection.cursor() as cursor:
        cursor.execute(sql)
        res = cursor.fetchone()
        return res


def main():
    sql = """select parking_collect_data_id,task_id,id from parking_points_push 
       where created_at>'2025-03-06 00:00:00' and task_id != 'None' and match_result=0"""
    # sql = """select parking_collect_data_id,task_id,id from parking_points_push
    # where created_at>'2025-03-06 00:00:00' and task_id != 'None' and parking_collect_data_id in
    # (select id from parking_collect_data where source='common' and status='INIT' and collect_times>0)"""
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    logger.info(f"{len(res)}")
    for item in res:
        # print(item[0])
        parking_collect_data_id = item[0]
        task_id = item[1]
        push_id = item[2]

        status = _get_match_status(task_id)
        if status is not None and len(status) > 0 and status[0][2] == 21:
            # _update_match_status(parking_collect_data_id, 'MATCHED')
            # _update_match_times(parking_collect_data_id)
            _update_match_result(push_id)
            # logger.info(f"{parking_collect_data_id}, {status[0][2]}")
        # status = _get_callback_status(task_id)
        # if status is True:
        #     _update_match_status(parking_collect_data_id, 'MATCHED')
        # else:
        #     _update_match_status(parking_collect_data_id, 'PUSHED')
        #     logger.info(f"{item[0]}, {status}")

if __name__ == "__main__":
    # sql = f"select 'common' as source, bid as source_id, st_astext(geom) as parking_geom, '20250220' as batch from aoi_post_zhongyuan"
    # data = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    # process_data_in_parallel(data)

    # main()
    _get_data_info(139182)
