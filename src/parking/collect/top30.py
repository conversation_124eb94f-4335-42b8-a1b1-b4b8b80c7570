"""
统计
"""
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests
import csv
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
from shapely.ops import transform
from shapely.ops import unary_union
import pyproj
import random

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from shapely import Polygon, LineString

from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER


def create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def _create_vdust_connection():
    """
    创建 mysql 数据库连接
    """
    # host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host="***********", port=int(6082), user="zhaoxiaolong_r",
                           password="offline_read", db="vdust", charset="utf8", autocommit=1)


def get_prime_id_from_file(file_path: Path, position_tags: set[str], pv_tags: set[str]):
    """
    获取文件中符合条件的 prime_id
    :param file_path:
    :param position_tags:
    :param pv_tags:
    :return:
    """
    idx_prime_id = 1
    idx_pv_tag = -2
    idx_position_tags = -1

    rows = [(x[idx_prime_id], x[idx_pv_tag], set(x[idx_position_tags].split(","))) for x in tsv.read_tsv(file_path)]
    for prime_id, pv_tag, p_tags in rows:
        if pv_tag in pv_tags and p_tags & position_tags:
            yield prime_id


def _get_storefront_face_data():
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    # sql = (f"select b.geom,b.id,b.face_id,b.task_id from park_storefront_task a join "
    #        f"park_storefront_strategy b "
    #        f"on a.task_id = b.task_id where a.batch in ('beijing_except_haidian_20241208',"
    #        f" 'beijing_except_haidian_20241221_quanjing') and b.step = 'VERIFIED' "
    #        f"and a.status!='END'")
    sql = (f"select b.geom,b.id,b.face_id,b.task_id from park_storefront_task a join "
           f"park_storefront_strategy b "
           f"on a.task_id = b.task_id where a.batch in ('shanghai_partial_20241114') and b.step = 'VERIFIED' "
           f"and a.status!='END'")
    cursor.execute(sql)
    data_list = cursor.fetchall()
    for data in data_list:
        geom = data[0]
        strategy_id = data[1]
        face_id = data[2]
        task_id = data[3]

        new_geom = shapely.wkt.loads('POLYGON EMPTY')

        insert_sql = (f"insert into park_storefront_post_zhongyuan(geom,strategy_id,"
                      f"face_id,batch,task_id,new_geom,city) values("
                      f"'{geom}',{strategy_id},'{face_id}','20250127','{task_id}',"
                      f"st_geomfromtext('{new_geom}', 4326), '上海市')")
        # print(insert_sql)

        cursor.execute(insert_sql)
        print(f"{strategy_id}插入成功")


def _top30_face_data(strategy_id, city):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"select id,geom,face_id,task_id from park_storefront_strategy where id={strategy_id}"
    cursor.execute(sql)
    data_info = cursor.fetchone()
    strategy_id = data_info[0]
    geom = data_info[1]
    face_id = data_info[2]
    task_id = data_info[3]
    new_geom = shapely.wkt.loads('POLYGON EMPTY')

    insert_sql = (f"insert into park_storefront_post_zhongyuan(geom,strategy_id,"
                  f"face_id,batch,task_id,new_geom,city) values("
                  f"'{geom}',{strategy_id},'{face_id}','20250127','{task_id}', "
                  f"st_geomfromtext('{new_geom}', 4326), '{city}')")
    # print(insert_sql)

    cursor.execute(insert_sql)
    print(f"{strategy_id}插入成功")



if __name__ == '__main__':
    _get_storefront_face_data()
    # 调用
    # cd /home/<USER>/dingping/aoi-ml/src/parking/storefront/product
    #  {city}_{date}/prime/priority_analysis.tag.tsv
    # batch_dir = f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    # changsha_20250123/prime/priority_analysis.tag.tsv"

    # batch_dir_list = [
    #     # ['长沙市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     changsha_20250123/prime/priority_analysis.tag.tsv"],
    #     ['成都市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     chengdu_20250116/prime/priority_analysis.tag.tsv"],
    #     ['重庆市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     chongqing_20250117/prime/priority_analysis.tag.tsv"],
    #     ['东莞市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     dongguan_20250122/prime/priority_analysis.tag.tsv"],
    #     ['佛山市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     foshan_20250123/prime/priority_analysis.tag.tsv"],
    #     ['福州市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     fuzhou1_20250124/prime/priority_analysis.tag.tsv"],
    #     ['广州市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     guangzhou_20250110/priority_analysis.tag.csv"],
    #     ['杭州市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     hangzhou_20250113/prime/priority_analysis.tag.tsv"],
    #     ['合肥市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     hefei_20250123/prime/priority_analysis.tag.tsv"],
    #     ['济南市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     jinan_20250123/prime/priority_analysis.tag.tsv"],
    #     ['金华市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     jinhua_20250124/prime/priority_analysis.tag.tsv"],
    #     ['昆明市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     kunming_20250122/prime/priority_analysis.tag.tsv"],
    #     ['南京市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     nanjing_20250122/prime/priority_analysis.tag.tsv"],
    #     ['南宁市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     nanning_20250126/prime/priority_analysis.tag.tsv"],
    #     ['宁波市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     ningbo_20250123/prime/priority_analysis.tag.tsv"],
    #     ['青岛市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     qingdao_20250123/prime/priority_analysis.tag.tsv"],
    #     ['厦门市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     shamen_20250123/prime/priority_analysis.tag.tsv"],
    #     ['深圳市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     shenzhen_20250113/prime/priority_analysis.tag.tsv"],
    #     ['石家庄市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     shijiazhuang_20250124/prime/priority_analysis.tag.tsv"],
    #     ['苏州市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     suzhou1_20250123/prime/priority_analysis.tag.tsv"],
    #     ['天津市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     tianjin_20250123/prime/priority_analysis.tag.tsv"],
    #     ['温州市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     wenzhou_20250124/prime/priority_analysis.tag.tsv"],
    #     ['武汉市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     wuhan_20250116/priority_analysis.tag.tsv"],
    #     ['无锡市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     wuxi_20250124/prime/priority_analysis.tag.tsv"],
    #     ['西安市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     xian_20250117/prime/priority_analysis.tag.tsv"],
    #     ['郑州市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     zhengzhou_20250122/prime/priority_analysis.tag.tsv"],
    #     ['中山市', f"/home/<USER>/dingping/aoi-ml/src/parking/storefront/product/
    #     zhongshan_20250124/prime/priority_analysis.tag.tsv"],
    # ]
    #
    # # position_tags = {"bua_area"}
    # # position_tags = {""}
    # total_count = 0
    # for batch_dir_t in batch_dir_list:
    #     city = batch_dir_t[0]
    #     batch_dir = batch_dir_t[1]
    #     # print(batch_dir)
    #     # position_tags = {""} # count: 26848
    #     position_tags = {"", "bua_area"} # count: 36235
    #     pv_tags = {"TOP60", "TOP60-TOP80"}
    #     for prime_id in get_prime_id_from_file(
    #         batch_dir, position_tags=position_tags, pv_tags=pv_tags
    #     ):
    #         print(prime_id)
    #         _top30_face_data(prime_id, city)
