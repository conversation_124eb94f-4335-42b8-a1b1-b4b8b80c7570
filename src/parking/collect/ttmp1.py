"""
临时文件
"""
def get_pre_shutoff_timestamps(end_car_timestamp, parking_time):
    """
    计算熄火前 2 分钟的时间戳列表，并去除停车时间。
    - 起始时间去掉第一秒
    - 最后时间戳保留熄火时间
    :param end_car_timestamp: int, 熄火时间戳（毫秒）
    :param parking_time: int, 停车时间（分钟）
    :return: list, 时间戳列表（毫秒）
    """
    # 计算停车时间和熄火前 2 分钟的时间（单位：毫秒）
    parking_time_ms = int(parking_time * 60 * 1000)
    two_minutes_ms = 2 * 60 * 1000  # 2 分钟（毫秒）
    # 计算起始时间（去除停车时间 + 2 分钟）
    start_timestamp = end_car_timestamp - parking_time_ms - two_minutes_ms
    # 计算结束时间（熄火时间 - 停车时间）
    end_timestamp = end_car_timestamp - parking_time_ms
    # 确保时间范围合理
    if start_timestamp >= end_timestamp:
        return [end_car_timestamp]  # 如果时间无效，则仅返回熄火时间
    # 去掉第一秒（start_timestamp + 1000）
    timestamps = list(range(start_timestamp + 1000, end_timestamp, 1000))
    # 添加最后的熄火时间戳
    timestamps.append(end_car_timestamp)
    return timestamps


def get_post_start_timestamps(start_car_timestamp, parking_time):
    """
    计算启动后 2 分钟的时间戳列表，并去除停车时间。
    - 第一个时间戳是启动时间。
    - 从第二个时间戳开始，跳过启动后的 parking_time 分钟，生成时间戳。
    - 总共生成 120 个时间戳（包括启动时间）。
    :param start_car_timestamp: int, 启动时间戳（毫秒）
    :param parking_time: int, 停车时间（分钟）
    :return: list, 时间戳列表（毫秒）
    """
    # 计算停车时间（单位：毫秒）
    parking_time_ms = int(parking_time * 60 * 1000)
    # 计算起始时间（启动时间 + 停车时间）
    start_timestamp = start_car_timestamp + parking_time_ms
    # 计算结束时间（起始时间 + 119 秒，因为第一个时间戳是启动时间）
    end_timestamp = start_timestamp + 119 * 1000  # 119 秒（毫秒）
    # 生成时间戳列表（每秒一个，共 119 个）
    timestamps = list(range(start_timestamp, end_timestamp + 1000, 1000))
    # 将启动时间插入到列表开头
    timestamps.insert(0, start_car_timestamp)
    return timestamps


def generate_timestamps(start_timestamp_ms):
    """
    生成从 start_timestamp_ms 开始，持续2分钟的毫秒时间戳列表，每秒一个。

    :param start_timestamp_ms: 开始时间的毫秒时间戳（整数）
    :return: 包含2分钟内每秒时间戳的列表
    """
    # 2分钟的毫秒数
    two_minutes_ms = 2 * 60 * 1000

    # 生成时间戳列表
    timestamps = [start_timestamp_ms + i * 1000 for i in range(two_minutes_ms // 1000)]

    return timestamps

# 示例调用
# end_car_timestamp = 1740130200000  # 假设熄火时间是 2025-02-21 15:30:00（毫秒）
# parking_time = 3  # 停车 3 分钟
result = get_pre_shutoff_timestamps(1740130200000, 1)
print(result)

# res = get_post_start_timestamps(1740130200000, 2)
# print(res)