#!/usr/bin/env python
# -*- coding: utf-8 -*-
########################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
########################################################################

"""
File: calc.py
Author: lifan(<EMAIL>)
Date: 2024/10/09 14:12:51
"""
import shapely.wkt
import time
import traceback
import itertools
import gzip
import sys
import json
import math
from datetime import datetime
from src.trajectory.utils.coord_trans import wgs84_to_bd09, wgs84_to_gcj02_wkt, ll_to_mc_wkt, \
    wgs84_to_bd09_wkt
from src.parking.collect.turnoff_point_calc.pg_tool import PgTool
from shapely.geometry import LineString

PARKING_SPEED_MAX_LIMIT = 10.0  # 认定停车最大车速：km / h
PARKING_SPEED_MIN_LIMIT = 3  # 认定停车置信车速：km / h
PARKING_DIST_LIMIT = 50.0  # 停车点之间最大距离：m
PARKING_TRAJ_LENGTH_LIMIT = 500.0  # 停车轨迹最大长度：m


def get_grouped_traj_points(file_path):
    """
    解析轨迹文件数据，按用户轨迹段分组
    """
    if not file_path.endswith(".json"):
        print("file_path is not a json file")
        return []

    traj_points = []
    # with gzip.open(file_path, "rt") as file:
    with open(file_path, "rt") as file:
        data = json.load(file)
        # print(data)
        # lines = file.read().split("\n")
        for info in data:
            timestamp_in_seconds = info['gpstime'] / 1000.0  # 转换为秒
            rounded_timestamp = math.ceil(timestamp_in_seconds)  # 向上取整
            (
                lon,
                lat,
                traj_src,
                uid,
                timestamp,
                line_speed,
                point_speed,
                traj_id,
                route_status,
                bit_map,
                traj_db_id,
                gps_r,
            ) = (
                info['x'],
                info['y'],
                "31",
                info['device_no'],
                rounded_timestamp,
                info['speed'],
                info['speed'],
                info['gpstime'],
                -1,
                info['gpstime'],
                info['gpstime'],
                -1,
            )
            if traj_src != "31":
                continue  # 只取众源轨迹

            time_stamp = datetime.fromtimestamp(int(timestamp))
            traj_point = {
                "lon": float(lon),
                "lat": float(lat),
                "traj_src": traj_src,
                "uid": uid,
                "timestamp": time_stamp,
                "int_time": int(timestamp),
                "line_speed": float(line_speed),
                "point_speed": float(point_speed),
                "traj_id": traj_id,
                "route_status": int(route_status),
                "bit_map": bit_map,
                "traj_db_id": traj_db_id,
                "gps_r": float(gps_r),
            }
            traj_points.append(traj_point)
    grouped_traj_points = [
        list(group)
        for key, group in itertools.groupby(traj_points, key=lambda x: x["uid"])
    ]
    return grouped_traj_points


def mining_parking_points(traj_points_group):
    """
    挖掘停车点
    """
    result = []
    for group in traj_points_group:
        parking_points = []
        final_points = filter_err_time_traj_points(group)
        if len(final_points) < 5:
            # 小于5个点，认为轨迹序列无效
            continue

        # 1、挖掘开始启动点，视为停车点
        start_p, start_p_time, start_speed = find_start_point(final_points)
        if start_p is not None:
            start_p["parking_type"] = "start_car"
            start_p["parking_speed"] = start_speed
            start_p["parking_time"] = start_p_time
            start_p["parking_traj_wkt"], start_p["parking_traj_time_str"] = get_start_car_parking_traj(start_p,
                                                                                                       final_points)
            parking_points.append(start_p)

        # 2、挖掘中间驻留点，视为停车点
        wait_p, wait_p_time, wait_p_speed = find_wait_point(final_points, start_p)
        if wait_p is not None and not is_same_point(start_p, wait_p):
            wait_p["parking_type"] = "mid_stop_car"
            wait_p["parking_speed"] = wait_p_speed
            wait_p["parking_time"] = wait_p_time
            wait_p["parking_traj_wkt"], wait_p["parking_traj_time_str"] = get_mid_stop_car_parking_traj(wait_p,
                                                                                                        final_points)
            parking_points.append(wait_p)

        # 3、挖掘结束熄火点，视为停车点
        end_p, end_p_time, end_speed = find_stop_point(final_points)
        if end_p is not None and not is_same_point(wait_p, end_p) and not is_same_point(start_p, end_p):
            end_p["parking_type"] = "end_car"
            end_p["parking_speed"] = end_speed
            end_p["parking_time"] = end_p_time
            end_p["parking_traj_wkt"], end_p["parking_traj_time_str"] = get_end_car_parking_traj(end_p, final_points)
            parking_points.append(end_p)

        # 4、挖掘结束熄火点，速度递减加阈值限制，视为停车点
        if end_p is None:
            (
                end_p_recall,
                end_p_time_recall,
                end_speed_recall,
            ) = find_end_car_recall_by_speed_desc(final_points)
            if end_p_recall is not None and not is_same_point(wait_p, end_p_recall) and not is_same_point(start_p,
                                                                                                          end_p):
                end_p_recall["parking_type"] = "end_car"
                end_p_recall["parking_speed"] = end_speed_recall
                end_p_recall["parking_time"] = end_p_time_recall
                end_p_recall["parking_traj_wkt"], end_p_recall["parking_traj_time_str"] = get_end_car_parking_traj(
                    end_p_recall, final_points)
                parking_points.append(end_p_recall)
        result.append(parking_points)
    return result


def get_start_car_parking_traj(p, points):
    """
    获取行前打火点停车轨迹(驶出)
    """
    final_points = []
    total_dist = 0
    flag = False
    for point in points:
        if is_solid_same_point(p, point):
            flag = True

        if not flag:
            continue

        if len(final_points) > 0:
            last_p = final_points[-1]
            if is_same_point(last_p, point):
                continue

            total_dist += get_dist(last_p, point)
            if total_dist > PARKING_TRAJ_LENGTH_LIMIT:
                break
        final_points.append(point)

    if len(final_points) < 2:
        return None, None

    new_traj_wkt = points_to_linestring_wkt(final_points)
    if new_traj_wkt is None:
        return None, None

    traj_time_str = points_to_traj_time_str(final_points)

    return new_traj_wkt, traj_time_str


def get_mid_stop_car_parking_traj(p, points):
    """
    获取行中驻留停车轨迹
    """
    final_points = []
    total_dist = 0

    # 后250m
    flag_prev = False
    for point in points:
        if is_solid_same_point(p, point):
            flag_prev = True

        if not flag_prev:
            continue

        if len(final_points) > 0:
            last_p = final_points[-1]
            if is_same_point(last_p, point):
                continue

            total_dist += get_dist(last_p, point)
            if total_dist > PARKING_TRAJ_LENGTH_LIMIT / 2:
                break
        final_points.append(point)

    # 前250m
    flag_next = False
    for point in reversed(points):
        if is_solid_same_point(p, point):
            flag_next = True

        if not flag_next:
            continue

        if len(final_points) > 0:
            first_p = final_points[0]
            if is_same_point(first_p, point):
                continue

            total_dist += get_dist(first_p, point)
            if total_dist > PARKING_TRAJ_LENGTH_LIMIT:
                break
        final_points.insert(0, point)

    if len(final_points) < 2:
        return None

    new_traj_wkt = points_to_linestring_wkt(final_points)
    if new_traj_wkt is None:
        return None

    traj_time_str = points_to_traj_time_str(final_points)

    return new_traj_wkt, traj_time_str


def get_end_car_parking_traj(p, points):
    """
    获取行末熄火点停车轨迹(驶入)
    """
    final_points = []
    total_dist = 0
    flag = False
    for point in reversed(points):
        if is_solid_same_point(p, point):
            flag = True

        if not flag:
            continue

        if len(final_points) > 0:
            first_p = final_points[0]
            if is_same_point(first_p, point):
                continue

            total_dist += get_dist(first_p, point)
            if total_dist > PARKING_TRAJ_LENGTH_LIMIT:
                break
        final_points.insert(0, point)

    if len(final_points) < 2:
        return None

    new_traj_wkt = points_to_linestring_wkt(final_points)
    if new_traj_wkt is None:
        return None

    traj_time_str = points_to_traj_time_str(final_points)

    return new_traj_wkt, traj_time_str


def points_to_linestring_wkt(points):
    """
    轨迹点序列转线WKT
    """
    arr = []
    for point in points:
        wgs_point_wkt = format_point_to_wkt(point)
        gcj02_point_wkt = wgs84_to_gcj02_wkt(wgs_point_wkt)
        arr.append(shapely.wkt.loads(gcj02_point_wkt))
    lineString = LineString(arr)
    if lineString is not None:
        return lineString.wkt
    return None


def points_to_traj_time_str(points):
    """
    轨迹点序列转时间戳字符串
    """
    arr = []
    for point in points:
        timestamp_str = point["timestamp"].strftime("%Y-%m-%d %H:%M:%S")
        arr.append(timestamp_str)
    return '&'.join(arr)


def find_start_point(points):
    """
    找启动点
    """
    speed_start = calc_speed(points[0], points[1])
    if speed_start < PARKING_SPEED_MIN_LIMIT:
        # 初始速度很低，认为刚启动车
        return points[0], 0, -1

    if speed_start > PARKING_SPEED_MAX_LIMIT * 1.5:
        # 超高速，不置信
        return None, 0, -1

    group = [points[0]]
    for i in range(1, len(points)):
        prev_p, curr_p = points[i - 1], points[i]
        speed = calc_speed(prev_p, curr_p)
        if speed <= PARKING_SPEED_MAX_LIMIT:
            group.append(curr_p)
        else:
            break

    if len(group) < 2:
        return None, 0, -1

    group_dist = get_group_dist(group)
    if group_dist >= PARKING_DIST_LIMIT * 1.5:
        return None, 0, -1

    parking_time = calc_parking_time(group)
    return group[0], parking_time, speed_start


def find_wait_point(points, start_p):
    """
    找驻留点
    低速放入组内，组内第一个点和最后一个点时间大于阈值，距离小于阈值
    """
    group = []
    max_speed = -1
    for i in range(1, len(points)):
        prev_p, curr_p = points[i - 1], points[i]
        speed = calc_speed(prev_p, curr_p)
        if speed <= PARKING_SPEED_MAX_LIMIT:
            if len([g for g in group if g["timestamp"] == prev_p["timestamp"]]) == 0:
                group.append(prev_p)
            if len([g for g in group if g["timestamp"] == curr_p["timestamp"]]) == 0:
                group.append(curr_p)
            max_speed = max(max_speed, speed)

    if len(group) < 2:
        return None, 0, -1

    first_p, last_p = group[0], group[-1]
    dist_diff = get_dist(first_p, last_p)
    if dist_diff < PARKING_DIST_LIMIT:
        if start_p is not None and is_same_point(first_p, start_p):
            return None, 0, -1

        parking_time = calc_parking_time(group)
        return last_p, parking_time, max_speed
    return None, 0, -1


def find_stop_point(points):
    """
    找熄火点
    """
    speed_end = calc_speed(points[-2], points[-1])
    if speed_end > PARKING_SPEED_MAX_LIMIT:
        return None, 0, -1

    group = [points[-1]]
    max_speed = -1
    for i in range(len(points) - 2, 0, -1):
        curr_p = points[i]
        next_p = points[i + 1]
        speed = calc_speed(curr_p, next_p)
        if speed <= PARKING_SPEED_MAX_LIMIT:
            group.append(curr_p)
            max_speed = max(max_speed, speed)
        else:
            break

    if len(group) < 2:
        return None, 0, -1

    group_dist = get_group_dist(group)
    if group_dist >= PARKING_DIST_LIMIT:
        return None, 0, -1

    parking_time = calc_parking_time(group)
    return group[0], parking_time, max_speed


def find_end_car_recall_by_speed_desc(points):
    """
    终点三个点速度递减，认为终点为停车点
    """
    group = []
    prev_speed = calc_speed(points[-2], points[-1])
    max_speed = -1
    for i in range(len(points) - 2, 0, -1):
        curr_p, next_p = points[i], points[i + 1]
        speed = calc_speed(curr_p, next_p)
        if speed <= prev_speed and speed < PARKING_SPEED_MAX_LIMIT * 3:
            group.append(curr_p)
            max_speed = max(max_speed, speed)
            prev_speed = speed
        else:
            break

    if len(group) < 3:
        return None, 0, -1

    group_dist = get_group_dist(group)
    if group_dist >= PARKING_DIST_LIMIT * 2:
        return None, 0, -1

    if prev_speed > PARKING_SPEED_MAX_LIMIT * 3:
        return None, 0, -1

    parking_time = calc_parking_time(group)
    return group[-1], parking_time, max_speed


def get_group_dist(group):
    """
    计算组内距离
    todo：后续改成活动范围计算方法
    """
    first_p, last_p = group[0], group[-1]
    dist = get_dist(first_p, last_p)
    return dist


def calc_parking_time(group):
    """
    计算停车时间
    """
    first_p, last_p = group[0], group[-1]
    time_diff = (last_p["timestamp"] - first_p["timestamp"]).total_seconds() / 60
    return abs(time_diff)


def is_same_point(p1, p2):
    """
    判断是否为相同点
    """
    if p1 is None or p2 is None:
        return False
    if p1["uid"] == p2["uid"] and p1["timestamp"] == p2["timestamp"]:
        # 同设备时间相同
        return True
    if p1["uid"] == p2["uid"] and p1["lon"] == p2["lon"] and p1["lat"] == p2["lat"]:
        # 同设备位置相同
        return True
    return False


def is_solid_same_point(p1, p2):
    """
    判断是否为相同点(位置 + 时间)
    """
    if p1 is None or p2 is None:
        return False
    if p1["uid"] == p2["uid"] and p1["lon"] == p2["lon"] and p1["lat"] == p2["lat"] and p1["timestamp"] == p2[
        "timestamp"]:
        return True
    return False


def filter_err_time_traj_points(points):
    """
    过滤掉时间乱序的GPS点
    """
    if not points:
        return []

    new_points = [points[0]]
    for i in range(1, len(points)):
        prev_p, curr_p = points[i - 1], points[i]
        prev_time, curr_time = prev_p["int_time"], curr_p["int_time"]
        if curr_time <= prev_time:
            continue
        new_points.append(curr_p)
    return new_points


def calc_speed(prev_point, curr_point):
    """
    计算两点平均速度，单位km / h
    """
    prev_timestamp, curr_timestamp = prev_point["timestamp"], curr_point["timestamp"]
    time_hour = abs(curr_timestamp - prev_timestamp).total_seconds() / 3600
    dist_km = get_dist(prev_point, curr_point) / 1000
    return dist_km / time_hour


def get_dist(p1, p2):
    """
    计算两点距离，单位m
    """
    p1_wkt = format_point_to_wkt(p1)
    p2_wkt = format_point_to_wkt(p2)
    p1_sp = shapely.wkt.loads(p1_wkt)
    p2_sp = shapely.wkt.loads(p2_wkt)
    distance = p1_sp.distance(p2_sp) * 110000
    return distance


def format_point_to_wkt(p):
    """
    格式化GPS点信息为WKT点
    """
    return "POINT({} {})".format(p["lon"], p["lat"])


def create_parking_points_table():
    """
    创建停车点表
    """
    with PgTool() as pg:
        sql = """
            CREATE TABLE IF NOT EXISTS parking_points (
                id serial primary key,
                user_id character varying(255) DEFAULT ''::character varying NOT NULL,
                traj_id character varying(255) DEFAULT ''::character varying NOT NULL,
                traj_db_id character varying(255) DEFAULT ''::character varying NOT NULL,
                traj_src character varying(50) DEFAULT ''::character varying NOT NULL,
                mesh_id character varying(100),
                geom geometry(Point, 4326),
                parking_traj_geom geometry(LineString, 4326),
                parking_traj_time_str text,
                line_speed double precision DEFAULT -1.0 NOT NULL,
                point_speed double precision DEFAULT -1.0 NOT NULL,
                route_status int DEFAULT -1 NOT NULL,
                bit_map character varying(200) DEFAULT ''::character varying NOT NULL,
                gps_r double precision DEFAULT -1.0 NOT NULL,
                parking_type character varying(100) DEFAULT ''::character varying NOT NULL,
                parking_time double precision DEFAULT -1.0 NOT NULL,
                parking_speed double precision DEFAULT -1.0 NOT NULL,
                batch_src character varying(100) DEFAULT ''::character varying NOT NULL,
                traj_time timestamp without time zone DEFAULT '1970-01-01 00:00:00'::timestamp without time zone,
                create_time timestamp without time zone DEFAULT '1970-01-01 00:00:00'::timestamp without time zone
            );
            CREATE INDEX parking_points_geom_gist ON parking_points USING gist(geom);
            create index parking_points_mesh_id_index on parking_points(mesh_id);
        """
        pg.cursor_traj_feature.execute(sql)
        pg.conn_traj_feature.commit()


def save_parking_points_to_pg(parking_points_result, file_path, batch_src):
    """
    停车点成果存到pg库
    """
    # mesh_id = file_path.split('/')[-2].split("_")[-1]
    mesh_id = '-1'
    create_time = datetime.today().strftime("%Y-%m-%d %H:%M:%S")
    batch_size = 500

    sql_template = """
        INSERT INTO parking_points (
            user_id, traj_id, traj_db_id, traj_src, geom,
            line_speed, point_speed, route_status, bit_map, gps_r,
            parking_type, parking_time, batch_src, traj_time, create_time,
            parking_speed, parking_traj_geom, mesh_id
        ) VALUES (
            %s, %s, %s, %s, ST_GeomFromText(%s, 4326),
            %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, ST_GeomFromText(%s, 4326), %s
        )
    """

    sql_template2 = """
        INSERT INTO parking_traj_time_str (
            uid, time_str
        ) VALUES (
            %s, %s
        )
    """
    data_to_insert = []
    data_time_str_to_insert = []
    with PgTool() as pg:
        for points in parking_points_result:
            for point in points:
                wgs_point_wkt = format_point_to_wkt(point)
                gcj02_point_wkt = wgs84_to_gcj02_wkt(wgs_point_wkt)
                data = (
                    point["uid"],
                    point["traj_id"],
                    point["traj_db_id"],
                    point["traj_src"],
                    gcj02_point_wkt,
                    point["line_speed"],
                    point["point_speed"],
                    point["route_status"],
                    point["bit_map"],
                    point["gps_r"],
                    point["parking_type"],
                    point["parking_time"],
                    batch_src,
                    point["timestamp"],
                    create_time,
                    point["parking_speed"],
                    point["parking_traj_wkt"],
                    mesh_id,
                )
                data_to_insert.append(data)
                timestamp_str = point["timestamp"].strftime("%Y-%m-%d %H:%M:%S")
                data_time_str_to_insert.append((point["uid"] + timestamp_str, point["parking_traj_time_str"]))

                # 每500条记录执行一次插入和提交
                if len(data_to_insert) >= batch_size:
                    pg.cursor_traj_feature.executemany(sql_template, data_to_insert)
                    pg.conn_traj_feature.commit()

                    pg.cursor_dest_traj_new.executemany(sql_template2, data_time_str_to_insert)
                    pg.conn_dest_traj_new.commit()

                    # 清空列表以便下一批数据的插入
                    data_to_insert = []
                    data_time_str_to_insert = []
        # 处理剩余的数据
        if len(data_to_insert) > 0:
            pg.cursor_traj_feature.executemany(sql_template, data_to_insert)
            pg.conn_traj_feature.commit()

            pg.cursor_dest_traj_new.executemany(sql_template2, data_time_str_to_insert)
            pg.conn_dest_traj_new.commit()


def get_gcj02_traj_wkt(points):
    """
    根据轨迹点获取轨迹线
    """
    arr = []
    for point in points:
        wgs_point_wkt = format_point_to_wkt(point)
        gcj02_point_wkt = wgs84_to_gcj02_wkt(wgs_point_wkt)
        arr.append(shapely.wkt.loads(gcj02_point_wkt))
    lineString = LineString(arr)
    if lineString is not None:
        return lineString.wkt
    return None


def run(file_path, batch_src):
    """
    run
    """
    start_time = time.time()
    formatted_time = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')
    print("start_time:", formatted_time)

    print("step_1: get_grouped_traj_points")
    traj_points_group = get_grouped_traj_points(file_path)
    # print(traj_points_group)

    print("step_2: mining_parking_points")
    parking_points_result = mining_parking_points(traj_points_group)
    # print(parking_points_result)

    print("step_3: save_parking_points_to_pg")
    save_parking_points_to_pg(parking_points_result, file_path, batch_src)

    end_time = time.time()
    elapsed_time_seconds = end_time - start_time
    elapsed_time_minutes = elapsed_time_seconds / 60
    print(
        "process_time:",
        "|",
        elapsed_time_seconds,
        "s",
        "|",
        elapsed_time_minutes,
        "min",
    )


def main():
    """
    main
    """
    if len(sys.argv) < 2:
        print("文件路径必传")
        return

    # 文件路径
    file_path = sys.argv[1]
    batch_src = sys.argv[2]
    try:
        run(file_path, batch_src)
        sys.exit(1)  # 这里返回退出码 1 表示成功
    except Exception as e:
        print("程序运行出错，错误详情如下:")
        print(e)
        traceback.print_exc()
        sys.exit(-1)  # 这里返回退出码 -1 表示失败


if __name__ == "__main__":
    """
    main
    """
    main()
