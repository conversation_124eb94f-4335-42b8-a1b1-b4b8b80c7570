# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pg库链接接口
"""
import psycopg2
import os

# 项目根目录
root_path = os.path.dirname(os.path.dirname(__file__) + "/../../")


class PgTool:
    """
    pg库操作工具
    """

    def __init__(self):
        """
        初始化连接
        """
        self.conn_aoi_dest_traj = psycopg2.connect(
            database="aoi_dest_traj_db",
            user="aoi_dest_traj_rw",
            password="aoi_dest_traj_rw",
            host="*************",
            port="5432",
        )
        self.cursor_aoi_dest_traj = self.conn_aoi_dest_traj.cursor()

        self.conn_aoi_dest_traj2 = psycopg2.connect(
            database="aoi_dest_traj_db2",
            user="aoi_dest_traj2_rw",
            password="aoi_dest_traj2_rw",
            host="*************",
            port="6432",
        )
        self.cursor_aoi_dest_traj2 = self.conn_aoi_dest_traj2.cursor()

        # self.conn_traj_feature = psycopg2.connect(
        #     database="traj_feature_db",
        #     user="traj_feature_rw",
        #     password="traj_feature_rw",
        #     host="*************",
        #     port="9432",
        # )
        # 空间不够，迁移
        self.conn_traj_feature = psycopg2.connect(
            database="aoi_dw",
            user="aoi_dw_r",
            password="aoi_dw_pw_2025",
            host="*************",
            port="8034",
        )
        self.cursor_traj_feature = self.conn_traj_feature.cursor()

        #self.conn_dest_traj_to_aoi = psycopg2.connect(
        #    database="dest_traj_to_aoi",
        #    user="dest_traj_to_aoi_se_rw",
        #    password="uwymqbnx",
        #    host="*************",
        #    port="8432",
        #)
        #self.cursor_dest_traj_to_aoi = self.conn_dest_traj_to_aoi.cursor()

        #self.conn_aoi = psycopg2.connect(
        #    database="master_back",
        #    user="master_back_se_ro",
        #    password="mapread",
        #    host="**************",
        #    port="5432",
        #)
        #self.cursor_aoi = self.conn_aoi.cursor()

        self.conn_dest_traj_new = psycopg2.connect(
            database="dest_traj",
            user="dest_traj_se_rw",
            password="erwmbdpm",
            host="**************",
            port="7432",
        )
        self.cursor_dest_traj_new = self.conn_dest_traj_new.cursor()

    def __enter__(self):
        """
        支持with操作
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        支持with操作，自动关闭连接
        """
        if not self.conn_aoi_dest_traj.closed:
            self.conn_aoi_dest_traj.commit()
            self.conn_aoi_dest_traj.close()

        if not self.conn_aoi_dest_traj2.closed:
            self.conn_aoi_dest_traj2.commit()
            self.conn_aoi_dest_traj2.close()

        #if not self.conn_dest_traj_to_aoi.closed:
        #    self.conn_dest_traj_to_aoi.commit()
        #    self.conn_dest_traj_to_aoi.close()

        #if not self.conn_aoi.closed:
        #    self.conn_aoi.commit()
        #    self.conn_aoi.close()

        if not self.conn_dest_traj_new.closed:
            self.conn_dest_traj_new.commit()
            self.conn_dest_traj_new.close()

        if not self.conn_traj_feature.closed:
            self.conn_traj_feature.commit()
            self.conn_traj_feature.close()


    def commit(self):
        """
        提交
        """
        if not self.conn_aoi_dest_traj.closed:
            self.conn_aoi_dest_traj.commit()

        if not self.conn_aoi_dest_traj2.closed:
            self.conn_aoi_dest_traj2.commit()

        #if not self.conn_dest_traj_to_aoi.closed:
        #    self.conn_dest_traj_to_aoi.commit()

        #if not self.conn_aoi.closed:
        #    self.conn_aoi.commit()

        if not self.conn_dest_traj_new.closed:
            self.conn_dest_traj_new.commit()

        if not self.conn_traj_feature.closed:
            self.conn_traj_feature.commit()

    def rollback(self):
        """
        回滚
        """
        if not self.conn_aoi_dest_traj.closed:
            self.conn_aoi_dest_traj.rollback()

        if not self.conn_aoi_dest_traj2.closed:
            self.conn_aoi_dest_traj2.rollback()

        #if not self.conn_dest_traj_to_aoi.closed:
        #    self.conn_dest_traj_to_aoi.rollback()

        #if not self.conn_aoi.closed:
        #    self.conn_aoi.rollback()

        if not self.conn_dest_traj_new.closed:
            self.conn_dest_traj_new.rollback()

        if not self.conn_traj_feature.closed:
            self.conn_traj_feature.rollback()
