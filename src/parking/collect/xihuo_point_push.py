#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
Description
安轨迹下发众源任务
"""
import sys
import time
import requests

import json
import jwt
import logging
import requests
import time
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests
import csv
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
from shapely.ops import transform
from shapely.ops import unary_union
import pyproj

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from shapely import Polygon, LineString
import subprocess
from multiprocessing import Pool
import multiprocessing


url = "http://dedc.baidu.com/vdust/main/createnewtask"


def get_jwt_token(account, token, service_list):
    """获取加密字符串

    Args:
        account (str): 授权账号
        token (str): 账号对应token
        service_list (list): 要访问的接口，对于路网服务，列表长度只能为1，第0个元素为要访问的接口名

    Returns:
        _type_: _description_
    """
    msg = {
        'exp': int(time.time()) + 260,  # 过期时间
        'timeout': int(time.time()) + 6 * 60,  # 有效期5min,
        'name': account,  # 账号名称
        'service': service_list # 服务名称
    }
    # 获取token
    ret = jwt.encode(msg, token, algorithm='HS256')
    return ret


def _get_parking_points(batch_src, geom):
    qry = (f"select id,user_id,traj_time,st_astext(geom),st_astext(parking_traj_geom) from parking_points"
           f" where batch_src ='{batch_src}'"
           f" and ST_Contains(ST_GeomFromText('{geom}', 4326), geom)")
    # print(qry)
    res = dbutils.fetch_one(pgsql.TRAJ_FEATURE, qry)
    return res


def run(device_no):
    """主流程
    """
    # url = "http://*************:8891/de/servicehub/create_task"
    url = "http://service.mapde.baidu-int.com/de/servicehub/create_task"

    account = 'parking'
    token = 'c262159e031c40889226dd0d2aa04707'

    service_list = ['vdust']
    data = {
        "task_info": [{
            "device_no": device_no,
            # "geo_timestamp": *************,
            "geo_timestamp": *************,
            # 当前 前后100米
            "geo_range": "100,100",
            # "time_trace": geom_area,
            # 传具体时间戳list 时 不用传 geo_timestamp  geo_range
            # "timestamp_list": [*************, *************, *************, *************,
            # *************, *************, *************, *************, *************, *************],
            }],
        "deliver_source": "parking"
    }
    #data = {'test':'a'}
    key = get_jwt_token(account, token, service_list)
    headers = {'content-type': "application/json", 'Authorization': 'Bearer ' + key}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(response)
    logging.info(response.text)


def _decrypt_user_id(encrypted_user_id):
    device_code_length = 6
    encrypted_prefix = 'trajplat_'

    encrypted_user_id_without_prefix = encrypted_user_id.replace(encrypted_prefix, '')
    index_of_first_underscore = encrypted_user_id_without_prefix.find('_')
    index_of_first_device_code_number = index_of_first_underscore - device_code_length
    decrypted_user_id_start = encrypted_user_id_without_prefix[index_of_first_device_code_number::]
    decrypted_user_id_end = encrypted_user_id_without_prefix[:index_of_first_device_code_number]
    return f"{decrypted_user_id_start}{decrypted_user_id_end}"


def _get_beijing_parking(batch_src):
    """
    获取待推送的数据
    """
    sql = f"select strategy_id, ST_AsText(geom), '{batch_src}' from park_storefront_post_zhongyuan"
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_beijing_yessi(batch_src):
    sql = (f"select bid, st_astext(geom), '{batch_src}' from"
           f" aoi_post_zhongyuan where issi = 'yes' order by random() limit 10000")
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_beijing_nosi(batch_src):
    sql = (f"select bid, st_astext(geom), '{batch_src}' from"
           f" aoi_post_zhongyuan where issi = 'no'  order by random() limit 10000")
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def process_file(args):
    """
    处理单个文件的函数
    """
    target_dir, file = args
    file_path = os.path.join(target_dir, file)
    print(file_path)
    script_path = ("cd /home/<USER>/lifan14/parking_point_test/calc_parking_points_distributed_v5_laofan "
                   "&& py39 calc_test.py")
    command = f"source ~/fanjiabin/bashrc && {script_path} {file_path}"
    print(f"Processing {command}")
    try:
        # 调用子进程运行脚本
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        print(f"Processed {file}")
        print(result.stdout)
    except Exception as e:
        print(f"Error processing {file}: {e}")


def _push_data(device_no, timestamp_list, extra_info):
    """主流程
    """
    # url = "http://*************:8891/de/servicehub/create_task"
    url = "http://service.mapde.baidu-int.com/de/servicehub/create_task"

    account = 'parking'
    token = 'c262159e031c40889226dd0d2aa04707'

    service_list = ['vdust']
    data = {
        "task_info": [{
            "device_no": device_no,
            "timestamp_list": timestamp_list,
            }],
        "deliver_source": "parking"
    }
    logging.info(data)
    key = get_jwt_token(account, token, service_list)
    headers = {'content-type': "application/json", 'Authorization': 'Bearer ' + key}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    logging.info(response.text)
    _insert_push_data(extra_info, json.dumps(data), response.text)
    # return json.loads(response.text)


def _process_aoi_geom(park_geom):
    try:
        bid = park_geom[0]
        park_geomm = park_geom[1]
        batch_src = park_geom[2]
        park_points = _get_parking_points(batch_src, park_geomm)
        if park_points:
            # id,user_id,traj_time
            park_points_id = park_points[0]
            user_id = park_points[1]
            traj_time = park_points[2]
            point_geom = park_points[3]
            parking_traj_geom = park_points[4]
            user_id_res = _decrypt_user_id(user_id)
            uuid = user_id_res.split('_')[1]

            timestamp = int(traj_time.timestamp())
            logger.info(f"park_points_id: {park_points_id}, uuid:{uuid}, timestamp is {timestamp}, "
                        f"bid is {bid}")
            timestamp_ms = timestamp * 1000
            # 获取前 2 分钟的起始时间戳
            start_timestamp_ms = timestamp_ms - (2 * 60 * 1000)
            # 生成毫秒级时间戳列表
            time_stamps_list = list(range(start_timestamp_ms, timestamp_ms, 1000))  # 每毫秒递增

            extra_info = {
                "strategy_id": 0,
                "bid": bid,
                "park_points_id": park_points_id,
                "point_time": timestamp,
                "device_no": uuid,
                "point_geom": point_geom,
                "parking_traj_geom": parking_traj_geom,
                "request_info": "",
                "batch": "20250110_bj_aoi"
            }
            _push_data(uuid, time_stamps_list, extra_info)
    except Exception as e:
        logger.error(f"error in _process_aoi_geom, error is {e}")


def _process_park_geom(park_geom):
    try:
        strategy_id = park_geom[0]
        park_geomm = park_geom[1]
        batch_src = park_geom[2]
        park_points = _get_parking_points(batch_src, park_geomm)
        if park_points:
            # id,user_id,traj_time
            park_points_id = park_points[0]
            user_id = park_points[1]
            traj_time = park_points[2]
            point_geom = park_points[3]
            parking_traj_geom = park_points[4]
            user_id_res = _decrypt_user_id(user_id)
            uuid = user_id_res.split('_')[1]

            timestamp = int(traj_time.timestamp())
            logger.info(f"park_points_id: {park_points_id}, uuid:{uuid}, timestamp is {timestamp}, "
                        f"strategy_id is {strategy_id}")
            timestamp_ms = timestamp * 1000
            # 获取前 2 分钟的起始时间戳
            start_timestamp_ms = timestamp_ms - (2 * 60 * 1000)
            # 生成毫秒级时间戳列表
            time_stamps_list = list(range(start_timestamp_ms, timestamp_ms, 1000))  # 每毫秒递增
            extra_info = {
                "strategy_id": strategy_id,
                "bid": "",
                "park_points_id": park_points_id,
                "point_time": timestamp,
                "device_no": uuid,
                "point_geom": point_geom,
                "parking_traj_geom": parking_traj_geom,
                "request_info": "",
                "batch": "20250110_bj_parkstore"
            }
            _push_data(uuid, time_stamps_list, extra_info)
    except Exception as e:
        logger.exception(e)


def _insert_push_data(data, request_info, response_info):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    strategy_id = data['strategy_id']
    bid = data['bid']
    park_points_id = data['park_points_id']
    point_time = data['point_time']
    device_no = data['device_no']
    point_geom = data['point_geom']
    parking_traj_geom = data['parking_traj_geom']
    batch = data['batch']

    # park_points_push
    sql = f"""insert into park_points_push (strategy_id,bid,park_points_id,point_time,
    device_no,point_geom,parking_traj_geom,request_info,response_info,
    batch) values({strategy_id},'{bid}',{park_points_id},
           {point_time},'{device_no}',st_geomfromtext('{point_geom}', 4326),
           st_geomfromtext('{parking_traj_geom}', 4326),
            '{request_info}','{response_info}','{batch}')
        """
    # logger.info(f"_insert_push_data is {sql}")
    cursor.execute(sql)
    logger.info(f"insert park_points_push success")


def _isnot_pushed(strategy_id):
    sql = f"select count(*) from park_points_push where strategy_id={strategy_id}"
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    cursor.execute(sql)
    res = cursor.fetchone()
    if res[0]:
        return False
    else:
        return True


def _get_wait_cal_data_by_status(status):
    sql = f"""select flow_name,flow_data from park_collect_flow where status = '{status}'"""
    return dbutils.fetch_one(pgsql.POI_CONFIG, sql)


def _update_flow_status(flow_name, status):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    sql = f"update park_collect_flow set status = '{status}' where flow_name = '{flow_name}'"
    cursor.execute(sql)
    rowcount = cursor.rowcount
    return rowcount


if __name__ == '__main__':
    # ps aux | grep "xihuo_point_push.py push_poi" | grep -v grep | awk '{print $2}' | xargs kill -9
    arguments = sys.argv[1:]
    print(arguments)
    if len(arguments) < 1:
        print("请输入参数: nohup py39 xihuo_point_push.py push_storefront > push_storefront_0111_1100_log 2>&1 & ")
        print("请输入参数: nohup py39 xihuo_point_push.py push_aoi_yessi > push_aoi_yessi_0111_1100_log 2>&1 & ")
        print("请输入参数: nohup py39 xihuo_point_push.py push_aoi_nosi > push_aoi_yessi_0111_1100_log 2>&1 & ")
        exit(0)
    action = arguments[0]

    if action == 'push_storefront':
        # nohup py39 xihuo_point_push.py push_storefront > push_storefront_0111_1200_log 2>&1 &

        # select st_astext(a.point_geom),st_astext(a.parking_traj_geom),st_astext(b.geom) from park_points_push a
        # left join park_storefront_post_zhongyuan b on a.strategy_id=b.strategy_id order by a.id desc limit 1;
        data = _get_wait_cal_data_by_status('CALLED')
        if not data:
            logger.info(f"没有数据")
            exit(0)
        # logger.info(f"data: {data}")
        flow_name = data[0]
        flow_data = data[1]
        city = flow_data['city']
        date_arg = flow_data['date']
        time_arg = flow_data['hour']
        batch_src = f"{city}_{date_arg}_{time_arg}"
        print(f"batch_src: {batch_src}")

        park_geom_list = _get_beijing_parking(batch_src)
        # print(f"park_geom_list is {len(park_geom_list)}")
        # park_geom_list_not_pushed = []
        # for park_geom in park_geom_list:
        #     strategy_id = park_geom[0]
        #     res = _isnot_pushed(strategy_id)
        #     if not res:
        #         continue
        #     park_geom_list_not_pushed.append(park_geom)
        # print(f"park_geom_list_not_pushed is {len(park_geom_list_not_pushed)}")

        # 使用多进程处理
        with multiprocessing.Pool(processes=10) as pool:
            pool.map(_process_park_geom, park_geom_list)

        if not _update_flow_status(flow_name, 'CALLED_PARK'):
            logger.error("Failed to update flow status to CALLED_PARK")
        else:
            logger.info("Flow status updated to CALLED_PARK")

    if action == 'push_aoi_yessi':
        # nohup py39 xihuo_point_push.py push_aoi_yessi > push_aoi_yessi_0111_1200_log 2>&1 &

        # select b.issi,count(*) from park_points_push a left join aoi_post_zhongyuan b on a.bid=b.bid
        # where a.batch='20250110_bj_aoi' group by b.issi;
        data = _get_wait_cal_data_by_status('CALLED_PARK')
        if not data:
            logger.info(f"没有数据")
            exit(0)
        # logger.info(f"data: {data}")
        flow_name = data[0]
        flow_data = data[1]
        city = flow_data['city']
        date_arg = flow_data['date']
        time_arg = flow_data['hour']
        batch_src = f"{city}_{date_arg}_{time_arg}"
        print(f"batch_src: {batch_src}")

        park_geom_list = _get_beijing_yessi(batch_src)
        with multiprocessing.Pool(processes=10) as pool:
            pool.map(_process_aoi_geom, park_geom_list)

        if not _update_flow_status(flow_name, 'CALLED_PARK_SI'):
            logger.error("Failed to update flow status to CALLED_PARK_SI")
        else:
            logger.info("Flow status updated to CALLED_PARK_SI")

    if action == 'push_aoi_nosi':
        # nohup py39 xihuo_point_push.py push_aoi_nosi > push_poi_0111_1200_log 2>&1 &

        # select b.issi,count(*) from park_points_push a left join aoi_post_zhongyuan b on a.bid=b.bid
        # where a.batch='20250110_bj_aoi' group by b.issi;
        data = _get_wait_cal_data_by_status('CALLED_PARK_SI')
        if not data:
            logger.info(f"没有数据")
            exit(0)
        # logger.info(f"data: {data}")
        flow_name = data[0]
        flow_data = data[1]
        city = flow_data['city']
        date_arg = flow_data['date']
        time_arg = flow_data['hour']
        batch_src = f"{city}_{date_arg}_{time_arg}"
        print(f"batch_src: {batch_src}")

        park_geom_list = _get_beijing_nosi(batch_src)
        with multiprocessing.Pool(processes=10) as pool:
            pool.map(_process_aoi_geom, park_geom_list)

        if not _update_flow_status(flow_name, 'CALLED_PARK_SI_NOSI'):
            logger.error("Failed to update flow status to CALLED_PARK_SI_NOSI")
        else:
            logger.info("Flow status updated to CALLED_PARK_SI_NOSI")