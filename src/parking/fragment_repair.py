"""
停车场面修形：仅针对“碎片化场景”。
"""

import math
from dataclasses import dataclass, field
from functools import reduce
from pathlib import Path

from shapely import wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.fix_geom_process import tools
from src.seg_post_process import smooth_point_string
from src.tools import pgsql, pipeline, tsv

METER = 1e-5

get_connection = pgsql.get_connection_ttl


@dataclass
class Context:
    """
    上下文
    """

    geom: BaseGeometry
    error: str = None
    result: any = None
    diff_iou: float = math.nan
    area_ratio: float = math.nan
    buildings: list[tuple[str, str]] = field(default_factory=list)
    inner_roads: list[tuple[str, str]] = field(default_factory=list)
    outer_roads: list[tuple] = field(default_factory=list)


def convert_bd09mc_to_gcj02(ctx: Context, proceed):
    """
    pipe: 转换 bd09mc 坐标到 gcj02
    """
    with get_connection(pgsql.BACK_CONFIG) as conn:
        sql = """
            select st_astext(mc2gcj(st_geomfromtext(%s, 3857)));
        """
        ret = pgsql.fetch_one(conn, sql, [str(ctx.geom)])
        geom_gcj = ret[0]
        ctx.geom = wkt.loads(geom_gcj)

    proceed()


def filter_by_area(max_area: float):
    """
    pipe: 面积大小筛选
    """

    def pipe(ctx: Context, proceed):
        if ctx.geom.area > max_area:
            ctx.error = "error: area is too large"
            return

        proceed()

    return pipe


def filter_by_iou(min_iou: float):
    """
    pipe: iou 筛选
    """

    def pipe(ctx: Context, proceed):
        if not ctx.result.startswith("POLYGON") or "EMPTY" in ctx.result:
            proceed()
            return

        iou = calc_iou(ctx.result, str(ctx.geom))
        ctx.diff_iou = iou
        if iou < min_iou:
            ctx.error = "error: iou too small"

    return pipe


def filter_by_area_ratio(min_area_ratio: float):
    """
    pipe: 压盖面积率筛选
    """

    def pipe(ctx: Context, proceed):
        if not ctx.result.startswith("POLYGON") or "EMPTY" in ctx.result:
            proceed()
            return

        area_ratio = calc_area_ratio(ctx.geom, ctx.result)
        ctx.area_ratio = area_ratio
        if area_ratio < min_area_ratio:
            ctx.error = "error: area-ratio too small"

    return pipe


def get_features(ctx: Context, proceed):
    """
    pipe: 查询周边要素
    """
    buffer_geom = str(ctx.geom.buffer(30 * METER))
    ctx.buildings = get_buildings(buffer_geom)
    ctx.inner_roads = get_inner_roads(buffer_geom)
    ctx.outer_roads = get_outer_roads(buffer_geom)
    proceed()


def calc_building_overlap(max_overlap_ratio: float):
    """
    pipe: 计算建筑物压盖，并筛掉压盖过多的 case
    """

    def pipe(ctx: Context, proceed):
        if not ctx.buildings:
            proceed()
            return

        union_buildings = reduce(
            lambda s, x: s.union(x), (geom for _, geom in ctx.buildings)
        )
        free_geom = ctx.geom.difference(union_buildings)
        overlap_ratio = 1 - free_geom.area / ctx.geom.area
        if overlap_ratio > max_overlap_ratio:
            ctx.error = "error: excessively overlapped by buildings"
            return

        proceed()

    return pipe


def calc_min_rect(ctx: Context, proceed):
    """
    pipe: 获取最小外接矩形
    """
    ctx.result = ctx.geom.minimum_rotated_rectangle
    proceed()


def diff_overlap(ctx: Context, proceed):
    """
    pipe: 去除与其它要素的压盖
    """
    for _, geom in ctx.buildings:
        ctx.result = ctx.result.difference(geom)

    for _, direction, kind, lane_l, lane_r, form, geom in ctx.outer_roads:
        line_width = tools.get_road_width(direction, kind, lane_l, lane_r, form)
        line_buffer = line_width * METER / 2
        line = geom.buffer(line_buffer)
        ctx.result = ctx.result.difference(line)

    proceed()


def smooth_parking(opening_buffer: float):
    """
    pipe: 平滑停车场面
    """

    def pipe(ctx: Context, proceed):
        geoms = flat_geom(ctx.result)
        geom = max(geoms, key=lambda x: x.area)
        geom = geom.buffer(-opening_buffer * METER).buffer(opening_buffer * METER)
        if geom.is_empty:
            # TODO: 当 geom.is_empty 时，计算 geom 主轴，再外扩。
            pass
        elif geom.geom_type == "MultiPolygon":
            geom = max(geom.geoms, key=lambda x: x.area)

        ctx.result = smooth_point_string.smooth_polygon(str(geom))
        proceed()

    return pipe


# helpers:


def calc_iou(geom1, geom2):
    """
    交并比
    """
    # noinspection PyBroadException
    try:
        geom1 = wkt.loads(geom1) if type(geom1) is str else geom1
        geom2 = wkt.loads(geom2) if type(geom2) is str else geom2

        a = geom1.intersection(geom2)
        b = geom1.union(geom2)

        a = a.area
        b = b.area
        return a / b if b > 0 else 0.0
    except:
        return 0.0


def calc_area_ratio(label, geom):
    """
    压盖面积率：计算 geom 被 label 所压盖的部分占 geom 总面积的比值
    """
    # noinspection PyBroadException
    try:
        label = wkt.loads(label) if type(label) is str else label
        geom = wkt.loads(geom) if type(geom) is str else geom

        a = label.intersection(geom)
        b = geom

        a = a.area
        b = b.area
        return a / b if b > 0 else 0.0
    except:
        return 0.0


def flat_geom(geom_wkt: str):
    """
    将 MultiPolygon 展开为 Polygon
    """

    def flat(geometry):
        geom_type = geometry.geom_type
        if geom_type == "Polygon":
            yield geometry
        elif geom_type == "MultiPolygon":
            yield from geometry.geoms
        elif geom_type == "GeometryCollection":
            yield from (x for g in geometry.geoms for x in flat(g))

    geom = wkt.loads(geom_wkt) if type(geom_wkt) is str else geom_wkt
    return list(flat(geom))


def get_inner_roads(geom):
    """
    数据库：根据 geom 查询压盖的内部路
    """
    with get_connection(pgsql.ROAD_CONFIG) as conn:
        sql = """
            select link_id, st_astext(geom) from nav_link
            where form = '52' and st_intersects(st_geomfromtext(%s, 4326), geom);
        """
        return pgsql.fetch_all(conn, sql, [geom])


def get_outer_roads(geom):
    """
    数据库：根据 geom 查询压盖的高等级道路
    """
    with get_connection(pgsql.ROAD_CONFIG) as conn:
        sql = """
            select link_id, dir, kind, lane_l, lane_r, form, st_astext(geom) from nav_link
            where kind < 8 and st_intersects(st_geomfromtext(%s, 4326), geom);
        """
        ret = pgsql.fetch_all(conn, sql, [geom])
        return [(*x[:-1], wkt.loads(x[-1])) for x in ret]


def get_buildings(geom):
    """
    数据库：根据 geom 查询压盖的建筑物
    """
    with get_connection(pgsql.BACK_CONFIG) as conn:
        sql = """
            select face_id, st_astext(geom) from bud_face
            where st_intersects(st_geomfromtext(%s, 4326), geom);
        """
        ret = pgsql.fetch_all(conn, sql, [geom])
        return [(face_id, wkt.loads(geom)) for face_id, geom in ret]


def repair(geoms: list[tuple[str, str]]):
    """
    修复给定的 geoms
    """
    pipe = pipeline.Pipeline(
        convert_bd09mc_to_gcj02,
        filter_by_area(max_area=1000 * METER**2),
        get_features,
        calc_building_overlap(max_overlap_ratio=0.8),
        calc_min_rect,
        diff_overlap,
        smooth_parking(opening_buffer=2),
        filter_by_iou(min_iou=0.2),
    )

    for bid, geom in geoms:
        ctx = Context(wkt.loads(geom))
        pipe(ctx)
        yield bid, ctx


def main():
    """
    主函数：入口点
    """
    file_path = Path("poly_res.satellite.relate_result.bid.tsv")

    output_name = f"{file_path.stem}_fragment_repair{file_path.suffix}"
    output_path = file_path.parent / output_name
    output_path.unlink(missing_ok=True)

    geoms = [
        (bid, shape)
        for bid, shape, shape_type, area_size, in_aoi in tsv.read_tsv(file_path)
        if shape_type == "concave"
    ]
    # 保存结果
    for bid, ctx in tqdm(repair(geoms), total=len(geoms)):
        repair_geom = ctx.error if ctx.error else ctx.result
        row = [bid, ctx.diff_iou, ctx.geom, repair_geom]
        tsv.write_tsv(output_path, [row], mode="a")


if __name__ == "__main__":
    main()
