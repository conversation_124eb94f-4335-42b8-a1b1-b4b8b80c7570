"""
高 pv 新增情报
https://console.cloud.baidu-int.com/devops/icafe/issue/poiinshanghai-15434/show?source=copy-shortcut
"""
import argparse
import csv
import functools
import glob
import logging
import shutil
from multiprocessing import Pool
import traceback

import dataclasses
import json
import os
import sys
import time
import uuid
from datetime import datetime
from typing import List

import tqdm
import requests

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.tools import pgsql, notice_tool
from src.model_mysql.beeflow_model import BFQuery

TAGS = [
    '购物;购物中心'
]
MIN_PV = 1000
TIME_FORMAT = '%Y-%m-%d%H:%M:%S'
SRC_TCZ = 9
SRC_TCQ = 12
PROJECT_TCZ = 'TCZ'
PROJECT_TCQ = 'TCQ'
PROJECT_NEW = 'NEW'  # 新开通的 poi

DAY = 24 * 3600
COOLING_DOWN_PERIOD = 15 * DAY


@dataclasses.dataclass
class Context:
    """
    上下文
    """
    poi_conn = None
    poi_curs = None

    bf_model = None

    back_conn = None
    back_curs = None

    time = ''

    INFO_SRC = SRC_TCQ  # 情报类型

    BATCH_ID = 'TCZGWH2024103101'
    # BATCH_ID = "TCQGWH2024110101"
    FROM_SRC = 'high_pv_add_park'
    COOLING_DOWN_PERIOD = 15 * DAY  # 15 天

    HI_TOKEN = 'd399e15bdee106761428d56441394ee5d'
    MIN_PV = MIN_PV

    monitored_config = {
        'shopping': {
            # 'where': f" std_tag = '购物;购物中心' and click_pv > 1000 ",
            'where': f" std_tag in ('购物;购物中心', '购物;百货商场') and click_pv > 1000 ",
            'in_fact_no_park': True,  # 事实上没有停车场
            'cooling_down': COOLING_DOWN_PERIOD,  # 冷却期，过滤掉
            'have_park_level': {
                'current': True,  # 当前点有停车场，就过滤
            },
        },
        'shopping2': {
            'where': f" std_tag in ('购物;超市') and click_pv > 1000 ",
            'in_fact_no_park': True,  # 事实上没有停车场
            'cooling_down': COOLING_DOWN_PERIOD,  # 冷却期，过滤掉
            'have_park_level': {
                'ancestor': True,  # 比当前点高的点（父点）有停车场，那么就过滤
            },
        },
        'hospital': {
            # 'where': f" std_tag like '医疗%' and click_pv > 1000 ",
            'where': f" std_tag in ('医疗;综合医院', '医疗;专科医院') and click_pv > 1000 ",
            'in_fact_no_park': True,  # 事实上没有停车场
            'cooling_down': COOLING_DOWN_PERIOD,  # 冷却期，过滤掉
            'have_park_level': {
                'descendants': True,  # 子点有，也过滤
            },
        },
        'transportation1': {
            'where': f" (std_tag in ('交通设施;火车站', '交通设施;港口') or (std_tag like '交通设施%' and show_tag = '航站楼')) "
                     f" and click_pv > 1000 ",
            'in_fact_no_park': True,  # 事实上没有停车场
            'cooling_down': COOLING_DOWN_PERIOD,  # 冷却期，过滤掉
            'have_park_level': {
                'current': True,  # 当前点有停车场，就过滤
            },
        },
        'transportation2': {
            'where': f" std_tag = '交通设施;飞机场' and click_pv > 1000 ",
            'in_fact_no_park': True,  # 事实上没有停车场
            'cooling_down': COOLING_DOWN_PERIOD,  # 冷却期，过滤掉
            'have_park_level': {
                'descendants': True,  # 子点有，也过滤
            },
        },
        'scenic': {
            'where': f" std_tag like '旅游景点%' and click_pv > 1000 ",
            # 'area': {
            #     'min': 10000,
            #     'max': 20000,
            # },
            # 'name': {
            #     'forbidden_suffixes': [
            #         '观景台', '观景平台', '妈祖庙', '碑', '风车', '桥', '路', '牌坊', '门', '舞台', '观景点', '观赏点',
            #         '打卡点', '起点', '终点', '灯塔', '网红墙', '玻璃栈道',
            #     ],
            # },
            'in_fact_no_park': True,  # 事实上没有停车场
            'cooling_down': COOLING_DOWN_PERIOD,  # 冷却期，过滤掉
            'have_park_level': {  # 层级过滤
                'ancestor': True,  # 比当前点高的点（父点）有停车场，那么就过滤
            },
        },
        'new_file': {
            'where': './data/new.csv',
            'in_fact_no_park': True,  # 事实上没有停车场
            'cooling_down': 7 * DAY,  # 冷却期，过滤掉
            'have_park_level': {
                'current': True,  # 当前点有停车场，就过滤
            },
        },
    }
    monitored_types = []

    def gen_ref_qb_id(self, bid: str) -> str:
        """
        生成原始情报id
        """
        return '-'.join([self.get_ref_qb_id_pref(bid), uuid.uuid4().hex])

    def get_ref_qb_id_pref(self, bid: str) -> str:
        """
        获取原始情报id前缀
        """
        return '-'.join([bid, self.FROM_SRC])


def gen_ctx() -> Context:
    """
    生成上下文
    """
    ctx = Context()

    ctx.poi_conn = pgsql.get_connection(pgsql.POI_SLAVER_CONFIG)
    # ctx.poi_conn = pgsql.get_connection(pgsql.POI_CONFIG)
    ctx.poi_curs = ctx.poi_conn.cursor()

    ctx.back_conn = pgsql.get_connection(pgsql.BACK_CONFIG)
    ctx.back_curs = ctx.back_conn.cursor()

    ctx.bf_model = BFQuery()

    ctx.time = _get_cur_time()

    return ctx


def get_cate_type(std_tag: str) -> str:
    """
    获取垂类类型
    """
    if std_tag.startswith('购物'):
        return '购物'
    if std_tag.startswith('医疗'):
        return '医院'
    if std_tag.startswith('旅游景点'):
        return '景区'
    if std_tag.startswith('交通设施'):
        return '交通枢流'
    return '其他'


def timer(fun):
    """
    打印耗时
    """
    @functools.wraps(fun)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        res = fun(*args, **kwargs)
        end_time = time.time()
        print("{} 耗时{}; curTime: {}".format(
            fun.__name__, end_time - start_time, time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))))
        return res

    return wrapper


def _get_one(curs, sql: str) -> tuple:
    """
    获取一条记录
    """
    print(sql)
    curs.execute(sql)
    res = curs.fetchone()

    if res is None or len(res) == 0:
        return tuple()
    return res


def _get_list(curs, sql: str) -> list:
    """
    获取多条记录
    """
    print(sql)
    curs.execute(sql)
    res = curs.fetchall()

    if res is None or len(res) == 0:
        return []
    return res


def _get_cur_time() -> str:
    """
    获取当前时间
    """
    return time.strftime(TIME_FORMAT, time.localtime(time.time()))


def _get_children_bids(ctx: Context, master_bids: list) -> list:
    """
    获取主点下的所有子点 bid
    """
    if len(master_bids) == 0:
        return []

    def _get_children(bids: list) -> list:
        """
        获取子点
        """
        bid_str = ','.join([f"'{bid}'" for bid in bids])
        sql = f"select distinct(bid) from poi where relation_bid in ({bid_str})"
        res = _get_list(ctx.poi_curs, sql)
        return [item[0] for item in res]

    resp = []
    # 避免 master_bids 一次过多，分开查询
    bid_chunks = array_chunk(master_bids, 30)
    for bid_chunk in bid_chunks:
        resp += _get_children(bid_chunk)
    return resp


def array_chunk(data: list, size: int) -> list:
    """
    数据分成块，size 是每块的数量；（最后一块的数量会 <= num）
    :param data:
    :param size:
    :return:
    """
    resp = []

    datum = []
    for _item in data:
        datum.append(_item)

        if len(datum) == size:
            resp.append(datum)
            datum = []

    if len(datum) > 0:
        resp.append(datum)
    return resp


@timer
def _get_descendants_bids(ctc: Context, master_bid: str) -> list:
    """
    获取属于主点的所有bid（包括主点本身）（子孙）
    """
    res = [master_bid]
    tmp = res
    while True:
        children = _get_children_bids(ctc, tmp)
        eff_bids = []  # 没出现过的才是有效的
        for child in children:
            if child not in res:
                eff_bids.append(child)

        if len(eff_bids) == 0:
            break

        res += eff_bids
        tmp = eff_bids
    return res


def _get_parent_id(ctx: Context, bid: str) -> str:
    """
    获取父点 id
    """
    qry = f"select relation_bid from poi where bid = '{bid}'"
    res = _get_one(ctx.poi_curs, qry)
    if len(res) == 0:
        return ''
    return res[0]


def _get_ancestor_bids(ctx: Context, bid: str) -> list:
    """
    获取所有的祖先 bid 集合，包括自身
    """

    def _is_valid(a_bid: str) -> bool:
        """
        是否是有效 bid，有效返回 True
        """
        return a_bid not in ['', '0']

    if not _is_valid(bid):
        return []

    resp = [bid]
    while True:
        parent_id = _get_parent_id(ctx, bid)
        if not _is_valid(parent_id) or parent_id in resp:
            break
        resp.append(parent_id)
        bid = parent_id
        if len(resp) > 10:
            break
    return resp


def _any_bid_have_park(ctx: Context, bids: list) -> bool:
    """
    任意一个 bid 有停车场，返回 True
    """
    bid_str = ','.join([f"'{bid}'" for bid in bids])
    # sql = (f"select bid from park_statistical_data where precise10 = 1 "
    #        f"and ( bid in ({bid_str}) or parent_bid in ({bid_str}))")
    # res = _get_one(ctx.poi_curs, sql)
    # if len(res) > 0:
    #     return True

    # parking 表时效性更高
    # sql = (f"select bid from parking where precise = 1 "
    #        f"and ( bid in ({bid_str}) or parent_id in ({bid_str}))")
    sql = f"select bid from parking where ( bid in ({bid_str}) or parent_id in ({bid_str}))"
    res = _get_one(ctx.back_curs, sql)
    return len(res) > 0


@timer
def _have_park(ctx: Context, master_poi: dict, conf) -> bool:
    """
    主点 poi 有停车场，返回 True
    只有精准停车场才算停车场
    """
    # {  # 层级过滤
    #     'descendants': True,  # 子孙点有停车场，那么就过滤
    #     'current': True,  # 当前点有停车场，就过滤
    #     'ancestor': True, # 祖先点有就过滤
    # }

    if 'current' in conf:
        return _any_bid_have_park(ctx, [master_poi['bid']])
    if 'ancestor' in conf:
        return _any_bid_have_park(ctx, _get_ancestor_bids(ctx, master_poi['bid']))
    if 'descendants' in conf:
        bids_chunks = array_chunk(_get_descendants_bids(ctx, master_poi['bid']), 30)
        for bids_chunk in bids_chunks:
            if _any_bid_have_park(ctx, bids_chunk):
                return True
        return False
    return False


@timer
def _in_fact_no_park(ctx: Context, master_poi: dict, conf) -> bool:
    """
    事实上没有没有停车场，返回 True
    """
    sql = (f"select park_manual_task_id, item_content from park_manual_task "
           f"where source_id like '{ctx.get_ref_qb_id_pref(master_poi['bid'])}%'")
    # park_manual_task_id_3305
    # sql = (f"select park_manual_task_id, item_content from park_manual_task "
    #        f"where source_id = 'park_manual_task_id_3305'")
    print(sql)
    res = ctx.bf_model.queryall(sql)
    if res is None or len(res) == 0:
        return False

    # keyword = '主点不存在停车场'
    keywords = [
        '主点不存在停车场',
        '主点无停车场',
    ]

    for item in res:
        content = json.loads(item[1])
        for data in content:
            note = data['note']
            for a_work in note['work']:
                for a_keyword in keywords:
                    if a_keyword in a_work['note']:
                        return True
    return False


@timer
def _in_cooling_down_period(ctx: Context, master_poi: dict, cooling_down_period) -> bool:
    """
    处于冷却期，返回 True
    冷却期指，投放过，后半个月不用再投放
    """
    sql = (f"select created_at from integration_qb "
           # f"where ref_qb_id like '{ctx.get_ref_qb_id_pref(master_poi['bid'])}%' "
           f"where ref_qb_id like '{ctx.get_ref_qb_id_pref(master_poi['bid'])}%' "
           f"and ref_qb_batch_id not in ('TCQGWH2024110806', 'TCQGWH2024110805', 'TCQGWH2024110803', "
           f"'TCQGWH2024110802', 'TCQGWH2024110801')"
           f"order by id desc")
    res = _get_one(ctx.poi_curs, sql)
    if len(res) == 0:
        return False

    # cooling_down_period = conf['cooling_down_period'] if 'cooling_down_period' in conf else ctx.COOLING_DOWN_PERIOD
    created_at = res[0]
    dif_second = (datetime.strptime(ctx.time, TIME_FORMAT) - created_at).total_seconds()
    print(master_poi['bid'], dif_second, 'dif_second', created_at, ctx.time, datetime.strptime(ctx.time, TIME_FORMAT))
    return dif_second < cooling_down_period


def _filter_fn_exp(ctx: Context, master_poi: dict, conf) -> bool:
    """
    过滤函数，被过滤返回 True; 仅示例
    """


def _filter_by_area(ctx: Context, poi: dict, area_conf: dict) -> bool:
    """
    根据面积过滤, 不符合面积要求返回 True
    """
    min_area, max_area = area_conf['min'], area_conf['max']
    area = _get_poi_area(ctx, poi)
    poi['area'] = area
    if min_area < poi['area'] < max_area:
        return False
    return True


def _filter_by_name(ctx: Context, poi: dict, name_conf: dict) -> bool:
    """
    根据名称过滤
    """
    forbidden_suffixes = name_conf['forbidden_suffixes']
    filtered = False
    for a_suffix in forbidden_suffixes:
        if str(poi['name']).endswith(a_suffix):
            filtered = True
            break
    return filtered


def _filter_by_conf(ctx: Context, pois: list, conf: dict) -> list:
    """
    根据配置过滤
    """
    name2fn = {  # 应该按过滤的速度来排，速度越快的应该放前面
        'name': _filter_by_name,
        'area': _filter_by_area,
        'in_fact_no_park': _in_fact_no_park,  # 事实上没有停车场
        'cooling_down': _in_cooling_down_period,  # 冷却期，过滤掉
        'have_park_level': _have_park,
    }
    resp = []
    for idx, a_poi in enumerate(pois):
        print(f"总共是：{len(pois)}; 当前是：{idx + 1}")

        if idx % 100 == 0:
            ctx.poi_conn.rollback()
            print(f"{idx}; 提交或者回滚事务，避免被 kill 掉")

        filtered = False
        for name, fn in name2fn.items():
            if name not in conf:
                continue
            filtered = fn(ctx, a_poi, conf[name])
            if filtered:
                break
        if not filtered:
            resp.append(a_poi)
    return resp


def _get_poi_area(ctx, poi: dict) -> float:
    """
    获取 poi 的面积
    """
    sql = (f"select area from blu_face_poi poi left join blu_face aoi on poi.face_id = aoi.face_id"
           f" where poi_bid = '{poi['bid']}'")
    res = _get_one(ctx.back_curs, sql)
    if len(res) == 0:
        return 0
    return float(res[0])


def _get_pois_by_where(ctx: Context, where: str) -> list:
    """
    根据条件获取 poi
    """
    sql = (f"select bid, st_astext(geometry) geom, name, click_pv, std_tag, relation_bid from poi "
           f"where {where}")
    # sql = (f"select bid, st_astext(geometry) geom, name, click_pv, std_tag from poi "
    #        f"where std_tag in ({ctx.tag_str}) and click_pv > {ctx.MIN_PV}")
    # sql = (f"select bid, st_astext(geometry) geom, name, click_pv, std_tag, relation_bid from poi "
    #        f"where bid = '6146465528451856023' ")
    _pois = _get_list(ctx.poi_curs, sql)
    res = []
    for _poi in _pois:
        res.append({
            'bid': _poi[0],
            'geom': _poi[1],
            'name': _poi[2],
            'click_pv': _poi[3],
            'std_tag': _poi[4],
            'relation_bid': _poi[5],
        })
    return res


def split_list_evenly_simpler(input_list, num_splits):
    """
    拆分列表
    """
    avg_size = len(input_list) // num_splits
    remainder = len(input_list) % num_splits
    return [
        input_list[i * avg_size + min(i, remainder):(i + 1) * avg_size + min(i + 1, remainder)]
        for i in range(num_splits)
    ]


def collect_res(dir_name, header):
    """
    收集结果
    """
    resp = []
    files = glob.glob("{}/*".format(dir_name))
    for a_file in files:
        with open(a_file, 'r') as hdr:
            reader = csv.reader(hdr)
            # next(reader)
            for row in reader:
                data = {}
                for idx, key in enumerate(header):
                    data[key] = row[idx]
                resp.append(data)
    return resp


def _filter_and_writer(poi_chunk: list, file: str, config: dict):
    with open(file, 'w') as hdw:
        writer = csv.writer(hdw)
        items = _filter_by_conf(gen_ctx(), poi_chunk, config)
        if len(items) == 0:
            return
        for item in items:
            writer.writerow(list(item.values()))


def error_callback(error):
    """
    异常打印
    """
    print(f"有异常：Error: {error}")
    print("Stack trace:")
    print("".join(traceback.format_exception(type(error), error, error.__traceback__)))


def multi_filter(pois: list, config: dict, multi: int = 20) -> list:
    """
    批量过滤
    """
    if len(pois) == 0:
        return []

    dst = f'./data/collect/{uuid.uuid4().hex}'
    if not os.path.exists(dst):
        os.makedirs(dst)
    else:
        os.system(f"rm -rf {dst}/*")

    poi_chunks = split_list_evenly_simpler(pois, multi)
    print(f"pois len:{len(pois)}; {multi}")
    p = Pool(multi)
    for idx, a_poi_chunk in enumerate(poi_chunks):
        _file = os.path.join(dst, f"{idx}.csv")
        p.apply_async(_filter_and_writer, args=(a_poi_chunk, _file, config), error_callback=error_callback)

    start = time.time()
    print('Waiting for all subprocesses done...')
    p.close()
    p.join()
    end = time.time()
    print('All subprocesses done. {}s'.format(end - start))

    resp = collect_res(dst, list(pois[0].keys()))

    shutil.rmtree(dst)

    return resp


def _get_pois_from_file(ctx: Context, file: str) -> list:
    """
    从文件中读取 poi
    """
    if not os.path.exists(file):
        return []

    with open(file, 'r') as hdr:
        reader = csv.reader(hdr)
        bids = [row[0] for row in reader]

    # bids = ['6073983802283207604']  # todo test

    bid_chunks = array_chunk(list(set(bids)), 50)
    pois = []
    for a_bid_chunk in bid_chunks:
        bid_str = ','.join([f"'{bid}'" for bid in a_bid_chunk])
        where = f"bid in ({bid_str})"
        pois += _get_pois_by_where(ctx, where)
    return pois


@timer
def get_monitored_pois(ctx: Context, num: int) -> list:
    """
    获取监控的 poi
    """
    result = []
    for _type, config in ctx.monitored_config.items():
        if _type not in ctx.monitored_types:
            continue

        new_ctx = gen_ctx()
        if _type == 'new_file':  # 从文件中读取
            pois = _get_pois_from_file(new_ctx, config['where'])
        else:
            pois = _get_pois_by_where(new_ctx, config['where'])

        # pois = _filter_by_conf(ctx, pois, config)
        pois = multi_filter(pois, config, 50)
        print(f"_type:{_type}; len:{len(pois)}")

        result += pois
        if 0 < num <= len(result):
            break
    return result


def get_tcz_param(ctx: Context, master_poi: dict) -> dict:
    """
    获取 tcz 推送参数
    """
    _pois = [
        ('', master_poi['geom']),
    ]
    return {
        "src": 9,  # 必填，9:停车场, 10:停车场出入口
        "ref_qb_id": ctx.gen_ref_qb_id(master_poi['bid']),  # 原始情报id，如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": ctx.BATCH_ID,  # 必填，批次号
        "main_poi_bid": master_poi['bid'],  # 停车场主点bid
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标 不用管这里固定的占个位置
        "work_type": 2,  # 必填，1自动 2人工
        "qb_type": 1,
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": "TCZ",  # 必填 固定不用改
                "priority": 10,  # 必填 自己定义
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填 固定不用改
                "online_source": "chuilei_scope.rg",  # 必填 固定不用改
                "creator": "<EMAIL>",  # 必填 固定不用改
                "tips": f"工艺提示：高pv停车场新增",  # 必填 自己天写备注
                "parks": [{'park_name': _park_name, 'park_wkt': _poi_wkt, 'parent_id': master_poi['bid']} for
                          _park_name, _poi_wkt in _pois]  # 具体的停车场位置情报，没有的话就是空
                # 具体的停车场位置情报，没有的话就是空列表
            }
        },
        'from_src': ctx.FROM_SRC,  # 高 pv 停车场新增
    }


def get_tcq_param(ctx: Context, master_poi: dict) -> dict:
    """
    获取 tcq 推送参数
    """
    master_bid = master_poi['bid']
    return {
        "src": ctx.INFO_SRC,  # 必填，9:停车场, 10:停车场出入口, 11:停车场整体制作
        "ref_qb_id": ctx.gen_ref_qb_id(master_bid),  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": ctx.BATCH_ID,  # 必填，批次号
        "main_poi_bid": master_bid,
        "mark_geom": "POINT(1 2)",  # 必填 停车场坐标，现在无意义，可以固定死
        "work_type": 2,  # 必填，1自动 2人工
        "qb_type": 1,
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": "TCQ",  # 必填
                "priority": 10,  # 必填
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": "新增停车场和出入口",
                "park_list": [
                    {
                        "park": {  # 停车场信息
                            "bid": '',
                            "name": '',
                            "wkt": master_poi['geom'],
                            "show_tag": "停车场",
                            "status": 1,
                            "parent_bid": master_bid
                        },
                        "park_gate_list": [  # 停车场大门信息
                            # {
                            #     "bid": "",
                            #     "name": "",
                            #     "wkt": master_poi['geom'],
                            #     "show_tag": "",
                            #     "status": 1,
                            #     "parent_bid": "",
                            # },
                            # {
                            #     "bid": "",
                            #     "name": "",
                            #     "wkt": master_poi['geom'],
                            #     "show_tag": "",
                            #     "status": 1,
                            #     "parent_bid": ""
                            # },
                            # {
                            #     "bid": "",
                            #     "name": "",
                            #     "wkt": master_poi['geom'],
                            #     "show_tag": "",
                            #     "status": 1,
                            #     "parent_bid": ""
                            # },
                        ]
                    }
                ]
            }
        },
        'from_src': ctx.FROM_SRC,  # 高 pv 停车场新增
    }


@timer
def deliver(ctx: Context, master_poi: dict) -> bool:
    """
    情报投放
    """
    if ctx.INFO_SRC == SRC_TCZ:
        data = get_tcz_param(ctx, master_poi)
    else:
        data = get_tcq_param(ctx, master_poi)
    req = requests.post(
        url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
        json=data
    )
    print(req.content)
    print(master_poi['bid'], req.json())

    """
    {'code': 0, 'msg': '', 'data': {'qb_id': 11825219}}
    """
    res = req.json()
    if 'data' in res and 'qb_id' in res['data'] and res['data']['qb_id'] > 0:
        return True
    return False


def _deliver_fn_exp(ctx: Context, pois: list):
    """
    投放函数示例
    """


@timer
def deliver_work_platform(ctx: Context, pois: list):
    """
    投放到作业平台
    """
    is_ok, no_ok = 0, 0
    for _poi in pois:
        ok = deliver(ctx, _poi)
        if not ok:
            print(f"{_poi} 投放失败")
            no_ok += 1
        else:
            print(f"{_poi} 投放成功")
            is_ok += 1
        # break  # 测试
    text = (f"总量：{len(pois)}, 投放成功：{is_ok}; 投放失败：{no_ok}；监控的 std_tag: {ctx.monitored_types}; "
            f"clickPv:{ctx.MIN_PV}; 批次号：{ctx.BATCH_ID}")
    print(text)
    if 'new_file' in ctx.monitored_types:
        text = f"总量：{len(pois)}, 投放成功：{is_ok}; 投放失败：{no_ok}；批次号：{ctx.BATCH_ID}; 新开购物中心，请每天优先核实"
    notice_tool.send_hi(text, token=ctx.HI_TOKEN)
    return


def deliver_info_platform(ctx: Context, pois: list):
    """
    投放到情报平台
    """
    poi_conn = pgsql.get_connection(pgsql.POI_CONFIG)
    poi_curs = poi_conn.cursor()

    day = datetime.now().date().strftime('%Y%m%d')
    vals = []
    for a_poi in pois:
        vals.append(f"("
                    f"'{uuid.uuid4().hex}', "  # qb_id
                    f"'HIGH_PV', "  # model
                    f"'', "  # recall_desc
                    f"'{a_poi['bid']}', "  # parent_id
                    f"'', "  # parking_bid
                    f"'', "  # parking_gate_bid
                    f"'', "  # node_id
                    f"'{get_cate_type(a_poi['std_tag'])}', "  # main_std_tag
                    f"'', "  # features
                    f"'', "  # special_tag
                    f"0, "  # auto_qb
                    f"0, "  # is_push
                    f"1, "  # status
                    f"st_geomfromtext('POINT(0 0)', 4326), "  # 坐标默认为空
                    f"'00', "  # part
                    f"'{day}', "  # dt
                    f"99)"  # push_status, 标记待挖掘
                    )
    qry = (f"insert into parking_miss_prepush (qb_id, model, recall_desc, parent_id, parking_bid, parking_gate_bid, "
           f"node_id, main_std_tag, features, special_tag, auto_qb, is_push, status, geom, part, dt, push_status) "
           f"values {','.join(vals)}")
    # print(qry)
    poi_curs.execute(qry)
    poi_conn.commit()
    # ctx.poi_conn.rollback()


@timer
def deliver_csv(ctx: Context, pois: list):
    """
    投放到（写入）csv，方便验证
    """
    if len(pois) == 0:
        return

    dest = './data'
    if not os.path.exists(dest):
        os.makedirs(dest)
    filepath = f"{dest}/{ctx.time}_poi.csv"
    with open(filepath, 'w') as hdw:
        writer = csv.writer(hdw)
        for poi in pois:
            writer.writerow(list(poi.values()))
    print(filepath)
    return


def _get_pois_from_file_tmp(filepath: str):
    pois = []
    header = ['bid', 'geom', 'name', 'click_pv', 'std_tag', 'relation_bid']
    with open(filepath, 'r') as hdr:
        reader = csv.reader(hdr)
        for row in reader:
            a_poi = {}
            for idx, key in enumerate(header):
                a_poi[key] = row[idx]
            pois.append(a_poi)
    return pois


@timer
def handled_center(ctx: Context, num: int, deliver_fns: List[callable]):
    """
    高 pv 情报处理中心
    num: 情报数量
    _deliver_fn: 投放函数
    """
    print(f"监控的标签有：{ctx.monitored_types}; 限制的 pv 是：{ctx.MIN_PV}")
    delivered_pois = get_monitored_pois(ctx, num)
    if num < 1:
        pois = delivered_pois
    else:
        pois = delivered_pois[:num]
    print(f"有情报量：{len(delivered_pois)}; 限制数量：{num};")

    if len(pois) > 0:
        for deliver_fn in deliver_fns:
            print(f"可以投放的量：{len(pois)}; deliver_fn:{deliver_fn.__name__}")
            deliver_fn(ctx, pois)


def again_push(ctx: Context):
    """
    再推送一次，一般是捞回，或者批次作废
    """
    qry = f"select main_poi_bid from integration_qb where ref_qb_batch_id = 'TCQGWH2024110801'"
    res = _get_list(ctx.poi_curs, qry)

    parent_ids = [item[0] for item in res]
    pids_str = ','.join([f"'{pid}'" for pid in parent_ids])
    pois = _get_pois_by_where(ctx, where=f" bid in ({pids_str})")

    deliver_work_platform(ctx, pois)


def main():
    """
    主函数
    """
    dest = str(ARGS.dest)
    num = int(ARGS.num)

    deliver_fns = [deliver_csv]
    if dest == 'work':
        deliver_fns = [deliver_csv, deliver_work_platform]
    elif dest == 'info':
        deliver_fns = [deliver_csv, deliver_info_platform]

    ctx = gen_ctx()
    project = ARGS.project
    if project not in [PROJECT_TCZ, PROJECT_TCQ, PROJECT_NEW]:
        raise Exception(f"{project} 项目不允许")

    if ARGS.types != '':
        ctx.monitored_types = str(ARGS.types).split(',')

    ctx.MIN_PV = int(ARGS.min_pv)
    if project == PROJECT_TCZ:
        ctx.BATCH_ID = 'TCZGWH2024103105'
        ctx.INFO_SRC = SRC_TCZ
    elif project == PROJECT_NEW:
        ctx.BATCH_ID = 'TCQGWH2024112601'
        ctx.INFO_SRC = SRC_TCQ
    else:
        ctx.BATCH_ID = 'TCQGWH2024110806'
        ctx.INFO_SRC = SRC_TCQ

    # again_push(ctx)
    handled_center(ctx, num, deliver_fns)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='高 pv 新增情报')
    parser.add_argument('--dest', type=str, default='csv')  # 情报添加到的目的地，有 csv 和 work（作业平台）
    parser.add_argument('--num', type=int, default=1)  # 情报量级
    parser.add_argument('--min_pv', type=int, default=MIN_PV)  # 最小 pv
    parser.add_argument('--project', type=str, required=True)  # 项目
    parser.add_argument('--types', type=str, default='shopping')  # 项目

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()




