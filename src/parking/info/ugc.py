# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ugc数据下发
https://console.cloud.baidu-int.com/devops/icafe/issue/poiinshanghai-15717/show
"""
import os
import sys
import re
import json
from typing import List, Tuple

import dataclasses
import requests

ROOT_PATH = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from tqdm import tqdm

from src.parking.recognition import dbutils
from src.tools import pgsql, notice_tool
from src.model_mysql.beeflow_model import BFQuery


batch_id2num = {}


class UgcStatus:
    """
    状态
    """
    INIT = 0
    DONE = 1
    ERROR = 2


@dataclasses.dataclass
class UgcInfo:
    """
    ugc 情报
    """
    id: int
    bid: str
    content: dict
    remark: str
    status: int

    def gen_ref_qb_id(self) -> str:
        """
        生成情报 id
        """
        return f"_ugc_{self.id}"

    def is_gate(self) -> bool:
        """
        出入口返回 True
        """
        return '入口' in self.content['std_tag']


def get_infos() -> Tuple[List[UgcInfo], List[UgcInfo]]:
    """
    获取 ugc 情报
    """
    bf_query = BFQuery()

    qry = f"select id, bid, content, remark, status from park_ugc_task where option_type = 'add' and status in (0, 2)"
    res = bf_query.queryall(qry)
    if res is None or not res:
        return [], []

    ugc_parks = []
    ugc_gates = []

    no = 0
    pushed = 0
    pushed_limit = 5
    for item in res:
        _poi = _get_poi(item[1])
        if len(_poi) == 0:
            no += 1
            continue

        content = json.loads(item[2])
        content['std_tag'] = _poi['std_tag']  # 数据中 std_tag 不可信
        ugc = UgcInfo(
            id=item[0],
            bid=item[1],
            content=content,
            remark=item[3],
            status=item[4],
        )

        _park = _get_park(ugc.bid)
        if len(_park) == 0:
            # if pushed_limit > 0 and pushed < pushed_limit:
            #     _push_park_gate(_poi, ugc.gen_ref_qb_id(), [])
            #     pushed += 1
            #
            #     ugc.status = UgcStatus.DONE
            #     ugc.remark = "bid 不存在 park_online_data 库"
            #     _save_ugc_handled_result(ugc)
            continue

        if ugc.is_gate():
            ugc_gates.append(ugc)
        else:
            ugc_parks.append(ugc)
    print(f"有{no}个 bid 在 poi 表不存在")
    print(f"在 poi 库存在，但不存在 park_online_data 表；推送了:{pushed}")
    # _send_hi()
    return ugc_parks, ugc_gates


def _get_poi(bid: str) -> dict:
    qry = f"select bid, relation_bid, name, st_astext(geometry) as geom, std_tag from poi where bid='{bid}';"
    res = dbutils.fetch_one(pgsql.POI_CONFIG, qry)
    if res is None or not res:
        return {}
    return {
        'bid': res[0],
        'parent_id': res[1],
        'name': res[2],
        'geom': res[3],
        'std_tag': res[4],
    }


def _get_park(bid: str) -> dict:
    """
    获取停车场
    """
    qry = f"""
    select bid, precise, parent_id, name, st_astext(gcj_geom) as geom, std_tag 
    from park_online_data where bid='{bid}';
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, qry)
    if res is None or not res:
        return {}
    return {
        'bid': res[0],
        'precise': res[1],
        'parent_id': res[2],
        'name': res[3],
        'geom': res[4],
        'std_tag': res[5],
    }


def _park_has_gate(bid: str) -> bool:
    """
    停车场有出入口，返回 True
    """
    qry = f"select * from park_online_data where parent_id='{bid}' and status = 1;"
    res = dbutils.fetch_one(pgsql.POI_CONFIG, qry)
    if res is None or not res:
        return False
    return True


def handle_apostrophe(val: str) -> str:
    """
    单引号处理
    """
    return val.replace("'", "''")


def _get_gates_by_wkt_and_name(maybe_name: str, geom: str) -> list:
    """
    根据范围和名称获取出入口
    """
    if geom == '' or maybe_name == '停车场':
        return []

    qry = (f"select bid , st_astext(gcj_geom) geom, name from park_online_data "
           f"where name like '{handle_apostrophe(maybe_name)}%' "
           f"and std_tag='出入口;停车场出入口' and st_intersects(gcj_geom, st_geomfromtext('{geom}', 4326))")
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    if res is None or not res:
        return []
    return [{
        'bid': item[0],
        'geom': item[1],
        'name': item[2],
    } for item in res]


def processing_park(ugc_infos: List[UgcInfo]):
    """
    处理停车场数据
    :return
    """
    for ugc in tqdm(ugc_infos):
        ugc, handled = _handle_ugc_park_gate(ugc)
        _save_ugc_handled_result(ugc)


def _get_gate(bid: str) -> dict:
    """
    获取大门
    """
    sql = (f"select bid, road_relation, parent_id, name, st_astext(gcj_geom) as geom "
           f"from park_online_data where bid='{bid}';")
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if res is None or not res:
        return {}
    return {
        'bid': res[0],
        'road_relation': res[1],
        'parent_id': res[2],
        'name': res[3],
        'geom': res[4],
    }


def _parent_id_is_effected(parent_id: str) -> bool:
    """
    父点是否有效
    """
    return parent_id not in ['', '0']


def _get_aoi_wkt(point_wkt: str) -> str:
    """
    获取包含停车场的所有 aoi bid
    """
    qry = (f"select st_astext(geom) geom from blu_face where st_intersects(geom, st_geomfromtext('{point_wkt}', 4326)) "
           f"and aoi_level = 2")
    res = dbutils.fetch_one(pgsql.BACK_CONFIG, qry)
    if res is None or not res:
        return ''
    return res[0]


def _get_park_by_wkt_and_name(maybe_name: str, geom: str) -> dict:
    """
    根据名称，范围获取停车场
    """
    if geom == '':
        return {}

    qry = (f"select bid , st_astext(gcj_geom) as park_geom from park_online_data "
                     f"where name like '{handle_apostrophe(maybe_name)}%' "
                     f"and std_tag in ('交通设施;停车场', '交通设施;路侧停车位') "
                     f"and st_intersects(gcj_geom, st_geomfromtext('{geom}', 4326))")
    res = dbutils.fetch_one(pgsql.POI_CONFIG, qry)
    if res is None or not res:
        return {}
    return {
        'bid': res[0],
    }


def _push_park_gate(park: dict, ref_qb_id: str, gates: list) -> int:
    """
    推送停车场
    """
    tips = "核实出入口，保障这个停车场至少有一个入口或者出入口。注意，若停车场冗余或者不存在或者暂停营业，则修改停车场状态即可，无需补充出入口。"
    park_geom = park['geom']
    parent_id = park['parent_id']
    item = {
        'park': {
            'bid': park['bid'],
            'name': park['name'],
            'wkt': park_geom,
            'parent_bid': parent_id,
            'show_tag': ''
        },
        'park_gate_list': gates,
    }

    batch_id = 'TCQUGC2024120402'
    if len(gates) > 0:
        batch_id = 'TCQUGC2024120303'

    return push(batch_id, park['bid'], park_geom, [item, ], tips, 2, ref_qb_id)


def _push_gate(gate: dict, ref_qb_id: str) -> int:
    """
    推送出入口新增情报
    """
    tips = "核实出入口是否存在，存在则关联对应父点，父点不存在则新增。注意，若出入口状态异常，则只需要修改出入口状态即可。"
    gate_geom = gate['geom']
    item = {
        'park': {
            'bid': '',
            'name': '',
            'wkt': f'{gate_geom}',
            'parent_bid': '',
            'show_tag': ''
        },
        'park_gate_list': [
            {
                'bid': gate['bid'],
                'name': gate['name'],
                'status': 1,
                'wkt': '',
            }
        ]
    }
    return push('TCQUGC2024120301', gate['bid'], gate_geom, [item, ], tips, 1, ref_qb_id)


def _save_ugc_handled_result(ugc: UgcInfo):
    """
    保存 ugc 处理结果
    """
    qry = f"update park_ugc_task set status = {ugc.status}, remark = '{ugc.remark}' where id = {ugc.id}"
    bf_query = BFQuery()
    bf_query.execute(qry)
    # print(qry)  # todo test


def _handle_ugc_park_gate(ugc: UgcInfo) -> Tuple[UgcInfo, bool]:
    """
    处理停车场的出入口
    """
    park_bid = ugc.bid
    print(f"处理停车场bid={park_bid}")
    park_info = _get_park(park_bid)
    if not park_info:
        ugc.remark = 'park_online_data 库无数据，暂不处理'
        return ugc, False

    precise = park_info['precise']
    parent_id = park_info['parent_id']
    if precise == 1 and parent_id:
        ugc.remark = '已经是精准停车场且有父点的'
        ugc.status = UgcStatus.DONE
        return ugc, True

    if _park_has_gate(ugc.bid):
        ugc.remark = '停车场有出入口不处理'
        ugc.status = UgcStatus.DONE
        return ugc, True

    name = park_info['name']
    new_name = name.split('-')[0]
    new_name = str(new_name).replace('停车场', '')
    _aoi_wkt = _get_aoi_wkt(park_info['geom'])
    gates = _get_gates_by_wkt_and_name(new_name, _aoi_wkt)
    qb_id = _push_park_gate(park_info, ugc.gen_ref_qb_id(), gates)

    if qb_id > 0:
        ugc.remark = qb_id
        ugc.status = UgcStatus.DONE
    else:
        ugc.remark = "情报投放失败"
        ugc.status = UgcStatus.ERROR
    return ugc, True


def _handle_ugc_gate(ugc: UgcInfo) -> Tuple[UgcInfo, bool]:
    """
    处理 ugc 出入口
    """
    gate_bid = ugc.bid
    gate_info = _get_gate(gate_bid)
    if len(gate_info) == 0:
        ugc.remark = 'poi 库无数据，暂不处理'
        return ugc, False

    is_precise = False
    road_relation = gate_info['road_relation']
    if road_relation and _parent_id_is_effected(gate_info['parent_id']):
        is_precise = _is_precise_gate(road_relation)
    if is_precise:
        ugc.remark = '已经是精准出入口且有父点的不用下发'
        ugc.status = UgcStatus.DONE
        return ugc, True

    aoi_wkt = _get_aoi_wkt(gate_info['geom'])
    new_name = gate_info['name'].split('-')[0]
    park = _get_park_by_wkt_and_name(new_name, aoi_wkt)
    if not park:
        qb_id = _push_gate(gate_info, ugc.gen_ref_qb_id())
        if qb_id > 0:
            ugc.remark = qb_id
            ugc.status = UgcStatus.DONE
        else:
            ugc.remark = "情报投放失败"
            ugc.status = UgcStatus.ERROR
        return ugc, True
    return ugc, False


def processing_gate(ugc_infos: List[UgcInfo]):
    """
    处理出入口数据
    :return
    """
    for ugc in tqdm(ugc_infos):
        ugc, handled = _handle_ugc_gate(ugc)
        _save_ugc_handled_result(ugc)


def _is_precise_gate(road_relation: dict):
    """
    是否是精准出入口，是返回 True
    """
    link_infos = road_relation['link_info']
    for link_info in link_infos:
        link_info_type = link_info.get('type', '')
        link_id = link_info.get('link_id', '')
        node_id = link_info.get('node_id', '')
        if str(link_info_type) == '1' and link_id.isdigit() and node_id.isdigit():
            return True
        elif str(link_info_type) == '2' and link_id.isdigit() and not node_id:
            return True
    return False


def push(ref_qb_batch_id, _main_bid, park_wkt, _pois, tips, qb_type, _ref_qb_id) -> int:
    """
    情报推送
    """
    data = {
        "src": 12,  # 必填，9:停车场, 10:停车场出入口
        "ref_qb_id": _ref_qb_id,  # 原始情报id， 如果不传默认uuid，最好自己生成一个便于去重，各来源唯一
        "ref_qb_batch_id": ref_qb_batch_id,  # 必填，批次号
        "main_poi_bid": _main_bid,  # 停车场主点bid，注意：不传就代表是新增停车场数据，传了就是更新停车场数据，如果是更新场景，bid需要保证线上有效，否则报错
        "mark_geom": park_wkt,  # 必填 停车场坐标
        "work_type": 2,  # 必填，1自动 2人工
        "qb_type": qb_type,  # 0:新增-出入口, 1:新增, 2:出入口
        "from_src": 'ugc',
        "extra": {
            "detail": {  # 非公共参数全部放这里
                "project": "TCQ",  # 必填
                "priority": 10,  # 必填
                "callback": "http://mapde-poi.baidu-int.com/prod/parking/manualCallback",  # 必填
                "online_source": "chuilei_scope.rg",  # 必填
                "creator": "<EMAIL>",  # 必填
                "tips": tips,
                "park_list": _pois
            },
        }
    }
    print(data)

    def _push() -> int:
        """
        推送
        """
        req = requests.post(
            url='http://mapde-poi.baidu-int.com/prod/integration/qbSyncV2',
            json=data
        )
        print(_main_bid, req.json())
        """
            {'code': 0, 'msg': '', 'data': {'qb_id': 11825219}}
            """
        res = req.json()
        if 'data' in res and 'qb_id' in res['data'] and res['data']['qb_id'] > 0:
            return res['data']['qb_id']
        return 0

    def _push_test():
        return 1

    res = _push()
    # res = _push_test()  # todo test

    if res > 0:
        global batch_id2num
        batch_id2num[ref_qb_batch_id] = batch_id2num.setdefault(ref_qb_batch_id, 0) + 1
    return res


def _send_hi():
    """
    通知
    """
    if len(batch_id2num) == 0:
        return

    text = f"ugc 渠道：{batch_id2num}"
    # print(text)
    notice_tool.send_hi(text, token='d399e15bdee106761428d56441394ee5d')


def main():
    """
    主函数
    """
    ugc_parks, ugc_gates = get_infos()
    # print(len(ugc_gates))
    # print(len(ugc_parks))
    # exit()
    processing_gate(ugc_gates)
    processing_park(ugc_parks)
    _send_hi()


if __name__ == '__main__':
    main()




