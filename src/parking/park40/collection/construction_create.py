"""
建设集合构建
"""
import tqdm
import datetime

from dataclasses import dataclass
from multiprocessing import Pool
from typing import Optional
from src.parking.park40.common import query
from src.parking.park40.common.monitor import setup_logger, send_hi


logger = setup_logger()  # 设置 logger


def run():
    """
    run
    """
    count = query.count_park_precise_statistical_data()
    if count == 0:
        logger.info("count_park_precise_statistical_data 0")
        return

    query.create_park_40_construction_2025Q3_first()
    logger.info("create_park_40_construction_2025Q3_first done")

    query.create_park_40_construction_2025Q3_second()
    logger.info("create_park_40_construction_2025Q3_second done")

    logger.info("start monitor")
    monitor()
    logger.info("end monitor")


def monitor():
    """
    监控
    """
    count1 = query.count_park_40_construction_2025Q3_first()
    count2 = query.count_park_40_construction_2025Q3_second()
    print(
        f"""
        4.0建设集合一：{count1} 条，4.0建设集合二：{count2} 条：
        ------------------------------------------------------------------
        数据更新时间：{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        """
    )


if __name__ == '__main__':
    """
    main
    """
    run()


