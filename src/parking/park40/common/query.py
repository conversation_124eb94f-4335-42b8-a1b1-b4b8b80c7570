"""
数据交互层
"""
from src.parking.recognition import dbutils
from src.tools import pgsql


def get_precise20_num():
    """
    获取精准2.0的量
    """
    sql = """
        select count(distinct bid) 
        from park_precise_statistical_data 
        where precise20=1 
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_has_ld_num():
    """
    获取LD的量
    """
    sql = """
        select count(distinct bid) 
        from park_precise_statistical_data 
        where has_ld=1 
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_has_chu_num():
    """
    获取出口的量
    """
    sql = """
        select count(distinct bid)  
        from park_precise_statistical_data 
        where has_chu=1 
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_has_inner_road_num():
    """
    获取内部路的量
    """
    sql = """
        select count(distinct bid)  
        from park_precise_statistical_data 
        where has_inner_road=1 
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_has_ld_num_with_20():
    """
    获取2.0 LD的量
    """
    sql = """
        select count(distinct bid) 
        from park_precise_statistical_data 
        where has_ld=1 and precise20=1
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_has_chu_num_with_20():
    """
    获取2.0 出口的量
    """
    sql = """
        select count(distinct bid) 
        from park_precise_statistical_data 
        where has_chu=1 and precise20=1
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_has_inner_road_num_with_20():
    """
    获取2.0 内部路的量
    """
    sql = """
        select count(distinct bid) 
        from park_precise_statistical_data 
        where has_inner_road=1 and precise20=1
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_has_model_num():
    """
    获取模型的量
    """
    sql = """
        select count(distinct p1.bid)
        from parking p1
        where exists (
            select 1
            from parking p2
            where p2.parent_id = p1.bid
        ) and 
        not exists (
            select 1
            from parking p2,
               LATERAL jsonb_array_elements(p2.road_relation::jsonb -> 'link_info') AS elem
            where p2.parent_id = p1.bid
            and (elem ->> 'orientation')::int = 1 
            and gate_x IS NOT NULL
            and gate_y IS NOT NULL
            and in_link IS NOT NULL
            and link_orientation IS NOT NULL
        );
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_has_model_parks():
    """
    获取有模型的停车场集合
    """
    sql = """
        select distinct p1.bid
        from parking p1
        where not exist (
          select 1
          from parking p2,
               LATERAL jsonb_array_elements(p2.road_relation::jsonb -> 'link_info') AS elem
            where p2.parent_id = p1.bid
            and (elem ->> 'orientation')::int = 1 
            and gate_x IS NOT NULL
            and gate_y IS NOT NULL
            and in_link IS NOT NULL
            and link_orientation IS NOT NULL
        );
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    return ret


def is_precise20_park(bid):
    """
    判断是否是2.0停车场
    """
    sql = """
        select *
        from park_precise_statistical_data 
        where precise20=1 and bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid, ))
    if ret is None:
        return False
    return True


def create_stat_park40_indicator_first_level():
    """
    一级指标：4.0规模
    """
    sql = """
        insert into stat_park40_indicator_first_level (park40_num)
        select count(*)::float
        from park_precise_statistical_data
        where precise40 = 1;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()


def create_stat_park40_indicator_second_level(values):
    """
    一级指标：4.0规模
    """
    sql = """
        insert into stat_park40_indicator_second_level 
        (
             park20_num, 
             ld_num, 
             inner_road_num, 
             model_num, 
             park20_ld_num,
             park20_inner_road_num, 
             park20_model_num
        )
        values(%s, %s, %s, %s, %s, %s, %s)
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql, values)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()


def truncate_stat_tables():
    """
    清表数据
    """
    sql = """
        truncate table stat_park40_indicator_first_level;
        truncate table stat_park40_indicator_second_level;
        truncate table stat_park40_indicator_process_funnel;
        truncate table stat_park40_indicator_process_fast_increase;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()


def get_20_not_40_only_lack_exit_parks():
    """
    获取2.0非4.0就差出口停车场集合
    """
    sql = """
          select bid
          from park_precise_statistical_data
          where precise20=1
          and precise40=0 
          and has_inner_road=1 
          and has_chu=0 
          and has_ld=1
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return ret


def get_park_exit(bid):
    """
    获取停车场出口
    """
    sql = """
          select road_relation
          from park_online_data
          where parent_id = %s and name like '%出口%'
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (bid, ))
    return ret


def create_park_40_construction_2025Q3_first():
    """
    构建4.0第一建设集合
    地上停车场 中 精准2.0非精准4.0的集合
    """
    sql = """
        DROP TABLE park_40_construction_2025Q3_first;
        CREATE TABLE park_40_construction_2025Q3_first (LIKE park_precise_statistical_data INCLUDING ALL);
        INSERT INTO park_40_construction_2025Q3_first
        SELECT *
        FROM park_precise_statistical_data
        WHERE show_tag = '地上停车场'
          AND precise40 = 0
          AND precise20 = 1;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()


def create_park_40_construction_2025Q3_second():
    """
    构建4.0第二建设集合
    地上停车场 中 非精准2.0，根父点月导航pv≥20(不包括无父点)
    """
    sql = """
        DROP TABLE park_40_construction_2025Q3_second;
        CREATE TABLE park_40_construction_2025Q3_second (LIKE park_precise_statistical_data INCLUDING ALL);
        INSERT INTO park_40_construction_2025Q3_second
        SELECT *
        FROM park_precise_statistical_data
        WHERE show_tag = '地上停车场'
          AND root_nav_pv >= 20
          AND precise20 = 0;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()


def count_park_precise_statistical_data():
    """
    计算精准集合数量
    """
    sql = """
        select count(distinct bid)
        from park_precise_statistical_data
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def count_park_40_construction_2025Q3_first():
    """
    计算2025Q3停车场4.0建设集合一数量
    """
    sql = """
        select count(distinct bid)
        from park_40_construction_2025Q3_first
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def count_park_40_construction_2025Q3_second():
    """
    计算2025Q3停车场4.0建设集合二数量
    """
    sql = """
        select count(distinct bid)
        from park_40_construction_2025Q3_second
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def get_park_precise(bid):
    """
    获取停车场精准化
    """
    sql = """
        select precise20, precise40, has_chu, has_ld, has_inner_road
        from park_precise_statistical_data 
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    columns = [
        "precise20",
        "precise40",
        "has_chu",
        "has_ld",
        "has_inner_road",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def is_model_park(bid):
    """
    判断是否具备出入口模型的停车场
    """
    sql = """
        select count(distinct p1.bid)
        from parking p1
        where exists (
            select 1
            from parking p2
            where p2.parent_id = p1.bid
        ) and 
        p1.bid = %s and not exists (
            select 1
            from parking p2,
               LATERAL jsonb_array_elements(p2.road_relation::jsonb -> 'link_info') AS elem
            where p2.parent_id = p1.bid
            and (elem ->> 'orientation')::int = 1 
            and gate_x IS NOT NULL
            and gate_y IS NOT NULL
            and in_link IS NOT NULL
            and link_orientation IS NOT NULL
        );
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, (bid, ))
    if ret is None:
        return 0
    return ret[0]


def get_parks_by_show_tag(show_tag):
    """
    根据show_tag获取停车场
    """
    sql = """
        select distincet bid
        from park_online_data
        where show_tag = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (show_tag, ))
    return ret


def get_parks_by_show_tag_with_40_construction(show_tag):
    """
    根据show_tag获取4.0建设集合停车场
    """
    sql = """
        select distinct bid
        from (
            select bid
            from park_40_construction_2025q3_first
            where show_tag = %s
        
            union
        
            select bid
            from park_40_construction_2025q3_second
            where show_tag = %s
        ) as combined;
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (show_tag, show_tag, ))
    return ret


def get_parks_by_show_tag_with_20(show_tag):
    """
    根据show_tag获取2.0停车场
    """
    sql = """
        select distincet bid
        from park_online_data a inner join park_statistical_data b
        on a.bid = b.bid
        where a.show_tag = %s and b.precise20=1
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (show_tag, ))
    return ret


def get_parks_by_show_tag_with_20_and_construction(show_tag):
    """
    根据show_tag获取建设集合2.0停车场
    """
    sql = """
        select distinct bid
        from (
            select bid
            from park_40_construction_2025q3_first
            where show_tag = %s and precise20=1
        
            union
        
            select bid
            from park_40_construction_2025q3_second
            where show_tag = %s and precise20=1
        ) as combined;
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (show_tag, ))
    return ret


def create_stat_park40_indicator_process_funnel(values):
    """
    2.0漏斗监控表
    """
    sql = """
        insert into stat_park40_indicator_process_funnel 
        (
            all_dishang_num, 
            all_park20_dishang_num, 
            all_menqian_num, 
            all_luce_num,
            construction_dishang_num, 
            construction_park20_dishang_num, 
            construction_menqian_num, 
            construction_luce_num
        )
        values(%s, %s, %s, %s, %s, %s, %s, %s)
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql, values)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()


def create_stat_park40_indicator_process_fast_increase(values):
    """
    2.0漏斗监控表
    """
    sql = """
        insert into stat_park40_indicator_process_fast_increase 
        (
            all_dishang_lack_20_num, 
            all_dishang_lack_exit_num, 
            all_dishang_lack_inner_road_num, 
            all_dishang_lack_ld_num,
            all_dishang_lack_model_num, 
            all_menqian_lack_ld_num, 
            all_menqian_lack_model_num, 
            construction_dishang_lack_20_num,
            construction_dishang_lack_exit_num,
            construction_dishang_lack_inner_road_num,
            construction_dishang_lack_ld_num,
            construction_dishang_lack_model_num,
            construction_menqian_lack_ld_num,
            construction_menqian_lack_model_num
        )
        values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql, values)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()





