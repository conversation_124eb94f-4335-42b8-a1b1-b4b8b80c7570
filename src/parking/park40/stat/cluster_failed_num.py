"""
统计出口聚合丢失问题(2.0非4.0就差出口，出口还因为聚合丢了)
"""
import tqdm
import datetime

from dataclasses import dataclass
from multiprocessing import Pool
from typing import Optional
from src.parking.park40.common import query
from src.parking.park40.common.monitor import setup_logger


logger = setup_logger()  # 设置 logger


def run():
    """
    run
    """
    parks = query.get_20_not_40_only_lack_exit_parks()
    logger.info("get_20_not_40_only_lack_exit_parks done")

    parks = get_failed_cluster_exit_parks(parks)
    logger.info(f"get_failed_cluster_exit_parks done, {len(parks)}")


def get_failed_cluster_exit_parks(parks):
    """
    获取出口因为聚合丢了的停车场集合
    """
    res = []
    for park_bid in tqdm.tqdm(parks):
        park_exits = query.get_park_exit(park_bid)
        if len(park_exits) > 0:
            res.append(park_bid)
    return res


if __name__ == "__main__":
    """
    main
    """
    run()
