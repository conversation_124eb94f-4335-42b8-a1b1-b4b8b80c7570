"""
停车场4.0指标体系
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Fr8QITM0sh/YZCUDtktsX/PSWhj51FOFEeK0
"""
import tqdm
import datetime

from src.parking.park40.common import query
from src.parking.park40.common.monitor import setup_logger

logger = setup_logger()  # 设置 logger


def run():
    """
    run
    """
    query.truncate_stat_tables()
    logger.info("truncate_stat_tables done")

    create_stat_park40_indicator_first_level()
    logger.info("create_stat_park40_indicator_first_level done")

    create_stat_park40_indicator_second_level()
    logger.info("create_stat_park40_indicator_second_level done")


def create_stat_park40_indicator_first_level():
    """
    一级指标：4.0规模
    """
    query.create_stat_park40_indicator_first_level()


def create_stat_park40_indicator_second_level():
    """
    二级指标
    """
    precise20_num = query.get_precise20_num()
    has_ld_num = query.get_has_ld_num()
    has_chu_num = query.get_has_chu_num()
    has_inner_road_num = query.get_has_inner_road_num()
    has_model_num = query.get_has_model_num()

    has_ld_num_with_20 = query.get_has_ld_num_with_20()
    has_chu_num_with_20 = query.get_has_chu_num_with_20()
    has_inner_road_num_with_20 = query.get_has_inner_road_num_with_20()
    has_model_parks = query.get_has_model_parks()
    has_model_parks_num_with_20 = 0
    for park_bid in has_model_parks:
        if query.is_precise20_park(park_bid):
            has_model_parks_num_with_20 += 1

    query.create_stat_park40_indicator_second_level(
        (
            precise20_num,
            has_ld_num,
            has_inner_road_num,
            has_chu_num,
            has_model_num,
            has_ld_num_with_20,
            has_inner_road_num_with_20,
            has_chu_num_with_20,
            has_model_parks_num_with_20,
        )
    )


def create_stat_park40_indicator_process_funnel():
    """
    过程指标
    """
    # 2.0漏斗
    all_dishang_parks = query.get_parks_by_show_tag("地上停车场")
    all_20_dishang_parks = query.get_parks_by_show_tag_with_20("地上停车场")
    all_menqian_parks = query.get_parks_by_show_tag("门前停车场")
    all_luce_parks = query.get_parks_by_show_tag("路侧停车场")

    js_dishang_parks = query.get_parks_by_show_tag_with_40_construction("地上停车场")
    js_20_dishang_parks = query.get_parks_by_show_tag_with_20_and_construction("地上停车场")
    js_menqian_parks = query.get_parks_by_show_tag_with_40_construction("门前停车场")
    js_luce_parks = query.get_parks_by_show_tag_with_40_construction("路侧停车场")
    query.create_stat_park40_indicator_process_funnel(
        (
            all_dishang_parks,
            all_20_dishang_parks,
            all_menqian_parks,
            all_luce_parks,
            js_dishang_parks,
            js_20_dishang_parks,
            js_menqian_parks,
            js_luce_parks,
        )
    )

    # 4.0快速上量
    all_dishang_lack_info = get_lack_info(all_dishang_parks)
    all_dishang_lack_20_num = all_dishang_lack_info["lack_20_num"]
    all_dishang_lack_exit_num = all_dishang_lack_info["lack_exit_num"]
    all_dishang_lack_inner_road_num = all_dishang_lack_info["lack_inner_road_num"]
    all_dishang_lack_ld_num = all_dishang_lack_info["lack_ld_num"]
    all_dishang_lack_model_num = all_dishang_lack_info["lack_model_num"]

    all_menqian_lack_info = get_lack_info(all_menqian_parks)
    all_menqian_lack_ld_num = all_menqian_lack_info["lack_ld_num"]
    all_menqian_lack_model_num = all_menqian_lack_info["lack_model_num"]

    js_dishang_lack_info = get_lack_info(js_dishang_parks)
    js_dishang_lack_20_num = js_dishang_lack_info["lack_20_num"]
    js_dishang_lack_exit_num = js_dishang_lack_info["lack_exit_num"]
    js_dishang_lack_inner_road_num = js_dishang_lack_info["lack_inner_road_num"]
    js_dishang_lack_ld_num = js_dishang_lack_info["lack_ld_num"]
    js_dishang_lack_model_num = js_dishang_lack_info["lack_model_num"]

    js_menqian_lack_info = get_lack_info(js_menqian_parks)
    js_menqian_lack_ld_num = js_menqian_lack_info["lack_ld_num"]
    js_menqian_lack_model_num = js_menqian_lack_info["lack_model_num"]

    query.create_stat_park40_indicator_process_fast_increase(
        (
            all_dishang_lack_20_num,
            all_dishang_lack_exit_num,
            all_dishang_lack_inner_road_num,
            all_dishang_lack_ld_num,
            all_dishang_lack_model_num,
            all_menqian_lack_ld_num,
            all_menqian_lack_model_num,
            js_dishang_lack_20_num,
            js_dishang_lack_exit_num,
            js_dishang_lack_inner_road_num,
            js_dishang_lack_ld_num,
            js_dishang_lack_model_num,
            js_menqian_lack_ld_num,
            js_menqian_lack_model_num,
        )
    )


def get_lack_info(parks):
    """
    获取4.0仅缺要素信息
    """
    lack_20_num = 0
    lack_exit_num = 0
    lack_inner_road_num = 0
    lack_ld_num = 0
    lack_model_num = 0
    for bid in parks:
        park_precise_res = query.get_park_precise(bid)
        is_model_park = query.is_model_park(bid)
        if len(park_precise_res) == 0:
            continue
        park_precise = park_precise_res[0]
        precise20, precise40, has_chu, has_ld, has_inner_road = (
            park_precise["precise20"],
            park_precise["precise40"],
            park_precise["has_chu"],
            park_precise["has_ld"],
            park_precise["has_inner_road"],
        )
        if precise40 == 1:
            continue
        if precise20 == 0 and has_chu == 1 and has_ld == 1 and has_inner_road == 1 and is_model_park:
            lack_20_num += 1
        elif precise20 == 1 and has_chu == 0 and has_ld == 1 and has_inner_road == 1 and is_model_park:
            lack_exit_num += 1
        elif precise20 == 1 and has_chu == 1 and has_ld == 1 and has_inner_road == 0 and is_model_park:
            lack_inner_road_num += 1
        elif precise20 == 1 and has_chu == 1 and has_ld == 1 and has_inner_road == 1 and not is_model_park:
            lack_model_num += 1
        elif precise20 == 1 and has_chu == 1 and has_ld == 0 and has_inner_road == 1 and is_model_park:
            lack_ld_num += 1
    return {
        "lack_20_num": lack_20_num,
        "lack_exit_num": lack_exit_num,
        "lack_inner_road_num": lack_inner_road_num,
        "lack_model_num": lack_model_num,
        "lack_ld_num": lack_ld_num,
    }


if __name__ == "__main__":
    """
    run
    """
    run()
