"""
【R6013】停车场只有出口没有入口
"""

import json
import tqdm

from src.parking.qc.common import query


def run():
    """
    run
    """
    park_list = query.get_all_parks_list()
    risk_list = check_park_list(park_list)
    save_result(risk_list)


def check_park_list(park_list):
    """
    质检停车场
    """
    result = []
    for park in tqdm.tqdm(park_list):
        park_bid, name, road_relation, important_bid = (
            park["bid"],
            park["name"],
            park["road_relation"],
            park["important_bid"],
        )
        is_51_important = False
        if important_bid:
            is_51_important = True

        crk_list = query.get_crk_list(park_bid)
        crk_num = len([x for x in crk_list if x[0] in ["停车场入口", "停车场出入口"]])
        ck_num = len([x for x in crk_list if x[0] in ["停车场出口"]])
        if ck_num > 0 and crk_num == 0:
            result.append(
                {
                    "reason": "停车场只有出口没有入口",
                    "name": name,
                    "road_relation": json.dumps(road_relation),
                    "crk_bid": park["bid"],
                    "std_tag": park["std_tag"],
                    "show_tag": park["show_tag"],
                    "precise10": park["precise10"],
                    "precise20": park["precise20"],
                    "park_click_pv": park["click_pv"],
                    "is_51_important": is_51_important,
                }
            )
    return result


def save_result(result):
    """
    保存结果到文件
    """
    with open("./R6013.tsv", "w") as file:
        # 设置文件头
        header = "bid\troad_relation\tname\tstd_tag\tshow_tag\t是否精准1.0\t是否精准2.0\t停车场click_pv\t是否51重保\t原因\n"
        file.write(header)

        # 遍历结果并格式化写入
        for item in result:
            strs = (
                f"{item['crk_bid']}\t"
                f"{item['road_relation']}\t"
                f"{item['name']}\t"
                f"{item['std_tag']}\t"
                f"{item['show_tag']}\t"
                f"{item['precise10']}\t"
                f"{item['precise20']}\t"
                f"{item['park_click_pv']}\t"
                f"{item['is_51_important']}\t"
                f"{item['reason']}\n"
            )
            file.write(strs)


if __name__ == "__main__":
    """
    main
    """
    run()
