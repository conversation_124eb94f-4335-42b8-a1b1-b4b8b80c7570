"""
* 地下停车场出入口，gate.x,gate.y构成的点buffer5m，判断是否压盖LD边界线和路口面。是则输出停车场bid、出入口bid
* 地上停车场出入口，gate.x,gate.y构成的点buffer3m，判断是否压盖LD边界线和路口面。是则输出停车场bid、出入口bid
* 停车场(非门前)装饰线(central_line)，buffer3m，判断是否压盖LD边界线和路口面。是则输出停车场bid
* 统计：
   1）压盖LD边界线和路口面的出入口数量 / 全部存在gate.x,gate.y出入口数量
   2）停车场(非门前)装饰线压盖LD边界线和路口面数量 / 全部存在装饰线停车场数量
"""
from pg_tool import PgTool
from tqdm import tqdm
import shapely.wkt
from coord_trans import mc_to_ll_wkt, bd09_to_gcj02_wkt, mc_to_ll, bd09_to_gcj02
from shapely.wkt import loads, dumps
from shapely.geometry import MultiLineString, LineString


def run():
    """
    run
    """
    with PgTool() as pg:
        # 装饰线压盖
        central_line_cover_result = central_line_cover(pg)
        save_result_central_line_cover(central_line_cover_result)

        # 入口模型压盖
        gate_cover_result = gate_cover(pg)
        save_result_gate_cover(gate_cover_result)


def gate_cover(pg):
    """
    入口模型压盖LD
    """
    result = []
    sql = f""" 
        select bid, gate_x, gate_y, parent_id
        from park_online_data 
        where std_tag in ('出入口;停车场出入口') 
        and gate_x != 0 
        and gate_y != 0 
    """
    pg.cursor_poi.execute(sql)
    res = pg.cursor_poi.fetchall()
    for item in tqdm(res):
        bid, gate_x, gate_y, parent_id = item
        point_mc_wkt = f"POINT({gate_x} {gate_y})"
        point_wkt = bd09_to_gcj02_wkt(mc_to_ll_wkt(point_mc_wkt))
        show_tag = get_park_show_tag(pg, parent_id)
        p_buf_wkt = ""
        if show_tag == "地上停车场":
            p_buf_wkt = shapely.wkt.loads(point_wkt).buffer(2.8 / 110000).wkt
        elif show_tag == "地下停车场":
            p_buf_wkt = shapely.wkt.loads(point_wkt).buffer(4.8 / 110000).wkt
        if p_buf_wkt != "" and (is_area_cover_ld(pg, p_buf_wkt) or is_cover_road_pg(pg, p_buf_wkt)):
            result.append((bid, p_buf_wkt, show_tag, "模型压盖"))
    return result


def central_line_cover(pg):
    """
    计算装饰线压盖LD
    """
    result = []
    sql = f""" 
        select bid, st_astext(central_line), show_tag
        from park_online_data 
        where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
        and central_line is not null
    """
    pg.cursor_poi.execute(sql)
    res = pg.cursor_poi.fetchall()
    for item in tqdm(res):
        bid, central_line, show_tag = item
        if "EMPTY" in central_line:
            continue
        central_line_buf_wkt = shapely.wkt.loads(multiline_mc_to_gcj02_wkt(central_line)).buffer(3 / 110000).wkt
        if central_line_buf_wkt and (
            is_area_cover_ld(pg, central_line_buf_wkt) or is_cover_road_pg(pg, central_line_buf_wkt)
        ):
            result.append((bid, central_line_buf_wkt, show_tag, "装饰线压盖"))
    return result


def multiline_mc_to_gcj02_wkt(wkt_str):
    """
    多线墨卡托转国测02
    """
    geom = loads(wkt_str)

    # 统一转换成列表方便处理
    if isinstance(geom, LineString):
        lines = [geom]
    elif isinstance(geom, MultiLineString):
        lines = list(geom.geoms)
    else:
        raise TypeError("输入必须是 LineString 或 MultiLineString 类型的 WKT")

    converted_lines = []

    for line in lines:
        coords = []
        for x, y in line.coords:
            lon_bd, lat_bd = mc_to_ll([x, y])
            lon_gcj, lat_gcj = bd09_to_gcj02(lon_bd, lat_bd)
            coords.append((lon_gcj, lat_gcj))
        converted_lines.append(LineString(coords))

    return MultiLineString(converted_lines).wkt


def save_result_gate_cover(mining_result):
    """
    保存入口模型压盖数据
    """
    file = open("./save_result_gate_cover", "w")
    title = f"bid\t" f"show_tag\t" f"memo\t\n"
    file.write(title)
    for item in mining_result:
        strs = f"'{item[0]}'\t" f"'{item[2]}'\t" f"'{item[3]}'\t\n"
        file.write(strs)
    file.close()


def save_result_central_line_cover(mining_result):
    """
    保存装饰线压盖数据
    """
    file = open("./save_result_central_line_cover", "w")
    title = f"bid\t" f"show_tag\t" f"memo\t\n"
    file.write(title)
    for item in mining_result:
        strs = f"'{item[0]}'\t" f"'{item[2]}'\t" f"'{item[3]}'\t\n"
        file.write(strs)
    file.close()


def get_park_show_tag(pg, bid):
    """
    根据show_tag获取停车场
    """
    sql = f"""
        select show_tag 
        from park_online_data 
        where bid = '{bid}' and park_spec = 2
    """
    pg.cursor_poi.execute(sql)
    res = pg.cursor_poi.fetchone()
    if res is None:
        return None
    return res[0]


def is_area_cover_ld(pg, poly_wkt):
    """
    是否压盖LD
    """
    sql = f"""
        SELECT *
        FROM NAV_LANE_BOUNDARY
        WHERE ST_Intersects(ST_GeomFromText('{poly_wkt}', 4326), geom)
    """
    pg.cursor_road.execute(sql)
    res = pg.cursor_road.fetchall()
    return len(res) > 0


def is_cover_road_pg(pg, poly_wkt):
    """
    判断是否压盖道路面
    """
    sql = f"""
        select *
        from NAV_LANE_ROAD_PG
        where ST_Intersects(ST_Force3D(ST_GeomFromText('{poly_wkt}', 4326)), geom)
    """
    pg.cursor_road.execute(sql)
    res = pg.cursor_road.fetchall()
    return len(res) > 0


if __name__ == "__main__":
    """
    main
    """
    run()
