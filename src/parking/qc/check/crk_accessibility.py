"""
检查停车场关联的出入口(关联node场景)数据准确
"""
import json
import shapely.wkt
import tqdm

from enum import Enum
from shapely import Point
from src.parking.qc.common import query


class PassType(Enum):
    """
    大门类型
    """

    ENTRANCE = "入口"
    EXIT = "出口"
    EMERGENCY = "紧急"
    ENTRANCE_EXIT = "出入口"
    UNKNOWN = "未知"


def run():
    """
    run
    """
    crk_list = query.get_all_crk_list()
    risk_list = check_crk_list(crk_list)
    save_result(risk_list)


def check_crk_list(crk_list):
    """
    出入口质检
    """
    result = []
    for crk in tqdm.tqdm(crk_list):
        name, road_relation, important_bid = crk["name"], crk["road_relation"], crk["important_bid"]
        is_51_important = False
        if important_bid:
            is_51_important = True

        check_flag1, name_type1, gate_type = check_crk_accessibility_by_gate(crk)
        if not check_flag1:
            result.append(
                {
                    "reason": "出入口名称通行性和关联的node对应的gate通行性不一致",
                    "name": name,
                    "road_relation": json.dumps(road_relation),
                    "crk_bid": crk["bid"],
                    "std_tag": crk["std_tag"],
                    "show_tag": crk["show_tag"],
                    "precise10": crk["precise10"],
                    "precise20": crk["precise20"],
                    "park_click_pv": crk["click_pv"],
                    "parking_show_tag": crk["parking_show_tag"],
                    "is_51_important": is_51_important,
                }
            )

        check_flag2, name_type2, link_type = check_crk_accessibility_by_link(crk)
        if not check_flag2:
            result.append(
                {
                    "reason": "出入口名称通行性和关联的link对应的link通行性不一致",
                    "name": name,
                    "road_relation": json.dumps(road_relation),
                    "crk_bid": crk["bid"],
                    "std_tag": crk["std_tag"],
                    "show_tag": crk["show_tag"],
                    "precise10": crk["precise10"],
                    "precise20": crk["precise20"],
                    "park_click_pv": crk["click_pv"],
                    "parking_show_tag": crk["parking_show_tag"],
                    "is_51_important": is_51_important,
                }
            )
    return result


def check_crk_accessibility_by_gate(crk):
    """
    检查出入口和关联的大门通行性一致性
    * 停车场是入口，大门是出口
    * 停车场是入口，大门是出入口
    * 停车场是入口，大门是紧急门
    * 停车场是出口，大门是入口
    * 停车场是出口，大门是出入口
    * 停车场是出口，大门是紧急门
    * 停车场是出入口，大门是出口
    * 停车场是出入口，大门是入口
    * 停车场是出入口，大门是紧急门
    """
    name, road_relation = crk["name"], crk["road_relation"]
    crk_type = get_crk_type_by_name(name)
    node_ids = get_node_ids(road_relation)
    for node_id in node_ids:
        gate_type = get_gate_type(node_id)
        if gate_type == PassType.UNKNOWN:
            # 大门类型未知，不处理
            continue

        if crk_type != gate_type:
            # 大门类型和出入口类型不一致，质检失败
            return False, crk_type, gate_type
    # 大门类型和出入口类型一致，质检成功
    return True, crk_type, crk_type


def check_crk_accessibility_by_link(crk):
    """
    检查出入口和关联的LINK一致性(只质检link是单向通行的场景)
    * 停车场是入口，link是出口
    * 停车场是出口，link是入口
    """
    name, road_relation = crk["name"], crk["road_relation"]
    crk_type = get_crk_type_by_name(name)
    link_ids = get_link_ids(road_relation)
    for link_id in link_ids:
        link_type = get_link_type_by_aoi(link_id)
        if link_type != PassType.UNKNOWN:
            # LINK类型未知，不处理
            continue

        if link_type == PassType.ENTRANCE_EXIT:
            # link是双向通行，不在此质检
            continue

        if crk_type == PassType.ENTRANCE_EXIT:
            # 出入口是双向通行，不在此质检
            continue

        if crk_type != link_type:
            # LINK类型和出入口类型不一致，质检失败
            return False, crk_type, link_type
    return True, crk_type, crk_type


def get_crk_type_by_name(name: str):
    """
    根据名称获取出入口通行性
    """
    if name.endswith("出入口"):
        return PassType.ENTRANCE_EXIT
    if name.endswith("入口"):
        return PassType.ENTRANCE
    if name.endswith("出口"):
        return PassType.EXIT
    return PassType.UNKNOWN


def get_link_type_by_aoi(link_id):
    """
    根据AOI获取LINK通行性方向
    """
    long_link_id = query.get_long_link_id(link_id)
    link_res = query.get_nav_link(long_link_id)
    if link_res is None:
        return PassType.UNKNOWN

    link_wkt, dir = link_res
    line = shapely.wkt.loads(link_wkt)
    start_coords, end_coords = line.coords[0], line.coords[-1]

    start_point = Point(start_coords)
    end_point = Point(end_coords)

    s_in_aoi = query.is_in_aoi(start_point.wkt)
    e_in_aoi = query.is_in_aoi(end_point.wkt)
    if dir == 1:
        return PassType.ENTRANCE_EXIT
    elif dir == 2:
        if s_in_aoi and not e_in_aoi:
            return PassType.EXIT
        else:
            return PassType.ENTRANCE
    elif dir == 3:
        if not s_in_aoi and e_in_aoi:
            return PassType.EXIT
        else:
            return PassType.ENTRANCE
    return PassType.UNKNOWN


def get_gate_type(node_id):
    """
    获取大门类型
    """
    semantic_res = query.get_gate_type(node_id)
    if len(semantic_res) == 1:
        type, nnww_tag = semantic_res[0][4], semantic_res[0][5]
        if type == "0":
            return PassType.EMERGENCY
        else:
            if "入口" in nnww_tag:
                return PassType.ENTRANCE
            elif "出口" in nnww_tag:
                return PassType.EXIT
            else:
                if type == "1":
                    return PassType.EXIT
                elif type == "2":
                    return PassType.ENTRANCE
                elif type == "3":
                    return PassType.ENTRANCE_EXIT
    elif len(semantic_res) == 2:
        """
        (0, 0)  ---> 紧急
        (0, 1) ----> 出口
        (0, 2) ---> 入口
        (3, 3) ---> 出入口
        """
        semantic_res_0, semantic_res_1 = semantic_res[0], semantic_res[1]
        type_arr = [semantic_res_0[4], semantic_res_1[4]]
        if type_arr[0] == "0" and type_arr[1] == "0":
            return PassType.EMERGENCY

        if type_arr[0] == "3" and type_arr[1] == "3":
            return PassType.ENTRANCE_EXIT

        if "0" in type_arr and "2" in type_arr:
            return PassType.ENTRANCE

        if "0" in type_arr and "1" in type_arr:
            return PassType.EXIT
    return PassType.UNKNOWN


def get_node_ids(road_relation):
    """
    根据 road_relation 获取出入口关联的去重 node_ids
    """
    node_ids = set()
    if road_relation is None or "link_info" not in road_relation:
        return []

    for x in road_relation["link_info"]:
        if "node_id" in x and "type" in x and x["type"] == 1:
            node_ids.add(x["node_id"])
    return list(node_ids)


def get_link_ids(road_relation):
    """
    根据 road_relation 获取出入口关联的去重 link_ids
    """
    link_ids = set()
    if road_relation is None or "link_info" not in road_relation:
        return []

    for x in road_relation["link_info"]:
        if "link_id" in x and "type" in x and x["type"] == 2:
            link_ids.add(x["link_id"])
    return list(link_ids)


def save_result(result):
    """
    保存结果到文件
    """
    with open("./check_crk_accessibility.tsv", "w") as file:
        # 设置文件头
        header = (
            "出入口bid\troad_relation\tname\tstd_tag\tshow_tag\t是否精准1.0\t是否精准2.0\t停车场click_pv\t是否51重保\t停车场show_tag\t原因\n"
        )
        file.write(header)

        # 遍历结果并格式化写入
        for item in result:
            strs = (
                f"{item['crk_bid']}\t"
                f"{item['road_relation']}\t"
                f"{item['name']}\t"
                f"{item['std_tag']}\t"
                f"{item['show_tag']}\t"
                f"{item['precise10']}\t"
                f"{item['precise20']}\t"
                f"{item['park_click_pv']}\t"
                f"{item['is_51_important']}\t"
                f"{item['parking_show_tag']}\t"
                f"{item['reason']}\n"
            )
            file.write(strs)


if __name__ == "__main__":
    """
    main
    """
    run()
