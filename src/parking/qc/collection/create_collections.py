"""
天级例行创建集合：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/6QPRxAgpgZZ6O7
* 全量停车场主点集合
* 极高热防护集合
* 高热防护集合
* 潜热防护集合
"""
import tqdm
import datetime

from dataclasses import dataclass
from multiprocessing import Pool
from typing import Optional
from src.parking.qc.common import query
from src.parking.qc.common.monitor import send_hi, setup_logger

logger = setup_logger()  # 设置 logger

WORKERS_NUM: int = 60


@dataclass
class ParkData:
    """
    停车场数据
    """

    park_bid: str
    park_click_pv: Optional[int]
    park_nav_pv: Optional[int]
    park_std_tag: Optional[str]
    parent_bid: Optional[str]
    parent_std_tag: Optional[str]
    parent_click_pv: Optional[int]
    parent_nav_pv: Optional[int]
    root_bid: Optional[str]
    root_click_pv: Optional[int]
    root_nav_pv: Optional[int]
    root_std_tag: Optional[str]
    main_bid: Optional[str]
    main_click_pv: Optional[int]
    main_nav_pv: Optional[int]
    main_std_tag: Optional[str]
    city: str
    is_4categories: bool  # 是否是四垂类停车场
    is_4categories_first_push: bool  # 是否是四垂类首推停车场
    is_sd: bool  # 是否是商单车厂数据
    root_std_tag_in_4c: Optional[str]  # 四垂类表里的主点std_tag


def run():
    """
    run
    """
    logger.info("start truncate_collections")
    query.truncate_collections()
    logger.info("end truncate_collections")

    logger.info("start create_park_main_poi_collection")
    create_park_main_poi_collection()
    logger.info("end create_park_main_poi_collection")

    logger.info("start create_over_heat_protection")
    create_over_heat_protection()
    logger.info("end create_over_heat_protection")

    logger.info("start create_high_heat_protection")
    create_high_heat_protection()
    logger.info("end create_high_heat_protection")

    logger.info("start create_potential_heat_protection")
    create_potential_heat_protection()
    logger.info("end create_potential_heat_protection")

    logger.info("start monitor")
    monitor()
    logger.info("end monitor")


def create_park_main_poi_collection():
    """
    创建全量停车场主点集合
    """
    logger.info("query_all_park_data")
    parks: list[ParkData] = query_all_park_data()

    logger.info("fill_main_poi")
    fill_main_poi(parks)

    # 转为 dict 格式，准备入库
    records = []
    for park in tqdm.tqdm(parks, desc="构建主点集合"):
        record = {
            "park_bid": park.park_bid,
            "park_click_pv": park.park_click_pv,
            "park_nav_pv": park.park_nav_pv,
            "park_std_tag": park.park_std_tag,
            "root_bid": park.root_bid,
            "root_click_pv": park.root_click_pv,
            "root_nav_pv": park.root_nav_pv,
            "root_std_tag": park.root_std_tag,
            "main_bid": park.main_bid,
            "main_click_pv": park.main_click_pv,
            "main_nav_pv": park.main_nav_pv,
            "main_std_tag": park.main_std_tag,
            "is_4categories": park.is_4categories,
            "is_4categories_first_push": park.is_4categories_first_push,
            "is_sd": park.is_sd,
            "city": park.city,
            "root_std_tag_in_4c": park.root_std_tag_in_4c,
        }
        records.append(record)

    logger.info("copy_heat_data_to_table")
    query.copy_heat_data_to_table(records, "park_main_poi")


def process_park(park):
    """
    停车场信息组装
    """
    try:
        root_bid = calc_poi_root(park)
        park_bid = park["bid"]
        park_std_tag = park["std_tag"]
        park_parent_bid = park["parent_bid"]
        city_name = park["city_name"]

        return ParkData(
            park_bid=park_bid,
            park_nav_pv=query.get_nav_pv(park_bid),
            park_click_pv=query.get_click_pv(park_bid),
            park_std_tag=park_std_tag,
            parent_bid=park_parent_bid,
            parent_nav_pv=query.get_nav_pv(park_parent_bid),
            parent_click_pv=query.get_click_pv(park_parent_bid),
            parent_std_tag=query.get_std_tag(park_parent_bid),
            root_bid=root_bid,
            root_nav_pv=query.get_nav_pv(root_bid),
            root_click_pv=query.get_click_pv(root_bid),
            root_std_tag=query.get_std_tag(root_bid),
            main_bid="",
            main_nav_pv=-1,
            main_click_pv=-1,
            main_std_tag="",
            city=city_name,
            is_4categories=query.is_4categories_park(park_bid),
            is_4categories_first_push=query.is_4categories_first_push_park(park_bid),
            is_sd=park_bid in sd_parks_bid_list(),
            root_std_tag_in_4c=query.get_4categories_park_root_std_tag(park_bid),
        )
    except Exception as e:
        print(f"处理停车场 {park.get('bid')} 时出错: {e}")
        return None


def query_all_park_data() -> list[ParkData]:
    """
    全量停车场数据
    """
    parks = query.get_all_parks()
    with Pool(processes=WORKERS_NUM) as pool:  # 根据CPU核数调整进程数
        results = list(tqdm.tqdm(pool.imap_unordered(process_park, parks), total=len(parks)))

    # 过滤 None
    park_data_list = [r for r in results if r is not None]
    return park_data_list


def process_fill_main_poi(park: ParkData) -> ParkData:
    """
    填充主点信息
    1、没有父点，主点为空
    2、有父点，满足以下条件主点取根节点
        * 父点为四大垂类、房地产;写字楼、公司企业;园区、生活服务%、交通设施%、休闲娱乐%、购物%、医疗%、旅游景点%、交通设施%
        * 父点不在上述垂类，但父点垂类=最高主点垂类
    3、不满足1和2的部分主点取父点
    """
    try:
        # 没有父点，主点为空
        if not park.parent_bid or park.parent_bid in (0, "0", None):
            park.main_bid = ""
            park.main_nav_pv = 0.0
            park.main_click_pv = 0.0
            park.main_std_tag = ""
            return park

        # 有父点，判断是否取根节点
        is_4c_park = query.is_4categories_park(park.park_bid)
        if (
            is_4c_park
            or park.parent_std_tag in ["房地产;写字楼", "公司企业;园区"]
            or park.parent_std_tag.startswith("生活服务")
            or park.parent_std_tag.startswith("交通设施")
            or park.parent_std_tag.startswith("休闲娱乐")
            or park.parent_std_tag.startswith("购物")
            or park.parent_std_tag.startswith("医疗")
            or park.parent_std_tag.startswith("旅游景点")
            or park.parent_std_tag == park.root_std_tag
        ):
            park.main_bid = park.root_bid
            park.main_nav_pv = park.root_nav_pv
            park.main_click_pv = park.root_click_pv
            park.main_std_tag = park.root_std_tag
            return park

        # 不满足以上条件的，主点取父点
        park.main_bid = park.parent_bid
        park.main_nav_pv = park.parent_nav_pv
        park.main_click_pv = park.parent_click_pv
        park.main_std_tag = park.parent_std_tag
        return park
    except Exception as e:
        logger.error("填充主点信息失败：%s", e)
        return park


def fill_main_poi(parks: list[ParkData]):
    """
    填充主点信息
    """
    with Pool(processes=WORKERS_NUM) as pool:
        results = list(tqdm.tqdm(pool.imap_unordered(process_fill_main_poi, parks), total=len(parks)))

    # inplace 修改 parks 列表
    parks[:] = results


def sd_parks_bid_list():
    """
    商单车厂停车场bid集合(写死)
    """
    return [
        "11002189735670893812",
        "11762402290356425994",
        "4738197633283294079",
        "16408375058257923372",
        "12449360207632799303",
        "14501004416283120277",
        "13414771093811789811",
        "1897554424937450186",
        "5122534946050214808",
        "9091858050995732787",
        "5132238455073377852",
        "2013676330218175174",
        "15271362048624273013",
        "14335245167630437947",
        "8382043569227485985",
        "13942326390635610841",
        "18014549821179822079",
        "8640468016433424176",
        "6194986260442007734",
        "9416565236137912102",
        "15909421775940461905",
        "9870003802630865817",
        "2937412382012817778",
        "3070838576954171170",
        "14481189051687819187",
        "2781046546996667512",
        "3650149263217454984",
        "2485938503890860581",
        "6794734176912481627",
        "7635919176748040272",
        "10752023857221012218",
        "12774499688538819106",
        "3704721247571467567",
    ]


def calc_poi_root(park):
    """
    计算根节点，最多向上追溯 10 次
    """
    root_bid = park["bid"]
    rec_bids = [root_bid]

    max_depth = 10
    depth = 0
    while depth < max_depth:
        poi_info = query.get_poi_info(root_bid)
        if poi_info is None:
            break

        relation_bid = poi_info["relation_bid"]
        if relation_bid in ("0", None, 0) or relation_bid in rec_bids:
            break

        root_bid = relation_bid
        rec_bids.append(relation_bid)
        depth += 1

    if root_bid == park["bid"]:
        return ""
    return root_bid


def create_over_heat_protection():
    """
    创建极高热防护集合
    """
    data = query.get_over_heat_pv_data()
    query.insert_over_heat_data_to_table(data)


def create_high_heat_protection():
    """
    创建高热防护集合
    """
    data = query.get_high_heat_pv_data()
    query.copy_heat_data_to_table(data, "high_heat_protection")


def create_potential_heat_protection():
    """
    创建潜热防护集合
    """
    data = query.get_potential_heat_pv_data()
    query.copy_heat_data_to_table(data, "potential_heat_protection")


def monitor():
    """
    监控
    """
    park_main_poi_collection_num = query.count_park_main_poi_collection()
    over_heat_protection = query.count_over_heat_protection()
    high_heat_protection = query.count_high_heat_protection()
    potential_heat_protection = query.count_potential_heat_protection()
    send_hi(
        f"""
        全量停车场主点集合：{park_main_poi_collection_num} 条：
        ------------------------------------------------------------------
        极高热防护集合：{over_heat_protection} 条
        高热防护集合：{high_heat_protection} 条
        潜热防护集合：{potential_heat_protection} 条
        ------------------------------------------------------------------
        数据更新时间：{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        """
    )


if __name__ == "__main__":
    """
    main
    """
    run()
