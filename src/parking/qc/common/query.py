"""
数据交互层
"""
from src.parking.recognition import dbutils
from src.tools import pgsql


def get_node_geom(node_id):
    """
    获取node坐标
    """
    sql = "select st_astext(geom) from nav_node where node_id = %s"
    node_wkt = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [node_id])
    return node_wkt


def get_link_geom(link_id):
    """
    获取link坐标
    """
    sql = "select st_astext(geom) from nav_link where link_id = %s"
    link_wkt = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [link_id])
    return link_wkt


def get_link_kind(link_id):
    """
    获取link类型
    """
    sql = "select kind from nav_link where link_id = %s"
    link_kind_res = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [link_id])
    return link_kind_res


def get_long_link_id(short_link_id):
    """
    获取长link_id
    """
    sql = """
        SELECT sid FROM image_r WHERE tid = %s
        """
    res = dbutils.fetch_one(pgsql.TRANS_ID, sql, (short_link_id,))
    return res[0]


def get_long_node_id_by_short(short_node_id):
    """
    获取长node_id
    """
    sql = f"select sid from image_n where tid = '{short_node_id}'"
    trans_node = dbutils.fetch_one(pgsql.TRANS_ID, sql, (short_node_id,))
    if trans_node is None:
        return None
    l_nodeid = trans_node[0]
    return l_nodeid


def get_nav_link(long_link_id):
    """
    1-双方向
    2-顺方向
    3-逆方向
    """
    sql = """
      select st_astext(geom), dir from nav_link where link_id = %s
    """
    link_res = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [long_link_id])
    return link_res


def is_in_aoi(point_wkt):
    """
    判断坐标点是否在AOI内
    """
    sql = """
         select * 
         from blu_face 
         where aoi_level = 2 AND src != 'SD' and st_contains(geom, ST_GeomFromText(%s, 4326))
    """
    res = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, [point_wkt])
    return res


def get_gate_type(node_id):
    """
    获取大门类型
    """
    sql = """
        select node_id, gate_id, passage, traversability, type, nnww_tag, type_desc 
        from gates_semantic 
        where node_id = %s
    """
    semantic_res = dbutils.fetch_all(pgsql.DEST_TRAJ, sql, (node_id,))
    return semantic_res


def get_all_crk_list():
    """
    获取所有停车场出入口
    """
    sql = """
          select gate.bid, gate.name, gate.address, gate.std_tag, gate.show_tag, gate.road_relation, gate.parent_id,
          parking.click_pv, parking.show_tag as parking_show_tag,
          level.precise10, level.precise20,
          important.bid as important_bid
          from park_online_data gate
          left join poi parking on parking.bid = gate.parent_id
          left join park_statistical_data level on level.bid = gate.parent_id
          left join important_51_park_bid_2025 important on important.bid = gate.parent_id
          where gate.std_tag = '出入口;停车场出入口' and gate.status = 1 and
          gate.road_relation is not null
      """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)

    columns = [
        "bid",
        "name",
        "address",
        "std_tag",
        "show_tag",
        "road_relation",
        "parent_id",
        "click_pv",
        "parking_show_tag",
        "precise10",
        "precise20",
        "important_bid",
    ]
    # 映射为 dict 列表
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_all_parks_list():
    """
    获取所有停车场
    """
    sql = """
          select park.bid, park.name, park.address, park.std_tag, park.show_tag, park.road_relation, park.parent_id,
          p.click_pv, p.show_tag as parking_show_tag,
          level.precise10, level.precise20,
          important.bid as important_bid
          from park_online_data park
          left join poi p on p.bid = park.bid
          left join park_statistical_data level on level.bid = park.parent_id
          left join important_51_park_bid_2025 important on important.bid = park.parent_id
          where park.std_tag in ('交通设施;停车场') and park.status = 1
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = [
        "bid",
        "name",
        "address",
        "std_tag",
        "show_tag",
        "road_relation",
        "parent_id",
        "click_pv",
        "park_show_tag",
        "precise10",
        "precise20",
        "important_bid",
    ]
    # 映射为 dict 列表
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_crk_list(park_bid):
    """
    获取出入口列表
    """
    sql = """
        select show_tag 
        from park_online_data 
        where parent_id = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (park_bid,))
    return ret


def get_all_parks():
    """
    获取所有停车场数据
    """
    sql = """
      select bid, parent_id, std_tag, city_name
      from park_online_data
      where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = ["bid", "parent_bid", "std_tag", "city_name"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_nav_pv(bid: str):
    """
    获取月导航pv
    """
    sql = """
         select pv
         from poi_nav_500m_pv
         where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    if ret is None:
        return -1
    return ret[0]


def get_click_pv(bid: str):
    """
    获取算路pv
    """
    sql = """
        select click_pv
        from poi
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    if ret is None:
        return -1
    return ret[0]


def get_poi_info(bid: str):
    """
    获取POI信息
    """
    sql = """
        select bid, relation_bid, click_pv, std_tag 
        from poi 
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    columns = ["bid", "relation_bid", "click_pv", "std_tag"]

    if ret is None:
        return None
    return dict(zip(columns, ret))


def get_std_tag(bid: str):
    """
    获取poi.std_tag
    """
    sql = """
        select std_tag
        from poi
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    if ret is None:
        return ""
    return ret[0]


def is_4categories_park(bid: str):
    """
    是否是四大垂类停车场
    """
    sql = """
        select *
        from park_4categories_list_2024q4
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    if ret is None:
        return False
    return True


def is_4categories_first_push_park(bid: str):
    """
    是否是四大垂类停车场
    """
    sql = """
        select *
        from first_push_park_4categories_list_2024q4
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    if ret is None:
        return False
    return True


def get_4categories_park_root_std_tag(bid: str):
    """
    是否是四大垂类停车场
    """
    sql = """
        select root_std_tag
        from park_4categories_list_2024q4
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    if ret is None or ret[0] is None:
        return ""
    return ret[0]


def get_over_heat_pv_data():
    """
    获取极热数据
    """
    sql = """
        select 
          park_bid, park_click_pv, park_nav_pv, park_std_tag, 
          root_bid, root_click_pv, root_nav_pv, root_std_tag, 
          main_bid, main_click_pv, main_nav_pv, main_std_tag,
          is_4categories, is_4categories_first_push, is_sd, city,
          root_std_tag_in_4c
        from park_main_poi
        where 
          main_nav_pv >= 2000 or
          main_click_pv >= 100000 or
          root_std_tag_in_4c like '%交通设施%' or 
          is_sd is TRUE
      """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = [
        "park_bid",
        "park_click_pv",
        "park_nav_pv",
        "park_std_tag",
        "root_bid",
        "root_click_pv",
        "root_nav_pv",
        "root_std_tag",
        "main_bid",
        "main_click_pv",
        "main_nav_pv",
        "main_std_tag",
        "is_4categories",
        "is_4categories_first_push",
        "is_sd",
        "city",
        "root_std_tag_in_4c",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_high_heat_pv_data():
    """
    获取高热数据
    """
    sql = """
      select 
        park_bid, park_click_pv, park_nav_pv, park_std_tag, 
        root_bid, root_click_pv, root_nav_pv, root_std_tag, 
        main_bid, main_click_pv, main_nav_pv, main_std_tag,
        is_4categories, is_4categories_first_push, is_sd, city,
        root_std_tag_in_4c
      from park_main_poi
      where 
        (
            (main_nav_pv >= 500 and main_nav_pv < 2000) or (main_click_pv >= 20000 and main_click_pv < 100000)
        ) or
        (
            is_4categories is TRUE and 
            (
                (main_nav_pv >= 300 and main_nav_pv < 2000) or (main_click_pv >= 5000 and main_click_pv < 100000)
            )
        ) or 
        (
            city in ('北京市','上海市','广州市','深圳市') and 
            (
                (main_nav_pv >= 300 and main_nav_pv < 2000) or (main_click_pv >= 5000 and main_click_pv < 100000)
            )
        )
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = [
        "park_bid",
        "park_click_pv",
        "park_nav_pv",
        "park_std_tag",
        "root_bid",
        "root_click_pv",
        "root_nav_pv",
        "root_std_tag",
        "main_bid",
        "main_click_pv",
        "main_nav_pv",
        "main_std_tag",
        "is_4categories",
        "is_4categories_first_push",
        "is_sd",
        "city",
        "root_std_tag_in_4c",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_potential_heat_pv_data():
    """
    获取潜热数据
    """
    sql = """
      select 
        park_bid, park_click_pv, park_nav_pv, park_std_tag, 
        root_bid, root_click_pv, root_nav_pv, root_std_tag, 
        main_bid, main_click_pv, main_nav_pv, main_std_tag,
        is_4categories, is_4categories_first_push, is_sd, city,
        root_std_tag_in_4c
      from park_main_poi
      where 
         main_nav_pv >= 300 or
         main_click_pv >= 3000 or 
         is_4categories is TRUE or 
         is_4categories_first_push is TRUE
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = [
        "park_bid",
        "park_click_pv",
        "park_nav_pv",
        "park_std_tag",
        "root_bid",
        "root_click_pv",
        "root_nav_pv",
        "root_std_tag",
        "main_bid",
        "main_click_pv",
        "main_nav_pv",
        "main_std_tag",
        "is_4categories",
        "is_4categories_first_push",
        "is_sd",
        "city",
        "root_std_tag_in_4c",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def copy_heat_data_to_table(data, table_name, batch_size=5000):
    """
     批量写入数据到指定表中
     params：
        data: list[dict] - 每条记录是一个 dict
        table_name: str - 目标表名
        batch_size: int - 每批写入的数据量
    """
    if not data:
        return

    insert_sql_template = f"""
         INSERT INTO {table_name} (
             park_bid, park_click_pv, park_nav_pv, park_std_tag,
             root_bid, root_click_pv, root_nav_pv, root_std_tag,
             main_bid, main_click_pv, main_nav_pv, main_std_tag,
             is_4categories, is_4categories_first_push, is_sd, city,
             root_std_tag_in_4c
         ) VALUES (
             %(park_bid)s, %(park_click_pv)s, %(park_nav_pv)s, %(park_std_tag)s,
             %(root_bid)s, %(root_click_pv)s, %(root_nav_pv)s, %(root_std_tag)s,
             %(main_bid)s, %(main_click_pv)s, %(main_nav_pv)s, %(main_std_tag)s,
             %(is_4categories)s, %(is_4categories_first_push)s, %(is_sd)s, %(city)s,
             %(root_std_tag_in_4c)s
         )
    """

    total = len(data)
    for i in range(0, total, batch_size):
        batch = data[i : i + batch_size]
        try:
            conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
            pgsql.execute_many(conn, insert_sql_template, batch)
            print("已写入 %d / %d 条数据" % (min(i + batch_size, total), total))
        except Exception as e:
            print(f"第 {i}-{i + batch_size} 条记录写入失败: {e}")


def insert_over_heat_data_to_table(data, batch_size=5000):
    """
    将数据插入 over_heat_protection 表中，避免重复插入（以 aoi_bid 为唯一键）。
    :param data: list[dict] - 每条记录是一个 dict
    :param batch_size: int - 每批写入的数据量
    """
    if not data:
        return

    insert_sql_template = """
       INSERT INTO over_heat_protection (
             park_bid, park_click_pv, park_nav_pv, park_std_tag,
             root_bid, root_click_pv, root_nav_pv, root_std_tag,
             main_bid, main_click_pv, main_nav_pv, main_std_tag,
             is_4categories, is_4categories_first_push, is_sd, city,
             root_std_tag_in_4c
       ) VALUES (
             %(park_bid)s, %(park_click_pv)s, %(park_nav_pv)s, %(park_std_tag)s,
             %(root_bid)s, %(root_click_pv)s, %(root_nav_pv)s, %(root_std_tag)s,
             %(main_bid)s, %(main_click_pv)s, %(main_nav_pv)s, %(main_std_tag)s,
             %(is_4categories)s, %(is_4categories_first_push)s, %(is_sd)s, %(city)s,
             %(root_std_tag_in_4c)s
       )
       ON CONFLICT (park_bid) DO NOTHING
    """

    total = len(data)
    for i in range(0, total, batch_size):
        batch = data[i : i + batch_size]
        try:
            conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
            pgsql.execute_many(conn, insert_sql_template, batch)
            print("已写入 %d / %d 条数据" % (min(i + batch_size, total), total))
        except Exception as e:
            print(f"第 {i}-{i + batch_size} 条记录写入失败: {e}")


def count_park_main_poi_collection():
    """
    计算主点集合停车场总量
    """
    sql = """
        select count(distinct park_bid)
        from park_main_poi
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def count_over_heat_protection():
    """
    计算极高热防护集合停车场总量
    """
    sql = """
        select count(distinct park_bid)
        from over_heat_protection
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def count_high_heat_protection():
    """
    计算高热防护集合停车场总量
    """
    sql = """
        select count(distinct park_bid)
        from high_heat_protection
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def count_potential_heat_protection():
    """
    计算潜热防护集合停车场总量
    """
    sql = """
        select count(distinct park_bid)
        from potential_heat_protection
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    if ret is None:
        return 0
    return ret[0]


def truncate_collections():
    """
    清表数据
    """
    sql = """
        truncate table park_main_poi;
        truncate table over_heat_protection;
        truncate table high_heat_protection;
        truncate table potential_heat_protection;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    try:
        pgsql.execute(conn, sql)
        conn.commit()
    except Exception as e:
        conn.rollback()
    finally:
        conn.close()
