# 停车场识别流程

## 数据库

- `recognition_parking_info`：写入情报点信息
- `recognition_parking_result`：写入识别面信息
- `recognition_parking_product`：写入情报点与识别面的匹配信息

## 输出目录结构

对于给定的一个 `task_id` 识别任务，将按以下方式组织目录：

```
<task_id>
├── info
│   ├── point.tsv  # 情报点
│   └── region.tsv  # 识别范围
├── dataset
│   ├── image
│   ├── <aux channels> ...
│   ├── label_v2
│   ├── _json_info/image  # 配准信息
│   └── prod.txt  # 用于 paddleseg 读取
└── result
    ├── heatmap_prediction  # 识别热力图
    ├── output_vectorize  # 矢量化输出
    │   ├── debug
    │   ├── failed_cases
    │   └── vectorize_info
    ├── vectors.tsv  # 矢量化输出合并
    ├── confidence.jpg  # 置信度分布直方图，用于评估这批边框的合格率
    └── product.tsv  # 最终结果，是情报点关联了识别框的结果
```
