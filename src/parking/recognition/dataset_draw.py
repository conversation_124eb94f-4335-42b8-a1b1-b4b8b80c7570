"""
多模态数据集绘制
"""
from concurrent.futures import ProcessPoolExecutor
from dataclasses import dataclass, field
from pathlib import Path

import cv2
import numpy as np
from shapely import wkt, Polygon
from tqdm import tqdm

from src.aikit import boundary, satellite_imagery, preview_image
from src.fix_geom_process import tools
from src.parking.recognition import dbutils
from src.tools import pipeline, pgsql, utils, linq, tsv, turnoff_point

METER = 1e-5
IMAGE_VERSION = "google-2023"
desc = pipeline.get_desc()


@dataclass
class Label:
    """
    数据集标签
    """

    label_id: str
    source: str
    geom: str


@dataclass
class Context:
    """
    数据集上下文
    """

    output_dir: Path
    case_id: str
    case_geom: str
    labels: list[Label]
    bounds: tuple[float, float, float, float] = field(init=False)
    bounds_geom: Polygon = field(init=False)
    bounds_wkt_with_srid: str = field(init=False)
    size: tuple[int, int] = field(init=False)
    images: dict[str, np.ndarray] = field(default_factory=dict)

    def __post_init__(self):
        geom = wkt.loads(self.case_geom)
        self.bounds = boundary.from_wkt(geom.wkt)
        bounds_wkt = boundary.to_envelope(self.bounds)
        self.bounds_geom = wkt.loads(bounds_wkt)
        self.bounds_wkt_with_srid = f"SRID=4326;{self.bounds_geom.wkt}"


# pipes:


def read_size(image_dir: Path):
    """
    pipe: 读取图片尺寸
    """

    @desc(f"read image size: {image_dir}")
    def pipe(ctx: Context, proceed):
        image_path = image_dir / f"{ctx.case_id}.jpg"
        if not image_path.exists():
            return

        image = cv2.imread(str(image_path))
        ctx.size = image.shape[:2]
        proceed()

    return pipe


def draw_image(image_version: str):
    """
    pipe: 绘制卫星影像图
    """

    @desc(f"draw image: {image_version=}")
    def pipe(ctx: Context, proceed):
        image = satellite_imagery.crop(ctx.bounds, version=image_version, level=20)
        if image is None:
            return

        ctx.size = image.shape[:2]
        ctx.images["image"] = image
        proceed()

    return pipe


@desc()
def draw_inner_road(ctx: Context, proceed):
    """
    pipe: 绘制内部道路 channel
    """
    sql = """
        select link_id, st_astext(geom), dir, kind, lane_l, lane_r, form
        from nav_link
        where st_intersects(geom, %s) and (form ~ '52' or kind = 8);
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [ctx.bounds_wkt_with_srid])
    zero = np.zeros(ctx.size, dtype=np.uint8)
    if ret:
        roads = [(wkt.loads(row[1]), tools.get_road_width(*row[-5:])) for row in ret]
        roads = [geom.buffer(width * METER / 2) for geom, width in roads]
        for geom in roads:
            preview_image.fill_polygon(zero, geom.wkt, ctx.bounds, color=255)

    ctx.images["inner_road"] = zero
    proceed()


@desc()
def draw_outer_road(ctx: Context, proceed):
    """
    pipe: 绘制外道路 channel
    """
    sql = """
        select link_id, st_astext(geom), dir, kind, lane_l, lane_r, form
        from nav_link
        where st_intersects(geom, %s) and kind < 8;
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [ctx.bounds_wkt_with_srid])
    zero = np.zeros(ctx.size, dtype=np.uint8)

    if ret:
        roads = [(wkt.loads(row[1]), tools.get_road_width(*row[-5:])) for row in ret]
        roads = [geom.buffer(width * METER / 2) for geom, width in roads]
        for geom in roads:
            preview_image.fill_polygon(zero, geom.wkt, ctx.bounds, color=255)

    ctx.images["outer_road"] = zero
    proceed()


@desc()
def draw_blu_face(ctx: Context, proceed):
    """
    pipe: 绘制 AOI 面 channel
    """
    sql_blu_face = """
        select face_id, st_astext(geom) from blu_face
        where st_intersects(geom, %s)
            and src != 'SD'
            and aoi_level = 2;
    """
    sql_blu_face_poi = """
        select face_id, poi_bid from blu_face_poi
        where face_id in %s;
    """
    sql_poi = """
        select bid, std_tag from poi
        where bid in %s;
    """
    blu_faces = dbutils.fetch_all(pgsql.BACK_CONFIG, sql_blu_face, [ctx.bounds_wkt_with_srid])
    face_ids = [x[0] for x in blu_faces]
    bids = dbutils.fetch_all(pgsql.BACK_CONFIG, sql_blu_face_poi, [tuple(face_ids)]) if face_ids else []
    bids = {face_id: bid for face_id, bid in bids}

    std_tags = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql_poi, [tuple(bids.values())]) if bids else []
    std_tags = {bid: tag for bid, tag in std_tags}

    blu_faces = [(bids[face_id], geom) for face_id, geom in blu_faces if face_id in bids]
    blu_faces = [(std_tags[bid], geom) for bid, geom in blu_faces if bid in std_tags]
    # 过滤掉 停车场 标签的 blu_face，否则相当于给模型作弊了。
    blu_faces = [geom for std_tag, geom in blu_faces if std_tag not in ("交通设施;停车场", "交通设施;路侧停车位")]

    zero = np.zeros(ctx.size, dtype=np.uint8)
    if blu_faces:
        for geom in blu_faces:
            preview_image.fill_polygon(zero, geom, ctx.bounds, color=128)

        for geom in blu_faces:
            preview_image.draw_polygon(zero, geom, ctx.bounds, thickness=8, color=255)

    ctx.images["blu_face"] = zero
    proceed()


@desc()
def draw_bud_face(ctx: Context, proceed):
    """
    pipe: 绘制建筑面 channel
    """
    sql = """
        select st_astext(geom) from bud_face
        where st_intersects(geom, %s);
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [ctx.bounds_wkt_with_srid])
    zero = np.zeros(ctx.size, dtype=np.uint8)
    if ret:
        geoms = [x[0] for x in ret]
        for geom in geoms:
            preview_image.fill_polygon(zero, geom, ctx.bounds, color=255)

    ctx.images["bud_face"] = zero
    proceed()


@desc()
def draw_doctor_turn_off_point(ctx: Context, proceed):
    """
    pipe: 绘制博士驻留点 channel
    """
    points1 = turnoff_point.get_points_of_doctor(ctx.bounds_geom.wkt)
    points2 = turnoff_point.get_points_of_other(ctx.bounds_geom.wkt)
    points = points1 + points2
    zero = np.zeros(ctx.size, dtype=np.uint8)
    if points:
        for point in points:
            preview_image.draw_point(zero, point.point.wkt, ctx.bounds, radius=2, color=255)

    ctx.images["doctor_turn_off_point"] = zero
    proceed()


@desc()
def draw_label(ctx: Context, proceed):
    """
    pipe: 绘制标签
    """
    ignore_index = 255
    zero = np.zeros(ctx.size, dtype=np.uint8)
    if ctx.labels:
        geoms = [x.geom for x in ctx.labels]
        for geom in geoms:
            preview_image.fill_polygon(zero, geom, ctx.bounds, color=2)

        for geom in geoms:
            preview_image.draw_polygon(zero, geom, ctx.bounds, thickness=14, color=ignore_index)

        for geom in geoms:
            preview_image.draw_polygon(zero, geom, ctx.bounds, thickness=8, color=1)

    ctx.images["label_v2"] = zero
    proceed()


def filter_size(compare):
    """
    pipe: 过滤面积过小或过大的图片
    """

    @desc("filter size")
    def pipe(ctx: Context, proceed):
        if not compare(get_pixel_area(ctx.bounds_geom)):
            return

        proceed()

    return pipe


@desc()
def save_georeferencing_info(ctx: Context, proceed):
    """
    pipe: 保存图片配准信息
    """
    save_dir = ctx.output_dir / "_json_info" / "image"
    utils.ensure_dir(save_dir)

    h, w = ctx.size
    left, top, right, bottom = ctx.bounds
    info = {
        "id": ctx.case_id,
        "height": h,
        "width": w,
        "region": {"left": left, "top": top, "right": right, "bottom": bottom},
    }
    utils.write_json(save_dir / f"{ctx.case_id}.json", info)
    proceed()


@desc()
def save_images(ctx: Context, proceed):
    """
    pipe: 保存数据集图片
    """

    def is_jpg(img: np.ndarray) -> bool:
        return len(img.shape) == 3 and img.shape[2] == 3

    for name, image in ctx.images.items():
        suffix = "jpg" if is_jpg(image) else "png"
        save_path = utils.ensure_path(ctx.output_dir / name / f"{ctx.case_id}.{suffix}")
        cv2.imwrite(str(save_path), image)

    proceed()


# helpers:


def get_pixel_area(geom) -> float:
    """
    计算像素面积
    """
    return geom.area / satellite_imagery.GEO_PER_PIXEL_20**2


def buffer_wkt(wkt_str: str, buffer: float):
    """
    缩放 wkt
    """
    geom = wkt.loads(wkt_str)
    return geom.buffer(buffer).wkt


def get_street_infos(cases: list[tuple[str, str]]) -> list[tuple[str, str, tuple[str]]]:
    """
    获取街道信息
    """
    sql = """
        select face_id, st_astext(geom) from street_region
        where st_area(st_intersection(geom, %(park)s)) / st_area(%(park)s) > 0.8;
    """
    infos: list[tuple[str, str]] = []
    for bid, geom in tqdm(cases, desc="get street info"):
        ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, {"park": f"SRID=4326;{geom}"})
        infos.extend([(bid, *x) for x in ret])

    streets = linq.group_by(infos, key=lambda x: x[1:], value=lambda x: x[0])
    return [(face_id, geom, tuple(bids)) for (face_id, geom), bids in streets.items()]


def get_completed_case_ids(dataset_dir: Path):
    """
    获取已完成的数据集图片，用于断电恢复
    """
    image_names = [x.name for x in dataset_dir.iterdir() if x.is_dir() and not x.name.startswith("_")]
    image_dirs = [dataset_dir / name for name in image_names]
    case_ids = [{x.stem for x in image_dir.glob("*.*")} for image_dir in image_dirs]
    return set.intersection(*case_ids) if case_ids else set()


DRAW_PIPE = pipeline.Pipeline(
    # filter_size(lambda size: size <= 2048 * 2048),
    filter_size(lambda size: size >= 128 * 128),
    # read_size(OUTPUT_DIR / "image"),
    draw_image(IMAGE_VERSION),
    draw_label,
    draw_inner_road,
    draw_outer_road,
    draw_blu_face,
    draw_bud_face,
    draw_doctor_turn_off_point,
    save_georeferencing_info,
    save_images,
)
desc.attach(DRAW_PIPE)
desc.disabled = True  # 正常运行不需要打印日志，但有时某个数据库卡住了，可以把这行注释掉，打出日志，方便排查卡在哪步


def draw_pipe(ctx: Context):
    """
    绘制数据集图片，用于并行
    """
    DRAW_PIPE(ctx)


def main():
    """
    主函数
    """
    output_dir = Path("output/head_20_key_category.missing_polygon.20240903-part_0.point")
    info_dir = output_dir / "info"
    region_path = info_dir / "region.tsv"
    dataset_dir = output_dir / "dataset"

    # 从 tsv 中导入
    tasks = [
        Context(output_dir=dataset_dir, case_id=x[-2], case_geom=x[-1], labels=[]) for x in tsv.read_tsv(region_path)
    ]

    # 断点恢复：
    completed_case_ids = get_completed_case_ids(dataset_dir)
    tasks = [task for task in tasks if task.case_id not in completed_case_ids]
    with ProcessPoolExecutor(max_workers=2) as executor:
        _ = list(tqdm(executor.map(draw_pipe, tasks), total=len(tasks)))


if __name__ == "__main__":
    main()
