"""
根据给定的 POINT 生成相应的识别 Region，供给 dataset_draw.py 进行数据集绘制。
"""
from dataclasses import dataclass
from pathlib import Path
from typing import Callable, Optional

from shapely import wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.aikit import boundary, satellite_imagery
from src.tools import pgsql, pipeline, tsv, utils

METER = 1e-5


@dataclass
class Region:
    """
    识别范围
    """

    case_id: str
    geom: str


@dataclass
class Context:
    """
    上下文
    """

    info_id: str
    info_geom: BaseGeometry
    region: Optional[Region] = None


def try_street_region(ctx: Context, proceed):
    """
    pipe: 尝试使用街区作为识别范围
    """
    sql = """
        select face_id, st_astext(geom) from street_region
        where 1 = 1
            and st_isvalid(geom)
            and st_isvalid(%(park)s)
            and geom && %(park)s
            and st_area(st_intersection(geom, %(park)s)) / st_area(%(park)s) > 0.5;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_SLAVER_CONFIG)
    info_geom = ctx.info_geom.buffer(10 * METER)
    ret = pgsql.fetch_one(conn, sql, {"park": f"SRID=4326;{info_geom.wkt}"})
    if ret:
        face_id, geom = ret
        ctx.region = Region(f"street_region-{face_id}", geom)

    proceed()


def try_blu_face_region(ctx: Context, proceed):
    """
    pipe: 尝试使用 AOI 作为识别范围
    """
    sql = """
        select face_id, st_astext(geom) from blu_face
        where 1 = 1
            and st_isvalid(geom)
            and st_isvalid(%(park)s)
            and geom && %(park)s
            and st_area(st_intersection(geom, %(park)s)) / st_area(%(park)s) > 0.5
            and aoi_level = 2
            and src != 'SD';
    """
    conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
    info_geom = ctx.info_geom.buffer(50 * METER)
    ret = pgsql.fetch_one(conn, sql, {"park": f"SRID=4326;{info_geom.wkt}"})
    if ret:
        face_id, geom = ret
        ctx.region = Region(f"blu_face-{face_id}", geom)

    proceed()


def try_buffer_region(ctx: Context, proceed):
    """
    pipe: 最后保底，直接 buffer 作为识别范围
    """
    info_geom = ctx.info_geom.buffer(250 * METER)
    ctx.region = Region(f"buffer-{utils.md5(info_geom.wkt)}", info_geom.wkt)

    proceed()


def buffer(meter: float):
    """
    pipe: 调整识别范围的大小，如果 region 存在
    """

    def pipe(ctx: Context, proceed):
        if ctx.region:
            geom = wkt.loads(ctx.region.geom).buffer(meter * METER)
            ctx.region.geom = geom.wkt

        proceed()

    return pipe


def break_if_done(check_size: Callable[[float], bool]):
    """
    pipe: 如果识别范围的面积在给定的范围内，则跳过后续的 pipe 执行
    """

    def pipe(ctx: Context, proceed):
        if ctx.region and check_size(get_pixel_area(ctx.region.geom)):
            return

        ctx.region = None
        proceed()

    return pipe


# helpers:


def get_pixel_area(geom, map_level: int = 20) -> float:
    """
    得到一个 geom aabb 的像素单位的面积
    """
    bounds = boundary.from_wkt(geom)
    envelope = boundary.to_envelope(bounds)
    geom = wkt.loads(envelope)
    geo_per_pixel = satellite_imagery.GEO_PER_PIXEL_20 * (2 ** (20 - map_level))
    return geom.area / geo_per_pixel**2


def main():
    """
    主函数
    """
    output_dir = Path("output/head_20_key_category.missing_porlygon.20240903-part_0.point")
    info_dir = output_dir / "info"
    info_path = info_dir / "point.tsv"
    region_path = info_dir / "region.tsv"

    def check_size(area: float):
        return 128**2 <= area <= 2304**2

    generate_region = pipeline.Pipeline(
        try_street_region,
        buffer(30),
        break_if_done(check_size),
        try_blu_face_region,
        buffer(50),
        break_if_done(check_size),
        try_buffer_region,
    )

    infos = list(tsv.read_tsv(info_path))
    tasks = (Context(info_id=info_id, info_geom=wkt.loads(point)) for info_id, point in infos)
    for task in tqdm(tasks, total=len(infos)):
        generate_region(task)
        if task.region:
            row = [task.info_id, task.info_geom.wkt, task.region.case_id, task.region.geom]
            tsv.write_tsv(region_path, [row], mode="a")


if __name__ == "__main__":
    main()
