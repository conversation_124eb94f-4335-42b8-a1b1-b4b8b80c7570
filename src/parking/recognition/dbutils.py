"""
重试 pgsql 操作
"""
from typing import Union, Sequence, Mapping, Callable

import psycopg2
import retrying

from src.tools import pgsql

RETRY_ERRORS = (
    psycopg2.errors.AdminShutdown,
    psycopg2.errors.SerializationFailure,
    psycopg2.OperationalError,
    psycopg2.InterfaceError,
)


def retry_if_special_errors(exception):
    """
    判断是否指定的错误（在 RETRY_ERRORS 列表中添加），若为指定错误，则重试，否则直接抛异常
    """
    return isinstance(exception, RETRY_ERRORS)


@retrying.retry(
    stop_max_attempt_number=8,
    wait_random_min=1000,
    wait_random_max=5000,
    retry_on_exception=retry_if_special_errors,
)
def fetch_all(config: Union[dict, Callable[[], dict]], sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    """
    pgsql.fetch_all 的可重试版本
    """
    conn = pgsql.get_connection_ttl(config)
    res = pgsql.fetch_all(conn, sql, args)
    conn.close()
    return res


@retrying.retry(
    stop_max_attempt_number=8,
    wait_random_min=1000,
    wait_random_max=5000,
    retry_on_exception=retry_if_special_errors,
)
def fetch_one(config: Union[dict, Callable[[], dict]], sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    """
    pgsql.fetch_one 的可重试版本
    """
    conn = pgsql.get_connection_ttl(config)
    return pgsql.fetch_one(conn, sql, args)


@retrying.retry(
    stop_max_attempt_number=8,
    wait_random_min=1000,
    wait_random_max=5000,
    retry_on_exception=retry_if_special_errors,
)
def execute(config: Union[dict, Callable[[], dict]], sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    """
    pgsql.execute 的可重试版本
    """
    conn = pgsql.get_connection_ttl(config)
    try:
        pgsql.execute(conn, sql, args)
    except Exception:
        _safe_rollback_or_close(conn)
        raise


# noinspection PyBroadException
def _safe_rollback_or_close(conn):
    """
    尝试回滚；如果回滚失败，则认为连接已坏，关闭它。不要让 rollback 和 close 的异常干扰主线，ignore 即可。
    """
    try:
        conn.rollback()
    except Exception:
        try:
            conn.close()
        except Exception:
            pass
