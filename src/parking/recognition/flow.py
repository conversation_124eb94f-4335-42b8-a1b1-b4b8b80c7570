"""
代表一次识别计划，将情报清单拆分成多个并行任务，启动 task.py
"""
import subprocess
import sys
from pathlib import Path

from src.tools import pgsql, tsv, utils


def get_position_by_bid(bids: list[str]):
    """
    根据 bid 获取位置信息
    """
    sql = """
        select bid, st_astext(geometry) from poi
        where bid in %s;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        return pgsql.fetch_all(conn, sql, [tuple(bids)])


def split_list(data: list, part_num: int):
    """
    将列表拆分成多个子列表，每个子列表的长度相同，多余项并入最后一个子列表
    """
    count_by_park = len(data) // part_num
    parts = [count_by_park] * part_num
    parts[-1] += len(data) % part_num

    for i in range(len(parts)):
        start = sum(parts[:i])
        end = start + parts[i]
        yield data[start:end]


def emit_task(file_path: Path, gpu: int):
    """
    启动一个识别任务
    """
    output_dir = Path("output")
    task_id, part_id = file_path.stem.split(".")
    log_path = utils.ensure_path(output_dir / task_id / f"task.{part_id}.log")

    commands = (
        f"source ~/dingping/bashrc;"
        f"nohup python -u task.py {file_path.absolute()} {gpu} > {log_path.absolute()} 2>&1 & "
        f"echo start task: {file_path.stem}, pid: $!"
    )
    # 启动一个完全独立的子进程，不依赖父进程的标准输入、输出或错误流
    _ = subprocess.Popen(
        commands,
        shell=True,
        close_fds=True,  # 关闭文件描述符
    )


def main(data_type: str, file_path: Path):
    """
    主函数：根据情报清单，拆分成多个并行任务
    """

    assert "." not in file_path.stem, "file_stem must not contain the dot ('.') splitter"

    if data_type == "bid":
        infos = get_position_by_bid([x[0] for x in tsv.read_tsv(file_path)])
    elif data_type == "point":
        infos = list(tsv.read_tsv(file_path))
    else:
        raise ValueError(f"unknown data_type '{data_type}'")

    gpu_list = [1, 2]
    # 只有 2 块 GPU，最多可以分 4 个任务，一个 GPU 上跑两个，显存够
    for i, batch in enumerate(split_list(infos, 4)):
        part_path = file_path.parent / f"{file_path.stem}.part_{i}.tsv"
        tsv.write_tsv(part_path, batch)
        emit_task(part_path, gpu_list[i % 2])


if __name__ == "__main__":
    main(sys.argv[1], Path(sys.argv[2]))
