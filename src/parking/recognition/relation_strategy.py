"""
关联停车场面到附近的 POI，基于计算出的特征（见 class Feature），判定规则见函数：eval_feature()
"""
import math
import sys
from dataclasses import dataclass, field
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Callable, Union

import shapely
from shapely import Point, wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.aoi_cleanup_strategy import road_tools
from src.parking.recognition import dbutils
from src.tools import pgsql, pipeline, tsv, linq, utils

VERSION = "v2.0.0"  # 初始化提交

QUERY_BUFFER = 50
METER_GCJ = 1e-5

desc = pipeline.get_desc()
get_connection = pgsql.get_connection_ttl


@dataclass
class Feature:
    """
    关联特征，用于决定是否可关联
    """

    # POI 与停车场面是否同属一个 AOI 内，-1 表示 POI 没有命中 AOI，>= 0 表示停车场面与 AOI 的 ioa
    with_aoi: float = -1
    # POI 与停车场面是否有内部路组连通，-1 表示周围没有内部路组/停车场面与内部路组不相交，>= 0 表示 POI 与内部路组的最短距离
    with_inner_road_group: float = math.inf
    # POI 与停车场面是否有外部路组连通，-1 表示周围没有外部路/停车场面与外部路不相交，>= 0 表示 POI 与外部路的最短距离
    # 连通的外部路之间不可有分叉点。
    with_outer_road: float = math.inf
    # POI 与停车场面的连线跨几个建筑
    cross_building: float = 0
    # POI 与停车场面的最短距离
    face_distance: float = math.inf


@dataclass
class ParkFace:
    """
    停车场面
    """

    face_id: str
    confidence: float
    geom: BaseGeometry


@dataclass
class Intermediate:
    """
    中间结果，用于传递上下文
    """

    poi: Point = None
    std_tag: str = ""
    show_tag: str = ""
    blu_faces: list[tuple[str, BaseGeometry]] = field(default_factory=list)
    bud_faces: list[tuple[str, BaseGeometry]] = field(default_factory=list)
    inner_roads: list[tuple[str, str, str, BaseGeometry]] = field(default_factory=list)
    outer_roads: list[tuple[str, str, str, BaseGeometry]] = field(default_factory=list)


@dataclass
class Context:
    """
    上下文
    """

    point: Union[str, tuple[str, str]]  # bid or (case_id, point_wkt)
    get_candidate_polygons: Callable[[str, float], tuple[str, float, str]]
    intermediate: Intermediate = field(default_factory=Intermediate)
    candidates: list[tuple[ParkFace, Feature]] = field(default_factory=list)
    result: tuple[ParkFace, Feature] = None
    error: str = ""

    @property
    def is_bid(self):
        """
        :return: 牵引信息是否为 bid，若 False，则牵引信息为 (case_id, point_wkt)
        """
        return isinstance(self.point, str)

    @property
    def case_id(self):
        """
        :return: 牵引信息的 case_id，可能为 bid 或 标记 POINT 的自定义 id
        """
        return self.point if self.is_bid else self.point[0]


# pipes:


@desc()
def resolve_if_bid(ctx: Context, proceed):
    """
    解析 POI 信息
    """
    if not ctx.is_bid:
        return proceed()

    sql = """
        select std_tag, show_tag, st_astext(geometry) from poi
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [ctx.point])
    if not ret:
        ctx.error = "not-found:poi"
        return

    std_tag, show_tag, geom = ret
    ctx.intermediate.std_tag = std_tag
    ctx.intermediate.show_tag = show_tag
    ctx.intermediate.poi = wkt.loads(geom)
    proceed()


@desc()
def resolve_if_point(ctx: Context, proceed):
    """
    解析 POINT 信息
    """
    if ctx.is_bid:
        return proceed()

    _bid, geom = ctx.point
    ctx.intermediate.poi = wkt.loads(geom)
    proceed()


@desc()
def query_blu_face(ctx: Context, proceed):
    """
    查询 AOI
    """
    sql = """
        select face_id, st_astext(geom) from blu_face
        where aoi_level in (2, 3) and src != 'SD' and st_contains(geom, st_geomfromtext(%s, 4326))
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [ctx.intermediate.poi.wkt])
    ctx.intermediate.blu_faces = [(face_id, wkt.loads(geom)) for face_id, geom in ret]
    proceed()


@desc()
def query_bud_face(ctx: Context, proceed):
    """
    查询建筑物面
    """
    sql = """
        select face_id, st_astext(geom) from bud_face
        where st_intersects(st_buffer(st_geomfromtext(%s, 4326), %s), geom)
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [ctx.intermediate.poi.wkt, QUERY_BUFFER * METER_GCJ])
    ctx.intermediate.bud_faces = [(face_id, wkt.loads(geom)) for face_id, geom in ret]
    proceed()


@desc()
def query_inner_road(ctx: Context, proceed):
    """
    查询内部路
    """
    sql = """
        select link_id, s_nid, e_nid, st_astext(geom) from nav_link
        where form = '52' and st_intersects(st_buffer(st_geomfromtext(%s, 4326), %s), geom)
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [ctx.intermediate.poi.wkt, QUERY_BUFFER * METER_GCJ])
    ctx.intermediate.inner_roads = [(link_id, s_nid, e_nid, wkt.loads(geom)) for link_id, s_nid, e_nid, geom in ret]
    proceed()


@desc()
def query_outer_road(ctx: Context, proceed):
    """
    查询外部路
    """
    sql = """
        select link_id, s_nid, e_nid, st_astext(geom) from nav_link
        where kind <= 7 and st_intersects(st_buffer(st_geomfromtext(%s, 4326), %s), geom)
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [ctx.intermediate.poi.wkt, QUERY_BUFFER * METER_GCJ])
    ctx.intermediate.outer_roads = [(link_id, s_nid, e_nid, wkt.loads(geom)) for link_id, s_nid, e_nid, geom in ret]
    proceed()


@desc()
def query_park_face(ctx: Context, proceed):
    """
    查询停车场面，并填充 ctx.candidates
    """
    ret = ctx.get_candidate_polygons(ctx.intermediate.poi.wkt, QUERY_BUFFER * METER_GCJ)
    park_faces = [ParkFace(face_id, confidence, wkt.loads(geom)) for face_id, confidence, geom in ret]
    for park_face in park_faces:
        if not park_face.geom.is_valid:
            park_face.geom = park_face.geom.buffer(0)

    park_faces = [x for x in park_faces if x.geom.is_valid]
    ctx.candidates = [(x, Feature()) for x in park_faces]
    if not ctx.candidates:
        ctx.error = "not-found:park_face_in_query_buffer"
        return

    proceed()


@desc()
def calc_with_aoi(ctx: Context, proceed):
    """
    计算停车场面是否和 POI 同属一个 AOI 内
    """
    blu_faces = ctx.intermediate.blu_faces
    if not blu_faces:
        return proceed()

    for park, feature in ctx.candidates:
        max_ioa = max(utils.calc_ioa(geom, park.geom) for face_id, geom in blu_faces)
        # 这里的 AOI 都已包含了 POI，故只需要检查 AOI 是否包含 park_face 即可。
        feature.with_aoi = max_ioa

    proceed()


@desc()
def calc_with_inner_road_group(ctx: Context, proceed):
    """
    计算是否有内部路组连通停车场面和 POI
    """
    inner_roads = ctx.intermediate.inner_roads
    poi = ctx.intermediate.poi

    road_ids = [(link_id, s_nid, e_nid) for link_id, s_nid, e_nid, geom in inner_roads]
    road_dict = {link_id: geom for link_id, s_nid, e_nid, geom in inner_roads}
    road_group_ids = list(road_tools.search_inner_road_group(road_ids))
    road_groups = [shapely.unary_union([road_dict[link_id] for link_id in link_ids]) for link_ids in road_group_ids]
    for park, feature in ctx.candidates:
        adjacent_road_groups = [x for x in road_groups if park.geom.intersects(x)]
        if not adjacent_road_groups:
            continue

        min_distance = min(poi.distance(x) for x in adjacent_road_groups)
        feature.with_inner_road_group = min_distance / METER_GCJ

    proceed()


@desc()
def calc_with_outer_road(ctx: Context, proceed):
    """
    计算是否有外部路连通停车场面和 POI
    """
    poi = ctx.intermediate.poi
    outer_roads = ctx.intermediate.outer_roads
    for park, feature in ctx.candidates:
        adjacent_roads = [geom for link_id, s_nid, e_nid, geom in outer_roads if park.geom.intersects(geom)]
        if not adjacent_roads:
            continue

        min_distance = min(poi.distance(x) for x in adjacent_roads)
        feature.with_outer_road = min_distance / METER_GCJ

    proceed()


@desc()
def calc_face_distance(ctx: Context, proceed):
    """
    计算 POI 距离停车场面的最小距离
    """
    poi = ctx.intermediate.poi
    for park, feature in ctx.candidates:
        feature.face_distance = poi.distance(park.geom) / METER_GCJ

    proceed()


@desc()
def calc_cross_building(ctx: Context, proceed):
    """
    计算 POI 与停车场面是否被建筑隔开
    """
    poi = ctx.intermediate.poi
    for park, feature in ctx.candidates:
        shortest_line = shapely.shortest_line(poi, park.geom)
        feature.cross_building = sum(
            (0.5 if geom.contains(poi) else 1) if geom.intersects(shortest_line) else 0
            for face_id, geom in ctx.intermediate.bud_faces
        )

    proceed()


@desc()
def calc_result(ctx: Context, proceed):
    """
    评估特征值，并生成最终的输出
    """
    ctx.candidates.sort(key=lambda x: x[0].confidence, reverse=True)
    ret = linq.first_or_default(ctx.candidates, lambda x: eval_feature(x[1]))
    if not ret:
        ctx.error = "recall-failed:no_valid_candidate"
        return proceed()

    ctx.result = ret
    proceed()


# helpers:


def eval_feature(f: Feature) -> bool:
    """
    CORE: 评估特征值，并最终决策是否可以关联
    """
    if f.with_aoi > 0.5:
        return f.face_distance < 30 and f.cross_building < 1

    if f.with_inner_road_group < 10 or f.with_outer_road < 10:
        return f.cross_building < 0.5

    return f.face_distance < 1


def get_recognition_polygons(point_wkt: str, buffer: float) -> tuple[str, float, str]:
    """
    根据给定的 POINT 和搜索范围，返回作为匹配候选的停车场识别面
    """
    sql = """
        select face_id, confidence, st_astext(geom)
        from recognition_parking_result
        where confidence >= 0.2 and st_dwithin(%s, geom, %s);
    """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql, [f"SRID=4326;{point_wkt}", buffer])


PIPE = pipeline.Pipeline(
    resolve_if_bid,
    resolve_if_point,
    query_park_face,
    query_blu_face,
    query_bud_face,
    query_inner_road,
    query_outer_road,
    calc_face_distance,
    calc_with_aoi,
    calc_with_inner_road_group,
    calc_with_outer_road,
    calc_cross_building,
    calc_result,
)
desc.attach(PIPE)
desc.output = lambda x: None


def execute_once(item: Union[str, tuple[str, str]]) -> Context:
    """
    处理单个 POI，主要用于多进程并行
    """
    ctx = Context(item, get_recognition_polygons)
    PIPE(ctx)
    return ctx


# TODO: 下面这几个 output_* 函数写的真是糟糕，和文件系统耦合在了一起，没有考虑入库需求，后续需要重构：抽象输入输出


def output_relation_info(save_path: Path, items: list[Union[str, tuple[str, str]]]):
    """
    导出结果到本地文件
    """

    error_path = utils.ensure_path(save_path.parent / f"{save_path.stem}.error.tsv")
    with Pool(8) as pool:
        for ctx in tqdm(pool.imap(execute_once, items), total=len(items)):
            if ctx.error:
                # 错误要另外记录，因为错误是针对这个 bid 为什么没挂上的，而数据库是针对停车场面的
                error = [ctx.case_id, ctx.error]
                tsv.write_tsv(error_path, [error], mode="a")
                continue

            park_face, feature = ctx.result
            success = [ctx.case_id, park_face.face_id]
            tsv.write_tsv(save_path, [success], mode="a")


def output_eval_info(result_path: Path):
    """
    输出评估信息：包含 poi 和面的信息
    """
    save_path = utils.ensure_path(result_path.parent / f"{result_path.stem}.eval.tsv")
    success_cases = list(tsv.read_tsv(result_path))
    sql_bid = """
        select bid, name, std_tag, show_tag, st_astext(geometry) from poi
        where bid in %s;
    """
    sql_face_id = """
        select face_id, confidence, batch, case_id, st_astext(geom) from recognition_parking_result
        where face_id in %s;
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql_bid, [tuple(x[0] for x in success_cases)])
    bid_infos = {x[0]: x for x in ret}
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql_face_id, [tuple(x[1] for x in success_cases)])
    face_id_infos = {x[0]: x for x in ret}

    for bid, face_id in success_cases:
        if bid not in bid_infos or face_id not in face_id_infos:
            continue

        bid_info = bid_infos[bid]
        face_id_info = face_id_infos[face_id]

        point_geom = wkt.loads(bid_info[-1])
        face_geom = wkt.loads(face_id_info[-1])
        distance = point_geom.distance(face_geom) / METER_GCJ

        info = [*bid_info, distance, *face_id_info]
        tsv.write_tsv(save_path, [info], mode="a")


def output_eval_info_only_face(result_path: Path):
    """
    输出评估信息：仅包含面的信息
    """
    save_path = utils.ensure_path(result_path.parent / f"{result_path.stem}.eval.tsv")
    success_cases = list(tsv.read_tsv(result_path))
    sql_face_id = """
        select face_id, confidence, batch, case_id, st_astext(geom) from recognition_parking_result
        where face_id in %s;
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql_face_id, [tuple(x[1] for x in success_cases)])
    face_id_infos = {x[0]: x for x in ret}

    for bid, face_id in success_cases:
        if face_id not in face_id_infos:
            continue

        face_id_info = face_id_infos[face_id]

        info = [*bid.split("_"), *face_id_info]
        tsv.write_tsv(save_path, [info], mode="a")


def main(file_path: Path):
    """
    主函数
    """
    items = list(tsv.read_tsv(file_path))
    result_path = utils.ensure_path(file_path.parent / f"{file_path.stem}.match_v2.tsv")
    output_relation_info(result_path, items)
    output_eval_info_only_face(result_path)


if __name__ == "__main__":
    main(Path(sys.argv[1]))
