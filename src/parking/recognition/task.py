"""
代表一个完整的识别任务
"""
import sys
from dataclasses import dataclass, field
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Callable, Optional

import numpy as np
import psycopg2.extras
from loguru import logger
from matplotlib import pyplot as plt
from shapely import wkt
from tqdm import tqdm

from src.parking.recognition import dataset_draw, dataset_region, relation_strategy
from src.seg_post_process import contexts as vector_contexts, main as vector_entry
from src.tools import pgsql, pipeline, tsv, utils, function, linq

MODEL_NAME = "parking-segformer_b3-3plus5_channels_20240806"
METER = 1e-5
desc = pipeline.get_desc()


@dataclass
class Directory:
    """
    目录结构
    """

    output_dir: Path
    part_id: str
    info_dir: Path = field(init=False)
    region_path: Path = field(init=False)

    dataset_dir: Path = field(init=False)
    prod_path: Path = field(init=False)

    result_dir: Path = field(init=False)
    vectorize_dir: Path = field(init=False)
    vector_path: Path = field(init=False)
    product_path: Path = field(init=False)

    def __post_init__(self):
        self.info_dir = self.output_dir / "info"
        self.region_path = utils.ensure_path(self.info_dir / f"region.{self.part_id}.tsv", cleanup=False)

        self.dataset_dir = self.output_dir / "dataset"
        self.prod_path = utils.ensure_path(self.dataset_dir / f"prod.{self.part_id}.txt")

        self.result_dir = self.output_dir / "result"
        self.vectorize_dir = self.result_dir / "output_vectorize"
        self.vector_path = utils.ensure_path(self.result_dir / f"vector.{self.part_id}.tsv")
        self.product_path = utils.ensure_path(self.result_dir / f"product.{self.part_id}.tsv")


@dataclass
class Context:
    """
    上下文
    """

    task_id: str
    part_id: str
    infos: list[tuple[str, str]]  # info_id, info_geom
    paths: Directory

    __region_ids: Optional[set[str]] = field(init=False, default=None)

    @property
    def region_ids(self):
        """
        :return: 识别区域标识集合
        """
        if self.__region_ids is None:
            self.__region_ids = {x[-2] for x in tsv.read_tsv(self.paths.region_path)}

        return self.__region_ids


# pipes:


@desc()
def export_info_to_db(ctx: Context, proceed):
    """
    pipe: 将情报信息导出至数据库
    """
    sql = """
         INSERT INTO recognition_parking_info (info_id, geom, batch)
         VALUES %s
         ON CONFLICT (info_id, batch, geom) DO NOTHING;
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cursor:
        args = [(info_id, f"SRID=4326;{geom}", ctx.task_id) for info_id, geom in ctx.infos]
        psycopg2.extras.execute_values(cursor, sql, args, page_size=10_000)

    proceed()


@desc()
def export_info_to_local(ctx: Context, proceed):
    """
    pipe: 将情报信息导出至本地 tsv 文件
    """
    tsv.write_tsv(ctx.paths.info_dir / f"point.{ctx.part_id}.tsv", ctx.infos)
    proceed()


@desc()
def make_region(ctx: Context, proceed):
    """
    pipe: 根据情报信息生成识别所需的区域，并导出至 tsv 文件
    """
    if ctx.paths.region_path.exists():
        return proceed()

    def check_size(area: float):
        return 128**2 <= area <= 2304**2

    generate_region = pipeline.Pipeline(
        dataset_region.try_street_region,
        dataset_region.buffer(30),
        dataset_region.break_if_done(check_size),
        dataset_region.try_blu_face_region,
        dataset_region.buffer(50),
        dataset_region.break_if_done(check_size),
        dataset_region.try_buffer_region,
    )
    tasks = (dataset_region.Context(info_id=info_id, info_geom=wkt.loads(point)) for info_id, point in ctx.infos)
    for task in tqdm(tasks, total=len(ctx.infos)):
        generate_region(task)
        if task.region:
            row = [task.info_id, task.info_geom.wkt, task.region.case_id, task.region.geom]
            tsv.write_tsv(ctx.paths.region_path, [row], mode="a")

    proceed()


@desc()
def make_dataset(ctx: Context, proceed):
    """
    pipe: 根据给定的区域信息，生成识别所需的多模态数据集
    """
    # 去重：case_id 与 case_geom 对应，作为 region 的唯一标识，多个情报点可能产生相同的 region
    regions = {(x[-2], x[-1]) for x in tsv.read_tsv(ctx.paths.region_path)}
    # 断点恢复：
    done_case_ids = dataset_draw.get_completed_case_ids(ctx.paths.dataset_dir)
    undo_case_ids = {case_id for case_id, _ in regions if case_id not in done_case_ids}
    # tasks 必须用生成器！！！因为 list 会储存 Context 的引用，导致已使用完毕的 Context 不会被释放，1000 张图能占用 20+GB 的内存！！！
    tasks = (
        dataset_draw.Context(output_dir=ctx.paths.dataset_dir, case_id=case_id, case_geom=case_geom, labels=[])
        for case_id, case_geom in regions
        if case_id in undo_case_ids
    )
    for task in tqdm(tasks, total=len(undo_case_ids)):
        dataset_draw.draw_pipe(task)

    proceed()


def make_prod_file(image_name: str, label_name: str):
    """
    pipe: 生成 prod.txt 文件，供给 paddleseg 读取
    """

    @desc("split tasks")
    def pipe(ctx: Context, proceed):
        dataset_dir = ctx.paths.dataset_dir
        image_dir = dataset_dir / image_name
        other_folders = [
            x.stem
            for x in dataset_dir.iterdir()
            if x.is_dir() and x.stem not in (image_name, label_name) and not x.stem.startswith("_")
        ]
        other_folders.sort()
        paths = [x for x in image_dir.glob("*.jpg")]
        paths.sort(key=lambda x: x.stat().st_size)
        names = [x.stem for x in paths]
        # 只选取本次任务中的图片进行矢量化
        names = [x for x in names if x in ctx.region_ids]
        items = [
            (f"{image_name}/{x}.jpg", *[f"{folder}/{x}.png" for folder in other_folders], f"{label_name}/{x}.png")
            for x in names
        ]
        tsv.write_tsv(ctx.paths.prod_path, items)
        proceed()

    return pipe


def recognize(gpu: int):
    """
    pipe: 调用 paddleseg 进行识别
    """

    @desc(f"recognize in gpu: {gpu}")
    def pipe(ctx: Context, proceed):
        emit_recognition_task(image_path=ctx.paths.prod_path, save_dir=ctx.paths.result_dir, gpu=gpu)
        proceed()

    return pipe


@desc()
def vectorize(ctx: Context, proceed):
    """
    pipe: 将识别结果矢量化
    """
    image_type = "heatmap-adaptive-background"
    path_info = vector_contexts.PathInfo(
        image_dir=ctx.paths.result_dir / "heatmap_prediction" / "0" / "image",
        image_info_dir=ctx.paths.dataset_dir / "_json_info" / "image",
        output_dir=ctx.paths.vectorize_dir,
    )
    path_info.ensure_dirs(vector_entry.ENABLE_DEBUG)
    feed_image_paths = vector_entry.get_feed_image_paths(path_info)

    # 只选取本次任务中的图片进行矢量化
    feed_image_paths = [x for x in feed_image_paths if x.stem in ctx.region_ids]

    def generate_data():
        """
        生成并发任务包
        """
        for feed_image_path in feed_image_paths:
            image_info = vector_contexts.ImageInfo()
            image_info.image_type = image_type
            image_info.image_path = feed_image_path

            c = vector_contexts.Context()
            c.enable_debug = vector_entry.ENABLE_DEBUG
            c.task_id = feed_image_path.stem
            c.path_info = path_info
            c.image_info = image_info
            yield c

    with Pool(8) as p:
        _ = list(tqdm(p.imap_unordered(vector_entry.try_process, generate_data()), total=len(feed_image_paths)))

    # DEBUG: 此处用于 debug 执行，debug 时为了方便打断点，不使用多进程并发。
    # for context in tqdm(generate_data(), total=len(feed_image_paths)):
    #     MAIN_PIPELINE(context)
    proceed()


@desc()
def export_vectors(ctx: Context, proceed):
    """
    pipe: 将识别结果导出至 tsv 文件
    """

    vectors_dir = ctx.paths.vectorize_dir / "vectorize_info"
    vectors = (utils.read_json(x) for x in vectors_dir.glob("*.json") if x.stem in ctx.region_ids)
    features = ((x["id"], f["id"], f["tags"][0], f["geom"]) for x in vectors for f in x["features"])
    features = sorted(features, key=lambda x: x[2], reverse=True)

    group = linq.group_by(features, lambda x: x[1])
    features = [x[0] for x in group.values()]
    tsv.write_tsv(ctx.paths.vector_path, features)

    proceed()


def export_confidence_histogram(provider: Callable[[Context], tuple[Path, list[float]]]):
    """
    pipe: 导出识别置信度直方图
    """

    @desc("export confidence histogram")
    def pipe(ctx: Context, proceed):
        save_path, data = provider(ctx)
        draw_histogram(save_path, ctx.task_id, data, threshold=0.3)
        proceed()

    return pipe


@desc()
def export_vectors_to_db(ctx: Context, proceed):
    """
    pipe: 将识别结果写入数据库
    """
    sql = """
         INSERT INTO recognition_parking_result (face_id, case_id, confidence, geom, batch, image_version, model)
         VALUES %s
         ON CONFLICT (face_id) DO NOTHING;
    """
    vectors = tsv.read_tsv(ctx.paths.vector_path)
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cursor:
        args = [
            (
                face_id,
                case_id,
                float(confidence),
                f"SRID=4326;{geom}",
                ctx.task_id,
                dataset_draw.IMAGE_VERSION,
                MODEL_NAME,
            )
            for case_id, face_id, confidence, geom in vectors
        ]
        psycopg2.extras.execute_values(cursor, sql, args, page_size=10_000)

    proceed()


def match_simply(distance: float):
    """
    pipe: 匹配情报点和识别面，仅使用距离进行匹配
    """
    strategy_name = f"distance_{distance}m"
    sql_query = """
        with info as (
            select id, info_id, geom as point, st_buffer(geom, %s) as buffer from recognition_parking_info
            where batch = %s
        )
        select
            b.info_id, a.case_id, a.face_id, a.model,
            a.confidence, st_distance(b.point::geography, a.geom::geography),
            st_astext(b.point), st_astext(a.geom),
            b.id
        from recognition_parking_result a
        inner join info b on a.geom && b.buffer and st_intersects(a.geom, b.buffer);
    """
    sql_insert = """
        INSERT INTO recognition_parking_product (info_id, face_id, batch, relation_strategy)
        VALUES %s
        ON CONFLICT (info_id, face_id, batch) DO NOTHING;
    """

    @desc(f"match simply: {distance=}")
    def pipe(ctx: Context, proceed):
        with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
            ret = pgsql.fetch_all(conn, sql_query, [distance * METER, ctx.task_id])
            tsv.write_tsv(ctx.paths.product_path, [x[:-1] for x in ret])

        with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cursor:
            args = [(x[-1], x[2], ctx.task_id, strategy_name) for x in ret]
            psycopg2.extras.execute_values(cursor, sql_insert, args, page_size=10_000)

        proceed()

    return pipe


@desc()
def match_v2(ctx: Context, proceed):
    """
    pipe: 匹配情报点和识别面，使用 v2 策略，这个只能用于补面，不能用于新增
    """
    bids = [x[0] for x in ctx.infos]
    relation_strategy.output_relation_info(ctx.paths.product_path, bids)
    if ctx.paths.product_path.exists():
        relation_strategy.output_eval_info(ctx.paths.product_path)

    proceed()


# helpers:


def emit_recognition_task(image_path: Path, save_dir: Path, model_name=MODEL_NAME, gpu: int = 0):
    """
    使用 paddleseg 模型进行识别
    """
    working_directory = "/home/<USER>/dingping/building_research/models/PaddleSeg2.7_MultiChannel"
    log_path = save_dir.parent / f"{image_path.stem}.log"
    commands = f"""
        source ~/chenbaojun/bashrc;
        export CUDA_VISIBLE_DEVICES={gpu};
        python -u predict.py \
            --config configs/segformer/segformer_b3_parking_20240806.yml \
            --model_path output/{model_name}/best_model/model.pdparams \
            --image_path {image_path.absolute()} \
            --save_dir {save_dir.absolute()} > {log_path.absolute()} 2>&1;
    """
    return_code, _ = function.exec_shell_cmd(commands, cwd=working_directory)
    if return_code != 0:
        raise Exception(f"predict_vehicles failed, return code: {return_code}")


def draw_histogram(save_path: Path, name: str, data: list[float], step: float = 0.1, threshold=0.2):
    """
    绘制识别置信度直方图
    """
    if not data:
        return

    bins = np.arange(0, 1, step)
    qualified = sum(1 for x in data if x >= threshold) / len(data)

    plt.hist(data, bins=bins, density=False)
    plt.axvline(x=threshold, color="red", linestyle="--")

    plt.xlabel("confidence")
    plt.ylabel("count")
    plt.title(f"{name} (qualified: {qualified*100:.2f}%)")
    plt.xlim(0, 1)

    plt.savefig(save_path)
    plt.close()


def vectors_histogram_provider(ctx: Context):
    """
    提供绘制 confidence-vectors 直方图的信息
    """
    data = [float(x[2]) for x in tsv.read_tsv(ctx.paths.vector_path)]
    return ctx.paths.result_dir / f"confidence-vectors.{ctx.part_id}.jpg", data


@logger.catch
def main(file_path: Optional[Path], gpu: int):
    """
    主函数
    """
    output_dir = Path("output")
    pipe = pipeline.Pipeline(
        export_info_to_db,
        export_info_to_local,
        make_region,
        make_dataset,
        make_prod_file(image_name="image", label_name="label_v2"),
        recognize(gpu),
        vectorize,
        export_vectors,
        export_confidence_histogram(vectors_histogram_provider),
        export_vectors_to_db,
        # match_simply(distance=20),
        match_v2,
    )
    desc.attach(pipe)

    task_id, part_id = file_path.stem.split(".")
    ctx = Context(
        task_id=task_id,
        part_id=part_id,
        infos=list(tsv.read_tsv(file_path)),
        paths=Directory(output_dir=output_dir / task_id, part_id=part_id),
    )
    pipe(ctx)


if __name__ == "__main__":
    main(Path(sys.argv[1]), int(sys.argv[2]))
