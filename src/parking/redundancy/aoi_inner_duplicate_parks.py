"""
AOI内重复挖掘
* 位置相近的临时
* 同一父节点AOI下(不包括购物中心)，空间属性相同，开放属性相同(地上/立体/停车场)
* 同名+同空间属性+同开放属性+位置相近的停车场

* 同一根节点AOI下，空间属性相同，开放属性相同，子点std_tag前缀一样(需要工艺确认是否可下)
* A停车场父点是B停车场根节点(不是父点)，空间属性相同，开放属性相同,子点std_tag前缀一样(先评估场景)

* 同空间属性+同开放属性+无父点+内部路连通+距离100m(先评估场景)
* 同一AOI范围下，空间属性相同、开放属性相同停车场(先评估场景)
"""

import shapely.wkt
import tqdm as tqdm
import logging
import multiprocessing
import json
import sys
import multiprocessing
import re
import traceback

from functools import partial
from dataclasses import dataclass, field, asdict
from typing import List, Tuple
from src.parking import redundancy
from collections import defaultdict
from shapely.geometry import Point, LineString

PROCESS_WORKERS: int = 30
HIGH_LEVEL_ROAD_KIND: int = 8

# 添加日志配置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", stream=sys.stdout)
logger = logging.getLogger(__name__)


@dataclass
class DuplicatePark:
    """
    重复停车场信息
    """

    park_bid: str = field(default="", init=False)
    park_name: str = field(default="", init=False)
    dup_park_bid: str = field(default="", init=False)
    park_click_pv: str = field(default="", init=False)
    dup_park_click_pv: str = field(default="", init=False)
    dup_park_name: str = field(default="", init=False)
    show_tag: str = field(default="", init=False)
    memo: str = field(default="", init=False)
    two_park_dist: float = field(default=0, init=False)
    is_parent_id_same: bool = field(default=False, init=False)
    aoi_std_tag: str = field(default="", init=False)
    gap_line_across_high_road: bool = field(default=False, init=False)  # 两停车场连线是否跨高等级道路
    is_two_parks_inner_road_conn: bool = field(default=False, init=False)


def run():
    """
    run
    """
    # duplicate_parks = near_tmp_parks()
    # logger.info("near_tmp_parks success")

    duplicate_parks = same_parent_aoi_parks_com()
    logger.info("same_parent_aoi_parks_com success")

    # duplicate_parks += same_name_and_near_parks_com()
    # logger.info("same_name_and_near_parks_com success")

    # duplicate_parks += same_root_aoi_parks_com()
    # logger.info("same_root_aoi_parks_com success")

    # duplicate_parks = same_aoi_area_parks_com()
    # logger.info("same_aoi_area_parks_com success")

    # duplicate_parks = root_parent_extend_parks()
    # logger.info("root_parent_extend_parks success")

    duplicate_parks += inner_road_conn_parks_com()
    logger.info("inner_road_conn_parks success")

    duplicate_parks = filter_repeat(duplicate_parks)
    logger.info("filter_repeat success")

    duplicate_parks = filter_error_duplicate_parks(duplicate_parks)
    logger.info("filter_error_duplicate_parks success")

    _save_result_to_review(duplicate_parks, "./aoi_inner_dup_parks.csv")
    logger.info("_save_result_to_review success")


def is_two_parks_inner_road_conn(park_bid, dup_park_bid):
    """
    内部路是否连通
    """
    park_data = redundancy.get_parking_data(park_bid)
    dup_park_data = redundancy.get_parking_data(dup_park_bid)

    park_point_wkt = park_data[0]["gcj_geom"]
    dup_park_point_wkt = dup_park_data[0]["gcj_geom"]
    park_area = None
    if not park_area:
        park_area = shapely.wkt.loads(park_point_wkt).buffer(30 / 110000).wkt

    dup_park_area = None
    if not dup_park_area:
        dup_park_area = shapely.wkt.loads(dup_park_point_wkt).buffer(30 / 110000).wkt

    aoi = redundancy.get_aoi_info(park_data[0]["parent_id"])
    if len(aoi) == 0:
        point1 = shapely.wkt.loads(park_point_wkt)
        point2 = shapely.wkt.loads(dup_park_point_wkt)
        line = LineString([point1, point2])
        aoi_wkt = line.buffer(100 / 110000).wkt
    else:
        aoi_wkt = aoi[0]["geom"]

    if is_inner_road_conn(park_point_wkt, dup_park_point_wkt, aoi_wkt, park_area, dup_park_area):
        print(park_bid, dup_park_bid, "TRUE")
        return True
    else:
        print(park_bid, dup_park_bid, "FALSE")
    return False


def test(park_bid, dup_park_bid):
    """
    test
    """
    park_data = redundancy.get_parking_data(park_bid)
    dup_park_data = redundancy.get_parking_data(dup_park_bid)

    park_point_wkt = park_data[0]["gcj_geom"]
    dup_park_point_wkt = dup_park_data[0]["gcj_geom"]
    park_area = None
    if not park_area:
        park_area = shapely.wkt.loads(park_point_wkt).buffer(30 / 110000).wkt

    dup_park_area = None
    if not dup_park_area:
        dup_park_area = shapely.wkt.loads(dup_park_point_wkt).buffer(30 / 110000).wkt

    aoi = redundancy.get_aoi_info(park_data[0]["parent_id"])
    if len(aoi) == 0:
        point1 = shapely.wkt.loads(park_point_wkt)
        point2 = shapely.wkt.loads(dup_park_point_wkt)
        line = LineString([point1, point2])
        aoi_wkt = line.buffer(100 / 110000).wkt
    else:
        aoi_wkt = aoi[0]["geom"]

    if is_inner_road_conn(park_point_wkt, dup_park_point_wkt, aoi_wkt, park_area, dup_park_area):
        print(park_bid, dup_park_bid, True)
    else:
        print(park_bid, dup_park_bid, False)


def same_parent_aoi_parks_com():
    """
    同一父节点AOI下(不包括购物中心)，空间属性相同，开放属性相同(地上/立体/停车场)
    """
    # duplicate_parks = same_parent_aoi_parks("地下停车场")
    # logger.info("same_parent_aoi_parks 地下 success")

    duplicate_parks = same_parent_aoi_parks("地上停车场")
    logger.info("same_parent_aoi_parks 地上 success")

    duplicate_parks += same_parent_aoi_parks("停车场")
    logger.info("same_parent_aoi_parks 地上 success")

    # duplicate_parks += same_parent_aoi_parks("立体停车场")
    # logger.info("same_parent_aoi_parks 地上 success")

    return duplicate_parks


def same_name_and_near_parks_com():
    """
    同名+同空间属性+同开放属性+位置相近的停车场
    """
    duplicate_parks = same_name_and_near_parks("地下停车场")
    logger.info("same_name_and_near_parks success")

    duplicate_parks += same_name_and_near_parks("停车场")
    logger.info("same_name_and_near_parks success")

    duplicate_parks += same_name_and_near_parks("立体停车场")
    logger.info("same_name_and_near_parks success")

    duplicate_parks += same_name_and_near_parks("门前停车场")
    logger.info("same_name_and_near_parks success")

    duplicate_parks += same_name_and_near_parks("地上停车场")
    logger.info("same_name_and_near_parks success")

    duplicate_parks += same_name_and_near_parks("临时停车点")
    logger.info("same_name_and_near_parks success")

    return duplicate_parks


def same_root_aoi_parks_com():
    """
    同一根节点AOI下，空间属性相同，开放属性相同，子点std_tag前缀一样(需要工艺确认是否可下)
    """
    duplicate_parks = same_root_aoi_parks("地上停车场")
    logger.info("same_root_aoi_parks 地上 success")

    duplicate_parks += same_root_aoi_parks("地下停车场")
    logger.info("same_root_aoi_parks 地下 success")

    duplicate_parks += same_root_aoi_parks("停车场")
    logger.info("same_root_aoi_parks 地下 success")

    duplicate_parks += same_root_aoi_parks("立体停车场")
    logger.info("same_root_aoi_parks 地下 success")

    return duplicate_parks


def same_aoi_area_parks_com():
    """
    同一AOI范围下，空间属性相同、开放属性相同停车场(先评估场景)
    """
    duplicate_parks = same_aoi_area_parks()
    logger.info("same_aoi_area_parks success")
    return duplicate_parks


def filter_error_duplicate_parks(duplicate_parks):
    """
    过滤错误重复场景
    """
    parks = []
    for dup_park in duplicate_parks:
        # 两停车场连线是否跨高等级道路
        dup_park.gap_line_across_high_road = _is_two_parks_across_high_road(dup_park.park_bid, dup_park.dup_park_bid)
        if dup_park.gap_line_across_high_road:
            continue

        # 两停车场名称包含PX
        if re.search(r"P[\da-zA-Z]+", dup_park.dup_park_name, re.IGNORECASE) or re.search(
            r"P[\da-zA-Z]+", dup_park.park_name, re.IGNORECASE
        ):
            continue

        # 包含 "大巴" 或 "[A-Z]区"（如：A区、B区...Z区）
        if (
            re.search(r"大巴", dup_park.dup_park_name)
            or re.search(r"大巴", dup_park.park_name)
            or re.search(r"[A-Z]区", dup_park.dup_park_name, re.IGNORECASE)
            or re.search(r"[A-Z]区", dup_park.park_name, re.IGNORECASE)
        ):
            continue

        # 酒店场景不做
        if "酒店" in dup_park.aoi_std_tag:
            continue

        parks.append(dup_park)
    return parks


def filter_repeat(parks: List[DuplicatePark]):
    """
    过滤重复数据
    """
    result = []
    is_put = {}
    for park in tqdm.tqdm(parks):
        is_put[park.park_bid + park.dup_park_bid] = False
    for park in tqdm.tqdm(parks):
        com1 = park.park_bid + park.dup_park_bid
        com2 = park.dup_park_bid + park.park_bid
        if is_put.get(com1, False) or is_put.get(com2, False):
            continue
        result.append(park)
        is_put[com1] = True
        is_put[com2] = True
    return result


def same_parent_aoi_parks(show_tag):
    """
    同一父节点AOI下，空间属性相同，开放属性相同 (多进程，带进度条)
    """
    result: List[DuplicatePark] = []
    park_list = redundancy.get_all_parking_by_show_tag(show_tag)
    args = [(park, show_tag) for park in park_list]

    with multiprocessing.Pool(processes=PROCESS_WORKERS) as pool:
        with tqdm.tqdm(total=len(park_list)) as pbar:
            for sublist in pool.imap_unordered(_unpack_process_same_parent_park, args):
                result.extend(sublist)
                pbar.update(1)

    return result


def _unpack_process_same_parent_park(args):
    # 单参数解包函数，供 imap_unordered 使用
    return process_same_parent_park(*args)


def process_same_parent_park(park, show_tag):
    """
    同一父节点AOI下，空间属性相同，开放属性相同(单进程)
    """
    result = []
    bid = park["bid"]
    try:
        parent_bid_res = redundancy.get_parent_bid(bid)
        if not parent_bid_res:
            return result
        parent_bid = parent_bid_res[0]
        if parent_bid in (0, "0", None):
            return result

        aoi_info = redundancy.get_aoi_info(parent_bid)
        if not aoi_info:
            return result

        aoi_poi_res = redundancy.get_poi_info(aoi_info[0]["poi_bid"])
        if not aoi_poi_res:
            return result
        aoi_std_tag = aoi_poi_res[0]["std_tag"]

        # 四大垂类，除去医院，都不做
        park_main_poi_res = redundancy.get_park_main_poi_tag(bid)
        if (
            park_main_poi_res
            and park_main_poi_res[0]["is_4categories"] is True
            and ("医疗" not in park_main_poi_res[0]["root_std_tag"])
        ):
            return result

        park_data_res = redundancy.get_park_data(bid)
        if not park_data_res:
            return result
        park_data = park_data_res[0]
        dup_parks = redundancy.get_same_parent_bid_parks(parent_bid)
        for dup in dup_parks:
            dup_bid = dup["bid"]
            if dup_bid == bid:
                continue

            dup_show_tag = dup["show_tag"]
            is_show_tag_same = False
            if dup_show_tag == show_tag:
                is_show_tag_same = True

            if dup_show_tag == "地上停车场" and show_tag in ["停车场", "临时停车点"]:
                is_show_tag_same = True

            if dup_show_tag == "停车场" and show_tag in ["地上停车场", "临时停车点"]:
                is_show_tag_same = True

            if not is_show_tag_same:
                continue

            if dup["open_limit_new"] in ("-1", "14") or dup["open_limit_new"] is None:
                dup["open_limit_new"] = "0"

            if park_data["open_limit_new"] in ("-1", "14") or park_data["open_limit_new"] is None:
                park_data["open_limit_new"] = "0"

            if dup["open_limit_new"] != park_data["open_limit_new"]:
                continue

            two_park_dist = (
                shapely.wkt.loads(dup["gcj_geom"]).distance(shapely.wkt.loads(park_data["gcj_geom"])) * 110000
            )

            if two_park_dist > 110:
                continue

            is_inner_road_connect = False
            if is_two_parks_inner_road_conn(bid, dup_bid):
                is_inner_road_connect = True

            if show_tag != "地下停车场" and not is_inner_road_connect:
                return result

            item = DuplicatePark()
            item.is_parent_id_same = dup["parent_id"] == park_data["parent_id"]
            item.park_bid = bid
            item.dup_park_bid = dup_bid
            item.show_tag = show_tag
            item.dup_park_name = dup["name"]
            item.park_name = park_data["name"]
            item.memo = f"关联同一父节点AOI，都是{show_tag}，开放属性一样"
            item.two_park_dist = two_park_dist
            item.aoi_std_tag = aoi_std_tag
            item.is_two_parks_inner_road_conn = is_inner_road_connect

            dup_park_poi_res = redundancy.get_poi_info(dup_bid)
            if len(dup_park_poi_res) > 0:
                item.dup_park_click_pv = dup_park_poi_res[0]["click_pv"]

            park_poi_res = redundancy.get_poi_info(bid)
            if len(park_poi_res) > 0:
                item.park_click_pv = park_poi_res[0]["click_pv"]

            result.append(item)
    except Exception as e:
        print(f"Error processing bid={bid}: {e}")
        traceback.print_exc()
    return result


def same_aoi_area_parks() -> List[DuplicatePark]:
    """
    同一AOI范围下，空间属性相同、开放属性相同停车场（多进程）
    """
    result: List[DuplicatePark] = []
    aoi_list = redundancy.get_all_aoi_list()

    with multiprocessing.Pool(processes=PROCESS_WORKERS) as pool:
        for sublist in tqdm.tqdm(pool.imap_unordered(process_same_aoi_area_parks, aoi_list), total=len(aoi_list)):
            result.extend(sublist)

    return result


def process_same_aoi_area_parks(aoi: dict) -> List[DuplicatePark]:
    """
    子进程任务：处理一个 AOI 的所有停车场，判断空间 + 开放属性相同的组
    """
    result = []
    try:
        poi_bid, aoi_wkt = aoi["poi_bid"], aoi["aoi_wkt"]
        dup_parks = redundancy.get_parks_by_area(aoi_wkt)

        dup_parks_group = defaultdict(list)
        for d_park in dup_parks:
            bid = d_park["bid"]
            open_limit_new = d_park["open_limit_new"]
            show_tag = d_park["show_tag"]
            key = (open_limit_new, show_tag)
            dup_parks_group[key].append(bid)

        for key, bid_list in dup_parks_group.items():
            if len(bid_list) < 2:
                continue  # 不构成重复

            dup_park = DuplicatePark()
            dup_park.park_bid = bid_list[0]  # 任意选一个代表
            dup_park.dup_park_bid = ",".join(bid_list[1:])
            dup_park.memo = "同一AOI范围下，空间属性相同、开放属性相同停车场"
            dup_park.show_tag = key[1]
            dup_park.is_parent_id_same = False
            result.append(dup_park)
    except Exception as e:
        print(f"[ERROR] AOI={aoi.get('poi_bid')} -> {e}")
        traceback.print_exc()
    return result


def same_root_aoi_parks(show_tag) -> List[DuplicatePark]:
    """
    同一根节点AOI下，空间属性相同，开放属性相同（使用多进程 + tqdm）
    """
    result: List[DuplicatePark] = []
    park_list = redundancy.get_all_parks_by_show_tag(show_tag)

    # 打包参数给子函数
    args = [(park, show_tag) for park in park_list]

    with multiprocessing.Pool(processes=PROCESS_WORKERS) as pool:
        for sublist in tqdm.tqdm(pool.imap_unordered(process_same_root_aoi_park, args), total=len(args)):
            result.extend(sublist)

    return result


def process_same_root_aoi_park(args):
    """
    子进程任务：同一根节点AOI下，空间属性相同，开放属性相同
    """
    park, show_tag = args
    result = []
    try:
        bid = park["bid"]
        root_bid_res = redundancy.get_root_bid(bid)
        if not root_bid_res:
            return result
        root_bid = root_bid_res[0]
        if root_bid in (0, "0", None):
            return result

        # 非基础院落AOI不处理
        aoi_info = redundancy.get_aoi_info(root_bid)
        if not aoi_info:
            return result

        park_data_res = redundancy.get_park_data(bid)
        if not park_data_res:
            return result
        park_data = park_data_res[0]

        dup_parks = redundancy.get_same_root_bid_parks(root_bid)
        for p in dup_parks:
            dup_bid = p["bid"]
            if dup_bid == bid:
                continue

            dup_park_data_res = redundancy.get_park_data(dup_bid)
            if not dup_park_data_res:
                continue
            dup_park_data = dup_park_data_res[0]

            if dup_park_data["show_tag"] != show_tag:
                continue

            if dup_park_data["open_limit_new"] in ("-1", None):
                dup_park_data["open_limit_new"] = "0"
            if park_data["open_limit_new"] in ("-1", None):
                park_data["open_limit_new"] = "0"

            if dup_park_data["open_limit_new"] != park_data["open_limit_new"]:
                continue

            dup_park = DuplicatePark()
            dup_park.is_parent_id_same = dup_park_data["parent_id"] == park_data["parent_id"]
            dup_park.park_bid = bid
            dup_park.dup_park_bid = dup_bid
            dup_park.show_tag = show_tag
            dup_park.memo = f"关联同一根节点AOI，都是{show_tag}，开放属性一样"
            result.append(dup_park)

    except Exception as e:
        print(f"[ERROR] park_bid={park.get('bid')} -> {e}")
        traceback.print_exc()
    return result


def root_parent_extend_parks() -> List[DuplicatePark]:
    """
    A停车场父点是B停车场根节点(不是父点)，空间属性相同，开放属性相同 (多进程 + tqdm + multiprocessing.Pool)
    """
    result: List[DuplicatePark] = []
    park_list = redundancy.get_all_parks()

    with multiprocessing.Pool(processes=PROCESS_WORKERS) as pool:
        for sublist in tqdm.tqdm(pool.imap_unordered(process_root_parent_extend_park, park_list), total=len(park_list)):
            result.extend(sublist)

    return result


def process_root_parent_extend_park(park):
    """
    子任务处理函数：A停车场父点是B停车场根节点(不是父点)，空间属性相同，开放属性相同
    """
    result = []
    try:
        bid, open_limit_new, show_tag = park["bid"], park["open_limit_new"], park["show_tag"]

        parent_bid_res = redundancy.get_parent_bid(bid)
        if not parent_bid_res:
            return result
        parent_bid = parent_bid_res[0]
        if parent_bid in ("0", 0, None):
            return result

        dup_parks = redundancy.get_same_root_bid_parks(parent_bid)
        for d_park in dup_parks:
            d_bid = d_park["bid"]
            if d_bid == bid:
                continue

            d_parent_bid_res = redundancy.get_parent_bid(d_bid)
            if not d_parent_bid_res:
                continue
            d_parent_bid = d_parent_bid_res[0]
            if d_parent_bid in ("0", 0, None) or d_parent_bid == parent_bid:
                continue

            dup_park_data_res = redundancy.get_park_data(d_bid)
            if not dup_park_data_res:
                continue
            dup_park_data = dup_park_data_res[0]

            if dup_park_data["show_tag"] != show_tag:
                continue
            if dup_park_data["open_limit_new"] != open_limit_new:
                continue

            dup_park = DuplicatePark()
            dup_park.park_bid = bid
            dup_park.dup_park_bid = d_bid
            dup_park.show_tag = show_tag
            dup_park.is_parent_id_same = False
            dup_park.memo = "A停车场父点是B停车场根节点(不是父点)，空间属性相同，开放属性相同"
            result.append(dup_park)

    except Exception as e:
        print(f"[ERROR] park_bid={park.get('bid')} -> {e}")
        traceback.print_exc()
    return result


def same_name_and_near_parks(show_tag) -> List[DuplicatePark]:
    """
    同名 + 同空间属性 + 同开放属性 + 位置相近的停车场(多进程)
    """
    result: List[DuplicatePark] = []
    park_list = redundancy.get_all_parks_by_show_tag(show_tag)

    with multiprocessing.Pool(processes=PROCESS_WORKERS) as pool:
        all_results = list(
            tqdm.tqdm(pool.imap_unordered(process_same_name_and_near_parks, park_list), total=len(park_list))
        )

    for sublist in all_results:
        result.extend(sublist)

    return result


def process_same_name_and_near_parks(park):
    """
    同名+同空间属性+同开放属性+位置相近的停车场(单进程)
    """
    result = []
    bid, name, open_limit_new, show_tag, point, parent_id = (
        park["bid"],
        park["name"],
        park["open_limit_new"],
        park["show_tag"],
        park["point"],
        park["parent_id"],
    )
    if name in ("停车场", "临时停车点", "门前停车场"):
        return result

    try:
        dup_parks = redundancy.get_same_name_parks(name)
    except Exception as e:
        print(f"Error fetching duplicate parks for {name}: {e}")
        traceback.print_exc()
        return result

    for d_park in dup_parks:
        dup_bid, dup_point, dup_parent_id, dup_open_limit_new, dup_show_tag, dup_park_name, dup_park_spec = (
            d_park["bid"],
            d_park["point"],
            d_park["parent_id"],
            d_park["open_limit_new"],
            d_park["show_tag"],
            d_park["name"],
            d_park["park_spec"],
        )
        if dup_park_spec != 0:
            continue

        if bid == dup_bid:
            continue

        if open_limit_new in ("-1", "14") or open_limit_new is None:
            open_limit_new = "0"

        if dup_open_limit_new in ("-1", "14") or dup_open_limit_new is None:
            dup_open_limit_new = "0"

        if open_limit_new != dup_open_limit_new:
            continue

        if show_tag != dup_show_tag:
            continue

        two_park_dist_limit = 50
        if dup_show_tag == "临时停车点":
            two_park_dist_limit = 20
        elif dup_show_tag == "停车场":
            two_park_dist_limit = 50
        elif dup_show_tag == "地上停车场":
            two_park_dist_limit = 120
        elif dup_show_tag == "立体停车场":
            two_park_dist_limit = 30
        elif dup_show_tag == "门前停车场":
            two_park_dist_limit = 50
        elif dup_show_tag == "地下停车场":
            two_park_dist_limit = 200

        two_park_dist = shapely.wkt.loads(dup_point).distance(shapely.wkt.loads(point)) * 110000
        try:
            if two_park_dist > two_park_dist_limit:
                continue
        except Exception as e:
            print(f"WKT parsing error: {e}")
            traceback.print_exc()
            continue

        dup_park = DuplicatePark()
        dup_park.park_bid = bid
        dup_park.park_name = name
        dup_park.dup_park_bid = dup_bid
        dup_park.two_park_dist = two_park_dist
        dup_park.memo = "同名+同空间属性+同开放属性+位置相近的停车场"
        dup_park.is_parent_id_same = dup_parent_id == parent_id
        dup_park.show_tag = show_tag
        dup_park.dup_park_name = dup_park_name

        result.append(dup_park)

    return result


def process_near_tmp_park(park):
    """
    位置相近的临时(单进程)
    """
    result = []
    bid = park["bid"]
    show_tag = "临时停车点"
    try:
        park_data_res = redundancy.get_park_data(bid)
        if not park_data_res:
            return result
        park_data = park_data_res[0]
        dup_parks = redundancy.get_parks_by_distance_show_tag(park_data["gcj_geom"], show_tag, 5 / 110000)
    except Exception as e:
        print(f"Error processing bid={bid}: {e}")
        traceback.print_exc()
        return result

    for d_park in dup_parks:
        if d_park["bid"] == bid:
            continue

        if is_two_parks_across_high_road(park_data["gcj_geom"], d_park["gcj_geom"]):
            continue

        res = DuplicatePark()
        res.park_bid = bid
        res.dup_park_bid = d_park["bid"]
        res.memo = "位置相近的临时"
        res.is_parent_id_same = True
        res.show_tag = show_tag
        res.park_name = park["name"]
        res.two_park_dist = (
            shapely.wkt.loads(park_data["gcj_geom"]).distance(shapely.wkt.loads(d_park["gcj_geom"])) * 110000
        )
        result.append(res)
    return result


def is_two_parks_across_high_road(a_wkt, b_wkt):
    """
     判断给定两点 wkt是否跨指定等级道路
    """
    store_park_point = shapely.wkt.loads(a_wkt)
    dup_park_point = shapely.wkt.loads(b_wkt)
    line_wkt = f"LINESTRING({store_park_point.x} {store_park_point.y}, {dup_park_point.x} {dup_park_point.y})"

    return redundancy.is_line_across_road(line_wkt, HIGH_LEVEL_ROAD_KIND)


def near_tmp_parks() -> List[DuplicatePark]:
    """
    位置相近的临时(多进程)
    """
    show_tag = "临时停车点"
    result: List[DuplicatePark] = []
    park_list = redundancy.get_all_parks_by_show_tag(show_tag)

    with multiprocessing.Pool(processes=PROCESS_WORKERS) as pool:
        all_results = list(tqdm.tqdm(pool.imap_unordered(process_near_tmp_park, park_list), total=len(park_list)))

    for sublist in all_results:
        result.extend(sublist)

    return result


def inner_road_conn_parks(show_tag) -> List[DuplicatePark]:
    """
    内部路连通的停车场
    """
    result: List[DuplicatePark] = []
    park_list = redundancy.get_all_parking_by_show_tag(show_tag)
    args = [(park, show_tag) for park in park_list]

    with multiprocessing.Pool(processes=PROCESS_WORKERS) as pool:
        with tqdm.tqdm(total=len(park_list)) as pbar:
            for sublist in pool.imap_unordered(_unpack_process_inner_road_conn_parks, args):
                result.extend(sublist)
                pbar.update(1)

    return result


def _unpack_process_inner_road_conn_parks(args):
    # 单参数解包函数，供 imap_unordered 使用
    return process_inner_road_conn_parks(*args)


def inner_road_conn_parks_com():
    """
    内部路连通的停车场
    """
    duplicate_parks = inner_road_conn_parks("地上停车场")
    logger.info("same_parent_aoi_parks 地上 success")

    duplicate_parks += inner_road_conn_parks("停车场")
    logger.info("same_parent_aoi_parks 地上 success")

    return duplicate_parks


def process_inner_road_conn_parks(park, show_tag):
    """
    内部路连通的停车场(单进程)
    """
    result = []
    bid, park_point_wkt, park_parent_id, park_open_limit_new = (
        park["bid"],
        park["point"],
        park["parent_id"],
        park["open_limit_new"],
    )
    try:
        area_wkt = shapely.wkt.loads(park_point_wkt).buffer(100 / 110000).wkt
        near_parks = redundancy.get_parks_by_area(area_wkt)
        for near_park in near_parks:
            near_park_show_tag = near_park["show_tag"]
            if near_park_show_tag in ["地下停车场", "立体停车场", "门前停车场", "路侧停车场"]:
                return result

            near_park_parent_id = near_park["parent_id"]
            if near_park_parent_id != park_parent_id and near_park_parent_id not in (0, "0", None):
                return result

            near_park_open_limit_new = near_park["open_limit_new"]
            if near_park_open_limit_new in ("-1", "14") or near_park_open_limit_new is None:
                near_park_open_limit_new = "0"

            if park_open_limit_new in ("-1", "14") or park_open_limit_new is None:
                park_open_limit_new = "0"

            if park_open_limit_new != near_park_open_limit_new:
                return result

            two_park_dist = (
                shapely.wkt.loads(near_park["gcj_geom"]).distance(shapely.wkt.loads(park_point_wkt)) * 110000
            )

            # 四大垂类，除去医院，都不做
            park_main_poi_res = redundancy.get_park_main_poi_tag(bid)
            if (
                park_main_poi_res
                and park_main_poi_res[0]["is_4categories"] is True
                and ("医疗" not in park_main_poi_res[0]["root_std_tag"])
            ):
                return result

            if not is_two_parks_inner_road_conn(bid, near_park["bid"]):
                return result

            item = DuplicatePark()
            item.is_parent_id_same = near_park["parent_id"] == park["parent_id"]
            item.park_bid = bid
            item.dup_park_bid = near_park["bid"]
            item.show_tag = show_tag
            item.dup_park_name = near_park["name"]
            item.park_name = park["name"]
            item.memo = f"{show_tag}与附近{near_park_show_tag}开放/空间属性一致，内部路连通"
            item.two_park_dist = two_park_dist
            item.aoi_std_tag = ""
            item.is_two_parks_inner_road_conn = True

            near_park_poi_res = redundancy.get_poi_info(near_park["bid"])
            if len(near_park_poi_res) > 0:
                item.dup_park_click_pv = near_park_poi_res[0]["click_pv"]

            park_poi_res = redundancy.get_poi_info(bid)
            if len(park_poi_res) > 0:
                item.park_click_pv = park_poi_res[0]["click_pv"]

            result.append(item)

    except Exception as e:
        print(f"Error processing bid={bid}: {e}")
        traceback.print_exc()
    return result


def is_inner_road_conn(park_point_wkt, dup_park_point_wkt, area_wkt, park_area_wkt, dup_park_area_wkt) -> bool:
    """
    判断两个停车场内部路是否连通
    """
    park_nodes = redundancy.get_inner_road_nodes(park_area_wkt)
    if not park_nodes:
        return False

    dup_park_nodes = redundancy.get_inner_road_nodes(dup_park_area_wkt)
    if not dup_park_nodes:
        return False

    area_wkt = shapely.wkt.loads(area_wkt).buffer(200 / 110000).wkt
    links = redundancy.get_all_links_by_area(area_wkt)
    for park_node in park_nodes:
        for dup_node in dup_park_nodes:
            graph = redundancy.build_graph(links)
            is_conn = redundancy.is_connected(graph, park_node[0], dup_node[0])
            if is_conn:
                return True

    return False


def _is_two_parks_across_high_road(park_bid_a, park_bid_b):
    """
    两停车场连线是否跨高等级道路
    """
    park = redundancy.get_park_info(park_bid_a)
    if park is None:
        return False

    dup_park = redundancy.get_park_info(park_bid_b)
    if dup_park is None:
        return False

    park_point = shapely.wkt.loads(park[3])
    dup_park_point = shapely.wkt.loads(dup_park[3])
    line_wkt = f"LINESTRING({park_point.x} {park_point.y}, {dup_park_point.x} {dup_park_point.y})"

    return redundancy.is_line_across_road(line_wkt, HIGH_LEVEL_ROAD_KIND)


def _save_result_to_review(result: List[DuplicatePark], output_path: str):
    """
    导出评估数据
    """
    with open(output_path, "w") as file:
        header = (
            "park_bid\t"
            "dup_park_bid\t"
            "dup_park_show_tag\t"
            "dup_park_name\t"
            "is_parent_id_same\t"
            "park_name\t"
            "two_park_dist\t"
            "aoi_std_tag\t"
            "is_two_parks_inner_road_conn\t"
            "gap_line_across_high_road\t"
            "dup_park_click_pv\t"
            "memo\n"
        )
        file.write(header)
        for item in result:
            strs = (
                f"{item.park_bid}\t"
                f"{item.dup_park_bid}\t"
                f"{item.show_tag}\t"
                f"{item.dup_park_name}\t"
                f"{item.is_parent_id_same}\t"
                f"{item.park_name}\t"
                f"{item.two_park_dist}\t"
                f"{item.aoi_std_tag}\t"
                f"{item.is_two_parks_inner_road_conn}\t"
                f"{item.gap_line_across_high_road}\t"
                f"{item.dup_park_click_pv}\t"
                f"{item.memo}\n"
            )
            file.write(strs)


if __name__ == "__main__":
    """
    main
    """
    # run()

    test('16380848975265740751', '15257601829557311247')
