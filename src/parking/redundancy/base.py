"""
sql
"""
from typing import Iterable

from src.parking.recognition import dbutils
from src.tools import pgsql


def rongyu_intel_to_db(bid: str, memo: str):
    """
    冗余情报入库
    """
    sql = """
        insert into rongyu_parks_intel(bid, memo)
        values (%s, %s) ON CONFLICT (bid) DO NOTHING;
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        try:
            pgsql.execute(conn, sql, (bid, memo))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e


def get_all_parks_by_show_tag(show_tag: str):
    """
    根据show_tag获取所有停车场
    """
    sql = """
       select distinct 
        bid, 
        parent_id, 
        name, 
        open_limit_new, 
        show_tag, 
        st_astext(gcj_geom),
        st_astext(area)
       from park_online_data
       where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
       and show_tag = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (show_tag,))
    columns = ["bid", "parent_id", "name", "open_limit_new", "show_tag", "point", "area"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_all_parking_by_show_tag(show_tag: str):
    """
    根据show_tag获取所有停车场
    """
    sql = """
       select distinct 
        bid, 
        parent_id, 
        name, 
        open_limit_new, 
        show_tag, 
        st_astext(gcj_geom),
        st_astext(area)
       from parking
       where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
       and show_tag = %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (show_tag,))
    columns = ["bid", "parent_id", "name", "open_limit_new", "show_tag", "point", "area"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_root_bid(bid: str):
    """
    获取root_bid
    """
    sql = """
      select root_bid
      from park_main_poi
      where park_bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    return ret


def get_park_info(bid: str):
    """
    获取park信息
    """
    sql = """
        select 
            park_spec, 
            status, 
            show_tag, 
            st_astext(gcj_geom), 
            std_tag, 
            precise, 
            open_limit_new
        from park_online_data
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, (bid,))
    return ret


def get_same_root_bid_parks(root_bid: str):
    """
    获取相同root_bid的停车场集合
    """
    sql = """
          select distinct park_bid
          from park_main_poi
          where root_bid = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (root_bid,))
    columns = [
        "bid",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_same_parent_bid_parks(parent_bid: str):
    """
    获取相同parent_bid的停车场集合
    """
    sql = """
          select 
            distinct bid, 
            name, 
            show_tag, 
            open_limit_new, 
            parent_id, 
            st_astext(gcj_geom)
          from park_online_data
          where parent_id = %s 
          and std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (parent_bid,))
    columns = ["bid", "name", "show_tag", "open_limit_new", "parent_id", "gcj_geom"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_parent_bid(bid: str):
    """
    获取父点bid
    """
    sql = """
        select parent_id
        from parking 
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, (bid,))
    return ret


def get_parks_by_distance_show_tag(point_wkt: str, show_tag: str, dist: float):
    """
    根据位置和空间属性获取停车场
    """
    sql = """
          select distinct bid, st_astext(gcj_geom)
          from park_online_data
          where show_tag = %s
          and st_dwithin(ST_GeomFromText(%s, 4326), gcj_geom, %s)
       """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (show_tag, point_wkt, dist))
    columns = ["bid", "gcj_geom"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_parks_by_area(area_wkt: str):
    """
    根据范围筛选停车场
    """
    sql = """
       select 
        distinct bid, 
        open_limit_new, 
        show_tag,
        parent_id,
        name,
        st_astext(gcj_geom)
       from park_online_data
       where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
       and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (area_wkt,))
    columns = ["bid", "open_limit_new", "show_tag", "parent_id", "name", "gcj_geom"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_same_name_open_show_parks(name: str, open_limit_new, show_tag: str):
    """
    获取同名、同空间属性、同开放属性的停车场
    """
    sql = """
        select 
            distinct bid, 
            st_astext(gcj_geom), 
            parent_id
        from park_online_data
        where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
        and name = %s and open_limit_new = %s and show_tag = %s
     """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (name, open_limit_new, show_tag))
    columns = ["bid", "point", "parent_id"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_same_name_parks(name: str):
    """
    获取同名停车场
    """
    sql = """
          select 
            distinct bid, 
            st_astext(gcj_geom), 
            parent_id,
            name, 
            open_limit_new, 
            show_tag, 
            std_tag,
            park_spec
          from park_online_data
          where name = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (name,))
    columns = ["bid", "point", "parent_id", "name", "open_limit_new", "show_tag", "std_tag", "park_spec"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_all_parks():
    """
    获取所有停车场
    """
    sql = """
       select distinct bid, name, open_limit_new, show_tag, st_astext(gcj_geom), parent_id
       from park_online_data
       where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = ["bid", "name", "open_limit_new", "show_tag", "point", "parent_id"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_all_aoi_list():
    """
    获取所有是非商单基础院落AOI
    """
    sql = """
        select b.poi_bid, st_astext(a.geom) 
        from blu_face a inner join blu_face_poi b 
        on a.face_id = b.face_id
        where a.src!= 'SD'
        and a.aoi_level = 2  
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    columns = [
        "poi_bid",
        "aoi_wkt",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_park_data(bid: str):
    """
    获取park信息
    """
    sql = """
        select 
            park_spec, 
            status, 
            show_tag, 
            st_astext(gcj_geom), 
            std_tag, 
            precise, 
            open_limit_new,
            st_astext(area),
            parent_id,
            name,
            road_relation_childrens
        from park_online_data
        where bid = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, (bid,))
    columns = [
        "park_spec",
        "status",
        "show_tag",
        "gcj_geom",
        "std_tag",
        "precise",
        "open_limit_new",
        "area",
        "parent_id",
        "name",
        "road_relation_childrens",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_parking_data(bid: str):
    """
    获取parking信息
    """
    sql = """
        select 
            park_spec, 
            status, 
            show_tag, 
            st_astext(gcj_geom), 
            std_tag, 
            precise, 
            open_limit_new,
            st_astext(area),
            parent_id,
            name,
            road_relation_childrens
        from parking
        where bid = %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (bid,))
    columns = [
        "park_spec",
        "status",
        "show_tag",
        "gcj_geom",
        "std_tag",
        "precise",
        "open_limit_new",
        "area",
        "parent_id",
        "name",
        "road_relation_childrens",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_aoi_info(bid: str):
    """
    根据bid获取非商单基础院落AOI
    """
    sql = """
        select b.poi_bid, area, st_astext(a.geom)
        from blu_face a inner join blu_face_poi b 
        on a.face_id = b.face_id
        where a.src!= 'SD'
        and a.aoi_level = 2  
        and b.poi_bid = %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (bid,))
    columns = ["poi_bid", "area", "geom"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def is_line_across_road(gcj_wkt: str, road_level: int) -> bool:
    """
    判断给定linestring wkt是否跨指定等级道路
    """
    sql = """
        select * 
        from nav_link
        where kind < %s 
        and st_intersects(st_geomfromtext(%s, 4326), geom)
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, (road_level, gcj_wkt))
    return len(ret) > 0


def get_poi_info(bid: str):
    """
    获取poi信息
    """
    sql = """
        select 
            std_tag, 
            click_pv, 
            st_astext(geometry), 
            relation_bid, 
            city
        from poi
        where bid = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, (bid,))
    columns = ["std_tag", "click_pv", "geometry", "relation_bid", "city"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_parks_with_poly(gcj_poly_wkt: str):
    """
    获取给定范围内的停车场
    """
    sql = """
        select 
            distinct bid, 
            st_astext(
                case 
                    when show_area is not null then show_area
                    else area
                end
            ) as geom_text
        from parking 
        where st_intersects(area, st_geomfromtext(%s, 4326)) and status = 1
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (gcj_poly_wkt,))
    return ret


def get_park_7days_accept_ratio(bid: str):
    """
    获取停车场的7天采纳率
    """
    sql = """
        select avg(accept_percent)
        from park_low_accept_list_history
        where park_bid = %s
    """
    ret = dbutils.fetch_one(pgsql.ACCEPT_DATA, sql, (bid,))
    return ret


def is_in_protect_list(bid: str) -> bool:
    """
    判断是否是防护集合停车场
    """
    sql = """
         select *
         from over_heat_protection 
         where park_bid = %s
    """
    ret1 = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, (bid,))

    sql = """
         select *
         from high_heat_protection 
         where park_bid = %s
    """
    ret2 = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, (bid,))
    return len(ret1 + ret2) > 0


def is_street_poi(bid: str) -> bool:
    """
    判断给定的poi是否是临街poi
    """
    if not bid or not isinstance(bid, str):
        print("[WARN] invalid bid:", bid)
        return False
    sql = """
         select distinct bid
         from poi_prod_scene_data where 
         bid = %s
         and scene_class4 ~ '临街'
    """
    ret = dbutils.fetch_all(pgsql.TRAJ_DB, sql, (bid,))
    return len(ret) > 0


def get_cover_aoi_poi_bid(poly_wkt: str):
    """
    获取与给定面压盖的非商单AOI.poi_bid
    """
    sql = """
        select b.poi_bid 
        from blu_face a inner join blu_face_poi b 
        on a.face_id = b.face_id
        where a.src!= 'SD' 
        and st_intersects(st_geomfromtext(%s, 4326), a.geom)
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (poly_wkt,))
    return ret


def get_all_old_storefront_parks():
    """
    获取全量旧门前
    """
    sql = """
       select distinct 
        bid, 
        parent_id, 
        name, 
        open_limit_new, 
        show_tag, 
        st_astext(gcj_geom),
        st_astext(area)
       from park_online_data
       where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
       and show_tag = '门前停车场'
       and park_spec not in (1, 2)
       and central_line is null
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    columns = ["bid", "parent_id", "name", "open_limit_new", "show_tag", "point", "area"]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def insert_duplicate_log(park_bid, dup_park_bid, final_pick_dup_park_bid, remark):
    """
    插入重复停车场记录
    """
    sql = """
        insert into park_storefront_duplicate_log (storefront_park_bid, dup_park_bid, final_pick_dup_park_bid, remark)
        values (%s, %s, %s, %s)
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        try:
            pgsql.execute(conn, sql, (park_bid, dup_park_bid, final_pick_dup_park_bid, remark))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e


def get_all_links_by_area(area_wkt: str):
    """
    根据范围获取路网数据
    """
    sql = """
        select s_nid, e_nid 
        from nav_link
        where form = '52'
        and st_intersects(geom, ST_GeomFromText(%s, 4326)) 
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, (area_wkt,))
    return ret


def get_inner_road_nodes(area_wkt: str):
    """
    给定范围，计算最近的内部路的node
    """
    sql = """
        SELECT s_nid
        FROM nav_link
        WHERE form = '52'
        AND st_intersects(geom, ST_GeomFromText(%s, 4326))
       """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, (area_wkt,))
    return ret


def get_wait_push_duplicate_parks():
    """
    获取待推送冗余停车场数据
    """
    sql = """
        select id, dup_park_bid, final_pick_dup_park_bid
        from park_storefront_duplicate_log
        where status = 0
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    return ret


def is_park_storefront_prod_parking(bid):
    """
    是否是新门前产线生成的停车场bid
    """
    sql = """
        select * 
        from park_storefront_prod_parking 
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, (bid,))
    return ret


def pushed_then_duplicate_park_status(id, status):
    """
    推送完更新推送状态
    """
    sql = """
        update park_storefront_duplicate_log 
        set status = %s 
        where id = %s
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        try:
            pgsql.execute(conn, sql, (status, id))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e


def get_park_main_poi_tag(bid: str):
    """
    获取主点tag
    """
    sql = """
        select root_std_tag, is_4categories 
        from park_main_poi
        where park_bid = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, (bid,))
    columns = ["root_std_tag", "is_4categories"]
    result = [dict(zip(columns, row)) for row in ret]
    return result
