"""
地上 旧门前 临时 立体 停车场 临时压盖重复
* 地上与地上
* 地上和临时
* 地上和停车场
* 地上和旧门前

* 停车场和停车场
* 停车场和临时
* 停车场和旧门前

* 旧门前与旧门前
* 旧门前与临时

* 临时和临时

* 立体与立体
"""
import shapely.wkt
import tqdm as tqdm
import logging
import multiprocessing
import json
import sys

from functools import partial
from dataclasses import dataclass, field, asdict
from typing import List, Tuple
from src.parking import redundancy
from src.parking.storefront.diff import polygon_differ
from shapely.wkt import loads
from src.parking.redundancy.config import DUPLICATE_PARKS_RUN_NUM_WORKERS, HIGH_LEVEL_ROAD_KIND


# 添加日志配置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", stream=sys.stdout)
logger = logging.getLogger(__name__)


@dataclass
class DuplicatePark:
    """
    重复停车场信息
    """

    park_bid: str = field(default="", init=False)  # 停车场bid
    dup_park_bid: str = field(default="", init=False)  # 重复停车场bid
    dup_park_city: str = field(default="", init=False)  # 重复停车场城市
    dup_park_show_tag: str = field(default="", init=False)  # 重复停车场空间属性
    dup_park_click_pv: float = field(default=-1, init=False)  # 重复停车场click_pv
    dup_park_accept_ratio: float = field(default=-1, init=False)  # 重复停车场采纳率
    dup_park_spec: float = field(default=0, init=False)  # 重复停车场park_spec
    dup_park_precise: float = field(default=0, init=False)  # 重复停车场precise
    dup_reason: str = field(default="", init=False)  # 重复原因
    dup_intersects_iou: float = field(default=0, init=False)  # 停车场和重复停车场范围相交iou
    dup_link_iou: float = field(default=0, init=False)  # 停车场和重复停车场link投影相交iou
    dup_link_ioa: float = field(default=0, init=False)  # 停车场和重复停车场link投影相交ioa
    dup_link_iob: float = field(default=0, init=False)  # 停车场和重复停车场link投影相交iob
    dup_two_parks_dist: float = field(default=0, init=False)  # 两停车场距离
    dup_exist_parent_bid: bool = field(default=False, init=False)  # 重复停车场是否存在父点
    dup_cover_aoi_tag: str = field(default="", init=False)  # 重复停车场压盖的AOI.tag
    dup_parent_std_tag: str = field(default="", init=False)  # 重复停车场父点std_tag
    dup_parent_is_street_poi: bool = field(default="", init=False)  # 重复停车场父点是否是临街POI
    dup_parent_is_in_storefront_park_area: bool = field(default="", init=False)  # 重复停车场父点是否在门前面内
    dup_parent_is_aoi: bool = field(default=False, init=False)  # 重复停车场父点是否是AOI
    dup_parent_aoi_area: float = field(default=-1, init=False)  # 重复停车场父点aoi面积
    area_diff_ratio: float = field(default=-1, init=False)  # 重复停车场中没有被门前停车场面覆盖的比例
    gap_line_across_high_road: bool = field(default=False, init=False)  # 两停车场连线是否跨高等级道路
    is_in_protect_list: bool = field(default=False, init=False)  # 是否在防护集合


def run() -> None:
    """
    run
    """
    duplicate_parks = ground_find_duplicate_parks()
    logger.info("ground_find_duplicate_parks success")

    duplicate_parks += unknown_find_duplicate_parks()
    logger.info("unknown_find_duplicate_parks success")

    duplicate_parks += old_storefront_find_duplicate_parks()
    logger.info("old_storefront_find_duplicate_parks success")

    duplicate_parks += tmp_find_duplicate_parks()
    logger.info("tmp_find_duplicate_parks success")

    duplicate_parks += multi_level_find_duplicate_parks()
    logger.info("multi_level_find_duplicate_parks success")

    duplicate_parks = _filter_potential_valid_parks(duplicate_parks)
    logger.info("_filter_potential_valid_parks success")

    _save_result_to_review(duplicate_parks, "./street_duplicate_parks.csv")
    logger.info("_save_result_to_review success")


def _filter_potential_valid_parks(dup_parks: List[DuplicatePark]) -> List[DuplicatePark]:
    """
    过滤掉潜在正确的停车场：
    """
    result: List[DuplicatePark] = []
    for dup_park in dup_parks:
        (storefront_park_bid, dup_park_bid, dup_exist_parent_bid, dup_park_spec,) = (
            dup_park.park_bid,
            dup_park.dup_park_bid,
            dup_park.dup_exist_parent_bid,
            dup_park.dup_park_spec,
        )
        if dup_park_spec == 1:
            logger.info(f"{dup_park_bid}, park_spec = 1的不下线")
            continue

        if dup_park.is_in_protect_list:
            logger.info(f"{dup_park_bid}, 在防护集合的不下线")
            continue

        if dup_exist_parent_bid:
            logger.info(f"{dup_park_bid}, 有父点策略暂时不下线")
            continue
        else:
            if _filter_not_exist_parent_dup_park(dup_park):
                result.append(dup_park)
            else:
                logger.info(f"{dup_park_bid}, 无父点策略判断不下线")

    return result


def _filter_not_exist_parent_dup_park(dup_park: DuplicatePark) -> bool:
    """
    过滤不存在父点的重复停车场
    """
    (
        storefront_park_bid,
        dup_park_bid,
        dup_park_show_tag,
        dup_exist_parent_bid,
        dup_reason,
        dup_dist,
        across_high_road,
        dup_park_spec,
        dup_intersects_iou,
        dup_link_ioa,
        dup_link_iou,
        dup_link_iob,
        area_diff_ratio,
        dup_accept_ratio,
        dup_park_click_pv,
        dup_park_precise,
    ) = (
        dup_park.park_bid,
        dup_park.dup_park_bid,
        dup_park.dup_park_show_tag,
        dup_park.dup_exist_parent_bid,
        dup_park.dup_reason,
        dup_park.dup_two_parks_dist,
        dup_park.gap_line_across_high_road,
        dup_park.dup_park_spec,
        dup_park.dup_intersects_iou,
        dup_park.dup_link_ioa,
        dup_park.dup_link_iou,
        dup_park.dup_link_iob,
        dup_park.area_diff_ratio,
        dup_park.dup_park_accept_ratio,
        dup_park.dup_park_click_pv,
        dup_park.dup_park_precise,
    )

    if dup_park_spec != 0:
        # 高度精准的暂不下线
        return False

    if dup_accept_ratio >= 0.6:
        # 采纳率高的暂不下线
        return False

    if dup_park_click_pv >= 20:
        # 算路pv高的暂不下线
        return False

    if across_high_road is True:
        # 跨高等级道路暂不下线
        return False

    if dup_reason == "单点停车场在门前buffer范围内":
        # 单点场景先不做
        return False

    # 非单点场景
    if dup_intersects_iou <= 0.05:
        # 面交集很小的暂不下线
        return False

    if area_diff_ratio > 0.75:
        # 重复停车场没有被门前覆盖的范围过大，暂不下线
        return False

    if dup_dist < 25:
        return True
    return False


def ground_find_duplicate_parks() -> List[DuplicatePark]:
    """
    * 地上与地上
    * 地上和临时
    * 地上和停车场
    * 地上和旧门前
    """
    result = []
    parks = redundancy.get_all_parking_by_show_tag("地上停车场")
    result += _find_duplicate_parks(parks, "地上停车场")
    result += _find_duplicate_parks(parks, "临时停车点")
    result += _find_duplicate_parks(parks, "停车场")
    result += _find_duplicate_parks(parks, "门前停车场")

    return result


def unknown_find_duplicate_parks() -> List[DuplicatePark]:
    """
    * 停车场和停车场
    * 停车场和临时
    * 停车场和旧门前
    """
    result = []
    parks = redundancy.get_all_parking_by_show_tag("停车场")
    result += _find_duplicate_parks(parks, "停车场")
    result += _find_duplicate_parks(parks, "临时停车点")
    result += _find_duplicate_parks(parks, "门前停车场")

    return result


def old_storefront_find_duplicate_parks() -> List[DuplicatePark]:
    """
    * 旧门前与旧门前
    * 旧门前与临时
    """
    result = []
    parks = redundancy.get_all_old_storefront_parks()
    result += _find_duplicate_parks(parks, "临时停车点")
    result += _find_duplicate_parks(parks, "门前停车场")

    return result


def tmp_find_duplicate_parks() -> List[DuplicatePark]:
    """
    * 临时和临时
    """
    result = []
    parks = redundancy.get_all_parking_by_show_tag("临时停车点")
    result += _find_duplicate_parks(parks, "临时停车点")

    return result


def multi_level_find_duplicate_parks() -> List[DuplicatePark]:
    """
    * 立体与立体
    """
    result = []
    parks = redundancy.get_all_parking_by_show_tag("立体停车场")
    result += _find_duplicate_parks(parks, "立体停车场")

    return result


def _find_duplicate_parks(parks, show_tag) -> List[DuplicatePark]:
    """
    根据全量停车场获取全量重复地上停车场
    """
    result: List[DuplicatePark] = []
    logger.info("_find_duplicate_parks start")

    chunk_size = max(1, len(parks) // DUPLICATE_PARKS_RUN_NUM_WORKERS)  # 拆分任务
    with multiprocessing.Pool(processes=DUPLICATE_PARKS_RUN_NUM_WORKERS) as pool:
        # 使用 partial 绑定 show_tag 参数
        func = partial(_get_duplicate_parks, show_tag=show_tag)
        print("processing: ", show_tag)
        results = list(tqdm.tqdm(pool.imap(func, parks, chunksize=chunk_size), total=len(parks)))

    # 合并所有进程计算结果
    for r in results:
        result.extend(r)

    return result


def _get_duplicate_parks(park, show_tag) -> List[DuplicatePark]:
    """
    根据给定停车场获取全量重复停车场
    """
    result: List[DuplicatePark] = []
    park_bid, park_area = park["bid"], park["area"]
    if not park_area:
        return result

    def _get_park(wkt: str) -> List[polygon_differ.Polygon]:
        """
        获取停车场面
        """
        return [polygon_differ.Polygon(face_id=park_bid, geom=park_area)]

    def _get_park_polygons(poly_wkt) -> List[polygon_differ.Polygon]:
        """
        获取附近停车场面
        """
        polygons: List[polygon_differ.Polygon] = []
        buf_wkt = shapely.wkt.loads(poly_wkt).buffer(50 * 1e-5).wkt
        parks = redundancy.get_parks_with_poly(buf_wkt)
        for park in parks:
            bid, area_wkt = park
            park_data = redundancy.get_park_info(bid)
            if park_data is None:
                continue

            online_status, online_show_tag = park_data[1], park_data[2]
            if online_status != 1 or online_show_tag != show_tag:
                continue

            polygons.append(polygon_differ.Polygon(face_id=bid, geom=area_wkt))
        return polygons

    park_polygons = _get_park_polygons(park_area)
    diff_res = []
    try:
        diff_res = polygon_differ.diff_center(park_polygons, 30 * 1e-5, _get_park,)
    except Exception as e:
        logger.error(f"Error polygon_differ.diff_center: {e}")

    for diff in diff_res:
        if diff is None or diff.similarity is None:
            continue

        # 相似度
        similarity = diff.similarity.get_detail()
        link_similarity, geom_similarity = similarity["link"], similarity["geom"]

        # 交并比
        link_iou, link_ioa, link_iob = (
            link_similarity["iou"],
            link_similarity["ioa"],
            link_similarity["iob"],
        )
        geom_iou = geom_similarity["iou"]
        if geom_iou == 0 and link_iou == 0 and link_ioa == 0 and link_iob == 0:
            continue

        # 停车场和重复停车场面
        a_face_ids, b_face_ids = diff.a_face_ids, diff.b_face_ids
        if not a_face_ids or not b_face_ids:
            logger.warning(f"skipping due to empty face_ids: a_face_ids={a_face_ids}, b_face_ids={b_face_ids}")
            continue

        dup_park = DuplicatePark()
        dup_park.park_bid = b_face_ids[0]
        dup_park.dup_park_bid = a_face_ids[0]
        dup_park.dup_park_show_tag = show_tag
        dup_park.dup_reason = f"{park['show_tag']} 与 " + show_tag + " 范围压盖or link投影有交集"
        dup_park.dup_intersects_iou = geom_iou
        dup_park.dup_link_iou = link_iou
        dup_park.dup_link_ioa = link_ioa
        dup_park.dup_link_iob = link_iob

        # 不与自己判重
        if park_bid == dup_park.dup_park_bid:
            continue

        # park_spec/precise
        dup_park_info = redundancy.get_park_info(dup_park.dup_park_bid)
        if dup_park_info:
            dup_park.dup_park_spec = dup_park_info[0]
            dup_park.dup_park_precise = dup_park_info[5]

        # click_pv、城市
        dup_park_poi = redundancy.get_poi_info(dup_park.dup_park_bid)
        if len(dup_park_poi) > 0:
            dup_park.dup_park_click_pv = dup_park_poi[0]["click_pv"]
            dup_park.duplicate_park_city = dup_park_poi[0]["city"]

        # 是否在防护集合
        dup_park.is_in_protect_list = redundancy.is_in_protect_list(dup_park.dup_park_bid)

        # 最近7天采纳率
        duplicate_park_accept_ratio = redundancy.get_park_7days_accept_ratio(dup_park.dup_park_bid)
        if duplicate_park_accept_ratio is not None and duplicate_park_accept_ratio[0] is not None:
            dup_park.duplicate_park_accept_ratio = round(duplicate_park_accept_ratio[0], 4)

        # 两停车场距离和面积差占比
        dist, area_diff_ratio = -1, -1
        a_c, b_c = diff.a_characteristics_set[0], diff.b_characteristics_set[0]
        try:
            if a_c and a_c and a_c.polygon and b_c.polygon:
                a_sp = shapely.wkt.loads(a_c.polygon.geom)
                b_sp = shapely.wkt.loads(b_c.polygon.geom)
                dist = a_sp.distance(b_sp) * 1e5
                area_diff_ratio = a_sp.difference(a_sp.intersection(b_sp)).area / a_sp.area
        except Exception as e:
            logger.error(e)
        dup_park.duplicate_two_parks_dist = dist
        dup_park.area_diff_ratio = area_diff_ratio

        # 两停车场连线是否跨高等级道路
        dup_park.gap_line_across_high_road = _is_two_parks_across_high_road(dup_park.park_bid, dup_park.dup_park_bid)

        # 父点挂接
        parent_bid_res = redundancy.get_parent_bid(dup_park.dup_park_bid)
        if parent_bid_res is not None and parent_bid_res[0] not in ("0", 0, "", None):
            dup_park.duplicate_exist_parent_bid = True
            parent_bid = parent_bid_res[0]

            # 父点.std_tag
            parent_poi_info = redundancy.get_poi_info(parent_bid)
            if len(parent_poi_info) > 0:
                dup_park.duplicate_parent_std_tag = parent_poi_info[0]["std_tag"]

            # 父点是否是临街POI
            parent_is_street_poi = redundancy.is_street_poi(parent_bid)
            dup_park.duplicate_parent_is_street_poi = parent_is_street_poi

            # 父点是否在门前面内
            if len(parent_poi_info) > 0:
                is_parent_poi_in_area = shapely.wkt.loads(park_area).intersects(
                    shapely.wkt.loads(parent_poi_info[0]["geometry"])
                )
                dup_park.duplicate_parent_is_in_storefront_park_area = is_parent_poi_in_area

            # 父点AOI信息
            if len(parent_poi_info) > 0:
                parent_aoi1 = redundancy.get_aoi_info(parent_bid)
                parent_aoi2 = redundancy.get_aoi_info(parent_poi_info[0]["relation_bid"])
                if len(parent_aoi1) > 0:
                    dup_park.duplicate_parent_is_aoi = True
                    dup_park.duplicate_parent_aoi_area = parent_aoi1[0]["area"]
                elif len(parent_aoi2) > 0:
                    dup_park.duplicate_parent_is_aoi = True
                    dup_park.duplicate_parent_aoi_area = parent_aoi2[0]["area"]

        # aoi.poi.std_tag
        poi_bids = redundancy.get_cover_aoi_poi_bid(b_c.polygon.geom)
        aoi_std_tags = ""
        for poi_bid in poi_bids:
            aoi_poi_info = redundancy.get_poi_info(poi_bid)
            if len(aoi_poi_info) == 0:
                continue
            aoi_std_tags += aoi_poi_info[0]["std_tag"]
        dup_park.duplicate_cover_aoi_tag = aoi_std_tags

        result.append(dup_park)
    return result


def _is_two_parks_across_high_road(park_bid_a, park_bid_b):
    """
    两停车场连线是否跨高等级道路
    """
    store_park = redundancy.get_park_info(park_bid_a)
    if store_park is None:
        return False

    dup_park = redundancy.get_park_info(park_bid_b)
    if dup_park is None:
        return False

    store_park_point = loads(store_park[3])
    dup_park_point = loads(dup_park[3])
    line_wkt = f"LINESTRING({store_park_point.x} {store_park_point.y}, {dup_park_point.x} {dup_park_point.y})"

    return redundancy.is_line_across_road(line_wkt, HIGH_LEVEL_ROAD_KIND)


def _save_result_to_review(result: List[DuplicatePark], output_path: str):
    """
    导出评估数据
    """
    with open(output_path, "w") as file:
        header = (
            "park_bid\t"
            "dup_park_bid\t"
            "dup_park_city\t"
            "dup_park_show_tag\t"
            "dup_reason\t"
            "dup_intersects_iou\t"
            "dup_link_iou\t"
            "dup_link_ioa\t"
            "dup_link_iob\t"
            "dup_two_parks_dist\t"
            "dup_park_accept_ratio\t"
            "dup_park_spec\t"
            "dup_park_precise\t"
            "dup_cover_aoi_tag\t"
            "dup_park_click_pv\t"
            "gap_line_across_high_road\t"
            "dup_parent_std_tag\t"
            "dup_parent_is_street_poi\t"
            "dup_parent_is_in_storefront_park_area\t"
            "dup_parent_is_aoi\t"
            "dup_parent_aoi_area\t"
            "area_diff_ratio\t"
            "is_in_protect_list\t"
            "dup_exist_parent_bid\n"
        )
        file.write(header)
        for item in result:
            strs = (
                f"{item.park_bid}\t"
                f"{item.dup_park_bid}\t"
                f"{item.dup_park_city}\t"
                f"{item.dup_park_show_tag}\t"
                f"{item.dup_reason}\t"
                f"{item.dup_intersects_iou}\t"
                f"{item.dup_link_iou}\t"
                f"{item.dup_link_ioa}\t"
                f"{item.dup_link_iob}\t"
                f"{item.dup_two_parks_dist}\t"
                f"{item.dup_park_accept_ratio}\t"
                f"{item.dup_park_spec}\t"
                f"{item.dup_park_precise}\t"
                f"{item.dup_cover_aoi_tag}\t"
                f"{item.dup_park_click_pv}\t"
                f"{item.gap_line_across_high_road}\t"
                f"{item.dup_parent_std_tag}\t"
                f"{item.dup_parent_is_street_poi}\t"
                f"{item.dup_parent_is_in_storefront_park_area}\t"
                f"{item.dup_parent_is_aoi}\t"
                f"{item.dup_parent_aoi_area}\t"
                f"{item.area_diff_ratio}\t"
                f"{item.is_in_protect_list}\t"
                f"{item.dup_exist_parent_bid}\n"
            )
            file.write(strs)


if __name__ == "__main__":
    """
    main
    """
    run()
