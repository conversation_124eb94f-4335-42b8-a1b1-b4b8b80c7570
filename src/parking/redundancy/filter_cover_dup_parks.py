import shapely.wkt
import tqdm as tqdm
import logging
import multiprocessing
import json
import sys

from functools import partial
from dataclasses import dataclass, field, asdict
from typing import List, Tuple
from src.parking import redundancy
from src.parking.storefront.diff import polygon_differ
from shapely.wkt import loads
from src.parking.redundancy.config import DUPLICATE_PARKS_RUN_NUM_WORKERS, HIGH_LEVEL_ROAD_KIND

# 添加日志配置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", stream=sys.stdout)
logger = logging.getLogger(__name__)


@dataclass
class DuplicatePark:
    """
    重复停车场信息
    """

    park_bid: str = field(default="", init=False)  # 停车场bid
    dup_park_bid: str = field(default="", init=False)  # 重复停车场bid
    dup_park_city: str = field(default="", init=False)  # 重复停车场城市
    dup_park_show_tag: str = field(default="", init=False)  # 重复停车场空间属性
    dup_park_click_pv: float = field(default=-1, init=False)  # 重复停车场click_pv
    dup_park_accept_ratio: float = field(default=-1, init=False)  # 重复停车场采纳率
    dup_park_spec: float = field(default=0, init=False)  # 重复停车场park_spec
    dup_park_precise: float = field(default=0, init=False)  # 重复停车场precise
    dup_reason: str = field(default="", init=False)  # 重复原因
    dup_intersects_iou: float = field(default=0, init=False)  # 停车场和重复停车场范围相交iou
    dup_link_iou: float = field(default=0, init=False)  # 停车场和重复停车场link投影相交iou
    dup_link_ioa: float = field(default=0, init=False)  # 停车场和重复停车场link投影相交ioa
    dup_link_iob: float = field(default=0, init=False)  # 停车场和重复停车场link投影相交iob
    dup_two_parks_dist: float = field(default=0, init=False)  # 两停车场距离
    dup_exist_parent_bid: bool = field(default=False, init=False)  # 重复停车场是否存在父点
    dup_cover_aoi_tag: str = field(default="", init=False)  # 重复停车场压盖的AOI.tag
    dup_parent_std_tag: str = field(default="", init=False)  # 重复停车场父点std_tag
    dup_parent_is_street_poi: bool = field(default="", init=False)  # 重复停车场父点是否是临街POI
    dup_parent_is_in_storefront_park_area: bool = field(default="", init=False)  # 重复停车场父点是否在门前面内
    dup_parent_is_aoi: bool = field(default=False, init=False)  # 重复停车场父点是否是AOI
    dup_parent_aoi_area: float = field(default=-1, init=False)  # 重复停车场父点aoi面积
    area_diff_ratio: float = field(default=-1, init=False)  # 重复停车场中没有被门前停车场面覆盖的比例
    gap_line_across_high_road: bool = field(default=False, init=False)  # 两停车场连线是否跨高等级道路
    is_in_protect_list: bool = field(default=False, init=False)  # 是否在防护集合


def run():
    dup_parks = get_dup_parks()
    logger.info("get_dup_parks success")

    dup_parks = update_value(dup_parks)
    logger.info("update_value success")

    parks = filter(dup_parks)
    logger.info("filter success")

    _save_result_to_review(parks, "filter_cover_dup_parks.csv")
    logger.info("_save_result_to_review success")


def update_value(dup_parks: list, processes=50):
    with multiprocessing.Pool(processes=processes) as pool:
        results = list(tqdm.tqdm(pool.imap_unordered(process_dup_park, dup_parks), total=len(dup_parks)))

    # 过滤掉 None（表示处理失败或跳过的）
    return [r for r in results if r is not None]


def process_dup_park(dup_park: DuplicatePark):
    """
    重复停车场处理(单进程)
    """
    try:
        park_data = redundancy.get_park_data(dup_park.park_bid)
        dup_park_data = redundancy.get_park_data(dup_park.dup_park_bid)
        accept = redundancy.get_park_7days_accept_ratio(dup_park.dup_park_bid)

        if len(park_data) == 0 or len(dup_park_data) == 0:
            return None

        park_data = park_data[0]
        dup_park_data = dup_park_data[0]

        dup_park.dup_two_parks_dist = (
            shapely.wkt.loads(park_data["gcj_geom"]).distance(shapely.wkt.loads(dup_park_data["gcj_geom"])) * 110000
        )

        if accept is not None:
            dup_park.dup_park_accept_ratio = accept[0]

        dup_park.dup_reason = f"{park_data['show_tag']} {dup_park.dup_reason}"

        return dup_park

    except Exception as e:
        print(f"处理失败: {dup_park} 错误: {e}")
        return None


def filter(dup_parks: list[DuplicatePark]):
    """
    过滤
    """
    parks = []
    for dup_park in tqdm.tqdm(dup_parks):
        if dup_park.is_in_protect_list == True:
            continue
        if dup_park.dup_two_parks_dist > 25:
            continue
        if dup_park.dup_park_accept_ratio and dup_park.dup_park_accept_ratio >= 0.6:
            # 采纳率高的暂不下线
            continue
        parks.append(dup_park)
    return parks


def get_dup_parks():
    """
    获取重复停车场
    """
    parks = []
    with open("/home/<USER>/lifan14/output/street_duplicate_parks.csv", "r", encoding="utf-8") as f:
        for line in tqdm.tqdm(f.readlines()):
            items = line.strip().split("\t")
            (
                park_bid,
                dup_park_bid,
                dup_park_city,
                dup_park_show_tag,
                dup_reason,
                dup_intersects_iou,
                dup_link_iou,
                dup_link_ioa,
                dup_link_iob,
                dup_two_parks_dist,
                dup_park_accept_ratio,
                dup_park_spec,
                dup_park_precise,
                dup_cover_aoi_tag,
                dup_park_click_pv,
                gap_line_across_high_road,
                dup_parent_std_tag,
                dup_parent_is_street_poi,
                dup_parent_is_in_storefront_park_area,
                dup_parent_is_aoi,
                dup_parent_aoi_area,
                area_diff_ratio,
                is_in_protect_list,
                dup_exist_parent_bid,
            ) = items
            dup = DuplicatePark()
            dup.park_bid = park_bid
            dup.dup_park_bid = dup_park_bid
            dup.dup_park_city = dup_park_city
            dup.dup_park_show_tag = dup_park_show_tag
            dup.dup_reason = dup_reason
            dup.dup_intersects_iou = dup_intersects_iou
            dup.dup_link_iou = dup_link_iou
            dup.dup_link_ioa = dup_link_ioa
            dup.dup_link_iob = dup_link_iob
            dup.dup_two_parks_dist = dup_two_parks_dist
            dup.dup_park_accept_ratio = dup_park_accept_ratio
            dup.dup_park_spec = dup_park_spec
            dup.dup_park_precise = dup_park_precise
            dup.dup_cover_aoi_tag = dup_cover_aoi_tag
            dup.dup_park_click_pv = dup_park_click_pv
            dup.gap_line_across_high_road = gap_line_across_high_road
            dup.dup_parent_std_tag = dup_parent_std_tag
            dup.dup_parent_is_street_poi = dup_parent_is_street_poi
            dup.dup_parent_is_in_storefront_park_area = dup_parent_is_in_storefront_park_area
            dup.dup_parent_is_aoi = dup_parent_is_aoi
            dup.dup_parent_aoi_area = dup_parent_aoi_area
            dup.area_diff_ratio = area_diff_ratio
            dup.is_in_protect_list = is_in_protect_list
            dup.dup_exist_parent_bid = dup_exist_parent_bid
            parks.append(dup)
    return parks


def _save_result_to_review(result: List[DuplicatePark], output_path: str):
    """
    导出评估数据
    """
    with open(output_path, "w") as file:
        header = (
            "park_bid\t"
            "dup_park_bid\t"
            "dup_park_city\t"
            "dup_park_show_tag\t"
            "dup_reason\t"
            "dup_intersects_iou\t"
            "dup_link_iou\t"
            "dup_link_ioa\t"
            "dup_link_iob\t"
            "dup_two_parks_dist\t"
            "dup_park_accept_ratio\t"
            "dup_park_spec\t"
            "dup_park_precise\t"
            "dup_cover_aoi_tag\t"
            "dup_park_click_pv\t"
            "gap_line_across_high_road\t"
            "dup_parent_std_tag\t"
            "dup_parent_is_street_poi\t"
            "dup_parent_is_in_storefront_park_area\t"
            "dup_parent_is_aoi\t"
            "dup_parent_aoi_area\t"
            "area_diff_ratio\t"
            "is_in_protect_list\t"
            "dup_exist_parent_bid\n"
        )
        file.write(header)
        for item in result:
            strs = (
                f"{item.park_bid}\t"
                f"{item.dup_park_bid}\t"
                f"{item.dup_park_city}\t"
                f"{item.dup_park_show_tag}\t"
                f"{item.dup_reason}\t"
                f"{item.dup_intersects_iou}\t"
                f"{item.dup_link_iou}\t"
                f"{item.dup_link_ioa}\t"
                f"{item.dup_link_iob}\t"
                f"{item.dup_two_parks_dist}\t"
                f"{item.dup_park_accept_ratio}\t"
                f"{item.dup_park_spec}\t"
                f"{item.dup_park_precise}\t"
                f"{item.dup_cover_aoi_tag}\t"
                f"{item.dup_park_click_pv}\t"
                f"{item.gap_line_across_high_road}\t"
                f"{item.dup_parent_std_tag}\t"
                f"{item.dup_parent_is_street_poi}\t"
                f"{item.dup_parent_is_in_storefront_park_area}\t"
                f"{item.dup_parent_is_aoi}\t"
                f"{item.dup_parent_aoi_area}\t"
                f"{item.area_diff_ratio}\t"
                f"{item.is_in_protect_list}\t"
                f"{item.dup_exist_parent_bid}\n"
            )
            file.write(strs)


if __name__ == "__main__":
    run()
