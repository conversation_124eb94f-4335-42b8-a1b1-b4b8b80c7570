"""
一组重复停车场，挑选出待下线的停车场
* 都精准就保留精准出入口多的，合并下出入口
* 一个精准一个不精准，就保留精准的
* 都不精准，或者精准出入口一致的，就保留高pv的
"""
import shapely.wkt
import tqdm as tqdm
import logging
import multiprocessing
import json
import sys
import multiprocessing
import re

from functools import partial
from dataclasses import dataclass, field, asdict
from typing import List, Tuple
from src.parking import redundancy
from collections import defaultdict


# 添加日志配置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", stream=sys.stdout)
logger = logging.getLogger(__name__)


@dataclass
class DuplicatePark:
    """
    重复停车场信息
    """

    park_bid: str = field(default="", init=False)
    dup_park_bid: str = field(default="", init=False)
    park_click_pv: float = field(default=0, init=False)
    dup_park_click_pv: float = field(default=0, init=False)
    final_pick_dup_park_bid: str = field(default="", init=False)
    final_pick_dup_park_bid_reason: str = field(default="", init=False)
    park_name: str = field(default="", init=False)
    dup_park_name: str = field(default="", init=False)
    show_tag: str = field(default="", init=False)
    memo: str = field(default="", init=False)
    two_park_dist: float = field(default=0, init=False)
    is_parent_id_same: bool = field(default=False, init=False)
    aoi_std_tag: str = field(default="", init=False)
    gap_line_across_high_road: bool = field(default=False, init=False)
    update_parent_id: dict = field(default_factory=dict, init=False)  # 出入口继承，需要改父子关系


def run():
    """
    run
    """
    dup_parks = get_dup_parks()
    logger.info("get_dup_parks: %d", len(dup_parks))

    dup_parks = process_duplicate_parks(dup_parks)
    logger.info("process_duplicate_parks: %d", len(dup_parks))

    dup_parks = filter_risks(dup_parks)
    logger.info("filter_risks: %d", len(dup_parks))

    _save_result_to_review(dup_parks, "./pick_duplicate_parks.csv")
    logger.info("_save_result_to_review: %d", len(dup_parks))

    _save_duplicate_data_to_db(dup_parks)
    logger.info("_save_duplicate_data_to_db: %d", len(dup_parks))


def process_duplicate_parks(dup_parks):
    """
    处理一组重复停车场，进行挑选
    * 一个精准一个不精准，就保留精准的
    * 都精准就保留精准出入口多的，合并下出入口
    * 都不精准，或者精准出入口一致的，就保留高pv的
    """
    new_dup_parks = []
    for dup in tqdm.tqdm(dup_parks):
        park_bid = dup.park_bid
        park_res = redundancy.get_park_data(park_bid)
        if not park_res:
            continue
        park_precise = park_res[0]["precise"]

        dup_park_bid = dup.dup_park_bid
        dup_park_res = redundancy.get_park_data(dup_park_bid)
        if not dup_park_res:
            continue
        dup_park_precise = dup_park_res[0]["precise"]

        if dup_park_bid == "304032842017480938":
            new_dup_parks.append(dup)
            continue

        if park_precise == 0 and dup_park_precise == 1:
            dup.final_pick_dup_park_bid = dup.park_bid
            dup.final_pick_dup_park_bid_reason = "一个精准一个不精准，就保留精准的"
        elif park_precise == 1 and dup_park_precise == 0:
            dup.final_pick_dup_park_bid = dup.dup_park_bid
            dup.final_pick_dup_park_bid_reason = "一个精准一个不精准，就保留精准的"
        elif park_precise == 0 and dup_park_precise == 0:
            if dup.park_click_pv >= dup.dup_park_click_pv:
                dup.final_pick_dup_park_bid = dup.dup_park_bid
                dup.final_pick_dup_park_bid_reason = "都不精准，就保留高pv的"
            else:
                dup.final_pick_dup_park_bid = dup.park_bid
                dup.final_pick_dup_park_bid_reason = "都不精准，就保留高pv的"
        elif park_precise == 1 and dup_park_precise == 1:
            if is_road_relation_same(park_bid, dup_park_bid):
                if dup.park_click_pv >= dup.dup_park_click_pv:
                    dup.final_pick_dup_park_bid = dup.dup_park_bid
                    dup.final_pick_dup_park_bid_reason = "精准出入口一致的，就保留高pv的"
                else:
                    dup.final_pick_dup_park_bid = dup.park_bid
                    dup.final_pick_dup_park_bid_reason = "精准出入口一致的，就保留高pv的"
            else:
                continue
                # park_rk_num = get_rk_num(park_bid)
                # dup_park_rk_num = get_rk_num(dup_park_bid)
                # if abs(dup.park_click_pv - dup.dup_park_click_pv) > 100:
                #     # 如果是pv差别很大，则把出入口合并到pv高的停车场，下线低pv停车场
                #     if dup.park_click_pv > dup.dup_park_click_pv:
                #         continue
                #     else:
                #         continue
                # else:
                #     # 如果是pv差别不大，则把出入口合并到出入口多的停车场
                #     if park_rk_num == dup_park_rk_num:
                #         continue
                #     elif park_rk_num > dup_park_rk_num:
                #         continue
                #     elif park_rk_num < dup_park_rk_num:
                #         continue
        new_dup_parks.append(dup)

    return new_dup_parks


# def update_crk_parent_id(park_bid, dup_park_bid):
#     """
#     更新出入口父子关系
#     把dup_park_bid的出口、出入口、入口的父子关系改到park_bid
#     注意要做30m内的去重
#     """
#     dup_park = redundancy.get_park_data(dup_park_bid)
#     if len(dup_park) == 0:
#         return
#
#     dup_park_road_relation_childrens = dup_park[0]['road_relation_childrens']
#     if dup_park_road_relation_childrens is None:
#         return
#
#
#     dup_link_info = dup_park_road_relation_childrens["link_info"]
#     for x in dup_link_info:


def is_road_relation_same(park_bid, dup_park_bid):
    """
    判断两个停车场入口是否相同
    """
    park = redundancy.get_park_data(park_bid)
    if len(park) == 0:
        return False
    dup_park = redundancy.get_park_data(dup_park_bid)
    if len(dup_park) == 0:
        return False

    park_road_relation_childrens = park[0]["road_relation_childrens"]
    dup_park_road_relation_childrens = dup_park[0]["road_relation_childrens"]
    if park_road_relation_childrens is None and dup_park_road_relation_childrens is None:
        return True
    if park_road_relation_childrens is None or dup_park_road_relation_childrens is None:
        return False

    park_ids = get_rk_ids(park_road_relation_childrens)
    dup_park_ids = get_rk_ids(dup_park_road_relation_childrens)
    if len(park_ids) != len(dup_park_ids):
        return False

    for id in park_ids:
        if id not in dup_park_ids:
            return False
    return True


def get_rk_num(park_bid):
    """
    获取入口数量
    """
    park = redundancy.get_park_data(park_bid)
    if len(park) == 0:
        return 0

    park_road_relation_childrens = park[0]["road_relation_childrens"]
    if park_road_relation_childrens is None:
        return 0

    rk_ids = get_rk_ids(park_road_relation_childrens)
    return len(rk_ids)


def get_rk_ids(road_relation_childrens):
    """
    获取入口的nodeid、linkid集合
    """
    ids = []
    link_info = road_relation_childrens["link_info"]
    node_ids = [x["node_id"] for x in link_info if x["type"] == 1 and x["orientation"] == 1]
    link_ids = [x["link_id"] for x in link_info if x["type"] == 2 and x["orientation"] == 1]
    ids += node_ids
    ids += link_ids
    return ids


def filter_risks(dup_parks):
    """
    过滤风险重复停车场
    """
    new_dup_parks = []
    for dup in tqdm.tqdm(dup_parks):
        dup_park_bid = dup.dup_park_bid

        # 过滤高PV
        poi = redundancy.get_poi_info(dup_park_bid)
        if len(poi) == 0:
            continue
        click_pv = poi[0]["click_pv"]
        if click_pv > 100:
            continue

        # 过滤距离特别远的
        two_park_dist = dup.two_park_dist
        if float(two_park_dist) > 500:
            continue

        # 新门前不下线
        dup_is_new_storefront = redundancy.is_park_storefront_prod_parking(dup_park_bid)
        if dup_is_new_storefront is not None:
            continue
        dup_is_new_storefront = redundancy.is_park_storefront_prod_parking(dup.final_pick_dup_park_bid)
        if dup_is_new_storefront is not None:
            continue

        # 两停车场名称包含PX，过滤
        if re.search(r"P[\da-zA-Z]+", dup.dup_park_name, re.IGNORECASE) or re.search(
            r"P[\da-zA-Z]+", dup.park_name, re.IGNORECASE
        ):
            continue

        new_dup_parks.append(dup)

    return new_dup_parks


def _save_result_to_log(dup_park: DuplicatePark):
    """
    保存重复停车场记录
    """
    park_bid, dup_park_bid, final_pick_dup_park_bid = (
        dup_park.park_bid,
        dup_park.dup_park_bid,
        dup_park.final_pick_dup_park_bid,
    )
    redundancy.insert_duplicate_log(
        park_bid, dup_park_bid, final_pick_dup_park_bid, json.dumps(asdict(dup_park), ensure_ascii=False, default=str)
    )


def _save_duplicate_data_to_db(result: List[DuplicatePark]) -> None:
    """
    将重复停车场数据存到库中
    """
    for dup_park in result:
        _save_result_to_log(dup_park)


def _save_result_to_review(result: List[DuplicatePark], output_path: str):
    """
    导出评估数据
    """
    with open(output_path, "w") as file:
        header = (
            "park_bid\t"
            "dup_park_bid\t"
            "park_click_pv\t"
            "dup_park_click_pv\t"
            "dup_park_show_tag\t"
            "dup_park_name\t"
            "is_parent_id_same\t"
            "park_name\t"
            "two_park_dist\t"
            "final_pick_dup_park_bid\t"
            "final_pick_dup_park_bid_reason\t"
            "aoi_std_tag\t"
            "memo\n"
        )
        file.write(header)
        for item in result:
            strs = (
                f"{item.park_bid}\t"
                f"{item.dup_park_bid}\t"
                f"{item.park_click_pv}\t"
                f"{item.dup_park_click_pv}\t"
                f"{item.show_tag}\t"
                f"{item.dup_park_name}\t"
                f"{item.is_parent_id_same}\t"
                f"{item.park_name}\t"
                f"{item.two_park_dist}\t"
                f"{item.final_pick_dup_park_bid}\t"
                f"{item.final_pick_dup_park_bid_reason}\t"
                f"{item.aoi_std_tag}\t"
                f"{item.memo}\n"
            )
            file.write(strs)


def get_dup_parks():
    """
    获取重复停车情报清单
    """
    parks = []
    with open("/home/<USER>/lifan14/output/aoi_inner_dup_parks.csv", "r", encoding="utf-8") as f:
        for line in tqdm.tqdm(f.readlines()):
            items = line.strip().split("\t")
            (
                park_bid,
                dup_park_bid,
                dup_park_show_tag,
                dup_park_name,
                is_parent_id_same,
                park_name,
                two_park_dist,
                aoi_std_tag,
                memo,
            ) = items
            dup = DuplicatePark()
            dup.park_bid = park_bid
            dup.dup_park_bid = dup_park_bid
            dup.show_tag = dup_park_show_tag
            dup.dup_park_name = dup_park_name
            dup.is_parent_id_same = is_parent_id_same
            dup.park_name = park_name
            dup.two_park_dist = two_park_dist
            dup.aoi_std_tag = aoi_std_tag
            dup.memo = memo

            park_poi = redundancy.get_poi_info(park_bid)
            if len(park_poi) == 0:
                continue
            dup.park_click_pv = park_poi[0]["click_pv"]

            dup_park_poi = redundancy.get_poi_info(dup_park_bid)
            if len(dup_park_poi) == 0:
                continue
            dup.dup_park_click_pv = dup_park_poi[0]["click_pv"]

            parks.append(dup)
    return parks


if __name__ == "__main__":
    """
    main
    """
    run()
