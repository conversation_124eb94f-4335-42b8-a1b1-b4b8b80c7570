"""
推送重复停车场下线
"""
from time import sleep

import requests
import logging
import tqdm as tqdm
import json

from src.parking import redundancy

logger = logging.getLogger(__name__)

PUSH_SUCCEED: int = 2  # 推送成功
PUSH_FAILED: int = -2  # 推送失败


def run():
    """
    run
    """
    dup_parks = redundancy.get_wait_push_duplicate_parks()
    logger.info(f"待推送重复停车场数量: {len(dup_parks)}")

    for dup_park in tqdm.tqdm(dup_parks):
        id, dup_park_bid, final_pick_dup_park_bid = dup_park
        if final_pick_dup_park_bid != "":
            dup_park_bid = final_pick_dup_park_bid

        try:
            _push_duplicate_park_offline(id, dup_park_bid)
            redundancy.pushed_then_duplicate_park_status(id, PUSH_SUCCEED)
            logger.info(f"成功推送下线: {id}, {dup_park_bid}")
        except Exception as e:
            redundancy.pushed_then_duplicate_park_status(id, PUSH_FAILED)
            logger.error(f"推送失败: {id}, {dup_park_bid}, 错误: {e}")


def _push_duplicate_park_offline(id, dup_park_bid):
    """
    推送单个重复停车场下线
    """
    _dataUpdateRequest(
        {"bid": dup_park_bid, "force_update": 1, "status": 2, "source": "STRATEGY_OFFLINE_DUPLICATE_PARK"}
    )
    logger.info(f"_push_duplicate_park_offline, {id}, {dup_park_bid}")
    sleep(0.5)


def _dataUpdateRequest(req_map):
    """
    发送更新请求
    """
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    payload = json.dumps(req_map)
    logger.info(payload)
    headers = {"Content-Type": "application/json"}
    response = requests.request("POST", url, headers=headers, data=payload)
    if response.status_code != 200:
        logger.error(f"Request failed with status {response.status_code}: {response.text}")
        raise Exception(f"Failed request: {response.status_code}")
    logger.info(response.text)
    print(response.text)


def _offline_duplicate_park_from_csv(output_path: str):
    """
    从文件获取重复停车场操作下线
    """
    with open(output_path, "r", encoding="utf-8") as f:
        for line in f.readlines():
            items = line.strip().split("\t")
            duplicate_park_bid = items[1]
            if duplicate_park_bid == "duplicate_park_bid":
                continue
            print(duplicate_park_bid)


if __name__ == "__main__":
    """
    main
    """
    run()
