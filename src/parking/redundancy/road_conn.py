"""
判断给定路网上两点是否连通
links = [
    (1, 2),
    (2, 3),
    (3, 4),
    (4, 5)
]

graph = build_graph(links)

print(is_connected(graph, 1, 5))         # True
print(is_connected(graph, 1, 1000))      # False（目标不存在）
print(is_connected(graph, 1, 5, 3))      # False（限制3步，走不到）
"""

from collections import defaultdict


def build_graph(links):
    """
    建图
    """
    graph = defaultdict(list)
    for start, end in links:
        graph[start].append(end)
        graph[end].append(start)  # 如果是有向图，注释这一行
    return graph


def is_connected(graph, node_a, node_b, max_steps=10000):
    """
    是否连通
    """
    if node_a not in graph or node_b not in graph:
        return False  # 防御：节点不存在

    visited = set()
    stack = [node_a]
    steps = 0

    while stack:
        if steps >= max_steps:
            print("Reach max step limit, possible infinite loop.")
            return False  # 防御：限制最大步数

        node = stack.pop()
        if node == node_b:
            return True
        if node in visited:
            continue
        visited.add(node)
        steps += 1

        for neighbor in graph[node]:
            if neighbor not in visited:
                stack.append(neighbor)

    return False
