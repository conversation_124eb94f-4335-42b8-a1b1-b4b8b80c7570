"""
冗余情报入库
"""
import tqdm
from src.parking.redundancy.base import rongyu_intel_to_db

CSV_PATH = "src/parking/redundancy/1.csv"


def run():
    """
    run
    """
    memo = "德金冗余情报库-临街批次"
    parks = get_parks()
    for park_bid in tqdm.tqdm(parks):
        if park_bid == "bid":
            continue
        rongyu_intel_to_db(park_bid, memo)


def get_parks():
    """
    获取停车场
    """
    parks = []
    with open(CSV_PATH, "r") as f:
        for row in f:
            items = row.strip().split("\t")
            if items:  # 避免空行出错
                park_bid = items[0]
                parks.append(park_bid)

    return parks


if __name__ == "__main__":
    """
    run
    """
    run()
