"""
处理 park_face_overlap 表中的自压盖数据，并将处理结果写入该表的 result 字段中。
result = 'retain' | 'discard'
"""
import json
from functools import cmp_to_key
from pathlib import Path

from tqdm import tqdm

from src.tools import pgsql, tsv

ORDER_PARKING = ["down", "luce", "park"]
RETAIN = "retain"
DISCARD = "discard"

get_connection = pgsql.get_connection_ttl

# business logic:


def import_parking_data(file_path: Path, batch: str):
    """
    导入待处理压盖的停车场数据
    """
    tsv_file = tsv.read_tsv(file_path, skip_header=True)
    items = [
        (bid, shape, tag, shape_type, batch, json.dumps({"face_id": area_id}))
        for bid, shape, tag, shape_type, relate_type, area_size, area_id, _ in tsv_file
        if shape
    ]
    sql = """
        INSERT INTO park_face_overlap (bid, geom, tag, shape_type, batch, info)
        VALUES (%s, st_geomfromtext(%s, 3857), %s, %s, %s, %s);
    """
    with get_connection(pgsql.POI_TEST_CONFIG) as conn:
        pgsql.execute_many(conn, sql, items)


def resolve_overlap(batch: str):
    """
    解决自压盖的停车场面
    """
    sql_get_all = """
        select id, st_astext(geom), st_area(geom) from park_face_overlap
        where batch = %s;
    """
    sql_get_overlap = """
        select id, tag, st_area(geom) from park_face_overlap
        where batch = %s and result = '' and st_intersects(geom, st_geomfromtext(%s, 3857));
    """
    # noinspection SqlWithoutWhere
    sql_clear_result = "update park_face_overlap set result = '';"
    sql_set_result = "update park_face_overlap set result = %s where id = %s;"

    with get_connection(pgsql.POI_TEST_CONFIG) as conn:
        # 清空结果，初始化计算状态
        pgsql.execute(conn, sql_clear_result)
        all_parks = pgsql.fetch_all(conn, sql_get_all, [batch])
        for pid, geom, area in tqdm(all_parks, desc="resolve overlap"):
            ret = pgsql.fetch_all(conn, sql_get_overlap, [batch, geom])
            self_contained = pid in [x[0] for x in ret]
            if not self_contained:
                # 1. 可能匹配不到自身，因为自身可能已被设置为 discard，故不需要处理了
                continue

            if len(ret) == 1:
                # 2. 匹配的是自身，即：无压盖
                pgsql.execute(conn, sql_set_result, [RETAIN, pid])
                continue

            park_overlaps = sorted(
                ret,
                key=cmp_to_key(lambda x, y: compare_park(x[1:], y[1:])),
                reverse=True,
            )
            max_overlap = park_overlaps[0]
            if max_overlap[0] == pid:
                # 3. 最高优先是自身，保留自身，丢弃其它
                pgsql.execute(conn, sql_set_result, [RETAIN, pid])
                for other_id, _, _ in park_overlaps[1:]:
                    pgsql.execute(conn, sql_set_result, [DISCARD, other_id])
            else:
                # 4. 最高优先不是自身，只能丢弃自身，不能保留其它，因为此处没有获得其它的所有压盖
                pgsql.execute(conn, sql_set_result, [DISCARD, pid])


# helpers:


def compare_park(park1: tuple[str, float], park2: tuple[str, float]) -> int:
    """
    根据 tag 和 面积 比较两个停车场大小，遵从导航 PM 定下的规则。
    """

    def convert_tag(tag: str):
        """
        aoi 就是直接使用了 aoi geom 的地上停车场，所以按 park 处理。
        """
        return "park" if tag == "aoi" else tag

    tag1, area1 = park1
    tag2, area2 = park2

    tag1 = convert_tag(tag1)
    tag2 = convert_tag(tag2)

    ret = ORDER_PARKING.index(tag1) - ORDER_PARKING.index(tag2)
    if ret != 0:
        return ret

    return -1 if area1 < area2 else 1


def main():
    """
    入口函数
    """
    file_path = Path(
        "/home/<USER>/dingping/parking/result.relate_show.4_city.final.filter.remain"
    )
    batch = "20240321"
    # import_parking_data(file_path, batch)  # 停车场数据导入一次就行了
    resolve_overlap(batch)


if __name__ == "__main__":
    main()
