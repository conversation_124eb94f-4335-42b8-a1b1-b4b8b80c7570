"""
路侧停车场自动化上线策略：用于自动化判断 DC 路场景的测线准确率。
分别用 区间叠加法 和 分段密度法 来处理 掘金结论 和 识别结果
- 20250224 整体随机 5000 样本：
TOP: 区间叠加法-掘金结论-xgb：precision: 0.9617 (15677/16301), recall: 0.8365 (15677/18741)
1. 区间叠加法-掘金结论：precision 98.1% (2425/2473), recall 72.8%
2. 分段密度法-掘金结论：precision 98.8% (1021/2473)
3. [不合格] 区间叠加法-识别结果：precision 78.5% (179/228)
4. [不合格] 分段密度法-识别结果：precision 65.6% (86/131)

P.S. 2 有 98.1% (1014/1033) 的 case 被 1 所包含，没有独立使用的价值。
"""
import math
from dataclasses import dataclass, field
from datetime import datetime
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Callable

import cv2
import numpy as np
import xgboost as xgb
from loguru import logger
from psycopg2.extras import Json
from shapely import wkt, LineString, Point
from tqdm import tqdm

from src.aikit import boundary, satellite_imagery, preview_image, algo
from src.parking.recognition import dbutils
from src.parking.roadside import storage
from src.parking.roadside.storage import Roadside, Image, Link
from src.parking.storefront import verify
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.tools import pgsql, tsv, utils, linq, pipeline

VERSION = "2.0.0"


JUEJIN_YES = "7"
JUEJIN_NO = "8"

SEG_LEFT = "left_car"
SEG_RIGHT = "right_car"
SEG_BOTH = "double_car"
SEG_NO = "no_car"

FEATURE_FRAGMENT_JUEJIN = "fragment.juejin"
FEATURE_FRAGMENT_SEG = "fragment.seg"
FEATURE_HEAT_JUEJIN = "heat.juejin"
FEATURE_HEAT_SEG = "heat.seg"

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")


@dataclass
class Feature:
    """
    特征
    """

    valid: float
    cover: float
    total: float
    num_valid_pts: int
    num_total_pts: int

    @property
    def cover_rate(self):
        """
        覆盖率
        """
        return self.cover / self.total

    @property
    def valid_rate(self):
        """
        有效率
        """
        return self.valid / self.total

    @property
    def point_valid_rate(self):
        """
        点位有效率
        """
        return self.num_valid_pts / self.num_total_pts


@dataclass
class Context:
    """
    上下文
    """

    work_id: int
    parking: Roadside
    juejin_images: list[Image] = field(default_factory=list)
    seg_images: list[Image] = field(default_factory=list)
    features: dict[str, Feature] = field(default_factory=dict)
    error: str = field(default="")


# pipes:


def must_be_dc(ctx: Context, proceed):
    """
    pipe: 必须是 DC 路
    """

    def is_dc(lns: list[Link]):
        mds = {ln.md for ln in lns}
        return len(mds) == 1 and 1 in mds

    if not is_dc(ctx.parking.links):
        ctx.error = "not_dc"
        return

    proceed()


def not_empty_links(ctx: Context, proceed):
    """
    pipe: links 不能为空
    """
    if not ctx.parking.links:
        ctx.error = "empty_links"
        return

    proceed()


def fetch_juejin_images(ctx: Context, proceed):
    """
    pipe: 获取掘金核实结论信息
    """
    ctx.juejin_images = [
        Image(image_id=image_id, conclusion=conclusion, north=north, geom=wkt.loads(geom))
        for ln in ctx.parking.links
        for image_id, geom, north, conclusion in _get_juejin_results(ln.link_id)
    ]
    proceed()


def fetch_seg_images(ctx: Context, proceed, provider: Callable[[int], list[Image]]):
    """
    pipe: 获取识别成果信息
    """
    ctx.seg_images = provider(ctx.work_id)
    proceed()


def calc_feature_fragment_juejin(ctx: Context, proceed, step: float, lead_threshold: float):
    """
    pipe: 分段密度法-掘金特征 "fragment.juejin"
    """
    roadside = ctx.parking
    images = ctx.juejin_images

    points = [(np.array([img.geom.x, img.geom.y]), verify.get_direction(img.north), img.conclusion) for img in images]
    # 平移众源图片，测量大约 17-18m 的提前量
    moved_points = [(p + d * lead_threshold, c) for p, d, c in points]
    moved_points = [(Point(*p), c) for p, c in moved_points]
    # 过滤平移后，不在测线上的点
    moved_points = [(p, c) for p, c in moved_points if geometric.get_foot_point(p, roadside.line)]
    projected_points = [(roadside.line.project(p), c) for p, c in moved_points]
    step_points = [(int(d // step), c) for d, c in projected_points]
    grouped_points = linq.group_by(step_points, key=lambda x: x[0], value=lambda x: x[1])
    results = [(k, any(x == JUEJIN_YES for x in v)) for k, v in grouped_points.items()]
    num_ok = sum(1 for k, is_valid in results if is_valid)
    num_cover = len(results)
    num_total = math.ceil(roadside.line.length / step)

    ctx.features[FEATURE_FRAGMENT_JUEJIN] = Feature(
        valid=num_ok,
        cover=num_cover,
        total=num_total,
        num_valid_pts=sum(1 for _, c in moved_points if c == JUEJIN_YES),
        num_total_pts=len(moved_points),
    )
    proceed()


def calc_feature_fragment_seg(ctx: Context, proceed, step: float, lead_threshold: float):
    """
    pipe: 分段密度法-识别特征 "fragment.seg"
    """
    roadside = ctx.parking
    images = ctx.seg_images

    num_total = math.ceil(roadside.line.length / step)
    if not images:
        ctx.feature_seg = 0, 0, num_total
        return proceed()

    # 转换所有结论为同向
    points = [(np.array([img.geom.x, img.geom.y]), verify.get_direction(img.north), img.conclusion) for img in images]
    # 平移众源图片，测量大约 17-18m 的提前量
    moved_points = [(p + d * lead_threshold, d, c) for p, d, c in points]
    moved_points = [(Point(*p), d, c) for p, d, c in moved_points]
    # 过滤平移后，不在测线上的点
    moved_points = [(p, d, c) for p, d, c in moved_points if geometric.get_foot_point(p, roadside.line)]
    moved_points = [
        (p, normalize_conclusion(c, d, get_direction_in_line(roadside.line, p))) for p, d, c in moved_points
    ]
    projected_points = [(roadside.line.project(p), c) for p, c in moved_points]
    step_points = [(int(d // step), c) for d, c in projected_points]
    grouped_points = linq.group_by(step_points, key=lambda x: x[0], value=lambda x: x[1])
    results = [(k, any(x in (SEG_RIGHT, SEG_BOTH) for x in v)) for k, v in grouped_points.items()]
    num_ok = sum(1 for k, is_valid in results if is_valid)
    num_cover = len(results)

    ctx.features[FEATURE_FRAGMENT_SEG] = Feature(
        valid=num_ok,
        cover=num_cover,
        total=num_total,
        num_valid_pts=sum(1 for _, c in moved_points if c in (SEG_RIGHT, SEG_BOTH)),
        num_total_pts=len(moved_points),
    )
    proceed()


def calc_feature_heat_juejin(ctx: Context, proceed, lead_threshold: float):
    """
    pipe: 区间叠加法-掘金特征 "heat.juejin"
    """
    line = ctx.parking.line
    images = ctx.juejin_images

    points = [(np.array([img.geom.x, img.geom.y]), verify.get_direction(img.north), img.conclusion) for img in images]
    # 平移众源图片，测量大约 17-18m 的提前量
    moved_points = [(p, p + d * lead_threshold, c) for p, d, c in points]
    moved_points = [(Point(*p0), Point(*p1), c) for p0, p1, c in moved_points]
    dists = [(line.project(p0), line.project(p1), c) for p0, p1, c in moved_points]
    ranges = [(min(d0, d1), max(d0, d1), c) for d0, d1, c in dists]
    ranges = [(d0, d1, c) for d0, d1, c in ranges if d0 > 0 and d1 < line.length and (d1 - d0) > 1e-8]

    total_ranges = [(d0, d1) for d0, d1, c in ranges]
    heat_ranges = algo.calc_overlap_ranges(total_ranges)
    cover_length = sum(d1 - d0 for (d0, d1), n in heat_ranges if n > 0)

    valid_ranges = [(d0, d1) for d0, d1, c in ranges if c == JUEJIN_YES]
    valid_heat_ranges = algo.calc_overlap_ranges(valid_ranges)
    valid_length = sum(d1 - d0 for (d0, d1), n in valid_heat_ranges if n > 0)

    ctx.features[FEATURE_HEAT_JUEJIN] = Feature(
        valid=valid_length / METER,
        cover=cover_length / METER,
        total=line.length / METER,
        num_valid_pts=len(valid_ranges),
        num_total_pts=len(total_ranges),
    )
    proceed()


def calc_feature_heat_seg(ctx: Context, proceed, lead_threshold: float):
    """
    pipe: 区间叠加法-识别特征 "heat.seg"
    """
    line = ctx.parking.line
    images = ctx.seg_images

    points = [(np.array([img.geom.x, img.geom.y]), verify.get_direction(img.north), img.conclusion) for img in images]
    # 平移众源图片，测量大约 17-18m 的提前量
    moved_points = [(p, p + d * lead_threshold, d, c) for p, d, c in points]
    moved_points = [(Point(*p0), Point(*p1), d, c) for p0, p1, d, c in moved_points]
    # 修正结论
    moved_points = [
        (p0, p1, normalize_conclusion(c, d, get_direction_in_line(line, p0))) for p0, p1, d, c in moved_points
    ]
    dists = [(line.project(p0), line.project(p1), c) for p0, p1, c in moved_points]
    ranges = [(min(d0, d1), max(d0, d1), c) for d0, d1, c in dists]
    ranges = [(d0, d1, c) for d0, d1, c in ranges if d0 > 0 and d1 < line.length and (d1 - d0) > 1e-8]

    total_ranges = [(d0, d1) for d0, d1, c in ranges]
    heat_ranges = algo.calc_overlap_ranges(total_ranges)
    cover_length = sum(d1 - d0 for (d0, d1), n in heat_ranges if n > 0)

    valid_ranges = [(d0, d1) for d0, d1, c in ranges if c in (SEG_RIGHT, SEG_BOTH)]
    valid_heat_ranges = algo.calc_overlap_ranges(valid_ranges)
    valid_length = sum(d1 - d0 for (d0, d1), n in valid_heat_ranges if n > 0)

    ctx.features[FEATURE_HEAT_SEG] = Feature(
        valid=valid_length / METER,
        cover=cover_length / METER,
        total=line.length / METER,
        num_valid_pts=len(valid_ranges),
        num_total_pts=len(total_ranges),
    )
    proceed()


def draw_preview_juejin(ctx: Context, proceed, save_dir: Path):
    """
    pipe: 绘制掘金预览图
    """
    roadside = ctx.parking

    bounds = boundary.from_wkt(roadside.line.wkt, buffer=30 * METER)
    image = satellite_imagery.crop(bounds)
    if image is None:
        return proceed()

    color = preview_image.COLOR_BLUE if roadside.is_valid else preview_image.COLOR_PURPLE
    preview_image.draw_linestring(image, roadside.line.wkt, bounds, thickness=4, color=color)
    color_map = {
        JUEJIN_YES: preview_image.COLOR_GREEN,
        JUEJIN_NO: preview_image.COLOR_RED,
    }
    for img in ctx.juejin_images:
        color = color_map.get(img.conclusion, preview_image.COLOR_YELLOW)
        preview_image.draw_point(image, img.geom.wkt, bounds, radius=4, color=color)
        direction = verify.get_direction(img.north)
        p0 = np.array([img.geom.x, img.geom.y])
        p1 = p0 + direction * 5 * METER
        preview_image.draw_arrow(image, p0, p1, bounds, color=color)

    preview_path = utils.ensure_path(save_dir / f"{ctx.work_id}.jpg")
    cv2.imwrite(str(preview_path), image)
    proceed()


# helpers:


def get_direction_in_line(ln: LineString, point: Point):
    """
    获取点投影在测线上的方向（沿着测线的方向）
    """
    d = ln.project(point)
    p0, p1 = ln.interpolate(d - 1 * METER), ln.interpolate(d + 1 * METER)
    v = np.array([p1.x - p0.x, p1.y - p0.y])
    return v / np.linalg.norm(v)


def normalize_conclusion(conclusion: str, pt_dir: np.ndarray, ln_dir: np.ndarray):
    """
    规范化结论，使其与测线方向一致
    """
    if conclusion in (SEG_BOTH, SEG_NO):
        return conclusion

    is_reverse = np.dot(pt_dir, ln_dir) < 0
    if not is_reverse:
        return conclusion

    if conclusion == SEG_LEFT:
        return SEG_RIGHT
    elif conclusion == SEG_RIGHT:
        return SEG_LEFT

    return SEG_NO


def get_seg_images_provider(file_path: Path):
    """
    获取别图片提供器
    """
    source = utils.read_json(file_path)

    def getter(work_id: int):
        items = source.get(str(work_id), None)
        if not items:
            return []

        return [
            Image(image_id=o["pic_id"], conclusion=o["rec_res"], north=o["north"], geom=wkt.loads(o["point"]))
            for o in items
        ]

    return getter


def _get_juejin_results(link_id: str):
    sql = """
        select distinct a.image_id, st_astext(point), north, b.conclusion
        from parking_line_jujin_prev a 
        join park_storefront_verify b on a.image_id = b.image_id
        where a.link_id = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [link_id])
    return ret


# io:


def _get_todo_work_ids():
    sql = """
        select id from parking_line_turing_work
        where status = -1 and message = 'pending_for_dc'
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return [x[0] for x in ret]


def generate_contexts(work_ids: list[int]):
    """
    生成上下文（使用生成器，节约内存）
    """
    for roadside in storage.get_roadside_by_ids(work_ids):
        yield Context(work_id=roadside.work_id, parking=roadside)


def get_predictor(model_path: Path):
    """
    返回一个预测函数，用于预测单个 case
    """
    model = xgb.XGBClassifier()
    model.load_model(model_path)

    def predict(x: list[float]) -> int:
        x = np.array([x])
        y_pred_proba = model.predict_proba(x)[:, 1]
        threshold = 0.8  # 可以调整此值以找到合适的精确率-召回率平衡
        y_pred = (y_pred_proba > threshold).astype(int)
        return y_pred[0]

    return predict


def save_to_db(roadside: Roadside, x: list[float], y_hat: int):
    """
    保存结果到数据库，包括预测结果和策略备份
    """
    result_auto = 1
    result_manual = 2
    sql = """
        update parking_line_turing_work
        set status = %s, message = %s, side_json = %s, updated_at = now()
        where id = %s
    """
    sql_strategy = """
        insert into parking_line_automatic_strategy 
        (work_id, strategy_name, strategy_params, strategy_result, strategy_batch)
        values (%s, %s, %s, %s, %s)
    """

    def create_side_json(links: list[Link]):
        return {
            link.link_id: storage.TURING_RIGHT if link.dir == storage.DIR_NORMAL else storage.TURING_LEFT
            for link in links
        }

    result = [
        storage.STATUS_DONE,
        storage.MESSAGE_AUTO if y_hat == 1 else storage.MESSAGE_MANUAL,
        Json(create_side_json(roadside.links)),
        roadside.work_id,
    ]
    dbutils.execute(pgsql.POI_CONFIG, sql, result)

    params = {
        "model": "xgb_roadside_dc_heat_juejin_5d_v1",
        "x": x,
    }
    strategy = [
        roadside.work_id,
        f"roadside_dc-{VERSION}",
        Json(params),
        result_auto if y_hat == 1 else result_manual,
        timestamp,
    ]
    dbutils.execute(pgsql.POI_CONFIG, sql_strategy, strategy)


def mark_unsolved_to_db(work_id: int, message="dc_unsolved"):
    """
    标记未解决的 case
    """
    sql = """
        update parking_line_turing_work
        set message = %s, updated_at = now()
        where id = %s
    """
    dbutils.execute(pgsql.POI_CONFIG, sql, [message, work_id])


@logger.catch
def main():
    """
    主函数
    """
    current_dir = Path(__file__).parent
    model_path = current_dir / "models" / "xgb_roadside_dc_heat_juejin_5d_v1.json"
    error_path = utils.ensure_path(current_dir / "products" / f"roadside_auto-{VERSION}-{timestamp}.log")

    pipe = pipeline.Pipeline(
        must_be_dc,
        not_empty_links,
        # 掘金数据处理
        fetch_juejin_images,
        partial(calc_feature_heat_juejin, lead_threshold=18 * METER),
        # partial(calc_feature_fragment_juejin, step=10 * METER, lead_threshold=18 * METER),
        # partial(draw_preview_juejin, save_dir=output_dir / "preview"),
        # 识别数据处理
        # partial(fetch_seg_images, provider=get_seg_images_provider(Path("varea_tracks_result_335.json"))),
        # partial(calc_feature_heat_seg, lead_threshold=18 * METER),
        # partial(calc_feature_fragment_seg, step=20 * METER, lead_threshold=18 * METER),
    )
    predict = get_predictor(model_path)

    work_ids = _get_todo_work_ids()
    if not work_ids:
        logger.info("nothing to do")
        return

    with Pool(8) as p:
        # 生成特征
        for ctx in tqdm(p.imap_unordered(pipe, generate_contexts(work_ids)), total=len(work_ids)):
            if ctx.error:
                mark_unsolved_to_db(ctx.work_id)
                tsv.write_tsv(error_path, [[ctx.work_id, ctx.error]], mode="a")
                continue

            # 预测
            f = ctx.features[FEATURE_HEAT_JUEJIN]
            x = [f.valid, f.cover, f.total, f.num_valid_pts, f.num_total_pts]
            y_hat = predict(x)
            # 保存到 db
            save_to_db(ctx.parking, x, y_hat)


if __name__ == "__main__":
    main()
