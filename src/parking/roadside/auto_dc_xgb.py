"""
利用 xgboost 模型，对 auto_dc_strategy.py 所生成的特征文件进行训练和评估。

备份信息：

- 数据集格式：[valid, cover, total, num_valid_pts, num_total_pts, turning_result]
- 数据集：/user/map-data-streeview/aoi-ml/parking/roadside/models/xgb_roadside_dc_heat_juejin_5d_v1.dataset.tsv
- 模型：/user/map-data-streeview/aoi-ml/parking/roadside/models/xgb_roadside_dc_heat_juejin_5d_v1.json
"""
import sys
from pathlib import Path

import numpy as np
import xgboost as xgb
from loguru import logger
from sklearn.metrics import precision_score, recall_score
from sklearn.model_selection import train_test_split

from src.tools import tsv


def _to_x_5d(rows: list[tuple]):
    idx_valid = 3
    idx_cover = 4
    idx_total = 5
    idx_num_valid_pts = 8
    idx_num_total_pts = 9
    x = [
        [
            float(row[idx_valid]),
            float(row[idx_cover]),
            float(row[idx_total]),
            float(row[idx_num_valid_pts]),
            float(row[idx_num_total_pts]),
        ]
        for row in rows
    ]
    return np.array(x)


def _to_y(rows: list[tuple]):
    idx_turning_result = 2
    y = [(1 if int(row[idx_turning_result]) == 1 else 0) for row in rows]
    return np.array(y)


def train(data_path: Path, model_path: Path):
    """
    模型训练
    """
    rows = list(tsv.read_tsv(data_path))
    x, y = _to_x_5d(rows), _to_y(rows)

    x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.2, random_state=42)
    model = xgb.XGBClassifier(
        n_estimators=100,  # 增加树的数量
        max_depth=4,  # 增加树的深度
        learning_rate=0.1,  # 降低学习率
        objective="binary:logistic",
        reg_alpha=0.01,  # L1 正则化
        reg_lambda=0.01,  # L2 正则化
    )
    model.fit(x_train, y_train)
    # 调整阈值
    y_pred_proba = model.predict_proba(x_test)[:, 1]
    threshold = 0.8  # 可以调整此值以找到合适的精确率-召回率平衡
    y_pred = (y_pred_proba > threshold).astype(int)

    # 计算评估指标
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)

    # 输出评估指标
    print(f"p-precision: {precision:.4f}")
    print(f"p-recall: {recall:.4f}")

    model.save_model(model_path)


def evaluate(model_path: Path, data_path: Path):
    """
    模型评估
    """
    rows = list(tsv.read_tsv(data_path))
    x, y = _to_x_5d(rows), _to_y(rows)

    model = xgb.XGBClassifier()
    model.load_model(model_path)

    y_pred_proba = model.predict_proba(x)[:, 1]
    threshold = 0.8  # 可以调整此值以找到合适的精确率-召回率平衡
    y_pred = (y_pred_proba > threshold).astype(int)

    # 计算评估指标
    tp = sum(1 for i, j in zip(y, y_pred) if i == 1 and j == 1)
    tn = sum(1 for i, j in zip(y, y_pred) if i == 0 and j == 0)
    fp = sum(1 for i, j in zip(y, y_pred) if i == 0 and j == 1)
    fn = sum(1 for i, j in zip(y, y_pred) if i == 1 and j == 0)

    p_precision = tp / (tp + fp)
    p_recall = tp / (tp + fn)

    n_precision = tn / (tn + fn)
    n_recall = tn / (tn + fp)

    # 输出评估指标
    print(f"p-precision: {p_precision:.4f} ({tp}/{tp + fp})")
    print(f"p-recall: {p_recall:.4f} ({tp}/{tp + fn})")

    # 输出评估指标
    print(f"n-precision: {n_precision:.4f} ({tn}/{tn + fn})")
    print(f"n-recall: {n_recall:.4f} ({tn}/{tn + fp})")

    merged_rows = [[*row, p] for row, p in zip(rows, y_pred)]
    tsv.write_tsv(data_path.parent / f"{data_path.stem}.{model_path.stem}.tsv", merged_rows)


@logger.catch
def main(model_path: Path, feature_path: Path):
    """
    主函数
    """
    # train(feature_path, model_path)
    evaluate(model_path, feature_path)


if __name__ == "__main__":
    main(Path(sys.argv[1]), Path(sys.argv[2]))
