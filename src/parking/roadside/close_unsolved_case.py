"""
用于闭环 parking_line_turing_work 表中最后一个自动化策略都无法解决的 case，转发给人工制作。

parking_line_turing_work.message 字段合理的规范应该是：

1. 每个策略（环节）在处理完 case 后，将自身无法处理（不属于自身场景）的 case 标记为 `{strategy_name}_unsolved`
2. 每个策略需要知道自己的上一个策略（环节）是什么，并且只处理 `{previous_strategy_name}_unsolved` 的 case
3. 本脚本负责 close 最后一个策略无法解决的 case

因为每个策略理论上只关心自己的场景，自己 cover 不到的 case 不应该直接 close，它没有资格。
这个方案是 workaround，尽量做到符合开闭原则，如果后续策略要插入到之前策略的中间，还是需要修改之前策略的代码的。
"""
import sys

from src.parking.roadside import storage
from src.tools import pgsql


def main(latest_messages: list[str]):
    """
    主函数
    """
    sql = """
        update parking_line_turing_work
        set status = %s, message = %s, updated_at = now()
        where message in %s
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute(conn, sql, [storage.STATUS_DONE, storage.MESSAGE_MANUAL, tuple(latest_messages)])


if __name__ == "__main__":
    main(sys.argv[1:])
