{"learner": {"attributes": {"best_iteration": "99", "best_ntree_limit": "100", "scikit_learn": "{\"use_label_encoder\": null, \"n_estimators\": 100, \"objective\": \"binary:logistic\", \"max_depth\": 4, \"max_leaves\": null, \"max_bin\": null, \"grow_policy\": null, \"learning_rate\": 0.1, \"verbosity\": null, \"booster\": null, \"tree_method\": null, \"gamma\": null, \"min_child_weight\": null, \"max_delta_step\": null, \"subsample\": null, \"sampling_method\": null, \"colsample_bytree\": null, \"colsample_bylevel\": null, \"colsample_bynode\": null, \"reg_alpha\": 0.01, \"reg_lambda\": 0.01, \"scale_pos_weight\": null, \"base_score\": null, \"missing\": NaN, \"num_parallel_tree\": null, \"random_state\": null, \"n_jobs\": null, \"monotone_constraints\": null, \"interaction_constraints\": null, \"importance_type\": null, \"gpu_id\": null, \"validate_parameters\": null, \"predictor\": null, \"enable_categorical\": false, \"feature_types\": null, \"max_cat_to_onehot\": null, \"max_cat_threshold\": null, \"eval_metric\": null, \"early_stopping_rounds\": null, \"callbacks\": null, \"classes_\": [0.0, 1.0], \"n_classes_\": 2, \"_estimator_type\": \"classifier\"}"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "100", "size_leaf_vector": "0"}, "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [1.4499755, 0.42443165, 1.7827446, 0.086564146, 0.86367476, 1.2023265, 1.8481525, 0.5209125, -0.37016737, 0.9325065, -1.9880478, 1.3742001, -0.3208763, 1.8688097, -0.0, 0.68265325, -0.52471083, -0.23154823, -1.2408208, 1.0676026, 0.3369678, 1.746758, 1.1214339, -1.1064302, 0.76380366, 1.8730441, -0.3888889, -1.9900333, 1.3281597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [341.24316, 36.350697, 28.610352, 27.481133, 20.914726, 20.045143, 25.903809, 12.014362, 8.130133, 8.3504715, 0.0, 6.4268494, 6.6239758, 6.437256, 19.875977, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.061596, 17.264828, 2.5, 22.034046, 40.5, 113.42148, 507.95148, 36.81784, 87.21457, 70.4629, -0.19880478, 6.5, 35.56208, 415.30353, 210.8233, 0.06826533, -0.052471083, -0.023154823, -0.12408208, 0.10676026, 0.03369678, 0.1746758, 0.11214339, -0.11064302, 0.076380365, 0.18730442, -0.03888889, -0.19900332, 0.13281597], "split_indices": [0, 0, 3, 1, 4, 2, 2, 2, 2, 1, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [1000.0, 245.0, 755.0, 138.5, 106.5, 76.5, 678.5, 71.0, 67.5, 104.0, 2.5, 68.75, 7.75, 671.0, 7.5, 61.5, 9.5, 58.25, 9.25, 84.75, 19.25, 27.75, 41.0, 4.5, 3.25, 669.75, 1.25, 3.0, 4.5], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [1.314093, 0.38261414, 1.6183722, 0.07794489, 0.77935094, 1.0874426, 1.6784437, 0.5591633, -0.26343858, 0.8660196, -1.0788777, 1.2084035, -0.73235446, 1.6972469, -0.0, 0.7425246, -0.46047422, -0.14667146, -1.1431535, 1.1808901, 0.6801132, -0.26672786, -1.8083651, 1.2499454, -1.1129843, -1.3426615, 0.3094068, 1.7130176, 1.1694986, -1.8100873, 1.2011023], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [281.5243, 29.558594, 23.834717, 22.7389, 17.117027, 16.776566, 21.231201, 10.746695, 8.30089, 5.9157104, 2.7788262, 6.9021835, 3.0328467, 5.4874268, 16.243126, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.061596, 17.264828, 2.5, 1.5, 30.5, 28.0, 507.95148, 36.2247, 87.21457, 4.5, 40.5, 192.65976, 165.04265, 350.7863, 210.8233, 0.074252464, -0.046047423, -0.014667146, -0.11431535, 0.11808901, 0.06801132, -0.026672786, -0.18083651, 0.12499454, -0.111298434, -0.13426615, 0.03094068, 0.17130177, 0.11694986, -0.18100874, 0.12011023], "split_indices": [0, 0, 3, 4, 4, 4, 2, 2, 2, 4, 4, 2, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [993.36005, 244.60751, 748.7525, 138.37854, 106.228966, 76.14228, 672.6102, 57.43723, 80.941315, 101.505424, 4.723541, 71.4049, 4.737385, 665.15955, 7.450706, 48.69325, 8.74398, 71.477005, 9.464314, 37.64264, 63.862785, 2.2480812, 2.4754598, 70.15832, 1.2465816, 2.9915318, 1.745853, 645.81915, 19.340393, 2.970493, 4.480213], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [1.2046145, 0.35429463, 1.4930276, 0.06698774, 0.71294385, 1.0050678, 1.5459394, 0.4372916, -0.32323542, 0.76669663, -1.6730247, 1.1398262, -0.46923634, 1.563356, -0.0, 0.5718313, -0.42729148, -0.21313092, -1.0181165, 0.8868253, 0.2616626, 1.1995059, -0.3686291, -0.94229466, 0.46865955, 1.5672827, -0.466996, -1.4690189, 1.2627382], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [239.40735, 25.473553, 18.781128, 19.846893, 14.108269, 14.190392, 17.716309, 8.202269, 5.100881, 6.511921, 0.0, 5.900032, 2.6613002, 5.203003, 13.622399, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5, 17.264828, 2.5, 22.034046, 40.5, 24.5, 507.95148, 36.81784, 87.21457, 69.647736, -0.16730247, 122.87525, 165.04265, 415.30353, 223.00488, 0.057183128, -0.04272915, -0.021313092, -0.10181165, 0.08868253, 0.02616626, 0.11995059, -0.03686291, -0.09422947, 0.046865955, 0.15672827, -0.046699602, -0.14690189, 0.12627383], "split_indices": [3, 0, 3, 1, 4, 4, 2, 2, 2, 1, 0, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [976.2896, 247.28459, 729.005, 137.31999, 109.96461, 71.354546, 657.65045, 70.470825, 66.84916, 107.55257, 2.412041, 65.386925, 5.9676213, 650.32745, 7.3230395, 60.99432, 9.476509, 57.72935, 9.119807, 86.8636, 20.68896, 62.906796, 2.4801316, 3.9738805, 1.9937407, 649.07935, 1.2480977, 3.3863354, 3.9367042], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [1.1135947, 0.3126521, 1.3867989, 0.044548646, 0.6280142, 0.9081317, 1.4425001, 0.35409075, -0.42527354, 0.7141882, -0.5146958, 1.0460064, -0.2847197, 1.4743276, 0.68694955, 0.48516372, -0.20721, -0.34886622, -1.7023505, 0.98707587, 0.5516378, -0.18204638, -1.564908, 1.3940988, 0.8130314, -0.9510656, 0.6341044, 1.3008472, 1.5101987, -0.68046385, 1.2318475], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [208.29651, 20.467478, 18.889648, 19.039658, 10.961132, 12.187313, 15.257935, 5.8112974, 5.0463724, 4.5686874, 2.7043781, 5.349594, 4.706054, 3.7487793, 19.187347, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.061596, 14.528929, 2.5, 24.565266, 22.5, 113.42148, 350.7863, 29.44632, 149.59767, 4.5, 44.0, 6.5, 35.56208, 4.5, 337.83762, 0.048516374, -0.020721002, -0.034886625, -0.17023505, 0.09870759, 0.055163782, -0.018204639, -0.1564908, 0.13940988, 0.08130314, -0.095106564, 0.06341044, 0.13008472, 0.15101987, -0.06804638, 0.123184755], "split_indices": [0, 0, 3, 1, 4, 2, 2, 2, 2, 4, 4, 4, 0, 3, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [951.99097, 242.1492, 709.8418, 130.90451, 111.24468, 74.02885, 635.8129, 78.927155, 51.977364, 103.45541, 7.789275, 66.36744, 7.661406, 610.0902, 25.722694, 64.00103, 14.926124, 49.062523, 2.9148407, 38.57834, 64.87707, 5.931696, 1.8575795, 26.572384, 39.795055, 4.4469438, 3.214462, 104.647095, 505.44315, 7.3235397, 18.399155], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [1.0357118, 0.2908068, 1.3034225, 0.053619165, 0.5911091, 0.9693564, 1.3734262, 0.43492657, -0.22390474, 0.6552837, -0.8827949, 1.0265399, -0.98792917, 1.4082724, 0.63188195, 0.56767106, -0.28410643, -0.1359735, -0.8963145, 1.1556116, 0.5772026, -0.26200467, -1.4988791, 1.0874115, -0.03790939, -1.658055, 0.21964642, 1.3317066, 1.4446721, -0.7098616, 1.1093749], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [184.0669, 17.377956, 15.843506, 14.436703, 10.189236, 13.184303, 14.476196, 5.490141, 4.655381, 4.0078278, 1.6960261, 7.421547, 2.721881, 1.4471436, 16.1736, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5, 17.264828, 3.5, 18.041077, 30.5, 194.71904, 350.7863, 36.2247, 87.21457, 19.239784, 40.5, 27.5, 43.0, 7.5, 328.19287, 0.056767106, -0.028410643, -0.01359735, -0.08963145, 0.115561165, 0.057720263, -0.026200468, -0.1498879, 0.10874116, -0.003790939, -0.1658055, 0.021964643, 0.13317066, 0.14446722, -0.07098616, 0.11093749], "split_indices": [3, 0, 3, 1, 4, 2, 2, 2, 2, 1, 4, 4, 4, 3, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [923.1044, 244.06146, 679.0429, 136.38536, 107.6761, 117.69231, 561.3506, 57.463303, 78.92205, 103.19527, 4.480838, 114.36102, 3.3312783, 536.1344, 25.216177, 48.521503, 8.9418, 69.818726, 9.103325, 13.894696, 89.30057, 2.2470787, 2.2337592, 108.183624, 6.1774035, 2.1453176, 1.1859609, 172.96297, 363.17142, 6.6116867, 18.60449], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.96766907, 0.37459585, 1.2824368, 0.03817708, 0.61750317, 1.2984622, -0.06256307, 0.3021279, -0.36071196, 0.6865386, -0.54858094, 1.302685, -0.66217, -1.5044785, 0.86951643, 0.4141957, -0.1727197, -1.4203061, -0.26540357, 0.8700808, 0.42933974, -1.826901, -0.37766716, 1.1384465, 1.3361121, 0.058653206, 1.5304449], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [166.37146, 25.249683, 12.5633545, 13.647531, 14.451813, 4.779297, 9.236593, 4.1551147, 5.190833, 7.9820557, 2.1620195, 3.1124268, 0.0, 0.0, 2.216722, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 14.528929, 507.95148, 24.565266, 27.5, 415.30353, 210.8233, 29.44632, 25.99505, 8.5, 54.59361, 4.5, -0.066217, -0.15044785, 452.3753, 0.04141957, -0.017271971, -0.14203061, -0.026540358, 0.08700808, 0.042933974, -0.1826901, -0.037766717, 0.11384465, 0.13361122, 0.0058653206, 0.15304449], "split_indices": [3, 0, 2, 1, 4, 0, 0, 2, 1, 4, 1, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [891.31506, 309.05646, 582.2586, 129.60728, 179.44917, 575.4105, 6.8481107, 78.01266, 51.594635, 169.43108, 10.018104, 574.18225, 1.2282307, 2.6905112, 4.1575994, 63.1347, 14.877953, 4.237718, 47.35692, 98.83659, 70.59448, 1.163147, 8.854957, 97.20948, 476.9728, 1.8787113, 2.2788882], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.9068728, 0.34119034, 1.2198126, 0.034477614, 0.56533265, 1.2510917, 0.52066815, 0.27321497, -0.32585394, 0.80195844, 0.27919224, 1.0733055, 1.2889905, -1.5623943, 0.78706276, 0.3748868, -0.1556051, -1.4588779, -0.2501809, 0.51556826, 1.0272387, -0.5199962, 0.43021458, 1.1210246, -1.7608546, 1.3157208, 1.1992587, -1.9406474, -1.009685, -0.2935984, 0.95128506], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [151.89966, 21.008839, 12.058289, 11.106017, 11.943981, 3.5256958, 13.151055, 3.3892407, 4.3893027, 6.214115, 9.653275, 12.594833, 1.0043945, 0.5123043, 3.7389898, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 14.528929, 350.7863, 24.565266, 8.5, 4.5, 219.44946, 29.44632, 25.407284, 31.035679, 52.406517, 199.22542, 191.4157, 200.00272, 59.5, 0.03748868, -0.01556051, -0.14588779, -0.02501809, 0.051556826, 0.102723874, -0.051999625, 0.04302146, 0.112102464, -0.17608546, 0.13157208, 0.11992587, -0.19406474, -0.1009685, -0.029359842, 0.09512851], "split_indices": [3, 0, 2, 1, 4, 3, 1, 2, 1, 2, 1, 2, 2, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [858.1729, 305.67514, 552.49774, 129.08856, 176.58656, 528.8168, 23.68097, 77.66311, 51.425457, 96.62713, 79.959435, 93.01828, 435.7985, 2.6753085, 21.005661, 62.798393, 14.864717, 3.1998825, 48.225574, 42.577408, 54.049725, 12.6939945, 67.26544, 91.487465, 1.5308139, 335.58307, 100.21545, 1.5626712, 1.1126372, 2.7619486, 18.243711], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.8516418, 0.31064096, 1.164968, 0.041952375, 0.5275517, 1.1964682, 0.4821334, 0.34026787, -0.17172988, 0.59803396, -0.6098245, 1.0152369, 1.2356017, -0.5715962, 0.9405809, 0.2627512, 1.4583427, -0.09285669, -0.7941614, 0.750454, 0.39252898, -0.16400774, -1.1729361, 1.0621321, -1.4979495, 1.2629066, 1.1444435, -1.440881, -0.046117164, 1.1306747, -0.13541229], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [139.79407, 17.625645, 11.211609, 8.618328, 13.420658, 3.5056763, 11.150028, 4.870061, 3.8560376, 4.9226456, 2.4357016, 10.472328, 0.98321533, 3.17809, 3.3006735, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 17.264828, 350.7863, 18.041077, 27.5, 4.5, 337.83762, 17.998516, 87.21457, 8.5, 40.5, 199.22542, 191.4157, 219.44946, 77.5, 0.026275119, 0.14583427, -0.009285669, -0.07941614, 0.0750454, 0.0392529, -0.016400775, -0.11729361, 0.10621321, -0.14979495, 0.12629066, 0.11444435, -0.1440881, -0.0046117166, 0.11306747, -0.013541229], "split_indices": [3, 0, 2, 1, 4, 3, 1, 1, 2, 4, 4, 2, 2, 1, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [824.79517, 302.51434, 522.2808, 135.15504, 167.3593, 499.22845, 23.05237, 56.42402, 78.73103, 157.60611, 9.753186, 88.74854, 410.47992, 6.981437, 16.070932, 52.78566, 3.6383605, 69.900894, 8.830134, 90.44742, 67.15869, 5.460649, 4.2925377, 87.13244, 1.6161113, 315.68375, 94.79618, 2.6165323, 4.3649044, 13.665463, 2.4054685], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.80152905, 0.28318778, 1.1163958, 0.025955766, 0.47576022, 1.1481088, 0.44893432, 0.22078027, -0.28654405, 0.5625474, -0.17635809, 0.96248555, 1.1887618, -0.5208164, 0.88385516, 0.31568873, -0.38990456, -0.49079096, 0.13342066, 0.38963124, 0.70371777, -0.0, -1.0102156, 1.1098884, 0.19800012, 1.2199262, 1.1036835, -1.3363106, -0.041499585, 1.0753719, -0.16682678], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [129.09952, 14.805347, 10.396118, 7.7951384, 9.680401, 3.512085, 9.435995, 4.5730305, 4.2204394, 3.6697884, 2.9540954, 9.506889, 0.98498535, 2.6906557, 3.1183424, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 14.528929, 350.7863, 3.5, 19.5, 4.5, 337.83762, 37.329853, 49.05276, 17.997099, 44.0, 19.5, 180.34677, 219.44946, 155.5, 0.031568874, -0.038990457, -0.049079098, 0.013342067, 0.038963124, 0.07037178, -0.0, -0.101021565, 0.11098885, 0.019800013, 0.121992625, 0.11036835, -0.13363107, -0.0041499585, 0.10753719, -0.016682679], "split_indices": [3, 0, 2, 4, 4, 3, 1, 2, 1, 0, 4, 4, 2, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [791.10547, 298.97552, 492.1299, 128.02594, 170.9496, 469.7868, 22.343126, 78.88248, 49.143456, 150.88568, 20.063915, 84.490776, 385.29602, 6.9102507, 15.432876, 68.29045, 10.592029, 33.07822, 16.065235, 67.86631, 83.01937, 16.567486, 3.4964306, 70.81845, 13.672328, 281.81528, 103.48074, 2.5437143, 4.366536, 13.061355, 2.371521], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.75491595, 0.25801462, 1.072367, 0.10503518, 0.53427285, 1.1034124, 0.39763135, 0.27032253, -0.067098014, 0.5821527, -0.9544176, 0.9855404, 1.1774576, -0.53457403, 0.858407, 0.33523178, -0.21654293, -0.73231435, 0.014077998, 0.7615084, 0.39215872, -1.4720595, -0.01383989, 1.039061, -1.0042709, 1.183545, 0.75234395, -1.3009509, 0.0038399224, 1.0727142, -0.1493434], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [119.60733, 12.487564, 9.67334, 5.4169126, 7.510412, 3.8273926, 8.7632475, 3.0710778, 5.040298, 3.4637299, 1.5817897, 18.197815, 0.67456055, 2.7947414, 2.9592094, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 17.991005, 363.39035, 3.5, 39.5, 7.5, 348.7515, 37.48995, 33.3927, 8.5, 56.5, 204.92172, 118.5, 18.5, 155.5, 0.03352318, -0.021654293, -0.07323144, 0.0014077998, 0.07615084, 0.039215874, -0.14720595, -0.001383989, 0.103906095, -0.10042709, 0.1183545, 0.0752344, -0.1300951, 0.00038399224, 0.107271425, -0.0149343405], "split_indices": [3, 0, 2, 4, 4, 3, 1, 2, 2, 4, 4, 2, 4, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [758.3395, 295.6354, 462.70404, 190.30373, 105.33169, 442.3307, 20.373365, 97.1091, 93.19462, 102.061935, 3.269751, 170.76662, 271.56406, 6.7314715, 13.641893, 85.70632, 11.402776, 10.140319, 83.0543, 52.458263, 49.603676, 2.0985456, 1.1712056, 166.30331, 4.4633136, 267.6895, 3.8745496, 2.7795205, 3.951951, 11.257222, 2.3846712], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.7110532, 0.23476155, 1.0320787, 0.011364385, 0.40607408, 1.0645663, 0.39045915, 0.18828149, -0.25350893, 0.608771, 0.17372996, 0.86290056, 1.1099843, -1.1999356, 0.59814394, 0.14968747, 1.0516993, -0.1865998, -1.4714183, 0.32129493, 0.83818215, -0.58890486, 0.32042873, 1.0185239, 0.101374894, 1.1447971, 1.0158792, -1.5671036, -0.68164116, 0.77235836, -0.38047966], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [111.1452, 11.199192, 9.0336, 5.955413, 7.792923, 3.7564087, 6.9241743, 2.5264564, 4.126525, 5.81884, 8.638617, 9.001858, 1.0717163, 0.42711043, 3.1696305, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 14.528929, 350.7863, 24.565266, 8.5, 4.5, 219.44946, 24.024578, 149.59767, 31.035679, 52.406517, 19.5, 180.34677, 200.00272, 77.5, 0.014968747, 0.10516993, -0.018659981, -0.14714183, 0.032129493, 0.08381822, -0.058890488, 0.032042872, 0.101852395, 0.01013749, 0.11447971, 0.101587914, -0.15671037, -0.06816412, 0.07723584, -0.038047966], "split_indices": [3, 0, 2, 1, 4, 3, 1, 1, 2, 2, 1, 4, 2, 1, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [726.99475, 292.731, 434.26373, 127.081535, 165.64948, 413.314, 20.949728, 76.215485, 50.866047, 88.43744, 77.21203, 76.051155, 337.26285, 2.4086907, 18.541037, 72.9768, 3.238685, 48.235825, 2.6302211, 39.28138, 49.15606, 12.440416, 64.771614, 63.132965, 12.91819, 246.02983, 91.23301, 1.3867587, 1.021932, 15.749619, 2.7914183], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.66967374, 0.21364394, 0.9942902, 0.08442517, 0.4685727, 1.026572, 0.34099582, 0.44960716, 0.015792437, 0.53539187, -0.5667549, 0.8982399, 1.1089125, -0.50317675, 0.7794203, 0.24902517, 1.4396338, 0.03462389, -1.1991526, 0.55688125, -1.0269324, -1.3358343, -0.24343395, 0.9549728, -0.9636036, 1.115724, 0.64819443, -1.2054759, -0.014660948, 0.9928648, -0.13598709], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [103.1745, 9.543279, 8.570129, 4.817756, 6.746502, 4.069824, 7.113717, 6.019872, 3.695279, 3.0758781, 1.4494802, 16.037575, 0.7165222, 2.239028, 2.4816723, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 17.994022, 363.39035, 27.817184, 28.0, 7.5, 348.7515, 17.287209, 310.08356, 192.65976, 34.5, 204.92172, 118.5, 18.5, 77.5, 0.024902517, 0.14396338, 0.003462389, -0.11991526, 0.055688124, -0.10269324, -0.13358343, -0.024343396, 0.09549728, -0.09636036, 0.1115724, 0.06481945, -0.12054759, -0.0014660949, 0.09928648, -0.013598709], "split_indices": [3, 0, 2, 2, 4, 3, 1, 0, 2, 2, 4, 2, 4, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [697.047, 289.87305, 407.17395, 192.401, 97.47203, 387.98123, 19.1927, 30.407448, 161.99355, 91.57626, 5.8957663, 151.73471, 236.24652, 6.5522547, 12.640446, 25.303507, 5.1039405, 159.53868, 2.454878, 90.346405, 1.2298576, 1.7265645, 4.169202, 147.25781, 4.4769015, 232.7692, 3.4773283, 2.6735556, 3.878699, 10.25855, 2.381896], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.630571, 0.19429266, 0.95915055, 0.004458392, 0.34348285, 0.9919682, 0.31804466, 0.23351102, -0.16972397, 0.4180922, -0.1927838, 0.8594499, 1.0783355, -0.45687306, 0.7360904, 0.16087642, 1.300941, -0.12661763, -1.3880377, 0.5985191, 0.29926556, -1.1665643, -0.065367416, 0.9163164, -0.83559585, 1.1204537, 0.99307376, -1.1057546, -0.013198005, 0.95328254, -0.1586873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [95.82608, 8.131431, 8.006836, 5.045596, 6.436989, 4.1230774, 6.03137, 4.220142, 3.7540078, 3.0161781, 2.422587, 13.814171, 0.7561035, 1.8644055, 2.3597398, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 14.528929, 363.39035, 18.041077, 19.5, 7.5, 348.7515, 17.998516, 149.59767, 5.5, 64.61685, 204.92172, 184.9061, 18.5, 155.5, 0.016087642, 0.1300941, -0.012661763, -0.13880377, 0.05985191, 0.029926557, -0.11665644, -0.0065367417, 0.09163164, -0.08355959, 0.11204537, 0.09930738, -0.11057546, -0.0013198005, 0.09532826, -0.015868729], "split_indices": [3, 0, 2, 1, 4, 3, 1, 1, 2, 4, 1, 2, 2, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [668.5496, 287.22586, 381.32373, 126.42986, 160.796, 362.7346, 18.589155, 54.636276, 71.79359, 141.17549, 19.620522, 143.21574, 219.51884, 6.50542, 12.0837345, 51.174892, 3.4613833, 69.35878, 2.4348028, 56.00807, 85.16742, 2.2517176, 17.368803, 138.57716, 4.63858, 146.77512, 72.74373, 2.626566, 3.8788543, 9.731982, 2.351752], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.5936815, 0.17679913, 0.92579836, 0.07045802, 0.4225763, 0.95919126, 0.2965809, 0.28379396, -0.02535689, 0.51782364, -0.34554544, 0.8223087, 1.049919, -0.4152774, 0.6950369, 0.18438646, 0.8750739, -0.0028364153, -1.1527965, -1.2118306, 0.544787, -0.60070926, 1.0678521, 0.87923396, -0.73028433, 1.0949051, 0.9598008, -1.377512, -0.13859665, 0.86238986, -0.3985522], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [88.77939, 7.42772, 7.4841614, 4.0610757, 6.2849703, 4.1817627, 5.1162133, 3.6066265, 3.4665813, 3.5630722, 3.4157248, 11.956047, 0.7949219, 1.7036165, 2.124517, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 17.997099, 363.39035, 18.041077, 112.459984, 7.5, 348.7515, 17.991133, 185.18152, 17.9998, 35.986916, 204.92172, 184.9061, 124.53139, 181.5, 0.018438647, 0.08750739, -0.00028364154, -0.11527965, -0.12118306, 0.0544787, -0.06007093, 0.106785215, 0.0879234, -0.07302844, 0.109490514, 0.09598008, -0.1377512, -0.013859666, 0.08623899, -0.039855223], "split_indices": [3, 0, 2, 1, 2, 3, 1, 1, 2, 1, 0, 2, 2, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [641.3025, 284.38275, 356.91974, 198.5354, 85.84734, 338.91235, 18.007397, 61.558205, 136.97719, 76.3911, 9.456245, 135.17786, 203.7345, 6.4531155, 11.55428, 52.724125, 8.834082, 134.3132, 2.6639957, 1.160134, 75.230965, 8.021018, 1.4352273, 130.4073, 4.7705564, 135.74792, 67.986565, 1.4241848, 5.0289307, 10.030554, 1.5237266], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.5581668, 0.1606357, 0.8938424, 0.0013828395, 0.3006714, 0.92778623, 0.2765015, 0.19581848, -0.13503355, 0.33456874, -0.9698593, 0.68620473, 0.98534536, -0.38026574, 0.65532947, 0.13408776, 1.1664289, -1.5014021, -0.11284693, 0.35794705, -1.0446502, -1.626752, 0.12469571, 0.8689864, -0.13005157, 1.0305917, 0.8683389, -1.3293635, -0.12468242, 0.9480343, 0.017616289], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [82.1962, 6.2874455, 6.9843445, 3.502671, 6.459503, 4.3785706, 4.34364, 3.2490644, 2.3334937, 4.7100544, 2.817103, 9.106529, 1.3252563, 1.5302832, 2.056868, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 17.264828, 363.39035, 18.041077, 189.41861, 4.5, 348.7515, 17.998516, 18.379105, 40.5, 27.945133, 19.5, 180.34677, 124.53139, 415.30353, 0.013408775, 0.11664289, -0.15014021, -0.011284693, 0.035794705, -0.10446502, -0.1626752, 0.012469571, 0.08689865, -0.013005157, 0.10305917, 0.08683389, -0.13293634, -0.012468242, 0.09480343, 0.0017616289], "split_indices": [3, 0, 2, 1, 2, 3, 1, 1, 1, 4, 0, 4, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [616.05133, 282.06012, 333.9912, 132.01274, 150.0474, 316.56403, 17.427162, 54.459713, 77.553024, 146.16304, 3.8843443, 60.969982, 255.59406, 6.365308, 11.061854, 51.223583, 3.2361302, 1.2213032, 76.33172, 143.74123, 2.4218178, 2.4301648, 1.4541795, 49.824535, 11.145445, 184.20038, 71.39368, 1.3332398, 5.032068, 7.5677676, 3.4940867], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.5243785, 0.14601463, 0.8624556, 0.053360563, 0.36638483, 0.89807236, 0.27372247, 0.18064144, -0.07171774, 0.7588008, 0.20330489, -0.19551666, 0.9118524, -1.0025383, 0.44374415, 0.13648184, 0.82279485, -0.90374774, 0.030107627, -1.5715195, 0.8606832, -1.4347435, 0.30041417, 0.7719538, -2.4589198, 0.7824742, 0.99777484, -0.47717032, -1.4770945, 0.6187743, -0.38395303], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [75.74338, 5.7022076, 6.543701, 3.1343338, 5.280802, 4.4528046, 3.8767967, 2.7565267, 8.415759, 5.7650146, 9.290298, 8.06958, 3.2117767, 0.49624276, 2.2914207, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 17.997099, 350.7863, 3.5, 5.5, 24.802284, 219.44946, 25.922901, 33.3927, 17.99973, 35.564957, 24.2858, 7.5, 35.5, 77.5, 0.013648184, 0.08227949, -0.090374775, 0.0030107626, -0.15715195, 0.086068325, -0.14347436, 0.030041417, 0.07719538, -0.24589197, 0.07824742, 0.09977748, -0.04771703, -0.14770946, 0.06187743, -0.038395304], "split_indices": [3, 0, 2, 4, 4, 1, 1, 1, 2, 1, 1, 1, 3, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [592.2117, 279.47598, 312.7357, 196.79279, 82.6832, 294.87436, 17.86133, 97.57502, 99.217766, 24.24503, 58.438164, 3.6587453, 291.2156, 2.087067, 15.774262, 91.32264, 6.252378, 10.82059, 88.39718, 1.005445, 23.239586, 3.2570593, 55.181103, 2.5646715, 1.0940737, 116.3054, 174.91022, 1.0100245, 1.0770423, 13.032857, 2.7414055], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.4913077, 0.13253416, 0.8311207, 0.15948, -0.5551404, 0.86947364, 0.27902883, 0.08592322, 0.4815525, -1.2252004, -0.26662093, 0.60855144, 0.9353367, -2.069178, 0.41168016, 0.07579895, 1.8963727, 0.86558485, 0.27368984, -0.5259438, -1.8368111, 0.9112673, -0.73955667, 0.80389434, -0.2014428, 0.98341274, 0.80711305, -0.27903965, 0.64521956], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [69.59558, 5.1435685, 6.194397, 6.326311, 2.0042114, 4.6898956, 5.93422, 3.9680057, 3.9511232, 1.3076525, 4.091468, 8.759228, 1.3231506, 0.0, 2.9201956, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 28.5, 337.46826, 1.5, 34.5, 4.5, 9.5, 140.19368, 6.5, 31.5, 37.5, 19.5, 180.34677, -0.20691781, 322.661, 0.007579895, 0.18963727, 0.08655848, 0.027368983, -0.052594382, -0.18368112, 0.09112673, -0.07395567, 0.08038943, -0.02014428, 0.09834128, 0.080711305, -0.027903965, 0.06452196], "split_indices": [3, 4, 2, 3, 4, 3, 3, 1, 4, 4, 4, 4, 2, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [570.9271, 277.7368, 293.19034, 267.2857, 10.451102, 274.12357, 19.06677, 217.62659, 49.659103, 3.1254365, 7.325666, 55.30044, 218.82314, 1.0076623, 18.059109, 216.43222, 1.194375, 17.411362, 32.24774, 1.4745398, 1.6508968, 2.089978, 5.235688, 44.565247, 10.735193, 159.04593, 59.777206, 4.5518165, 13.507292], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.45992193, 0.1499318, 0.83605283, 0.05599713, 0.40266374, 0.87885195, 0.21192725, 0.06722185, -1.2765299, 0.51436967, -0.28166795, 0.74243516, 0.95183945, -0.34265342, 0.553903, 0.0768771, -1.0207405, 0.26455072, 0.6553869, -0.020112677, -2.2610037, 0.7910107, -2.2972198, 0.962353, 0.2868836, -0.0960022, -1.2884978, 0.8658315, -0.04857703], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [64.214615, 7.1646204, 6.6345367, 3.282421, 6.256858, 2.2945557, 3.0380647, 2.2853546, 0.0, 2.4669495, 5.916178, 12.000992, 1.044693, 1.4073441, 1.8711295, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 18.061596, 363.39035, 44.0, 112.44241, 7.5, 348.7515, 311.69003, -0.12765299, 53.01736, 53.667732, 241.47743, 118.5, 36.5, 415.30353, 0.00768771, -0.10207405, 0.026455073, 0.0655387, -0.0020112677, -0.22610037, 0.07910107, -0.22972198, 0.0962353, 0.02868836, -0.00960022, -0.12884979, 0.08658315, -0.004857703], "split_indices": [3, 0, 2, 4, 2, 3, 1, 2, 0, 2, 0, 2, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [550.8256, 301.98434, 248.84131, 220.19476, 81.789566, 232.8534, 15.987912, 218.37228, 1.8224881, 70.32694, 11.462627, 81.24734, 151.60605, 6.0871477, 9.900764, 216.46968, 1.9026015, 25.410559, 44.91638, 10.138103, 1.3245249, 79.9799, 1.2674404, 149.22713, 2.378922, 4.8452277, 1.2419199, 6.5314126, 3.3693519], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.42985833, 0.13622911, 0.8076898, 0.030946564, 0.33502296, 0.85149217, 0.19648391, 0.32355973, -0.02126905, 0.42724517, -0.28744453, 0.71063036, 0.92823595, -1.2778044, 0.31838515, 0.15444031, 1.1384276, 0.18128687, -0.083966315, -1.3462473, 0.44869208, -0.09472156, -1.8564571, 0.9300597, 0.082009956, 0.9930436, 0.8043745, -0.7230896, 0.48594233], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [59.04492, 6.26582, 6.219391, 2.9959846, 5.95463, 2.324768, 2.796934, 4.077052, 2.109894, 3.4325714, 4.021962, 10.566353, 1.1018982, 0.0, 2.5170152, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 17.997099, 363.39035, 27.817184, 112.44241, 7.5, 124.53139, 17.287209, 18.585432, 17.99973, 53.667732, 19.5, 184.9061, -0.12778044, 381.08975, 0.015444032, 0.11384276, 0.018128688, -0.008396632, -0.13462473, 0.04486921, -0.009472157, -0.18564571, 0.09300597, 0.008200996, 0.09930436, 0.08043745, -0.07230896, 0.048594233], "split_indices": [3, 0, 2, 2, 2, 3, 0, 0, 1, 1, 0, 4, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [532.2978, 299.54694, 232.75082, 195.8731, 103.67384, 217.16714, 15.583689, 29.681164, 166.19193, 90.31269, 13.361153, 76.671265, 140.49588, 1.1766222, 14.407066, 24.599829, 5.081334, 39.24022, 126.951706, 1.0661528, 89.24654, 11.914645, 1.4465073, 56.817547, 19.853714, 92.13659, 48.359287, 1.9837791, 12.423287], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.40123275, 0.12391746, 0.7787693, -0.035471298, 0.23922217, 0.8275963, 0.22242963, -0.019313164, -1.4644579, 0.40780282, 0.07088052, 0.741514, 0.9602579, -1.1170875, 0.40421328, -0.0023960748, -1.3779615, -0.01650588, 0.66750216, -0.7023386, 0.21023636, 0.8062944, -2.4186902, 1.0272213, 0.7923261, -0.89896077, -1.3173372, 0.5715784, -0.33890095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [53.924866, 5.4609203, 5.913269, 2.8630323, 4.8870306, 2.265747, 4.290435, 2.8195386, 0.0, 9.4941025, 9.295051, 24.91729, 0.86013794, 0.057843685, 1.9367018, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 14.528929, 337.46826, 1.5579352, 8.5, 99.63033, 219.44946, 27.0, -0.1464458, 29.31308, 52.355698, 223.13199, 48.5, 405.38135, 77.5, -0.00023960749, -0.13779615, -0.001650588, 0.06675022, -0.07023386, 0.021023637, 0.08062944, -0.24186902, 0.10272213, 0.07923261, -0.089896075, -0.13173372, 0.05715784, -0.033890095], "split_indices": [3, 0, 2, 0, 4, 0, 1, 4, 0, 2, 1, 2, 4, 2, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [515.1435, 297.01453, 218.12895, 124.64064, 172.37389, 200.50853, 17.620422, 123.26398, 1.376663, 86.08901, 86.28487, 121.68811, 78.82042, 2.0927823, 15.527639, 121.765205, 1.4987708, 32.674603, 53.41441, 13.159105, 73.12576, 119.25416, 2.4339516, 56.271328, 22.549091, 1.0518544, 1.0409279, 12.686202, 2.8414369], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.3740256, 0.112584524, 0.75090843, 0.036795106, 0.32704356, 0.7973918, 0.16133143, 0.04663014, -1.228579, 0.42786223, -0.23734875, 0.6457376, 0.8827211, 0.01702581, 1.2884253, 0.05346359, -1.2248888, 0.18350372, 0.56827456, -0.65345895, 0.6191524, 0.88314366, 0.018410077, 0.9572024, 0.7438163, 0.23516677, -1.5639466], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [49.185616, 4.7880583, 5.592842, 2.700242, 4.3812294, 2.4314117, 2.4217346, 1.8676012, 0.0, 2.2320318, 4.1575785, 10.1652355, 1.2298508, 4.5737643, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 18.061596, 363.39035, 44.0, 112.44241, 7.5, 452.3753, 18.019375, -0.122857906, 53.01736, 160.0762, 19.5, 184.9061, 415.30353, 0.12884253, 0.005346359, -0.12248888, 0.018350372, 0.056827456, -0.0653459, 0.061915245, 0.08831437, 0.0018410077, 0.09572024, 0.074381635, 0.023516677, -0.15639466], "split_indices": [3, 0, 2, 4, 2, 3, 0, 0, 0, 2, 1, 4, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [499.26718, 294.8035, 204.46368, 217.86778, 76.935715, 189.50246, 14.961218, 216.20488, 1.6629032, 65.2925, 11.643208, 68.30614, 121.19632, 13.2799635, 1.6812549, 215.0665, 1.138375, 23.861248, 41.43126, 7.84564, 3.797567, 49.53868, 18.76746, 78.80818, 42.388134, 11.682817, 1.5971456], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.3477317, 0.10212089, 0.7220547, -0.03691445, 0.20490487, 0.77364236, 0.18733387, 0.09852831, -0.18388657, 0.36580667, 0.046658166, 0.67990917, 0.92163104, -1.5752922, 0.31236154, 0.06698058, 1.775522, -1.1557783, -0.12452208, -0.022202902, 0.6141188, -0.6393045, 0.17445783, 0.7707593, -1.3751531, 1.0276939, 0.78354174, 0.20780219, 1.2522318], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [44.588593, 4.1878624, 5.289871, 2.4788568, 4.285138, 2.4085312, 3.7251496, 3.405953, 3.4322326, 8.050872, 7.445869, 20.049957, 0.96806335, 0.0, 1.5384704, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 14.528929, 337.46826, 29.44632, 8.5, 99.63033, 200.00272, 28.501022, 31.495588, 29.31308, 52.355698, 190.92062, 41.5, -0.15752922, 452.3753, 0.006698058, 0.17755221, -0.11557783, -0.012452208, -0.0022202902, 0.061411884, -0.06393045, 0.017445784, 0.07707593, -0.1375153, 0.10276939, 0.07835417, 0.020780219, 0.12522319], "split_indices": [3, 0, 2, 2, 4, 0, 1, 2, 2, 2, 1, 2, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [485.07434, 292.91714, 192.15718, 124.46421, 168.45294, 175.22868, 16.928507, 64.73899, 59.72522, 83.48794, 84.96501, 107.35536, 67.873314, 1.1082697, 15.820237, 63.55966, 1.1793267, 3.4177666, 56.307453, 32.567375, 50.92056, 13.325002, 71.64001, 102.82098, 4.5343904, 38.310894, 29.562422, 14.257013, 1.5632236], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.32324937, 0.09281127, 0.69407046, 0.0076430095, 0.24367304, 0.7436722, 0.12671858, 0.3022418, -0.046005186, 0.31597683, -0.23882487, 0.62054366, 0.869303, -0.35438308, 0.4475368, 0.12410954, 1.30142, -0.034851953, -1.200269, -0.12726204, 0.4174446, -0.06497937, -1.5979317, 0.66244656, -2.1603577, 0.8888491, 0.021321872, -0.96409446, 0.008486225, 0.7854248, -0.16034828], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [40.27993, 3.7339547, 5.0749893, 2.941214, 3.6608162, 2.5496216, 2.248958, 5.0874166, 2.0118136, 4.1070814, 3.2092576, 9.785183, 1.3526039, 1.2963679, 1.8044705, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 17.994022, 363.39035, 27.817184, 112.44241, 10.5, 348.7515, 18.666903, 17.99281, 29.46128, 53.667732, 293.18204, 118.5, 18.5, 415.30353, 0.012410954, 0.130142, -0.0034851953, -0.1200269, -0.0127262045, 0.04174446, -0.006497937, -0.15979318, 0.066244654, -0.21603577, 0.08888491, 0.0021321874, -0.09640945, 0.0008486225, 0.078542486, -0.016034828], "split_indices": [3, 0, 2, 2, 2, 3, 1, 1, 0, 2, 0, 2, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [471.4683, 290.7981, 180.6702, 185.91658, 104.8815, 166.12564, 14.5445595, 28.662426, 157.25417, 91.23398, 13.647518, 83.96853, 82.15711, 5.8058467, 8.738712, 24.34371, 4.3187165, 155.76799, 1.4861641, 16.976198, 74.25778, 12.115625, 1.5318929, 82.73311, 1.2354186, 80.29405, 1.863062, 2.1702585, 3.6355884, 5.625406, 3.113307], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.29984927, 0.0842294, 0.6663566, 0.09561268, -0.92484546, 0.76998204, 0.38409495, 0.032926377, 0.28197816, -1.341681, -0.72004473, 1.0120318, 0.69175893, -3.589714, 0.5910364, 0.12660424, -0.068446934, 0.55199146, 0.101336025, -0.56626284, -0.8442089, -0.10754961, 1.077492, -1.240223, 0.7275558, -2.6765227, -4.501274, 0.7839087, 0.1694634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [36.27163, 3.3129952, 4.9586716, 3.3363616, 0.24424148, 2.3300476, 37.576004, 2.0325468, 3.5003748, 0.0, 0.023006558, 2.2324944, 6.5067596, 1.690134, 3.522294, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 320.28497, 198.22841, 18.061596, 383.0681, 12.5, 147.50461, 4.5, 8.5, -0.1341681, 8.978943, 24.507298, 31.919846, 7.5, 363.39035, 0.012660424, -0.0068446933, 0.055199146, 0.010133603, -0.056626286, -0.08442089, -0.010754961, 0.1077492, -0.124022305, 0.07275558, -0.26765227, -0.45012742, 0.078390874, 0.01694634], "split_indices": [3, 2, 2, 0, 2, 4, 1, 4, 4, 0, 0, 0, 0, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [459.07404, 289.05798, 170.01607, 285.85226, 3.2057106, 124.3217, 45.69436, 213.95224, 71.90003, 1.0217602, 2.1839504, 30.30382, 94.01788, 2.2513247, 43.443035, 111.246666, 102.70557, 28.790203, 43.10983, 1.0364337, 1.1475167, 1.6651509, 28.63867, 1.6991252, 92.318756, 1.1497996, 1.1015251, 29.784237, 13.658796], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.27785775, 0.076336585, 0.6392774, -0.039443143, 0.16407041, 0.74361956, 0.36216664, -0.024889609, -1.3327514, 0.6960436, 0.12523596, 0.9985836, 0.6634242, -3.0610433, 0.5588868, -0.010471922, -1.3121684, 0.42594323, 1.2663634, -0.32527855, 0.20074877, -0.09594558, 1.0694437, 0.25673333, 0.8475402, -4.0203404, -2.226551, 0.7553335, 0.1536262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [32.590477, 2.9204364, 4.6183624, 2.3171515, 3.369501, 2.3556442, 29.53275, 2.2599247, 0.0, 1.6886001, 5.185522, 2.16601, 6.6184273, 1.7554092, 3.2931986, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 14.528929, 198.22841, 1.5579352, 18.62513, 12.5, 147.50461, 27.0, -0.13327514, 17.99973, 28.075882, 24.507298, 7.5, 262.82056, 363.39035, -0.0010471923, -0.13121684, 0.042594325, 0.12663634, -0.032527857, 0.020074878, -0.009594559, 0.106944375, 0.025673334, 0.08475402, -0.40203404, -0.2226551, 0.07553335, 0.01536262], "split_indices": [3, 0, 2, 0, 1, 4, 1, 4, 0, 1, 2, 0, 3, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [447.55914, 287.36777, 160.19136, 123.83626, 163.53151, 116.33437, 43.856987, 122.47597, 1.3602912, 11.096707, 152.43481, 27.778692, 88.55568, 2.3726733, 41.484314, 121.13707, 1.3389009, 7.554119, 3.5425878, 21.85872, 130.5761, 1.6802542, 26.098438, 27.621418, 60.934265, 1.0804604, 1.2922128, 27.917992, 13.566323], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.25696504, 0.06910658, 0.6122468, 0.08673195, -0.47666252, 0.71724975, 0.3408029, 0.07223645, 1.058948, -1.6577735, -0.18892631, 0.98548704, 0.6352758, -2.5444012, 0.52970874, 0.09258114, -0.68887454, 1.3998644, 0.2071954, 0.3182304, -0.866685, -0.085681766, 1.0622525, 0.23637179, 0.82585037, -3.0710237, -2.0131893, 0.6164022, -0.3667246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [29.17996, 2.7484727, 4.297516, 3.8938851, 3.0145166, 2.3746033, 23.009863, 4.223667, 1.1601973, 0.0, 2.4837081, 2.1050797, 6.3393097, 0.61286163, 3.0858574, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 194.71904, 198.22841, 164.97635, 202.49092, 12.5, 150.91327, 27.5, 187.57385, -0.16577736, 320.28497, 24.507298, 7.5, 262.82056, 77.5, 0.009258114, -0.06888746, 0.13998644, 0.020719541, 0.03182304, -0.0866685, -0.008568177, 0.10622525, 0.02363718, 0.08258504, -0.30710238, -0.20131893, 0.06164022, -0.03667246], "split_indices": [3, 2, 2, 2, 2, 4, 1, 4, 2, 0, 2, 0, 3, 2, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [437.29922, 286.0747, 151.22452, 277.15097, 8.923734, 109.004814, 42.219696, 273.10016, 4.0508256, 1.7319576, 7.1917768, 25.460411, 83.5444, 2.5835097, 39.63619, 266.0115, 7.088633, 2.879944, 1.1708815, 4.1082125, 3.083564, 1.6938193, 23.76659, 27.033571, 56.510834, 1.2638958, 1.3196138, 36.154034, 3.4821522], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.2370625, 0.041869756, 0.54862005, 0.061062142, -0.4615544, -0.7378381, 0.5698941, 0.052426737, 1.1347744, -1.0856531, -0.17835148, -2.2355175, 0.4302314, 0.79461586, 0.44671, 0.1358084, -0.04776739, -0.48908255, -1.726009, 1.3628552, -0.48319843, 0.91736716, 0.6358126, -1.8263683, 0.49449757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, -1, -1, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [26.02803, 2.5395875, 4.5148735, 2.3388205, 1.69082, 4.7141094, 4.4742737, 2.102802, 0.0, 1.1149902, 3.1268938, 0.0, 0.0, 1.0973892, 11.38237, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, -1, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 28.5, 31.41743, 140.19368, 34.5, 21.204067, 97.20796, 5.5, 0.113477446, 20.140373, 36.5, -0.22355175, 0.04302314, 12.5, 14.5, 0.013580839, -0.004776739, -0.048908256, -0.17260091, 0.13628553, -0.048319843, 0.09173672, 0.06358126, -0.18263684, 0.049449757], "split_indices": [3, 4, 2, 1, 4, 0, 2, 4, 0, 0, 4, 0, 0, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [428.08652, 263.22058, 164.86592, 253.58177, 9.638821, 2.668893, 162.19702, 251.57823, 2.0035331, 2.9887724, 6.6500483, 1.1691942, 1.499699, 57.381542, 104.81548, 137.36183, 114.2164, 1.564501, 1.4242715, 1.0870036, 5.5630445, 32.302055, 25.079487, 2.1462214, 102.66926], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "25", "size_leaf_vector": "0"}}, {"base_weights": [0.21811709, 0.06726868, 0.5906196, 0.082956985, -1.2020056, 0.6594547, 0.027753586, 0.003707387, 0.29984766, -3.7062666, -0.19783951, 0.5671266, 0.8638225, -1.0633715, 0.19211078, 0.011309605, -1.1666684, 0.3512896, -0.7504837, 1.1449891, -1.0515988, 0.6851217, -1.3431576, 0.7465816, 1.0585172, 0.9143016, -0.10804724], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [23.599195, 5.9446125, 4.6837234, 5.0746193, 9.117034, 2.0159798, 2.3639154, 1.9139888, 4.2682033, 0.0, 3.0083992, 16.763166, 0.7389946, 0.0, 2.5029504, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.5, 56.84178, 363.39035, 1.5, 62.972248, 124.05173, 381.08975, 44.0, 194.21667, -0.37062666, 114.48448, 223.70538, 222.33905, -0.10633715, 418.61975, 0.0011309605, -0.116666846, 0.035128962, -0.07504837, 0.11449891, -0.10515988, 0.06851217, -0.13431577, 0.07465816, 0.105851725, 0.09143016, -0.010804724], "split_indices": [3, 0, 2, 3, 0, 0, 2, 4, 2, 0, 2, 2, 1, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [420.0928, 299.03458, 121.05821, 295.40063, 3.6339452, 107.8494, 13.208809, 216.39018, 79.010445, 1.0295393, 2.6044059, 74.3477, 33.501705, 1.7140603, 11.494749, 215.01202, 1.3781706, 75.33686, 3.6735861, 1.0063896, 1.5980161, 70.03349, 4.314207, 20.972332, 12.529375, 3.378592, 8.116157], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.20099263, 0.061149165, 0.5635629, 0.07521484, -0.97053474, 0.72197396, 0.313968, 0.032082804, 0.42136228, -2.464251, -0.17381495, -0.10143938, 0.75343305, -2.0844738, 0.4823469, 0.11910971, -0.0633144, -0.8993162, 0.50522983, 0.48491788, -1.0521647, 1.0415859, -1.5611883, 0.8907953, 0.5779625, -2.8732905, -1.1098536, 0.6724419, 0.05581172], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.892036, 4.308626, 4.5226707, 4.377282, 4.7205577, 1.8241348, 17.99647, 2.1676188, 3.5996022, 0.0, 1.5259886, 4.3232293, 1.6088028, 2.174842, 3.3706474, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.5, 56.84178, 180.34677, 2.5, 62.972248, 31.919846, 158.41684, 5.5, 28.5542, -0.2464251, 77.2366, 9.5, 105.80037, 26.5, 363.39035, 0.011910971, -0.00633144, -0.08993162, 0.050522983, 0.048491787, -0.105216466, 0.104158595, -0.15611884, 0.08907953, 0.05779625, -0.28732905, -0.11098536, 0.067244194, 0.005581172], "split_indices": [3, 0, 2, 3, 0, 0, 1, 4, 2, 0, 0, 4, 1, 4, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [412.16672, 297.47122, 114.69548, 293.48886, 3.9823778, 70.128105, 44.567375, 261.0058, 32.483063, 1.3736091, 2.6087687, 2.568181, 67.55992, 2.9121797, 41.655193, 136.54437, 124.461426, 1.9266874, 30.556377, 1.4868073, 1.1219614, 1.4381121, 1.1300689, 37.8401, 29.719826, 1.5930723, 1.3191075, 28.793509, 12.861685], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.18486913, 0.055466425, 0.53759575, 0.09225207, -0.27395856, 0.6965982, 0.29432234, 0.028261678, 0.43481243, -0.15225826, -1.4129575, -0.08259224, 0.7288913, -1.6555324, 0.47065592, -0.05627467, 0.13676378, 0.99164903, 0.30106673, -0.51433754, 0.08910328, -2.8652675, -0.31511143, 1.0403342, -1.85958, 0.87314403, 0.5497813, -2.5859644, -1.118539, 0.6808657, 0.061767656], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.484344, 3.5903428, 4.1929817, 5.840782, 4.105932, 1.6604424, 14.7837715, 2.0621982, 3.108139, 2.3552558, 4.5234485, 5.238644, 1.6114655, 1.7149754, 3.3834257, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.5, 19.5, 180.34677, 24.5457, 54.82789, 31.919846, 169.56615, 17.264463, 6.5, 106.98457, 63.37812, 11.5, 105.80037, 85.398315, 350.7863, -0.0056274673, 0.013676378, 0.0991649, 0.030106673, -0.051433753, 0.008910328, -0.28652677, -0.031511143, 0.104033425, -0.18595801, 0.087314405, 0.054978132, -0.25859645, -0.1118539, 0.06808657, 0.0061767655], "split_indices": [3, 4, 2, 0, 0, 0, 1, 0, 4, 1, 0, 4, 1, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [405.0961, 296.3982, 108.69791, 266.65894, 29.739239, 65.69814, 42.999767, 224.72101, 41.93794, 26.886568, 2.852672, 2.6015248, 63.09662, 3.5544882, 39.44528, 126.26099, 98.46002, 8.095256, 33.842686, 10.766016, 16.120552, 1.2172278, 1.6354443, 1.5932348, 1.0082899, 34.89671, 28.19991, 1.2799606, 2.2745275, 26.031403, 13.413874], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.17011936, 0.03751637, 0.48329416, 0.04629401, -0.7810917, 0.6588612, 0.31311017, -0.009306095, 0.16347475, -0.9374716, -0.4639136, 0.005209832, 0.72826797, -2.1940813, 0.43915588, 0.04037196, -0.63092834, 1.4744489, 0.105063915, 1.0527916, -1.159861, 0.9893211, 0.5867316, -1.0620253, -3.8071835, -0.33100945, 0.5629706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.525803, 2.0027447, 3.5255451, 1.8046484, 0.12824166, 2.6346283, 19.00528, 5.8007884, 6.8066444, 0.0, 0.0, 6.86298, 1.9230843, 5.1660337, 5.467861, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 320.28497, 137.62752, 49.724396, 162.34355, 31.919846, 117.185745, 8.5, 50.730988, -0.09374716, -0.04639136, 8.5, 72.90387, 8.5, 8.5, 0.004037196, -0.063092835, 0.14744489, 0.0105063915, 0.10527916, -0.1159861, 0.09893211, 0.058673162, -0.106202535, -0.38071835, -0.033100944, 0.05629706], "split_indices": [3, 2, 2, 1, 1, 0, 1, 4, 1, 0, 0, 4, 1, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [398.06418, 279.6846, 118.37959, 276.73886, 2.9457214, 58.225204, 60.154385, 187.63391, 89.104965, 1.9387602, 1.0069613, 5.60393, 52.621273, 2.8677711, 57.286613, 173.74225, 13.891659, 3.783111, 85.32186, 2.9557726, 2.6481574, 18.453896, 34.167377, 1.6985394, 1.1692318, 7.920595, 49.36602], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.15600647, 0.04475465, 0.4866291, 0.07822561, -0.24884279, 0.5630369, -0.028348152, 0.05085466, 0.7811806, -0.14138858, -1.179421, 0.45912158, 0.79762137, -0.1730118, 1.1963472, -0.012527117, 0.23339291, 0.92147243, 0.31517765, -0.46282429, 0.0759914, -2.1826656, -0.29228786, 0.57608783, -1.0251623, 0.6561727, 1.0427195, 0.0517908, -1.5642685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [14.42851, 2.8841949, 3.8921833, 5.063824, 2.987259, 2.0809574, 2.251563, 2.9383934, 0.62974405, 1.8863432, 2.7251463, 10.361492, 0.89030075, 3.588647, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.5, 19.5, 363.39035, 3.5, 54.82789, 124.05173, 452.3753, 49.724396, 52.355366, 106.98457, 63.37812, 223.70538, 222.33905, 415.30353, 0.119634725, -0.0012527116, 0.023339292, 0.092147246, 0.031517766, -0.04628243, 0.00759914, -0.21826656, -0.029228786, 0.057608783, -0.102516234, 0.06561727, 0.10427195, 0.00517908, -0.15642685], "split_indices": [3, 4, 2, 3, 0, 0, 0, 1, 0, 1, 0, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [392.40738, 293.64114, 98.76625, 263.62817, 30.01298, 86.02161, 12.744647, 253.77199, 9.856168, 26.92608, 3.0868993, 59.664047, 26.357557, 11.413242, 1.3314041, 188.33275, 65.43924, 7.5515223, 2.304645, 10.87707, 16.04901, 1.4365041, 1.6503953, 55.317696, 4.346353, 16.76175, 9.595809, 9.8277, 1.5855433], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.1429903, 0.04704749, 0.4864051, 0.06660417, -0.5294092, 0.76480913, 0.30677322, -0.000172009, 0.2289928, -1.0234683, -0.07234124, 1.0342811, 0.6483841, -2.3274796, 0.42605662, 0.0123759685, -0.75170034, 0.53798366, 0.06005598, -0.4764709, -1.2672515, 0.74347466, -0.73183113, -0.911005, 0.7208763, -1.2831199, -3.4486039, -1.5719172, 0.47444123], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.745674, 3.406334, 4.212179, 3.175479, 2.2264209, 1.0151005, 16.139381, 1.9626148, 4.447025, 0.61013985, 2.7866716, 0.0, 2.623931, 2.5126705, 4.7505436, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.5, 194.71904, 139.83286, 18.061596, 244.68796, 72.90387, 117.185745, 17.999968, 8.5, 28.443031, 320.28497, 0.10342812, 75.472466, 61.36991, 22.5, 0.0012375969, -0.07517003, 0.053798366, 0.0060055978, -0.04764709, -0.12672515, 0.074347466, -0.07318311, -0.09110051, 0.07208763, -0.12831199, -0.3448604, -0.15719172, 0.047444124], "split_indices": [3, 2, 2, 0, 2, 1, 1, 0, 4, 1, 2, 0, 1, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [386.99872, 302.5212, 84.477516, 292.62006, 9.901139, 33.095665, 51.381847, 207.31715, 85.30292, 4.741564, 5.159575, 9.935256, 23.160408, 2.2142143, 49.167633, 203.90909, 3.4080617, 30.124043, 55.17887, 1.483491, 3.2580726, 2.2990813, 2.860494, 1.017576, 22.142832, 1.1620736, 1.0521406, 1.1502004, 48.017433], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.1309674, 0.026430497, 0.40873826, -0.05824531, 0.09395262, 0.9087831, 0.34072855, -0.04575291, -1.1598399, 1.224309, 0.074858524, 0.18490839, 0.9811302, -1.2255329, 0.37814113, -0.03470658, -1.226186, -0.21951418, 0.14136048, 1.032408, 0.46363148, 0.19287954, -2.7052093, 0.10312427, 0.5032301], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.085786, 1.5872095, 3.5350723, 1.6810145, 3.3201768, 0.6399174, 5.383687, 1.5739568, 0.0, 0.0, 2.9737587, 0.0, 0.28085232, 4.5239196, 3.0830097, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 14.528929, 11.5, 1.5579352, 22.836498, 24.493393, 31.919846, 27.0, -0.115983985, 0.1224309, 28.075882, 0.01849084, 83.73007, 29.219387, 8.5, -0.003470658, -0.12261861, -0.021951418, 0.014136048, 0.1032408, 0.04636315, 0.019287955, -0.27052093, 0.010312427, 0.05032301], "split_indices": [3, 0, 4, 0, 2, 0, 0, 4, 0, 0, 2, 0, 2, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [381.92096, 277.52393, 104.39702, 123.05751, 154.46643, 12.466415, 91.9306, 121.69692, 1.3605853, 2.5467336, 151.9197, 1.1485257, 11.31789, 2.131032, 89.79957, 120.58725, 1.1096729, 27.963797, 123.9559, 10.270895, 1.0469946, 1.0890299, 1.042002, 28.105606, 61.693966], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "25", "size_leaf_vector": "0"}}, {"base_weights": [0.119615585, 0.03857362, 0.43133244, 0.045406163, -1.540187, 0.53693324, -0.10934307, 0.0011221721, 0.21968941, 0.36088437, 0.7221211, -0.23642416, 1.1536386, 0.008548018, -1.5952101, 0.7094778, 0.02301935, 0.7152154, -0.7086497, 0.89870846, 0.31958896, -0.020505859, -1.4840804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [9.536772, 3.2197394, 4.4531555, 2.3018296, 0.0, 2.1120968, 2.0356312, 2.8067386, 5.8144426, 12.689405, 2.2407856, 3.1029835, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.5, 79.68119, 350.7863, 24.5457, -0.1540187, 99.63033, 452.3753, 24.342422, 9.5, 139.83286, 48.5, 77.5, 0.11536386, 0.0008548018, -0.15952101, 0.07094778, 0.0023019349, 0.07152154, -0.07086497, 0.08987085, 0.031958897, -0.0020505858, -0.14840804], "split_indices": [3, 0, 2, 0, 0, 0, 0, 0, 4, 2, 4, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [377.69495, 299.79565, 77.8993, 298.51978, 1.2758838, 65.18617, 12.713127, 238.09035, 60.429424, 33.460464, 31.725712, 11.565457, 1.1476694, 237.00406, 1.0862887, 17.290966, 43.13846, 25.143288, 8.317176, 22.025953, 9.6997595, 9.874725, 1.690732], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.109448716, 0.020616855, 0.36134207, 0.22245742, -0.0061089885, 0.8876979, 0.29452088, 0.16556531, 1.4559554, -0.07121962, 0.107534155, 0.19132824, 0.9686432, -1.2798599, 0.32542902, 0.09176046, 1.2182664, -0.018956847, -0.6076045, 1.3695314, 0.05471826, 1.026944, 0.44806123, 0.35973975, -0.6021611], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, -1, 21, -1, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.353124, 1.4918163, 3.4092216, 2.2504148, 1.80386, 0.6034174, 4.1993127, 2.388632, 0.0, 4.3407955, 5.9012, 0.0, 0.27773666, 0.0, 2.697279, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, -1, 22, -1, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 27.817184, 11.5, 27.167913, 49.724396, 24.493393, 30.403542, 18.007957, 0.14559554, 8.5, 50.730988, 0.019132825, 83.297806, -0.127986, 77.5, 0.009176046, 0.12182664, -0.0018956847, -0.06076045, 0.13695315, 0.005471826, 0.10269441, 0.044806123, 0.035973977, -0.06021611], "split_indices": [3, 2, 4, 2, 1, 0, 0, 0, 0, 4, 1, 0, 2, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [373.46875, 276.13754, 97.33122, 32.323437, 243.8141, 10.933628, 86.39759, 30.916918, 1.4065185, 155.06372, 88.75038, 1.1549355, 9.778693, 1.6494832, 84.74811, 28.910381, 2.0065355, 141.32289, 13.7408285, 3.547473, 85.20291, 8.767833, 1.0108602, 81.74147, 3.0066416], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "25", "size_leaf_vector": "0"}}, {"base_weights": [0.099776454, 0.030321803, 0.38553596, 0.04794954, -0.4873347, 0.6905341, 0.2083928, 0.035696205, 0.7912913, -1.136679, -0.17622694, 1.0266107, 0.5557747, -1.9578645, 0.3269166, 0.05675491, -0.48216102, 1.0772172, 0.040158335, -0.70655864, -1.98369, -0.53667414, 0.78064203, 0.1671582, 0.9228054, -0.882578, -3.2217612, -1.4359127, 0.37804222], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [7.34083, 2.712915, 3.8975716, 2.61449, 1.9621699, 1.1809311, 11.747299, 3.085887, 0.9884436, 1.1096711, 2.291764, 0.0, 2.696817, 3.1549034, 3.9089527, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.5, 194.71904, 139.83286, 164.97635, 223.60678, 72.90387, 117.185745, 111.37943, 57.015976, 35.0, 42.5, 0.10266107, 10.5, 9.5, 22.5, 0.005675491, -0.0482161, 0.10772172, 0.0040158336, -0.07065587, -0.19836901, -0.053667415, 0.0780642, 0.01671582, 0.092280544, -0.088257805, -0.32217613, -0.14359127, 0.037804224], "split_indices": [3, 2, 2, 2, 2, 1, 1, 1, 0, 4, 4, 0, 3, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [370.07034, 297.74792, 72.32242, 287.9701, 9.777844, 26.540531, 45.781883, 283.3237, 4.6464095, 3.1482127, 6.629632, 7.5560684, 18.984463, 2.362524, 43.419357, 272.27963, 11.044058, 3.352568, 1.2938416, 2.107932, 1.0402806, 4.826397, 1.8032348, 9.24138, 9.743083, 1.2899593, 1.0725647, 1.2106109, 42.208748], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.09093488, 0.015021859, 0.31812507, 0.09406883, -0.045524694, 0.86773056, 0.2529754, -0.010190714, 0.3579835, -1.0143465, 0.010186914, 0.16105679, 0.95828336, -0.037737027, 0.4103445, 0.005985658, -1.1727259, 0.08016391, 1.1008517, -0.3786286, -1.385903, -0.33291793, 0.05967345, 1.0246454, 0.4677905, 0.12865447, -1.0587212, 0.67100585, 0.07510392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.325512, 1.3171593, 3.276268, 3.2887423, 8.414765, 0.60900545, 3.7629628, 1.6208822, 6.9708457, 1.9794703, 2.4975846, 0.0, 0.26014423, 4.910855, 4.6524773, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 4.5, 11.5, 17.67747, 32.675133, 24.493393, 8.5, 17.519722, 28.407604, 24.742046, 37.558792, 0.01610568, 7.5, 79.75446, 43.5, 0.00059856585, -0.11727259, 0.008016392, 0.11008517, -0.037862863, -0.1385903, -0.033291794, 0.005967345, 0.10246455, 0.04677905, 0.0128654465, -0.10587212, 0.067100585, 0.0075103925], "split_indices": [3, 4, 4, 0, 2, 0, 3, 0, 2, 1, 1, 0, 3, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [366.95258, 275.0893, 91.86328, 119.384995, 155.70428, 9.706037, 82.15724, 85.5573, 33.8277, 8.46722, 147.23708, 1.1181579, 8.587879, 28.834503, 53.322742, 84.38445, 1.172841, 24.638048, 9.189655, 3.1407053, 5.3265147, 18.52669, 128.71039, 7.5368853, 1.0509938, 24.792906, 4.041596, 29.975721, 23.347021], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.08302814, 0.024047462, 0.34010908, 0.05486137, -0.22051291, 0.4513197, -0.16368712, 0.0047944975, 0.32512316, -0.16307652, -1.8350127, 0.38555077, 1.0675561, -0.5062946, 0.31037197, 0.0129618645, -1.694971, 0.9316495, 0.21050093, -0.25253314, 1.1765312, 0.50112545, -0.36720455, -0.23601764, -1.0887481, 1.0763164, -0.41443726], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5.5116444, 2.2283785, 3.8048658, 3.5532134, 3.0443063, 2.2346401, 1.9946859, 3.0625582, 2.8404775, 3.823452, 0.0, 4.37625, 0.0, 1.1070073, 2.8630042, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.5, 19.5, 350.7863, 24.5457, 79.68119, 246.64815, 376.9491, 24.348883, 6.5, 64.07459, -0.18350127, 46.5, 0.106755614, 37.0, 415.30353, 0.0012961865, -0.1694971, 0.09316495, 0.021050094, -0.025253315, 0.117653124, 0.050112545, -0.036720455, -0.023601765, -0.10887481, 0.10763164, -0.041443728], "split_indices": [3, 4, 2, 0, 0, 1, 1, 0, 4, 0, 0, 4, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [363.73315, 295.89697, 67.83619, 262.8298, 33.06716, 55.5866, 12.249585, 221.78973, 41.04009, 31.947811, 1.1193506, 50.25548, 5.331121, 7.122496, 5.127089, 220.74486, 1.0448713, 6.4978795, 34.54221, 29.962545, 1.9852655, 43.58089, 6.674589, 4.886312, 2.2361841, 2.4971573, 2.6299314], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.075797595, 0.029126897, 0.362519, 0.043776322, -0.8582776, 0.6735629, 0.107693285, 0.034818254, 1.0726355, -2.54503, -0.39187658, 0.4613566, 0.9274539, -1.989618, 0.28147575, 0.06335059, -0.18743537, 1.0991983, -1.236675, 0.7513255, -1.7801425, 1.0234865, 0.57338434, -1.2261629, -2.6966383, 0.4337024, -0.44774422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.8216934, 4.0246124, 3.990408, 2.8011532, 3.9152665, 1.2048683, 10.107058, 1.9174924, 0.0, 0.0, 4.996163, 8.066965, 0.3285141, 1.0726604, 2.8492043, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.5, 79.75446, 184.9061, 71.921135, 82.89813, 122.87894, 169.56615, 19.5, 0.10726356, -0.254503, 26.5, 116.802055, 23.5, 94.22523, 113.5, 0.0063350587, -0.018743537, 0.10991984, -0.1236675, 0.07513255, -0.17801426, 0.10234865, 0.057338435, -0.12261629, -0.26966384, 0.043370243, -0.044774424], "split_indices": [3, 0, 2, 0, 0, 1, 1, 4, 0, 0, 4, 1, 3, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [360.61624, 310.17398, 50.442265, 305.15717, 5.0168114, 22.690386, 27.75188, 302.54306, 2.614123, 1.0724083, 3.9444032, 12.396634, 10.293752, 2.1111555, 25.640724, 268.16843, 34.374615, 1.4211736, 2.5232296, 10.986489, 1.4101448, 8.060412, 2.233339, 1.0350134, 1.0761422, 21.227245, 4.4134793], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.0691658, -0.0018949725, 0.24472083, 0.082644254, -0.09480833, -0.7780967, 0.27085868, 0.04510099, 0.9566917, -0.77818465, -0.024060687, 0.44245517, -2.2632303, 0.56107897, 0.13315843, 0.010957544, 0.77175945, 0.53813344, 1.123436, -0.48166203, -2.0896482, 1.1077656, -0.042673595, 0.7536086, 0.3455252, -1.3074018, 0.18355097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.4730606, 2.003758, 2.7587109, 4.3704414, 5.869813, 4.6815624, 4.0183854, 3.168531, 0.36027575, 4.394443, 2.3112943, 0.0, 0.0, 1.3306818, 4.9538097, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 5.5, 31.41743, 20.237606, 35.135387, 24.458725, 97.20796, 37.558792, 2.5, 20.025894, 44.486942, 0.04424552, -0.22632304, 12.5, 85.477196, 0.0010957544, 0.077175945, 0.053813346, 0.1123436, -0.048166204, -0.20896482, 0.11077656, -0.0042673596, 0.07536086, 0.034552522, -0.13074018, 0.018355098], "split_indices": [3, 4, 2, 0, 1, 1, 2, 1, 4, 0, 2, 0, 0, 4, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [358.3547, 255.06357, 103.29114, 133.49434, 121.56923, 2.557102, 100.73403, 128.01752, 5.476824, 11.382296, 110.18693, 1.4037349, 1.1533672, 32.381866, 68.352165, 122.29516, 5.7223606, 1.5893962, 3.8874276, 9.3007, 2.0815957, 1.7645503, 108.42238, 17.06674, 15.315125, 2.295089, 66.05708], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.06308229, 0.021767426, 0.32898605, 0.035157673, -0.75660616, 0.5907676, 0.021348668, 0.02708331, 1.0639333, -2.1676202, -0.33346233, 0.25249672, 0.8665011, -1.0428153, 0.21439528, -0.057566576, 0.090503216, 1.0866485, -1.0128351, 0.7620059, -0.7800287, 1.0209687, 0.59374434, 0.013588529, -2.5553243, 0.5787799, -0.11645255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.9063406, 3.2041354, 3.8456273, 2.5049028, 3.071394, 2.3951616, 4.516917, 1.6139399, 0.0, 0.0, 3.885052, 6.122327, 0.5763159, 5.417532, 2.2539368, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.5, 79.75446, 43.5, 71.921135, 82.863525, 99.63033, 124.31906, 17.264828, 0.10639333, -0.21676202, 137.83739, 127.10041, 195.76349, 223.13199, 363.39035, -0.0057566576, 0.009050322, 0.10866485, -0.10128351, 0.0762006, -0.07800287, 0.10209687, 0.059374433, 0.001358853, -0.25553244, 0.057877988, -0.011645256], "split_indices": [3, 0, 4, 0, 0, 0, 0, 0, 0, 0, 1, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [355.89743, 308.07812, 47.819294, 302.88995, 5.1881757, 25.81258, 22.006716, 300.55106, 2.3388963, 1.1819593, 4.0062165, 11.616652, 14.195929, 3.3644514, 18.642263, 128.66216, 171.8889, 1.2896703, 2.716546, 7.7865906, 3.8300605, 9.022315, 5.1736126, 1.9828435, 1.381608, 8.882315, 9.759949], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.057629634, 0.012410212, 0.27487823, 0.028039556, -0.4454134, 0.58437574, 0.11738687, 0.016224265, 0.7540173, -1.0986977, -0.14361948, 1.0182004, 0.42319384, -1.643801, 0.2315773, 0.02858929, -0.8588498, 1.0140918, 0.05607359, -0.75451183, -1.7425948, -0.4686126, 0.7187522, -0.90323395, 0.577207, -0.8632436, -2.7257922, -1.3277365, 0.2856552], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.4689429, 2.0894399, 2.958263, 2.4191632, 1.8861328, 1.4145417, 8.106834, 3.0047102, 0.80871487, 0.6341474, 1.8546059, 0.0, 3.0619047, 2.0126905, 3.1907935, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.5, 194.71904, 139.83286, 164.97635, 223.60678, 72.90387, 117.185745, 60.446213, 57.015976, 192.85098, 42.5, 0.101820044, 78.268326, 20.5, 22.5, 0.002858929, -0.08588498, 0.10140919, 0.005607359, -0.07545119, -0.17425948, -0.04686126, 0.07187522, -0.090323396, 0.057720702, -0.08632436, -0.27257922, -0.13277365, 0.02856552], "split_indices": [3, 2, 2, 2, 2, 1, 1, 0, 0, 1, 4, 0, 1, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [353.40012, 292.56125, 60.838852, 282.9336, 9.627657, 20.487265, 40.351585, 278.4262, 4.50739, 3.0234168, 6.60424, 5.518605, 14.968659, 2.4434905, 37.908096, 274.5676, 3.858611, 3.2700243, 1.2373658, 1.9945223, 1.0288945, 4.8083344, 1.7959056, 1.5454037, 13.423256, 1.4352849, 1.0082057, 1.2565168, 36.651577], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.052393164, -0.0057125976, 0.20381038, 0.074764594, -0.09382294, 0.30053118, -0.037721995, 0.04093193, 0.92754394, -0.4570283, 0.016959151, -0.427839, 0.3588773, -1.5623202, 0.11398683, 0.010162641, 0.7049956, 0.49680364, 1.1081085, -0.33462474, -1.9182923, 0.6610578, -0.02419558, -0.06472454, -1.8882116, 1.0392778, 0.24745964, -1.300129, -1.7381763, -1.2854176, 0.2149135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.095414, 1.8017138, 2.281724, 3.8192565, 4.886735, 2.9625025, 6.4634175, 2.6016498, 0.3714447, 5.0474143, 2.470881, 2.715691, 4.877224, 0.06363869, 3.5731065, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.5, 5.5, 198.22841, 20.237606, 44.336067, 33.55829, 150.91327, 37.558792, 2.5, 27.361094, 54.704453, 40.734, 10.5, 98.2892, 209.34552, 0.0010162642, 0.07049956, 0.049680363, 0.11081085, -0.033462476, -0.19182923, 0.066105776, -0.0024195581, -0.0064724544, -0.18882117, 0.10392778, 0.024745964, -0.13001291, -0.17381763, -0.12854175, 0.02149135], "split_indices": [3, 4, 2, 0, 1, 1, 1, 1, 4, 0, 2, 2, 4, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [351.58652, 254.04234, 97.544174, 132.71379, 121.32855, 69.680016, 27.864155, 127.67087, 5.042933, 28.37115, 92.9574, 5.1503496, 64.52967, 2.519307, 25.344849, 122.04131, 5.629554, 1.5169985, 3.5259342, 26.195963, 2.1751852, 5.588353, 87.36905, 4.1385984, 1.0117514, 9.055698, 55.47397, 1.0679108, 1.4513961, 1.690378, 23.65447], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "31", "size_leaf_vector": "0"}}, {"base_weights": [0.047823228, 0.013690497, 0.28203666, 0.025973218, -0.67582494, 0.5581879, -0.024361134, 0.018592833, 1.0601913, -1.6969118, -0.3191567, 0.22270595, 0.84126467, -0.9785403, 0.15799448, 0.04576155, -0.1923096, 1.086262, -0.93465805, 0.7177445, -0.6035727, 1.0176516, 0.55497706, -0.23009631, -2.4971418, 0.31286716, -0.660391], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.7903745, 2.57694, 3.7628498, 2.276655, 1.9135623, 2.2072444, 3.6712518, 1.7038716, 0.0, 0.0, 3.4391277, 4.3930807, 0.61778736, 3.8160717, 2.237752, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.5, 79.75446, 43.5, 71.921135, 82.863525, 99.63033, 124.31906, 19.5, 0.10601913, -0.16969119, 26.5, 127.10041, 195.76349, 230.982, 142.5, 0.0045761555, -0.01923096, 0.1086262, -0.093465805, 0.07177445, -0.060357273, 0.101765156, 0.055497706, -0.023009632, -0.24971418, 0.031286716, -0.0660391], "split_indices": [3, 0, 4, 0, 0, 0, 0, 4, 0, 0, 4, 2, 2, 2, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [349.40826, 305.01123, 44.397057, 299.6967, 5.314537, 23.363943, 21.033112, 297.59286, 2.103833, 1.3588222, 3.9557152, 10.716601, 12.647342, 3.3765302, 17.656582, 263.68045, 33.912403, 1.1974944, 2.7582207, 6.7099595, 4.0066423, 7.788334, 4.8590074, 2.2739248, 1.1026056, 14.862393, 2.794189], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.04358859, -0.0020891689, 0.20370388, 0.18561235, -0.027045097, 0.8308347, 0.13784106, -0.752916, 0.25673017, -0.0887271, 0.0816527, 0.1445532, 0.9497364, -1.015141, 0.17742498, 0.5788379, -2.0868802, 1.4770672, 0.19823328, -0.041840523, -0.5615988, 1.2775849, 0.03353087, 1.0150845, 0.59292746, 0.24755847, -2.2239661, 0.8027465, 0.11475472], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.5455737, 1.26578, 3.1796012, 2.1130695, 1.6015846, 0.5853801, 3.1848562, 3.9813128, 2.0853405, 3.3736572, 4.959847, 0.0, 0.12313795, 3.5575204, 2.6399765, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 27.817184, 11.5, 14.005108, 49.724396, 24.493393, 31.919846, 13.200996, 14.77444, 8.5, 50.730988, 0.01445532, 76.29364, 29.219387, 49.76841, 0.057883788, -0.20868802, 0.14770673, 0.019823328, -0.0041840523, -0.05615988, 0.12775849, 0.003353087, 0.10150845, 0.059292745, 0.024755849, -0.22239661, 0.08027465, 0.011475473], "split_indices": [3, 2, 4, 2, 1, 0, 0, 2, 2, 4, 1, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [347.76892, 270.53732, 77.23159, 31.693708, 238.84361, 7.3147, 69.91689, 2.2157953, 29.477913, 152.43576, 86.40786, 1.0958562, 6.218844, 2.3041315, 67.61276, 1.1078976, 1.1078976, 1.3294772, 28.148436, 138.71391, 13.7218485, 3.3244612, 83.0834, 5.2169123, 1.0019319, 1.1267962, 1.1773353, 6.1336617, 61.4791], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.03969752, 0.009614632, 0.25385615, 0.019090984, -0.82642466, 0.526139, -0.033210717, 0.04501645, -0.1582451, -1.2214502, -0.62960565, 0.19500878, 0.8191655, -0.85988843, 0.13461828, 0.011678534, 0.4947615, -0.29110655, 0.34604952, -0.4142445, -0.80195856, 0.6901505, -0.5428397, 1.0151981, 0.5176875, -0.206046, -2.1126795, 0.5128679, -0.17805052], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.2282822, 2.3980553, 3.3356488, 1.3795853, 0.23752427, 2.111916, 2.8849778, 3.9240398, 2.5636458, 0.0, 0.06994468, 3.7698154, 0.6645112, 2.847846, 2.0423703, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.5, 325.63138, 43.5, 19.5, 383.0681, 99.63033, 124.31906, 3.5, 159.72784, -0.12214502, 19.273697, 127.10041, 195.76349, 222.63345, 363.39035, 0.0011678534, 0.04947615, -0.029110655, 0.03460495, -0.04142445, -0.08019586, 0.06901505, -0.054283973, 0.101519816, 0.05176875, -0.020604601, -0.21126795, 0.051286794, -0.017805053], "split_indices": [3, 2, 4, 4, 2, 0, 0, 3, 1, 0, 0, 2, 2, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [346.27444, 303.6744, 42.600037, 300.2924, 3.3820262, 21.876598, 20.72344, 262.0466, 38.24578, 1.0904902, 2.2915359, 10.294374, 11.582223, 3.5003016, 17.223137, 243.99283, 18.05378, 30.28971, 7.95607, 1.0595924, 1.2319435, 6.1688776, 4.125497, 6.984068, 4.598155, 2.312838, 1.1874633, 7.805749, 9.417389], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.036202494, 0.0087226145, 0.23716931, 0.0187645, -0.72381526, 0.50144696, -0.029174238, 0.011948525, 0.8649088, -1.8207288, -0.28795266, -0.3953314, 0.61469185, -0.41606358, 0.26598063, 0.01843211, -1.579292, 0.20164332, 1.4652867, -1.1394193, 0.27495176, 0.24689272, -1.2400059, 1.0110471, 0.46648812, 1.1227827, -0.63966256, 1.0206141, -0.09109278], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.898918, 2.221501, 2.916947, 1.7147579, 1.9259493, 2.115522, 2.354486, 3.0436184, 0.92665696, 0.0, 1.4212034, 1.2751552, 1.0653987, 3.0755036, 3.1559997, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.5, 82.785, 43.5, 222.27347, 85.734055, 15.5, 199.9442, 193.02306, 38.442963, -0.18207288, 6.5, 87.81104, 127.10041, 12.5, 363.39035, 0.0018432111, -0.15792921, 0.020164331, 0.14652868, -0.11394193, 0.027495176, 0.024689272, -0.12400059, 0.101104714, 0.04664881, 0.112278275, -0.06396626, 0.10206141, -0.009109278], "split_indices": [3, 0, 4, 1, 0, 4, 0, 1, 0, 0, 3, 2, 2, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [344.28745, 302.92603, 41.36142, 298.85263, 4.073381, 20.775642, 20.585775, 296.4863, 2.3663366, 1.1427538, 2.930627, 2.316694, 18.458948, 8.921391, 11.664385, 295.29926, 1.1870422, 1.1385462, 1.2277904, 1.1693522, 1.7612749, 1.313358, 1.0033361, 4.9894624, 13.469486, 1.1210686, 7.800322, 3.749555, 7.91483], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.032962307, -0.032757495, 0.11278448, 0.11731536, -0.09789021, 0.8129938, 0.0908834, 0.08740379, 1.2434634, -2.3213704, -0.0800052, 1.036983, -0.0010376747, -1.3353788, 0.101252966, 0.15626095, -0.28267246, -0.15229881, 0.13298151, 0.2973868, 1.3352175, 1.1721601, 0.0868413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8011988, 1.8391497, 2.3678303, 1.902485, 5.1942124, 0.8650031, 2.2128515, 1.4122617, 0.0, 0.0, 2.0056865, 0.792603, 0.0, 0.0, 2.2905416, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [17.997099, 1.5, 17.998547, 17.99152, 17.948483, 73.57818, 17.99973, 36.2247, 0.12434634, -0.23213704, 53.255356, 28.27616, -0.000103767474, -0.13353787, 18.633335, 0.015626095, -0.028267248, -0.015229881, 0.013298151, 0.02973868, 0.13352175, 0.11721601, 0.00868413], "split_indices": [0, 4, 0, 0, 1, 1, 1, 2, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [343.18414, 188.1531, 155.03104, 56.896305, 131.2568, 4.677176, 150.35387, 55.443333, 1.4529718, 1.0326273, 130.22418, 3.6753976, 1.0017782, 1.0690666, 149.28479, 46.77338, 8.669953, 97.26092, 32.963257, 1.0715488, 2.603849, 1.9624329, 147.32236], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.029922936, -0.023127422, 0.11587195, -0.017119324, -1.1283469, 0.76427865, 0.077639446, 0.10193942, -0.066825375, -1.1274608, 1.1708817, -0.56114626, 0.11034811, -0.0021095616, 0.3539234, -0.65473956, -0.04195741, 1.2240098, 0.92627025, -0.11628839, -2.0573475, 0.7619559, 0.057425134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, 17, 19, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5597659, 1.3916419, 3.2274098, 1.2437087, 0.0, 5.5929685, 2.572505, 1.6253245, 2.161819, 0.0, 0.04486847, 3.964898, 4.037348, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, 18, 20, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5, 44.0, 19.820995, 18.514736, -0.11283469, 19.12809, 21.240452, 17.963614, 19.640026, -0.11274608, 52.84658, 20.916473, 6.5, -0.00021095616, 0.03539234, -0.06547396, -0.004195741, 0.12240098, 0.092627026, -0.011628839, -0.20573476, 0.07619559, 0.0057425136], "split_indices": [3, 4, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [341.8627, 211.32031, 130.54239, 210.19685, 1.123451, 7.2435455, 123.298836, 61.84663, 148.35022, 1.2739786, 5.969567, 5.983157, 117.31568, 43.75117, 18.095459, 5.9937615, 142.35646, 4.8378887, 1.1316782, 4.6253777, 1.3577793, 8.788267, 108.52741], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.027209109, 0.0030818316, 0.20861953, 0.011594627, -0.7774451, 0.4664487, -0.039223496, -0.682361, 0.016940637, -0.37406898, -0.96014917, 0.12831856, 0.7810268, -0.7234949, 0.11088588, 0.51788336, -1.7905327, 1.4006121, 0.010977626, -0.44744647, -1.4172789, 0.63862234, -0.5357061, 1.0119362, 0.45147827, 0.3039832, -1.3643377, 0.26253355, -0.6062361], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.4887403, 1.990375, 2.556349, 1.0970483, 0.22122943, 2.0740576, 2.0973213, 3.0280342, 2.4215562, 0.0, 0.49080038, 3.2136202, 0.7518835, 2.4343076, 1.8128277, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.5, 325.63138, 43.5, 14.005108, 39.739166, 99.63033, 124.31906, 13.200996, 14.77444, -0.0374069, 409.2824, 127.10041, 195.76349, 159.75261, 77.5, 0.051788338, -0.17905328, 0.14006121, 0.0010977626, -0.044744648, -0.1417279, 0.063862234, -0.05357061, 0.10119362, 0.04514783, 0.030398322, -0.13643377, 0.026253356, -0.060623612], "split_indices": [3, 2, 4, 2, 1, 0, 0, 2, 2, 0, 2, 2, 2, 2, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [340.60788, 300.68286, 39.92502, 297.46127, 3.22159, 19.583736, 20.34128, 2.250046, 295.21124, 1.0308578, 2.1907322, 9.460649, 10.123088, 3.6646135, 16.676666, 1.0783758, 1.1716701, 1.2495383, 293.9617, 1.0522381, 1.1384941, 5.358261, 4.102388, 5.9223433, 4.2007446, 1.4036927, 2.2609208, 13.782422, 2.8942444], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.024773292, 0.021119343, 1.1502775, 0.027748974, -1.3974441, 0.019797428, 1.1808898, 0.03548969, -0.6268707, 1.0432132, 1.3293946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [1.3837234, 3.1681852, 0.0, 3.0753782, 0.0, 3.38819, 0.009375334, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [452.3753, 415.30353, 0.11502775, 376.9491, -0.13974442, 363.39035, 87.0, 0.003548969, -0.06268707, 0.10432132, 0.13293946], "split_indices": [0, 0, 0, 1, 0, 2, 4, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [339.36212, 338.28296, 1.0791634, 336.72614, 1.5568302, 334.43884, 2.2872977, 326.53983, 7.8990083, 1.2635055, 1.0237923], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "11", "size_leaf_vector": "0"}}, {"base_weights": [0.022409577, -0.00034664167, 0.19891591, 0.008550576, -0.63023835, 0.4428836, -0.027067099, 0.0022980035, 0.80775505, -1.4980193, -0.24933359, -0.43364015, 0.56142354, -0.35418445, 0.24401987, 0.00811678, -1.5054661, 0.1922544, 1.3939698, -0.9916265, 0.28646216, -1.1708585, 0.17744301, 1.0076361, 0.40935186, 1.1158057, -0.5541538, 1.0169182, -0.0872303], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3624545, 1.6874908, 2.139651, 1.4700397, 1.3651184, 1.9411464, 1.7839416, 2.560392, 0.8054936, 0.0, 1.1770817, 1.0107374, 1.0972381, 2.6835454, 2.8207586, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [8.5, 82.785, 43.5, 222.27347, 85.734055, 15.5, 199.9442, 193.02306, 38.442963, -0.14980194, 6.5, 63.039177, 127.10041, 12.5, 363.39035, 0.000811678, -0.15054661, 0.019225439, 0.13939698, -0.09916265, 0.028646216, -0.11708585, 0.0177443, 0.10076361, 0.040935185, 0.11158057, -0.05541538, 0.10169182, -0.008723031], "split_indices": [3, 0, 4, 1, 0, 4, 0, 1, 0, 0, 3, 0, 2, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [338.69998, 299.97836, 38.721622, 295.79425, 4.184104, 18.63655, 20.085073, 293.52045, 2.2738147, 1.2593834, 2.9247208, 2.2069187, 16.429632, 9.117237, 10.967835, 292.40857, 1.1118871, 1.1240001, 1.1498145, 1.2303486, 1.694372, 1.0034945, 1.2034243, 4.145064, 12.284568, 1.0802715, 8.036965, 3.2932537, 7.674582], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.020448675, -0.05923617, 0.068812594, -0.043163233, -1.0024309, 1.3548445, 0.051237352, -0.07812231, 0.47756097, -2.0923526, 0.06283117, -0.04315718, -0.68415433, 1.6589527, -0.14858629, 0.59225035, 0.034686714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3023043, 1.9227039, 4.7396245, 2.2797146, 0.0, 0.0, 5.1386714, 2.4836483, 5.8425045, 0.0, 3.0693662, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [17.264828, 16.939968, 17.601456, 64.66063, -0.10024309, 0.13548444, 17.633373, 9.5, 87.21457, -0.20923527, 17.883833, -0.004315718, -0.06841543, 0.16589527, -0.014858629, 0.059225034, 0.0034686716], "split_indices": [0, 0, 0, 1, 0, 0, 0, 4, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [337.87564, 127.536415, 210.33922, 125.4202, 2.11622, 2.8178654, 207.52136, 117.55574, 7.8644586, 1.1020675, 206.4193, 111.16903, 6.3867054, 2.7241356, 5.140323, 10.391492, 196.0278], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "17", "size_leaf_vector": "0"}}, {"base_weights": [0.018519863, -0.020914024, 0.10625309, -0.013586732, -1.5426468, 0.5821356, 0.035334494, -0.018949013, 1.0698967, -0.284835, 0.8107989, -1.3618127, 0.10116825, 0.059320703, -0.09636961, 1.1198386, -1.1226779, 1.096904, 0.5086726, -2.05929, 0.05356482, 0.5979763, 0.050017517], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, 19, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1671079, 2.5765638, 3.5241458, 1.3340864, 0.0, 2.6935606, 8.365583, 1.3958205, 0.0, 3.3384876, 0.906116, 4.0608563, 2.2038333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, 20, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [24.5457, 24.348883, 9.5, 3.5, -0.15426467, 42.569477, 51.79444, 4.5, 0.106989674, 26.46257, 35.816227, 3.5, 12.5, 0.0059320703, -0.009636961, 0.11198386, -0.11226779, 0.109690405, 0.05086726, -0.205929, 0.005356482, 0.059797633, 0.0050017517], "split_indices": [0, 0, 4, 3, 0, 2, 1, 4, 0, 0, 0, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [337.08426, 232.48273, 104.601524, 231.38524, 1.097482, 13.538893, 91.06264, 230.2647, 1.1205513, 2.8159826, 10.72291, 4.0821013, 86.98054, 114.44136, 115.82333, 1.046383, 1.7695996, 5.4769692, 5.2459407, 2.7362387, 1.3458624, 8.09121, 78.88932], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.017020367, 0.00038393593, 0.21417195, 0.016533744, -0.32412118, 0.6861244, -0.0073574195, 0.00661787, 0.7383253, -0.47340328, 0.90326643, 1.0129569, 0.35896116, -0.71870476, 0.12503496, 0.013358101, -1.6113391, 1.2296792, -0.061483532, -0.2777698, -2.778778, -0.6583139, 1.0536958, 0.4401177, -2.601983, 0.66098726, -0.18478449], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.100237, 1.6216195, 2.7364504, 2.1063583, 2.6875308, 0.87597704, 1.67973, 3.1617258, 1.5799525, 5.865943, 0.0, 0.0, 2.9833834, 6.1450577, 2.4921784, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [109.56474, 189.49962, 43.5, 154.74878, 252.56874, 195.76349, 198.09229, 149.67593, 36.5, 230.70627, 0.090326644, 0.10129569, 148.95673, 184.9061, 363.39035, 0.0013358102, -0.16113392, 0.12296792, -0.006148353, -0.02777698, -0.2778778, -0.065831386, 0.10536958, 0.044011768, -0.26019832, 0.06609873, -0.01847845], "split_indices": [0, 2, 4, 1, 1, 2, 1, 1, 4, 1, 0, 0, 0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [336.0929, 309.99503, 26.097866, 295.33716, 14.657885, 8.344556, 17.75331, 291.35846, 3.9786963, 13.081166, 1.5767183, 4.148611, 4.1959443, 2.7905998, 14.96271, 290.16574, 1.1927006, 2.469013, 1.5096833, 12.07226, 1.0089062, 1.6970886, 2.4988558, 1.729038, 1.0615618, 5.488747, 9.473964], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.01563911, 0.01220733, 1.1340035, 0.019134017, -1.0267458, 0.011735849, 0.7984262, -2.0651956, -0.09355327, 0.021024339, -0.698525, 0.1942776, 1.1599989], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2747084, 2.3948038, 0.0, 1.9056219, 2.1119456, 2.162902, 0.6633178, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [452.3753, 77.5, 0.113400355, 47.5, 104.5, 418.61975, 80.5, -0.20651956, -0.009355327, 0.002102434, -0.0698525, 0.01942776, 0.11599989], "split_indices": [0, 3, 0, 3, 3, 2, 4, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [335.14243, 334.13617, 1.0062698, 331.94257, 2.1936154, 328.8437, 3.0988762, 1.0274408, 1.1661748, 324.6222, 4.221488, 1.1763513, 1.9225249], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "13", "size_leaf_vector": "0"}}, {"base_weights": [0.014131372, -0.060246114, 0.056303047, -0.10034448, 0.22322991, 1.1749003, 0.045033988, -0.048830967, -0.6764716, 1.5108783, -0.032031927, -0.24398237, 0.08840628, -0.060895048, 1.0221623, -0.98603314, -0.3945511, 1.0658311, 1.9350132, -0.916565, 0.15989995, 0.3167659, -0.4404088, 1.0855194, 0.038424045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0494487, 1.3741935, 2.6799207, 3.1391416, 4.9339895, 0.0, 2.649842, 1.2488537, 0.7403028, 0.41796112, 2.1298418, 3.0393672, 9.155392, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [14.528929, 49.05276, 22.836498, 42.181847, 51.527863, 0.11749003, 28.075882, 41.03672, 45.596138, 50.035343, 53.255356, 18.62513, 4.5, -0.006089505, 0.10221624, -0.098603316, -0.039455112, 0.10658311, 0.19350132, -0.0916565, 0.015989995, 0.03167659, -0.04404088, 0.10855194, 0.0038424046], "split_indices": [0, 1, 2, 1, 1, 0, 2, 1, 1, 1, 1, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [334.57092, 120.97243, 213.5985, 106.01771, 14.954724, 2.1112385, 211.48726, 97.34221, 8.675498, 2.4724798, 12.4822445, 27.560148, 183.92711, 96.27648, 1.0657266, 4.106227, 4.5692706, 1.2351433, 1.2373364, 2.228121, 10.254124, 7.1348634, 20.425285, 8.760169, 175.16693], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "25", "size_leaf_vector": "0"}}, {"base_weights": [0.012929394, -0.004842479, 0.17922959, -0.009241129, 1.0900956, 0.8251561, 0.04954567, -0.0018650048, -0.83771956, 1.0087351, 0.025606813, -2.1604364, 0.13678285, 0.0031177572, -0.7263377, -0.32053304, -1.1707922, 0.8000265, -0.0085362075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, -1, 17, -1, -1, -1, -1, -1, -1], "loss_changes": [0.98802495, 1.4411381, 2.6943083, 1.8264668, 0.0, 0.77947974, 5.1690626, 1.0816711, 0.4357072, 0.0, 0.0, 0.0, 2.5036335, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, -1, 18, -1, -1, -1, -1, -1, -1], "split_conditions": [10.5, 68.0, 139.83286, 325.63138, 0.109009564, 21.5, 121.00792, 49.5, 46.575188, 0.10087351, 0.0025606814, -0.21604364, 184.9061, 0.00031177572, -0.072633766, -0.032053303, -0.11707922, 0.08000265, -0.00085362076], "split_indices": [3, 4, 2, 2, 0, 3, 1, 4, 1, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [333.7013, 301.4374, 32.263897, 300.25027, 1.1871346, 5.3727407, 26.891157, 297.62256, 2.6277058, 4.3571925, 1.0155482, 1.007817, 25.883339, 295.58572, 2.0368192, 1.0488014, 1.5789043, 4.656158, 21.227182], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "19", "size_leaf_vector": "0"}}, {"base_weights": [0.011782371, -0.0052304897, 0.17660591, 0.0033381118, -1.3049215, 1.167167, 0.082475945, -0.0055773705, 0.75901234, 1.3124782, 1.0353808, 0.37195975, -0.073489055, 0.00058856624, -1.6546221, 1.0082567, 0.12892053, 0.059639666, 1.0424385, -0.60083395, 0.19687355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.935294, 3.3733287, 2.8960958, 2.0271194, 0.0, 0.014598131, 1.2907615, 3.0289083, 0.5383265, 0.0, 0.0, 2.084198, 2.6454072, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [159.72784, 153.42719, 31.5, 106.06833, -0.13049215, 3.5, 46.5, 98.52843, 155.24673, 0.13124782, 0.10353809, 148.7595, 246.64815, 5.8856625e-05, -0.16546221, 0.10082567, 0.0128920525, 0.0059639667, 0.10424385, -0.060083397, 0.019687355], "split_indices": [1, 1, 4, 0, 0, 3, 4, 0, 2, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [332.91833, 301.72382, 31.19451, 299.7499, 1.9739107, 2.6879616, 28.506548, 296.2514, 3.498496, 1.1996835, 1.4882782, 9.998599, 18.50795, 295.15173, 1.0996685, 2.4911885, 1.0073075, 6.838784, 3.1598144, 6.2811904, 12.22676], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "21", "size_leaf_vector": "0"}}, {"base_weights": [0.010849809, -0.0484362, 0.05606187, 0.0055501335, -0.72336507, 1.610567, 0.036239855, -0.02274224, 0.6085468, -0.4183909, -2.3476708, 1.9053624, 1.2889876, 1.5859321, 0.025071014, 0.03669177, -0.47056264, 0.9046475, -0.5130041, -0.6197477, 1.0686522, -0.69509774, 0.046483554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, 21, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.89163023, 5.24736, 5.800532, 2.2777128, 5.2457643, 0.16822624, 3.210441, 3.3896325, 1.9957814, 2.6959682, 0.0, 0.0, 0.0, 0.0, 2.8485403, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [37.558792, 6.5, 43.3882, 21.01149, 24.441591, 17.229733, 38.085052, 4.5, 33.50856, 2.5, -0.23476708, 0.19053625, 0.12889877, 0.15859322, 48.26672, 0.003669177, -0.047056265, 0.090464756, -0.051300414, -0.06197477, 0.10686522, -0.069509774, 0.0046483553], "split_indices": [1, 4, 2, 0, 0, 0, 1, 4, 1, 3, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [332.5943, 143.80402, 188.7903, 133.14879, 10.655228, 2.360562, 186.42973, 127.174835, 5.9739556, 8.986656, 1.668572, 1.1896498, 1.1709123, 1.317514, 185.11221, 112.2624, 14.9124365, 4.734777, 1.2391784, 7.9259763, 1.0606796, 5.3223915, 179.78983], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.010007966, -0.6144391, 0.014353997, 0.47037056, -1.5603179, 1.3422794, 0.009548348, -1.2583468, 0.013838199, 1.0051885, 0.0071318084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 0, 0, 0, 1, 0, 1, 0, 0], "id": 61, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, 9, -1, -1], "loss_changes": [0.89500815, 2.356511, 2.0903528, 0.0, 0.0, 0.0, 1.7746272, 0.0, 2.1667473, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6, 8, 8], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, 10, -1, -1], "split_conditions": [14.005108, 13.200996, 14.77444, 0.047037058, -0.15603179, 0.13422795, 15.2718315, -0.12583469, 16.16793, 0.100518845, 0.00071318087], "split_indices": [2, 2, 2, 0, 0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [331.95953, 2.2687993, 329.69073, 1.0542061, 1.2145932, 1.1712813, 328.51944, 1.0900606, 327.42938, 2.1801212, 325.24927], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "11", "size_leaf_vector": "0"}}, {"base_weights": [0.009059651, -0.01338293, 0.1189568, -0.002828803, -0.7089741, 0.9155666, 0.06440225, -0.0131104775, 0.32968712, -1.5652298, -0.07645386, 1.0093492, 0.6567063, -0.5332438, 0.13809042, 0.14041977, -0.03345865, 0.7171053, -0.36804673, 0.37053144, -0.6554947, -1.8816704, -0.16546558, 0.7083658, 0.022491418], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8193545, 2.015005, 2.4380765, 0.9245837, 2.20437, 0.064071655, 2.3199325, 0.8213945, 2.198385, 0.0, 0.6204543, 0.0, 0.0, 2.8415375, 3.0898814, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.5, 56.84178, 11.5, 3.5, 62.972248, 69.59786, 15.5, 27.817184, 19.5, -0.15652297, 79.68119, 0.10093492, 0.06567063, 40.571407, 105.80037, 0.014041977, -0.003345865, 0.071710534, -0.036804672, 0.037053145, -0.06554947, -0.18816704, -0.016546559, 0.07083658, 0.002249142], "split_indices": [3, 0, 4, 3, 0, 2, 4, 2, 4, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [331.7654, 275.43533, 56.330082, 271.3427, 4.0926356, 3.5885565, 52.741524, 263.24268, 8.100008, 1.7258123, 2.3668232, 2.5845692, 1.0039873, 5.767367, 46.974155, 30.741636, 232.50105, 5.2172008, 2.8828075, 1.3280095, 1.0388137, 1.2212837, 4.546083, 7.8939204, 39.08024], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "25", "size_leaf_vector": "0"}}, {"base_weights": [0.008374518, -0.068450466, 0.041371208, -0.0041450835, -0.7395, 0.78055173, 0.013668004, -0.023564141, 0.9867169, -0.5383992, -1.7922386, 0.5743378, 1.9530373, -0.09351283, 0.08494386, 0.030157521, -0.4688038, 0.050573174, -1.1709648, 1.1567543, -0.4901406, 0.015390136, -0.3631934, 0.46664867, 0.018332714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8389263, 4.2807755, 4.7360506, 1.7359798, 1.8039179, 1.9837952, 1.7060724, 2.132954, 0.0, 2.7341816, 0.0, 4.4311204, 0.0, 2.6216486, 3.4069672, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [31.43778, 4.5, 35.164383, 26.875484, 30.61413, 34.74009, 17.99327, 23.776947, 0.0986717, 17.990248, -0.17922387, 29.26352, 0.19530372, 17.883833, 8.5, 0.0030157522, -0.04688038, 0.0050573177, -0.11709648, 0.11567543, -0.049014058, 0.0015390137, -0.03631934, 0.046664868, 0.0018332715], "split_indices": [2, 4, 2, 1, 2, 2, 0, 1, 0, 0, 0, 1, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [331.02695, 99.36508, 231.66187, 90.69858, 8.666506, 8.345786, 223.31609, 88.97469, 1.7238845, 7.2971683, 1.3693376, 7.1174192, 1.2283672, 89.135475, 134.1806, 79.38333, 9.59136, 3.7753694, 3.5217988, 4.6058893, 2.5115294, 63.47515, 25.660322, 19.905434, 114.27518], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "25", "size_leaf_vector": "0"}}, {"base_weights": [0.007729038, -0.6722881, 0.011440303, 0.82610106, 0.0074462183, -1.1079484, 0.0113782985, 0.93151504, 0.005398403], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 0, 1, 0, 1, 0, 1, 0, 0], "id": 64, "left_children": [1, -1, 3, -1, 5, -1, 7, -1, -1], "loss_changes": [0.8275194, 0.0, 1.0611832, 0.0, 1.4239092, 0.0, 1.7843543, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 6, 6], "right_children": [2, -1, 4, -1, 6, -1, 8, -1, -1], "split_conditions": [13.6150055, -0.06722882, 14.77444, 0.08261011, 15.2718315, -0.11079484, 16.16793, 0.0931515, 0.0005398403], "split_indices": [2, 0, 2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [330.47424, 1.7694038, 328.70483, 1.5814404, 327.1234, 1.1303539, 325.99304, 2.0841317, 323.9089], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "9", "size_leaf_vector": "0"}}, {"base_weights": [0.0069863857, -0.034667917, 0.06713094, 0.023377126, -0.36395264, 0.82771, 0.011931879, -0.0067128786, 0.75354004, -0.23204306, -1.4515097, -0.32448742, 1.0077024, -0.31771326, 0.042999957, 0.0033910254, -1.4481137, 0.877922, 0.052614987, -0.3394552, 1.3567147, -2.917519, -0.6179404, 1.3777038, 0.49623814, 0.41895837, -1.2079914, 1.3369282, 0.0057204594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8279756, 3.7322557, 5.6708493, 3.6510127, 4.1777077, 1.9058213, 1.2893008, 2.3340409, 0.5636153, 4.451328, 3.8023, 0.0, 1.4724455, 7.121188, 5.54905, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [63.12879, 8.5, 69.741745, 27.58728, 60.44388, 17.223146, 57.175667, 26.46257, 56.655624, 58.653526, 53.17195, -0.032448743, 22.222404, 4.5380635, 63.830505, 0.00033910255, -0.14481138, 0.0877922, 0.005261499, -0.03394552, 0.13567148, -0.29175192, -0.061794043, 0.13777038, 0.049623813, 0.041895837, -0.12079914, 0.13369283, 0.000572046], "split_indices": [2, 4, 2, 0, 2, 0, 1, 0, 2, 2, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [330.34344, 195.07698, 135.26646, 165.82492, 29.252058, 9.130983, 126.135475, 159.2581, 6.566812, 26.106886, 3.1451707, 1.2250978, 7.9058857, 10.828326, 115.307144, 158.15253, 1.1055758, 5.562867, 1.0039451, 24.466896, 1.6399907, 1.1266751, 2.0184956, 4.566162, 3.3397236, 5.922086, 4.906241, 3.2118173, 112.09533], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "29", "size_leaf_vector": "0"}}, {"base_weights": [0.006582111, 0.011051026, -0.5200079, 0.004039833, 0.80067396, -1.1200315, 0.4072729, 0.015998468, -0.534005, 1.160606, 0.13977726, 0.0060365098, 0.9487188, -0.27386165, -1.5067822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [0.770463, 1.8011094, 1.5442474, 2.0792208, 0.6665387, 0.0, 0.0, 2.935161, 1.7559421, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [77.5, 376.9491, 105.5, 363.39035, 461.7738, -0.11200315, 0.04072729, 251.51945, 37.0, 0.11606061, 0.013977726, 0.000603651, 0.09487188, -0.027386164, -0.15067822], "split_indices": [3, 1, 3, 2, 2, 0, 0, 1, 3, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [329.5271, 326.78262, 2.744466, 323.9291, 2.8535051, 1.6715484, 1.0729177, 316.91367, 7.0154634, 1.8327019, 1.0208031, 313.58517, 3.3284733, 5.553439, 1.4620243], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "15", "size_leaf_vector": "0"}}, {"base_weights": [0.006029454, 0.0031626765, 0.8757022, 0.01242008, -0.46206006, 0.0022026114, 0.5504924, -1.4090158, -0.19639902, 0.015556241, -0.41703916, 1.0106606, -0.09203077, -1.3031716, 0.12012329], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [0.81190455, 1.4087328, 0.0, 1.7634017, 1.5864899, 1.76397, 1.7775451, 0.0, 1.7660592, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [643.8622, 418.61975, 0.08757022, 260.07104, 443.0387, 46.5, 39.5, -0.14090158, 10.5, 0.0015556242, -0.041703917, 0.10106607, -0.009203077, -0.13031717, 0.012012329], "split_indices": [2, 2, 0, 1, 2, 4, 3, 0, 3, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [329.17923, 328.11917, 1.0600727, 321.748, 6.371191, 315.78024, 5.9677324, 1.37813, 4.9930606, 306.065, 9.715258, 3.4830463, 2.4846864, 1.1104857, 3.8825753], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "15", "size_leaf_vector": "0"}}, {"base_weights": [0.005463936, -0.010298935, 0.15687895, -0.0021831812, -1.8006715, 1.139616, 0.061542653, -0.010452169, 0.7388126, 1.2717075, 1.035738, 0.3111633, -0.07250791, 0.013190255, -0.34250665, 1.0121766, 0.11162839, 0.01619853, 1.0384088, -0.7163114, 0.107119985], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7866143, 4.310333, 2.8989165, 1.8099066, 0.0, 0.0013277531, 0.95121145, 2.3059993, 0.5465025, 0.0, 0.0, 2.1199656, 2.1377397, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [158.28839, 153.42719, 31.5, 106.06833, -0.18006715, 3.5, 46.5, 110.02521, 42.5, 0.12717076, 0.1035738, 148.7595, 52.5, 0.0013190255, -0.034250665, 0.101217665, 0.011162839, 0.0016198531, 0.10384088, -0.07163114, 0.010711999], "split_indices": [1, 1, 4, 0, 0, 3, 4, 1, 4, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [328.94662, 297.87952, 31.067099, 296.55087, 1.3286666, 2.7283669, 28.338732, 293.30118, 3.249679, 1.1092062, 1.6191605, 9.922693, 18.416039, 273.78693, 19.514261, 2.2478504, 1.0018288, 7.076497, 2.846196, 4.0230036, 14.393035], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "21", "size_leaf_vector": "0"}}, {"base_weights": [0.0051348074, -0.051469937, 0.04865501, -0.004326851, -0.6249291, 1.5327632, 0.030540997, -0.029172428, 0.5631681, -0.37690482, -1.7490367, 1.7527791, 1.2679782, 1.4877477, 0.020491177, -0.013525379, -1.3089831, 0.8817274, -0.6005425, -0.62749946, 0.7187467, -0.6189888, 0.039858874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, 21, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8096073, 3.8530157, 4.9815803, 1.8551049, 2.9910583, 0.07776022, 2.6745305, 2.5183825, 2.0535727, 2.44469, 0.0, 0.0, 0.0, 0.0, 2.2539952, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [37.558792, 6.5, 43.3882, 21.01149, 24.441591, 17.229733, 38.085052, 2.5, 33.50856, 8.5, -0.17490368, 0.17527792, 0.12679783, 0.14877477, 48.26672, -0.0013525379, -0.13089831, 0.08817274, -0.06005425, -0.062749945, 0.07187467, -0.061898883, 0.0039858874], "split_indices": [1, 4, 2, 0, 0, 0, 1, 3, 1, 4, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [328.63907, 142.74524, 185.89383, 131.92725, 10.817997, 2.224795, 183.66904, 126.419556, 5.507685, 8.880781, 1.9372158, 1.1638029, 1.0609921, 1.2411351, 182.4279, 124.91031, 1.5092423, 4.3326597, 1.1750251, 7.2385893, 1.642192, 5.338429, 177.08946], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.0047983914, -0.05593312, 0.042732302, -0.04450064, -1.235952, 1.3024522, 0.026328469, -0.07597399, 0.4257499, -1.7430372, 0.03691621, -0.048195887, -0.5709268, 1.4859344, -0.1371843, 0.52931523, 0.010703589], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7563736, 1.6884704, 4.1652026, 1.8462454, 0.0, 0.0, 3.7236302, 1.6041772, 4.6827526, 0.0, 2.5562327, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [17.264828, 17.114584, 17.601456, 64.66063, -0.1235952, 0.13024522, 17.633373, 9.5, 87.21457, -0.17430373, 17.883833, -0.0048195887, -0.057092678, 0.14859344, -0.013718431, 0.052931525, 0.0010703589], "split_indices": [0, 0, 0, 1, 0, 0, 0, 4, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [328.35486, 126.139465, 202.2154, 124.94778, 1.1916909, 2.5814483, 199.63394, 117.13754, 7.810235, 1.1721365, 198.4618, 110.94234, 6.1952076, 2.7092402, 5.1009946, 10.002039, 188.45976], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "17", "size_leaf_vector": "0"}}, {"base_weights": [0.004384849, -0.015620182, 0.104115434, -0.0021861857, -0.44324097, 0.9043791, 0.05466122, -0.015775843, 0.51183975, -0.042491216, -0.72375983, 1.0065331, 0.6675037, -0.45981407, 0.121595174, -0.002546129, -0.8404407, -0.9097696, 0.7617825, 0.6604736, -1.6461008, -2.5716875, -0.22199236, -0.09306679, -1.412223, 0.680065, 0.01357082], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.65488565, 1.5635495, 2.160737, 1.8439597, 0.9240929, 0.053489685, 1.7768714, 2.8050642, 2.421064, 3.8977005, 4.474391, 0.0, 0.0, 2.0565653, 2.7534022, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [4.5, 47.531776, 11.5, 37.918922, 112.22339, 67.83649, 15.5, 35.993294, 51.64514, 19.5, 123.916824, 0.10065331, 0.06675037, 97.449905, 105.80037, -0.0002546129, -0.08404407, -0.09097696, 0.07617825, 0.06604736, -0.16461007, -0.25716874, -0.022199236, -0.0093066795, -0.1412223, 0.0680065, 0.001357082], "split_indices": [3, 0, 4, 0, 2, 2, 4, 0, 1, 4, 2, 0, 0, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [327.78308, 272.94168, 54.841404, 264.6606, 8.281056, 3.1699805, 51.671425, 257.87222, 6.788397, 3.429224, 4.851832, 2.162626, 1.0073545, 5.924646, 45.74678, 253.82242, 4.0497975, 1.0051212, 5.7832756, 2.3838675, 1.0453564, 1.0230231, 3.828809, 4.293002, 1.6316438, 7.390981, 38.355797], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "27", "size_leaf_vector": "0"}}, {"base_weights": [0.004211697, -0.52569103, 0.007997966, 0.4749989, -1.3853757, 1.2656975, 0.0038887046, -0.96249014, 0.007443728, 0.87612695, 0.0019558514], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 0, 0, 0, 1, 0, 1, 0, 0], "id": 72, "left_children": [1, 3, 5, -1, -1, -1, 7, -1, 9, -1, -1], "loss_changes": [0.6516031, 1.9974682, 1.6671077, 0.0, 0.0, 0.0, 1.1036048, 0.0, 1.5301933, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 6, 6, 8, 8], "right_children": [2, 4, 6, -1, -1, -1, 8, -1, 10, -1, -1], "split_conditions": [14.005108, 13.200996, 14.77444, 0.04749989, -0.13853757, 0.12656975, 15.2718315, -0.096249014, 16.16793, 0.087612696, 0.00019558515], "split_indices": [2, 2, 2, 0, 0, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [327.3189, 2.2936556, 325.02524, 1.0565977, 1.2370579, 1.0405681, 323.98468, 1.1672794, 322.8174, 2.005186, 320.81223], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "11", "size_leaf_vector": "0"}}, {"base_weights": [0.0038290808, -0.24059261, 0.012310623, 0.0772747, -3.1097777, 0.5429739, -0.0018097943, -0.308163, 0.36694622, 0.18850425, 1.029098, -1.1392269, 0.0026023772, -0.025150985, -1.0755064, 0.9140281, 0.02101108, 0.5960172, -0.8898546, 0.5288465, 1.25176, 0.80828154, -0.0022031604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.67599356, 10.003976, 2.3751168, 1.1013987, 0.0, 1.4015107, 1.5572951, 0.9044543, 1.0583501, 2.1007702, 0.3598771, 0.0, 1.196125, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.107029, 17.607897, 22.836498, 15.146784, -0.3109778, 20.404066, 23.394932, 14.6212, 16.16793, 17.817776, 8.695886, -0.11392269, 24.252373, -0.0025150985, -0.10755064, 0.091402814, 0.0021011082, 0.05960172, -0.088985465, 0.05288465, 0.125176, 0.08082815, -0.00022031604], "split_indices": [2, 2, 2, 2, 0, 2, 2, 2, 2, 1, 1, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [327.1598, 10.923143, 316.23663, 9.839323, 1.08382, 8.205284, 308.03137, 4.2067075, 5.632616, 4.7651234, 3.4401605, 1.1890911, 306.84225, 3.0903008, 1.1164064, 2.164392, 3.468224, 3.468224, 1.2968991, 1.08382, 2.3563406, 1.8217584, 305.0205], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.0035781113, 0.0010048333, 0.78430074, 0.00641879, -0.7463862, -0.00044669383, 0.7700962, -1.3405311, -0.10389679, 0.0055439067, -1.04275, 0.17458273, 1.1523271], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6490938, 1.3115605, 0.0, 1.7045006, 0.8739618, 2.013164, 0.6440338, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [643.8622, 77.5, 0.07843008, 47.5, 104.5, 494.88867, 80.5, -0.13405311, -0.010389679, 0.00055439066, -0.104275, 0.017458273, 0.115232706], "split_indices": [2, 3, 0, 3, 3, 2, 4, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [326.99316, 325.94168, 1.0514891, 323.62073, 2.3209524, 320.73422, 2.8865113, 1.1921123, 1.1288401, 318.9017, 1.8325232, 1.1443502, 1.742161], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "13", "size_leaf_vector": "0"}}, {"base_weights": [0.0032425046, -0.03555825, 0.054310244, -0.012274409, -1.1659981, 1.0509995, 0.0341321, -0.0026563096, -1.2233541, -0.013374607, -1.8553668, 1.3689785, 0.5051565, -0.46463135, 0.05564676, -0.031779345, 0.378144, -3.3884215, -0.1241142, -0.048671108, -1.7447137, 0.7727604, 0.0060038106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, -1, -1, 19, 21, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6476169, 4.872209, 2.8281531, 2.1057665, 2.9497118, 0.4588318, 1.4813099, 1.9975241, 0.0, 0.0, 6.1510057, 0.0, 0.0, 3.0189366, 4.716444, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, -1, -1, 20, 22, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [58.143356, 56.463085, 59.82238, 26.0, 50.64966, 12.5, 63.12879, 49.098305, -0.12233541, -0.0013374607, 57.604233, 0.13689785, 0.05051565, 35.816227, 69.741745, -0.0031779346, 0.0378144, -0.33884215, -0.01241142, -0.004867111, -0.17447136, 0.07727604, 0.00060038106], "split_indices": [2, 2, 2, 4, 1, 4, 2, 1, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [326.72272, 185.55052, 141.1722, 181.8244, 3.7261102, 2.7813704, 138.39082, 180.40991, 1.4144925, 1.4037471, 2.3223631, 1.7364229, 1.0449475, 5.695031, 132.6958, 167.6256, 12.784327, 1.2235446, 1.0988184, 4.312036, 1.3829954, 8.568793, 124.127], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.0031110216, -0.008259398, 0.17436548, -0.0021324756, -0.9033071, 1.0089899, 0.05276876, 0.0049591498, -0.77539784, 0.04111844, -1.8544283, -1.0849918, 0.18772528, -0.0070150318, 0.95843375, -2.6073031, 0.6958247, 0.69734764, -0.13771562], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, -1, -1, -1, -1, -1], "loss_changes": [0.63745713, 1.6697147, 2.0575473, 1.675179, 1.8784882, 0.0, 2.7281694, 3.4496005, 7.5194335, 0.0, 0.0, 0.0, 2.6502767, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 12, 12], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, -1, -1, -1, -1, -1], "split_conditions": [124.05173, 20.5, 184.9061, 230.93082, 149.43086, 0.100898996, 199.74252, 98.75909, 252.56874, 0.004111844, -0.18544284, -0.108499184, 350.7863, -0.0007015032, 0.095843375, -0.26073033, 0.06958247, 0.06973477, -0.013771563], "split_indices": [0, 3, 2, 1, 2, 0, 2, 0, 1, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [326.4948, 306.12106, 20.373747, 304.06085, 2.0602093, 2.5710652, 17.802683, 301.29468, 2.7661731, 1.0332685, 1.0269407, 1.8724409, 15.930241, 297.55728, 3.7374253, 1.2316494, 1.5345238, 6.215856, 9.714385], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "19", "size_leaf_vector": "0"}}, {"base_weights": [0.0030007083, -0.011052336, 0.14204575, -0.0036569603, -1.4648869, 1.124632, 0.05342347, 0.008250022, -0.46582887, 0.29493812, -0.072349735, -0.00017912552, 1.0056744, 0.21587412, -0.9783872, 0.0257387, 1.0352118, -0.6299366, 0.08970356], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.63830477, 3.1678321, 2.599004, 1.6254143, 0.0, 0.0, 0.83861727, 2.4239817, 2.60189, 1.8734595, 1.6398227, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [158.28839, 153.42719, 31.5, 190.39638, -0.1464887, 0.1124632, 46.5, 106.06833, 17.999598, 148.7595, 52.5, -1.7912553e-05, 0.10056744, 0.021587413, -0.09783872, 0.00257387, 0.10352118, -0.06299366, 0.008970357], "split_indices": [1, 1, 4, 2, 0, 0, 4, 0, 1, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [325.9763, 295.99762, 29.978685, 294.51636, 1.4812443, 2.4611635, 27.517523, 287.10776, 7.408602, 9.445658, 18.071865, 284.70175, 2.4060175, 3.1737676, 4.234835, 6.9442487, 2.5014093, 4.0769353, 13.994929], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "19", "size_leaf_vector": "0"}}, {"base_weights": [0.0028506252, -0.030525606, 0.05957334, -0.015894582, -0.6559813, 1.2167064, 0.039899748, 0.009149475, -0.19914584, 0.53155607, -1.1883631, 1.1888188, 0.027653802, -0.016445426, 1.3998379, -2.8954968, -0.07244324, -2.1647127, -0.13310452, -0.3566398, 0.07392909], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6164165, 1.867091, 2.7339494, 0.92034364, 2.9621222, 0.0, 1.6571385, 6.279455, 8.204919, 0.0, 3.3018212, 0.0, 2.0858495, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 12, 12], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.061596, 17.999916, 18.321356, 70.4629, 5.5, 0.12167064, 27.996908, 65.94615, 72.28531, 0.05315561, 11.5, 0.11888188, 42.684517, -0.0016445427, 0.13998379, -0.28954968, -0.007244324, -0.21647127, -0.013310452, -0.03566398, 0.007392909], "split_indices": [0, 0, 0, 1, 4, 0, 2, 1, 1, 0, 4, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [325.42358, 204.76569, 120.65791, 200.11081, 4.654866, 1.9984629, 118.659454, 176.01115, 24.099659, 1.4350524, 3.2198136, 1.2326686, 117.42678, 172.83295, 3.17821, 1.0682764, 23.031382, 1.6620536, 1.55776, 12.58998, 104.8368], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "21", "size_leaf_vector": "0"}}, {"base_weights": [0.0027546573, -0.55788434, 0.0059071686, 0.77876204, 0.0022974922, -0.676416, 0.006088791, 1.304976, 0.0007530754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 0, 1, 0, 1, 0, 1, 0, 0], "id": 79, "left_children": [1, -1, 3, -1, 5, -1, 7, -1, -1], "loss_changes": [0.56894594, 0.0, 0.8938824, 0.0, 0.82124734, 0.0, 2.20455, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 6, 6], "right_children": [2, -1, 4, -1, 6, -1, 8, -1, -1], "split_conditions": [13.6150055, -0.055788435, 14.77444, 0.0778762, 15.558803, -0.0676416, 16.16793, 0.1304976, 7.5307544e-05], "split_indices": [2, 0, 2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [325.00186, 1.7897125, 323.21216, 1.4797066, 321.73245, 1.7627095, 319.96976, 1.2913983, 318.67834], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "9", "size_leaf_vector": "0"}}, {"base_weights": [0.002511029, 0.006281082, -0.4513658, 0.00019812632, 0.7313944, -0.9406652, 0.3357437, 0.009053083, -0.3161078, 0.13110787, 1.1395053, 0.0031495953, 0.9113419, -0.07882585, -1.3945438], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [0.55156493, 1.4141937, 1.0317204, 0.8921735, 0.63926995, 0.0, 0.0, 1.6471822, 2.2036803, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [77.5, 47.5, 105.5, 332.67218, 80.5, -0.09406652, 0.03357437, 251.51945, 37.5, 0.013110787, 0.11395053, 0.00031495953, 0.09113419, -0.007882585, -0.13945438], "split_indices": [3, 3, 3, 2, 4, 0, 0, 1, 3, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [324.91226, 322.26727, 2.6449661, 319.60986, 2.6574047, 1.6376746, 1.0072916, 310.9463, 8.663586, 1.0907484, 1.5666564, 308.94604, 2.0002518, 7.117555, 1.5460305], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "15", "size_leaf_vector": "0"}}, {"base_weights": [0.0023414404, -0.051147286, 0.03361535, -0.08528262, 0.19026326, 1.1308353, 0.024149712, -0.004686535, -0.39588964, 1.3484044, -0.03760344, -0.21239708, 0.061642107, -0.0271017, 1.4837734, -0.5347461, 0.5730359, 0.9194429, 1.7541916, -1.0835319, 0.10408441, 0.78121734, -0.290879, 1.0469495, 0.015966183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5430941, 0.98557764, 2.1178908, 2.622539, 3.9184318, 0.0, 1.8018423, 2.7664232, 2.9074686, 0.38187647, 1.8435615, 2.1639318, 7.8898234, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [14.528929, 49.05276, 22.836498, 53.725517, 51.527863, 0.113083534, 28.075882, 48.46602, 199.59766, 7.5, 52.67855, 17.949062, 4.5, -0.0027101699, 0.14837734, -0.053474613, 0.05730359, 0.09194429, 0.17541917, -0.10835319, 0.010408442, 0.07812174, -0.029087901, 0.104694955, 0.0015966183], "split_indices": [0, 1, 2, 2, 1, 0, 2, 2, 2, 4, 1, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [324.74597, 119.69752, 205.04845, 104.91104, 14.786478, 1.7346395, 203.31381, 83.33078, 21.580261, 2.430102, 12.356376, 27.773298, 175.54051, 82.11079, 1.2199948, 18.8882, 2.692061, 1.2091743, 1.2209277, 1.4746597, 10.881716, 2.0172389, 25.75606, 7.757606, 167.78291], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "25", "size_leaf_vector": "0"}}, {"base_weights": [0.0022735412, 0.004945642, -0.6309841, -0.00082526146, 0.5399701, 0.009961753, -0.47078297, 1.1104466, -0.020699067, 0.0017007601, 1.0159746, -1.3059502, -0.20737287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [0.54212946, 1.001844, 0.0, 1.6232061, 1.1185668, 2.5835738, 1.5627898, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [101.0, 376.9491, -0.063098416, 363.39035, 415.30353, 34.5, 381.08975, 0.11104466, -0.0020699068, 0.00017007602, 0.101597466, -0.13059501, -0.020737289], "split_indices": [3, 1, 0, 2, 0, 3, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [324.05447, 322.71844, 1.3360329, 319.26605, 3.452387, 312.09106, 7.1749673, 1.7152073, 1.7371796, 309.56897, 2.5221047, 1.7017767, 5.4731903], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "13", "size_leaf_vector": "0"}}, {"base_weights": [0.0021307112, -0.00022700254, 0.7089759, 0.004630092, -0.6219965, -0.0006697757, 1.1125506, -0.23709667, -1.1407034, 0.008134761, -0.357417], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.546723, 0.9808286, 0.0, 1.8913871, 0.48614246, 1.0045365, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [643.8622, 372.87982, 0.070897594, 301.73196, 155.5, 332.67218, 0.111255065, -0.023709668, -0.11407035, 0.0008134761, -0.035741698], "split_indices": [2, 0, 0, 0, 4, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [323.7952, 322.7146, 1.0805795, 320.2071, 2.5075283, 318.6836, 1.5234922, 1.4588537, 1.0486745, 310.99008, 7.693508], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "11", "size_leaf_vector": "0"}}, {"base_weights": [0.0019388451, -0.010557166, 0.13254513, -0.0049604536, -1.6128967, 1.1177466, 0.05625347, -0.01286989, 0.7414463, 0.3040061, -0.06745817, -0.007082055, -1.3659594, 1.0028557, 0.19316734, 0.053269476, 1.033403, -0.8524038, 0.056390274], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5293884, 2.6318846, 2.116929, 1.7301491, 0.0, 0.0, 0.80906326, 2.2668447, 0.4264878, 1.5965183, 1.7127134, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [159.72784, 157.34113, 31.5, 106.06833, -0.16128968, 0.11177466, 46.5, 98.52843, 165.65761, 148.7595, 208.22546, -0.0007082055, -0.13659595, 0.10028557, 0.019316735, 0.0053269477, 0.103340305, -0.08524039, 0.0056390273], "split_indices": [1, 1, 4, 0, 0, 0, 4, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [323.6027, 295.2835, 28.3192, 294.2719, 1.0115724, 2.0161262, 26.303074, 291.2093, 3.0626276, 8.781984, 17.52109, 289.98633, 1.2229724, 2.055888, 1.0067397, 6.5535755, 2.228409, 2.390722, 15.130367], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "19", "size_leaf_vector": "0"}}, {"base_weights": [0.0019164676, -0.21293408, 0.009456374, 0.07227174, -2.53296, 0.3151949, -0.010455317, -0.06186325, 0.7982224, 0.23084953, 1.4261252, -0.13320221, 0.027242005, 0.08097247, -0.8206348, 0.14317806, 1.1658818, -0.03057057, -0.5692719, 0.72867876, 0.0011618715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5219519, 7.2530475, 1.9057158, 0.9554939, 0.0, 1.7736604, 1.35874, 0.8980442, 0.0, 1.4452556, 0.0, 3.08193, 4.0979953, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.107029, 17.607897, 27.817184, 16.714153, -0.253296, 27.167913, 31.43778, 16.294125, 0.07982224, 18.007957, 0.14261252, 3.5, 35.164383, 0.008097247, -0.08206348, 0.014317806, 0.116588175, -0.003057057, -0.056927193, 0.07286788, 0.00011618715], "split_indices": [2, 2, 2, 2, 0, 2, 2, 2, 0, 0, 0, 4, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [323.43552, 10.911528, 312.524, 9.721786, 1.1897421, 19.130735, 293.39325, 8.201715, 1.5200704, 17.800354, 1.3303806, 68.99087, 224.4024, 6.8987975, 1.3029175, 16.294737, 1.5056181, 55.874172, 13.116691, 8.021008, 216.3814], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "21", "size_leaf_vector": "0"}}, {"base_weights": [0.0018352115, -0.034798004, 0.04409238, -0.02181569, -1.8795384, 1.2255844, 0.03238431, -0.031385258, 0.44842654, -0.3405522, 0.07479111, -0.019974666, -1.5891957, 1.2534511, -0.30291975, -1.3008975, 0.011229913, 0.5003339, -0.009147659], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.50033104, 4.1229343, 2.0644922, 0.7689494, 0.0, 0.0, 2.3497245, 2.9765801, 2.0732574, 5.1355476, 4.776171, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [17.991005, 17.98904, 17.998869, 202.49092, -0.18795384, 0.12255844, 28.075882, 182.9122, 266.0, 17.999126, 8.5, -0.0019974667, -0.15891957, 0.12534511, -0.030291975, -0.13008974, 0.0011229913, 0.05003339, -0.00091476593], "split_indices": [0, 0, 1, 2, 0, 0, 2, 2, 2, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [323.12796, 172.95578, 150.1722, 171.76254, 1.1932355, 1.4549844, 148.71721, 168.36682, 3.3957171, 15.152862, 133.56435, 167.15897, 1.2078524, 1.6424994, 1.7532176, 4.0628786, 11.089983, 22.016624, 111.54773], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "19", "size_leaf_vector": "0"}}, {"base_weights": [0.001773021, -0.010368865, 0.12588292, -0.003723154, -1.1745564, 1.1004817, 0.04859454, 0.007371645, -0.44032332, 0.6743361, 0.00026306947, -0.0023240151, 0.7322625, 0.1673077, -0.9098754, -0.8787843, 0.06864746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [0.48727587, 2.2610385, 2.1588895, 1.4194, 0.0, 0.0, 0.80143404, 2.00947, 2.0808702, 0.0, 1.4852139, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [158.28839, 153.42719, 31.5, 190.39638, -0.11745564, 0.110048175, 180.07077, 139.96552, 17.999598, 0.06743361, 179.09384, -0.00023240152, 0.07322625, 0.01673077, -0.09098754, -0.08787843, 0.006864746], "split_indices": [1, 1, 4, 2, 0, 0, 2, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [322.5718, 293.76123, 28.810577, 292.11234, 1.6488883, 2.09765, 26.712929, 284.86044, 7.2518916, 1.8912122, 24.821716, 281.09686, 3.7635927, 3.1546755, 4.097216, 1.7724905, 23.049225], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "17", "size_leaf_vector": "0"}}, {"base_weights": [0.0016734973, -0.49998257, 0.0045551523, 0.72359616, 0.0012796427, -0.60199475, 0.0047403774, 1.263435, -5.458013e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 0, 1, 0, 1, 0, 1, 0, 0], "id": 88, "left_children": [1, -1, 3, -1, 5, -1, 7, -1, -1], "loss_changes": [0.4608995, 0.0, 0.74742687, 0.0, 0.66005695, 0.0, 1.9271054, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 6, 6], "right_children": [2, -1, 4, -1, 6, -1, 8, -1, -1], "split_conditions": [13.6150055, -0.049998257, 14.77444, 0.072359614, 15.558803, -0.060199477, 16.16793, 0.1263435, -5.458013e-06], "split_indices": [2, 0, 2, 0, 2, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [322.23068, 1.81074, 320.41992, 1.4292008, 318.99072, 1.7931335, 317.1976, 1.2017215, 315.99588], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "9", "size_leaf_vector": "0"}}, {"base_weights": [0.0015288274, -0.0005960904, 0.64174044, 0.003083671, -0.7697448, -0.008743408, 0.21666352, -0.0043049925, -1.1567249, 1.005603, 0.1025126], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.44467428, 0.91644907, 0.0, 0.809295, 0.0, 1.5309199, 1.5019118, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [643.8622, 91.0, 0.06417405, 124.05173, -0.07697448, 397.97205, 184.9061, -0.00043049926, -0.11567249, 0.1005603, 0.01025126], "split_indices": [2, 3, 0, 0, 0, 2, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [322.14523, 321.0739, 1.0713013, 319.54214, 1.5317949, 302.7405, 16.80162, 301.59323, 1.1472948, 2.1027863, 14.698833], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "11", "size_leaf_vector": "0"}}, {"base_weights": [0.0013931141, 0.11730478, -0.010387769, 0.060503736, 1.3745576, -0.11402475, 0.021756792, 0.0042920616, 1.1481048, 0.12346738, -0.28222328, 0.6689144, -0.0017141752, 0.09322394, -1.1221153, 0.04829305, 1.490241, -1.1661645, -0.18529424, 1.0661405, 0.09811311, -0.20298125, 0.030417258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, 19, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.44056016, 2.1108994, 0.9739502, 1.7309642, 0.0, 2.766982, 3.3918474, 2.7075365, 0.0, 2.9307346, 3.4585598, 1.7592201, 1.3925709, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, 20, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [27.817184, 27.167913, 31.43778, 18.007957, 0.13745576, 18.585432, 35.164383, 17.99601, 0.11481048, 18.349949, 19.640026, 29.26352, 35.098152, 0.009322394, -0.112211525, 0.004829305, 0.14902411, -0.11661645, -0.018529424, 0.10661405, 0.009811311, -0.020298125, 0.0030417258], "split_indices": [2, 2, 2, 0, 0, 1, 2, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [321.82477, 29.761524, 292.06323, 28.492697, 1.2688278, 69.20989, 222.85335, 27.110737, 1.3819598, 28.670738, 40.539158, 7.8048115, 215.04854, 25.14365, 1.9670861, 27.19277, 1.4779679, 3.9849608, 36.554195, 4.5866914, 3.21812, 29.640762, 185.40778], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.0014158147, -0.20348829, 0.008595501, 0.054634094, -2.1952758, 0.47384533, -0.0032336088, -0.06863665, 0.7374352, 0.15729259, 0.9641168, -0.8866718, 0.0005227568, 0.06042251, -0.72576517, 0.5385012, -0.7957971, 0.47522765, 1.2203543, 0.7630463, -0.0038972814], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, 19, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.47090715, 5.592585, 1.7140187, 0.814908, 0.0, 1.1856867, 1.0139526, 0.69620234, 0.0, 1.7153814, 0.3549776, 0.0, 1.0240904, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, 20, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [18.107029, 17.607897, 22.836498, 16.714153, -0.21952759, 20.404066, 23.394932, 16.294125, 0.07374352, 17.817776, 8.695886, -0.08866718, 24.252373, 0.006042251, -0.072576515, 0.05385012, -0.07957971, 0.047522765, 0.12203544, 0.07630464, -0.00038972814], "split_indices": [2, 2, 2, 2, 0, 2, 2, 2, 0, 1, 1, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [321.39807, 10.823903, 310.57416, 9.586288, 1.237614, 7.711922, 302.86224, 8.115493, 1.4707959, 4.706615, 3.0053072, 1.2836329, 301.5786, 6.778138, 1.3373545, 3.3725383, 1.3340768, 1.0567157, 1.9485915, 1.7412006, 299.8374], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "21", "size_leaf_vector": "0"}}, {"base_weights": [0.0013429541, -0.42941162, 0.004511464, 0.51283556, -1.2546732, 0.14919981, -0.008856145, 0.09417613, 1.3268045, -0.10175298, 0.020064147, 0.040971126, 1.1320347, -0.0106426, -0.48756698, 0.62038684, -0.0011456005], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, 5, -1, -1, 7, 9, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [0.43417487, 1.8198622, 0.6182096, 0.0, 0.0, 1.7362852, 0.7849858, 1.4150454, 0.0, 2.4336712, 2.8393106, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, -1, -1, 8, 10, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [14.005108, 13.200996, 27.817184, 0.051283557, -0.12546733, 27.167913, 31.43778, 18.007957, 0.13268046, 3.5, 35.164383, 0.004097113, 0.113203466, -0.00106426, -0.0487567, 0.062038686, -0.00011456005], "split_indices": [2, 2, 2, 0, 0, 2, 2, 0, 0, 4, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [321.20074, 2.312542, 318.8882, 1.0758511, 1.2366908, 27.024696, 291.86353, 25.836763, 1.1879337, 69.36668, 222.49684, 24.595903, 1.2408597, 56.14433, 13.222348, 7.599137, 214.8977], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "17", "size_leaf_vector": "0"}}, {"base_weights": [0.0012720609, -0.00093675964, 0.58332586, 0.0028876234, -1.1120994, -0.0031377424, 0.6035308, 0.006260318, -0.3633748, 0.2317792, 1.0918422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [0.4184138, 1.3696684, 0.0, 1.1590616, 0.0, 1.071484, 0.56164944, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [192.0, 155.5, 0.05833259, 47.5, -0.111209944, 332.67218, 380.49805, 0.00062603183, -0.03633748, 0.02317792, 0.10918422], "split_indices": [4, 4, 0, 3, 0, 2, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [320.90448, 319.68414, 1.2203575, 318.5886, 1.095539, 315.4178, 3.1708217, 307.38074, 8.037049, 1.8202728, 1.3505489], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "11", "size_leaf_vector": "0"}}, {"base_weights": [0.0011779825, -0.032253552, 0.03997813, -0.020312812, -1.6424577, 1.199114, 0.029307876, -0.00034087934, -0.24688756, -1.2287408, 0.03912116, 0.008489295, -1.0196291, -1.3283329, -0.02829924, 1.1606191, 0.026912104], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4160995, 3.2927947, 1.8250405, 0.7708272, 0.0, 0.0, 1.8063904, 1.4241512, 3.248886, 0.0, 1.9885678, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [17.991005, 17.98904, 17.998869, 15.5, -0.16424577, 0.119911395, 17.999847, 83.89892, 62.615395, -0.12287408, 18.633335, 0.0008489295, -0.10196292, -0.13283329, -0.002829924, 0.11606192, 0.0026912105], "split_indices": [0, 0, 1, 4, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [320.70065, 172.13083, 148.56984, 170.87997, 1.2508525, 1.3364555, 147.23338, 157.08731, 13.792658, 1.1220886, 146.1113, 155.73825, 1.3490666, 2.3028762, 11.489782, 1.5545433, 144.55675], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "17", "size_leaf_vector": "0"}}, {"base_weights": [0.0011076604, -0.030165285, 0.04302537, -0.008686438, -1.032149, 1.0202237, 0.024082277, -0.024719415, 0.63396907, -0.5383057, -2.3194094, 0.51065457, 1.3743231, -0.63128656, 0.038303655, -0.007715107, -2.0018158, 1.1120396, -0.031063588, -0.037262242, -1.3598197, -1.3472486, 0.51960784, 0.46391398, -0.0006345001], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.420013, 3.9345636, 2.5252757, 1.843381, 2.3982234, 0.442353, 1.2470614, 5.8677187, 1.3954018, 1.1346905, 0.0, 0.0, 0.0, 2.3557534, 2.1852627, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [58.143356, 56.463085, 59.82238, 33.592484, 54.699596, 58.879646, 61.00618, 31.832355, 37.87614, 17.991066, -0.23194094, 0.051065456, 0.13743232, 53.037964, 69.741745, -0.0007715107, -0.20018159, 0.11120396, -0.003106359, -0.0037262242, -0.13598196, -0.13472486, 0.051960785, 0.046391398, -6.345001e-05], "split_indices": [2, 2, 2, 0, 1, 2, 2, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [320.28482, 183.29832, 136.98651, 179.47119, 3.8271356, 2.5849097, 134.4016, 175.12729, 4.3439016, 2.781857, 1.0452787, 1.0813899, 1.5035198, 2.8303957, 131.5712, 173.6489, 1.4783958, 2.5320258, 1.8118759, 1.7420205, 1.0398365, 1.7492095, 1.081186, 11.040592, 120.53061], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "25", "size_leaf_vector": "0"}}, {"base_weights": [0.0011844534, -0.00079817924, 0.60670394, 0.0038066148, -0.61267537, -0.0009178965, 1.1138624, -0.9184078, -0.21779762, 0.0047111963, -0.48125726], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [0.3903515, 0.90505475, 0.0, 1.6718522, 0.27631098, 0.8573705, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [643.8622, 372.87982, 0.060670394, 364.296, 469.36627, 406.05298, 0.11138624, -0.09184078, -0.021779763, 0.00047111962, -0.048125725], "split_indices": [2, 0, 0, 1, 1, 2, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [320.09024, 319.0391, 1.0511472, 316.6497, 2.3894107, 315.3087, 1.3410031, 1.3249733, 1.0644373, 311.6456, 3.6630888], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "11", "size_leaf_vector": "0"}}, {"base_weights": [0.0010883844, -0.00982063, 0.11494915, -0.0036143207, -1.037858, 1.0902016, 0.043897305, 0.0058735954, -0.3845221, 0.34760183, -0.05177619, -0.00064945995, 0.999818, 0.18814911, -0.8384816, -0.09275303, 0.9249515, -2.4358792, 0.102300085], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.39848527, 1.8520403, 1.9322698, 1.0525262, 0.0, 0.0, 0.763565, 1.8455704, 1.8481741, 1.6097848, 7.330248, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [158.28839, 153.42719, 31.5, 190.39638, -0.103785805, 0.109020166, 194.3283, 106.06833, 17.999598, 179.09384, 209.34552, -6.4946e-05, 0.09998181, 0.018814912, -0.08384816, -0.009275303, 0.09249515, -0.24358793, 0.0102300085], "split_indices": [1, 1, 4, 2, 0, 0, 1, 0, 1, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [319.94806, 291.90216, 28.045874, 290.17017, 1.7320083, 1.8852257, 26.160648, 283.10205, 7.06811, 6.285677, 19.874971, 281.25616, 1.8458945, 3.1182525, 3.9498575, 3.5608366, 2.7248406, 1.2014295, 18.673542], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "19", "size_leaf_vector": "0"}}, {"base_weights": [0.0011328981, -0.01620513, 0.07373728, -0.0024788175, -0.31791168, 0.75841486, 0.014414351, -0.010483317, 1.3920875, -1.7971573, -0.13110843, 0.07147586, 0.9348325, -0.9568035, 0.046617515, -0.0031148407, -0.8363339, -1.4702477, 0.11037593, 1.0056572, 0.71312344, 0.6643932, -0.02573872], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, -1, 19, -1, 21, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.40292683, 1.0648435, 2.4992516, 2.7404306, 3.0707574, 0.5840614, 1.7686813, 1.4844775, 0.0, 0.0, 3.2338512, 0.0, 0.036765814, 0.0, 2.4660687, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 12, 12, 14, 14], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, -1, 20, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 35.993294, 11.5, 35.98569, 35.99845, 25.500933, 30.403542, 44.0, 0.13920875, -0.17971574, 62.028816, 0.0071475864, 72.61451, -0.09568035, 49.76841, -0.00031148407, -0.083633386, -0.14702478, 0.011037594, 0.100565724, 0.071312346, 0.06643932, -0.002573872], "split_indices": [3, 0, 4, 0, 0, 0, 0, 4, 0, 0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [319.60727, 257.89227, 61.714996, 246.71123, 11.181041, 4.8980117, 56.816986, 245.32024, 1.390988, 1.2379897, 9.943051, 1.0153099, 3.8827019, 1.8042681, 55.01272, 243.17273, 2.1475105, 1.5176313, 8.42542, 2.8816872, 1.0010147, 5.7736683, 49.239048], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}, {"base_weights": [0.0011904748, -0.03879158, 0.032693963, 0.0047105523, -0.5626936, 1.4640347, 0.01572317, -0.01704212, 0.5304827, -0.34719923, -1.4611113, 1.4151577, 0.006460939, -0.0025244583, -1.1831415, 0.86693627, -0.6086003, -0.56888986, 0.6608516, -3.0799916, 0.26553357, -0.5366483, 0.023512606], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4020732, 3.2090473, 4.325864, 1.4894445, 2.0666282, 0.0, 2.2751093, 2.0976355, 1.9909343, 1.9533361, 5.83952, 0.0, 1.620125, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [37.558792, 6.5, 43.3882, 21.01149, 24.441591, 0.14640348, 38.085052, 2.5, 33.50856, 8.5, 27.766548, 0.14151578, 48.26672, -0.00025244584, -0.11831415, 0.08669363, -0.060860034, -0.056888986, 0.06608516, -0.30799916, 0.026553357, -0.05366483, 0.0023512605], "split_indices": [1, 4, 2, 0, 0, 0, 1, 3, 1, 4, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [319.242, 140.54819, 178.6938, 129.76407, 10.784119, 2.0769734, 176.61684, 124.599686, 5.1643834, 8.718013, 2.066106, 1.1441832, 175.47264, 123.08589, 1.5137932, 3.9954164, 1.168967, 7.158083, 1.5599296, 1.0652732, 1.000833, 5.314375, 170.15826], "tree_param": {"num_deleted": "0", "num_feature": "5", "num_nodes": "23", "size_leaf_vector": "0"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "5", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [1, 7, 3]}