"""
路侧停车场的情报数据整合模块
"""

from dataclasses import dataclass

import requests
from shapely import LineString, Point, wkt

from src.parking.recognition import dbutils
from src.tools import pgsql

TURING_RIGHT = 3
TURING_LEFT = 4
TURING_BOTH = 5
TURING_UNKNOWN = -1
TURING_TODO = -2

DIR_BOTH = 1
DIR_NORMAL = 2
DIR_REVERSE = 3


STATUS_DONE = 0
MESSAGE_AUTO = "auto_release"
MESSAGE_MANUAL = "manual"


@dataclass
class Image:
    """
    图片信息：点位、方位、核实结论
    """

    image_id: str
    conclusion: str
    north: float
    geom: Point


@dataclass
class Link:
    """
    道路信息
    """

    link_id: str
    kind: int
    md: int
    dir: int
    geom: LineString


@dataclass
class Roadside:
    """
    路侧停车场
    """

    work_id: int
    turing_id: str
    turing_result: str
    line: LineString
    links: list[Link]

    @property
    def is_valid(self):
        """
        是否有效（人工结论）
        """
        return self.turing_result == 1


def get_roadside_by_ids(work_ids: list[int]):
    """
    获取路侧停车场信息
    """
    sql = """
        select id, turing_id, turing_result, link_id_list, st_astext(line_geom)
        from parking_line_turing_work
        where id in %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [tuple(work_ids)])
    for work_id, turing_id, turing_result, link_id_list, line_geom in ret:
        short_link_ids = link_id_list.split(",")
        roadside = Roadside(
            work_id=work_id,
            turing_id=turing_id,
            turing_result=turing_result,
            line=wkt.loads(line_geom),
            links=_get_links(short_link_ids),
        )
        yield roadside


def convert_link_id_short2long(short_ids: list[str]) -> dict[str, str]:
    """
    将短 link_id 转换为长 link_id
    """
    api = "http://mapde-poi.baidu-int.com/prod/api/roadLinkTrans"
    resp = requests.get(api, params={"ids": ",".join(short_ids), "trans_type": 2})
    return resp.json()["data"]


def _get_links(short_ids: list[str]) -> list[Link]:
    if not short_ids:
        return []

    long_ids = list(convert_link_id_short2long(short_ids).values())
    if not long_ids:
        return []

    infos = _get_link_infos(long_ids)
    # 说明存在 link_id 失效，不处理这种情况
    if len(short_ids) != len(infos):
        return []

    links = [
        Link(link_id=link_id, kind=kind, md=md, dir=direction, geom=wkt.loads(geom))
        for link_id, kind, md, direction, geom in infos
    ]
    return links


def _get_measure_line(work_id: int):
    sql = """
        select st_astext(line_geom)
        from parking_line_turing_work 
        where id = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [work_id])
    return ret[0]


def _get_link_info(link_id: str):
    sql = """
        select kind, md, dir, st_astext(geom) from nav_link
        where link_id = %s
    """
    ret = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [link_id])
    return ret


def _get_link_infos(link_ids: list[str]):
    sql = """
        select link_id, kind, md, dir, st_astext(geom) from nav_link
        where link_id in %s
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [tuple(link_ids)])
    return ret
