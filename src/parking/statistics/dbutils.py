"""
与数据库有关的通用方法
"""
import re
import time
from datetime import datetime, timedelta
from typing import Union, Sequence, Mapping, Callable, Optional

import psycopg2
import retrying

from src.parking.recognition import dbutils
from src.tools import pgsql

RETRY_ERRORS = [
    psycopg2.errors.AdminShutdown,
    psycopg2.errors.SerializationFailure,
    psycopg2.OperationalError,
    psycopg2.InterfaceError,
]


def wait_db_sync_done(config, view_name: str, today: str, unsynced: Callable[[str, str], None]):
    """
    等待 pg 数据库同步完成：检查 VIEW 所指向的 TABLE 是否存在，TABLE 的命名必须符合：{view_name}_{today}
    :param config: 数据库连接配置
    :param view_name: VIEW 名称
    :param today: 今日日期字符串，e.g. 20240902
    :param unsynced: 未完成同步时的回调函数：(actual_table_name, expected_table_name) -> None
    """
    expected_table_name = f"{view_name}_{today}"
    while True:
        actual_table_name = get_table_by_view(config, view_name)
        if actual_table_name == expected_table_name:
            break

        unsynced(actual_table_name, expected_table_name)
        time.sleep(10)


def exists_table(config, table_name: str) -> bool:
    """
    判断给定的 table_name 是否存在于给定的数据库（config）中
    """
    sql = f"""
        SELECT EXISTS (
            SELECT 1
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            AND table_name = '{table_name}'
        );
    """
    ret = dbutils.fetch_one(config, sql)
    return ret[0]


def get_table_by_view(config, view_name: str) -> Optional[str]:
    """
    获取 VIEW 所指向的 TABLE 名称
    :param config: 数据库连接配置
    :param view_name: VIEW 名称
    :return: TABLE 名称
    """

    def get_table_name(view_definition: str) -> str:
        match = re.search(r"FROM (\w+?);", view_definition)
        return match.group(1) if match else None

    def get_view_definition(view: str) -> str:
        sql = """
            SELECT definition FROM pg_views WHERE viewname = %s;
        """
        ret = dbutils.fetch_one(config, sql, [view])
        return ret[0] if ret else None

    definition = get_view_definition(view_name)
    if not definition:
        return None

    actual_table_name = get_table_name(definition)
    return actual_table_name


def drop_expired_tables(config, view_name: str, expire_day: int):
    """
    删除 x 天前的 TABLE
    :param config: 数据库连接配置
    :param view_name: 表名前缀，也就是 VIEW 的名称
    :param expire_day: 指定要保留的天数，例如 7 表示保留最近一周的数据
    :return: 被删除的表名
    """
    today = datetime.now()
    expire = today - timedelta(days=expire_day)
    tables = get_tables_by_view(config, view_name)
    expired_tables = [table_name for table_name, day in tables if day < expire]
    with pgsql.get_connection(config) as conn:
        for table_name in expired_tables:
            # NOTE: 此处不要把 {table_name} 写成 %s 的形式，pgsql 会把它当成字符串，加上引号，导致 sql 语法错误
            sql_clear = f"""
                drop table if exists {table_name};
            """
            pgsql.execute(conn, sql_clear)

    return expired_tables


def get_tables_by_view(config, view_name: str) -> list[tuple[str, datetime]]:
    """
    根据视图名称获取相关表名，并解析其日期
    """

    def parse_date(date_str: str):
        # noinspection PyBroadException
        try:
            return datetime.strptime(date_str, "%Y%m%d")
        except:
            return None

    sql = f"""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name LIKE '{view_name}_%';
    """
    ret = dbutils.fetch_all(config, sql)
    tables = [(x[0], x[0].split("_")[-1]) for x in ret]
    tables = [(table_name, parse_date(day)) for table_name, day in tables]
    tables = [(table_name, day) for table_name, day in tables if day is not None]
    return tables


def retry_if_special_errors(exception):
    """
    判断是否指定的错误（在 RETRY_ERRORS 列表中添加），若为指定错误，则重试，否则直接抛异常
    """
    return any(isinstance(exception, error) for error in RETRY_ERRORS)


@retrying.retry(
    stop_max_attempt_number=8,
    wait_random_min=1000,
    wait_random_max=5000,
    retry_on_exception=retry_if_special_errors,
)
def fetch_all(config: Union[dict, Callable[[], dict]], sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    """
    pgsql.fetch_all 的可重试版本
    """
    conn = pgsql.get_connection_ttl(config)
    return pgsql.fetch_all(conn, sql, args)


@retrying.retry(
    stop_max_attempt_number=8,
    wait_random_min=1000,
    wait_random_max=5000,
    retry_on_exception=retry_if_special_errors,
)
def fetch_one(config: Union[dict, Callable[[], dict]], sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    """
    pgsql.fetch_one 的可重试版本
    """
    conn = pgsql.get_connection_ttl(config)
    return pgsql.fetch_one(conn, sql, args)
