"""
例行重点垂类停车场集合
"""
from datetime import datetime
from pathlib import Path

from loguru import logger

from src.parking.statistics import dbutils
from src.tools import pgsql, tsv


def fetch_key_category_list(query_table: str):
    """
    拉取重点垂类停车场集合
    :param query_table: 查询表名，必须含有 bid, type 字段
    """
    sql_primary = f"""
        select bid from {query_table}
        where type in (0, 1, 2, 3, 4, 5, 6);
    """
    sql_children = f"""
        select b.relation_bid, b.bid
        from {query_table} a
        inner join poi b on a.bid = b.relation_bid
        where
            -- 医院，需要孙子点，医院的孙子停车场全都是重要子点
               (a.type = 2 and b.std_tag != '交通设施;停车场')
            -- 交通枢纽，需要孙子点
            or (a.type = 3 and (b.std_tag like '交通设施%' and b.std_tag != '交通设施;停车场' or b.std_tag = '休闲娱乐;休闲广场'))
            -- 景点，需要孙子点
            or (a.type = 4 and (b.std_tag like '旅游景点%' or b.std_tag in ('生活服务', '生活服务;售票处', '生活服务;其他')))
    """
    sql_park = """
        select relation_bid, bid, name, click_pv from poi
        where relation_bid in %s and std_tag in ('交通设施;停车场', '交通设施;路侧停车位');
    """
    sql_root_info = """
        select bid, name, click_pv, std_tag from poi
        where bid in %s;
    """

    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        primary_bids = pgsql.fetch_all(conn, sql_primary)
        child_bids = pgsql.fetch_all(conn, sql_children)

        # 合并祖父点和父点，一起查子点停车场
        parent_bids = {x[0] for x in primary_bids} | {x[1] for x in child_bids}
        parks = pgsql.fetch_all(conn, sql_park, [tuple(parent_bids)])

        # 把孙点的父点替换为祖父点（根点）
        relay_map = {relay_bid: root_bid for root_bid, relay_bid in child_bids}
        parks = [(relay_map.get(x[0], x[0]), 2 if x[0] in relay_map else 1, *x[1:]) for x in parks]

        root_bids = {x[0] for x in parks}
        root_infos = pgsql.fetch_all(conn, sql_root_info, [tuple(root_bids)])
        root_info_map = {x[0]: x for x in root_infos}

        items = [(*root_info_map.get(x[0], (x[0], "", 0, "")), *x[1:]) for x in parks]
        return items


def create_key_category_table(view_name: str, table_name: str, additional_path: Path):
    """
    创建 park_key_category_list 表，并导入来自 tsv 的数据
    """
    sql_clear = f"""
        -- 所有操作发生在同一个事务中，删除 VIEW 不会导致空窗期
        DROP VIEW IF EXISTS {view_name};
        DROP TABLE IF EXISTS {table_name};
    """
    sql_create = f"""
        CREATE TABLE {table_name} (
            root_bid VARCHAR(64) NOT NULL,
            root_name VARCHAR(256),
            root_pv INT DEFAULT 0,
            root_std_tag VARCHAR(64),
            depth INT,
            bid VARCHAR(64) NOT NULL,
            name VARCHAR(256) NOT NULL,
            pv INT,
            PRIMARY KEY (bid)
        );
    """
    sql_copy = f"""
        COPY {table_name} FROM STDIN WITH CSV DELIMITER AS '\t';
    """
    sql_index = f"""
        CREATE INDEX idx_{table_name}_root_bid ON {table_name}(root_bid);
    """
    sql_view = f"""
        CREATE OR REPLACE VIEW {view_name} AS
        SELECT * FROM {table_name};
    """

    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
        open(additional_path, "r", encoding="utf-8") as f,
    ):
        cursor.execute(sql_clear)
        cursor.execute(sql_create)
        # noinspection PyTypeChecker
        cursor.copy_expert(sql_copy, f)
        cursor.execute(sql_index)
        cursor.execute(sql_view)
        conn.commit()


def generate(view_name: str, query_table: str):
    today = datetime.now().strftime("%Y%m%d")
    save_path = Path(f"parking.{view_name}.{today}.tsv")

    items = fetch_key_category_list(query_table)
    tsv.write_tsv(save_path, items)
    logger.info(f"export {len(items)}")

    table = f"{view_name}_{today}"
    create_key_category_table(view_name, table, save_path)
    save_path.unlink()
    logger.info(f"create '{table}'")

    expired_tables = dbutils.drop_expired_tables(pgsql.POI_CONFIG, view_name, expire_day=8)
    logger.info(f"drop {expired_tables}")


def main():
    """
    主函数
    """
    generate("park_key_category_list", "aoi_30_build_list")
    generate("park_4categories_list_2024q4", "poi_4categories_list_2024q4")


if __name__ == "__main__":
    main()
