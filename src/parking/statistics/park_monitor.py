"""
停车场 2.0 数据监控，对于任意给定的停车场集合（bid），进行如流群通知：
- summary：2.0数量+占比，2.0pv占比
- 三缺一（上量路径，每日优先）：4要素清单，是2.0非1.0，上量pv潜力
- 监控警报：2.0退化数量+清单，4要素退化数量+清单
"""
import shutil
import sys
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Callable

import requests
from loguru import logger
from retrying import retry

from src.parking.statistics import dbutils
from src.tools import tsv, pipeline, utils, function, pgsql, remote_dir

DB_FIELD = [
    ("bid", "bid"),
    ("name", "名称"),
    ("std_tag", "std_tag"),
    ("show_tag", "show_tag"),
    ("pv", "停车场pv"),
    ("churukou_all_jz", "入口全精准"),
    ("has_polygon", "原始面"),
    ("has_opentag", "开放属性"),
    ("has_parking_lot", "车位数"),
    ("has_spatial_tag", "空间属性"),
    ("has_parking_fee", "收费信息"),
    ("parent_bid", "父点bid"),
    ("parent_name", "父点名称"),
    ("parent_std_tag", "父点std_tag"),
    ("parent_pv", "父点pv"),
    ("precise10", "停车场精准1.0"),
    ("precise20", "停车场精准2.0"),
    ("precise30", "停车场精准3.0"),
    ("category", "垂类"),
    ("value_tag", "价值标签"),
    ("open_limit_new", "开放属性标签"),
]
DB_FIELD_INDEX = {name: i for i, (name, _) in enumerate(DB_FIELD)}
DB_FIELD_NAME = {name: display_name for name, display_name in DB_FIELD}
AFS_DIR = remote_dir.RemoteDir(
    remote_root="/user/map-data-streeview/aoi-ml/parking/park20_monitor",
    local_root=Path("output"),
)


desc = pipeline.get_desc()


@dataclass
class Summary:
    """
    概述
    """

    count: int
    park20: int
    park20_ratio: float
    park20_pv: int
    park20_pv_ratio: float


@dataclass
class MissingOne:
    """
    三缺一指标
    """

    name: str
    count: int
    expected_increase: float


@dataclass
class Notice:
    """
    进化、退化指标
    """

    name: str
    increase: int
    decrease: int


@dataclass
class Context:
    """
    上下文
    """

    previous: str
    current: str
    items: list[tuple] = field(default_factory=list)
    prev_items: list[tuple] = field(default_factory=list)
    summary: Summary = field(default=None)
    missing_ones: list[MissingOne] = field(default_factory=list)
    notices: list[Notice] = field(default_factory=list)
    url: str = field(default=None)

    @staticmethod
    def set_items(c: "Context", value: list[tuple]):
        """
        赋值 items
        """
        c.items = value

    @staticmethod
    def set_prev_items(c: "Context", value: list[tuple]):
        """
        赋值 prev_items
        """
        c.prev_items = value


# pipes:


def read_data(provider: Callable[[], list[tuple]], setter: Callable[[Context, list[tuple]], None]):
    """
    pipe: 读取数据库
    """

    @desc("read data")
    def pipe(ctx: Context, proceed):
        setter(ctx, provider())
        proceed()

    return pipe


@desc()
def calc_summary(ctx: Context, proceed):
    """
    pipe: 计算概述
    """
    precise10_index = DB_FIELD_INDEX["precise10"]
    precise20_index = DB_FIELD_INDEX["precise20"]
    open_index = DB_FIELD_INDEX["open_limit_new"]
    pv_index = DB_FIELD_INDEX["pv"]

    def is_park20(x):
        return x[precise20_index] == 1 or (x[open_index] not in (None, "", "0", "-1") and x[precise10_index] == 1)

    count = len(ctx.items)
    park20 = sum(1 for x in ctx.items if is_park20(x))
    park20_ratio = 1.0 * park20 / count
    park20_pv = sum(x[pv_index] for x in ctx.items if is_park20(x))
    park20_pv_ratio = 1.0 * park20_pv / sum(x[pv_index] for x in ctx.items)

    ctx.summary = Summary(
        count=count,
        park20=park20,
        park20_ratio=park20_ratio,
        park20_pv=park20_pv,
        park20_pv_ratio=park20_pv_ratio,
    )
    proceed()


def calc_missing_one(prop: str, save_dir: Path):
    """
    pipe: 计算三缺一
    """
    park20_props = ["churukou_all_jz", "has_opentag", "has_polygon", "has_spatial_tag", "precise10"]
    assert prop in park20_props

    @desc(f"calc missing one: {prop}")
    def pipe(ctx: Context, proceed):
        true_indexes = [DB_FIELD_INDEX[p] for p in park20_props if p != prop]
        false_index = DB_FIELD_INDEX[prop]
        pv_index = DB_FIELD_INDEX["pv"]

        filtered_items = [x for x in ctx.items if x[false_index] == 0 and all(x[i] == 1 for i in true_indexes)]
        pv = sum([x[pv_index] for x in filtered_items])
        pv_ratio = 1.0 * pv / ctx.summary.park20_pv

        file_path = save_dir / f"missing_one.{prop}.{ctx.current}.tsv"
        tsv.write_tsv(file_path, filtered_items)
        missing_one = MissingOne(
            name=DB_FIELD_NAME[prop],
            count=len(filtered_items),
            expected_increase=pv_ratio,
        )
        ctx.missing_ones.append(missing_one)
        proceed()

    return pipe


def calc_notice(prop: str, save_dir: Path):
    """
    pipe: 计算进化、退化情况
    """
    park20_props = ["churukou_all_jz", "has_opentag", "has_polygon", "has_spatial_tag", "precise10", "precise20"]
    assert prop in park20_props

    bid_index = DB_FIELD_INDEX["bid"]
    prop_index = DB_FIELD_INDEX[prop]

    def is_new(curr: dict[str, tuple], prev: dict[str, tuple], x: tuple):
        bid = x[bid_index]
        curr_value = curr[bid][prop_index]
        prev_value = prev[bid][prop_index] if bid in prev else 0
        return (curr_value, prev_value) == (1, 0)

    @desc(f"calc notice: {prop}")
    def pipe(ctx: Context, proceed):
        items_dict = {x[bid_index]: x for x in ctx.items}
        prev_items_dict = {x[bid_index]: x for x in ctx.prev_items}

        increases = [x for x in ctx.items if is_new(items_dict, prev_items_dict, x)]
        increase_path = save_dir / f"increase.{prop}.{ctx.previous}-{ctx.current}.tsv"
        tsv.write_tsv(increase_path, increases)

        decreases = [x for x in ctx.prev_items if is_new(prev_items_dict, items_dict, x)]
        decrease_path = save_dir / f"decrease.{prop}.{ctx.previous}-{ctx.current}.tsv"
        tsv.write_tsv(decrease_path, decreases)

        notice = Notice(
            name=DB_FIELD_NAME[prop],
            increase=len(increases),
            decrease=len(decreases),
        )
        ctx.notices.append(notice)
        proceed()

    return pipe


def upload_dir(target_dir: Path):
    """
    pipe: 上传目录
    """

    @desc(f"upload dir: {target_dir}")
    def pipe(ctx: Context, proceed):
        function.exec_shell_cmd(cmd=f"zip -r {target_dir.name}.zip {target_dir.name}", cwd=target_dir.parent)

        zip_path = target_dir.parent / f"{target_dir.name}.zip"
        ctx.url = upload_file(zip_path)
        zip_path.unlink(missing_ok=True)
        proceed()

    return pipe


def send_ruliu(collection_name: str):
    """
    pipe: 发送通知到如流
    """

    @desc(f"send ruliu: {collection_name}")
    def pipe(ctx: Context, proceed):
        missing_ones = {x.name: x for x in ctx.missing_ones}
        notices = {x.name: x for x in ctx.notices}
        common_names = set(missing_ones.keys()) & set(notices.keys())
        single_names = (set(missing_ones.keys()) | set(notices.keys())) - common_names

        park20_pv_ratio = ctx.summary.park20_pv_ratio * 100
        park20_ratio = ctx.summary.park20_ratio * 100
        summary = (
            f'> pv占比：<font color="green">{park20_pv_ratio:.2f}%</font>，'
            f'数量占比：<font color="green">{park20_ratio:.2f}%</font> '
            f'<font color="gray">({ctx.summary.park20}/{ctx.summary.count})</font>'
        )
        message = f"""
### {collection_name}：{ctx.previous} - {ctx.current}

{summary}

        """

        for name in single_names:
            if name in missing_ones:
                x = missing_ones[name]
                message += "\n"
                message += f'- **{x.name}**：{x.count} (<font color="green">+{x.expected_increase*100:.2f}%</font>)'

            if name in notices:
                x = notices[name]
                message += "\n"
                message += (
                    f'- **{x.name}**（<font color="green">{x.increase} ↑</font> '
                    f'<font color="red">{x.decrease} ↓</font>）'
                )

        pairs = [(missing_ones[name], notices[name]) for name in common_names]
        pairs.sort(key=lambda x: x[0].expected_increase, reverse=True)
        for x, y in pairs:
            message += "\n"
            message += (
                f'- **{x.name}**（<font color="green">{y.increase} ↑</font> <font color="red">{y.decrease} ↓</font>）：'
                f'{x.count} (<font color="green">+{x.expected_increase*100:.2f}%</font>)'
            )

        message += "\n\n"
        message += f"> {ctx.url}"
        resp = send_hi([{"type": "MD", "content": message}])
        logger.info(f"{len(message)=}, {resp=}")
        proceed()

    return pipe


def sync_to_remote(local_dir: Path):
    """
    pipe: 同步到AFS
    """

    @desc(f"sync to afs: {AFS_DIR.remote_root}/{local_dir.name}.tar")
    def pipe(_ctx: Context, proceed):
        AFS_DIR.push(local_dir.name, force=True)
        proceed()

    return pipe


# helpers:


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(file_path: Path) -> str:
    """
    上传文件
    """
    url_post = "http://mapde-poi.baidu-int.com/prod/compat/upload"
    url_get = "https://mapde-poi.baidu-int.com/beefs/get?uuid={0}"

    files = {"file": open(file_path, "rb")}
    response = requests.post(url_post, files=files)
    uuid = response.text
    return url_get.format(uuid)


def send_hi(msgs: list[dict]):
    """
    发送如流消息
    """
    webhook = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d3239527e7ff3388621ecbc54e4b7a973"
    message = {"message": {"body": msgs}}
    resp = requests.post(webhook, json=message)
    return resp.json()


def get_sql_provider(sql: str, config):
    """
    将给定的 sql，将其包装为 provider 函数
    """

    def provider(date: str):
        with pgsql.get_connection(config) as conn:
            return pgsql.fetch_all(conn, sql.format(date))

    return provider


# collection configs:


def for_high_middle_value_static():
    """
    中高价值集合-静态
    """
    sql = """
        select a.*
        from park_statistical_data_{0} a
        inner join park_value_list b on a.bid = b.bid
        where b.value in ('TOP80', 'TOP80-TOP90');
    """
    return "high_middle_value_static", "中高价值集合-静态", get_sql_provider(sql, pgsql.POI_SLAVER_CONFIG)


def for_high_middle_value_dynamic():
    """
    中高价值集合-动态
    """
    sql = """
        select * from park_statistical_data_{0}
        where value_tag in ('TOP70', 'TOP70-TOP90');
    """
    return "high_middle_value_dynamic", "中高价值集合-动态", get_sql_provider(sql, pgsql.POI_SLAVER_CONFIG)


def for_high_value():
    """
    高价值集合
    """
    sql = """
        select * from park_statistical_data_{0}
        where value_tag in ('TOP70');
    """
    return "high_value", "高价值集合", get_sql_provider(sql, pgsql.POI_SLAVER_CONFIG)


def for_key_category():
    """
    重点垂类集合
    """
    sql = """
        select a.*
        from park_statistical_data_{0} a
        inner join park_key_category_list_{0} b on a.bid = b.bid;
    """
    return "key_category", "重点垂类集合", get_sql_provider(sql, pgsql.POI_SLAVER_CONFIG)


def for_low_adoption():
    """
    低采纳率集合
    """
    sql = """
        select a.*
        from park_statistical_data_{0} a
        inner join aoi_30_build_list b on a.parent_bid = b.bid
        where b.type in (7);
    """
    return "low_adoption", "低采纳率集合", get_sql_provider(sql, pgsql.POI_SLAVER_CONFIG)


def for_the_four_q4():
    """
    四大垂类Q4集合
    """
    sql = """
        select a.*
        from park_statistical_data_{0} a
        inner join park_4categories_list_2024q4 b on a.bid = b.bid;
    """
    return "the_four_q4", "四大垂类Q4集合", get_sql_provider(sql, pgsql.POI_SLAVER_CONFIG)


def for_park_top_city_list():
    """
    TOP4城市2.0上量集合
    """
    sql = """
        select a.*
        from park_statistical_data_{0} a
        join park_top_city_list_{0} b on a.bid = b.bid;
    """
    return "top_city", "TOP4城市2.0上量集合", get_sql_provider(sql, pgsql.POI_SLAVER_CONFIG)


def for_all():
    """
    停车场全量集合
    """
    sql = """
        select * from park_statistical_data_{0}
    """
    return "all", "全集", get_sql_provider(sql, pgsql.POI_SLAVER_CONFIG)


# compose pipes:


def get_pipe(config, output_dir: Path, previous: str, current: str):
    """
    组合管道
    """
    collection_id, display_name, provider = config
    shutil.rmtree(output_dir, ignore_errors=True)
    output_dir = utils.ensure_dir(output_dir / f"park20_monitor.{collection_id}.{current}")
    pipe = pipeline.Pipeline(
        read_data(lambda: provider(current), Context.set_items),
        calc_summary,
        calc_missing_one("churukou_all_jz", output_dir),
        calc_missing_one("has_opentag", output_dir),
        calc_missing_one("has_polygon", output_dir),
        calc_missing_one("has_spatial_tag", output_dir),
        calc_missing_one("precise10", output_dir),
        read_data(lambda: provider(previous), Context.set_prev_items),
        calc_notice("churukou_all_jz", output_dir),
        calc_notice("has_opentag", output_dir),
        calc_notice("has_polygon", output_dir),
        calc_notice("has_spatial_tag", output_dir),
        calc_notice("precise10", output_dir),
        calc_notice("precise20", output_dir),
        upload_dir(output_dir),
        sync_to_remote(output_dir),
        send_ruliu(display_name),
    )
    desc.attach(pipe)
    return pipe


def on_unsynced(actual_table_name: str, expected_table_name: str):
    """
    检查到数据表未同步完成时的回调函数
    """
    logger.warning(f"垃圾从库，还没同步：'{actual_table_name}' != '{expected_table_name}'")


@logger.catch
def main(start_date: str, end_date: str, *collections: str):
    """
    主函数
    """
    configs = {
        "high_middle_value_static": for_high_middle_value_static(),  # 未来都将用动态集合，静态集合逐渐淡出
        "high_middle_value_dynamic": for_high_middle_value_dynamic(),
        "high_value": for_high_value(),
        "key_category": for_key_category(),
        "low_adoption": for_low_adoption(),  # 低采纳率集合，暂不关注
        "the_four_q4": for_the_four_q4(),
        "top_city": for_park_top_city_list(),
        "all": for_all(),
    }
    expected_configs = [configs[x] for x in collections]
    for config in expected_configs:
        logger.info(f"=============== {config[:2]} ===============")
        ctx = Context(start_date, end_date)
        pipe = get_pipe(config, AFS_DIR.local_root, start_date, end_date)
        pipe(ctx)


def _resolve_args(args):
    arg1, arg2 = args[1], args[2]
    # 两种模式：
    # 1. 若第一个参数是 'delta'，表示：从今天往前推 delta_day 天，作为开始日期，结束日期默认是 today
    # 2. 直接给出明确的两个 '%Y%m%d' 格式的日期，作为开始日期和结束日期
    if arg1 == "delta":
        delta_day = int(arg2)
        today = datetime.now()
        current = today.strftime("%Y%m%d")
        while True:
            assert delta_day < 7, "delta_day must be less than 7"
            previous = (today - timedelta(days=delta_day)).strftime("%Y%m%d")
            if dbutils.exists_table(
                pgsql.POI_SLAVER_CONFIG, f"park_statistical_data_{previous}"
            ) and dbutils.exists_table(pgsql.POI_SLAVER_CONFIG, f"park_key_category_list_{previous}"):
                break

            delta_day += 1

        # 我们只检查后 current 的表是否存在，previous 默认应该存在，不存在报错也符合预期
        dbutils.wait_db_sync_done(pgsql.POI_SLAVER_CONFIG, "park_statistical_data", current, on_unsynced)
        dbutils.wait_db_sync_done(pgsql.POI_SLAVER_CONFIG, "park_key_category_list", current, on_unsynced)
        return previous, current
    else:
        return arg1, arg2


if __name__ == "__main__":
    start, end = _resolve_args(sys.argv)
    main(start, end, *sys.argv[3:])
