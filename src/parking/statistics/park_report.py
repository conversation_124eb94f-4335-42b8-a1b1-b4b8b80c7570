"""
例行输出停车场2.0统计用的中间数据，储存于 park_statistical_data VIEW 中。
注意：此处只产中间结果，最终统计在 Sugar 平台：
https://sugar.baidu-int.com/group/aoi_monitor/report/r_1013e-bepqp1nj-12br89?__scp__=Baidu
"""
import shutil
import sys
import time
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from loguru import logger

from src.parking.statistics import dbutils
from src.tools import pgsql, tsv, remote_dir

CATEGORIES = {
    "机场&火车站": ["交通设施;飞机场", "交通设施;火车站"],
    "长途汽车站": ["交通设施;长途汽车站"],
    "医院": ["医疗;综合医院", "医疗;专科医院"],
    "大学": ["教育培训;高等院校"],
    "购物中心": ["购物;购物中心"],
    "体育场馆&展览馆&剧院": ["文化传媒;展览", "休闲娱乐;剧院", "运动健身;体育场馆"],
    "住宅区": ["房地产;住宅区"],
    "学校": ["教育培训;小学", "教育培训;幼儿园", "教育培训;中学"],
    "写字楼": ["房地产;写字楼"],
    "酒店": ["酒店;星级酒店"],
    "港口": ["交通枢纽;港口"],
    "景区": ["旅游景点"],
    "公司": ["公司企业"],
    "政府": ["政府机构"],
}
INVERSE_CATEGORIES = {tag: category for category, tags in CATEGORIES.items() for tag in tags}
DEFAULT_CATEGORY = "其他"
VIEW_NAME = "park_statistical_data"
LOCAL_CACHE_DIR = Path(VIEW_NAME)
AFS_DIR = remote_dir.RemoteDir(
    remote_root=f"/user/map-data-streeview/aoi-ml/parking/{VIEW_NAME}",
    local_root=LOCAL_CACHE_DIR,
)
FOUR_CATEGORY = ["机场&火车站", "医院", "购物中心", "景区"]
CAL_ROOT_PV_CATEGORY = ["生活服务;售票处", "生活服务;其他", "交通设施;其他", "房地产;写字楼", "公司企业;园区"]


@dataclass
class StatisticalData:
    """
    停车场统计数据
    """

    bid: str
    name: str
    std_tag: str
    show_tag: str
    pv: int
    churukou_all_jz: int
    has_polygon: int
    has_opentag: int
    has_parking_lot: int
    has_spatial_tag: int
    has_parking_fee: int
    parent_bid: str
    parent_name: str
    parent_std_tag: str
    parent_pv: int
    precise10: int
    precise20: int
    precise30: int
    open_limit_new: str
    category: str = ""
    value_tag: str = ""
    root_bid: str = ""
    root_pv: int = 0


# business logic:


def copy_original_statistical_data(original_path: Path, date_str: str):
    """
    导出原始数据到本地 tsv 文件中
    """
    online_table = f"park_online_data_{date_str}"
    churukou_status_table = f"temp_churukou_status_{date_str}"
    sql = f"""
        -- 查询来自 poi 的数据
        with poi_info as (
            select
                a.bid,
                b.click_pv as pv,
                c.bid as parent_bid,
                c.name as parent_name,
                c.std_tag as parent_std_tag,
                c.click_pv as parent_pv
            from {online_table} a
            inner join poi b on a.std_tag in ('交通设施;停车场', '交通设施;路侧停车位') and a.bid = b.bid
            left join poi c on a.parent_id != '0' and a.parent_id = c.bid
        ),
        -- 统计出bid维度每个条件的详情
        indicators as (
            select 
                p.bid,
                name,
                std_tag,
                show_tag,
                p.open_limit_new,
                case when precise > 0 then 1 else 0 end as jz10,
                case when 
                    -- 历史上 '路侧停车场' 和 '临时停车点' 的 std_tag 都是 '交通设施;路侧停车位'，
                    -- 但后来 '路侧停车场' 【部分】改成使用 '交通设施;停车场'，此处要兼容
                    (std_tag = '交通设施;路侧停车位' or (std_tag = '交通设施;停车场' and show_tag = '路侧停车场'))
                    or (c.churukou_jz_count = c.churukou_count and c.churukou_count > 0) 
                    then 1 else 0 end as churukou_all_jz,
                case when area is not null or smart_area is not null then 1 else 0 end as has_polygon,
                case when 
                    (std_tag = '交通设施;路侧停车位' or (std_tag = '交通设施;停车场' and show_tag = '路侧停车场'))
                    or mohu_text = '不对外开放' 
                    or open_limit_new not in ('', '-1') 
                    then 1 else 0 end as has_opentag,
                case when vague_parking_lot > 0 or premise_parking_lot > 0 or parking_smart_tp > 0 or parking_smart_lot > 0 then 1 else 0 end has_parking_lot,
                case when show_tag in ('路侧停车场', '地上停车场', '地下停车场', '临时停车点', '立体停车场', '路侧停车位', '门前停车场') then 1 else 0 end as has_spatial_tag,
                case when fee is not null then 1 else 0 end as has_parking_fee,
                park_spec
            from {online_table} p
            left join {churukou_status_table} c on c.bid = p.bid
            where std_tag in ('交通设施;停车场', '交通设施;路侧停车位')
        )
        select
            i.bid,
            i.name,
            i.std_tag,
            i.show_tag,
            pv,
            churukou_all_jz,
            has_polygon,
            has_opentag,
            has_parking_lot,
            has_spatial_tag,
            has_parking_fee,
            parent_bid,
            parent_name,
            parent_std_tag,
            parent_pv,
            jz10,
            (
                case when 
                    (jz10 = 1 and churukou_all_jz = 1 and has_polygon = 1 and has_opentag = 1 and has_spatial_tag = 1)
                    or (park_spec in (1, 2) and show_tag in ('门前停车场', '路侧停车场'))
                then 1 else 0 end
            ) as jz20,
            (case when jz10 = 1 and churukou_all_jz = 1 and has_polygon = 1 and has_opentag = 1 and has_spatial_tag = 1 and has_parking_fee = 1 then 1 else 0 end) as jz30,
            i.open_limit_new
        from indicators i
        left join poi_info p on i.bid = p.bid
        order by p.pv desc nulls last;
    """

    original_path.unlink(missing_ok=True)
    pgsql.copy_to_tsv(pgsql.POI_SLAVER_CONFIG, sql, original_path)


def create_temp_churukou_status(table_name: str, date_str: str):
    """
    准备出入口状态表，要创建索引，不然慢的要死
    """
    online_table = f"park_online_data_{date_str}"
    sql = f"""
        create table if not exists {table_name} AS
        select 
            parent_id as bid,
            sum(case when road_relation is not null then 1 else 0 end) churukou_jz_count,
            count(1) churukou_count
        from {online_table}
        where std_tag = '出入口;停车场出入口' and name like '%入口' and status in (1, 15)
        group by parent_id;

        create unique index if not exists idx_{table_name}_bid on {table_name} (bid);
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute(conn, sql)

    # 主库到从库可能要花很长时间的
    while not dbutils.exists_table(pgsql.POI_SLAVER_CONFIG, table_name):
        logger.warning(f"waiting for '{table_name}' to be synced to slave database")
        time.sleep(10)


def read_statistical_data(original_path: Path):
    """
    读取原始数据为 StatisticalData 列表
    """
    rows = tsv.read_tsv(original_path)
    items = [
        StatisticalData(
            bid=x[0],
            name=x[1],
            std_tag=x[2],
            show_tag=x[3],
            pv=int(x[4]) if x[4] != "" else 0,
            churukou_all_jz=int(x[5]),
            has_polygon=int(x[6]),
            has_opentag=int(x[7]),
            has_parking_lot=int(x[8]),
            has_spatial_tag=int(x[9]),
            has_parking_fee=int(x[10]),
            parent_bid=x[11],
            parent_name=x[12],
            parent_std_tag=x[13],
            parent_pv=int(x[14]) if x[14] != "" else 0,
            precise10=int(x[15]),
            precise20=int(x[16]),
            precise30=int(x[17]),
            open_limit_new=x[18],
        )
        for x in rows
    ]
    for item in items:
        item.category = get_category(item)

    fill_value_tag_for_has_parent(items)
    fill_value_tag_for_no_parent(items)
    fill_value_tag_for_root_pv(items)
    return items


def write_statistical_data(additional_path: Path, items: list[StatisticalData]):
    """
    写入 StatisticalData 列表到本地 tsv 文件中
    """
    rows = [
        [
            x.bid,
            x.name,
            x.std_tag,
            x.show_tag,
            x.pv,
            x.churukou_all_jz,
            x.has_polygon,
            x.has_opentag,
            x.has_parking_lot,
            x.has_spatial_tag,
            x.has_parking_fee,
            x.parent_bid,
            x.parent_name,
            x.parent_std_tag,
            x.parent_pv,
            x.precise10,
            x.precise20,
            x.precise30,
            x.category,
            x.value_tag,
            x.open_limit_new,
            x.root_bid,
            x.root_pv,
        ]
        for x in items
    ]
    tsv.write_tsv(additional_path, rows)


def create_statistical_data_table(table_name: str, additional_path: Path):
    """
    创建 park_statistical_data 表，并导入来自 tsv 的数据
    """
    sql_clear = f"""
        -- 所有操作发生在同一个事务中，删除 VIEW 不会导致空窗期
        DROP VIEW IF EXISTS {VIEW_NAME};
        DROP TABLE IF EXISTS {table_name};
    """
    sql_create = f"""
        CREATE TABLE {table_name} (
            bid VARCHAR(64) PRIMARY KEY NOT NULL,
            name VARCHAR NOT NULL DEFAULT '',
            std_tag VARCHAR NOT NULL DEFAULT '',
            show_tag VARCHAR,
            pv INTEGER NOT NULL DEFAULT 0,
            churukou_all_jz INTEGER NOT NULL DEFAULT 0,
            has_polygon INTEGER NOT NULL DEFAULT 0,
            has_opentag INTEGER NOT NULL DEFAULT 0,
            has_parking_lot INTEGER NOT NULL DEFAULT 0,
            has_spatial_tag INTEGER NOT NULL DEFAULT 0,
            has_parking_fee INTEGER NOT NULL DEFAULT 0,
            parent_bid VARCHAR,
            parent_name VARCHAR,
            parent_std_tag VARCHAR,
            parent_pv INTEGER NOT NULL DEFAULT 0,
            precise10 INTEGER NOT NULL DEFAULT 0,
            precise20 INTEGER NOT NULL DEFAULT 0,
            precise30 INTEGER NOT NULL DEFAULT 0,
            category VARCHAR,
            value_tag VARCHAR NOT NULL DEFAULT '',
            open_limit_new VARCHAR(127),
            root_bid VARCHAR(127),
            root_pv INTEGER NOT NULL DEFAULT 0
        );
    """
    sql_copy = f"""
        COPY {table_name} FROM STDIN WITH CSV DELIMITER AS '\t';
    """
    sql_index = f"""
        CREATE INDEX idx_{table_name}_parent_bid ON {table_name}(parent_bid);
    """
    sql_view = f"""
        CREATE OR REPLACE VIEW {VIEW_NAME} AS
        SELECT * FROM {table_name};
    """

    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
        open(additional_path, "r", encoding="utf-8") as f,
    ):
        cursor.execute(sql_clear)
        cursor.execute(sql_create)
        # noinspection PyTypeChecker
        cursor.copy_expert(sql_copy, f)
        cursor.execute(sql_index)
        cursor.execute(sql_view)
        conn.commit()


# helpers:


def drop_table(table_name: str):
    """
    删一张表
    """
    sql = f"""
        drop table {table_name};
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute(conn, sql)


def get_category(data: StatisticalData) -> str:
    """
    获取数据的分类标签
    """
    if data.std_tag == "交通设施;路侧停车位":
        return ""
    if not data.parent_std_tag:
        return ""

    parent_std_tag_1 = data.parent_std_tag.split(";")[0]
    category = DEFAULT_CATEGORY
    if parent_std_tag_1 in INVERSE_CATEGORIES:
        category = INVERSE_CATEGORIES[parent_std_tag_1]
    elif data.parent_std_tag in INVERSE_CATEGORIES:
        category = INVERSE_CATEGORIES[data.parent_std_tag]
    if data.parent_name.endswith("航站楼"):
        category = "机场&火车站"

    return category


def fill_value_tag_for_has_parent(items: list[StatisticalData]):
    """
    填充有父节点的数据的 value_tag 字段
    """
    parent_bid_park_d = {}
    park_info_d = {}
    for item in items:
        if not item.parent_bid:
            continue

        if item.category not in park_info_d:
            park_info_d[item.category] = []
        if item.parent_bid not in parent_bid_park_d:
            parent_bid_park_d[item.parent_bid] = []
            park_info_d[item.category].append([item.parent_pv, item.parent_bid])
        parent_bid_park_d[item.parent_bid].append(item)

    for key, group_list in park_info_d.items():
        group_list.sort(key=lambda x: x[0], reverse=True)
        total = sum(x[0] for x in group_list)
        current_sum = 0
        for parent_pv, parent_bid in group_list:
            current_sum += parent_pv
            if current_sum == parent_pv or current_sum <= 0.7 * total:
                flag = "TOP70"
            elif current_sum <= 0.9 * total:
                flag = "TOP70-TOP90"
            else:
                flag = "TOP90-TOP100"

            if key in ["住宅区", "公司"] and flag in ["TOP70", "TOP70-TOP90"]:
                is_all_low = True
                for item in parent_bid_park_d[parent_bid]:
                    if item.pv >= 3:
                        is_all_low = False
                        break
                if is_all_low:
                    flag = "TOP90-TOP100"

            for item in parent_bid_park_d[parent_bid]:
                item.value_tag = flag


def fill_value_tag_for_no_parent(items: list[StatisticalData]):
    """
    填充没有父节点的数据的 value_tag 字段
    """
    park_info_d = {}
    for item in items:
        if item.parent_bid:
            continue

        if item.category not in park_info_d:
            park_info_d[item.category] = []
        park_info_d[item.category].append([item.pv, item])

    for key, group_list in park_info_d.items():
        group_list.sort(key=lambda x: x[0], reverse=True)
        total = sum(x[0] for x in group_list)
        current_sum = 0
        for pv, item in group_list:
            current_sum += pv
            if current_sum <= 0.7 * total:
                flag = "TOP70"
            elif current_sum <= 0.9 * total:
                flag = "TOP70-TOP90"
            else:
                flag = "TOP90-TOP100"

            item.value_tag = flag


def fill_value_tag_for_root_pv(items: list[StatisticalData]):
    """
    填充祖节点bid/pv
    1.父点为四大垂类、生活服务;售票处、生活服务;其他、交通设施;其他、房地产;写字楼
    2.父点不在上述垂类，但父点垂类=最高主点垂类
    最高pv：对比父点和最高主点，谁的pv高取谁
    """
    parent_bid_park_d = {}
    park_info_d = {}
    logger.info(f"fill root_bid/pv...")
    for item in items:
        if not item.parent_bid:
            continue
        item.root_bid = item.parent_bid
        item.root_pv = item.parent_pv
        # 测试发现POI relation_bid存在互为父点的数据
        valid_bids_list = [item.bid, item.parent_bid]
        max_depth_bid = item.parent_bid
        max_depth_pv = item.parent_pv
        max_poi_std_tag = item.parent_std_tag
        while True:
            sql = "select bid,relation_bid,click_pv,std_tag from poi where bid = %s"
            poi_info = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [max_depth_bid])
            max_depth_pv = poi_info[2] if poi_info else 0
            max_poi_std_tag = poi_info[3] if poi_info else max_poi_std_tag
            if poi_info and poi_info[1] and poi_info[1] != "0" and poi_info[1] not in valid_bids_list:
                max_depth_bid = poi_info[1]
                valid_bids_list.append(poi_info[1])
                continue
            break
        # 父点为四大垂类、生活服务;售票处、生活服务;其他、交通设施;其他、房地产;写字楼 直接变更
        # 需求变更父点为四大垂类、房地产;写字楼、公司企业;园区、生活服务%、交通设施%、休闲娱乐%、购物%、医疗%、旅游景点%、交通设施%
        if (
            item.category in FOUR_CATEGORY
            or item.parent_std_tag in CAL_ROOT_PV_CATEGORY
            or item.parent_std_tag == max_poi_std_tag
            or item.parent_std_tag.startswith("生活服务")
            or item.parent_std_tag.startswith("交通设施")
            or item.parent_std_tag.startswith("休闲娱乐")
            or item.parent_std_tag.startswith("购物")
            or item.parent_std_tag.startswith("医疗")
            or item.parent_std_tag.startswith("旅游景点")
            or item.parent_std_tag.startswith("交通设施")
        ):
            item.root_bid = max_depth_bid
            item.root_pv = max([item.root_pv, max_depth_pv])


def on_unsynced(actual_table_name: str, expected_table_name: str):
    """
    检查到数据表未同步完成时的回调函数
    """
    logger.warning(f"垃圾从库，还没同步：'{actual_table_name}' != '{expected_table_name}'")


@logger.catch
def main(date_str: str):
    """
    主函数
    """
    today = datetime.now().strftime("%Y%m%d")
    if date_str == today:
        # 当天的 park_online_data 需要等待主从同步，而之前的日期，理论上早就应该同步好，如果没有，就等后面直接报错就行
        dbutils.wait_db_sync_done(pgsql.POI_SLAVER_CONFIG, "park_online_data", date_str, on_unsynced)

    with AFS_DIR.sync(date_str, force_push=True) as save_dir:
        original_path = save_dir / "original.tsv"
        additional_path = save_dir / "data.tsv"

        logger.info(f"create temp churukou status...")
        churukou_status_table = f"temp_churukou_status_{date_str}"
        create_temp_churukou_status(churukou_status_table, date_str)

        logger.info(f"copy original statistical data...")
        copy_original_statistical_data(original_path, date_str)

        logger.info(f"drop temp churukou status...")
        drop_table(churukou_status_table)

        logger.info(f"read statistical data...")
        items = read_statistical_data(original_path)

        logger.info(f"write statistical data ({len(items)})...")
        write_statistical_data(additional_path, items)

        logger.info(f"delete {original_path.name}, it's no longer useful...")
        original_path.unlink()

    logger.info(f"create statistical data table...")
    table_name = f"{VIEW_NAME}_{date_str}"
    create_statistical_data_table(table_name, additional_path)

    logger.info(f"clear local cache and expired tables...")
    shutil.rmtree(LOCAL_CACHE_DIR)

    dropped_tables = dbutils.drop_expired_tables(pgsql.POI_CONFIG, VIEW_NAME, expire_day=8)
    logger.info(f"drop table: {dropped_tables}")
    logger.info(f"completed!")


if __name__ == "__main__":
    target_date = sys.argv[1] if len(sys.argv) > 1 else datetime.now().strftime("%Y%m%d")
    main(target_date)
