"""
例行重点垂类停车场集合
"""
import os
import sys
import shutil
from datetime import datetime
from pathlib import Path

from loguru import logger

from src.parking.statistics import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F


def create_poi_spatial_classify_table(view_name: str, table_name: str, additional_path: Path):
    """
    创建 poi_spatial_classify 表，并导入来自 tsv 的数据
    """
    sql_clear_table = f"""
            DROP TABLE IF EXISTS {table_name};
        """
    print(sql_clear_table)
    sql_create = f"""
            CREATE TABLE {table_name} (
                bid VARCHAR(64) PRIMARY KEY NOT NULL,
                classify VARCHAR(16)
            );
        """
    sql_copy = f"""
            COPY {table_name} FROM STDIN WITH CSV DELIMITER AS '\t';
        """
    sql_clear_view = f"""
                DROP VIEW IF EXISTS {view_name};
            """
    sql_view = f"""
        CREATE OR REPLACE VIEW {view_name} AS
        SELECT * FROM {table_name};
    """

    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as cursor,
        open(additional_path, "r", encoding="utf-8") as f,
    ):
        cursor.execute(sql_clear_table)
        cursor.execute(sql_create)
        cursor.copy_expert(sql_copy, f)
        cursor.execute(sql_clear_view)
        cursor.execute(sql_view)
        conn.commit()


def exec_cmd(cmd=""):
    """
    执行命令
    """
    return F.exec_shell_cmd(f"{cmd}")


def afs_sync(today):
    """
    从 afs 下载数据
    """
    print("start download data")
    afs = AfsTool('poi_yingxiang')
    current_directory = os.getcwd()
    dir_path = os.path.join(current_directory, today)
    if not os.path.exists(dir_path):
        os.mkdir(dir_path)
    data_dir = f"{dir_path}/spatial_classify"
    if os.path.exists(data_dir):
        try:
            shutil.rmtree(data_dir)
            logger.info(f"delete directory {data_dir}")
        except Exception as e:
            logger.error(f"delete directory {data_dir} failed")

    output_data = f"{dir_path}/spatial_classify.csv"
    # if os.path.exists(output_data):
    #     os.unlink(output_data)
    code, _ = afs.get("/user/poi-yingxiang/output/relation/spatial_classify", dir_path)
    logger.info(f"afs code is {code}")
    if code == 0:
        print("download success")
        # data_dir = f"{dir_path}/spatial_classify"
        # output_data = f"{dir_path}/spatial_classify.csv"
        result, _ = exec_cmd(f"cat {data_dir}/* > {output_data}")
        if result == 0:
            print("merge file success")

            view_name = 'poi_spatial_classify'
            table = f"{view_name}_{today}"

            create_poi_spatial_classify_table(view_name, table, output_data)
            logger.info(f"create '{table}'")

    else:
        print("download failed")


if __name__ == "__main__":
    """
    主函数
    """
    arguments = sys.argv[1:]
    print(arguments)
    if len(arguments) < 1:
        print("请输入参数===========================")
        exit(0)
    action = arguments[0]
    today = datetime.now().strftime("%Y%m%d")

    if action == 'afs_download':
        afs_sync(today)