# 门前停车场策略

## 计算观察点来源占比

```shell
awk -F '\t' '{print $3, $6}' segment.tsv | sort | uniq | awk '{print $NF}' | sort | uniq -c | sort -nr
```

```shell
awk -F '\t' '{print $3, $6}' segment.tsv | sort | uniq | awk '{print $NF}' | sort | uniq -c | sort -nr | awk '{sum += $1; c[NR] = $1; n[NR] = $2} END {for (i = 1; i <= NR; i++) {print c[i]/sum, n[i]}}'
```

## 常用统计命令

```shell
cat */prime/polygon.tsv | awk -F '\t' '$6 ~ "failed$" {print $6}' | awk -F '-' '{print $NF}' | sort -T . | uniq -c | sort -nr
```