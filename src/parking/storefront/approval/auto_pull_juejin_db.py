"""
自动核实面入库：已准出 -> 已入库
"""

from loguru import logger

from src.parking.storefront import prod_flow
from src.parking.storefront.approval import icafe
from src.parking.storefront.verify import verified_to_db


def main():
    """
    主函数
    """
    cards = icafe.fetch_issues(icafe.CARD_TYPE_PULL, icafe.CARD_STATUS_PULL_APPROVAL_DONE)
    for card in cards:
        verified_dir = prod_flow.PROD_DIR / card.plan_id / card.batch / "verified"
        polygon_dir = verified_dir / card.batch_polygon
        if not polygon_dir.exists():
            logger.error(f"not found polygon-dir: {card.batch_image}, {polygon_dir}")
            continue

        verified_to_db.save_verified(polygon_dir)
        icafe.modify_issue_status(card.sequence, icafe.CARD_STATUS_PULL_SAVE_TO_DB)
        logger.info(f"modified icafe-status: {card.batch_image}")


if __name__ == "__main__":
    main()
