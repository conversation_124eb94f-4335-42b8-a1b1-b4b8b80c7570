"""
自动拉取掘金作业成果：准入-已投放掘金 -> 准入-已生成准出 & 准出-待准出
当完成率达到 100% 时，生成核实面，并发送准出报告
"""
from multiprocessing import pool

from loguru import logger

from src.parking.storefront import task_flow, prod_verified_flow
from src.parking.storefront.approval import icafe
from src.parking.storefront.verify import verified_flow
from src.tools import linq


def process_prime(task: tuple[int, str]):
    """
    多进程并发函数
    """
    prime_id, batch_image = task
    return verified_flow.get_prime_by_id(prime_id, batch_image)


def get_progress(batch: str, batch_image: str) -> tuple[int, int]:
    """
    获取进度掘金核实进度（面维度）：(ready, total)
    """
    prime_ids = task_flow.get_strategy_ids(status="VERIFYING", batch=batch, batch_image=batch_image)
    if not prime_ids:
        logger.warning(f"not found any PRIMEs with status VERIFYING and batch {batch}")
        return -1, 0

    with pool.Pool(16) as p:
        tasks = ((prime_id, batch_image) for prime_id in prime_ids)
        primes = [x for x in p.imap_unordered(process_prime, tasks) if x]

    counted_primes = linq.count_by(primes, key=lambda x: x.status)
    total = sum(x[1] for x in counted_primes.items())
    ready = counted_primes.get("ready", 0)
    return ready, total


def main():
    """
    主函数
    """
    cards = icafe.fetch_issues(icafe.CARD_TYPE_PUSH, icafe.CARD_STATUS_PUSH_PUBLISH_DONE)
    completed_cards = []
    # 确保未完成的日志能统一显示在最上方，方便老范去盯
    for card in cards:
        ready, total = get_progress(card.batch, card.batch_image)
        if ready == total:
            completed_cards.append(card)
            continue

        logger.info(f"[verifying] {card.batch_image} {ready/total:.2%} ({ready}/{total})")

    for card in completed_cards:
        logger.info(f"[verified-start] {card.batch_image}")
        prod_verified_flow.main(card.plan_id, card.batch, card.batch_image, disable_tqdm=True)
        icafe.modify_issue_status(card.sequence, icafe.CARD_STATUS_PUSH_VERIFIED_DONE)
        logger.info(f"[verified-end] {card.batch_image}")


if __name__ == "__main__":
    main()
