"""
自动投放掘金：已准入 -> 已投放掘金
"""
import requests
from loguru import logger

from src.parking.storefront.approval import icafe
from src.tools import ruliu

API = "http://gzdt-sys-rpm100vqs1j3.gzdt.baidu.com:8016/audit/api/inter/parkingfrontpic"
AFS_BASE = "/user/map-data-streeview/aoi-ml/parking/storefront/street_picture"


def push_to_juejin(batch_image: str):
    """
    推送到掘金（老范的接口）
    """
    body = {
        "afs_path": f"{AFS_BASE}/{batch_image}.tar",
        "token": "gD2fI9UKZUtYhvww",
    }
    resp = requests.post(url=API, json=body)
    json_obj = resp.json()
    return json_obj["code"] == 0 and json_obj["msg"] == "success"


def main():
    """
    主函数
    """
    # 1. 轮询拉取 icafe 类型="掘金准入"，状态="已准入" 的卡片
    cards = icafe.fetch_issues(icafe.CARD_TYPE_PUSH, icafe.CARD_STATUS_PUSH_APPROVAL_DONE)
    for card in cards:
        # 2. 调用老范接口投放掘金
        if not push_to_juejin(card.batch_image):
            logger.error(f"failed to push juejin: {card.batch_image}")
            continue

        logger.info(f"pushed juejin: {card.batch_image}")
        # 3. 修改卡片状态为"已投放掘金"
        icafe.modify_issue_status(card.sequence, icafe.CARD_STATUS_PUSH_PUBLISH_DONE)
        logger.info(f"modified icafe-status: {card.batch_image}")
        # 4. 如流通知相关成员：投放成果
        msg1 = f"【{icafe.CARD_STATUS_PUSH_PUBLISH_DONE}】\n- 卡片链接："
        card_url = f"https://console.cloud.baidu-int.com/devops/icafe/issue/storefront-juejin-{card.sequence}/show"
        msg2 = f"\n- 任务批次：{card.batch}\n- 掘金批次：{card.batch_image}\n"
        ruliu.send(
            [
                {"type": "TEXT", "content": msg1},
                {"type": "LINK", "href": card_url},
                {"type": "TEXT", "content": msg2},
                {"type": "AT", "atuserids": ["zhangdingping_cd", "mayuqing_cd", "yanghuiyu_cd"]},
            ]
        )
        logger.info(f"sent notification: {card.batch_image}")


if __name__ == "__main__":
    main()
