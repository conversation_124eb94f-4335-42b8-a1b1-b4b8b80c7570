"""
create icafe card by api
"""
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Iterable, Union

import requests
from loguru import logger

from src.parking.storefront.approval import pull_info, push_info

SPACE_ID = "storefront-juejin"
USERNAME = "gaoxinyu_cd"
PASSWORD = "VVV4iiarA2%2B4%2FQG02zze4YOE8jkOWVIgEjC"

CARD_TYPE_PUSH = "掘金准入"
CARD_TYPE_PULL = "掘金准出"

CARD_STATUS_PUSH_APPROVAL_DOING = "待准入"  # 【自动】脚本创建
CARD_STATUS_PUSH_APPROVAL_DONE = "已准入"  # 【手动】工艺批准
CARD_STATUS_PUSH_PUBLISH_DONE = "已投放掘金"  # 【自动】脚本轮询状态 - 投放掘金
CARD_STATUS_PUSH_VERIFIED_DONE = "核实完成"  # 【自动】脚本轮询状态 - 生成准出

CARD_STATUS_PULL_APPROVAL_DOING = "待准出"  # 【自动】脚本创建
CARD_STATUS_PULL_APPROVAL_DONE = "已准出"  # 【手动】工艺批准
CARD_STATUS_PULL_SAVE_TO_DB = "已入库"  # 【自动】脚本轮询状态 - 入库
CARD_STATUS_PULL_PUBLISH_READY = "允许下发图灵"  # 【手动】工艺点击下发
CARD_STATUS_PULL_PUBLISH_DONE = "已下发图灵"  # 【自动】脚本轮询状态 - 下发图灵
CARD_STATUS_PULL_MULTI_VERIFY_READY = "投多图核实"  # 【手动】工艺点击下发
CARD_STATUS_PULL_MULTI_VERIFY_DONE = "已投多图核实"  # 【自动】脚本轮询状态 - 投多图核实
CARD_STATUS_PULL_MULTI_VERIFY_APPROVAL_DOING = "多图核实待准出"  # 【自动】脚本轮询状态 - 已投多图核实
CARD_STATUS_PULL_MULTI_VERIFY_APPROVAL_DONE = "多图核实已准出"  # 【自动】工艺点击下发 - 多图核实待准出


@dataclass
class ICafeCard:
    """
    icafe 卡片信息
    """

    sequence: int
    title: str
    plan_id: str
    batch: str
    batch_image: str
    batch_diff: list[str]
    batch_polygon: str
    created_at: datetime
    detail: str


def get_approval_push_detail(analysis: push_info.Analysis):
    """
    获取准入 icafe 卡片详情（html）
    """
    x = analysis
    z = x.zhongyuan_analysis
    m = x.missing_analysis
    body = f"""
        <h2>综述（面维度）</h2>
        <blockquote>{x.task_count} 个街区，{x.prime.total} 个面，{x.segment.total} 个观察点，{x.image_count} 张图片</blockquote>
        <ul>
            <li>纯粹全景的面：{x.prime.panorama} ({x.prime.panorama / x.prime.total:.2%})</li>
            <li>参杂众源的面：{x.prime.mixed} ({x.prime.mixed / x.prime.total:.2%})</li>
            <li>缺失图片的面：{x.prime.missing} ({x.prime.missing / x.prime.total:.2%}) 将以“无法核实”继续流转</li>
        </ul>

        <h2>众源分析（观察点维度）</h2>
        <blockquote>众源使用率： {x.segment.zhongyuan / x.segment.total:.2%} ({x.segment.zhongyuan}/{x.segment.total})</blockquote>
        <ul>
            <li>全景缺失：{z.missing} ({z.missing / z.total:.2%})</li>
            <li>暗图过滤：{z.dark} ({z.dark / z.total:.2%})</li>
            <li>高架过滤：{z.viaduct} ({z.viaduct / z.total:.2%})</li>
        </ul>

        <h2>缺图分析（观察点维度）</h2>
        <blockquote>缺图率：{m.total / x.segment.total:.2%} ({m.total}/{x.segment.total})</blockquote>
        <ul>
            <li>资料缺失：{m.missing} ({m.missing / m.total:.2%})</li>
            <li>水系边界：{m.no_road} ({m.no_road / m.total:.2%})</li>
            <li>暗图过滤：{m.dark} ({m.dark / m.total:.2%})</li>
            <li>高架过滤：{m.viaduct} ({m.viaduct / m.total:.2%})</li>
        </ul>
    """
    return body


def get_approval_pull_detail(analysis: pull_info.Analysis):
    """
    获取准出 icafe 卡片详情（html）
    """
    x = analysis
    s = x.segment
    new_count = x.verified_count - x.duplicate_verified_count
    duplicate_count = x.duplicate_verified_count
    body = f"""
        <h2>面维度</h2>
        <ul>
            <li>原始面：{x.prime_count}</li>
            <li>核实面：{x.verified_count}（新增：{new_count}，重复：{duplicate_count}）</li>
        </ul>
        <h2>观察点维度</h2>
        <blockquote>回收率：{s.pull / s.total:.2%} ({s.pull}/{s.total})</blockquote>
        <ul>
            <li>门前可停：{s.yes}</li>
            <li>不可停车：{s.no}</li>
            <li>无法核实：{s.not_sure}</li>
        </ul>
    """
    return body


def get_approval_push_fields(
    plan_id: str,
    batch: str,
    batch_image: str,
    batch_diff: str,
    missing_url: str,
    owner="zhangdingping_cd,mayuqing_cd,yanghuiyu_cd",
    rd_owner="zhangdingping_cd",
    pm_owner="mayuqing_cd,yanghuiyu_cd",
) -> dict[str, str]:
    """
    获取准入 icafe 卡片字段
    """
    return {
        "所属计划": "2025Q1",
        "负责人": owner,
        "RD负责人": rd_owner,
        "PM负责人": pm_owner,
        "计划ID": plan_id,
        "任务批次": batch,
        "掘金批次": batch_image,
        "差分批次": batch_diff,
        "缺失分析清单": missing_url,
    }


def get_approval_pull_fields(
    plan_id: str,
    batch: str,
    batch_image: str,
    batch_diff: str,
    batch_polygon: str,
    polygon_url: str,
    owner="zhangdingping_cd,zhengcheng_cd,mayuqing_cd,yanghuiyu_cd",
    rd_owner="zhangdingping_cd",
    pm_owner="mayuqing_cd,yanghuiyu_cd",
) -> dict[str, str]:
    """
    获取准出 icafe 卡片字段
    """
    return {
        "所属计划": "2025Q1",
        "负责人": owner,
        "RD负责人": rd_owner,
        "PM负责人": pm_owner,
        "计划ID": plan_id,
        "任务批次": batch,
        "掘金批次": batch_image,
        "差分批次": batch_diff,
        "核实面批次": batch_polygon,
        "核实面清单": polygon_url,
    }


def create_issue(card_type: str, title: str, detail: str, fields: dict[str, str]):
    """
    创建 icafe 卡片
    """
    url = f"http://icafeapi.baidu-int.com/api/v2/space/{SPACE_ID}/issue/new"
    i_headers = {"Content-Type": "application/json"}
    values = {
        "username": USERNAME,
        "password": PASSWORD,
        "issues": [
            {
                "title": title,
                "detail": detail,
                "type": card_type,
                "fields": fields,
            }
        ],
    }
    resp = requests.post(url, json=values, headers=i_headers)
    json_obj = resp.json()
    if json_obj["status"] == 200:
        return json_obj["issues"][0]["url"]
    else:
        logger.error(json_obj)
        return None


def fetch_issues(card_type: str, status: Union[str, Iterable[str]]):
    """
    获取所有指定类型和状态的卡片
    https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/NbX2gitgSF/ZO_R4gUBoyeLJX
    """

    def parse_prop(props: list[dict], prop_name: str) -> str:
        return next((x["value"] for x in props if x["propertyName"] == prop_name), "")

    def parse_card(x: dict):
        props = x["properties"]
        return ICafeCard(
            sequence=x["sequence"],
            title=x["title"],
            plan_id=parse_prop(props, "计划ID"),
            batch=parse_prop(props, "任务批次"),
            batch_image=parse_prop(props, "掘金批次"),
            batch_diff=parse_prop(props, "差分批次").split(","),
            batch_polygon=parse_prop(props, "核实面批次"),
            created_at=datetime.strptime(x["createdTime"], "%Y-%m-%d %H:%M:%S"),
            detail=x["detail"],
        )

    url = f"http://icafeapi.baidu-int.com/api/spaces/{SPACE_ID}/cards"
    status = [status] if isinstance(status, str) else status
    status = ",".join(status)
    status = f"({status})"

    page = 1
    cards = []
    while True:
        params = {
            "u": USERNAME,
            "pw": PASSWORD,
            "iql": f"类型 = {card_type} AND 流程状态 IN {status}",
            "page": page,
            "showDetail": "true",
        }
        try:
            resp = requests.get(url, params=params)
            json_obj = resp.json()
            cards += [parse_card(x) for x in json_obj["cards"] if "cards" in json_obj]

            page += 1
            if page > json_obj["pageSize"]:
                break

        except Exception as e:
            logger.error(e)
            time.sleep(1)

    return cards


def modify_issue_status(sequence: int, target_status: str):
    """
    修改卡片（返回值好像没什么用，即便传错状态，修改失败，也是返回：`{'code': 200, 'message': 'OK, ', 'result': 'success'}`）
    """
    url = f"http://icafeapi.baidu-int.com/api/spaces/{SPACE_ID}/cards/{sequence}"
    params = {
        "u": USERNAME,
        "pw": PASSWORD,
    }
    values = {"fields": [f"流程状态={target_status}"]}
    resp = requests.post(url, params=params, data=values)
    return resp.json()
