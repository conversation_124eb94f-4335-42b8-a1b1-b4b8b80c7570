"""
掘金准出报告生成器
"""
from dataclasses import dataclass
from pathlib import Path

from src.parking.storefront import prime_tag
from src.parking.storefront.verify import verified_flow
from src.tools import tsv


@dataclass
class SegmentInfo:
    """
    观察点信息
    """

    push: int
    pull: int
    yes: int
    no: int
    not_sure: int

    @property
    def total(self):
        """
        总量
        """
        return self.yes + self.no + self.not_sure


@dataclass
class Analysis:
    """
    总分析结论
    """

    prime_count: int
    verified_count: int
    duplicate_verified_count: int  # 重复的核实面
    segment: SegmentInfo


def get_analysis(polygon_dir: Path):
    """
    获取掘金准出分析结果
    """
    verified_count = len(list(tsv.read_tsv(polygon_dir / "verified.tsv")))
    prime_count = len(list(tsv.read_tsv(polygon_dir / "primes.tsv")))
    segments = [int(x[-1]) for x in tsv.read_tsv(polygon_dir / "segment.tsv")]
    valid_conclusions = (verified_flow.CONCLUSION_NOT_SURE, verified_flow.CONCLUSION_YES, verified_flow.CONCLUSION_NO)
    tag_path = polygon_dir / prime_tag.FILE_NAME_TAG
    duplicate_count = sum(1 for x in tsv.read_tsv(tag_path) if x[1] == "duplicate") if tag_path.exists() else 0

    return Analysis(
        prime_count=prime_count,
        verified_count=verified_count,
        duplicate_verified_count=duplicate_count,
        segment=SegmentInfo(
            push=len(segments),
            pull=sum(1 for x in segments if x in valid_conclusions),
            yes=sum(1 for x in segments if x == verified_flow.CONCLUSION_YES),
            no=sum(1 for x in segments if x == verified_flow.CONCLUSION_NO),
            not_sure=sum(1 for x in segments if x == verified_flow.CONCLUSION_NOT_SURE),
        ),
    )
