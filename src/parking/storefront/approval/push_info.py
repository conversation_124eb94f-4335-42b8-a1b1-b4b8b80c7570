"""
掘金准入报告生成器
"""
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path

from src.tools import linq, tsv


@dataclass
class SegmentInfo:
    """
    观察点信息
    """

    panorama: int
    zhongyuan: int
    empty: int

    @property
    def total(self):
        """
        总量
        """
        return self.panorama + self.zhongyuan + self.empty


@dataclass
class PrimeInfo:
    """
    原始面信息
    """

    panorama: int
    mixed: int
    missing: int

    @property
    def total(self):
        """
        总量
        """
        return self.panorama + self.mixed + self.missing


@dataclass
class ZhongyuanAnalysis:
    """
    众源图片分析
    """

    missing: int
    dark: int
    viaduct: int

    @property
    def total(self):
        """
        总量
        """
        return self.missing + self.dark + self.viaduct


@dataclass
class MissingAnalysis:
    missing: int
    no_road: int
    dark: int
    viaduct: int

    @property
    def total(self):
        return self.missing + self.no_road + self.dark + self.viaduct


@dataclass
class Analysis:
    """
    总分析结论
    """

    task_count: int
    image_count: int
    prime: PrimeInfo
    segment: SegmentInfo
    zhongyuan_analysis: ZhongyuanAnalysis
    missing_analysis: MissingAnalysis


def get_analysis(image_dir: Path):
    """
    获取分析结果
    """
    images = [(row[0], row[1]) for path in image_dir.glob("image.*.tsv") for row in tsv.read_tsv(path)]
    task_count = len({x for x, _ in images})
    image_count = _get_image_count(image_dir)
    return Analysis(
        task_count=task_count,
        image_count=image_count,
        prime=_get_prime_info(image_dir),
        segment=_get_segment_info(image_dir),
        zhongyuan_analysis=_get_no_panorama_analysis(image_dir),
        missing_analysis=_get_no_image_analysis(image_dir),
    )


def _get_prime_info(image_dir: Path):
    images = [(row[1], row[6]) for path in image_dir.glob("image.*.tsv") for row in tsv.read_tsv(path)]
    grouped_images = linq.group_by(images, key=lambda x: x[0], value=lambda x: x[1])

    counter = defaultdict(int)
    for prime_id, types in grouped_images.items():
        types = set(types)
        if "<empty>" in types:
            counter["missing"] += 1
        elif len(types) == 1 and "panorama" in types:
            counter["panorama"] += 1
        else:
            counter["mixed"] += 1

    return PrimeInfo(panorama=counter["panorama"], mixed=counter["mixed"], missing=counter["missing"])


def _get_segment_info(image_dir: Path):
    images = [(row[3], row[6]) for path in image_dir.glob("image.*.tsv") for row in tsv.read_tsv(path)]
    grouped_images = linq.group_by(images, key=lambda x: x[1], value=lambda x: x[0])
    zhongyuan, panorama, empty = (
        set(grouped_images["zhongyuan"]),
        set(grouped_images["panorama"]),
        set(grouped_images["<empty>"]),
    )
    return SegmentInfo(panorama=len(panorama), zhongyuan=len(zhongyuan), empty=len(empty))


def _get_no_panorama_analysis(image_dir: Path):
    reasons = [(x[-2], x[-1]) for x in tsv.read_tsv(image_dir / "missing_reason.tsv")]
    reasons = [r.split(",") for t, r in reasons if t == "no_panorama"]
    counter = defaultdict(int)
    for r in reasons:
        if "panorama_dark" in r:
            counter["panorama_dark"] += 1
        elif "panorama_viaduct" in r:
            counter["panorama_viaduct"] += 1
        else:
            counter["other"] += 1

    return ZhongyuanAnalysis(
        missing=counter["other"], dark=counter["panorama_dark"], viaduct=counter["panorama_viaduct"]
    )


def _get_no_image_analysis(image_dir: Path):
    reasons = [(x[-2], x[-1]) for x in tsv.read_tsv(image_dir / "missing_reason.tsv")]
    reasons = [r.split(",") for t, r in reasons if t == "no_image"]
    counter = defaultdict(int)
    for reason in reasons:
        if any("dark" in r for r in reason):
            counter["dark"] += 1
        elif any("viaduct" in r for r in reason):
            counter["viaduct"] += 1
        elif "no_road" in reason:
            counter["no_road"] += 1
        else:
            counter["other"] += 1

    return MissingAnalysis(
        missing=counter["other"], no_road=counter["no_road"], dark=counter["dark"], viaduct=counter["viaduct"]
    )


def _get_image_count(image_dir: Path) -> int:
    side_dir = image_dir / "side_view"
    if side_dir.exists():
        return sum(1 for _ in side_dir.glob("*.jpg"))

    side_path = image_dir / "side_view.txt"
    if side_path.exists():
        return sum(1 for _ in tsv.read_tsv(side_path))

    return -1
