"""
门前面质检项
1、检查是否存在同批次待上线面互相压盖
"""
import dataclasses
import numpy as np
import shapely.wkt
import shapely.geometry

from enum import Enum

from src.parking.storefront.check.sql_utils import (
    get_waiting_check_park_area_by_id,
    get_not_self_storefront_park_bids,
)
from src.parking.storefront.check.context import Context
from src.parking.storefront.diff import polygon_differ
from src.parking.storefront.check import sql_utils

METER = 0.9e-5


class Area_failed_Reason(Enum):
    """
    质检状态
    """

    Empty = ""
    Exist_Cover_Online = "存在压盖线上面："
    Exist_Cover_Offline = "存在压盖线下面："
    FACE_TOO_SHORT = "face too short:"
    Exist_Cover_Other_Online_Only = "仅存在压盖其他渠道上线的门前停车场："
    Exist_Cover_LD = "压盖LD："
    Exist_Sharp_Angle = '存在锐角：'
    # !!!新增了其他值域，需要维护一下 only_cover_other_online_failed

    @staticmethod
    def only_cover_other_online_failed(reason: str) -> bool:
        """
        仅存在压盖其他渠道上线的门前停车场，返回 True
        """
        if Area_failed_Reason.Exist_Cover_Other_Online_Only.value not in reason:
            return False
        others = [
            Area_failed_Reason.Exist_Cover_Offline.value,
            Area_failed_Reason.FACE_TOO_SHORT.value,
            Area_failed_Reason.Exist_Cover_Online.value,
            Area_failed_Reason.Exist_Cover_LD.value,
            Area_failed_Reason.Exist_Sharp_Angle.value,
        ]
        for item in others:
            if item in reason:
                return False
        return True


@dataclasses.dataclass
class Park:
    """
    需要质检的面
    """
    bid: str
    geom: str
    park_id: int

    @staticmethod
    def from_ach(park: dict) -> 'Park':
        """
        成果转换
        """
        return Park(
            bid=park['bid'],
            geom=park['geom'],
            park_id=park['id'],
        )

    @staticmethod
    def from_online(park: dict) -> 'Park':
        """
        线上成果转换
        """
        return Park(
            bid=park['bid'],
            geom=park['geom'],
            park_id=park['id'],
        )


def _get_ach_need_checked_park(ctx: Context, park_bid: str) -> Park:
    """
    获取成果需要质检的停车场
    """
    ach = sql_utils.get_waiting_check_park_area_by_id_v2(ctx, park_bid)
    ach_dict = {
        'bid': ach[0],
        'geom': ach[1],
        'id': ach[4],
    }
    return Park.from_ach(ach_dict)


def _get_online_need_checked_park(ctx: Context, park_bid: str) -> Park:
    """
    获取线上成果需要质检的停车场
    """
    online = sql_utils.get_online_park(park_bid)
    park_wkt_res = get_waiting_check_park_area_by_id(ctx, park_bid)
    online_dict = {
        'bid': online[0],
        'geom': online[1],
        # 'id': park_wkt_res[2] if len(park_wkt_res) > 0 else -1,
        'id': -1 if not park_wkt_res else park_wkt_res[2],
    }
    return Park.from_online(online_dict)


def get_need_checked_park(ctx: Context, park_bid: str) -> Park:
    """
    获取需要质检的停车场
    """
    if ctx.is_check_online():
        return _get_online_need_checked_park(ctx, park_bid)
    if ctx.is_check_ach():
        return _get_ach_need_checked_park(ctx, park_bid)
    raise Exception(f"{ctx.check_mode} 不支持")


def check_area(ctx: Context, park_bid):
    """
    检查门前面
    """
    park = get_need_checked_park(ctx, park_bid)

    checked_cover, reason_cover = check_cover(ctx, park)
    checked_shaped, reason_shape = check_face_shape(ctx, park)

    # checked_cover_ld, reason_cover_ld = check_cover_ld_road(ctx, park)
    checked_cover_ld, reason_cover_ld = True, Area_failed_Reason.Empty.value

    checked_sharp_angle, reason_angle = check_has_sharp_angles(ctx, park)
    checked_status = checked_cover and checked_shaped and checked_cover_ld and checked_sharp_angle
    reason = f"{reason_cover},{reason_shape},{reason_cover_ld},{reason_angle}"
    return checked_status, reason


def check_cover(ctx: Context, park: Park):
    """
    检查是否存在面压盖
    """
    offline_passed, offline_covered_park_ids = True, []
    if ctx.is_check_ach():
        offline_passed, offline_covered_park_ids = check_cover_with_offline(ctx, park)
    online_passed, online_covered_park_ids = check_cover_with_online(ctx, park)
    if offline_passed and online_passed:
        return True, Area_failed_Reason.Empty.value

    def _join_ids(ids) -> str:
        return ",".join(map(str, ids))

    reasons = []
    if not offline_passed:
        reasons.append(f"{Area_failed_Reason.Exist_Cover_Offline.value}{_join_ids(offline_covered_park_ids)}")
    if not online_passed:
        other_park_bids = get_not_self_storefront_park_bids(online_covered_park_ids)
        if len(other_park_bids) == len(set(online_covered_park_ids)):
            text = Area_failed_Reason.Exist_Cover_Other_Online_Only.value
        else:
            text = Area_failed_Reason.Exist_Cover_Online.value
        reasons.append(f"{text}{_join_ids(online_covered_park_ids)}")
    return False, ' || '.join(reasons)


def check_cover_with_offline(ctx: Context, park: Park):
    """
    检查是否存在同批次待上线面互相压盖
    """
    a_polygon = polygon_differ.Polygon(
        face_id=park.bid,
        geom=park.geom,
    )

    def get_b_polygons(wkt):
        _res = sql_utils.get_intersects_parks(ctx, park.bid, wkt)
        b_polygons = []
        for _item in _res:
            b_polygons.append(
                polygon_differ.Polygon(
                    face_id=_item[0],
                    geom=_item[1],
                )
            )
        return b_polygons

    diff_res = polygon_differ.diff_center([a_polygon], 30 * 1e-5, get_b_polygons)
    item_res = diff_res[0]
    if len(item_res.b_face_ids) > 0:
        return False, item_res.b_face_ids
    return True, []


def check_cover_with_online(ctx: Context, park: Park):
    """
    检查是否存在同批次线上面互相压盖
    """
    a_polygon = polygon_differ.Polygon(
        face_id=park.bid,
        geom=park.geom,
    )

    def get_b_polygons(wkt):
        _res = sql_utils.get_intersects_online_parks(park.bid, wkt)
        b_polygons = []
        for _item in _res:
            b_polygons.append(
                polygon_differ.Polygon(
                    face_id=_item[0],
                    geom=_item[1],
                )
            )
        return b_polygons

    diff_res = polygon_differ.diff_center([a_polygon], 30 * 1e-5, get_b_polygons)
    item_res = diff_res[0]
    if len(item_res.b_face_ids) == 0:
        return True, []
    bids = item_res.b_face_ids + [park.bid]
    if sql_utils.area_allow_overlap(bids):
        print(f"{bids} 允许压盖")
        return True, []
    return False, item_res.b_face_ids


def check_face_shape(ctx: Context, park: Park):
    """
    检查面的形状
    """
    MIN_FACE_LENGTH = 20
    polygon = shapely.wkt.loads(park.geom)
    valid_res = sql_utils.check_valid_polygon(park.geom)
    if not valid_res[0]:
        return False, valid_res[1]
    minx, miny, maxx, maxy = polygon.bounds
    dis_x = (maxx - minx) / METER
    dis_y = (maxy - miny) / METER

    face_length = max(dis_x, dis_y)
    if face_length >= MIN_FACE_LENGTH:
        return True, Area_failed_Reason.Empty.value

    # 面短，若核实过，那么可以免检查
    reason = 'Face_Too_Short'
    park_wkt_res = get_waiting_check_park_area_by_id(ctx, park.bid)
    print(park_wkt_res)
    if park_wkt_res and sql_utils.park_id_area_repaired(park_wkt_res[2], reason):
        print(f"{park.bid}, {reason}, 但被修复过，可免检")
        return True, Area_failed_Reason.Empty.value
    return False, Area_failed_Reason.FACE_TOO_SHORT.value


def check_cover_ld_road(ctx: Context, park: Park):
    """
    检查面是否压盖 LD
    """
    park_wkt = park.geom

    cover_boundary = sql_utils.get_cover_nav_lane_boundary(park_wkt)
    if len(cover_boundary) > 0:
        return False, f"{Area_failed_Reason.Exist_Cover_LD.value}boundary, {str(cover_boundary)}"

    cover_road_pg = sql_utils.get_cover_nav_lane_road_pg(park_wkt)
    if len(cover_road_pg) > 0:
        return False, f"{Area_failed_Reason.Exist_Cover_LD.value}road_pg, {str(cover_road_pg)}"
    return True, Area_failed_Reason.Empty.value


def calculate_angle(p1, p2, p3):
    """
    计算两条向量之间的夹角
    """
    v1 = np.array(p1) - np.array(p2)
    v2 = np.array(p3) - np.array(p2)
    dot_product = np.dot(v1, v2)
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    cos_angle = dot_product / (norm_v1 * norm_v2)
    angle = np.arccos(cos_angle)
    return np.degrees(angle)


def has_sharp_angles(polygon, threshold=20):
    """
    有锐角返回 True
    """
    coords = np.array(polygon.exterior.coords[:-1])  # 去除封闭的最后一个点（和第一个重复）
    for i in range(1, len(coords) - 1):
        angle = calculate_angle(coords[i - 1], coords[i], coords[i + 1])

        # 如果夹角小于指定阈值，认为是锐角
        if angle < threshold:
            return True, shapely.geometry.Point(coords[i]).wkt
    return False, ""


def check_has_sharp_angles(ctx: Context, park: Park):
    """
    检查是否有锐角
    """
    park_wkt = park.geom
    polygon = shapely.wkt.loads(park_wkt)

    polygons = []
    if polygon.geom_type == 'Polygon':
        polygons.append(polygon)
    else:
        polygons = list(polygon.geoms)

    for a_polygon in polygons:
        has, point = has_sharp_angles(a_polygon, 15)
        if has:
            return False, f"{Area_failed_Reason.Exist_Sharp_Angle.value}{point}"
    return True, Area_failed_Reason.Empty.value

