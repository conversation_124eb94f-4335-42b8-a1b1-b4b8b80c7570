"""
质检环节
"""
import argparse
import csv
import logging
import multiprocessing
import time
from typing import Union, List

import dataclasses
from tqdm import tqdm
from enum import Enum

from src.parking.storefront.check.check_road_relation import check_road_relations, check_link_blocked_by_park_bids
from src.parking.storefront.check.check_area import check_area, Area_failed_Reason, check_cover_with_online, \
    get_need_checked_park
from src.parking.storefront.check.sql_utils import (
    get_waiting_check_parks_ids,
    update_prod_parking_checked_info,
    update_prod_access_checked_info,
    update_prod_parking_checked_info_with_remove_bids,
    get_not_self_storefront_park_bids,
    recover_access_status,
    get_online_park_bids,
    add_park_offline,
    get_waiting_check_park_area_by_id,
    update_online_parking_checked_info,
)
from src.parking.storefront.check.context import Context, gen_ctx, gen_online_ctx
from src.parking.storefront.flow.monitor.util import send_hi, upload_file


class Check_Status(Enum):
    """
    质检状态
    """

    CHECK_SUCCEED = "CHECK_SUCCEED"  # 质检通过
    CHECK_FAILED = "CHECK_FAILED"  # 质检不通过
    ACCESS_CHECK_FAILED = "ACCESS_CHECK_FAILED"  # 出入口质检不通过
    READY = 'READY'
    AREA_CHECK_FAILED = 'AREA_CHECK_FAILED'


@dataclasses.dataclass
class ParkAndAccessCheckedParam:
    """
    面和口质检参数
    """
    ctx: Context
    park_bid: str
    blocks: list


@dataclasses.dataclass
class ParkAndAccessCheckedResponse:
    """
    面和口质检结果
    """
    park_bid: str

    area_check_passed: bool
    area_failed_reason: str

    access_check_passed: bool
    access_failed_reason_list: list

    def are_ok(self) -> bool:
        """
        检查没有问题
        """
        return self.area_check_passed and self.access_check_passed

    def area_ok_but_access_no(self) -> bool:
        """
        面没问题，但口有问题
        """
        return self.area_check_passed and not self.access_check_passed

    def area_no_but_access_ok(self) -> bool:
        """
        面有问题，但口没有问题
        """
        return not self.area_check_passed and self.access_check_passed


def check_park_and_access_center(param: ParkAndAccessCheckedParam) -> Union[ParkAndAccessCheckedResponse, None]:
    """
    面和口质检中心
    """
    try:
        print(f"开始质检：{param.park_bid}")
        area_check_passed, area_failed_reason = check_area(param.ctx, param.park_bid)
        access_check_passed, access_failed_reason_list = check_road_relations(param.ctx, param.park_bid, param.blocks)
        return ParkAndAccessCheckedResponse(
            area_check_passed=area_check_passed,
            area_failed_reason=area_failed_reason,
            access_check_passed=access_check_passed,
            access_failed_reason_list=access_failed_reason_list,
            park_bid=param.park_bid,
        )
    except Exception as e:
        logging.exception(e)
        print(f"{param.park_bid} 质检时异常，暂不处理")
        return None


def save_ach_checked_res(ctx: Context, response: ParkAndAccessCheckedResponse):
    """
    写入成果质检结果
    """
    park_bid = response.park_bid
    area_failed_reason = response.area_failed_reason
    access_failed_reason_list = response.access_failed_reason_list
    checker = 'offline'

    # 先恢复口的状态
    recover_access_status(park_bid)
    if response.are_ok():
        # 1、全部质检通过
        update_prod_parking_checked_info(ctx, park_bid, Check_Status.CHECK_SUCCEED.value,
                                         Check_Status.CHECK_SUCCEED.value, checker)
    elif response.area_ok_but_access_no():
        # 2、面质检通过，出入口质检不通过
        update_prod_parking_checked_info(
            ctx, park_bid, Check_Status.CHECK_FAILED.value, Check_Status.ACCESS_CHECK_FAILED.value, checker
        )
        for item in access_failed_reason_list:
            access_id, reason = item["access_id"], item["reason"]
            if int(access_id) == -1:
                continue
            update_prod_access_checked_info(access_id, Check_Status.CHECK_FAILED.value, reason, checker)
    elif response.area_no_but_access_ok():
        # 3、面质检不通过，出入口质检通过
        if Area_failed_Reason.only_cover_other_online_failed(area_failed_reason):
            # 仅存在压盖其他渠道上线的门前停车场
            park = get_need_checked_park(ctx, park_bid)
            _, covered_park_bids = check_cover_with_online(ctx, park)
            other_park_bids = get_not_self_storefront_park_bids(covered_park_bids)
            update_prod_parking_checked_info_with_remove_bids(
                ctx,
                park_bid,
                Check_Status.CHECK_SUCCEED.value,
                area_failed_reason,
                other_park_bids,
            )
        else:
            update_prod_parking_checked_info(ctx, park_bid, Check_Status.CHECK_FAILED.value, area_failed_reason,
                                             checker)
    else:
        # 4、面、出入口都质检失败
        update_prod_parking_checked_info(ctx, park_bid, Check_Status.CHECK_FAILED.value, area_failed_reason, checker)
        for item in access_failed_reason_list:
            access_id, reason = item["access_id"], item["reason"]
            update_prod_access_checked_info(access_id, Check_Status.CHECK_FAILED.value, reason, checker)


def _get_cur_time(_format: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    获取当前时间
    """
    return time.strftime(_format, time.localtime(time.time()))


def save_online_checked_to_csv(ctx: Context, responses: List[ParkAndAccessCheckedResponse]):
    """
    保存线上结果
    """
    dst = f"./tmp/online_checked_{_get_cur_time('%Y%m%d%H')}.csv"
    num = 0
    with open(dst, 'w') as hdw:
        writer = csv.writer(hdw)
        header = ['park_bid', '面是否质检异常', 'access_bid', '口质检是否异常', 'check_memo']
        writer.writerow(header)
        for resp in responses:
            if resp.are_ok():
                print(f"{resp.park_bid} 质检通过")
                continue
            num += 1
            area_failed_reason = resp.area_failed_reason
            access_failed_reason_list = resp.access_failed_reason_list
            if resp.area_ok_but_access_no():
                # 2、面质检通过，出入口质检不通过
                for item in access_failed_reason_list:
                    access_id, reason = item["access_id"], item["reason"]
                    if int(access_id) == -1:
                        continue
                    writer.writerow([
                        resp.park_bid, '否', access_id, '是', reason
                    ])
            elif resp.area_no_but_access_ok():
                # 3、面质检不通过，出入口质检通过
                if Area_failed_Reason.only_cover_other_online_failed(area_failed_reason):
                    writer.writerow([
                        resp.park_bid, '否', '', '否', area_failed_reason
                    ])
                else:
                    writer.writerow([
                        resp.park_bid, '是', '', '否', area_failed_reason
                    ])
            else:
                # 4、面、出入口都质检失败
                writer.writerow([
                    resp.park_bid, '是', '', '否', area_failed_reason
                ])
                for item in access_failed_reason_list:
                    access_id, reason = item["access_id"], item["reason"]
                    writer.writerow([
                        resp.park_bid, '否', access_id, '是', reason
                    ])
    print(dst)
    url = upload_file(dst)
    msg = f"{_get_cur_time('%Y-%m-%d')} 线上质检不通过量：{num}; 详情：{url}"
    print(msg)
    send_hi(msg)


def save_online_checked_to_db(ctx: Context, responses: List[ParkAndAccessCheckedResponse]):
    """
    保存线上质检结果
    """
    checker = 'online'
    unexpected = []
    for response in responses:
        park_bid = response.park_bid
        area_failed_reason = response.area_failed_reason
        access_failed_reason_list = response.access_failed_reason_list
        if response.are_ok():
            # 1、全部质检通过
            continue

        # 如果线上口在线下不存在，先不处理
        _unexpected = []
        for item in access_failed_reason_list:
            access_id, reason = item["access_id"], item["reason"]
            if int(access_id) == -1:
                print(f"{park_bid} 存在没有对应的线下出入口")
                _unexpected.append(f"{park_bid} 存在没有对应的线下出入口")
        if len(_unexpected) > 0:
            unexpected += _unexpected
            continue

        # 对应的线下状态要是 ONLINE，用来去重
        park_ach = get_waiting_check_park_area_by_id(ctx, park_bid)
        if not park_ach or park_ach[3] != 'ONLINE':
            print(f"{park_bid} 对应的线下状态不是 ONLINE; 不再保存质检结果")
            continue
        if response.area_ok_but_access_no():
            # 2、面质检通过，出入口质检不通过
            update_online_parking_checked_info(
                park_bid, Check_Status.CHECK_FAILED.value, Check_Status.ACCESS_CHECK_FAILED.value, checker
            )
            for item in access_failed_reason_list:
                access_id, reason = item["access_id"], item["reason"]
                update_prod_access_checked_info(access_id, Check_Status.CHECK_FAILED.value, reason, checker)
        elif response.area_no_but_access_ok():
            # 3、面质检不通过，出入口质检通过
            if Area_failed_Reason.only_cover_other_online_failed(area_failed_reason):
                # 仅存在压盖其他渠道上线的门前停车场
                park = get_need_checked_park(ctx, park_bid)
                _, covered_park_bids = check_cover_with_online(ctx, park)
                other_park_bids = get_not_self_storefront_park_bids(covered_park_bids)
                for _other_park_bid in other_park_bids:
                    add_park_offline(_other_park_bid, 'online_checker', area_failed_reason)
            else:
                update_online_parking_checked_info(park_bid, Check_Status.CHECK_FAILED.value, area_failed_reason,
                                                   checker)
        else:
            # 4、面、出入口都质检失败
            update_online_parking_checked_info(park_bid, Check_Status.CHECK_FAILED.value, area_failed_reason,
                                               checker)
            for item in access_failed_reason_list:
                access_id, reason = item["access_id"], item["reason"]
                update_prod_access_checked_info(access_id, Check_Status.CHECK_FAILED.value, reason, checker)
    print(unexpected)


def check_park(ctx: Context, park_bid):
    """
    质检停车场
    """
    area_check_passed, area_failed_reason = check_area(ctx, park_bid)
    checker = 'offline'

    def _check_ok():
        update_prod_parking_checked_info(ctx, park_bid, Check_Status.READY.value,
                                         Check_Status.READY.value, checker)

    def _check_fail():
        update_prod_parking_checked_info(ctx, park_bid, Check_Status.AREA_CHECK_FAILED.value, area_failed_reason,
                                         checker)

    if area_check_passed or Area_failed_Reason.only_cover_other_online_failed(area_failed_reason):
        # 质检通过, 或者质检不通过，如果仅压盖其他渠道的门前，那么也算通过
        _check_ok()
    else:
        _check_fail()


def check_park_access_by_status(status: str, where: str, multi: int = 5):
    """
    质检待上线
    """
    ctx = gen_ctx(status, where)
    parks_ids = get_waiting_check_parks_ids(ctx)
    if not parks_ids:
        print(f"没有待质检的数据")
        return
    parks_ids = [item[0] for item in parks_ids]
    check_park_and_access_by_park_bids(ctx, parks_ids, multi)


def check_online_park(where: str):
    """
    质检线上的停车场
    """
    ctx = gen_online_ctx(where)
    bids = get_online_park_bids(ctx)
    check_park_and_access_by_park_bids(ctx, bids, multi=30)


def check_park_and_access_by_park_bids(ctx: Context, parks_bids: list, multi: int = 5):
    """
    质检面和出入口
    """
    print(f"停车场数量：{len(parks_bids)}")
    park_bid2blocks = check_link_blocked_by_park_bids(ctx, parks_bids)
    params = []
    for a_park_bid in tqdm(parks_bids):
        params.append(ParkAndAccessCheckedParam(
            ctx=ctx,
            park_bid=a_park_bid,
            blocks=park_bid2blocks[a_park_bid] if a_park_bid in park_bid2blocks else [],
        ))
    multi = min(multi, len(params))
    print(f"并发度：{multi}")
    with multiprocessing.Pool(processes=multi) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.map(check_park_and_access_center, params)

    results = [res for res in results if isinstance(res, ParkAndAccessCheckedResponse)]
    if ctx.is_check_ach():
        for response in results:
            save_ach_checked_res(ctx, response)
    else:
        save_online_checked_to_csv(ctx, results)
        save_online_checked_to_db(ctx, results)


def check_inited_bid(where: str):
    """
    bid 生成好之后，质检面是否达标
    """
    ctx = gen_ctx('INITED_BID', where)
    parks_ids = get_waiting_check_parks_ids(ctx)
    print(f"停车场数量：{len(parks_ids)}")
    white_ids = []
    for park_id in parks_ids:
        a_park_id = park_id[0]
        if len(white_ids) > 0 and a_park_id not in white_ids:
            continue
        try:
            check_park(ctx, a_park_id)
        except Exception as e:
            logging.exception(e)
            print(f"{a_park_id} 质检时异常，暂不处理")


def run():
    """
    run
    质检状态为GATE_MATCHED的门前停车场
    """
    where = ''
    if ARGS.where is not None:
        where = str(ARGS.where)
    fns = str(ARGS.fns)
    if fns == 'check_ready_online':
        check_park_access_by_status('READY_ONLINE', where)
    elif fns == 'check_park_area':
        check_inited_bid(where)
    elif fns == 'check_failed':
        if where == '':
            where = " 1 = 1 "
        where = f"{where} and checker = 'offline' "
        check_park_access_by_status('CHECK_FAILED', where, multi=5)
    elif fns == 'check_succeed':
        check_park_access_by_status('CHECK_SUCCEED', where)
    elif fns == 'check_online_park':
        check_online_park(where)


if __name__ == "__main__":
    """
    main
    """
    parser = argparse.ArgumentParser(description='门前停车场质检中心')
    parser.add_argument(
        '--fns',
        type=str,
        required=True,
        help="check_ready_online: 质检待上线; check_park_area: 质检停车场面"
    )
    parser.add_argument(
        '--where',
        type=str,
        help="质检的条件"
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    run()
