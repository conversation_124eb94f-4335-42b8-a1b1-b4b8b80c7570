"""
出入口质检项
1、检查出入口关联大门是否是紧急大门、出口大门
2、检查出入口是否重复
3、检查出入口与对应LINK的距离是否过远
4、检查出入口关联的LINK是否为步行路
5、检查出入口与门前面的距离是否过远
6、检查出入口关联的LINK是否为出口方向
"""
import json
import logging
from typing import Union, List

import dataclasses
import jwt
import requests
import time
import shapely.wkt
from enum import Enum
from src.parking_production import query
from shapely.geometry import Point

from src.parking.storefront.check import sql_utils
from src.parking.storefront.check.sql_utils import (
    get_accesses_by_park_id,
    get_node_geom,
    get_link_kind,
    get_link_geom,
    get_online_road_relations,
    get_park_bid_by_id,
    is_in_aoi,
    get_long_node_id_by_short,
)
from src.parking.storefront.check.context import Context
from src.parking.storefront.check.check_area import Park, get_need_checked_park

METER = 0.9e-5


class Access_failed_Reason(Enum):
    """
    质检状态
    """

    Empty = ""
    Re_Error_Gate = "出入口关联大门是紧急大门、出口大门"
    Entrance_Duplicate = "出入口存在重复"
    Node_Far_Away_To_Link = "出入口与对应LINK的距离过远：50m"
    Link_Is_Ped_Road = "出入口关联的LINK为步行路"
    Node_Far_Away_To_Area = "出入口与门前面的距离过远：30m"
    Re_Link_Is_Out = "出入口关联的LINK为出口方向"
    Node_Link_Dup = "node 和 link 重复"
    Node_Link_Invalid = "node 或 link 失效"
    No_Access = "无出入口"
    Out_Node_Relation_Link = "关联的link上有出口node"
    Link_Have_Block = "link上有阻断信息: "
    Node_With_Barricade = '出入口有路障'
    Err_Work_Manual_Memo = '作业异常备注: '
    Some_Road_Link = 'link和link在一条规划道路上，重复: '


def check_road_relations(ctx: Context, park_bid, other_failed_info: list = []):
    """
    检查出入口
    """
    park = get_need_checked_park(ctx, park_bid)
    accesses = get_need_checked_access(ctx, [park_bid])

    failed_info1 = check_entrance_gate_type(ctx, accesses)
    failed_info2 = check_duplicate_entries_v2(ctx, accesses, park)
    failed_info3 = check_entrance_dist_to_link(accesses)
    failed_info4 = check_re_link_type(accesses)
    failed_info5 = check_entrance_dist_to_area(ctx, accesses, park)
    failed_info6 = check_re_link_direction(accesses)
    failed_info7 = check_duplicate_node_with_link(accesses, park)
    failed_info8 = check_node_link_invalid(accesses)
    failed_info9 = check_not_have_access(ctx, accesses, park)
    failed_info10 = check_out_node_re_link(accesses, park)
    # failed_info11 = check_link_blocked(park_id)
    failed_info12 = check_have_barricade(accesses)
    failed_info13 = []
    if ctx.is_check_ach():
        failed_info13 = check_manual_work_have_err_memo_v2(accesses)
    # failed_info14 = check_link_belong_some_road(park_id)
    all_failed_infos = [
        failed_info1,
        failed_info2,
        failed_info3,
        failed_info4,
        failed_info5,
        failed_info6,
        failed_info7,
        failed_info8,
        failed_info9,
        failed_info10,
        # failed_info11,
        failed_info12,
        failed_info13,
        # failed_info14,
    ]
    if len(other_failed_info) > 0:
        all_failed_infos.append(other_failed_info)

    merged_failed_info = merge_failed_info(
        all_failed_infos
    )
    if len(merged_failed_info) > 0:
        return False, merged_failed_info
    return True, merged_failed_info


@dataclasses.dataclass
class Access:
    """
    需要质检的口
    """
    access_id: Union[int, str]
    road_relation: dict
    bid: str
    geom: str
    park_bid: str
    scene: str = ''

    @staticmethod
    def from_ach(access: dict) -> 'Access':
        """
        从成果中转换
        """
        return Access(
            access_id=access['id'],
            road_relation=access['road_relation'],
            bid=access['bid'],
            geom=access['geom'],
            scene=access['scene'],
            park_bid=access['parent_bid'],
        )

    @staticmethod
    def from_online(access: dict) -> 'Access':
        """
        从线上转换
        """
        return Access(
            access_id=access['id'],
            road_relation=access['road_relation'],
            bid=access['bid'],
            geom=access['geom'],
            park_bid=access['parent_bid'],
        )


def _get_ach_need_checked_accesses(park_bids: list) -> List[Access]:
    accesses = sql_utils.get_accesses_by_park_bids(park_bids)
    if not accesses:
        return []
    accesses = [{
        'id': an_access[0],
        'road_relation': an_access[1],
        'scene': an_access[2],
        'geom': an_access[3],
        'bid': an_access[4],
        'parent_bid': an_access[5],
    } for an_access in accesses]
    return [Access.from_ach(an_access) for an_access in accesses]


def _get_online_need_checked_accesses(park_bids: list) -> List[Access]:
    accesses = sql_utils.get_online_accesses_by_park_bids(park_bids)
    if not accesses:
        return []
    resp = []
    for an_access in accesses:
        access_ach = sql_utils.get_access_by_bid(an_access[0])
        if len(access_ach) == 0:
            print(f"{an_access[0]} 出入口没有线下的成果对应")
            access_id = -1
        elif access_ach['parent_bid'] != an_access[3]:
            print(f"{an_access[0]} 出入口绑定关系发生了变更；{access_ach['parent_bid']} -> {an_access[3]}")
            access_id = -1
        else:
            access_id = access_ach['id']
        resp.append(
            {
                'id': access_id,
                'road_relation': an_access[1],
                'geom': an_access[2],
                'bid': an_access[0],
                'parent_bid': an_access[3],
            }
        )
    return [Access.from_online(an_access) for an_access in resp]


def get_need_checked_access(ctx: Context, park_bids: list) -> List[Access]:
    """
    获取需要质检的出入口
    """
    if ctx.is_check_online():
        return _get_online_need_checked_accesses(park_bids)
    if ctx.is_check_ach():
        return _get_ach_need_checked_accesses(park_bids)
    raise Exception(f"{ctx.check_mode} 不支持")


def merge_failed_info(all_failed_info):
    """
    合并失败信息
    """
    merged_info = {}
    for failed_list in all_failed_info:
        for info in failed_list:
            access_id = info.get("access_id")
            info_reson = info.get("reason")
            if info_reson is None:
                continue
            reason = info_reson
            if not isinstance(info['reason'], str):
                reason = info_reson.value
            if access_id in merged_info:
                merged_info[access_id] += f",{reason}"
            else:
                merged_info[access_id] = reason

    # 转换为列表形式
    return [{"access_id": k, "reason": v} for k, v in merged_info.items()]


def check_entrance_gate_type(ctx: Context, accesses: List[Access]) -> list:
    """
    检查出入口关联大门是否是紧急大门、出口大门
    """
    failed_info = []
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        if road_relation is None:
            continue

        node_ids = get_node_ids(road_relation)
        for node_id in node_ids:
            gate_v1 = sql_utils.is_enter_gate_v3(node_id)
            if gate_v1 == 1:
                # 明确知道入口
                continue
            if gate_v1 == 2:
                # 明确知道出口
                failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Re_Error_Gate})
                continue
            # 不清楚查道路母库
            long_node_ids = get_long_node_ids([node_id])
            if len(long_node_ids) != 1:
                continue
            gate_v2 = sql_utils.is_entry_gate_by_master_road(long_node_ids[0])
            if gate_v2 == 2:
                # 明确知道出口
                print(f"道路母库明确知道出口：{access_id}")
                failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Re_Error_Gate})
    return failed_info


def get_online_node_link_ids(park_bid: str, access_bid: str) -> tuple:
    """
    获取线上的 node_ids， link_ids
    """
    road_relations = sql_utils.get_online_road_relations_expect_access_bid(park_bid, access_bid)

    online_node_ids = []
    online_link_ids = []
    for road_relation_res in road_relations:
        road_relation = road_relation_res[0]
        if road_relation is None:
            continue

        online_node_ids += get_node_ids(road_relation)
        online_link_ids += get_link_ids(road_relation)
    return online_node_ids, online_link_ids


def repeated_access_ids_are_repaired(access_ids: list) -> bool:
    """
    重复的出入口是否都修复过，如果都修复过，返回 True
    """
    if len(access_ids) == 0:
        return False
    return sql_utils.access_ids_are_repaired(access_ids, 'Repeated_Access')


def link_related_node_are_repaired(access_ids: list) -> bool:
    """
    link 关联的 node 异常, 都修复过了返回 True
    """
    if len(access_ids) == 0:
        return False
    return sql_utils.access_ids_are_repaired(access_ids, 'Related_Link_Has_Exit_Or_Emergency_Door')


def check_duplicate_entries_v2(ctx: Context, accesses: List[Access], park: Park):
    """
    检查重复出入口
    """
    failed_info = []

    # 线下质检时，大部分是没有 access_bid, 只获取一次就好
    all_online_node_ids, all_online_link_ids = get_online_node_link_ids(park.bid, '')

    def _get_online_node_link_ids(_access_bid) -> tuple:
        if _access_bid == '':
            return all_online_node_ids, all_online_link_ids
        return get_online_node_link_ids(park.bid, _access_bid)

    offline_node_ids = []
    offline_link_ids = []
    for access in accesses:
        access_id = access.access_id
        access_bid = access.bid
        road_relation = access.road_relation

        online_node_ids, online_link_ids = _get_online_node_link_ids(access_bid)
        node_ids = get_node_ids(road_relation)
        link_ids = get_link_ids(road_relation)

        if len(node_ids) > 0 and list(node_ids)[0] is not None and set(node_ids).intersection(offline_node_ids):
            failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Entrance_Duplicate})
            continue
        if set(link_ids).intersection(offline_link_ids):
            failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Entrance_Duplicate})
            continue
        if set(node_ids).intersection(online_node_ids) or set(link_ids).intersection(online_link_ids):
            failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Entrance_Duplicate})
            continue

        offline_node_ids += node_ids
        offline_link_ids += link_ids

    failed_access_ids = [info['access_id'] for info in failed_info]
    if repeated_access_ids_are_repaired(failed_access_ids):
        # 如果错误的出入口都有修复记录
        print(f"{park.bid}, 检查重复出入口 异常都有修复")
        return []
    return failed_info


def check_entrance_dist_to_link(accesses: List[Access]):
    """
    检查出入口与对应LINK的距离是否过远
    """
    failed_info = []
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        if road_relation is None:
            continue

        node_link_list = get_node_link(road_relation)
        for item in node_link_list:
            link_id = item.get('link_id')
            if link_id is None:
                continue
            long_link_id = sql_utils.get_long_link_id(link_id)
            if long_link_id is None:
                continue
            link_res = get_link_geom(long_link_id)
            if link_res is None:
                continue
            link_sp = shapely.wkt.loads(link_res[0])
            if 'node_id' in item:
                node_id = item.get('node_id')
                long_node_id = sql_utils.get_long_node_id_by_short(node_id)
                node_res = get_node_geom(long_node_id)
                if node_res is None:
                    continue
                node_sp = shapely.wkt.loads(node_res[0])
            else:
                node_sp = shapely.wkt.loads(item.get('point'))
            if node_sp.distance(link_sp) > 50 / 110000:
                failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Node_Far_Away_To_Link})
    return failed_info


def check_re_link_type(accesses: List[Access]):
    """
    检查出入口关联的LINK是否为步行路
    """
    failed_info = []
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        if road_relation is None:
            continue

        link_ids = get_link_ids(road_relation)
        for link_id in link_ids:
            long_link_id = sql_utils.get_long_link_id(link_id)
            if long_link_id is None:
                continue
            link_kind_res = get_link_kind(long_link_id)
            if link_kind_res is None:
                continue
            if link_kind_res[0] == 10:
                # 步行路
                failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Link_Is_Ped_Road})
    return failed_info


def check_entrance_dist_to_area(ctx: Context, accesses: List[Access], park: Park):
    """
    检查出入口与门前面的距离是否过远
    """
    failed_info = []
    if len(accesses) == 0 or park is None:
        return failed_info

    distance = 30
    if ctx.is_check_online():
        distance = 40

    reason = 'AreaDistanceAccess'
    area_repaired = sql_utils.park_id_area_repaired(park.park_id, reason)

    park_sp = shapely.wkt.loads(park.geom)
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        if road_relation is None:
            continue

        access_repaired = sql_utils.access_ids_are_repaired([access_id], reason, 'turing')
        if area_repaired and access_repaired:
            print(f"{access_id} 口距离面过远质检跳过")
            continue

        node_ids = get_node_ids(road_relation)
        for node_id in node_ids:
            long_node_id = sql_utils.get_long_node_id_by_short(node_id)
            node_wkt_res = get_node_geom(long_node_id)
            if node_wkt_res is None:
                continue
            node_sp = shapely.wkt.loads(node_wkt_res[0])
            sp_dist = node_sp.distance(park_sp)
            if sp_dist >= distance / 110000:
                failed_info.append({
                    "access_id": access_id,
                    "reason": f"{Access_failed_Reason.Node_Far_Away_To_Area.value}: {sp_dist / 110000}"
                })

        link_ids = get_link_ids(road_relation)
        for link_id in link_ids:
            long_link_id = sql_utils.get_long_link_id(link_id)
            if long_link_id is None:
                continue
            link_res = sql_utils.get_nav_link(long_link_id)
            if link_res is None:
                continue

            link_wkt = link_res[0]
            line_geo = shapely.wkt.loads(link_wkt)
            geo_dist = line_geo.distance(park_sp)
            if geo_dist >= distance / 110000:
                failed_info.append({
                    "access_id": access_id,
                    "reason": f"{Access_failed_Reason.Node_Far_Away_To_Area.value}: {geo_dist / 110000}"
                })

    return failed_info


def check_re_link_direction(accesses: List[Access]):
    """
    检查出入口关联的LINK是否为出口方向
    """
    failed_info = []
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        if road_relation is None:
            continue

        link_ids = get_link_ids(road_relation)
        for link_id in link_ids:
            long_link_id = sql_utils.get_long_link_id(link_id)
            if long_link_id is None:
                continue
            link_res = sql_utils.get_nav_link(long_link_id)
            if link_res is None:
                continue

            link_wkt, dir = link_res
            line = shapely.wkt.loads(link_wkt)
            start_coords, end_coords = line.coords[0], line.coords[-1]

            start_point = Point(start_coords)
            end_point = Point(end_coords)

            s_in_aoi = is_in_aoi(start_point.wkt)
            e_in_aoi = is_in_aoi(end_point.wkt)
            if dir == 1:
                continue
            elif dir == 2:
                if s_in_aoi and not e_in_aoi:
                    failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Re_Link_Is_Out.value})
            elif dir == 3:
                if not s_in_aoi and e_in_aoi:
                    failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Re_Link_Is_Out.value})

    return failed_info


def get_node_ids(road_relation):
    """
    根据road_relation获取出入口关联的node_ids
    """
    node_ids = []
    if road_relation is None or "link_info" not in road_relation:
        return node_ids

    link_info = road_relation["link_info"]
    for x in link_info:
        # FIXME, 有的是否只绑定了出口？？
        # if x["orientation"] == 1 and "node_id" in x:
        if "node_id" in x:
            node_ids.append(x.get("node_id"))
    return list(set(node_ids))


def get_link_ids(road_relation):
    """
    根据road_relation获取出入口关联的links
    """
    links = []
    if road_relation is None or "link_info" not in road_relation:
        return links

    link_info = road_relation["link_info"]
    for x in link_info:
        if x["orientation"] == 1 and "link_id" in x:
            links.append(x["link_id"])
    return links


def get_node_link(road_relation):
    """
    根据road_relation获取出入口关联的node和link对
    """
    res = []
    if road_relation is None or "link_info" not in road_relation:
        return res

    link_info = road_relation["link_info"]
    for x in link_info:
        if x["orientation"] == 1:
            if "node_id" in x and "link_id" in x:
                res.append({'node_id': x.get('node_id'), 'link_id': x["link_id"]})
                continue
            mc_point = x.get('point')
            point_x, point_y = map(float, mc_point.split(','))
            mc_point_wkt = Point(point_x, point_y).wkt
            gc_point_wkt = query.mc2gcj(mc_point_wkt)
            gc_point = shapely.wkt.loads(gc_point_wkt)
            if gc_point.x < 0 or gc_point.y < 0:
                res.append({'point': mc_point_wkt, 'link_id': x["link_id"]})
                continue
            res.append({'point': gc_point_wkt, 'link_id': x["link_id"]})
    return res


def get_points(road_relation):
    """
    获取point
    """
    points = []
    if road_relation is None or "link_info" not in road_relation:
        return points

    link_info = road_relation["link_info"]
    for x in link_info:
        if x["orientation"] == 1:
            mc_point = x.get('point')
            x, y = map(float, mc_point.split(','))
            mc_point_wkt = Point(x, y).wkt
            gc_point_wkt = query.mc2gcj(mc_point_wkt)
            gc_point = shapely.wkt.loads(gc_point_wkt)
            if gc_point.x < 0 or gc_point.y < 0:
                points.append(mc_point_wkt)
                continue
            points.append(gc_point_wkt)
    return points


def check_duplicate_node_with_link(accesses: List[Access], park: Park):
    """
    检查出入口是否重复
    """
    failed_info = []
    # 取出线上出入口数据
    node_ids = []
    link_node_ids = []
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        if road_relation is None:
            continue

        node_ids += get_type1_node_ids(road_relation)
        link_ids = get_type2_link_ids(road_relation)
        link_node_ids += get_node_ids_from_link_ids(link_ids)
        dp_node_ids = set(node_ids).intersection(link_node_ids)
        if len(dp_node_ids) > 0:
            failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Node_Link_Dup.value})
            continue

    failed_access_ids = [info['access_id'] for info in failed_info]
    if repeated_access_ids_are_repaired(failed_access_ids):
        # 如果错误的出入口都有修复记录
        print(f"{park.bid}, 检查 node_link 重复 异常都有修复")
        return []
    return failed_info


def get_type1_node_ids(road_relation):
    """
    根据road_relation获取出入口(type=1)关联的node_ids
    """
    node_ids = []
    if road_relation is None or "link_info" not in road_relation:
        return node_ids

    link_info = road_relation["link_info"]
    for x in link_info:
        if x["orientation"] == 1 and "node_id" in x and "type" in x and x["type"] == 1:
            l_node_id = get_long_node_id_by_short(x.get("node_id"))
            if l_node_id is None:
                continue
            node_ids.append(l_node_id)
    return node_ids


def get_type2_link_ids(road_relation):
    """
    根据road_relation获取出入口(type=2)关联的link_ids
    """
    links = []
    if road_relation is None or "link_info" not in road_relation:
        return links

    link_info = road_relation["link_info"]
    for x in link_info:
        if x["orientation"] == 1 and "link_id" in x and "type" in x and x["type"] == 2:
            links.append(x["link_id"])
    return links


def get_node_ids_from_link_ids(link_ids):
    """
    根据一组link_id获取对应一组node_id
    """
    node_ids = []
    for link_id in link_ids:
        long_link_id = sql_utils.get_long_link_id(link_id)
        if long_link_id is None:
            continue

        node_ids_res = sql_utils.get_node_ids_from_link_id(long_link_id)
        if not node_ids_res:
            continue
        node_ids.append(node_ids_res[0])
        node_ids.append(node_ids_res[1])
    return list(set(node_ids))


def check_node_link_invalid(accesses: List[Access]) -> list:
    """
    检查 node 或者 link 失效
    """
    failed_info = []
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        if road_relation is None:
            continue

        short_node_ids = get_type1_short_node_ids(road_relation)
        long_node_ids = get_long_node_ids(short_node_ids)
        if len(short_node_ids) != len(long_node_ids) or sql_utils.any_node_id_invalid(long_node_ids):
            print(f"有 node 失效,long: {long_node_ids}; short: {short_node_ids}")
            failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Node_Link_Invalid.value})
            continue

        short_link_ids = get_type2_link_ids(road_relation)
        long_link_ids = get_long_link_ids(short_link_ids)
        if len(short_link_ids) != len(long_link_ids) or sql_utils.any_link_id_invalid(long_link_ids):
            print(f"有 link 失效, long: {long_link_ids}; short: {short_link_ids}")
            failed_info.append({"access_id": access_id, "reason": Access_failed_Reason.Node_Link_Invalid.value})
            continue
    return failed_info


def get_type1_short_node_ids(road_relation):
    """
    根据road_relation获取出入口(type=1)关联的node_ids
    """
    node_ids = []
    if road_relation is None or "link_info" not in road_relation:
        return node_ids

    link_info = road_relation["link_info"]
    for x in link_info:
        if x["orientation"] == 1 and "node_id" in x and "type" in x and x["type"] == 1:
            node_ids.append(x.get("node_id"))
    return node_ids


def get_long_node_ids(short_node_ids: list) -> list:
    """
    通过短获取长的
    """
    long_node_ids = []
    for node_id in short_node_ids:
        long_node_id = get_long_node_id_by_short(node_id)
        if long_node_id is not None:
            long_node_ids.append(long_node_id)
    return long_node_ids


def get_long_link_ids(short_link_ids: list) -> list:
    """
    通过短获取长的
    """
    long_link_ids = []
    for link_id in short_link_ids:
        long_link_id = sql_utils.get_long_link_id(link_id)
        if long_link_id is None:
            continue
        long_link_ids.append(long_link_id)
    return long_link_ids


def check_not_have_access(ctx: Context, accesses: List[Access], park: Park) -> list:
    """
    质检没有出入口
    """
    if ctx.is_check_online():
        if len(accesses) == 0:
            return [
                {"access_id": -1, "reason": Access_failed_Reason.No_Access.value}
            ]
        return []

    accesses = sql_utils.get_accesses_by_park_id_v3(park.bid)
    if len(accesses) == 0:
        return [
            {"access_id": -1, "reason": Access_failed_Reason.No_Access.value}
        ]
    return []


def check_out_node_re_link(accesses: List[Access], park: Park):
    """
    检查关联的link上有出口node的
    """
    failed_info = []
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        if road_relation is None:
            continue

        link_ids = get_type2_link_ids(road_relation)
        link_node_ids = get_node_ids_from_link_ids(link_ids)
        for gate in link_node_ids:
            s_node = sql_utils.get_short_node_by_long_node(gate)
            is_in, reason = sql_utils.is_enter_gate_v2(s_node)
            if reason == '紧急门':
                print(f"{reason} 不再过滤")
                continue
            if not is_in and s_node not in {item["node_id"] for item in failed_info}:
                failed_info.append({
                    # "park_id": park_id,
                    "access_id": access_id,
                    # "access": access,
                    "node_id": s_node,
                    # "risk": f"{reason}"
                    "reason": f"{Access_failed_Reason.Out_Node_Relation_Link.value}: {reason}"
                })

    failed_access_ids = [info['access_id'] for info in failed_info]
    if link_related_node_are_repaired(failed_access_ids):
        # 如果错误的出入口都有修复记录
        print(f"{park.bid}, 关联的link上有出口node的 异常都有修复")
        return []

    return failed_info


def check_link_blocked(park_id) -> list:
    """
    link 是否有阻断信息
    """
    # return []  # todo Test !!!!!

    failed_info = []
    accesses = get_accesses_by_park_id(park_id)
    link_id2access_ids = {}
    all_link_ids = []
    for access in accesses:
        id, road_relation = access
        if road_relation is None:
            continue
        link_ids = get_conn_link_ids(road_relation)
        # link_ids = get_conn_link_ids_v2(road_relation)
        for a_link_id in link_ids:
            link_id2access_ids.setdefault(a_link_id, []).append(id)
        all_link_ids += link_ids
    block_link_ids = get_have_block_link_ids(all_link_ids)
    for a_block_link_id in list(set(block_link_ids)):
        for access_id in link_id2access_ids[str(a_block_link_id)]:
            failed_info.append(
                {"access_id": access_id, "reason": Access_failed_Reason.Link_Have_Block.value + str(a_block_link_id)}
            )
    return failed_info


def array_chunk(data: list, size: int) -> list:
    """
    数据分成块，size 是每块的数量；（最后一块的数量会 <= num）
    :param data:
    :param size:
    :return:
    """
    resp = []

    datum = []
    for _item in data:
        datum.append(_item)

        if len(datum) == size:
            resp.append(datum)
            datum = []

    if len(datum) > 0:
        resp.append(datum)
    return resp


def check_link_blocked_by_park_bids(ctx: Context, park_bids: list) -> dict:
    """
    检查阻断需要请求接口，接口有次数限制，把所有停车场聚合在一起，批量检查
    """
    park_bid_chunks = array_chunk(park_bids, 100)
    accesses = []
    for bid_chunk in park_bid_chunks:
        accesses += get_need_checked_access(ctx, bid_chunk)

    link_id2access_ids = {}
    access_id2park_bid = {}
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        park_bid = access.park_bid
        access_id2park_bid[access_id] = park_bid
        if road_relation is None:
            continue
        link_ids = get_conn_link_ids_v2(road_relation)
        for a_link_id in link_ids:
            link_id2access_ids.setdefault(a_link_id, []).append(access_id)

    park_bid2fails = {}
    link_id_chunks = array_chunk(list(set(link_id2access_ids.keys())), 99)
    for link_id_chunk in link_id_chunks:
        block_link_ids = get_have_block_link_ids(link_id_chunk)
        for a_block_link_id in list(set(block_link_ids)):
            for access_id in link_id2access_ids[str(a_block_link_id)]:
                park_bid = access_id2park_bid[access_id]
                park_bid2fails.setdefault(park_bid, []).append(
                    {"access_id": access_id,
                     "reason": Access_failed_Reason.Link_Have_Block.value + str(a_block_link_id)}
                )
    return park_bid2fails


def get_conn_link_ids(road_relation):
    """
    获取关联的 link_ids
    """
    link_ids = []
    if road_relation is None or "link_info" not in road_relation:
        return link_ids
    link_info = road_relation["link_info"]
    for x in link_info:
        if 'link_id' not in x:
            continue
        link_ids.append(x["link_id"])
    return link_ids


def get_conn_link_ids_v2(road_relation):
    """
    获取关联的 link_ids
    """
    link_ids = []
    if road_relation is None or "link_info" not in road_relation:
        return link_ids
    link_info = road_relation["link_info"]
    for x in link_info:
        if 'link_id' not in x or x.get('type') == 1:
            continue
        link_ids.append(x["link_id"])
    return link_ids


def get_jwt_token(account, token, service_list):
    """获取加密字符串

    Args:
        account (str): 授权账号
        token (str): 账号对应token
        service_list (list): 要访问的接口，对于路网服务，列表长度只能为1，第0个元素为要访问的接口名

    Returns:
        _type_: _description_
    """
    msg = {
        'exp': int(time.time()) + 680,  # 过期时间
        'timeout': int(time.time()) + 5 * 60,  # 有效期5min,
        'name': account,  # 账号名称
        'service': service_list  # 服务名称
    }
    # 获取token
    ret = jwt.encode(msg, token, algorithm='HS256')
    return ret


def get_have_block_link_ids(shot_link_ids: list, result_types: list = None) -> list:
    """
    获取有阻断的 link
    """
    if result_types is None:
        result_types = [
            1,  # 车导
            22,  # 中置信度
        ]

    url = "http://service.mapde.baidu-int.com/de/servicehub/create_task"

    account = 'aoi_get_block_cache_service'
    token = '0e00aa2ee0e74d2e9a56d32304d9a805'

    shot_link_ids = [int(sid) for sid in shot_link_ids]
    service_list = ['search_event']
    data = {"link_id": shot_link_ids, "status": "1"}
    key = get_jwt_token(account, token, service_list)
    headers = {'content-type': "application/json", 'Authorization': 'Bearer ' + key}

    try:
        response = requests.post(url, data=json.dumps(data), headers=headers)
        res = response.json()
        print(json.dumps(res))

        if 'errno' not in res or res['errno'] != 0:
            raise Exception(f"{res['msg']}, 请求异常")

        if 'data' not in res or 'data' not in res['data']:
            raise Exception(f"{res}, 不符合数据规格")

        block_link_ids = []
        for item in res['data']['data']:
            result_type = item['result_type']
            print(item['link_id'], result_type, len(result_types) > 0, result_type in result_types)
            if len(result_types) > 0 and result_type in result_types:
                block_link_ids.append(item['link_id'])
        return block_link_ids
    except Exception as e:
        logging.exception(e)
        raise Exception(f"请求阻断时异常：{str(e)}")


def _get_road_node_block_list(access: Access) -> list:
    road_relation = access.road_relation
    if road_relation is None:
        return []
    link_ids = get_conn_link_ids(road_relation)
    node_ids = get_node_ids(road_relation)

    long_link_ids = get_long_link_ids(link_ids)
    long_node_ids = get_long_node_ids(node_ids)
    if not long_link_ids and not long_node_ids:
        return []

    for a_long_link_id in long_link_ids:
        long_link_info = sql_utils.get_node_ids_from_link_id(a_long_link_id)
        if not long_link_info:
            continue
        long_node_ids += [long_link_info[0], long_link_info[1]]

    long_node_infos = sql_utils.get_long_node_infos(long_node_ids)
    if not long_node_infos:
        return []
    return [item for item in long_node_infos if item[1] == '33']


def _get_road_node_block_list_v2(access: Access) -> list:
    access_id = access.access_id
    road_relation = access.road_relation
    if road_relation is None:
        return []
    node_ids = get_node_ids(road_relation)
    if len(node_ids) == 0:
        # 关联的是 link
        return _get_road_node_block_list(access)
    # 关联的是大门
    long_node_ids = get_long_node_ids(node_ids)
    if len(long_node_ids) == 0:
        return []
    gates = sql_utils.get_gates(long_node_ids)
    link_ids = list(set([gate[0] for gate in gates]))

    two_way_door = len(link_ids) == 2
    long_node_ids = []
    link_infos = sql_utils.get_link_infos(link_ids)
    inner_nums = len([a_link for a_link in link_infos if a_link[5] == '52'])
    not_all_inner_road = inner_nums != len(link_infos)

    for link_info in link_infos:
        form = link_info[5]
        s_id = link_info[3]
        e_id = link_info[4]
        if form == '52' and two_way_door and not_all_inner_road:
            # 双向大门，进入link是外部路，退出link是內部路，且路障在退出link（內部路），质检放开
            print(f"{access_id}; 双向大门，进入link是外部路，退出link是內部路，且路障在退出link（內部路），质检放开")
            continue
        long_node_ids += [s_id, e_id]
    long_node_arr = sql_utils.get_long_node_infos(long_node_ids)
    result = [node[0] for node in long_node_arr if str(node[1]) == '33']
    return list(set(result))


def check_have_barricade(accesses: List[Access]):
    """
    是否有路障
    """
    failed_info = []
    for access in accesses:
        access_id = access.access_id
        road_block_list = _get_road_node_block_list_v2(access)
        if len(road_block_list) > 0:
            failed_info.append(
                {"access_id": access_id,
                 "reason": Access_failed_Reason.Node_With_Barricade.value + str(road_block_list)})
    return failed_info


def _node_block_had_repaired(access: dict) -> bool:
    """
    路障是否修复过，修复过返回 True
    """
    return sql_utils.access_ids_are_repaired([access['access_id']], 'Node_Block', user_name='block_info')


def _link_block_had_repaired(access: dict) -> bool:
    """
    阻断是否修复过，修复过返回 True
    """
    return sql_utils.access_ids_are_repaired([access['access_id']], 'Link_Block', user_name='block_info')


def _road_err_had_repaired(access: dict) -> bool:
    """
    道路问题是否修复过，修复过返回 True
    """
    return sql_utils.access_ids_are_repaired([access['access_id']], 'Lack_Road', user_name='parking_gate_push_turing')


def _has_road_relation(access: dict) -> bool:
    return len(access['road_relation']) > 0


def _gate_pass_had_repaired(access: dict) -> bool:
    """
    大门问题是否修复过
    """
    # 出口大门错误，高置信库查不到的话以人工备注为准
    node_ids = get_node_ids(access['road_relation'])
    for node_id in node_ids:
        if 0 == sql_utils.is_enter_gate_v3(node_id):
            # 0 表示查不到，那么以人工备注为准，那么就是有错误信息
            # 不是 0，那么是否有错误，有其他质检项保障，当前不再重复判断
            return False
    return True


def check_manual_work_have_err_memo_v2(accesses: List[Access]):
    """
    质检作业是否有异常备注
    """
    memo2repaired_fn = {
        '路障': _node_block_had_repaired,  # 以质检项为准，不再以备注为准
        '阻断link': _link_block_had_repaired,  # 以质检项为准，不再以备注为准
        '出口link': _road_err_had_repaired,  # 以质检项为准，不再以备注为准
        '步行路': _road_err_had_repaired,  # 以质检项为准，不再以备注为准
        '非机动车道': _road_err_had_repaired,  # 以质检项为准，不再以备注为准
        '缺路戳点': _has_road_relation,
        '缺失link': _has_road_relation,
        '出口大门': _gate_pass_had_repaired,  # 以质检项为准，不再以备注为准
    }
    failed_info = []
    for access in accesses:
        access_id = access.access_id
        road_relation = access.road_relation
        scene = str(access.scene).strip()
        access_dict = {'access_id': access_id, 'road_relation': road_relation}

        for memo, fn in memo2repaired_fn.items():
            if memo not in scene:
                continue
            if fn(access_dict):
                print(f"{access_id}; 备注：{scene}; 场景：{memo}, 已经处理过了，不再拦截")
                continue
            failed_info.append({
                "access_id": access_id,
                "reason": Access_failed_Reason.Err_Work_Manual_Memo.value + str(memo)
            })
    return failed_info


def check_manual_work_have_err_memo(park_id):
    """
    质检作业是否有异常备注
    """
    err_memos = [
        # '路障',
        # '阻断link',
        '出口link',
        # '出口大门错误',
        '步行路',
        '非机动车道',
        '缺路戳点',

        '缺失link',
        # 'link上有路障',
        # '阻断link',
        '出口link',
        # '出口大门',
    ]

    def _memo_has_err(memo: str) -> bool:
        """
        作业备注中是否有异常，有返回 True
        """
        for err in err_memos:
            if err in memo:
                return True
        return False

    failed_info = []
    accesses = sql_utils.get_accesses_by_park_id_v2(park_id)
    for access in accesses:
        access_id = access[0]
        scene = str(access[2]).strip()
        print(scene, '路障' in scene, 111, access_id)
        if _memo_has_err(scene):
            failed_info.append({
                "access_id": access_id,
                "reason": Access_failed_Reason.Err_Work_Manual_Memo.value + str(scene)
            })
            continue
        if '阻断' in scene:
            if not sql_utils.access_ids_are_repaired([access_id], 'Link_Block'):
                failed_info.append({
                    "access_id": access_id,
                    "reason": Access_failed_Reason.Err_Work_Manual_Memo.value + str(scene)
                })
            continue
        if '路障' in scene:
            if not sql_utils.access_ids_are_repaired([access_id], 'Node_Block'):
                failed_info.append({
                    "access_id": access_id,
                    "reason": Access_failed_Reason.Err_Work_Manual_Memo.value + str(scene)
                })
            continue
        if '出口大门' not in scene:
            continue

        # 出口大门错误，高置信库查不到的话以人工备注为准
        node_ids = get_node_ids(access[1])
        for node_id in node_ids:
            if 0 == sql_utils.is_enter_gate_v3(node_id):
                # 0 表示查不到，那么以人工备注为准，那么就是有错误信息
                # 不是 0，那么是否有错误，有其他质检项保障，当前不再重复判断
                failed_info.append({
                    "access_id": access_id,
                    "reason": Access_failed_Reason.Err_Work_Manual_Memo.value + str(scene)
                })
                break

    return failed_info


def check_link_belong_some_road(park_id):
    """
    检查link是否属于同一道路
    """
    failed_info = []
    accesses = sql_utils.get_accesses_by_park_id_v2(park_id)
    if len(accesses) <= 1:
        return failed_info

    invalid_access_id = []
    for access in accesses:
        access_id = access[0]
        road_relation = access[1]
        access_geom = access[3]
        if access_id in invalid_access_id:
            continue
        link_ids = get_conn_link_ids(road_relation)
        node_ids = get_node_ids(road_relation)
        if not link_ids:
            continue
        long_link_ids = get_long_link_ids(link_ids)
        link_res = get_link_geom(long_link_ids[0])
        if not link_res:
            continue

        link_sp = shapely.wkt.loads(link_res[0])
        access_sp = shapely.wkt.loads(access_geom)
        # 判断50m范围内有没有临近出入口关联距离差不多的LINK
        for other_access in accesses:
            other_access_id = other_access[0]
            son_road_relation = other_access[1]
            son_park_access_geom = other_access[3]
            if other_access_id in invalid_access_id:
                continue
            if other_access_id == access_id:
                continue
            other_link_ids = get_conn_link_ids(son_road_relation)
            if not other_link_ids:
                continue

            other_node_ids = get_node_ids(son_road_relation)
            if len(node_ids) > 0 and len(other_node_ids) > 0:
                print(f"两个关联的都是大门：{other_access_id}, 不再质检link是否属于同一道路")
                continue

            other_long_link_ids = get_long_link_ids(other_link_ids)
            son_link_res = get_link_geom(other_long_link_ids[0])
            if son_link_res is None:
                continue
            son_link_sp = shapely.wkt.loads(son_link_res[0])
            son_park_access_sp = shapely.wkt.loads(son_park_access_geom)
            # 如果两个LINK距离较远,正常数据
            if access_sp.distance(son_park_access_sp) > 50 / 100000:
                continue
            # 如果两个LINK距离较近,直接输出
            if link_sp.distance(son_link_sp) < 5 / 100000:
                print(f"原出入口关联入口距离较近:{link_ids[0]}\t{other_link_ids[0]}")
                invalid_access_id.append(access_id)
                invalid_access_id.append(other_access_id)
                break
            # 如果子点Node关联的LINK与当前LINK距离较近,直接输出
            son_node_ids = [son_link_res[1], son_link_res[2]]
            son_node_relation_link_list = sql_utils.get_link_info_by_node(son_node_ids)
            if not son_node_relation_link_list:
                continue
            # 如果间隔一条LINK有距离较近的LINK,直接输出
            for son_node_relation_link in son_node_relation_link_list:
                # son_node_relation_link_id = son_node_relation_link[0]
                son_node_relation_link_sp = shapely.wkt.loads(son_node_relation_link[1])
                if son_node_relation_link_sp.distance(link_sp) < 5 / 100000:
                    print(f"原出入口间隔LINK关联入口距离较近:{link_ids[0]}\t{son_node_relation_link[0]}")
                    invalid_access_id.append(access_id)
                    invalid_access_id.append(other_access_id)
                    break

    for access_id in invalid_access_id:
        failed_info.append({
            "access_id": access_id,
            "reason": Access_failed_Reason.Some_Road_Link,
        })

    failed_access_ids = [info['access_id'] for info in failed_info]
    if repeated_access_ids_are_repaired(failed_access_ids):
        print(f"{park_id}, 检查 link和link在一条规划道路上 异常都有修复")
        # 如果错误的出入口都有修复记录
        return []

    return failed_info
