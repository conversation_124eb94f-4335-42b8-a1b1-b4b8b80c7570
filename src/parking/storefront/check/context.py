"""
质检上下文
"""


class Context:
    """
    上下文
    """
    check_status: str  # 质检的状态
    check_where: str  # 质检的条件 exp: city = '北京市'

    check_mode: str = 'ach'

    def gen_query_where(self) -> str:
        """
        生成查询条件
        """
        where = f" 1 = 1 "
        if self.check_status != '':
            where = f"{where} and status = '{self.check_status}'"
        if self.check_where != '':
            where = f"{where} and {self.check_where}"
        return where

    def get_not_over_statuses(self) -> list:
        """
        获取不能压盖的状态列表
        """
        must_statuses = [  # 禁止压盖的状态
            'READY_ONLINE',
            'CHECK_SUCCEED',
            'COMING_ONLINE',
        ]
        status2not = {
            'READY_ONLINE': must_statuses,  # 待上线的质检
            'INITED_BID': must_statuses + [  # 仅面质检
                'INITED_BID',
                'READY',
                'PARK_ACCESS_INTELLIGENCE',
                'PARK_ACCESS_INTELLIGENCE_PUSHED',
                'PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALING',
                'PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALED',
            ],
            'CHECK_SUCCEED': must_statuses,
            'CHECK_FAILED': must_statuses,
            'REPAIRING': must_statuses,
        }
        if self.check_status not in status2not:
            raise Exception(f"{self.check_status} 不能压盖的状态列表 为空")
        return status2not[self.check_status]

    def is_check_ach(self) -> bool:
        """
        质检成果数据
        """
        return self.check_mode == 'ach'

    def is_check_online(self) -> bool:
        """
        质检线上数据
        """
        return self.check_mode == 'online'


def gen_ctx(status: str, where: str) -> Context:
    """
    生成质检成果上下文
    """
    ctx = Context()

    ctx.check_status = status
    ctx.check_where = where

    ctx.check_mode = 'ach'
    return ctx


def gen_online_ctx(where: str) -> Context:
    """
    生成质检线上，上下文
    """
    ctx = Context()

    ctx.check_status = ''
    ctx.check_where = where

    ctx.check_mode = 'online'
    return ctx

