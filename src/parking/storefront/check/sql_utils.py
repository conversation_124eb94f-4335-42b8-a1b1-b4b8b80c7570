# -*- coding: utf-8 -*-
"""
数据获取层
"""
from src.parking.recognition import dbutils
from src.tools import pgsql
from src.parking.storefront.check.context import Context


def get_waiting_check_parks_ids(ctx: Context):
    """
    获取所有待质检门前停车场id
    表park_storefront_prod_parking.status == 'READY_ONLINE'的停车场id集合
    """
    # and info_id in (select info_id from park_storefront_verify_pushed where info_source = 'repair')
    # and type = 'update'
    sql = f"""
           select bid 
           from park_storefront_prod_parking 
           where ({ctx.gen_query_where()}) and bid_status = 'effected'
    """
    print(sql)
    parks_ids = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return parks_ids


def get_accesses_by_park_id(park_bid):
    """
    根据park_id获取待质检出入口
    """
    sql = """
        select id, road_relation 
        from park_storefront_prod_access 
        where parent_bid = %s and status in ('READY_ONLINE', 'CHECK_FAILED')
    """
    # where park_id = %s and status not in ('ALREADY_OFFLINE', 'CHECK_FAILED')
    accesses_list = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [park_bid])
    return accesses_list


def get_accesses_by_park_id_v2(park_bid):
    """
    根据park_id获取待质检出入口
    """
    sql = """
        select id, road_relation, scene, st_astext(geom), bid 
        from park_storefront_prod_access 
        where parent_bid = %s and status in ('READY_ONLINE', 'CHECK_FAILED')
    """
    # where park_id = %s and status not in ('ALREADY_OFFLINE', 'CHECK_FAILED')
    accesses_list = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [park_bid])
    return accesses_list


def get_access_by_bid(bid: str) -> dict:
    """
    获取出入口
    """
    qry = f"""
    select id, road_relation, scene, st_astext(geom), bid, parent_bid 
    from park_storefront_prod_access 
    where bid = '{bid}' and status != 'CANCEL' and bid != '' 
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, qry)
    print(qry)
    print(res)
    if not res:
        return {}
    return {
        'id': res[0],
        'road_relation': res[1],
        'scene': res[2],
        'geom': res[3],
        'bid': res[4],
        'parent_bid': res[5],
    }


def get_accesses_by_park_id_v3(park_bid):
    """
    根据park_id获取待质检出入口
    """
    sql = """
        select id, road_relation, scene, st_astext(geom), bid 
        from park_storefront_prod_access 
        where parent_bid = %s and status in ('READY_ONLINE', 'CHECK_FAILED', 'ALREADY_ONLINE')
    """
    # where park_id = %s and status not in ('ALREADY_OFFLINE', 'CHECK_FAILED')
    accesses_list = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [park_bid])
    return accesses_list


def get_online_park_bids(ctx: Context):
    """
    获取线上的停车场 bid
    """
    sql = f"""
    select bid 
    from parking 
    where show_tag = '门前停车场' and park_spec in (1, 2) and status = 1 and {ctx.gen_query_where()} 
    """
    parks = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    if not parks:
        return []
    return [a_park[0] for a_park in parks]


def get_accesses_by_park_bids(park_bids: list):
    """
    根据park_id获取待质检出入口
    """
    if not park_bids:
        return []
    sql = f"""
    select id, road_relation, scene, st_astext(geom), bid, parent_bid
    from park_storefront_prod_access 
    where parent_bid = ANY(%s) and status in ('READY_ONLINE', 'CHECK_FAILED')
    """
    accesses_list = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [park_bids])
    return accesses_list


def get_online_accesses_by_park_bids(park_bids: list):
    """
    获取线上出入口
    """
    sql = """
        select bid, road_relation, st_astext(gcj_geom), parent_id  
        from parking 
        where parent_id = ANY(%s) and status  = 1 
    """
    # where park_id = %s and status not in ('ALREADY_OFFLINE', 'CHECK_FAILED')
    return dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [park_bids])


def update_prod_parking_checked_info(ctx: Context, park_id, status, reason, checker):
    """
    更新门前停车场质检结果
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = """
            update park_storefront_prod_parking 
            set status = %s , check_memo = %s, checker = %s
            where bid = %s and status = %s and bid_status = 'effected' 
        """
        print(sql, park_id, status, reason, checker)  # todo test
        pgsql.execute(conn, sql, [status, reason, checker, park_id, ctx.check_status])


def update_online_parking_checked_info(park_id, status, reason, checker):
    """
    更新门前停车场质检结果
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = """
            update park_storefront_prod_parking 
            set status = %s , check_memo = %s, checker = %s
            where bid = %s and status = 'ONLINE' and bid_status = 'effected' 
        """
        print(sql, park_id, status, reason, checker)  # todo test
        pgsql.execute(conn, sql, [status, reason, checker, park_id])


def update_prod_parking_checked_info_with_remove_bids(ctx: Context, park_id, status, reason, remove_bids):
    """
    更新门前停车场质检结果
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = """
            update park_storefront_prod_parking 
            set status = %s , check_memo = %s, remove_bids = %s
            where bid = %s and status = %s and bid_status = 'effected' 
        """
        print(sql, park_id, status, reason, remove_bids)  # todo test
        pgsql.execute(conn, sql, [status, reason, remove_bids, park_id, ctx.check_status])


def update_prod_access_checked_info(access_id, status, reason, checker):
    """
    更新出入口质检结果
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = """
            update park_storefront_prod_access 
            set status = %s , check_memo = %s, checker = %s 
            where id = %s
        """
        print(sql, access_id, status, reason)  # todo test
        pgsql.execute(conn, sql, [status, reason, checker, access_id])


def get_waiting_check_parks_areas_v2(ctx: Context):
    """
    获取所有待质检停车场门前面
    """
    # exp: 'CHECK_SUCCEED', 'COMING_ONLINE', 'READY_ONLINE'
    status_str = ','.join([f"'{status}'" for status in ctx.get_not_over_statuses()])
    sql = f"""
        select bid, st_astext(geom) 
        from park_storefront_prod_parking 
        where status in ({status_str}) and bid_status = 'effected' 
    """
    parks_areas = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return parks_areas


def get_waiting_check_park_area_by_id(ctx: Context, park_id):
    """
    根据停车场id获取待质检停车场门前面
    """
    sql = """
        select bid, st_astext(geom), id, status  
        from park_storefront_prod_parking
        where bid = %s and bid_status = 'effected' 
    """
    park_area = dbutils.fetch_one(pgsql.POI_CONFIG, sql, [park_id])
    return park_area


def get_waiting_check_park_area_by_id_v2(ctx: Context, park_id):
    """
    根据停车场id获取待质检停车场门前面
    """
    sql = """
        select bid, st_astext(geom), bid, type, id 
        from park_storefront_prod_parking
        where status = %s and bid = %s and bid_status = 'effected' 
    """
    park_area = dbutils.fetch_one(pgsql.POI_CONFIG, sql, [ctx.check_status, park_id])
    return park_area


def get_intersects_parks(ctx: Context, park_bid: str, wkt: str) -> list:
    """
    获取相交的停车场
    """
    status_str = ','.join([f"'{status}'" for status in ctx.get_not_over_statuses()])
    qry = f"""
    select bid, st_astext(geom) 
    from park_storefront_prod_parking 
    where bid != '{park_bid}' and bid_status = 'effected' and status in ({status_str}) 
        and st_intersects(geom, st_geomfromtext('{wkt}', 4326)) 
    """
    return dbutils.fetch_all(pgsql.POI_CONFIG, qry)


def get_node_geom(node_id):
    """
    获取node坐标
    """
    sql = "select st_astext(geom) from nav_node where node_id = %s"
    node_wkt = dbutils.fetch_one(pgsql.ROAD_CONFIG_WITH_INDEX, sql, [node_id])
    return node_wkt


def is_enter_gate(node_id):
    """
    判断大门是否可入
    """
    sql = f"""
       select node_id, gate_id, passage,traversability, type, nnww_tag, type_desc from gates_semantic 
       where node_id = '{node_id}'
   """
    semantic_res = dbutils.fetch_all(pgsql.DEST_TRAJ, sql)
    if len(semantic_res) == 0:
        return True
    if len(semantic_res) == 2:
        semantic_res_0, semantic_res_1 = semantic_res[0], semantic_res[1]
        type_arr = [semantic_res_0[4], semantic_res_1[4]]
        if type_arr[0] == "3" and type_arr[1] == "3":
            return True
        elif "2" in type_arr and "0" in type_arr:
            return True
        return False
    type = semantic_res[0][4]
    nnww_tag = semantic_res[0][5]

    if type == "0":
        return False  # 紧急门
    else:
        if "入口" in nnww_tag:
            return True
        elif "出口" in nnww_tag:
            return False  # 出口门
        else:
            if type in ["2", "3"]:
                return True
            elif type in ["1"]:
                return False  # 出口门
    return False


def is_enter_gate_v2(node_id):
    """
    判断大门是否可入
    """
    sql = f"""
       select node_id, gate_id, passage,traversability, type, nnww_tag, type_desc from gates_semantic 
       where node_id = '{node_id}'
   """
    semantic_res = dbutils.fetch_all(pgsql.DEST_TRAJ, sql)
    if len(semantic_res) == 0:
        return True, ""
    if len(semantic_res) == 2:
        semantic_res_0, semantic_res_1 = semantic_res[0], semantic_res[1]
        type_arr = [semantic_res_0[4], semantic_res_1[4]]
        if type_arr[0] == '3' and type_arr[1] == '3':
            return True, ""
        elif '2' in type_arr and '0' in type_arr:
            return True, ""
        elif semantic_res_0[6] == "紧急大门" and semantic_res_1[6] == "紧急大门":
            return False, "紧急门"  # 紧急门
        return False, "出口门"
    type = semantic_res[0][4]
    nnww_tag = semantic_res[0][5]

    if type == "0":
        return False, "紧急门"  # 紧急门
    else:
        if "入口" in nnww_tag:
            return True, ""
        elif "出口" in nnww_tag:
            return False, "出口门"  # 出口门
        else:
            if type in ["2", "3"]:
                return True, ""
            elif type in ["1"]:
                return False, "出口门"  # 出口门
    return False, "出口门"


def is_enter_gate_v3(node_id):
    """
    判断大门是否可入
    """
    sql = f"""
       select node_id, gate_id, passage,traversability, type, nnww_tag, type_desc from gates_semantic 
       where node_id = '{node_id}'
   """
    semantic_res = dbutils.fetch_all(pgsql.DEST_TRAJ, sql)
    if len(semantic_res) == 0:
        return 0
    if len(semantic_res) == 2:
        semantic_res_0, semantic_res_1 = semantic_res[0], semantic_res[1]
        type_arr = [semantic_res_0[4], semantic_res_1[4]]
        if type_arr[0] == "3" and type_arr[1] == "3":
            return 1
        elif "2" in type_arr and "0" in type_arr:
            return 1
        return 2
    type = semantic_res[0][4]
    nnww_tag = semantic_res[0][5]

    if type == "0":
        return 2  # 紧急门
    else:
        if "入口" in nnww_tag:
            return 1
        elif "出口" in nnww_tag:
            return 2  # 出口门
        else:
            if type in ["2", "3"]:
                return 1
            elif type in ["1"]:
                return 2  # 出口门
    return 2


def get_gates(long_node_ids: list) -> list:
    """
    获取大门
    """
    if len(long_node_ids) == 0:
        return []
    qry = f"""
        select in_linkid, out_linkid, node_id, type 
        from nav_gate where node_id in ({join_str(long_node_ids)})
    """
    return dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, qry)


def get_link_infos(long_link_ids: list) -> list:
    """
    获取 link
    """
    if len(long_link_ids) == 0:
        return []
    qry = f"""
    select link_id, dir, st_astext(geom), s_nid, e_nid, form 
    from nav_link where link_id in ({join_str(long_link_ids)})
    """
    return dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, qry)


def is_entry_gate_by_master_road(long_node_id: str) -> int:
    """
    根据道路母库判断是否可入
    可入返回 True
    紧急大门 type字段区分
    出口大门：首先是单向大门，out_link是內部路，属于出口大门
    """
    gates = get_gates([long_node_id])
    types = [a_gate[3] for a_gate in gates]
    if len(gates) == 0:
        # 确认不了
        return 0
    if 0 in types:
        # 紧急门
        return 2
    if len(gates) > 1:
        # 双向大门，认为可入可出，不太准
        return 1

    out_linkid = gates[0][1]
    link_infos = get_link_infos([out_linkid])
    if len(link_infos) == 0:
        return 0
    out_form = link_infos[0][5]
    out_forms = str(out_form).split(',')
    if '52' in out_forms:
        # out_link 属于内部路, 不知道是啥
        return 0

    # in_link 属于内部路 且 out_link 不属于内部路 说明可出；此处详细策略可参考
    # https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/CgIKhBCL9KWYDi
    in_link_id = gates[0][0]
    link_infos = get_link_infos([in_link_id])
    if len(link_infos) == 0:
        return 0
    in_form = link_infos[0][5]
    in_forms = str(in_form).split(',')
    if '52' in in_forms:
        # 大门，进入大门的是内部路，那么认为是出口
        return 2
    return 0


def get_link_geom(link_id):
    """
    获取link坐标
    """
    sql = "select st_astext(geom), s_nid, e_nid from nav_link where link_id = %s"
    link_wkt = dbutils.fetch_one(pgsql.ROAD_CONFIG_WITH_INDEX, sql, [link_id])
    return link_wkt


def get_link_kind(link_id):
    """
    获取link类型
    """
    sql = "select kind from nav_link where link_id = %s"
    link_kind_res = dbutils.fetch_one(pgsql.ROAD_CONFIG_WITH_INDEX, sql, [link_id])
    return link_kind_res


def get_covered_park_bids(park_wkt, park_bid):
    """
    获取和成果库压盖的停车场
    """
    sql = """
        select bid 
        from parking 
        where st_intersects(st_geomfromtext(%s, 4326), area)
        and show_tag in ('门前停车场') and status = 1 and bid != %s
    """
    bid_list = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [park_wkt, park_bid])
    return bid_list


def get_not_self_storefront_park_bids(park_bids) -> list:
    """
    获取不是自有渠道生成的门前停车场 bid
    """
    if len(park_bids) == 0:
        return []
    bids_set = set(park_bids)
    bids_str = ','.join([f"'{val}'" for val in bids_set])
    sql = f"""
        select bid 
        from parking_area_push
        where type = '1' and bid in ({bids_str})
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    if not res:
        return list(bids_set)
    resp_set = set([item[0] for item in res])
    return list(bids_set.difference(resp_set))


def get_online_road_relations(park_bid):
    """
    根据停车场bid取出线上所有的停车场出入口poi
    """
    sql = """
        select road_relation 
        from parking
        where parent_id  = %s and std_tag in ('出入口;停车场出入口') and status = 1
    """
    road_relations_res = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [park_bid])
    return road_relations_res


def get_online_road_relations_expect_access_bid(park_bid, access_bid):
    """
    根据停车场bid, 取出 除某个 bid 外的 线上所有的停车场出入口poi
    """
    sql = """
            select road_relation 
            from parking
            where parent_id  = %s and std_tag in ('出入口;停车场出入口') and status = 1 and bid != %s
        """
    road_relations_res = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [park_bid, access_bid])
    return road_relations_res


def get_park_bid_by_id(ctx: Context, park_id):
    """
    根据id获取停车场bid
    """
    sql = """
        select bid 
        from park_storefront_prod_parking 
        where bid = %s and status = %s and bid_status = 'effected' 
    """
    park_bid_res = dbutils.fetch_one(pgsql.POI_CONFIG, sql, [park_id, ctx.check_status])
    return park_bid_res


def get_long_link_id(short_link_id):
    """
    获取长link_id
    """
    sql = """
        SELECT sid FROM image_r WHERE tid = %s
        """
    res = dbutils.fetch_one(pgsql.TRANS_ID, sql, (short_link_id,))
    if res is None:
        return None
    return res[0]


def check_valid_polygon(geom_wkt):
    """
    检查面异常
    """
    sql = 'SELECT ST_IsValid(%s), ST_IsValidReason(%s)'
    res = dbutils.fetch_one(pgsql.TRANS_ID, sql, (geom_wkt, geom_wkt,))
    return res


def get_long_node_id_by_short(short_node_id):
    """
    获取长node_id
    """
    sql = f"select sid from image_n where tid = '{short_node_id}'"
    trans_node = dbutils.fetch_one(pgsql.TRANS_ID, sql)
    if trans_node is None:
        return None
    l_nodeid = trans_node[0]
    return l_nodeid


def get_nav_link(long_link_id):
    """
    1-双方向
    2-顺方向
    3-逆方向
    """
    sql = f"""
      select st_astext(geom), dir from nav_link where link_id = %s
    """
    link_res = dbutils.fetch_one(pgsql.ROAD_CONFIG_WITH_INDEX, sql, [long_link_id])
    return link_res


def is_in_aoi(point_wkt):
    """
    判断坐标点是否在AOI内
    """
    sql = f"""
         select * 
         from blu_face 
         where aoi_level = 2 AND src != 'SD' and st_contains(geom, ST_GeomFromText(%s, 4326))
    """
    res = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, [point_wkt])
    return res


def get_node_ids_from_link_id(long_link_id):
    """
    获取link_id两端的node_id
    """
    sql = f"""
        select s_nid, e_nid from nav_link where link_id = %s
    """
    node_ids = dbutils.fetch_one(pgsql.ROAD_CONFIG_WITH_INDEX, sql, [long_link_id])
    return node_ids


def join_str(vals: list) -> str:
    """
    拼接
    """
    return ','.join([f"'{val}'" for val in vals])


def any_node_id_invalid(node_ids: list) -> bool:
    """
    任一 node_id 失效了，就返回 True
    """
    if len(node_ids) == 0:
        return False
    sql = f"""
    select node_id from nav_node where node_id in ({join_str(node_ids)})
    """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql)
    if not res:
        return True
    print(len(res), 'node')
    return len(res) != len(set(node_ids))


def any_link_id_invalid(link_ids: list) -> bool:
    """
    任一 link_id 失效了就返回 True
    """
    if len(link_ids) == 0:
        return False

    sql = f"""
        select link_id from nav_link where link_id in ({join_str(link_ids)})
        """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql)
    if not res:
        return True
    return len(res) != len(set(link_ids))


def get_short_node_by_long_node(long_node_id):
    """
    获取短 node
    """
    sql = f"select tid from image_n where sid = '{long_node_id}'"
    trans_node = dbutils.fetch_one(pgsql.TRANS_ID, sql)
    if trans_node is None:
        return None
    return trans_node[0]


def get_long_node_infos(long_node_ids) -> list:
    """
    获取长 node 信息
    """
    if not long_node_ids:
        return []
    sql = f"select node_id, form from nav_node where node_id in({join_str(long_node_ids)})"
    return dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql)


def get_cover_nav_lane_boundary(geom: str) -> list:
    """
    获取压盖道路的边界
    """
    qry = f"""
    select lane_boundary_id from nav_lane_boundary where st_intersects(geom, st_geomfromtext('{geom}', 4326))
    """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, qry)
    if not res:
        return []
    return [item[0] for item in res]


def get_cover_nav_lane_road_pg(geom: str) -> list:
    """
    获取压盖路头
    """
    qry = f"""
    select road_pg_id from nav_lane_road_pg where st_intersects(geom, st_geomfromtext('{geom}', 4326))
    """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, qry)
    if not res:
        return []
    return [item[0] for item in res]


def get_link_info_by_node(node_list):
    """
    计算Node关联LINK
    """
    if len(node_list) == 0:
        return []

    node_str = "','".join(node_list)
    sql = f"select adjoin_nid from nav_node where node_id in('{node_str}') and  adjoin_nid !='' "
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql)
    if res:
        node_list += [item[0] for item in res]
    node_str = "','".join(node_list)
    sql = f"""
        select link_id, st_astext(geom) from nav_link 
        where (s_nid in('{node_str}') or e_nid in('{node_str}')) and kind > 7
    """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql)
    return res


def recover_access_status(park_bid):
    """
    恢复出入口的状态
    """
    qry = f"""
    update park_storefront_prod_access 
    set status = 'READY_ONLINE', check_memo = ''  
    where parent_bid = '{park_bid}' and status = 'CHECK_FAILED'
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        print(qry, park_bid)
        pgsql.execute(conn, qry)


def get_online_park(park_bid):
    """
    获取线上停车场
    """
    qry = f"""
    select bid, st_astext(area) 
    from parking 
    where bid = '{park_bid}'
    """
    res = dbutils.fetch_one(pgsql.BACK_CONFIG, qry)
    return res


def get_intersects_online_parks(park_bid, wkt):
    """
    获取相交的线上停车场
    """
    qry = f"""
    select bid, st_astext(area) 
    from parking 
    where bid != '{park_bid}' and status = 1
    and st_intersects(area, st_geomfromtext('{wkt}', 4326)) 
    and show_tag = '门前停车场'
    """
    res = dbutils.fetch_all(pgsql.BACK_CONFIG, qry)
    return res


def access_ids_are_repaired(access_ids: list, reason: str, user_name: str = None) -> bool:
    """
    出入口是否都修复过，如果都修复过，返回 True
    """
    access_str_ids = ','.join([f"'{str(aid)}'" for aid in list(set(access_ids))])
    qry = f"""
    select distinct(prev_id) from park_storefront_repair 
    where prev_tab = 'park_storefront_prod_access' 
        and prev_id in ({access_str_ids}) and reason like '%{reason}%' and status = 'DONE' 
    """
    if user_name:
        qry += f" and user_name = '{user_name}'"
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    print(qry, access_str_ids, res)
    return len(res) == len(set(access_ids))


def park_id_area_repaired(park_id: int, reason: str) -> bool:
    """
    停车场是否被修复过，修复过返回 True
    """
    qry = f"""
    select id from park_storefront_repair 
    where prev_tab = 'park_storefront_prod_parking' and prev_id = '{park_id}' 
        and user_name = 'park_storefront_verify_pushed'  
        and reason like '%{reason}%' 
        and status = 'DONE' 
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, qry)
    if not res:
        return False
    return True


def add_park_offline(bid: str, user: str, reason: str):
    """
    新增停车线下线干预
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        qry = f"""
            insert into park_storefront_intervention (bid, type, username, reason) 
            values ('{bid}', 'PARK_OFFLINE', '{user}', '{handle_apostrophe(reason)}')
            """
        pgsql.execute(conn, qry)


def area_allow_overlap(bids: list) -> bool:
    """
    面允许压盖返回 True
    在同一个更新作业中，两个作业结论都是有效
    """
    bids = list(set(bids))
    if len(bids) < 2:
        return False
    qry = f"""
    SELECT info_id
    FROM park_storefront_turing_result
    WHERE bid IN ({join_str(bids)})
      AND precise IN ('1', '3')
      AND data_type = '1214' 
      AND recall = '1' 
    GROUP BY info_id
    HAVING COUNT(DISTINCT bid) = {len(bids)};
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    if not res:
        return False
    return len(res) > 0


def handle_apostrophe(val: str) -> str:
    """
    单引号处理
    """
    return val.replace("'", "''")

