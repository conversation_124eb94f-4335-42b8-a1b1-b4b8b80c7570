"""
聚类点簇投影相关的逻辑
"""
from typing import TypeVar, Callable

import numpy as np
import shapely.ops as shapely_opts
from shapely import Point, Polygon, LineString
from sklearn.cluster import DBSCAN

from src.tools import linq

T = TypeVar("T")


def cluster_by_dbscan(cases: list[T], eps: float, min_samples: int, key: Callable[[T], Point] = None) -> list[list[T]]:
    """
    聚类算法
    """
    points = [key(case) for case in cases] if key else cases
    data = [[x.x, x.y] for x in points]
    data = np.array(data)

    # 应用DBSCAN算法
    db = DBSCAN(eps=eps, min_samples=min_samples).fit(data)

    # noinspection PyUnresolvedReferences
    labels = db.labels_

    # 将聚类标签映射回原始数据
    case_with_labels = zip(cases, labels)
    clusters = linq.group_by(case_with_labels, key=lambda x: x[1], value=lambda x: x[0])
    return [cluster for label, cluster in clusters.items() if label >= 0]


def get_projection(target_line: LineString, points: list[Point]) -> tuple[LineString, list[tuple[Point, Point]]]:
    """
    以给定的点簇截取给定线段上的一部分，即：点簇投影所占的部分
    :param target_line: 投影的目标线段
    :param points: 点集合
    :return: 投影点所截取到的 target_line, (原始点, 投影点)
    """
    project_distances = [(p, target_line.project(p)) for p in points]
    project_points = [(p, target_line.interpolate(d), d) for p, d in project_distances]

    project_points.sort(key=lambda x: x[-1])
    min_project_len = project_points[0][-1]
    max_project_len = project_points[-1][-1]
    project_line = shapely_opts.substring(target_line, start_dist=min_project_len, end_dist=max_project_len)

    point_pairs = [(p1, p2) for p1, p2, _d in project_points]
    return project_line, point_pairs


def get_projection_cluster_again(
    target_line: LineString, points: list[Point], eps: float, min_samples: int, min_disparity: float
) -> tuple[LineString, list[tuple[Point, Point]]]:
    """
    以给定的点簇截取给定线段上的一部分，采用二次聚类，可以修复投影绕一周的问题
    :param target_line: 投影的目标线段
    :param points: 点集合
    :param eps: dbscan 参数
    :param min_samples: dbscan 参数
    :param min_disparity: 比例悬殊阈值
    :return: 投影点所截取到的 target_line, (原始点, 投影点)
    """
    project_distances = [(p, target_line.project(p)) for p in points]
    distances = np.array([d for _, d in project_distances])
    distances = distances.reshape(-1, 1)
    db = DBSCAN(eps=eps, min_samples=min_samples).fit(distances)
    # noinspection PyUnresolvedReferences
    labels = db.labels_
    if len(set(labels)) > 1:
        candidate_labels = set()
        # 计算点簇比例
        labels_count = linq.count_by(labels, key=lambda x: x)
        labels_count = sorted(labels_count.items(), key=lambda x: x[1], reverse=True)
        most_label, most_count = labels_count[0]
        other_label = sum(c for _, c in labels_count[1:])
        disparity = abs(1.0 * (most_count - other_label) / len(points))
        candidate_labels.add(most_label)

        if disparity < min_disparity:
            raise ValueError(f"failed to cluster again, disparity: {disparity}, labels count: {labels_count}")

        project_distances = [pd for pd, l in zip(project_distances, labels) if l == most_label]

    project_points = [(p, target_line.interpolate(d), d) for p, d in project_distances]

    project_points.sort(key=lambda x: x[-1])
    min_project_len = project_points[0][-1]
    max_project_len = project_points[-1][-1]
    project_line = shapely_opts.substring(target_line, start_dist=min_project_len, end_dist=max_project_len)

    point_pairs = [(p1, p2) for p1, p2, _d in project_points]
    return project_line, point_pairs


def get_distances(point_pairs: list[tuple[Point, Point]]) -> np.ndarray:
    """
    获取所有点对之间的距离
    """
    return np.array([p1.distance(p2) for p1, p2 in point_pairs])


def to_points(points: list[tuple[float, float]]) -> list[Point]:
    """
    将坐标列表转换为 Point 对象列表
    """
    return [Point(x, y) for x, y in points]


def get_linestring_from_full_polygon(geom: Polygon, points: list[Point]) -> LineString:
    """
    将 Polygon 转为 LineString，会考虑点簇位置，使得起终点远离点簇。
    FIXME: 有 bug，若点簇已经包围了绝大部分的 Polygon 边界，会导致离点簇中心点最远的点处于点簇范围内。
    """
    center_x = sum(p.x for p in points) / len(points)
    center_y = sum(p.y for p in points) / len(points)
    center_point = Point(center_x, center_y)

    geom_points = [Point(x, y) for x, y in geom.exterior.coords]
    geom_points = geom_points[1:]

    farthest_point = max(geom_points, key=lambda x: x.distance(center_point))
    farthest_index = geom_points.index(farthest_point)
    ret = [*geom_points[farthest_index:], *geom_points[:farthest_index], geom_points[farthest_index]]
    return LineString(ret)
