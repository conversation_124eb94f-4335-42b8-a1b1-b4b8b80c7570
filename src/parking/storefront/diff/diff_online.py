"""
停车场线上差分
"""
import re
import sys

import tqdm as tqdm
from dataclasses import dataclass, field
from loguru import logger
from src.parking.recognition import dbutils
from src.tools import pgsql, utils, tsv
from typing import Optional
from shapely import wkt
from shapely.geometry import MultiPoint, Polygon

NORMAL_PARK_SHOW_TAG = ['地上停车场', '地下停车场', '立体停车场', '停车场']
ROAD_PARK_SHOW_TAG = ['路侧停车场', '临时停车点']


@dataclass
class DiffAchieve:
    """
    POI 信息
    """
    can_push: str = field(default=True, init=False)
    remove_bids: list[str] = field(default_factory=list, init=False)
    parent_id: str = field(default='', init=False)
    remark: str = field(default='', init=False)
    park_access_geom_list: list[str] = field(default_factory=list, init=False)


@dataclass
class Park:
    """
    停车场信息
    """
    bid: str
    name: str
    gcj_geom: str
    parent_id: str
    show_tag: str
    precise: int


def get_diff_achieve_by_area(area: str, park_access_list: list[str]) -> DiffAchieve:
    """
    门前线上差分
    """
    achieve = DiffAchieve()
    achieve.park_access_geom_list = park_access_list
    # 获取压盖停车场面
    intersects_park_list = get_intersects_park_list_by_area(area)
    if len(intersects_park_list) == 0:
        achieve.remark = "无相交停车场"
        return achieve
    intersects_park_list = filter_online_park(intersects_park_list)
    # aoi 存在相同出入口
    aoi_exist_same_park_access(achieve, intersects_park_list)
    if not achieve.can_push:
        return achieve
    # aoi 不存在相同出入口
    aoi_exist_not_same_park_access(achieve, intersects_park_list)
    if not achieve.can_push:
        return achieve
    # 非AOI场景精准停车场
    no_aoi_precise_park(achieve, intersects_park_list)
    if not achieve.can_push:
        return achieve

    # 获取需要下线停车场
    aoi_no_precise_park(achieve, intersects_park_list)
    no_aoi_no_precise_park(achieve, intersects_park_list)
    intersects_old_storefront_park(achieve, intersects_park_list)
    intersects_tmp_park(achieve, intersects_park_list)
    return achieve


def filter_online_park(intersects_park_list: list[Park]) -> list[Park]:
    """
    过滤清单bids
    :param online_bid_filepath:
    :param intersects_park_list:
    :return:
    """
    online_bid_filepath = "./gate_front_online_bid.csv"
    with open(online_bid_filepath, encoding='utf-8') as file:
        online_bids = set([line.strip() for line in file.readlines()])
    return [park_info for park_info in intersects_park_list if park_info.bid not in online_bids]


def aoi_exist_same_park_access(achieve: DiffAchieve, intersects_park_list: list[Park]):
    """
    停车场与AOI存在相同出入口
    :return:
    """
    for park_info in intersects_park_list:
        parent_id = park_info.parent_id
        if park_info.show_tag not in NORMAL_PARK_SHOW_TAG or parent_id == '0' \
                or parent_id == '' or park_info.precise != 1:
            continue
        # 判断停车场是否是AOI子点
        if not park_in_aoi(parent_id):
            logger.info(f"{park_info.bid}不在aoi范围内")
            continue
        # 判断门前停车场子点与线上停车场子点是否一致
        same_park_access_bid = storefront_park_access_diff(achieve.park_access_geom_list, park_info.bid)
        if same_park_access_bid:
            achieve.can_push = False
            achieve.remark = f"AOI场景下存在压盖停车场;出入口位置相同:park_access_bid:{same_park_access_bid}"
            return


def aoi_exist_not_same_park_access(achieve: DiffAchieve, intersects_park_list: list[Park]):
    """
    停车场与AOI不存在相同出入口
    :return:
    """
    for park_info in intersects_park_list:
        parent_id = park_info.parent_id
        if park_info.show_tag not in NORMAL_PARK_SHOW_TAG \
                or parent_id == '0' or parent_id == '' or park_info.precise != 1:
            continue
        # 判断停车场是否是AOI子点
        if not park_in_aoi(parent_id):
            logger.info(f"{park_info.bid}不在aoi范围内")
            continue
        achieve.can_push = False
        achieve.remark = f"AOI场景下存在压盖停车场;出入口位置不同:aoi_bid:{parent_id}"
        return


def no_aoi_precise_park(achieve: DiffAchieve, intersects_park_list: list[Park]):
    """
    非AOI场景精准停车场
    :return:
    """
    no_aoi_precise_park_list = [park_info for park_info in intersects_park_list if
                                park_info.precise == 1 and park_info.show_tag in NORMAL_PARK_SHOW_TAG]
    if len(no_aoi_precise_park_list) > 0:
        bids = [park_info.bid for park_info in no_aoi_precise_park_list]
        achieve.can_push = False
        achieve.remark += f"存在非aoi场景精准停车场:park_bid:{bids}|"
    return


def aoi_no_precise_park(achieve: DiffAchieve, intersects_park_list: list[Park]):
    """
    AOI场景下存在非精准停车场
    :return:
    """
    remove_bids_list = []
    aoi_bid = ''
    for park_info in intersects_park_list:
        parent_id = park_info.parent_id
        if park_info.show_tag not in NORMAL_PARK_SHOW_TAG \
                or parent_id == '0' or parent_id == '' or park_info.precise == 1:
            continue
        # 判断停车场是否是AOI子点
        if not park_in_aoi(parent_id):
            logger.info(f"{park_info.bid}不在aoi范围内")
            continue
        aoi_bid = park_info.parent_id
        remove_bids_list.append(park_info.bid)
    achieve.can_push = True
    achieve.remove_bids = remove_bids_list
    achieve.parent_id = aoi_bid
    achieve.remark = f"AOI场景下存在压盖非精准停车场;aoi_bid:{aoi_bid};"
    return


def no_aoi_no_precise_park(achieve: DiffAchieve, intersects_park_list: list[Park]):
    """
    非AOI场景非精准停车场
    :return:
    """
    remove_bids_list = []
    for park_info in intersects_park_list:
        parent_id = park_info.parent_id
        if park_info.show_tag not in NORMAL_PARK_SHOW_TAG or park_info.precise == 1:
            continue
        # 判断停车场是否是AOI子点
        if parent_id != '' and parent_id != '0' and park_in_aoi(parent_id):
            continue
        remove_bids_list.append(park_info.bid)
    achieve.can_push = True
    achieve.remove_bids = list(set(achieve.remove_bids + remove_bids_list))
    achieve.remark = f"非AOI场景下存在非精准停车场;park_bid:{remove_bids_list}"
    return


def intersects_old_storefront_park(achieve: DiffAchieve, intersects_park_list: list[Park]):
    """
    如果跟旧的门前压盖则下线旧门前停车场
    :return:
    """
    old_storefront_park_list = [park_info for park_info in intersects_park_list if park_info.show_tag == '门前停车场']
    if len(old_storefront_park_list) > 0:
        bids = [park_info.bid for park_info in old_storefront_park_list]
        achieve.can_push = True
        achieve.remove_bids = list(set(achieve.remove_bids + bids))
        achieve.remark += f"存在门前停车场:park_bid:{bids}|"
    return


def intersects_tmp_park(achieve: DiffAchieve, intersects_park_list: list[Park]):
    """
    是否存在临时停车场
    :return:
    """
    tmp_park_list = [park_info for park_info in intersects_park_list if park_info.show_tag == '临时停车点']
    if len(tmp_park_list) > 0:
        remove_bids = [park_info.bid for park_info in tmp_park_list]
        achieve.can_push = True
        achieve.remove_bids = list(set(achieve.remove_bids + remove_bids))
        achieve.remark += f"存在临时停车场;park_bid:{remove_bids}|"
    return


def park_in_aoi(park_parent_id: str) -> bool:
    """
    判断停车场是否在AOI下面
    :param park_parent_id:
    :return:
    """
    poi_info = get_poi_info(park_parent_id)
    if not poi_info:
        return False
    main_bids = [park_parent_id]
    if poi_info[1] != '' and poi_info[1] != '0':
        main_bids.append(poi_info[1])
    # 查询AOI是否存在
    aoi_list = get_aoi_info(main_bids)
    return True if aoi_list and len(aoi_list) > 0 else False


def storefront_park_access_diff(storefront_park_access: list[str], park_bid) -> bool:
    """
    门前停车场出入口与线上停车场出入口是否一致
    :param storefront_park_access:
    :param park_bid:
    :return:
    """
    point_list = [wkt.loads(point_gcj) for point_gcj in storefront_park_access]
    multi_point = MultiPoint(point_list)
    sql = f"""
        select bid from parking 
        where parent_id=%s 
            and std_tag = '出入口;停车场出入口' 
            and st_intersects(gcj_geom, st_geomfromtext(%s, 4326))
    """
    res = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, [park_bid, multi_point.buffer(0.00005).wkt])
    return res[0] if res and len(res) > 0 else False


def get_intersects_park_list_by_area(park_geom: str) -> list[Park]:
    """
    获取压盖的停车场面
    :param park_geom:
    :return:
    """
    sql = f"""
        select bid,name,st_astext(gcj_geom) as gcj_geom,parent_id,show_tag,precise from parking
        where status=1 
            and std_tag in('交通设施;停车场', '交通设施;路侧停车位')
            and (st_intersects(area, st_geomfromtext(%s, 4326)) 
            or st_intersects(gcj_geom, st_geomfromtext(%s, 4326)))
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [park_geom, park_geom])
    return [Park(bid, name, gcj_geom, parent_id, show_tag, precise)
            for bid, name, gcj_geom, parent_id, show_tag, precise in ret]


def get_poi_info(bid: str):
    """
    获取POI信息
    """
    sql = f"""
        select bid,relation_bid from poi where bid=%s
    """
    return dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [bid])


def get_aoi_info(main_bids: list[str]):
    """
    获取AOI信息
    """
    sql = f"""
        select poi_bid from blu_face_poi where poi_bid = Any(%s)
    """
    return dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [main_bids])


if __name__ == "__main__":
    bid = sys.argv[1]
    bids = bid.split(',')
    if bid == '0':
        online_bid_filepath = "./gate_front_online_bid.csv"
        with open(online_bid_filepath, encoding='utf-8') as file:
            bids = list(set([line.strip() for line in file.readlines()]))
    sql = f"""
        select bid,st_astext(area) from parking where bid=ANY(%s)
    """
    storefront_park_list = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [bids])
    achieve_filepath = "./diff_online_achieve.csv"
    with open(achieve_filepath, 'w', encoding='utf-8') as file:
        file.write("bid,can_push,remove_bids,parent_id,remark\n")
        for storefront_park in tqdm.tqdm(storefront_park_list):
            bid, area = storefront_park
            # 查询子点信息
            sql = f"select st_astext(gcj_geom) from parking where parent_id=%s"
            storefront_park_access_list = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [bid])
            park_access_wkt_list = [item[0] for item in storefront_park_access_list] \
                if storefront_park_access_list else []
            achieve = get_diff_achieve_by_area(area, park_access_wkt_list)
            file.write(f"{bid},{achieve.can_push},{achieve.remove_bids},{achieve.parent_id},{achieve.remark}\n")
