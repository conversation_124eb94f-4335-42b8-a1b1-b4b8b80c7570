"""
几何差分
"""
import copy
import logging
import math
import multiprocessing
import uuid

import dataclasses
from typing import List, Tuple, Union, Dict
from functools import lru_cache

import shapely.wkt
import shapely.affinity
from shapely import ops
from shapely.geometry import LineString, Point, MultiPoint
from tqdm import tqdm

from src.parking.storefront.post_process import autocomplete as auto_cpt
from src.parking.storefront import unlimited_cluster
from src.parking.recognition import dbutils
from src.tools import pgsql, utils, tsv
from src.parking.storefront.utils import geometric


def project(line: LineString, pt: Point) -> float:
    """
    点映射到线上
    """
    return line.project(pt, normalized=True)


def interpolate(line: LineString, location: float) -> Point:
    """
    根据映射的位置获取点信息
    """
    return line.interpolate(location, normalized=True)


def get_line_by_location(line: LineString, start_location, end_location):
    """
    通过位置获取线
    """
    start_point = interpolate(line, start_location)
    end_point = interpolate(line, end_location)
    coords = [
        (start_point.x, start_point.y)
    ]
    # 中间的用线上的点
    for a_coord in line.coords:
        location = project(line, Point(a_coord))
        if location >= end_location:
            break
        if start_location < location < end_location:
            coords.append(a_coord)
    # 添加终点
    coords.append((end_point.x, end_point.y))
    return LineString(coords)


@dataclasses.dataclass
class Link:
    """
    nav_link 对象
    """
    link_id: str
    s_nid: str
    e_nid: str
    kind: int
    form: str
    direction: int
    geom: LineString

    def projected(self, pt: Point) -> float:
        """
        获取点映射在线上的位置
        """
        return project(self.geom, pt)

    def get_point_by_projected_loc(self, location: float) -> Point:
        """
        根据映射的位置获取点信息
        """
        return interpolate(self.geom, location)


def get_links_by_wkt(point: str, buffer: float) -> List[Link]:
    """
    获取范围附近的 link
    """
    sql = """
        select link_id, s_nid, e_nid, kind, form, dir, st_astext(geom)
        from nav_link
        where 1 = 1
            and st_dwithin(%s, geom, %s)
            and kind <= 7 and viad != 1
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{point}", buffer])
    return [
        Link(
            link_id=link_id,
            s_nid=s_nid,
            e_nid=e_nid,
            kind=kind,
            form=form,
            direction=direction,
            geom=shapely.wkt.loads(geom),
        )
        for link_id, s_nid, e_nid, kind, form, direction, geom in ret
    ]


@dataclasses.dataclass
class Polygon:
    """
    几何面
    """
    face_id: str
    geom: str
    uid: int = 0
    precision = 1e-5 * 5
    extra: dict = None

    def __post_init__(self):
        self.geo = shapely.wkt.loads(self.geom)
        self.exteriors = []  # 外边界
        self.exterior_coords = self._extract_exterior_coords()
        self.shell = ops.linemerge(self.exteriors) if not self.geo.is_empty else LineString([])
        self.exterior_add_points_list = [add_points_to_line(exterior, self.precision).coords for exterior in
                                         self.exteriors]

    def _extract_exterior_coords(self) -> list:
        """
        提取外轮廓的点
        """
        simple_geo = self.geo
        if simple_geo.geom_type == 'Polygon':
            self.exteriors = [simple_geo.exterior]
            return simple_geo.exterior.coords
        if simple_geo.geom_type != 'MultiPolygon':
            raise Exception(f"{simple_geo.geom_type} 不支持")

        coords = []
        for _geo in simple_geo.geoms:
            self.exteriors.append(_geo.exterior)
            coords += _geo.exterior.coords
        return coords

    def get_exterior_coords(self) -> list:
        """
        获取外轮廓的点
        """
        return self.exterior_coords

    def get_shell_add_points(self) -> list:
        """
        获取外壳加密后的所有点
        """
        coords = []
        for _coords in self.exterior_add_points_list:
            coords += _coords
        return coords

    def point_foot_on_boundary(self, pt: Point) -> bool:
        """
        点的垂足是否在线上，在返回 True
        """
        for a_exterior in self.exteriors:
            if get_boundary_foot_point(pt, a_exterior) is not None:
                return True
        return False

    def get_any_foot_boundary(self, pt: Point):
        """
        获取边上任一一个垂足
        """
        for a_exterior in self.exteriors:
            foot = get_boundary_foot_point(pt, a_exterior)
            if foot is not None:
                return foot
        return None

    def point_foot_distance_meet_standard(self, pt: Point, distance: float):
        """
        点到垂足的距离符合要求；小于 <= distance
        """
        for a_exterior in self.exteriors:
            for foot in get_boundary_foot_pts(pt, a_exterior):
                if foot is None:
                    continue
                if foot.distance(pt) <= distance:
                    return True
        return False

    def get_shell(self) -> LineString:
        """
        获取外壳
        """
        return self.shell

    def __eq__(self, other: 'Polygon'):
        if isinstance(other, Polygon):
            return self.face_id == other.face_id and self.geo.equals(other.geo)
        return False

    def __hash__(self):
        return hash(self.geom + str(self.face_id))


@dataclasses.dataclass
class ProjectedLink:
    """
    映射的 link
    """
    start_location: float  # 开始位置点 取值范围 [0, 1]
    end_location: float  # 结束位置点 取值范围 [0, 1]
    link: Link
    dist: float = 0

    polygon_start_location: float = 0
    polygon_end_location: float = 0
    polygon: Polygon = None
    azimuth_angle: float = None

    def __post_init__(self):
        self.line = self._get_projected_line()
        self.start_location = min(self.start_location, self.end_location)
        self.end_location = max(self.start_location, self.end_location)

        self.polygon_start_location = min(self.polygon_start_location, self.polygon_end_location)
        self.polygon_end_location = max(self.polygon_start_location, self.polygon_end_location)

    def _get_projected_line(self) -> LineString:
        """
        获取映射的线
        """
        start_location = self.start_location
        end_location = self.end_location
        return self.get_line_by_location(start_location, end_location)

    def get_line_by_location(self, start_location, end_location):
        """
        通过位置获取线
        """
        return get_line_by_location(self.link.geom, start_location, end_location)

    def get_link_id(self) -> str:
        """
        获取对应的 link_id
        """
        return self.link.link_id

    def intersection(self, start: float, end: float) -> LineString:
        """
        相交
        """
        if start >= self.end_location or end <= self.start_location:
            return LineString([])
        return self.get_line_by_location(max([start, self.start_location]), min(end, self.end_location))

    def union(self, start: float, end: float) -> LineString:
        """
        聚合
        """
        if start >= self.end_location or end <= self.start_location:
            return ops.linemerge([self.line, self.get_line_by_location(start, end)])
        return self.get_line_by_location(min([start, self.start_location]), max(end, self.end_location))

    def difference(self, start: float, end: float) -> LineString:
        """
        减去
        """
        if start >= self.end_location or end <= self.start_location:  # 没有交集
            return self.line
        if start <= self.start_location and end >= self.end_location:
            return LineString()
        if start > self.start_location and end < self.end_location:  # 在中间，包含了
            return ops.linemerge([
                self.get_line_by_location(self.start_location, start),
                self.get_line_by_location(end, self.end_location),
            ])
        if start > self.start_location:
            return self.get_line_by_location(self.start_location, start)
        else:
            return self.get_line_by_location(end, self.end_location)

    def new_by_polygon_location(self, start: float, end: float) -> 'ProjectedLink':
        """
        根据几何位置上的点重新映射
        """
        p_s_pt = interpolate(self.polygon.shell, start)
        p_e_pt = interpolate(self.polygon.shell, end)

        s_location = self.link.projected(p_s_pt)
        e_location = self.link.projected(p_e_pt)

        return ProjectedLink(
            start_location=s_location,
            end_location=e_location,
            link=self.link,
            dist=self.dist,
            polygon_start_location=start,
            polygon_end_location=end,
            polygon=self.polygon,
        )


def _get_min_street_contain_pt(pt_wkt: str) -> str:
    """
    获取包含 pt 的最小街区 wkt
    """
    tab = 'street_region'
    qry = (f"select st_astext(geom) geom, st_area(geom) area from {tab} "
           f"where st_intersects(geom, st_geomfromtext('{pt_wkt}', 4326))")
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    if res is None or not res:
        return ''

    streets = sorted(res, key=lambda x: x[1])
    return streets[0][0]


def _get_shortest_foot_pt_with_street(pt_wkt: str, polygon: Polygon) -> Union[None, Point]:
    """
    获取pt和街区的最短距离垂足
    """
    min_street_wkt = _get_min_street_contain_pt(pt_wkt)
    if min_street_wkt == '':
        return None
    pt_geo = shapely.wkt.loads(pt_wkt)
    st_geo = shapely.wkt.loads(min_street_wkt).buffer(0.00)

    if utils.calc_ioa(min_street_wkt, polygon.geom) < 0.5:
        return None

    resp = []
    for foot in get_boundary_foot_pts(pt_geo, st_geo.exterior):
        if foot is None:
            continue
        resp.append((foot, foot.distance(pt_geo)))

    resp = sorted(resp, key=lambda x: x[1])
    if len(resp) == 0:
        return None
    return resp[0][0]


def get_related_store_pois(polygon: Polygon, search_buffer: float) -> List[auto_cpt.Poi]:
    """
    获取相关的门前 poi
    """
    pois = auto_cpt.get_relation_store_pois(polygon.geom, search_buffer)
    resp = []
    for a_poi in pois:
        if polygon.geo.intersects(a_poi.point.buffer(1e-5 * 0.5)):
            resp.append(a_poi)
            continue

        if not polygon.point_foot_distance_meet_standard(a_poi.point, search_buffer):
            continue
        foot = _get_shortest_foot_pt_with_street(a_poi.point.wkt, polygon)
        if foot is None:
            continue
        line = LineString([foot, a_poi.point])
        if line.intersects(polygon.geo):
            resp.append(a_poi)
    return resp


def get_projected_link(polygon: Polygon, link: Link) -> Union[ProjectedLink, None]:
    """
    获取映射的 link
    """
    location = []
    for a_coord in polygon.get_exterior_coords():
        location.append(link.projected(Point(a_coord)))

    if len(location) < 2:
        return None
    location.sort()
    link = ProjectedLink(
        start_location=location[0],
        end_location=location[-1],
        link=link,
    )
    if link.line.length < 1e-5 * 0.01:
        return None
    return link


def get_foot_point(pt: Point, line: LineString):
    """
    获取垂足，若 垂足 不在线上返回 None
    """
    try:
        return geometric.get_foot_point(pt, line)
    except Exception as e:
        logging.exception(e)
        return None


def get_boundary_foot_point(pt: Point, boundary: LineString):
    """
    获取垂足，若 垂足 不在 boundary 上返回 None
    """
    for foot in get_boundary_foot_pts(pt, boundary):
        if foot is not None:
            return foot
    return None


def get_boundary_foot_pts(pt: Point, boundary: LineString):
    """
    获取所有垂足
    有 极端情况，垂足可能刚好落在某个点上，若分开看，可能会因为精度导致丢失
    """
    yield get_foot_point(pt, boundary)

    coords = boundary.coords
    for i in range(0, len(coords) - 1):
        line = shapely.geometry.LineString([coords[i], coords[i + 1]])
        foot = get_foot_point(pt, line)
        yield foot


def get_related_road_links(polygon: Polygon, search_buffer: float) -> List[Link]:
    """
    获取相关的道路 link
    """
    links = get_links_by_wkt(polygon.geom, search_buffer)
    id2link = {}
    for a_coord in polygon.get_exterior_coords():
        ret_link = unlimited_cluster.filter_link(Point(a_coord), links)
        if ret_link is None:
            continue
        id2link[ret_link.link_id] = ret_link

    # 特殊情况，link 很短，可能 geom 的任一点的垂点都不会落在 link 上，所以需要反过来确认
    for a_link in links:
        if a_link.link_id in id2link:
            continue

        # 若有点在线上
        on_line = False
        for b_coord in polygon.get_exterior_coords():
            b_pt = Point(b_coord)
            foot = get_foot_point(b_pt, a_link.geom)
            if not foot:
                continue
            if b_pt.distance(foot) > search_buffer:
                continue
            line = LineString([b_pt, foot])
            intersected = False
            for b_link in id2link.values():
                if line.intersects(b_link.geom):
                    intersected = True
                    break
            if not intersected:
                id2link[a_link.link_id] = a_link
                on_line = True
        if on_line:
            continue

        # 用中心线判断
        perpendicular = _get_center_perpendicular(a_link.geom, polygon)
        if perpendicular is None or perpendicular.length > search_buffer:
            continue

        # 中点的垂线不能和已有的线相交
        intersected = False
        for b_link in id2link.values():
            if perpendicular.intersects(b_link.geom):
                intersected = True
                break
        if intersected:
            continue
        id2link[a_link.link_id] = a_link
    return list(id2link.values())


def azimuth_angle(x1, y1, x2, y2) -> float:
    """
    计算方位角函数[0,360)
    """
    angle = 0.0
    dx = x2 - x1
    dy = y2 - y1
    if x2 == x1:
        angle = math.pi / 2.0
        if y2 == y1:
            angle = 0.0
        elif y2 < y1:
            angle = 3.0 * math.pi / 2.0
    elif y2 == y1:
        if x2 > x1:
            angle = 0
        else:
            angle = math.pi
    elif x2 > x1 and y2 > y1:
        angle = math.atan(dy / dx)
    elif x2 > x1 and y2 < y1:
        angle = 3.0 * math.pi / 2.0 + math.atan(dx / -dy)
    elif x2 < x1 and y2 < y1:
        angle = 3.0 * math.pi / 2.0 + math.atan(dx / -dy)
    elif x2 < x1 and y2 > y1:
        angle = math.pi + math.atan(dy / dx)
    return angle * 180 / math.pi


def cal_angle(a: Point, b: Point, c: Point) -> float:
    """
    计算 向量 ab 与 ac 之间的夹角度(角A) 返回值在 [0, 180] 之间
    """
    ang1 = azimuth_angle(a.x, a.y, b.x, b.y)
    ang2 = azimuth_angle(a.x, a.y, c.x, c.y)

    ang3 = math.fabs(ang1 - ang2)
    if ang3 > 180:
        ang3 = 360 - ang3
    return ang3


def _get_center_perpendicular(line: LineString, polygon: Polygon) -> Union[None, LineString]:
    """
    作一条垂线，线的中点，到面的垂线
    """
    center = interpolate(line, 0.5)
    start = interpolate(line, 0)
    resp = []
    for a_exterior in polygon.exteriors:
        for foot in get_boundary_foot_pts(center, a_exterior):
            if foot is None:
                continue
            angle = cal_angle(center, foot, start)
            if angle < 80 or angle > 110:
                continue
            resp.append(LineString([center, foot]))

    if len(resp) == 0:
        return None
    resp.sort(key=lambda x: x.length)
    return resp[0]


def get_related_projected_links(polygon: Polygon, search_buffer: float) -> List[ProjectedLink]:
    """
    获取相关的映射的线
    """
    resp = get_projected_links(polygon, search_buffer)
    filter = ProjectedLinksFilter(projects=resp, distance=search_buffer)
    result = filter.filter()
    return result


def add_points_to_line_by_distance(x0, y0, x1, y1, distance_interval):
    """
    线上的点加密
    """
    # 计算线段的长度
    line_length = Point(x0, y0).distance(Point(x1, y1))

    # 计算需要增加的点的数量（不包括端点）
    num_points = math.ceil(line_length / distance_interval) - 1

    # 存储新点的坐标列表
    new_points = []

    # 使用线性插值计算新点的坐标
    for i in range(1, num_points + 1):
        t = i / (num_points + 1)  # 插值参数，从0到1
        new_x = x0 + t * (x1 - x0)
        new_y = y0 + t * (y1 - y0)
        new_points.append((new_x, new_y))

    # 返回包含端点和新点的坐标列表
    return [(x0, y0)] + new_points + [(x1, y1)]


def add_points_to_line(line, length: float):
    """
    为线增加点
    """
    coords = list(line.coords)
    if len(coords) == 0:
        return LineString([])
    num_original_points = len(coords) - 1
    new_coords = []

    for i in range(num_original_points):
        x0, y0 = coords[i]
        x1, y1 = coords[i + 1]
        new_coords += add_points_to_line_by_distance(x0, y0, x1, y1, length)
        new_coords = new_coords[:len(new_coords) - 1]  # 去除末尾一点
    new_coords.append(coords[0])
    return LineString(new_coords)


def plot_perpendicular_line(x1, y1, angle_deg, length: float):
    """
    做垂线
    """
    # 将角度从度数转换为弧度
    angle_deg2 = angle_deg + 180
    if angle_deg2 > 360:
        angle_deg2 -= 360
    angle_rad1 = math.radians(angle_deg)
    angle_rad2 = math.radians(angle_deg2)

    # 计算射线的端点坐标
    x2 = x1 + length * math.cos(angle_rad1)
    y2 = y1 + length * math.sin(angle_rad1)
    x3 = x1 + length * math.cos(angle_rad2)
    y3 = y1 + length * math.sin(angle_rad2)

    return LineString(((x3, y3), (x2, y2)))


def get_projected_link_v2(polygon: Polygon, link: Link, search_buffer: float) -> List[ProjectedLink]:
    """
    获取映射的线
    """
    shell = polygon.get_shell()

    def _get_tow_pt_per_location(_pt1, _pt2) -> tuple:
        _angle = azimuth_angle(_pt1[0], _pt1[1], _pt2[0], _pt2[1])
        _angle += 90
        if _angle > 360:
            _angle -= 360
        per = plot_perpendicular_line(_pt1[0], _pt1[1], _angle, search_buffer * 1.1)
        res = per.intersection(polygon.geo)
        if not res.is_empty:
            _s_pt, _ = ops.nearest_points(res, link.geom)
            _s_loc = project(shell, _s_pt)
            return _s_pt, _s_loc
        return interpolate(polygon.shell, 0), 0

    def _get_link_start_location() -> tuple:
        """
        获取 link，起点对应的位置，和坐标
        """
        _coords = link.geom.coords
        return _get_tow_pt_per_location(_coords[0], _coords[1])

    def _get_link_end_location() -> tuple:
        """
        获取 link，起点对应的位置，和坐标
        """
        _coords = link.geom.coords
        return _get_tow_pt_per_location(_coords[-1], _coords[-2])

    ready_location = {
        0: _get_link_start_location(),
        1: _get_link_end_location(),
    }

    locations = []
    for coords in polygon.exterior_add_points_list:
        alocation = []
        for i in range(len(coords)):
            s_pt = Point(coords[i])
            l_loc = link.projected(s_pt)
            s_loc = project(shell, s_pt)
            l_pt = link.get_point_by_projected_loc(l_loc)
            dist = l_pt.distance(s_pt)

            if l_loc in ready_location:
                s_pt, s_loc = ready_location[l_loc]

            # 垂足没有落在线上，重新计算一下 s_loc 的位置

            if dist > search_buffer:
                if len(alocation) >= 2:
                    locations.append(alocation)
                alocation = []  # 重新清空
            else:
                alocation.append({
                    'l_pt': l_pt,
                    's_pt': s_pt,
                    'l_loc': l_loc,
                    's_loc': s_loc,
                })
        if len(alocation) >= 2:
            locations.append(alocation)

    resp = []
    loc_flag = {}
    for a_location in locations:
        a_location.sort(key=lambda x: x['l_loc'])
        s_loc, e_loc = a_location[0], a_location[-1]
        loc_key = f"{s_loc['l_loc']}_{e_loc['l_loc']}"
        if s_loc == e_loc or loc_key in loc_flag:
            continue
        loc_flag[loc_key] = 1
        projected = ProjectedLink(
            start_location=s_loc['l_loc'],
            end_location=e_loc['l_loc'],
            link=link,
            polygon_start_location=min(s_loc['s_loc'], e_loc['s_loc']),
            polygon_end_location=max(s_loc['s_loc'], e_loc['s_loc']),
            dist=link.geom.distance(polygon.geo),
            polygon=polygon,
        )
        resp.append(projected)
    return resp


def get_projected_links(polygon: Polygon, search_buffer: float) -> List[ProjectedLink]:
    """
    获取映射的线
    """
    projects = []
    links = get_links_by_wkt(polygon.geom, search_buffer)
    for a_link in links:
        projects += get_projected_link_v2(polygon, a_link, search_buffer)
    return projects


class ProjectedLinksFilter:
    """
    映射的线过滤
    """

    def __init__(self, projects: List[ProjectedLink], distance: float):
        projects = filter_by_length(projects)
        projects.sort(key=lambda x: (x.dist, -x.line.length))
        self.projects = projects
        self.distance = distance
        self.max_end = 0.0
        self.min_start = 1.0
        self.response = []

    def filter(self) -> List[ProjectedLink]:
        """
        过滤
        """
        for link in self.projects:
            if self.is_covered(link):
                # print(link.link.geom, "被遮挡了")
                continue
            self.response += self._split(link)
        return self.response

    def is_covered(self, link: ProjectedLink) -> bool:
        """
        被覆盖了返回 True
        """
        points = [
            link.link.get_point_by_projected_loc(link.start_location),
            link.link.get_point_by_projected_loc(link.end_location),
            interpolate(link.polygon.shell, link.polygon_end_location),
            interpolate(link.polygon.shell, link.polygon_start_location),
        ]
        multipoint = MultiPoint(points)
        geo = multipoint.convex_hull

        lines = []
        for res in self.response:
            lines.append(res.link.geom)
            if res.link.link_id == link.link.link_id:  # 已经存在答案中，那么不会被遮盖
                return False

        # lines = [res.line for res in self.response]
        if len(lines) == 0:
            return False

        u_geo = ops.unary_union(lines)
        if u_geo.geom_type != 'LineString':
            u_geo = ops.linemerge(u_geo)
        if u_geo.geom_type == 'LineString':
            splitters = [u_geo]
        else:
            splitters = u_geo.geoms

        for a_splitter in splitters:
            sp_res = ops.split(geo, a_splitter)
            if len(sp_res.geoms) > 1:
                return True
        return False

    def _split(self, link) -> List[ProjectedLink]:
        links = [link]
        for b_link in self.response:
            new_links = []
            for a_link in links:
                new_links += filter_by_length(difference(a_link, b_link, self.distance))
            links = new_links
        return links


def filter_by_length(links: List[ProjectedLink]) -> List[ProjectedLink]:
    """
    根据长度过滤
    """
    resp = []
    for a_link in links:
        if a_link.line.length < 1e-5 * 0.5:
            continue
        resp.append(a_link)
    return resp


def intersects(a: ProjectedLink, b: ProjectedLink) -> bool:
    """
    相交
    """
    if a.polygon_end_location <= b.polygon_start_location or a.polygon_start_location >= b.polygon_end_location:
        return False
    return True


def _diff(a: ProjectedLink, start: float, end: float) -> List[ProjectedLink]:
    """
    裁切
    """
    if not a.intersection(start, end):
        return [a]
    if a.end_location <= end and a.start_location >= start:  # 被区间覆盖了
        return []

    def create_projected_link(start_loc, end_loc):
        """
        辅助函数：创建一个裁剪后的 ProjectedLink
        """
        return ProjectedLink(
            start_location=start_loc,
            end_location=end_loc,
            link=a.link,
            dist=a.dist,
            polygon_start_location=0,
            polygon_end_location=0,
            polygon=a.polygon,
            azimuth_angle=a.azimuth_angle,
        )

    # 裁剪处理
    result = []
    if a.end_location <= end:
        result.append(create_projected_link(a.start_location, start))
    if a.start_location > start:
        result.append(create_projected_link(end, a.end_location))
    return result


def _calc_line_azimuth_angle(line: LineString) -> float:
    """
    计算线方位角函数[0,360)
    用线的两个端点
    """
    s_pt = line.coords[0]
    e_pt = line.coords[1]
    return azimuth_angle(s_pt[0], s_pt[1], e_pt[0], e_pt[1])


def _calc_line_angle(line1: LineString, line2: LineString) -> float:
    """
    计算两条线之间的夹角；[0,180)
    """
    ang1 = _calc_line_azimuth_angle(line1)
    ang2 = _calc_line_azimuth_angle(line2)
    ang3 = math.fabs(ang1 - ang2)
    if ang3 > 180:
        ang3 = 360 - ang3
    return ang3


def difference(a: ProjectedLink, b: ProjectedLink, distance: float) -> List[ProjectedLink]:
    """
    差分
    """
    if a.link.link_id == b.link.link_id:
        return _diff(a, b.start_location, b.end_location)

    # 两条线的方位不一样，不用裁切
    multipoint = MultiPoint(list(a.line.coords) + list(b.line.coords))
    if (not a.line.intersects(a.polygon.geo)
            and not b.line.intersects(a.polygon.geo)
            and multipoint.convex_hull.intersects(a.polygon.geo)):
        return [a]

    angle = _calc_line_angle(a.line, b.line)
    if 60 <= angle <= 120:  # 接近垂直
        return [a]

    locations = set()
    for _coord in b.line.coords:
        b_pt = Point(_coord)
        _loc = a.link.projected(Point(_coord))
        a_pt = a.link.get_point_by_projected_loc(_loc)
        if b_pt.distance(a_pt) > distance:
            continue
        locations.add(_loc)

    if len(locations) <= 1:
        return [a]
    locations = list(locations)
    locations.sort()

    start, end = locations[0], locations[-1]
    return _diff(a, start, end)


def get_polygons_by_wkt_exp(wkt: str) -> List[Polygon]:
    """
    示例，通过范围获取 polygons
    """
    pass


def get_polygons_intelligence_by_wkt(wkt: str) -> List[Polygon]:
    """
    获取 park_storefront_intelligence 中的 polygons
    """
    qry = (f"select face_id, st_astext(geom) geom from park_storefront_intelligence "
           f"where st_intersects(geom, st_geomfromtext('{wkt}', 4326)) "
           f"and batch = 'beijing_haidian_20241022_20241102.v4'")
    res = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, qry)
    if res is None or not res:
        return []

    return [Polygon(face_id=item[0], geom=item[1]) for item in res]


def get_polygons_diff_tmp_by_wkt(wkt: str) -> List[Polygon]:
    """
    获取临时差分几何信息
    """
    qry = (f"select face_id, st_astext(geom) geom from parking_diff_tmp "
           f"where st_intersects(geom, st_geomfromtext('{wkt}', 4326)) ")
    res = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, qry)
    if res is None or not res:
        return []

    return [Polygon(face_id=item[0], geom=item[1]) for item in res]


@dataclasses.dataclass
class CharacteristicsOfPolygon:
    """
    几何特征
    """
    polygon: Polygon
    pois: List[auto_cpt.Poi]
    links: List[ProjectedLink]
    covered_geom: str


def find_overlapping_intervals(interval, interval_list):
    """
    找出与给定区间重叠的所有区间，并返回需要拆分的区间列表
    :param interval: 给定的区间 (start, end)
    :param interval_list: 区间列表 [(start, end), ...]
    :return: 拆分后的区间列表，如果不重叠则返回原区间
    """
    overlapping = []
    for i_interval in interval_list:
        # 检查重叠（包括端点）
        if max(interval[0], i_interval[0]) < min(interval[1], i_interval[1]):
            # 计算重叠部分和非重叠部分
            overlap_start = max(interval[0], i_interval[0])
            overlap_end = min(interval[1], i_interval[1])

            # 如果原区间左端点在重叠区域外，则添加左半部分
            if interval[0] < overlap_start:
                overlapping.append([interval[0], overlap_start])

            # 添加重叠部分（这里选择不添加，因为我们只关心差分）
            # overlapping.append((overlap_start, overlap_end))

            # 如果原区间右端点在重叠区域外，则添加右半部分
            if interval[1] > overlap_end:
                overlapping.append([overlap_end, interval[1]])

    # 如果没有重叠，则返回原区间
    if not overlapping:
        overlapping.append(interval)
    return overlapping


def subtract_intervals(base_interval, sub_intervals):
    """从 base_interval 中减去 sub_intervals"""
    result = []
    start, end = base_interval

    for sub_start, sub_end in sub_intervals:
        if sub_end <= start or sub_start >= end:
            # 无交集，跳过
            continue
        if sub_start > start:
            result.append([start, sub_start])
        start = max(start, sub_end)

    if start < end:
        result.append([start, end])
    return result


def find_overlapping_intervals_v2(interval, interval_list):
    """
    裁切掉所有重叠的区域
    """
    # 第一步：找出所有真正有交集的区间
    overlapping = []
    for i in interval_list:
        if max(interval[0], i[0]) < min(interval[1], i[1]):
            overlapping.append([max(interval[0], i[0]), min(interval[1], i[1])])

    # 第二步：合并重叠部分
    merged = merge_intervals(overlapping)
    # 第三步：从原区间中剪除合并后的重叠区间
    return subtract_intervals(interval, merged)


def find_difference_intervals(intervals1, intervals2):
    """
    找出第一个区间列表中存在但不被第二个列表中的任何区间完全覆盖的区间
    :param intervals1: 第一个区间列表 [(start, end), ...]
    :param intervals2: 第二个区间列表 [(start, end), ...]
    :return: 差分区间列表 [(start, end), ...]
    """
    interval_list1 = copy.deepcopy(intervals1)
    interval_list2 = copy.deepcopy(intervals2)

    difference_intervals = []
    for interval in interval_list1:
        # 找出与当前区间重叠的所有区间，并拆分
        # split_intervals = find_overlapping_intervals(interval, interval_list2)
        split_intervals = find_overlapping_intervals_v2(interval, interval_list2)
        # 将拆分后的非重叠部分添加到差分列表中
        difference_intervals.extend(split_intervals)

    # 合并差分列表中的重叠区间
    merged_difference = merge_intervals(difference_intervals)

    # 去除被第二个列表完全覆盖的区间（再次检查，因为合并可能会引入新的问题）
    final_difference = []
    for diff_interval in merged_difference:
        covered = False
        for i_interval in interval_list2:
            if diff_interval[0] >= i_interval[0] and diff_interval[1] <= i_interval[1]:
                # 完全覆盖
                covered = True
                break
        if not covered:
            final_difference.append(diff_interval)
    return final_difference


def merge_two_intervals(interval_list1, interval_list2):
    """
    合并两个区间列表
    """
    merged_intervals = interval_list1 + interval_list2
    return merge_intervals(merged_intervals)


def merge_intervals(input_intervals):
    """
    区间合并
    """
    # 按区间的起始位置进行排序
    if not input_intervals:
        return []

    # 不修改原来的要素
    intervals = copy.deepcopy(input_intervals)

    intervals.sort(key=lambda x: x[0])
    # 合并重叠的区间
    merged_result = []
    current_interval = intervals[0]
    for interval in intervals[1:]:
        if interval[0] <= current_interval[1]:  # 有重叠
            current_interval[1] = max(current_interval[1], interval[1])
        else:  # 无重叠
            merged_result.append(current_interval)
            current_interval = interval
    # 添加最后一个区间
    merged_result.append(current_interval)
    return merged_result


def interval_intersection(intervals1, intervals2):
    """
    区间交集
    """
    # 先对两个区间列表进行排序，以便更容易地找到重叠区间
    interval_list1 = copy.deepcopy(intervals1)
    interval_list2 = copy.deepcopy(intervals2)

    interval_list1.sort()
    interval_list2.sort()

    # 初始化指针和结果列表
    i, j = 0, 0
    intersection_intervals = []

    # 遍历两个区间列表
    while i < len(interval_list1) and j < len(interval_list2):
        # 获取当前区间的起始和结束位置
        start1, end1 = interval_list1[i]
        start2, end2 = interval_list2[j]

        # 检查是否存在重叠
        if end1 >= start2 and end2 >= start1:
            # 计算重叠部分的起始和结束位置
            intersection_start = max(start1, start2)
            intersection_end = min(end1, end2)
            # 将重叠部分添加到结果列表中
            intersection_intervals.append([intersection_start, intersection_end])

        # 移动指针以继续查找下一个可能的重叠区间
        if end1 < end2:
            i += 1
        else:
            j += 1
    return intersection_intervals


def union_characteristics(a_chars: CharacteristicsOfPolygon, b_chars: CharacteristicsOfPolygon) \
        -> CharacteristicsOfPolygon:
    """
    两个特征集合并
    """
    polygon = Polygon(
        face_id=uuid.uuid4().hex,
        geom=a_chars.polygon.geo.union(b_chars.polygon.geo).wkt,
    )

    pois = a_chars.pois + b_chars.pois
    resp = {}
    for a_poi in pois:
        resp[a_poi.bid] = a_poi
    pois = list(resp.values())

    a_covered_geo = shapely.wkt.loads(a_chars.covered_geom)
    b_covered_geo = shapely.wkt.loads(b_chars.covered_geom)
    c_covered_geo = a_covered_geo.union(b_covered_geo)

    links = []
    aid2links = _format_id2links(a_chars.links)
    bid2links = _format_id2links(b_chars.links)

    i_id_set = set(aid2links.keys()).intersection(bid2links.keys())

    def _append_not_exists_intersected_link(_links: List[ProjectedLink]):
        for _link in _links:
            if _link.link.link_id in i_id_set:
                continue
            links.append(_link)

    _append_not_exists_intersected_link(a_chars.links)
    _append_not_exists_intersected_link(b_chars.links)

    for i_id in i_id_set:
        a_links = aid2links[i_id]
        b_links = bid2links[i_id]

        represent = a_links[0]

        a_intervals = [[a_link.start_location, a_link.end_location] for a_link in a_links]
        b_intervals = [[a_link.start_location, a_link.end_location] for a_link in b_links]

        merged_resp = merge_two_intervals(a_intervals, b_intervals)
        for _resp in merged_resp:
            links.append(ProjectedLink(
                start_location=_resp[0],
                end_location=_resp[1],
                link=represent.link,
                polygon=represent.polygon,
                dist=represent.dist,
            ))

    return CharacteristicsOfPolygon(
        polygon=polygon,
        pois=pois,
        links=links,
        covered_geom=c_covered_geo.wkt,
    )


def union_more_characteristics(characteristics_set: List[CharacteristicsOfPolygon]) -> CharacteristicsOfPolygon:
    """
    多个特征合并
    """
    if len(characteristics_set) == 0:
        raise Exception("characteristics_set 不能为空")

    if len(characteristics_set) == 1:
        return characteristics_set[0]

    a_characteristics = characteristics_set[0]
    for i in range(1, len(characteristics_set)):
        a_characteristics = union_characteristics(a_characteristics, characteristics_set[i])
    return a_characteristics


@dataclasses.dataclass
class CharacteristicsSetOfPolygon:
    """
    特征集
    """
    characteristics_set: List[CharacteristicsOfPolygon]

    def __post_init__(self):
        self.key2characteristics = {}
        self.add_characteristics_set_of_polygon(self.characteristics_set)

    def add_characteristics_set_of_polygon(self, characteristics_set: List[CharacteristicsOfPolygon]):
        """
        添加多个特征集
        """
        for a_characteristics in characteristics_set:
            self.add_characteristics_of_polygon(a_characteristics)

    def add_characteristics_of_polygon(self, characteristics: CharacteristicsOfPolygon):
        """
        添加一个特征值
        """
        key = characteristics.polygon.face_id
        if key not in self.key2characteristics:
            self.key2characteristics[key] = characteristics
            self.characteristics_set.append(characteristics)

    def union(self, characteristics: 'CharacteristicsSetOfPolygon'):
        """
        合并
        """
        self.add_characteristics_set_of_polygon(characteristics.characteristics_set)

    def get_characteristics_by_fid(self, face_id: str) -> CharacteristicsOfPolygon:
        """
        通过 fid 获取特征集
        """
        return self.key2characteristics[face_id]

    def get_characteristics_by_fids(self, face_ids: list) -> List[CharacteristicsOfPolygon]:
        """
        通过 fids 获取特征集
        """
        resp = []
        for a_fid in face_ids:
            resp.append(self.get_characteristics_by_fid(a_fid))
        return resp


def _build_covered_geom_by_poi_and_link(pois: List[auto_cpt.Poi], links: List[ProjectedLink]) -> str:
    """
    构成覆盖的范围，用 poi 和 link
    """
    if len(pois) == 0 or len(links) == 0:
        return 'POLYGON EMPTY'

    points = []
    for a_poi in pois:
        points.append((a_poi.point.x, a_poi.point.y))

    for a_link in links:
        points += [(x, y) for x, y in a_link.line.coords]

    # FIXME 使用 凸包有点简单粗暴
    multipoint = MultiPoint(points)
    return multipoint.convex_hull.wkt


def _build_covered_geom_by_poi_and_geom(pois: List[auto_cpt.Poi], polygon: Polygon) -> str:
    """
    用 aoi 和 geom 构成覆盖范围
    """
    if len(pois) == 0:
        return polygon.geom

    points = []
    for a_poi in pois:
        points.append((a_poi.point.x, a_poi.point.y))

    points += polygon.get_exterior_coords()

    multipoint = MultiPoint(points)
    return multipoint.convex_hull.wkt


def _build_covered_geom_by_link_and_geom(links: List[ProjectedLink], polygon: Polygon) -> str:
    """
    用 link 和 geom 构成覆盖范围
    """
    if len(links) == 0:
        return polygon.geom

    points = []
    for a_link in links:
        points += [(x, y) for x, y in a_link.line.coords]

    points += polygon.get_exterior_coords()

    multipoint = MultiPoint(points)
    return multipoint.convex_hull.wkt


def _build_covered_geom(polygon: Polygon, pois: List[auto_cpt.Poi], links: List[ProjectedLink]) -> str:
    """
    构建覆盖的范围
    """
    geom1 = _build_covered_geom_by_poi_and_link(pois, links)
    geom2 = _build_covered_geom_by_poi_and_geom(pois, polygon)
    geom3 = _build_covered_geom_by_link_and_geom(links, polygon)

    geos = [
        shapely.wkt.loads(geom1),
        shapely.wkt.loads(geom2),
        shapely.wkt.loads(geom3),
    ]
    return ops.unary_union(geos).wkt


@lru_cache(maxsize=1000)
def get_polygon_characteristics(polygon: Polygon, search_buffer: float) -> CharacteristicsOfPolygon:
    """
    获取几何的特征
    """
    pois = get_related_store_pois(polygon, search_buffer)
    links = get_related_projected_links(polygon, search_buffer)
    covered_geom = _build_covered_geom(polygon, pois, links)
    return CharacteristicsOfPolygon(
        polygon=polygon,
        pois=pois,
        links=links,
        covered_geom=covered_geom
    )


@dataclasses.dataclass
class Similarity:
    """
    相似度
    """
    iou: float
    ioa: float
    iob: float

    trust: bool  # 是否可信; 比如若三个指标都是 0，那么可能有两种情况，第一中情况是真实都是 0；第二种是没有获取到数据导致的 0

    intersected: list  # 两者公共的
    a_diffed: list  # a-b 剩下的
    b_diffed: list  # b-a 剩下的

    def simply_info(self) -> dict:
        """
        简单输出相似度信息
        """
        return {
            'iou': self.iou,
            'ioa': self.ioa,
            'iob': self.iob,
            'trust': self.trust,
            'intersected': self.intersected,
            'a_diffed': self.a_diffed,
            'b_diffed': self.b_diffed,
        }


@dataclasses.dataclass
class PolygonSimilarity:
    """
    几何相似度
    """
    polygon_a: Polygon
    polygon_b: Polygon

    poi_similarity: Similarity
    link_similarity: Similarity
    covered_geom_similarity: Similarity
    geom_similarity: Similarity

    def get_detail(self) -> dict:
        """
        获取明细
        """
        resp = {
            'poi': self.poi_similarity.simply_info(),
            'link': self.link_similarity.simply_info(),
            'cover': self.covered_geom_similarity.simply_info(),
            'geom': self.geom_similarity.simply_info(),
        }
        return resp


def no_relation(similarity: PolygonSimilarity) -> bool:
    """
    没有关系
    """
    return (similarity.poi_similarity.iou == 0
            and similarity.link_similarity.iou == 0
            and similarity.geom_similarity.iou == 0)


def think_the_same(similarity: PolygonSimilarity) -> bool:
    """
    认为一样
    """

    def _calc_similarity_score(val: float, min_val: float, trust: bool) -> float:
        """
        计算积分
        """
        if not trust:
            return 0.1
        if val >= min_val:
            return 1
        return 0

    def _calc_score(simi: Similarity):
        return _calc_similarity_score(simi.iou, 0.9, simi.trust)

    # score = (_calc_score(similarity.poi_similarity)
    #          + _calc_score(similarity.link_similarity)
    #          + _calc_score(similarity.geom_similarity))
    # return score >= 2.1
    score = (_calc_score(similarity.link_similarity)
             + _calc_score(similarity.geom_similarity))
    return score >= 1.1
    # return (similarity.poi_similarity.iou >= 0.95
    #         and similarity.link_similarity.iou >= 0.95
    #         and similarity.geom_similarity.iou >= 0.95)
    # return (similarity.link_similarity.iou >= 0.9
    #         and similarity.geom_similarity.iou >= 0.9)


def _calc_rate(a: float, b: float) -> float:
    """
    计算 a/b
    """
    return a / b if b > 0 else 0.0


def _calc_poi_similarity(a_pois: List[auto_cpt.Poi], b_pois: List[auto_cpt.Poi]) -> Similarity:
    """
    计算 poi 的相似度
    """
    a_bids = set()
    b_bids = set()

    def _init_bid_set(pois: List[auto_cpt.Poi], bid_set: set):
        """
        初始化 bid 集合
        """
        for a_poi in pois:
            bid_set.add(a_poi.bid)

    _init_bid_set(a_pois, a_bids)
    _init_bid_set(b_pois, b_bids)

    u_bids = a_bids.union(b_bids)
    i_bids = a_bids.intersection(b_bids)

    return Similarity(
        iou=_calc_rate(len(i_bids), len(u_bids)),
        ioa=_calc_rate(len(i_bids), len(a_bids)),
        iob=_calc_rate(len(i_bids), len(b_bids)),
        intersected=list(i_bids),
        a_diffed=list(a_bids.difference(b_bids)),
        b_diffed=list(b_bids.difference(a_bids)),
        trust=len(a_bids) > 0 and len(b_bids) > 0,
    )


def point_relative_to_line(p: tuple, l1: tuple, l2: tuple) -> int:
    """
    计算点 P 相对于通过 L1 和 L2 定义的直线的位置。
    返回：
    -1: P 在直线的左侧
     0: P 在直线上
    +1: P 在直线的右侧
    """
    x0, y0 = l1
    x1, y1 = l2
    xp, yp = p

    # 计算向量 L1L2 和 LP
    L1L2_x = x1 - x0
    L1L2_y = y1 - y0
    LP_x = xp - x0
    LP_y = yp - y0

    # 计算叉积
    cross_product = L1L2_x * LP_y - L1L2_y * LP_x

    # 判断叉积的符号
    if cross_product > 0:
        return 1  # 右侧
    elif cross_product < 0:
        return -1  # 左侧
    else:
        return 0  # 直线上


def _on_the_same_side(line: LineString, a_polygon: Polygon, b_polygon: Polygon) -> bool:
    """
    在同一侧，返回 True
    """
    if a_polygon.geo.intersects(b_polygon.geo):
        return True

    coords = line.coords
    s_pt, e_pt = coords[0], coords[-1]

    def get_represent_point(polygon: Polygon) -> Union[Point, None]:
        """
        获取代表的一点
        """
        for a_coord in polygon.get_shell_add_points():
            a_pt = Point(a_coord)
            if get_foot_point(a_pt, line) is not None:
                return a_pt
        return polygon.get_any_foot_boundary(Point(s_pt))

    if a_polygon.geo.intersects(line) or b_polygon.geo.intersects(line):
        a_rep_pt = a_polygon.geo.centroid
        b_rep_pt = b_polygon.geo.centroid
    else:
        a_rep_pt = get_represent_point(a_polygon)
        b_rep_pt = get_represent_point(b_polygon)

    # a_rep_pt = get_represent_point(a_polygon)
    # b_rep_pt = get_represent_point(b_polygon)

    if a_rep_pt is None or b_rep_pt is None:
        return False

    a_side = point_relative_to_line(a_rep_pt.coords[0], s_pt, e_pt)
    b_side = point_relative_to_line(b_rep_pt.coords[0], s_pt, e_pt)

    return a_side == b_side


def _format_id2links(links: List[ProjectedLink]) -> Dict[str, List[ProjectedLink]]:
    """
    格式化
    """
    id2links = {}
    for _a_link in links:
        id2links.setdefault(_a_link.link.link_id, []).append(_a_link)
    return id2links


def _get_lines_by_intervals(link: ProjectedLink, intervals: list) -> List[LineString]:
    """
    通过区间获取 lines
    """
    lines = []
    for a_interval in intervals:
        start, end = a_interval
        lines.append(link.get_line_by_location(start, end))
    return lines


def _calc_link_similarity(a_links: List[ProjectedLink], b_links: List[ProjectedLink]) -> Similarity:
    """
    计算 link 相似度
    """
    a_id2links = _format_id2links(a_links)
    b_id2links = _format_id2links(b_links)

    a_id_set = set(a_id2links.keys())
    b_id_set = set(b_id2links.keys())

    i_id_set = a_id_set.intersection(b_id_set)

    i_lines = []
    u_lines = []
    d_lines = []  # a - intersects
    e_lines = []  # b - intersects
    for i_id in i_id_set:
        i_a_links = a_id2links[i_id]
        i_b_links = b_id2links[i_id]

        represent = i_a_links[0]  # 代表 link

        _a_polygon = i_a_links[0].polygon
        _b_polygon = i_b_links[0].polygon

        a_intervals = [[a_link.start_location, a_link.end_location] for a_link in i_a_links]
        b_intervals = [[a_link.start_location, a_link.end_location] for a_link in i_b_links]

        a_merged_intervals = merge_intervals(a_intervals)
        b_merged_intervals = merge_intervals(b_intervals)
        u_merged_intervals = merge_two_intervals(a_merged_intervals, b_merged_intervals)
        a_diffed_intervals = find_difference_intervals(a_merged_intervals, b_merged_intervals)
        b_diffed_intervals = find_difference_intervals(b_merged_intervals, a_merged_intervals)

        intersects_intervals = interval_intersection(a_merged_intervals, b_merged_intervals)
        for _interval in intersects_intervals:
            _start, _end = _interval
            intersection = represent.get_line_by_location(_start, _end)
            if not intersection.is_empty and _on_the_same_side(intersection, _a_polygon, _b_polygon):
                i_lines.append(intersection)
            else:
                a_diffed_intervals.append(_interval)
                b_diffed_intervals.append(_interval)

        a_diffed_intervals = merge_intervals(a_diffed_intervals)
        b_diffed_intervals = merge_intervals(b_diffed_intervals)

        u_lines += _get_lines_by_intervals(represent, u_merged_intervals)
        d_lines += _get_lines_by_intervals(represent, a_diffed_intervals)
        e_lines += _get_lines_by_intervals(represent, b_diffed_intervals)

    def _filter_intersected_line(links: List[ProjectedLink]) -> list:
        lines = []
        for _a_link in links:
            if _a_link.link.link_id in i_id_set:
                continue
            lines.append(_a_link.line)
        return lines

    a_lines = _filter_intersected_line(a_links)
    b_lines = _filter_intersected_line(b_links)
    u_merge = ops.linemerge(u_lines + a_lines + b_lines)
    i_merge = ops.linemerge(i_lines)
    a_merge = ops.linemerge([a_link.line for a_link in a_links])
    b_merge = ops.linemerge([b_link.line for b_link in b_links])

    return Similarity(
        iou=_calc_rate(i_merge.length, u_merge.length),
        ioa=_calc_rate(i_merge.length, a_merge.length),
        iob=_calc_rate(i_merge.length, b_merge.length),
        intersected=[
            i_merge.wkt,
        ],
        a_diffed=[
            ops.linemerge(a_lines + d_lines).wkt,
        ],
        b_diffed=[
            ops.linemerge(b_lines + e_lines).wkt,
        ],
        trust=a_merge.length > 0 and b_merge.length > 0,
    )


def _calc_geom_similarity(a_geom: str, b_geom: str) -> Similarity:
    """
    计算几何范围的相似度
    """
    return Similarity(
        iou=utils.calc_iou(a_geom, b_geom),
        ioa=utils.calc_ioa(b_geom, a_geom),
        iob=utils.calc_ioa(a_geom, b_geom),
        intersected=[],
        a_diffed=[],
        b_diffed=[],
        trust=True,
    )


def calc_characteristics_similarity(a: CharacteristicsOfPolygon, b: CharacteristicsOfPolygon) -> PolygonSimilarity:
    """
    计算两个特征的相似度
    """
    poi_similarity = _calc_poi_similarity(a.pois, b.pois)
    link_similarity = _calc_link_similarity(a.links, b.links)
    geom_similarity = _calc_geom_similarity(a.covered_geom, b.covered_geom)

    return PolygonSimilarity(
        polygon_a=a.polygon,
        polygon_b=b.polygon,
        poi_similarity=poi_similarity,
        link_similarity=link_similarity,
        covered_geom_similarity=geom_similarity,
        geom_similarity=_calc_geom_similarity(a.polygon.geom, b.polygon.geom),
    )


@dataclasses.dataclass
class PolygonSimilarities:
    """
    几何相似度集
    """
    similarities: List[PolygonSimilarity]
    polygon: Polygon

    def get_similar_face_ids(self) -> list:
        """
        获取相似的面 id
        """
        face_ids = []
        for a_similarity in self.similarities:
            face_ids.append(a_similarity.polygon_b.face_id)
        return face_ids


def _filter_polygons(b_polygons: List[Polygon], char: CharacteristicsOfPolygon) -> List[Polygon]:
    """
    过滤部分 b_polygons
    场景：部分 polygon 会压盖道路，导致道路两边的 polygon 都会召回
    """
    resp = []
    for b_polygon in b_polygons:
        distance = b_polygon.geo.distance(char.polygon.geo)
        if (not b_polygon.geo.intersects(char.polygon.geo)
                and distance > 1e-5 * 5
                and utils.calc_iou(char.covered_geom, b_polygon.geom) < 0.1):
            continue
        resp.append(b_polygon)
    return resp


def calc_polygon_similarity(
        polygon: Polygon,
        search_buffer: float,
        get_polygons_by_wkt_fn: callable,
        filter: bool = True) -> Tuple[PolygonSimilarities, CharacteristicsSetOfPolygon]:
    """
    计算几何的相似度
    """
    similarities = []
    characteristics_set = []

    a_characteristics = get_polygon_characteristics(polygon, search_buffer)
    b_polygons = get_polygons_by_wkt_fn(a_characteristics.covered_geom)
    if filter:
        b_polygons = _filter_polygons(b_polygons, a_characteristics)
    characteristics_set.append(a_characteristics)

    for b_polygon in b_polygons:
        b_characteristics = get_polygon_characteristics(b_polygon, search_buffer)
        similarity = calc_characteristics_similarity(a_characteristics, b_characteristics)
        if no_relation(similarity):
            continue
        similarities.append(similarity)
        characteristics_set.append(b_characteristics)
    return (PolygonSimilarities(similarities=similarities, polygon=polygon),
            CharacteristicsSetOfPolygon(characteristics_set=characteristics_set))


def classify(ids_list: list) -> list:
    """
    聚类，把互相依赖的数据聚合在一起
    比如 ids_list = [
        [1, 2],
        [2, 3],
        [4, 5],
        [6, 7],
        [5, 6]
    ]  聚合之后是 [
        [1, 2, 3],
        [4, 5, 6, 7]
    ]
    """

    def has_coupling(ids1: list, ids2: list) -> bool:
        """
        是否有依赖
        """
        for _id in ids1:
            if _id in ids2:
                return True
        return False

    done_idx = {}
    response = []
    for _i, i_ids in enumerate(ids_list):
        if _i in done_idx:  # 已经处理了
            continue

        done_idx[_i] = 1
        _cur_ids = i_ids

        while True:
            num = 0
            for _j, j_ids in enumerate(ids_list):
                if _j in done_idx:  # 已经处理了
                    continue
                if not has_coupling(_cur_ids, j_ids):
                    continue

                done_idx[_j] = 1
                _cur_ids += j_ids
                num += 1

            if num == 0:  # 已经找不到了
                break

        response.append(list(set(_cur_ids)))
    return response


@dataclasses.dataclass
class DiffResponse:
    """
    diff 结果
    a 和 b 的匹配结果，是多与多的关系
    """
    a_face_ids: list
    b_face_ids: list

    a_characteristics_set: List[CharacteristicsOfPolygon]
    b_characteristics_set: List[CharacteristicsOfPolygon]

    merge_a_characteristics: CharacteristicsOfPolygon = None  # a 合并之后的综合特征
    merge_b_characteristics: CharacteristicsOfPolygon = None  # b 合并之后的综合特征

    similarity: PolygonSimilarity = None  # 综合相似度


def _diff_single(polygons: List[Polygon], search_buffer: float, get_polygons_by_wkt_fn: callable, filter: bool = True):
    """
    跑单个
    """
    resp = []
    for a_polygon in tqdm(polygons):
        resp.append(calc_polygon_similarity(a_polygon, search_buffer, get_polygons_by_wkt_fn, filter))
    return resp


def _diff_center(
        polygons: List[Polygon],
        search_buffer: float,
        get_polygons_by_wkt_fn: callable,
        multi: int = 30,
        filter: bool = True,
) -> List[Tuple[PolygonSimilarities, CharacteristicsSetOfPolygon]]:
    """
    diff 中心
    """
    if len(polygons) == 0:
        return []
    return _diff_single(polygons, search_buffer, get_polygons_by_wkt_fn, filter)

    multi = min(multi, len(polygons))
    print(f"multi:{multi}")
    if multi == 1:
        return _diff_single(polygons, search_buffer, get_polygons_by_wkt_fn)

    params = []
    for a_polygon in polygons:
        params.append((a_polygon, search_buffer, get_polygons_by_wkt_fn, filter))

    with multiprocessing.Pool(processes=multi) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.starmap(calc_polygon_similarity, params)
    return results


def diff_center(
        polygons: List[Polygon],
        search_buffer: float,
        get_polygons_by_wkt_fn: callable,
        filter: bool = True,
) -> List[DiffResponse]:
    """
    差分中心
    """
    # 存放全部的特征集合
    char_set_all = CharacteristicsSetOfPolygon(characteristics_set=[])
    b_fid2a_fids = {}
    b_fids_set = []
    similarities_set: List[PolygonSimilarities] = []
    empty_aids = []

    for similarities, item_char_set in _diff_center(polygons, search_buffer, get_polygons_by_wkt_fn, 1, filter):
        char_set_all.add_characteristics_set_of_polygon(item_char_set.characteristics_set)
        similarities_set.append(similarities)
        similar_ids = similarities.get_similar_face_ids()

        a_polygon = similarities.polygon
        if len(similar_ids) == 0:
            empty_aids.append(a_polygon.face_id)
            continue

        b_fids_set.append(similar_ids)
        for b_fid in similar_ids:
            b_fid2a_fids.setdefault(b_fid, []).append(a_polygon.face_id)

    b_fids_set = classify(b_fids_set)  # 有交集的需要聚类

    def _get_fid2int_num(_a_fids: list, _b_fids: list) -> dict:
        """
        获取每个面相交的数量
        """
        fid2int_num = {}
        for _a_fid in _a_fids:
            _a_char = char_set_all.get_characteristics_by_fid(_a_fid)
            int_num = 0
            for _b_fid in _b_fids:
                _b_char = char_set_all.get_characteristics_by_fid(_b_fid)
                _tmp_si = calc_characteristics_similarity(_a_char, _b_char)
                if _tmp_si.geom_similarity.iou > 0:
                    int_num += 1
            fid2int_num[_a_fid] = int_num
        return fid2int_num

    response = []
    all_aids = []
    for b_fids in b_fids_set:
        a_fids = []
        for b_fid in b_fids:
            a_fids += b_fid2a_fids[b_fid]
        a_fids = list(set(a_fids))

        special_aids = set()
        special_bids = set()

        if len(a_fids) > 1 or len(b_fids) > 1:
            a_fid2int_num = _get_fid2int_num(a_fids, b_fids)  # 面对应的相交数量
            b_fid2int_num = _get_fid2int_num(b_fids, a_fids)
            for a_fid in a_fids:
                if a_fid2int_num[a_fid] != 1:
                    continue
                for b_fid in b_fids:
                    if b_fid2int_num[b_fid] != 1:
                        continue
                    a_char = char_set_all.get_characteristics_by_fid(a_fid)
                    b_char = char_set_all.get_characteristics_by_fid(b_fid)
                    tmp_si = calc_characteristics_similarity(a_char, b_char)
                    # print(tmp_si.get_detail())
                    if think_the_same(tmp_si):
                        response.append(DiffResponse(
                            a_face_ids=a_fids,
                            b_face_ids=b_fids,
                            merge_a_characteristics=a_char,
                            merge_b_characteristics=b_char,
                            a_characteristics_set=[a_char],
                            b_characteristics_set=[b_char],
                            similarity=tmp_si,
                        ))

                        special_aids.add(a_fid)
                        special_bids.add(b_fid)

        a_fids = list(set(a_fids).difference(special_aids))
        b_fids = list(set(b_fids).difference(special_bids))

        if len(a_fids) == 0:
            continue
        if len(b_fids) == 0:
            empty_aids += a_fids
            continue

        all_aids += a_fids

        a_characteristics_set = char_set_all.get_characteristics_by_fids(a_fids)
        b_characteristics_set = char_set_all.get_characteristics_by_fids(b_fids)

        a_characteristics = union_more_characteristics(a_characteristics_set)
        b_characteristics = union_more_characteristics(b_characteristics_set)

        similarity = calc_characteristics_similarity(a_characteristics, b_characteristics)
        response.append(DiffResponse(
            a_face_ids=a_fids,
            b_face_ids=b_fids,
            merge_a_characteristics=a_characteristics,
            merge_b_characteristics=b_characteristics,
            a_characteristics_set=a_characteristics_set,
            b_characteristics_set=b_characteristics_set,
            similarity=similarity,
        ))

    # 有的新增，匹配不到
    for aid in empty_aids:
        if aid in all_aids:
            continue
        response.append(DiffResponse(
            a_face_ids=[aid],
            b_face_ids=[],
            merge_a_characteristics=char_set_all.get_characteristics_by_fid(aid),
            merge_b_characteristics=None,
            a_characteristics_set=[char_set_all.get_characteristics_by_fid(aid)],
            b_characteristics_set=[],
            similarity=None,
        ))
    return response
