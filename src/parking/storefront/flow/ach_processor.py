"""
加工停车场成果
"""
import argparse
import json
import logging
from typing import List

import dataclasses
import shapely.wkt
from tqdm import tqdm

from src.parking.storefront.flow.db import array_chunk, join_str, handle_apostrophe
from src.parking.storefront.flow.model.ach import ParkingAch, get_parking_aches
from src.parking.storefront.post_process.autocomplete import try_get_primary_poi, get_park_name
from src.parking.storefront.post_process.central_line import get_central_line_irregular, get_central_line
import src.parking.storefront.flow.cmd.generator as cmd_gener
import src.parking.storefront.flow.model.cmd as cmd_model
import src.parking.storefront.flow.model.ach as ach
import src.parking.storefront.flow.cmd.executor as executor
import src.parking.storefront.flow.cmd.sheduler as sheduler
from src.parking.storefront.flow.context import Context, gen_ctx
import src.parking.storefront.flow.model.match as match
from src.parking.storefront.flow.traj import processor as traj_processor
from src.parking.storefront.flow.verifier import get_panorama_images


class VerifyStatus:
    """
    核实状态
    INIT -> (INVALID, MODIFY, IMPORTED)
    """
    INIT = 'INIT'
    INVALID = 'INVALID'
    IMPORTED = 'IMPORTED'


class ParkingAchStatus:
    """
    停车场成果库状态
    """
    INIT = 'INIT'
    INITED_BASE = 'INITED_BASE'
    INITING_BID = 'INITING_BID'
    READY = 'READY'
    CHECK_SUCCEED = 'CHECK_SUCCEED'


@dataclasses.dataclass
class VerifiedAch:
    """
    核实成果
    """
    info_id: str
    user: str  # 核实方
    bid: str
    geom: str
    id: int
    city: str
    recall: str = ''
    precise: str = ''


def _get_info_geoms(ctx: Context, info_ids: list) -> dict:
    """
    获取情报的范围
    """
    if len(info_ids) == 0:
        return {}
    qry = f"select info_id, st_astext(geom) from {ctx.verified_push_tab} where info_id in ({join_str(info_ids)})"
    res = ctx.poi_db.fetchall(qry)

    if not res:
        return {}
    return {item[0]: item[1] for item in res}


def _get_turing_verified_aches(ctx: Context, limit: int, where: str = '') -> List[VerifiedAch]:
    """
    获取图灵核实成果
    """
    return _get_turing_verified_aches_when_perfect(ctx, limit, where)


def _get_turing_verified_aches_by_ids(ctx: Context):
    iids = []
    where = f"turing_id in ({join_str(iids)}) and status = 'INIT' and (st_isempty(geom) or st_isvalid(geom))"
    return _get_verified_aches_by_where(ctx, where)


def _get_turing_verified_aches_when_perfect(ctx: Context, limit, where):
    """
    获取图灵核实成果，当情报完美，情报有效且范围也是对的
    """
    qry = f"""
    status = '{VerifyStatus.INIT}' 
    and sub_result = '1' 
    and (
        (recall = '1' and precise = '1') 
        or 
        (recall = '1' and precise != '1' and not st_isempty(geom) 
        and st_isvalid(geom) and st_geometrytype(geom) = 'ST_Polygon')
    )  
    and (
        (info_id in (select info_id from park_storefront_verify_pushed where batch !='' or info_source = 'online_risk') 
    and data_type in ('1209')) 
         or data_type = '1218' 
    )
    """
    if where != '':
        qry += f" and ({where})"
    if limit > 0:
        qry += f" limit {limit}"
    return _get_verified_aches_by_where(ctx, qry)


def _get_turing_upd_verified_aches(ctx: Context, limit, where):
    """
    获取图灵更新核实成果
    https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/3ZlP6R54FU/j4oQPAkxhezcNi
    """
    qry = f"""
    status = '{VerifyStatus.INIT}' 
    and (
        (recall = '1' and precise = '5' and not st_isempty(geom) 
        and st_isvalid(geom) and st_geometrytype(geom) = 'ST_Polygon')
        or (recall = '1' and precise in ('1', '3', '4'))
        or (recall = '3')
        or (recall = '2')
        or (recall = '0' and precise = '4')
    )   
    and data_type = '1214' 
    """
    if where != '':
        qry += f" and ({where})"
    if limit > 0:
        qry += f" limit {limit}"
    return _get_verified_aches_by_where(ctx, qry)


def _get_turing_open_aches(ctx: Context, limit, where):
    """
    获取图灵开放停车场
    """
    qry = f"""
    status = '{VerifyStatus.INIT}' 
    and sub_result = '1' 
    and (
        (recall = '2' and precise = '1') 
        or (recall = '2' and precise = '2' and not st_isempty(geom) 
        and st_isvalid(geom) and st_geometrytype(geom) = 'ST_Polygon') 
    )   
    and data_type in ('1209', '1218') 
    """
    if where != '':
        qry += f" and ({where})"
    if limit > 0:
        qry += f" limit {limit}"
    return _get_verified_aches_by_where(ctx, qry)


def _get_pushed_info(ctx: Context, info_id: str) -> dict:
    qry = f"""
    select city from {ctx.pushed_tab} where info_id = '{info_id}'
    """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        return {}
    return {
        'city': res[0],
    }


def _get_admin(ctx: Context, geom: str) -> dict:
    """
    获取行政区划
    """
    qry = f"select province_ch, cityname from mesh_conf_wkt where st_intersects(wkt, st_geomfromtext('{geom}', 4326))"
    res = ctx.back_db.fetchone(qry)
    if not res:
        raise Exception(f"获取行政区划失败")
    return {
        'province_ch': res[0],
        'cityname': res[1],
    }


def _get_city_info(ctx: Context, info_id: str, info_wkt: str) -> dict:
    """
    获取城市信息
    """
    if info_id != '':
        return _get_pushed_info(ctx, info_id)
    admin = _get_admin(ctx, info_wkt)
    return {
        'city': admin['cityname'],
    }


def _get_verified_aches_by_where(ctx, where: str):
    qry = f"""
    select bid, info_id, id, st_astext(geom), recall, precise from {ctx.turing_result_tab} 
    where {where}
    """
    res = ctx.poi_db.fetchall(qry)
    if not res:
        return []
    info_ids = [item[1] for item in res if item[1] != '']
    id2geom = _get_info_geoms(ctx, info_ids)
    aches = []
    for item in res:
        info_id = item[1]
        new_geom = item[3]
        recall, precise = item[4], item[5]
        if recall in ['1', '2'] and precise == '1':
            new_geom = id2geom[info_id]
            user = 'strategy'
            print(f"{info_id} 使用情报范围")
        else:
            print(f"{info_id} 使用新范围")
            user = 'turing'
        city_info = _get_city_info(ctx, info_id, new_geom)
        aches.append(VerifiedAch(
            bid=item[0],
            info_id=info_id,
            geom=new_geom,
            user=user,
            id=item[2],
            city=city_info['city'],
            recall=recall,
            precise=precise,
        ))
    return aches


def _import_an_ach(ctx: Context, verified: VerifiedAch):
    """
    导入一个核实成果
    """
    upd_qry = f"""
    update {ctx.turing_result_tab} set status = '{VerifyStatus.IMPORTED}' where id = {verified.id}
    """

    _type = 'new'
    if verified.bid != '':
        _type = 'update'

    ins_qry = f"""
    insert into {ctx.park_ach_tab} (bid, geom, status, info_id, source, turing_result_id, type, city) values 
    ('{verified.bid}', st_geomfromtext('{verified.geom}', 4326), 
    '{ParkingAchStatus.INIT}', '{verified.info_id}', '{verified.user}', {verified.id}, '{_type}', '{verified.city}')
    """

    queries = []
    if verified.bid != '':
        queries.append(f"""
        update {ctx.park_ach_tab} set bid_status = 'invalid' where bid = '{verified.bid}' and bid_status = 'effected'
        """)

    queries += [upd_qry, ins_qry]
    for qry in queries:
        ctx.poi_db.execute(qry)
    # 要在同一个事务中
    ctx.poi_db.commit_or_rollback()


def gen_park_access_offline_cmds(ctx: Context, bid: str, memo: str = ''):
    """
    生成停车场，及出入口的下线命令
    """
    match_id = match.gen_blank(ctx, match.ParkMatchStatus.OFFLINE)

    park_ach = ach.get_parking_ach_by_bid(ctx, bid)
    if park_ach is None:
        # 线上停车场
        return cmd_gener.gen_online_park_access_offline_cmds(ctx, bid, match_id, memo)

    # 线下停车场, 面可以直接下线
    if memo == '':
        memo = park_ach.memo
    park_cmd = cmd_gener.gen_park_offline_cmd(ctx, park_ach, match_id, memo)
    park_cmd.add_after_middlewares(executor.RanCmdExecutor.__name__, [executor.OfflineParkAfter.__name__])

    commands = [park_cmd]

    # 口可能有多种情况, 已经上线的口
    access_bids = ach.get_online_park_access_bids(ctx, bid)
    accesses = ach.get_park_accesses_by_bid(ctx, bid)
    for access in accesses:
        if access.bid in access_bids:
            # 线上，需要下线
            a_cmd = cmd_gener.gen_access_offline_cmd(ctx, access, match_id, memo)
            a_cmd.add_after_middlewares(
                executor.RanCmdExecutor.__name__, [executor.OfflineAccessAfter.__name__])
            commands.append(a_cmd)
        else:
            # 更改状态
            qry = f"""
            update {ctx.access_ach_tab} set status = 'ALREADY_OFFLINE' 
            where id = {access.access_id} and status not in ('CANCEL')
            """
            ctx.poi_db.execute(qry)
    return commands


def _import_an_upd_ach(ctx: Context, verified: VerifiedAch):
    """
    导入一个更新的核实成果
    https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/3ZlP6R54FU/j4oQPAkxhezcNi
    """
    park_qry = ''
    if verified.recall == '1' and verified.precise in ['1', '3']:
        # 没有问题，可以直接上的
        if verified.bid == '':
            raise Exception(f"{verified.id}, 更新缺失 bid")
        # FIXME status 有多种情况，干预的口需要再做一遍
        # 只更改 status = 'REPAIRED'，若不是这个状态的，状态保持不变
        park_qry = f"""
        update {ctx.park_ach_tab} 
        set status = 'READY' 
        where status = 'REPAIRED' and bid = '{verified.bid}' and bid_status = 'effected'
        """
        verifies_status = 'DONE'
    elif verified.recall == '2':
        # 开方式门前
        if verified.bid != '':
            park_qry = f"""
            update {ctx.park_ach_tab} 
            set status = 'READY_OFFLINE', 
            memo = '开放式门前停车场', memo_time = now() 
            where status = 'REPAIRED' and bid = '{verified.bid}' and bid_status = 'effected'
            """
        verifies_status = 'DONE'
    elif ((verified.recall == '1' and verified.precise == '4')
          or (verified.recall == '2' and verified.precise == '4')
          or (verified.recall == '3')
          or (verified.recall == '0' and verified.precise == '4')):
        # 需要下线的
        if verified.bid == '':
            raise Exception(f"{verified.id}, 更新缺失 bid")
        park_qry = f"""
            update {ctx.park_ach_tab} 
            set status = 'READY_OFFLINE' 
            where bid = '{verified.bid}' and bid_status = 'effected'
        """
        verifies_status = 'DONE'

        # 添加下线命令
        commands = gen_park_access_offline_cmds(ctx, verified.bid)
        cmd_model.add_commands(ctx, commands)
    elif verified.recall == '1' and verified.precise == '5':
        # 新增
        park_qry = f"""
            insert into {ctx.park_ach_tab} (bid, geom, status, info_id, source, turing_result_id, type, city) values 
            ('', st_geomfromtext('{verified.geom}', 4326), 
            '{ParkingAchStatus.INIT}', '{verified.info_id}', '{verified.user}', {verified.id}, 'new', '{verified.city}')
        """
        verifies_status = VerifyStatus.IMPORTED
    else:
        # 当前需要阻塞的
        print(f"{verified.id}, 当前需要阻塞")
        # return
        verifies_status = 'BLOCK'
        if verified.bid != '':
            park_qry = f"""
                update {ctx.park_ach_tab} 
                set status = 'REPAIRED' 
                where bid = '{verified.bid}' and bid_status = 'effected'
            """

    upd_qry = f"""
        update {ctx.turing_result_tab} set status = '{verifies_status}' where id = {verified.id}
    """
    queries = []
    if park_qry != '':
        queries = [park_qry]
    queries.append(upd_qry)
    for qry in queries:
        ctx.poi_db.execute(qry)
    # 要在同一个事务中
    ctx.poi_db.commit_or_rollback()


def _import_an_open_ach(ctx: Context, verified: VerifiedAch):
    """
    导入一个开放门前停车场
    """
    upd_qry = f"""
        update {ctx.turing_result_tab} set status = '{VerifyStatus.IMPORTED}' where id = {verified.id}
        """

    ins_qry = f"""
        insert into {ctx.park_ach_tab} (bid, geom, status, info_id, source, turing_result_id, park_type, city) values 
        ('{verified.bid}', st_geomfromtext('{verified.geom}', 4326), 
        '{ParkingAchStatus.INIT}', '{verified.info_id}', '{verified.user}', {verified.id}, 'open', '{verified.city}')
        """

    queries = [upd_qry, ins_qry]
    for qry in queries:
        ctx.poi_db.execute(qry)
    # 要在同一个事务中
    ctx.poi_db.commit_or_rollback()


def import_ach(ctx: Context, limit: int, where: str = ''):
    """
    导入到成果库
    limit：param 限制的数量
    """
    verified_aches = _get_turing_verified_aches(ctx, limit, where)
    for an_ach in tqdm(verified_aches):
        _import_an_ach(ctx, an_ach)


def import_upd_ach(ctx: Context, limit: int, where: str = ''):
    """
    导入更新成果
    """
    verified_aches = _get_turing_upd_verified_aches(ctx, limit, where)
    for an_ach in tqdm(verified_aches):
        _import_an_upd_ach(ctx, an_ach)


def import_open_ach(ctx: Context, limit: int, where: str = ''):
    """
    导入开放门前停车场成果
    """
    verified_aches = _get_turing_open_aches(ctx, limit, where)
    for an_ach in tqdm(verified_aches):
        _import_an_open_ach(ctx, an_ach)


def _get_park_aches(ctx: Context, status: str, limit: int = 0, where: str = '') -> List[ParkingAch]:
    """
    获取停车场成果
    """
    qry = f"""
    select id from {ctx.park_ach_tab} 
    where status = '{status}' and st_isvalid(geom) and bid_status = 'effected' 
    """
    if where != '':
        qry += f" and ({where}) "
    if limit > 0:
        qry += f" limit {limit}"
    # qry = f"""
    # select id from {ctx.park_ach_tab} where info_id = '' and st_isvalid(geom) limit 2
    # """  # todo test

    ids = ctx.poi_db.get_values(qry)
    ids_chunks = array_chunk(ids, 100)
    park_aches = []
    for an_ids_chunk in ids_chunks:
        park_aches += get_parking_aches(ctx, an_ids_chunk)
    return park_aches


def get_park_central_line(ctx: Context, park: ParkingAch) -> str:
    """
    获取停车场中心线
    """
    return _gen_central_line(ctx, park)


def _gen_central_line(ctx: Context, park: ParkingAch) -> str:
    """
    生成中心线
    """
    id2wkt = {}
    if str(park.park_id) in id2wkt:
        return id2wkt[str(park.park_id)]

    ept_wkt = "LINESTRING EMPTY"

    info_qry = f"""
    select id, info_source, prev_info_id from {ctx.verified_push_tab} where info_id = '{park.info_id}'
    """
    info_res = ctx.poi_db.fetchone(info_qry)

    prev_info_id = ''
    info_source = ''
    if len(info_res) > 0:
        prev_info_id = info_res[2]
        info_source = info_res[1]

    def _get_strategy_central_line() -> str:
        # sid = park.info_id.replace('_strategy', '')
        sid = prev_info_id
        if not sid.isdigit():
            raise Exception(f"{sid}在策略中不存在")
        qry = f"select st_astext(baseline) baseline from park_storefront_strategy where id = {sid}"
        res = ctx.poi_db.fetchone(qry)
        if not res:
            raise Exception(f"{park.info_id} 在策略履历表中不存在")
        try:
            line = get_central_line(park.polygon, shapely.wkt.loads(res[0]), 8 * 1e-5)
            return line.wkt
        except Exception as _e:
            logging.exception(_e)
            return 'LineString EMPTY'

    def _get_online_central_line() -> str:
        """
        获取线上中心线
        """
        qry = f"select st_astext(central_line) from parking where bid = '{prev_info_id}'"
        res = ctx.back_db.fetchone(qry)
        if len(res) == 0:
            return ept_wkt
        return res[0]

    def _get_manual_central_line() -> str:
        line = get_central_line_irregular(park.polygon)
        if line is None:
            raise Exception(f"{park.info_id} 获取人工中心线失败")
        return line.wkt

    line_wkt = "LINESTRING EMPTY"
    try:
        if park.source in ['strategy', 'online']:
            if info_source == 'strategy':
                line_wkt = _get_strategy_central_line()
                print(f"策略的中心线")
            if info_source == 'online':
                line_wkt = _get_online_central_line()
                print(f"使用线上的中心线")
        if line_wkt is None or shapely.wkt.loads(line_wkt).is_empty:
            line_wkt = _get_manual_central_line()
            print(f"用人工策略生成中心线")
    except Exception as e:
        logging.exception(e)
        print(f"获取中心线异常:{str(e)}")
    return line_wkt


def _try_get_primary_poi(geom):
    try:
        return try_get_primary_poi(geom)
    except Exception as e:
        logging.exception(e)
        return None


def maintain_base_info(ctx: Context):
    """
    维护基本信息
    包括，名称，地址，中心线
    """
    parks = _get_park_aches(ctx, ParkingAchStatus.INIT)
    for a_park in tqdm(parks):
        # poi = try_get_primary_poi(a_park.geom)
        poi = _try_get_primary_poi(a_park.geom)
        if poi is None:
            # raise Exception(f"{a_park.park_id} 停车场没有对应的 poi")
            print(f"{a_park.park_id} 停车场没有对应的 poi")
            continue

        line_wkt = _gen_central_line(ctx, a_park)
        line_geo = shapely.wkt.loads(line_wkt)
        if line_geo is None or line_geo.is_empty:
            status = 'INITED_FAILED'
            line_wkt = 'LINESTRING EMPTY'
        else:
            status = ParkingAchStatus.INITED_BASE

        qry = f"""
        update {ctx.park_ach_tab} set 
            status = '{status}',
            name = '{handle_apostrophe(get_park_name(poi.name))}', 
            address = '{handle_apostrophe(poi.address)}', 
            central_line = st_geomfromtext('{line_wkt}', 4326)
            where id = {a_park.park_id}
        """
        ctx.poi_db.execute(qry)
        ctx.poi_db.commit_or_rollback()


def maintain_track_info(ctx: Context):
    """
    维护轨迹信息
    """
    statuses = [
        'PARK_ACCESS_INTELLIGENCE',
        'INITING_BID',
        'INITED_BID',
        'READY',
        'AREA_CHECK_FAILED',
    ]
    where = f"""
    track_url = '' and bid_status = 'effected' 
    and park_type = 'close' 
    and status in ({join_str(statuses)}) order by id desc 
    """
    aff = 0
    for parks in ach.get_parks_generate(ctx, where):
        for park in parks:
            try:
                uuids, url = traj_processor.get_online_track_ids_and_img_url(ctx, park)
                if len(uuids) > 0 and url == '':
                    url = 'https://www.baidu.com'

                qry = f"""
                        update {ctx.park_ach_tab} set track_ids = ARRAY[{join_str(uuids)}]::text[], 
                        track_url = '{url}'  
                        where id = {park.park_id}
                        """
                aff += ctx.poi_db.execute(qry)
            except Exception as e:
                logging.exception(e)
                print(f"{park.park_id} 维护轨迹 id 失败")
        print(f"已维护了：{aff}")
    print(f"维护结束：{aff}")


def maintain_collect_images(ctx: Context):
    """
    维护采集资料
    """
    statuses = [
        'PARK_ACCESS_INTELLIGENCE',
        'INITING_BID',
        'INITED_BID',
        'READY',
        'AREA_CHECK_FAILED',
    ]
    where = f"""
        bid_status = 'effected' and not collect_urls_ok 
        and park_type = 'close' 
        and status in ({join_str(statuses)}) order by id desc 
        """
    aff = 0
    for parks in ach.get_parks_generate(ctx, where):
        for park in parks:
            try:
                images = get_panorama_images(park.geom)
                cd2imgs = {}
                for img in images:
                    cd2imgs.setdefault(img['cdid'], []).append(img)
                qry = f"""
                    update {ctx.park_ach_tab} 
                    set collect_urls = '{json.dumps(cd2imgs)}', collect_urls_ok = true 
                    where id = {park.park_id}
                """
                aff += ctx.poi_db.execute(qry)
            except Exception as e:
                logging.exception(e)
                print(f"{park.park_id} 维护采集资料 失败")
        print(f"已维护了：{aff}")
    print(f"维护结束：{aff}")


def _gen_push_cmd(ctx: Context, parking: ParkingAch):
    """
    生成推送命令
    """
    push_cmd = cmd_gener.gen_park_add_cmd_simply(parking, 0)
    push_cmd.add_after_middlewares(executor.RanCmdExecutor.__name__, [executor.AddParkAfter.__name__])
    cmd_model.add_commands(ctx, [push_cmd])
    return


def point_on_online_face(ctx: Context, parking: ach.ParkingAch):
    """
    点是否在线上的面内，在返回 True
    如果原情报带了 bid，那么这个 bid 不能用来判断
    """
    origin_bid = ''
    info_qry = f"""
    select info_source, bid from {ctx.pushed_tab} where info_id = '{parking.info_id}'
    """
    info_res = ctx.poi_db.fetchone(info_qry)
    if len(info_res) > 0:
        # 历史数据没有情报id，兼容下
        info_source = info_res[0]
        info_bid = info_res[1]
        if info_source == 'online' and info_bid != '':
            origin_bid = info_bid

    qry = f"""
    select bid 
    from {ctx.park_tab} 
    where 
        show_tag = '门前停车场' and status = 1 and park_spec = 1 
        and st_intersects(area, st_geomfromtext('{parking.polygon.representative_point().wkt}', 4326))
    """
    if origin_bid != '':
        qry = f"{qry} and bid != '{origin_bid}' "
    res = ctx.back_db.fetchone(qry)
    return len(res) > 0


def maintain_bid(ctx: Context):
    """
    维护 bid
    若 bid 为空，那么需要生成
    """
    parks = _get_park_aches(ctx, ParkingAchStatus.INITED_BASE)
    # parks = _get_park_aches(ctx, 'INITING_BID')  # todo test
    for a_park in tqdm(parks):
        if a_park.bid == '':
            if point_on_online_face(ctx, a_park):
                status = 'INIT_BID_FAILED'
            else:
                _gen_push_cmd(ctx, a_park)
                status = ParkingAchStatus.INITING_BID
        else:
            status = ach.get_park_ready_status(ctx, a_park.park_id)
        ach.update_park_ach_status(ctx, a_park.park_id, status)
        ctx.poi_db.commit_or_rollback()


def _test_point_on_online_face(ctx: Context):
    park_id = 35149
    res = point_on_online_face(ctx, ach.get_parking_ach(ctx, park_id))
    print(park_id, res)


def offline_access(ctx: Context, access: ach.AccessAch, match_id: int):
    """
    下线出入口
    """
    if access.bid == '':
        # 还没有上线就下线
        qry = f"""
        update {ctx.access_ach_tab} 
        set status = '{ach.AccessAchStatus.ALREADY_OFFLINE}', remark = remark || ';还没有上线就下线' 
        where id = {access.access_id}
        """
        ctx.poi_db.execute(qry)
    else:
        access_cmd = cmd_gener.gen_access_offline_cmd(ctx, access, match_id)
        access_cmd.add_after_middlewares(executor.RanCmdExecutor.__name__, [executor.OfflineAccessAfter.__name__])
        cmd_model.add_commands(ctx, [access_cmd])


def _gen_add_cmds(ctx: Context, park: ParkingAch):
    """
    生成新增流程的推送命令
    出入口是增量更新，而且可能是下线
    """
    match_id = match.gen_blank(ctx, match.ParkMatchStatus.ONLINE)

    # 停车场是更新
    park_cmd = cmd_gener.gen_park_upd_cmd(ctx, park, match_id)
    park_cmd.park_spec(1)
    park_cmd.add_after_middlewares(executor.RanCmdExecutor.__name__, [executor.UpdateParkV2After.__name__])
    commands = [park_cmd]

    # park.remove_bids = ['1772709673138476865']  # todo test
    if len(park.get_remove_bids()) > 0:
        # 需要下线
        commands += cmd_gener.gen_wait_park_offline_cmds(ctx, park_cmd, park.get_remove_bids())

    # 出入口是新增或者下线
    # accesses = ach.get_park_accesses(ctx, park.park_id)
    accesses = ach.get_park_accesses_by_bid(ctx, park.bid)
    for an_access in accesses:
        if not an_access.need_push():
            # raise Exception(f"{park.park_id} 停车场的出入口状态不符合预期:{an_access.status}")
            print(f"{park.park_id} 停车场的出入口状态不符合预期:{an_access.status}")
            continue
        if an_access.need_online():
            if an_access.bid != '':
                access_cmd = cmd_gener.gen_access_upd_cmd(ctx, an_access, match_id)
            else:
                access_cmd = cmd_gener.gen_access_add_cmd(an_access, match_id)
            access_cmd.add_after_middlewares(executor.RanCmdExecutor.__name__, [executor.AddAccessAfter.__name__])
            commands.append(access_cmd)
        else:
            if an_access.bid == '':
                # 还没有上线就下线
                qry = f"""
                update {ctx.access_ach_tab} 
                set status = '{ach.AccessAchStatus.ALREADY_OFFLINE}', remark = remark || ';还没有上线就下线' 
                where id = {an_access.access_id}
                """
                ctx.poi_db.execute(qry)
            else:
                access_cmd = cmd_gener.gen_access_offline_cmd(ctx, an_access, match_id)
                access_cmd.add_after_middlewares(
                    executor.RanCmdExecutor.__name__, [executor.OfflineAccessAfter.__name__])
                commands.append(access_cmd)

    ach.update_park_ach_status(ctx, park.park_id, ach.ParkingAchStatus.COMING_ONLINE)
    cmd_model.add_commands(ctx, commands)
    ctx.poi_db.commit_or_rollback()


def append_push_list(ctx: Context, limit, where):
    """
    添加到推送列表
    """
    where_tag = {
        "all_city": f"""
        1 = 1 
        """,
    }
    if where in where_tag:
        where = where_tag[where]

    parks = _get_park_aches(ctx, ParkingAchStatus.CHECK_SUCCEED, limit, where)
    for a_park in tqdm(parks):
        _gen_add_cmds(ctx, a_park)


def main():
    """
    主函数
    """
    where = str(ARGS.where)
    limit = int(ARGS.limit)

    # ctx = gen_ctx(is_committed=False, debug=True)
    ctx = gen_ctx(is_committed=True, debug=True)

    fns = str(ARGS.fns)
    if fns == 'import':
        import_ach(ctx, limit, where)
    if fns == 'import_upd':
        import_upd_ach(ctx, limit, where)
    if fns == 'import_open_ach':
        # ctx = gen_ctx(is_committed=False, debug=True)
        import_open_ach(ctx, limit, where)
    if fns == 'maintain_base_info':
        maintain_base_info(gen_ctx(autocommit=True, debug=True))
    if fns == 'maintain_track_info':
        maintain_track_info(gen_ctx(autocommit=True, debug=True))
    if fns == 'maintain_collect_images':
        maintain_collect_images(gen_ctx(autocommit=True, debug=True))
    if fns == 'maintain_bid':
        maintain_bid(ctx)
    if fns == 'push':
        # ctx = gen_ctx(is_committed=True, debug=True)
        append_push_list(ctx, limit, where)
    if fns == 'callback':
        sheduler.run_command(ctx)
    if fns == '_test_point_on_online_face':
        _test_point_on_online_face(gen_ctx(is_committed=False, debug=True))


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='核实，图灵的推送和回传')
    parser.add_argument('--where', type=str, default='')
    parser.add_argument('--limit', type=int, default=10)
    parser.add_argument('--multi', type=int, default=1)
    parser.add_argument(
        '--fns',
        type=str,
        required=True,
        help="执行的函数；import: 导入成果; callback: poi 回传；push: 推送停车场"
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()
