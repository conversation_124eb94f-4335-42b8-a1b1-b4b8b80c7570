"""
命令执行
"""
import abc
import json
import logging
from typing import Tuple, Union

import dataclasses

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.model.cmd import (CmdStatus, Cmd)
from src.parking.storefront.flow.db import handle_apostrophe
import src.parking.storefront.flow.model.ach as ach
import src.parking.storefront.flow.model.intervention as it


class CmdExecutor:
    """
    命令执行者
    """

    def __init__(self, ctx: Context, cmd: Cmd):
        self.ctx = ctx
        self.cmd = cmd

    @abc.abstractmethod
    def run(self):
        """
        命令执行
        """

    def log(self, text: str):
        """
        记录日志
        """
        qry = f"insert into {self.ctx.park_match_command_log_tab} (command_id, result) values ({self.cmd.id}, '{text}')"
        self.ctx.poi_db.execute(qry)
        print(f"match_id:{self.cmd.match_id};cmd_id:{self.cmd.id}; {text}")

    def maintain_status(self, status: str):
        """
        维护状态
        """
        qry = (f"update {self.ctx.park_match_command_tab} set status = '{status}' "
               f"where id = {self.cmd.id}")
        return self.ctx.poi_db.execute(qry)


class InitCmdExecutor(CmdExecutor):
    """
    初始化命令执行
    """

    def run(self):
        """
        检查命令是否准备好了，准备好了就变成 ready
        """
        key2fun = {
            'command_is_end': self._command_is_end,
            'package_park_bid_by_park_id': self._package_park_bid_by_park_id,
            'package_access_bid_by_access_id': self._package_access_bid_by_access_id,
            'commands_are_end': self._commands_are_end,
        }

        cmd_arg = self.cmd.args
        if cmd_arg.ready is None:
            return

        ready = cmd_arg.ready
        if ready.fun not in key2fun:
            return

        ok, req_arg = key2fun[ready.fun](ready.arg)
        if not ok:  # 还没有准备好
            return

        # 准备好了，可能需要更新一下请求参数
        if req_arg is not None:
            self._update_request_arg(req_arg)

        self.maintain_status(CmdStatus.READY)
        self.log('已准备好')
        self.ctx.poi_db.commit_or_rollback()
        return

    def _update_request_arg(self, request_arg: dict):
        """
        更新请求参数
        """
        self.cmd.args.request = request_arg
        arg_json = handle_apostrophe(json.dumps(dataclasses.asdict(self.cmd.args), ensure_ascii=False))
        qry = f"update {self.ctx.park_match_command_tab} set args = '{arg_json}' where id = {self.cmd.id}"
        self.ctx.poi_db.execute(qry)

    def _command_is_end(self, arg: dict) -> Tuple[bool, Union[None, dict]]:
        """
        命令是否结束
        """
        if 'source_id' not in arg:
            return False, None
        return command_is_end(self.ctx, arg['source_id']), None

    def _commands_are_end(self, arg: dict) -> Tuple[bool, Union[None, dict]]:
        """
        命令是否都结束了
        """
        source_ids = arg['source_ids']
        for _source_id in source_ids:
            if not command_is_end(self.ctx, _source_id):
                return False, None
        return True, None

    def _package_park_bid_by_park_id(self, arg: dict) -> Tuple[bool, Union[None, dict]]:
        """
        若能通过 停车场的 park_id, 组装成功 停车场的 bid，那么返回 True 和最新的 request 信息
        """
        if 'park_id' not in arg:
            return False, None
        park_id = arg['park_id']
        parking = ach.get_parking_ach(self.ctx, park_id)
        if parking is None or parking.bid == '':  # 还没有停车场 bid
            return False, None
        request = self.cmd.args.request
        request['bid'] = parking.bid
        return True, request

    def _package_access_bid_by_access_id(self, arg: dict) -> Tuple[bool, Union[None, dict]]:
        """
        若能通过 出入口的 access_id, 组装成功 停车场的 bid，那么返回 True 和最新的 request 信息
        """
        if 'access_id' not in arg:
            return False, None
        access_id = arg['access_id']
        an_access = ach.get_access(self.ctx, access_id)
        if an_access is None or an_access.bid == '':
            return False, None
        request = self.cmd.args.request
        request['bid'] = an_access.bid
        return True, request


def command_is_end(ctx: Context, source_id: str) -> bool:
    """
    命令结束了返回 True
    """
    qry = f"select id from {ctx.park_match_command_tab} where source_id = '{source_id}' and status = '{CmdStatus.END}'"
    res = ctx.poi_db.fetchone(qry)
    if res is None or not res:
        return False
    return True


class ReadyCmdExecutor(CmdExecutor):
    """
    命令执行
    """

    def run(self):
        """
        命令推送执行
        """
        try:
            if self.cmd.about_parking():
                ret = self._push(self.ctx.push_park_tab)
            else:
                ret = self._push(self.ctx.push_access_tab)
            self.maintain_status(CmdStatus.RAN)  # 标记已执行
            self.log(f"命令执行成功：{str(ret)}")
            self.ctx.poi_db.commit_or_rollback()
        except Exception as e:
            logging.exception(e)
            self.ctx.poi_db.rollback('异常回滚')

    def _push(self, table: str):
        """
        推送
        """
        cmd = self.cmd
        arg = self.cmd.args

        bid = arg.get_request_val('bid')
        pid = arg.get_request_val('parent_id')
        fid = arg.get_extra_val('park_id')

        c_type = arg.type
        rq_jsn = handle_apostrophe(arg.req_to_json())

        if c_type in ['2', '3'] and bid in ['', None]:
            raise Exception(f"{cmd.id} 命令参数不符合预期")

        if table == self.ctx.push_access_tab:
            qry = (f"insert into {table} (source_id, bid, status, request_info, type, face_id, pid) "
                   f"values ('{cmd.source_id}', '{bid}', 0, '{rq_jsn}', '{c_type}', '{fid}', '{pid}')")
        else:
            qry = (f"insert into {table} (source_id, bid, status, request_info, type, face_id) "
                   f"values ('{cmd.source_id}', '{bid}', 0, '{rq_jsn}', '{c_type}', '{fid}')")
        return self.ctx.poi_db.execute(qry)


class RanCmdExecutor(CmdExecutor):
    """
    已推送的命令等待回传
    """

    def __init__(self, ctx: Context, cmd: Cmd):
        super().__init__(ctx, cmd)
        self.pushed_result: dict = {}

    def run(self):
        """
        已推送的命令等待回传
        """
        try:
            res = self._handle()
            self.log(f"运行命令是否回传：{str(res)}")
            if res:
                self.maintain_status(CmdStatus.END)  # 标记已结束
                self._handle_after()  # after 中可能还会修改 cmd 的状态，需要放在修改状态后面
            self.ctx.poi_db.commit_or_rollback()
        except Exception as e:
            logging.exception(e)
            self.ctx.poi_db.rollback("异常回滚")

    def _handle(self):
        """
        处理停车场的回传
        """
        table = self.ctx.push_park_tab
        if self.cmd.about_access():
            table = self.ctx.push_access_tab
        res = self._get_pushed_result(self.cmd.source_id, table)

        if len(res) == 0:
            raise Exception(f"没有对应的推送记录：{self.cmd.source_id}")

        self.pushed_result = res
        if res['status'] != 2:  # 推送未生效
            return False
        return True

    def _get_pushed_result(self, source_id: str, table: str) -> dict:
        """
        获取推送的结果
        """
        qry = f"select status, bid, type from {table} where source_id = '{source_id}'"
        res = self.ctx.poi_db.fetchone(qry)

        if res is None or not res:
            return {}
        return {
            'status': res[0],
            'bid': res[1],
            'type': res[2],
        }

    def _handle_after(self):
        """
        后处理
        """
        middlewares = self.cmd.get_after_middlewares(RanCmdExecutor.__name__)
        for a_middleware in middlewares:
            handler = create_middleware(a_middleware, self)
            handler.handle()


class CancelCmdExecutor(CmdExecutor):
    """
    命令取消执行
    """

    def run(self):
        """
        命令取消执行
        """
        num = self.maintain_status(CmdStatus.CANCEL)
        self.log(f"取消成功:{num}")
        self.ctx.poi_db.commit_or_rollback()


def get_cmd_executor(ctx: Context, cmd: Cmd) -> CmdExecutor:
    """
    获取命令执行者
    """
    # 如果命令被回滚了且没有执行，那么命令取消
    if cmd.been_rollback() and cmd.not_ran():
        return CancelCmdExecutor(ctx, cmd)
    if cmd.status == CmdStatus.RAN:  # 已运行等待回掉
        return RanCmdExecutor(ctx, cmd)
    if cmd.status == CmdStatus.INIT:  # 等待准备
        return InitCmdExecutor(ctx, cmd)
    if cmd.status == CmdStatus.READY:  # 等待运行
        return ReadyCmdExecutor(ctx, cmd)
    raise Exception(f"{cmd.status} 没有对应的命令执行者")


class Middleware:
    """
    中间件
    """

    def __init__(self, executor: CmdExecutor):
        self.executor = executor

    @abc.abstractmethod
    def handle(self) -> bool:
        """
        处理
        """

    def _get_added_bid(self) -> str:
        """
        获取生成的 bid
        """
        assert isinstance(self.executor, RanCmdExecutor), f"{self.executor.__class__.__name__}"

        res = self.executor.pushed_result
        if res['bid'] == '':  # 需要捞回 bid, 不能为空
            raise Exception(f"新增已生效，bid 不能为空：{self.executor.cmd.source_id}")
        return res['bid']

    def _assert_ran(self):
        assert isinstance(self.executor, RanCmdExecutor), f"""
        actual: {self.executor.__class__.__name__}; expected:RanCmdExecutor
        """


class AddAccessAfter(Middleware):
    """
    出入口新增后处理
    """

    def handle(self) -> bool:
        bid = self._get_added_bid()
        ctx, cmd = self.executor.ctx, self.executor.cmd

        qry = f"""
        update {ctx.access_ach_tab} set bid = '{bid}', status = '{ach.AccessAchStatus.ALREADY_ONLINE}' 
        where id = {int(cmd.guid)}
        """
        ctx.poi_db.execute(qry)
        return True


class OfflineAccessAfter(Middleware):
    """
    下线出入口后处理
    """

    def handle(self) -> bool:
        self._assert_ran()

        ctx, cmd = self.executor.ctx, self.executor.cmd

        bid = str(cmd.guid)
        qry = f"""
                update park_storefront_prod_access 
                set status='ALREADY_OFFLINE', remark= remark || status || '入口下线' 
                where bid = '{bid}'
                """
        ctx.poi_db.execute(qry)
        return True


class OfflineParkAfter(Middleware):
    """
    下线停车场
    """

    def handle(self) -> bool:
        self._assert_ran()

        ctx, cmd = self.executor.ctx, self.executor.cmd

        park_id = int(cmd.args.get_extra_val('park_id', 0))
        if park_id > 0:
            qry = f"""
                            update {ctx.park_ach_tab} 
                            set status='OFFLINE'
                            where id = {park_id}
                            """
            ctx.poi_db.execute(qry)
        return True


class AddParkAfter(Middleware):
    """
    新增停车场后
    """

    def handle(self) -> bool:
        bid = self._get_added_bid()

        ctx, cmd = self.executor.ctx, self.executor.cmd
        park_id = int(cmd.guid)
        status = ach.get_park_ready_status(ctx, park_id)
        qry = f"update {ctx.park_ach_tab} set bid = '{bid}', status = '{status}' where id = {park_id}"
        ctx.poi_db.execute(qry)
        return True


class UpdateParkAfter(Middleware):
    """
    更新停车场后
    """

    def handle(self) -> bool:
        self._assert_ran()

        ctx, cmd = self.executor.ctx, self.executor.cmd
        ach.update_park_ach_status(ctx, int(cmd.guid), ach.ParkingAchStatus.ONLINE)
        return True


class UpdateParkV2After(Middleware):
    """
    更新停车场后处理
    """

    def handle(self) -> bool:
        """
        需要确保面在 parking 表已生效后，才能更新状态; 否则，需要回滚 cmd 状态
        """
        ctx, cmd = self.executor.ctx, self.executor.cmd

        park_id = int(cmd.guid)
        park = ach.get_parking_ach(ctx, park_id)

        qry = f"""
        select bid 
        from {ctx.park_tab} 
        where bid = '{park.bid}' and st_equals(area, st_geomfromtext('{park.geom}', 4326))
        """
        res = ctx.back_db.fetchone(qry)

        if len(res) > 0:
            # 面已生效
            ach.update_park_ach_status(ctx, park_id, ach.ParkingAchStatus.ONLINE)
        else:
            # 面没有生效，cmd 状态需要回滚
            self.executor.maintain_status(CmdStatus.RAN)
        return True


class UpdateInterventionOfflineParkStatusAfter(Middleware):
    """
    更新，下线停车场，干预记录的状态
    """

    def handle(self) -> bool:
        """
        面下线成功之后，干预记录更新
        """
        ctx, cmd = self.executor.ctx, self.executor.cmd

        bid = str(cmd.guid)

        qry = f"""
        update {ctx.intervention_tab} 
        set status = '{it.InterventionStatus.DONE}' 
        where bid = '{bid}' and status = '{it.InterventionStatus.ING}' and type = '{it.InterventionType.PARK_OFFLINE}'
        """
        ctx.poi_db.execute(qry)
        return True


def create_middleware(name: str, executor: CmdExecutor) -> Middleware:
    """
    创建中间件对象
    """
    register = {
        AddAccessAfter.__name__: AddAccessAfter,
        OfflineAccessAfter.__name__: OfflineAccessAfter,
        AddParkAfter.__name__: AddParkAfter,
        UpdateParkAfter.__name__: UpdateParkAfter,
        UpdateParkV2After.__name__: UpdateParkV2After,
        UpdateInterventionOfflineParkStatusAfter.__name__: UpdateInterventionOfflineParkStatusAfter,
        OfflineParkAfter.__name__: OfflineParkAfter,
    }
    if name not in register:
        raise Exception(f"{name} 没有对应的中间件")
    return register[name](executor)

