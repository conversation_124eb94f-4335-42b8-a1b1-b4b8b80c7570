"""
命令生成
"""
import abc
import time
import uuid
from typing import <PERSON>ple, List

import dataclasses

from src.parking.storefront.flow.model.ach import ParkingAch, OnlineParking, AccessAch
from src.parking.storefront.flow.model.cmd import CmdType, Cmd, CmdArg, CmdStatus, CmdReady
import src.parking.storefront.flow.model.ach as ach
from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.cmd import executor
import src.parking.storefront.flow.model.match as match


class Source:
    """
    推送源
    """
    ADD_PARKING = 'STRATEGY_STOREFRONT_ADD'
    ADD_ACCESS = 'STRATEGY_STOREFRONT_ACCESS_ADD'
    REMOVE_PARKING = 'STRATEGY_STOREFRONT_REMOVE'
    REMOVE_ACCESS = 'STRATEGY_STOREFRONT_ACCESS_REMOVE'
    UPDATE_PARKING = 'STRATEGY_STOREFRONT_UPDATE'
    UPDATE_ACCESS = 'STRATEGY_STOREFRONT_ACCESS_UPDATE'

    @staticmethod
    def get_source(c_type: str) -> str:
        """
        获取资源
        """
        type2source = {
            CmdType.ADD_PARKING: Source.ADD_PARKING,
            CmdType.ADD_ACCESS: Source.ADD_ACCESS,
            CmdType.REMOVE_PARKING: Source.REMOVE_PARKING,
            CmdType.REMOVE_ACCESS: Source.REMOVE_ACCESS,
            CmdType.UPDATE_PARKING: Source.UPDATE_PARKING,
            CmdType.UPDATE_ACCESS: Source.UPDATE_ACCESS,
            CmdType.ROLLBACK_ADD_PARKING: Source.REMOVE_PARKING,
            CmdType.ROLLBACK_ADD_ACCESS: Source.REMOVE_ACCESS,
            CmdType.ROLLBACK_REMOVE_PARKING: Source.UPDATE_PARKING,
            CmdType.ROLLBACK_REMOVE_ACCESS: Source.UPDATE_ACCESS,
            CmdType.ROLLBACK_UPDATE_PARKING: Source.UPDATE_PARKING,
            CmdType.ROLLBACK_UPDATE_ACCESS: Source.UPDATE_ACCESS,
        }
        return type2source[c_type]


@dataclasses.dataclass
class RequestSource:
    """
    推送请求源
    """
    source: str
    source_id: str
    batch_id: str


def _gen_push_source(source: str, randy: int) -> RequestSource:
    """
    生成推送请求源
    """
    return RequestSource(
        source=source,
        source_id=_gen_source_id(source, randy),
        batch_id=_gen_batch_id(source)
    )


def _gen_source_id(source: str, match_id: id) -> str:
    """
    生成 source_id，每个需要唯一
    """
    return f"{source}_{match_id}_{uuid.uuid4().hex}"


def _gen_batch_id(source: str) -> str:
    """
    生成批次号
    """
    return f"{source}_{_get_cur_time('%Y%m%d')}"


def _get_cur_time(_format: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    获取当前时间
    """
    return time.strftime(_format, time.localtime(time.time()))


def _gen_push_source_by_cmd_type(c_type: str, randy: int) -> RequestSource:
    """
    生成推送请求源
    """
    source = Source.get_source(c_type)
    return _gen_push_source(source, randy)


class RequestGenerator:
    """
    请求参数生成
    """

    def __init__(self, c_type: str, batch_id: int):
        """
        命令类型
        batch_id, 标记请求是某一批的
        """
        self.source = _gen_push_source_by_cmd_type(c_type, batch_id)
        self.request = {}

    def mark_source(self, source: RequestSource):
        """
        标记来源
        """
        self.request.update(dataclasses.asdict(source))
        if 'area' in self.request:
            self.request['area_batch'] = source.batch_id

    def mark_add(self):
        """
        标记新增
        """
        self.request['is_add_poi'] = 1

    @abc.abstractmethod
    def generate(self) -> dict:
        """
        生成请求参数
        """


class Provider:
    """
    数据来源供给者
    """

    @abc.abstractmethod
    def provide(self) -> dict:
        """
        提供数据
        """
        pass


@dataclasses.dataclass
class Parking:
    """
    停车场
    """
    bid: str
    name: str
    address: str
    point_x: float
    point_y: float
    geom: str
    central_line: str
    park_spec: int

    @staticmethod
    def by_ach(park: ParkingAch) -> 'Parking':
        """
        根据成果转换
        """
        return Parking(
            bid=park.bid,
            name=park.name,
            address=park.address,
            point_x=park.point_x,
            point_y=park.point_y,
            geom=park.geom,
            central_line=park.central_line,
            park_spec=0,
        )

    @staticmethod
    def by_online(park: OnlineParking) -> 'Parking':
        """
        根据线上数据转换
        """
        return Parking(
            bid=park.bid,
            name=park.name,
            address=park.address,
            point_x=park.point_x,
            point_y=park.point_y,
            geom=park.geom,
            central_line=park.central_line,
            park_spec=park.park_spec,
        )


@dataclasses.dataclass
class Access:
    """
    出入口
    """
    bid: str
    name: str
    address: str
    road_relation: dict
    point_x: float
    point_y: float
    parent_id: str

    @staticmethod
    def by_ach(access: AccessAch) -> 'Access':
        """根据成果转换"""
        return Access(
            bid=access.bid,
            parent_id=access.parent_bid,
            name=access.name,
            address=access.address,
            road_relation=access.road_relation,
            point_x=access.point_x,
            point_y=access.point_y,
        )

    @staticmethod
    def by_online(online: ach.OnlineAccess) -> 'Access':
        """根据线上数据转换"""
        return Access(
            bid=online.bid,
            parent_id=online.bid,
            name=online.name,
            address=online.address,
            road_relation=online.road_relation,
            point_x=online.point_x,
            point_y=online.point_y,
        )


class ParkSimplyProvider(Provider):
    """
    提供停车场信息，简单的，不包括面和线，只有点
    """

    def __init__(self, parking: Parking):
        self.parking = parking

    def provide(self) -> dict:
        parking = self.parking
        return {
            'address': parking.address,
            'name': parking.name,
            'point_x': parking.point_x,
            'point_y': parking.point_y,
            'show_tag': '门前停车场',
        }


class ParkCompleteProvider(Provider):
    """
    提供停车场信息，完整的包括面和线，点
    """

    def __init__(self, parking: Parking, with_bid: bool = True, park_spec=None):
        self.parking = parking
        self.with_bid = with_bid
        self.park_spec = park_spec

    def provide(self) -> dict:
        parking = self.parking
        results = {
            'address': parking.address,
            'name': parking.name,
            'point_x': parking.point_x,
            'point_y': parking.point_y,
            'show_tag': '门前停车场',
            'area': parking.geom,
            'show_area': parking.geom,
            'central_line': parking.central_line,
            'status': 1,
        }
        if self.with_bid:
            results['bid'] = parking.bid
        if self.park_spec is not None:
            results['park_spec'] = self.park_spec
        return results


class AccessProvider(Provider):
    """
    提供出入口数据
    """

    def __init__(self, access: Access, with_bid: bool = False):
        self.access = access
        self.with_bid = with_bid

    def provide(self) -> dict:
        access = self.access
        result = {
            'address': access.address,
            'parent_id': access.parent_id,
            'name': access.name,
            'point_x': access.point_x,
            'point_y': access.point_y,
            'show_tag': '停车场入口',
            'road_relation': access.road_relation,
            'status': 1,
        }
        if self.with_bid:
            result['bid'] = access.bid
        return result


class OfflineProvider(Provider):
    """
    下线数据供给器
    """

    def __init__(self, bid: str):
        self.bid = bid

    def provide(self) -> dict:
        return {
            'bid': self.bid,
            'status': 2,
        }


class OnlineProvider(Provider):
    """
    上线数据供给器
    """

    def __init__(self, bid: str):
        self.bid = bid

    def provide(self) -> dict:
        return {
            'bid': self.bid,
            'status': 1,
        }


class SpecialProvider(Provider):
    """
    特殊，原封不动返回
    """

    def __init__(self, data: dict):
        self.data = data

    def provide(self) -> dict:
        return self.data


class AddRequestGenerator(RequestGenerator):
    """
    新增请求参数生成
    """

    def __init__(self, provider: Provider, c_type: str, batch_id: int):
        super().__init__(c_type, batch_id)
        self.provider = provider

    def generate(self) -> dict:
        self.request = self.provider.provide()
        self.mark_source(self.source)
        self.mark_add()
        return self.request


class UpdRequestGenerator(RequestGenerator):
    """
    更新请求参数生成
    """

    def __init__(self, provider: Provider, c_type: str, batch_id: int):
        super().__init__(c_type, batch_id)
        self.provider = provider

    def generate(self) -> dict:
        self.request = self.provider.provide()
        self.mark_source(self.source)
        return self.request


def gen_park_add_cmd_simply(park: ParkingAch, match_id: int) -> Cmd:
    """
    生成停车场新增 cmd；没有面和线；park_spec = 0
    """
    provider = ParkSimplyProvider(parking=Parking.by_ach(park))
    return _gen_park_add_cmd(park, provider, match_id)


def gen_park_add_cmd_complete(park: ParkingAch, match_id: int) -> Cmd:
    """
    生成停车场新增 cmd；park_spec = 1
    """
    provider = ParkCompleteProvider(parking=Parking.by_ach(park), park_spec=1)
    return _gen_park_add_cmd(park, provider, match_id)


def _gen_park_add_cmd(park: ParkingAch, provider: Provider, match_id: int):
    extra = {'park_id': park.park_id}
    ready = CmdReady(fun='package_park_bid_by_park_id', arg={'park_id': park.park_id})
    _guid = str(park.park_id)
    return _gen_add_cmd(_guid, CmdType.ADD_PARKING, extra, match_id, provider, ready)


def _gen_add_cmd(guid: str, cmd_type: str, extra, match_id, provider, ready):
    ad_gener = AddRequestGenerator(provider=provider, c_type=cmd_type, batch_id=match_id)
    requests = ad_gener.generate()
    return Cmd(
        id=0,
        match_id=match_id,
        guid=guid,
        source_id=requests['source_id'],
        type=cmd_type,
        status=CmdStatus.READY,
        args=CmdArg(
            request=requests,
            type='1',
            extra=extra,
            rollback=CmdArg(
                request=_gen_add_back_cmd_req('', cmd_type=cmd_type, match_id=match_id),
                type='2',
                ready=ready,
                extra=extra,
            )
        )
    )


def _gen_add_back_cmd_req(bid: str, cmd_type: str, match_id: int) -> dict:
    """
    新增回滚请求参数
    """
    provider = OfflineProvider(bid=bid)
    up_gener = UpdRequestGenerator(provider, CmdType.get_rollback_type(cmd_type), match_id)
    return up_gener.generate()


def gen_park_upd_cmd(ctx: Context, park: ParkingAch, match_id: int) -> Cmd:
    """
    停车场更新
    """

    def _gen_request(parking: Parking, c_type: str) -> dict:
        provider = ParkCompleteProvider(parking=parking, with_bid=True, park_spec=parking.park_spec)
        up_gener = UpdRequestGenerator(provider=provider, c_type=c_type, batch_id=match_id)
        return up_gener.generate()

    online_park = ach.get_online_park(ctx, park.bid)
    if not online_park:
        raise Exception(f"{park.park_id} 不存在线上停车场")

    cmd_type = CmdType.UPDATE_PARKING
    requests = _gen_request(Parking.by_ach(park), cmd_type)
    rollback = _gen_request(Parking.by_online(online_park), CmdType.get_rollback_type(cmd_type))

    extra = {'park_id': park.park_id}
    return Cmd(
        id=0,
        match_id=match_id,
        guid=str(park.park_id),
        source_id=requests['source_id'],
        type=cmd_type,
        status=CmdStatus.READY,
        args=CmdArg(
            request=requests,
            type='3',
            extra=extra,
            rollback=CmdArg(
                request=rollback,
                type='3',
                extra=extra,
            )
        )
    )


def gen_access_add_cmd(access: AccessAch, match_id: int) -> Cmd:
    """
    生成出入口新增命令
    """
    cmd_type = CmdType.ADD_ACCESS
    provider = AccessProvider(access=Access.by_ach(access), with_bid=False)

    _guid = access.access_id
    extra = {}
    ready = CmdReady(
        fun='package_access_bid_by_access_id',
        arg={'access_id': access.access_id},
    )
    return _gen_add_cmd(_guid, cmd_type, extra, match_id, provider, ready)


def gen_access_upd_cmd(ctx: Context, access: AccessAch, match_id: int, memo: str = '') -> Cmd:
    """
    生成出入口更新命令
    """
    if access.bid == '':
        raise Exception(f"出入口更新，但没有 bid：{access.access_id}")
    cmd_type = CmdType.UPDATE_ACCESS
    _guid = access.access_id
    extra = {'bid': access.bid, 'access_id': access.access_id}

    def _gen_request(an_access: Access, c_type: str) -> dict:
        provider = AccessProvider(an_access, with_bid=True)
        up_gener = UpdRequestGenerator(provider=provider, c_type=c_type, batch_id=match_id)
        return up_gener.generate()

    online_access = ach.get_online_access(ctx, access.bid)
    if not online_access:
        raise Exception(f"{access.bid} 不存在线上出入口")

    requests = _gen_request(Access.by_ach(access), cmd_type)
    rollback = _gen_request(Access.by_online(online_access), CmdType.get_rollback_type(cmd_type))

    return Cmd(
        id=0,
        match_id=match_id,
        guid=str(_guid),
        source_id=requests['source_id'],
        type=cmd_type,
        status=CmdStatus.READY,
        args=CmdArg(
            request=requests,
            type='3',
            extra=extra,
            rollback=CmdArg(
                request=rollback,
                type='3',
                extra=extra,
            )
        ),
        memo=memo,
    )


def gen_access_offline_cmd(ctx: Context, access: AccessAch, match_id: int, memo: str = '') -> Cmd:
    """
    生成出入口下线命令
    """
    bid = access.bid
    if bid == '':
        raise Exception(f"{access.access_id} 不存在 bid")
    return _gen_offline_cmd(ctx, access.bid, match_id, CmdType.REMOVE_ACCESS, memo)


def gen_access_offline_cmd_by_bid(ctx: Context, bid: str, match_id: int, memo: str) -> Cmd:
    """
    生成口下线命令，根据 bid
    """
    return _gen_offline_cmd(ctx, bid, match_id, CmdType.REMOVE_ACCESS, memo)


def _gen_offline_cmd(ctx: Context, bid: str, match_id: int, cmd_type: str, memo: str = '') -> Cmd:
    online = ach.get_online(ctx, bid, fields=['status'])
    if not online:
        raise Exception(f" bid:{bid} 在线上不存在")

    provider = OfflineProvider(bid=bid)
    requests = UpdRequestGenerator(provider=provider, c_type=cmd_type, batch_id=match_id).generate()
    rollback = UpdRequestGenerator(
        provider=SpecialProvider(data={'status': online['status']}),
        c_type=CmdType.get_rollback_type(cmd_type),
        batch_id=match_id,
    ).generate()

    return Cmd(
        id=0,
        match_id=match_id,
        guid=bid,
        source_id=requests['source_id'],
        type=cmd_type,
        status=CmdStatus.READY,
        args=CmdArg(
            request=requests,
            type='2',
            rollback=CmdArg(
                request=rollback,
                type='2',
            )
        ),
        memo=memo,
    )


def gen_update_cmd(ctx: Context, bid: str, match_id: int, cmd_type: str, data: dict, memo: str = '') -> Cmd:
    """
    生成更新命令
    """
    online = ach.get_online(ctx, bid, fields=list(data.keys()))
    if not online:
        raise Exception(f" bid:{bid} 在线上不存在")

    data['bid'] = bid

    provider = SpecialProvider(data=data)
    requests = UpdRequestGenerator(provider=provider, c_type=cmd_type, batch_id=match_id).generate()
    rollback = UpdRequestGenerator(
        provider=SpecialProvider(data=online),
        c_type=CmdType.get_rollback_type(cmd_type),
        batch_id=match_id,
    ).generate()

    return Cmd(
        id=0,
        match_id=match_id,
        guid=bid,
        source_id=requests['source_id'],
        type=cmd_type,
        status=CmdStatus.READY,
        args=CmdArg(
            request=requests,
            type='3',
            rollback=CmdArg(
                request=rollback,
                type='3',
            )
        ),
        memo=memo,
    )


def gen_wait_park_offline_cmds(ctx: Context, wait_cmd: Cmd, bids: list) -> List[Cmd]:
    """
    生成等待下线的命令，等 wait_cmd 命令结束后才能执行
    """
    ready = CmdReady(
        fun='command_is_end', arg={'source_id': wait_cmd.source_id}
    )
    cmds = []
    bids = list(set(bids))
    for a_bid in bids:
        a_cmd = _gen_offline_cmd(ctx, a_bid, wait_cmd.match_id, CmdType.REMOVE_PARKING)
        a_cmd.set_ready(ready)
        cmds.append(a_cmd)

        # 下线出入口
        access_bids = ach.get_online_park_access_bids(ctx, a_bid)
        for an_access_bid in access_bids:
            a_cmd = _gen_offline_cmd(ctx, an_access_bid, wait_cmd.match_id, CmdType.REMOVE_ACCESS)
            a_cmd.set_ready(ready)
            cmds.append(a_cmd)
    return cmds


def gen_park_offline_cmd(ctx: Context, parking: ParkingAch, match_id: int, memo: str = '') -> Cmd:
    """
    生成停车车下线命令
    """
    if parking.bid == '':
        raise Exception(f"{parking.park_id} 没有bid，不能下线")
    cmd = _gen_offline_cmd(ctx, parking.bid, match_id, CmdType.REMOVE_PARKING, memo)
    cmd.args.set_extra_val('park_id', parking.park_id)
    return cmd


def gen_park_access_offline_cmds(ctx: Context, parking: ParkingAch, match_id: int) -> List[Cmd]:
    """
    生成停车场，及出入口的下线命令
    """
    park_cmd = gen_park_offline_cmd(ctx, parking, match_id)
    park_cmd.args.set_extra_val('park_id', parking.park_id)

    cmds = [park_cmd]
    # 下线出入口
    access_bids = ach.get_online_park_access_bids(ctx, parking.bid)
    for an_access_bid in access_bids:
        a_cmd = _gen_offline_cmd(ctx, an_access_bid, match_id, CmdType.REMOVE_ACCESS)
        a_cmd.add_after_middlewares(executor.RanCmdExecutor.__name__, [executor.OfflineAccessAfter.__name__])
        cmds.append(a_cmd)
    return cmds


def gen_online_park_access_offline_cmds(ctx: Context, bid: str, match_id: int, memo: str = '') -> List[Cmd]:
    """
    生成线上 停车场，及出入口的下线命令
    """
    cmds = [_gen_offline_cmd(ctx, bid, match_id, CmdType.REMOVE_PARKING, memo)]
    # 下线出入口
    access_bids = ach.get_online_park_access_bids(ctx, bid)
    for an_access_bid in access_bids:
        a_cmd = _gen_offline_cmd(ctx, an_access_bid, match_id, CmdType.REMOVE_ACCESS, memo)
        a_cmd.add_after_middlewares(executor.RanCmdExecutor.__name__, [executor.OfflineAccessAfter.__name__])
        cmds.append(a_cmd)
    return cmds
