"""
调度
"""
import logging
from typing import List

from tqdm import tqdm

import src.parking.storefront.flow.model.cmd as cmd
import src.tools.redis_tool as rt
from src.parking.storefront.flow.cmd.executor import (InitCmdExecutor, ReadyCmdExecutor, RanCmdExecutor,
                                                      CancelCmdExecutor, CmdExecutor)
from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.model.cmd import Cmd, CmdType, CmdStatus
from src.tools.locker import RedisLocker, lock_guardian


def _group_commands(commands: List[Cmd]) -> List[List[Cmd]]:
    parks = []
    accesses = []
    for a_cmd in commands:
        if a_cmd.about_parking():
            parks.append(a_cmd)
        else:
            accesses.append(a_cmd)
    return _group_commands_by_guid(parks) + _group_commands_by_guid(accesses)


def _group_commands_by_guid(commands: List[Cmd]) -> List[List[Cmd]]:
    """
    获取一个批次的
    """
    guid2cmds = {}
    for a_cmd in commands:
        guid2cmds.setdefault(a_cmd.guid, []).append(a_cmd)
    return list(guid2cmds.values())


def _commands_rollback(commands: List[Cmd]) -> bool:
    """
    命令都回滚了，返回 True
    """
    push_type2num = {}
    back_type2num = {}

    for a_cmd in commands:
        c_type = a_cmd.type
        if a_cmd.is_rollback_cmd():
            back_type2num[c_type] = back_type2num.get(c_type, 0) + 1
        else:
            push_type2num[c_type] = push_type2num.get(c_type, 0) + 1

    rollback = True
    for c_type, num in push_type2num.items():
        b_type = CmdType.get_rollback_type(c_type)
        if b_type not in back_type2num or back_type2num[b_type] < num:
            rollback = False
            break
    return rollback


def _get_cmd_executor(ctx: Context, command: Cmd) -> CmdExecutor:
    """
    获取命令执行者
    """
    # 如果命令被回滚了且没有执行，那么命令取消
    if command.been_rollback() and command.not_ran():
        return CancelCmdExecutor(ctx, command)
    if command.status == CmdStatus.RAN:  # 已运行等待回掉
        return RanCmdExecutor(ctx, command)
    if command.status == CmdStatus.INIT:  # 等待准备
        return InitCmdExecutor(ctx, command)
    if command.status == CmdStatus.READY:  # 等待运行
        return ReadyCmdExecutor(ctx, command)
    raise Exception(f"{command.status} 没有对应的命令执行者")


def run_match_command(ctx: Context, match_id: int):
    """
    跑一个匹配的命令
    若有回滚，把命令取消
    """
    commands = cmd.get_match_need_run_commands(ctx, match_id)
    cmds_group = _group_commands(commands)
    for cmds in cmds_group:
        rollback = _commands_rollback(cmds)
        for a_cmd in cmds:
            if a_cmd.is_end():  # 命令已结束
                continue
            a_cmd.mark_rollback(rollback)  # 标记命令是否回滚
            executor = _get_cmd_executor(ctx, a_cmd)
            executor.run()


def _get_match_redis_locker(match_id: int) -> RedisLocker:
    """
    获取 rds 加锁
    """
    return RedisLocker(
        rds=rt.RedisTool().redis_conn,
        key=f"park:push:match:{match_id}",
        ex=20 * 60,
    )


def run_command(ctx: Context):
    """
    命令执行
    """
    match_ids = cmd.get_wait_run_command_match_ids(ctx)
    for a_match_id in tqdm(match_ids):
        # if a_match_id != 0:  # todo debug
        #     continue

        try:
            # 确保一个
            rds = _get_match_redis_locker(a_match_id)
            lock_guardian(rds, run_match_command, ctx, a_match_id)
            # # todo debug
            # _run_match_command(ctx, a_match_id)
        except Exception as e:
            print(f"{a_match_id} 命令执行失败")
            logging.exception(e)
