"""
相关配置
"""


def get_cities_sort() -> list:
    """
    获取城市顺序
    """
    return [
        '北京市',
        '上海市',
        '广州市',
        '深圳市',
        '杭州市',
        '成都市',
        '武汉市',
        '西安市',
        '重庆市',
        '东莞市',
        '南京市',
        '昆明市',
        '天津市',
        '佛山市',
        '长沙市',
        '宁波市',
        '郑州市',
        '苏州市',
        '合肥市',
        '南宁市',
        '青岛市',
        '厦门市',
        '济南市',
        '福州市',
        '石家庄市',
        '中山市',
        '温州市',
        '金华市',
        '无锡市',
        '惠州市',
        '贵阳市',
        '常州市',
        '太原市',
        '绍兴市',
        '大连市',
        '南昌市',
        '江门市',
        '台州市',
        '汕头市',
        '咸阳市',
        '嘉兴市',
        '洛阳市',
        '南通市',
        '湛江市',
        '保定市',
        '乌鲁木齐市',
        '临沂市',
        '海口市',
        '湖州市',
        '沈阳市',  # 50城
        '绵阳市',
        '珠海市',
        '赣州市',
        '烟台市',
        '哈尔滨市',
        '银川市',
        '柳州市',
        '扬州市',
        '清远市',
        '呼和浩特市',
        '揭阳市',
        '桂林市',
        '潍坊市',
        '宜昌市',
        '南阳市',
        '盐城市',
        '廊坊市',
        '南充市',
        '济宁市',
        '德阳市',
        '邯郸市',
        '荆州市',
        '徐州市',
        '肇庆市',
        '襄阳市',
        '株洲市',
        '镇江市',
        '芜湖市',
        '新乡市',
        '淮安市',
        '黄冈市',
        '孝感市',
        '宜宾市',
        '眉山市',
        '泉州市',
        '兰州市',
        '渭南市',
        '包头市',
        '淄博市',
        '茂名市',
        '遵义市',
        '漳州市',
        '阳江市',
        '菏泽市',
        '衡阳市',
        '泸州市',
        '阜阳市',
        '河源市',
        '长春市',
        '泰州市',
        '常德市',
        '梅州市',
        '岳阳市',
        '沧州市',
        '安阳市',
        '许昌市',
        '九江市',
        '邢台市',
        '连云港市',
        '西宁市',
        '驻马店市',
        '湘潭市',
        '玉林市',
        '日照市',
        '聊城市',
        '商丘市',
        '开封市',
        '上饶市',
        '威海市',
        '乐山市',
        '资阳市',
        '六安市',
        '安庆市',
        '信阳市',
        '平顶山市',
        '鄂州市',
        '潮州市',
        '大理白族自治州',
        '郴州市',
        '曲靖市',
        '德州市',
        '韶关市',
        '达州市',
        '内江市',
        '大同市',
        '泰安市',
        '榆林市',
        '自贡市',
        '秦皇岛市',
        '汉中市',
        '宿迁市',
        '十堰市',
        '晋中市',
        '邵阳市',
        '汕尾市',
        '衢州市',
        '黄石市',
        '贵港市',
        '濮阳市',
        '丽水市',  # 150 城
        '广安市',
        '三亚市',
        '荆门市',
        '滁州市',
        '运城市',
        '莆田市',
        '遂宁市',
        '东营市',
        '咸宁市',
        '蚌埠市',
        '北海市',
        '宿州市',
        '宝鸡市',
        '衡水市',
        '马鞍山市',
        '亳州市',
        '云浮市',
        '拉萨市',
        '宜春市',
        '舟山市',
        '鄂尔多斯市',
        '承德市',
        '宣城市',
        '宁德市',
        '吉安市',
        '周口市',
        '赤峰市',
        '红河哈尼族彝族自治州',
        '永州市',
        '长治市',
        '张家口市',
        '凉山彝族自治州',
        '毕节市',
        '临汾市',
        '玉溪市',
        '鞍山市',
        '恩施土家族苗族自治州',
        '梧州市',
        '娄底市',
        '唐山市',
        '黔南布依族苗族自治州',
        '广元市',
        '河池市',
        '龙岩市',
        '益阳市',
        '仙桃市',
        '怀化市',
        '昭通市',
        '焦作市',
        '抚州市',
        '晋城市',
        '景德镇市',
        '楚雄彝族自治州',
        '延安市',
        '萍乡市',
        '喀什地区',
        '齐齐哈尔市',
        '淮北市',
        '黔东南苗族侗族自治州',
        '枣庄市',
        '雅安市',
        '安顺市',
        '商洛市',
        '鹤壁市',
        '西双版纳傣族自治州',
        '巴中市',
        '大庆市',
        '伊犁哈萨克自治州',
        '黔西南布依族苗族自治州',
        '吕梁市',
        '百色市',
        '来宾市',
        '丽江市',
        '通辽市',
        '黄山市',
        '铜仁市',
        '三明市',
        '六盘水市',
        '安康市',
        '漯河市',
        '文山壮族苗族自治州',
        '三门峡市',
        '贺州市',
        '锦州市',
        '抚顺市',
        '阿克苏地区',
        '保山市',
        '南平市',
        '湘西土家族苗族自治州',
        '丹东市',
        '钦州市',
        '忻州市',
        '乌兰察布市',
        '潜江市',
        '崇左市',
        '昌吉回族自治州',
        '盘锦市',
        '池州市',
        '普洱市',
        '天门市',
        '攀枝花市',
        '吉林市',
        '新余市',
        '巴音郭楞蒙古自治州',
        '巴彦淖尔市',
        '庆阳市',
        '葫芦岛市',
        '防城港市',
        '呼伦贝尔市',
        '营口市',
        '滨州市',
        '朔州市',
        '朝阳市',
        '随州市',
        '吴忠市',
        '鹰潭市',
        '张家界市',
        '阳泉市',
        '儋州市',
        '淮南市',
        '酒泉市',
        '铜川市',
        '牡丹江市',
        '绥化市',
        '延边朝鲜族自治州',
        '乌海市',
        '济源市',
        '石嘴山市',
        '阿坝藏族羌族自治州',
        '阜新市',
        '辽阳市',
        '临沧市',
        '佳木斯市',
        '兴安盟',
        '中卫市',
        '和田地区',
        '琼海市',
        '石河子市',
        '陵水黎族自治县',
        '铁岭市',
        '锡林郭勒盟',
        '德宏傣族景颇族自治州',
        '武威市',
        '铜陵市',
        '固原市',
        '平凉市',
        '张掖市',
        '克拉玛依市',
        '临夏回族自治州',
        '本溪市',
        '定西市',
        '陇南市',
        '文昌市',
        '嘉峪关市',
        '天水市',
        '海东市',
        '通化市',
        '鸡西市',
        '迪庆藏族自治州',
        '万宁市',
        '四平市',
        '东方市',
        '乐东黎族自治县',
        '澄迈县',
        '塔城地区',
        '黑河市',
        '白银市',
        '海西蒙古族藏族自治州',
        '定安县',
        '双鸭山市',
        '松原市',
        '临高县',
        '白城市',
        '日喀则市',
        '阿拉尔市',
        '博尔塔拉蒙古自治州',
        '金昌市',
        '白山市',
        '怒江傈僳族自治州',
        '阿勒泰地区',
        '昌江黎族自治县',
        '鹤岗市',
        '辽源市',
        '阿拉善盟',
        '七台河市',
        '昌都市',
        '五家渠市',
        '屯昌县',
        '海南藏族自治州',
        '琼中黎族苗族自治县',
        '伊春市',
        '图木舒克市',
        '保亭黎族苗族自治县',
        '克孜勒苏柯尔克孜自治州',
        '甘南藏族自治州',
        '五指山市',
        '海北藏族自治州',
        '黄南藏族自治州',
        '白沙黎族自治县',
        '阿里地区',
        '神农架林区',
        '玉树藏族自治州',
        '果洛藏族自治州',
        '大兴安岭地区',
        '三沙市',
        '山南市',
        '哈密地区',
        '吐鲁番地区',
        '林芝地区',
        '那曲地区',
        '莱芜市',
        '甘孜藏族自治州',
    ]


def sort_by_city(cities: list, items: list) -> list:
    """
    按照城市排序
    """
    sorted_cities = get_cities_sort()
    city_order = {city: idx for idx, city in enumerate(sorted_cities)}

    # 按照 city_order 排序，未在 city_order 里的城市排在最后
    sorted_items = sorted(zip(cities, items), key=lambda x: city_order.get(x[0], float('inf')))
    return [item for _, item in sorted_items]

