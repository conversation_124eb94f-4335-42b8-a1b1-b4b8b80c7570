"""
流程上下文
"""

from src.parking.storefront.flow.db import ReadDb, DB
from src.tools import pgsql
import src.model_mysql.beeflow_model as beeflow_model


class Context:
    """
    上下文
    """
    is_committed = True
    # is_committed = False

    poi_db: DB = None
    back_db: DB = None
    # compute_db: DB = None
    bf_query: beeflow_model.BFQuery = None
    road_db: ReadDb = None
    trans_db: ReadDb = None
    dest_db: ReadDb = None
    traj_db: DB = None
    traj_db2: DB = None

    park_match_tab = 'park_storefront_prod_match'
    park_match_command_tab = 'park_storefront_prod_match_command'
    park_match_command_log_tab = 'park_storefront_prod_match_command_log'
    park_ach_tab = 'park_storefront_prod_parking'
    access_ach_tab = 'park_storefront_prod_access'
    park_tab = 'parking'

    push_park_tab = 'parking_area_push '
    push_access_tab = 'parking_gate_push'

    strategy_tab = 'park_storefront_strategy'
    pushed_tab = 'park_storefront_verify_pushed'
    task_tab = 'park_storefront_task'
    turing_result_tab = 'park_storefront_turing_result'
    verified_push_tab = 'park_storefront_verify_pushed'
    repair_tab = 'park_storefront_repair'
    intervention_tab = 'park_storefront_intervention'


def gen_ctx(is_committed: bool = True, debug: bool = False, autocommit: bool = False) -> Context:
    """
    生成上下文
    """
    ctx = Context()
    ctx.is_committed = is_committed
    read_only = (not ctx.is_committed)

    ctx.poi_db = DB(config=pgsql.POI_CONFIG, read_only=read_only, debug=debug, autocommit=autocommit)
    ctx.back_db = ReadDb(config=pgsql.BACK_CONFIG, debug=debug, autocommit=True)
    # ctx.compute_db = ReadDb(config=pgsql.COMPUTE_CONFIG, debug=debug, autocommit=True)
    ctx.bf_query = beeflow_model.BFQuery()

    ctx.road_db = ReadDb(config=pgsql.ROAD_CONFIG_WITH_INDEX, debug=debug, autocommit=True)
    ctx.trans_db = ReadDb(config=pgsql.TRANS_ID, debug=debug, autocommit=True)
    ctx.dest_db = ReadDb(config=pgsql.DEST_TRAJ, debug=debug, autocommit=True)
    ctx.traj_db = DB(config=pgsql.TRAJECTORY_CONFIG, read_only=read_only, debug=debug, autocommit=autocommit)
    ctx.traj_db2 = DB(config=pgsql.TRAJ_DB2, read_only=read_only, debug=debug, autocommit=autocommit)

    return ctx


class ContextManager:
    """上下文管理"""
    def __init__(self, is_committed: bool = True, debug: bool = False, autocommit: bool = False):
        self.is_committed = is_committed
        self.debug = debug
        self.autocommit = autocommit
        self.ctx = None

    def __enter__(self) -> 'Context':
        """进入 with 块时调用，返回上下文对象"""
        self.ctx = gen_ctx(self.is_committed, self.debug, self.autocommit)
        return self.ctx

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出 with 块时调用，负责资源清理"""
        # 关闭所有数据库连接
        for attr in ['poi_db', 'back_db', 'road_db', 'trans_db',
                     'dest_db', 'traj_db', 'traj_db2']:
            db = getattr(self.ctx, attr, None)
            if db is not None:
                try:
                    db.close()
                except Exception as e:
                    print(f"Error closing {attr}: {e}")

        # 如果有异常且 is_committed=False，可以在这里处理回滚逻辑
        if exc_type is not None and not self.is_committed:
            print(f"Operation failed, rolling back: {exc_val}")

        # 返回 False 让异常继续传播
        return False


def gen_ctx_manager(is_committed: bool = True, debug: bool = False, autocommit: bool = False) -> ContextManager:
    """
    生成支持 with 语句的上下文管理器

    示例:
        with gen_ctx(is_committed=False) as ctx:
            ctx.poi_db.query(...)
    """
    return ContextManager(is_committed=is_committed,
                          debug=debug,
                          autocommit=autocommit)

