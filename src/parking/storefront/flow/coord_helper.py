"""
坐标助手
"""
import abc

import requests


class Transfer:
    """
    坐标转换
    """
    GCJ02 = 1
    BD09MC = 2
    BD09LL = 3
    WGS84 = 4

    @staticmethod
    @abc.abstractmethod
    def transfer(wkt: str, coord_from: int, coord_to: int) -> str:
        """
        坐标系转换
        """


class TransferByBeeFlow(Transfer):
    """
    通过 beeFlow 的方式来转换坐标系
    """

    @staticmethod
    def transfer(wkt: str, coord_from: int, coord_to: int) -> str:
        url = 'http://mapde-poi.baidu-int.com/prod/api/coordsTrans'
        param = {
            'wkt': wkt,
            'from': str(coord_from),
            'to': str(coord_to),
        }
        res = requests.post(
            url, data=param,
        )
        jsn = res.json()
        if jsn['code'] != 0:
            print(jsn)
            return ''
        return jsn['data']


def gcj2bdmc(wkt: str, transfer: str = 'beeflow') -> str:
    """
    gcj2bdmc
    """
    return TransferByBeeFlow.transfer(wkt, Transfer.GCJ02, Transfer.BD09MC)


def bdmc2gcj(wkt: str, transfer: str = 'beeflow') -> str:
    """
    bdmc2gcj
    """
    return TransferByBeeFlow.transfer(wkt, Transfer.BD09MC, Transfer.GCJ02)


def bd092gcj(wkt: str, transfer: str = 'beeflow') -> str:
    """
    bd092gcj
    """
    return TransferByBeeFlow.transfer(wkt, Transfer.BD09LL, Transfer.GCJ02)


def gcj2bd09(wkt: str, transfer: str = 'beeflow') -> str:
    """
    gcj2bd09
    """
    return TransferByBeeFlow.transfer(wkt, Transfer.GCJ02, Transfer.BD09LL)

