"""
数据库
"""


from src.tools import pgsql


class DB:
    """
    pg 数据库
    """

    def __init__(self, config: dict, read_only: bool = False, debug: bool = False, autocommit: bool = False):
        self.conn = None
        self.curs = None

        self.config = config
        self.autocommit = autocommit
        self.read_only = read_only
        self.debug = debug
        self.last_qry = ''

    def close(self):
        """关闭"""
        self.conn = None

    def _init_conn(self):
        """懒加载"""
        if self.conn is None:
            self.conn = pgsql.get_connection(self.config)
            self.conn.autocommit = self.autocommit
            self.curs = self.conn.cursor()

    def fetchone(self, qry: str) -> tuple:
        """
        获取一条记录
        """
        self._execute(qry)
        res = self.curs.fetchone()
        if res is None or not res:
            return ()
        return res

    def fetchall(self, qry: str) -> list:
        """
        获取多条记录
        """
        self._execute(qry)
        res = self.curs.fetchall()
        if res is None or not res:
            return []
        return res

    def get_values(self, qry: str) -> list:
        """
        获取值列表
        """
        res = self.fetchall(qry)
        return [item[0] for item in res]

    def execute(self, qry: str) -> int:
        """
        执行
        """
        self._execute(qry)
        return self.curs.rowcount

    def insert_return_id(self, qry: str) -> int:
        """
        新增并返回自增id
        """
        if 'RETURNING' not in qry:
            qry = f"{qry}RETURNING id;"
        self._execute(qry)
        return self.curs.fetchone()[0]

    def _execute(self, qry: str):
        if self.debug:
            print(qry)
        self._init_conn()
        self.curs.execute(qry)
        self.last_qry = qry

    def commit(self, desc: str = ''):
        """
        提交事务
        """
        if self.read_only:
            raise Exception(f"仅只读，不可提交事务")
        self.conn.commit()
        print(f"事务已提交; {desc}")

    def rollback(self, desc: str = ''):
        """
        事务回滚
        """
        self.conn.rollback()
        print(f"事务已回滚; {desc}")

    def commit_or_rollback(self, desc: str = ''):
        """
        提交或者回滚
        """
        if not self.read_only:
            self.commit(desc)
        else:
            self.rollback(desc)

    def get_last_qry(self) -> str:
        """
        获取最后一条记录
        """
        return self.last_qry


class ReadDb(DB):
    """
    只读数据库
    """

    def __init__(self, config: dict, debug: bool = False, autocommit: bool = False):
        super().__init__(config, read_only=True, debug=debug, autocommit=autocommit)


def join_str(vals: list) -> str:
    """
    拼接
    """
    return ','.join([f"'{val}'" for val in vals])


def handle_apostrophe(val: str) -> str:
    """
    单引号处理
    """
    return val.replace("'", "''")


def join_int(vals: list) -> str:
    """
    拼接成字符串
    """
    return ','.join([f"{val}" for val in vals])


def array_chunk(data: list, size: int) -> list:
    """
    数据分成块，size 是每块的数量；（最后一块的数量会 <= num）
    :param data:
    :param size:
    :return:
    """
    resp = []

    datum = []
    for _item in data:
        datum.append(_item)

        if len(datum) == size:
            resp.append(datum)
            datum = []

    if len(datum) > 0:
        resp.append(datum)
    return resp

