"""
和建筑物的距离来过滤
"""
import dataclasses
from typing import List, Union

import shapely.wkt
import shapely.ops
from shapely.geometry import Point, Polygon as PPolygon, LineString
import numpy as np

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.merger.model import StrategyFace
from src.parking.storefront.flow.model.road import get_complete_ld_polygon_2d
from src.parking.storefront.diff.polygon_differ import project, interpolate, find_difference_intervals, \
    merge_two_intervals, merge_intervals, Polygon, get_foot_point
from src.parking.storefront.utils.geometric import flat_line, flat_point, extend_linestring, flat_polygon
from src.parking.storefront.check.check_area import calculate_angle
from src.parking.storefront.post_process import autocomplete as auto_cpt
from src.tools.trajectory_tools import get_trajectory_data_by_bid
from src.parking.storefront.post_process.buffer_ultra import buffer_depend_on_street

METER = 1e-5

MIN_DISTANCE = 7 * METER
MIN_RATE = 0.8


@dataclasses.dataclass
class ProjectLink:
    """
    映射的 link
    """
    face_id: str
    start_pt: Point
    end_pt: Point
    start_foot: Point
    end_foot: Point
    start_dist: float
    end_dist: float
    # average_dist: float

    def __post_init__(self):
        self.average_dist = (self.start_dist + self.end_dist) / 2

    def get_polygon(self):
        """
        获取覆盖的面
        """
        return PPolygon(LineString([self.start_pt, self.end_pt, self.end_foot, self.start_foot, self.start_pt]))

    def get_length(self) -> float:
        """
        获取长度
        """
        return self.start_foot.distance(self.end_foot)

    def get_line(self) -> LineString:
        """
        获取线段
        """
        return LineString([self.start_pt, self.end_pt])


@dataclasses.dataclass
class FaceDistanceBud:
    """
    面和建筑物的距离
    """
    face: StrategyFace
    buds: list
    road: str
    projected_links: List[ProjectLink]
    has_storefront_road: bool = False
    has_competitor: bool = False

    def __post_init__(self):
        # _print_projected_links(self.projected_links, "映射的")
        self.close_projected_links = self._get_close_proj_links()
        # _print_projected_links(self.close_projected_links, "映射的过近的")

        self.base_len = self._get_base_len()
        self.close_proj_len = self._get_close_proj_len()
        self.close_rate = self._calc_close_rate()
        self.is_close = self._is_close()

    def _get_base_len(self) -> float:
        return self.face.length

    def _get_close_proj_links(self) -> List[ProjectLink]:
        min_dist = MIN_DISTANCE
        close = []
        for link in self.projected_links:
            if link.average_dist < min_dist:
                close.append(link)
        return close

    def _get_close_proj_len(self) -> float:
        proj_len = 0
        for link in self.close_projected_links:
            proj_len += link.get_length()
        return proj_len

    def _calc_close_rate(self) -> float:
        return self.close_proj_len / self.base_len

    def _is_close(self) -> bool:
        if self.has_storefront_road:
            return False
        if self.has_competitor:
            return False
        return self.close_rate > MIN_RATE


@dataclasses.dataclass
class FaceDistResp:
    """
    面距离结果
    """
    faces: List[FaceDistanceBud]

    def __post_init__(self):
        self.is_close = self._is_close()
        self.has_storefront_road = self._has_storefront_road()
        self.has_competitor = self._has_competitor()
        self.rates = [f.close_rate for f in self.faces]

    def _is_close(self) -> bool:
        for _f in self.faces:
            if not _f.is_close:
                # 只要有一部分不满足，都不算
                return False
        return True

    def _has_storefront_road(self) -> bool:
        for _f in self.faces:
            if _f.has_storefront_road:
                return True
        return False

    def _has_competitor(self) -> bool:
        for _f in self.faces:
            if _f.has_competitor:
                return True
        return False


def split_l_face(face: StrategyFace) -> List[StrategyFace]:
    """
    面拆分，拆分拐角 L 的面，拆分为两个直面
    """
    faces = [face]
    while True:
        new_faces = []
        for f in faces:
            new_faces += _split_l_face(f)
        if len(new_faces) == len(faces):
            break
        faces = new_faces
    return faces


def _split_l_face(face: StrategyFace) -> List[StrategyFace]:
    boundary = face.geo.boundary.simplify(5 * METER)
    coords = np.array(boundary.coords[:-1])  # 去除封闭的最后一个点（和第一个重复）
    for _i, i_cd in enumerate(coords):
        if _i == 0:  # 第一个角度
            bf = coords[-1]
        else:
            bf = coords[_i - 1]
        if _i == len(coords) - 1:  # 最后一个角
            af = coords[0]
        else:
            af = coords[_i + 1]
        angle = calculate_angle(bf, coords[_i], af)
        if angle < 60 or angle > 120:
            continue

        _pt = Point(i_cd)
        _buffer = _pt.buffer(15 * METER)
        split_res = face.geo.difference(_buffer)
        split_polygons = flat_polygon(split_res)
        if len(split_polygons) == 1:
            continue

        split_polygons_by_line = []
        for _j, j_cd in enumerate(coords):
            if _i == _j:
                continue
            j_pt = Point(j_cd)
            if not _buffer.intersects(j_pt):
                continue
            line = LineString([_pt, j_pt])
            if line.length == 0:
                continue
            split_line = extend_linestring(line, 50 * METER)
            split_polygons_by_line = flat_polygon(shapely.ops.split(face.geo, split_line))
            if len(split_polygons_by_line) > 1:
                break
        if len(split_polygons_by_line) > 1:
            split_polygons = split_polygons_by_line

        _faces = []
        for _idx, _polygon in enumerate(split_polygons):
            _face = StrategyFace(
                sid=f"{face.sid}_{_idx}",
                wkt=_polygon.wkt,
                face_id=f"{face.face_id}_{_idx}",
                prev_face_ids=face.prev_face_ids,
                task_id=face.task_id,
                baseline=face.baseline,
            )
            if _face.length < 20 * METER:
                continue
            _faces.append(_face)
        if len(_faces) != len(split_polygons):
            continue
        return _faces
    return [face]


def filter_close_with_bud(ctx: Context, face: StrategyFace) -> FaceDistResp:
    """
    过滤和建筑物过近的面
    """
    buffer_size = 30 * METER
    buffer_wkt = face.geo.buffer(buffer_size).wkt
    street_wkt = _get_street_wkt(ctx, face.task_id)
    buds = _get_buds(ctx, buffer_wkt, street_wkt)
    road = _get_road(ctx, buffer_wkt)

    # has_exp_traj = _has_exp_traj(face.wkt, road, street_wkt)
    # print(has_exp_traj)
    # exit()

    has_competitor = _has_competitor(ctx, face.wkt)
    has_storefront_road = _has_storefront_road(ctx, face, buffer_wkt, street_wkt)
    projected_links = _get_bud_projected_roads(buds, road)
    projected_links = _filter_dup_project_links(projected_links)

    faces = []
    for f in split_l_face(face):
        faces.append(
            FaceDistanceBud(
                face=f,
                buds=buds,
                road=road,
                projected_links=_filter_by_face(f, projected_links),
                has_storefront_road=has_storefront_road,
                has_competitor=has_competitor,
            )
        )
    return FaceDistResp(faces=faces)


def _get_street_wkt(ctx: Context, task_id: int) -> str:
    """
    获取街区范围
    """
    qry = f"""
    select st_astext(geom) from {ctx.task_tab} where task_id = {task_id}
    """
    res = ctx.poi_db.fetchone(qry)
    return res[0]


def _has_competitor(ctx: Context, wkt: str) -> bool:
    """
    有竞品返回 True
    """
    qry = f"""
    select count(*) from competitor_park 
    where st_intersects(geom, st_geomfromtext('{wkt}', 4326)) 
    and poi_tag = '交通设施;停车场' and space_attr in ('地上停车场', '') 
    """
    res = ctx.poi_db.fetchone(qry)
    return res[0] > 0


def _get_trajs(wkt: str):
    """
    获取经验轨迹
    """
    pois = auto_cpt.get_relation_store_pois(wkt, 50 * METER)
    trajs = []
    for p in pois:
        trajs += get_trajectory_data_by_bid(p.bid)
    return trajs


def _moved_wkt(wkt: str, road_wkt: str, street_wkt: str):
    road_geo = shapely.wkt.loads(road_wkt)
    geo = shapely.wkt.loads(wkt)
    if not geo.intersects(road_geo):
        return wkt
    res = buffer_depend_on_street(geo, shapely.wkt.loads(street_wkt), 30 * METER)
    return res.wkt


def _has_exp_traj(wkt: str, road_wkt: str, street_wkt: str) -> bool:
    """
    有经验轨迹
    """
    trajs = _get_trajs(wkt)
    if len(trajs) == 0:
        return False
    print(len(trajs))
    print(trajs)
    exit()
    wkt = _moved_wkt(wkt, road_wkt, street_wkt)
    geo = shapely.wkt.loads(wkt)
    for t in trajs:
        t_geo = shapely.wkt.loads(t['wkt'])
        if geo.intersects(t_geo):
            return True
    return False


def _has_storefront_road(ctx: Context, face: StrategyFace, buffer_wkt: str, street_wkt: str) -> bool:
    """
    是否有门前路，有返回 True
    """
    qry = f"""
    select link_id, st_astext(geom) from nav_link 
    where st_intersects(geom, st_geomfromtext('{buffer_wkt}', 4326)) 
    and st_intersects(geom, st_geomfromtext('{street_wkt}', 4326)) 
    and '82' = ANY (string_to_array(form, ','))
    """
    links = ctx.road_db.fetchall(qry)
    if len(links) == 0:
        return False

    street_geo = shapely.wkt.loads(street_wkt)
    street_boundary = flat_line(street_geo.boundary)[0]

    # 可能是 buffer 导致的，再投影到面上看看
    for _link in links:
        link_geo = shapely.wkt.loads(_link[1])
        for _coord in list(link_geo.coords):
            _pt = Point(_coord)
            _ft = get_foot_point(_pt, street_boundary)
            if _ft is None:
                continue
            line = LineString([_pt, _ft])
            if line.length == 0:
                continue
            _per = extend_linestring(line, 50 * METER)
            if _per.intersects(face.geo):
                return True
    return False


def _get_buds(ctx: Context, wkt: str, street_wkt) -> list:
    """
    获取建筑物
    """
    qry = f"""
    select face_id, st_astext(geom) from bud_face 
    where st_intersects(geom, st_geomfromtext('{wkt}', 4326)) 
    and st_intersects(geom, st_geomfromtext('{street_wkt}', 4326)) 
    """
    res = ctx.back_db.fetchall(qry)
    return [{
        'face_id': item[0],
        'geom': item[1],
    } for item in res]


def _get_road(ctx: Context, wkt: str):
    return get_complete_ld_polygon_2d(ctx, wkt)


def _get_bud_projected_roads(buds: list, road_wkt: str) -> List[ProjectLink]:
    road_geo = shapely.wkt.loads(road_wkt)
    road_shell_geos = flat_line(road_geo.boundary)
    if len(road_shell_geos) == 0:
        return []
    # road_shell_geo = flat_line(road_geo.boundary)[0]
    projected_links = []
    # print(road_wkt)
    for _bud in buds:
        # print(_bud)
        polygon = Polygon(
            face_id=_bud['face_id'],
            geom=_bud['geom'],
        )
        coords = polygon.get_shell_add_points()
        for idx, cd in enumerate(coords):
            if idx == len(coords) - 1:
                continue
            start_pt = Point(cd)
            end_pt = Point(coords[idx + 1])

            road_shell_geo = _get_close_road_shell(start_pt, road_shell_geos)

            start_foot = get_foot_point(start_pt, road_shell_geo)
            end_foot = get_foot_point(end_pt, road_shell_geo)
            if start_foot is None or end_foot is None:
                continue
            link = ProjectLink(face_id=_bud['face_id'], start_pt=start_pt, start_foot=start_foot,
                               start_dist=start_pt.distance(start_foot), end_pt=end_pt, end_foot=end_foot,
                               end_dist=end_pt.distance(end_foot))
            # _wkt = 'LINESTRING (110.1698144 22.63236, 110.1698016 22.6323856)'
            # _geo = shapely.wkt.loads(_wkt)
            # if _geo.intersects(link.get_line()):
            #     print(link.get_line())
            #     print(link.get_length() / METER)
            if link.get_length() / METER < 0.5:
                continue
            projected_links.append(link)
    # _print_projected_links(projected_links, '映射好')
    return projected_links


def _get_close_road_shell(point: Point, shells: list) -> LineString:
    """
    获取最近的一个道路边界
    """
    with_dist = []
    for s in shells:
        with_dist.append({
            'shell': s,
            'dist': point.distance(s),
        })
    with_dist.sort(key=lambda x: x['dist'])
    return with_dist[0]['shell']


def _print_projected_links(links: List[ProjectLink], desc: str):
    print(f"开始打印：{desc}")
    for _l in links:
        print(_l.get_line())
    print(f"结束打印：{desc}")


def _filter_dup_project_links(projected_links: List[ProjectLink]) -> List[ProjectLink]:
    """
    过滤掉重复的映射 link
    """
    projected_links.sort(key=lambda x: x.average_dist)
    projected_polygons = []
    resp = []
    for link in projected_links:
        # covered_polygon = link.get_polygon().buffer(-0.01)  # 避免点接触导致的
        covered_polygon = link.get_polygon().buffer(-0.001 * METER)  # 避免点接触导致的
        has_intersected = False
        for _projected in projected_polygons:
            if _projected.intersects(covered_polygon):
                has_intersected = True
                break
        if has_intersected:
            continue
        projected_polygons.append(covered_polygon)
        resp.append(link)
    return resp


def _filter_by_face(face: StrategyFace, projected_links: List[ProjectLink]) -> List[ProjectLink]:
    """
    垂线和面没有交集，需要过滤掉
    """
    resp = []
    for link in projected_links:
        start_per = LineString([link.start_pt, link.start_foot])
        end_per = LineString([link.end_pt, link.end_foot])

        start_per = extend_linestring_one_side(start_per, 50 * METER, which='end')
        end_per = extend_linestring_one_side(end_per, 50 * METER, which='end')
        if start_per.intersects(face.geo) or end_per.intersects(face.geo):
            resp.append(link)
    return resp


def extend_linestring_one_side(line: LineString, dist: float, which: str = "end") -> LineString:
    """
    延长 LineString 的一端（起点或终点）
    :param line: 原始 LineString
    :param dist: 要延长的距离
    :param which: "start" 或 "end"，表示要延长哪一端
    :return: 延长后的 LineString
    """

    def extend_point(p0: Point, p1: Point) -> Point:
        vec = np.array([p1.x - p0.x, p1.y - p0.y])
        vec = vec / np.linalg.norm(vec)
        vec = vec * dist
        return Point(p1.x + vec[0], p1.y + vec[1])

    coords = [Point(x, y) for x, y in line.coords]

    if which == "start":
        extended = [extend_point(coords[1], coords[0])] + coords
    elif which == "end":
        extended = coords + [extend_point(coords[-2], coords[-1])]
    else:
        raise ValueError("which 参数必须为 'start' 或 'end'")

    return LineString(extended)
