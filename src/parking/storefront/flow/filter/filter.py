"""
过滤掉无效的核实面
为了减量，减少重复作业，提高作业效率

相关case：
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/aO7UyUYGkm/9c1952474e4547
规则明细：
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/aO7UyUYGkm/b9500894402a41
"""
import csv
import os
from typing import List, Union

import shapely.wkt

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.merger.model import StrategyFace
from src.parking.storefront.flow.filter.model import DupStrategyFace, CoveredPark, AoiPark
from src.parking.storefront.diff.polygon_differ import diff_center, Polygon
from src.parking.storefront.flow.merger.merger import get_face_belong_to_aoi
from src.tools import utils

METER = 1e-5


def filter_dup_strategy_face(ctx: Context, face: StrategyFace) -> DupStrategyFace:
    """
    过滤掉重复的策略面
    """
    covered_parks = _get_covered_common_parks(ctx, face)
    reason1 = f"压盖的停车场数：{len(covered_parks)}; {[p.bid for p in covered_parks]}"
    print(f"{face.face_id}; {reason1}")

    aoi_parks = _filter_parks(ctx, covered_parks)
    reason2 = f"压盖的 aoi 数：{len(aoi_parks)}; {[a.aoi_id for a in aoi_parks]}"
    print(f"{face.face_id}; {reason2}")

    dup_face = DupStrategyFace(face=face, reason='||' . join([reason1, reason2]))

    for _park in aoi_parks:
        is_dup, detail = _is_dup(ctx, face, _park)
        if is_dup:
            dup_face.detail = detail
            break
    return dup_face


def _get_covered_common_parks(ctx: Context, face: StrategyFace) -> List[CoveredPark]:
    """
    获取压盖的常规停车场
    核实面和有父点的精准地上停车场面存在压盖（空间关系+投影）
    1、duplicate_intersects_iou>0.1 （空间关系）
    2、(duplicate_link_iou>0.5) or (duplicate_link_ioa>0.5) or ( duplicate_link_iob＞0.5)，投影压盖距离限制
    """
    a_polygon = Polygon(
        face_id=face.face_id,
        geom=face.wkt,
    )

    buffer_size = 40 * METER

    buffer_wkt = a_polygon.geo.buffer(buffer_size).wkt

    def get_polygons_by_wkt_fn(wkt: str):
        qry = f"""
        select bid, st_astext(area), parent_id 
        from parking 
        where 
            status = 1 and precise = 1 
            and parent_id not in ('', '0') 
            and show_tag in ('地上停车场') 
            and st_intersects(area, st_geomfromtext('{buffer_wkt}', 4326))
        """
        res = ctx.back_db.fetchall(qry)
        return [
            Polygon(
                face_id=item[0],
                geom=item[1],
                extra={
                    'parent_id': item[2],
                }
            )
            for item in res
        ]

    diff_res = diff_center([a_polygon], buffer_size, get_polygons_by_wkt_fn, False)
    assert len(diff_res) == 1, f"diff 结果数量不符合预期; strategy_face_id: {face.face_id}"
    response = diff_res[0]

    if response.similarity is None:
        # 新增的
        return []

    # 相似度
    similarity = response.similarity.get_detail()
    link_similarity, geom_similarity = similarity["link"], similarity["geom"]

    # 交并比
    link_iou, link_ioa, link_iob = (
        link_similarity["iou"],
        link_similarity["ioa"],
        link_similarity["iob"],
    )
    geom_iou = geom_similarity["iou"]
    parks = [
        CoveredPark(
            bid=b_char.polygon.face_id,
            wkt=b_char.polygon.geom,
            parent_id=b_char.polygon.extra['parent_id'],
            reason={
                'geom_iou': geom_iou,
                'link_iou': link_iou,
                'link_ioa': link_ioa,
                'link_iob': link_iob,
            }
        )
        for b_char in response.b_characteristics_set
    ]
    print(f"geom_ioi:{geom_iou}; link_iou:{link_iou};link_ioa:{link_ioa};link_iob:{link_iob}")
    # if geom_iou > 0.1 and ((link_iou > 0.5) or (link_ioa > 0.5) or (link_iob > 0.5)):
    if (link_iou > 0.5) or (link_ioa > 0.5) or (link_iob > 0.5):
        return parks
    return []


def _filter_parks(ctx: Context, covered_parks: List[CoveredPark]) -> List[AoiPark]:
    """
    过滤停车场
    """
    show_tag2std_tags = _get_not_build_show_tag2std_tags()
    parks = []
    for _park in covered_parks:
        aoi_park = _get_aoi_park(ctx, _park)
        if aoi_park is None:
            continue
        aoi = aoi_park.get_aoi()
        if not aoi_park.park.geo.intersects(aoi.geo):
            print(f"aoi_id:{aoi.face_id} 和 park_bid: {_park.bid} 没有交集")
            continue
        if _is_special_cate(aoi_park, show_tag2std_tags):
            parks.append(aoi_park)
    return parks


def _get_not_build_show_tag2std_tags() -> dict:
    """
    获取不用建设的 tag 信息
    """
    show_tag2std_tags = {}

    current_dir = os.path.dirname(os.path.abspath(__file__))
    conf_file = os.path.join(current_dir, 'not_build.tsv')
    with open(conf_file, 'r') as hdr:
        reader = csv.reader(hdr, delimiter='\t')
        next(reader)
        for row in reader:
            show_tag, std_tag = row
            show_tag2std_tags.setdefault(show_tag, []).append(std_tag)
    return show_tag2std_tags


def _get_covered_aoi(ctx: Context, wkt: str) -> tuple:
    """
    获取压盖的 aoi
    """
    qry = f"""
    select 
        face_id, st_astext(geom), 
        st_area(st_intersection(st_geomfromtext('{wkt}', 4326), geom)) as area
    from blu_face 
    where st_intersects(st_geomfromtext('{wkt}', 4326), geom) order by area desc
    """
    res = ctx.back_db.fetchone(qry)
    if len(res) == 0:
        return res

    ioa = utils.calc_ioa(res[1], wkt)
    if ioa > 0.5:
        return res
    return ()


def _get_aoi_park(ctx: Context, park: CoveredPark) -> Union[AoiPark, None]:
    """
    获取 aoi 的停车场
    """
    poi_qry = f"""
    select bid, show_tag, std_tag from poi 
    where bid = '{park.parent_id}'
    """
    poi_res = ctx.poi_db.fetchone(poi_qry)
    if len(poi_res) == 0:
        return None

    aoi_qry = f"""
    select b.face_id, st_astext(geom) 
    from blu_face_poi a left join blu_face b on a.face_id = b.face_id 
    where a.poi_bid = '{park.parent_id}'
    """
    aoi_res = ctx.back_db.fetchone(aoi_qry)
    if len(aoi_res) == 0:
        # 用压盖兜底
        aoi_res = _get_covered_aoi(ctx, park.wkt)

    if len(aoi_res) == 0:
        return None

    return AoiPark(
        aoi_id=aoi_res[0],
        wkt=shapely.wkt.loads(aoi_res[1]).buffer(5 * METER).wkt,
        std_tag=poi_res[2],
        show_tag=poi_res[1],
        park=park,
    )


def _is_special_cate(aoi_park: AoiPark, show_tag2std_tags: dict) -> bool:
    """
    是否是特殊垂类，特殊垂类不用建设，返回 True
    """
    if aoi_park.show_tag in show_tag2std_tags and aoi_park.std_tag in show_tag2std_tags[aoi_park.show_tag]:
        print(f"show_tag:{aoi_park.show_tag}, std_tag:{aoi_park.std_tag} 是特殊垂类")
        return True
    print(f"show_tag:{aoi_park.show_tag}, std_tag:{aoi_park.std_tag} 不是特殊垂类")
    return False


def _is_dup(ctx: Context, face: StrategyFace, park: AoiPark) -> tuple:
    """
    策略面和常规停车场是否重复
    重复返回 True，重复原因
    """
    aoi = get_face_belong_to_aoi(ctx, face, [park.get_aoi()])
    if aoi is None:
        return False, {}
    reason = park.park.reason
    reason['aoi'] = aoi.face_id
    reason['online_bid'] = park.park.bid
    return True, reason


def _print_covered_parks(parks: List[CoveredPark], desc: str):
    print(f"{desc}; 压盖的停车场数量：{len(parks)}")
    for _park in parks:
        print(f"{desc}; 压盖的停车场bid：{_park.bid}")


def _print_aoi_parks(aoi_parks: List[AoiPark], desc: str):
    print(f"{desc}; 压盖的停车有 aoi 的数量：{len(aoi_parks)}")
    for _park in aoi_parks:
        print(f"{desc}; aoi_id: {_park.aoi_id}")
