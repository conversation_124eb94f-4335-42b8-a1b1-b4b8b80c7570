"""
相关模型定义
"""
import dataclasses

import shapely.wkt

from src.parking.storefront.flow.merger.model import StrategyFace, Aoi


@dataclasses.dataclass
class DupStrategyFace:
    """
    重复的策略面
    """
    face: StrategyFace
    reason: str
    detail: dict = None


# @dataclasses.dataclass
# class Aoi:
#     """
#     aoi
#     """
#     face_id: str
#     bid: str
#     wkt: str
#     std_tag: str
#     show_tag: str


@dataclasses.dataclass
class CoveredPark:
    """
    压盖的停车场
    """
    bid: str
    wkt: str
    parent_id: str
    reason: dict

    def __post_init__(self):
        self.geo = shapely.wkt.loads(self.wkt).buffer(0)
        self.wkt = self.geo.wkt


@dataclasses.dataclass
class AoiPark:
    """
    aoi 的停车场
    """
    aoi_id: str
    wkt: str
    std_tag: str
    show_tag: str
    park: CoveredPark

    def get_aoi(self) -> Aoi:
        """
        获取关联的 aoi
        """
        return Aoi(
            face_id=self.aoi_id,
            wkt=self.wkt,
        )
