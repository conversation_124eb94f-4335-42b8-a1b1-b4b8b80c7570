"""
空间关系上存在压盖
"""
import dataclasses
import json
from datetime import datetime
from typing import List, Union, Tuple

import shapely.wkt

from src.parking.storefront.diff.polygon_differ import diff_center, Polygon as DfPolygon, DiffResponse, ProjectedLink
from src.parking.storefront.utils.geometric import flat_line
from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.db import join_str
from src.parking.storefront.flow.model import ach

METER = 1e-5


@dataclasses.dataclass
class Access:
    """
    出入口
    """
    uid: str
    geom: str

    def __post_init__(self):
        self.geo = shapely.wkt.loads(self.geom)

    def maybe_same(self, b: 'Access', distance: float) -> bool:
        """
        如果是同一个出入口，返回 True
        """
        if self.geo.distance(b.geo) < distance:
            return True
        return False

    @staticmethod
    def by_ach(access: ach.AccessAch) -> 'Access':
        """
        根据成果转换
        """
        return Access(
            uid=access.access_id,
            geom=access.geom,
        )


@dataclasses.dataclass
class Park:
    """
    待处理的停车场
    """
    uid: str
    geom: str
    created_at: str
    status: int
    accesses: List[Access] = None
    park_ach: ach.ParkingAch = None

    def __post_init__(self):
        if isinstance(self.created_at, datetime):
            self.created_at_obj = self.created_at
        else:
            self.created_at_obj = datetime.strptime(self.created_at, "%Y-%m-%d %H:%M:%S")

    def to_diff_polygon(self) -> DfPolygon:
        """
        转换待 diff 的对象
        """
        return DfPolygon(
            face_id=self.uid,
            geom=self.geom,
        )

    def access_are_same(self, accesses: List[Access]) -> bool:
        """
        出入口都一样, 返回 True
        """
        return _two_group_accesses_are_equals(self.accesses, accesses, 10 * METER)

    def is_offline(self) -> bool:
        """
        已经下线
        """
        return self.status == 2

    @staticmethod
    def by_ach(park: ach.ParkingAch) -> 'Park':
        """
        根据成果转换
        """
        return Park(
            uid=park.bid,
            geom=park.geom,
            status=2 if 'OFFLINE' in park.status else 1,
            created_at=park.created_at,
        )


@dataclasses.dataclass
class ChoicePark:
    """
    选择的停车场
    """
    park: Park
    reason: str


def _two_group_accesses_are_equals(accesses1: List[Access], accesses2: List[Access], tolerance: float) -> bool:
    """
    两组口是否一样
    """
    len1 = len(accesses1) if accesses1 else 0
    len2 = len(accesses2) if accesses2 else 0
    if len1 != len2:
        return False
    if len1 == 0:
        return True
    for a1 in accesses1:
        maybe_same = False
        for a2 in accesses2:
            if a1.maybe_same(a2, tolerance):
                maybe_same = True
                break
        if not maybe_same:
            return False
    return True


def can_auto_choice_online_park(ctx: Context, a_bid: str, b_bid: str) -> tuple:
    """
    能否自动选择线上停车场
    """

    def _to_overlap_park(data: tuple) -> Park:
        return Park(
            uid=data[0],
            geom=data[1],
            created_at=str(data[2]),
            status=data[3],
            accesses=_get_accesses(data[0]),
        )

    def _get_accesses(bid: str) -> Union[List[Access], None]:
        _qry = f"""
        select bid, st_astext(gcj_geom) from parking 
        where parent_id = '{bid}' and status = 1 and std_tag in ('出入口;停车场出入口')
        """
        _res = ctx.back_db.fetchall(_qry)
        return [
            Access(
                uid=_r[0],
                geom=_r[1],
            ) for _r in _res
        ]

    qry = f"""
            select bid, st_astext(area), create_time, status from parking 
            where bid in ({join_str([a_bid, b_bid])})
            """
    parks = ctx.back_db.fetchall(qry)
    return can_auto_choice(
        _to_overlap_park(parks[0]),
        _to_overlap_park(parks[1]),
    )


def _diff_too_long(diffed: List[str], limit_long: float = 30 * METER) -> bool:
    """
    差异太明显，返回 True
    """
    for d in diffed:
        for dl in flat_line(d):
            if dl.length > limit_long:
                return True
    return False


def can_auto_choice(a_park: Park, b_park: Park) -> tuple:
    """
    压盖的停车场，能否自动选择，能返回 True, 面差异，口差异
    看相似度，如果高相似度，那么可以自动选择
    """
    if a_park.is_offline() or b_park.is_offline():
        return True, {}, True

    if not a_park.access_are_same(b_park.accesses):
        return False, {}, False

    a_diffed, b_diffed = _calc_diff(a_park, b_park)

    detail = {
        'a_diffed': a_diffed,
        'b_diffed': b_diffed,
    }
    if _diff_too_long(a_diffed) or _diff_too_long(b_diffed):
        return False, detail, True
    return True, detail, True


def _calc_diff(a_park: Park, b_park: Park) -> tuple:
    a_polygon = a_park.to_diff_polygon()

    def get_polygons_by_wkt_fn(_: str) -> List[DfPolygon]:
        return [b_park.to_diff_polygon()]

    resp = diff_center([a_polygon], 30 * METER, get_polygons_by_wkt_fn=get_polygons_by_wkt_fn, filter=False)
    assert len(resp) == 1, f"{a_park.uid} diff_center 结果不符合预期"

    diff_res: DiffResponse = resp[0]
    link_similarity = diff_res.similarity.link_similarity
    a_diffed = link_similarity.a_diffed
    b_diffed = link_similarity.b_diffed

    return a_diffed, b_diffed


def choice_by_created_at(a_park: Park, b_park: Park) -> Tuple[Park, Park]:
    """
    返回上线的，下线的
    """


def choice_park_ach(a_park: Park, b_park: Park) -> tuple:
    """
    能否自动选择成果
    """
    if a_park is None or b_park is None:
        return None, "存在不是成果的数据"

    parks = [a_park, b_park]
    online_parks = [_park for _park in parks if not _park.is_offline()]
    if len(online_parks) == 0:
        return None, "没有线上的停车场"
    if len(online_parks) == 1:
        reason = '只有一个上线的停车场'
        return online_parks[0], reason

    have_access_parks = [_park for _park in parks if len(_park.accesses) > 0]
    if len(have_access_parks) == 1:  # 只有一个停车场有口，那么就选这个停车场
        reason = '只有一个停车场有口'
        return have_access_parks[0], reason

    a_diffed, b_diffed = _calc_diff(a_park, b_park)
    detail = {
        'a_diffed': a_diffed,
        'b_diffed': b_diffed,
    }
    if _diff_too_long(a_diffed) or _diff_too_long(b_diffed):
        return None, json.dumps(detail)
    online_park_aches = [_park for _park in parks if _park.park_ach.status == 'ONLINE']
    if len(online_park_aches) == 1:
        return online_park_aches[0], "有一个已经上线了"
    reason = '相似度一样，选择新的'
    if a_park.created_at > b_park.created_at:
        return a_park, reason
    return b_park, reason


def auto_choice_park_ach(ctx: Context, a_bid: str, b_bid: str) -> tuple:
    """
    能否自动选择线上停车场
    """

    def _gen_park(bid: str) -> Union[Park, None]:
        park_ach = ach.get_parking_ach_by_bid(ctx, bid)
        if park_ach is None:
            return None
        accesses = ach.get_park_accesses_by_bid(ctx, bid)
        park = Park.by_ach(park_ach)
        park.accesses = [Access.by_ach(a) for a in accesses]
        park.park_ach = park_ach
        return park

    return choice_park_ach(_gen_park(a_bid), _gen_park(b_bid))



