# coding=utf-8
"""
数据生成器
"""


def get_limit_where(uq_key, min_uq, max_uq):
    """获取限制条件"""
    if isinstance(min_uq, int):
        limit_str = f" {uq_key} > {min_uq} "
        if max_uq != '':
            limit_str = f" {limit_str} and {uq_key} <= {max_uq}"
    else:
        limit_str = f" {uq_key} > '{min_uq}' "
        if max_uq != '':
            limit_str = f" {limit_str} and {uq_key} <= '{max_uq}'"
    return limit_str


class DBGenerator:
    """
    Db 数据生成器
    """
    __uq_key = ''
    __fields = []
    __table = ''
    __where = ''

    __cursor = None
    __sql = ''

    def __init__(self, table, fields, uq_key, where=''):
        """
        Args:
            table: 表名，数据来源的表
            fields: 需要生成器中包含的字段
            uq_key: 唯一值 key，一定要有索引，避免慢查询
            where: 数据过滤条件
        """
        # 若唯一 key 不在查询字段中，那么会被自动加入
        alias_keys = []
        for _field in fields:
            alias_keys.append(alias(_field))
        if uq_key not in alias_keys:
            fields.append(uq_key)

        self.__table = table
        self.__uq_key = uq_key
        self.__fields = fields
        self.__where = where

    def generate(self, cursor, num, min_uq='', max_uq=''):
        """
        Args:
            cursor: db 链接的 cursor
            num: 生成器中一次包含的数据数量
            :param num:
            :param cursor:
            :param min_uq: 偏移量
            :param max_uq: (min_uq, max_uq]   左开右闭区间
        """
        self.__cursor = cursor

        min_uq = min_uq
        while True:
            res_list = self.__get_list(num, min_uq, max_uq)
            yield res_list

            if len(res_list) != num:
                break

            min_uq = res_list[len(res_list) - 1][self.__uq_key]

    def __get_list(self, num, min_uq, max_uq):
        """
        根据 max_uq 获取 num 个数据
        Args:
            min_uq: 偏移量
            max_uq:
            num: 数量
        """
        limit_str = get_limit_where(self.__uq_key, min_uq, max_uq)

        self.__sql = f"select {', '.join(self.__fields)} from {self.__table} where {limit_str} {self.__where} " \
                     f"order by {self.__uq_key} asc limit {num}"
        print(self.__sql)

        self.__cursor.execute(self.__sql)
        res = self.__cursor.fetchall()

        if res is None or 0 == len(res):
            return []

        a_list = []
        for item in res:
            val = {}
            for i in range(0, len(self.__fields)):
                fields = str(self.__fields[i]).split(' ')
                fields = list(filter(lambda x: x != ' ', fields))
                a_field = fields[len(fields) - 1]
                if a_field in item:
                    val[a_field] = item[a_field]
                else:
                    val[a_field] = item[i]
            a_list.append(val)
        return a_list

    def get_last_sql(self):
        """
        获取查询的最后一条 sql
        """
        return self.__sql


class FileGenerator:
    """
    文件生成器
    """

    __data_file = ''

    def __init__(self, data_file):
        self.__data_file = data_file

    def generate(self, num, fun=None):
        """生成"""
        lines = []
        with open(self.__data_file, 'r') as handler:
            while True:
                row = handler.readline()
                if not row:
                    break

                row = row.strip('\n')
                if fun is not None:
                    row = fun(row)

                lines.append(row)
                if len(lines) >= num:
                    yield lines
                    lines = []

        if len(lines) > 0:
            yield lines


class DBUQGenerator:
    """
    数据段生成
    """
    def __init__(self, tab, uq_key, where=''):
        self.tab = tab
        self.uq_key = uq_key
        self.uq_key_alias = alias(uq_key)
        self.where = where

        self.curs = None
        self.min_uq = ''
        self.max_uq = ''

    def generate(self, curs, num, min_uq='', max_uq=''):
        """数据段生成"""
        self.curs = curs
        self.min_uq = min_uq
        self.max_uq = max_uq

        uqs = self._get_uqs(num, min_uq, max_uq)
        return self._split_uqs(uqs)

    def _get_uqs(self, num, min_uq, max_uq):
        uqs = []
        while True:
            end_uq = self._get_uq(num, min_uq, max_uq)
            uqs.append(end_uq)
            min_uq = end_uq

            if end_uq == max_uq or end_uq == '':
                break
        return uqs

    def _get_uq(self, num, min_uq, max_uq):
        limit_str = get_limit_where(self.uq_key_alias, min_uq, max_uq)

        sql = f"select {self.uq_key} from {self.tab} where {limit_str} {self.where} order by {self.uq_key_alias} asc " \
              f"OFFSET {num-1} limit 1"
        print(sql)
        # exit()

        self.curs.execute(sql)
        res = self.curs.fetchone()

        if res is None or len(res) == 0:
            return ''
        return res[0]

    def _split_uqs(self, uqs):
        segments = []

        uqs.insert(0, self.min_uq)
        for idx, val in enumerate(uqs):
            segments.append({
                'start_id': val,
                'end_id': uqs[idx + 1]
            })
            if idx + 1 == len(uqs) - 1:
                break
        return segments


def alias(key):
    """
    别名
    """
    vals = [' as ', ' ', ' AS ']
    for _val in vals:
        if _val not in key:
            continue

        fields = str(key).split(_val)
        key = fields[-1]
        break
    return key

