"""
人工作业上下文
"""
import random
import subprocess

from src.parking.storefront.flow.context import Context as BaseContext, gen_ctx as gen_base_ctx


class Context(BaseContext):
    """
    上下文
    """
    env: str
    url: str
    callback_urls: str
    offline_url: str  # 下线 url

    @classmethod
    def from_base(cls, base: BaseContext) -> "Context":
        """根据原始上下文生成"""
        ctx = cls.__new__(cls)
        ctx.__dict__ = base.__dict__.copy()
        return ctx

    def get_callback_url(self) -> str:
        """
        获取回调 url
        """
        return random.choice(self.callback_urls)

    def is_online(self) -> bool:
        """
        线上环境返回 True
        """
        return self.env == 'online'


def gen_ctx(env: str, autocommit=False, is_committed: bool = True, debug: bool = False) -> Context:
    """
    生成上下文
    """
    print(f"环境是：{env}")

    ctx = Context.from_base(gen_base_ctx(is_committed=is_committed, debug=debug, autocommit=autocommit))

    if env == 'online':
        offline_url = 'http://*************:80/stargaze/api/removeIntelligence'
        url = 'http://*************:80/stargaze/api/addintelligence'
        callback_urls = []
        bns = 'group.opera-platformIntelligence-plartformIntelligence-all.map-de.all'
        for host in get_host_by_bns(bns):
            ip, port = host
            callback_urls.append(f"http://{ip}:{port}/stargaze/feedback/aoi_feedback_ack/")
    else:
        offline_url = 'http://istargaze.map.baidu.com/stargaze/api/removeIntelligence'
        url = 'http://*************:80/stargaze/api/addintelligence'
        callback_urls = [
            f'http://***********:8086/stargaze/feedback/aoi_feedback_ack/'
        ]
    ctx.offline_url = offline_url
    ctx.url = url
    ctx.callback_urls = callback_urls
    ctx.env = env

    return ctx


def gen_online_ctx(autocommit=False, is_committed: bool = True, debug: bool = False) -> Context:
    """
    获取线上环境上下文
    """
    return gen_ctx('online', is_committed=is_committed, debug=debug, autocommit=autocommit)


def gen_test_ctx(autocommit=False, is_committed: bool = True, debug: bool = False) -> Context:
    """
    获取测试环境上下文
    """
    return gen_ctx('test', is_committed=is_committed, debug=debug, autocommit=autocommit)


def gen_ctx_by_batch(batch: str, autocommit=False, is_committed: bool = True, debug: bool = False) -> Context:
    """
    根据批次号获取上下文
    """
    if 'test' in batch:
        return gen_test_ctx(is_committed=is_committed, debug=debug, autocommit=autocommit)
    return gen_online_ctx(is_committed=is_committed, debug=debug, autocommit=autocommit)


def get_host_by_bns(host_bns: str):
    """
    获取 bns ip
    """
    data = "", 0
    ret = subprocess.run(
        "get_instance_by_service -ips {}".format(host_bns) + " | awk '{print $2,$3,$4}'",
        shell=True,
        stdout=subprocess.PIPE
    )
    code = ret.returncode
    if code == 0:
        stdout = ret.stdout
        host_result = stdout.decode('UTF-8').split("\n")
        if len(host_result) < 1:
            yield data
        for i in host_result:
            item_list = i.split(" ")
            if len(item_list) < 3:
                continue
            if item_list[2] != '0':
                # 状态不是0，实例有问题
                continue
            yield item_list[0], int(item_list[1])
    else:
        print("获取bns失败{}, 状态码{}, 返回{}".format(host_bns, code, ret.stdout))
        yield data

