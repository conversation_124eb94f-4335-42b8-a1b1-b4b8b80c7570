"""
多图掘金核实回传处理
"""
from dataclasses import dataclass, field
from typing import Iterator

from tqdm import tqdm

from src.parking.storefront.flow.context import gen_ctx_manager as gen_db_manager
from src.parking.storefront.flow.manual import multi_juejin_pusher as multi_juejin
from src.parking.storefront.flow.manual.context import gen_ctx, Context


@dataclass
class ImageVerifiedRes:
    """
    图片核实结果
    """
    id: int
    batch_id: str
    image_id: str
    conclusion: str
    conclusion_txt: str
    status: str


@dataclass
class VerifiedFace:
    """
    核实面
    """
    sid: int
    results: list[ImageVerifiedRes] = field(default_factory=list)

    def status_expected(self) -> bool:
        """状态是否符合预期，所有状态一样返回(符合预期) True"""
        statuses = self.get_unique_statuses()
        if len(statuses) != 1:
            return False
        if statuses[0] != multi_juejin.VERIFY_STATUS_ING:
            return False
        return True

    def get_unique_statuses(self) -> list:
        """获取唯一的状态"""
        return list(set([r.status for r in self.results]))

    def conclusion_expected(self) -> bool:
        """结论是否符合预期，所有结论一致返回(符合预期) True"""
        conclusions = self.get_unique_conclusions()
        if len(conclusions) != 1:
            return False
        if conclusions[0] not in multi_juejin.CONCLUSION2FACE_STATUS:
            return False
        return True

    def get_unique_conclusions(self) -> list:
        """获取唯一的结论"""
        return list(set([r.conclusion for r in self.results]))

    def verified(self) -> bool:
        """有核实结论返回 True"""
        conclusions = self.get_unique_conclusions()
        return len(conclusions) == 1 and (None not in conclusions)

    def _get_face_status_by_conclusion(self) -> str:
        """根据结果获取面状态"""
        assert self.verified(), f"没有核实结论，不能获取到核实面的状态"

        uq_conclusion = self.get_unique_conclusions()[0]
        return multi_juejin.CONCLUSION2FACE_STATUS[uq_conclusion]

    def save(self) -> int:
        """保存核实结论"""
        with gen_db_manager(is_committed=True, debug=True) as db:
            queries = []
            for r in self.results:
                queries.append(f"""
                update park_storefront_strategy_multi_verify 
                set status = '{multi_juejin.VERIFY_STATUS_DONE}'
                , conclusion = '{r.conclusion}'
                , conclusion_txt = '{r.conclusion_txt}' 
                where id = {r.id} and status = '{r.status}'  
                """)
            queries.append(f"""
            update park_storefront_strategy 
            set status = '{self._get_face_status_by_conclusion()}' 
            where id = {self.sid} and status = '{multi_juejin.FACE_STATUS_ING}' 
            """)
            for qry in queries:
                num = db.poi_db.execute(qry)
                if num == 0:
                    print(f"{self.sid} 保存核实结论 失败")
                    db.poi_db.rollback()
                    return 0
            db.poi_db.commit_or_rollback()
        return 1


def _get_verified_face(ctx: Context, sid: int) -> VerifiedFace:
    qry = f"""
    select a.id, strategy_id, status, a.batch_id, a.image_id, b.conclusion, b.conclusion_txt 
    from park_storefront_strategy_multi_verify a left join park_storefront_verify b 
        on a.image_id = b.image_id and a.batch_id = b.batch_id 
    where strategy_id = {sid} 
    """
    res = ctx.poi_db.fetchall(qry)

    image_results: list[ImageVerifiedRes] = []
    for item in res:
        image_results.append(ImageVerifiedRes(
            id=item[0],
            status=item[2],
            batch_id=item[3],
            image_id=item[4],
            conclusion=item[5],
            conclusion_txt=item[6],
        ))
    return VerifiedFace(sid=sid, results=image_results)


def _get_verifying_faces(ctx: Context) -> Iterator[VerifiedFace]:
    """获取核实中的面"""
    qry = f"""
    select distinct strategy_id 
    from park_storefront_strategy_multi_verify 
    where status = '{multi_juejin.VERIFY_STATUS_ING}' 
    and batch_id not like 'test%'
    """
    ids = ctx.poi_db.get_values(qry)

    for sid in tqdm(ids, desc='获取核实中的面'):
        yield _get_verified_face(ctx, sid)


def callback(ctx: Context):
    """回传"""
    un_expected_num = 0
    verified_num = 0
    for verified_face in tqdm(_get_verifying_faces(ctx), desc='遍历多面核实'):
        verified_face: VerifiedFace

        if not verified_face.status_expected():
            un_expected_num += 1
            print(f"{verified_face.sid} 状态不符合预期；{verified_face.get_unique_statuses()}")
            continue

        if not verified_face.verified():
            print(f"{verified_face.sid} 还没有核实结果")
            continue

        if not verified_face.conclusion_expected():
            un_expected_num += 1
            print(f"{verified_face.sid} 结论不符合预期；{verified_face.get_unique_conclusions()}")
            continue

        verified_num += verified_face.save()
    print(f"核实了：{verified_num}; 不符合预期:{un_expected_num}; over")


def main():
    """主函数"""
    callback(gen_ctx(env='online', debug=True, autocommit=True))


if __name__ == '__main__':
    main()

