"""
多图核实处理中心
"""
import argparse
import json

from tqdm import tqdm

from src.parking.storefront.flow.manual.context import gen_ctx, Context
from src.parking.storefront.approval import icafe
from src.tools import ruliu
from src.parking.storefront.flow.manual import multi_juejin_pusher as multi_juejin


def _get_cards_by_card_status(status: str) -> list[icafe.ICafeCard]:
    """根据卡片状态 获取卡片"""
    return icafe.fetch_issues(icafe.CARD_TYPE_PULL, [status])


def subscribe_card_status_if_multi_verify_ready(ctx: Context):
    """订阅卡片 投多图核实"""
    cards = _get_cards_by_card_status(icafe.CARD_STATUS_PULL_MULTI_VERIFY_READY)
    ok_num = 0
    for _card in tqdm(cards, desc="订阅卡片 投多图核实"):
        ok = multi_juejin.push_batch(_card.batch, ctx.env)
        if not ok:
            _send_hi_by_status(_card, icafe.CARD_STATUS_PULL_MULTI_VERIFY_READY, msg='投放失败')
            continue
        ok_num += 1
        detail = get_batch_multi_verify_detail(ctx, _card.batch)
        detail_str = json.dumps(detail, ensure_ascii=False)
        _modify_card_status_and_notify(_card, icafe.CARD_STATUS_PULL_MULTI_VERIFY_DONE, detail_str)
    print(f"订阅卡片 投多图核实, 卡片数：{len(cards)}；处理成功数：{ok_num}")


def subscribe_card_status_if_multi_verify_done(ctx: Context):
    """订阅卡片 已投多图核实"""
    cards = _get_cards_by_card_status(icafe.CARD_STATUS_PULL_MULTI_VERIFY_DONE)
    md_ok = 0
    for _card in cards:
        detail = get_batch_multi_verify_detail(ctx, _card.batch)
        if len(detail) == 0:
            print(f"{_card.batch} 没有记录")
            continue
        if detail.get('verifying', 0) > 0:
            print(f"{_card.batch} 还没有核实完")
            continue
        total = sum(list(detail.values()))
        if total == detail.get('not_need_multi_verify'):
            print(f"{_card.batch} 没有多图核实记录")
            continue

        md_ok += 1
        detail_str = json.dumps(detail, ensure_ascii=False)
        print(f"{_card.batch} 多图核实已完成；明细：{detail_str}")
        _modify_card_status_and_notify(_card, icafe.CARD_STATUS_PULL_MULTI_VERIFY_APPROVAL_DOING, detail_str)
    print(f"订阅卡片 已投多图核实, 卡片数：{len(cards)}；处理成功数：{md_ok}")


def get_batch_multi_verify_detail(ctx: Context, batch: str) -> dict:
    """获取该批次 多图核实明细"""
    qry = f"""
    select 
        COUNT(
            CASE WHEN 
                a.status = '' 
            THEN 1 END
        ) AS not_need_multi_verify,
        COUNT(
            CASE WHEN 
                a.status = '{multi_juejin.FACE_STATUS_ING}' 
            THEN 1 END
        ) AS verifying,
        COUNT(
            CASE WHEN 
                a.status = '{multi_juejin.FACE_STATUS_ERR}' 
            THEN 1 END
        ) AS verify_err,
        COUNT(
            CASE WHEN 
                a.status = '{multi_juejin.FACE_STATUS_DONE_NOTHING}' 
            THEN 1 END
        ) AS verify_nothing,
        COUNT(
            CASE WHEN 
                a.status = '{multi_juejin.FACE_STATUS_DONE_EFFECTED}' 
            THEN 1 END
        ) AS verify_effected,
        COUNT(
            CASE WHEN 
                a.status = '{multi_juejin.FACE_STATUS_DONE_INCONSISTENT}' 
            THEN 1 END
        ) AS verify_inconsistent,
        COUNT(
            CASE WHEN 
                a.status = '{multi_juejin.FACE_STATUS_DONE_NO_JUDGE}' 
            THEN 1 END
        ) AS verify_no_judge,
        COUNT(
            CASE WHEN 
                a.status = '{multi_juejin.FACE_STATUS_DONE_UN_EFFECTED}' 
            THEN 1 END
        ) AS verify_un_effected
    from {ctx.strategy_tab} a left join {ctx.task_tab} b on a.task_id = b.task_id 
    where step = 'VERIFIED' and batch = '{batch}' 
    """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        return {}
    return {
        'not_need_multi_verify': res[0],
        'verifying': res[1],
        'verify_err': res[2],
        'verify_nothing': res[3],
        'verify_effected': res[4],
        'verify_inconsistent': res[5],
        'verify_no_judge': res[6],
        'verify_un_effected': res[7],
    }


def _modify_card_status_and_notify(card: icafe.ICafeCard, to_status: str, msg: str):
    icafe.modify_issue_status(card.sequence, to_status)
    _send_hi_by_status(card, to_status, msg)


def _send_hi_by_status(card: icafe.ICafeCard, status: str, msg: str):
    msg1 = f"【{status}】\n- 卡片链接："
    card_url = f"https://console.cloud.baidu-int.com/devops/icafe/issue/storefront-juejin-{card.sequence}/show"
    msg2 = f"\n- 任务批次：{card.batch}\n- 掘金批次：{card.batch_image}\n- 明细：{msg}\n"
    ruliu.send(
        [
            {"type": "TEXT", "content": msg1},
            {"type": "LINK", "href": card_url},
            {"type": "TEXT", "content": msg2},
            {"type": "AT", "atuserids": ["zhengcheng_cd", "mayuqing_cd", "yanghuiyu_cd"]},
        ]
    )


def main():
    """主函数"""
    fns = str(ARGS.fns)
    ctx = gen_ctx(env='online', autocommit=True, debug=True)

    if fns == 'subscribe_card_status_if_multi_verify_done':
        subscribe_card_status_if_multi_verify_done(ctx)
    elif fns == 'subscribe_card_status_if_multi_verify_ready':
        subscribe_card_status_if_multi_verify_ready(ctx)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='核实，图灵的推送和回传')
    parser.add_argument(
        '--fns',
        type=str,
        required=True,
        help="执行的函数；callback: 图灵回传"
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()


