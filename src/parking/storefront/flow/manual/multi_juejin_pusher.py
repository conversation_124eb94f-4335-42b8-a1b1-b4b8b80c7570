"""
多图掘金核实
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Fr8QITM0sh/YZCUDtktsX/wAPYGI0xv_pwC4
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/M8zoYDL6nL/IPaYJlC88WFUAx
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/iFWp8zBPVV/ihyMAoU3ExnHzE
"""
import datetime
import logging
import os.path
import subprocess
import dataclasses
import shutil

import uuid
import cv2
import requests
import shapely.wkt
import tarfile
from shapely.geometry import LineString
from tqdm import tqdm

from src.parking.storefront.flow.manual.context import gen_ctx, Context
from src.parking.storefront.flow.db import array_chunk
from src.parking.storefront.flow.context import gen_ctx_manager
from src.tools.utils import ensure_dir
from src.parking.storefront.verify.verified_image import (PrimePolygon, get_prime_by_strategy_id,
                                                          fetch_panorama_image, ViewImage)
from src.parking.storefront.diff.polygon_differ import get_foot_point
from src.parking.storefront.utils.geometric import extend_linestring
from src.tools.afs_tool import AfsTool

METER = 1e-5
LIMIT_IMG_NUM = 5  # 限制图片的数量


FACE_STATUS_ING = 'VERIFY.MULTI.ING'
FACE_STATUS_ERR = 'VERIFY.MULTI.ERR'
FACE_STATUS_DONE_INCONSISTENT = 'VERIFY.MULTI.DONE.INCONSISTENT'  # 交叉验证不一致
FACE_STATUS_DONE_NO_JUDGE = 'VERIFY.MULTI.DONE.NO_JUDGE'  # 无法判断
FACE_STATUS_DONE_EFFECTED = 'VERIFY.MULTI.DONE.EFFECTED'  # 有门前停车场
FACE_STATUS_DONE_UN_EFFECTED = 'VERIFY.MULTI.DONE.UN_EFFECTED'  # 无效门前停车场
FACE_STATUS_DONE_NOTHING = 'VERIFY.MULTI.DONE.NOTHING'  # 没有门前停车场

VERIFY_STATUS_ING = 'ING'
VERIFY_STATUS_ERR = 'ERR'
VERIFY_STATUS_DONE = 'DONE'

CONCLUSION2FACE_STATUS = {
    '10': FACE_STATUS_DONE_INCONSISTENT,
    '3': FACE_STATUS_DONE_NO_JUDGE,
    '32': FACE_STATUS_DONE_EFFECTED,
    '33': FACE_STATUS_DONE_UN_EFFECTED,
    '34': FACE_STATUS_DONE_NOTHING,
}


@dataclasses.dataclass
class StrategyFace:
    """
    策略面
    """

    sid: int
    wkt: str
    image_batch: str
    reason: str = ''
    can_push: bool = True

    def __post_init__(self):
        self.geo = shapely.wkt.loads(self.wkt)
        self.image_names = []
        self.batch_id = ''

    def get_batch_id(self) -> str:
        """获取批次号"""
        return self.batch_id if self.batch_id != '' else self.image_batch


class ImageFileSystemSaver:
    """图片文件系统保存"""

    def __init__(self, root: str, save_dir: str):
        img_path = os.path.join(root, save_dir)
        ensure_dir(img_path)

        self.root = root
        self.save_dir = save_dir
        self.img_path = img_path
        self.tar_name = f"{save_dir}.tar"
        self.tar_path = os.path.join(root, self.tar_name)

    def get_tar_name_pref(self) -> str:
        """获取 tar 包名称前缀"""
        return self.tar_name.replace('.tar', '')

    def save_images(self, images: list[ViewImage], img_pref: str) -> list:
        """保存多张图片"""
        image_ids = []
        for image in images:
            image_id = f"{img_pref}_{uuid.uuid4().hex}"
            cv2.imwrite(os.path.join(self.img_path, f"{image_id}.jpg"), image.image)
            image_ids.append(image_id)
        return image_ids

    def tar(self) -> str:
        """压缩，返回压缩包路径"""
        with tarfile.open(self.tar_path, "w") as tar:
            for root, _, files in os.walk(self.img_path):
                print(f"当前目录: {root}, 文件数: {len(files)}")
                for file in files:
                    if not file.lower().endswith(".jpg"):
                        continue
                    full_path = str(os.path.join(root, file))
                    tar.add(full_path, arcname=file)
        return self.tar_path

    def upload_tar_to_afs(self) -> tuple:
        """
        把 tar 包上传到 afs
        成功返回 True, afs_path
        失败返回 False, afs_path
        """
        afs_dir = '/user/map-data-streeview/aoi-ml/parking/storefront/street_picture/'

        afs = AfsTool()
        afs.set_shell('/home/<USER>/afs-api/bin/afsshell')

        afs.put(local_file=self.tar_path, remote_dir=afs_dir)
        afs_path = f"{afs_dir}{self.tar_name}"

        return afs.test(afs_path, '-e'), afs_path

    def clear(self):
        """清理数据空间"""
        if os.path.exists(self.tar_path):
            os.remove(self.tar_path)
        shutil.rmtree(self.img_path)


def get_prime_polygon(ctx: Context, verified_sid: int) -> PrimePolygon:
    """获取原始面"""
    qry = f"""
    SELECT id
FROM {ctx.strategy_tab}
WHERE face_id = ANY (
    SELECT UNNEST(prev_face_ids)
    FROM {ctx.strategy_tab}
    WHERE id = {verified_sid}
);
    """
    res = ctx.poi_db.fetchone(qry)
    return get_prime_by_strategy_id(res[0])


def remove_prime_view_points(prime: PrimePolygon, face: StrategyFace):
    """
    移除原始面的观察点
    因为核实面只是原始面的一部分，只需要保留核实面对应的观察点，其他的都需要移除
    作一条从观察点到原始面的垂线，垂线延伸，如果能接触到核实面（buffer 15m）
    """
    points = []
    for p in prime.view_points:
        foot = get_foot_point(p.geom, prime.geom.boundary)
        if foot is None:
            continue
        per_line = extend_linestring(LineString([p.geom, foot]), 1 * METER)
        if per_line.intersects(face.geo):
            points.append(p)
    prime.view_points = points


def no_thing():
    """空函数，啥也不做"""
    pass


def get_view_images(prime: PrimePolygon) -> list:
    """获取观察点照片"""
    fetch_panorama_image(prime, no_thing, nearly_month=36, min_brightness=50)
    images = []
    for vp in prime.view_points:
        for image in vp.side_views:
            images.append(image)
    return images


def _get_faces(ctx: Context, batch: str, image_batch: str) -> list[StrategyFace]:
    """
    获取策略面
    """
    qry = f"""
    select id, st_astext(geom) 
    from {ctx.strategy_tab} 
    where task_id in (select task_id from {ctx.task_tab} where batch = '{batch}') 
    and step = 'VERIFIED' 
    and st_perimeter(geom::geography) < (200 * 2 + 10 * 2)  -- 长 200
    and status = ''
    """
    res = ctx.poi_db.fetchall(qry)
    return [StrategyFace(
        sid=i[0],
        wkt=i[1],
        image_batch=image_batch,
    ) for i in res]


def _split_faces(faces: list[StrategyFace]) -> list[list[StrategyFace]]:
    """
    把面分组
    一个包最好2000-10000张图，根据一个面的图片数量分组
    """
    return array_chunk(faces, 1000)


def _download_face_images_v2(ctx: Context, img_saver: ImageFileSystemSaver, face: StrategyFace):
    """
    下周面对应的图片
    """
    prime = get_prime_polygon(ctx, face.sid)
    remove_prime_view_points(prime, face)
    if len(prime.view_points) > LIMIT_IMG_NUM:
        face.can_push = False
        face.reason = f"图片数量超过限制：{len(prime.view_points)} > {LIMIT_IMG_NUM}"
        return
    images = get_view_images(prime)
    if len(images) == 0:
        face.can_push = False
        face.reason = f"没有全景图片"
        return
    face.image_names = img_saver.save_images(images, f"parkStoreFront-{face.sid}_{len(images)}")
    face.batch_id = img_saver.get_tar_name_pref()


def _upload_image_to_afs(ctx: Context, faces: list[StrategyFace], image_batch: str):
    img_saver = ImageFileSystemSaver(root=os.path.abspath('./tmp/juejin_multi'), save_dir=image_batch)

    for f in tqdm(faces, desc='下载全景图片'):
        try:
            _download_face_images_v2(ctx, img_saver, f)
        except Exception as e:
            logging.exception(e)
            f.can_push = False
            f.reason = f"处理异常：{str(e)}"

    img_saver.tar()
    ok = img_saver.upload_tar_to_afs()
    img_saver.clear()
    return ok


def _push(afs_path: str):
    url = 'http://inner-lutao.baidu-int.com/audit/api/inter/parkingfrontpic'
    param = {
        'afs_path': afs_path,
        'token': 'gD2fI9UKZUtYhvww',
        'type': 8,
    }
    print(param)
    resp = requests.post(url=url, json=param)
    json_obj = resp.json()
    return json_obj["code"] == 0 and json_obj["msg"] == "success"


def main():
    """主函数"""
    batches = [
        # 'jilin_2025q2top151top360_20250528',
        # 'suihua_2025q2top151top360_20250528',
        # 'jiamusi_2025q2top151top360_20250529',
        # 'xilinguolemeng_2025q2top151top360_20250529',

        # 'weifang_2025q2top46top150_20250325',
        # 'yulin2_2025q2top46top150_20250325',
        # 'haerbin_2025q2top46top150_20250325',
        # 'handan_2025q2top46top150_20250325',

        # 'yunfu_2025q2top151top360_20250526',
        # 'chengde_2025q2top151top360_20250526',
        # 'benxi_2025q2top151top360_20250529',
        'shuangyashan_2025q2top151top360_20250529',
    ]
    for batch in batches:
        # _push_batch(batch, 'test')
        push_batch(batch, 'online')


def _maintain_faces_info(faces: list[StrategyFace]):
    """维护面信息"""
    with gen_ctx_manager(is_committed=True, debug=True) as ctx:
        for f in tqdm(faces, desc='维护面信息'):
            face_status = FACE_STATUS_ING
            verify_status = VERIFY_STATUS_ING
            if not f.can_push:
                face_status = FACE_STATUS_ERR
                verify_status = VERIFY_STATUS_ERR

            queries = [
                f"""
                    update {ctx.strategy_tab} 
                    set status = '{face_status}', batch_image = '{f.get_batch_id()}' 
                    where status = '' and id = {f.sid} 
                """
            ]
            images = f.image_names
            if len(images) == 0:
                images = ['']
            for img in images:
                queries.append(f"""
                insert into park_storefront_strategy_multi_verify 
                (strategy_id, image_id, batch_id, status, memo) 
                values (
                {f.sid}, '{img}', '{f.get_batch_id()}', '{verify_status}', '{f.reason}' 
                )
                """)
            for qry in queries:
                ctx.poi_db.execute(qry)
        ctx.poi_db.commit_or_rollback()


def _get_image_batch(ctx: Context, batch: str) -> str:
    batch_pref = batch if ctx.is_online() else 'test_' + batch
    today = str(datetime.date.today()).replace('-', '')
    return f"{batch_pref}-{today}_{uuid.uuid4().hex}"


def push_batch(batch, env: str = 'online') -> bool:
    """
    推送某个批次，推送成功返回 True
    存在失败即返回 False
    """
    ctx = gen_ctx(env=env, autocommit=True, debug=True)
    image_batch = _get_image_batch(ctx, batch)
    faces = _get_faces(ctx, batch, image_batch)
    faces_chunks = _split_faces(faces)

    ok = True
    for _faces_chunk in faces_chunks:
        ok, afs = _upload_image_to_afs(ctx, _faces_chunk, image_batch)
        if not ok:
            ok = False
            print(f"afs 上传失败")
            continue
        push_ok = _push(afs)
        if not push_ok:
            ok = False
            print(f"推送掘金失败")
            continue
        _maintain_faces_info(_faces_chunk)
        print(push_ok)
    print(f"{batch}, over")
    return ok


if __name__ == '__main__':
    main()

