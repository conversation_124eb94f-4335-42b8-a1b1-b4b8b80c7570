"""
开放式门前推送作业, 绑定 link
"""
import argparse
import ast
import json
import uuid
from datetime import datetime

import shapely.wkt
from shapely.geometry import mapping

from src.parking.storefront.flow.manual.context import Context, gen_ctx, gen_test_ctx, gen_ctx_by_batch
from src.parking.storefront.flow.manual.pusher import push_turing
from src.parking.storefront.flow.model import road, ach
from src.parking.storefront.flow.coord_helper import gcj2bd09
from src.tools import tsv


def _gen_push_param(refgeo_json: str, info_id: str, tips: str, geo_json: list = []):
    """
    生成推送参数
    """
    current_datetime = datetime.now()
    current_timestamp = current_datetime.timestamp()
    param = {
        'platform': 0,
        'source': 1207,
        'sourceid': f"park_storefront_open_{info_id}_{uuid.uuid4().hex}",
        'subbusinesstype': 403,
        'roadname': "",
        'provname': "",
        'cityname': "",
        'pic': "",
        'problem': 0,
        'occurtime': current_timestamp,
        'content': tips,
        'reliability': 0,
        'geo': geo_json,
        'refgeo': refgeo_json,
        'properties': {
            "need_giving_back": {
                "data_type": "park_storefront_open"
            },
            "scene": {"diversion": 0},
            "area_uid": info_id,  # 面id
            # "tasks": tasks,
        },
        'coordsys': "bd09ll",
        'sourcetime': current_timestamp,
        "_signkeys": "platform,source",
        "sign": "1f083f433f94477a9b1786c8306665d5"
    }
    return param


def _gen_refgeo_by_short_link_ids(ctx: Context, short_link_ids: list) -> list:
    long_link_ids = []
    for short_link_id in short_link_ids:
        long_link_id = road.get_long_link_id(ctx, short_link_id)
        long_link_ids.append(long_link_id)
    return _gen_refgeo_by_long_link_ids(ctx, long_link_ids)


def _gen_refgeo_by_long_link_ids(ctx: Context, long_link_ids: list) -> list:
    refgeo = []
    links = road.get_link_infos(ctx, long_link_ids)
    for link in links:
        long_link_id = link[0]
        road_dir = link[1]
        mesh_id = link[6]
        geom = link[2]

        orientation = 4 if road_dir == 3 else 3

        line_wkt_bd09 = gcj2bd09(geom)
        refgeo.append({
            'type': 'Feature',
            'geometry': mapping(shapely.wkt.loads(line_wkt_bd09)),
            'properties': {
                'linkId': long_link_id,
                'dir': int(road_dir),
                'orientation': int(orientation),
                'mesh_id': mesh_id,
                'skipCheck': True,
            },
        })
    return refgeo


def _gen_refgeo_by_park_geom(park_wkt: str) -> list:
    park_wkt_bd09 = gcj2bd09(park_wkt)
    return [
        {
            'type': 'Feature',
            'geometry': mapping(shapely.wkt.loads(park_wkt_bd09)),
            'properties': [],
        }
    ]


def get_wkt_by_turing_id(ctx: Context, iid: str) -> str:
    """根据图灵id获取范围"""
    qry = f"""
    select st_astext(a.geom), st_astext(b.geom) 
    from {ctx.turing_result_tab} a left join {ctx.pushed_tab} b on a.info_id = b.info_id 
    where a.turing_id = '{iid}'
    """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        return ''

    geo1 = shapely.wkt.loads(res[0])
    if not geo1.is_empty:
        return res[0]
    return res[1]


def _gen_push_param_by(ctx: Context, uid: str, wkt: str, short_link_ids: list) -> dict:
    """
    生成推送参数
    """
    refgeo = _gen_refgeo_by_short_link_ids(ctx, short_link_ids)

    tips = '门前停车场开放场景核实'
    if len(refgeo) == 0:
        tag = '，link缺失'
    else:
        tag = '，link核实'
    tips += tag

    refgeo += _gen_refgeo_by_park_geom(wkt)
    ref_jsn = json.dumps(refgeo)
    return _gen_push_param(ref_jsn, uid, tips=tips)


def demo():
    """测试"""
    ctx = gen_test_ctx()
    data = [
        {
            'bid': '676ff52f015bc70a582cf401',
            'short_link_ids': []
        },
        {
            'bid': '6772a26238bc1d56706e08a5',
            'short_link_ids': ['1679542777', '1679542804', '1657909138', '1574175036', '1656001036']
        },
        {
            'bid': '6772162f6a79577bcd387d0e',
            'short_link_ids': {'1657966626', '1612212383', '1670452444', '1612212005'},
        },
        {
            'bid': '676ff51233afdd30a47df5db',
            'short_link_ids': [],
        },
        {
            'bid': '676ff30d025222a8fd38df1e',
            'short_link_ids': {'1678075159'},
        },
        {
            'bid': '6772162f6a79577bcd387d0e',
            'short_link_ids': {'1657966626', '1612212383', '1670452444', '1612212005'},
        },
        {
            'bid': '6772162f6a79577bcd387d0e',
            'short_link_ids': {'1657966626', '1612212383', '1670452444', '1612212005'},
        },
    ]

    for item in data:
        wkt = get_wkt_by_turing_id(ctx, item['bid'])
        param = _gen_push_param_by(ctx, item['bid'], wkt, list(item['short_link_ids']))
        print(push_turing(ctx, param))


def demo_by_file():
    """测试"""
    limit = 30
    file = 'tmp/data_0627_01.csv'
    data = []
    for row in tsv.read_tsv(file, skip_header=True):
        data.append({
            'iid': row[0],
            'short_link_ids': list(ast.literal_eval(row[3])),
        })

    ctx = gen_test_ctx(autocommit=True)
    data = data[:limit]
    for item in data:
        wkt = get_wkt_by_turing_id(ctx, item['iid'])
        if wkt == '':
            print(f"{item} 没有获取到范围")
            continue
        param = _gen_push_param_by(ctx, item['iid'], wkt, item['short_link_ids'])
        print(push_turing(ctx, param))


def _push_park(ctx: Context, park: ach.ParkingAch, batch: str) -> int:
    param = _gen_push_param_by(ctx, park.bid, park.geom, park.get_road_relation_strategy_link_ids())
    verify_id = push_turing(ctx, param)
    if verify_id == '':
        print(f"{park.bid} 推送绑定 link 失败")
        return 0

    info_id = uuid.uuid4().hex
    ins_qry = f"""
    insert into {ctx.pushed_tab} 
            (info_id, info_source, verifier, verify_id, batch, remark, geom, prev_info_id, city, 
            task_id, status, cancel_memo, data_type, bid) 
            values('{info_id}', 'parking', 'turing', '{verify_id}', '{batch}', '', 
            st_geomfromtext('{park.geom}', 4326), '{park.park_id}', '{park.city}', 0, 
            'PUSHED', '', '1207', '{park.bid}')
    """
    upd_qry = f"""
    update {ctx.park_ach_tab} 
    set status = 'PARK_ACCESS_INTELLIGENCE_PUSHED' 
    where id = {park.park_id} and status = 'PARK_ACCESS_INTELLIGENCE'
    """
    queries = [ins_qry, upd_qry]

    for qry in queries:
        ctx.poi_db.execute(qry)
    return 1


def push(ctx: Context, batch: str, limit: int):
    """
    推送到作业平台
    """
    where = f"""
    bid_status = 'effected' and status = 'PARK_ACCESS_INTELLIGENCE' 
    and park_type = 'open'  
    """
    if not ctx.is_online():
        where += " and bid like '%bak%' "
    if limit > 0:
        where += f" limit {limit} "
    num = 0
    for parks in ach.get_parks_generate(ctx, where):
        for _park in parks:
            num += _push_park(ctx, _park, batch)
            ctx.poi_db.commit_or_rollback()
        print(f"已推送：{num}")
    print(f"总共推送了：{num}")


def main():
    """
    主函数
    """
    batch = str(ARGS.batch)
    limit = int(ARGS.limit)

    today = datetime.today().strftime("%Y%m%d")
    batch = f"{batch}{today}"
    print(f"batch:{batch}")

    ctx = gen_ctx_by_batch(batch, is_committed=True, debug=True)
    return push(ctx, batch, limit)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='开放停车场策略绑定 link')
    parser.add_argument('--limit', type=int, default=10)
    parser.add_argument('--batch', type=str)

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()
