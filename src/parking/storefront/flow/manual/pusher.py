"""
推送给作业平台
"""
import logging

import requests

from src.parking.storefront.flow.manual.context import Context


class TuringPusher:
    """
    图灵推送
    """

    def __init__(self, ctx: Context, param: dict):
        self.ctx = ctx
        self.param = param
        self.response = {}
        self.error = ''

    def push(self) -> str:
        """
        推送到图灵平台, 推送成功返回 图灵id，失败返回空字符串
        """
        param = self.param
        ctx = self.ctx

        print(param)
        print(ctx.url)
        # return '123'  # todo test
        try:
            res = requests.post(
                ctx.url, json=param
            )
            """
            {'errno': 0, 'errmsg': 'success', 'data': {'id': '676cc6e22d4c5da4e56c4d51', 'duplicate': 0}}
            """
            print(res.json())
            data = res.json()
            self.response = data
            if data['errno'] == 0:
                return data['data']['id']
            return ''
        except Exception as e:
            logging.exception(e)
            self.error = str(e)
            return ''

    def get_response(self) -> dict:
        """
        获取推送结果
        """
        return self.response

    def get_error(self) -> str:
        """
        获取错误
        """
        return self.error


def push_turing(ctx: Context, param: dict) -> str:
    """
    推送图灵
    """
    pusher = TuringPusher(ctx, param)
    return pusher.push()


