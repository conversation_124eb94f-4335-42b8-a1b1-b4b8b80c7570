"""
数据库交互
"""
from typing import List

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.db import join_str, handle_apostrophe
from src.parking.storefront.flow.merger.model import StrategyFace


def get_task_ids(ctx: Context, batch: str) -> list:
    """
    获取某个批次需要合并的任务
    """
    qry = f"""
    select task_id from park_storefront_task a 
    where batch = '{batch}' and status in ('GENERATED') 
    and exists(
    select 1 from park_storefront_strategy b 
    where a.task_id = b.task_id and b.step = 'VERIFIED' 
    )
    """
    return ctx.poi_db.get_values(qry)


def get_faces_by_where(ctx: Context, where: str) -> List[StrategyFace]:
    """
    根据条件获取策略面
    """
    qry = f"""
    select id, task_id, face_id, prev_face_ids, st_astext(geom), st_astext(baseline) 
    from park_storefront_strategy 
    where {where}
    """
    res = ctx.poi_db.fetchall(qry)

    return [StrategyFace(
        sid=str(item[0]),
        task_id=item[1],
        face_id=item[2],
        prev_face_ids=item[3],
        wkt=item[4],
        baseline=item[5],
    ) for item in res]


def save_merged_face(ctx: Context, face: StrategyFace) -> int:
    """
    保存一个合并后的面
    """
    qry = f"""
    insert into park_storefront_strategy 
    (task_id, face_id, prev_face_ids, baseline, geom, strategy, remark, step) 
    values 
    (
        {face.task_id}, '{face.face_id}', ARRAY[{join_str(face.prev_face_ids)}]::text[],
        st_geomfromtext('{face.baseline}', 4326), st_geomfromtext('{face.wkt}', 4326), 
        'MEGED', '{handle_apostrophe(face.merged_reason)}', 'POST'
    ) 
    """
    return ctx.poi_db.insert_return_id(qry)


def modify_task_status(ctx: Context, task_id: int, to_status: str, from_status: str) -> bool:
    """
    修改任务状态
    """
    qry = f"""
    update park_storefront_task set status = '{to_status}' 
    where task_id = {task_id} and status = '{from_status}'
    """
    aff = ctx.poi_db.execute(qry)
    return aff > 0

