"""
离散停车场面合并
为了减量，减少重复作业，提高作业效率
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/aO7UyUYGkm/5nlUlSLntKuB0t

相关 case
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/aO7UyUYGkm/a763974372db4c
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/aO7UyUYGkm/42348fcfbb3947
"""
import math
from typing import List, Union, Tuple

import shapely.wkt
from shapely.geometry import Point, LineString
from shapely.ops import unary_union
from tqdm import tqdm

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.db import join_int, join_str
from src.parking.storefront.flow.merger.model import StrategyFace, Street, Aoi, Segment, SegmentWithAoi, \
    AoiSegments, FaceSegments
from src.parking.storefront.diff.polygon_differ import project, interpolate, find_difference_intervals, \
    merge_two_intervals, merge_intervals
from src.parking.storefront.flow.merger.db_utli import get_faces_by_where, modify_task_status, get_task_ids
from src.parking.storefront.flow.merger.saver import save_to_db, save_to_file
from src.tools.utils import calc_iou

METER = 1e-5

# STREET_DISTANCE = 150 * METER
# AOI_DISTANCE = 150 * METER

STREET_DISTANCE = 200 * METER
AOI_DISTANCE = 200 * METER

# DEBUG = True
DEBUG = False


def merge_batch_faces(ctx: Context, batch: str, saver: str, file: str = ''):
    """
    合并一个批次的所有策略面
    """
    for tid in get_task_ids(ctx, batch):
        merge_task_faces(ctx, tid, saver, file)
        ctx.poi_db.commit_or_rollback()


def merge_task_faces(ctx: Context, task_id: int, saver: str, file: str = ''):
    """
    合并一个任务的策略面
    """
    statuses = ['', 'INIT']
    if DEBUG:
        statuses.append('PUSHED')

    where = f"""
    task_id = {task_id} and step = 'VERIFIED' and status in ({join_str(statuses)})
    """
    before = get_faces_by_where(ctx, where)
    merged = merge_faces(ctx, before)
    if saver == '':
        return merged

    if saver == 'db':
        if modify_task_status(ctx, task_id, 'MERGED', 'GENERATED'):
            save_to_db(ctx, merged)
        else:
            print(f"{task_id} 的任务状态修改失败，暂不入库保存")
    else:
        save_to_file(file, before, merged, str(task_id))
    return merged


def merge_faces(ctx: Context, faces: List[StrategyFace]) -> List[StrategyFace]:
    """
    合并策略面
    返回合并后的策略面
    """
    remains = faces
    results = []
    streets = _get_streets(ctx, faces)
    for street in tqdm(streets):
        street_faces = _get_street_faces(street, remains)
        results += _merge_street_faces(ctx, street, street_faces)

        # 确保面不重复合，不遗漏
        street_fids = [f.face_id for f in street_faces]
        other_faces = []
        for f in remains:
            if f.face_id not in street_fids:
                other_faces.append(f)
        remains = other_faces
    merged = results + remains

    before_geos = [f.geo for f in faces]
    merged_geos = [f.geo for f in merged]

    before_union = unary_union(before_geos)
    merged_union = unary_union(merged_geos)
    iou = calc_iou(before_union, merged_union)
    assert (before_union.equals(merged_union) or iou > 0.999), "合并前与合并后的面积应该一样"

    if DEBUG:
        _print_faces(faces, "合并前")
        _print_faces(merged, "合并后")

    return merged


def _get_streets(ctx: Context, faces: List[StrategyFace]) -> List[Street]:
    """
    获取策略面所在的街区
    """
    task_ids = list(set([face.task_id for face in faces]))
    if len(task_ids) == 0:
        return []

    return get_streets_by_task_ids(ctx, task_ids)


def get_streets_by_task_ids(ctx: Context, task_ids: list) -> List[Street]:
    """
    获取街区
    """
    qry = f"""
    select region_id, st_astext(geom), task_id from park_storefront_task 
    where task_id in ({join_int(task_ids)}) 
    """
    res = ctx.poi_db.fetchall(qry)
    return [Street(
        face_id=item[0],
        wkt=item[1],
        task_id=item[2],
    ) for item in res]


def _get_street_aois(ctx: Context, street: Street) -> List[Aoi]:
    """
    获取街区所在的 aoi
    """
    qry = f"""
    select face_id, st_astext(geom) 
    from blu_face where st_contains(st_geomfromtext('{street.wkt}', 4326), geom) 
    """
    res = ctx.back_db.fetchall(qry)

    aois = [Aoi(
        face_id=item[0],
        wkt=item[1],
    ) for item in res]

    def _been_contained(aoi: Aoi) -> bool:
        for b in aois:
            if b.face_id == aoi.face_id:
                continue
            if b.geo.contains(aoi.geo):
                return True
        return False

    resp = []
    for _aoi in aois:
        if _been_contained(_aoi):
            continue
        resp.append(_aoi)
    return resp


def _get_street_faces(street: Street, faces: List[StrategyFace]) -> List[StrategyFace]:
    """
    获取街区所在的策略面
    """
    resp = []
    for f in faces:
        if f.task_id == street.task_id:
            resp.append(f)
    return resp


def _merge_street_faces(ctx: Context, street: Street, faces: List[StrategyFace]) -> List[StrategyFace]:
    """
    合并一个街区的策略面
    """
    if len(faces) <= 1:
        return faces

    aois = _get_street_aois(ctx, street)

    debug_geo = shapely.wkt.loads('LINESTRING (118.3185924 35.1284064, 118.3185976 35.1284352)')
    debug_fid = ['190da44d6c2451a1cafab9f77f9beb33', '2eacf2c19f514b8caf70ae22b4df12e3']

    segment_aois = get_segment_aois(street, aois)
    aoi_segments_list = _to_aoi_segments_list(segment_aois)

    if DEBUG:
        _print_aois(aois, '')
        _print_segments([s.segment for s in segment_aois], 'aoi 映射的线段')

    face_segments_list = get_face_segments_list(street, faces)
    merged_face = _merge_by_aoi(aoi_segments_list, face_segments_list)
    return _merge_by_distance(merged_face)


def get_face_segments_list(street: Street, faces: List[StrategyFace]) -> List[FaceSegments]:
    """get_face_segments_list"""
    face_segments_list = []
    for face in faces:
        # if face.face_id not in debug_fid:  # todo test
        #     continue
        face_segments_list.append(_get_face_street_segment(street, face))
    return face_segments_list


def get_segment_aois(street: Street, aois: List[Aoi]) -> List[SegmentWithAoi]:
    """
    获取 segment_aois
    """
    segment_aois = []
    for segment in street.get_segments():
        # if not debug_geo.equals(segment.line):  # todo test
        # if not debug_geo.intersects(segment.line):
        #     continue
        segment_aois += _get_segment_with_aois(segment, aois)
    return segment_aois


def _get_segment_with_aois(segment: Segment, aois: List[Aoi]) -> List[SegmentWithAoi]:
    """
    获取 aoi 映射到街区某一段的区间
    """
    segment_with_aois = []
    for aoi in aois:
        if segment.line.distance(aoi.geo) > 50 * METER:
            continue
        with_aoi = _get_segment_with_aoi(segment, aoi)
        if with_aoi is None:
            continue
        segment_with_aois.append(with_aoi)
    return _unique_segment_with_aois_by_distance(segment_with_aois)


def _get_segment_with_aoi(segment: Segment, aoi: Aoi) -> Union[SegmentWithAoi, None]:
    distances = []
    locations = []
    for pt in aoi.get_points():
        location = project(segment.line, pt)
        projected_pt = interpolate(segment.line, location)
        locations.append(location)

        if location <= 0:
            distance = segment.get_start_pt().distance(aoi.geo)
        elif location >= 1:
            distance = segment.get_end_pt().distance(aoi.geo)
        else:
            distance = pt.distance(projected_pt)
        distances.append(distance)
    if min(locations) == max(locations):
        return None

    start_pt = interpolate(segment.line, min(locations))
    end_pt = interpolate(segment.line, max(locations))

    # 需要再转换到街区上
    new_segment = segment.street.gen_segment(start_pt, end_pt)
    return SegmentWithAoi(
        segment=new_segment,
        aoi=aoi,
        distance=min(distances),
    )


def _unique_segment_with_aois_by_distance(segment_with_aois: List[SegmentWithAoi]) -> List[SegmentWithAoi]:
    """
    根据距离唯一去重
    街区的每一段，只保留距离最近的 aoi
    各段之间不能有重叠
    """
    segment_with_aois.sort(key=lambda x: x.distance)

    results = []
    merged_interval = []
    for segment_aoi in segment_with_aois:
        intervals = find_difference_intervals([segment_aoi.segment.get_interval()], merged_interval)
        merged_interval = merge_two_intervals(merged_interval, intervals)
        for i in intervals:
            new_segment = segment_aoi.segment.street.gen_segment_by_location(i[0], i[1])
            results.append(SegmentWithAoi(
                segment=new_segment,
                aoi=segment_aoi.aoi,
                distance=segment_aoi.distance,
            ))
    return results


def _get_face_street_segment(street: Street, face: StrategyFace) -> FaceSegments:
    """
    获取策略面映射到街区的区间
    """
    debug_geo = shapely.wkt.loads('LINESTRING EMPTY')

    segments = []
    for line in face.get_lines():
        # if not line.equals(debug_geo):  # todo test
        #     continue
        segments += _get_street_segments(line, street)

    return FaceSegments(
        segments=segments,
        face=face,
    )


def _get_street_segments(line: LineString, street: Street) -> List[Segment]:
    start_pt, end_pt = Point(line.coords[0]), Point(line.coords[-1])
    start_loc = project(street.get_boundary(), start_pt)
    end_loc = project(street.get_boundary(), end_pt)

    if end_loc < start_loc and end_loc == 0:
        print(f"请注意: 位置发生了变更：{end_loc} -> 1")
        end_loc = 1

    if math.fabs(start_loc - end_loc) > 0.5:  # 需要取反
        """
        0.1 -> 0.9
        0.9 -> 0.1
        """
        locations = [
            [0, min(start_loc, end_loc)],
            [max(start_loc, end_loc), 1],
        ]
    else:
        locations = [
            [min([start_loc, end_loc]), max([start_loc, end_loc])],
        ]
    segments = []
    for i in locations:
        segments.append(street.gen_segment_by_location(i[0], i[1]))
    return segments


def _merge_by_aoi(aoi_segments_list: List[AoiSegments], face_segments_list: List[FaceSegments]) -> List[StrategyFace]:
    """
    根据 aoi 合并
    同一AOI对应的门前停车场面
    """

    aoi2faces = {}
    merged_resp = []
    for face_segments in face_segments_list:
        aoi = _get_belong_to_aoi(face_segments, aoi_segments_list)
        if aoi is None:
            merged_resp.append(face_segments.face)
            continue
        aoi2faces.setdefault(aoi.face_id, []).append(face_segments.face)

    for aoi, faces in aoi2faces.items():
        mergeds = merged_faces(faces, f'因为 aoi:{aoi} 被合并', AOI_DISTANCE)
        # print(f"{merged.merged_reason}")
        merged_resp += mergeds
    return merged_resp


def _to_aoi_segments_list(segment_aois: List[SegmentWithAoi]) -> List[AoiSegments]:
    fid2aoi_segments = {}
    for segment_aoi in segment_aois:
        fid = segment_aoi.aoi.face_id
        if fid not in fid2aoi_segments:
            fid2aoi_segments[fid] = AoiSegments(
                segments=[segment_aoi.segment],
                aoi=segment_aoi.aoi
            )
        else:
            fid2aoi_segments[fid].segments.append(segment_aoi.segment)
    return list(fid2aoi_segments.values())


def _get_belong_to_aoi(face: FaceSegments, aoi_segments_list: List[AoiSegments]) -> Union[Aoi, None]:
    """
    获取所属的 aoi
    如果超过一定的距离，不属于任何一个 aoi，或者属于另外的 aoi，那么返回 None
    """
    face_intervals = merge_intervals([segment.get_interval() for segment in face.segments])
    if len(face_intervals) == 0:
        return None
    for aoi_segments in aoi_segments_list:
        aoi_intervals = merge_intervals([segment.get_interval() for segment in aoi_segments.segments])
        dif_intervals = find_difference_intervals(face_intervals, aoi_intervals)

        dif_length = 0
        for i in dif_intervals:
            dif_length += face.segments[0].street.get_length(i[0], i[1])
        if dif_length / face.face.length >= 0.5:
            continue
        print(f"diff_length: {dif_length / METER}")
        if dif_length < 30 * METER:
            return aoi_segments.aoi
    return None


def merged_faces(faces: List[StrategyFace], reason, limit_distance: float) -> List[StrategyFace]:
    """
    策略面合并，如果面之间的距离超过限制，那么不合并
    """
    if len(faces) <= 1:
        return faces

    resp = []
    wait = []
    for f1 in faces:
        is_ok = False
        for f2 in faces:
            if f1.face_id == f2.face_id:
                continue
            if f1.geo.distance(f2.geo) < limit_distance:
                is_ok = True
                break
        if is_ok:
            wait.append(f1)
        else:
            resp.append(f1)
    if len(wait) > 0:
        merged = _merged_faces(wait, reason)
        print(merged.merged_reason)
        resp.append(merged)
    return resp


def _merged_faces(faces: List[StrategyFace], reason) -> StrategyFace:
    """
    策略面合并
    """
    merged = faces[0]
    for f in faces[1:]:
        merged = merged.merge_other(f, reason)
    return merged


def _merge_by_distance(faces: List[StrategyFace]) -> List[StrategyFace]:
    """
    根据距离合并
    """
    while True:
        merged, times = _merge_once_by_distances(faces)
        if times == 0:
            return merged
        faces = merged


def _merge_once_by_distances(faces: List[StrategyFace]) -> Tuple[List[StrategyFace], int]:
    min_distance = STREET_DISTANCE

    times = 0
    merged_face = []
    merged_sids = set()
    for f1 in faces:
        if f1.sid in merged_sids:
            continue
        merged_sids.add(f1.sid)
        for f2 in faces:
            if f2.sid in merged_sids:
                continue
            distance = f1.geo.distance(f2.geo)
            if distance <= min_distance:
                f1 = f1.merge_other(f2, f"因为距离：{distance/METER} 被合并")
                print(f1.merged_reason)

                merged_sids.add(f2.sid)
                times += 1
        merged_face.append(f1)
    return merged_face, times


def get_face_belong_to_aoi(ctx: Context, face: StrategyFace, aois: List[Aoi]) -> Union[Aoi, None]:
    """
    获取策略面所属的 aoi
    """
    streets = get_streets_by_task_ids(ctx, [face.task_id])
    if len(streets) == 0:
        return None
    street = streets[0]

    segment_aois = get_segment_aois(street, aois)
    aoi_segments_list = _to_aoi_segments_list(segment_aois)
    face_segments_list = get_face_segments_list(street, [face])

    if DEBUG:
        _print_aois(aois, '')
        _print_segments([s.segment for s in segment_aois], 'aoi 映射的线段')
        _print_face_segments_list(face_segments_list, '停车面的映射')

    for f in face_segments_list:
        aoi = _get_belong_to_aoi(f, aoi_segments_list)
        if aoi is not None:
            return aoi
    return None


def _print_segments(segments: List[Segment], desc: str):
    print(f"{desc} 开始打印线段")
    for s in segments:
        print(s.line)
    print(f"{desc} 结束打印线段")


def _print_faces(faces: List[StrategyFace], desc: str):
    print(f"{desc} 开始打印面")
    for s in faces:
        print(s.wkt)
    print(f"{desc} 结束打印面")


def _print_aois(aois: List[Aoi], desc: str):
    print(f"{desc} 开始打印 aoi")
    for s in aois:
        print(s.wkt)
    print(f"{desc} 结束打印 aoi")


def _print_face_segments_list(face_segments_list: List[FaceSegments], desc: str):
    print(f"{desc} 开始打印 face_segments_list")
    for f in face_segments_list:
        _print_segments(f.segments, f.face.face_id)
    print(f"{desc} 结束打印 face_segments_list")
