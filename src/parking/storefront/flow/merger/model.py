"""
离散面合并
相关模型定义
"""
import dataclasses
import uuid
from typing import List, Tuple

import shapely.wkt
from shapely.geometry import LineString, Point, Polygon
from shapely.ops import linemerge

from src.parking.storefront.diff.polygon_differ import project, interpolate, get_line_by_location
from src.parking.storefront.utils.geometric import flat_line

METER = 1e-5


@dataclasses.dataclass
class Street:
    """
    街区
    """
    face_id: str
    wkt: str
    task_id: int

    def __post_init__(self):
        self.geo = shapely.wkt.loads(self.wkt).buffer(0).simplify(5 * 1e-5)
        self.wkt = self.geo.wkt
        self.boundary = flat_line(self.geo.boundary)[0]

    def get_segments(self) -> List['Segment']:
        """
        获取分段列表
        """
        segments = []
        for line in split_cell_lines(self.boundary):
            start_pt, end_pt = _get_start_end_pt(line)
            segments.append(self.gen_segment(start_pt, end_pt))
        return segments

    def gen_segment(self, start_pt: Point, end_pt: Point) -> 'Segment':
        """
        重新生成片段
        """
        start_loc = project(self.boundary, start_pt)
        end_loc = project(self.boundary, end_pt)
        if end_loc < start_loc and end_loc == 0:
            print(f"请注意: 位置发生了变更：{end_loc} -> 1")
            end_loc = 1
        line = get_line_by_location(self.boundary, start_loc, end_loc)
        return Segment(
            street=self,
            line=line,
            start=start_loc,
            end=end_loc,
        )

    def gen_segment_by_location(self, start_loc: float, end_loc: float) -> 'Segment':
        """
        根据位置生成片段
        """
        start_pt = interpolate(self.boundary, start_loc)
        end_pt = interpolate(self.boundary, end_loc)
        return self.gen_segment(start_pt, end_pt)

    def get_boundary(self) -> LineString:
        """获取外围边界"""
        return self.boundary

    def get_length(self, start_loc: float, end_loc: float):
        """获取位置所代表的长度"""
        if end_loc < start_loc and end_loc == 0:
            print(f"请注意")
            end_loc = 1
        line = get_line_by_location(self.boundary, start_loc, end_loc)
        return line.length


def split_cell_lines(line) -> List[LineString]:
    """
    拆分成多条线，每条线都是两个点
    """
    resp = []
    for i in flat_line(line):
        coords = i.coords
        for idx, cd in enumerate(coords):
            if idx == len(coords) - 1:
                # 最后一个
                continue
            start_pt = Point(cd)
            end_pt = Point(coords[idx + 1])
            resp.append(LineString((start_pt, end_pt)))
    return resp


def _get_start_end_pt(line: LineString) -> Tuple[Point, Point]:
    coords = line.coords
    return Point(coords[0]), Point(coords[-1])


@dataclasses.dataclass
class Segment:
    """
    一段
    """
    street: Street
    line: LineString
    start: float
    end: float

    def __post_init__(self):
        self.coords = self.line.coords

    def get_start_pt(self) -> Point:
        """获取起点"""
        return Point(self.coords[0])

    def get_end_pt(self) -> Point:
        """获取终点"""
        return Point(self.coords[-1])

    def get_interval(self) -> list:
        """获取间隔"""
        return [self.start, self.end]


@dataclasses.dataclass
class Aoi:
    """
    aoi
    """
    face_id: str
    wkt: str

    def __post_init__(self):
        self.geo: Polygon = shapely.wkt.loads(self.wkt)

    def get_points(self) -> List[Point]:
        """
        获取所有的坐标点
        """
        pts = []
        for cd in self.geo.boundary.coords:
            pts.append(Point(cd))
        return pts


@dataclasses.dataclass
class StrategyFace:
    """
    策略面
    """
    sid: str
    wkt: str
    face_id: str
    prev_face_ids: list
    task_id: int
    baseline: str
    merged_reason: str = ''

    def __post_init__(self):
        self.geo = shapely.wkt.loads(self.wkt)
        self.length = (self.geo.length - 20 * METER) / 2

    def get_points(self) -> List[Point]:
        """
        获取所有的坐标点
        """
        pts = []
        for cd in self.geo.boundary.coords:
            pts.append(Point(cd))
        return pts

    def get_lines(self) -> List[LineString]:
        """
        把边界拆分成多条线
        """
        return split_cell_lines(self.geo.boundary)

    def merge_other(self, other: 'StrategyFace', reason: str) -> 'StrategyFace':
        """
        合并其他的面
        """
        new_geo = self.geo.union(other.geo)
        baselines = flat_line(shapely.wkt.loads(self.baseline)) + flat_line(shapely.wkt.loads(other.baseline))
        new_line = linemerge(baselines)

        face_id = uuid.uuid4().hex

        prev_face_ids = []
        faces = [self, other]
        for f in faces:
            if f.is_merged():
                prev_face_ids += f.prev_face_ids
            else:
                prev_face_ids.append(f.face_id)

        reasons = [s for s in [self.merged_reason, other.merged_reason, f"{prev_face_ids}; {reason}"] if s.strip()]
        return StrategyFace(
            sid='_'.join([self.sid, other.sid]),
            wkt=new_geo.wkt,
            face_id=face_id,
            prev_face_ids=prev_face_ids,
            task_id=self.task_id,
            merged_reason=' || '.join(reasons),
            baseline=new_line.wkt,
        )

    def is_merged(self):
        """
        是否是合并面
        """
        return self.merged_reason != ''


@dataclasses.dataclass
class SegmentWithAoi:
    """
    片段和aoi
    """
    segment: Segment
    aoi: Aoi
    distance: float


@dataclasses.dataclass
class AoiSegments:
    """
    aoi 的所有片段
    """
    segments: List[Segment]
    aoi: Aoi


@dataclasses.dataclass
class FaceSegments:
    """
    面和所有的片段
    """
    segments: List[Segment]
    face: StrategyFace


