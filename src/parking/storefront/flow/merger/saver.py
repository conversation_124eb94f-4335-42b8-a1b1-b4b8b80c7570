"""
合并的结果保存
"""
import csv
from typing import List

from src.parking.storefront.flow.merger.model import StrategyFace
from src.parking.storefront.flow.merger.db_utli import save_merged_face
from src.parking.storefront.flow.context import Context


def save_to_file(file: str, before: List[StrategyFace], after: List[StrategyFace], source_id: str, mode='a+'):
    """
    保存到文件
    """
    with open(file, 'a+') as hdw:
        writer = csv.writer(hdw, delimiter='\t')
        for f in after:
            writer.writerow([source_id, f.wkt, '', f.merged_reason])

        for f in before:
            writer.writerow([source_id, '', f.wkt, ''])


def save_to_db(ctx: Context, after: List[StrategyFace]):
    """
    保存到数据库
    """
    for f in after:
        save_merged_face(ctx, f)

