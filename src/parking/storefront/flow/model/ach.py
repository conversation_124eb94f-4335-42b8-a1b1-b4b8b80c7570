"""
成果
"""
import json
from typing import Union, List

import dataclasses
import shapely.wkt

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.db import join_int, join_str, array_chunk, handle_apostrophe
from src.parking.storefront.flow.coord_helper import gcj2bdmc


class ParkingAchStatus:
    """
    停车场成果状态
    """
    READY = 'READY'
    ONLINE = 'ONLINE'
    COMING_ONLINE = 'COMING_ONLINE'
    INITED_BID = 'INITED_BID'


@dataclasses.dataclass
class ParkingAch:
    """
    停车场成果
    """
    face_id: str
    bid: str
    name: str
    address: str
    geom: str
    batch: str
    central_line: str
    parent_bid: str
    task_id: int
    park_id: int
    remark: dict
    remove_bids: list
    source: str
    info_id: str
    status: str
    city: str
    check_memo: str
    checker: str
    type: str
    online_num: int
    memo: str
    created_at: str
    road_relation_strategy: dict
    road_relation_manual: dict
    point_x = 0
    point_y = 0

    def __post_init__(self):
        self.polygon = shapely.wkt.loads(self.geom)

    def get_remove_bids(self) -> list:
        """
        获取需要下线的 bid
        """
        if self.remove_bids is None:
            return []
        return self.remove_bids

    def get_extends(self) -> dict:
        """
        获取需要继承的信息
        """
        if self.remark is None:
            return {}
        if 'diff_online' not in self.remark:
            return {}
        if 'extends' not in self.remark['diff_online']:
            return {}
        return self.remark['diff_online']['extends']

    def get_extend_park_bid(self) -> str:
        """
        获取需要继承的停车场 bid
        """
        extends = self.get_extends()
        return extends['park_bid'] if 'park_bid' in extends else ''

    def get_extend_access_bids(self) -> list:
        """
        获取需要继承的出入口 bids
        """
        extends = self.get_extends()
        return extends['access_bids'] if 'access_bids' in extends else []

    def checker_is_online(self) -> bool:
        """
        线上流程质检的
        """
        return self.checker == 'online'

    def get_road_relation_strategy_link_ids(self) -> list:
        """
        获取策略绑定的 link_ids
        """
        if 'link_info' not in self.road_relation_strategy:
            return []
        return [item['link_id'] for item in self.road_relation_strategy['link_info'] if 'link_id' in item]


def get_parking_ach(ctx: Context, park_id: int) -> Union[ParkingAch, None]:
    """
    获取停车场成果
    """
    aches = get_parking_aches(ctx, [park_id])
    if len(aches) == 0:
        return None
    return aches[0]


def get_parking_ach_by_bid(ctx: Context, bid: str) -> Union[ParkingAch, None]:
    """
    根据 bid 获取停车场
    """
    qry = f"""
    select id from {ctx.park_ach_tab} where bid = '{bid}' and bid_status = 'effected'
    """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        return None
    return get_parking_ach(ctx, res[0])


def get_parking_aches_by_bids(ctx: Context, bids: list) -> List[ParkingAch]:
    """
    通过 bids 获取停车场成果
    """
    if len(bids) == 0:
        return []
    qry = f"""
        select id from {ctx.park_ach_tab} where bid in ({join_str(bids)}) and bid_status = 'effected'
        """
    ids = ctx.poi_db.get_values(qry)
    return get_parking_aches(ctx, ids)


def get_parking_ach_by_ids(ctx: Context, park_ids: list) -> List[ParkingAch]:
    """
    通过停车场 id 获取停车场成果
    """
    if len(park_ids) == 0:
        return []
    ids_chunks = array_chunk(park_ids, 500)
    park_aches = []
    for an_ids_chunk in ids_chunks:
        park_aches += get_parking_aches(ctx, an_ids_chunk)
    return park_aches


def get_parking_aches(ctx: Context, park_ids: list) -> List[ParkingAch]:
    """
    获取停车场成果
    """
    if len(park_ids) == 0:
        return []

    qry = f"""
        select face_id, bid, name, address, st_astext(geom) geom, batch, 
        st_astext(central_line) central_line, parent_bid, task_id, id, remark, remove_bids, 
        source, info_id, status, city, check_memo, online_num, checker, type, memo, created_at, 
        road_relation_strategy, road_relation_manual 
        from {ctx.park_ach_tab} where id in ({join_int(park_ids)}) and bid_status = 'effected' 
    """
    res = ctx.poi_db.fetchall(qry)

    if res is None or not res:
        return []
    aches = []
    for item in res:
        parking = ParkingAch(
            face_id=item[0],
            bid=item[1],
            name=item[2],
            address=item[3],
            geom=item[4],
            batch=item[5],
            central_line=item[6],
            parent_bid=item[7],
            task_id=item[8],
            park_id=item[9],
            remark=item[10],
            remove_bids=item[11],
            source=item[12],
            info_id=item[13],
            status=item[14],
            city=item[15],
            check_memo=item[16],
            online_num=item[17],
            checker=item[18],
            type=item[19],
            memo=item[20],
            created_at=item[21],
            road_relation_strategy=item[22],
            road_relation_manual=item[23],
        )
        point_gcj = parking.polygon.representative_point().wkt
        point_mct = gcj2bdmc(point_gcj)
        point_geo = shapely.wkt.loads(point_mct)

        parking.point_x = point_geo.x
        parking.point_y = point_geo.y
        aches.append(parking)
    return aches


@dataclasses.dataclass
class OnlineParking:
    """
    线上停车场
    """
    bid: str
    geom: str
    central_line: str
    point_x: float
    point_y: float
    premise_parking_lot: int  # 精准车位数
    vague_parking_lot: int  # 模糊车位数
    parking_smart_tp: int
    parking_smart_lot: int
    open_limit_new: int  # 开放属性
    fee: dict
    parent_id: str
    name: str
    address: str
    park_spec: int
    status: int

    def get_need_extend_and_not_empty(self) -> dict:
        """
        获取需要继承，且不为空的要素
        """
        extend = {}
        if self.premise_parking_lot > 0:
            extend['premise_parking_lot'] = str(self.premise_parking_lot)
        if self.vague_parking_lot > 0:
            extend['vague_parking_lot'] = str(self.vague_parking_lot)
        if self.parking_smart_tp > 0:
            extend['parking_smart_tp'] = str(self.parking_smart_tp)
        if self.parking_smart_lot > 0:
            extend['parking_smart_lot'] = str(self.parking_smart_lot)
        if self.parent_id not in ['0', '']:
            extend['parent_id'] = self.parent_id
        if self.fee is not None and len(self.fee) > 0:
            extend['fee'] = self.fee
        extend['open_limit_new'] = self.open_limit_new
        return extend

    def get_values(self, keys: list) -> dict:
        """
        根据值获取 values
        """
        values = {}
        if 'premise_parking_lot' in keys:
            values['premise_parking_lot'] = str(self.premise_parking_lot)
        if 'vague_parking_lot' in keys:
            values['vague_parking_lot'] = str(self.vague_parking_lot)
        if 'parking_smart_tp' in keys:
            values['parking_smart_tp'] = str(self.parking_smart_tp)
        if 'parking_smart_lot' in keys:
            values['parking_smart_lot'] = str(self.parking_smart_lot)
        if 'parent_id' in keys:
            values['parent_id'] = self.parent_id
        if 'fee' in keys:
            values['fee'] = self.fee
        if 'open_limit_new' in keys:
            values['open_limit_new'] = self.open_limit_new
        return values


def get_online_park(ctx: Context, bid: str) -> Union[OnlineParking, None]:
    """
    获取线上停车场
    """
    qry = (f"select bid, st_astext(area) geom, point_x, point_y, COALESCE(premise_parking_lot, 0), "
           f"COALESCE(vague_parking_lot, 0) , COALESCE(parking_smart_tp, 0), "
           f"COALESCE(parking_smart_lot, 0), fee, COALESCE(open_limit_new, 0), "
           f"parent_id, st_astext(central_line) central_line, name, address, park_spec, status "
           f"from {ctx.park_tab} where bid = '{bid}'")
    res = ctx.back_db.fetchone(qry)

    if res is None or not res:
        return None
    return OnlineParking(
        bid=res[0],
        geom=res[1] if res[1] is not None else 'POLYGON EMPTY',
        point_x=res[2],
        point_y=res[3],
        premise_parking_lot=res[4],
        vague_parking_lot=res[5],
        parking_smart_tp=res[6],
        parking_smart_lot=res[7],
        fee=res[8],
        open_limit_new=res[9],
        parent_id=res[10],
        central_line=res[11] if res[11] is not None else 'LINESTRING EMPTY',
        name=res[12],
        address=res[13],
        park_spec=res[14],
        status=res[15],
    )


class AccessAchStatus:
    """
    出入口状态
    """
    READY_ONLINE = 'READY_ONLINE'  # 待上线
    READY_OFFLINE = 'READY_OFFLINE'  # 待下线
    ALREADY_ONLINE = 'ALREADY_ONLINE'  # 已上线
    ALREADY_OFFLINE = 'ALREADY_OFFLINE'  # 已下线
    CHECK_FAILED = 'CHECK_FAILED'

    @staticmethod
    def get_need_statuses() -> list:
        """
        获取需要推送的状态
        """
        return [AccessAchStatus.READY_ONLINE, AccessAchStatus.READY_OFFLINE]


@dataclasses.dataclass
class RoadRelation:
    """
    道路关联关系
    """
    point: str
    link_id: str  # 短 link_id
    orientation: int
    type: int


@dataclasses.dataclass
class NodeRelation(RoadRelation):
    """
    关联的是大门
    """
    node_id: str

    @staticmethod
    def from_dict(relation: dict) -> 'NodeRelation':
        """转换为字典"""
        return NodeRelation(
            point=relation['point'],
            link_id=relation['link_id'],
            orientation=relation['orientation'],
            type=relation['type'],
            node_id=relation['node_id'],
        )


@dataclasses.dataclass
class LinkRelation(RoadRelation):
    """
    关联的是 link
    """

    @staticmethod
    def from_dict(relation: dict) -> 'LinkRelation':
        """转换为字典"""
        return LinkRelation(
            point=relation['point'],
            link_id=relation['link_id'],
            orientation=relation['orientation'],
            type=relation['type'],
        )


@dataclasses.dataclass
class AccessRoadRelation:
    """
    出入口的道路关联关系
    """
    link_info: List[RoadRelation]

    @staticmethod
    def from_dict(road_relation: dict) -> 'AccessRoadRelation':
        """转换为字典"""
        if len(road_relation) == 0 or 'link_info' not in road_relation:
            return AccessRoadRelation(link_info=[])
        relations = []
        for item in road_relation['link_info']:
            if item['type'] == 1:
                relations.append(NodeRelation.from_dict(item))
            elif item['type'] == 2:
                relations.append(LinkRelation.from_dict(item))
            else:
                raise Exception(f"{road_relation} 的 type 不符合预期")
        return AccessRoadRelation(link_info=relations)

    def get_short_node_ids(self) -> list:
        """
        获取短 node_ids
        """
        node_ids = []
        for relation in self.link_info:
            if isinstance(relation, NodeRelation):
                node_ids.append(relation.node_id)
        return list(set(node_ids))

    def get_short_link_ids(self) -> list:
        """
        获取短的 link_ids
        """
        link_ids = []
        for relation in self.link_info:
            link_ids.append(relation.link_id)
        return list(set(link_ids))

    def get_link_relation_short_link_ids(self) -> list:
        """
        获取绑定 link 的 link_ids
        """
        link_ids = []
        for relation in self.link_info:
            if isinstance(relation, LinkRelation):
                link_ids.append(relation.link_id)
        return link_ids

    def change2entry(self) -> bool:
        """
        改变成入口
        如果有入口了，那么就不再处理
        """
        _relation = None

        for relation in self.link_info:
            if isinstance(relation, LinkRelation):
                continue
            if relation.orientation == 1:
                return False # 已经有入口，直接返回
            _relation = relation  # 记录最后一个可能的出口

        if _relation is not None:
            _relation.orientation = 1  # 将其改为入口
            return True
        return False


@dataclasses.dataclass
class AccessAch:
    """
    出入口成果数据
    """
    access_id: str
    face_id: str
    bid: str
    parent_bid: str
    name: str
    address: str
    geom: str
    road_relation: dict
    status: str
    check_memo: str
    created_at: str
    scene: str
    pic_urls: list
    checker: str
    memo: str
    point_x = 0
    point_y = 0
    manual_access_id = 0  # 人工作业出入口 id

    def __post_init__(self):
        self.access_road_relation = AccessRoadRelation.from_dict(self.road_relation)
        self.check_memo = self.check_memo if self.check_memo is not None else ''

    def need_push(self) -> bool:
        """
        是否需要推送
        """
        return self.status in AccessAchStatus.get_need_statuses()

    def need_online(self) -> bool:
        """
        需要上线
        """
        return self.status == AccessAchStatus.READY_ONLINE

    def need_offline(self) -> bool:
        """
        需要下线
        """
        return self.status == AccessAchStatus.READY_OFFLINE

    def get_short_node_ids(self) -> list:
        """
        获取 short_node_ids
        """
        return self.access_road_relation.get_short_node_ids()

    def get_short_link_ids(self) -> list:
        """
        获取 short_link_ids
        """
        return self.access_road_relation.get_short_link_ids()

    def get_link_relation_short_link_ids(self) -> list:
        """获取绑定 link 的 link_ids"""
        return self.access_road_relation.get_link_relation_short_link_ids()

    def checker_is_online(self) -> bool:
        """
        是否是线上质检
        """
        return self.checker == 'online'


def get_park_accesses(ctx: Context, park_id: int) -> List[AccessAch]:
    """
    获取停车场下的所有出入口
    """
    where = f"park_id > 0 and park_id = {park_id}"
    return _get_accesses(ctx, where)


def get_park_accesses_by_bid(ctx: Context, park_bid: str) -> List[AccessAch]:
    """
    根据停车场 bid 获取所有出入口
    """
    where = f"parent_bid = '{park_bid}'"
    return _get_accesses(ctx, where)


def get_access(ctx: Context, access_id: int) -> Union[AccessAch, None]:
    """
    通过 id 获取出入口
    """
    where = f"id = {access_id}"
    aches = _get_accesses(ctx, where)
    if len(aches) == 0:
        return None
    return aches[0]


def get_access_by_bid(ctx: Context, bid: str) -> Union[AccessAch, None]:
    """
    通过 bid 获取出入口
    """
    where = f"bid = '{bid}'"
    aches = _get_accesses(ctx, where)
    if len(aches) == 0:
        return None
    return aches[0]


def get_accesses_by_ids(ctx: Context, access_ids: list) -> List[AccessAch]:
    """
    通过 id 获取出入口
    """
    if len(access_ids) == 0:
        return []
    where = f" id in ({join_int(access_ids)})"
    return _get_accesses(ctx, where)


def _get_accesses(ctx: Context, where: str) -> List[AccessAch]:
    qry = f"""
    select id, face_id, bid, parent_bid, name, address, road_relation, 
        st_astext(geom) geom, status, check_memo, created_at, scene, pic_urls, checker, memo 
    from {ctx.access_ach_tab} 
    where {where} 
    """
    res = ctx.poi_db.fetchall(qry)

    if res is None or not res:
        return []
    accesses = []
    for item in res:
        access = AccessAch(access_id=str(item[0]), face_id=item[1], bid=item[2], parent_bid=item[3], name=item[4],
                           address=item[5], road_relation=item[6], geom=item[7], status=item[8], check_memo=item[9],
                           created_at=item[10], scene=item[11], pic_urls=list(item[12]), checker=item[13],
                           memo=item[14],
                           )

        point_mct = gcj2bdmc(access.geom)
        point_geo = shapely.wkt.loads(point_mct)
        access.point_x = point_geo.x
        access.point_y = point_geo.y

        accesses.append(access)
    return accesses


@dataclasses.dataclass
class OnlineAccess:
    """
    线上出入口
    """
    bid: str
    point_x: float
    point_y: float
    road_relation: dict
    name: str
    address: str
    status: str


def get_online_access(ctx: Context, bid: str) -> Union[OnlineAccess, None]:
    """
    获取线上出入口
    """
    qry = f"select bid, point_x, point_y, name, address, road_relation, status from {ctx.park_tab} where bid = '{bid}'"
    res = ctx.back_db.fetchone(qry)

    if res is None or not res:
        return None
    return OnlineAccess(
        bid=res[0],
        point_x=res[1],
        point_y=res[2],
        name=res[3],
        address=res[4],
        road_relation=res[5],
        status=res[6],
    )


def get_online_park_access_bids(ctx: Context, park_bid: str) -> list:
    """
    获取停车场下的所有出入口
    """
    qry = f"select bid from {ctx.park_tab} where parent_id = '{park_bid}' and status = 1"
    return ctx.back_db.get_values(qry)


def get_online(ctx: Context, bid: str, fields: list) -> dict:
    """
    获取线上数据
    """
    qry = f"select {','.join(fields)} from {ctx.park_tab} where bid = '{bid}'"
    res = ctx.back_db.fetchone(qry)
    if not res:
        return {}
    data = {}
    for idx, key in enumerate(fields):
        data[key] = res[idx]
    return data


def update_park_ach_status(ctx: Context, park_id: int, status: str):
    """
    更新停车场成果状态
    """
    qry = f"""
            update {ctx.park_ach_tab} set 
                status = '{status}' 
                where id = {park_id}
            """
    return ctx.poi_db.execute(qry)


def _park_located_main_city_area(ctx: Context, park_id: int) -> bool:
    """
    停车场位于主城区
    """
    file = '/home/<USER>/zhengcheng/aoiMl/output/src/parking/storefront/flow/tmp/bj_jianchengqu_01.txt'
    _wkt = open(file).read()

    qry = f"""
    select count(*) from {ctx.park_ach_tab} where id = {park_id} and st_intersects(geom, st_geomfromtext('{_wkt}', 4326))
    """
    res = ctx.poi_db.fetchone(qry)
    return res[0] > 0


def get_park_ready_status(ctx: Context, park_id: int) -> str:
    """
    获取停车场面准备好的状态
    """
    status = ParkingAchStatus.INITED_BID
    return status
    if not _park_located_main_city_area(ctx, park_id):
        status = 'READY_PAUSE'
    return status


def update_road_relation(ctx: Context, aid: int, road_relation: dict, remark: str) -> int:
    """
    更新 road_relation
    """
    qry = f"""
    update {ctx.access_ach_tab} 
        set road_relation = '{json.dumps(road_relation, ensure_ascii=False)}', 
        remark = remark || '{remark}' 
    where id = {aid}
    """
    return ctx.poi_db.execute(qry)


@dataclasses.dataclass
class NodeRoadRelation:
    """
    node 关联 道路
    """
    point: str
    link_id: str
    node_id: str
    orientation: int
    type: int = 1

    def format(self) -> dict:
        """
        格式化
        """
        return {
            'link_info': [
                {
                    'type': self.type,
                    'point': self.point,
                    'link_id': self.link_id,
                    'node_id': self.node_id,
                    'orientation': self.orientation,
                },
            ],
        }

    @staticmethod
    def from_dict(road_relation: dict) -> 'NodeRoadRelation':
        """
        通过字典生成
        """
        type1 = road_relation['link_info'][0]

        if len(type1) != 5 or type1['type'] != 1:
            raise Exception(f"{road_relation} 不符合预期")

        return NodeRoadRelation(
            type=type1['type'],
            point=type1['point'],
            link_id=type1['link_id'],
            node_id=type1['node_id'],
            orientation=type1['orientation'],
        )


@dataclasses.dataclass
class LinkRoadRelation:
    """
    link 关联 道路
    """
    point: str
    link_id: str
    orientation: int
    type: int = 2

    def format(self) -> dict:
        """
        格式化
        """
        return {
            'link_info': [
                {
                    'type': self.type,
                    'point': self.point,
                    'link_id': self.link_id,
                    'orientation': self.orientation,
                },
            ],
        }

    @staticmethod
    def from_dict(road_relation: dict) -> 'LinkRoadRelation':
        """
        通过字典生成
        """
        type2 = road_relation['link_info'][0]

        if len(type2) != 4 or type2['type'] != 2:
            raise Exception(f"{road_relation} 不符合预期")

        return LinkRoadRelation(
            type=type2['type'],
            point=type2['point'],
            link_id=type2['link_id'],
            orientation=type2['orientation'],
        )


def is_self_ach(ctx: Context, bid: str) -> bool:
    """
    是否自有成果
    """
    qry = f"""
    select id from {ctx.park_ach_tab} 
    where bid = '{bid}' and bid_status = 'effected'
    """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        return False
    return True


def get_parks_generate(ctx: Context, where: str):
    """
    获取停车场
    """
    qry = f"""
        select id from {ctx.park_ach_tab} a 
        where {where}  
    """
    ids = ctx.poi_db.get_values(qry)

    id_chunks = array_chunk(ids, 200)
    for _id_chunk in id_chunks:
        yield get_parking_ach_by_ids(ctx, _id_chunk)


def add_park_memo(ctx: Context, park_id: int, memo: str):
    """
    停车场增加备注信息
    """
    qry = f"""
    update {ctx.park_ach_tab} set memo = '{memo}', memo_time = now() 
    where id = {park_id}
    """
    return ctx.poi_db.execute(qry)


def add_access_memo(ctx: Context, access_id: int, memo: str):
    """
    给出入口增加备注
    """
    qry = f"""
        update {ctx.access_ach_tab} set memo = '{memo}', memo_time = now() 
        where id = {access_id}
        """
    return ctx.poi_db.execute(qry)


def remark_park(ctx: Context, park_id: int, key: str, val):
    """
    添加备注
    """
    qry = f"""
    update {ctx.park_ach_tab} 
    set remark = jsonb_set(remark, '{{{key}}}', '"{val}"'::jsonb) 
    where id = {park_id}
    """
    return ctx.poi_db.execute(qry)


def change_to_entry(ctx: Context, access: AccessAch) -> int:
    """
    出口变入口
    """
    if not access.access_road_relation.change2entry():
        print(f"{access.access_id} 出入口属性不需要改变")
        return 0
    new_relation = dataclasses.asdict(access.access_road_relation)
    qry = f"""
    update {ctx.access_ach_tab} 
    set road_relation = '{handle_apostrophe(json.dumps(new_relation, ensure_ascii=False))}' 
    where id = {access.access_id}
    """
    return ctx.poi_db.execute(qry)
