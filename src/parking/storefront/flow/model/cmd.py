"""
推送命令
"""
import json
from typing import Union, List

import dataclasses

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.db import handle_apostrophe

ROLLBACK_CMD_PREF = 'ROLLBACK'  # 回滚命令前缀


class CmdStatus:
    """
    命令状态
    """
    INIT = 'INIT'
    READY = 'READY'  # 就绪待运行
    RAN = 'RAN'  # 已运行, 但可能还需要监控回掉等
    END = 'END'  # 结束
    CANCEL = 'CANCEL'  # 已取消，还未执行的回滚取消执行了

    @staticmethod
    def not_ran_statuses() -> list:
        """
        还没有执行的状态
        """
        return [CmdStatus.INIT, CmdStatus.READY]

    @staticmethod
    def get_end_statuses() -> list:
        """
        获取结束的状态
        """
        return [CmdStatus.END, CmdStatus.CANCEL]


@dataclasses.dataclass
class CmdReady:
    """
    判断是否已准备好
    """
    fun: str
    arg: dict

    @staticmethod
    def from_dict(data: dict) -> Union['CmdReady', None]:
        """转换为字典"""
        if data is None:
            return None
        return CmdReady(
            fun=data['fun'],
            arg=data['arg'],
        )


@dataclasses.dataclass
class CmdArg:
    """
    推送 arg 参数
    """
    request: dict  # 请求参数
    type: str  # 推送类型，2（上下线），3（更新），1（新增）；其中 1，2 下游会维护是否推送已生效
    extra: dict = None
    ready: CmdReady = None  # 若命令状态是 INIT，需要判断是否已准备好，能执行
    rollback: 'CmdArg' = None  # 若需要回滚，填写该字段
    middleware: dict = None

    @staticmethod
    def from_dict(data: dict) -> Union['CmdArg', None]:
        """转换为字典"""
        if data is None:
            return None
        return CmdArg(
            request=data['request'],
            type=data['type'],
            extra=data['extra'],
            ready=CmdReady.from_dict(data['ready']) if 'ready' in data else None,
            rollback=CmdArg.from_dict(data['rollback']) if 'rollback' in data else None,
            middleware=data['middleware'] if 'middleware' in data else None,
        )

    def get_request_val(self, key: str, default=''):
        """
        获取 request 中的值，若没有返回 default
        """
        return self.request[key] if key in self.request else default

    def get_extra_val(self, key: str, default=''):
        """
        获取额外信息中的值，没有返回 default
        """
        return self.extra[key] if self.extra is not None and key in self.extra else default

    def set_extra_val(self, key: str, val):
        """
        保存额外的值
        """
        if self.extra is None:
            self.extra = {}
        self.extra[key] = val

    def req_to_json(self) -> str:
        """
        请求参数转换为 json 字符串
        """
        return json.dumps(self.request, ensure_ascii=False)

    def get_after_middlewares(self, name: str) -> list:
        """
        获取后处理中间件
        """
        if self.middleware is None or name not in self.middleware or 'after' not in self.middleware[name]:
            return []
        return self.middleware[name]['after']

    def add_after_middlewares(self, name: str, middlewares: list):
        """
        添加后处理中间件
        """
        if self.middleware is None:
            self.middleware = {}
        if name not in self.middleware:
            self.middleware[name] = {}
        if 'after' not in self.middleware[name]:
            self.middleware[name]['after'] = []
        self.middleware[name]['after'] += middlewares


@dataclasses.dataclass
class Cmd:
    """
    停车场推送命令
    """
    id: int
    match_id: int
    guid: str
    source_id: str
    type: str
    status: str
    args: CmdArg
    memo: str = ''
    is_rollback: bool = False  # 命令是否被回滚了

    @staticmethod
    def from_dict(data: dict) -> 'Cmd':
        """
        从字典中转换
        """
        return Cmd(
            id=data['id'] if 'id' in data else 0,
            match_id=data['match_id'],
            guid=data['guid'],
            source_id=data['source_id'],
            type=data['type'],
            status=data['status'],
            args=data['args'],
            memo=data['memo'] if 'memo' in data else '',
        )

    def to_dict(self) -> dict:
        """
        转换为字典
        """
        resp = dataclasses.asdict(self)
        del resp['is_rollback']  # 不对外输出
        return resp

    def is_end(self) -> bool:
        """
        命令是否已结束
        """
        return self.status in CmdStatus.get_end_statuses()

    def mark_rollback(self, rollbacked):
        """
        标记命令已回滚
        """
        if self.is_rollback_cmd():  # 是回滚命令，不能被回滚
            rollbacked = False
        self.is_rollback = rollbacked

    def is_rollback_cmd(self) -> bool:
        """
        是否是回滚命令
        """
        return ROLLBACK_CMD_PREF in self.type

    def been_rollback(self) -> bool:
        """
        命令被回滚了，返回 True
        """
        return self.is_rollback

    def not_ran(self) -> bool:
        """
        没有执行过
        """
        return self.status in CmdStatus.not_ran_statuses()

    def about_parking(self) -> bool:
        """
        是关于停车场的命令，是返回 True
        """
        return 'PARKING' in self.type

    def about_access(self) -> bool:
        """
        关于出入口的命令
        """
        return 'ACCESS' in self.type

    def get_rollback_cmd(self) -> Union[dict, None]:
        """
        是否有提前生成好的回滚命令, 没有返回 None
        """
        if self.args.rollback is None:
            return None
        rollback_arg = self.args.rollback
        status = CmdStatus.READY  # 可以直接运行
        if rollback_arg.ready is not None:
            status = CmdStatus.INIT  # 需要等待就绪

        return {
            'match_id': self.match_id,
            'guid': self.guid,
            'type': f"ROLLBACK_{self.type}",
            'source_id': rollback_arg.get_request_val('source_id'),
            'status': status,
            'args': dataclasses.asdict(rollback_arg)
        }

    def park_spec(self, value: int):
        """
        设置是否精准停车场
        """
        self.args.request['park_spec'] = value

    def get_after_middlewares(self, name: str) -> list:
        """
        获取后处理中间件
        """
        return self.args.get_after_middlewares(name)

    def add_after_middlewares(self, name: str, middlewares: list):
        """
        添加或处理中间件
        """
        self.args.add_after_middlewares(name, middlewares)

    def set_ready(self, ready: CmdReady):
        """
        配置需要状态，状态调整为 INIT
        """
        self.args.ready = ready
        self.status = CmdStatus.INIT


class CmdType:
    """
    停车场推送命令类型
    """
    ADD_PARKING = 'ADD_PARKING'
    ADD_ACCESS = 'ADD_ACCESS'
    REMOVE_PARKING = 'REMOVE_PARKING'
    REMOVE_ACCESS = 'REMOVE_ACCESS'
    UPDATE_PARKING = 'UPDATE_PARKING'
    UPDATE_ACCESS = 'UPDATE_ACCESS'
    ROLLBACK_ADD_PARKING = f'{ROLLBACK_CMD_PREF}_ADD_PARKING'  # 回滚上线停车场
    ROLLBACK_ADD_ACCESS = f'{ROLLBACK_CMD_PREF}_ADD_ACCESS'  # 回滚上线停车场
    ROLLBACK_REMOVE_PARKING = f'{ROLLBACK_CMD_PREF}_REMOVE_PARKING'  # 回滚上线出人口
    ROLLBACK_REMOVE_ACCESS = f'{ROLLBACK_CMD_PREF}_REMOVE_ACCESS'  # 回滚下线出入口
    ROLLBACK_UPDATE_PARKING = f'{ROLLBACK_CMD_PREF}_UPDATE_PARKING'  # 更新回滚
    ROLLBACK_UPDATE_ACCESS = f'{ROLLBACK_CMD_PREF}_UPDATE_ACCESS'  # 更新回滚

    @staticmethod
    def get_rollback_type(c_type) -> str:
        """
        获取回滚命令类型
        """
        if ROLLBACK_CMD_PREF not in c_type:
            return f'{ROLLBACK_CMD_PREF}_{c_type}'


def add_commands(ctx: Context, cmds: List[Cmd]) -> int:
    """
    新增命令
    """
    data = [dataclasses.asdict(a_cmd) for a_cmd in cmds]
    return _add_commands(ctx, data)


def _add_commands(ctx: Context, commands: list):
    """
    新增命令
    当前操作不提交事务，统一由调用方控制事务是否提交
    """
    if len(commands) == 0:
        return
    qry = f"insert into {ctx.park_match_command_tab} (match_id, guid, type, source_id, status, args, memo) values "
    val = []
    for cmd in commands:
        val.append(
            f"""
            ({cmd['match_id']}, '{cmd['guid']}', '{cmd['type']}', '{cmd['source_id']}', '{cmd['status']}', 
            '{handle_apostrophe(json.dumps(cmd['args'], ensure_ascii=False))}', '{cmd['memo']}')
            """
        )
    qry = f"{qry} {','.join(val)}"
    return ctx.poi_db.execute(qry)


def get_match_commands(ctx: Context, match_id: int) -> List[Cmd]:
    """
    获取某一批命令
    """
    where = f"match_id = {match_id}"
    return _get_commands_by_where(ctx, where)


def get_match_need_run_commands(ctx: Context, match_id: int) -> List[Cmd]:
    """
    获取一批需要运行的命令
    """
    status_str = ','.join([f"'{x}'" for x in CmdStatus.get_end_statuses()])
    where = f"match_id = {match_id} and status not in ({status_str})"
    return _get_commands_by_where(ctx, where)


def _get_commands_by_where(ctx: Context, where: str) -> List[Cmd]:
    qry = f"""
    select id, match_id, guid, source_id, type, status, args from {ctx.park_match_command_tab} where {where}
    """
    res = ctx.poi_db.fetchall(qry)
    if res is None or not res:
        return []

    commands = []
    for item in res:
        commands.append(Cmd(
            id=item[0],
            match_id=item[1],
            guid=item[2],
            source_id=item[3],
            type=item[4],
            status=item[5],
            args=CmdArg.from_dict(item[6]),
        ))
    return commands


def get_wait_run_command_match_ids(ctx: Context) -> list:
    """
    获取需要运行的匹配号
    """
    end_statuses = CmdStatus.get_end_statuses()
    end_status_str = ','.join([f"'{status}'" for status in end_statuses])

    # fixme 数据量大了，可能性能不咋理想
    qry = f"select match_id from {ctx.park_match_command_tab} where status not in ({end_status_str}) group by match_id"
    # qry = f"select match_id from {ctx.park_match_command_tab} where match_id = 52 group by match_id"
    return ctx.poi_db.get_values(qry)