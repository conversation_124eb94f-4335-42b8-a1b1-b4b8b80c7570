"""
干预
"""
from typing import List

import dataclasses

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.db import handle_apostrophe


class InterventionType:
    """
    干预类型
    """
    PARK_OFFLINE = 'PARK_OFFLINE'  # 停车场下线


class InterventionStatus:
    """
    干预状态
    """
    INIT = 'INIT'
    ING = 'ING'
    DONE = 'DONE'


def add_park_offline(ctx: Context, bid: str, user: str, reason: str):
    """
    新增停车线下线干预
    """
    qry = f"""
    insert into {ctx.intervention_tab} (bid, type, username, reason) 
    values ('{bid}', '{InterventionType.PARK_OFFLINE}', '{user}', '{handle_apostrophe(reason)}')
    """
    return ctx.poi_db.insert_return_id(qry)


@dataclasses.dataclass
class Intervention:
    """干预"""
    id: int
    bid: str
    type: str
    reason: str
    username: str
    remark: str
    created_at: str
    intervention_time: str
    status: str


def get_wait_offline_parks(ctx: Context) -> List[Intervention]:
    """
    获取待下线干预的停车场
    """
    where = f"type = '{InterventionType.PARK_OFFLINE}' and status = 'INIT'"
    return _get_interventions(ctx, where)


def get_bid_interventions(ctx: Context, bid: str) -> List[Intervention]:
    """
    获取某个 bid 干预记录
    """
    where = f"bid = '{bid}' order by id desc"
    return _get_interventions(ctx, where)


def _get_interventions(ctx, where):
    qry = f"""
    select id, bid, username, reason, type, remark, created_at, intervente_time, status 
    from {ctx.intervention_tab} 
    where {where}
    """
    res = ctx.poi_db.fetchall(qry)
    waits = []
    for item in res:
        waits.append(Intervention(
            id=item[0],
            bid=item[1],
            username=item[2],
            reason=item[3],
            type=item[4],
            remark=item[5],
            created_at=item[6],
            intervention_time=item[7],
            status=item[8],
        ))
    return waits


def handle_fail(ctx: Context, iid: int, remark: str):
    """
    处理失败
    """
    qry = f"""
    update {ctx.intervention_tab} 
    set status = 'FAIL', remark = '{handle_apostrophe(remark)}' 
    where id = {iid} 
    """
    return ctx.poi_db.execute(qry)


def handling(ctx: Context, iid: int, remark: str):
    """
    标记干预中
    """
    qry = f"""
        update {ctx.intervention_tab} 
        set status = '{InterventionStatus.ING}', remark = '{handle_apostrophe(remark)}', intervente_time = now() 
        where id = {iid} 
        """
    return ctx.poi_db.execute(qry)

