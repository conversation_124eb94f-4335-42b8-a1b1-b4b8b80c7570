"""
有两层逻辑
第一是整体，用同一个匹配id推送的，那么相当于一个整体，支持整体推送，整体回滚
第二是存放匹配关系，因为上某些数据，需要下某些数据
"""
import dataclasses


from src.parking.storefront.flow.context import Context


class ParkMatchStatus:
    """
    停车场匹配关系状态
    初始化 -> 推送上线 -> 推送下线 -> 回滚（可能）-> 结束
    """
    INIT = 'INIT'
    ONLINEING = 'ONLINEING'
    ONLINE = 'ONLINE'  # 已上线
    OFFLINEING = 'OFFLINEING'
    OFFLINE = 'OFFLINE'  # 已下线
    ROLLBACK = 'ROLLBACK'  # 需要回滚，还没有回滚
    ROLLBACKING = 'ROLLBACKING'  # 回滚中
    END = 'END'  # 有回滚的结束状态
    UPDATE = 'UPDATE'  # 更新

    @staticmethod
    def get_end_statuses() -> list:
        """
        获取结束的状态
        """
        return [
            ParkMatchStatus.OFFLINE,  # 正常流程结束状态
            ParkMatchStatus.END,  # 有回滚的结束状态
            ParkMatchStatus.UPDATE,  # 常规更新（非上线/下线），
        ]

    @staticmethod
    def get_need_gen_cmds_statuses() -> list:
        """
        获取需要生成命令的状态
        """
        return [
            ParkMatchStatus.INIT,  # 需要生成上线命令
            ParkMatchStatus.ONLINE,  # 上线完成，需要生成下线
            ParkMatchStatus.ROLLBACK,  # 需要生成回滚
        ]

    @staticmethod
    def get_belong_rollback_status() -> list:
        """
        获取属于回滚的状态
        """
        return [
            ParkMatchStatus.ROLLBACK,
            ParkMatchStatus.ROLLBACKING,
            ParkMatchStatus.END,
        ]


@dataclasses.dataclass
class ParkMatch:
    """
    停车场匹配关系
    """
    id: int
    status: str
    old: list
    new: list
    batch: str
    task_id: int

    def by_update_online(self) -> bool:
        """
        走更新上线
        """
        return len(self.old) == 1 and len(self.new) == 1

    def need_offline_bids(self) -> list:
        """
        需要下线的 bid
        """
        if self.by_update_online():
            return []
        return self.old


def gen_blank(ctx: Context, status: str) -> int:
    """
    生成一个空白的批次
    """
    qry = f"""
    insert into {ctx.park_match_tab} (batch, strategy, old, new, status) values 
    ('', '', Array[]::varchar[], Array[]::varchar[], '{status}')
    """
    return ctx.poi_db.insert_return_id(qry)



