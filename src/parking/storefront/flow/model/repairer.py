"""
修复
"""
import json
from datetime import datetime
from typing import List

import dataclasses

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.model import ach


class RepairStatus:
    """
    状态
    """
    INIT = 'INIT'
    SUCCESS = 'SUCCESS'
    FAILED = 'FAILED'
    ING = 'ING'


class RepairReason:
    """
    原因
    """
    NodeValid = 'NodeValid'
    LinkValid = 'LinkValid'
    CoverLD = 'CoverLD'
    ExistsSharpAngle = 'ExistsSharpAngle'

    Gate_Pass = 'Gate_Pass'  # 大门通行性
    Link_Block = 'Link_Block'  # 阻断
    Node_Block = 'Node_Block'  # 路障


@dataclasses.dataclass
class Repair:
    """
    修复
    """
    id: int
    prev_id: str
    prev_tab: str
    user_name: str
    user_id: str
    reason: str
    status: str
    result: dict


class RepairAchContext:
    """
    修复成果上下文
    """

    def __init__(self, ctx: Context, repair: Repair):
        self.ctx = ctx
        self.repair = repair

    def as_access_ach(self) -> ach.AccessAch:
        """
        转换为出入口成果
        """
        if self.repair.prev_tab != self.ctx.access_ach_tab:
            raise Exception(f"{self.repair.id}, {self.repair.prev_tab} 不是出入口")
        return ach.get_access(self.ctx, int(self.repair.prev_id))

    def as_park_ach(self) -> ach.ParkingAch:
        """
        转换为停车场成果
        """
        if self.repair.prev_tab != self.ctx.park_ach_tab:
            raise Exception(f"{self.repair.id}, {self.repair.prev_tab} 不是停车场")
        return ach.get_parking_ach(self.ctx, int(self.repair.prev_id))


def has_exists(ctx: Context, prev_id: str, prev_tab: str, reason: str, cooling_time: int = 0) -> bool:
    """
    是否已经存在，INIT 只能存在一次
    """
    qry = f"""
    select id 
    from {ctx.repair_tab} 
    where prev_id = '{prev_id}' and prev_tab = '{prev_tab}' and reason = '{reason}' 
        and status in ('{RepairStatus.INIT}', '{RepairStatus.SUCCESS}')
    """
    res = ctx.poi_db.fetchone(qry)
    if len(res) > 0:
        return True

    # 若有失败，那么过了冷却期的才能继续投
    qry = f"""
        select id, updated_at 
        from {ctx.repair_tab} 
        where prev_id = '{prev_id}' and prev_tab = '{prev_tab}' and reason = '{reason}' 
            and status in ('{RepairStatus.FAILED}') order by id desc 
        """
    res = ctx.poi_db.fetchone(qry)
    if len(res) == 0 or cooling_time == '':
        # 不存在，或者没有冷却期
        return False

    current_time = datetime.now()
    updated_time = datetime.strptime(res[1], "%Y-%m-%d %H:%M:%S")
    return (current_time - updated_time).total_seconds() > cooling_time


def add_wait_repaired(ctx: Context, prev_id: str, prev_tab: str, reason: str) -> int:
    """
    添加待修复
    """
    qry = f"""
    insert into {ctx.repair_tab} (prev_id, prev_tab, reason, status, user_name, user_id) 
    values ('{prev_id}', '{prev_tab}', '{reason}', '{RepairStatus.INIT}', '', '') 
    """
    return ctx.poi_db.insert_return_id(qry)


def update_status_result(ctx: Context, rp_id: int, status: str, result: dict):
    """
    更新状态，结果
    """
    qry = f"""
        update {ctx.repair_tab} 
        set status = '{status}', result = '{json.dumps(result, ensure_ascii=False)}'
        where id = {rp_id}
        """
    return ctx.poi_db.execute(qry)


def repair_failed(ctx: Context, rp_id: int):
    """
    修复失败
    """
    return update_status_result(ctx, rp_id, RepairStatus.FAILED, {})


def repairing(ctx: Context, rp_id: int, user_name: str, user_id: str):
    """
    修复中
    """
    qry = f"""
        update {ctx.repair_tab} 
        set user_name = '{user_name}', user_id = '{user_id}', status = '{RepairStatus.ING}' 
        where id = {rp_id}
    """
    return ctx.poi_db.execute(qry)


def repair_success(ctx: Context, rp_id: int, result: dict):
    """
    修复成功
    """
    return update_status_result(ctx, rp_id, RepairStatus.SUCCESS, result)


def get_wait_repairs(ctx: Context, limit: int, where: str = ' 1 = 1 ') -> List[Repair]:
    """
    获取待修复的数据
    """
    qry = f"""
    select id, prev_id, prev_tab, user_id, user_name, reason, status, result 
    from {ctx.repair_tab} 
    where status = '{RepairStatus.INIT}' and ({where})
    limit {limit}
    """
    res = ctx.poi_db.fetchall(qry)

    repairs = []
    for item in res:
        repairs.append(Repair(
            id=item[0],
            prev_id=item[1],
            prev_tab=item[2],
            user_id=item[3],
            user_name=item[4],
            reason=item[5],
            status=item[6],
            result=item[7],
        ))
    return repairs


def access_is_repairing(ctx: Context, access: ach.AccessAch, reason: str) -> bool:
    """
    出入口是否在修复某个问题
    """
    qry = f"""
        select id from {ctx.repair_tab} 
--         where reason like '%{reason}%' and status in ('ING', 'DONE')
        where reason like '%{reason}%' and status in ('ING', 'INIT')
        and prev_id = '{access.access_id}' and prev_tab = '{ctx.access_ach_tab}'
        """
    res = ctx.poi_db.fetchone(qry)
    return len(res) > 0


def park_can_repair_access(ctx: Context, park: ach.ParkingAch) -> bool:
    """
    停车场能否修复出入口，能修复返回 True
    """
    if park.status not in ['CHECK_FAILED', 'REPAIRING']:
        # 口没有异常，那么不需要修复
        return False
    if park.status == 'CHECK_FAILED':
        # 口可能没有异常，那么理论上来说面不需要修复，但也可以‘修复’；若面不需要修复口，应该由外层逻辑来保障
        return True

    # 以下是修复中的逻辑
    qry = f"""
    select id from {ctx.repair_tab} 
    where prev_id = '{park.park_id}' and prev_tab = '{ctx.park_ach_tab}' 
    and reason NOT SIMILAR TO '%(Node_Block|Link_Block|Lack_Road|Gate_Pass)%' 
    and status in ('INIT', 'ING') 
    """
    res = ctx.poi_db.fetchone(qry)
    if len(res) > 0:
        # 除了阻断、路障、缺路，大门通行性 外，还有其他项在修复中，那么面不能推送
        return False
    return True

