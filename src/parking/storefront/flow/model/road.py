"""
道路相关
"""
import shapely.geometry
import shapely.ops
import shapely.wkt
from shapely.geometry import (
    Point, LineString, Polygon,
    MultiPoint, MultiLineString, MultiPolygon,
    GeometryCollection
)

from src.parking.recognition import dbutils
from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.db import join_str
from src.tools import pgsql


def get_short_node_id(ctx: Context, long_node_id: str) -> str:
    """
    获取短 node_id
    """
    if long_node_id == '':
        return ''
    qry = f"select tid from image_n where sid = '{long_node_id}'"
    node = ctx.trans_db.fetchone(qry)
    if not node:
        return ''
    return node[0]


def get_long_node_id(ctx: Context, short_node_id: str) -> str:
    """
    获取长 node_id
    """
    if short_node_id == '':
        return ''
    qry = f"select sid from image_n where tid = '{short_node_id}'"
    node = ctx.trans_db.fetchone(qry)
    if not node:
        return ''
    return node[0]


def get_long_link_id(ctx: Context, short_link_id: str) -> str:
    """
    获取长 link_id
    """
    if short_link_id == '':
        return ''
    qry = f"SELECT sid FROM image_r WHERE tid = '{short_link_id}' "
    link = ctx.trans_db.fetchone(qry)
    if not link:
        return ''
    return link[0]


def get_short_link_id(ctx: Context, long_link_id: str) -> str:
    """
    获取短 link_id
    """
    if long_link_id == '':
        return ''

    qry = f"SELECT tid FROM image_r WHERE sid = '{long_link_id}'"
    link = ctx.trans_db.fetchone(qry)
    if not link:
        return ''
    return link[0]


def get_in_link_id_by_node_id(ctx: Context, long_node_id: str):
    """
    获取 in_link_id
    """
    if long_node_id == '':
        return ''
    qry = f"select in_linkid from nav_gate where node_id = '{long_node_id}'"
    res = ctx.road_db.fetchone(qry)
    if not res:
        return ''
    return res[0]


def is_enter_gate(ctx: Context, short_node_id):
    """
    判断大门是否可入
    """
    sql = f"""
       select node_id, gate_id, passage,traversability, type, nnww_tag, type_desc from gates_semantic 
       where node_id = '{short_node_id}'
   """
    semantic_res = ctx.dest_db.fetchall(sql)
    if len(semantic_res) == 0:
        return False
    if len(semantic_res) == 2:
        semantic_res_0, semantic_res_1 = semantic_res[0], semantic_res[1]
        type_arr = [semantic_res_0[4], semantic_res_1[4]]
        if type_arr[0] == '3' and type_arr[1] == '3':
            return True
        elif '2' in type_arr and '0' in type_arr:
            return True
        return False
    type = semantic_res[0][4]
    nnww_tag = semantic_res[0][5]

    if type == "0":
        return False  # 紧急门
    else:
        if "入口" in nnww_tag:
            return True
        elif "出口" in nnww_tag:
            return False  # 出口门
        else:
            if type in ["2", "3"]:
                return True
            elif type in ["1"]:
                return False  # 出口门
    return False


def is_enter_gate_v3(ctx, short_node_id):
    """
    判断大门是否可入
    """
    sql = f"""
       select node_id, gate_id, passage,traversability, type, nnww_tag, type_desc from gates_semantic 
       where node_id = '{short_node_id}'
   """
    semantic_res = ctx.dest_db.fetchall(sql)
    if len(semantic_res) == 0:
        return 0
    if len(semantic_res) == 2:
        semantic_res_0, semantic_res_1 = semantic_res[0], semantic_res[1]
        type_arr = [semantic_res_0[4], semantic_res_1[4]]
        if type_arr[0] == "3" and type_arr[1] == "3":
            return 1
        elif "2" in type_arr and "0" in type_arr:
            return 1
        return 2
    type = semantic_res[0][4]
    nnww_tag = semantic_res[0][5]

    if type == "0":
        return 2  # 紧急门
    else:
        if "入口" in nnww_tag:
            return 1
        elif "出口" in nnww_tag:
            return 2  # 出口门
        else:
            if type in ["2", "3"]:
                return 1
            elif type in ["1"]:
                return 2  # 出口门
    return 2


def is_entry_gate_by_master_road(ctx: Context, long_node_id: str) -> int:
    """
    根据道路母库判断是否可入
    可入返回 True
    紧急大门 type字段区分
    出口大门：首先是单向大门，out_link是內部路，属于出口大门
    0 确认不了，未知
    1 出入口
    2 紧急门，出口
    """
    gates = get_gates(ctx, [long_node_id])
    types = [a_gate[3] for a_gate in gates]
    if len(gates) == 0:
        # 确认不了
        return 0
    if 0 in types:
        # 紧急门
        return 2
    if len(gates) > 1:
        # 双向大门，认为可入可出，不太准
        return 1

    # in_link 属于内部路 且 out_link 不属于内部路 说明可出；此处详细策略可参考
    # https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/CgIKhBCL9KWYDi
    out_linkid = gates[0][1]
    link_infos = get_link_infos(ctx, [out_linkid])
    if len(link_infos) == 0:
        return 0
    out_form = link_infos[0][5]
    out_forms = str(out_form).split(',')
    if '52' in out_forms:
        # out_link 属于内部路, 不知道是啥
        return 0
    in_link_id = gates[0][0]
    link_infos = get_link_infos(ctx, [in_link_id])
    if len(link_infos) == 0:
        return 0
    in_form = link_infos[0][5]
    in_forms = str(in_form).split(',')
    if '52' in in_forms:
        # 大门，进入大门的是内部路，那么认为是出口
        return 2
    return 0


def get_node_ids_from_link_id(ctx: Context, long_link_id: str):
    """
    获取link_id两端的node_id
    """
    qry = f"select s_nid, e_nid from nav_link where link_id = '{long_link_id}'"
    return ctx.road_db.fetchone(qry)


def get_long_node_infos(ctx: Context, long_node_ids: list) -> list:
    """
    获取长 node 信息
    """
    if not long_node_ids:
        return []
    qry = f"select node_id, form, st_astext(geom) from nav_node where node_id in({join_str(long_node_ids)})"
    return ctx.road_db.fetchall(qry)


def get_gates(ctx: Context, long_node_ids: list) -> list:
    """
    获取大门
    """
    if len(long_node_ids) == 0:
        return []
    qry = f"""
        select in_linkid, out_linkid, node_id, type 
        from nav_gate where node_id in ({join_str(long_node_ids)})
    """
    return ctx.road_db.fetchall(qry)


def get_link_infos(ctx: Context, long_link_ids: list) -> list:
    """
    获取 link
    """
    if len(long_link_ids) == 0:
        return []
    qry = f"""
    select link_id, dir, st_astext(geom), s_nid, e_nid, form, mesh_id 
    from nav_link where link_id in ({join_str(long_link_ids)})
    """
    return ctx.road_db.fetchall(qry)


def get_lane_boundaries(wkt: str) -> list:
    """
    获取压盖的车道级边界, 也就是 link
    """
    qry = f"""
    select lane_group_id, lane_boundary_id, st_astext(geom) geom
    from nav_lane_boundary 
    where st_intersects(geom, st_geomfromtext('{wkt}', 4326))
    """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG, qry)
    return [{
        'lane_group_id': item[0],
        'lane_boundary_id': item[1],
        'geom': item[2],
    } for item in res]


def get_lane_marking_pl(wkt: str) -> list:
    """
    获取道路标线
    """
    qry = f"""
    select lane_group_id, marking_pl_id, st_astext(geom) geom 
    from nav_lane_marking_pl 
    where st_intersects(geom, st_geomfromtext('{wkt}', 4326))
    """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG, qry)
    return [{
        'lane_group_id': item[0],
        'marking_pl_id': item[1],
        'geom': item[2],
    } for item in res]


def get_lane_link(wkt: str) -> list:
    """
    获取 LD link
    """
    qry = f"""
    select lane_link_id, lane_group_id, st_astext(geom) geom 
    from nav_lane_link 
    where st_intersects(geom, st_geomfromtext('{wkt}', 4326))
    """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG, qry)
    return [{
        'lane_link_id': item[0],
        'lane_group_id': item[1],
        'geom': item[2],
    } for item in res]


def get_ld_polygon(wkt: str) -> list:
    """
    获取相交的 LD 道路面范围
    可能有多个
    """
    fns = [
        get_lane_boundaries,  # 压盖了边界
        get_lane_marking_pl,  # 压盖了标线
        get_lane_link,  # 压盖了中心线
    ]
    infos = []
    for _fn in fns:
        infos += _fn(wkt)

    if len(infos) == 0:  # 还是没有
        return []

    bounds = []
    for idx, info in enumerate(infos):
        qry = f"""
        select lane_group_id, lane_boundary_id, st_astext(geom) geom
        from nav_lane_boundary 
        where lane_group_id = '{info['lane_group_id']}'
        """
        res = dbutils.fetch_all(pgsql.ROAD_CONFIG, qry)
        for item in res:
            bounds.append({
                'lane_group_id': item[0],
                'lane_boundary_id': item[1],
                'geom': item[2],
            })

    # 线按组分
    gid2group = {}
    for _boundary in bounds:
        _gid = _boundary['lane_group_id']
        _bid = _boundary['lane_boundary_id']
        if _gid not in gid2group:
            gid2group[_gid] = {
                'lane_group_id': _gid,
                'lane_boundary_ids': [],
                'geo': shapely.wkt.loads('POLYGON EMPTY'),
                'lane_boundaries': [],
            }

        if _bid in gid2group[_gid]['lane_boundary_ids']:
            continue

        gid2group[_gid]['lane_boundary_ids'].append(_bid)
        gid2group[_gid]['lane_boundaries'].append(_boundary['geom'])
        gid2group[_gid]['geo'] = gid2group[_gid]['geo'].union(shapely.wkt.loads(_boundary['geom']))

    resp = []
    for _group in gid2group.values():
        _item = {
            'lane_group_id': _group['lane_group_id'],
            'lane_boundary_ids': _group['lane_boundary_ids'],
        }
        try:  # 通过线点的顺序组成面，若不行，那么取凸包
            bounds = []
            for _boundary in _group['lane_boundaries']:
                _geo = shapely.wkt.loads(_boundary)
                # 和已有的边界是否有交集，若有交集那么就合并
                new_bounds = []
                for _bound in bounds:
                    if not _bound.intersects(_geo):
                        new_bounds.append(_bound)
                        continue
                    _geo = shapely.ops.linemerge([_geo, _bound])

                new_bounds.append(_geo)
                bounds = new_bounds.copy()

            bound_num = len(bounds)
            if bound_num != 2:
                raise Exception(f"数据不是2；是 {bound_num}; 不支持")

            coord1 = list(bounds[0].coords)
            coord2 = list(bounds[1].coords)

            c1_point = shapely.geometry.Point(coord1[0])
            c2_point = shapely.geometry.Point(coord2[0])
            cn_point = shapely.geometry.Point(coord2[-1])
            if c1_point.distance(c2_point) < c1_point.distance(cn_point):
                coord2.reverse()

            _geo = shapely.geometry.Polygon(coord1 + coord2)
            if _geo.is_empty:
                raise Exception("面为空")
            if not _geo.is_valid:
                if _geo.buffer(0).area / _geo.area < 0.99:
                    raise Exception(f"buffer 之后，面积差异过大")
                _geo = _geo.buffer(0)
            if not _geo.is_valid:
                raise Exception(f"面还是非法;{_geo.wkt}")

            geom = _geo.wkt
            mode = 1
        except Exception as e:
            print(f"构成 LD 面异常:{str(e)}")
            geom = _group['geo'].convex_hull.wkt
            mode = 2

        _item['geom'] = geom
        _item['mode'] = mode
        resp.append(_item)
    return resp


def get_lane_roads(wkt: str) -> list:
    """
    获取压盖的车道级路口
    """
    qry = f"""
    select st_astext(geom) geom, road_pg_id 
    from nav_lane_road_pg 
    where st_intersects(geom, st_geomfromtext('{wkt}', 4326))
    """
    res = dbutils.fetch_all(pgsql.ROAD_CONFIG, qry)
    return [{
        'geom': item[0],
        'road_pg_id': item[1],
    } for item in res]


def get_complete_ld_polygon_3d(wkt: str) -> str:
    """
    获取完整的 ld 面
    ld 边界面 + ld 路口面
    """
    ld_polygons = get_ld_polygon(wkt)
    lane_roads = get_lane_roads(wkt)

    geos = []
    for item1 in ld_polygons:
        geos.append(shapely.wkt.loads(item1['geom']))
    for item2 in lane_roads:
        geos.append(shapely.wkt.loads(item2['geom']))
    if len(geos) == 0:
        return 'Polygon EMPTY'
    return shapely.ops.unary_union(geos).wkt


def get_complete_ld_polygon_2d(_ctx: Context, wkt: str) -> str:
    """
    获取完整的 ld 面
    ld 边界面 + ld 路口面
    """
    res = get_complete_ld_polygon_3d(wkt)
    return to_2d(shapely.wkt.loads(res)).wkt


def get_full_ld_polygon(wkt: str) -> str:
    """
    获取完整的 ld 面，ld 边界面 + ld 路口面，不使用 context 版本。
    """
    res = get_complete_ld_polygon_3d(wkt)
    return to_2d(shapely.wkt.loads(res)).wkt


def to_2d(geom):
    """
    将 shapely 几何对象从 3D 转为 2D（去掉 Z 坐标）
    支持 Point, LineString, Polygon, Multi*, GeometryCollection
    """
    if geom.is_empty:
        return geom
    if isinstance(geom, Point):
        return Point(geom.x, geom.y)
    elif isinstance(geom, LineString):
        return LineString([(x, y) for x, y, *_ in geom.coords])
    elif isinstance(geom, Polygon):
        exterior = [(x, y) for x, y, *_ in geom.exterior.coords]
        interiors = [
            [(x, y) for x, y, *_ in ring.coords] for ring in geom.interiors
        ]
        return Polygon(exterior, interiors)
    elif isinstance(geom, MultiPoint):
        return MultiPoint([to_2d(g) for g in geom.geoms])
    elif isinstance(geom, MultiLineString):
        return MultiLineString([to_2d(g) for g in geom.geoms])
    elif isinstance(geom, MultiPolygon):
        return MultiPolygon([to_2d(g) for g in geom.geoms])
    elif isinstance(geom, GeometryCollection):
        return GeometryCollection([to_2d(g) for g in geom.geoms])
    else:
        raise TypeError(f"Unsupported geometry type: {type(geom)}")