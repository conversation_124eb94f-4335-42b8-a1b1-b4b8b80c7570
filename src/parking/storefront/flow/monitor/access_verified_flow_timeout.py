"""
出入口超时未流转
"""

from datetime import datetime, timedelta

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.monitor.util import DAY, get_pt_id_by_qb_ids
from src.parking.storefront.flow.context import Context


class AccessVerifiedFlowTimeoutMonitor(Monitor):
    """
    出入口超时未流转
    """

    def __init__(self, ctx: Context, timeout: int):
        super().__init__(ctx)
        self.timeout = timeout

    def monitor(self) -> Report:
        """监控"""
        detail = self._get_detail()
        header = f"出入口超过{self.timeout // DAY}天未流转：{len(detail)}"
        return Report(header=header, detail=detail)

    def _get_detail(self) -> list:
        ctx = self.ctx

        current_time = datetime.now()
        timeout_time = current_time - timedelta(seconds=self.timeout)

        qry = f"""
            select t1.id, t2.source_id, t3.id, t1.updated_at 
                FROM 
                    {ctx.park_ach_tab} t1
                JOIN 
                    park_access_intelligence  t2
                ON 
                    t1.id = t2.outside_task_id
                JOIN 
                    integration_qb t3
                ON 
                    t2.source_id = t3.ref_qb_id
                WHERE 
                    t1.status = 'PARK_ACCESS_INTELLIGENCE_PUSHED' and t1.updated_at < '{timeout_time}'
--                     t1.status = 'PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALED' and t1.id not in (select p.id from park_storefront_prod_parking p left join park_storefront_prod_access a on p.id = a.park_id where p.status = 'PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALED' and a.id is not null) 
--                     and info_id in (select info_id from {ctx.pushed_tab} where batch in ('beijing_except_haidian_20241208', 'beijing_jianchengqu_0106') and remark in ('已有出入口,建城区', '有出入口情报,建城区')) 

                    and t3.ref_qb_id is not null 
        """
        res = ctx.poi_db.fetchall(qry)

        qb_ids = [item[2] for item in res]

        id2ptid = get_pt_id_by_qb_ids(self.ctx, qb_ids)

        result = []
        for item in res:
            qb_id = item[2]
            result.append({
                'park_id': item[0],
                'source_id': item[1],
                'qb_id': qb_id,
                'ptid': id2ptid[qb_id] if qb_id in id2ptid else '',
                'updated_at': item[3],
            })
        return result

