"""
每日回收出入口成果量
"""

from src.parking.storefront.flow.monitor.model import Monitor, Report


class CallbackAccessDailyMonitor(Monitor):
    """
    每日回收出入口成果量
    """

    def monitor(self) -> Report:
        """监控"""
        detail = self._get_detail()
        ok_num = sum([item[1] for item in detail])
        no_num = sum([item[2] for item in detail])
        pk_num = sum([item[3] for item in detail])
        header = f"回收出入口成果量，有效的：{ok_num}; 无效的：{no_num}; 面数量：{pk_num}"
        return Report(header=header, detail=detail)

    def _get_detail(self):
        ctx = self.ctx

        qry = f"""
                SELECT 
                    t2.city, 
                    COUNT(CASE WHEN  conclusion = 'new_poi' THEN 1 END) AS yes,
                    COUNT(CASE WHEN  conclusion != 'new_poi' THEN 1 END) AS no,
                    COUNT(distinct t3.park_id) as park_num
                FROM 
                    {ctx.park_ach_tab} t1
                JOIN 
                    {ctx.pushed_tab} t2
                ON 
                    t1.info_id = t2.info_id
                JOIN 
                    park_storefront_access  t3
                ON 
                    t1.id = t3.park_id
                WHERE 
                    t2.info_id not in (select info_id from {ctx.pushed_tab} where batch = '') 
                    and t3.created_at > '{self.day}' 
                GROUP BY 
                    t2.city
                """
        res = ctx.poi_db.fetchall(qry)
        return res

