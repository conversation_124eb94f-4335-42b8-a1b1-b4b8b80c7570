"""
每日回收核实面成果量
"""

from src.parking.storefront.flow.config import sort_by_city
from src.parking.storefront.flow.monitor.model import Monitor, Report


class CallbackTuringDailyMonitor(Monitor):
    """
    每日回收核实面成果量
    """

    def monitor(self) -> Report:
        """监控"""
        turing_tol, info_tol = self._get_total()

        res = self._get_city_detail()
        detail = [{
            '城市': item[0],
            '是': item[1],
            '错': item[2],
            '否': item[3],
            '其他': item[4],
            '错-画的面异常': item[5],
            '入成果库': item[1] + item[2] - item[5],
        } for item in res]
        cities = [item['城市'] for item in detail]
        detail = sort_by_city(cities, detail)
        ach_tol = sum(item['入成果库'] for item in detail)
        txt = f'回收核实面成果量：{turing_tol}; 消费情报量：{info_tol}; 入成果库：{ach_tol}'
        return Report(header=txt, detail=detail)

    def _get_total(self) -> tuple:
        """
        获取总量
        """
        ctx = self.ctx
        qry = f"""
                select count(turing_id), count(distinct r.info_id) 
                from {ctx.turing_result_tab} r left join {ctx.pushed_tab} p on r.info_id = p.info_id 
                where r.created_at > '{self.day}' 
                and r.info_id not in (select info_id from {ctx.pushed_tab} where batch = '') 
                """
        res = ctx.poi_db.fetchone(qry)
        return res

    def _get_city_detail(self) -> list:
        """
        获取每个城市的数量
        """
        ctx = self.ctx
        qry = f"""
        SELECT 
            t2.city,
            COUNT(CASE WHEN recall = '1' AND precise = '1' THEN 1 END) AS yes,
            COUNT(CASE WHEN recall = '1' AND precise != '1' THEN 1 END) AS wrong,
            COUNT(CASE WHEN sub_result != '1' OR (sub_result = '1' and recall not in ('1', '2')) THEN 1 END) AS no, 
            COUNT(CASE WHEN recall = '2' THEN 1 END) AS other,
            COUNT(CASE WHEN recall = '1' AND precise != '1' and (not st_isvalid(t1.geom) or st_geometrytype(t1.geom) != 'ST_Polygon') THEN 1 END) AS error
        FROM 
            {ctx.turing_result_tab} t1
        JOIN 
            {ctx.pushed_tab} t2
        ON 
            t1.info_id = t2.info_id
        WHERE 
            t2.info_id not in (select info_id from {ctx.pushed_tab} where batch = '') 
            and t1.created_at > '{self.day}' 
        GROUP BY 
            t2.city
        """
        res = ctx.poi_db.fetchall(qry)
        return res

