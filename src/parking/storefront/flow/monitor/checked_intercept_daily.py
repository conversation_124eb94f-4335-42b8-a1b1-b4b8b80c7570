"""
每日拦截面量、入口量
"""

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.config import sort_by_city


class CheckedInterceptDailyMonitor(Monitor):
    """
    每日拦截面量、入口量
    """

    def monitor(self) -> Report:
        """监控"""
        intercepts = self._get_intercept_num()
        detail = [{
            '城市': item[0],
            '面数量': item[1],
            '口数量': item[2],
        } for item in intercepts]
        cities = [item['城市'] for item in detail]
        detail = sort_by_city(cities, detail)

        park_num = sum([item[1] for item in intercepts])
        access_num = sum([item[2] for item in intercepts])

        txt = f"质检拦截面量：{park_num}，入口量：{access_num}"
        return Report(header=txt, detail=detail)

    def _get_intercept_num(self) -> list:
        ctx = self.ctx

        qry = f"""
                SELECT 
                    t2.city, count(distinct t1.id), count(distinct t3.id)
                FROM 
                    {ctx.park_ach_tab} t1
                JOIN 
                    {ctx.pushed_tab} t2
                ON 
                    t1.info_id = t2.info_id
                JOIN 
                    {ctx.access_ach_tab} t3
                ON 
                    t1.id = t3.park_id
                WHERE 
                    t2.info_id not in (select info_id from {ctx.pushed_tab} where batch = '') 
                    and t1.updated_at > '{self.day}' 
                    and t1.status = 'CHECK_FAILED' 
                GROUP BY 
                    t2.city
                """
        res = ctx.poi_db.fetchall(qry)
        return res

