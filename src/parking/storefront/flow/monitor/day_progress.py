"""
每日进展
面维度回库量、口维度回库量、拦截量、最终上线量
"""
from datetime import datetime, timedelta

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.context import Context


INTERVAL_DAYS = '1 days'


class DayProgressMonitor(Monitor):
    """
    每日进展
    """

    def __init__(self, ctx: Context, before_day: int = 60):
        super().__init__(ctx)
        self.before_time = (datetime.today() - timedelta(days=before_day)).strftime('%Y-%m-%d')

    def monitor(self) -> Report:
        """
        监控
        """
        area_detail = self._get_area_detail()
        access_detail1 = self._get_access_detail1()
        access_upd_detail = self._get_access_upd_detail()
        access_detail2 = self._get_access_detail2()
        online_detail = self._get_online_detail()
        offline_detail = self._get_offline_detail()
        online_add_detail = self._get_online_add_detail()
        online_upd_detail = self._get_online_upd_detail()
        intercept_detail = self._get_intercept_detail()
        intercept_upd_detail = self._get_intercept_upd_detail()

        dates = list(set(
            list(area_detail.keys()) + list(access_detail1.keys())
            + list(access_detail2.keys()) + list(online_detail.keys())
            + list(intercept_detail.keys()) + list(access_upd_detail.keys())
            + list(online_add_detail.keys()) + list(online_upd_detail.keys())
            + list(intercept_upd_detail.keys()) + list(offline_detail.keys())
        ))
        dates.sort()

        detail = []
        for _date in dates:
            detail.append({
                '日期': _date,
                '面回库量': area_detail.get(_date, 0),
                '口回库量-面维度(新增)': access_detail1.get(_date, 0),
                '拦截量-新增': intercept_detail.get(_date, 0),
                '上线量-新增': online_add_detail.get(_date, 0),
                '口回库量-面维度(更新)': access_upd_detail.get(_date, 0),
                '拦截量-更新': intercept_upd_detail.get(_date, 0),
                '上线量-更新': online_upd_detail.get(_date, 0),
                # '口回库量-口维度': access_detail2.get(_date, 0),
                '上线量': online_detail.get(_date, 0),
                '下线量': offline_detail.get(_date, 0),
            })
        return Report(header='每日闭环量', detail=detail)

    def _get_area_detail(self) -> dict:
        """
        面维度回库量
        """
        qry = f"""
        select count(*), created_at::date 
        from park_storefront_prod_parking 
        where created_at > '{self.before_time}' and bid_status = 'effected' group by created_at::date 
        """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_access_detail1(self) -> dict:
        """
        口回库量，面维度
        """
        qry = f"""
        select count(*), created_at::date 
        from parking_turing_result 
        where data_type = '1210' and created_at > '{self.before_time}'  group by created_at::date;
        """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_access_upd_detail(self) -> dict:
        """
        口回库量，面维度, 更新
        """
        qry = f"""
        select count(*), created_at::date 
        from parking_turing_result 
        where data_type = '1213' and created_at > '{self.before_time}'  group by created_at::date;
        """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_access_detail2(self) -> dict:
        """
        口回库量，口维度
        """
        qry = f"""
        select count(*), created_at::date  
        from park_storefront_prod_access
        where created_at > '{self.before_time}' group by created_at::date 
        """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_online_detail(self):
        """
        上线量
        """
        qry = f"""
        select count(distinct guid), created_at::date 
        from park_storefront_prod_match_command 
        where type = 'UPDATE_PARKING' and args->'request'->>'area' is not null 
        and created_at > '{self.before_time}' 
        group by created_at::date 
        """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_offline_detail(self):
        """
        下线量
        """
        qry = f"""
                select count(distinct guid), created_at::date 
                from park_storefront_prod_match_command 
                where type = 'REMOVE_PARKING' 
                and created_at > '{self.before_time}' 
                group by created_at::date 
                """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_intercept_detail(self):
        """
        每日拦截量
        """
        qry = f"""
        SELECT COUNT(DISTINCT a.turing_id), a_day AS missing_count
FROM (
    SELECT a.turing_id, a.created_at::date AS a_day
    FROM parking_turing_result a
    WHERE a.created_at >= '{self.before_time}' and data_type = '1210' 
) a
WHERE NOT EXISTS (
    SELECT 1
    FROM park_access_intelligence b
    JOIN park_storefront_prod_parking c ON b.bid = c.bid
    JOIN park_storefront_prod_match_command d 
        ON d.guid = c.id::text and d.type = 'UPDATE_PARKING' and args->'request'->>'area' is not null 
    WHERE b.outside_id = a.turing_id
      AND d.created_at >= a.a_day
      AND d.created_at < a.a_day + INTERVAL '{INTERVAL_DAYS}'
)
GROUP BY a_day
ORDER BY a_day;
        """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_online_add_detail(self):
        """
        每日新增上线量
        """
        qry = f"""
            SELECT COUNT(DISTINCT a.turing_id), a_day AS missing_count
    FROM (
        SELECT a.turing_id, a.created_at::date AS a_day
        FROM parking_turing_result a
        WHERE a.created_at >= '{self.before_time}' and data_type = '1210' 
    ) a
    WHERE EXISTS (
        SELECT 1
        FROM park_access_intelligence b
        JOIN park_storefront_prod_parking c ON b.bid = c.bid
        JOIN park_storefront_prod_match_command d 
            ON d.guid = c.id::text and d.type = 'UPDATE_PARKING' and args->'request'->>'area' is not null 
        WHERE b.outside_id = a.turing_id
          AND d.created_at >= a.a_day
          AND d.created_at < a.a_day + INTERVAL '{INTERVAL_DAYS}'
    )
    GROUP BY a_day
    ORDER BY a_day;
            """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_online_upd_detail(self):
        """
        每日更新上线量
        """
        qry = f"""
                            SELECT COUNT(DISTINCT a.turing_id), a_day AS missing_count
                    FROM (
                        SELECT a.turing_id, a.created_at::date AS a_day
                        FROM parking_turing_result a
                        WHERE a.created_at >= '{self.before_time}' and data_type = '1213' 
                    ) a left join park_storefront_repair b on a.turing_id = b.user_id and user_name = 'turing' 
                    where reason = 'ACCESS_CHECK_FAILED' and prev_tab = 'park_storefront_prod_parking' 
                    and EXISTS (
                        SELECT 1
                        FROM park_storefront_prod_match_command d 
                        where d.guid = b.prev_id and d.type = 'UPDATE_PARKING' and args->'request'->>'area' is not null 
                          AND d.created_at >= a.a_day
                          AND d.created_at < a.a_day + INTERVAL '{INTERVAL_DAYS}'
                    )
                    GROUP BY a_day
                    ORDER BY a_day;
                            """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}

    def _get_intercept_upd_detail(self):
        qry = f"""
                    SELECT COUNT(DISTINCT a.turing_id), a_day AS missing_count
            FROM (
                SELECT a.turing_id, a.created_at::date AS a_day
                FROM parking_turing_result a
                WHERE a.created_at >= '{self.before_time}' and data_type = '1213' 
            ) a left join park_storefront_repair b on a.turing_id = b.user_id and user_name = 'turing' 
            where reason = 'ACCESS_CHECK_FAILED' and prev_tab = 'park_storefront_prod_parking' 
            and NOT EXISTS (
                SELECT 1
                FROM park_storefront_prod_match_command d 
                where d.guid = b.prev_id and d.type = 'UPDATE_PARKING' and args->'request'->>'area' is not null 
                  AND d.created_at >= a.a_day
                  AND d.created_at < a.a_day + INTERVAL '{INTERVAL_DAYS}'
            )
            GROUP BY a_day
            ORDER BY a_day;
                    """
        res = self.ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}
