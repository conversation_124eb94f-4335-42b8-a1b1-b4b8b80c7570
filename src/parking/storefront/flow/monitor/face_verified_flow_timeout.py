"""
面核实超时未流转
"""
from datetime import datetime, timedelta

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.monitor.util import DAY


class FaceVerifiedFlowTimeoutMonitor(Monitor):
    """
    面核实超时未流转
    """

    def __init__(self, ctx: Context, timeout: int):
        super().__init__(ctx)
        self.timeout = timeout

    def monitor(self) -> Report:
        """监控"""
        ids = self._get_timeout_turing_ids()
        txt = f"面核实超过{self.timeout // DAY}天未回收的量：{len(ids)}"
        return Report(header=txt, detail=ids)

    def _get_timeout_turing_ids(self) -> list:
        """
        获取超时的情报
        """
        ctx = self.ctx

        current_time = datetime.now()
        timeout_time = current_time - timedelta(seconds=self.timeout)

        qry = f"""
        select verify_id, updated_at from {ctx.pushed_tab} where updated_at < '{timeout_time}' and status = 'PUSHED' 
        order by updated_at asc
        """
        res = ctx.poi_db.fetchall(qry)
        return [{
            'turing_id': item[0],
            'updated_at': item[1],
        } for item in res]

