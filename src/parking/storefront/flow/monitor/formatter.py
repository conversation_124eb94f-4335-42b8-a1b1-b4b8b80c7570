"""
格式化
"""
import abc
import csv
import os
import uuid

from src.parking.storefront.flow.monitor.model import Report
from src.parking.storefront.flow.monitor.util import upload_file
from src.tools.utils import ensure_dir


class Formatter:
    """
    格式化
    """

    def __init__(self, report: Report):
        self.report = report

    @abc.abstractmethod
    def format(self) -> str:
        """格式化"""
        pass


class TextFormatter(Formatter):
    """
    格式化文本
    """

    def __init__(self, report: Report, size: str = ''):
        super().__init__(report)
        self.size = size if size != '' else '#####'

    def format(self) -> str:
        """
        格式化
        """
        header = self.report.header
        detail = self.report.detail

        values = [f"{self.size} {header}"]
        for item in detail:
            if isinstance(item, dict):
                item = TextFormatter._format_dict_item(item)
            values.append(f"- {item}")
        return '\n'.join(values)

    @staticmethod
    def _format_dict_item(item: dict) -> str:
        vals = []
        for key, val in item.items():
            vals.append(f"{key}: {val}")
        return ';'.join(vals)


class UrlFormatter(Formatter):
    """
    格式化为 url
    """

    def format(self) -> str:
        """
        格式化
        """
        header = self.report.header
        detail = self.report.detail

        result = f"##### {header};"
        if len(detail) > 0:
            result += f"{self._upload(detail)}"
        return result

    def _upload(self, detail: list) -> str:
        file = self._to_csv(detail)
        return upload_file(file)

    def _to_csv(self, detail: list) -> str:
        dest = './tmp'
        ensure_dir(dest)
        file = os.path.join(dest, f"{uuid.uuid4().hex}.csv")
        with open(file, 'w') as hdr:
            writer = csv.writer(hdr)
            header = detail[0].keys()
            writer.writerow(header)
            for item in detail:
                writer.writerow(item.values())
        return file

