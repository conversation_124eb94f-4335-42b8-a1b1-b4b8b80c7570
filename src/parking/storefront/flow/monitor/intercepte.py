"""
拦截的监控
"""

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.config import sort_by_city


class InterceptedMonitor(Monitor):
    """
    拦截的监控
    """

    def monitor(self) -> Report:
        """
        质检拦截/干预闭环监控：按城市区分，如：北京市：累计总共xxx拦截闭环，累计还剩xxx拦截闭环，累计xxx闭环上线
        """
        detail = self._get_details()
        cities = [item['city'] for item in detail]
        detail = sort_by_city(cities, detail)

        total = sum(item['total'] for item in detail)
        done = sum(item['done'] for item in detail)
        text = f"质检拦截闭环, 总量：{total}, 已闭环：{done}; 还剩余:{total - done}"
        return Report(header=text, detail=detail)

    def _get_details(self) -> list:
        """
        获取某个城市的 累计总共xxx拦截闭环，累计还剩xxx拦截闭环，累计xxx闭环上线
        """
        repair_qry = f"""
        SELECT 1 
        FROM {self.ctx.repair_tab} b 
        WHERE a.id::text = b.prev_id 
        AND prev_tab = '{self.ctx.park_ach_tab}' 
        AND b.status != 'CANCEL' 
        AND reason != '' 
        """
        qry = f"""
        SELECT 
    city,
    COUNT(CASE 
              WHEN status IN ('AREA_CHECK_FAILED', 'CHECK_FAILED') 
                   OR EXISTS ({repair_qry})
              THEN 1
              ELSE NULL 
          END) AS total,
    COUNT(CASE 
              WHEN status IN ('ONLINE', 'OFFLINE', 'READY_OFFLINE') 
                   AND EXISTS ({repair_qry})
              THEN 1
              ELSE NULL 
          END) AS done
FROM {self.ctx.park_ach_tab} a
WHERE bid_status = 'effected'
GROUP BY city;
        """
        res = self.ctx.poi_db.fetchall(qry)
        return [{
            'city': item[0],
            'total': item[1],
            'done': item[2],
            'ing': item[1] - item[2],
        } for item in res]

