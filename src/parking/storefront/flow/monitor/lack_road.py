"""
缺路监控
"""
from datetime import datetime, timedelta

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.repairer.lack_road import stat


class LackRoadInfoMonitor(Monitor):
    """
    缺路情报监控
    """

    def monitor(self) -> Report:
        """
        监控
        """
        day_detail = self._get_day_detail()
        total_detail = self._get_total_detail()

        detail = [
            {
                '最近 24h 新增了': day_detail['add'],
                '投放了': day_detail['push'],
                '拉回来': day_detail['pull'],
                '池中总共有': total_detail['total'],
                '池中完成了': total_detail['done'],
                '池中作业中': total_detail['work'],
                '池中待推送': total_detail['wait'],
            }
        ]
        header = f"""缺路情报监控"""
        return Report(header=header, detail=detail)

    def _get_day_detail(self) -> dict:
        """
        获取最近 3 天详情
        新增了多少，投放了多少，拉回了多少
        """
        now = datetime.now()
        past_24h = now - timedelta(hours=24)
        past_24h_str = past_24h.strftime("%Y-%m-%d %H:%M:%S")

        qry = f"""
        SELECT
    SUM(CASE 
        WHEN create_time > '{past_24h_str}'
        THEN 1 ELSE 0 END) AS add_count,
    SUM(CASE 
        WHEN update_time > '{past_24h_str}' AND turing_qid != '' AND turing_result = 0 
        THEN 1 ELSE 0 END) AS push_count,
    SUM(CASE 
        WHEN update_time > '{past_24h_str}' AND turing_result != 0 
        THEN 1 ELSE 0 END) AS pull_count
FROM parking_gate_push_turing where batch_id LIKE 'park_storefront_%';
        """
        res = self.ctx.bf_query.queryone(qry)
        return {
            'add': res[0],
            'push': res[1],
            'pull': res[2],
        }

    def _get_total_detail(self) -> dict:
        """
        获取总的明细
        总共多少，完成了多少，还在作业中多少，还剩多少没有投
        """
        qry = """
        SELECT
    COUNT(*) AS total_count,
    SUM(CASE 
        WHEN turing_result != 0 
        THEN 1 ELSE 0 END) AS done_count,
    SUM(CASE 
        WHEN turing_result = 0 AND turing_qid != '' 
        THEN 1 ELSE 0 END) AS working_count,
    SUM(CASE 
        WHEN turing_qid = '' 
        THEN 1 ELSE 0 END) AS wait_work_count
FROM parking_gate_push_turing
WHERE batch_id LIKE 'park_storefront_%';
        """
        res = self.ctx.bf_query.queryone(qry)
        return {
            'total': res[0],
            'done': res[1],
            'work': res[2],
            'wait': res[3],
        }


class LackRoadCityDetailMonitor(Monitor):
    """
    缺路 城市明细 监控
    """

    def monitor(self) -> Report:
        """
        监控
        """
        header = f"""缺路城市明细"""
        return Report(header=header, detail=stat(self.ctx))
