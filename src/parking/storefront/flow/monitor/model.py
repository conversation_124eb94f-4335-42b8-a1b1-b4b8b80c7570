"""
监控相关数据模型定义
"""
import abc
import dataclasses

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.monitor.util import get_cur_time


@dataclasses.dataclass
class Report:
    """
    监控报告
    """
    header: str
    detail: list


class Monitor:
    """
    监控者
    """

    def __init__(self, ctx: Context):
        self.ctx = ctx
        self.day = get_cur_time('%Y-%m-%d')

    @abc.abstractmethod
    def monitor(self) -> Report:
        """
        监控
        """
        pass

