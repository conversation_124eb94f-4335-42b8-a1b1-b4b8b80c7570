"""
监控中心
"""
import argparse
from typing import List

from src.parking.storefront.flow.context import gen_ctx, Context
from src.parking.storefront.flow.monitor.formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Formatter, TextFormatter
from src.parking.storefront.flow.monitor.util import send_hi_by_contents
from src.parking.storefront.flow.monitor.work_progress import WorkProgressMonitor
from src.parking.storefront.flow.monitor.turing_callback import TuringCallbackMonitor
from src.parking.storefront.flow.monitor.model import Report
from src.parking.storefront.flow.monitor.util import get_cur_time, DAY, HOUR
from src.parking.storefront.flow.monitor.pushed_access_daily import PushedAccessDailyMonitor
from src.parking.storefront.flow.monitor.pushed_verified_num_daily import PushedVerifiedNumDailyMonitor
from src.parking.storefront.flow.monitor.access_verified_flow_timeout import AccessVerifiedFlowTimeoutMonitor
from src.parking.storefront.flow.monitor.checked_intercept_daily import CheckedInterceptDailyMonitor
from src.parking.storefront.flow.monitor.face_verified_flow_timeout import FaceVerifiedFlowTimeoutMonitor
from src.parking.storefront.flow.monitor.intercepte import InterceptedMonitor
from src.parking.storefront.flow.monitor.lack_road import LackRoadInfoMonitor, LackRoadCityDetailMonitor
from src.parking.storefront.flow.monitor.no_access import NoAccessMonitor
from src.parking.storefront.flow.monitor.online_daily import OnlineDailyMonitor
from src.parking.storefront.flow.monitor.online_intercept_daily import OnlineInterceptDailyMonitor
from src.parking.storefront.flow.monitor.park_ch_flow_timeout_daily import ParkAchFlowTimeoutDailyMonitor
from src.parking.storefront.flow.monitor.road_item import RoadItemMonitor
from src.parking.storefront.flow.monitor.callback_turing_daily import CallbackTuringDailyMonitor
from src.parking.storefront.flow.monitor.callback_access_daily import CallbackAccessDailyMonitor
from src.parking.storefront.flow.monitor.day_progress import DayProgressMonitor
from src.parking.storefront.flow.monitor.week_progress import WeekProgressMonitor
from src.parking.storefront.flow.monitor.wpf_progress import WpfProgressMonitor


def _output(contents: list, notify: bool):
    """
    对外输出
    """
    message = '\n'.join(contents)
    print(message)
    if notify:
        send_hi_by_contents(contents)


def _format(formatters: List[Formatter]) -> list:
    """
    格式化
    """
    contents = []
    for a_format in formatters:
        contents.append(a_format.format())
    return contents


def monitor_center(formatters: List[Formatter], notify: bool, ):
    """
    监控中心
    """
    contents = _format(formatters)
    _output(contents, notify)


def monitor_work_process(ctx: Context, notify: bool):
    """
    监控作业进展
    单独拿出来监控，是因为统计耗时长
    """
    formatters = [
        UrlFormatter(WorkProgressMonitor(ctx).monitor()),  # 作业进展
    ]
    monitor_center(formatters, notify)


def monitor_turing_callback(ctx: Context, notify: bool):
    """
    监控图灵回传
    单独拿出来，是因为获取图灵信息的接口受机器的限制，目前只能在 ssc01 上请求
    """
    formatters = [
        UrlFormatter(TuringCallbackMonitor(ctx).monitor()),  # 图灵回传结果监控
    ]
    monitor_center(formatters, notify)


def monitor_common(ctx: Context, notify: bool):
    """
    常规监控
    """
    formatters = [
        TextFormatter(Report(header=f"门前停车场：{get_cur_time('%Y-%m-%d')}", detail=[]), size='####'),
        UrlFormatter(PushedVerifiedNumDailyMonitor(ctx).monitor()),  # 每日投放核实面任务量
        UrlFormatter(CallbackTuringDailyMonitor(ctx).monitor()),  # 每日回收核实面成果量
        UrlFormatter(PushedAccessDailyMonitor(ctx).monitor()),  # 每日投放出入口任务量
        TextFormatter(CallbackAccessDailyMonitor(ctx).monitor()),  # 每日回收出入口成果量
        UrlFormatter(FaceVerifiedFlowTimeoutMonitor(ctx, timeout=3 * DAY).monitor()),  # 超时未流转面任务
        UrlFormatter(AccessVerifiedFlowTimeoutMonitor(ctx, timeout=2 * DAY).monitor()),  # 超时未流转入口任务
        UrlFormatter(ParkAchFlowTimeoutDailyMonitor(ctx, timeout=2 * DAY).monitor()),  # 超时未流转任务
        UrlFormatter(CheckedInterceptDailyMonitor(ctx).monitor()),  # 每日拦截面量、入口量
        UrlFormatter(NoAccessMonitor(ctx).monitor()),  # 无口面
        UrlFormatter(OnlineDailyMonitor(ctx).monitor()),  # 每日上线量
        UrlFormatter(OnlineInterceptDailyMonitor(ctx, timeout=5 * HOUR).monitor()),  # 每日上线被指控拦截量
        UrlFormatter(InterceptedMonitor(ctx).monitor()),  # 每日上线被指控拦截量
    ]
    monitor_center(formatters, notify)


def monitor_progress(ctx: Context, notify: bool):
    """
    进展监控
    """
    formatters = [
        UrlFormatter(DayProgressMonitor(ctx, 60).monitor()),  # 每日进展
        UrlFormatter(WeekProgressMonitor(ctx, 8).monitor()),  # 每周进展
        TextFormatter(WpfProgressMonitor(ctx).monitor()),  # 环节进展
    ]
    monitor_center(formatters, notify)


def monitor_road(ctx: Context, notify: bool):
    """
    道路相关的监控
    单独拿出来是因为很重要，需要重点关注
    """
    formatters = [
        TextFormatter(RoadItemMonitor(ctx).monitor()),  # 道路要素闭环监控
        TextFormatter(LackRoadInfoMonitor(ctx).monitor()),  # 缺路情报
        UrlFormatter(LackRoadCityDetailMonitor(ctx).monitor()),  # 缺路情报
    ]
    monitor_center(formatters, notify)


def debug(ctx: Context, notify=False):
    """
    测试
    """
    formatters = [
        # TextFormatter(RoadItemMonitor(ctx).monitor()),  # 道路要素闭环监控
        # TextFormatter(LackRoadInfoMonitor(ctx).monitor()),  # 缺路情报
    ]
    monitor_center(formatters, notify)


def main():
    """
    主函数
    """
    notify = ARGS.notify
    items = str(ARGS.items).split(',')

    item2fn = {
        'work_process': monitor_work_process,
        'road': monitor_road,
        'common': monitor_common,
        'turing_callback': monitor_turing_callback,
        'progress': monitor_progress,
        'debug': debug,
    }

    ctx = gen_ctx(autocommit=True, debug=True)
    for item in items:
        if item not in item2fn:
            print(f"{item} 没有相应的监控项")
            continue
        item2fn[item](ctx, notify)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='监控')
    parser.add_argument('--notify', dest='notify', action='store_true')  # 不加 表示不通知
    parser.add_argument(
        '--items',
        type=str,
        required=True,
        help="监控项，多个用逗号分隔"
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()

