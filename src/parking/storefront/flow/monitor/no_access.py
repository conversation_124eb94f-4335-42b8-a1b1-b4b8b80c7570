"""
无口面监控
"""

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.monitor.util import get_pt_id_by_qb_ids


class NoAccessMonitor(Monitor):
    """
    无口面
    """

    def monitor(self) -> Report:
        """监控"""
        detail = self._get_detail()
        header = f"无口面：{len(detail)}"
        return Report(header=header, detail=detail)

    def _get_detail(self) -> list:
        ctx = self.ctx

        qry = f"""
                select t1.id, t2.source_id, t3.id
                    FROM 
                        {ctx.park_ach_tab} t1
                    JOIN 
                        park_access_intelligence  t2
                    ON 
                        t1.id = t2.outside_task_id
                    JOIN 
                        integration_qb t3
                    ON 
                        t2.source_id = t3.ref_qb_id
                    WHERE 
                        t1.status = 'NO_ACCESS' and t1.id not in (select p.id from park_storefront_prod_parking p left join park_storefront_prod_access a on p.id = a.park_id where p.status = 'NO_ACCESS' and a.id is not null) 
--                         and info_id in (select info_id from {ctx.pushed_tab} where batch in ('beijing_except_haidian_20241208', 'beijing_jianchengqu_0106') and remark in ('已有出入口,建城区', '有出入口情报,建城区')) 
                        and t3.ref_qb_id is not null 
                        and t1.updated_at > '{self.day}'
            """
        res = ctx.poi_db.fetchall(qry)

        qb_ids = [item[2] for item in res]
        id2ptid = get_pt_id_by_qb_ids(self.ctx, qb_ids)

        result = []
        for item in res:
            qb_id = item[2]
            result.append({
                'park_id': item[0],
                'source_id': item[1],
                'qb_id': qb_id,
                'ptid': id2ptid[qb_id] if qb_id in id2ptid else '',
            })
        return result

