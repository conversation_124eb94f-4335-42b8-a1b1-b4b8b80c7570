"""
每日上线量
"""


from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.config import sort_by_city


class OnlineDailyMonitor(Monitor):
    """
    每日上线量
    """

    def monitor(self) -> Report:
        """监控"""
        detail = self._get_detail()
        detail = [{
            '城市': item[0],
            '量级': item[1],
        } for item in detail]
        cities = [item['城市'] for item in detail]
        detail = sort_by_city(cities, detail)

        txt = f"上线量：{sum([item['量级'] for item in detail])}"
        return Report(header=txt, detail=detail)

    def _get_detail(self):
        ctx = self.ctx
        qry = f"""
        select count(*), city 
        from  {ctx.park_ach_tab} a 
        where bid_status = 'effected' and status = 'ONLINE' 
        and exists (
            select 1 from {ctx.park_match_command_tab} b 
            where a.id::text = b.guid  and b.type = 'UPDATE_PARKING' 
            and b.created_at > '{self.day}' 
        )
        group by city 
        """
        res = ctx.poi_db.fetchall(qry)
        return res

