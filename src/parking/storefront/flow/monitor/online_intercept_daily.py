"""
每日上线被指控拦截量
"""

from datetime import datetime, timedelta

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.context import Context


class OnlineInterceptDailyMonitor(Monitor):
    """
    每日上线被指控拦截量
    """

    def __init__(self, ctx: Context, timeout: int):
        super().__init__(ctx)
        self.timeout = timeout

    def monitor(self) -> Report:
        """监控"""
        detail = self._get_detail()

        txt = f"上线被质控拦截量：{len(detail)}"
        return Report(header=txt, detail=detail)

    def _get_detail(self):
        """
        推送生成 poi 时，可能会被拦截
        需要给出拦截详情：park_id、logid
        """
        ctx = self.ctx

        current_time = datetime.now()
        timeout_time = current_time - timedelta(seconds=self.timeout)

        qry = f"""
        with tmp as (
            select id from {ctx.park_ach_tab} where status in ('INITING_BID', 'COMING_ONLINE') 
            and info_id != '' and updated_at < '{timeout_time}' and created_at > '{self.day}'
        ) select tmp.id, source_id, c.status, c.updated_at from {ctx.park_match_command_tab} c 
        left join tmp on c.guid = tmp.id::varchar 
        where c.status != 'END' and tmp.id is not null
        """
        res = ctx.poi_db.fetchall(qry)

        result = []
        for item in res:
            log_id = self._get_log_id(item[1])
            result.append({
                'park_id': item[0],
                'source_id': item[1],
                'status': item[2],
                'log_id': log_id,
                'updated_at': item[3],
            })
        return result

    def _get_log_id(self, source_id: str) -> str:
        if source_id == '':
            return ''
        qry = f"""
        select t2.log_id from parking_push_data t1 
            left join place_data_release_record t2 on t1.record_id = t2.record_id 
            where t1.source_id in ('{source_id}') and t2.log_id is not null group by t2.log_id;
        """
        res = self.ctx.bf_query.queryone(qry)
        if res is None or not res:
            return ''
        return res[0]

