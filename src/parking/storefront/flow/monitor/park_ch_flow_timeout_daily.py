"""
超时未流转任务
"""
from datetime import datetime, timedelta

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.db import join_str
from src.parking.storefront.flow.monitor.util import DAY


class ParkAchFlowTimeoutDailyMonitor(Monitor):
    """
    超时未流转任务
    """

    def __init__(self, ctx: Context, timeout: int):
        super().__init__(ctx)
        self.timeout = timeout

    def monitor(self) -> Report:
        """监控"""
        timeouts = self._get_timeouts()

        txt = f"超时{self.timeout // DAY}天未流转的停车场量：{len(timeouts)}"
        return Report(header=txt, detail=timeouts)

    def _get_timeouts(self) -> list:
        ctx = self.ctx

        end_status = [
            'ONLINE', 'READY_OFFLINE', 'OFFLINE', 'NOT_PUSH', 'NO_ACCESS',
        ]
        current_time = datetime.now()
        timeout_time = current_time - timedelta(seconds=self.timeout)

        qry = f"""
        select id, status, updated_at from {ctx.park_ach_tab} 
        where info_id != '' and status not in ({join_str(end_status)}) and updated_at < '{timeout_time}' 
        and bid_status = 'effected'  
        """
        res = ctx.poi_db.fetchall(qry)
        return [{
            'park_id': item[0],
            'status': item[1],
            'updated_at': item[2],
        } for item in res]

