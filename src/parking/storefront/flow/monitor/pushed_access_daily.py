"""
每日投放出入口任务量
"""

from src.parking.storefront.flow.monitor.model import Monitor, Report


class PushedAccessDailyMonitor(Monitor):
    """
    每日投放出入口任务量
    """

    def monitor(self) -> Report:
        """
        每日投放出入口任务量
        """
        detail = self._get_detail()
        detail = [{
            '城市': item[0],
            '口量级': item[1],
            '面量级': item[2],
        } for item in detail]
        header = f"""
        投放出入口任务量：{sum([item['口量级'] for item in detail])}, 面数量：{sum([item['面量级'] for item in detail])}
        """
        return Report(header=header, detail=detail)

    def _get_detail(self) -> list:
        ctx = self.ctx

        qry = f"""
                SELECT 
                    t2.city, count(distinct t3.id), count(distinct t1.id)
                FROM 
                    {ctx.park_ach_tab} t1
                JOIN 
                    {ctx.pushed_tab} t2
                ON 
                    t1.info_id = t2.info_id
                JOIN 
                    park_access_intelligence  t3
                ON 
                    t1.id = t3.outside_task_id
                WHERE 
                    t2.info_id not in (select info_id from {ctx.pushed_tab} where batch = '') 
                    and t3.type='park_access_gate_front' and t3.status='PUSHED' and t3.created_at > '{self.day}' 
                GROUP BY 
                    t2.city
                """
        res = ctx.poi_db.fetchall(qry)
        return res
