"""
每日投放核实面任务量
"""


from src.parking.storefront.flow.config import sort_by_city
from src.parking.storefront.flow.monitor.model import Monitor, Report


class PushedVerifiedNumDailyMonitor(Monitor):
    """
    每日投放核实面任务量
    """

    def monitor(self) -> Report:
        """监控"""
        ctx = self.ctx

        qry = f"""
        select 
        COUNT(*) AS total_count,
        COUNT(CASE WHEN pushed_times > 1 THEN 1 END) AS pushed_more_than_once, 
        city 
        from {ctx.pushed_tab} 
        where created_at > '{self.day}' and status in ('PUSHED', 'CALLBACKED') group by city
        """
        res = ctx.poi_db.fetchall(qry)

        detail = [{'city': item[2], 'num': item[0], 'repeat_pushed': item[1]} for item in res]
        cities = [item['city'] for item in detail]
        detail = sort_by_city(cities, detail)

        num = sum(item['num'] for item in detail)
        repeated_num = sum(item['repeat_pushed'] for item in detail)
        txt = f'投放核实面任务量：{num}; 重复投放的：{repeated_num}'
        return Report(header=txt, detail=detail)

