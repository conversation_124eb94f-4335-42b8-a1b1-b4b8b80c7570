"""
道路末端要素监控
"""
from datetime import datetime, timedelta

from src.parking.storefront.flow.monitor.model import Monitor, Report


class RoadItemMonitor(Monitor):
    """
    道路要素监控
    """

    def monitor(self) -> Report:
        """监控"""
        reasons = {
            'Node_Block': '路障',
            'Link_Block': 'link上有阻断信息|阻断link',
            'Lack_Road': '出口link|出入口关联的LINK为出口方向|步行路|缺路戳点|缺失link|非机动车道',
            'Gate_Pass': '出口大门|待修复大门',
        }
        detail = []
        for _reason, _detail in reasons.items():
            detail.append(self._get_reason_detail(_reason, _detail))
        return Report(header="末端要素闭环详情", detail=detail)

    def _get_reason_detail(self, reason: str, detail: str) -> dict:
        """
        路障信息
        累计路障情报，闭环路障情报数，本周闭环阻断情报数, 本周闭环上量停车场个数
        """
        ctx = self.ctx

        day1 = datetime.today() - timedelta(days=7)

        qry1 = f"""
        SELECT 
    COUNT(*) FILTER (WHERE reason = '{reason}' AND status != 'CANCEL' and prev_tab = '{ctx.park_ach_tab}') AS total_count,
    COUNT(*) FILTER (WHERE reason = '{reason}' AND status = 'DONE' and prev_tab = '{ctx.park_ach_tab}') AS done_count,
    COUNT(*) FILTER (WHERE reason = '{reason}' AND status = 'DONE' AND updated_at > '{day1}' and prev_tab = '{ctx.park_ach_tab}') AS done_on_week,
    COUNT(*) FILTER (WHERE reason = '{reason}' AND status != 'CANCEL' AND created_at > '{day1}' and prev_tab = '{ctx.park_ach_tab}') AS push_on_week
FROM {ctx.repair_tab}
        """
        qry2 = f"""
        SELECT 
    COUNT(*) FILTER (
        WHERE EXISTS (
            SELECT 1 FROM {ctx.repair_tab} b 
            WHERE a.id::text = b.prev_id AND reason = '{reason}' and prev_tab = '{ctx.park_ach_tab}' 
        )
    ) AS total_online,

    COUNT(*) FILTER (
        WHERE EXISTS (
            SELECT 1 FROM {ctx.repair_tab} b 
            WHERE a.id::text = b.prev_id AND reason = '{reason}' and prev_tab = '{ctx.park_ach_tab}' 
            AND updated_at > '{day1}'
        )
    ) AS online_on_week 
FROM {ctx.park_ach_tab} a
WHERE status IN ('ONLINE', 'OFFLINE', 'READY_OFFLINE') and bid_status = 'effected';
        """
        qry3 = f"""
        select 
        count(*) FILTER (
            where exists (
                SELECT 1 FROM {ctx.repair_tab} b 
                WHERE a.id::text = b.prev_id AND reason = '{reason}' and prev_tab = '{ctx.access_ach_tab}' 
            )
        ) as total_online, 
        count(*) FILTER (
            WHERE EXISTS (
                SELECT 1 FROM {ctx.repair_tab} b 
                WHERE a.id::text = b.prev_id AND reason = '{reason}' and prev_tab = '{ctx.access_ach_tab}' 
                AND updated_at > '{day1}'
            )
        ) as online_on_week 
        from {ctx.access_ach_tab} a where exists (
            select 1 from {ctx.park_ach_tab} b 
            where a.parent_bid = b.bid and b.status in ('ONLINE', 'OFFLINE', 'READY_OFFLINE') and bid_status = 'effected' 
        )
        """
        qry5 = f"""
        select count(*) from {ctx.access_ach_tab} a
        where status = 'CHECK_FAILED' and check_memo SIMILAR to '%({detail})%' 
        and not exists (
            select 1 from {ctx.repair_tab} b where a.id::text = b.prev_id 
            and prev_tab = '{ctx.access_ach_tab}' and reason = '{reason}' 
        )
        """
        res1 = ctx.poi_db.fetchone(qry1)
        res2 = ctx.poi_db.fetchone(qry2)
        res3 = ctx.poi_db.fetchone(qry3)
        res5 = ctx.poi_db.fetchone(qry5)
        return {
            'reason': reason,
            '投放总量(口)': res1[0],
            '本周投放总量(口)': res1[3],
            '未投放总量(口)': res5[0],
            '总闭环量(口)': res1[1],
            '本周闭环量(口)': res1[2],
            '总上线量(口)': res3[0],
            '本周上线量(口)': res3[1],
            '总上线量(面)': res2[0],
            '本周上线量(面)': res2[1],
        }
