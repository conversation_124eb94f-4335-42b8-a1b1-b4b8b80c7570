"""
图灵回传监控
"""
import dataclasses
import logging
import time
from datetime import datetime
from typing import Dict

import requests

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.db import array_chunk


@dataclasses.dataclass
class TuringResult:
    """
    图灵结果
    """
    turing_id: str
    source_id: str
    source: str
    update_timestamp: int
    status: int
    update_time: str = ''

    def __post_init__(self):
        self.update_time = datetime.fromtimestamp(self.update_timestamp).strftime("%Y-%m-%d %H:%M:%S")

    def is_done(self) -> bool:
        """
        是否已结束，验收完成表示已结束
        """
        return self.status == 121

    def is_leaked(self) -> bool:
        """
        是否被遗漏，验收超过 6 个小时，还没有被拉回来，那么就算遗漏
        """
        if not self.is_done():
            return False
        current = int(time.time())  # 获取当前时间戳
        if current - self.update_timestamp > 6 * 3600:
            return True
        return False


def request_turing_detail(turing_ids: list) -> dict:
    """
    请求图灵详情
    """
    if len(turing_ids) == 0:
        return {}
    url_pref = 'https://stargaze.map.baidu.com/stargaze/api/getintelligence?iid='
    url = f"{url_pref}{','.join(turing_ids)}"
    payload = {}
    headers = {}

    try:
        response = requests.request("GET", url, headers=headers, data=payload)
        res_json = response.json()
        # print(url)
        print(res_json['errmsg'])
        if '非法' in res_json['errmsg']:
            print(url)
        # exit()
        return res_json['data']
    except Exception as e:
        logging.exception(e)
        return {}


def get_leaked_id2turing_result(turing_ids: list) -> Dict[str, TuringResult]:
    """
    获取遗漏的图灵结果
    """
    response = {}
    turing_id_chunks = array_chunk(turing_ids, 29)
    for _turing_id_chunk in turing_id_chunks:
        id2detail = request_turing_detail(_turing_id_chunk)
        if not id2detail:
            continue
        for tid, detail in id2detail.items():
            result = TuringResult(
                turing_id=tid,
                source_id=detail['sourceid'],
                source=detail['origin_source'],
                update_timestamp=detail['updatetime'],
                status=detail['status'],
            )
            print(tid, result.is_leaked())
            if result.is_leaked():
                response[tid] = result
    return response


class TuringCallbackMonitor(Monitor):
    """
    图灵回传监控
    """

    def monitor(self) -> Report:
        """监控"""
        area_turing_ids = self._get_area_no_callback_turing_ids()
        access_turing_ids = self._get_access_no_callback_turing_ids()
        all_turing_ids = area_turing_ids + access_turing_ids

        tid2leaked_detail = get_leaked_id2turing_result(all_turing_ids)

        detail = [dataclasses.asdict(turing_result) for turing_result in tid2leaked_detail.values()]
        header = f"有{len(detail)}个情报，图灵已验收，但没有拉回作业数据；"
        return Report(header=header, detail=detail)

    def _get_area_no_callback_turing_ids(self) -> list:
        """
        获取面没有回传的图灵 id
        """
        qry = f"""
        select verify_id from {self.ctx.pushed_tab} a
        where status = 'PUSHED' and not exists(
            select 1 from parking_turing_result b 
            where a.verify_id = b.turing_id
        )
        """
        return self.ctx.poi_db.get_values(qry)

    def _get_access_no_callback_turing_ids(self) -> list:
        """
        获取口没有回传的图灵 id
        """
        qry = f"""
        select distinct outside_id from park_access_intelligence a
        where status = 'PUSHED' and remark = 'turing' 
        and not exists (
            select id from parking_turing_result b where a.outside_id = b.turing_id
        )
        """
        add_turing_ids = self.ctx.poi_db.get_values(qry)

        qry = f"""
        select user_id from {self.ctx.repair_tab} a 
        where status = 'ING' and reason = 'ACCESS_CHECK_FAILED' and prev_tab = '{self.ctx.park_ach_tab}' 
        and not exists (
            select id from parking_turing_result b where a.user_id = b.turing_id
        )
        """
        upd_turing_ids = self.ctx.poi_db.get_values(qry)
        return add_turing_ids + upd_turing_ids

