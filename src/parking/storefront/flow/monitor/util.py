"""
常用工具函数
"""
import json
import logging
import os
import time
import requests

from src.parking.storefront.flow.db import join_int
from src.parking.storefront.flow.context import Context

HOUR = 3600
DAY = 24 * HOUR


def get_cur_time(_format: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    获取当前时间
    """
    return time.strftime(_format, time.localtime(time.time()))


def get_pt_id_by_qb_ids(ctx: Context, qb_ids):
    """
    获取对应的 ptid
    """
    if len(qb_ids) == 0:
        return {}

    qry = f"""
    select content, qb_id from qb_feedback_records where qb_id in ({join_int(qb_ids)})
    """
    res = ctx.poi_db.fetchall(qry)
    id2ptid = {}
    for item in res:
        try:
            content = json.loads(item[0])
            ptid = content['resp']['data'][0]['ptid']
        except Exception as e:
            logging.exception(e)
            ptid = ''
        id2ptid[item[1]] = ptid
    return id2ptid


def upload_file(file):
    """
    上传文件
    """
    url = 'http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?space=fenglei&method=postfile&file='
    cmd = f"curl -F 'file=@{file}' '{url}{file}'"
    print(f"执行命令：{cmd}")
    res = os.popen(cmd).read()
    res = str(res).split('\n')
    uid = res[0] if len(res) > 0 else ''
    if uid == '':
        return ''
    return f'http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/fileserver?method=getfile&space=fenglei&uuid={uid}'


def send_hi(message: str):
    """
    发送如流消息
    markdown内容长度超过2048个字符
    """
    print("send_hi")
    # exit()
    # http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d9733bf6e9a551e1a03f619b072f3c051
    # http: // apiin.im.baidu.com / api / msg / groupmsgsend?access_token = d604dbffa81d6a1e7abcdda4d2f78eade
    # http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d604dbffa81d6a1e7abcdda4d2f78eade
    token = 'd604dbffa81d6a1e7abcdda4d2f78eade'
    # token = 'd604dbffa81d6a1e7abcdda4d2f78eade'
    msgs = [{"type": "MD", "content": message}]
    webhook = f"http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token={token}"
    message = {"message": {"body": msgs}}
    resp = requests.post(webhook, json=message)
    print(resp.json())
    return resp.json()


def send_hi_by_contents(contents: list):
    """
    发送如流消息
    """
    bf_msg = ''
    hi_msg = []
    for _content in contents:
        if len(bf_msg) > 1500:
            hi_msg.append(bf_msg)
            bf_msg = ''
            continue
        if bf_msg == '':
            bf_msg = _content
        else:
            bf_msg += f"\n{_content}"
    if bf_msg != '':
        hi_msg.append(bf_msg)
    for _hi in hi_msg:
        send_hi(_hi)
        time.sleep(10)

