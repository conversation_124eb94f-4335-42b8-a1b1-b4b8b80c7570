"""
每周的漏斗
"""
from datetime import datetime, timedelta

from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.context import Context


class WeekProgressMonitor(Monitor):
    """
    每日进展
    """

    def __init__(self, ctx: Context, week_num: int = 8):
        super().__init__(ctx)
        self.weeks = get_latest_8_weeks()

    def monitor(self) -> Report:
        """
        监控
        """
        status2reason = {
            'INIT': '初始化(OR无临街POI)',
            'INITED_FAILED': '中心线生成失败',
            'INITED_BASE': '待生成 BID',
            'INITING_BID': '生成 BID 中',
            'INITED_BID': '已生成 BID',
            'INIT_BID_FAILED': 'BID 生成失败，点在面内',
            'AREA_CHECK_FAILED': '面质检失败',
            'READY': '待挖掘出入口',
            'PARK_ACCESS_INTELLIGENCE': '已挖掘出入口情报',
            'PARK_ACCESS_INTELLIGENCE_PUSHED': '已推送出入口情报',
            'READY_ONLINE': '待上线质检',
            'CHECK_FAILED': '质检失败',
            'REPAIRING': '更新修复中',
            'REPAIRED': '更新已修复',
            'CHECK_SUCCEED': '待上线',
            'READY_OFFLINE': '待下线',
            'OFFLINE': '已下线',
            'COMING_ONLINE': '上线中',
            'ONLINE': '已上线',
        }
        detail = []
        for week in self.weeks:
            start, end = week.values()
            week_detail = self._get_week_detail(start, end)
            item = {'week': f"{start}-{end}"}

            for status, reason in status2reason.items():
                item[f"{reason}({status})"] = week_detail.get(status, 0)
            detail.append(item)
        return Report(header="每周进展", detail=detail)

    def _get_week_detail(self, start_day: str, end_day: str) -> dict:
        """
        每周详情
        """
        ctx = self.ctx

        qry = f"""
        select count(*), status 
        from {ctx.park_ach_tab}
        where bid_status = 'effected' 
            and created_at >= '{start_day} 00:00:00' and created_at <= '{end_day} 23:59:59'
        group by status
        """
        res = ctx.poi_db.fetchall(qry)
        return {item[1]: item[0] for item in res}


def get_latest_8_weeks():
    """
    获取最近 8 周的
    """
    today = datetime.today()
    # 找到本周的周一
    this_monday = today - timedelta(days=today.weekday())

    weeks = []
    for i in range(7, -1, -1):  # 从最早的一周到本周
        start_date = this_monday - timedelta(weeks=i)
        end_date = start_date + timedelta(days=6)
        weeks.append({
            'start': start_date.strftime('%Y-%m-%d'),
            'end': end_date.strftime('%Y-%m-%d')
        })
    weeks.append({
            'start': '2024-11-01',
            'end': '2026-11-01',
        })
    return weeks
