"""
作业进展监控
"""
import html
import multiprocessing
import re

from src.parking.storefront.approval import icafe
from src.parking.storefront.flow.monitor.model import Monitor, Report
from src.parking.storefront.flow.config import get_cities_sort, sort_by_city
from src.parking.storefront.flow.context import gen_ctx, Context
from src.parking.storefront.flow.manual import multi_juejin_pusher as multi_juejin


def get_multi_city_detail(cities: list) -> list[dict]:
    """
    获取多个城市
    """
    with multiprocessing.Pool(processes=10) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.map(_get_city_detail, cities)

    cities = [item['city'] for item in results]
    return sort_by_city(cities, results)


def _get_city_detail(city: str) -> dict:
    ctx = gen_ctx(autocommit=True, debug=True)
    monitor = WorkProgressMonitor(ctx=ctx)
    detail = monitor.get_city_detail(city)

    return {
        'city': city,
        'detail': detail,
    }


class WorkProgressMonitor(Monitor):
    """
    作业进展监控
    """

    def monitor(self) -> Report:
        """监控"""
        cities = get_cities_sort()
        detail = []
        verified_time, verified_num = get_city_verified_info(False)
        patch3_verified_time, _ = get_city_verified_info(True)
        patch3_verified_detail = get_patch3_city_verify_detail()

        results = get_multi_city_detail(cities)
        for _res in results:
            _city = _res['city']
            item = _res['detail']

            item['掘金完成时间'] = verified_time[_city] if _city in verified_time else '未完成'
            item['V3掘金完成时间'] = patch3_verified_time[_city] if _city in patch3_verified_time else '未完成'
            item['V3面数'] = patch3_verified_detail[_city]['area'] if _city in patch3_verified_detail else 0
            item['V3图片数'] = patch3_verified_detail[_city]['image'] if _city in patch3_verified_detail else 0
            item['待准人核实面数量'] = verified_num.get(_city, 0)
            key_sorted = [
                '城市',
                '面情报量', '面掘金图片量', 'V3面数', 'V3图片数', 'V3掘金完成时间', '掘金完成时间',
                '待准人核实面数量', '准入核实面数量',
                '投多图核实量', '投多图核实比例', '多图核实无效量', '多图核实去无效比例',
                '投放图灵核实量', '核实面比例',
                '面-是', '面-错', '面-否', '面-开放式', '面-有效率',
                ('出入口情报有口', '入口挖掘完成数量'), '出入口情报量级', '面出入口情报平均值', '出入口有效情报量级', '出入口成果量级', '出入口情报有效率',
                '成果量级', '有口量级', ('无口量级1', '无入口面数量'),
                '已推送数量', '未推送数量', '面更新数量', '口更新数量',
            ]
            result = {}
            used_keys = []
            for val in key_sorted:
                if isinstance(val, tuple):
                    old_key, new_key = val
                else:
                    old_key, new_key = val, val
                result[new_key] = item[old_key]
                used_keys.append(old_key)

            # 没有要求排序的 key，放在最后面
            for key, val in item.items():
                if key in used_keys:
                    continue
                result[key] = val
            detail.append(result)
        return Report(header='作业进展监控', detail=detail)

    def get_city_detail(self, city):
        """获取城市详情"""
        ctx = self.ctx

        ach_num_qry = f"""
            select count(*) from park_storefront_prod_parking where city = '{city}' and bid_status = 'effected' 
            """
        no_access_qry = f"""
            select count(*) from park_storefront_prod_parking a
            where city = '{city}' and bid_status = 'effected' 
            and (status = 'NO_ACCESS' or (status = 'OFFLINE' and not exists (
            select 1 from {ctx.access_ach_tab} b where a.bid = b.parent_bid and b.status != 'CANCEL' 
            )))
            """
        no_access_qry2 = f"""
                select count(*) from park_storefront_prod_parking where city = '{city}' and status = 'PARK_ACCESS_INTELLIGENCE_PUSHED' and bid_status = 'effected' ;
            """
        have_access_qry = f"""
            select count(*) from park_storefront_prod_parking where city = '{city}' 
            and status in ('READY_ONLINE', 'CHECK_SUCCEED', 'CHECK_FAILED', 'COMING_ONLINE', 'ONLINE', 'ALREADY_OFFLINE', 'ALREADY_ONLINE') 
            and bid_status = 'effected' 
            """

        access_info_have_qry = f"""
            select count(distinct bid) from park_access_intelligence 
            where type='park_access_gate_front'  and strategy != 'NO_PARK_ACCESS' 
            and bid in(select bid from park_storefront_prod_parking where city='{city}')
            """
        access_info_num_qry = f"""
        select count(*) from park_access_intelligence 
        where status = 'PUSHED' and type='park_access_gate_front' 
        and bid in(
            select bid from park_storefront_prod_parking 
            where city='{city}' and bid_status = 'effected' and bid not like '%bak%' and bid != ''
        ) 
        """
        access_info_effected_num_qry = f"""
                select count(*) from park_access_intelligence 
                where status = 'PUSHED' and type='park_access_gate_front' 
                and bid in(
                    select bid from park_storefront_prod_parking 
                    where city='{city}' and bid_status = 'effected' and bid not like '%bak%' and bid != ''
                ) and conclusion = '2' 
                """
        access_ach_num_qry = f"""
        select count(*) from {ctx.access_ach_tab} a 
        where status != 'CANCEL' and exists (
            select 1 from {ctx.park_ach_tab} b 
            where a.parent_bid = b.bid and bid_status = 'effected' 
            and city='{city}'
        )
        """

        access_info_not_qry = f"""
            select count(distinct bid) from park_access_intelligence 
            where type='park_access_gate_front'  and strategy = 'NO_PARK_ACCESS' 
            and bid in(select bid from park_storefront_prod_parking where city='{city}')
            """
        park_pushed_num = f"""
            select count(*) from {ctx.park_ach_tab} 
            where bid_status = 'effected' and city = '{city}' and status = 'ONLINE' 
            """
        park_not_pushed_num = f"""
            select 
                COUNT(
                    CASE WHEN 
                    memo like '%Gate_Pass%'
                    THEN 1 END
                ) AS gate_pass,
                COUNT(
                    CASE WHEN 
                    memo like '%Lack_Road%' and memo not like '%出入口修复次数过多%'
                    THEN 1 END
                ) AS lack_road,
                COUNT(
                    CASE WHEN 
                    memo like '%Link_Block%'
                    THEN 1 END
                ) AS link_block,
                COUNT(
                    CASE WHEN 
                    memo like '%Node_Block%'
                    THEN 1 END
                ) AS node_block,
                COUNT(
                    CASE WHEN 
                    memo like '%Lack_Road 出入口修复次数过多%'
                    THEN 1 END
                ) AS lack_road_more,
                COUNT(
                    CASE WHEN 
                    memo like '%面的出入口修复次数过多%'
                    THEN 1 END
                ) AS access_repaire_more,
                COUNT(
                    CASE WHEN 
                    memo = '' 
                    THEN 1 END
                ) AS status_not_ok, 
                COUNT(
                    CASE WHEN 
                        memo NOT LIKE '%Gate_Pass%' AND
                        memo NOT LIKE '%Lack_Road%' AND
                        memo NOT LIKE '%Link_Block%' AND
                        memo NOT LIKE '%Node_Block%' AND
                        memo NOT LIKE '%Lack_Road 出入口修复次数过多%' AND
                        memo NOT LIKE '%面的出入口修复次数过多%' AND
                        memo != ''
                    THEN 1 END
                ) AS other
            from {ctx.park_ach_tab} 
            where bid_status = 'effected' and city = '{city}' 
            and status not in ('ONLINE', 'OFFLINE', 'READY_OFFLINE', 'NOT_PUSH', 'CHECK_SUCCEED', 'COMING_ONLINE') 
            """
        park_upd_num = f"""
            select count(*) from {ctx.park_ach_tab} a 
            where bid_status = 'effected' and city = '{city}' 
            and exists (
                select 1 from {ctx.repair_tab} b 
                where a.id::text = b.prev_id and prev_tab = '{ctx.park_ach_tab}' 
                and user_name = '{ctx.pushed_tab}' 
            ) 
            """
        access_upd_num = f"""
            select count(*) from {ctx.access_ach_tab} a 
            where exists (
                select 1 from {ctx.repair_tab} b 
                where a.id::text = b.prev_id and prev_tab = '{ctx.access_ach_tab}' 
                and user_name = 'turing' and exists(
                select 1 from {ctx.repair_tab} c 
                where b.user_id = c.user_id and c.prev_tab = '{ctx.park_ach_tab}' 
                and c.reason = 'ACCESS_CHECK_FAILED' 
                )
            ) and exists (
            select 1 from {ctx.park_ach_tab} b where a.parent_bid = b.bid and b.city = '{city}'
            )
            """
        area_work_sql = f"""
        SELECT 
            COUNT(CASE WHEN recall = '1' AND precise = '1' THEN 1 END) AS yes,
            COUNT(CASE WHEN recall = '1' AND precise != '1' THEN 1 END) AS wrong,
            COUNT(CASE WHEN sub_result != '1' OR (sub_result = '1' and recall not in ('1', '2')) THEN 1 END) AS no, 
            COUNT(CASE WHEN recall = '2' THEN 1 END) AS other,
            COUNT(CASE WHEN recall = '1' AND precise != '1' and (not st_isvalid(t1.geom) or st_geometrytype(t1.geom) != 'ST_Polygon') THEN 1 END) AS error
        FROM 
            {ctx.turing_result_tab} t1
        JOIN 
            {ctx.pushed_tab} t2
        ON 
            t1.info_id = t2.info_id
        WHERE 
            t2.info_id not in (select info_id from {ctx.pushed_tab} where batch like '%test%') 
            and t2.city = '{city}' 
        """
        area_work_res = ctx.poi_db.fetchone(area_work_sql)

        area_verified_sql = f"""
        select count(distinct b.id)
from park_storefront_task a
join park_storefront_strategy b on a.task_id = b.task_id
where b.step = 'VERIFIED' and a.city = '{city}';
        """

        area_info_sql = f"""
        with t as (
    select a.batch, b.batch_image, a.task_id, b.id, jsonb_array_elements_text(result->'segment_ids') as seg_id
    from park_storefront_task a
    join park_storefront_strategy b on a.task_id = b.task_id
    where b.step = 'PRIME' and a.city = '{city}' 
)
select 
    count(distinct t.id) as info_num, 
    count(distinct c.image_name) as img_num
from t
join park_storefront_image c on t.seg_id = c.segment_id;
        """
        area_info_res = ctx.poi_db.fetchone(area_info_sql)

        area_push_sql = f"""
        select count(*) from {ctx.pushed_tab} 
        where city = '{city}' and batch not like '%test%'
        """

        queries = {
            '成果量级': ach_num_qry,
            '有口量级': have_access_qry,
            '无口量级1': no_access_qry,
            '无口量级2': no_access_qry2,
            '出入口情报有口': access_info_have_qry,
            '出入口情报量级': access_info_num_qry,
            '出入口有效情报量级': access_info_effected_num_qry,
            '出入口成果量级': access_ach_num_qry,
            '出入口情报无口': access_info_not_qry,
            '已推送数量': park_pushed_num,
            # '未推送数量': park_not_pushed_num,
            '面更新数量': park_upd_num,
            '口更新数量': access_upd_num,
            '准入核实面数量': area_verified_sql,
            '投放图灵核实量': area_push_sql,
        }
        resp = {
            '城市': city,
        }
        for name, qry in queries.items():
            res = ctx.poi_db.fetchone(qry)
            num = res[0]
            resp[name] = num
        resp['面-是'] = area_work_res[0]
        resp['面-错'] = area_work_res[1]
        resp['面-否'] = area_work_res[2]
        resp['面-开放式'] = area_work_res[3]
        resp['面-有效率'] = _calc_rate(resp['面-是'] + resp['面-错'], resp['面-是'] + resp['面-错'] + resp['面-否'])

        resp['面情报量'] = area_info_res[0]
        resp['面掘金图片量'] = area_info_res[1]
        resp['核实面比例'] = _calc_rate(resp['准入核实面数量'], resp['面情报量'])
        resp['出入口情报有效率'] = _calc_rate(resp['出入口有效情报量级'], resp['出入口情报量级'])
        resp['面出入口情报平均值'] = _calc_rate(resp['出入口情报量级'], resp['出入口情报有口'])

        # 未推送的量级
        not_pushed_res = ctx.poi_db.fetchone(park_not_pushed_num)
        not_pushed_num = sum(not_pushed_res)
        resp['未推送-Gate_Pass'] = not_pushed_res[0]
        resp['未推送-Lack_Road'] = not_pushed_res[1]
        resp['未推送-Link_Block'] = not_pushed_res[2]
        resp['未推送-Node_Block'] = not_pushed_res[3]
        resp['未推送-Lack_Road-修复多次'] = not_pushed_res[4]
        resp['未推送-面的出入口修复次数过多'] = not_pushed_res[5]
        resp['未推送-状态未就绪'] = not_pushed_res[6]
        resp['未推送-其他'] = not_pushed_res[7]
        resp['未推送数量'] = not_pushed_num

        # 多图核实
        multi_verified_detail = _get_city_multi_verified_detail(self.ctx, city)
        resp.update(multi_verified_detail)
        resp['投多图核实比例'] = _calc_rate(resp['投多图核实量'], resp['准入核实面数量'])
        resp['多图核实去无效比例'] = _calc_rate(resp['多图核实无效量'], resp['准入核实面数量'])
        return resp


def _get_city_multi_verified_detail(ctx: Context, city: str) -> dict:
    qry = f"""
    SELECT 
        COUNT(
            CASE WHEN 
                b.status in ('{multi_juejin.VERIFY_STATUS_ING}', '{multi_juejin.VERIFY_STATUS_DONE}')  
            THEN 1 END
        ) AS verifying,
        COUNT(
            CASE WHEN 
                b.status = '{multi_juejin.VERIFY_STATUS_DONE}' 
                and b.conclusion in ('33', '34')
            THEN 1 END
        ) AS verify_invalid
    FROM 
        park_storefront_strategy AS a
    LEFT JOIN LATERAL (
        SELECT status, conclusion 
        FROM park_storefront_strategy_multi_verify AS b
        WHERE b.strategy_id = a.id and batch_id not like 'test%' 
        ORDER BY b.id ASC
        LIMIT 1
    ) AS b ON TRUE 
    WHERE task_id in (
        select task_id from park_storefront_task 
        where city = '{city}' 
    ) and step = 'VERIFIED' 
    """
    res = ctx.poi_db.fetchone(qry)
    return {
        '投多图核实量': res[0] if len(res) else 0,
        '多图核实无效量': res[1] if len(res) else 0,
    }


def _calc_rate(a, b) -> float:
    if b == 0:
        return 0
    return a / b


def get_city_verified_info(is_patch3: bool = False) -> tuple:
    """
    获取城市掘金核实完成时间
    如果一个城市还有掘金未核实完的，那么就认为没有完成
    """

    def filter_issues(cards) -> list:
        """
        过滤卡片
        """
        resp = []
        for c in cards:
            if is_patch3 and 'patch3' not in c.title:
                continue
            if not is_patch3 and 'patch3' in c.title:
                continue
            resp.append(c)
        return resp

    push_statuses = [
        icafe.CARD_STATUS_PUSH_PUBLISH_DONE,
        icafe.CARD_STATUS_PUSH_APPROVAL_DONE,
        # icafe.CARD_STATUS_PUSH_VERIFIED_DONE,
        icafe.CARD_STATUS_PUSH_APPROVAL_DOING,
    ]
    issues = icafe.fetch_issues(icafe.CARD_TYPE_PUSH, push_statuses)
    issues = filter_issues(issues)

    verifying_cities = set()  # 还在核实中的城市
    for i in issues:
        verifying_cities.add(get_city_from_title(i.title))

    pull_statuses = [
        icafe.CARD_STATUS_PULL_APPROVAL_DOING,
        icafe.CARD_STATUS_PULL_APPROVAL_DONE,
        icafe.CARD_STATUS_PULL_PUBLISH_DONE,
        icafe.CARD_STATUS_PULL_PUBLISH_READY,
        icafe.CARD_STATUS_PULL_SAVE_TO_DB,
    ]
    issues = icafe.fetch_issues(icafe.CARD_TYPE_PULL, pull_statuses)
    issues = filter_issues(issues)

    city2max_date = {}  # 每个城市保留最大的日期
    city2verify_num = {}
    for i in issues:
        city = get_city_from_title(i.title)
        verify_num = extract_verify_num(html.unescape(i.detail))
        city2verify_num[city] = city2verify_num.setdefault(city, 0) + verify_num
        if city in verifying_cities:
            continue
        date = i.created_at
        if city not in city2max_date:
            city2max_date[city] = date

        if city2max_date[city] < date:
            city2max_date[city] = date
    return city2max_date, city2verify_num


def extract_verify_num(detail) -> int:
    """提取核实面数量"""
    match = re.search(r'新增：(\d+)', detail)
    if match:
        result = int(match.group(1))
        return result
    return 0

def get_city_from_title(title: str) -> str:
    """
    从 title 中获取城市名称
    【门前 - 准出报告】银川市 - yinchuan_2025q2top46top150_20250325_bua_area_top80_20250405
    """
    return str(title.split(' - ')[1]).split('】')[1]


def get_patch3_city_verify_detail() -> dict:
    """
    获取批次 3，城市详情
    """

    def extract_summary(text):
        match = re.search(r'<blockquote>(\d+)\s*个街区，(\d+)\s*个面，(\d+)\s*个观察点，(\d+)\s*张图片</blockquote>', text)
        if match:
            return {
                'street': int(match.group(1)),
                'area': int(match.group(2)),
                'point': int(match.group(3)),
                'image': int(match.group(4)),
            }
        return {}

    push_statuses = [
        icafe.CARD_STATUS_PUSH_PUBLISH_DONE,
        icafe.CARD_STATUS_PUSH_APPROVAL_DONE,
        icafe.CARD_STATUS_PUSH_VERIFIED_DONE,
        icafe.CARD_STATUS_PUSH_APPROVAL_DOING,
    ]
    issues = icafe.fetch_issues(icafe.CARD_TYPE_PUSH, push_statuses)

    city2detail = {}
    for i in issues:
        if 'patch3' not in i.title:
            continue
        city = get_city_from_title(i.title)
        detail = extract_summary(html.unescape(i.detail))
        if city not in city2detail:
            city2detail[city] = detail
        else:
            keys = ['street', 'area', 'point', 'image']
            for _key in keys:
                city2detail[city][_key] = detail[_key]
    return city2detail


if __name__ == '__main__':
    _res = _get_city_multi_verified_detail(gen_ctx(autocommit=True, debug=True), city='云浮市')
    print(_res)

