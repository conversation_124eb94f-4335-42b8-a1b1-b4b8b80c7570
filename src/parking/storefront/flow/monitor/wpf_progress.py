"""
环节进展
"""


from src.parking.storefront.flow.monitor.model import Monitor, Report


class WpfProgressMonitor(Monitor):
    """
    环节进展监控
    """

    def monitor(self) -> Report:
        """
        监控
        """
        area_add_push = self._get_area_add_push()
        area_add_callback = self._get_area_add_callback()
        access_add_push = self._get_access_add_push()
        online = self._get_online()

        detail = [
            {**area_add_push, **area_add_callback, **access_add_push, **online}
        ]
        return Report(header="环节进展", detail=detail)

    def _get_area_add_push(self) -> dict:
        """
        面投放前详情
        """
        ctx = self.ctx
        qry = f"""
        select 
             COUNT(CASE WHEN status = 'INIT' THEN 1 END) AS init,
             COUNT(CASE WHEN status = 'MAINTAINED' THEN 1 END) AS maintained,
             COUNT(CASE WHEN status = 'PUSHED' THEN 1 END) AS pushed
        from {ctx.pushed_tab}  
        where batch not like '%test%' and data_type in ('1209', '1218') 
        """
        res = ctx.poi_db.fetchone(qry)
        return {
            '面情报待准备资料': res[0],
            '面情报待推送': res[1],
            '面情报已推送': res[2],
        }

    def _get_area_add_callback(self) -> dict:
        ctx = self.ctx
        qry1 = f"""
        select count(*) from parking_turing_result 
        where status = 'INIT' and data_type in ('1209', '1218')
        """
        res1 = ctx.poi_db.fetchone(qry1)

        qry2 = f"""
        select count(*) from {ctx.turing_result_tab} 
        where data_type in ('1209', '1218') and sub_result = '1' and recall = '1' and status = 'INIT' 
        """
        res2 = ctx.poi_db.fetchone(qry2)
        return {
            '面待入图灵成果库': res1[0],
            '面待入线下成果库': res2[0],
        }

    def _get_access_add_push(self) -> dict:
        ctx = self.ctx
        qry = f"""
        select 
            COUNT(CASE WHEN status = 'PARK_ACCESS_INTELLIGENCE_PUSHED' THEN 1 END) AS pushed,
            COUNT(CASE WHEN status = 'PARK_ACCESS_INTELLIGENCE' and track_url != '' and zhongyuan_complete and collect_urls_ok THEN 1 END),
            COUNT(CASE WHEN status = 'PARK_ACCESS_INTELLIGENCE' and track_url = '' and zhongyuan_complete and collect_urls_ok THEN 1 END),
            COUNT(CASE WHEN status = 'PARK_ACCESS_INTELLIGENCE' and track_url != '' and not zhongyuan_complete and collect_urls_ok THEN 1 END),
            COUNT(CASE WHEN status = 'PARK_ACCESS_INTELLIGENCE' and track_url != '' and zhongyuan_complete and not collect_urls_ok THEN 1 END),
            COUNT(CASE WHEN 
                (status IN ('READY', 'INITED_BID', 'INITED_BASE')) 
                OR (status = 'INIT' and created_at >= NOW() - INTERVAL '6 hours') 
                OR (status = 'INITING_BID' and created_at >= NOW() - INTERVAL '6 hours')
            THEN 1 END)
        from {ctx.park_ach_tab} 
        where bid_status = 'effected' and park_type = 'close' 
        """
        res = ctx.poi_db.fetchone(qry)
        return {
            '口已推送': res[0],
            '口待推送': res[1],
            '口待推送但轨迹资料未就绪': res[2],  # 等待推送，但轨迹资料没有准备好
            '口待推送但采集资料未就绪': res[3],  # 等待推送，但采集资料没有准备好
            '口待推送但全景资料未就绪': res[4],  # 等待推送，但状态未就绪
            '口待推送但状态未就绪': res[5],  # 等待推送，但状态未就绪
        }

    def _get_online(self) -> dict:
        ctx = self.ctx
        qry1 = f"""
        select count(*) from parking where park_spec in (1, 2) and show_tag = '门前停车场' and status = 1;
        """
        res1 = ctx.back_db.fetchone(qry1)

        qry2 = f"""
        select count(*) from {ctx.park_ach_tab} where status = 'ONLINE' and bid_status = 'effected' 
        """
        res2 = ctx.poi_db.fetchone(qry2)
        return {
            '线下库上线量': res2[0],
            '线上库上线量': res1[0],
            # '上线城市': 0,
        }



