"""
处理下线逻辑
"""
import argparse
import csv
import glob
import json
import os
import re
import time
import uuid
from datetime import datetime

import shapely.wkt
from tqdm import tqdm

from src.parking.storefront.flow.db import ReadDb, DB, join_int, join_str, handle_apostrophe, array_chunk
import src.model_mysql.beeflow_model as beeflow_model
from src.parking.storefront.flow.context import Context, gen_ctx
from src.tools import pgsql
from src.parking.storefront.flow.ach_processor import _gen_central_line
import src.parking.storefront.flow.model.ach as ach
import src.parking.storefront.flow.cmd.generator as cmd_gener
import src.parking.storefront.flow.cmd.executor as executor
from src.parking.storefront.flow.ach_processor import gen_park_access_offline_cmds
from src.parking.storefront.flow.repairer import check_memo
import src.parking.storefront.flow.model.cmd as cmd_model
from src.parking.storefront.flow.filter import overlap
from src.parking.storefront.flow.verifier import cancel, Context as TuringContext, gen_ctx as gen_turing_ctx


def offline_ready_offline(ctx: Context):
    """
    下线将要下线的数据
    且没有添加下线命令（可能是由于历史原因）
    """
    qry = f"""
    select bid, offline_memo from {ctx.park_ach_tab} a
    where bid_status = 'effected' and status = 'READY_OFFLINE' 
    and not exists (
        select 1 from {ctx.park_match_command_tab} b 
        where a.bid = b.guid and b.status != 'END' and b.type = '{cmd_model.CmdType.REMOVE_PARKING}'
    )
    """
    res = ctx.poi_db.fetchall(qry)

    for item in tqdm(res):
        bid, memo = item
        commands = gen_park_access_offline_cmds(ctx, bid, memo)
        cmd_model.add_commands(ctx, commands)
    ctx.poi_db.commit_or_rollback()


def offline_no_access(ctx: Context):
    """
    处理没有口的面
    关注的状态是 CHECK_FAILED, REPAIRING
    可能是口作业下线了所有口
    可能是末端要素闭环，下线了口
    """
    # 当前流程没有正常的口
    qry = f"""
    select id from {ctx.park_ach_tab} a 
--     where bid_status = 'effected' and status in ('CHECK_FAILED') 
    where bid_status = 'effected' and status in ('CHECK_FAILED', 'NO_ACCESS') 
    and not exists (
        select 1 from {ctx.access_ach_tab} b 
        where a.bid = b.parent_bid and b.status in ('READY_ONLINE', 'CHECK_FAILED')
    )
--     and checker = 'offline'  
    """
    ids = ctx.poi_db.get_values(qry)
    parks = ach.get_parking_ach_by_ids(ctx, ids)

    no_effected_access_num = 0
    no_access_num = 0
    offline_num = 0
    are_offline_num = 0
    for a_park in tqdm(parks):
        if a_park.status == 'CHECK_FAILED' and check_memo.park_checked_memo_has_area_err(a_park.check_memo):
            print(f"bid:{a_park.bid}; 面有异常：{a_park.check_memo}")
            continue

        qry = f"""
        select id from {ctx.access_ach_tab} 
        where parent_bid = '{a_park.bid}' and status in ('ALREADY_ONLINE')
        """
        old_access = ctx.poi_db.fetchone(qry)
        if len(old_access) > 0:
            qry = f"""
            update {ctx.park_ach_tab} set status = 'CHECK_SUCCEED' 
            where id = {a_park.park_id} 
            """
            no_effected_access_num += ctx.poi_db.execute(qry)
            print(f"{a_park.bid} 之前上线过，但当前流程没有有效口")
            continue
        accesses = [an_access for an_access in ach.get_park_accesses_by_bid(ctx, a_park.bid) if
                    an_access.status != 'CANCEL']
        no_access = len(accesses) == 0
        are_offline = len([an_access for an_access in accesses if 'OFFLINE' in an_access.status]) == len(accesses)

        offline_memo = ''
        if no_access:
            no_access_num += 1
            print(f"{a_park.bid} 无口面")
            offline_memo += "无口面"
        if not no_access and are_offline:
            are_offline_num += 1
            print(f"{a_park.bid} 都已下线")
            offline_memo += "口都已下线"

        if no_access or are_offline:
            # 无口面或者都已下线
            qry = f"""
            update {ctx.park_ach_tab} set status = 'READY_OFFLINE', offline_memo = '{offline_memo}' 
            where id = {a_park.park_id} 
            """
            offline_num += ctx.poi_db.execute(qry)
            # cmd = cmd_gener.gen_park_offline_cmd(ctx, a_park, 0)
            # cmd.add_after_middlewares(executor.RanCmdExecutor.__name__, [executor.OfflineParkAfter.__name__])
            # offline_num += cmd_model.add_commands(ctx, [cmd])
        else:
            raise Exception(f"{a_park.bid} 不符合预期")
    ctx.poi_db.commit_or_rollback()
    print(
        f"无有效口量：{no_effected_access_num}; 无口面：{no_access_num}; 都已下线量：{are_offline_num}; 下线数量：{offline_num}")


def offline_open_park(ctx: Context):
    """
    下线开放式门前停车场
    """
    qry = f"""
    select id, bid from {ctx.park_ach_tab} 
    where bid_status = 'effected' and status = 'REPAIRED' 
--     and bid = '4709458693118050735'
    """
    res = ctx.poi_db.fetchall(qry)

    aff = 0
    for item in tqdm(res):
        qry = f"""
        select user_id from {ctx.repair_tab} 
        where user_name = 'turing' 
            and prev_tab = '{ctx.park_ach_tab}' and prev_id = '{item[0]}'
        order by id desc
        """
        repair = ctx.poi_db.fetchone(qry)
        if not repair:
            print(f"没有口修复记录：{item}")
            continue
            # exit()

        turing_id = repair[0]
        qry = f"""
                select bp_message from parking_turing_result 
                where turing_id = '{turing_id}'
                """
        res = ctx.poi_db.fetchone(qry)
        msg = json.loads(res[0])
        data_commit = json.loads(msg['data'])
        edit_commit = data_commit['edit_commit']
        parking_conclusion = edit_commit['parking_conclusion']

        if parking_conclusion not in [2]:
            print(f"{item} 不符合预期")
            continue

        # 无出入口门前停车场
        qry = f"""
        update {ctx.park_ach_tab} 
        set status = 'READY_OFFLINE', 
        memo = '开放式门前停车场', memo_time = now() 
        where id = {item[0]}
        """
        aff += ctx.poi_db.execute(qry)
    ctx.poi_db.commit_or_rollback()
    print(aff)


def _get_repair(ctx: Context, park_id: int) -> dict:
    """
    获取修复信息
    """
    repair_qry = f"""
            select id, user_id, user_name, args from {ctx.repair_tab} 
            where prev_id = '{park_id}' and prev_tab = '{ctx.park_ach_tab}' 
            and status = 'ING' order by id desc 
            """
    repair_res = ctx.poi_db.fetchone(repair_qry)
    if not repair_res:
        return {}
    return {
        'id': repair_res[0],
        'user_id': repair_res[1],
        'user_name': repair_res[2],
        'args': repair_res[3],
    }


def _get_iid(ctx: Context, info_pk_id: int) -> str:
    info_qry = f"""
    select verify_id from {ctx.pushed_tab} where id = {info_pk_id} and data_type = '1214' 
    """
    info_res = ctx.poi_db.fetchone(info_qry)
    return info_res[0]


def offline_online_overlap_0609(ctx: Context):
    """
    下线线上数据压盖
    背景：线上数据规格变更导致质检漏检，一批压盖的数据推送上线了
    量级大概在 2k 左右，量级比较大；压盖的数据，需要自动选择哪些上线，哪些不上线
    有自动选择的，除了上下线操作以外，还需要把之前下发人工作业的任务撤销掉
    """
    file = 'tmp/overlap_0609_01.log'

    iid2data = {}
    with open(file, 'r') as hdr:
        reader = csv.reader(hdr)
        next(reader)
        for row in reader:
            memo = row[4]
            if '存在压盖线上面' not in memo:
                print(f"{row[0]}; {memo}；不能走自动选择逻辑")
                continue
            reasons = check_memo.get_area_reasons_en(memo)
            if len(reasons) != 1:
                print(f"{row[0]}; {memo}；存在多个原因，不能走自动选择逻辑")
                continue
            overlap_bids = check_memo.extract_bids_from_area_check_memo(memo)
            if len(overlap_bids) != 1:
                print(f"注意，压盖的数量不是 1，是：{len(overlap_bids)}，交给人工处理")
                continue
            park_can, detail, access_can = overlap.can_auto_choice_online_park(ctx, row[0], overlap_bids[0])
            if not park_can:
                print(f"能自动选择：{park_can}", f"口是否一样：{access_can}", f"detail: {detail}")
                continue

            bids = [row[0], overlap_bids[0]]
            park_aches = ach.get_parking_aches_by_bids(ctx, bids)
            assert len(park_aches) == 2, f"{bids} 在线下库不存在"

            dt1 = park_aches[0].created_at
            dt2 = park_aches[1].created_at
            if dt1 >= dt2:
                online, offline = park_aches[0], park_aches[1]
            else:
                online, offline = park_aches[1], park_aches[0]

            repair_a = _get_repair(ctx, online.park_id)
            repair_b = _get_repair(ctx, offline.park_id)
            if len(repair_a) == 0 or len(repair_b) == 0:
                print(f"{bids} 没有对应的修复信息")
                continue

            if repair_a['user_id'] != repair_b['user_id']:
                raise Exception(f"不是同一个修复者")
            print(f"status:{online.status} vs {offline.status}")

            turing_id = _get_iid(ctx, repair_a['user_id'])
            if turing_id in iid2data:
                print(f"{turing_id} 已经存在了")
                continue
            iid2data[turing_id] = {
                'turing_id': turing_id,
                'user_id': repair_a['user_id'],
                'online': online,
                'offline': offline,
            }
            # break  # todo test
    _cancel_pushed_updated(iid2data)


def _cancel_pushed_updated(iid2data):
    turing_chunks = array_chunk(list(iid2data.values()), 99)
    # print(f'需要取消的量级:{len(iid2data)}; 分{len(turing_chunks)}次取消')
    # exit()
    t_ctx = gen_turing_ctx(env='online')
    for _chunk in tqdm(turing_chunks):
        success, failed = cancel(t_ctx, [_i['turing_id'] for _i in _chunk], 1214)
        # success, failed = [_i['turing_id'] for _i in _chunk], []
        print(f"取消成功的量级：{len(success)}; 取消失败量级：{len(failed)}; 失败明细:{json.dumps(failed)}")

        cancel_num = 0
        ctx = gen_ctx(is_committed=True, debug=True)
        for s_iid in success:
            data = iid2data[s_iid]
            if not _cancel_info(ctx, s_iid, data['user_id'], online=data['online'], offline=data['offline']):
                ctx.poi_db.rollback()
                continue
            ctx.poi_db.commit_or_rollback()
            cancel_num += 1
        print(f"图灵取消成功了：{len(success)}; 信息取消成功了：{cancel_num}")


def _cancel_info(ctx: Context, turing_id: str, info_id: int, online: ach.ParkingAch, offline: ach.ParkingAch):
    def _cancel_pushed():
        # 情报取消掉
        qry = f"""
        update {ctx.pushed_tab} set status = 'CANCEL', cancel_memo = '线上压盖，自动选择上下线' 
        where id = {info_id} 
        and status not in ('CALLBACKED', 'CANCEL')
        """
        aff = ctx.poi_db.execute(qry)
        if aff == 0:
            print(f"{turing_id} 推送情报取消失败")
            return False
        return True

    def _cancel_repair():
        qry = f"""
        update {ctx.repair_tab} set status = 'CANCEL', cancel_memo = '线上压盖，自动选择上下线' 
        where user_id = '{info_id}' and user_name = '{ctx.pushed_tab}' and status = 'ING'
        """
        aff = ctx.poi_db.execute(qry)
        if aff == 0:
            print(f"{turing_id} 取消修复信息失败")
            return False
        return True

    def _cancel_park():
        qry = f"""
        update {ctx.park_ach_tab} set status = 'READY_OFFLINE', offline_memo = '线上压盖，自动选择下线' 
        where bid = '{offline.bid}' and bid_status = 'effected' 
        """
        aff = ctx.poi_db.execute(qry)
        if aff == 0:
            print(f"{turing_id} 下线停车场")
            return False
        return True

    def _online_park():
        qry = f"""
        update {ctx.park_ach_tab} set status = 'ONLINE' 
        where bid = '{online.bid}' and bid_status = 'effected' 
        """
        aff = ctx.poi_db.execute(qry)
        if aff == 0:
            print(f"{turing_id} 下线停车场")
            return False
        return True

    cancel_fns = [
        _cancel_pushed,
        _cancel_repair,
        _cancel_park,
        _online_park,
    ]
    for _fn in cancel_fns:
        if not _fn():
            return False
    return True


def _extract_dup_info(msg: str) -> tuple:
    # 提取主 bid
    main_bid_match = re.search(r"bid=(\d+)", msg)
    main_bid = main_bid_match.group(1) if main_bid_match else None

    # 提取包含的 bid 列表
    sub_bids_match = re.search(r"包含的门前停车场bid=\[([^\]]+)\]", msg)
    sub_bids = eval(f"[{sub_bids_match.group(1)}]") if sub_bids_match else []

    return main_bid, sub_bids


def offline_park_ach_overlap_0620(ctx: Context):
    """
    下线成果压盖
    """
    src = './tmp/park_ach_overlap_0620.tsv'
    iid2data = {}
    with open(src, 'r') as hdr:
        reader = csv.reader(hdr, delimiter='\t')
        next(reader)
        for row in reader:
            can_choice = row[1]
            if can_choice == 'FALSE':
                continue
            main_bid, sub_bids = _extract_dup_info(row[0])
            bids = [main_bid] + sub_bids

            parks = ach.get_parking_aches_by_bids(ctx, bids)
            assert len(parks) == 2, f"{bids} 不是两个停车场"

            choice_bid = row[2]
            if parks[0].bid == choice_bid:
                online, offline = parks[0], parks[1]
            else:
                online, offline = parks[1], parks[0]

            repair_a = _get_repair(ctx, online.park_id)
            repair_b = _get_repair(ctx, offline.park_id)
            if len(repair_a) == 0 or len(repair_b) == 0:
                print(f"{bids} 没有对应的修复信息")
                continue

            if repair_a['user_id'] != repair_b['user_id']:
                print(f"{bids} 没有对应的修复信息")
                continue
                # raise Exception(f"不是同一个修复者")
            print(f"status:{online.status} vs {offline.status}")

            turing_id = _get_iid(ctx, repair_a['user_id'])
            if turing_id in iid2data:
                print(f"{turing_id} 已经存在了")
                continue
            iid2data[turing_id] = {
                'turing_id': turing_id,
                'user_id': repair_a['user_id'],
                'online': online,
                'offline': offline,
            }
            # break  # todo test
        _cancel_pushed_updated(iid2data)


def main():
    """
    主函数
    """
    fns = str(ARGS.fns)
    ctx = gen_ctx(is_committed=True, debug=True)
    if fns == 'offline_ready_offline':
        offline_ready_offline(ctx)
    elif fns == 'offline_no_access':
        offline_no_access(gen_ctx(is_committed=True, debug=True))
    elif fns == 'offline_open_park':
        offline_open_park(gen_ctx(is_committed=True, debug=True))
    elif fns == 'offline_online_overlap_0609':
        offline_online_overlap_0609(gen_ctx(autocommit=True, debug=True))
    elif fns == 'offline_park_ach_overlap_0620':
        offline_park_ach_overlap_0620(gen_ctx(autocommit=True, debug=True))


if __name__ == '__main__':
    # offline_ready_offline(gen_ctx(is_committed=False, debug=True))
    parser = argparse.ArgumentParser(description='下线')
    parser.add_argument(
        '--fns',
        type=str,
        required=True,
        help="执行的函数；import: 导入成果; callback: poi 回传；push: 推送停车场"
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()
