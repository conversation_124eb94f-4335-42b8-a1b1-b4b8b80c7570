"""
刷新 poi 缓存
"""

import hashlib
import json
import time
import uuid

import requests

from src.parking.storefront.flow.db import ReadDb, DB
from src.tools import pgsql


class Context:
    """
    上下文
    """
    env: str

    token: str
    url: str

    poi_slave_db: ReadDb


def gen(env: str) -> Context:
    """
    生成上下文
    """
    ctx = Context()

    ctx.poi_slave_db = ReadDb(config=pgsql.POI_SLAVER_CONFIG)

    if env == 'online':
        ctx.token = 'abcde26f60d2462b8b45a4d868eb8f3d'
        ctx.url = 'http://chenxi.vpn.guoke.baidu.com/zoom_ipm_img/indoormap/test/aoidatapro'
    else:
        ctx.token = 'd5f9f26f60d2462b8b45a4d868eb8f3d'
        ctx.url = 'http://127.0.0.1:8189'
    return ctx


def sign(data: dict, sk: str) -> str:
    """
    生成签名
    :param data: dict
    :param sk: 密钥
    :return: 签名
    """
    concatenated_str = _implode2(data) + sk
    print(concatenated_str)
    return hashlib.md5(concatenated_str.encode('utf-8')).hexdigest()


def _implode2(data: dict) -> str:
    """
    对数据进行排序并拼接成字符串
    :param data: 数据字典
    :return: 拼接后的字符串
    """
    keys = sorted(data.keys())
    result_str = ""
    for key in keys:
        if key == 'authcode':
            continue
        value = data[key]
        if isinstance(value, dict):
            result_str += f"{key}=" + _implode2(value)
        elif isinstance(value, list):
            result_str += f"{key}=" + _implode2({str(i): v for i, v in enumerate(value)})
        elif isinstance(value, bool):
            result_str += f"{key}={str(value).lower()}"
        else:
            result_str += f"{key}={value}"
    return result_str


def get_ttfc(ctx: Context, mesh: str):
    """
    获取 ttfc 数据
    """
    data = _gen_request_param(ctx, mesh)
    print(json.dumps(data))

    url = ctx.url + '/expimp/poi/reference'
    print(url)

    res = requests.post(
        url, json=data
    )
    print(res.json())


def refresh(ctx: Context, mesh: str):
    """
    刷新缓存
    """
    data = _gen_request_param(ctx, mesh)
    print(json.dumps(data))

    url = ctx.url + '/expimp/poi/refresh'
    print(url)

    res = requests.post(
        url, json=data
    )
    print(res.content)
    return res.json()


def _gen_request_param(ctx: Context, mesh: str):
    data = {
        "client_id": "face_client",
        "user_id": f"beeflow_ttfc_ref_{uuid.uuid4().hex}",
        "req_id": 1514,
        "authcode": "",
        "url": "",
        "timestamp": str(int(time.time())),
        "params": {
            "proc_version": "",
            "extra_type": "meshes",
            "extra_way": f"{mesh}",
            "format": "sqlite",
            "compress": 2,
        }
    }
    token = sign(data, ctx.token)
    data['authcode'] = token
    return data


def _get_pois(ctx: Context, bids: list) -> list:
    """
    会过滤不存在的 bid
    """
    def get_poi(bid: str) -> tuple:
        qry = f"select bid, mesh_id from poi where bid = '{bid}'"
        return ctx.poi_slave_db.fetchone(qry)

    resp = []
    for _bid in bids:
        poi = get_poi(_bid)
        if len(poi) == 0:
            continue
        resp.append({
            'bid': poi[0],
            'mesh_id': poi[1],
        })
    return resp


def refresh_by_bids(bids: list) -> list:
    """
    根据 bid 列表刷新缓存，返回刷新过的 bid 列表
    注意，因为缓存是根据 poi 从库生成的，若 bid 在 poi 从库不存在，那么就不会刷新；那么该 bid 就不会在结果中出现
    """
    ctx = gen(env='online')

    pois = _get_pois(ctx, bids)
    if len(pois) == 0:
        return []

    mesh_ids = list(set([item['mesh_id'] for item in pois]))
    resp = refresh(ctx, ','.join(mesh_ids))
    if 'ErrNo' in resp and resp['ErrNo'] == 0:
        return [item['bid'] for item in pois]
    print(f"刷新失败：{resp}")
    return []


def main():
    """
    主函数
    """
    ctx = gen('online')
    # refresh_mesh_cache('595672')
    # get_ttfc(ctx, '595672')
    # get_ttfc(ctx, '374420')
    # refresh(ctx, '374420')
    refresh_by_bids(['10002475308532987757'])


if __name__ == '__main__':
    main()





