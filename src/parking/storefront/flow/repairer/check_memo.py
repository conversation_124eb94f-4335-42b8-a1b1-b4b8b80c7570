"""
质检备注
"""
import abc
import re
from typing import List, Dict

import dataclasses

from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.model import cmd, ach


@dataclasses.dataclass
class Reason:
    """
    失败原因
    """
    text_zh: str
    text_en: str

    def __str__(self):
        return self.text_zh


@dataclasses.dataclass
class AccessReason(Reason):
    """
    出入口失败原因
    """
    log: list = dataclasses.field(default_factory=list)

    @abc.abstractmethod
    def work_directly(self) -> bool:
        """
        不需要修复直接可以作业, 返回 True
        """

    @abc.abstractmethod
    def had_repaired(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        已经修复了，返回 True
        """

    def can_work(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        能作业返回 True
        """
        if self.work_directly():
            return True
        if self.had_repaired(ctx, access):
            return True
        return False


@dataclasses.dataclass
class WorkDirectlyAccessReason(AccessReason):
    """
    可以直接作业的
    """

    def work_directly(self) -> bool:
        return True

    def had_repaired(self, ctx: Context, access: ach.AccessAch) -> bool:
        return True


@dataclasses.dataclass
class LackRoadAccessReason(AccessReason):
    """
    缺路
    """
    text_en = 'Lack_Road'

    def work_directly(self) -> bool:
        """
        是否可以直接作业
        """
        return False

    def had_repaired(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        是否修复了
        """
        qry = f"""
        select id from {ctx.repair_tab}
        where prev_id = '{access.access_id}' and prev_tab = '{ctx.access_ach_tab}'
        and status = 'DONE' and reason = '{self.text_en}' and user_name = 'parking_gate_push_turing'
        order by id desc
        """
        res = ctx.poi_db.fetchone(qry)
        if len(res) == 0:
            return False

        # 虽然修复过了，但若后面作业了多次，还是反馈有问题（比如说缺路，修复过后，还是多次反馈缺路，那么就认为有问题）
        qry = f"""
        select count(*) from {ctx.repair_tab}
        where prev_id = '{access.access_id}' and prev_tab = '{ctx.access_ach_tab}'
            and status = 'DONE' and user_name = 'turing' and reason like '%{self.text_en}%'
            and id > {res[0]}
        """
        res = ctx.poi_db.fetchone(qry)
        if res[0] < 2:
            return True
        self.log.append(f"出入口修复次数过多：{res[0]}")
        # 认为修复的有问题
        return False

    def can_work(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        线上质检，出入口关联的LINK为步行路，需要下发人工核实
        线下质检，等修复好才能下发
        """
        if not access.checker_is_online():
            # 线下质检
            return self.had_repaired(ctx, access)
        # 线上的，可以直接下发作业的质检备注
        online_memo = [
            '步行路',
            '出口方向',
        ]
        for _memo in online_memo:
            if _memo in access.check_memo:
                return True
        return False


@dataclasses.dataclass
class GatePassAccessReason(AccessReason):
    """
    大门通行性
    """
    text_en = 'Gate_Pass'

    def work_directly(self) -> bool:
        return False

    def had_repaired(self, ctx: Context, access: ach.AccessAch) -> bool:
        # FIXME
        return False


@dataclasses.dataclass
class LinkBlockAccessReason(AccessReason):
    """
    link 阻断
    """
    text_en = 'Link_Block'

    def work_directly(self) -> bool:
        return False

    def had_repaired(self, ctx: Context, access: ach.AccessAch) -> bool:
        return False

    def can_work(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        线下质检的，不能下发作业
        线上质检的，可以下发作业
        """
        if access.checker_is_online():
            return True
        return False


@dataclasses.dataclass
class NodeBlockAccessReason(AccessReason):
    """
    node 阻断 (路障)
    """
    text_en = 'Node_block'

    def work_directly(self) -> bool:
        return False

    def had_repaired(self, ctx: Context, access: ach.AccessAch) -> bool:
        return False

    def can_work(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        线下质检的，不能下发作业
        线上质检的，可以下发作业
        """
        if access.checker_is_online():
            return True
        return False


@dataclasses.dataclass
class AreaDistanceAccessReason(AccessReason):
    """
    口与面距离太远
    """

    def work_directly(self) -> bool:
        return False

    def had_repaired(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        有 面修复过的记录就算
        """
        park = ach.get_parking_ach_by_bid(ctx, access.parent_bid)
        return area_had_repaired_two_times_no_check(ctx, park)

    def can_work(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        线上和线下已有
        """
        # if access.checker_is_online():
        #     # 方案待定，
        #     return False
        return self.had_repaired(ctx, access)


@dataclasses.dataclass
class LinkDistanceAccessReason(AccessReason):
    """
    口与link距离太远
    """

    def work_directly(self) -> bool:
        return False

    def had_repaired(self, ctx: Context, access: ach.AccessAch) -> bool:
        """
        有 面修复过的记录就算
        """
        park = ach.get_parking_ach_by_bid(ctx, access.parent_bid)
        return area_had_repaired_two_times_no_check(ctx, park)


def park_checked_memo_has_area_err(check_memo: str) -> bool:
    """
    停车场的质检信息中，是否有面异常错误
    """
    fails = [
        '存在压盖线上面',
        '存在压盖线下面',
        'face too short',
        # '压盖LD',
        '存在锐角',
    ]
    for err in fails:
        if err in check_memo:
            return True
    return False


def park_checked_memo_has_area_forbidden_err(check_memo: str) -> bool:
    """
    面中，存在，不允许的异常，返回 True
    """
    fails = [
        '存在压盖线上面',
        '存在压盖线下面',
        # 'face too short',  # 第一次不允许，但第二次核实后，会变成允许
        # '压盖LD',
        '存在锐角',
    ]
    for err in fails:
        if err in check_memo:
            return True
    return False


def _get_key2access_reason() -> Dict[str, AccessReason]:
    """
    获取关键词对应的原因
    """
    factor = [
        # 可直接下发的
        {
            'zh': 'node 或 link 失效',
            'en': 'Invalid',
            'reason': WorkDirectlyAccessReason,
        },
        {
            'zh': '关联的link上有出口node',
            'en': 'Related_Link_Has_Exit_Or_Emergency_Door',
            'reason': WorkDirectlyAccessReason,
        },

        # 重复
        {
            'zh': '出入口存在重复',
            'en': 'Repeated_Access',
            'reason': WorkDirectlyAccessReason,
        },
        {
            'zh': 'node 和 link 重复',
            'en': 'Repeated_Access',
            'reason': WorkDirectlyAccessReason,
        },
        {
            'zh': 'link和link在一条规划道路上，重复',
            'en': 'Repeated_Access',
            'reason': WorkDirectlyAccessReason,
        },

        # 大门通行性
        {
            'zh': '出口大门',
            'en': GatePassAccessReason.text_en,
            'reason': GatePassAccessReason,
        },

        # 阻断
        {
            'zh': '阻断link',
            'en': LinkBlockAccessReason.text_en,
            'reason': LinkBlockAccessReason,
        },
        {
            'zh': 'link上有阻断信息',
            'en': LinkBlockAccessReason.text_en,
            'reason': LinkBlockAccessReason,
        },

        # 路障
        {
            'zh': '路障',
            'en': NodeBlockAccessReason.text_en,
            'reason': NodeBlockAccessReason,
        },

        # 缺路
        {
            'zh': '缺路戳点',
            'en': LackRoadAccessReason.text_en,
            'reason': LackRoadAccessReason,
        },
        {
            'zh': '缺失link',
            'en': LackRoadAccessReason.text_en,
            'reason': LackRoadAccessReason,
        },
        {
            'zh': '出口link',
            'en': LackRoadAccessReason.text_en,
            'reason': LackRoadAccessReason,
        },
        {
            'zh': '出入口关联的LINK为出口方向',
            'en': LackRoadAccessReason.text_en,
            'reason': LackRoadAccessReason,
        },
        {
            'zh': '步行路',
            'en': LackRoadAccessReason.text_en,
            'reason': LackRoadAccessReason,
        },
        {
            'zh': '非机动车道',
            'en': LackRoadAccessReason.text_en,
            'reason': LackRoadAccessReason,
        },

        {
            'zh': '出入口与门前面的距离过远',
            'en': 'AreaDistanceAccess',
            'reason': AreaDistanceAccessReason,
        },
        {
            'zh': '出入口与对应LINK的距离过远',
            'en': 'LinkDistanceAccess',
            'reason': LinkDistanceAccessReason,
        },
    ]
    result = {}
    for item in factor:
        result[item['zh']] = item['reason'](item['zh'], item['en'])
    return result


def get_access_reasons(check_memo: str) -> List[AccessReason]:
    """
    获取出入口失败原因
    """
    result = []
    for keyword, reason in _get_key2access_reason().items():
        if keyword in check_memo:
            result.append(reason)
    return result


def get_access_reasons_en(check_memo: str) -> list:
    """
    获取出入口英文版的原因
    """
    result = []
    for reason in get_access_reasons(check_memo):
        result.append(reason.text_en)
    return result


def get_access_has_area_reasons_en() -> list:
    """
    获取出入口中有面的异常原因
    """
    return ['AreaDistanceAccess', 'LinkDistanceAccess']


def get_park_area_reasons_en_two_times_no_check() -> list:
    """
    面，二次作业免检
    """
    return ['Face_Too_Short']


def get_access_area_reasons_en(check_memo: str) -> list:
    """
    获取出入口中有面的异常原因
    """
    reasons = get_access_reasons_en(check_memo)
    return list(set(reasons).intersection(get_access_has_area_reasons_en()))


def get_area_reasons_en(check_memo: str) -> list:
    """
    获取面英文版的原因
    """
    key2reason = {
        '存在压盖线上面：': 'Cover_Online_Area',
        '存在压盖线下面：': 'Cover_Offline_Area',
        'face too short:': 'Face_Too_Short',
        # '压盖LD：': 'Cover_LD',
        '存在锐角：': 'ExistsSharpAngle',
    }
    reasons = []
    for err, txt in key2reason.items():
        if err in check_memo:
            reasons.append(txt)
    return list(set(reasons))


def extract_bids_from_area_check_memo(memo: str) -> list:
    """
    从面的质检信息中提取 bids
    """
    pattern = r'(?:存在压盖线下面|存在压盖线上面)：([\d,]+)'
    matches = re.findall(pattern, memo)
    bids = []
    for match in matches:
        bids.extend(match.split(','))
    return list(set([bid.strip() for bid in bids if bid.strip()]))


def area_had_repaired_two_times_no_check(ctx: Context, park: ach.ParkingAch) -> bool:
    """
    面已经修复过了，第二次免检；免检返回 True
    """
    reason_arr = get_access_has_area_reasons_en() + get_park_area_reasons_en_two_times_no_check()
    reason_str = '|'.join(reason_arr)
    qry = f"""
        select id from {ctx.repair_tab}
        where prev_id = '{park.park_id}' and prev_tab = '{ctx.park_ach_tab}'
        and user_name = '{ctx.verified_push_tab}'
        and reason SIMILAR TO '%({reason_str})%'
    --     and status != 'CANCEL'
        and status = 'DONE'
        """
    res = ctx.poi_db.fetchone(qry)
    return len(res) > 0


def get_manual_remark_link_ids(text: str) -> dict:
    """
    获取人工备注的 link_ids
    有路障，路障Linkid=abc12345,
    阻断Linkid=12345abc
    步行路Linkid=a123bdd
    """
    def handel_text(txt: str) -> str:
        """处理文本，转换成小写，然后再去掉所有空格"""
        return re.sub(r'\s+', '', txt.lower())

    # 正则表达式匹配三种类型的 Linkid
    pattern = r"(路障linkid|阻断linkid|步行路linkid)=([a-zA-Z0-9]+)"

    matches = re.findall(pattern, handel_text(text), flags=re.IGNORECASE)

    # 构建字典
    key2lids = {}
    for key, value in matches:
        key2lids.setdefault(key, []).append(value)
    return key2lids


def get_manual_link_block_short_link_ids(text: str) -> list:
    """
    获取人工备注的，阻断短 link
    """
    key2lids = get_manual_remark_link_ids(text)
    key = '阻断Linkid'
    return key2lids[key] if key in key2lids else []


def get_manual_node_block_short_link_ids(text: str) -> list:
    """
    获取人工备注的，路障短 link
    """
    key2lids = get_manual_remark_link_ids(text)
    key = '路障Linkid'
    return key2lids[key] if key in key2lids else []


def get_manual_road_err_short_link_ids(text: str) -> list:
    """
    获取道路异常的备注 link
    """
    key2lids = get_manual_remark_link_ids(text)
    key = '步行路Linkid'
    return key2lids[key] if key in key2lids else []


if __name__ == '__main__':
    # print(_get_key2access_reason())
    # memo = f"""
    # 有路障，路障Linkid=abc12345,
    # 阻断Linkid=12345abc
    # 步行路Linkid=a123bdd
    # """
    memo = f"""
        有路障，路障Linkid=abc12345, 路障Linkid=abc1234666,
        步行路Linkid=a123bdd，阻断Linkid=12345abc，
        """
    print(get_manual_remark_link_ids(memo))
