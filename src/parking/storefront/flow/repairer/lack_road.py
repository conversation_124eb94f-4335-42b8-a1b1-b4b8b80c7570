"""
缺路
"""
import argparse
import csv
import json
import logging
import multiprocessing
import time
import uuid
from collections import defaultdict
from typing import List, Dict

import dataclasses
import requests
import shapely.wkt
from shapely.geometry import Polygon, mapping
from tqdm import tqdm

from src.parking.storefront.flow.context import Context, gen_ctx, gen_ctx_manager
from src.parking.storefront.flow.db import array_chunk, join_int, handle_apostrophe, join_str
from src.parking.storefront.flow.model import cmd, ach
from src.parking.storefront.flow import config
from src.parking.storefront.flow.coord_helper import gcj2bd09, bd092gcj


REPAIRED_USERNAME = 'parking_gate_push_turing'


def relate_info_and_access(ctx: Context):
    """
    关联缺路情报和出入口
    历史原因，存在一部分情报和口没有关联
    """
    qry = f"""
    select park_manual_task_id from parking_gate_push_turing
    """
    res = ctx.bf_query.queryall(qry)
    for item in tqdm(res):
        # qry = f"""
        # select count(*) from
        # """
        park_manual_task_id = item[0]
        qry = f"""
        select source_id from park_manual_task where park_manual_task_id = {park_manual_task_id}
        """
        manual = ctx.bf_query.queryone(qry)
        if not manual or len(manual) == 0:
            print(f"{item} 不存在 park_manual_task")
            continue

        source_id = manual[0]
        qry = f"""
        select id from park_storefront_access where source_id = '{source_id}'
        """
        res = ctx.poi_db.fetchall(qry)
        if len(res) > 0:
            print(item, source_id)
            exit()
        print(item)


def relate_info_and_access_v2(ctx: Context):
    """关联情报和出入口信息"""
    qry = f"""
    select id, source_id, st_astext(geom) from park_storefront_access where conclusion = 'lack'
    """
    work_accesses = ctx.poi_db.fetchall(qry)

    not_matched = 0
    yes_matched = 0
    status_map = {}
    result_map = {}
    for work_access in tqdm(work_accesses):
        source_id = work_access[1]
        access_id = work_access[0]

        qry = f"""
        select id from {ctx.access_ach_tab} where access_id = {access_id}
        """
        prod_access = ctx.poi_db.fetchone(qry)
        if len(prod_access) == 0:
            continue

        access_wkt = work_access[2]
        access_geo = shapely.wkt.loads(gcj2bd09(access_wkt))

        qry = f"""
        select t.status, longitude, latitude, t.id, turing_result
        from parking_gate_push_turing t left join park_manual_task m on t.park_manual_task_id = m.park_manual_task_id 
        where m.source_id = '{source_id}'
        """
        push_turing_list = ctx.bf_query.queryall(qry)
        # print(push_turing_list)
        # exit()
        if len(push_turing_list) == 0:
            print(work_access)
            continue

        matched_push = None
        for push_turing in push_turing_list:
            bd09_wkt = f"POINT({push_turing[1]} {push_turing[2]})"
            bd09_geo = shapely.wkt.loads(bd09_wkt)

            distance = access_geo.distance(bd09_geo) / 1e-5
            # print(distance)
            status = push_turing[0]
            result = push_turing[4]
            if distance < 1:
                matched_push = push_turing
                status_map[status] = status_map.setdefault(status, 0) + 1
                result_map[result] = result_map.setdefault(result, 0) + 1
                break
        if not matched_push:
            not_matched += 1
            continue
            # print(push_turing_list)
            # print(work_access, access_geo)
            # exit()
        # else:
        yes_matched += 1

        matched_id = matched_push[3]
        qry = f"""
        insert into {ctx.repair_tab} (prev_id, prev_tab, user_name, user_id, reason, status) 
        values ('{prod_access[0]}', '{ctx.access_ach_tab}', 'parking_gate_push_turing', '{matched_id}', 'Lack_Road', 'INIT')
        """
        ctx.poi_db.execute(qry)
    print(not_matched, yes_matched, status_map, result_map)
    ctx.poi_db.commit_or_rollback()


def callback_turing_id(ctx: Context):
    """
    回传图灵情报 id
    """
    qry = f"""
    select id, user_id, result 
    from {ctx.repair_tab} 
    where user_name = 'parking_gate_push_turing' and reason = 'Lack_Road' and status = 'INIT' 
    """
    repairs = ctx.poi_db.fetchall(qry)

    num = 0
    for repair in tqdm(repairs):
        user_id = repair[1]
        pk_id = repair[0]
        qry = f"""
        select turing_qid 
        from parking_gate_push_turing 
        where id = {user_id} and turing_qid != ''  
        """
        res = ctx.bf_query.queryone(qry)
        if not res:
            continue

        result = repair[2]
        result['turing_id'] = res[0]

        qry = f"""
        update {ctx.repair_tab} 
        set status = 'ING', result = '{handle_apostrophe(json.dumps(result, ensure_ascii=False))}' 
        where id = {pk_id} and status = 'INIT'
        """
        num += ctx.poi_db.execute(qry)
    ctx.poi_db.commit_or_rollback()
    print(f"回传了图灵 id:{num}")


def callback_turing_result(ctx: Context):
    """
    回传图灵结论
    ING  待发版
    DONE 已发版
    DONE 无效
    https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/3ZlP6R54FU/HwBTZES61tFmxm
    """
    EFFECTED = 1
    INVALID = 3

    qry = f"""
        select id, user_id, result 
        from {ctx.repair_tab} 
        where user_name = 'parking_gate_push_turing' and reason = 'Lack_Road' and status = 'ING' and remark = '' 
        """
    repairs = ctx.poi_db.fetchall(qry)

    num = 0
    for repair in tqdm(repairs):
        user_id = repair[1]
        pk_id = repair[0]
        result = repair[2]

        qry = f"""
            select turing_result, turing_qid
            from parking_gate_push_turing 
            where id = {user_id} and turing_result != 0  
            """
        res = ctx.bf_query.queryone(qry)
        if not res:
            continue

        turing_result = res[0]
        turing_qid = res[1]
        if turing_result not in [EFFECTED, INVALID]:
            status = 'ERR'
            remark = ''
        elif turing_result == EFFECTED:
            # 有效，还需要查发版状态
            status = 'ING'
            remark = '待发版'
        else:
            status = 'DONE'
            remark = '无效'

        result['turing_result'] = turing_result
        if 'turing_id' not in result:
            result['turing_id'] = turing_qid
        qry = f"""
        update {ctx.repair_tab} 
        set status = '{status}', remark = '{remark}', result = '{handle_apostrophe(json.dumps(result, ensure_ascii=False))}' 
        where id = {pk_id} and status = 'ING'
        """
        num += ctx.poi_db.execute(qry)
    ctx.poi_db.commit_or_rollback()
    print(f"回传了图灵 result:{num}")


@dataclasses.dataclass
class ReleasedInfo:
    """
    发版信息
    """
    turing_id: str
    navi_version: str
    navi_status: int  # 导航发版状态 =2, 已发版
    navi_utime: str  # 导航发版时间
    work_status: int  # 内业作业状态
    work_end_time: str  # 内业作业结束时间

    def had_released(self) -> bool:
        """
        已经发版了返回 True
        """
        return int(self.navi_status) == 2


def _query_released_infos(turing_ids: list) -> list:
    """
    查询发版信息
    curl -X POST http://m.map.baidu.com:8088/trunk/mica/api/ugcarchive
    -H "Content-Type: application/json"
    -d '{"iid_list": ["67592f54dda6cc14dc58b0a0"],"action":"getugcstatus4stargaze"}'
    """
    url = "http://m.map.baidu.com:8088/trunk/mica/api/ugcarchive"
    headers = {"Content-Type": "application/json"}
    data = {
        "iid_list": turing_ids,
        "action": "getugcstatus4stargaze"
    }
    print(url)
    print(data)

    # 接口 qps 限制 1
    time.sleep(1)

    try:
        response = requests.post(url, json=data, headers=headers)
        rjson = response.json()
        print(rjson['code'], rjson['msg'])
        return rjson['data']
    except Exception as e:
        logging.exception(e)
        return []


def get_turing_id_2_released_info(turing_ids: list) -> Dict[str, ReleasedInfo]:
    """
    查询图灵情报发版信息
    """
    resp = {}
    turing_id_chunks = array_chunk(turing_ids, 99)
    # turing_id_chunks = array_chunk(turing_ids, 2)
    for turing_id_chunk in turing_id_chunks:
        released_infos = _query_released_infos(turing_id_chunk)
        for info in released_infos:
            turing_id = info['info_id']
            released = ReleasedInfo(
                turing_id=turing_id,
                navi_utime=(info['navi_utime']),
                navi_version=(info['navi_version']),
                navi_status=(info['navi_status']),
                work_status=int(info['work_status']),
                work_end_time=str(info['work_end_time']),
            )
            resp[turing_id] = released
            print(f"turing_id: {turing_id}; released: {dataclasses.asdict(released)}")
        # break  # todo test
    return resp


def callback_release_info(ctx: Context):
    """
    回传发版信息
    """
    qry = f"""
            select id, user_id, result 
            from {ctx.repair_tab} 
            where user_name = 'parking_gate_push_turing' 
                and reason = 'Lack_Road' and status = 'ING' and remark = '待发版' 
--                 and reason = 'Lack_Road' and ((status = 'ING' and remark = '待发版') or (status = 'DONE' and remark = '已发版')) 
            """
    repairs = ctx.poi_db.fetchall(qry)

    turing_id2repairs = defaultdict(list)
    for repair in tqdm(repairs):
        result = repair[2]
        turing_id = result['turing_id']
        turing_id2repairs[turing_id].append(repair)

    turing_ids = list(turing_id2repairs.keys())
    print(f"turing_ids:{len(turing_ids)}")
    num = 0

    turing_id_2_released = get_turing_id_2_released_info(turing_ids)
    for turing_id, released in tqdm(turing_id_2_released.items()):
        released: ReleasedInfo
        if not released.had_released():
            status = 'ING'
            remark = '待发版'
        else:
            status = 'DONE'
            remark = '已发版'

        for repair in turing_id2repairs[turing_id]:
            pk_id = repair[0]
            result = {**repair[2], **dataclasses.asdict(released)}

            qry = f"""
            update {ctx.repair_tab}
            set status = '{status}', remark = '{remark}', result = '{handle_apostrophe(json.dumps(result, ensure_ascii=False))}'
            where id = {pk_id} and status = 'ING' and remark = '待发版'
            """
            num += ctx.poi_db.execute(qry)
    ctx.poi_db.commit_or_rollback()
    print(f"回传发版信息:{num}")


def export_invalid(ctx: Context):
    """
    导出无效的
    """
    qry = f"""
                select id, user_id, prev_id
                from {ctx.repair_tab} 
                where user_name = 'parking_gate_push_turing' 
                    and reason = 'Lack_Road' and status = 'DONE' and remark = '无效' 
            """
    repairs = ctx.poi_db.fetchall(qry)

    dst = './tmp/Lack_Road_invalid.csv'
    with open(dst, 'w') as hdw:
        writer = csv.writer(hdw)
        header = ['parent_bid', 'id', 'name', 'access_geom', 'scene', 'pt_id', 'turing_id', 'park_geom']
        writer.writerow(header)

        for repair in repairs:
            access_id = repair[2]
            user_id = repair[1]

            access = ach.get_access(ctx, access_id)
            park = ach.get_parking_ach_by_bid(ctx, access.parent_bid)

            qry = f"""
            select turing_qid, ptid 
            from parking_gate_push_turing t left join park_manual_task m 
                on t.park_manual_task_id = m.park_manual_task_id 
            where t.id = {user_id}
            """
            res = ctx.bf_query.queryone(qry)

            turing_id = res[0]
            ptid = res[1]

            writer.writerow([
                access.parent_bid, access_id, access.name, access.geom, access.scene,
                ptid, turing_id, park.geom
            ])
    print(dst, 'over')


def _get_lack_road_accesses(ctx: Context, where: str) -> List[ach.AccessAch]:
    qry = f"""
    select id from {ctx.access_ach_tab} a where status = 'CHECK_FAILED' 
    and check_memo SIMILAR to '%(出口link|出入口关联的LINK为出口方向|步行路|缺路戳点|缺失link|非机动车道)%' 
    and checker = 'offline'  
--     and pic_urls::text != '{{}}'  
    and exists (select 1 from {ctx.park_ach_tab} b 
        where b.bid = a.parent_bid and ({where}))
--         b.city in ('北京市'))
--     and id = 52094
    """
    ids = ctx.poi_db.get_values(qry)

    id_chunks = array_chunk(ids, 100)
    accesses = []
    for id_chunk in id_chunks:
        accesses += ach.get_accesses_by_ids(ctx, id_chunk)
    return accesses


def _is_repairing(ctx: Context, access: ach.AccessAch, reason: str) -> bool:
    """
    在修复中
    """
    statuses = ['ING', 'DONE']
    access_id = access.access_id
    return _exists_repaired_access_record(ctx, access_id, reason, statuses)


def _exists_repaired_access_record(ctx: Context, access_id, reason: str, statuses: list):
    """
    是否存在出入口修复记录
    """
    num = _get_repaired_access_record_times(ctx, access_id, reason, statuses)
    return num > 0


def _get_repaired_access_record_times(ctx: Context, access_id, reason: str, statuses: list):
    qry = f"""
        select count(*) from {ctx.repair_tab} 
        where reason like '%{reason}%' and status in ({join_str(statuses)})
        and prev_id = '{access_id}' and prev_tab = '{ctx.access_ach_tab}' 
        and user_name = '{REPAIRED_USERNAME}' 
        """
    res = ctx.poi_db.fetchone(qry)
    return res[0]


def _access_can_push(ctx: Context, access: ach.AccessAch, reason: str) -> bool:
    """
    出入口能否推送补路情报
    """
    access_id = access.access_id
    if not _exists_repaired_access_record(ctx, access.access_id, reason, ['INIT', 'ING', 'DONE']):
        print(f"{access_id} 没有补路记录")
        return True
    if _exists_repaired_access_record(ctx, access_id, reason, ['INIT', 'ING']):
        print(f"{access_id} 出入口在补路中")
        return False
    """
    存在补路记录，需要判断是否有第再次投放补路的机会
    如果已经没有机会，那么就不能再投补路
    """
    return _has_again_chance(ctx, access, reason)


def _has_again_chance(ctx: Context, access: ach.AccessAch, reason: str) -> bool:
    """
    是否有第再次投放补路的机会
    如果已经投过补路情报多次，那么就没有机会了
    若只投了一次，且作业修复了多次，那么可以再投补路情报
    """
    max_info_num = 2  # 最多投补路情报的次数
    access_id = access.access_id
    times = _get_repaired_access_record_times(ctx, access_id, reason, ['DONE'])
    if times >= 2:
        print(f"{access_id} 已经投过补路情报：{times}; 达到了：{max_info_num}; 不能再投了")
        return False
    min_work_num = 2  # 最少作业次数

    # 若限制最多补路是两次，那么这么判断没有问题；因为先投的补路，再投我们作业

    qry = f"""
    select count(*) from {ctx.repair_tab} 
    where user_name = 'turing' and status = 'DONE'
        and prev_id = '{access_id}' and prev_tab = '{ctx.access_ach_tab}' 
        and reason like '%{reason}%' 
    """
    res = ctx.poi_db.fetchone(qry)
    if res[0] >= min_work_num:
        print(f"{access_id} 作业修复了多次: {res[0]}，可以再次投补路情报")
        return True
    return False


def _park_can_push(ctx: Context, park: ach.ParkingAch) -> bool:
    """
    从停车场的维度看，能否推送路障、阻断开通情报
    """
    if park.status not in ['CHECK_FAILED', 'REPAIRING']:
        return False
    if park.status == 'CHECK_FAILED':
        return True

    # 以下是修复中的逻辑
    qry = f"""
    select id from {ctx.repair_tab} 
    where prev_id = '{park.park_id}' and prev_tab = '{ctx.park_ach_tab}' 
    and status in ('INIT', 'ING') 
    and reason NOT SIMILAR TO '%(Node_Block|Link_Block|Lack_Road|Gate_Pass)%';
    """
    res = ctx.poi_db.fetchone(qry)
    if len(res) > 0:
        # 除了阻断、路障、缺路，大门通行性 外，还有其他项在修复中，那么面不能推送
        return False
    return True


def _save_access_repair(ctx: Context, access: ach.AccessAch, info_id: str, reason: str):
    """
    保存修复信息
    """
    args = {
        'check_memo': access.check_memo,
        'status': access.status,
        'road_relation': access.road_relation,
    }
    qry = f"""
            insert into {ctx.repair_tab} (prev_id, prev_tab, user_name, user_id, reason, args, status) 
            values ('{access.access_id}', '{ctx.access_ach_tab}', 'parking_gate_push_turing', '{info_id}', '{reason}', 
            '{handle_apostrophe(json.dumps(args, ensure_ascii=False))}', 'ING')
    """
    ctx.poi_db.execute(qry)


def _save_park_repair(ctx: Context, park: ach.ParkingAch, info_id: str, reason: str):
    args = {
        'check_memo': park.check_memo,
        'status': park.status,
    }
    qry = f"""
                insert into {ctx.repair_tab} (prev_id, prev_tab, user_name, user_id, reason, args, status) 
                values ('{park.park_id}', '{ctx.park_ach_tab}', 'parking_gate_push_turing', '{info_id}', '{reason}', 
                '{handle_apostrophe(json.dumps(args, ensure_ascii=False))}', 'ING')
        """
    ctx.poi_db.execute(qry)


def _main_park_status(ctx: Context, park: ach.ParkingAch):
    """
    维护停车场状态
    """
    qry = f"""
    update {ctx.park_ach_tab} set status = 'REPAIRING' 
    where id = {park.park_id} and status = 'CHECK_FAILED'
    """
    ctx.poi_db.execute(qry)


def _get_info_node(check_memo: str) -> str:
    """
    获取情报备注
    1、道路方向错误，也是按补路下发，需要备注【道路方向错误】下发
    2、道路种别错误，也是按补路下发，需要备注【道路种别错误】下发
    加个门前停车场
    """
    key2note = {
        '缺路戳点': '门前停车场缺路需补路',
        '缺失link': '门前停车场缺路需补路',
        '出口link': '门前停车场道路方向错误',
        '出口方向': '门前停车场道路方向错误',
        '步行路': '门前停车场道路种别错误',
    }
    for key, note in key2note.items():
        if key in check_memo:
            return note
    return '门前停车场缺路需补路'


def _push_info(ctx: Context, access: ach.AccessAch, park: ach.ParkingAch, batch: str) -> str:
    """
    推送一条缺路情报
    """
    bd09_wkt = gcj2bd09(access.geom)
    bd09_geo = shapely.wkt.loads(bd09_wkt)

    ext = {
        'note': _get_info_node(access.check_memo),
        'pic': ','.join(access.pic_urls),
    }

    qry = f"""
    insert into parking_gate_push_turing (cityname, longitude, latitude, ext, batch_id) 
    values ('{park.city}', {bd09_geo.x}, {bd09_geo.y}, '{json.dumps(ext, ensure_ascii=False)}', '{batch}')
    """
    return ctx.bf_query.execute_return_id(qry)


def push_info(ctx: Context, batch: str, limit: int, where: str):
    """
    推送缺路情报
    """
    batch = f"park_storefront_{batch}"
    accesses = _get_lack_road_accesses(ctx, where)
    print(f"缺路的出入口：{len(accesses)}")
    reason = 'Lack_Road'

    pushed_num = 0
    for access in tqdm(accesses):
        if not _access_can_push(ctx, access, reason):
            print(f"{access.access_id} 不能推送缺路情报")
            continue

        park = ach.get_parking_ach_by_bid(ctx, access.parent_bid)
        if not _park_can_push(ctx, park):
            print(f"{access.access_id} 所在的停车场：{park.bid} 不能推送缺路情报")
            continue

        info_id = _push_info(ctx, access, park, batch)
        if info_id == '':
            raise Exception(f"{access.access_id} 缺路推送失败")

        # 需要保证事务
        with gen_ctx_manager(is_committed=True, debug=True) as ctx:
            _save_access_repair(ctx, access, info_id, reason)
            _save_park_repair(ctx, park, info_id, reason)
            _main_park_status(ctx, park)

            ctx.poi_db.commit_or_rollback()

        pushed_num += 1
        if pushed_num >= limit:
            break
    print(f"缺路推送了：{pushed_num}")
    return pushed_num


def _maintain_access_pic_urls_part(turing_ids: list):
    ctx = gen_ctx(is_committed=True, debug=True)

    yes = 0
    for tid in tqdm(turing_ids):
        qry = f"""
        select bp_message from parking_turing_result where turing_id = '{tid}'
        """
        turing = ctx.poi_db.fetchone(qry)
        bp_message = json.loads(turing[0])
        edit_commit = json.loads(bp_message['data'])['edit_commit']

        for work in edit_commit['sub_info']:
            pic_urls = []
            if 'sub_pic' in work:
                for sub_pic in work['sub_pic']:
                    if 'picurl' in sub_pic:
                        pic_urls.append(sub_pic['picurl'])
            if len(pic_urls) == 0:
                continue

            unique_id = ''
            if 'unique_id' in work:
                unique_id = work['unique_id']
            qry = f"""
            select id, st_astext(geom) from {ctx.access_ach_tab} 
            where source_id = '{unique_id}' and face_id = '{tid}' 
            """
            accesses = ctx.poi_db.fetchall(qry)

            if len(accesses) == 0:
                print(f"不符合预期：turing_id: {tid}, unique_id:{unique_id}: {len(accesses)}")
                continue

            access = None
            point_info = work['point_info']
            access_geo = shapely.wkt.loads(bd092gcj(f"point({point_info[0]} {point_info[1]})"))
            if len(accesses) > 1:
                print(f"不符合预期：turing_id: {tid}, unique_id:{unique_id}: {len(accesses)}")
                for _access in accesses:
                    _geo = shapely.wkt.loads(_access[1])
                    if _geo.distance(access_geo) < 1e-5:
                        access = _access
                        break
                if access is None:
                    continue
                print(f"通过坐标比对找到了")
            else:
                access = accesses[0]

            qry = f"""
            update {ctx.access_ach_tab} set pic_urls = '{json.dumps(pic_urls, ensure_ascii=False)}'
            where id = {access[0]}
            """
            yes += ctx.poi_db.execute(qry)
            # yes += 1
        ctx.poi_db.commit_or_rollback()
    print(f"符合条件的数量：{yes}")


def maintain_access_pic_urls(ctx: Context):
    """
    维护出入口的照片信息
    """
    qry = f"""
    select turing_id
    from {ctx.access_ach_tab} a left join parking_turing_result r on a.face_id = r.turing_id 
    where r.turing_id is not null and a.remark = 'turing' and a.pic_urls = '{{}}'::jsonb
    """
    tids = ctx.poi_db.get_values(qry)

    tid_chunks = array_chunk(tids, 3000)
    print(f"停车场数量：{len(tids)}")
    with multiprocessing.Pool(processes=10) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.map(_maintain_access_pic_urls_part, tid_chunks)
    return results


@dataclasses.dataclass
class CityStatRep:
    """
    城市统计结论
    """
    city: str
    info_num: int = 0  # 情报量
    ing_num: int = 0  # 作业中
    invalid_num: int = 0  # 无效
    effected_num: int = 0  # 无效
    done_num: int = 0
    release_num: int = 0
    inner_work_num: int = 0  # 内业完成量

    def stat(self, repair: dict):
        """统计"""
        status = repair['status']
        remark = repair['remark']
        result = repair['result']

        self.info_num += 1
        if status == 'ING' and remark == '':
            self.ing_num += 1
        elif status == 'DONE' and remark == '无效':
            self.invalid_num += 1
        elif status == 'ING' and remark == '待发版':
            self.effected_num += 1
            if result.get('work_end_time', '') not in ['None', '']:
                self.inner_work_num += 1
        elif status == 'DONE' and remark == '已发版':
            self.release_num += 1
            self.effected_num += 1
            self.inner_work_num += 1

    def format(self) -> str:
        """格式化"""
        return '\t'.join([str(item) for item in [
            self.city, self.info_num, self.ing_num, self.invalid_num,
            f"{self.effected_num}({self.release_num})", self.inner_work_num
        ]])

    @staticmethod
    def format_header():
        """格式化 header"""
        # return '\t'.join(['城市', '情报量', '作业中量', '无效量', '有效量', '内业作业完成量', '发布量'])
        return '\t'.join(['城市', '情报量', '作业中量', '无效量', '有效量(发版量)', '内业作业完成量'])


def stat(ctx: Context):
    """
    统计
    """
    qry = f"""
    select prev_id, status, remark, result from {ctx.repair_tab} 
    where prev_tab = 'park_storefront_prod_access' and user_name = 'parking_gate_push_turing' 
    and reason = 'Lack_Road' 
    """
    city2Resp = {}

    repairs = [{
        'prev_id': item[0],
        'status': item[1],
        'remark': item[2],
        'result': item[3],
    } for item in ctx.poi_db.fetchall(qry)]
    data = []
    for repair in repairs:
        qry = f"""
        select city from {ctx.access_ach_tab} a left join {ctx.park_ach_tab} b on a.parent_bid = b.bid 
        where a.id = {repair['prev_id']}
        """
        res = ctx.poi_db.fetchone(qry)
        city = res[0]

        if city not in city2Resp:
            resp = CityStatRep(city=city)
        else:
            resp = city2Resp[city]
        resp.stat(repair)
        city2Resp[city] = resp

    header_str = f"总量\t未投放量\t城市{CityStatRep.format_header()}\t面已上线的量(口)"
    data.append(header_str)
    print(header_str)

    response = []
    city_sort = config.get_cities_sort()
    for city in city_sort:
        qry = f"""
        select count(*) from {ctx.access_ach_tab} a where status = 'CHECK_FAILED' and 
        check_memo SIMILAR to '%(出口link|出入口关联的LINK为出口方向|出入口关联的LINK为步行路|缺路戳点|缺失link)%' 
        and exists (select 1 from {ctx.park_ach_tab} b where a.parent_bid = b.bid and b.city = '{city}') 
        and not exists (select 1 from {ctx.repair_tab} c where a.id::text = c.prev_id and prev_tab = '{ctx.access_ach_tab}' and reason = 'Lack_Road')
        """
        res = ctx.poi_db.fetchone(qry)
        # print(ctx.poi_db.get_last_qry())

        qry = f"""
        select count(*) from {ctx.park_ach_tab} a where city = '{city}' and bid_status = 'effected' 
        and status in ('ONLINE', 'COMING_ONLINE', 'CHECK_SUCCEED', 'READY_OFFLINE', 'OFFLINE') 
        and exists (
            select 1 from {ctx.repair_tab} b 
            where a.id::text = b.prev_id and b.prev_tab = '{ctx.park_ach_tab}' and reason = 'Lack_Road' 
        )
        """
        num = ctx.poi_db.fetchone(qry)[0]

        qry = f"""
        select count(*) from {ctx.access_ach_tab} a
        where exists(
            select 1 from {ctx.repair_tab} b 
            where a.id::text = b.prev_id and b.prev_tab = '{ctx.access_ach_tab}' and reason = 'Lack_Road'
        ) and exists(
            select 1 from {ctx.park_ach_tab} c 
            where a.parent_bid = c.bid and city = '{city}' and bid_status = 'effected' 
            and (
                 status in ('ONLINE', 'COMING_ONLINE', 'CHECK_SUCCEED', 'READY_OFFLINE', 'OFFLINE') 
                 or (online_num > 0  and status = 'PARK_ACCESS_INTELLIGENCE_PUSHED')
            ) 
        )
        """
        access_num = ctx.poi_db.fetchone(qry)[0]
        if city in city2Resp:
            _resp = city2Resp[city]
            _text = f"{res[0] + _resp.info_num}\t{res[0]}\t{_resp.format()}\t{num}({access_num})"
        else:
            _text = f"{res[0]}\t{res[0]}\t{city}\t{num}({access_num})"
        print(_text)
        data.append(_text)

        resp = city2Resp.get(city)
        response.append({
            '城市': city,
            '缺路总量': res[0] if not resp else res[0] + resp.info_num,
            '未投放量': res[0],
            '投放情报量': 0 if not resp else resp.info_num,
            '作业中量': 0 if not resp else resp.ing_num,
            '无效量': 0 if not resp else resp.invalid_num,
            '有效量': 0 if not resp else resp.effected_num,
            '内业作业完成量': 0 if not resp else resp.inner_work_num,
            '发版量': 0 if not resp else resp.release_num,
            '面已上线量': num,
            '口已上线量': access_num,
        })
        # break  # todo test

    file = './tmp/lack_road.tsv'
    with open(file, 'w') as hdw:
        writer = csv.writer(hdw, delimiter='\t')
        for item in data:
            writer.writerow(item.split('\t'))
    print(file)
    return response


def callback(ctx: Context):
    """
    回传
    """
    callback_turing_id(ctx)
    callback_turing_result(ctx)
    callback_release_info(ctx)
    stat(gen_ctx(is_committed=False, debug=False))


def push(ctx: Context, batch: str, limit: int, where: str):
    """
    推送
    """
    if batch == '':
        raise Exception(f"批次号不能为空：{batch}")
    push_info(ctx, batch, limit, where)


def push_by_city_sort(ctx: Context, limit: int):
    """
    根据城市顺序推送
    """
    ctx = gen_ctx(autocommit=True, debug=True)
    cities = config.get_cities_sort()

    batch = f"park_storefront_{time.strftime('%Y%m%d%H', time.localtime(time.time()))}"
    print(f"批次号：{batch}, 将推送：{limit}")
    for city in cities:
        where = f"city = '{city}'"
        pushed_num = push_info(ctx, batch, limit, where)
        print(f"{city} 推送了：{pushed_num}")
        limit -= pushed_num
        if limit <= 0:
            break
    print(f"已推送完毕; 还剩余额度：{limit}")


def main():
    """
    主函数
    """
    ctx = gen_ctx(is_committed=True, debug=True)
    fns = str(ARGS.fns)
    batch = str(ARGS.batch)
    where = str(ARGS.where)
    limit = int(ARGS.limit)

    if fns == 'callback':
        callback(ctx)
    elif fns == 'push':
        push(ctx, batch, limit, where)
    elif fns == 'stat':
        stat(gen_ctx(is_committed=False, debug=False))
    elif fns == 'push_auto':
        push_by_city_sort(ctx, limit)


if __name__ == '__main__':
    # relate_info_and_access(gen_ctx(is_committed=False, debug=True))
    # relate_info_and_access_v2(gen_ctx(is_committed=True, debug=True))

    # stat(gen_ctx(is_committed=False, debug=False))
    # export_invalid(gen_ctx(is_committed=False, debug=True))
    # maintain_access_pic_urls(gen_ctx(is_committed=False, debug=True))
    # push_info(gen_ctx(is_committed=True, debug=True), batch='lack_road_0306_01')
    parser = argparse.ArgumentParser(description='停车场干预')
    parser.add_argument(
        '--fns',
        type=str,
        required=True,
        help="执行的函数；init_park_offline, 初始化停车车下线干预信息"
    )
    parser.add_argument(
        '--limit',
        type=int,
        default=1,
    )
    parser.add_argument(
        '--batch',
        type=str,
        default='',
    )
    parser.add_argument(
        '--where',
        type=str,
        default='',
    )
    parser.add_argument(
        '--cities',
        type=str,
        default='',
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()
    pass
