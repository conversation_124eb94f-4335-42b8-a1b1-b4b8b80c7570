"""
主机服务
"""
import subprocess


def get_host_by_bns(host_bns: str):
    """
    获取 bns ip
    """
    data = "", 0
    ret = subprocess.run(
        "get_instance_by_service -ips {}".format(host_bns) + " | awk '{print $2,$3,$4}'",
        shell=True,
        stdout=subprocess.PIPE
    )
    code = ret.returncode
    if code == 0:
        stdout = ret.stdout
        host_result = stdout.decode('UTF-8').split("\n")
        if len(host_result) < 1:
            yield data
        for i in host_result:
            item_list = i.split(" ")
            if len(item_list) < 3:
                continue
            if item_list[2] != '0':
                # 状态不是0，实例有问题
                continue
            yield item_list[0], int(item_list[1])
    else:
        print("获取bns失败{}, 状态码{}, 返回{}".format(host_bns, code, ret.stdout))
        yield data

