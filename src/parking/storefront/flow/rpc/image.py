"""
图片服务
"""

# !/usr/bin/env python
# coding=utf-8
import os
import uuid

# 从Python SDK导入BOS配置管理模块以及安全认证模块
from baidubce.bce_client_configuration import BceClientConfiguration
from baidubce.auth.bce_credentials import BceCredentials
from baidubce.services.bos import bos_client

# 设置BosClient的Host，Access Key ID和Secret Access Key
bos_host = "su.bcebos.com"
access_key_id = "11b2eec931ed4613981861844b252dad"
secret_access_key = "bf41defd09cf4b2590d4a876676ef598"

# 创建BceClientConfiguration
config = BceClientConfiguration(credentials=BceCredentials(access_key_id, secret_access_key), endpoint=bos_host)
client = bos_client.BosClient(config)


def upload_pic(filename: str, new_file_name: str):
    """
    上传图片
    """
    client.put_object_from_file('lutao-pic', new_file_name, filename)
    return "https://lutao-pic.su.bcebos.com/{}".format(new_file_name)


if __name__ == "__main__":
    file_name = 'test.jpg'  # 文件路径
    pic_url = upload_pic(file_name, f"park_storefront/{uuid.uuid4().hex}.jpg")
    print(pic_url)  # 文件连接
    pass
