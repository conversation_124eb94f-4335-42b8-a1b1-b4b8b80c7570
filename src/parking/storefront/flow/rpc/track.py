"""
轨迹服务
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/zDOzDolH4A/Azp8-O00gdgspz
"""
import json
import logging
import random

import shapely.wkt
from bson import ObjectId
import requests

from src.parking.storefront.flow.rpc.host import get_host_by_bns


class Context:
    """
    上下文
    """
    appkey: str
    hosts: list

    def get_host(self) -> str:
        """
        获取 url
        """
        return random.choice(self.hosts)


def gen_test_ctx() -> Context:
    """
    生成测试上下文
    """
    ctx = Context()

    ctx.hosts = ['http://*************:8276']
    ctx.appkey = 'test'

    return ctx


def gen_pre_ctx() -> Context:
    """
    生成试生产上下文
    """
    ctx = Context()
    bns = 'group.opera-mirror-misApi-000-gz.map-de.all'

    hosts = []
    for host in get_host_by_bns(bns):
        ip, port = host
        hosts.append(f"http://{ip}:{port}")

    ctx.hosts = hosts
    ctx.appkey = 'test'

    return ctx


def gen_online_ctx() -> Context:
    """
    生成正式环境上下文
    """
    ctx = Context()
    bns = 'group.opera-online-misApi-000-gz.map-de.all'

    hosts = []
    for host in get_host_by_bns(bns):
        ip, port = host
        hosts.append(f"http://{ip}:{port}")

    ctx.hosts = hosts
    ctx.appkey = 'platform'

    return ctx


class Client:
    """
    服务客户端
    """

    def __init__(self, ctx: Context):
        self.ctx = ctx

    def _add_track(self, payload: dict) -> tuple:
        """
        新增轨迹
        """
        url = f"{self.ctx.get_host()}/mis-api/track/add"
        print(f"request: {url}")
        print(json.dumps(payload))
        headers = {
            "App-Key": self.ctx.appkey,
            "Content-Type": "application/json"
        }
        try:
            response = requests.post(url, json=payload, headers=headers)
            resp_jsn = response.json()
            print(json.dumps(resp_jsn, ensure_ascii=False))
            if resp_jsn.get('errno') == 0:
                return True, ""
            return False, resp_jsn.get('errmsg', '请求失败')
        except Exception as e:
            logging.exception(e)
            return False, "请求失败"

    def add_tracks(self, track_points: list, retry_time: int = 3) -> tuple:
        """
        添加轨迹，
        添加成功返回 True, uuid
        添加失败返回 False, 错误信息
        """
        payload = {
            'uid': str(ObjectId()),
            'track': track_points,
        }
        while True:
            ok, msg = self._add_track(payload)
            if ok:
                return ok, payload['uid']
            print(f"请求失败：{msg}")

            retry_time -= 1
            if retry_time <= 0:
                return False, msg

    def add_tracks_group(
            self, track_geoms: list, retry_time: int = 3, points_limit: int = 100, max_num: int = 50) -> list:
        """
        添加一组轨迹
        """
        groups = []
        points = []
        number = 0
        for _track in track_geoms:
            geo = shapely.wkt.loads(_track)
            tmp = [list(_coord) for _coord in list(geo.coords)]

            num = len(tmp)
            if num >= points_limit:
                # raise Exception(f"一组放不下一条轨迹；{_track}; 限制为：{points_limit}；实际是：{num}")
                print(f"一组放不下一条轨迹；{_track}; 限制为：{points_limit}；实际是：{num}")
                continue

            if number + num >= points_limit:
                # 超过了一组限制
                groups.append(points)
                points = []
                number = 0

            points.append(tmp)
            number += num
        if len(points) > 0:
            groups.append(points)

        if len(groups) > max_num:
            # log.warning 可选
            print(f"轨迹分组超过最大限制，截断为 {max_num} 组")
            groups = groups[:max_num]

        resp = []
        for _group in groups:
            ok, uuid = self.add_tracks(_group, retry_time)
            if not ok:
                raise Exception(f"{uuid}; 插入终点轨迹失败")
            resp.append(uuid)
        return resp


def new_test_client() -> Client:
    """
    测试客户端
    """
    ctx = gen_test_ctx()
    return Client(ctx=ctx)


def new_pre_client() -> Client:
    """
    试生产客户端
    """
    ctx = gen_pre_ctx()
    return Client(ctx=ctx)


def new_online_client() -> Client:
    """
    线上环境客户端
    """
    ctx = gen_online_ctx()
    return Client(ctx=ctx)


