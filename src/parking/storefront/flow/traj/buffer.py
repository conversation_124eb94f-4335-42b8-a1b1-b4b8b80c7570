"""
需要 buffer 一定的范围去获取轨迹
"""
from typing import List
from shapely.geometry import Polygon, MultiPolygon, LineString
import shapely.wkt
import shapely.ops

from src.parking.collect.import_parking import try_buffer
from src.parking.storefront.utils import geometric
from src.tools import utils


def generate_track_buffer(area_polygon, baseline, inward_offset, side_extension, street_bounds, need_filter_polygon):
    """
    根据基准线和缓冲策略，生成轨迹缓冲区。
    参数：
        area_polygon: Polygon，整体范围
        baseline: LineString，基准线
        inward_offset: float，向内收缩的距离（buffer）
        side_extension: float，左右延伸的长度
        street_bounds: Polygon，所在街区的轮廓
    返回：
        Polygon 或 MultiPolygon：缓冲后的区域
    """
    buffer_geom = _generate_buffer_primary(area_polygon, baseline, inward_offset, side_extension)
    if _buffer_primary_succeed(buffer_geom, area_polygon):
        print(f"两边推")
        return buffer_geom
    #     return _extract_polygon(buffer_geom, need_filter_polygon, area_polygon)
    print(f"buffer 裁切")
    # distance = need_filter_polygon.distance(area_polygon)
    # if distance > 0:
    #     need_filter_polygon = need_filter_polygon.buffer(distance)
    return _generate_buffer_fallback(area_polygon, inward_offset, street_bounds, need_filter_polygon)


def _buffer_primary_succeed(buffer_geo, area_polygon) -> bool:
    """
    buffer 成功了
    """
    if buffer_geo is None:
        return False
    if utils.calc_ioa(buffer_geo, area_polygon) > 0.9:
        return True
    return False


def generate_area_track_buffer(area_polygon, inward_offset, street_bounds, need_filter_polygon):
    """
    生成面轨迹 buffer
    """
    return _generate_buffer_fallback_v2(area_polygon, inward_offset, street_bounds, need_filter_polygon)


def generate_access_track_buffer(
        area_polygon, baseline, inward_offset, side_extension, street_bounds, need_filter_polygon):
    """
    生成口作业，面轨迹 buffer
    """
    buffer_geom = _generate_buffer_primary(area_polygon, baseline, inward_offset, side_extension)
    if buffer_geom is not None:
        return _extract_polygon(buffer_geom, need_filter_polygon, area_polygon)
    return _generate_buffer_fallback(area_polygon, inward_offset, street_bounds, need_filter_polygon)


def _generate_buffer_primary(polygon, baseline, buffer_size, extend_length):
    polygons = geometric.flat_polygon(polygon)
    items = []
    for _polygon in polygons:
        item = try_buffer(_polygon, baseline, buffer_size, extend_length)
        if item is None:
            return None
        items.append(item)
    return shapely.ops.unary_union(items)


def _generate_buffer_fallback(polygon, buffer_size, street, need_filter_polygon):
    polygon_buffer = polygon.buffer(buffer_size)
    intersected = street.intersection(polygon_buffer)
    resp = extract_largest_overlap_polygon(intersected, polygon)

    return _extract_polygon(resp, need_filter_polygon, polygon)


def _generate_buffer_fallback_v2(polygon, buffer_size, street, need_filter_polygon):
    polygon_buffer = polygon.buffer(buffer_size)
    intersected = street.intersection(polygon_buffer)
    resp = extract_largest_overlap_polygon(intersected, polygon)
    return _extract_polygon_v2(resp, need_filter_polygon, polygon)


def _extract_polygon(resp, need_filter, polygon):
    """
    过滤掉 need_filter，但 need_filter 和 polygon 的交集会被保留
    """
    geos = geometric.flat_polygon(resp.difference(need_filter))
    resp = extract_largest_overlap_polygon(shapely.ops.unary_union(geos), polygon)
    return resp.union(polygon)


def _extract_polygon_v2(resp, need_filter, polygon):
    """
    严格过滤掉 need_filter
    """
    geos = geometric.flat_polygon(resp) + geometric.flat_polygon(polygon)
    resp = shapely.ops.unary_union(geos).difference(need_filter)
    return extract_largest_overlap_polygon(resp, polygon)
    # return resp.union(polygon)


def extract_largest_overlap_polygon(geometry, target) -> Polygon:
    """
    从输入 geometry 中提取与 target 区域交集面积最大的 Polygon。
    参数：
        geometry: 任意几何类型（Polygon 或 MultiPolygon）
        target: 目标区域，用于计算交集面积
    返回：
        与 target 区域交集面积最大的 Polygon，如果无有效交集则返回空 Polygon。
    """
    polygons = geometric.flat_polygon(geometry)
    best_polygon = Polygon()
    max_overlap_area = 0

    for poly in polygons:
        intersection_area = poly.intersection(target).area
        if intersection_area > max_overlap_area:
            max_overlap_area = intersection_area
            best_polygon = poly

    return best_polygon
