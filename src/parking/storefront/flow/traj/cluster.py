"""
轨迹过多，需要聚合
"""
from typing import List
import numpy
import dataclasses
import shapely.wkt
import shapely.geometry
from shapely.strtree import STRtree

from src.parking.storefront.traj_clean.dest_traj_clean import filter_drift_traj, cluster_trajectories
from tqdm import tqdm


@dataclasses.dataclass
class ClusterItem:
    """聚类要素"""
    idx: int
    wkt: str
    geo: None


def _new_cluster_items(wkts: list) -> List[ClusterItem]:
    items = []
    for _idx, _wkt in enumerate(wkts):
        items.append(ClusterItem(
            idx=_idx,
            wkt=_wkt,
            geo=shapely.wkt.loads(_wkt),
        ))
    return items


class ClusterHelper:
    """
    聚合 helper
    """

    def __init__(self, items: List[ClusterItem]):
        self.items = items
        self.id2item = {}
        self.rtree = self._build_rtree()

    def _build_rtree(self):
        """
        初始化 rtree
        """
        geos = []
        for _item in self.items:
            geos.append(_item.geo)
            self.id2item[id(_item.geo)] = _item
        return STRtree(geos)

    def get_items(self, geo) -> List[ClusterItem]:
        """获取聚类的要素"""
        items = []
        for _item in self.rtree.query(geo):
            if isinstance(_item, numpy.int64):  # shapley 版本不一样，返回的结果类型不一样
                _item = self.rtree.geometries.take(_item)
            if geo.intersects(_item):
                items.append(self.id2item[id(_item)])
        return items


def cluster_tracks(polygon: shapely.geometry, wkt_list: List[str], eps=0.00009090909091, buffer_size=1e-5 * 2):
    """
    轨迹聚合
    """
    items = _new_cluster_items(wkt_list)
    helper = ClusterHelper(items=items)

    result = []
    used_idx = set()
    for _item in tqdm(items):
        if _item.idx in used_idx:
            continue

        waits = []
        buffer_geo = _item.geo.interpolate(0.5, normalized=True).buffer(buffer_size)
        temps = helper.get_items(buffer_geo)
        for _temp in temps:
            if _temp.idx in used_idx:
                continue
            used_idx.add(_temp.idx)
            waits.append(_temp)

        if len(waits) == 0:
            continue

        geos = [_w.geo for _w in waits]
        for item in cluster_trajectories(geos, eps=eps).values():
            max_len = 0
            max_geo = None
            for _geo in item:
                _int = _geo.intersection(polygon)
                if _int.length > max_len:
                    max_len = _int.length
                    max_geo = _geo
            if max_geo is None:
                continue
            result.append(max_geo.wkt)
    return result
