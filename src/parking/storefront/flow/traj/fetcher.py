"""
获取轨迹
"""
import hashlib
import json
from typing import List

import requests
import shapely.wkt
from shapely.geometry import LineString
from shapely.ops import substring
from coord_convert.transform import wgs2gcj, gcj2wgs
import numpy as np

from src.parking.recognition import dbutils
from src.tools import pgsql
from src.trajectory.utils import trajutils
from src.parking.storefront.post_process import autocomplete as auto_cpt

METER = 1e-5


def fetch_tracks(wkt: str, used_storefront_track=True) -> List[str]:
    """
    获取轨迹
    """
    pois = auto_cpt.get_relation_store_pois(wkt, 50 * METER)
    if not pois:
        return []
    wkts = []
    for _poi in pois:
        if used_storefront_track:
            wkts += _get_poi_tracks_v2(_poi.bid)
        else:
            wkts += _get_poi_tracks_v1(_poi.bid)
    return wkts


def _get_poi_tracks_v1(bid: str) -> List[str]:
    """
    获取 poi 轨迹
    """
    storefront_tracks = fetch_storefront_tracks(bid)
    common_tracks = fetch_common_tracks(bid)
    if len(storefront_tracks) > 0 and len(storefront_tracks) >= len(common_tracks):
        print(f"{bid} 门前轨迹比常规多：{len(storefront_tracks)}; {len(common_tracks)}")
        return storefront_tracks
    o2d = get_dest_trajectory(bid)
    o2d = [item.wkt for item in o2d]
    print(f"""
    {bid}; 门前轨迹：{len(storefront_tracks)}; 常规轨迹：{len(common_tracks)}; o2d: {len(o2d)}; o2d 多：{len(o2d) > len(common_tracks)}
    """)
    return common_tracks + o2d


def _get_poi_tracks_v2(bid: str) -> List[str]:
    """
    获取 poi 轨迹
    """
    storefront_tracks = fetch_storefront_tracks(bid)
    if len(storefront_tracks) > 0:
        print(f"{bid} 存在门前轨迹：{len(storefront_tracks)}")
        return storefront_tracks

    common_tracks = fetch_common_tracks(bid)
    o2d = get_dest_trajectory(bid)
    o2d = [item.wkt for item in o2d]
    print(f"""
        {bid}; 常规轨迹：{len(common_tracks)}; o2d: {len(o2d)}; o2d 多：{len(o2d) > len(common_tracks)}
        """)
    return common_tracks + o2d


def fetch_common_tracks(bid: str) -> List[str]:
    """
    获取常规轨迹
    """
    return trajutils.get_dest_traj_by_bid(bid)


def fetch_storefront_tracks(bid: str) -> List[str]:
    """
    获取门前 poi 轨迹
    """
    sql = f"""
    select st_astext(geom) from park_storefront_dest_traj
    where bid = %s 
    """

    config = pgsql.TRAJ_DB2
    ret = dbutils.fetch_all(config, sql, [bid])
    if not ret:
        return []
    return [r[0] for r in ret]


def calcu_hash_index(bid):
    """
    使用 hashlib 计算 bid 的哈希值
    """
    hash_object = hashlib.md5(bid.encode())
    hash_value = int(hash_object.hexdigest(), 16)
    return hash_value % 10


def get_dest_trajectory(bid):
    """
    根据接口获取bid的终点轨迹信息
    """
    req_data = json.dumps(
        {
            "qt": "navi_dest_traj",
            "bid": bid,
            "yddak": "map-aoi@baidu.com_yddak_273db2a4d6b5a6b465abb7d0effcc7bd",
        }
    )
    headers = {"Content-Type": "application/json"}
    r = requests.post(
        "http://chenxi.vpn.guoke.baidu.com/gpoint_janus", headers=headers, data=req_data
    )

    res = r.json()
    if (
            res["content"] is None
            or "navi_dest_traj" not in res["content"]
            or res["content"]["navi_dest_traj"] is None
            or res["content"]["navi_dest_traj"][bid] is None
            or res["content"]["navi_dest_traj"][bid]["redis_result"] is None
    ):
        return []
    if res["content"]["navi_dest_traj"][bid]["redis_result"]["error"] != 0:
        return []
    if "trajs" not in res["content"]["navi_dest_traj"][bid]:
        return []

    traj_info_list = res["content"]["navi_dest_traj"][bid]["trajs"]
    trajectory_line_region_end_list = []
    for traj_info_tmp in traj_info_list:
        cuid_info, trajectory_info = traj_info_tmp.split("@", 1)
        cuid, nav_end_time, _ = cuid_info.split("_", 2)
        trajectory_info_list = trajectory_info.split(";")
        trajectory_line = "LINESTRING("
        for trajectory_info_tmp in trajectory_info_list:
            trajectory_info_tmp_data = trajectory_info_tmp.split(",", 2)
            trajectory_line = f"{trajectory_line}{trajectory_info_tmp_data[0]} {trajectory_info_tmp_data[1]},"
        trajectory_line = trajectory_line.strip(",") + ")"
        trajectory_line_region = shapely.wkt.loads(trajectory_line)
        trajectory_line_region_end = substring(
            trajectory_line_region, start_dist=-0.01, end_dist=np.inf
        )
        line_array = []
        for tmp in trajectory_line_region_end.coords[:]:
            gcj_lon, gcj_lat = wgs2gcj(tmp[0], tmp[1])
            # gcj_lon, gcj_lat = tmp[0], tmp[1]
            line_array.append((gcj_lon, gcj_lat))
        trajectory_line_region_end_list.append(LineString(line_array))
    return trajectory_line_region_end_list


