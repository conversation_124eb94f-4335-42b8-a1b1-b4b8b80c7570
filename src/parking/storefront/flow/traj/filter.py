"""
轨迹过多，或者质量不高等其他原因，需要过滤
"""
import hashlib
from typing import List

from shapely import make_valid
from shapely.ops import unary_union
import shapely.wkt
from shapely.geometry import LineString, Point, MultiPoint, Polygon, MultiLineString
from src.parking.storefront.utils import geometric
from src.parking.storefront.traj_clean.dest_traj_clean import filter_drift_traj

COORDINATE_FACTOR = 100000


def filter_self_intersecting_traj(traj_sp, limit: int = 5):
    """
    过滤自相交较多的轨迹
    """
    self_intersects_count = count_self_intersections_v2(traj_sp)
    # print(f"轨迹自相交次数:{self_intersects_count}")
    # if self_intersects_count > 0:
    #     print(f"轨迹自相交次数:{self_intersects_count}, traj_sp:{traj_sp.wkt}")
    if self_intersects_count >= limit:
        print(f"轨迹自相交次数:{self_intersects_count}, traj_sp:{traj_sp.wkt}")
        return False
    else:
        return True


def count_self_intersections_v2(line, precision: int = 7):
    """
    统计自相交的点数
    """
    num = 0
    for _line in geometric.flat_line(line):
        num += _count_self_intersections(_line, precision)
    return num


def _count_self_intersections(line, precision):
    # 拆分成 segments
    coords = list(line.coords)
    segments = [LineString([coords[i], coords[i + 1]]) for i in range(len(coords) - 1)]
    intersection_points = set()

    def round_point(pt):
        return tuple(round(c, precision) for c in pt)

    for i in range(len(segments)):
        seg1 = segments[i]
        for j in range(i + 2, len(segments)):
            if j == i + 1:
                continue  # 跳过相邻段
            seg2 = segments[j]
            inter = seg1.intersection(seg2)
            if inter.is_empty:
                continue
            if inter.geom_type == 'Point':
                intersection_points.add(round_point(inter.coords[0]))
            elif inter.geom_type == 'MultiPoint':
                intersection_points.update(round_point(pt.coords[0]) for pt in inter.geoms)
    return len(intersection_points)


def split_by_intersects_points(traj_sp, park_area_geom, park_area_buffer=None):
    """
    交割点裁切轨迹
    """
    traj_sp_list = [traj_sp]
    if traj_sp.geom_type == 'MultiLineString':
        traj_sp_list = traj_sp.geoms
    intersects_line_list = []
    for traj_sp in traj_sp_list:
        # 获取轨迹与面交割点
        park_area_sp = shapely.wkt.loads(park_area_geom)
        park_area_buffer_5m_sp = park_area_sp.buffer(5 / COORDINATE_FACTOR)
        if park_area_buffer is not None:
            park_area_buffer_5m_sp = park_area_buffer
        # park_area_buffer_5m_sp = park_area_sp.buffer(10 / COORDINATE_FACTOR)
        intersect_point_list = calcul_intersect_points_by_traj_and_area(park_area_sp.wkt, [traj_sp])
        if not intersect_point_list:
            intersect_point_list = calcul_intersect_points_by_traj_and_area(park_area_buffer_5m_sp.wkt,
                                                                            [traj_sp])
        if not intersect_point_list:
            return None
        # 获取交割点集合距离起点最近的点
        start_sp = Point(traj_sp.coords[0])
        nearest_intersect_point = min(intersect_point_list, key=lambda pt: shapely.wkt.loads(pt).distance(start_sp))
        intersect_point_sp = shapely.wkt.loads(nearest_intersect_point)
        # 交割点buffer10m获取相交line
        # intersect_point_buffer_10m_sp = intersect_point_sp.buffer(20 / COORDINATE_FACTOR)

        intersect_point_buffer_10m_sp = intersect_point_sp.buffer(50 / COORDINATE_FACTOR)
        intersect_point_buffer_10m_sp = unary_union([intersect_point_buffer_10m_sp, park_area_sp])

        intersects_line = intersect_point_buffer_10m_sp.intersection(traj_sp)
        intersects_line_list.append(intersects_line)
    return unary_union(intersects_line_list)


def split_by_intersects_points_v2(traj_sp, area_wkt: str, area_buffer_sp, buffer_size=50 / COORDINATE_FACTOR):
    """
    交割点裁切轨迹
    """
    park_area_sp = shapely.wkt.loads(area_wkt)
    traj_sp_list = geometric.flat_line(traj_sp)
    intersects_line_list = []
    for traj_sp in traj_sp_list:
        # 获取轨迹与面交割点
        intersect_point_list = calcul_intersect_points_by_traj_and_area(area_buffer_sp.wkt, [traj_sp], False)
        if not intersect_point_list:
            continue

        # 获取交割点集合距离起点最近的点
        start_sp = Point(traj_sp.coords[0])
        nearest_intersect_point = min(intersect_point_list, key=lambda pt: shapely.wkt.loads(pt).distance(start_sp))
        intersect_point_sp = shapely.wkt.loads(nearest_intersect_point)

        intersect_point_buffer_sp = intersect_point_sp.buffer(buffer_size)
        intersect_point_buffer_sp = unary_union([intersect_point_buffer_sp, park_area_sp])
        intersects_line = cut_line_by_polygon_from_point(traj_sp, intersect_point_sp, intersect_point_buffer_sp)
        for _line in geometric.flat_line(intersects_line):
            if _line.intersects(area_buffer_sp):
                intersects_line_list.append(_line)
    return shapely.ops.linemerge(intersects_line_list)


def cut_line_by_polygon_from_point(line: LineString, start_point: Point, polygon: Polygon) -> LineString:
    """
    切割线
    """
    coords = list(line.coords)
    coords, start_index = _insert_start_point(coords, start_point)

    selected_coords = [coords[start_index]]

    # 往前扩展
    selected_coords = _extend_until_outside(coords, start_index, polygon, False, selected_coords)
    # 往后扩展
    selected_coords = _extend_until_outside(coords, start_index, polygon, True, selected_coords)

    return LineString(selected_coords)


def _insert_start_point(coords, start_point):
    """将 start_point 插入到轨迹线上，返回新坐标列表和插入后的位置索引"""
    if (start_point.x, start_point.y) in coords:
        return coords, coords.index((start_point.x, start_point.y))

    min_dist = float('inf')
    insert_index = 0
    for i in range(len(coords) - 1):
        seg = LineString([coords[i], coords[i + 1]])
        dist = seg.distance(start_point)
        if dist < min_dist:
            min_dist = dist
            insert_index = i + 1
    coords.insert(insert_index, (start_point.x, start_point.y))
    return coords, insert_index


def _extend_until_outside(coords, start_index, polygon, forward=True, selected_coords=None):
    """从 start_index 开始往前或往后扩展，直到遇到越界点；必要时插入交点"""
    if selected_coords is None:
        selected_coords = []

    step = 1 if forward else -1
    i = start_index + step

    while 0 <= i < len(coords):
        pt = Point(coords[i])
        if polygon.contains(pt):
            if forward:
                selected_coords.append(coords[i])
            else:
                selected_coords.insert(0, coords[i])
            i += step
        else:
            # 插入交点并终止
            prev_i = i - step
            seg = LineString([coords[prev_i], coords[i]])
            inter_point = _get_intersection_with_polygon(seg, polygon, Point(coords[start_index]))
            if inter_point:
                inter_coords = (inter_point.x, inter_point.y)
                if forward:
                    selected_coords.append(inter_coords)
                else:
                    selected_coords.insert(0, inter_coords)
            break
    return selected_coords


def _get_intersection_with_polygon(seg, polygon, ref_point):
    """获取线段与 polygon 的交点（取离 ref_point 最近的点）"""
    inter = seg.intersection(polygon.boundary)
    if inter.is_empty:
        return None
    if inter.geom_type == "Point":
        return inter
    elif inter.geom_type.startswith("Multi"):
        return min(inter.geoms, key=lambda g: g.distance(ref_point))
    return None


def calcul_intersect_points_by_traj_and_area(area_wkt, traj_list, is_need_point_in_area=True):
    """
    计算轨迹和面的交点
    """
    aoi_sp = shapely.wkt.loads(area_wkt)
    aoi_buffer_sp = aoi_sp.buffer(10 / COORDINATE_FACTOR)
    ints_p_wkt_arr = []
    for traj_sp in traj_list:
        if is_need_point_in_area:
            # 必须起点或者终点在AOI范围内
            line_start_point = traj_sp.coords[0]
            line_end_point = traj_sp.coords[-1]
            # 起点与终点都不在AOI范围内则过滤
            if not aoi_buffer_sp.contains(Point(line_start_point)) and not aoi_buffer_sp.contains(
                    Point(line_end_point)):
                continue
        ints_p_wkt = cal_line_and_area_intersect_point(traj_sp.wkt, area_wkt)
        if not ints_p_wkt:
            continue
        # 兜底校验 如果交点距离aoi边界小于30米，则不作为交点
        if shapely.wkt.loads(ints_p_wkt).distance(aoi_sp.boundary) > 20 / COORDINATE_FACTOR:
            continue
        ints_p_wkt_arr.append(ints_p_wkt)
    return ints_p_wkt_arr


def cal_line_and_area_intersect_point(line_wkt, area_wkt):
    """
    计算点与面交割点, 每次范围最优的一个交割点
    """
    # 计算轨迹和AOI的交点
    line_sp = shapely.wkt.loads(line_wkt)
    aoi_sp = shapely.wkt.loads(area_wkt)
    track_line_start = Point(line_sp.coords[0])
    track_line_end = Point(line_sp.coords[-1])
    if aoi_sp.contains(track_line_start):
        track_line_end = Point(line_sp.coords[0])
    # intersection = line_sp.intersection(aoi_sp.buffer(-3 / COORDINATE_FACTOR))
    intersection = line_sp.intersection(aoi_sp)
    # print('intersection:', intersection)
    if not intersection:
        # print('not intersection')
        return
    intersect_point = None
    if intersection.geom_type == "Point":
        intersect_point = intersection.wkt
    elif intersection.geom_type == "LineString":
        start_p, end_p = Point(intersection.coords[0]), Point(intersection.coords[-1])
        if Point(start_p).wkt == track_line_end.wkt:
            intersect_point = end_p.wkt
        else:
            intersect_point = start_p.wkt
    elif intersection.geom_type == "MultiLineString":
        for linestring in intersection.geoms:
            start_p, end_p = Point(linestring.coords[0]), Point(linestring.coords[-1])
            for point in linestring.coords:
                if Point(point).wkt == track_line_end.wkt:
                    intersect_p = end_p if Point(point).wkt == start_p.wkt else start_p
                    if intersect_p.distance(aoi_sp.boundary) > 30 / COORDINATE_FACTOR:
                        continue
                    intersect_point = intersect_p.wkt
                    break
            if intersect_point:
                break
        if not intersect_point:
            # 不存在，取第一个点
            linestring = list(intersection.geoms)[0]
            intersect_point = Point(linestring.coords[0]).wkt
    else:
        print(f"轨迹与AOI相交未知的几何类型: {intersection.geom_type}")
    # print('intersect_point:', intersect_point)
    return intersect_point


def filter_by_intersect_points(wkt_list: list, area_wkt: str, buffer_wkt: str, save_size: float) -> List[str]:
    """
    根据交割点过滤
    save_size 保留的长度范围
    """
    result = []
    for wkt in wkt_list:
        geo = shapely.wkt.loads(wkt)
        buffer_geo = shapely.wkt.loads(buffer_wkt)
        res = split_by_intersects_points_v2(geo, area_wkt, buffer_geo, save_size)
        if res is None or res.is_empty:
            continue
        for line in geometric.flat_line(res):
            result.append(line.wkt)
    return result


def filter_dup(wkt_list: list) -> list:
    """
    过滤重复的
    """
    key2wkt = {}
    for _wkt in wkt_list:
        key2wkt[hashlib.md5(_wkt.encode('utf-8')).hexdigest()] = _wkt
    return list(key2wkt.values())


def filter_by_self_intersects(wkt_list: list, limit: int = 5) -> list:
    """
    过滤自相交的
    """
    resp = []
    for wkt in wkt_list:
        geo = shapely.wkt.loads(wkt)
        if filter_self_intersecting_traj(geo, limit):
            resp.append(wkt)
    return resp


def filter_by_min_length(wkt_list: list, min_length: float) -> list:
    """
    根据最小长度过滤
    min_length: 若限制 1m；那么传参数：1 * 1e*-5
    """
    resp = []
    for wkt in wkt_list:
        geo = shapely.wkt.loads(wkt)
        if geo.length > min_length:
            resp.append(wkt)
    return resp


def filter_by_drift(wkt_list: list) -> list:
    """
    过滤低质量轨迹
    """
    resp = []
    for wkt in wkt_list:
        geo = shapely.wkt.loads(wkt)
        geo = filter_drift_traj(geo)
        if geo is not None:
            resp.append(wkt)
    return resp


def filter_by_area(wkt_list: list, area_wkt: str) -> list:
    """
    根据指定的范围过滤
    """
    resp = []
    area_geo = shapely.wkt.loads(area_wkt)
    for wkt in wkt_list:
        geo = shapely.wkt.loads(wkt)
        if area_geo.intersects(geo):
            resp.append(wkt)
    return resp

