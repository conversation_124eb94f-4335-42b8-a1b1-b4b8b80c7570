"""
轨迹分组
"""
from typing import List, Dict, Tuple

import dataclasses
import shapely.wkt
import shapely.geometry

from src.parking.storefront.flow.traj.model import TrackOfArea


@dataclasses.dataclass
class GroupItem:
    """
    分组要素
    """
    track: TrackOfArea
    num: int
    weight: float


def _calc_weight(line, polygon, buffer_geo) -> float:
    """
    权重
    """
    ints1 = line.intersection(polygon)
    ints2 = line.intersection(buffer_geo)
    return 2 * ints1.length + ints2.length


def _new_items(tracks: List[TrackOfArea]) -> List[GroupItem]:
    items = []
    for track in tracks:
        items.append(GroupItem(
            track=track,
            num=len(track.track.geo.coords),
            weight=_calc_weight(track.track.geo, track.area, track.buffer_area),
        ))
    return items


def _group(items: List[GroupItem], max_group_num=50, max_group_total=99) -> Tuple[List[List[TrackOfArea]], int]:
    # 按 weight 降序排
    sorted_items = sorted(items, key=lambda x: (-x.weight, x.num))

    groups = []  # 每组是 {'items': [...], 'num_sum': ...}
    reduce_num = 0
    for item in sorted_items:
        if item.num > max_group_total:
            item.track = item.track.reconstruct_desc(max_group_total)
            item.num = max_group_total
            # 权重不变
            print(f"单条轨迹超过了数量，进行了裁切")

        placed = False
        for child in groups:
            if child['num_sum'] + item.num <= max_group_total:
                child['items'].append(item.track)
                child['num_sum'] += item.num
                placed = True
                break

        if not placed:
            if len(groups) >= max_group_num:
                reduce_num += item.num
                continue  # 已达最大组数，且无法放入
            groups.append({'items': [item.track], 'num_sum': item.num})

    # 提取纯 items 分组结果
    return [g['items'] for g in groups], reduce_num


def group(tracks: List[TrackOfArea], max_group_num=50, max_group_total=99) -> List[List[TrackOfArea]]:
    """
    轨迹分组
    """
    track_groups, _ = _group(_new_items(tracks), max_group_num, max_group_total)
    return track_groups


def group2(tracks: List[TrackOfArea], max_group_num=50, max_group_total=99) -> List[List[str]]:
    """
    轨迹分组
    """
    track_groups, _ = _group(_new_items(tracks), max_group_num, max_group_total)
    resp = []
    for _tracks in track_groups:
        resp.append([_track.track.wkt for _track in _tracks])
    return resp


def group3(tracks: List[TrackOfArea], max_group_num=50, max_group_total=99) -> Tuple[List[List[str]], int]:
    """
    轨迹分组
    """
    track_groups, num = _group(_new_items(tracks), max_group_num, max_group_total)
    resp = []
    for _tracks in track_groups:
        resp.append([_track.track.wkt for _track in _tracks])
    return resp, num


def get_group_reduce_num(tracks: List[TrackOfArea], max_group_num=50, max_group_total=99) -> int:
    """
    分组是否会减少轨迹，是返回 True
    """
    _, reduce = _group(_new_items(tracks), max_group_num, max_group_total)
    return reduce

