"""
数据模型定义
"""
import hashlib
from typing import List, Union

import dataclasses
import shapely.wkt
import shapely.geometry
from shapely.geometry import Point, LineString

from src.parking.storefront.utils import geometric
from src.parking.storefront.flow.traj.filter import cal_line_and_area_intersect_point


@dataclasses.dataclass
class Track:
    """
    轨迹
    """
    wkt: str

    def __post_init__(self):
        self.geo = shapely.wkt.loads(self.wkt)
        self.uid = hashlib.md5(self.wkt.encode('utf-8')).hexdigest()

    def reconstruct_desc(self, point_limit: int) -> 'Track':
        """
        重构，保留倒数多少点
        """
        line = self.geo
        coords = list(line.coords)
        last_coords = coords[-point_limit:] if len(coords) >= point_limit else coords
        return new_track(LineString(last_coords).wkt)[0]


@dataclasses.dataclass
class TrackOfArea:
    """
    某个面的轨迹
    """
    track: Track
    area: shapely.geometry
    buffer_area: shapely.geometry

    def __post_init__(self):
        self.area_intersection_point = cal_line_and_area_intersect_point(self.track.wkt, self.area.wkt)
        self.buffer_intersection_point = cal_line_and_area_intersect_point(self.track.wkt, self.buffer_area.wkt)

    def get_intersection_point(self) -> Union[str, None]:
        """
        获取交割点
        """
        if self.area_intersection_point is not None:
            return self.area_intersection_point
        return self.buffer_intersection_point

    def reconstruct_desc(self, point_limit: int) -> 'TrackOfArea':
        """
        重构，保留倒数多少点
        """
        return TrackOfArea(
            track=self.track.reconstruct_desc(point_limit),
            area=self.area,
            buffer_area=self.buffer_area,
        )


def new_track(wkt: str) -> List[Track]:
    """
    初始化轨迹
    """
    tracks = []
    for line in geometric.flat_line(wkt):
        tracks.append(Track(
            wkt=line.wkt,
        ))
    return tracks


def new_tracks(wkt_list: list) -> List[Track]:
    """
    初始化轨迹
    """
    tracks = []
    for wkt in wkt_list:
        tracks += new_track(wkt)
    return tracks


def new_tracks_of_area(wkt_list: list, area_wkt: str, buffer_wkt: str) -> List[TrackOfArea]:
    """
    初始化面轨迹
    """
    tracks = new_tracks(wkt_list)
    result = []
    for _track in tracks:
        result.append(TrackOfArea(
            track=_track,
            area=shapely.wkt.loads(area_wkt),
            buffer_area=shapely.wkt.loads(buffer_wkt),
        ))
    return result

