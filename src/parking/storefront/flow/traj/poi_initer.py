"""
门前 poi 的终点轨迹，需要单独处理
那么 poi 需要单独保存
保存原始面的临街 poi
"""
import argparse
import multiprocessing
from typing import List

from tqdm import tqdm

from src.parking.storefront.flow.db import array_chunk, join_str, handle_apostrophe
from src.parking.storefront.flow.context import Context, gen_ctx
from src.parking.storefront.flow.generator import DBUQGenerator, DBGenerator
from src.parking.storefront.post_process import autocomplete as auto_cpt
from src.parking.storefront.flow.monitor.util import send_hi

HANDLE_TAB = 'park_storefront_strategy'


def _need_handle(face: dict) -> bool:
    """
    是否需要处理
    """
    if face['step'] == 'PRIME' and face['status'] != '' and face['init_poi_traj'] == '':
        return True
    return False


def _save_poi(ctx: Context, poi: auto_cpt.Poi):
    """
    保存一个 poi
    """
    qry = f"""
        INSERT INTO sync_traj_bid_record (
        bid, traj_type, batch, std_tag
    ) VALUES (
        '{poi.bid}', 'DEST', 'storefront_work_improve', '{poi.std_tag}'
    )
    ON CONFLICT (bid, traj_type) DO UPDATE SET
        batch = EXCLUDED.batch, 
        updated_at = now();
        """
    return ctx.traj_db.execute(qry)


def _save_pois(ctx: Context, pois: List[auto_cpt.Poi]):
    """
    保存多个
    """
    values = []
    for poi in pois:
        values.append(f" ('{poi.bid}', 'DEST', 'storefront_work_improve', '{poi.std_tag}') ")
    qry = f"""
            INSERT INTO sync_traj_bid_record (
            bid, traj_type, batch, std_tag
        ) VALUES {','.join(values)} 
        ON CONFLICT (bid, traj_type) DO UPDATE SET
            batch = EXCLUDED.batch, 
            updated_at = now();
            """
    return ctx.traj_db.execute(qry)


def _save_pois_v2(ctx: Context, pois: List[auto_cpt.Poi]):
    """
    保存多个
    """
    values = []
    for poi in pois:
        values.append(f" ('{poi.bid}', 'storefront_work_improve', '{poi.city}') ")
    qry = f"""
        INSERT INTO sync_traj_bid_record (
            bid, batch, city 
        ) SELECT v.bid, v.batch, v.city 
        FROM (
            VALUES {','.join(values)} 
        ) AS v(bid, batch, city)
        LEFT JOIN sync_traj_bid_record r ON r.bid = v.bid
        WHERE r.bid IS NULL;
        """
    return ctx.traj_db2.execute(qry)


def _handle_face(ctx: Context, face: dict) -> int:
    """
    处理一个面
    """
    pois = auto_cpt.get_relation_store_pois(face['geom'], search_buffer=50 * 1e-5)
    if not pois:
        print(f"{face['id']} 没有临街 poi？不符合预期")
        return 0
    bid2poi = {_poi.bid: _poi for _poi in pois}

    num = 0
    poi_chunks = array_chunk(list(bid2poi.values()), 100)
    for _chunk in poi_chunks:
        # num += _save_pois(ctx, _chunk)
        num += _save_pois_v2(ctx, _chunk)

    qry = f"""
    update {HANDLE_TAB} 
    set init_poi_traj = 'DONE' where id = {face['id']}
    """
    ctx.poi_db.execute(qry)
    return num


def _handle_part(ctx: Context, min_id: int, max_id: int, where: str) -> int:
    """
    处理一部分
    """
    gener = DBGenerator(
        table=HANDLE_TAB,
        uq_key='id',
        fields=['st_astext(geom) geom', 'id', 'step', 'status', 'init_poi_traj'],
        where=where,
    )
    num = 0
    tot = 0
    for faces in gener.generate(ctx.poi_db.curs, num=200, min_uq=min_id, max_uq=max_id):
        tot += len(faces)
        for _face in faces:
            if not _need_handle(_face):
                continue
            num += _handle_face(ctx, _face)
            # ctx.poi_db.commit_or_rollback()
            # print(f"{min_id} -> {max_id}; 已处理了: {tot}; 保存了：{num} poi")
        print(f"{min_id} -> {max_id}; 已处理了: {tot}; 保存了：{num} poi")
    print(f"{min_id} -> {max_id} 已结束; 总处理了: {tot}; 保存了：{num} poi")
    return num


def _handle_segment(segment: dict) -> int:
    ctx = gen_ctx(autocommit=True)
    return _handle_part(ctx, segment['start_id'], segment['end_id'], segment['where'])


def get_segments(ctx: Context, number: int, where: str) -> list:
    """
    获取分段数
    """
    qry = f"""
    select count(*) from {HANDLE_TAB} 
    where 1 = 1 {where}
    """

    res = ctx.poi_db.fetchone(qry)
    tot = res[0]

    size = (tot + number - 1) // number
    gener = DBUQGenerator(tab=HANDLE_TAB, uq_key='id', where=where)
    segments = gener.generate(curs=ctx.poi_db.curs, num=size, min_uq=0)
    for _item in segments:
        _item['where'] = where
    return segments


def multi_handle(ctx: Context, multi: int, where: str):
    """
    批量处理
    """
    segments = get_segments(ctx, multi, where)
    with multiprocessing.Pool(processes=multi) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.map(_handle_segment, segments)
    total = sum(results)
    message = f"轨迹-临街 poi总共处理了：{total}"
    print(message)
    send_hi(message)


def debug():
    """
    测试
    """
    where = ''
    ctx = gen_ctx(is_committed=False, debug=True)
    _handle_part(ctx, 21171, 21173, where)
    # print(get_segments(ctx, 1, where))


def _clear_other_cities(ctx: Context, white_cities: list):
    """
    清空其他城市的 bid
    """
    qry = f"""
    select distinct city from sync_traj_bid_record 
    where bid != 'to_db_succeed' 
    and city not in ({join_str(white_cities)})
    """
    cities = ctx.traj_db2.get_values(qry)

    for _city in tqdm(cities):
        qry = f"""
        delete from sync_traj_bid_record 
        where city = '{_city}'
        """
        aff = ctx.traj_db2.execute(qry)
        ctx.poi_db.commit_or_rollback()
        print(f"{_city} 清理了：{aff}")


def main():
    """
    主函数
    """
    city_arg = str(ARGS.cities)
    if city_arg != '':
        cities = city_arg.split(',')
    else:
        cities = [
            '江门市',
            # '台州市',
            '嘉兴市',
            '洛阳市',
            '南通市',
            '湛江市',
            '保定市',

            '乌鲁木齐市',
            # '临沂市',
            # '海口市',
            # '湖州市',
            # '沈阳市',
            # '绵阳市',
        ]
    where = f"""
    and step = 'PRIME' and status != '' and init_poi_traj = '' and exists (
        select 1 from park_storefront_task a where a.task_id = {HANDLE_TAB}.task_id 
        and a.city in ({join_str(cities)}) 
    )
    """
    ctx = gen_ctx(autocommit=True, debug=True)
    multi_handle(ctx, 20, where)

    _clear_other_cities(gen_ctx(autocommit=True, debug=True), white_cities=cities)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='核实，图灵的推送和回传')
    parser.add_argument(
        '--cities',
        type=str,
        help="城市，多个用逗号分隔"
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    # debug()
    main()



