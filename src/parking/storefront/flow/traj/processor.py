"""
轨迹处理中心
"""
import os
import uuid
from pathlib import Path

import cv2
from typing import List, Tuple
import shapely.wkt
import shapely.ops
from shapely.geometry import Polygon

from src.parking.storefront.flow.traj import (
    filter as traj_filter,
    model as traj_model,
    group as traj_group,
    cluster as traj_cluster,
    fetcher as traj_fetcher,
    buffer as traj_buffer,
)
from src.parking.storefront.flow.context import Context
from src.parking.storefront.flow.model import road
from src.parking.storefront.flow.model import ach
from src.parking.storefront.flow.rpc import track
from src.parking.storefront.flow.coord_helper import gcj2bd09
from src.aikit import boundary, satellite_imagery, preview_image
from src.parking.storefront.utils import geometric
from src.tools.utils import ensure_dir
from src.parking.storefront.flow.monitor.util import upload_file

METER = 1e-5


def filter_center(wkt_list: list, area_wkt: str, buffer_wkt: str, fns: list):
    """
    过滤中心
    """
    fn2conf = {
        'filter_self_intersecting': {
            'desc': '过滤自相交较多的轨迹',
            'args': (5, ),
            'func': traj_filter.filter_by_self_intersects,
        },
        'filter_by_length': {
            'desc': '过滤碎轨迹',
            'args': (10 * METER, ),
            'func': traj_filter.filter_by_min_length,
        },
        'filter_drift': {
            'desc': '过滤低质轨迹',
            'args': (),
            'func': traj_filter.filter_by_drift,
        },
        'filter_dup': {
            'desc': '过滤重复的轨迹',
            'args': (),
            'func': traj_filter.filter_dup,
        },
        'split_by_intersects_points': {
            'desc': '根据范围过滤',
            'args': (area_wkt, buffer_wkt, 50 * METER),
            'func': traj_filter.filter_by_intersect_points,
        },
        'debug': {
            'desc': 'debug',
            'args': (shapely.wkt.loads(f"POINT(121.11365217379974 28.857414120921252)").buffer(1 * METER).wkt, ),
            'func': traj_filter.filter_by_area,
        },
    }
    # fns.insert(0, 'debug')  # todo debug

    for _fn in fns:
        if _fn not in fn2conf:
            raise Exception(f"{_fn} 不支持的轨迹过滤策略")
        _conf = fn2conf[_fn]
        print(f"{_conf['desc']}-前：{len(wkt_list)}")
        wkt_list = _conf['func'](wkt_list, *_conf['args'])
        print(f"{_conf['desc']}-后：{len(wkt_list)}")

    # _print_tracks(wkt_list)  # todo debug
    return wkt_list


def _print_tracks(wkt_list: list):
    for _wkt in wkt_list:
        print(_wkt)


def filter_v1(wkt_list: list, area_wkt: str, buffer_wkt: str):
    """
    轨迹过滤
    """
    fns = [  # 按照顺序过滤
        'filter_dup',
        'split_by_intersects_points',
        'filter_by_length',
        'filter_drift',
        'filter_self_intersecting',
    ]
    return filter_center(wkt_list, area_wkt, buffer_wkt, fns)


def not_filter(wkt_list: list, area_wkt: str, buffer_wkt: str):
    """
    不过滤
    """
    fns = [  # 按照顺序过滤
        'filter_dup',
    ]
    return filter_center(wkt_list, area_wkt, buffer_wkt, fns)


def group(tracks: list, area_wkt: str, buffer_wkt: str) -> List[List[str]]:
    """
    轨迹分组
    若超过限制，那么需要聚合或者调整保留的轨迹长度
    """
    group_point_limit = 99  # 每组轨迹限制的点数
    group_num_limit = 50  # 限制的组数

    buffer_geo = shapely.wkt.loads(buffer_wkt)
    before_cluster_track_num = 0  # 上一次聚合的轨迹数量

    origins = tracks
    eps = 0.00009090909091 / 2
    times = 0
    save_size = 50
    while True:
        track_of_areas = traj_model.new_tracks_of_area(tracks, area_wkt, buffer_wkt)
        tracks, reduce_num = traj_group.group3(track_of_areas, group_num_limit, group_point_limit)
        if reduce_num == 0:
            return tracks

        save_size -= 5
        if save_size > 20:
            origins = traj_filter.filter_by_intersect_points(origins, area_wkt, buffer_wkt, save_size * METER)

            track_of_areas = traj_model.new_tracks_of_area(origins, area_wkt, buffer_wkt)
            tracks, reduce_num = traj_group.group3(track_of_areas, group_num_limit, group_point_limit)
            if reduce_num == 0:
                return tracks

        group_tracks = tracks

        tracks = traj_cluster.cluster_tracks(buffer_geo, origins, eps)
        if before_cluster_track_num == len(tracks):
            print(f"如果最近两次聚合后，轨迹数量是一致，那么不再聚合;当前：{len(tracks)}; 上一次：{before_cluster_track_num}")
            return group_tracks
        before_cluster_track_num = len(tracks)

        times += 1
        if times > 3:
            eps = eps * 1.5
        else:
            eps = eps * 1.1

        print(f"聚合的次数：{times}; 量级：{len(tracks)} 聚合的距离：{eps / 0.00009090909091}")
        if times > 20:
            print(f"循环次数过多：{times}; 结束轨迹处理")
            return group_tracks


def get_verify_area_track_group(ctx: Context, area_wkt: str) -> List[List[str]]:
    """
    获取核实面轨迹组
    """
    track_group, _, _ = get_verify_area_track_info(ctx, area_wkt)
    return track_group


def get_verify_area_track_info(ctx: Context, area_wkt: str, used_storefront_track=True) -> tuple:
    """
    获取核实面轨迹信息
    """
    buffer_size = 30 * METER
    polygon = shapely.wkt.loads(area_wkt)

    buffer = _get_track_buffer_polygon(ctx, polygon, buffer_size)
    origin = traj_fetcher.fetch_tracks(buffer.wkt, used_storefront_track)
    tracks = filter_v1(origin, area_wkt, buffer.wkt)
    return group(tracks, area_wkt, buffer.wkt), buffer, origin


def get_achievement_area_track_group(ctx: Context, area_wkt: str, sid='') -> List[List[str]]:
    """
    获取成果面轨迹组
    """
    track_group, _, _ = get_achievement_area_track_info(ctx, area_wkt, sid)
    return track_group


def get_achievement_area_track_info(
        ctx: Context, area_wkt: str, sid='', baseline=None, used_storefront_track=True) -> tuple:
    """
    获取成果面轨迹信息
    """
    buffer_size = 30 * METER
    polygon = shapely.wkt.loads(area_wkt)

    if baseline is None:
        baseline = get_base_line(ctx, sid)
    buffer = _get_track_buffer_polygon_v2(ctx, polygon, buffer_size, baseline)
    origin = traj_fetcher.fetch_tracks(buffer.wkt, used_storefront_track)
    tracks = filter_v1(origin, area_wkt, buffer.wkt)
    return group(tracks, area_wkt, buffer.wkt), buffer, origin


def gen_track_img(tracks: List[str], area_wkt: str, buffer_wkt: str, img_file: str, baseline=None):
    """
    生成轨迹图片
    """
    bounds = boundary.from_wkt(area_wkt, buffer=60 * 1e-5)
    image = satellite_imagery.crop(bounds)
    if image is None:
        print(f"{img_file} 画图失败")
        return False

    polygon = shapely.wkt.loads(area_wkt)
    buffer_polygon = shapely.wkt.loads(buffer_wkt)

    for _geo in geometric.flat_polygon(buffer_polygon):
        preview_image.draw_polygon(image, _geo.wkt, bounds, thickness=1, color=preview_image.COLOR_GREEN)
    if baseline is not None:
        preview_image.draw_linestring(image, baseline, bounds, thickness=1, color=preview_image.COLOR_RED)
    for _track in tracks:
        preview_image.draw_linestring(image, _track, bounds, thickness=1, color=preview_image.COLOR_YELLOW)
    for _geo in geometric.flat_polygon(polygon):
        preview_image.draw_polygon(image, _geo.wkt, bounds, thickness=3, color=preview_image.COLOR_BLUE)
    cv2.imwrite(str(img_file), image)
    return os.path.exists(img_file)


def get_park_track_group_for_access(ctx: Context, park: ach.ParkingAch) -> List[List[str]]:
    """
    获取停车场的轨迹组, 针对出入口作业的
    """
    sid = extrack_sid(park.info_id)
    return get_achievement_area_track_group(ctx, park.geom, sid)


def get_park_track_info_for_access(ctx: Context, park: ach.ParkingAch, used_storefront_track=True) -> tuple:
    """
    获取停车场的轨迹信息, 针对出入口作业的
    """
    sid = extrack_sid(park.info_id)
    # if park.central_line != '':
    #     baseline = shapely.wkt.loads(park.central_line)
    # else:
    #     baseline = _get_base_line(ctx, sid)
    baseline = get_base_line(ctx, sid)
    # baseline = shapely.wkt.loads(park.central_line)
    return get_achievement_area_track_info(ctx, park.geom, sid, baseline, used_storefront_track)


def extrack_sid(info_id: str):
    """
    提取策略面 id
    """
    sid = ''
    if '_strategy' in info_id:
        items = info_id.split('_strategy')
        if str(items[0]).isdigit():
            sid = int(items[0])
        # sid = int(str(info_id).replace('_strategy', ''))
    return sid


def flatten_group(groups: List[List[str]]) -> List[str]:
    """
    展开组
    """
    result = []
    for _group in groups:
        result += _group
    return result


def get_track_ids(groups: List[List[str]], env: str) -> List[str]:
    """
    获取轨迹id
    """
    if env == 'test':
        client = track.new_test_client()
    elif env == 'pre':
        client = track.new_pre_client()
    elif env == 'online':
        client = track.new_online_client()
    else:
        raise Exception(f"{env} 不支持")

    uuids = []
    for _group in groups:
        _group = [gcj2bd09(_wkt) for _wkt in _group]
        uuids += client.add_tracks_group(_group)
    print(f"groups:{len(groups)}; uuids: {len(uuids)}")
    return uuids[:50]


def get_online_track_ids_and_img_url(
        ctx: Context, park: ach.ParkingAch, used_storefront_track=True) -> Tuple[List[str], str]:
    """
    获取线上轨迹 ids 和 图片 url
    """
    track_group, buffer_geo, origin = get_park_track_info_for_access(ctx, park, used_storefront_track)
    track_ids = get_online_track_ids(track_group)
    return track_ids, _gen_track_img_url(park.bid, origin, park.geom, buffer_geo.wkt)


def _gen_track_img_url(uid: str, tracks: List[str], area_wkt, buffer_wkt) -> str:
    tmp_dir = Path('./tmp/track_img')
    ensure_dir(tmp_dir)
    img_file = str(tmp_dir / f"{uuid.uuid4().hex}_{uid}.jpg")
    if not gen_track_img(tracks, area_wkt, buffer_wkt, img_file):
        return ''
    return upload_file(img_file)


def get_online_verify_track_ids_and_img_url(
        ctx: Context, area_wkt: str, used_storefront_track=True) -> Tuple[List[str], str]:
    """
    获取线上核实面轨迹 ids 和图片 url
    """
    track_group, buffer_geo, origin = get_verify_area_track_info(ctx, area_wkt, used_storefront_track)
    track_ids = get_online_track_ids(track_group)
    return track_ids, _gen_track_img_url('', origin, area_wkt, buffer_geo.wkt)


def get_pre_verify_track_ids_and_img_url(
        ctx: Context, area_wkt: str, used_storefront_track=True) -> Tuple[List[str], str]:
    """
    获取试生产核实面轨迹 ids 和图片 url
    """
    track_group, buffer_geo, origin = get_verify_area_track_info(ctx, area_wkt, used_storefront_track)
    track_ids = get_pre_track_ids(track_group)
    return track_ids, _gen_track_img_url('', origin, area_wkt, buffer_geo.wkt)


def get_pre_track_ids_and_img_url(ctx: Context, park: ach.ParkingAch) -> Tuple[List[str], str]:
    """
    获取试生产环境 轨迹 ids 和 图片 url
    """
    track_group, buffer_geo, origin = get_park_track_info_for_access(ctx, park)
    track_ids = get_pre_track_ids(track_group)
    return track_ids, _gen_track_img_url(park.bid, origin, park.geom, buffer_geo.wkt)


def get_online_track_ids(groups: List[List[str]]) -> List[str]:
    """
    获取线上环境的轨迹 id
    """
    return get_track_ids(groups, 'online')


def get_pre_track_ids(groups: List[List[str]]) -> List[str]:
    """
    获取试生产环境的轨迹 id
    """
    return get_track_ids(groups, 'pre')


def get_test_track_ids(groups: List[List[str]]) -> List[str]:
    """
    获取测试生产环境的轨迹 id
    """
    return get_track_ids(groups, 'test')


def get_base_line(ctx, sid):
    """
    获取 base_line
    """
    baseline = shapely.wkt.loads('LineString EMPTY')
    strategy = _get_strategy_info(ctx, sid)
    if strategy is not None and len(strategy) > 0:
        baseline = shapely.wkt.loads(strategy[1])
    return baseline


def _get_strategy_info(ctx: Context, sid=''):
    """
    获取策略面
    """
    if sid == '':
        return None
    qry = f"""
    select st_astext(geom), st_astext(baseline) 
    from park_storefront_strategy 
    where id = {sid} 
    """
    return ctx.poi_db.fetchone(qry)


def _get_track_buffer_polygon(ctx, polygon, buffer_size):
    """
    获取轨迹 buffer 范围
    """
    street = find_best_matching_street(ctx, polygon)
    ld_wkt = road.get_complete_ld_polygon_2d(ctx, polygon.buffer(buffer_size).wkt)
    ld_geo = shapely.wkt.loads(ld_wkt)
    return traj_buffer.generate_area_track_buffer(polygon, buffer_size, street, ld_geo)


def _get_track_buffer_polygon_v2(ctx: Context, polygon, buffer_size, baseline):
    street = find_best_matching_street(ctx, polygon)
    ld_wkt = road.get_complete_ld_polygon_2d(ctx, polygon.buffer(buffer_size).wkt)
    ld_geo = shapely.wkt.loads(ld_wkt)
    return traj_buffer.generate_track_buffer(polygon, baseline, buffer_size, buffer_size, street, ld_geo)


def find_best_matching_street(ctx: Context, polygon):
    """
    根据给定 polygon，查询与之相交的街区，并返回交集面积最大的一个街区 Polygon。
    """
    qry = f"""
        select st_astext(geom) from street_region 
        where st_intersects(geom, st_geomfromtext('{polygon.wkt}', 4326))
        """
    wkts = ctx.poi_db.get_values(qry)
    if not wkts:
        return Polygon()
    unioned_geom = shapely.ops.unary_union([shapely.wkt.loads(wkt) for wkt in wkts])
    return traj_buffer.extract_largest_overlap_polygon(unioned_geom, polygon)


def track_ready(ctx: Context) -> bool:
    """
    轨迹资料准备好了返回 True
    """
    key = 'to_db_succeed'
    qry = f"""
    select bid from sync_traj_bid_record where bid = '{key}' limit 1
    """
    res = ctx.traj_db2.fetchone(qry)
    if len(res) > 0:
        return True

    qry = f"""
    select bid from park_storefront_dest_traj where bid = '{key}' limit 1
    """
    res = ctx.traj_db2.fetchone(qry)
    if len(res) > 0:
        return True

    return False

