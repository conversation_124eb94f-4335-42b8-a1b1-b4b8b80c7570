"""
恢复出入口数据
"""
import argparse
import csv
import json
import logging
import multiprocessing
import re
import time
import uuid
from typing import List

import dataclasses
import requests
import shapely.wkt
import shapely.ops
from shapely.geometry import Polygon, mapping, shape
from tqdm import tqdm

from src.parking.storefront.flow.context import Context, gen_ctx
from src.parking.storefront.flow.db import array_chunk, join_int, join_str, handle_apostrophe
from src.parking.storefront.flow.coord_helper import bdmc2gcj, bd092gcj
from src.parking.storefront.flow.model import cmd, ach
from src.parking.storefront.flow.cmd import generator
from src.parking.storefront.diff.polygon_differ import classify
from src.parking.storefront.flow import verifier


@dataclasses.dataclass
class RepairedPark:
    """
    待修复的停车场
    """
    id: int
    bid: str
    status: str
    city: str
    check_memo: str
    geom: str

    @staticmethod
    def from_park_ach(park: ach.ParkingAch) -> 'RepairedPark':
        """
        从成果库获取待修复的停车场
        """
        return RepairedPark(
            id=int(park.park_id),
            bid=park.bid,
            status=park.status,
            city=park.city,
            check_memo=park.check_memo,
            geom=park.geom,
        )

    @staticmethod
    def from_online_park(ctx, park: ach.OnlineParking) -> 'RepairedPark':
        """
        从线上库获取待修复的停车场
        """
        admin = _get_admin(ctx, park.geom)
        return RepairedPark(
            id=0,
            bid=park.bid,
            status='ONLINE' if park.status == 1 else 'OFFLINE',
            city=admin['cityname'],
            check_memo='线上面',
            geom=park.geom,
        )

    def get_show_check_memo(self, ctx: Context) -> str:
        """
        获取展示的提示信息
        """
        if self.status == 'OFFLINE':
            return '更新'
        reasons = get_reasons(self.check_memo)
        if len(reasons) > 0:
            return self.check_memo

        qry = f"""
        select check_memo from {ctx.access_ach_tab} 
        where status = 'CHECK_FAILED' and check_memo like '%出入口与门前面的距离过远%' 
        and parent_bid = '{self.bid}' 
        """
        res = ctx.poi_db.fetchone(qry)
        if len(res) > 0:
            return res[0]
        return ''


def push_park_update_data(ctx: Context, ids: list):
    """推送停车场更新数据"""
    # ids = [
    #     31849,
    #     31310
    # ]

    qry = f"""
    select id, bid, check_memo, st_astext(geom) geom from {ctx.park_ach_tab} where id in ({join_int(ids)})
    """
    res = ctx.poi_db.fetchall(qry)

    parks = []
    for item in res:
        parks.append({
            'id': item[0],
            'bid': item[1],
            'check_memo': item[2],
            'geom': item[3],
        })
    push_turing(ctx, parks)


def push_turing(ctx: Context, parks: List[RepairedPark], tips: str = '核实门前停车场'):
    """
    推送图灵 核实; 返回图灵 id
    """
    representative_point = ''
    sub_infos = []
    tpl_wkt = ''
    for a_park in parks:
        item = {
            'intelligence_conclusion': None,
            'polygon_conclusion': None,
            'polygon_check_memo': '',
            'unique_id': '',
            'bid': '',
            'polygons': [],
            "expanded": True,
        }
        geom = a_park.geom
        representative_point = _gcj2bd09(shapely.wkt.loads(geom).representative_point())
        tpl_wkt = geom
        wkt_bd09 = _gcj2bd09(geom)
        if wkt_bd09 == '':
            raise Exception(f"坐标转换失败")

        unique_id = a_park.id if a_park.id > 0 else int(a_park.bid)

        geo_bd09 = shapely.wkt.loads(wkt_bd09)
        polygons = []
        if geo_bd09.geom_type == 'MultiPolygon':
            for idx, _geo in enumerate(geo_bd09.geoms):
                gid = unique_id * 10 + idx
                polygons.append(_geo2map_v2(_geo, gid))
        else:
            polygons.append(_geo2map_v2(geo_bd09, unique_id * 10))
        item['polygons'] = polygons
        item['polygon_check_memo'] = a_park.get_show_check_memo(ctx)
        item['unique_id'] = str(unique_id)
        item['bid'] = a_park.bid

        sub_infos.append(item)

    ref_geos = [_geo2map(shapely.wkt.loads(representative_point))]
    admin = _get_admin(ctx, tpl_wkt)
    param = {
        "sourceid": f"park_storefront_update_{uuid.uuid4().hex}",
        "cityname": admin['cityname'],
        "sign": "7a58af613830561c96bd1fcedc23258c",
        "source": 1214,
        "platform": 0,
        # "content": "核实门前停车场",
        "content": f"{tips}",
        "_signkeys": "platform,source",
        "subbusinesstype": 408,
        "provname": admin['province_ch'],
        "problem": 0,
        "occurtime": int(time.time()),
        "name": "",
        "coordsys": "bd09ll",
        "refgeo": ref_geos,
        'properties': {
            'need_giving_back': {
                'data_type': 'park_front_area_update',  # 门前停车场更新
            },
            'sub_info': sub_infos,
        },
    }
    # ctx.url = 'http://*************:80/stargaze/api/addintelligence'  # test
    ctx.url = 'http://*************:80/stargaze/api/addintelligence'  # online
    print(ctx.url)
    print(json.dumps(param, ensure_ascii=False))
    # exit()
    # return '123'  # todo test
    try:
        res = requests.post(
            ctx.url, json=param
        )
        """
        {'errno': 0, 'errmsg': 'success', 'data': {'id': '676cc6e22d4c5da4e56c4d51', 'duplicate': 0}}
        """
        print(res.json())
        data = res.json()
        if data['errno'] == 0:
            return data['data']['id']
        return ''
    except Exception as e:
        logging.exception(e)
        return ''


def _get_admin(ctx: Context, geom: str) -> dict:
    """
    获取行政区划
    """
    qry = f"select province_ch, cityname from mesh_conf_wkt where st_intersects(wkt, st_geomfromtext('{geom}', 4326))"
    res = ctx.back_db.fetchone(qry)
    if not res:
        raise Exception(f"获取行政区划失败")
    return {
        'province_ch': res[0],
        'cityname': res[1],
    }


def _gcj2bd09(wkt: str) -> str:
    """
    gcj -> bd09
    """
    url = 'http://mapde-poi.baidu-int.com/prod/api/coordsTrans'
    param = {
        'wkt': wkt,
        'from': '1',
        'to': '3',
    }
    res = requests.post(
        url, data=param,
    )
    jsn = res.json()
    if jsn['code'] != 0:
        print(jsn)
        return ''
    return jsn['data']


def _geo2map(geo: Polygon, gid: int = None) -> dict:
    properties = {
        'color': '#33FF00',
    }
    if gid:
        properties['id'] = gid
    return {
        "type": "Feature",
        "geometry": mapping(geo),
        "properties": properties,
    }


def _geo2map_v2(geo: Polygon, gid: int = None) -> dict:
    data = mapping(geo)
    properties = {
        'color': '#33FF00',
    }
    if gid:
        properties['id'] = gid
    data['properties'] = properties
    return data


def get_area_err_parks(ctx: Context, where: str) -> List[RepairedPark]:
    """
    获取面异常的停车场
    """
    qry = f"""
    select id from {ctx.park_ach_tab} where {where}
    """
    ids = ctx.poi_db.get_values(qry)
    return [RepairedPark.from_park_ach(a_park) for a_park in ach.get_parking_ach_by_ids(ctx, ids)]


def only_cover_other_online_failed(reason: str) -> bool:
    """
    仅存在压盖其他渠道上线的门前停车场，返回 True
    """
    if '仅存在压盖其他渠道上线的门前停车场：' not in reason:
        return False
    others = [
        '存在压盖线上面：',
        '存在压盖线下面：',
        'face too short:',
        '压盖LD：',
        '存在锐角：',
    ]
    for item in others:
        if item in reason:
            return False
    return True


def get_reasons(check_memo: str) -> list:
    """
    获取原因
    """
    err_map = {
        '存在压盖线上面：': 'Cover_Online_Area',
        '存在压盖线下面：': 'Cover_Offline_Area',
        'face too short:': 'Face_Too_Short',
        '压盖LD：': 'Cover_LD',
        '存在锐角：': 'ExistsSharpAngle',
    }
    reasons = []
    for err, txt in err_map.items():
        if err in check_memo:
            reasons.append(txt)
    return list(set(reasons))


def extract_bids(text):
    """
    提取压盖的 bids
    (?: ... ) 代表非捕获组，它匹配 存在压盖线下面 或 存在压盖线上面。
    | 是 "或" 操作符，表示匹配 “存在压盖线下面” 或 “存在压盖线上面”。
    """
    pattern = r'(?:存在压盖线下面|存在压盖线上面)：([\d,]+)'
    matches = re.findall(pattern, text)
    bids = []
    for match in matches:
        bids.extend(match.split(','))
    return list(set([bid.strip() for bid in bids if bid.strip()]))


def rely_bids(ctx: Context, park: RepairedPark) -> list:
    """
    获取一个停车场依赖的 bid；
    返回 当前 bid + 依赖的 bid
    """
    if only_cover_other_online_failed(park.check_memo):
        return []

    self = [park.bid]
    bids = extract_bids(park.check_memo)
    if len(bids) == 0:
        return self

    # 再在库里过一遍
    qry = f"""
    select distinct(bid) from parking where bid in ({join_str(bids)})
    """
    return list(set(ctx.back_db.get_values(qry) + self))


def save_verify_info(ctx: Context, parks: List[RepairedPark], batch) -> int:
    """
    保存面核实信息，返回主键 id
    """
    geos = [shapely.wkt.loads(a_park.geom) for a_park in parks]
    ugeo = shapely.ops.unary_union(geos)
    city = parks[0].city
    info_id = uuid.uuid4().hex

    qry = f"""
    insert into {ctx.verified_push_tab} (info_id, info_source, verifier, verify_id, batch, geom, city, data_type) 
    values ('{info_id}', 'repair', 'turing', '', '{batch}', st_geomfromtext('{ugeo.wkt}', 4326), '{city}', '1214')
    """
    return ctx.poi_db.insert_return_id(qry)


def save_repair(ctx: Context, verify_info_id: int, parks: List[RepairedPark]):
    """
    保存修复信息
    """
    for a_park in parks:
        reason = ','.join(get_reasons(a_park.check_memo))
        args = {
            'check_memo': a_park.check_memo,
            'status': a_park.status,
        }

        if a_park.id == 0:
            # 线上的
            prev_id = a_park.bid
            prev_tab = ctx.park_tab
        else:
            # 线下的
            prev_id = a_park.id
            prev_tab = ctx.park_ach_tab

        qry = f"""
        insert into {ctx.repair_tab} (prev_id, prev_tab, user_name, user_id, reason, args, status) 
        values ('{prev_id}', '{prev_tab}', '{ctx.verified_push_tab}', '{verify_info_id}', '{reason}', 
        '{handle_apostrophe(json.dumps(args, ensure_ascii=False))}', 'INIT')
        """
        ctx.poi_db.execute(qry)


def maintain_park_status(ctx: Context, parks: List[RepairedPark]):
    """
    修复停车场状态
    """
    for a_park in parks:
        # 更新时加了个状态限制，是因为有的面状态还处于可能再一次流转的可能，比如口作业中，等等
        # 为了避免影响面的正常状态流转，若状态还可能被流程变更，那么状态先不变，等后续作业回来再操作，因为可能这个面是对的，不需要变更
        # 上线时需要加一道拦截，避免还在修复中的就上线了
        if a_park.id <= 0:
            continue
        qry = f"""
                update {ctx.park_ach_tab} set status = 'WAIT_REPAIR' where id = {a_park.id} and 
                status in ('CHECK_FAILED', 'AREA_CHECK_FAILED') and bid_status = 'effected'
                """
        ctx.poi_db.execute(qry)


def _get_group_parks(ctx: Context, bids: list) -> List[RepairedPark]:
    """
    获取一组停车场
    """
    parks = ach.get_parking_aches_by_bids(ctx, bids)
    parks = [RepairedPark.from_park_ach(a_park) for a_park in parks]
    if len(parks) == len(bids):
        return parks

    exists_bids = [a_park.bid for a_park in parks]
    for bid in bids:
        if bid in exists_bids:
            continue
        print(f"{bid} 获取线上面")
        a_online_park = ach.get_online_park(ctx, bid)
        parks.append(RepairedPark.from_online_park(ctx, a_online_park))
    return parks


def repair_area_err_parks(ctx: Context, parks: List[RepairedPark], batch: str):
    """
    修复面异常的停车场
    """
    rely_bids_list = []
    for a_park in tqdm(parks):
        r_bids = rely_bids(ctx, a_park)
        print('r_bids', a_park.check_memo, r_bids, a_park.bid)
        if len(r_bids) > 0:
            rely_bids_list.append(r_bids)

    classify_bids_list = classify(rely_bids_list)
    for classify_bids in tqdm(classify_bids_list):
        group_parks = _get_group_parks(ctx, classify_bids)
        verify_info_id = save_verify_info(ctx, group_parks, batch)
        save_repair(ctx, verify_info_id, group_parks)
        maintain_park_status(ctx, group_parks)
        ctx.poi_db.commit_or_rollback()
    print('over')


def _get_repaired_parks_by_verify_pushed_info_id(ctx: Context, info_id: int) -> List[RepairedPark]:
    """
    根据推送的情报，获取待修复的面
    """
    qry = f"""
        select prev_id, prev_tab from {ctx.repair_tab} 
        where user_name = '{ctx.verified_push_tab}' and user_id = '{info_id}'
        """
    res = ctx.poi_db.fetchall(qry)

    parks = []
    for item in res:
        prev_id = item[0]
        prev_tab = item[1]
        if prev_tab == ctx.park_ach_tab:
            parks.append(RepairedPark.from_park_ach(ach.get_parking_ach(ctx, int(prev_id))))
        elif prev_tab == ctx.park_tab:
            parks.append(RepairedPark.from_online_park(ctx, ach.get_online_park(ctx, prev_id)))
        else:
            raise Exception(f"{item} 不存在对应的停车场面")
    return parks


def _push_info(ctx: Context, info_id: int):
    """
    推送一条情报
    """
    parks = _get_repaired_parks_by_verify_pushed_info_id(ctx, info_id)

    turing_id = push_turing(ctx, parks)
    if turing_id == '':
        raise Exception(f"id: {info_id} 推送图灵失败")
    print(f"id:{info_id}, turing_id:{turing_id}")

    info_qry = f"""
    update {ctx.verified_push_tab} 
    set verify_id = '{turing_id}', status = 'PUSHED' 
    where id = {info_id}
    """
    repair_qry = f"""
    update {ctx.repair_tab} 
    set status = 'ING' 
    where user_id = '{info_id}' and user_name = '{ctx.verified_push_tab}'
    """
    queries = [info_qry, repair_qry]
    park_ids = [a_park.id for a_park in parks if a_park.id > 0]
    if len(park_ids) > 0:
        park_qry = f"""
        update {ctx.park_ach_tab} 
        set status = 'REPAIRING' 
        where id in ({join_int(park_ids)}) and status = 'WAIT_REPAIR'
        """
        queries.append(park_qry)

    for qry in queries:
        ctx.poi_db.execute(qry)


def push(ctx: Context, batch: str):
    """
    推送
    """
    # ctx = gen_ctx(is_committed=False, debug=True)
    # ctx = gen_ctx(is_committed=True, debug=True)
    # # batch = 'beijing_repair_2025030112'
    # batch = 'shanghai_repair_2025030114'

    qry = f"""
    select id from {ctx.verified_push_tab} where batch = '{batch}' and status = 'INIT' and data_type = '1214' 
--     and id = 44755
    """
    ids = ctx.poi_db.get_values(qry)

    num = 0
    for info_id in tqdm(ids):
        _push_info(ctx, info_id)
        num += 1
        # if num > 2:
        #     break
        ctx.poi_db.commit_or_rollback()


def push_upd(ctx: Context, batch: str):
    """推送更新"""
    qry = f"""
    select id, bid from {ctx.verified_push_tab} 
    where batch = '{batch}' and status = 'INIT' and data_type = '1214' 
    """
    infos = [{
        'id': item[0],
        'bid': item[1],
    } for item in ctx.poi_db.fetchall(qry)]

    num = 0
    for a_info in infos:
        _push_upd_info(ctx, a_info)
        num += 1
        # if num > 2:
        #     break
        ctx.poi_db.commit_or_rollback()


def _push_upd_info(ctx: Context, info: dict):
    """
    推送一条情报
    """
    if info['bid'] == '':
        raise Exception(f"{info} 没有 bid")
    parks = [RepairedPark.from_park_ach(ach.get_parking_ach_by_bid(ctx, info['bid']))]

    info_id = info['id']
    turing_id = push_turing(ctx, parks)
    if turing_id == '':
        raise Exception(f"id: {info_id} 推送图灵失败")
    print(f"id:{info_id}, turing_id:{turing_id}")

    info_qry = f"""
    update {ctx.verified_push_tab} 
    set verify_id = '{turing_id}', status = 'PUSHED' 
    where id = {info_id}
    """
    ctx.poi_db.execute(info_qry)


def _get_pushed_info(ctx: Context, turing_id: str):
    """
    获取推送的情报
    """
    qry = f"""
    select id, info_id, bid, status from {ctx.pushed_tab} where verify_id = '{turing_id}'
    """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        return {}
    return {
        'id': res[0],
        'info_id': res[1],
        'bid': res[2],
        'status': res[3],
    }


def callback_turing_id(turing_id):
    """
    回传图灵
    """
    # verify_ctx = verifier.gen_ctx('test', '1214')
    verify_ctx = verifier.gen_ctx('online', '1214')
    verifier.callback(verify_ctx, turing_id, 1)


def consume_turing_result(ctx: Context):
    """
    消费图灵的核实结果
    https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/3ZlP6R54FU/j4oQPAkxhezcNi
    """

    qry = f"""
    select turing_id, bp_message, id from parking_turing_result where data_type = '1214' and status = 'INIT' 
--     limit 1
    """
    res = ctx.poi_db.fetchall(qry)

    for item in tqdm(res):
        bp_message = json.loads(item[1])
        turing_id = item[0]
        park_trid = item[2]
        # if turing_id != '676ccade2d4c5da3090ab579':
        #     continue

        pushed_info = _get_pushed_info(ctx, turing_id)
        if len(pushed_info) == 0:
            # raise Exception(f"")
            print(f"{turing_id} 不存在对应的情报")
            continue
        try:
            data_commit = json.loads(bp_message['data'])
        except Exception as e:
            logging.exception(e)
            continue
        edit_commit = data_commit['edit_commit']
        sub_result = edit_commit['sub_result']

        have_error = False
        for work in edit_commit['sub_info']:
            recall = work['intelligence_conclusion']
            precise = work['polygon_conclusion']
            unique_id = work['unique_id'] if work['unique_id'] else ''
            bid = work['bid'] if work['bid'] else ''

            try:
                geom = "POLYGON EMPTY"
                geos = []
                if 'polygons' in work:
                    for _item_geo in work['polygons']:
                        geos.append(shape(_item_geo))
                if len(geos) > 0:
                    ugeo = shapely.ops.unary_union(geos)
                    geom = bd092gcj(ugeo.wkt)
            except Exception as e:
                logging.exception(e)
                have_error = True
                break

            qry = f"""
            insert into {ctx.turing_result_tab} (info_id, turing_id, recall, precise, geom, sub_result, bid, data_type) values 
            ('{pushed_info['info_id']}', '{turing_id}', '{recall}', '{precise}', st_geomfromtext('{geom}', 4326), '{sub_result}', '{bid}', '1214')
            """
            rid = ctx.poi_db.insert_return_id(qry)

            if unique_id == '':
                # 新增
                continue

            # 更新，结果写入修复记录
            arg = {
                'turing_result_id': rid,
                'sub_result': sub_result,
                'recall': recall,
                'precise': precise,
            }
            prev_tab = ctx.park_ach_tab
            if unique_id == bid:
                prev_tab = ctx.park_tab
            qry = f"""
            update {ctx.repair_tab} set status = 'DONE', result = '{json.dumps(arg, ensure_ascii=False)}' 
            where user_id = '{pushed_info['id']}' and user_name = '{ctx.pushed_tab}' 
                and prev_id = '{unique_id}' and prev_tab = '{prev_tab}'
            """
            aff = ctx.poi_db.execute(qry)

            if aff > 0 and prev_tab == ctx.park_ach_tab:
                # 线下成果表
                qry = f"""
                update {ctx.park_ach_tab} set status = 'REPAIRED' where id = {unique_id} and status = 'REPAIRING'
                """
                ctx.poi_db.execute(qry)

        if have_error:
            continue

        queries = [
            f"""
                update parking_turing_result set status = 'DONE' where id = {park_trid}
            """,
            f"""
                update {ctx.pushed_tab} set status = 'CALLBACKED', result_num = result_num + 1 
                where id = {pushed_info['id']}
            """,
        ]
        for qry in queries:
            ctx.poi_db.execute(qry)
        ctx.poi_db.commit_or_rollback()

        # callback_turing_id(turing_id)


def init(ctx: Context):
    """初始化"""
    # city_zh = '北京市'
    # city_en = 'beijing'

    # city_zh = '上海市'
    # city_en = 'shanghai'
    # city_en = 'top30'

    # city_zh = '北京市'
    city_en = 'beijing_shanghai'

    batch = f'{city_en}_repair_' + time.strftime('%Y%m%d', time.localtime(time.time()))
    where = f"""
        bid_status = 'effected' 
        and (
            (status = 'CHECK_FAILED' and check_memo != 'ACCESS_CHECK_FAILED') 
            or status = 'AREA_CHECK_FAILED' 
            or (status = 'CHECK_FAILED' 
                and exists (
                    select 1 from park_storefront_prod_access a 
                    where a.parent_bid = park_storefront_prod_parking.bid 
                    and a.status = 'CHECK_FAILED' and (a.check_memo like '%出入口与门前面的距离过远%' or a.check_memo like '%出入口与对应LINK的距离过远%')
                )
            )
        ) 
        and city in ('北京市', '上海市') 
        """
    parks = get_area_err_parks(ctx, where)
    print(len(parks))
    # exit()
    repair_area_err_parks(ctx, parks, batch)


def main():
    """主函数"""
    fns = ARGS.fns
    batch = ARGS.batch
    ctx = gen_ctx(is_committed=True, debug=True)
    if fns == 'init':
        init(ctx)
    elif fns == 'push':
        push(ctx, batch)
    elif fns == 'push_upd':
        push_upd(ctx, batch)
    elif fns == 'callback':
        consume_turing_result(ctx)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='核实，图灵的推送和回传')
    parser.add_argument(
        '--fns',
        type=str,
        required=True,
        help="执行的函数；callback: 图灵回传"
    )
    parser.add_argument(
        '--batch',
        type=str,
        help="推送的批次号"
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()
