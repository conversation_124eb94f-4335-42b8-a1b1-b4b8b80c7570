"""
面核实
"""
import argparse
import csv
import json
import logging
import multiprocessing
import os
import random
import subprocess
import time
import uuid
import math
from datetime import datetime
from pathlib import Path

import pymysql
from typing import List, Tuple

import dataclasses
from collections import defaultdict

import shapely.wkt
import shapely.ops
from shapely.geometry import Polygon, mapping, shape, LineString
import requests
from tqdm import tqdm
from PIL import Image as PImage
from io import BytesIO

from src.parking.storefront.flow.db import ReadDb, DB, join_int, join_str, handle_apostrophe, array_chunk
from src.parking.storefront.verify.verified_image import is_viaduct
from src.tools import pgsql, locker
from src.parking.storefront.post_process import autocomplete as auto_cpt
from src.parking.storefront.flow.traj import processor as traj_processor
from src.parking.storefront.approval import icafe
import src.tools.redis_tool as rt
from src.tools import ruliu
from src.tools import pgsql
from src.tools.utils import ensure_dir
from src.trajectory.utils import coord_trans
from src.parking.storefront.verify.verified_flow import Image
from src.parking.storefront.flow.filter.filter import filter_dup_strategy_face
from src.parking.storefront.flow.merger.db_utli import get_faces_by_where
from src.parking.storefront.flow.merger import merger
from src.parking.storefront.flow.rpc.image import upload_pic
from src.aikit import boundary
from src.aikit.track import track_point
from src.parking.storefront.utils.geometric import flat_polygon
from src.parking.storefront.flow.filter.distance_with_bud import filter_close_with_bud, FaceDistanceBud, FaceDistResp
from src.parking.storefront.flow.manual import multi_juejin_pusher as multi_juejin
from src.tools.utils import calc_iou


class Context:
    """
    上下文
    """
    env: str
    # is_committed = False  # 方便测试
    is_committed = True

    url: str
    callback_urls: str

    offline_url: str  # 下线 url

    poi_db: DB
    back_db: ReadDb
    road_db: ReadDb
    traj_db2: ReadDb

    strategy_tab = 'park_storefront_strategy'
    pushed_tab = 'park_storefront_verify_pushed'
    task_tab = 'park_storefront_task'
    turing_result_tab = 'park_storefront_turing_result'

    def get_callback_url(self) -> str:
        """
        获取回调 url
        """
        return random.choice(self.callback_urls)

    def is_online(self) -> bool:
        """
        线上环境返回 True
        """
        return self.env == 'online'


def gen_ctx(env: str, data_type: str = '1209', autocommit=False) -> Context:
    """
    生成上下文
    """
    print(f"环境是：{env}")

    ctx = Context()

    if env == 'online':
        offline_url = 'http://*************:80/stargaze/api/removeIntelligence'
        url = 'http://*************:80/stargaze/api/addintelligence'
        callback_urls = []
        bns = 'group.opera-platformIntelligence-plartformIntelligence-all.map-de.all'
        for host in get_host_by_bns(bns):
            ip, port = host
            callback_urls.append(f"http://{ip}:{port}/stargaze/feedback/aoi_feedback_ack/{data_type}")
    else:
        offline_url = 'http://istargaze.map.baidu.com/stargaze/api/removeIntelligence'
        url = 'http://*************:80/stargaze/api/addintelligence'
        callback_urls = [
            f'http://***********:8086/stargaze/feedback/aoi_feedback_ack/{data_type}'
        ]
    ctx.offline_url = offline_url
    ctx.url = url
    ctx.callback_urls = callback_urls
    ctx.env = env

    ctx.poi_db = DB(pgsql.POI_CONFIG, read_only=(not ctx.is_committed), debug=True, autocommit=autocommit)
    ctx.back_db = ReadDb(pgsql.BACK_CONFIG)

    print(f"road_with_idx: {pgsql.ROAD_CONFIG_WITH_INDEX()}")
    ctx.road_db = ReadDb(config=pgsql.ROAD_CONFIG_WITH_INDEX, debug=True, autocommit=True)
    ctx.traj_db2 = ReadDb(config=pgsql.TRAJ_DB2, debug=True, autocommit=True)

    return ctx


@dataclasses.dataclass
class StrategyFace:
    """
    策略面
    """
    id: int
    face_id: str
    geom: str
    task_id: int

    def get_info_id(self) -> str:
        """
        获取情报 id
        """
        return _get_strategy_info_id(self.id)


def _get_strategy_info_id(sid: int) -> str:
    return f"{sid}_strategy"


def _extract_strategy_id(info_id: str) -> int:
    return int(info_id.replace('_strategy', ''))


def _get_strategy_faces(ctx: Context, strategy_ids: list) -> List[StrategyFace]:
    """
    获取策略面
    """
    if len(strategy_ids) == 0:
        return []

    qry = f"select id, face_id, st_astext(geom), task_id from {ctx.strategy_tab} where id in ({join_int(strategy_ids)})"
    res = ctx.poi_db.fetchall(qry)

    if not res:
        return []
    return [StrategyFace(
        id=item[0],
        face_id=item[1],
        geom=item[2],
        task_id=item[3],
    ) for item in res]


def _gcj2bd09(wkt: str) -> str:
    """
    gcj -> bd09
    """
    url = 'http://mapde-poi.baidu-int.com/prod/api/coordsTrans'
    param = {
        'wkt': wkt,
        'from': '1',
        'to': '3',
    }
    res = requests.post(
        url, data=param,
    )
    jsn = res.json()
    if jsn['code'] != 0:
        print(jsn)
        return ''
    return jsn['data']


def _bd092gcj(wkt: str) -> str:
    url = 'http://mapde-poi.baidu-int.com/prod/api/coordsTrans'
    param = {
        'wkt': wkt,
        'from': '3',
        'to': '1',
    }
    res = requests.post(
        url, data=param,
    )
    jsn = res.json()
    if jsn['code'] != 0:
        print(jsn)
        return ''
    return jsn['data']


def _geo2map(geo: Polygon, can_not_edit=None) -> dict:
    res = {
        "type": "Feature",
        "geometry": mapping(geo),
        "properties": {
            'color': '#33FF00'
        },
    }
    if can_not_edit:
        res['properties']['can_not_edit'] = can_not_edit
    return res


def _get_admin(ctx: Context, geom: str) -> dict:
    """
    获取行政区划
    """
    qry = f"select province_ch, cityname from mesh_conf_wkt where st_intersects(wkt, st_geomfromtext('{geom}', 4326))"
    res = ctx.back_db.fetchone(qry)
    if not res:
        raise Exception(f"获取行政区划失败")
    return {
        'province_ch': res[0],
        'cityname': res[1],
    }


def _gen_source_id(info_id: str) -> str:
    return f"park_storefront_{info_id}_{uuid.uuid4().hex}"


def _extract_info_id(source_id: str) -> str:
    items = source_id.split('_')
    return f"{items[2]}_{items[3]}"


def _extract_info_id_v3(source_id: str) -> str:
    items = source_id.split('_')
    if len(items) > 4:
        return f"{items[2]}_{items[3]}_{items[4]}"
    return ''


def _extract_info_id_v2(source_id: str) -> str:
    items = source_id.split('_')
    return items[2]


def push(ctx: Context, geom: str, info_id: str, tips: str = '核实门前停车场', other_ref_geos: list = [],
         track_uids: list = [], url=None, collect_tasks: list = None):
    """
    推送图灵 核实; 返回图灵 id
    """
    wkt_bd09 = _gcj2bd09(geom)
    if wkt_bd09 == '':
        raise Exception(f"坐标转换失败")

    geo_bd09 = shapely.wkt.loads(wkt_bd09)
    ref_geos = []
    if geo_bd09.geom_type == 'MultiPolygon':
        for item in geo_bd09.geoms:
            ref_geos.append(_geo2map(item))
    else:
        ref_geos.append(_geo2map(geo_bd09))
    ref_geos += other_ref_geos

    admin = _get_admin(ctx, wkt_bd09)
    param = {
        "sourceid": f"park_storefront_{info_id}_{uuid.uuid4().hex}",
        "cityname": admin['cityname'],
        "sign": "236b3669b5f92461245863b4e71e4f1c",
        "source": 1209,
        "platform": 0,
        # "content": "核实门前停车场",
        "content": f"{tips}",
        "_signkeys": "platform,source",
        "subbusinesstype": 408,
        "provname": admin['province_ch'],
        "problem": 0,
        "occurtime": int(time.time()),
        "name": "",
        "coordsys": "bd09ll",
        "refgeo": ref_geos,
        'properties': {
            'need_giving_back': {
                'data_type': 1209,
            },
            "tasks": collect_tasks,
        },
    }
    if url is not None:
        param['url'] = url
    if track_uids is not None:
        param['properties']['track_uids'] = track_uids

    return _push_turing(ctx, param)


def push_multi_info(ctx: Context, infos: list, track_uids: list, collects: list, ref_geos: list):
    """
    一次推送多个情报
    """
    def _gen_source_id1() -> str:
        ids = [str(info['id']) for info in infos]
        return f"park_storefront_{'_'.join(ids)}_{uuid.uuid4().hex}"

    def _gen_ref_geo(info: dict) -> list:
        ref_geo = []
        wkt_bd09 = _gcj2bd09(info['geom'])
        if wkt_bd09 == '':
            raise Exception(f"坐标转换失败")

        properties = {
            'color': '#33FF00',
            'can_not_edit': 1,
            'id': info['id'],
        }
        geo_bd09 = shapely.wkt.loads(wkt_bd09)
        for item in flat_polygon(geo_bd09):
            _ref = mapping(item)
            _ref['properties'] = properties
            ref_geo.append(_ref)
        return ref_geo

    def _gen_sub_infos() -> list:
        sub_infos = []
        for _info in infos:
            sub_infos.append({
                'unique_id': str(_info['id']),
                'intelligence_conclusion': None,
                'polygon_conclusion': None,
                'ref_polygons': _gen_ref_geo(_info),
                'sub_content': '',
                'expanded': True,
            })
        return sub_infos

    admin = _get_admin(ctx, infos[0]['geom'])
    param = {
        "sourceid": _gen_source_id1(),
        "cityname": admin['cityname'],
        "sign": "85d01da79e63b2612a0e5bd185fd46a1",
        "source": 1218,
        "platform": 0,
        "content": infos[0]['tips'],
        "_signkeys": "platform,source",
        "subbusinesstype": 408,
        "provname": admin['province_ch'],
        "problem": 0,
        "occurtime": int(time.time()),
        "name": "",
        "coordsys": "bd09ll",
        "refgeo": ref_geos,
        'properties': {
            'need_giving_back': {
                'data_type': 'parking_lot_fron_door_many_pology_add',
            },
            "tasks": collects,
            "sub_info": _gen_sub_infos(),
        },
    }
    if track_uids is not None:
        param['properties']['track_uids'] = track_uids
    return _push_turing(ctx, param)


def _push_turing(ctx, param):
    print(param)
    print(ctx.url)
    # return '123'  # todo test
    try:
        res = requests.post(
            ctx.url, json=param
        )
        """
        {'errno': 0, 'errmsg': 'success', 'data': {'id': '676cc6e22d4c5da4e56c4d51', 'duplicate': 0}}
        """
        print(res.json())
        data = res.json()
        if data['errno'] == 0:
            return data['data']['id']
        return ''
    except Exception as e:
        logging.exception(e)
        return ''


def cancel(ctx: Context, turing_ids: list, source: int = 1209) -> Tuple[list, list]:
    """
    取消推送的情报
    返回成功，和失败的图灵列表
    """
    # return turing_ids, []  # todo test
    sign = '236b3669b5f92461245863b4e71e4f1c'
    if source == 1214:
        sign = '7a58af613830561c96bd1fcedc23258c'
    elif source == 1213:
        sign = 'da8ada28a286ab02fa4140028d186834'
    elif source == 1210:
        sign = 'd33d20fb851173a80b2e571fd5828668'

    param = {
        'platform': 0,
        "source": source,
        'iids': ','.join(turing_ids),
        'need_recover': 1,
        'edit_platform': 20,
        'sign': sign,
        '_signkeys': 'platform,source',
        'operator': '1146446073',
    }
    print(param)
    print(ctx.offline_url)
    # return '123'  # todo test
    try:
        res = requests.post(
            ctx.offline_url, json=param
        )
        """
        {
    "errno": 0,
    "errmsg": "",
    "data": {
        	success : ['5788480b0b4c155a1e3c9981', '578847f80b4c15c32a3c999f','578847c40b4c158e413c9952'],
			failed : ['578847900b4c150e223c9995']
        }
    }
}
        """
        print(res.json())
        data = res.json()
        if data['errno'] == 0:
            return data['data']['success'], data['data']['failed']
        return [], []
    except Exception as e:
        logging.exception(e)
        return [], []


def _get_sids(ctx: Context, batch: str, limit: int) -> list:
    """
    获取策略 id
    """
    qry = f"""
    select id from park_storefront_strategy where task_id in 
    (select task_id from park_storefront_task where batch = '{batch}' and status = 'VERIFIED') 
    and step = 'VERIFIED' 
    and st_geometrytype(geom) = 'ST_MultiPolygon' limit {limit}
    """
    res = ctx.poi_db.fetchall(qry)
    return [item[0] for item in res]


def callback(ctx: Context, turing_id: str, result: int, reason: str = ''):
    """
    回调
    """
    param = {
        "id": turing_id,
        "result_code": result,
        "reason": reason,
    }
    print(param)
    print(ctx.get_callback_url())
    # exit()
    res = requests.post(
        ctx.get_callback_url(), json=param
    )
    print(res.json())


def get_host_by_bns(host_bns: str):
    """
    获取 bns ip
    """
    data = "", 0
    ret = subprocess.run(
        "get_instance_by_service -ips {}".format(host_bns) + " | awk '{print $2,$3,$4}'",
        shell=True,
        stdout=subprocess.PIPE
    )
    code = ret.returncode
    if code == 0:
        stdout = ret.stdout
        host_result = stdout.decode('UTF-8').split("\n")
        if len(host_result) < 1:
            yield data
        for i in host_result:
            item_list = i.split(" ")
            if len(item_list) < 3:
                continue
            if item_list[2] != '0':
                # 状态不是0，实例有问题
                continue
            yield item_list[0], int(item_list[1])
    else:
        print("获取bns失败{}, 状态码{}, 返回{}".format(host_bns, code, ret.stdout))
        yield data


def _had_pushed(ctx: Context, face: StrategyFace) -> bool:
    info_id = face.get_info_id()
    return _has_pushed_by_info_id(ctx, info_id)


def _has_pushed_by_info_id(ctx, info_id):
    qry = f"select id from {ctx.pushed_tab} where info_id = '{info_id}'"
    res = ctx.poi_db.fetchone(qry)
    return len(res) > 0


def save_pushed_record(ctx: Context, face: StrategyFace, turing_id: str):
    """
    保存推送记录
    """
    qry = (f"insert into {ctx.pushed_tab} (info_id, bid, info_source, verify_id, verifier) values "
           f"('{face.get_info_id()}', '', 'stragety', '{turing_id}', 'turing')")
    ctx.poi_db.execute(qry)
    ctx.poi_db.commit_or_rollback()


def update_push_info(ctx: Context, info_id: str, turing_id: str):
    """
    更新推送信息
    """
    qry = f"""
    update {ctx.pushed_tab} 
    set verify_id = '{turing_id}', 
    pushed_times = pushed_times + 1, 
    status = 'PUSHED' 
    where info_id = '{info_id}'
    """
    ctx.poi_db.execute(qry)
    ctx.poi_db.commit_or_rollback()


def update_push_info_v2(ctx: Context, ids: list, turing_id: str):
    """
    更新推送信息
    """
    qry = f"""
    update {ctx.pushed_tab} 
    set verify_id = '{turing_id}', 
    pushed_times = pushed_times + 1, 
    status = 'PUSHED' 
    where id in ({join_int(ids)})
    """
    ctx.poi_db.execute(qry)
    ctx.poi_db.commit_or_rollback()


def had_pushed(ctx: Context, info_id: str) -> bool:
    """
    已经推送了返回 True
    """
    qry = f"""
    select id from {ctx.pushed_tab} 
    where info_id = '{info_id}' and status = 'PUSHED'
    """
    res = ctx.poi_db.fetchone(qry)
    if len(res) == 0:
        return False
    return True


def had_pushed_by_ids(ctx: Context, ids: list) -> bool:
    """ids 中有推送过，返回 True"""
    if len(ids) == 0:
        return False
    qry = f"""
    select id from {ctx.pushed_tab} 
    where id in ({join_int(ids)}) and status = 'PUSHED'
    """
    res = ctx.poi_db.fetchone(qry)
    if len(res) == 0:
        return False
    return True


def init_info_with_lock(batch: str):
    """
    初始化情报，加锁，避免重复下发
    """
    rds_locker = locker.RedisLocker(
        rds=rt.RedisTool().redis_conn,
        key=f'park:storefront:init:verify:info:{batch}',
        ex=60 * 30,
    )
    sleep_time = random.randint(1, 10)  # 睡眠调整为随机
    locker.lock_guardian_with_retry(
        locker=rds_locker, times=1, sleep_time=sleep_time,
        fun=init_info, batch=batch
    )


def _filtered_by_dist(ctx: Context, sid: int) -> bool:
    """
    建筑物距离道路策略过滤，被过滤了返回 True
    """
    where = f"id = {sid}"
    faces = get_faces_by_where(ctx, where)
    if len(faces) == 0:
        return False
    aface = faces[0]
    resp = filter_close_with_bud(ctx, aface)
    return resp.is_close


def init_info(batch: str):
    """
    初始化情报
    """
    ctx = gen_ctx('online')

    # statuses = ['INIT', '']
    statuses = [
        '',
        'INIT',
        multi_juejin.FACE_STATUS_ERR,
        multi_juejin.FACE_STATUS_DONE_INCONSISTENT,  # 交叉验证不一致，无法判断，有效门前停车场 接着推图灵
        multi_juejin.FACE_STATUS_DONE_EFFECTED,
        multi_juejin.FACE_STATUS_DONE_NO_JUDGE,
    ]
    qry = f"""
        select id 
        from park_storefront_strategy 
        where status in ({join_str(statuses)})
        and task_id in (select task_id from park_storefront_task where batch = '{batch}') and step = 'VERIFIED'
        """
    sids = ctx.poi_db.get_values(qry)
    print(f"{batch} 需要投放的情报量：{len(sids)}")

    task_batch = f"{batch}_{time.strftime('%Y%m%d%H', time.localtime(time.time()))}"

    aff = 0
    for sid in tqdm(sids):
        strategy_faces = get_faces_by_where(ctx, f" id = {sid}")
        if len(strategy_faces) != 1:
            print(f"{sid} 没有策略面？不符合预期; sid:{sid}")
            continue
        _strategy_face = strategy_faces[0]
        dup_face = filter_dup_strategy_face(ctx, _strategy_face)
        if dup_face.detail is not None:
            # 是重复的
            remark = json.dumps({
                'reason': dup_face.reason,
                'detail': dup_face.detail,
            }, ensure_ascii=False)
            qry = f"""
            update {ctx.strategy_tab} set result = result || '{handle_apostrophe(remark)}', status = 'DUPLICATE_CATE' 
            where id = {sid}
            """
            ctx.poi_db.execute(qry)
        else:
            info_id = _get_strategy_info_id(int(sid))
            if _has_pushed_by_info_id(ctx, info_id):
                print(f"{sid} 已推送")
                info_id = str(uuid.uuid4().hex)
                # continue

            try:
                filtered = _filtered_by_dist(ctx, sid)
            except Exception as e:
                logging.exception(e)
                filtered = False
            if filtered:
                status = 'CANCEL'
                cancel_memo = '建筑物距离道路过近，被过滤'
            else:
                status = 'INIT'
                cancel_memo = ''
            faces = _get_strategy_faces(ctx, [sid])
            aface = faces[0]
            admin = _get_admin(ctx, aface.geom)
            qry = f"""
            insert into {ctx.pushed_tab} 
            (info_id, info_source, verifier, verify_id, batch, remark, geom, prev_info_id, city, 
            task_id, status, cancel_memo, data_type) 
            values('{info_id}', 'strategy', 'turing', '', '{task_batch}', '', 
            st_geomfromtext('{aface.geom}', 4326), '{sid}', '{admin['cityname']}', {aface.task_id}, 
            '{status}', '{cancel_memo}', '1218')
            """
            aff += ctx.poi_db.execute(qry)

            qry = f"""
                   update park_storefront_strategy set status = 'PUSHED' where id = {sid}
                   """
            ctx.poi_db.execute(qry)
        ctx.poi_db.commit_or_rollback()
    print(f"{batch} 已初始化完成：{aff}")


def _get_need_push_turing_icafe_cards() -> List[icafe.ICafeCard]:
    """
    获取需要推送图灵的卡片
    """
    statuses = [
        icafe.CARD_STATUS_PULL_PUBLISH_READY,
    ]
    return icafe.fetch_issues(icafe.CARD_TYPE_PULL, statuses)


def init_info_by_icafe():
    """
    监控 icafe 的状态来初始化情报
    """
    cards = _get_need_push_turing_icafe_cards()
    for card in tqdm(cards):
        batch = card.batch
        init_info_with_lock(batch)
    print(f"处理了：{len(cards)} 卡片")


def online_verified_again(bids: list, batch: str, tips='再次核实', new: bool = True):
    """
    线上的数据再一次核实
    """
    ctx = gen_ctx('online')

    aff = 0
    for a_bid in bids:
        qry = f"""
        select bid, st_astext(area) from parking where bid = '{a_bid}'
        """
        res = ctx.back_db.fetchone(qry)
        if not res:
            print(f"{a_bid} 不存在")
            continue
        bid = ''
        if new:
            bid = a_bid

        info_id = str(uuid.uuid4().hex)
        admin = _get_admin(ctx, res[1])
        qry = f"""
                  insert into {ctx.pushed_tab} (info_id, info_source, verifier, verify_id, batch, remark, geom, prev_info_id, city, tips, bid) 
                  values('{info_id}', 'online', 'turing', '', '{batch}', '', 
                  st_geomfromtext('{res[1]}', 4326), '{a_bid}', '{admin['cityname']}', '{handle_apostrophe(tips)[:250]}', '{bid}')
               """
        aff += ctx.poi_db.execute(qry)
    ctx.poi_db.commit_or_rollback()
    print(aff)


def _online_verified_again_0121():
    bids = []
    batch = 'online_verified_again_0122_02'
    online_verified_again(bids, batch, tips='面核实，风险挖掘，压盖步行路', new=False)


def _online_verified_again_by_file(file, batch, reason: str = ''):
    if reason == '':
        reason = '再次核实门前停车场'
    with open(file, 'r') as hdr:
        reader = csv.reader(hdr)
        for row in reader:
            if len(row) == 1:
                bid = row[0]
            elif len(row) == 2:
                bid, reason = row
            bid = str(bid).strip()
            online_verified_again([bid], batch, reason, True)


def _get_ref_pois_bd09(geom: str) -> list:
    """
    获取参考 pois，坐标系 bd09
    """
    ref_geos = []
    icon_url = 'https://turing-front-static.cdn.bcebos.com/static/image/mapIcons/poiPurple.png'
    # icon_url = 'https://turing-front-static.cdn.bcebos.com/static/image/mapIcons/poiTheme.png'  测试的
    points = [_poi.point for _poi in auto_cpt.get_relation_store_pois(geom, 50 * 1e-5)]
    # points = cluster_points_by_distance(points, 10 * 1e-5)

    for _poi in points:
        wkt_bd09 = _gcj2bd09(_poi.wkt)
        _geo_map = _geo2map(shapely.wkt.loads(wkt_bd09))
        _geo_map['properties']['iconUrl'] = icon_url
        ref_geos.append(_geo_map)
    return ref_geos


def batch_is_pushed(ctx: Context, batch: str) -> bool:
    """
    推送完成了返回 True
    """
    qry = f"""
    select count(*) from {ctx.pushed_tab} 
    where batch = '{batch}' and status in ('INIT', 'MAINTAINED')
    """
    res = ctx.poi_db.fetchone(qry)
    if res[0] == 0:
        return True
    return False


def maintain_card_status_to_done(ctx: Context, batch: str):
    """
    维护卡片状态为已完成
    """
    pushed = batch_is_pushed(ctx, batch)
    cards = _get_need_push_turing_icafe_cards()
    for card in tqdm(cards):
        if card.batch not in batch:
            continue
        icafe.modify_issue_status(card.sequence, icafe.CARD_STATUS_PULL_PUBLISH_DONE)
        _send_hi_if_pushed(card, pushed)


def _send_hi_if_pushed(card, pushed):
    msg1 = f"推送成功：{pushed} \n【{icafe.CARD_STATUS_PULL_PUBLISH_DONE}】\n- 卡片链接："
    card_url = f"https://console.cloud.baidu-int.com/devops/icafe/issue/storefront-juejin-{card.sequence}/show"
    msg2 = f"\n- 任务批次：{card.batch}\n- 掘金批次：{card.batch_image}\n"
    ruliu.send(
        [
            {"type": "TEXT", "content": msg1},
            {"type": "LINK", "href": card_url},
            {"type": "TEXT", "content": msg2},
            {"type": "AT", "atuserids": ["zhengcheng_cd", "mayuqing_cd", "yanghuiyu_cd"]},
        ]
    )


def _push_auto(batch_info: tuple):
    batch, num, status = batch_info
    push_info_with_lock(batch, num, status)
    maintain_card_status_to_done(gen_ctx('online'), batch)


def push_auto():
    """
    自动推送图灵
    """
    status = 'MAINTAINED'
    ctx = gen_ctx('online')
    qry = f"""
    select batch, count(*), status from {ctx.pushed_tab} 
    where status = '{status}' and batch not like '%test%' group by batch, status
    """
    res = ctx.poi_db.fetchall(qry)

    for item in tqdm(res):
        batch, num, status = item
        # push_info_with_lock(batch, num, status)
        push_multi_info_with_lock(batch, num, status)
        maintain_card_status_to_done(gen_ctx('online'), batch)
    print("over")


def push_info_with_lock(batch: str, limit: int, status):
    """
    推送情报，加锁
    """
    rds_locker = locker.RedisLocker(
        rds=rt.RedisTool().redis_conn,
        key=f'park:storefront:push:verify:info:{batch}',
        ex=60 * 30,
    )
    sleep_time = random.randint(1, 10)  # 睡眠调整为随机
    locker.lock_guardian_with_retry(
        locker=rds_locker, times=1, sleep_time=sleep_time,
        fun=push_info, batch=batch, limit=limit, status=status,
    )


def push_multi_info_with_lock(batch: str, limit: int, status):
    """
    推送情报，加锁
    """
    rds_locker = locker.RedisLocker(
        rds=rt.RedisTool().redis_conn,
        key=f'park:storefront:push:multi:verify:info:{batch}',
        ex=60 * 30,
    )
    sleep_time = random.randint(1, 10)  # 睡眠调整为随机
    locker.lock_guardian_with_retry(
        locker=rds_locker, times=1, sleep_time=sleep_time,
        fun=push_multi, batch=batch, limit=limit, status=status,
    )


def push_info(batch: str, limit: int, status='MAINTAINED'):
    """
    推送信息
    """
    ctx = gen_ctx('online', autocommit=True)
    if 'test' in batch:
        ctx = gen_ctx('test', autocommit=True)

    qry = f"""
        select info_id, st_astext(geom), tips, track_ids, track_url, prev_info_id  
        from {ctx.pushed_tab}
        where batch = '{batch}' and data_type = '1209'
        and status = '{status}' and not st_isempty(geom) limit {limit}
        """
    res = ctx.poi_db.fetchall(qry)

    # tips = '面不准确'
    # tips = '核实门前停车场'
    for item in tqdm(res):
        ref_geos = _get_ref_pois_bd09(item[1])
        track_uids = item[3]
        track_url = item[4]
        prev_info_id = item[5]
        geom = item[1]

        if had_pushed(ctx, item[0]):
            print(f"{item[0]} 已经推送过了，不再推送")
            continue

        if track_url == '':
            print(f"{item[0]} 轨迹资料没有准备好，先不推")
            continue

        collect_tasks = _get_collect_data_info(ctx, prev_info_id, geom)
        if collect_tasks is None or len(collect_tasks) < 1:
            print(f"{item[3]} 没有采集or历史数据")
        else:
            print(f"{item[3]} 有采集数据")

        pushed_res = push(ctx, geom=item[1], info_id=item[0], tips=item[2], other_ref_geos=ref_geos,
                          track_uids=track_uids, url=track_url, collect_tasks=collect_tasks)
        if pushed_res == '':
            print(f"{item[0]} 情报推送失败")
            continue
        update_push_info(ctx, item[0], pushed_res)


def _get_callback_info(task_id):
    beeflow_connection = _create_beeflow_connection()
    sql = f"select task_id, content from collect_task_callback_records where task_id='{task_id}' group by task_id"
    with beeflow_connection.cursor() as cursor:
        cursor.execute(sql)
        res = cursor.fetchone()
        return res


def _get_callback_count(task_ids):
    beeflow_connection = _create_beeflow_connection()
    sql = f"select count(distinct task_id) from collect_task_callback_records where task_id in ({task_ids})"
    with beeflow_connection.cursor() as cursor:
        cursor.execute(sql)
        res = cursor.fetchone()
        return int(res[0])


def _create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    return pymysql.connect(host='*************', port=int(5730), user='bee_flow',
                           password='nl4c/mqeTcsgpH', db='bee_flow', charset="utf8mb4")


def get_panorama_images(wkt: str) -> list:
    """
    获取范围内的全景图片
    """
    return _get_verified_boundary_panorama_images(area=wkt)


def _get_verified_boundary_panorama_images(area: str) -> list:
    """
    获取核实面周围的全景图片
    {
        "bos_key": "20250518jd1155455",
        "cdid": "20250518jd",
        "create_time": "2025-05-23 19:45:48",
        "has_pic": true,
        "hell": 54.37,
        "id": "20250518jd1155455",
        "imp_ts": 1748000748,
        "is_ld": 0,
        "link_id": "",
        "meshid": 626352,
        "north": 29.6493,
        "pic_url": "?appkey=t_aoi_park&t=1748524716917&sign=1d9c03c1e6795e32626119861fd6e233",
        "radius": 0,
        "speed": 5.68,
        "thumb_url": "appkey=t_aoi_park&t=1748524716917&sign=1d9c03c1e6795e32626119861fd6e233",
        "time": "2025-05-18 11:55:45",
        "track_id": "20250518jd20250518jd1155455",
        "x": 123.36064756,
        "y": 41.7916806
    }
    """
    bounds = boundary.from_wkt(area, buffer=50 * 1e-5)
    images = []
    cd_num = 2
    times = cd_num  # 最多获取量车天的
    for i in range(1, 36 + 1):
        tracks = list(track_point.get_track_points(bounds, track_point.one_month(i), track_point.SRC_QUANJING))
        tracks = [x for x in tracks if x["has_pic"]]
        if len(tracks) == 0:
            continue
        images += tracks

        cdids = set([x['cdid'] for x in tracks])
        times -= len(cdids)
        if times <= 0:
            break

    images = _filter_viaduct(images)
    return _format_panorama_images(_extract_images(images, cd_num))


def _filter_viaduct(images: list) -> list:
    """
    过滤掉高架桥
    """
    resp = []
    for img in images:
        if is_viaduct(img.get('link_id')):
            print(f"{img['id']} 在高架桥上，被过滤")
            continue
        resp.append(img)
    return resp


def _extract_images(images: list, cd_num: int) -> list:
    cds = list(set([x['cdid'][:8] for x in images]))
    cds.sort(reverse=True)
    cds = cds[:cd_num]

    imgs = []
    for i in images:
        if i['cdid'][:8] in cds:
            imgs.append(i)
    return imgs


def _format_panorama_images(images: list) -> list:
    """
    格式化全景图片
    """
    if len(images) == 0:
        return []
    with multiprocessing.Pool(processes=10) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        formatted = pool.map(_formatted_panorama_image, images)
    # formatted = []
    # for img in images:
    #     formatted_img = _formatted_panorama_image(img)
    #     formatted.append(formatted_img)
    return formatted


def _formatted_panorama_image(img):
    timestamp = int(datetime.strptime(img['time'], "%Y-%m-%d %H:%M:%S").timestamp())
    point_bd09 = coord_trans.gcj02_to_bd09_wkt(f"POINT({img['x']} {img['y']})")
    point_bd09_geo = shapely.wkt.loads(point_bd09)
    bd09_x, bd09_y = point_bd09_geo.x, point_bd09_geo.y
    pic_url = f"http://m.map.baidu.com:8006/{img['cdid']}/{img['id']}.jpg"
    thumb_url = _gen_thumb_img_url(pic_url, img['id'], size=(2048 * 2, 2048 * 2))
    formatted_img = {
        "north": img['north'],
        "cdid": img['cdid'],
        "time": timestamp,  # timestamp
        "x": bd09_x,  # 坐标经度（BD09）
        "y": bd09_y,  # 坐标经度（BD09）
        # "pic_url": thumb_url,
        "pic_url": pic_url,
        "thumb_url": thumb_url,
        "selftag": 'panorama',
        "source": 'quanjing',
        "id": img['track_id'],
    }
    return formatted_img


def _get_prime_verified_images(ctx: Context, verified_sid: int, area: str) -> list:
    """
    获取原始面核实的图片
    """
    # return []
    qry = f"""
    SELECT b.id, b.result 
FROM park_storefront_strategy a
JOIN park_storefront_strategy b ON b.face_id = ANY(a.prev_face_ids)
WHERE a.id = {verified_sid};
    """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        print(f"{verified_sid} 没有核实面; 获取原始面核实的图片")
        return []

    result = res[1]
    segment_ids = result['segment_ids'] if 'segment_ids' in result else []
    if len(segment_ids) == 0:
        print(f"{verified_sid} 核实面中没有 segment_ids 信息; 获取原始面核实的图片")
        return []

    buffer_geo = shapely.wkt.loads(area).buffer(50 * 1e-5)
    qry = f"""
    select image_type, track_id, track_time, track_north, st_astext(track_point), image_name, batch 
    from park_storefront_image 
    where segment_id in ({join_str(segment_ids)}) 
    and st_intersects(track_point, st_geomfromtext('{buffer_geo.wkt}', 4326))
    """
    infos = ctx.poi_db.fetchall(qry)
    image_infos = []
    for i in infos:
        image_info = {
            'image_type': i[0],
            'track_id': i[1],
            'track_time': i[2],
            'track_north': i[3],
            'track_point': i[4],
            'image_name': i[5],
            'batch': i[6],
        }
        if image_info['image_type'] != 'panorama':
            continue
        image_infos.append(_format_verified_image(image_info))
    return image_infos


def _gen_thumb_img(img_url: str, thumb_img_dir: str, size: tuple) -> str:
    """
    根据图片链接生成缩略图
    """
    response = requests.get(img_url)
    response.raise_for_status()  # 检查请求是否成功

    output_path = os.path.join(thumb_img_dir, f"{uuid.uuid4().hex}.jpg")
    image = PImage.open(BytesIO(response.content))
    if size:
        image.thumbnail(size)
    image.save(output_path)
    return output_path


def _gen_thumb_img_with_retry(img_url: str, thumb_img_dir: str, size: tuple, retry_times: int = 3) -> str:
    if retry_times < 0:
        return ''
    try:
        img_path = _gen_thumb_img(img_url, thumb_img_dir, size)
        if os.path.exists(img_path):
            return img_path
        retry_times -= 1
    except Exception as e:
        logging.exception(e)
        retry_times -= 1
        time.sleep(1)
    return _gen_thumb_img_with_retry(img_url, thumb_img_dir, size, retry_times)


def _gen_thumb_img_url(img_url, img_id: str, size=(2048 * 2, 2048 * 2)) -> str:
    """
    生成缩略图 图片链接
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    thumb_img_dir = ensure_dir(os.path.join(current_dir, '/tmp'))
    thumb_img_path = _gen_thumb_img_with_retry(img_url, thumb_img_dir, size)
    new_thumb_img = f'park_storefront/thumb/{img_id}.jpg'
    thumb_img_url = upload_pic(thumb_img_path, new_thumb_img)
    os.remove(thumb_img_path)
    return thumb_img_url


def _format_verified_image(image_info: dict) -> dict:
    """
    格式化掘金核实图片
    """
    timestamp = int(image_info['track_time'].timestamp())
    point_bd09 = coord_trans.gcj02_to_bd09_wkt(image_info['track_point'])
    point_bd09_geo = shapely.wkt.loads(point_bd09)
    bd09_x, bd09_y = point_bd09_geo.x, point_bd09_geo.y
    if image_info['image_type'] == 'panorama':
        cdid = str(image_info['track_id'])[:10]
        pic_url = f"http://m.map.baidu.com:8006/{cdid}/{image_info['track_id']}.jpg"
        pic_url = _gen_thumb_img_url(pic_url, image_info['track_id'])
        return {
            "north": image_info['track_north'],
            "cdid": cdid,
            "time": timestamp,  # timestamp
            "x": bd09_x,  # 坐标经度（BD09）
            "y": bd09_y,  # 坐标经度（BD09）
            "pic_url": pic_url,
            "selftag": image_info['image_type'],
            "source": 'quanjing',
            "id": image_info['track_id'],
            # "bos_key": bos_key, -- 下游卡问题，要求暂时不要传
        }
    else:
        image = Image(
            image_id=image_info['image_name'],
            batch=image_info['batch'],
            conclusion=0,
        )
        print(f"有众源图片")
        # pic_url = f"http://debase.map.baidu-int.com/bos/{image_info['track_id']}"
        pic_url = image.url
        return {
            "north": image_info['track_north'],
            "cdid": 'unknown',
            "time": timestamp,  # timestamp
            "x": bd09_x,  # 坐标经度（BD09）
            "y": bd09_y,  # 坐标经度（BD09）
            "pic_url": pic_url,
            "selftag": image_info['image_type'],
            "source": 'newest',
            # "bos_key": bos_key, -- 下游卡问题，要求暂时不要传
        }


def _get_collect_data_info(ctx: Context, info_id: str, area: str):
    try:
        result = []
        grouped_pics = defaultdict(list)
        sql = f"select data from parking_collect_data_history where source_id = '{info_id}'"
        history = ctx.poi_db.fetchall(sql)
        if len(history) > 0:
            for item in history:
                for pic in item[0]:
                    cdid = pic['cdid']
                    point_bd09 = coord_trans.gcj02_to_bd09_wkt(f"POINT({pic['x']} {pic['y']})")
                    point_bd09_geo = shapely.wkt.loads(point_bd09)
                    x, y = point_bd09_geo.x, point_bd09_geo.y
                    bos_key = pic['bos_key']
                    pic_url = f"http://m.map.baidu.com:8011/{bos_key}"
                    one_pic = {
                        "north": pic['north'],
                        "cdid": cdid,
                        "time": pic['time'] // 1000,  # timestamp
                        "x": x,  # 坐标经度（BD09）
                        "y": y,  # 坐标经度（BD09）
                        "pic_url": pic_url,
                        "selftag": "history",
                        # "bos_key": bos_key, -- 下游卡问题，要求暂时不要传
                    }
                    grouped_pics[cdid].append(one_pic)

        # if ctx.is_online():
        #     verified_images = _get_prime_verified_images(ctx, int(info_id), area)
        # else:
        print(f"_get_verified_boundary_panorama_images: {info_id}")
        verified_images = _get_verified_boundary_panorama_images(area)

        for i in verified_images:
            grouped_pics[i['cdid']].append(i)

        for cdid, pics in grouped_pics.items():
            pics.sort(key=lambda x: x['time'])

            collected_pics = [p for p in pics if p['selftag'] == 'collected']
            history_pics = [p for p in pics if p['selftag'] == 'history']
            verified_pics = [p for p in pics if p['selftag'] in ['panorama', 'zhongyuan']]

            sampled_collected = []
            if len(collected_pics) > 0:
                points = [(coord['my_x'], coord['my_y']) for coord in collected_pics]
                lines = LineString(points)
                geom = shapely.wkt.loads(area)
                if not geom.intersects(lines):
                    logging.warning(f"{info_id} 不在区域内, {geom}, {lines}")
                    continue

                sampled_collected.append(collected_pics[0])
                if len(collected_pics) > 1:
                    sampled_collected = _sample_pics_by_time_and_distance(collected_pics)

            combined_pics = sampled_collected + history_pics + verified_pics
            combined_pics.sort(key=lambda x: x['time'])

            result.append({
                "pics": combined_pics
            })
        return result
    except Exception as e:
        logging.exception(e)
        print("Error occurred while fetching data:", e)
        return []


def _haversine(lon1, lat1, lon2, lat2):
    """
    使用 haversine 公式计算地球上两点之间的距离（单位：米）。

    参数:
        lon1 (float): 第一个点的经度。
        lat1 (float): 第一个点的纬度。
        lon2 (float): 第二个点的经度。
        lat2 (float): 第二个点的纬度。

    返回:
        float: 两点之间的距离（单位：米）。
    """
    # 地球半径（单位：米）
    R = 6371000
    # 将经纬度转换为弧度
    lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])
    # 计算差值
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    # haversine 公式
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    # 计算距离
    distance = R * c
    return distance


def _sample_pics_by_time_and_distance(collected_pics, time_interval=2, distance_threshold=5):
    if not collected_pics:
        return []
    # 先按时间排序
    collected_pics.sort(key=lambda x: x['time'])
    # 步骤1：按时间2秒筛选
    time_filtered = []
    last_time = None
    for pic in collected_pics:
        if last_time is None or pic['time'] - last_time >= time_interval:
            time_filtered.append(pic)
            last_time = pic['time']
    # 步骤2：再按距离筛选
    distance_filtered = []
    last_pic = None
    for pic in time_filtered:
        if last_pic is None:
            distance_filtered.append(pic)
            last_pic = pic
        else:
            dist = _haversine(last_pic['my_x'], last_pic['my_y'], pic['my_x'], pic['my_y'])
            if dist >= distance_threshold:
                distance_filtered.append(pic)
                last_pic = pic

    return distance_filtered


def _get_pushed_info(ctx: Context, turing_id: str):
    """
    获取推送的情报
    """
    qry = f"""
    select id, info_id, bid, status from {ctx.pushed_tab} where verify_id = '{turing_id}'
    """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        return {}
    return {
        'id': res[0],
        'info_id': res[1],
        'bid': res[2],
        'status': res[3],
    }


def _get_pushed_infos(ctx: Context, turing_id: str) -> list:
    """
    获取推送的情报
    """
    qry = f"""
    select id, info_id, bid, status from {ctx.pushed_tab} 
    where verify_id = '{turing_id}' and status = 'PUSHED' 
    and batch not like '%test%'
    """
    res = ctx.poi_db.fetchall(qry)
    if not res:
        return []
    return [{
        'id': i[0],
        'info_id': i[1],
        'bid': i[2],
        'status': i[3],
    } for i in res]


def _get_pushed_info_by_info_id(ctx: Context, info_id: str):
    qry = f"""
        select id, info_id, bid, status from {ctx.pushed_tab} where info_id = '{info_id}'
        """
    res = ctx.poi_db.fetchone(qry)
    if not res:
        return {}
    return {
        'id': res[0],
        'info_id': res[1],
        'bid': res[2],
        'status': res[3],
    }


def gen_ctx_by_batch(batch: str, data_type: str = '1209', autocommit=False) -> Context:
    """根据批次号获取上下文"""
    if 'test' in batch:
        return gen_ctx('test', data_type, autocommit)
    return gen_ctx('online', data_type, autocommit)


def _get_infos(ctx: Context, batch: str, status: str) -> list:
    qry = f"""
                select id, info_id, st_astext(geom), tips, track_ids, track_url, prev_info_id, task_id  
                from {ctx.pushed_tab} 
                where batch = '{batch}' and data_type = '1218'
                and status = '{status}' and not st_isempty(geom)
                """
    res = ctx.poi_db.fetchall(qry)
    return [{
        'id': item[0],
        'info_id': item[1],
        'geom': item[2],
        'tips': item[3],
        'track_ids': item[4],
        'track_url': item[5],
        'prev_info_id': item[6],
        'task_id': item[7],
    } for item in res]


def _merge_infos(ctx: Context, infos: list) -> List[list]:
    """
    把需要合并的情报整合在一起
    """
    sid2info = {str(i['prev_info_id']): i for i in infos}
    assert len(sid2info) == len(infos), f"情报中 sid 不唯一"

    sids = list(sid2info.keys())
    if len(sids) == 1:
        return [infos]

    before = get_faces_by_where(ctx, f" id in ({join_int(sids)})")
    merged = merger.merge_faces(ctx, before)

    result = []
    all_sids = []
    for mg in merged:
        merged_sids = mg.sid.split('_')
        merged_info = []
        merged_geos = []
        for m_sid in merged_sids:
            assert m_sid not in all_sids, f"{m_sid} 重复合并了"
            all_sids.append(m_sid)

            merged_info.append(sid2info[m_sid])
            merged_geos.append(shapely.wkt.loads(sid2info[m_sid]['geom']))

        merged_ugeo = shapely.ops.unary_union(merged_geos)
        merged_iou = calc_iou(merged_ugeo, mg.geo)
        assert (merged_ugeo.equals(mg.geo) or merged_iou > 0.999), f"合并后的情报范围应该一样"

        result.append(merged_info)

    assert len(all_sids) == len(sids), f"合并后情报面有丢失"
    return result


def _group_infos_by_task_id(infos: list) -> dict:
    tid2infos = {}
    for i in infos:
        tid2infos.setdefault(i['task_id'], []).append(i)
    return tid2infos


def _get_multi_collect_tasks(ctx: Context, infos: list) -> list:
    collect_tasks = []
    for info in infos:
        collect_tasks += _get_collect_data_info(ctx, info['prev_info_id'], info['geom'])
    return collect_tasks


def _get_multi_track_uids(infos: list) -> list:
    track_uids = []
    for info in infos:
        track_uids += info['track_ids']
    return track_uids[:150]


def _get_multi_ref_pois(infos: list) -> list:
    ref_pois = []
    for info in infos:
        ref_pois += _get_ref_pois_bd09(info['geom'])
    return ref_pois


def push_multi(batch: str, limit: int, status: str):
    """
    推送多个情报
    """
    ctx = gen_ctx_by_batch(batch, autocommit=True)
    infos = _get_infos(ctx, batch, status)
    tid2infos = _group_infos_by_task_id(infos)

    pushed_num = 0
    for _infos in tqdm(tid2infos.values()):
        if pushed_num >= limit:
            break
        info_groups = _merge_infos(ctx, _infos)
        print(f"同一街区，分成了：{len(info_groups)} 组")
        for _group in info_groups:
            if pushed_num >= limit:
                break
            pushed_num += _push_multi_info(ctx, _group)
    print(f"{batch} 推送了：{pushed_num} 情报")


def _push_multi_info(ctx: Context, infos: list):
    collect_tasks = _get_multi_collect_tasks(ctx, infos)
    track_uids = _get_multi_track_uids(infos)
    ref_pois = _get_multi_ref_pois(infos)
    ids = [i['id'] for i in infos]

    if had_pushed_by_ids(ctx, ids):
        print(f"{ids} 已经推送过了")
        return 0

    pushed_res = push_multi_info(ctx, infos, track_uids, collect_tasks, ref_pois)
    if pushed_res == '':
        print(f"{ids} 情报推送失败")
        return 0
    update_push_info_v2(ctx, ids, pushed_res)
    return 1


def consume_turing_result(ctx: Context):
    """
    消费图灵结果
    """
    qry = f"""
    select turing_id, bp_message, id from parking_turing_result where data_type = '1209' and status = 'INIT'
    """
    res = ctx.poi_db.fetchall(qry)

    for item in tqdm(res):
        turing_id = item[0]
        # if turing_id != '676ccade2d4c5da3090ab579':
        #     continue

        print(turing_id)
        _id = item[2]

        bp_message = json.loads(item[1])
        try:
            data_commit = json.loads(bp_message['data'])
        except Exception as e:
            logging.exception(e)
            continue

        source_id = data_commit['sourceid']
        edit_commit = data_commit['edit_commit']
        pushed_info = _get_pushed_info(ctx, turing_id)
        if len(pushed_info) == 0 and 'edit_content' in edit_commit and edit_commit['edit_content'] != '':
            pushed_info = _get_pushed_info(ctx, edit_commit['edit_content'])
        if len(pushed_info) == 0:
            pushed_info = _get_pushed_info_by_info_id(ctx, _extract_info_id(source_id))
        if len(pushed_info) == 0:
            pushed_info = _get_pushed_info_by_info_id(ctx, _extract_info_id_v3(source_id))
        if len(pushed_info) == 0:
            origin_turing_id = ''
            if ('original_info' in data_commit
                    and 'properties' in data_commit['original_info']
                    and 'original_iid' in data_commit['original_info']['properties']):
                origin_turing_id = data_commit['original_info']['properties']['original_iid']
            if origin_turing_id != '':
                pushed_info = _get_pushed_info(ctx, origin_turing_id)
        if len(pushed_info) == 0:
            pushed_info = _get_pushed_info_by_info_id(ctx, _extract_info_id_v2(source_id))

        if len(pushed_info) == 0:
            raise Exception(f"{turing_id} 找不到推送的情报")

        try:
            geom = "POLYGON EMPTY"
            geos = []
            if 'geo' in edit_commit:
                for _item_geo in edit_commit['geo']:
                    geos.append(shape(_item_geo['geometry']))
                    # geos.append(shape(_item_geo['geometry']).buffer(0.00))
            if len(geos) > 0:
                ugeo = shapely.ops.unary_union(geos)
                geom = _bd092gcj(ugeo.wkt)
        except Exception as e:
            logging.exception(e)
            continue

        sub_result = edit_commit['sub_result']
        if sub_result == 1:
            recall = edit_commit['intelligence_conclusion']
            precise = edit_commit['polygon_conclusion']
        else:
            recall = 0
            precise = 0

        bid = ''
        if pushed_info['bid'] != '' and pushed_info['status'] == 'PUSHED':
            bid = pushed_info['bid']

        queries = [
            f"""
            update parking_turing_result set status = 'DONE' where id = {_id}
            """,
            f"""
            update {ctx.pushed_tab} 
            set status = 'CALLBACKED', result_num = result_num + 1 where id = {pushed_info['id']}
            """,
            f"""
            insert into {ctx.turing_result_tab} (info_id, turing_id, recall, precise, geom, sub_result, bid) values 
            ('{pushed_info['info_id']}', '{turing_id}', '{recall}', '{precise}', 
            st_geomfromtext('{geom}', 4326), '{sub_result}', '{bid}')
            """,
        ]

        # callback(ctx, turing_id, 1)

        for qry in queries:
            ctx.poi_db.execute(qry)
        ctx.poi_db.commit_or_rollback()


def callback_multi(ctx: Context):
    """
    多面产线回传
    """
    qry = f"""
        select turing_id, bp_message, id 
        from parking_turing_result 
        where data_type = '1218' 
        and status = 'INIT' 
--         and turing_id = '685a59fdebe43d27dc44f6f7' 
        """
    messages = ctx.poi_db.fetchall(qry)

    for item in tqdm(messages):
        turing_id = item[0]
        msg_id = item[2]

        pushed_infos = _get_pushed_infos(ctx, turing_id)
        if len(pushed_infos) == 0:
            # raise Exception()
            print(f"{turing_id} 找不到推送的情报")
            continue

        try:
            bp_message = json.loads(item[1])
            data_commit = json.loads(bp_message['data'])
        except Exception as e:
            logging.exception(e)
            continue
        id2pushinfo = {str(info['id']): info for info in pushed_infos}
        edit_commit = data_commit['edit_commit']
        sub_result = edit_commit['sub_result']
        for sub_info in edit_commit['sub_info']:
            if sub_result == 1:
                recall = sub_info['intelligence_conclusion']
                precise = sub_info['polygon_conclusion']
            else:
                recall = 0
                precise = 0
            unique_id = sub_info['unique_id']
            work_wkt = _extract_work_wkt(sub_info)
            info_id = id2pushinfo.get(unique_id, {}).get('info_id', '')
            queries = [
                f"""
                insert into {ctx.turing_result_tab} 
                (info_id, turing_id, recall, precise, geom, sub_result, data_type) values 
                ('{info_id}', '{turing_id}', '{recall}', '{precise}', 
                st_geomfromtext('{work_wkt}', 4326), '{sub_result}', '1218')
                """,
            ]
            if unique_id:
                queries.append(
                    f"""
                update {ctx.pushed_tab} 
                set status = 'CALLBACKED', result_num = result_num + 1 where id = {unique_id} and status = 'PUSHED'
                """,
                )
            for qry in queries:
                ctx.poi_db.execute(qry)

        qry = f"""
        update parking_turing_result set status = 'DONE' where id = {msg_id}
        """
        ctx.poi_db.execute(qry)
        ctx.poi_db.commit_or_rollback()
        # callback(ctx, turing_id, 1)
    print('over')


def _extract_work_wkt(item: dict) -> str:
    """
    提取作业范围
    """
    geom = "POLYGON EMPTY"
    try:
        geos = []
        if 'polygons' in item:
            for _item_geo in item['polygons']:
                geos.append(shape(_item_geo))
                # geos.append(shape(_item_geo['geometry']).buffer(0.00))
        if len(geos) > 0:
            ugeo = shapely.ops.unary_union(geos)
            geom = _bd092gcj(ugeo.wkt)
    except Exception as e:
        logging.exception(e)
    return geom


def _cancel_by_turing_ids(ctx, turing_ids, source: int = 1209):
    turing_chunks = array_chunk(turing_ids, 99)
    print(f'需要取消的量级:{len(turing_ids)}; 分{len(turing_chunks)}次取消')
    # exit()
    aff = 0
    for a_chunk in turing_chunks:
        success, failed = cancel(ctx, a_chunk, source)
        print(f"取消成功的量级：{len(success)}; 取消失败:{json.dumps(failed)}")

        if len(success) > 0:
            qry = f"""
            update park_storefront_verify_pushed 
            set status = 'CANCEL', remark = remark || ',已取消'
            where verify_id in ({join_str(success)})
            """
            aff += ctx.poi_db.execute(qry)
            ctx.poi_db.commit_or_rollback()
        print(f"总共取消了：{aff}")
    print('over')


def maintain_track_info(batch: str, used_storefront_track=True):
    """
    维护轨迹信息
    """
    is_test = 'test' in batch

    def _gen_ctx() -> Context:
        if is_test:
            _ctx = gen_ctx(env='test', autocommit=True)
        else:
            _ctx = gen_ctx(env='online', autocommit=True)
        return _ctx

    ctx = _gen_ctx()

    qry = f"""
    select id, st_astext(geom), prev_info_id, track_times from {ctx.pushed_tab} 
    where batch = '{batch}' and status = 'INIT' and data_type in ('1209', '1218')
    """
    res = ctx.poi_db.fetchall(qry)

    # 有可能照片会生成失败，但轨迹我们已经生成了，那么图片 url 就填充空白
    blank_url = 'https://www.baidu.com'

    for item in tqdm(res):
        pk_id = item[0]
        _geom = item[1]
        sid = item[2]
        track_times = item[3]

        try:
            # 时间会有点长，可能会连接超时
            if is_test:
                uuids, track_url = traj_processor.get_pre_verify_track_ids_and_img_url(ctx, _geom,
                                                                                       used_storefront_track)
            else:
                uuids, track_url = traj_processor.get_online_verify_track_ids_and_img_url(ctx, _geom,
                                                                                          used_storefront_track)
            if len(uuids) > 50:
                print(f"{sid} 轨迹分组不符合预期")
                continue
            if track_url == '' and len(uuids) == 0:
                print(f"{sid} 轨迹资料准备失败")
                status = 'INIT'
            else:
                status = 'MAINTAINED'

            if len(uuids) > 0 and track_url == '':
                track_url = blank_url

            track_times += 1
            if track_times >= 3 and status == 'INIT':
                status = 'MAINTAINED'
                track_url = blank_url  # 空链接，标记已经生成过轨迹了，只不过是空的轨迹资料

            qry = f"""
            update {ctx.pushed_tab} set track_ids = ARRAY[{join_str(uuids)}]::text[], 
            status = '{status}', 
            track_times = {track_times}, 
            track_url = '{track_url}'  
            where id = {pk_id}
            """
            ctx.poi_db.execute(qry)
        except Exception as e:
            logging.exception(e)
            print(f"{pk_id} 维护轨迹 id 失败")
            ctx = _gen_ctx()
        # ctx.poi_db.commit_or_rollback()
    print('over')


def _maintain_auto(batch_info: tuple):
    batch, city, num = batch_info

    ctx = gen_ctx(env='online', autocommit=True)
    qry = f"""
            select bid from sync_traj_bid_record where city = '{city}' limit 1
            """
    res = ctx.traj_db2.fetchone(qry)
    if len(res) == 0:
        print(f"{city} 轨迹资料未准备好")
        return
    maintain_track_info(batch, True)


def maintain_auto(ctx: Context, used_storefront_track: bool = True):
    """
    自动维护情报信息
    """
    # if used_storefront_track and not traj_processor.track_ready(ctx):
    #     print(f"轨迹资料未准备好")
    #     return

    qry = f"""
    select batch, city, count(*) from {ctx.pushed_tab} 
    where status = 'INIT' and batch not like '%test%' group by batch, city order by count(*) desc 
    """
    res = ctx.poi_db.fetchall(qry)
    if len(res) == 0:
        print(f"没有数据需要准备资料")
        return

    multi = min(3, len(res))
    print(f"并发度：{multi}")

    with multiprocessing.Pool(processes=multi) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        pool.map(_maintain_auto, res)
    print("over")


def main():
    """
    主函数
    """
    fns = ARGS.fns
    ctx = gen_ctx('online')

    limit = int(ARGS.limit)
    batch = str(ARGS.batch)
    if ',' in batch:
        batches = batch.split(',')
    else:
        batches = [batch]

    if 'callback' == fns:
        consume_turing_result(ctx)
    if 'callback_multi' == fns:
        ctx.is_committed = False
        callback_multi(ctx)
    if 'push' == fns:
        for _batch in batches:
            push_info_with_lock(_batch, limit=limit, status='MAINTAINED')
            print(f"{_batch} over")
    if 'push_test' == fns:
        for _batch in batches:
            push_info(_batch, limit=limit, status='MAINTAINED')
    if 'push_auto' == fns:
        push_auto()
    if 'push_multi' == fns:
        for _batch in batches:
            # push_multi(_batch, limit=limit, status='MAINTAINED')
            push_multi_info_with_lock(_batch, limit=limit, status='MAINTAINED')
    if 'init' == fns:
        for _batch in batches:
            init_info_with_lock(_batch)
    if 'init_by_icafe' == fns:
        init_info_by_icafe()
    if 'again' == fns:
        batch = str(ARGS.batch)
        file = str(ARGS.file)
        reason = str(ARGS.reason)
        _online_verified_again_by_file(file, batch, reason)
    if 'maintain' == fns:
        maintain_track_info(batch)
    if 'maintain_auto' == fns:
        maintain_auto(gen_ctx(env='online', autocommit=True))


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='核实，图灵的推送和回传')
    parser.add_argument(
        '--fns',
        type=str,
        required=True,
        help="执行的函数；callback: 图灵回传"
    )
    parser.add_argument(
        '--batch',
        type=str,
        help="推送的批次号",
        default='',
    )
    parser.add_argument(
        '--file',
        type=str,
        help="文件"
    )
    parser.add_argument(
        '--reason',
        type=str,
        help="原因"
    )
    parser.add_argument(
        '--limit',
        type=int,
        default=10000,
    )

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()
