"""
门前停车场面修形入口文件
"""
from dataclasses import dataclass
from functools import partial
from pathlib import Path
from typing import Callable, Iterator

from tqdm import tqdm

from src.parking.storefront import region_task, store_poi_cluster
from src.tools import pipeline

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    上下文
    """

    batch: str


@desc()
def collect_street_region(ctx: Context, proceed, provider: Callable[[], Iterator[str]], batch: str):
    """
    收集街道区域
    """
    points = list(provider())
    points_stream = tqdm((x for x in points), total=len(points))
    regions_stream = region_task.get_street_regions(points_stream)
    region_task.set_tasks(list(regions_stream), batch)
    proceed()


@desc()
def resolve_street_region(ctx: Context, proceed, output_dir: Path):
    # tasks = region_task.get_tasks_by_batch(ctx.batch)  # [(task_id, geom)]
    # tasks = region_task.get_tasks_by_id([369])  # 异常面 case: 739
    # tasks = region_task.get_tasks_by_id([327])  # 临街POI面过滤
    # tasks = region_task.get_tasks_by_id([1, 15, 16, 24, 27])  # 压辅路: 1, 15, 16, 24, 27
    # tasks = region_task.get_tasks_by_id([77, 755, 128])  # 内部临街POI
    # tasks = region_task.get_tasks_by_id([421])  # 漏招 421
    # tasks = region_task.get_tasks_by_id([51])  # case1201-4 51
    # tasks = region_task.get_tasks_by_id([756])  # case1201-22 756
    tasks = region_task.get_tasks_by_id([1050])  # case1201-7 1050
    store_poi_cluster.execute(tasks, output_dir)
    proceed()


def execute_by_position(output_dir: Path):
    """
    # 漏招 case：20241102
    """
    positions = [
        "POINT(116.369607562 39.9780025412)",
        "POINT(116.260209585 39.9150750872)",
        "POINT(116.253471147 40.0829376069)",
        "POINT(116.293928891 39.9101052813)",
        "POINT(116.310721051 39.9357808479)",
        "POINT(116.347175587 39.9670133546)",
        "POINT(116.323585523 39.9010385529)",
        "POINT(116.32047474 40.0428947256)",
        "POINT(116.322854277 39.9740365508)",
        "POINT(116.212183809 40.1005780388)",
        "POINT(116.226376355 39.9968224548)",
        "POINT(116.330239109 39.9279943401)",
        "POINT(116.366828226 39.9960933833)",
        "POINT(116.264174995 39.9293318499)",
        "POINT(116.323918516 39.9049248224)",
        "POINT(116.267608284 40.0318236796)",
        "POINT(116.268518772 39.**********)",
        "POINT(116.340542761 40.03527995)",
        "POINT(116.128765672 40.**********)",
        "POINT(116.342040271 40.**********)",
        "POINT(116.324276503 39.**********)",
    ]
    tasks = [task for p in positions for task in region_task.get_tasks_by_wkt(p)]
    store_poi_cluster.execute(tasks, output_dir)


def main():
    batch = "beijing_haidian_20241022"
    # batch = "beijing_20241023"
    pipe = pipeline.Pipeline(
        # partial(collect_street_region, provider=lambda: region_task.get_store_points_by_city("北京市"), batch=batch),
        # partial(collect_street_region, provider=lambda: region_task.get_store_points_by_wkt(wkt_str), batch=batch),
        # partial(resolve_street_region, output_dir=Path("output_full") / f"{batch}_20241031"),
        partial(resolve_street_region, output_dir=Path("output_full") / "repair_polygon"),
    )
    desc.attach(pipe)
    ctx = Context(batch=batch)
    pipe(ctx)


if __name__ == "__main__":
    # main()
    execute_by_position(output_dir=Path("output_full") / "recall_missing")
