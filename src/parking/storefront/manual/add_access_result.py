"""
根据差分结果回捞人工核实结果
"""
import os
import sys
import shutil
import pymysql
import json
import time
from datetime import datetime
from pathlib import Path

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.parking.storefront.post_process import autocomplete
from src.trajectory.utils import coord_trans


def create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def get_poi_online_connetion():
    """
    创建 pg poi_onlie 数据库连接
    """
    host, port, user, pwd, database = pgsql.get_connection(pgsql.POI_CONFIG)
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")

get_poi_online_connetion

def get_diff_result():
    """
    获取差分结果: park_storefront_strategy_diff.status
    状态
    GATE_DIFFED_MATCHING : 需要从人工核实库去捞
    GATE_DIFFED_REUSE：从门前履历库[park_storefront_strategy]去捞
    :return:
    """
    sql = """
            select task_id, face_id, status from park_storefront_strategy_diff
            -- where status in ('GATE_DIFFED_MATCHING', 'GATE_DIFFED_REUSE')
            where status in ('INIT')
        """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return ret


def update_diff_status(task_id, face_id, from_status, to_status):
    """
    更新差分状态
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    sql = f"""
            update park_storefront_strategy_diff set status = '{to_status}', updated_at = '{update_time}'
            where task_id = {task_id} and face_id = '{face_id} and status = '{from_status}
        """
    cursor.execute(sql)
    conn.commit()


def update_diff_status_by_id(id, from_status, to_status):
    """
    更新差分状态
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    sql = f"""
            update park_storefront_strategy_diff set status = '{to_status}', updated_at = '{update_time}'
            where id= {id} and status = '{from_status}'
        """
    cursor.execute(sql)
    conn.commit()


def update_diff_info_by_id(id, from_status, to_status, access_ids):
    """
    更新差分状态
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    access_ids = json.dumps(access_ids)
    sql = f"""
            update park_storefront_strategy_diff set access_ids = '{access_ids}',
             status = '{to_status}', updated_at = '{update_time}'
            where id= {id} and status = '{from_status}'
        """
    cursor.execute(sql)
    conn.commit()


def get_one_task_id_list_by_status(status, task_id=None):
    """
    获取需要回捞的任务ID
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    if task_id:
        sql = f"""
                select task_id from park_storefront_task where status = '{status}' 
                 and task_id = {task_id} order by task_id asc limit 1
            """
    else:
        sql = f"""
            select task_id from park_storefront_task where status = '{status}' order by task_id asc limit 1
        """
    # print(sql)
    cursor.execute(sql)
    res = cursor.fetchone()
    conn.commit()
    if not res:
        return 0
    else:
        return res[0]


def get_task_list_by_status(status, task_id=None):
    """
    获取需要回捞的任务ID
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    if task_id:
        sql = f"""
                select task_id,batch from park_storefront_task where status = '{status}' and task_id = {task_id}
            """
    else:
        sql = f"""
            select task_id,batch from park_storefront_task where status = '{status}'
        """
    cursor.execute(sql)
    res = cursor.fetchall()
    return res


def get_task_list_by_status_haidian(status, task_id=None):
    """
    获取需要回捞的任务ID
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    if task_id:
        sql = f"""
                select task_id,batch from park_storefront_task where status = '{status}' and task_id = {task_id}
            """
    else:
        sql = f"""
            select task_id,batch from park_storefront_task where status = '{status}' and batch = 'beijing_haidian_20241206'
        """
    cursor.execute(sql)
    res = cursor.fetchall()
    return res


def update_task_status(task_id, from_status, to_status):
    """
    更新任务状态
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    sql = f"""
            update park_storefront_task set status = '{to_status}', updated_at = '{update_time}'
            where task_id = {task_id} and status = '{from_status}'
        """
    cursor.execute(sql)
    conn.commit()


def get_diff_face_list_by_task(task_id, name, status=None):
    """
    获取等待人工回捞的数据
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    if not status:
        sql = f"""
                    select id, face_id, status, result from park_storefront_strategy_diff
                    where task_id = {task_id} and name = '{name}'
                """
    else:
        sql = f"""
                select id, face_id, status, result from park_storefront_strategy_diff
                where task_id = {task_id} and name = '{name}' and status in ('{status}')
            """
    # print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    # conn.commit()
    return res


def get_reuse_access_list_by_diff_id(id):
    """
    根据diff_id获取可重用的access_ids
    """
    conn_poi = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor_poi = conn_poi.cursor()

    conn_back = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
    cursor_back = conn_back.cursor()

    print(f"select access_ids, result from park_storefront_strategy_diff where id = {id}")
    cursor_poi.execute(f"""
                    select access_ids, result from park_storefront_strategy_diff where id = {id}
                """)
    diff_data = cursor_poi.fetchone()
    # print(diff_data)
    if not diff_data:
        return []
    # all_access_ids = 当前面 access_ids(current_access_ids)  + 上一个面的 access_ids(old_access_ids)
    all_access_ids = []
    current_access_ids = diff_data[0] or []
    logger.info(f"diff_id:{id}, current_access_ids:{current_access_ids}")
    result = diff_data[1]
    # b_ids 会存在多个
    logger.info(f"diff_id:{id}, b_fids:{result['b_ids']}")
    old_access_ids_set = set()
    for b_id in result['b_ids']:
        b_id = int(b_id)
        logger.info(f"diff_id:{id}, current_b_id:{b_id}")
        # 查询当前面，需要复用的 access_ids , 通过 b_id 查询查询 strategy_id 定位到需要复用的那个面的 access_ids
        cursor_poi.execute(f"""
                        select access_ids from park_storefront_strategy_diff 
                        where strategy_id = {b_id} order by id desc limit 1
                    """)
        old_access_ids = cursor_poi.fetchone()
        if not old_access_ids:
            continue
        else:
            old_access_ids_set = old_access_ids_set.union(set(old_access_ids[0]))
    # old_access_ids = list(old_access_ids_set)
    logger.info(f"diff_id:{id}, old_access_ids_set:{old_access_ids_set}")
    # current_access_ids = [1,2,3]
    # old_access_ids = [3,4,5]
    merged_set = set(current_access_ids).union(old_access_ids_set)
    all_access_ids = list(merged_set)
    if not all_access_ids:
        logger.info(f"diff_id:{id}, all_access_ids:{all_access_ids}")
        return []

    # 检查一下当前 access_ids是否在线上
    online_access_ids = []
    for access_id in all_access_ids:
        cursor_poi.execute(f"""
                        select bid from park_storefront_prod_access where access_id = {access_id} order by id desc limit 1
                    """)
        bid = cursor_poi.fetchone()
        if bid:
            bid = bid[0]
            cursor_back.execute(f""" select status from parking where bid = '{bid}' """)
            status = cursor_back.fetchone()
            if status:
                if int(status[0]) == 1:
                    online_access_ids.append(access_id)
        # 最后一条如果没有bid ，表示还没同步完成，也认为可以上线
        if not bid:
            online_access_ids.append(access_id)
    # conn_back.commit()
    # conn_poi.commit()
    return online_access_ids


def insert_relation_info(task_id):
    """
    插入关系表
    """
    conn_poi = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor_poi = conn_poi.cursor()
    sql = f"""
            select id,task_id,face_id,bid,access_ids,strategy_id,status 
            from park_storefront_strategy_diff where task_id = {task_id} and name = 'GATE_DIFFED'
        """
    cursor_poi.execute(sql)
    res = cursor_poi.fetchall()
    for item in res:
        id = item[0]
        # task_id = item[1]
        face_id = item[2]
        bid = item[3]
        access_ids = item[4]
        strategy_id = item[5]
        currect_diff_status = item[6]

        add_access_ids = []
        for access_id in access_ids:
            add_access_ids.append(int(access_id))

        # 先删除之前的数据
        cursor_poi.execute(f""" delete from park_storefront_relation 
            where task_id = {task_id} and face_id = '{face_id}' """)

        # 再插入新的数据
        cursor_poi.execute("""
                        insert into park_storefront_relation(task_id,face_id,strategy_id,access_ids) 
                        values(%s,%s,%s,%s)""", (task_id, face_id, strategy_id, add_access_ids))
        # 更新diff_id的状态
        # cursor_poi.execute(f"""
        #                 update park_storefront_strategy_diff set status = '{currect_diff_status}_RELATION_DONE'
        #                  where id = {id} and status = '{currect_diff_status}'
        #             """)

    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    cursor_poi.execute(f"""
                update park_storefront_task set status = 'GATE_MATCHED', updated_at = '{update_time}'
                where task_id = {task_id} and status = 'GATE_MATCHING'
            """)
    logger.info(f"task_id:{task_id}, 所有face_id都已完成, 更新 task 为 GATE_MATCHED")

    conn_poi.commit()
    pass


def handle_haidian_data():
    """
    处理海淀数据
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    cursor.execute(f""" select distinct face_id from park_storefront_access where status!='NOT_ONLINE' """)
    face_id_list = cursor.fetchall()
    count = 0
    face_list = set()
    for face in face_id_list:
        face_id = face[0]
        cursor.execute(f"""
                            select id,task_id from park_storefront_strategy where face_id = '{face_id}' order by id desc limit 1
                        """)
        strategy_data = cursor.fetchone()
        if not strategy_data:
            print(f"face_id:{face_id}, 获取履历数据失败")
            continue

        insert_strategy_id = strategy_data[0]
        insert_task_id = strategy_data[1]
        insert_face_id = face_id
        insert_status = 'GATE_DIFFED_MATCHING_MANUALING_FINISHED'
        insert_name = 'GATE_DIFFED'
        remark = '20241209回捞线上海淀'

        cursor.execute(f""" select id from park_storefront_access where face_id = '{insert_face_id}' """)
        access_ids_list = cursor.fetchall()
        if not access_ids_list:
            print(f"face_id:{insert_face_id}, 从park_storefront_access获取出入口失败")
            continue
        insert_access_ids = []
        for access_id in access_ids_list:
            insert_access_ids.append(access_id[0])
        insert_access_ids_json = json.dumps(insert_access_ids)

        cursor.execute(
            f""" select id from park_access_intelligence where outside_id = '{insert_face_id}' """)
        access_intelligence_ids_list = cursor.fetchall()
        if not access_intelligence_ids_list:
            print(f"face_id:{insert_face_id}, 从park_access_intelligence获取出入口失败")
            continue
        insert_access_intelligence_ids = []
        for access_intelligence_id in access_intelligence_ids_list:
            insert_access_intelligence_ids.append(access_intelligence_id[0])
        insert_access_intelligence_ids_json = json.dumps(insert_access_intelligence_ids)

        insert_sql = f""" insert into park_storefront_strategy_diff(task_id,face_id,strategy_id,status,name,
        remark,access_ids,access_intelligence_ids) values ({insert_task_id},'{insert_face_id}'
        ,{insert_strategy_id},'{insert_status}','{insert_name}',
               '{remark}','{insert_access_ids_json}','{insert_access_intelligence_ids_json}') """
        cursor.execute(insert_sql)

        # print(f"face_id:{insert_face_id}, 插入成功")
        access_info = {
            "face_id": insert_face_id,
            "access_ids": insert_access_ids,
            "access_intelligence_ids": insert_access_intelligence_ids
        }
        face_list.add(face_id)
        count = count + 1
        print(f"匹配到的：face_id:{face_id}, access_info:{access_info}")
    print(f"count:{count}")
    conn.commit()
    pass


def get_prod_parking_data():
    """
    获取在线的出入口bid列表
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    cursor.execute(f"""
            select * from park_storefront_prod_parking
        """)
    prod_parking_list = cursor.fetchall()
    count = 0
    if not prod_parking_list:
        return []
    else:
        for item in prod_parking_list:
            face_id = item[0]
            bid = item[1]
            name = item[2]
            address = item[3]
            geom = item[4]
            central_line = item[5]
            batch = item[6]

            cursor.execute(f"""
                    select * from park_storefront_strategy where face_id = '{face_id}' order by id desc limit 1
                """)
            strategy_data = cursor.fetchone()
            if not strategy_data:
                print(f"face_id:{face_id}, 从park_storefront_strategy 获取失败1111")
                continue
            prev_face_ids = strategy_data[2]
            # print(f"face_id:{face_id}, prev_face_ids:{prev_face_ids}")
            if not prev_face_ids:
                continue

            for prev_face_id in prev_face_ids:
                cursor.execute(f"""
                            select * from park_storefront_strategy where face_id = '{prev_face_id}'
                        """)
                prev_diff_data = cursor.fetchone()
                if not prev_diff_data:
                    print(f"face_id:{face_id}, 从park_storefront_strategy_diff 获取失败2222")
                    continue
                print(f"{prev_face_id}")
                count = count + 1

                insert_strategy_id = prev_diff_data[0]
                insert_task_id = 46250
                insert_face_id = prev_face_id
                insert_status = 'GATE_DIFFED_MATCHING_FINISHED_RELATION_DONE'
                insert_name = 'GATE_DIFFED'
                remark = '线上海淀'

                cursor.execute(f""" select id from park_storefront_access where face_id = '{insert_face_id}' """)
                access_ids_list = cursor.fetchall()
                if not access_ids_list:
                    print(f"face_id:{insert_face_id}, 从park_storefront_access获取出入口失败")
                    continue
                insert_access_ids = []
                for access_id in access_ids_list:
                    insert_access_ids.append(access_id[0])
                insert_access_ids_json = json.dumps(insert_access_ids)

                cursor.execute(
                    f""" select id from park_access_intelligence where outside_id = '{insert_face_id}' """)
                access_intelligence_ids_list = cursor.fetchall()
                if not access_intelligence_ids_list:
                    print(f"face_id:{insert_face_id}, 从park_access_intelligence获取出入口失败")
                    continue
                insert_access_intelligence_ids = []
                for access_intelligence_id in access_intelligence_ids_list:
                    insert_access_intelligence_ids.append(access_intelligence_id[0])
                insert_access_intelligence_ids_json = json.dumps(insert_access_intelligence_ids)

                insert_sql = f""" insert into park_storefront_strategy_diff(task_id,face_id,strategy_id,status,name,
                remark,access_ids,access_intelligence_ids) values ({insert_task_id},'{insert_face_id}'
                ,{insert_strategy_id},'{insert_status}','{insert_name}',
                       '{remark}','{insert_access_ids_json}','{insert_access_intelligence_ids_json}') """
                # print(insert_sql)
                cursor.execute(insert_sql)

    conn.commit()
    print(f"count:{count}")


def add_prod_parking(task_id, batch):
    """
    插入线上成果表
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    cursor.execute(f"""
                 select id, face_id from park_storefront_strategy
                 where task_id = {task_id} and step = 'POST'
             """)
    res = cursor.fetchall()

    for item in res:
        strategy_id = item[0]
        face_id = item[1]

        post_data = autocomplete.get_prod_parking(task_id, face_id)
        parking_name = post_data.name
        parking_address = post_data.address
        parking_geom = post_data.geom
        task_id = task_id

        # print(f"face_id:{face_id}, {post_data.name}")
        print(f"face_id:{face_id}, {parking_geom}")

        # 先插出入口
        for access_info in post_data.accesses:
            access_id = access_info.access_id
            access_name = access_info.name
            access_address = access_info.address
            access_road_relation = json.dumps(access_info.road_relation)
            source_id = '1'
            point = access_info.road_relation['link_info'][0]['point']
            point_x, point_y = point.split(',')

            point_wkt = coord_trans.mc_to_ll_wkt(f"POINT({point_x} {point_y})")
            point_gcj = coord_trans.bd09_to_gcj02_wkt(point_wkt)
            # print(f"face_id:{face_id}, point:{point}, point_gcj:{point_gcj}")

            cursor.execute(f"""
                    insert into park_storefront_prod_access (task_id, source_id, face_id,
                    name, address, road_relation, access_id, geom) values ({task_id}, '{source_id}',
                     '{face_id}', '{access_name}', '{access_address}',
                     '{access_road_relation}', {access_id}, st_geomfromtext('{point_gcj}', 4326))
                """)

        # 再插停车场
        cursor.execute(f"""
                insert into park_storefront_prod_parking (task_id, face_id, name, address, geom, batch, status)
                values ({task_id}, '{face_id}', '{parking_name}', 
                '{parking_address}', st_geomfromtext('{parking_geom}', 4326), '{batch}', 'INIT')
            """)

    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    cursor.execute(f"""
                update park_storefront_task set status = 'READY_ONLINE', updated_at = '{update_time}'
                where task_id = {task_id} and status = 'POSTED'
            """)
    logger.info(f"task_id:{task_id}, 所有face_id都已完成, 更新 task 为 READY_ONLINE")

    conn.commit()
    pass


if __name__ == "__main__":
    """
    主函数
    GATE_DIFFED_MATCHING : 需要从人工核实库去捞
    """
    arguments = sys.argv[1:]
    print(arguments)
    if len(arguments) < 1:
        print("请输入参数===========================")
        print("处理出入口匹配结果数据: deal_gate_matching_data")
        # print("拉取人工核实结果: get_manual_result")
        print("检测task下人工核实是否结束: check_manual_result")
        print("后处理完成推送数据到线上成果库: pre_prod_data")
        print("补齐数据到diff表: supply_data_to_diff")
        print("自测用: test")
        exit(0)
    action = arguments[0]
    # today = datetime.now().strftime("%Y%m%d")

    if action == 'deal_gate_matching_data':
        # task_id = get_one_task_id_list_by_status('GATE_MATCHING', 46250)
        task_id_list = get_task_list_by_status('GATE_MATCHING')
        if not task_id_list:
            logger.error(f"没有当前环节需要的task")
            exit(0)
        for task_info in task_id_list:
            task_id = task_info[0]

            check_status = "','".join(['GATE_DIFFED_MATCHING', 'GATE_DIFFED_REUSE', 'GATE_DIFFED_PARTIAL_REUSE'])
            # check_status = ','.join(['INIT'])
            task_face_list = get_diff_face_list_by_task(task_id, 'GATE_DIFFED', check_status)
            logger.info(f"task_id:{task_id}, count: {len(task_face_list)}")
            for face_item in task_face_list:
                diff_id = face_item[0]
                face_id = face_item[1]
                status = face_item[2]
                result = face_item[3]
                # result = json.loads(result)
                if status == 'GATE_DIFFED_MATCHING':
                    logger.info(f"diff_id:{diff_id},{task_id}, {face_id}, {status}")
                    update_diff_status_by_id(diff_id, 'GATE_DIFFED_MATCHING', 'GATE_DIFFED_MATCHING_MANUALING')

                # 全复用
                elif status == 'GATE_DIFFED_REUSE':
                    online_access_ids = get_reuse_access_list_by_diff_id(diff_id)
                    if not online_access_ids:
                        # 当前面无在线出入口，直接将这个face_id的状态改为GATE_DIFFED_MATCHING_NO_ONLINE_ACCESS
                        update_diff_status_by_id(diff_id,
                                                 'GATE_DIFFED_REUSE',
                                                 'GATE_DIFFED_REUSE_NO_ONLINE_ACCESS_FAILED')
                    else:
                        # 有在线出入口，则更新为GATE_DIFFED_MATCHING_ONLINE_ACCESS
                        update_diff_info_by_id(diff_id,
                                               'GATE_DIFFED_REUSE',
                                               'GATE_DIFFED_REUSE_FINISHED',
                                               online_access_ids)
                # 部分复用
                elif status == 'GATE_DIFFED_PARTIAL_REUSE':
                    online_access_ids = get_reuse_access_list_by_diff_id(diff_id)
                    if not online_access_ids:
                        # 当前面无在线出入口，直接将这个face_id的状态改为GATE_DIFFED_MATCHING_NO_ONLINE_ACCESS
                        update_diff_status_by_id(diff_id, 'GATE_DIFFED_PARTIAL_REUSE',
                                                 'GATE_DIFFED_PARTIAL_REUSE_NO_ONLINE_ACCESS_FAILED')
                    else:
                        # 有在线出入口，则更新为GATE_DIFFED_MATCHING_ONLINE_ACCESS
                        update_diff_info_by_id(diff_id, 'GATE_DIFFED_PARTIAL_REUSE',
                                               'GATE_DIFFED_PARTIAL_REUSE_MANUALING',
                                               online_access_ids)

    if action == 'check_manual_result':
        # task_id_list = get_task_list_by_status('GATE_MATCHING')
        task_id_list = get_task_list_by_status_haidian('GATE_MATCHING')
        if not task_id_list:
            logger.error(f"没有当前环节需要的task")
            exit(0)
        for task_info in task_id_list:
            task_id = task_info[0]
            logger.info(f"check_manual_result:begin: task_id:{task_id}")

            finished_status = [
                # 人工回捞全部完成
                'GATE_DIFFED_MATCHING_MANUALING_FINISHED',
                # 非复用部分人工回捞完成
                'GATE_DIFFED_PARTIAL_REUSE_MANUALING_FINISHED',
                # 勇哥直接更新完成的
                'GATE_DIFFED_MATCHING_FINISHED',
                # 全复用
                'GATE_DIFFED_REUSE_FINISHED'
            ]
            task_face_list = get_diff_face_list_by_task(task_id, 'GATE_DIFFED', '')
            # all_finished_diff_ids = []
            not_finished = []
            for face_item in task_face_list:
                diff_id = face_item[0]
                face_id = face_item[1]
                current_diff_status = face_item[2]
                if current_diff_status not in finished_status:
                    logger.info(f"task_id:{task_id}, face_id:{face_id}, 存在未finished的状态， 当前状态:{current_diff_status}")
                    not_finished.append(diff_id)
                    continue

            if not not_finished:
                logger.info(f"task_id:{task_id}, 所有face_id都已完成")
                insert_relation_info(task_id)
            else:
                logger.info(f"task_id:{task_id}, 还有未完成的diff_id:{not_finished}")

            pass

    if action == 'pre_prod_data':
        # task_id_list = get_task_list_by_status('POSTED', 46251)
        task_id_list = get_task_list_by_status('POSTED')
        if not task_id_list:
            logger.error(f"没有当前环节需要的task")
            exit(0)
        for task_info in task_id_list:
            task_id = task_info[0]
            batch = task_info[1]
            logger.info(f"pre_prod_data:begin: task_id:{task_id}, batch:{batch}")
            add_prod_parking(task_id, batch)
            logger.info(f"pre_prod_data:end: task_id:{task_id}")

    if action == 'handle_haidian_data':
        handle_haidian_data()
        pass

    if action == 'test':
        print("for test")
        res = autocomplete.get_prod_parking(46251, '7ad6c4663eade0f3ffddc2fd10424a45')
        print(res)
        pass