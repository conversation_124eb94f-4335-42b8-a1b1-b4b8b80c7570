"""
根据差分结果回捞人工核实结果
"""
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests
import csv
from shapely.geometry import Point, Polygon, LineString, MultiLineString, box
from shapely.ops import transform
from shapely.ops import unary_union
import pyproj
import random

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from shapely import Polygon, LineString

from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER


def _insert_lack_bid(bid, count):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    sql = f"""
    insert into park_storefront_lack_bid (bid,lack_count) values('{bid}', {count})
    """
    cursor.execute(sql)


if __name__ == '__main__':
    with open('/home/<USER>/fanjiabin/aoi-ml/output/src/parking/manual/0112_lack.txt', 'r', encoding='utf-8') as file:
        for line in file:
            bid, data = line.split('\t')
            count = data.count('source_id')

            print(bid, count)
            _insert_lack_bid(bid, count)



