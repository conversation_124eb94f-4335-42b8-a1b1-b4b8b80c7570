"""
根据差分结果回捞人工核实结果
"""
import os
import sys
import shutil
import uuid

import pymysql
import json
import time
from datetime import datetime
from pathlib import Path
import shapely.ops
import re
import requests

from loguru import logger

from src.parking.recognition import dbutils
from src.tools import pgsql, tsv
from src.tools.afs_tool import AfsTool
from src.tools import function as F
from src.tools.conf_tools import get_mysql_conf
from src.trajectory.utils import coord_trans
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.post_process import central_line
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, wkt
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER


def _point_on_face(geom):
    """
    获取面上一点
    """
    qry = f"select st_astext(ST_PointOnSurface(st_geomfromtext('{geom}', 4326))) geom"
    res = dbutils.fetch_one(pgsql.COMPUTE_CONFIG, qry)
    return res[0]


def _gcj2mc(geom):
    """
    将 gcj02 坐标转换为百度墨卡托坐标
    """
    qry = f"select st_astext(gcj2mc(st_geomfromtext('{geom}', 4326)))"
    res = dbutils.fetch_one(pgsql.COMPUTE_CONFIG, qry)
    return res[0]


def _mc2gcj(geom):
    """
    将 gcj02 坐标转换为百度墨卡托坐标
    """
    qry = f"select st_astext(mc2gcj(st_geomfromtext('{geom}', 4326)))"
    res = dbutils.fetch_one(pgsql.COMPUTE_CONFIG, qry)
    return res[0]


def _get_link_geom(link_id):
    """
    获取 link
    """
    qry = f"select link_id, st_astext(geom) geom from nav_link where link_id = '{link_id}'"
    res = dbutils.fetch_one(pgsql.ROAD_CONFIG, qry)
    if res is None or not res:
        return ''
    # return {
    #     'link_id': res[0],
    #     'geom': res[1],
    # }
    return res[1]


def _get_inlink_id_by_nodeid(node_id):
    """
    获取 link
    """
    qry = f"select in_linkid from nav_gate where node_id = '{node_id}'"
    res = dbutils.fetch_one(pgsql.ROAD_CONFIG, qry)
    if res is None or not res:
        return ''
    return res[0]


def _change_linkid_short2long(short_linkid):
    """
    短linkid 转换为 长linkid
    """
    if len(short_linkid) < 5:
        return None
    # 短 linkid to 长 linkid
    sql = f"select sid from image_r where tid = '{short_linkid}'"
    trans_link = dbutils.fetch_one(pgsql.TRANS_ID, sql)
    if trans_link is None or len(trans_link) == 0:
        return None
    long_linkid = trans_link[0]
    return long_linkid


def _change_linkid_long2short(long_linkid):
    """
    长linkid 转换为 短linkid
    """
    sql = f"select tid from image_r where sid = '{long_linkid}'"
    trans_link = dbutils.fetch_one(pgsql.TRANS_ID, sql)
    if trans_link is None or len(trans_link) == 0:
        return None
    short_linkid = trans_link[0]
    return short_linkid


def _change_nodeid_short2long(short_nodeid):
    """
    短nodeid 转换为 长nodeid
    """
    sql = f"select sid from image_n where tid = '{short_nodeid}'"
    trans_link = dbutils.fetch_one(pgsql.TRANS_ID, sql)
    if trans_link is None or len(trans_link) == 0:
        return None
    long_linkid = trans_link[0]
    return long_linkid


def _get_point(line: str) -> str:
    """
    获取线字符串中的点信息，返回格式为 "POINT(x y)"。

    Args:
        line (str): 包含点信息的线字符串，格式为 WKT（Well-Known Text），例如 "LINESTRING(1 2,3 4)"。

    Returns:
        str: 返回格式为 "POINT(x y)" 的点字符串，其中 x 和 y 分别表示点的横纵坐标。

    Raises:
        None
    """
    geo = shapely.wkt.loads(line)
    pt = geo.coords[0]
    return f"POINT({pt[0]} {pt[1]})"


def _get_pushed_access_qingbao():
    sql = f"""select outside_task_id,bid from park_access_intelligence where
    outside_task_id in(select id from park_storefront_prod_parking where status='PARK_ACCESS_INTELLIGENCE_PUSHED')
     group by outside_task_id,bid;"""
    data = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return data


def _get_manualed_parking():
    sql = f"""select id,bid,info_id from park_storefront_prod_parking 
    where status='PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALED'"""
    data = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return data


def _lock_prod_parking_task(id):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    sql = f"""update park_storefront_prod_parking set status='PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALING'
     where id={id}"""
    res = cursor.execute(sql)
    rowcount = cursor.rowcount
    return rowcount


def _unlock_prod_parking_task(id):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    sql = f"""update park_storefront_prod_parking set status='PARK_ACCESS_INTELLIGENCE_PUSHED'
     where id={id}"""
    cursor.execute(sql)


def _finish_prod_parking_task(id):
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    sql = f"""update park_storefront_prod_parking set status='PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALED'
     where id={id}"""
    cursor.execute(sql)


def _create_beeflow_connection():
    """
    创建 mysql 数据库连接
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    return pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4")


def _is_contain_note_string(note_string, keywords):
    """
    判断note_string是否包含关键字
    """
    # keywords = ["缺路", "缺失link", "需要补路"]
    if any(keyword in note_string for keyword in keywords):
        return True
    else:
        return False


def _is_note_lack_contain(note, keywords):
    note_list = set()
    for work in note['work']:
        if 'note' not in work:
            continue
        note = work['note']
        if note == '':
            continue
        note_list.add(note)
    note_string = ','.join(note_list)
    if not _is_contain_note_string(note_string, keywords):
        return False

    return True


def _get_manual_result(task_id, bid):
    """
    获取所有人工核实的出入口数据
    """
    collection_map = {
        '新增POI': 'new_poi',
        '更新POI': 'update_poi',
        '无效': 'invalid',
    }
    sql = f"""
                select source_id from park_access_intelligence where
                bid = '{bid}' and outside_task_id = {task_id} and type = 'park_access_gate_front';
          """
    # sql = f"""
    #         select source_id from park_access_intelligence where outside_id = '1d0de46c1ad4456aa00b4516b0baefa8'
    #         and type = 'park_access_gate_front';
    #       """
    list = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    beeflow_connection = _create_beeflow_connection()
    result = []
    try:
        for item in list:
            source_id = item[0]
            sql = (f"select park_manual_task_id,item_content,ptid,source_id from park_manual_task "
                   f"where source_id = '{source_id}' and source_id like 'storefront_access_add_%'")
            with beeflow_connection.cursor() as cursor:
                cursor.execute(sql)
                res = cursor.fetchone()
                if res is None:
                    logger.info(f"source_id:{source_id} 无park_manual_task记录")
                    continue

                address = ''
                item_content_json = res[1]
                for achieve_data in json.loads(item_content_json):
                    if 'data' in achieve_data and address == '':
                        address = achieve_data['data']['address']
                        break

                item_content = json.loads(item_content_json)
                for achieve_data in item_content:
                    answer = achieve_data['answer']
                    note = achieve_data['note']
                    scene = ''

                    if ('data' in achieve_data and 'address' in achieve_data['data']
                            and achieve_data['data']['address'] != ''):
                        address = achieve_data['data']['address']

                    conclusion = answer['conclusion']
                    if 'conclusion_new' in answer:
                        conclusion_new = answer['conclusion_new']
                    user_conclusion = collection_map.get(conclusion)
                    if user_conclusion is None:
                        user_conclusion = f"source_id_{source_id}_{conclusion}"
                    if conclusion != '新增POI':
                        link_id = node_id = 'invaild'
                        point = f"0,0"
                        related_type = 'OTHER'
                        if address == '':
                            address = '未知'
                    elif conclusion == '新增POI' and conclusion_new == '缺失LINK':
                        link_id = node_id = 'lack'
                        point = answer['point']
                        scene = '缺失link'
                        if _is_note_lack_contain(note, ['路障']):
                            scene = 'link上有路障'
                        elif _is_note_lack_contain(note, ['阻断link', '阻断LINK']):
                            scene = '阻断link'
                        elif _is_note_lack_contain(note, ['出口LINK', '出口link']):
                            scene = '出口link'
                        elif _is_note_lack_contain(note, ['出口大门错误']):
                            scene = '出口大门'
                        related_type = 'OTHER'
                        user_conclusion = 'lack'
                        if address == '':
                            address = '未知'
                    elif conclusion == '新增POI' and 'link_id' in answer and answer['link_id'] == '':
                        link_id = node_id = 'invaild'
                        point = f"0,0"
                        related_type = 'OTHER'
                        user_conclusion = 'nolink'
                        if address == '':
                            address = '未知'
                    else:
                        related_type = answer['related_type']
                        link_id = node_id = ''
                        point = ''
                        if related_type == 'LINK' and 'link_id' in answer:
                            link_id = answer['link_id']
                            point = answer['point']
                        elif related_type == 'DE大门':
                            related_type = 'NODE'
                            gate_list = answer['gate']
                            if len(gate_list) > 1:
                                print(f"source_id:{source_id}, 存在多个门")
                            for node_info in gate_list:
                                node_id = node_info['node_id']
                                point = node_info['point']

                    if point == '':
                        print(f"source_id:{source_id}, 存在空点")
                        continue

                    name = answer['name']
                    if name == '':
                        print(f"source_id:{source_id}, 存在空名称")
                        continue

                    if address == '':
                        print(f"source_id:{source_id}, 存在空地址")
                        continue

                    if link_id != '' or node_id != '':
                        temp = {
                            'bid': bid,
                            'source_id': source_id,
                            'point': point,
                            'name': name,
                            'address': address,
                            'type': related_type,
                            'link_id': link_id,
                            'node_id': node_id,
                            'user_conclusion': user_conclusion,
                            'scene': scene,
                        }
                        result.append(temp)
    except Exception as e:
        logger.error(f"exception:{e}")
    finally:
        beeflow_connection.close()
    return result


def _generate_gate_road_relation(data):
    result = []
    temp_lat = 0
    for item in data:
        source_id = item['source_id']
        bid = item['bid']
        point = item['point']
        name = item['name']
        address = item['address']
        type = item['type']
        user_conclusion = item['user_conclusion']
        scene = item['scene']
        try:
            if user_conclusion == 'lack':
                match = re.match(r"POINT\(([^ ]+) ([^ ]+)\)", point)
                if match:
                    lon = float(match.group(1))
                    lat = float(match.group(2))
                    point = f"{lon}, {lat}"
                lon_mc, lat_mc = point.split(',')
                # lon_gcj_tmp, lat_gcj_tmp = coord_util.bd09mc_to_gcj02(float(lon_mc), float(lat_mc))
                point_gcj = _mc2gcj(f'POINT({lon_mc} {lat_mc})')
                point_gcj_geo = shapely.wkt.loads(point_gcj)
                lon_gcj_tmp = point_gcj_geo.x
                lat_gcj_tmp = point_gcj_geo.y
                result.append({
                    "bid": bid,
                    "source_id": source_id,
                    "name": name,
                    "address": address,
                    # "road_relation": json.dumps({"link_info": "lack"}),
                    "road_relation": '{}',
                    "lon_gcj": lon_gcj_tmp,
                    "lat_gcj": lat_gcj_tmp,
                    "user_conclusion": user_conclusion,
                    "scene": scene
                })
                continue

            if user_conclusion != 'new_poi':
                result.append({
                    "bid": bid,
                    "source_id": source_id,
                    "name": name,
                    "address": address,
                    "road_relation": '{}',
                    "lon_gcj": 0,
                    "lat_gcj": temp_lat,
                    "user_conclusion": user_conclusion,
                    "scene": scene
                })
                temp_lat = temp_lat + 1
                continue

            match = re.match(r"POINT\(([^ ]+) ([^ ]+)\)", point)
            if match:
                lon = float(match.group(1))
                lat = float(match.group(2))
                point = f"{lon}, {lat}"

            new_type = 1
            if type == 'LINK':
                new_type = 2
            link_id = item['link_id']
            node_id = item['node_id']

            # link_info_list = []
            # temp_data = {}
            if new_type == 1:
                # 短node_id , 找短 link_id
                long_node_id = _change_nodeid_short2long(node_id)
                lone_link_id = _get_inlink_id_by_nodeid(long_node_id)
                if lone_link_id == '':
                    logger.error(f"{source_id} has no lone_link_id")
                    continue
                short_link_id = _change_linkid_long2short(lone_link_id)
                if short_link_id is None:
                    print(f"{source_id} has no short_link_id")
                    continue
                print(f"{source_id} has short_link_id: {short_link_id}")
                link_info_list = [
                    {
                        "type": 1,
                        "node_id": node_id,
                        "link_id": short_link_id,
                        "point": point,
                        "orientation": 1
                    }
                ]
            else:
                link_info_list = [
                    {
                        "type": 2,
                        "link_id": link_id,
                        "point": point,
                        "orientation": 1
                    }
                ]

            road_relation = {"link_info": link_info_list}
            road_relation_json = json.dumps(road_relation)

            lon_mc, lat_mc = point.split(',')
            # lon_gcj, lat_gcj = coord_util.bd09mc_to_gcj02(float(lon_mc), float(lat_mc))
            point_gcj = _mc2gcj(f'POINT({lon_mc} {lat_mc})')
            point_gcj_geo = shapely.wkt.loads(point_gcj)
            lon_gcj = point_gcj_geo.x
            lat_gcj = point_gcj_geo.y
            temp = {
                "bid": bid,
                "source_id": source_id,
                "name": name,
                "address": address,
                "road_relation": road_relation_json,
                "lon_gcj": lon_gcj,
                "lat_gcj": lat_gcj,
                "user_conclusion": user_conclusion,
                "scene": scene
            }
            result.append(temp)
        except Exception as e:
            print(e)
            print(f"bid:{bid}, source_id:{source_id}")
            continue

    return result


def _get_access_ids_by_strategy_id(strategy_id):
    sql = f"SELECT access_ids FROM park_storefront_relation WHERE strategy_id={strategy_id}"
    # print(sql)
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    return res


def _get_access_intelligence_ids_by_strategy_id(_strategy_id):
    sql = (f"select access_intelligence_ids from park_storefront_strategy_diff "
           f"where strategy_id={strategy_id} and name = 'GATE_DIFFED'")
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    if len(res) != 1:
        print(sql)
        print("匹配数量不符合预期")
        return []
    return res[0][0]


def _maintain_old_access_info(park_bid, intelligence_ids):
    if len(intelligence_ids) == 0:
        return 0

    ids = ','.join([f"{val}" for val in intelligence_ids])
    sql = f"update park_access_intelligence set bid = '{park_bid}' where id in ({ids})"
    print(sql)

    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    cursor.execute(sql)
    return cursor.rowcount


def _recover_park_status(park_id):
    sql = f"update park_storefront_prod_parking set status = 'READY' where id = {park_id};"
    print(sql)

    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    cursor.execute(sql)
    return cursor.rowcount


def _get_passed_access_by_park_id(park_id):
    sql = f"""
         select id,name,address,road_relation,geom,source_id,scene from park_storefront_access
          where park_id={park_id} and conclusion in('new_poi','lack')
     """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _test_passed_access_id():
    sql = f"""
         select id,name,address,road_relation,geom,source_id from park_storefront_access
          where id in(33366,26037,26038) and conclusion='new_poi'
     """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql)


def _get_access_info_by_strategy_id(strategy_id):
    sql = f"SELECT access_ids FROM park_storefront_relation WHERE strategy_id={strategy_id}"
    # print(sql)
    res = dbutils.fetch_one(pgsql.POI_CONFIG, sql)
    return res


def _history_beijing_prod_parking():
    sql = (f"select id,bid,info_id from park_storefront_prod_parking "
           f"where info_id != '' and status = 'READY' and source = 'strategy'")
    # sql = (f"select id,bid,info_id from park_storefront_prod_parking "
    #        f"where id= 4735")
    res = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return res


def no_repeat_park_access(park_bid: str, park_access_road_relation: str) -> bool:
    """
    判断是否重复出入口
    Args:
        park_bid:
        park_access_road_relation:

    Returns:

    """
    if not park_access_road_relation or not park_bid:
        return False
    # 获取成果库出入口
    related_park_access_list = []
    sql = f"""
        select st_astext(geom), road_relation from park_storefront_prod_access 
        where parent_bid = '{park_bid}' and status in('READY_ONLINE','ALREADY_ONLINE')
    """
    park_access_list = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    if park_access_list:
        related_park_access_list += park_access_list
    # 查询线上已关联成果库出入口
    sql = f"""
        select st_astext(gcj_geom), road_relation from park_online_data 
        where parent_id = '{park_bid}' and name like '%入口' 
    """
    park_access_list = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    if park_access_list:
        related_park_access_list += park_access_list
    if len(related_park_access_list) == 0:
        return True
    related_node_ids = []
    related_link_ids = []
    for related_park_access in related_park_access_list:
        road_relation = related_park_access[1]
        if road_relation is not None and "link_info" in road_relation:
            related_node_ids += [x["node_id"] for x in road_relation["link_info"] if "node_id" in x and x['node_id']]
            related_link_ids += [x["link_id"] for x in road_relation["link_info"] if "link_id" in x and x['link_id']]
    # 获取待关联的Node or Link
    node_id = None
    link_id = None
    road_relation_json = json.loads(park_access_road_relation)
    if "link_info" in road_relation_json and len(road_relation_json["link_info"]) > 0:
        node_id = road_relation_json["link_info"][0]['node_id'] if "node_id" in road_relation_json["link_info"][0] \
            else None
        link_id = road_relation_json["link_info"][0]['link_id'] if "link_id" in road_relation_json["link_info"][0] \
            else None
    if (node_id and node_id in related_node_ids) or (link_id and link_id in related_link_ids):
        return False
    return True


def _add_prod_access_bj_tmp(park_id, bid, access_ids):
    """
    插入线上成果表
    """
    conn = pgsql.get_connection(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    sql = f"""
                 select id,name,address,road_relation,geom,source_id from park_storefront_access 
                 where id in({access_ids}) and conclusion='new_poi'
             """
    cursor.execute(sql)
    res = cursor.fetchall()

    no_repeat_flag = 0
    for item in res:
        # 插出入口
        access_id = item[0]
        access_name = item[1]
        access_address = item[2]
        road_relation = item[3]
        road_relation_json = json.dumps(road_relation)
        geom = item[4]
        source_id = item[5]
        check_status = 'new_poi'

        if not no_repeat_park_access(bid, road_relation_json):
            logger.warning(f"park_id:{park_id}, bid:{bid}, access_id:{access_id},出入口重复")
            sql = f""" update park_storefront_access SET step = 'repeat' WHERE id = {access_id}; """
            cursor.execute(sql)
            continue

        sql = f"""
                SELECT road_relation FROM park_storefront_prod_access WHERE park_id = {park_id} ;
            """
        cursor.execute(sql)
        db_road_relation_list = cursor.fetchall()
        if len(db_road_relation_list) > 0:
            for db_road_relation in db_road_relation_list:
                if _road_relation_equals(db_road_relation[0], road_relation):
                    logger.warning(f"park_id:{park_id}, bid:{bid}, access_id:{item[0]},出入口重复")
                    sql = f""" update park_storefront_access SET step = 'repeat' WHERE id = {item[0]}; """
                    cursor.execute(sql)
                    check_status = 'repeat'
                    continue

        if check_status == 'new_poi':
            sql = f"""insert into park_storefront_prod_access(access_id, name, address, road_relation, 
                                geom, source_id, park_id, bid, status, face_id, remark) VALUES ({access_id}, '{access_name}',
                                 '{access_address}', '{road_relation_json}', '{geom}', '{source_id}', {park_id}, 
                                 '{bid}', 'READY_ONLINE', '-1', 'bj_history')"""
            res = cursor.execute(sql)
            if res == 0:
                logger.error(f"park_id:{park_id}, bid:{bid}, access_id:{access_id},插入失败")
                conn.rollback()
                return
            logger.info(f"park_id:{park_id}, bid:{bid}, access_id:{access_id},插入成功")
            no_repeat_flag += 1

    if no_repeat_flag <= 0:
        logger.error(f"park_id:{park_id}, bid:{bid}, 没有新建的出入口")
        result = False
    else:
        # 更新停车场
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        remark = json.dumps({"batch": "bj_history"})
        sql = f"""
                update park_storefront_prod_parking set status = 'READY_ONLINE',
                 updated_at = '{update_time}', remark='{remark}' where id = {prod_parking_id}
            """
        cursor.execute(sql)
        result = True
    conn.commit()
    logger.info(f"park_id:{park_id}, bid:{bid}, done")
    return result


def _add_prod_access(park_id, bid, access_data):
    """
    插入线上成果表
    """
    conn = pgsql.get_connection(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    no_repeat_flag = 0
    for item in access_data:
        access_id = item[0]
        access_name = item[1]
        if "'" in access_name:
            access_name = access_name.replace("'", "''")
        access_address = item[2]
        if "'" in access_address:
            access_address = access_address.replace("'", "''")
        road_relation = item[3]
        road_relation_json = json.dumps(road_relation)
        geom = item[4]
        source_id = item[5]
        scene = item[6]
        check_status = 'new_poi'

        if not no_repeat_park_access(bid, road_relation_json):
            logger.warning(f"park_id:{park_id}, bid:{bid}, access_id:{access_id},出入口重复")
            sql = f""" update park_storefront_access SET step = 'repeat' WHERE id = {access_id}; """
            cursor.execute(sql)
            continue

        sql = f"""
                    SELECT road_relation FROM park_storefront_prod_access WHERE park_id = {park_id} and scene='';
                """
        cursor.execute(sql)
        db_road_relation_list = cursor.fetchall()
        if len(db_road_relation_list) > 0:
            for db_road_relation in db_road_relation_list:
                if _road_relation_equals(db_road_relation[0], road_relation):
                    logger.warning(f"park_id:{park_id}, bid:{bid}, access_id:{item[0]},出入口重复")
                    sql = f""" update park_storefront_access SET step = 'repeat' WHERE id = {item[0]}; """
                    cursor.execute(sql)
                    check_status = 'repeat'
                    continue

        if check_status == 'new_poi':
            sql = f"""insert into park_storefront_prod_access(access_id, name, address, road_relation, 
                        geom, source_id, park_id, parent_bid, status, face_id, scene) VALUES ({access_id}, '{access_name}',
                         '{access_address}', '{road_relation_json}', '{geom}', '{source_id}', {park_id}, 
                         '{bid}', 'READY_ONLINE', '-1', '{scene}')"""
            res = cursor.execute(sql)
            if res == 0:
                logger.error(f"park_id:{park_id}, bid:{bid}, access_id:{access_id},插入失败")
                conn.rollback()
                return
            logger.info(f"park_id:{park_id}, bid:{bid}, access_id:{access_id},插入成功")
            no_repeat_flag += 1

    if no_repeat_flag <= 0:
        logger.error(f"park_id:{park_id}, bid:{bid}, 没有新建的出入口")

    # check park_storefront_prod_access 是否存在有效的出入口，如果存在就更新为 READY_ONLINE，否则更新为无口面
    # 更新停车场
    update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    sql = f"SELECT count(*) FROM park_storefront_prod_access WHERE park_id = {park_id} AND status = 'READY_ONLINE';"
    cursor.execute(sql)
    valid_access = cursor.fetchone()
    logger.info(f"park_id:{park_id}, bid:{bid}, valid_access:{valid_access}")
    if valid_access is not None and valid_access[0] > 0:
        park_status = 'READY_ONLINE'
    else:
        park_status = 'NO_ACCESS'
    sql = f"""
            update park_storefront_prod_parking set status = '{park_status}', updated_at = '{update_time}'
             where id = {prod_parking_id}
        """
    cursor.execute(sql)
    conn.commit()
    logger.info(f"park_id:{park_id}, bid:{bid}, done")


def _road_relation_equals(a: dict, b: dict) -> bool:
    """
    road_relation 是否相等，相等返回 True
    """
    if not a or not b or "link_info" not in a or "link_info" not in b:
        return False
    a_type = a['link_info'][0]['type']
    b_type = b['link_info'][0]['type']
    if a_type != b_type:
        return False
    if a_type == b_type == 1 and a['link_info'][0]['node_id'] == b['link_info'][0]['node_id']:
        return True
    if a_type == b_type == 2 and a['link_info'][0]['link_id'] == b['link_info'][0]['link_id']:
        return True
    return False


def _add_manual_result(access_list, prod_parking_id):
    conn = pgsql.get_connection(pgsql.POI_CONFIG)
    cursor = conn.cursor()

    for item in access_list:
        lon_gcj = item['lon_gcj']
        lat_gcj = item['lat_gcj']
        point_geom = f"POINT({lon_gcj} {lat_gcj})"

        # sql = f"select * from park_storefront_access where task_id={prod_parking_id} "
        # f"and bid='{item['bid']}' "
        # f"and source_id='{item['source_id']}'"
        # f"and road_relation='{item['road_relation']}'"
        # f"and geom=ST_GeomFromText('{point_geom}', 4326);"
        # cursor.execute(sql)
        # exist = cursor.fetchone()
        # if exist:
        #     print(f"prod_parking_id:{prod_parking_id}, bid:{bid}, 出入口已存在, 已存在")
        #     continue

        name = item['name']
        # name 中存在 ' 需要处理一下
        if "'" in name:
            name = name.replace("'", "''")
        address = item['address']
        if "'" in address:
            address = address.replace("'", "''")
        sql = f"""
            INSERT INTO park_storefront_access (park_id,face_id,source_id,name,
            address,batch,road_relation,geom,task_status,conclusion,scene) values ({prod_parking_id},
            '-1','{item['source_id']}',
             '{name}', '{address}', 'new_flow',
              '{item['road_relation']}', ST_GeomFromText('{point_geom}', 4326),
               'FINISHED', '{item['user_conclusion']}', '{item['scene']}');
                """
        cursor.execute(sql)

    sql = f"""update park_storefront_prod_parking 
    set status='PARK_ACCESS_INTELLIGENCE_PUSHED_MANUALED' where id={prod_parking_id}"""
    cursor.execute(sql)

    conn.commit()
    print(f"prod_parking_id:{prod_parking_id}, bid:{bid}, 回捞人工核实结果完成")


def add_access_parent_id():
    """
    添加出入口的父bid
    """
    conn = pgsql.get_connection(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    sql = f"""
        select park_id from park_storefront_prod_access where status in('READY_ONLINE') group by park_id;
    """
    cursor.execute(sql)
    rows = cursor.fetchall()
    for row in rows:
        park_id = row[0]
        sql = f"""
            select bid from park_storefront_prod_parking where id={park_id};
            """
        cursor.execute(sql)
        bid = cursor.fetchone()[0]
        logger.info(f"park_id:{park_id}, bid:{bid}")
        sql = f"""
            UPDATE park_storefront_prod_access SET parent_bid='{bid}' WHERE park_id={park_id};
        """
        cursor.execute(sql)

    conn.commit()


def is_all_callback_by_park_access(bid: str):
    """
    判断所有出入口成果是否都回来了
    Args:
        bid:

    Returns:

    """
    conn = pgsql.get_connection(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    beeflow_connection = _create_beeflow_connection()
    sql = f"""
        select source_id, resp from park_access_intelligence where bid = '{bid}'
    """
    cursor.execute(sql)
    park_access_list = cursor.fetchall()
    if len(park_access_list) == 0:
        print(f"出入口情报未空")
        return True

    bf_cursor = beeflow_connection.cursor()
    print(f"情报数量:{len(park_access_list)}")
    for park_access in park_access_list:
        # 判断是否推送过
        sql = f"select id from integration_qb where id = {park_access[1]} and ref_qb_id='{park_access[0]}'"
        cursor.execute(sql)
        res = cursor.fetchone()
        if res and len(res) > 0:
            # 判断成果是否回来
            sql = f"select park_manual_task_id from park_manual_task where source_id='{park_access[0]}'"
            bf_cursor.execute(sql)
            res = bf_cursor.fetchone()
            if not res:
                return False
    return True


def is_all_callback_by_park_access_bak(bid: str):
    """
    判断所有出入口成果是否都回来了
    Args:
        bid:

    Returns:

    """
    conn = pgsql.get_connection(pgsql.POI_CONFIG)
    cursor = conn.cursor()
    beeflow_connection = _create_beeflow_connection()
    sql = f"""
        select source_id, resp from park_access_intelligence where bid = '{bid}'
    """
    cursor.execute(sql)
    park_access_list = cursor.fetchall()
    if len(park_access_list) == 0:
        print(f"出入口情报未空")
        return True

    bf_cursor = beeflow_connection.cursor()
    print(f"情报数量:{len(park_access_list)}")
    for park_access in park_access_list:
        # 判断是否推送过
        sql = f"select qb_id,content from qb_feedback_records where qb_id = {park_access[1]}"
        cursor.execute(sql)
        res = cursor.fetchone()
        if res and len(res) > 0 and park_access[0] in res[1]:
            # 判断成果是否回来
            sql = f"select park_manual_task_id from park_manual_task where source_id='{park_access[0]}'"
            bf_cursor.execute(sql)
            res = bf_cursor.fetchone()
            if not res:
                return False
    return True


if __name__ == "__main__":
    """
    主函数
    新流程 - 捞取出入口人工核实结果
    """
    arguments = sys.argv[1:]
    print(arguments)
    if len(arguments) < 1:
        print("请输入参数===========================")
        print("拉取人工核实结果入线下成果库[park_storefront_access]: get_manual_result")
        print("线下成果库入线上成果库[park_storefront_prod_access]: add_prod_access")
        exit(0)
    action = arguments[0]

    if action == 'get_manual_result':
        data = _get_pushed_access_qingbao()
        if not data:
            print("无需处理")
            exit(0)
        for item in data:
            prod_parking_id = item[0]
            bid = item[1]

            # 判断是否都回来了，
            if not is_all_callback_by_park_access(bid):
                print(f"{bid} 出入口作业数据未就绪，先不拉")
                continue

            print(f"开始回捞人工核实结果：prod_parking_id: {prod_parking_id}, bid: {bid}")
            lock_res = _lock_prod_parking_task(prod_parking_id)
            if not lock_res:
                print("锁定失败")
                continue
            access_info = _get_manual_result(prod_parking_id, bid)
            if not access_info:
                print(f"prod_parking_id:{prod_parking_id}, bid:{bid}, 未捞到出入口核实结果")
                _unlock_prod_parking_task(prod_parking_id)
                continue
            else:
                access_data = _generate_gate_road_relation(access_info)
                if not access_data:
                    print(f"prod_parking_id:{prod_parking_id}, bid:{bid}, 回捞人工核实结果失败")
                    continue

                logger.info(f"prod_parking_id:{prod_parking_id}, bid:{bid}, access_data:{access_data}")
                _add_manual_result(access_data, prod_parking_id)

    if action == 'add_prod_access':
        prod_parking_info = _get_manualed_parking()
        if not prod_parking_info:
            print("无需处理")
            exit(0)

        print(f"开始入成果库：")
        for item in prod_parking_info:
            prod_parking_id = item[0]
            bid = item[1]
            print(f"prod_parking_id: {prod_parking_id}, bid: {bid}")

            access_data = _get_passed_access_by_park_id(prod_parking_id)
            print(f"有效出入口：prod_parking_id: {prod_parking_id}, bid: {bid}, access_data: {access_data}")

            _add_prod_access(prod_parking_id, bid, access_data)
        print(f"end")

    if action == 'temp_beijing':
        prod_parking_info = _history_beijing_prod_parking()
        for item in prod_parking_info:
            prod_parking_id = item[0]
            bid = item[1]
            info_id = item[2]
            # 97709_strategy
            strategy_id = info_id.split('_')[0]
            # strategy_id 是否数字
            if not strategy_id.isdigit():
                continue
            print(f"开始回捞人工核实结果：prod_parking_id: {prod_parking_id}, bid: {bid}, strategy_id: {strategy_id}")

            access_ids = _get_access_ids_by_strategy_id(strategy_id)
            if access_ids is None:
                # logger.info(f"strategy_id:{strategy_id} has no access_ids")
                continue

            access_ids_list = access_ids[0]
            access_ids_str = ','.join(map(str, access_ids_list))

            result = _add_prod_access_bj_tmp(prod_parking_id, bid, access_ids_str)
            if result:  # 需要把 bid 写入情报表
                access_intelligence_ids = _get_access_intelligence_ids_by_strategy_id(strategy_id)
                _maintain_old_access_info(bid, access_intelligence_ids)
                _recover_park_status(prod_parking_id)
            pass

    if action == 'test':
        # access_info = _get_manual_result(4704, '3641170150848626071')
        # print(access_info)
        # list_data = _generate_gate_road_relation(access_info)
        # print(list_data)
        # road_relation = ('{"link_info": [{"type": 1, "point": "13009811.14,4893302.9", '
        #                  '"link_id": "1561814902", "node_id": "1520369491", "orientation": 1}]}')
        # res = is_repeat_park_access('4726690176869406715', road_relation)
        # print(res)

        # access_data = _test_passed_access_id()

        # _add_prod_access(4735, '4726690176869406715', access_data)
        add_access_parent_id()




