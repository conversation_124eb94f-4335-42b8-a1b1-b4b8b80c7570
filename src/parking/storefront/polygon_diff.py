"""
diff
"""
import csv
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Iterable, Callable

from loguru import logger

import src.parking.storefront.diff.polygon_differ as geo_differ
from src.parking.recognition import dbutils
from src.tools import pgsql

METER = 1e-5


class DiffResponseExplainer:
    """
    差分结果解析
    """

    NEW = "new"
    EQUAL = "equal"
    CONTAIN = "contain"
    BEEN_CONTAINED = "been_contained"
    OTHER = "other"


def _explain_diff_response(response: geo_differ.DiffResponse) -> str:
    """
    解释 diff 结果
    是 新增？相等？包含？被包含？
    """
    if len(response.b_face_ids) == 0:  # 新增
        return DiffResponseExplainer.NEW

    if _think_the_same(response.similarity):  # 相等
        return DiffResponseExplainer.EQUAL

    if _think_contain(response.similarity):
        return DiffResponseExplainer.CONTAIN

    if _think_been_contained(response.similarity):
        return DiffResponseExplainer.BEEN_CONTAINED
    return DiffResponseExplainer.OTHER


def _think_the_same(similarity: geo_differ.PolygonSimilarity) -> bool:
    """
    认为一样
    """

    def _calc_score(simi: geo_differ.Similarity):
        return _calc_similarity_score(simi.iou, 0.9, simi.trust)

    # score = (_calc_score(similarity.poi_similarity)
    #          + _calc_score(similarity.link_similarity)
    #          + _calc_score(similarity.geom_similarity))
    # return score >= 2.1
    score = _calc_score(similarity.link_similarity) + _calc_score(similarity.geom_similarity)
    return score >= 1.1


def _think_contain(similarity: geo_differ.PolygonSimilarity) -> bool:
    """
    认为包含, a 包含 b
    """

    def _calc_score(simi: geo_differ.Similarity):
        return _calc_similarity_score(simi.iob, 0.9, simi.trust)

    # score = (_calc_score(similarity.poi_similarity)
    #          + _calc_score(similarity.link_similarity)
    #          + _calc_score(similarity.geom_similarity))
    # return score >= 2.1
    score = _calc_score(similarity.link_similarity) + _calc_score(similarity.geom_similarity)
    return score >= 1


def _think_been_contained(similarity: geo_differ.PolygonSimilarity) -> bool:
    """
    认为被包含，a 被 b 包含
    """

    def _calc_score(simi: geo_differ.Similarity):
        return _calc_similarity_score(simi.ioa, 0.9, simi.trust)

    # score = (_calc_score(similarity.poi_similarity)
    #          + _calc_score(similarity.link_similarity)
    #          + _calc_score(similarity.geom_similarity))
    # return score >= 2.1
    score = _calc_score(similarity.link_similarity) + _calc_score(similarity.geom_similarity)
    return score >= 1


def _calc_similarity_score(val: float, min_val: float, trust: bool) -> float:
    """
    计算积分
    """
    if not trust:
        return 0.1
    if val >= min_val:
        return 1
    return 0


def _format_diff_response(diff_res: geo_differ.DiffResponse, format_int: bool = False) -> dict:
    """
    格式化
    """

    def _format_int(ids: list) -> list:
        if not format_int:
            return ids
        return [int(_id) for _id in ids]

    return {
        "a_ids": _format_int(diff_res.a_face_ids),
        "b_ids": _format_int(diff_res.b_face_ids),
        "similarity": diff_res.similarity.get_detail() if diff_res.similarity is not None else {},
    }


def _get_prime_polygons(geom_str: str, prev_batch: Iterable[str]) -> list[geo_differ.Polygon]:
    """
    获取原始面
    """
    qry = """
        select face_id, st_astext(geom), task_id, id, strategy, created_at
        from park_storefront_strategy
        where step = 'PRIME' 
            and st_intersects(geom, %s)
            and exists (
                select 1 from park_storefront_task a 
                where a.task_id = park_storefront_strategy.task_id and a.batch in %s
            )
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, qry, [f"SRID=4326;{geom_str}", tuple(prev_batch)])
    polygons = [
        geo_differ.Polygon(
            # face_id=item[0],
            face_id=f"strategy-{item[3]}",  # 需要唯一，face_id 在 strategy 不唯一，用 id
            geom=item[1],
            uid=item[3],
            extra={
                "task_id": item[2],
                "face_id": item[0],
                "created_at": item[5],
                "strategy": item[4],
            },
        )
        for item in ret
    ]
    return polygons


def _get_verified_polygons(geom_str: str):
    """
    获取核实面
    """
    qry = """
        select id, info_id, st_astext(geom) from park_storefront_verify_pushed
        where status not in ('CANCEL')
            and st_intersects(geom, %s)
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, qry, [f"SRID=4326;{geom_str}"])
    polygons = [
        geo_differ.Polygon(
            face_id=f"verify_pushed-{item[0]}",  # 需要唯一，face_id 在 strategy 不唯一，用 id
            geom=item[2],
            uid=item[0],
            extra={"info_id": item[1]},
        )
        for item in ret
    ]
    return polygons


def _get_turing_polygons(geom_str: str) -> list[geo_differ.Polygon]:
    sql = """
        select id, st_astext(geom) 
        from park_storefront_turing_result 
        where st_intersects(geom, %s) 
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [f"SRID=4326;{geom_str}"])
    return [
        geo_differ.Polygon(
            face_id=f"turing_result-{primary_id}",
            geom=geom,
            uid=primary_id,
        )
        for primary_id, geom in ret
    ]


class PrimeDiffProcessor:
    """
    原始面差分处理器，不能用函数闭包，函数闭包不支持多进程序列化
    """

    def __init__(self, prev_batch: list[str]):
        self.prev_batch = prev_batch

    def __call__(self, a_polygon: geo_differ.Polygon):
        def get_b_list(geom_str: str):
            return _get_prime_polygons(geom_str, self.prev_batch) + _get_turing_polygons(geom_str)

        try:
            resp = geo_differ.diff_center([a_polygon], 30 * 1e-5, get_b_list)
            return resp[0]
        except Exception as e:
            logger.error(f"{a_polygon.face_id} 异常：{e}")
            return None


def verified_diff_processor(a_polygon: geo_differ.Polygon):
    """
    核实面差分处理器
    """

    def get_b_list(geom_str: str):
        return _get_verified_polygons(geom_str) + _get_turing_polygons(geom_str)

    try:
        resp = geo_differ.diff_center([a_polygon], 30 * 1e-5, get_b_list)
        return resp[0]
    except Exception as e:
        logger.error(f"{a_polygon.face_id} 异常：{e}")
        return None


def _process_diff(
    process: Callable[[geo_differ.Polygon], geo_differ.DiffResponse],
    a_polygons: list[geo_differ.Polygon],
    save_path: Path,
):
    # 每个进程处理的数量
    with Pool(32) as pool, open(save_path, "w") as hdw:
        writer = csv.writer(hdw)
        for item in pool.imap_unordered(process, a_polygons):
            if item is None:
                continue

            explain = _explain_diff_response(item)
            _format = _format_diff_response(item)
            writer.writerow([_format["a_ids"][0], explain, _format["b_ids"], _format["similarity"]])

    logger.info(save_path)


def process_prime(a_polygons: list[geo_differ.Polygon], b_batch: list[str], save_path: Path):
    """
    差分原始面
    """
    process = PrimeDiffProcessor(b_batch)
    _process_diff(process, a_polygons, save_path)


def process_verified(a_polygons: list[geo_differ.Polygon], save_path: Path):
    """
    差分核实面
    """
    process = verified_diff_processor
    _process_diff(process, a_polygons, save_path)
