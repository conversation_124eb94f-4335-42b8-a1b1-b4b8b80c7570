"""
面修形策略
"""

import shapely
import shapely.ops
from shapely import wkt, LineString, Point, Polygon
from shapely.geometry.base import BaseGeometry

from src.parking.recognition import dbutils
from src.parking.storefront import storage
from src.parking.storefront.utils import geometric
from src.tools import pgsql

METER = 1e-5
METER_2 = METER**2
LD_LINK_BUFFER = 0.01 * METER


def repair_by_bud_face(geom: BaseGeometry):
    """
    根据建筑物面修形
    """
    bud_faces = storage.get_bud_faces(geom.buffer(20 * METER).wkt)
    bud_geoms = [wkt.loads(x) for x in bud_faces]
    bud_union = shapely.unary_union(bud_geoms)
    bud_union = bud_union.buffer(50 * METER).buffer(-50 * METER)
    geom = geom.difference(bud_union)
    return geometric.single_polygon(geom)


def repair_by_sd_road(geom: BaseGeometry):
    """
    根据SD道路线修形
    """
    nav_faces = storage.get_nav_faces(geom.buffer(20 * METER).wkt)
    for nav_face in nav_faces:
        geom = geom.difference(nav_face)

    return geometric.single_polygon(geom)


def repair_by_ld_road(polygon: Polygon, baseline: LineString):
    """
    根据LD路口面和LD道路线修形
    """
    polygon_str = polygon.wkt
    # 获取相交的路口面
    ld_intersections = _get_ld_faces(polygon_str)
    ld_faces = [wkt.loads(x[0]) for x in ld_intersections]
    for ld_face in ld_faces:
        polygon = polygon.difference(ld_face)
    # 获取相交的道路线
    ld_boundaries = _get_ld_boundaries(polygon_str)
    ld_geoms = [wkt.loads(x) for x in ld_boundaries]
    # 将线段合并成multiLinestring
    ld_union = shapely.unary_union(ld_geoms)
    polygon = polygon.difference(ld_union.buffer(LD_LINK_BUFFER))

    polygons = geometric.flat_polygon(polygon)
    polygons = [x for x in polygons if x.area > 10 * METER_2]  # 移除小碎块
    if not polygons:
        return None

    polygons = [(polygon, [Point(x, y) for x, y in polygon.exterior.coords]) for polygon in polygons]
    polygons = [(polygon, max(baseline.distance(p) for p in points)) for polygon, points in polygons]
    polygon, _ = max(polygons, key=lambda x: x[1])
    polygon = polygon.buffer(2 * LD_LINK_BUFFER).buffer(-2 * LD_LINK_BUFFER)  # 修复没贯穿的刀痕
    return polygon


def _get_ld_faces(geom: str):
    """
    获取LD路口面
    """
    sql = f"""
        SELECT ST_AsText(ST_Force2D(geom))
        FROM NAV_LANE_ROAD_PG 
        WHERE ST_Intersects(ST_Force3D(%s), geom)    
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{geom}"])
    return ret


def _get_ld_boundaries(geom: str):
    """
    获取LD道路线
    """
    sql = f"""
        SELECT ST_AsText(ST_Force2D(geom)) 
        FROM NAV_LANE_BOUNDARY 
        WHERE ST_Intersects(ST_Force3D(%s), geom)    
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{geom}"])
    return ret
