"""
自动补全一些门前停车场的相关属性
"""
import re
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

from loguru import logger
from shapely import Point, wkt, LineString
from tqdm import tqdm

from src.parking.recognition import dbutils
from src.parking.storefront.utils import geometric
from src.tools import pgsql, utils, tsv, linq
from src.trajectory.utils import coord_trans

# VERSION = "1.0.0"  # 初始实现：https://ku.baidu-int.com/d/pEEcLv6bAhqnet
VERSION = "1.0.1"  # 使用停车场与 blu_face 的相交面积来决定使用哪个 AOI，而非 click_pv

METER = 1e-5


@dataclass
class Poi:
    """
    POI 信息
    """

    bid: str
    name: str
    std_tag: str
    address: str
    click_pv: int
    point: Point
    city: str


@dataclass
class Park:
    """
    停车场信息
    """

    face_id: str
    name: str
    geom: str


@dataclass
class ProdAccess:
    """
    成果出入口
    """

    access_id: int
    name: str
    address: str
    road_relation: dict


@dataclass
class ProdParking:
    """
    成果停车场
    """

    task_id: str
    face_id: str
    name: str
    address: str
    geom: str
    baseline: str
    accesses: list[ProdAccess]


def _get_haidian_parks() -> list[Park]:
    """
    获取海淀区所有停车场信息
    """
    sql = """
        select face_id, st_astext(geom)
        from park_storefront_intelligence
        where status != 'abnormal'
            and batch = 'beijing_haidian_20241022_20241102.v4'
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return [Park(face_id, "", geom) for face_id, geom in ret]


def try_get_primary_poi(geom: str) -> Optional[Poi]:
    """
    获取停车场关联关系信息
    :param geom: 给定的停车场
    :return: 无关联关系，返回 None，否则返回元组：关联的 POI 和 同 POI 下的其它停车场列表
    """
    blu_faces = list(_get_blu_faces(geom))
    blu_faces = [(poi, geom) for poi, geom in blu_faces if poi.std_tag not in ["教育培训;高等院校"]]
    # 1. 不与 AOI 压盖，使用临街 POI 散点
    if len(blu_faces) == 0:
        store_pois = get_relation_store_pois(geom, search_buffer=50 * METER)
        if len(store_pois) == 0:
            return None

        store_poi = max(store_pois, key=lambda x: x.click_pv)
        return store_poi

    # 2. 与 AOI 压盖（一个或多个），找到 click_pv 最大的 AOI 主点
    park_geom = wkt.loads(geom)
    blu_faces = [(poi, wkt.loads(geom)) for poi, geom in blu_faces]
    blu_face_poi, blu_face_geom = max(blu_faces, key=lambda x: x[1].intersection(park_geom).area)
    return blu_face_poi


def get_prod_parking(task_id: str, face_id: str) -> Optional[ProdParking]:
    """
    获取成果停车场信息，主要用于生成面合并后的停车场信息：
    1. 获取合并前的面信息（核实面），因为只有核实面会绑定出入口信息
    2. 获取所有出入口并去重
    注意：若查询的 face_id 没有经过合并，也会返回有效结果，其出入口信息是可信的，但 name 和 address 信息是策略自动生成的，而非人工作业得到
    """
    sql_prev = """
        select distinct prev_face_ids, st_astext(baseline), st_astext(geom) from park_storefront_strategy
        where task_id = %s and face_id = %s and step = 'POST'
    """
    sql_access = """
        with aids as (
            select unnest(access_ids) aid from park_storefront_relation
            where task_id = %s and face_id in %s
        )
        select a.id, a.road_relation
        from park_storefront_access a
        inner join aids b on a.id = b.aid
    """

    def get_access_actual_id(road: dict):
        link_infos = road["link_info"]
        uuids = sorted(x["link_id"] if x.get("link_id", None) else x["node_id"] for x in link_infos)
        return ",".join(uuids)

    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql_prev, [task_id, face_id])
    # 理论上结果是唯一的，其它情况都有问题，不敢处理
    if not ret or len(ret) != 1:
        return None

    prev_face_ids, baseline, geom = ret[0]
    poi = try_get_primary_poi(geom)
    if not poi:
        return None

    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql_access, [task_id, tuple(prev_face_ids)])

    accesses = [((aid, road), get_access_actual_id(road)) for aid, road in ret]
    grouped = linq.group_by(accesses, key=lambda x: x[1], value=lambda x: x[0])
    to_retain_accesses = [x[0] for x in grouped.values()]

    park_name = get_park_name(poi.name)
    accesses = [
        ProdAccess(access_id=aid, name=get_access_name(park_name), address=poi.address, road_relation=road_relation)
        for aid, road_relation in to_retain_accesses
    ]
    return ProdParking(
        task_id=task_id,
        face_id=face_id,
        name=park_name,
        address=poi.address,
        baseline=baseline,
        geom=geom,
        accesses=accesses,
    )


def get_road_relation(park_geom: str):
    """
    获取与停车场关联的道路信息
    :param park_geom: 停车场的面，可以是 Polygon 或 MultiPolygon
    :return: 生成器，每个元素为元组：(link_id, point)
    """
    store_pois = get_relation_store_pois(park_geom, search_buffer=50 * METER)
    if len(store_pois) == 0:
        return

    park_geom = wkt.loads(park_geom)
    # FIXME: 这里坐标转换的精度有误差，但对于该需求而言，在允许的范围内，误差 1m 以内
    points_gcj = [Point(x, y).wkt for polygon in geometric.flat_polygon(park_geom) for x, y in polygon.exterior.coords]
    points_bd09 = [coord_trans.gcj02_to_bd09_wkt(w) for w in points_gcj]
    points_mc = [coord_trans.ll_to_mc_wkt(w) for w in points_bd09]
    points_mc = [wkt.loads(w) for w in points_mc]
    seen_link_ids = set()
    for store_poi in store_pois:
        link_info = _get_face_link(store_poi.bid)
        if link_info is None:
            continue

        link_id, link_geom = link_info
        if link_id in seen_link_ids:
            continue

        seen_link_ids.add(link_id)
        info = _get_kind_form_short_id(link_id)
        if info is None:
            continue

        kind, form = info
        if not (3 <= kind <= 9 and "52" not in form):
            continue

        link_geom = wkt.loads(link_geom)
        dists = [link_geom.project(p) for p in points_mc]
        center_dist = (max(dists) + min(dists)) / 2
        center_point: Point = link_geom.interpolate(center_dist)
        yield link_id, link_geom, center_point


def _normalize_park_name(name: str, prev_n: int) -> str:
    name = name.replace("-", "")
    if prev_n > 0:
        return f"{name}-P{prev_n + 1}门前停车场"
    else:
        return f"{name}-门前停车场"


def get_park_name(poi_name: str) -> str:
    """
    根据 POI 名称生成停车场名称
    """
    poi_name = poi_name.replace("-", "")
    return f"{poi_name}-门前停车场"


def get_access_name(park_name: str, suffix="入口") -> str:
    """
    根据停车场名称生成出入口名称
    """
    park_name = park_name.replace("-", "")
    return f"{park_name}-{suffix}"


def _get_park_number_from_name(name: str) -> int:
    m = re.search(r"(.+?)-(?:P(\d+?))?门前停车场", name)
    if m is None:
        return 0

    return int(m.group(2)) if m.group(2) else 0


def _get_blu_faces(geom: str) -> list[tuple[Poi, str]]:
    sql = """
        select b.poi_bid, st_astext(a.geom)
        from blu_face a
        inner join blu_face_poi b on a.face_id = b.face_id
        where st_intersects(a.geom, %s)
            and a.aoi_level = 2
            and a.src != 'SD'
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [f"SRID=4326;{geom}"])
    if not ret:
        return []

    bids = [bid for bid, _ in ret]
    pois = _get_pois_by_bid(bids)
    poi_dict = {poi.bid: poi for poi in pois}
    for bid, geom in ret:
        if bid not in poi_dict:
            logger.error(f"not found blu_face.poi_bid: {bid}")
        else:
            yield poi_dict[bid], geom


def _get_pois_by_bid(bids: list[str]) -> list[Poi]:
    sql = """
        select bid, name, std_tag, address, click_pv, st_astext(geometry), city 
        from poi
        where bid in %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [tuple(bids)])
    return [
        Poi(bid=bid, name=name, std_tag=std_tag, address=address, click_pv=click_pv, point=wkt.loads(geom), city=city)
        for bid, name, std_tag, address, click_pv, geom, city in ret
    ]


def _get_online_parks(geom: str) -> list[Park]:
    sql = """
        select bid, name, st_astext(area)
        from parking
        where show_tag = '门前停车场' and st_intersects(area, %s)
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [f"SRID=4326;{geom}"])
    return [Park(face_id=bid, name=name, geom=wkt.loads(geom)) for bid, name, geom in ret]


def get_relation_store_pois(geom: str, search_buffer: float) -> list[Poi]:
    """
    获取可垂直投影于指点 Polygon 的临街 POI（在 search_buffer 的范围内搜索）
    """
    sql_poi = """
        select a.bid, name, std_tag, address, click_pv, st_astext(geometry), city 
        from poi a
        inner join poi_spatial_classify b on a.bid = b.bid
        where st_contains(st_buffer(%s, %s), geometry)
            and b.classify like '临街POI%%'
    """
    sql_link = """
        select link_id from nav_link
        where kind <= 7 and st_intersects(geom, %s)
    """

    def is_cross_link(line: LineString):
        link = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql_link, [f"SRID=4326;{line.wkt}"])
        return link is not None

    # 获取全量的临街 POI
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql_poi, [f"SRID=4326;{geom}", search_buffer])
    pois = [
        Poi(bid=bid, name=name, std_tag=std_tag, address=address, click_pv=click_pv, point=wkt.loads(geom), city=city)
        for bid, name, std_tag, address, click_pv, geom, city in ret
    ]
    # 仅需要能够垂直投影到 polygon 上的点
    geom = wkt.loads(geom)
    polygons = geometric.flat_polygon(geom)
    geom_lines = [p.exterior for p in polygons]
    foots = [(p, geometric.get_foot_point(p.point, geom_line)) for p in pois for geom_line in geom_lines]
    foots = [(p, f) for p, f in foots if f]
    # 垂足连线不可跨高等级路
    foot_lines = [(p, LineString([p.point, f])) for p, f in foots]
    valid_pois = [p for p, l in foot_lines if not is_cross_link(l)]
    return valid_pois


def _get_face_link(bid: str) -> Optional[tuple[str, str]]:
    sql = """
        select link_id, st_astext(link_geom)
        from poi_face_link
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, [bid])
    return ret


def _get_kind_form_short_id(short_id: str):
    sql_long_id = """
        SELECT sid FROM image_r WHERE tid = %s 
    """
    sql_link = """
        select kind, form from nav_link
        where link_id = %s
    """
    ret = dbutils.fetch_one(pgsql.TRANS_ID, sql_long_id, [short_id])
    if not ret:
        return None

    long_id = ret[0]
    ret = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql_link, [long_id])
    return ret


@logger.catch
def main():
    """
    主函数
    """
    parks = _get_haidian_parks()

    work_dir = Path()
    poi_path = utils.ensure_path(work_dir / f"poi.{VERSION}.tsv")
    face_link_path = utils.ensure_path(work_dir / f"face_link.{VERSION}.tsv")
    error_path = utils.ensure_path(work_dir / f"error.{VERSION}.tsv")

    for park in tqdm(parks):
        poi = try_get_primary_poi(park.geom)
        if poi:
            park_name = _normalize_park_name(poi.name, prev_n=0)
            info = [park.face_id, park_name, poi.bid, poi.name, poi.address, poi.click_pv, poi.point.wkt, park.geom]
            tsv.write_tsv(poi_path, [info], mode="a")
        else:
            info = [park.face_id, "no poi", park.geom]
            tsv.write_tsv(error_path, [info], mode="a")

        # links = list(get_road_relation(park.geom))
        # if links:
        #     info = [[park.face_id, link_id, link_geom, center, park.geom] for link_id, link_geom, center in links]
        #     tsv.write_tsv(face_link_path, info, mode="a")
        # else:
        #     info = [park.face_id, "no link", park.geom]
        #     tsv.write_tsv(error_path, [info], mode="a")


if __name__ == "__main__":
    main()
