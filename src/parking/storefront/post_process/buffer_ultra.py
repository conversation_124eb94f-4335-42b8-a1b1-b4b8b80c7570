"""
对门前停车场进行 buffer，考虑 LD 裁切，采用 采样 - 投影 - 裁剪 的方式
"""
import shapely.ops
from shapely import wkt, Polygon, LineString
from tqdm import tqdm

from src.parking.recognition import dbutils
from src.parking.storefront.flow.model import road
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.tools import pgsql, tsv


def buffer_depend_on_street(geom: Polygon, street: Polygon, buffer: float):
    """
    buffer 门前停车场面（geom）到指定的宽度（buffer），考虑街区（street）的 LD 边缘
    """
    street = remove_polygon_holes(street)
    street = clip_by_ld(street)
    street = street.buffer(-2 * METER).buffer(2 * METER)
    line = project_to_street_line(geom, street)
    buffered_geom = line.buffer(buffer, cap_style="square")
    buffered_geom = buffered_geom.intersection(street)
    buffered_geom = buffered_geom.buffer(-1 * METER).buffer(1 * METER)
    return max(geometric.flat_polygon(buffered_geom), key=lambda x: x.area)


def project_to_street_line(polygon: Polygon, street: Polygon, sampling_step: float = 2 * METER):
    """
    获取 polygon 所投影的 street 边缘线
    """
    exterior_line = LineString(polygon.exterior.coords)
    sampling_pts = [
        exterior_line.interpolate(i * sampling_step) for i in range(int(round(exterior_line.length / sampling_step)))
    ]
    # tsv.write_tsv("debug.pts.tsv", [[x.wkt] for x in sampling_pts])
    street_line = LineString(street.exterior.coords)
    project_dists = [street_line.project(pt) for pt in sampling_pts]
    extend_step = sampling_step * 2
    project_ranges = [(max(d - extend_step, 0), min(d + extend_step, street_line.length)) for d in project_dists]
    project_ranges = merge_ranges(project_ranges)
    project_lines = [shapely.ops.substring(street_line, a, b) for a, b in project_ranges]
    # tsv.write_tsv("debug.line.tsv", [[x.wkt] for x in project_lines])
    project_lines = [x for x in project_lines if isinstance(x, LineString)]
    merged_line = shapely.ops.linemerge(project_lines)
    merged_line = max(geometric.flat_line(merged_line), key=lambda x: x.length) if not merged_line.is_empty else None
    return merged_line


def remove_polygon_holes(polygon: Polygon):
    """
    移除 Polygon 内的孔洞
    """
    return Polygon(polygon.exterior.coords)


def clip_by_ld(polygon: Polygon, search_buffer: float = 50 * METER):
    """
    裁掉传入 Polygon 的 LD 部分
    """
    search_geom = polygon.buffer(search_buffer)
    ld_2d_geom = road.get_full_ld_polygon(search_geom.wkt)
    ld_2d_geom = wkt.loads(ld_2d_geom)
    clipped_polygon = polygon.difference(ld_2d_geom)
    clipped_polygon = max(geometric.flat_polygon(clipped_polygon), key=lambda x: x.area)
    return clipped_polygon


def merge_ranges(ranges: list[tuple[float, float]]) -> list[tuple[float, float]]:
    """
    合并区间：将可能重叠的多个区间合并为连续的一个区间
    """
    ranges = [(min(a, b), max(a, b)) for a, b in ranges]
    ranges = sorted(ranges, key=lambda x: x[0])
    prev_range = ranges[0]
    result = []
    for next_range in ranges[1:]:
        prev_a, prev_b = prev_range
        next_a, next_b = next_range
        if next_a <= prev_b < next_b:
            prev_range = prev_a, next_b
        elif next_a > prev_b:
            result.append(prev_range)
            prev_range = next_range

    result.append(prev_range)
    return result


def get_street_region_by_id(strategy_id: int):
    """
    根据策略 ID 获取街区
    """
    sql = """
        select st_astext(a.geom)
        from park_storefront_task a
        join park_storefront_strategy b on a.task_id = b.task_id
        where b.id = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [strategy_id])
    return ret[0] if ret else None


def get_geom_by_id(strategy_id: int):
    """
    根据策略 ID 获取门前停车场面
    """
    sql = """
        select st_astext(geom) from park_storefront_strategy
        where id = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [strategy_id])
    return ret[0] if ret else None


def debug():
    """
    测试
    """
    cases = [
        2464710,
        115779,
        1969978,
        1633869,
    ]
    rows = []
    for strategy_id in tqdm(cases):
        geom = get_geom_by_id(strategy_id)
        if geom is None:
            continue

        street = get_street_region_by_id(strategy_id)
        if street is None:
            continue

        geom = wkt.loads(geom)
        street = wkt.loads(street)

        # print(geom.wkt)
        # print(street.wkt)
        buffered_geom = buffer_depend_on_street(geom, street, 20 * METER)
        row = [strategy_id, geom.wkt, buffered_geom.wkt]
        rows.append(row)

    tsv.write_tsv("case.tsv", rows)


if __name__ == "__main__":
    debug()
