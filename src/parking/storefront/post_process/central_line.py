"""
为规则的门前停车场面生成中心线
"""
import sys
from typing import Union

import numpy as np
import shapely.ops
from shapely import Polygon, LineString, MultiLineString, MultiPolygon, Point, wkt
from sklearn.cluster import DBSCAN

from src.parking.storefront.utils import geometric

METER = 1e-5


def get_central_line(
    polygon: Union[Polygon, MultiPolygon], baseline: Union[LineString, MultiLineString], buffer: float
) -> Union[LineString, MultiLineString]:
    """
    获取给定 polygon 的 central_line，可能会抛出异常，外部需要做异常处理
    :param polygon: 给定的 polygon，也可以是 MultiPolygon
    :param baseline: polygon 的基准线
    :param buffer: polygon 外侧线向内推行的距离
    :return: 中心线
    """
    if isinstance(polygon, Polygon):
        return _get_single_central_line(polygon, baseline, buffer)
    elif isinstance(polygon, MultiPolygon):
        baseline = shapely.ops.linemerge(baseline)
        polygons = geometric.flat_polygon(polygon)
        baselines = geometric.flat_line(baseline)
        lines = [_get_single_central_line(x, y, buffer) for x, y in _get_multi_pairs(polygons, baselines)]
        assert len(lines) == len(polygons)
        return MultiLineString(lines)
    else:
        raise ValueError(f"unexpected geom_type: {polygon.geom_type}")


def _get_multi_pairs(polygons: list[Polygon], baselines: list[LineString]):
    for polygon in polygons:
        for baseline in baselines:
            # noinspection PyBroadException
            try:
                _ = geometric.get_sidelines(polygon, baseline, eps=1 * METER)
                yield polygon, baseline
                continue
            except:
                continue


def _get_single_central_line(polygon: Polygon, baseline: LineString, buffer: float) -> LineString:
    sideline, i_line = geometric.get_sidelines(polygon, baseline, eps=1 * METER)

    sideline = geometric.trim_linestring(sideline, -0.5, 10 * METER)
    sideline = geometric.trim_linestring_for_buffer(sideline, (buffer + 1 * METER))
    sideline = geometric.trim_linestring_for_buffer(sideline, -(buffer + 1 * METER))
    sideline = geometric.extend_linestring(sideline, 1 * METER)

    center_point = i_line.interpolate(i_line.length / 2)
    is_left = geometric.is_left(center_point, sideline)
    buffer *= 1 if is_left else -1

    central_line = geometric.move_linestring(sideline, move_dist=buffer / 2)
    return central_line


# munual center line
def cal_bisectors(polygon: Polygon, scale=10000):
    """
    计算内角平分线段
    """
    coords = list(polygon.exterior.coords)
    bisectors = []

    # 遍历每个顶点，计算角平分线
    for i in range(0, len(coords) - 1):
        if i > 0:
            # 获取前后两个点和当前点
            p0 = np.array(coords[i - 1])  # 上一个点
            p1 = np.array(coords[i])  # 当前点
            p2 = np.array(coords[i + 1])  # 下一个点
        else:
            # 获取前后两个点和当前点
            p0 = np.array(coords[len(coords) - 1 - 1])  # 上一个点
            p1 = np.array(coords[i])  # 当前点
            p2 = np.array(coords[i + 1])  # 下一个点

        if np.all(p0 == p1) or np.all(p1 == p2):
            continue

        # 计算两个边的向量
        v1 = p0 - p1
        v2 = p2 - p1

        # 将向量标准化为单位向量
        v1_norm = v1 / np.linalg.norm(v1)
        v2_norm = v2 / np.linalg.norm(v2)

        # 计算角平分线的方向向量（单位向量）
        bisector = v1_norm + v2_norm
        bisector /= np.linalg.norm(bisector)

        # 判断角平分线是否指向多边形内部
        endpoint = p1 + bisector * scale
        base_line = LineString([p1, endpoint])
        intersect_points = base_line.intersection(polygon.boundary)
        point_p1 = Point(p1)
        if intersect_points.geom_type == "MultiPoint":
            min_dist = sys.float_info.max
            min_point = None
            for point in intersect_points.geoms:
                if point == point_p1:
                    continue
                if point.distance(point_p1) < min_dist:
                    min_point = point
                    min_dist = point.distance(point_p1)
            tmp_point = Point((point_p1.x + min_point.x) / 2, (point_p1.y + min_point.y) / 2)
            if not polygon.contains(tmp_point):
                bisector = -bisector
        elif intersect_points.geom_type == "Point":
            if intersect_points == point_p1:
                bisector = -bisector
            else:
                raise Exception(f"[cal_bisectors] intersect_points error point")
        else:
            raise Exception(f"[cal_bisectors] intersect_points error type")

        # 生成角平分线的线段
        endpoint = p1 + bisector * scale  # 角平分线的端点
        base_line = LineString([p1, endpoint])
        intersection = base_line.intersection(polygon.boundary)
        min_dist = sys.float_info.max
        min_point = None
        if intersection.geom_type == "MultiPoint":
            for point in intersection.geoms:
                if point == point_p1:
                    continue
                if point.distance(point_p1) < min_dist:
                    min_point = point
                    min_dist = point.distance(point_p1)
        elif intersection.geom_type == "Point":
            continue
        else:
            raise Exception(f"[cal_bisectors] intersect_points error type")

        bisectors.append(LineString([point_p1, min_point]))
    return bisectors


def incenter_radius(polygon, center):
    """
    计算给定圆心的位置下，多边形的内切圆半径
    """
    point = Point(center)
    boundary = polygon.exterior
    min_distance = point.distance(boundary)
    return min_distance


def max_incenter_radius(polygon, line_start, line_end, num_points=10, max_simple_length=2 * METER):
    """
    计算沿着角平分线段最大内切圆半径
    """
    # 直线上的若干点，假设在直线上均匀分布10个点进行计算, 如果长度大于2m，则动态分段
    line_distance = ((line_end[0] - line_start[0]) ** 2 + (line_end[1] - line_start[1]) ** 2) ** 0.5
    key_num = int(line_distance / max_simple_length)
    if key_num % 2 == 1:
        key_num += 1
    num_points = max(num_points, key_num)
    max_radius = 0
    max_point = None
    idx = -1

    # 计算直线上的点
    for i in range(num_points + 1):
        x = line_start[0] + (line_end[0] - line_start[0]) * i / num_points
        y = line_start[1] + (line_end[1] - line_start[1]) * i / num_points
        radius = incenter_radius(polygon, (x, y))
        if radius > max_radius:
            max_radius = radius
            max_point = (x, y)
            idx = i

    return max_radius, max_point, idx


def simple_points(points, simple_distance):
    """
    采样关键点
    """
    work_points = []
    for point in points:
        work_points.append((point.x, point.y))
    work_points = np.array(work_points)
    dbscan_cluster = DBSCAN(eps=simple_distance, min_samples=2)
    dbscan_cluster.fit(work_points)

    labels = dbscan_cluster.labels_

    # 合并每个聚类内的点为几何中心
    merged_points = []
    for label in set(labels):
        cluster_points = work_points[labels == label]
        if label == -1:
            for point in cluster_points:
                merged_points.append(Point(point))
            continue
        centroid = cluster_points.mean(axis=0)  # 几何中心
        merged_points.append(Point(centroid))

    return merged_points


def connect_points(row_points, polygon: Polygon):
    """
    连接关键点
    """

    def euclidean_distance(p1, p2):
        """
        计算距离
        """
        return np.sqrt((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2)

    def project_line(p0, pa, pb, row_polygon):
        """
        计算图像内部的垂线段
        """
        px, py = p0
        ax, ay = pa
        bx, by = pb
        # 计算 AB 向量
        abx, aby = bx - ax, by - ay
        apx, apy = px - ax, py - ay

        # 计算向量 AP 在 AB 上的投影比例 t
        ab_squared = abx**2 + aby**2
        if ab_squared == 0:  # 避免除零（A 和 B 重合）
            return False
        t = (apx * abx + apy * aby) / ab_squared

        # 计算垂足点坐标 F
        fx = ax + t * abx
        fy = ay + t * aby

        proj_line = LineString([(px, py), (fx, fy)])

        # 垂线不在面内则舍弃
        if not row_polygon.contains(proj_line):
            return False

        # 距离过长则舍弃
        if proj_line.length > 10 * 1e-5:
            return False

        # 判断 t 是否在 [0, 1] 范围内
        return 0 <= t <= 1

    points = [[p.x, p.y] for p in row_points]
    # 计算所有点之间的距离
    distances = []
    for i in range(len(points)):
        for j in range(i + 1, len(points)):
            new_line = LineString([points[i], points[j]])
            if not polygon.contains(new_line):
                continue
            dist = euclidean_distance(points[i], points[j])
            distances.append((dist, i, j))  # 保存距离和点的索引

    # 按距离从小到大排序
    distances.sort()

    # 记录每个点的连接数
    connections = {i: 0 for i in range(len(points))}

    # 记录连接的边
    edges = []

    # 逐步连接点，直到连接数达到限制
    for dist, i, j in distances:
        if connections[i] < 2 and connections[j] < 2:
            not_project = True
            # 如果存在其他点的垂线在直线上则不连接
            for idx, point in enumerate(points):
                if idx == i or idx == j:
                    continue
                if not project_line(point, points[i], points[j], polygon):
                    continue
                else:
                    not_project = False
                    break
            if not not_project:
                continue
            # 如果两个点的连接数都小于2，则连接它们
            edges.append((i, j))
            connections[i] += 1
            connections[j] += 1

    # edges
    start_node = None
    point_list = []
    for i in range(len(points)):
        if connections[i] == 1:
            start_node = i
            point_list.append(points[i])
            break
    if start_node is None:
        raise Exception(connections)
    added_list = edges
    while True:
        updated = False
        for idx, edge in enumerate(added_list):
            if start_node in edge:
                if edge[0] == start_node:
                    start_node = edge[1]
                else:
                    start_node = edge[0]
                point_list.append(points[start_node])
                added_list.pop(idx)
                updated = True
                break
        if not updated:
            break
    key_line = LineString(point_list)

    return key_line


def get_central_line_irregular(polygon: Polygon, simple_distance=2 * METER):
    """
    获取所有角平分线
    对每条角平分线计算最大内切圆
    获取圆心
    连接圆心
    """
    # 获取所有角平分线
    bisectors_list = cal_bisectors(polygon)

    # 对每条角平分线计算最大内切圆
    # 一次计算，循环多次计算或增加角平分线分段数，获取更精细位置，目前不需要
    circle_point = []
    radius_point = []
    for line in bisectors_list:
        if line.coords[0] == line.coords[-1]:
            continue
        max_radius, max_point, idx = max_incenter_radius(polygon, line.coords[0], line.coords[-1])
        circle_point.append(Point(max_point))
        radius_point.append(max_radius)

    # # 如果平均大于5 且小于5的只有1个则删除小于5的(存在case需剪枝，废弃，如果再出现可打开)
    # mean = np.array(radius_point).mean()
    # less_5 = [circle_point[idx] for idx, p in enumerate(radius_point) if p < 5*METER]
    # if len(less_5) < 2 and mean > 5*METER:
    #     circle_point = [circle_point[idx] for idx, p in enumerate(radius_point) if p > 5*METER]

    # 对圆形点进行采样
    filtered_circle_points = simple_points(circle_point, simple_distance)

    # 连接中心点
    key_line = connect_points(filtered_circle_points, polygon)

    # 质检 将因为连接不到部分中心点导致的中线生成失败的情况过滤
    key_buffer = key_line.buffer(15 * METER)
    itx = key_buffer.intersection(polygon)
    if itx.area / polygon.area < 0.8 and len(filtered_circle_points) > len(key_line.coords):
        return None
    return key_line


def main():
    """
    主函数
    """
    baseline = "TODO"
    polygon = "TODO"
    line = get_central_line(wkt.loads(polygon), wkt.loads(baseline), 8 * METER)
    print(line.wkt)


if __name__ == "__main__":
    main()
