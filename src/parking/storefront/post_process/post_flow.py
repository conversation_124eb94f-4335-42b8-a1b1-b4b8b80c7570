"""
后处理管线
"""
import math
from dataclasses import dataclass, field
from functools import partial
from multiprocessing.pool import Pool
from typing import Callable

import numpy as np
import shapely
import shapely.affinity
import shapely.ops
from loguru import logger
from shapely import wkt
from shapely.geometry import Point, Polygon, LineString
from tqdm import tqdm

from src.parking.recognition import dbutils
from src.parking.storefront import cluster_projection, storage, turnoff_point_cluster, prime_flow
from src.parking.storefront import task_flow
from src.parking.storefront.utils import union_find, geometric
from src.tools import linq, utils, tsv, pipeline, pgsql, turnoff_point
from src.trajectory.utils import coord_trans

# VERSION = "1.0.0"  # 初始化提交
# VERSION = "1.1.0"  # 合并入奥楠开发的出入口延长+熄火点位移策略
# VERSION = "1.2.0"  # 出入口延长策略延长阈值由（30m, 60m）到（20m, 60m）+先取消熄火点位移策略
# VERSION = "1.3.0"  # 迭代熄火点策略，增加生成面过小则不更新面
VERSION = "1.3.1"  # 人工作业面保送

METER = 1e-5
METER_2 = METER**2
STRATEGY_ID_INVALID = -1


@dataclass
class Access:
    """
    出入口
    """

    access_id: int
    uuid: str = field(init=False)
    road_relation: dict

    def __post_init__(self):
        def get_access_actual_id(road: dict):
            items = road["link_info"]
            uuids = sorted(x["link_id"] if x.get("link_id", None) else x["node_id"] for x in items)
            return ",".join(uuids)

        self.uuid = get_access_actual_id(self.road_relation)


@dataclass
class Parking:
    """
    核实面
    """

    strategy_id: int
    face_id: str
    geom: str
    accesses: list[Access]
    prev_ids: list[int]
    prev_face_ids: list[str]
    tags: list[str] = field(default_factory=list)

    def __hash__(self):
        return hash(self.face_id)

    def __eq__(self, other):
        return self.face_id == other.face_id


@dataclass
class Context:
    """
    上下文
    """

    task_id: int
    parkings: list[Parking]
    error: str = field(default="")


def merge_by_same_access(ctx: Context, proceed):
    """
    合并同一出入口的停车场
    """

    def merge(parks: list[Parking]):
        parks = [(a.uuid, p) for p in parks for a in p.accesses]
        grouped_parks = linq.group_by(parks, key=lambda x: x[0], value=lambda x: x[1])
        grouped_parks = [set(parks) for parks in grouped_parks.values()]
        multi_parks = [parks for parks in grouped_parks if len(parks) > 1]
        single_parks = {parks.pop() for parks in grouped_parks if len(parks) == 1}
        single_parks = {p for p in single_parks if all(p not in mp for mp in multi_parks)}
        yield from single_parks

        multi_park_dict = {p.face_id: p for parks in multi_parks for p in parks}

        multi_park_ids = [{p.face_id for p in parks} for parks in multi_parks]
        merged_park_ids = union_find.merge_sets(multi_park_ids)
        for park_ids in merged_park_ids:
            merged_parks = [multi_park_dict[park_id] for park_id in park_ids]
            union_geom = shapely.unary_union([wkt.loads(p.geom) for p in merged_parks])
            union_geom = union_geom.simplify(0.01 * METER)
            face_id = utils.md5(union_geom.wkt)
            accesses = [a for p in merged_parks for a in p.accesses]
            merged_accesses = linq.group_by(accesses, key=lambda x: x.uuid)
            distinct_accesses = [values[0] for values in merged_accesses.values()]
            prev_tags = [tag for p in merged_parks for tag in p.tags]
            yield Parking(
                strategy_id=STRATEGY_ID_INVALID,
                face_id=face_id,
                geom=union_geom.wkt,
                accesses=distinct_accesses,
                prev_ids=[x.strategy_id for x in merged_parks],
                prev_face_ids=list(park_ids),
                tags=[*prev_tags, "merge_by_same_access"],
            )

    no_accesses = [p for p in ctx.parkings if not p.accesses]
    has_accesses = [p for p in ctx.parkings if p.accesses]
    ctx.parkings = [*no_accesses, *merge(has_accesses)]
    proceed()


@dataclass
class Projection:
    """
    修形投影信息
    """

    width: float
    line: LineString
    geom: Polygon
    tags: list[str] = field(default_factory=list)

    def __post_init__(self):
        assert not self.geom.is_empty

    @property
    def face_id(self) -> str:
        """
        获取面 id
        """
        return utils.md5(self.geom.wkt)


@dataclass
class ContextRefine:
    """
    修形上下文信息
    """

    street_region: Polygon = None
    projections: list[Projection] = field(default_factory=list)


def get_baseline(face_id):
    """
    获取基线
    """
    sql = """
        select st_astext(s.baseline), st_astext(t.geom) from park_storefront_strategy s inner join park_storefront_task t on s.task_id = t.task_id where s.face_id = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [face_id])
    if len(ret) == 0:
        return None, None
    return ret[0]


def lengthen_by_access(ctx: Context, proceed, search_min_distance, search_max_distance, final_distance):
    """
    通过出入口延长base_line
        计算较远出入口
        获取基线，街区边线
        计算面上点到基线的距离
        计算出入口延伸投影
        延伸基线
    """

    def move_to_point(start_point, end_point, ratio):
        """
        计算需要移动到的点位
        """
        dx = (end_point.x - start_point.x) * ratio
        dy = (end_point.y - start_point.y) * ratio
        point_x = start_point.x + dx
        point_y = start_point.y + dy
        return Point(point_x, point_y)

    for park in tqdm(ctx.parkings, total=len(ctx.parkings), desc="lengthen_by_access", disable=True):
        park_geom = wkt.loads(park.geom)
        if park_geom.geom_type == "MultiPolygon":
            continue
        far_access = []
        # 计算较远出入口
        for access in park.accesses:
            if access.road_relation is None:
                continue
            link_info = access.road_relation["link_info"]
            if len(link_info) < 1:
                continue
            point_mc = link_info[0]["point"].split(",")
            point_gc02_wkt = coord_trans.bd09_to_gcj02_wkt(
                coord_trans.mc_to_ll_wkt(Point(point_mc[0], point_mc[1]).wkt)
            )
            point_gc02_geom = wkt.loads(point_gc02_wkt)
            if search_max_distance > point_gc02_geom.distance(park_geom) > search_min_distance:
                access.point_gc02_geom = point_gc02_geom
                far_access.append(access)
        if len(far_access) < 1:
            continue

        # 获取基线和街区边线
        baseline, street_geom = get_baseline(park.face_id)
        if not baseline or not street_geom or "LINESTRING EMPTY" in baseline:
            continue
        baseline = wkt.loads(baseline)
        street_geom = wkt.loads(street_geom)

        try:
            outside_line, _ = geometric.get_sidelines(park_geom, baseline)
            head_point = Point(outside_line.coords[0])
            head_projection_length = outside_line.project(head_point)
            head_project_point = outside_line.interpolate(head_projection_length)

            end_point = Point(outside_line.coords[-1])
            end_projection_length = outside_line.project(end_point)
            end_project_point = outside_line.interpolate(end_projection_length)

            new_points = [head_project_point, end_project_point]
            street_line = cluster_projection.get_linestring_from_full_polygon(street_geom, new_points)
            new_line, _ = cluster_projection.get_projection(street_line, new_points)
            baseline = new_line
        except Exception as e:
            continue

        # 计算面上点到基线的距离，得到街区边缘合理范围
        boundary_points = list(park_geom.exterior.coords)
        distances = [baseline.distance(Point(point)) for point in boundary_points]
        max_distance = max(distances)
        min_distance = min(distances)
        sum_distance = max_distance + min_distance

        # 获取街区边缘合理范围内的点
        buffer_street_geom = street_geom.exterior.buffer(sum_distance)
        filtered_access = [access for access in far_access if access.point_gc02_geom.within(buffer_street_geom)]
        if len(filtered_access) < 1:
            continue

        # 确定每个出入口的延伸方向
        #   距离头更近且投影点在基线之外则是头侧延伸
        #     计算需要移动到的点
        #     计算投影点
        #     切分线段
        #     计算线段长度
        #     选择最长线段
        #   距离尾更近且投影点在基线之外则是尾侧延伸
        #     同上，计算投影点到基线尾的最大值
        head_add_line = None
        end_add_line = None
        head_point = Point(baseline.coords[0])
        end_point = Point(baseline.coords[-1])
        lengthen_head_point = None
        lengthen_end_point = None
        for far_access in filtered_access:
            access_to_head = head_point.distance(far_access.point_gc02_geom)
            access_to_end = end_point.distance(far_access.point_gc02_geom)
            ratio = 1 - final_distance / far_access.point_gc02_geom.distance(park_geom)
            if access_to_head < access_to_end:
                move_point = move_to_point(head_point, far_access.point_gc02_geom, ratio)
                project_point = shapely.ops.nearest_points(street_geom.exterior, move_point)
                project_point = project_point[0]
                if project_point.distance(baseline) < 0.1 * METER:
                    continue
                new_points = [project_point, head_point]
                street_line = cluster_projection.get_linestring_from_full_polygon(street_geom, new_points)
                new_line, _ = cluster_projection.get_projection(street_line, new_points)
                if head_add_line is None:
                    head_add_line = new_line
                    lengthen_head_point = project_point
                else:
                    if head_add_line.length < new_line.length:
                        head_add_line = new_line
                        lengthen_head_point = project_point
            else:
                move_point = move_to_point(end_point, far_access.point_gc02_geom, ratio)
                project_point = shapely.ops.nearest_points(street_geom.exterior, move_point)
                project_point = project_point[0]
                if project_point.distance(baseline) < 0.1 * METER:
                    continue
                new_points = [project_point, end_point]
                street_line = cluster_projection.get_linestring_from_full_polygon(street_geom, new_points)
                new_line, _ = cluster_projection.get_projection(street_line, new_points)
                if end_add_line is None:
                    end_add_line = new_line
                    lengthen_end_point = project_point
                else:
                    if end_add_line.length < new_line.length:
                        end_add_line = new_line
                        lengthen_end_point = project_point

        # 存在前后延长线则延长
        if head_add_line or end_add_line:
            # 获取全量的线段点，生成新直线
            new_base_line_list = []
            if head_add_line:
                new_base_line_list.append(lengthen_head_point)
            for point in baseline.coords:
                new_base_line_list.append(Point(point))
            if end_add_line:
                new_base_line_list.append(lengthen_end_point)
            street_line = cluster_projection.get_linestring_from_full_polygon(street_geom, new_base_line_list)
            new_base_line, _ = cluster_projection.get_projection(street_line, new_base_line_list)
            # 修形
            pipe2 = pipeline.Pipeline(
                partial(prime_flow.clip_by_bud_face, min_width=20 * METER),
                prime_flow.clip_by_ld_road,
                partial(prime_flow.repair_by_side_road, width=20 * METER, road_distance=0 * METER),
                prime_flow.clip_by_ld_road,
                partial(prime_flow.repair_by_side_road, width=10 * METER, road_distance=2 * METER),
            )
            ctx_refine = ContextRefine()
            ctx_refine.street_region = street_geom
            key_new_base_line = new_base_line
            if not geometric.is_left_polygon(park_geom, new_base_line):
                key_new_base_line = new_base_line.reverse()
            new_park_geom = key_new_base_line.buffer(10 * METER, cap_style="flat", single_sided=True)
            ctx_refine.projections.append(Projection(width=10 * METER, line=new_base_line, geom=new_park_geom))
            pipe2(ctx_refine)

            # 如果生成的面位移变化较大则放弃
            if ctx_refine.projections[0].geom.intersection(park_geom).area > 0.5 * park_geom.area:
                park.geom = ctx_refine.projections[0].geom.wkt
                park.prev_face_ids = [park.face_id]
                park.face_id = utils.md5(park.geom)
                park.prev_ids = [park.strategy_id]
                park.strategy_id = STRATEGY_ID_INVALID
                park.tags.append("lengthen_by_access")

    proceed()


def move_by_turnoff_point_common(ctx: Context, proceed):
    """
    通过熄火点移动
    """
    pipe2 = pipeline.Pipeline(
        partial(move_by_turnoff_point, provider=turnoff_point_cluster.get_doctor_points),
        clip_by_bud_face_extra,
        partial(prime_flow.clip_by_bud_face, min_width=20 * METER),
        prime_flow.clip_by_ld_road,
        partial(prime_flow.repair_by_side_road, width=20 * METER, road_distance=0 * METER),
        prime_flow.clip_by_ld_road,
        partial(prime_flow.repair_by_side_road, width=10 * METER, road_distance=2 * METER),
    )
    for park in tqdm(ctx.parkings, total=len(ctx.parkings), desc="move_by_turnoff_point_common", disable=True):
        ctx_refine = ContextRefine()
        # 获取基线和街区边线
        baseline, street_geom = get_baseline(park.face_id)
        if not baseline or not street_geom or "LINESTRING EMPTY" in baseline or "MULTI" in baseline:
            continue

        baseline = wkt.loads(baseline)
        street_geom = wkt.loads(street_geom)
        park_geom = wkt.loads(park.geom)

        try:
            outside_line, _ = geometric.get_sidelines(park_geom, baseline)
            head_point = Point(outside_line.coords[0])
            head_projection_length = outside_line.project(head_point)
            head_project_point = outside_line.interpolate(head_projection_length)

            end_point = Point(outside_line.coords[-1])
            end_projection_length = outside_line.project(end_point)
            end_project_point = outside_line.interpolate(end_projection_length)

            new_points = [head_project_point, end_project_point]
            street_line = cluster_projection.get_linestring_from_full_polygon(street_geom, new_points)
            new_line, _ = cluster_projection.get_projection(street_line, new_points)
            baseline = new_line
        except Exception as e:
            continue

        ctx_refine.street_region = street_geom
        ctx_refine.projections.append(Projection(width=10 * METER, line=baseline, geom=wkt.loads(park.geom)))
        pipe2(ctx_refine)
        if "clip_by_bud_face_extra.succ" in ctx_refine.projections[0].tags:
            if ctx_refine.projections[0].geom.area < park_geom.area * 0.5:
                continue
            park.geom = ctx_refine.projections[0].geom.wkt
            park.prev_face_ids = [park.face_id]
            park.face_id = utils.md5(park.geom)
            park.prev_ids = [park.strategy_id]
            park.strategy_id = STRATEGY_ID_INVALID
            park.tags.append("move_by_turnoff_point_common")

    proceed()


def move_by_turnoff_point(ctx: ContextRefine, proceed, provider: Callable[[str], list[turnoff_point.TurnoffPoint]]):
    """
    通过熄火点移动
        计算建筑物到基线的距离
        生成buffer
        计算附近熄火点
        判断是否需要根据熄火点移动
        计算投影
        计算移动距离和方向
        移动
    """

    def move_direction(pairs: list[tuple], geom: Polygon, point_geom: Polygon, bud_geoms: list[Polygon]):
        """
        获取移动向量
        Args:
        pairs: 两点对
        geom: 当前面
        point_geom: 附近点
        Returns:
        tuple: 移动向量
        """
        # geom_union = shapely.ops.unary_union(bud_geoms)
        max_itx = 0
        max_pair = None
        for p1, p2 in pairs:
            dx = p1.x - p2.x
            dy = p1.y - p2.y
            new_geom = shapely.affinity.translate(geom, dx, dy)
            # if geom_union.intersects(new_geom):
            #     continue
            itx = new_geom.intersection(point_geom).area
            if itx > max_itx:
                max_itx = itx
                max_pair = (p1, p2)
        dx = max_pair[0].x - max_pair[1].x
        dy = max_pair[0].y - max_pair[1].y
        return dx, dy

    def search_nearby_road_in_area(geom: str):
        sql = """
            select kind form
            from nav_link
            where (kind = 8 or kind = 10) and st_intersects(geom, %s);;
        """
        ret = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql, [f"SRID=4326;{geom}"])
        road_kind = []
        for row in ret:
            road_kind.append(row[0])
        return road_kind

    def separate_line(key_line: LineString, angle: int):
        """
        根据角度分割线段
        Args:
        key_line: 线段
        angle: 角度
        Returns:
        list[LineString]: 分割后的线段列表
        """
        new_line_list = []
        fp = key_line.coords[0]
        fp = Point(fp[0], fp[1])
        line = [fp]
        total_angle = 0
        for i in range(1, len(key_line.coords) - 1):
            cp = key_line.coords[i]
            ep = key_line.coords[i + 1]
            cp = Point(cp[0], cp[1])
            ep = Point(ep[0], ep[1])
            cf_x, cf_y = (fp.x - cp.x) / METER, (fp.y - cp.y) / METER
            ce_x, ce_y = (ep.x - cp.x) / METER, (ep.y - cp.y) / METER

            # 计算点积和模
            dot_product = cf_x * ce_x + cf_y * ce_y
            magnitude_cf = math.sqrt(cf_x**2 + cf_y**2)
            magnitude_ce = math.sqrt(ce_x**2 + ce_y**2)

            # 计算夹角的余弦值
            cos_theta = dot_product / (magnitude_cf * magnitude_ce)

            # 确保 cos_theta 在 [-1, 1] 范围内（避免浮点误差）
            cos_theta = abs(max(-1, min(1, cos_theta)))

            # 计算角度（弧度 -> 角度）
            line_angle = math.acos(cos_theta) * (180 / math.pi)
            total_angle += line_angle

            line.append(cp)
            if total_angle > angle:
                new_line_list.append(line)
                line = [cp]
                total_angle = 0
            fp = cp
        if len(line) == 1:
            ep = key_line.coords[-1]
            ep = Point(ep[0], ep[1])
            line.append(ep)
        new_line_list.append(line)
        line_list = [LineString(l) for l in new_line_list]
        return line_list

    def search_turnoff_poi_vertical_inner_road(pairs: list[tuple]):
        """
        计算熄火点到边界与道路（10，8）相交且接近垂直的数量
            计算熄火点垂线
            求相交道路
                大于2？
            求相交道路夹角
        """
        sql = """
            with t as (select st_geomfromtext(%s, 4326) as g)
            select st_astext(geom) from nav_link, t where kind in (10, 8) and st_intersects(geom, t.g);
        """
        total_num = 0
        for pair in pairs:
            if total_num > 4:
                break
            turnoff_poi_line = LineString([pair[0], pair[1]])
            ret = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql, [turnoff_poi_line.wkt])
            if len(ret) < 2:
                continue
            turnoff_vector = ((pair[1].x - pair[0].x) / METER, (pair[1].y - pair[0].y) / METER)
            for r in ret:
                road_geom = wkt.loads(r[0])
                road_vector = (
                    (road_geom.coords[-1][0] - road_geom.coords[0][0]) / METER,
                    (road_geom.coords[-1][1] - road_geom.coords[0][1]) / METER,
                )
                dot_product = turnoff_vector[0] * road_vector[0] + turnoff_vector[1] * road_vector[1]
                turnoff_vector_length = math.sqrt(turnoff_vector[0] ** 2 + turnoff_vector[1] ** 2)
                road_vector_length = math.sqrt(road_vector[0] ** 2 + road_vector[1] ** 2)
                cos_theta = dot_product / (turnoff_vector_length * road_vector_length)
                # 确保 cos_theta 在 [-1, 1] 范围内（避免浮点误差）
                cos_theta = abs(max(-1, min(1, cos_theta)))
                line_angle = math.acos(cos_theta) * (180 / math.pi)

                if 100 > line_angle > 80:
                    total_num += 1
        return total_num

    point_buffer = 1 * METER
    for projection in ctx.projections:
        if projection.geom.geom_type == "MultiPolygon":
            continue
        bud_faces = storage.get_bud_faces(projection.geom.buffer(50 * METER).wkt)
        bud_geoms = [wkt.loads(x) for x in bud_faces]
        bud_infos = [(x, x.area, projection.line.distance(x)) for x in bud_geoms]
        bud_geoms = [geom for geom, area, dist in bud_infos if area > 100 * METER_2 or dist > 10 * METER]
        if not bud_geoms:
            continue

        # 面已经和建筑物相交则不再考虑
        is_intersect = False
        for geom in bud_geoms:
            if projection.geom.intersects(geom):
                is_intersect = True
                break
        if is_intersect:
            continue

        split_line = projection.line
        if not geometric.is_left_polygon(projection.geom, projection.line):
            split_line = projection.line.reverse()
        lines_list = separate_line(split_line, angle=60)
        geom_list = []
        for line in lines_list:
            distances = np.array([line.distance(bud_geom) for bud_geom in bud_geoms])
            width = max(min(distances), 15 * METER)
            geom = line.buffer(width, cap_style="flat", single_sided=True)
            split_geoms = shapely.ops.split(geom, split_line)
            for split_geom in split_geoms.geoms:
                if geometric.is_left_polygon(split_geom, split_line):
                    geom_list.append(split_geom)
        geom_union = shapely.unary_union(geom_list)
        geom_union = geom_union.buffer(0.2 * METER).buffer(-0.2 * METER)

        turnoff_points = provider(geom_union.wkt)
        points = [x.point for x in turnoff_points]
        if not points or len(points) < 20:
            continue

        # 判断是否需要根据熄火点移动
        # 熄火点相交面积
        _, pairs = cluster_projection.get_projection(projection.line, points)

        filtered_points = []
        for p1, p2 in pairs:
            line = LineString([p1, p2])
            if any(geom.intersection(line) and not geom.contains(p1) and not geom.contains(p2) for geom in bud_geoms):
                continue

            filtered_points.append((p1, p2))

        points = filtered_points
        pts_buffers = [p1.buffer(point_buffer) for p1, _p2 in points]
        point_geom = shapely.unary_union(pts_buffers)
        itx = projection.geom.intersection(point_geom).area
        if itx / projection.geom.area > 0.5:
            continue

        # 熄火点到建筑物距离小于道路到建筑物距离平均距离的一半
        turnoff_poi_distance = np.array([projection.line.distance(x) for x in points])
        bud_poi_distance = np.array([projection.line.distance(x) for x in bud_geoms])
        # print(turnoff_poi_distance.mean(), bud_poi_distance.mean())
        if turnoff_poi_distance.mean() < bud_poi_distance.mean() / 3:
            continue
            # 存在道路类型为 10 和 8
        road_kind = search_nearby_road_in_area(geom_union)
        if not (10 in road_kind and 8 in road_kind):
            continue

        # 存在熄火点到边界与道路（10，8）相交且接近垂直
        correct_number = search_turnoff_poi_vertical_inner_road(pairs)
        # print(correct_number)
        if correct_number < 2:
            continue

        # 计算移动距离
        move_x, move_y = move_direction(pairs, projection.geom, point_geom, bud_geoms)
        new_geom = shapely.affinity.translate(projection.geom, move_x, move_y)
        projection.geom = new_geom
        projection.tags.append("move_by_turnoff_point.succ")

    proceed()


def clip_by_bud_face_extra(ctx: ContextRefine, proceed):
    """
    使用建筑物信息特殊修形
    """

    def separate_line(key_line: LineString, angle: int):
        """
        根据角度分割线段
        Args:
        key_line: 线段
        angle: 角度
        Returns:
        list[LineString]: 分割后的线段列表
        """
        new_line_list = []
        fp = key_line.coords[0]
        fp = Point(fp[0], fp[1])
        line = [fp]
        total_angle = 0
        for i in range(1, len(key_line.coords) - 1):
            cp = key_line.coords[i]
            ep = key_line.coords[i + 1]
            cp = Point(cp[0], cp[1])
            ep = Point(ep[0], ep[1])
            cf_x, cf_y = (fp.x - cp.x) / METER, (fp.y - cp.y) / METER
            ce_x, ce_y = (ep.x - cp.x) / METER, (ep.y - cp.y) / METER

            # 计算点积和模
            dot_product = cf_x * ce_x + cf_y * ce_y
            magnitude_cf = math.sqrt(cf_x**2 + cf_y**2)
            magnitude_ce = math.sqrt(ce_x**2 + ce_y**2)

            # 计算夹角的余弦值
            cos_theta = dot_product / (magnitude_cf * magnitude_ce)

            # 确保 cos_theta 在 [-1, 1] 范围内（避免浮点误差）
            cos_theta = abs(max(-1, min(1, cos_theta)))

            # 计算角度（弧度 -> 角度）
            line_angle = math.acos(cos_theta) * (180 / math.pi)
            total_angle += line_angle

            line.append(cp)
            if total_angle > angle:
                new_line_list.append(line)
                line = [cp]
                total_angle = 0
            fp = cp
        if len(line) == 1:
            ep = key_line.coords[-1]
            ep = Point(ep[0], ep[1])
            line.append(ep)
        new_line_list.append(line)
        line_list = [LineString(l) for l in new_line_list]
        return line_list

    def _clip_baseline(baseline: LineString, polygon: Polygon):
        """
        根据 Polygon 投影截断 baseline
        """
        geom_points = [Point(*p) for p in polygon.exterior.coords]
        line, _ = cluster_projection.get_projection(baseline, geom_points)
        return line

    for projection in ctx.projections:
        if not "move_by_turnoff_point.succ" in projection.tags:
            continue
        bud_faces = storage.get_bud_faces(projection.geom.buffer(50 * METER).wkt)
        bud_geoms = [wkt.loads(x) for x in bud_faces]
        bud_infos = [(x, x.area, projection.line.distance(x)) for x in bud_geoms]
        bud_geoms = [geom for geom, area, dist in bud_infos if area > 100 * METER_2 or dist > 10 * METER]
        if not bud_geoms:
            continue

        split_line = projection.line
        if not geometric.is_left_polygon(projection.geom, projection.line):
            split_line = projection.line.reverse()
        lines_list = separate_line(split_line, angle=60)
        geom_list = []
        for line in lines_list:
            distances = np.array([line.distance(bud_geom) for bud_geom in bud_geoms])
            width = max(min(distances), 15 * METER)
            geom = line.buffer(width, cap_style="flat", single_sided=True)
            split_geoms = shapely.ops.split(geom, split_line)
            for split_geom in split_geoms.geoms:
                if geometric.is_left_polygon(split_geom, split_line):
                    geom_list.append(split_geom)
        geom_union = shapely.unary_union(geom_list)
        geom_union = geom_union.buffer(0.2 * METER).buffer(-0.2 * METER)

        projection.geom = projection.geom.intersection(geom_union)
        projection.line = _clip_baseline(projection.line, geom_union)
        projection.width = projection.width
        projection.tags.append("clip_by_bud_face_extra.succ")

    proceed()


# helpers:


def get_verified_polygons_by_task_id(task_id: int):
    """
    根据 task_id 获取该街区下所有核实面，用于合并
    """
    sql_strategy = """
        select a.id, b.type, a.face_id, a.prev_face_ids, st_astext(a.geom)
        from park_storefront_strategy a
        inner join park_storefront_task b on a.task_id = b.task_id
        left join park_storefront_manual_check c on a.id = c.strategy_id
        where 1 = 1 
            and a.step = 'VERIFIED'
            -- 人工作业是查不到核实结论的，但也需要查询出来
            and (c.recall is null or (c.recall = 'yes' and c.precise = 'yes'))
            and a.task_id = %s
    """
    sql_access = """
        with aids as (
            select unnest(access_ids) aid from park_storefront_relation
            where task_id = %s and face_id = %s
        )
        select a.id, a.road_relation
        from park_storefront_access a
        inner join aids b on a.id = b.aid
    """

    def get_accesses(tid: int, fid: str):
        accesses = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql_access, [tid, fid])
        for access_id, road_relation in accesses:
            yield Access(access_id=access_id, road_relation=road_relation)

    parks = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql_strategy, [task_id])
    for strategy_id, polygon_type, face_id, prev_face_ids, geom in parks:
        yield Parking(
            strategy_id=strategy_id,
            face_id=face_id,
            prev_ids=[strategy_id],
            prev_face_ids=prev_face_ids,
            geom=geom,
            accesses=list(get_accesses(task_id, face_id)),
            tags=["manual"] if polygon_type == "manual" else [],
        )


flow_pipe = pipeline.Pipeline(
    merge_by_same_access,
    # move_by_turnoff_point_common,
    partial(
        lengthen_by_access, search_min_distance=20 * METER, search_max_distance=60 * METER, final_distance=0 * METER
    ),
)


def process(task_id: int):
    """
    处理任务，为多线程任务调用
    """
    polygons = list(get_verified_polygons_by_task_id(task_id))
    # 只有策略面要参与合并，人工作业面不要参与合并，但需要保送到 POST step
    strategy_polygons = [x for x in polygons if "manual" not in x.tags]
    manual_polygons = [x for x in polygons if "manual" in x.tags]
    ctx = Context(task_id=task_id, parkings=strategy_polygons)
    try:
        flow_pipe(ctx)
    except Exception as e:
        ctx.error = str(e)

    ctx.parkings.extend(manual_polygons)
    return ctx


@logger.catch
def main():
    """
    主函数
    """
    work_dir = task_flow.get_output_dir("output", "post", VERSION)
    task_ids = task_flow.get_task_ids("GATE_MATCHED")

    error_path = utils.ensure_path(work_dir / "error.tsv")
    post_path = utils.ensure_path(work_dir / "post.tsv")

    with Pool(64) as pool:
        for ctx in tqdm(pool.imap_unordered(process, task_ids), total=len(task_ids)):
            if ctx.error:
                tsv.write_tsv(error_path, [(ctx.task_id, ctx.error)], mode="a")
                continue

            rows = [
                (
                    ctx.task_id,
                    ",".join(map(str, sorted(p.prev_ids))),
                    p.strategy_id,
                    ",".join(sorted(p.prev_face_ids)) if p.tags else p.face_id,
                    p.face_id,
                    ",".join(p.tags) if p.tags else "no_changed",
                    p.geom,
                )
                for p in ctx.parkings
            ]
            rows.sort(key=lambda x: x[0], reverse=True)
            tsv.write_tsv(post_path, rows, mode="a")


if __name__ == "__main__":
    main()
