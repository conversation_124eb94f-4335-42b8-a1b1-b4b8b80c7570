"""
后处理面入库操作
"""
import sys
from pathlib import Path

import psycopg2.extras
import shapely.ops
from loguru import logger
from psycopg2.extras import <PERSON><PERSON>
from shapely import wkt

from src.parking.recognition import dbutils
from src.parking.storefront import task_flow
from src.parking.storefront.post_process import post_flow
from src.parking.storefront.utils import geometric
from src.tools import tsv, pgsql

STRATEGY = f"post.merge-{post_flow.VERSION}"


def save_post(post_path: Path):
    """
    保存后处理面到策略履历库
    """
    idx_task_id = 0
    idx_prev_ids = 1
    idx_prev_face_ids = 3
    idx_face_id = 4
    idx_tags = 5
    idx_geom = -1

    cases = list(tsv.read_tsv(post_path))
    strategy_ids = [int(strategy_id) for x in cases for strategy_id in x[idx_prev_ids].split(",")]
    info_dict = _get_info_by_strategy_id(strategy_ids)

    def get_value(case: tuple):
        infos = [info_dict[int(i)] for i in case[idx_prev_ids].split(",")]
        if not infos:
            print(f"{case[idx_face_id]} not found.")
            return None

        baselines = list({info[1] for info in infos})
        if len(baselines) > 1:
            lines = [single for line in baselines for single in geometric.flat_line(wkt.loads(line))]
            baseline = shapely.ops.unary_union(lines).wkt
        elif len(baselines) == 1:
            baseline = baselines[0]
        else:
            raise ValueError("baselines < 1")

        value = [
            int(case[idx_task_id]),
            case[idx_prev_face_ids].split(","),
            case[idx_face_id],
            f"SRID=4326;{baseline}",
            f"SRID=4326;{case[idx_geom]}",
            STRATEGY,
            "POST",
            Json({"process": case[idx_tags]}),
        ]
        return value

    sql = """
        insert into park_storefront_strategy (task_id, prev_face_ids, face_id, baseline, geom, strategy, step, result)
        values %s
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cur:
        # 1. 插入后处理面
        values = [get_value(x) for x in cases]
        values = [x for x in values if x]
        print(f"{len(values)} values.")
        psycopg2.extras.execute_values(cur, sql, values, page_size=1000)

        # 2. 更新任务状态
        done_task_ids = {int(x[idx_task_id]) for x in cases}
        print(f"{len(done_task_ids)} tasks done.")
        task_flow.update_task_status(conn, done_task_ids, from_status="GATE_MATCHED", to_status="POSTED")
        conn.commit()


def _get_info_by_strategy_id(strategy_ids: list[int]):
    sql = """
        select id, face_id, st_astext(baseline) from park_storefront_strategy
        where id in %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(strategy_ids)])
    return {strategy_id: (face_id, line) for strategy_id, face_id, line in ret}


@logger.catch
def main(mode: str, file_path: Path):
    """
    主函数
    """
    if mode == "post":
        save_post(file_path)
    else:
        raise ValueError(f"'{mode}' not supported")


if __name__ == "__main__":
    main(sys.argv[1], Path(sys.argv[2]))
