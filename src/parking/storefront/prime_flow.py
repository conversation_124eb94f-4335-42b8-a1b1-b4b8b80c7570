"""
门前停车场：面生成策略
"""
import math
from collections import defaultdict
from dataclasses import dataclass, field
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Callable

import cv2
import numpy as np
import shapely
import shapely.affinity
import shapely.ops
from loguru import logger
from shapely import wkt, LineString, Point, Polygon, MultiPolygon
from tqdm import tqdm

from src.aikit import preview_image, boundary, satellite_imagery
from src.fix_geom_process import tools
from src.parking.recognition import dbutils
from src.parking.storefront import storage, cluster_projection, polygon_strategy, verify
from src.parking.storefront import turnoff_point_cluster
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER, METER_2
from src.parking.storefront.verify import ViewPoint, DefaultViewPoint
from src.tools import utils, tsv, pipeline, turnoff_point, pgsql

# VERSION = "1.0.0"  # 初始化提交
# VERSION = "1.2.1"  # 20241206 版本，用于海淀+北京上量
# VERSION = "1.3.0"  # 环形面打断、sideline 提取提供中心线备选方案
# VERSION = "2.0.0"  # 去除 LD 裁切、辅路避让功能，使用新的修形方案替代
VERSION = "2.0.1"  # 去除街区内部的洞


desc = pipeline.get_desc()


@dataclass
class Cluster:
    """
    聚类信息
    """

    distance_mean: float
    store_pois: list[storage.PoiInfo]
    feature: dict
    args: dict


@dataclass
class Projection:
    """
    投影信息
    """

    width: float
    line: LineString
    geom: Polygon
    clusters: list[Cluster]
    tags: list[str] = field(default_factory=list)
    covered_pois: list[storage.PoiInfo] = field(default_factory=list)
    view_points: list[ViewPoint] = field(default_factory=list)

    def __post_init__(self):
        assert not self.geom.is_empty

    @property
    def face_id(self) -> str:
        """
        获取面 id
        """
        return utils.md5(self.geom.wkt)


@dataclass
class Elements:
    """
    地图要素信息
    """

    turnoff_points: list[turnoff_point.TurnoffPoint] = field(default_factory=list)


@dataclass
class DebugImage:
    """
    调试图片信息
    """

    bounds: tuple
    images: dict[str, np.ndarray] = field(default_factory=dict)


@dataclass
class Context:
    """
    上下文信息
    """

    task_id: str
    street_region: Polygon
    store_pois: list[storage.PoiInfo] = field(default_factory=list)
    clusters: list[Cluster] = field(default_factory=list)
    elements: Elements = field(default_factory=Elements)
    projections: list[Projection] = field(default_factory=list)
    debug_image: DebugImage = field(default=None)
    debug_info: dict[str, list] = field(default_factory=lambda: defaultdict(list))
    warning: str = field(default=None)
    error: str = field(default=None)


# pipes:


@desc()
def filter_street_region(ctx: Context, proceed, max_area: float):
    """
    若区域过大则不处理（> max_area）
    """
    if ctx.street_region.area > max_area:
        ctx.error = f"filter/street_region_too_large"
        return

    proceed()


@desc()
def repair_holes_of_region(ctx: Context, proceed):
    """
    去除街区中的孔洞
    """
    if isinstance(ctx.street_region, Polygon) and len(ctx.street_region.interiors) > 0:
        ctx.street_region = Polygon(ctx.street_region.exterior)

    proceed()


@desc()
def repair_gap_of_region(ctx: Context, proceed, min_gap_area: float):
    """
    筛选凹型街区
    """
    region = ctx.street_region.buffer(5 * METER).buffer(-5 * METER)
    gap_area = region.area - ctx.street_region.area
    if gap_area > min_gap_area:
        ctx.street_region = region

    proceed()


@desc()
def fetch_store_pois_only(ctx: Context, proceed):
    """
    仅获取街区内的临街 POI 集合，不做筛选
    """
    store_pois = storage.get_storefront_poi_infos(ctx.street_region.wkt)
    if not store_pois:
        ctx.error = "poi/no_nearby_store_pois"
        return

    ctx.store_pois.extend(store_pois)
    proceed()


@desc()
def fetch_store_pois(ctx: Context, proceed, min_inner_distance: float, max_cross_buildings: int):
    """
    获取街区内的临街 POI 集合
    """
    store_pois = storage.get_storefront_poi_infos(ctx.street_region.wkt)
    if not store_pois:
        ctx.error = "poi/no_nearby_store_pois"
        return

    def is_inner_poi(p: Point, line: LineString) -> bool:
        """
        过滤掉内部临街场景
        """
        d = line.project(p)
        foot = line.interpolate(d)
        foot_line = LineString([foot, p])
        if foot_line.length < min_inner_distance:
            return False

        sql = """
            select st_astext(st_buffer(st_unaryunion(st_buffer(st_collect(geom), 1 * 1e-5)), -1 * 1e-5))
            from bud_face
            where st_intersects(%s, geom)
        """
        ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [f"SRID=4326;{foot_line.wkt}"])
        buildings = [wkt.loads(x[0]) for x in ret]
        union_buildings = shapely.unary_union(buildings)
        buildings = geometric.flat_polygon(union_buildings)
        in_building = union_buildings.contains(p)
        num_cross_buildings = (len(buildings) - 1) if in_building else len(buildings)
        return num_cross_buildings > max_cross_buildings

    street_line = ctx.street_region.exterior
    for poi in store_pois:
        if is_inner_poi(poi.geom, street_line):
            ctx.debug_info["uncovered_poi"].append(build_poi_error_info(ctx, poi, "inner_store_poi"))
        else:
            ctx.store_pois.append(poi)

    proceed()


@desc()
def cluster_store_pois(ctx: Context, proceed, eps: float, min_samples: int):
    """
    聚类临街 POI 集合
    """
    poi_clusters = cluster_projection.cluster_by_dbscan(ctx.store_pois, eps, min_samples, key=lambda i: i.geom)
    picked_bids = {x.bid for c in poi_clusters for x in c}
    missing_pois = [p for p in ctx.store_pois if p.bid not in picked_bids]
    errs = [build_poi_error_info(ctx, p, "no_cluster") for p in missing_pois]
    if errs:
        ctx.debug_info["uncovered_poi"].extend(errs)

    if not poi_clusters:
        ctx.error = "cluster/no_clusters"
        return

    args = {"eps": eps, "min_samples": min_samples}
    for cluster in poi_clusters:
        cluster_points = [(p.geom.x, p.geom.y) for p in cluster]
        shapely_points = cluster_projection.to_points(cluster_points)
        line = ctx.street_region.exterior
        x = np.array([line.project(p) for p in shapely_points])
        y = np.array([line.distance(p) for p in shapely_points])
        y_mean = y.mean()

        # R² 系数
        if x.size > 2:
            slope, intercept = np.polyfit(x, y, 1)
            y_pred = slope * x + intercept
            ss_tot = np.sum(np.square(y - y_mean))
            ss_res = np.sum(np.square(y - y_pred))
            r2 = (1.0 - (ss_res / ss_tot)) if ss_tot > 0 else 1.0
        else:
            r2 = 1.0

        feature = {
            "stddev": y.std(),
            "rmse": np.sqrt(np.mean(np.square(y - y_mean))),
            "r2": r2,
        }
        result = Cluster(store_pois=cluster, distance_mean=y_mean, feature=feature, args=args)
        ctx.clusters.append(result)

    proceed()


@desc()
def make_cluster_polygon(
    ctx: Context, proceed, min_point_count: int, eps: float, min_samples: int, min_disparity: float, min_width: float
):
    """
    生成临街 POI 聚类的（初始）投影面
    """
    for cluster in ctx.clusters:
        shapely_points = [p.geom for p in cluster.store_pois]
        if len(shapely_points) < min_point_count:
            errs = [build_poi_error_info(ctx, p, "too_few_cluster") for p in cluster.store_pois]
            ctx.debug_info["uncovered_poi"].extend(errs)
            continue

        street_line = cluster_projection.get_linestring_from_full_polygon(ctx.street_region, shapely_points)
        try:
            line, pairs = cluster_projection.get_projection_cluster_again(
                target_line=street_line,
                points=shapely_points,
                eps=eps,
                min_samples=min_samples,
                min_disparity=min_disparity,
            )
        except Exception as e:
            errs = [build_poi_error_info(ctx, p, str(e)) for p in cluster.store_pois]
            ctx.debug_info["uncovered_poi"].extend(errs)
            continue

        distances = cluster_projection.get_distances(pairs)
        width = max(distances.mean() - distances.std() / 2, min_width)

        project_polygon = line.buffer(width, cap_style="flat")
        project_polygon = project_polygon.intersection(ctx.street_region)
        if project_polygon.is_empty or not project_polygon.is_valid:
            errs = [build_poi_error_info(ctx, p, "invalid_polygon") for p in cluster.store_pois]
            ctx.debug_info["uncovered_poi"].extend(errs)
            continue

        project_polygon = geometric.single_polygon(project_polygon)
        project = Projection(width=width, line=line, geom=project_polygon, clusters=[cluster])
        ctx.projections.append(project)

    if not ctx.projections:
        ctx.error = "polygon/no_projections"
        return

    proceed()


@desc()
def extend_by_bud_face(ctx: Context, proceed, min_width: float):
    """
    通过建筑物面沿长
    """
    bud_projects = []
    # 用现有 POI 投影找建筑面投影
    for poi_project in ctx.projections:
        bud_faces = storage.get_bud_faces(poi_project.geom.wkt)
        if not bud_faces:
            continue

        bud_geoms = [wkt.loads(x) for x in bud_faces]
        for bud_geom in bud_geoms:
            bud_points = [Point(x, y) for geom in geometric.flat_polygon(bud_geom) for x, y in geom.exterior.coords]
            street_line = cluster_projection.get_linestring_from_full_polygon(ctx.street_region, bud_points)
            line, pairs = cluster_projection.get_projection(street_line, bud_points)
            project_polygon = line.buffer(poi_project.width, cap_style="flat")
            project_polygon = project_polygon.intersection(ctx.street_region)
            if project_polygon.is_empty or not project_polygon.is_valid:
                continue

            bud_project = Projection(width=poi_project.width, line=line, geom=project_polygon, clusters=[])
            bud_projects.append(bud_project)

    # 使用建筑面外扩临街POI投影
    for bud_project in bud_projects:
        related_projections = [x for x in ctx.projections if x.geom.intersects(bud_project.geom)]
        if not related_projections:
            continue

        all_projections = [bud_project, *related_projections]
        points = [Point(x, y) for p in all_projections for x, y in p.line.coords]
        street_line = cluster_projection.get_linestring_from_full_polygon(ctx.street_region, points)
        new_line, _ = cluster_projection.get_projection(street_line, points)

        width = sum(x.width * x.line.length for x in related_projections) / sum(
            x.line.length for x in related_projections
        )
        width = max(width, min_width)
        new_geom = new_line.buffer(width, cap_style="flat")
        new_geom = new_geom.intersection(ctx.street_region)
        new_geom = geometric.single_polygon(new_geom)
        clusters = [c for x in related_projections for c in x.clusters]
        project = Projection(width=width, line=new_line, geom=new_geom, clusters=clusters)

        for related_projection in related_projections:
            ctx.projections.remove(related_projection)

        project.tags.append("extend_by_bud_face.extended")
        ctx.projections.append(project)

    proceed()


@desc()
def clip_by_sd_road(ctx: Context, proceed):
    """
    使用 SD 道路信息裁切
    """
    for projection in ctx.projections:
        geom = polygon_strategy.repair_by_sd_road(projection.geom)
        projection.geom = geom
        projection.line = _clip_baseline(projection.line, geom)
        projection.tags.append("clip_by_sd_road.clipped")

    proceed()


@desc()
def clip_by_ld_road(ctx: Context, proceed):
    """
    使用 LD 道路信息裁切
    """
    projects = list(ctx.projections)
    for project in projects:
        geom = polygon_strategy.repair_by_ld_road(project.geom, project.line)
        if geom is None or geom.is_empty:
            ctx.projections.remove(project)
            continue

        line = _clip_baseline(project.line, geom)
        if not isinstance(line, LineString):
            ctx.projections.remove(project)

        project.geom = geom
        project.line = line
        project.tags.append("clip_by_ld_road.clipped")

    proceed()


@desc()
def clip_by_aoi_gate(ctx: Context, proceed, vertical_tolerate: float, min_length: float):
    """
    使用 AOI 大门裁切，仅使用纵向大门裁切，纵向的判定为大门 link 与道路所成夹角在 vertical_tolerate (0 ~ 90) 范围内。
    """
    sql_nid = """
        select distinct b.node_id
        from blu_access a
        inner join blu_access_gate_rel b on a.access_id = b.access_id
        where 1 = 1
            and a.kind not in (2, 5)  -- 排除停车场出入口，建筑物门
            and st_intersects(a.geom, %s)
    """
    sql_node_geom = """
        select node_id, st_astext(geom)
        from nav_node
        where node_id in %s
    """
    sql_link = """
        select distinct st_astext(geom)
        from nav_link
        where s_nid = %(nid)s or e_nid = %(nid)s;
    """
    sql_semantic = """
        select passage from gates_semantic
        where long_node_id = %s
    """
    projects = list(ctx.projections)
    for project in projects:
        search_geom = project.line.buffer(project.width + 10 * METER, cap_style="flat")
        search_geom = search_geom.intersection(ctx.street_region)
        ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql_nid, [f"SRID=4326;{search_geom.wkt}"])
        if not ret:
            continue

        nids = [x[0] for x in ret]
        valid_links = []
        # 要找 nav_node 的坐标，blu_access 的坐标不准
        ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql_node_geom, [tuple(nids)])
        access_wkts = {nid: geom for nid, geom in ret}
        for nid in nids:
            if nid not in access_wkts:
                continue

            access_wkt = access_wkts[nid]
            # 纵向link
            ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql_link, {"nid": nid})
            if not ret:
                continue

            links = [wkt.loads(x[0]) for x in ret]
            if not any(_is_vertical_link(x, project.line, vertical_tolerate) for x in links):
                continue

            # 紧急大门
            ret = dbutils.fetch_all(pgsql.DEST_TRAJ, sql_semantic, [nid])
            passages = {x[0] for x in ret}
            if passages == {"URGENT"}:
                continue

            access_point = wkt.loads(access_wkt)
            project_distance = project.line.project(access_point)
            project_point = project.line.interpolate(project_distance)
            vertical_link = LineString([access_point, project_point])
            valid_links.append(vertical_link)

        if not valid_links:
            continue

        valid_links = [geometric.extend_linestring(x, 50 * METER) for x in valid_links]

        # DEBUG: 调试输出
        # tsv.write_tsv("vertical_links.txt", [[x] for x in vertical_links], mode="a")

        link_union = shapely.unary_union(valid_links)
        link_union = link_union.buffer(3 * METER)
        geom = project.geom.difference(link_union)
        geoms = geometric.flat_polygon(geom)
        if len(geoms) == 1:
            continue

        ctx.projections.remove(project)
        for geom in geoms:
            line = _clip_baseline(project.line, geom)
            if line.length < min_length:
                continue

            new_project = Projection(width=project.width, line=line, geom=geom, clusters=project.clusters)
            new_project.tags.extend(project.tags)
            new_project.tags.append("clip_by_aoi_gate.split")
            ctx.projections.append(new_project)

    proceed()


@desc()
def repair_by_side_road(ctx: Context, proceed, width: float, road_distance: float):
    """
    不压辅路
    """

    def get_side_roads(geom: str):
        sql = """
            select link_id, s_nid, e_nid, st_astext(geom), dir, kind, lane_l, lane_r, form
            from nav_link
            where (form ~ '34' or kind <= 7) and st_intersects(geom, %s);
        """
        ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{geom}"])
        links = [(wkt.loads(x[3]), tools.get_road_width(*x[-5:]) * METER) for x in ret]
        return links

    def get_move_info(geom: Polygon, baseline: LineString):
        side_roads = get_side_roads(geom.wkt)
        if not side_roads:
            return None

        side_roads = [
            (single_line, w) for line, w in side_roads for single_line in geometric.flat_line(line.intersection(geom))
        ]
        side_points = [(Point(x, y), w) for line, w in side_roads for x, y in line.coords]
        projected_points = [(p, baseline.interpolate(baseline.project(p)), w) for p, w in side_points]
        projected_points = [(p, b_p, p.distance(b_p) + w / 2) for p, b_p, w in projected_points]
        side_pt, base_pt, distance = max(projected_points, key=lambda x: x[-1])
        direction = np.array([side_pt.x, side_pt.y]) - np.array([base_pt.x, base_pt.y])
        direction = direction / np.linalg.norm(direction)
        return distance * direction

    def prune(target: Polygon, line: LineString, left: bool):
        to_clip_line = line.buffer(0.001 * METER)
        geoms = target.difference(to_clip_line)
        geoms = geometric.flat_polygon(geoms)
        is_handled = len(geoms) > 1
        if not is_handled:
            return is_handled, target

        geoms = [x for x in geoms if geometric.is_left_polygon(x, line) == left]
        return is_handled, max(geoms, key=lambda x: x.area)

    def resolve_one(proj: Projection):
        eps = 1 * METER
        if len(proj.geom.interiors) == 0:
            # 不处理环形的面
            # noinspection PyBroadException
            try:
                e_line, i_line = geometric.get_sidelines_v1(proj.geom, proj.line, eps)
                proj.tags.append("repair_by_side_road.sideline_v1")
            except:
                e_line, i_line = geometric.get_sidelines_v2(proj.geom, proj.line)
                proj.tags.append("repair_by_side_road.sideline_v2")

        elif proj.geom.exterior.length > width + 20 * METER:
            e_line = proj.geom.exterior
            e_line = shapely.ops.substring(e_line, 0, e_line.length - width - eps)
            i_line = max(proj.geom.interiors, key=lambda i: i.length)
            proj.tags.append("repair_by_side_road.ring_broken")
        else:
            raise NotImplementedError("not support ring-polygon")

        e_line = geometric.trim_linestring(e_line, -0.5, 10 * METER)
        e_line = geometric.trim_linestring_for_buffer(e_line, (width + eps))
        e_line = geometric.trim_linestring_for_buffer(e_line, -(width + eps))
        e_line = geometric.extend_linestring(e_line, eps)

        mid_pt = i_line.interpolate(i_line.length / 2)
        is_left = geometric.is_left(mid_pt, e_line)
        if road_distance > 0:
            outside_line = geometric.move_linestring(e_line, road_distance if is_left else -road_distance)
        else:
            outside_line = e_line

        inside_line = geometric.move_linestring(e_line, width if is_left else -width)
        mid_pt = inside_line.interpolate(inside_line.length / 2)
        is_left = geometric.is_left(mid_pt, outside_line)

        buffer_geom = outside_line.buffer(width if is_left else -width, cap_style="flat", single_sided=True)
        _, buffer_geom = prune(buffer_geom, outside_line, is_left)

        vec = get_move_info(proj.geom, e_line)
        if vec is None:
            proj.geom = buffer_geom
            proj.tags.append("repair_by_side_road.unmoved")
            return

        x, y = vec
        buffer_geom = shapely.affinity.translate(buffer_geom, xoff=x, yoff=y)
        clip_width = max(proj.line.distance(Point(x, y)) for x, y in buffer_geom.exterior.coords) + 0.1 * METER
        base_buffer_geom = proj.line.buffer(clip_width, cap_style="flat")
        buffer_geom = base_buffer_geom.intersection(buffer_geom)

        buffer_geom = geometric.single_polygon(buffer_geom)
        buffer_geom = buffer_geom.buffer(-0.2 * METER).buffer(0.2 * METER)
        proj.geom = buffer_geom
        proj.tags.append("repair_by_side_road.moved")

    for project in ctx.projections:
        # noinspection PyBroadException
        try:
            resolve_one(project)
        except:
            project.tags.append("repair_by_side_road.failed")

    proceed()


@desc()
def repair_by_side_line(ctx: Context, proceed, width: float, road_distance: float):
    """
    使用外侧线重新修正边框
    """

    def prune(target: Polygon, line: LineString, left: bool):
        to_clip_line = line.buffer(0.001 * METER)
        geoms = target.difference(to_clip_line)
        geoms = geometric.flat_polygon(geoms)
        is_handled = len(geoms) > 1
        if not is_handled:
            return is_handled, target

        geoms = [x for x in geoms if geometric.is_left_polygon(x, line) == left]
        return is_handled, max(geoms, key=lambda x: x.area)

    def resolve_one(proj: Projection):
        eps = 1 * METER
        if len(proj.geom.interiors) == 0:
            # 不处理环形的面
            # noinspection PyBroadException
            try:
                e_line, i_line = geometric.get_sidelines_v1(proj.geom, proj.line, eps)
                proj.tags.append("repair_by_side_line.sideline_v1")
            except:
                e_line, i_line = geometric.get_sidelines_v2(proj.geom, proj.line)
                proj.tags.append("repair_by_side_line.sideline_v2")
        elif len(proj.geom.interiors) == 1 and proj.geom.exterior.length > 2 * width + 20 * METER:
            e_line = proj.geom.exterior
            e_line = shapely.ops.substring(e_line, 0, e_line.length - 2 * width - eps)
            i_line = max(proj.geom.interiors, key=lambda i: i.length)
            proj.tags.append("repair_by_side_line.ring_broken")
        else:
            raise NotImplementedError("not support ring-polygon")

        e_line = geometric.trim_linestring(e_line, -0.5, 10 * METER)
        e_line = geometric.trim_linestring_for_buffer(e_line, (width + eps))
        e_line = geometric.trim_linestring_for_buffer(e_line, -(width + eps))
        e_line = geometric.extend_linestring(e_line, eps)

        mid_pt = i_line.interpolate(i_line.length / 2)
        is_left = geometric.is_left(mid_pt, e_line)
        if road_distance > 0:
            outside_line = geometric.move_linestring(e_line, road_distance if is_left else -road_distance)
        else:
            outside_line = e_line

        inside_line = geometric.move_linestring(e_line, width if is_left else -width)
        mid_pt = inside_line.interpolate(inside_line.length / 2)
        is_left = geometric.is_left(mid_pt, outside_line)

        buffer_geom = outside_line.buffer(width if is_left else -width, cap_style="flat", single_sided=True)
        _, buffer_geom = prune(buffer_geom, outside_line, is_left)
        proj.geom = buffer_geom
        proj.tags.append("repair_by_side_line.success")

    for project in ctx.projections:
        # noinspection PyBroadException
        try:
            resolve_one(project)
        except:
            project.tags.append("repair_by_side_line.failed")

    proceed()


@desc()
def rebind_store_poi(ctx: Context, proceed, max_distance: float):
    """
    计算 projection 所覆盖的临街 POI
    """

    def get_covered_pois(project: Projection):
        line, geom = project.line, project.geom
        total_pois = [p for c in project.clusters for p in c.store_pois]
        for poi in total_pois:
            d_line, d_geom = line.project(poi.geom), geom.distance(poi.geom)
            if d_geom > max_distance:
                continue

            if 0 < d_line < line.length:
                yield poi

    for projection in ctx.projections:
        projection.covered_pois = list(get_covered_pois(projection))

    proceed()


@desc()
def filter_no_poi_projection(ctx: Context, proceed):
    """
    过滤掉没有 cover 临街 POI 的 Projection
    """
    ctx.projections = [x for x in ctx.projections if len(x.covered_pois) > 0]
    proceed()


@desc()
def filter_by_overlap_road(_ctx: Context, proceed):
    """
    TODO: 排除压盖道路的面
    """
    proceed()


def filter_by_invalid_baseline(ctx: Context, proceed):
    """
    拦截 baseline 无效的面
    """
    for project in ctx.projections:
        if not isinstance(project.line, LineString):
            project.tags.append("filter_by_overlap_road.failed")

    proceed()


@desc()
def filter_without_in_street_region(ctx: Context, proceed, tolerance: float):
    """
    过滤掉不在街道范围内的面
    """
    for project in ctx.projections:
        if utils.calc_ioa(ctx.street_region, project.geom) < tolerance:
            project.tags.append("filter_without_in_street_region.failed")

    proceed()


@desc()
def filter_by_length(ctx: Context, proceed, min_length: float, max_length: float):
    """
    过滤掉不符合长度的面
    """
    for project in ctx.projections:
        length = project.line.length
        if length < min_length or length > max_length:
            project.tags.append("filter_by_length.failed")

    proceed()


@desc()
def simplify_projection(ctx: Context, proceed):
    """
    抽稀面
    """
    for project in ctx.projections:
        project.geom = project.geom.simplify(0.01 * METER)

    proceed()


def _clip_baseline(baseline: LineString, polygon: Polygon):
    """
    根据 Polygon 投影截断 baseline
    """
    geom_points = [Point(*p) for p in polygon.exterior.coords]
    line, _ = cluster_projection.get_projection(baseline, geom_points)
    return line


def _is_vertical_link(link1: LineString, link2: LineString, vertical_tolerate: float):
    points = [Point(x, y) for x, y in link1.coords]
    p00 = points[0]
    p01 = points[-1]

    d0 = link2.project(p00)
    d1 = link2.project(p01)
    if abs(d0 - d1) < 1e-9:
        return True

    p10 = link2.interpolate(d0)
    p11 = link2.interpolate(d1)

    vec1 = np.array([p01.x - p00.x, p01.y - p00.y])
    vec1 = vec1 / np.linalg.norm(vec1)

    vec2 = np.array([p11.x - p10.x, p11.y - p10.y])
    vec2 = vec2 / np.linalg.norm(vec2)

    return abs(np.dot(vec1, vec2)) < np.cos(np.deg2rad(vertical_tolerate))


@desc()
def clip_by_bud_face(ctx: Context, proceed, min_width: float):
    """
    使用建筑物信息修形
    """
    for projection in ctx.projections:
        bud_faces = storage.get_bud_faces(projection.geom.wkt)
        bud_geoms = [wkt.loads(x) for x in bud_faces]
        bud_infos = [(x, x.area, projection.line.distance(x)) for x in bud_geoms]
        bud_geoms = [geom for geom, area, dist in bud_infos if area > 100 * METER_2 or dist > 10 * METER]
        if not bud_geoms:
            projection.tags.append("clip_by_bud_face.no_building")
            continue

        distances = np.array([projection.line.distance(bud_geom) for bud_geom in bud_geoms])
        width = max(distances.mean() - distances.std() / 2, min_width)
        geom = projection.line.buffer(width, cap_style="flat")
        geom = geom.intersection(ctx.street_region)
        geom = geometric.single_polygon(geom)
        projection.geom = geom
        projection.line = _clip_baseline(projection.line, geom)
        projection.width = width
        projection.tags.append("clip_by_bud_face.has_building")

    proceed()


@desc()
def extend_by_recognition_face(_ctx: Context, proceed):
    """
    TODO: 使用识别结果修形
    """
    proceed()


@desc()
def extend_by_turnoff_point(
    ctx: Context, proceed, provider: Callable[[str], list[turnoff_point.TurnoffPoint]], min_width: float
):
    """
    使用熄火点聚类面修形
    """
    max_deepen = np.mean([x.width for x in ctx.projections])
    street_region = polygon_strategy.repair_by_sd_road(ctx.street_region)
    street_region = polygon_strategy.repair_by_ld_road(street_region, ctx.street_region.exterior)
    inner_geom = street_region.buffer(-max_deepen)
    street_gap = street_region.difference(inner_geom)

    turnoff_points = provider(street_gap.wkt)
    ctx.elements.turnoff_points = turnoff_points

    # 计算熄火点投影
    points = [x.point for x in turnoff_points]
    if not points:
        return proceed()

    clusters = cluster_projection.cluster_by_dbscan(points, 10 * METER, 2)
    projections = []
    for cluster in clusters:
        pts = [(p.x, p.y) for p in cluster]
        if len(pts) < 20:
            continue

        shapely_points = cluster_projection.to_points(pts)
        street_line = cluster_projection.get_linestring_from_full_polygon(ctx.street_region, shapely_points)
        line, pairs = cluster_projection.get_projection(street_line, shapely_points)
        distances = cluster_projection.get_distances(pairs)
        width = distances.mean() + distances.std()

        project_polygon = line.buffer(width, cap_style="flat")
        project_polygon = project_polygon.intersection(ctx.street_region)

        if not project_polygon.is_empty:
            density = 100 * len(pts) / (project_polygon.area / METER**2)
            project = Projection(width=width, line=line, geom=project_polygon, clusters=[])
            projections.append((project, density))

    # 筛选可用投影
    projections = [
        x
        for x, density in projections
        if 11 * METER < x.width < 80 * METER and x.line.length > x.width and density >= 2
    ]
    # 使用熄火点投影外扩临街POI投影
    for projection in projections:
        related_projections = [x for x in ctx.projections if x.geom.intersects(projection.geom)]
        if not related_projections:
            continue

        all_projections = [projection, *related_projections]
        points = [Point(x, y) for p in all_projections for x, y in p.line.coords]
        street_line = cluster_projection.get_linestring_from_full_polygon(ctx.street_region, points)
        new_line, _ = cluster_projection.get_projection(street_line, points)

        width = sum(x.width * x.line.length for x in all_projections) / sum(x.line.length for x in all_projections)
        width = max(width, min_width)
        new_geom = new_line.buffer(width, cap_style="flat")
        new_geom = new_geom.intersection(ctx.street_region)
        clusters = [c for x in related_projections for c in x.clusters]
        project = Projection(width=width, line=new_line, geom=new_geom, clusters=clusters)

        for related_projection in related_projections:
            ctx.projections.remove(related_projection)

        ctx.projections.append(project)

    proceed()


# panorama


@desc()
def get_view_points(ctx: Context, proceed, turning_angle: int, segment_length):
    """
    生成观察点
    """

    def get_view_point(line: LineString, center: Point):
        distance = line.project(center)
        foot_point = line.interpolate(distance)
        assert center is not None and foot_point is not None
        return DefaultViewPoint(center, foot_point)

    for projection in ctx.projections:
        centroids = verify.get_view_positions(projection.line, max_angle=turning_angle, segment_length=segment_length)
        inner_geom = ctx.street_region.buffer(-projection.width)
        if inner_geom.is_empty:
            projection.tags.append("get_view_points.empty_inner_geom.failed")
            continue

        if isinstance(inner_geom, MultiPolygon):
            total_area = inner_geom.area
            max_inner_geom = max(geometric.flat_polygon(inner_geom), key=lambda x: x.area)
            if max_inner_geom.area / total_area > 0.9:
                inner_geom = max_inner_geom
                projection.tags.append("get_view_points.multi_inner_geom.success")
            else:
                projection.tags.append("get_view_points.multi_inner_geom.failed")
                continue

        # noinspection PyBroadException
        try:
            # inner_geom = inner_geom if isinstance(inner_geom, Polygon) else projection.geom
            view_points = [get_view_point(inner_geom.exterior, x) for x in centroids]
            projection.view_points = view_points
        except:
            projection.tags.append("get_view_points.exception.failed")
            continue

    proceed()


# draw debug image


@desc()
def use_debug_image(ctx: Context, proceed, max_area: float):
    """
    初始化调试图片：整个街区
    """
    if ctx.street_region.area > max_area:
        ctx.warning = f"debug/street_region_too_large"
        return

    bounds = boundary.from_wkt(ctx.street_region.wkt, 30 * METER)
    image = satellite_imagery.crop(bounds)
    if image is None:
        ctx.warning = "debug/no_satellite_imagery"
        return

    debug_image = DebugImage(bounds)
    debug_image.images[ctx.task_id] = image
    ctx.debug_image = debug_image
    proceed()


@desc()
def draw_recognition_face(ctx: Context, proceed):
    """
    绘制识别区域
    """
    image, bounds = ctx.debug_image.images[ctx.task_id], ctx.debug_image.bounds
    recognition_faces = storage.get_recognition_faces(ctx.street_region.wkt)
    for recognition_face in recognition_faces:
        preview_image.draw_polygon(image, recognition_face, bounds, thickness=1, color=preview_image.COLOR_RED)
        preview_image.fill_polygon(image, recognition_face, bounds, color=preview_image.COLOR_RED, alpha=0.3)

    proceed()


@desc()
def draw_street_region(ctx: Context, proceed):
    """
    绘制街道区域
    """
    image, bounds = ctx.debug_image.images[ctx.task_id], ctx.debug_image.bounds
    preview_image.draw_polygon(image, ctx.street_region.wkt, bounds, thickness=1, color=preview_image.COLOR_GREEN)
    proceed()


@desc()
def draw_ld_road(ctx: Context, proceed):
    """
    绘制 LD 道路边界线+交叉口
    """
    image, bounds = ctx.debug_image.images[ctx.task_id], ctx.debug_image.bounds

    ld_boundaries = polygon_strategy._get_ld_boundaries(ctx.street_region.wkt)
    ld_geoms = [wkt.loads(x) for x in ld_boundaries]
    # 将线段合并成multiLinestring
    ld_union = shapely.unary_union(ld_geoms)
    preview_image.draw_linestring(image, ld_union.wkt, bounds, thickness=2, color=preview_image.COLOR_CYAN)

    ld_faces = polygon_strategy._get_ld_faces(ctx.street_region.wkt)
    ld_faces = [wkt.loads(x[0]) for x in ld_faces]
    for ld_face in ld_faces:
        preview_image.fill_polygon(image, ld_face.wkt, bounds, color=preview_image.COLOR_CYAN, alpha=0.3)
        preview_image.draw_polygon(image, ld_face.wkt, bounds, thickness=1, color=preview_image.COLOR_CYAN)

    proceed()


@desc()
def draw_cluster_point(ctx: Context, proceed):
    """
    绘制临街 POI 聚类点
    """
    # 绘制聚类点（不同类别不同颜色）
    image, bounds = ctx.debug_image.images[ctx.task_id], ctx.debug_image.bounds
    colors = preview_image.get_colors(len(ctx.clusters))
    for cluster, color in zip(ctx.clusters, colors):
        points = [(p.geom.x, p.geom.y) for p in cluster.store_pois]
        preview_image.draw_point(image, points, bounds, radius=8, color=color)

    proceed()


@desc()
def draw_projection_polygon(ctx: Context, proceed):
    """
    绘制门前停车场面
    """
    image, bounds = ctx.debug_image.images[ctx.task_id], ctx.debug_image.bounds
    for projection in ctx.projections:
        preview_image.draw_polygon(image, projection.geom.wkt, bounds, thickness=4, color=preview_image.COLOR_GREEN)
        preview_image.draw_linestring(image, projection.line.wkt, bounds, thickness=4, color=preview_image.COLOR_YELLOW)

    proceed()


@desc()
def draw_turnoff_point(ctx: Context, proceed):
    """
    绘制熄火点相关信息
    """
    if not ctx.elements.turnoff_points:
        return proceed()

    image, bounds = ctx.debug_image.images[ctx.task_id], ctx.debug_image.bounds
    points = [x.point for x in ctx.elements.turnoff_points]
    clusters = cluster_projection.cluster_by_dbscan(points, 10 * METER, 2)
    for points, color in zip(clusters, preview_image.get_colors(len(clusters))):
        pts = [(p.x, p.y) for p in points]
        if len(points) < 20:
            continue

        preview_image.draw_point(image, pts, bounds, radius=2, color=color)

        shapely_points = cluster_projection.to_points(pts)
        street_line = cluster_projection.get_linestring_from_full_polygon(ctx.street_region, shapely_points)
        line, pairs = cluster_projection.get_projection(street_line, shapely_points)
        distances = cluster_projection.get_distances(pairs)
        width = distances.mean() + distances.std()

        project_polygon = line.buffer(width, cap_style="flat")
        project_polygon = project_polygon.intersection(ctx.street_region)

        if not project_polygon.is_empty:
            density = 100 * len(points) / (project_polygon.area / METER**2)
            preview_image.fill_polygon(image, project_polygon.wkt, bounds, alpha=0.3, color=color)

            transform = preview_image._get_geo2pixel_transform(image, bounds)
            center = utils.get_point(project_polygon.centroid.wkt, transform)
            cv2.putText(
                image,
                f"{density:.2f},{width/METER:.2f},{line.length/METER:.2f}",
                center,
                2,
                cv2.FONT_HERSHEY_PLAIN,
                preview_image.COLOR_WHITE,
                thickness=2,
            )

    proceed()


@desc()
def draw_bud_face(ctx: Context, proceed):
    """
    绘制建筑物
    """
    image, bounds = ctx.debug_image.images[ctx.task_id], ctx.debug_image.bounds
    bud_faces = storage.get_bud_faces(ctx.street_region.wkt)
    for bud_face in bud_faces:
        preview_image.draw_polygon(image, bud_face, bounds, thickness=1, color=preview_image.COLOR_BLUE)

    proceed()


@desc()
def draw_view_point(ctx: Context, proceed):
    """
    绘制全景观察点的相关信息
    """
    image, bounds = ctx.debug_image.images[ctx.task_id], ctx.debug_image.bounds
    copy_image = image.copy()
    for project in ctx.projections:
        for p in project.view_points:
            p1, p2 = p.geom, p.foot
            color = (32, 32, 32)
            preview_image.draw_arrow(copy_image, p1.wkt, p2.wkt, bounds, color=color)

    ctx.debug_image.images[ctx.task_id] = copy_image
    proceed()


def build_poi_error_info(ctx: Context, poi: storage.PoiInfo, err: str):
    """
    构建 POI 的错误信息
    """
    return [ctx.task_id, poi.bid, poi.name, poi.std_tag, poi.geom.wkt, err]


# save pipe


# execute:


debug_image_pipe = pipeline.Pipeline(
    partial(use_debug_image, max_area=2000**2 * METER_2),
    # draw_recognition_face,
    draw_street_region,
    draw_cluster_point,
    draw_turnoff_point,
    draw_bud_face,
    draw_projection_polygon,
    draw_ld_road,
    draw_view_point,
)
repair_extend_pipe = pipeline.Pipeline(
    partial(extend_by_bud_face, min_width=20 * METER),
    partial(extend_by_turnoff_point, provider=turnoff_point_cluster.get_doctor_points, min_width=30 * METER),
    # partial(extend_by_turnoff_point, provider=turnoff_point_cluster.get_zhongyuan_points, min_width=20 * METER),
    partial(extend_by_bud_face, min_width=20 * METER),
    # OBSOLETE: 当前识别面可能包含路侧停车场，使用其延长会导致面过长，故不使用
    # extend_by_recognition_face,
)
repair_avoid_pipe = pipeline.Pipeline(
    # OBSOLETE: SD 道路存在数据问题，直接用于裁切可能导致面出现锯齿，故使用平移避让策略代替
    # clip_by_sd_road,
    clip_by_ld_road,
    partial(repair_by_side_road, width=20 * METER, road_distance=0 * METER),
    clip_by_ld_road,
    partial(repair_by_side_road, width=20 * METER, road_distance=0 * METER),
    clip_by_ld_road,
    partial(repair_by_side_road, width=10 * METER, road_distance=2 * METER),
)
repair_pipe = pipeline.Pipeline(
    repair_extend_pipe,
    partial(clip_by_bud_face, min_width=20 * METER),
    # OBSOLETE: 道路避让可能产生较多几何问题，目前情报召回不需要这么精细的修形
    # repair_avoid_pipe,
    partial(repair_by_side_line, width=10 * METER, road_distance=2 * METER),
    # OBSOLETE: 大门裁切会导致面过碎，已不再使用
    # partial(clip_by_aoi_gate, vertical_tolerate=70, min_length=10 * METER),
)
post_pipe = pipeline.Pipeline(
    filter_by_invalid_baseline,
    partial(filter_without_in_street_region, tolerance=0.99),
    partial(filter_by_length, min_length=20 * METER, max_length=math.inf),
    filter_by_overlap_road,
    partial(rebind_store_poi, max_distance=30 * METER),
    filter_no_poi_projection,
    simplify_projection,
)
pipe = pipeline.Pipeline(
    partial(filter_street_region, max_area=10000**2 * METER_2),
    repair_holes_of_region,
    partial(repair_gap_of_region, min_gap_area=20 * METER_2),
    # fetch_store_pois_only,
    partial(fetch_store_pois, min_inner_distance=10 * METER, max_cross_buildings=0),
    partial(cluster_store_pois, eps=30 * METER, min_samples=2),
    partial(
        make_cluster_polygon,
        min_point_count=2,
        eps=200 * METER,
        min_samples=2,
        min_disparity=0.3,
        min_width=20 * METER,
    ),
    repair_pipe,
    post_pipe,
    partial(get_view_points, turning_angle=60, segment_length=25 * METER),
    # debug_image_pipe,
)
desc.disabled = True
desc.attach(pipe)


def is_valid_tags(tags: list[str]) -> bool:
    """
    通过一组面的标签，判断该面是否有效
    WORKAROUND: 肮脏的实现方式，待优化
    """
    if not tags:
        return True

    if tags[-1] == "get_view_points.multi_inner_geom.success":
        idx_last_filter = -2
    else:
        idx_last_filter = -1

    if "failed" not in tags[idx_last_filter]:
        return True

    return False


@logger.catch
def execute_once(task: tuple[int, str]):
    """
    执行一次任务（为并行）
    """
    task_id, geom = task
    ctx = Context(task_id=str(task_id), street_region=wkt.loads(geom))
    try:
        pipe(ctx)
    except Exception as e:
        ctx.error = str(e)

    # 详细报错信息
    # pipe(ctx)
    return ctx


def execute(tasks: list[tuple[int, str]], output_dir: Path):
    """
    入口点
    """
    warning_path = utils.ensure_path(output_dir / "warning.tsv", cleanup=True)
    error_path = utils.ensure_path(output_dir / "error.tsv", cleanup=True)
    cluster_path = utils.ensure_path(output_dir / "cluster.tsv", cleanup=True)
    polygon_path = utils.ensure_path(output_dir / "polygon.tsv", cleanup=True)
    polygon_valid_path = utils.ensure_path(output_dir / "polygon_valid.tsv", cleanup=True)
    segment_path = utils.ensure_path(output_dir / "segment.tsv", cleanup=True)
    covered_poi_path = utils.ensure_path(output_dir / "covered_poi.tsv", cleanup=True)
    store_poi_path = utils.ensure_path(output_dir / "store_poi.tsv", cleanup=True)

    cleaned_files = set()
    with Pool(32) as executor, open(output_dir / "progress_prime.log", "w") as f_progress:
        for ctx in tqdm(executor.imap_unordered(execute_once, tasks), total=len(tasks), file=f_progress):
            if ctx.warning:
                item = [ctx.task_id, ctx.warning]
                tsv.write_tsv(warning_path, [item], mode="a")

            if ctx.error:
                item = [ctx.task_id, ctx.error]
                tsv.write_tsv(error_path, [item], mode="a")
                continue

            # 临街商铺
            for poi in ctx.store_pois:
                item = [ctx.task_id, poi.bid, poi.name, poi.std_tag, poi.geom.wkt]
                tsv.write_tsv(store_poi_path, [item], mode="a")

            # 聚类点信息
            for cluster in ctx.clusters:
                feature = cluster.feature
                item = [
                    ctx.task_id,
                    len(cluster.store_pois),
                    cluster.distance_mean,
                    feature["stddev"],
                    feature["rmse"],
                    feature["r2"],
                ]
                tsv.write_tsv(cluster_path, [item], mode="a")

            # 投影信息
            projects = [x for x in ctx.projections if not x.geom.is_empty]
            for project in projects:
                project_info = [
                    project.face_id,
                    project.geom.area / METER_2,
                    project.width / METER,
                    project.line.length / METER,
                    "-".join(project.tags),
                    project.line.wkt,
                    project.geom.wkt,
                ]
                item = [ctx.task_id, *project_info]
                tsv.write_tsv(polygon_path, [item], mode="a")
                if is_valid_tags(project.tags):
                    tsv.write_tsv(polygon_valid_path, [item], mode="a")

                # 关联的 POI
                cover_pois = [p for c in project.clusters for p in c.store_pois]
                cover_poi_infos = [
                    [ctx.task_id, project.face_id, p.bid, p.name, p.std_tag, p.geom.wkt] for p in cover_pois
                ]
                tsv.write_tsv(covered_poi_path, cover_poi_infos, mode="a")

            # 观察点信息
            for project in ctx.projections:
                for view_point in project.view_points:
                    view_info = [
                        ctx.task_id,
                        project.face_id,
                        view_point.view_id,
                        view_point.geom.wkt,
                        view_point.foot.wkt,
                    ]
                    items = []
                    if view_point.side_views:
                        for side in view_point.side_views:
                            image_info = [
                                side.image_type,
                                side.track_id,
                                side.time,
                                Point(side.direction).wkt,
                                side.position.wkt,
                            ]
                            items.append(view_info + image_info)
                    else:
                        items.append(view_info + ["<empty>"])

                    tsv.write_tsv(segment_path, items, mode="a")

            # 调试信息
            for key, items in ctx.debug_info.items():
                debug_path = output_dir / f"debug_{key}.tsv"
                if key not in cleaned_files:
                    utils.ensure_path(debug_path, cleanup=True)
                    cleaned_files.add(key)

                tsv.write_tsv(debug_path, items, mode="a")

            # 调试图片
            if ctx.debug_image:
                for key, image in ctx.debug_image.images.items():
                    if image is None:
                        continue

                    segments = key.split("/")
                    assert len(segments) <= 2
                    folder_name, image_name = ("debug", segments[0]) if len(segments) < 2 else segments
                    folder_path = utils.ensure_dir(output_dir / folder_name)
                    cv2.imwrite(str(folder_path / f"{image_name}.jpg"), image)
