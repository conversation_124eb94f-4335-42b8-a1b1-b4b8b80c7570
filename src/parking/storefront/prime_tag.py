"""
标签模块：给原始面打上各种标签
"""
import json
import logging
from dataclasses import dataclass, field
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Callable

import shapely
import shapely.ops
from shapely import wkt, Polygon
from tqdm import tqdm
from psycopg2.extras import Json

import src.parking.storefront.diff.polygon_differ as geo_differ
from src.parking.recognition import dbutils
from src.parking.storefront import polygon_diff
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.utils import cityutils, geometric
from src.parking.storefront.utils.geometric import METER
from src.tools import pgsql, pipeline, tsv, utils

FILE_NAME_TAG = "priority_analysis.tag.tsv"
FILE_NAME_POI = "priority_analysis.poi.tsv"
FILE_NAME_DIFF = "priority_analysis.diff.csv"

desc = pipeline.get_desc()
utils_dir = Path(__file__).parent / "utils"


@dataclass
class PrimeTags:
    """
    标签信息
    """

    pv_tag: str = field(default="")
    area_tags: list[str] = field(default_factory=list)
    diff_tag: str = field(default="")
    pv_tags: list[str] = field(default_factory=list)

    @staticmethod
    def from_dict(data: dict) -> 'PrimeTags':
        """生成标签信息"""
        tags = PrimeTags()
        if 'pv' in data:
            tags.pv_tags = data['pv']
        return tags


@dataclass
class TaggedPrime:
    """
    带有标签信息的原始面
    """

    task_id: int
    prime_id: int
    poi_count: int
    face_pv: int
    tags: PrimeTags = field(default_factory=PrimeTags)


@dataclass
class PrimeFace:
    """
    原始面信息
    """

    task_id: int
    strategy_id: int
    geom: Polygon
    store_pois: list[autocomplete.Poi] = field(default_factory=list)
    pv_sum: int = field(default=0)
    tags: PrimeTags = field(default_factory=PrimeTags)


@dataclass
class Context:
    """
    上下文
    """

    primes: list[PrimeFace]


@desc()
def fetch_store_pois(ctx: Context, proceed, search_buffer: float):
    """
    拉取临街 POI
    """
    with Pool(32) as pool:
        tasks = [(p.strategy_id, p.geom.wkt, search_buffer) for p in ctx.primes]
        prime_dict = {p.strategy_id: p for p in ctx.primes}
        for strategy_id, pois in tqdm(pool.imap_unordered(_process_fetch_store_pois, tasks), total=len(ctx.primes)):
            prime_dict[strategy_id].store_pois.extend(pois)

    proceed()


@desc()
def calc_pv_sum(ctx: Context, proceed):
    """
    计算 pv 和
    """
    for prime in ctx.primes:
        prime.pv_sum = sum(p.click_pv for p in prime.store_pois)

    proceed()


def calc_pv_num_by_file(ctx: Context, proceed, work_dir: Path):
    """
    根据文件获取 pv
    """
    file = work_dir / FILE_NAME_TAG

    id2prime = {prime.strategy_id: prime for prime in ctx.primes}
    for tag in read_tag_file(file):
        id2prime[tag.prime_id].pv_sum = tag.face_pv

    proceed()


@desc()
def calc_pv_tag(ctx: Context, proceed, thresholds: list[float], fn: Callable[[PrimeFace], bool]):
    """
    计算 pv 标签
    """
    primes = [x for x in ctx.primes if fn(x)]
    sorted_primes = sorted(primes, key=lambda x: x.pv_sum, reverse=True)
    total_pv = sum(x.pv_sum for x in sorted_primes)
    pv_limits = [total_pv * x for x in thresholds]
    pv_tags = [""] + [str(int(x * 100)) for x in thresholds] + ["100"]
    pv_tags = [f"TOP{x}-TOP{y}" if x else f"TOP{y}" for x, y in zip(pv_tags, pv_tags[1:])]
    sum_pv = 0
    for prime in sorted_primes:
        sum_pv += prime.pv_sum
        i = sum(1 for x in pv_limits if x <= sum_pv)
        tag = pv_tags[i]
        prime.tags.pv_tag = tag

    proceed()


@desc()
def calc_pv_tags(ctx: Context, proceed, thresholds: list[float], fn: Callable[[PrimeFace], bool], tag_pre: str):
    """
    计算 pv 标签
    """
    primes = [x for x in ctx.primes if fn(x)]
    sorted_primes = sorted(primes, key=lambda x: x.pv_sum, reverse=True)
    total_pv = sum(x.pv_sum for x in sorted_primes)
    pv_limits = [total_pv * x for x in thresholds]
    pv_tags = [""] + [str(int(x * 100)) for x in thresholds] + ["100"]
    pv_tags = [f"TOP{x}-TOP{y}" if x else f"TOP{y}" for x, y in zip(pv_tags, pv_tags[1:])]
    sum_pv = 0
    for prime in sorted_primes:
        sum_pv += prime.pv_sum
        i = sum(1 for x in pv_limits if x <= sum_pv)
        tag = pv_tags[i]
        prime.tags.pv_tags.append(f"{tag_pre}{tag}")
    proceed()


@desc()
def calc_area_tag(ctx: Context, proceed, area_tag: str, area_wkt: str, reserve: bool = False):
    """
    为与 area_wkt 相交的 PrimeFace.area_tags 添加 area_tag
    """
    area_geom = wkt.loads(area_wkt)
    for prime in ctx.primes:
        to_tag = area_geom.intersects(prime.geom)
        if reserve:
            to_tag = not to_tag

        if to_tag:
            prime.tags.area_tags.append(area_tag)

    proceed()


@desc()
def calc_diff_tag(ctx: Context, proceed, prev_batch: list[str], work_dir: Path):
    """
    计算差分标签
    """

    if not prev_batch:
        return proceed()

    a_polygons = [
        geo_differ.Polygon(face_id=str(p.strategy_id), geom=p.geom.wkt, uid=p.strategy_id) for p in ctx.primes
    ]
    save_path = work_dir / FILE_NAME_DIFF
    polygon_diff.process_prime(a_polygons, prev_batch, save_path)

    rows = list(tsv.read_tsv(save_path, splitter=","))
    new_ids = {int(x[0]) for x in tqdm(rows, desc="filter new") if filter_new(x)}
    contain_ids = {int(x[0]) for x in tqdm(rows, desc="filter contain") if filter_contain(x)}

    for prime in ctx.primes:
        if prime.strategy_id in new_ids:
            prime.tags.diff_tag = "new"
        elif prime.strategy_id in contain_ids:
            prime.tags.diff_tag = "contain"

    proceed()


@desc()
def save_tag(ctx: Context, proceed, save_path: Path):
    rows = [
        (
            p.task_id,
            p.strategy_id,
            len(p.store_pois),
            p.pv_sum,
            p.tags.pv_tag,
            ",".join(p.tags.area_tags),
            p.tags.diff_tag,
        )
        for p in ctx.primes
    ]
    tsv.write_tsv(utils.ensure_path(save_path), rows)
    proceed()


@desc()
def save_pv_tag_to_db(ctx: Context, proceed):
    """
    pv 标签保存到 db
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    curs = conn.cursor()
    for p in ctx.primes:
        qry = """
        update park_storefront_strategy 
        set tags = jsonb_set(tags, '{pv}', %s, true) 
        where id = %s
        """
        curs.execute(qry, [Json(p.tags.pv_tags), p.strategy_id])
        # print(qry)
    proceed()


@desc()
def save_poi(ctx: Context, proceed, save_path: Path):
    rows = [
        (p.task_id, p.strategy_id, poi.bid, poi.name, poi.click_pv, poi.std_tag, poi.point.wkt)
        for p in ctx.primes
        for poi in p.store_pois
    ]
    tsv.write_tsv(utils.ensure_path(save_path), rows)
    proceed()


# helpers:


def filter_new(row: tuple):
    """
    [处理 郑成-diff 标准输出文件] 判断是否新增
    """
    new_id, diff_type, old_ids, info_str = row
    return diff_type == "new"


def filter_contain(row: tuple, threshold=30 * METER):
    """
    [处理 郑成-diff 标准输出文件] 若新面包含旧面，且新面长于旧面 30m，就认为是新的，因为人工作业最多只看一个情报面延长 30m 的范围
    """
    new_id, diff_type, old_ids, info_str = row
    if diff_type in ("contain", "other"):
        info = eval(info_str)
        lines = [wkt.loads(x) for x in info["link"]["a_diffed"]]
        lines = [single_ln for ln in lines for single_ln in geometric.flat_line(ln)]
        merged_line = shapely.ops.linemerge(lines)
        lines = geometric.flat_line(merged_line)
        length = sum([x.length for x in lines])
        if length > threshold:
            return True

    return False


def read_tag_file(file_path: Path):
    idx_task_id = 0
    idx_prime_id = 1
    idx_poi_count = 2
    idx_pv = 3
    idx_pv_tag = 4
    idx_area_tags = 5
    idx_diff_tag = 6

    for x in tsv.read_tsv(file_path):
        yield TaggedPrime(
            task_id=int(x[idx_task_id]),
            prime_id=int(x[idx_prime_id]),
            poi_count=int(x[idx_poi_count]),
            face_pv=int(x[idx_pv]),
            tags=PrimeTags(
                pv_tag=x[idx_pv_tag],
                area_tags=x[idx_area_tags].split(","),
                diff_tag=x[idx_diff_tag] if len(x) > idx_diff_tag else "",
            ),
        )


def _process_fetch_store_pois(task: tuple[int, str, float]):
    strategy_id, geom, search_buffer = task
    return strategy_id, autocomplete.get_relation_store_pois(geom, search_buffer=search_buffer)


def _get_bua_area(city_zh: str):
    sql = """
        select st_astext(st_buffer(st_unaryunion(st_buffer(st_collect(geom), 1 * 1e-5)), -1 * 1e-5))
        from bua_area_20241226
        where city_name = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [city_zh])
    return ret[0]


def _get_wkt_from_file(*file_path: Path):
    wkts = [x.read_text().strip() for x in file_path]
    geoms = [wkt.loads(x) for x in wkts]
    geom = shapely.unary_union(geoms)
    return geom.wkt


def _get_primes_by_batch(batch: str):
    sql = """
        select a.task_id, b.id, st_astext(b.geom), tags 
        from park_storefront_task a
        inner join park_storefront_strategy b on a.task_id = b.task_id
        where step = 'PRIME' and a.batch = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [batch])
    return [
        PrimeFace(
            task_id=task_id,
            strategy_id=strategy_id,
            geom=wkt.loads(geom),
            tags=PrimeTags.from_dict(tags)
        ) for task_id, strategy_id, geom, tags in ret
    ]


def _get_bua_area_no_build_beijing():
    build_wkt = _get_wkt_from_file(utils_dir / "wkt_beijing_core.txt", utils_dir / "wkt_beijing_haidian.txt")
    bua_area_wkt = _get_bua_area("北京市")
    build_geom = wkt.loads(build_wkt)
    bua_area_geom = wkt.loads(bua_area_wkt)
    return bua_area_geom.difference(build_geom).wkt


def _get_bua_area_no_build_shanghai():
    build_wkt = _get_wkt_from_file(utils_dir / "wkt_shanghai_core.txt")
    bua_area_wkt = _get_bua_area("上海市")
    build_geom = wkt.loads(build_wkt)
    bua_area_geom = wkt.loads(bua_area_wkt)
    return bua_area_geom.difference(build_geom).wkt


def _get_area_tag_provider(city_zh: str):
    city_map = {
        "北京市": (
            "core_and_haidian",
            lambda: _get_wkt_from_file(utils_dir / "wkt_beijing_core.txt", utils_dir / "wkt_beijing_haidian.txt"),
        ),
        "上海市": ("mid_ring", lambda: _get_wkt_from_file(utils_dir / "wkt_shanghai_core.txt")),
    }
    return city_map.get(city_zh, ("bua_area", lambda: _get_bua_area(city_zh)))


def _get_area_tag_provider_v2(city_zh: str):
    city_map = {
        # "北京市": (
        #     "core_and_haidian",
        #     lambda: _get_wkt_from_file(utils_dir / "wkt_beijing_core.txt", utils_dir / "wkt_beijing_haidian.txt"),
        # ),
        # "上海市": ("mid_ring", lambda: _get_wkt_from_file(utils_dir / "wkt_shanghai_core.txt")),
    }
    return city_map.get(city_zh, ("bua_area", lambda: _get_bua_area(city_zh)))


def use_all_primes(_: PrimeFace):
    """
    所有的 prime 都用于 pv 排序
    """
    return True


def use_primes_in_bua(prime: PrimeFace):
    """
    仅将建成区内的 prime 用于 pv 排序
    """
    return "bua_area" in prime.tags.area_tags


def use_primes_in_non_bua(prime: PrimeFace):
    """
    非建成区返回 True
    """
    return "bua_area" not in prime.tags.area_tags


def execute(batch: str, prev_batch: list[str], city_py: str, work_dir: Path):
    """
    对外导出函数
    """
    city_zh = cityutils.PINYIN_TO_ZH[city_py]
    city_zhs = [x[0] for x in cityutils.CITY_ITEMS]
    idx_city = city_zhs.index(city_zh)
    # area_wkt = _get_bua_area_no_build_beijing()
    # area_wkt = _get_bua_area_no_build_shanghai()
    area_tag, get_area = _get_area_tag_provider(city_zh)
    pipe = pipeline.Pipeline(
        partial(fetch_store_pois, search_buffer=50 * METER),
        calc_pv_sum,
        partial(calc_area_tag, area_tag=area_tag, area_wkt=get_area()),
        # partial(calc_area_tag, area_tag="bua_area_no_build", area_wkt=area_wkt),
        # TOP45 城使用“TOP80 pv 中的建成区部分”，后面的城市使用“建成区中的 TOP80 pv 部分”
        partial(calc_pv_tag, thresholds=[0.6, 0.8, 0.9], fn=use_all_primes if idx_city < 45 else use_primes_in_bua),
        partial(calc_diff_tag, prev_batch=prev_batch, work_dir=work_dir),
        partial(save_tag, save_path=utils.ensure_path(work_dir / FILE_NAME_TAG)),
        partial(save_poi, save_path=utils.ensure_path(work_dir / FILE_NAME_POI)),
    )
    desc.attach(pipe)
    ctx = Context(primes=_get_primes_by_batch(batch))
    pipe(ctx)


def primes_are_tagged_pv(primes: list[PrimeFace]) -> bool:
    """
    都打上了 pv 标签返回 True
    """
    if len(primes) == 0:
        return False
    for p in primes:
        if len(p.tags.pv_tags) == 0:
            return False
    return True


def tag_prime_pv(batch: str, city_py: str, refresh: bool = False):
    """
    为某个城市的某批数据打 pv 标签
    某些数据可能有多个标签，比如
    BUA_TOP80 建成区 top80
    NON_BUA_TOP80 非建成区 top80
    CITY_TOP80 全城市的 top80
    """
    plan_dir = Path("/home/<USER>/dingping/storefront/product/")
    work_dirs = str(batch).split('_')
    work_dir = plan_dir / work_dirs[1] / batch / "prime"
    city_zh = cityutils.PINYIN_TO_ZH[city_py]
    area_tag, get_area = _get_area_tag_provider_v2(city_zh)
    pipe = pipeline.Pipeline(
        # partial(fetch_store_pois, search_buffer=50 * METER),
        partial(calc_pv_num_by_file, work_dir=work_dir),
        partial(calc_area_tag, area_tag=area_tag, area_wkt=get_area()),
        partial(calc_pv_tags, thresholds=[0.6, 0.8, 0.9], fn=use_all_primes, tag_pre='CITY_'),
        partial(calc_pv_tags, thresholds=[0.6, 0.8, 0.9], fn=use_primes_in_bua, tag_pre='BUA_'),
        partial(calc_pv_tags, thresholds=[0.6, 0.8, 0.9], fn=use_primes_in_non_bua, tag_pre='NON_BUA_'),
        save_pv_tag_to_db,
    )
    desc.attach(pipe)
    ctx = Context(primes=_get_primes_by_batch(batch))
    if not refresh and primes_are_tagged_pv(ctx.primes):
        print(f"{city_py}; {batch} 都已经打上了标签，且没有强制刷新，故结束")
        return
    if refresh:
        for p in ctx.primes:
            p.tags.pv_tags = []
    pipe(ctx)


def tag_city_latest_batch_prime_pv():
    """
    为城市最近一个批次打标签
    """
    # city_py2batch1 = _get_city_py2batch()
    city_py2batch = _get_cit_py2batch_v2()

    white_city_py = [
        # 'shanghai',
        # 'beijing',
        # 'wuzhou',
    ]
    for city_py, batch in tqdm(city_py2batch.items(), desc="遍历城市打 pv 标签"):
        if len(white_city_py) and city_py not in white_city_py:
            continue
        try:
            print(f"{city_py}; {batch} 开始打 pv 标签")
            # tag_prime_pv(batch, city_py, refresh=False)
            tag_prime_pv(batch, city_py, refresh=True)
        except Exception as e:
            logging.exception(e)
            print(f"{city_py}; {batch} 处理异常")
        # break


def _get_city_py2batch():
    qry = f"""
    SELECT batch, MAX(task_id) AS max_task_id
    FROM park_storefront_task
    GROUP BY batch
    ORDER BY max_task_id DESC;
    """
    tasks = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    print(f"有批次号：{len(tasks)}")

    city_py2batch = {}
    for t in tasks:
        batch = t[0]
        city_py = str(batch).split('_')[0]
        if city_py in city_py2batch:  # 每个城市保留最新的一个批次
            continue
        city_py2batch[city_py] = batch
    return city_py2batch


def _get_cit_py2batch_v2():
    qry = f"""
    SELECT batch 
    FROM park_storefront_task
    GROUP BY batch
    """
    resp = dbutils.fetch_all(pgsql.POI_CONFIG, qry)

    tasks = []
    for r in resp:
        batches = str(r[0]).split('_')
        if len(batches) != 3:
            continue
        tasks.append({
            'city_py': batches[0],
            'date': batches[2],
            'batch': r[0],
        })
    tasks.sort(key=lambda x: x['date'], reverse=True)

    city_py2batch = {}
    for t in tasks:
        batch = t['batch']
        city_py = t['city_py']
        if city_py in city_py2batch:  # 每个城市保留最新的一个批次
            continue
        city_py2batch[city_py] = batch
    return city_py2batch


if __name__ == '__main__':
    tag_city_latest_batch_prime_pv()

