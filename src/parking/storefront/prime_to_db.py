"""
导入原始面相关的线下数据到数据库中
"""
import sys
from pathlib import Path

import psycopg2.extras
from loguru import logger
from psycopg2.extras import Json

from src.parking.storefront import task_flow, prime_flow
from src.tools import tsv, pgsql, linq


def save_prime(prime_path: Path, segment_path: Path):
    """
    导入原始面（一并导入观察点到 result jsonb 中）
    """

    def parse_segment_values(segment_file: Path):
        idx_segment_id = 2
        idx_view_point = 3
        idx_target_point = 4

        segments = list(tsv.read_tsv(segment_file))
        values = [
            (x[idx_segment_id], f"SRID=4326;{x[idx_view_point]}", f"SRID=4326;{x[idx_target_point]}") for x in segments
        ]
        return values

    def parse_prime_values(prime_file: Path, segments: dict[str, list[str]]):
        idx_task_id = 0
        idx_face_id = 1
        idx_width = 3
        idx_process = 5
        idx_baseline = -2
        idx_geom = -1

        primes = list(tsv.read_tsv(prime_file))
        values = [
            (
                x[idx_task_id],
                x[idx_face_id],
                f"SRID=4326;{x[idx_baseline]}",
                f"SRID=4326;{x[idx_geom]}",
                prime_flow.VERSION,
                "PRIME",
                Json(
                    {"width": x[idx_width], "process": x[idx_process], "segment_ids": segments.get(x[idx_face_id], [])}
                ),
            )
            for x in primes
        ]
        return values

    def get_task_ids(prime_file: Path):
        idx_task_id = 0
        return {x[idx_task_id] for x in tsv.read_tsv(prime_file)}

    def get_segment_dict(segment_file: Path) -> dict[str, list[str]]:
        idx_face_id = 1
        idx_segment_id = 2

        segments = list(tsv.read_tsv(segment_file))
        face_id2segment_ids = linq.group_by(segments, key=lambda x: x[idx_face_id], value=lambda x: x[idx_segment_id])
        return face_id2segment_ids

    sql_prime = """
        insert into park_storefront_strategy (task_id, face_id, baseline, geom, strategy, step, result)
        values %s
    """
    sql_segment = """
        insert into park_storefront_segment (segment_id, view_point, target_point)
        values %s
        on conflict (segment_id) do nothing
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cur:
        # 1. 导入原始面：观察点 id 会插入原始面中
        segment_dict = get_segment_dict(segment_path)
        prime_values = parse_prime_values(prime_path, segment_dict)
        psycopg2.extras.execute_values(cur, sql_prime, prime_values, page_size=1000)
        logger.info(f"inserted {len(prime_values)} primes")

        # 2. 导入观察点
        segment_values = parse_segment_values(segment_path)
        psycopg2.extras.execute_values(cur, sql_segment, segment_values, page_size=1000)
        logger.info(f"inserted {len(segment_values)} segments")

        # 3. 更新任务状态
        task_ids = get_task_ids(prime_path)
        task_flow.update_task_status(conn, task_ids, "INIT", "GENERATED")
        logger.info(f"updated {len(task_ids)} tasks from 'INIT' to 'GENERATED'")


# helpers:


def main(operator: str, work_dir: Path):
    """
    主函数
    """
    if operator == "prime":
        prime_path = work_dir / "polygon_valid.tsv"
        segment_path = work_dir / "segment.tsv"
        save_prime(prime_path, segment_path)
    else:
        raise ValueError(f"unknown operator: {operator}")


if __name__ == "__main__":
    main(sys.argv[1], Path(sys.argv[2]))
