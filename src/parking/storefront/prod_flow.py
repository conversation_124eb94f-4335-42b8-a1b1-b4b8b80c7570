"""
门前情报生产流程，该流程负责：一个城市从开始到工艺掘金准入之前的工作
1. 生产原始面
2. 生成并上传观察点掘金核实资料
"""
import dataclasses
import logging
import os
import shutil
import sys
import warnings
from dataclasses import dataclass, field
from datetime import datetime
from functools import partial
from pathlib import Path
from typing import Callable

import requests
from loguru import logger
from retrying import retry

import src.tools.redis_tool as rt
from src.parking.recognition import dbutils
from src.parking.storefront import prime_flow, region_task, prime_tag, prime_to_db
from src.parking.storefront.approval import push_info, icafe
from src.parking.storefront.utils import cityutils
from src.parking.storefront.verify import verified_image, verified_to_db
from src.tools import pgsql, utils, pipeline, afs_tool, function, tsv, ruliu

warner = logging.getLogger(__name__)
desc = pipeline.get_desc()
AFS_TOOL = afs_tool.AfsTool("aries_rpm01")

PROD_DIR = Path(os.getenv("STOREFRONT_PROD_DIR", "product"))

STEP_PRIME_GENERATED = "DONE.PRIME.GENERATED"
STEP_PRIME_SAVE_TO_DB = "DONE.PRIME.SAVE_TO_DB"
STEP_PRIME_TAGGED = "DONE.PRIME.TAGGED"
STEP_IMAGE_DOWNLOADED = "DONE.IMAGE.DOWNLOADED"
STEP_IMAGE_SAVE_TO_BD = "DONE.IMAGE.SAVE_TO_DB"
STEP_IMAGE_SAVE_TO_AFS = "DONE.IMAGE.SAVE_TO_AFS"
STEP_NOTICE_COLLECT = "DONE.COLLECT.PUSH_TO_REDIS"
STEP_NOTICE_ICAFE = "DONE.APPROVAL_PUSH.CREATE_ICAFE"


@dataclass
class Context:
    """
    上下文
    """

    plan_id: str
    city_zh: str
    prod_dir: Path
    batch_dir: Path = field(init=False)
    prime_dir: Path = field(init=False)
    verified_dir: Path = field(init=False)
    image_dir: Path = field(init=False)
    city_py: str = field(default="")
    batch: str = field(default="")
    batch_image: str = field(default="")
    prev_batch: list[str] = field(default_factory=list)

    def __post_init__(self):
        self.city_py = cityutils.ZH_TO_PINYIN[self.city_zh]
        # 对同一个计划 ID 而言，应当优先选择本地已有的批次进行断点重试
        self.batch = self.__try_get_local_batch()
        if self.batch is None:
            date_str = datetime.now().strftime("%Y%m%d")
            self.batch = f"{self.city_py}_{self.plan_id}_{date_str}"

        self.batch_dir = utils.ensure_dir(self.prod_dir / self.batch)
        self.prime_dir = utils.ensure_dir(self.batch_dir / "prime")
        self.verified_dir = utils.ensure_dir(self.batch_dir / "verified")
        self.image_dir = utils.ensure_dir(self.verified_dir / "image")
        _init_global_logging(self.batch_dir)

    def has_flag(self, flag: str):
        return _has_flag(self.batch_dir, flag)

    def set_flag(self, flag: str, payload=""):
        _set_flag(self.batch_dir, flag, payload)

    def __try_get_local_batch(self):
        local_dirs = [x for x in self.prod_dir.glob(f"{self.city_py}_{self.plan_id}_*") if x.is_dir()]
        if len(local_dirs) == 0:
            return None
        else:
            return local_dirs[0].name


@desc()
def create_task_if_not_exists(ctx: Context, proceed):
    """
    pipe: 创建当天批次的任务，如果已经存在，则跳过
    """
    tasks = region_task.get_tasks_by_batch(ctx.batch)
    if len(tasks) > 0:
        return proceed()

    sql_insert_full = """
        insert into park_storefront_task (region_id, geom, batch, city)
        select face_id, geom, %s, city from street_region
        where city = %s
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute(conn, sql_insert_full, [ctx.batch, ctx.city_zh])

    # 确认一下任务是否设置成功，如果没有，可能是因为街区表里'xx市'和'xx地区'不匹配，所以用模糊匹配再试一次
    sql_count = """
        select count(*) from park_storefront_task
        where batch = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql_count, [ctx.batch])
    task_count = ret[0]
    if task_count <= 0:
        sql_insert_patch = """
            insert into park_storefront_task (region_id, geom, batch, city)
            select face_id, geom, %s, city from street_region
            where city ~ %s
        """
        city_zh = ctx.city_zh.rstrip("地区").rstrip("市")
        with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
            pgsql.execute(conn, sql_insert_patch, [ctx.batch, city_zh])

    proceed()


@desc()
def run_prime_task(ctx: Context, proceed):
    """
    pipe: 执行原始面生产流程
    """
    if ctx.has_flag(STEP_PRIME_GENERATED):
        return proceed()

    prime_dir = ctx.prime_dir
    _clear_and_create_folder(prime_dir)
    tasks = region_task.get_tasks_by_batch(ctx.batch)
    assert len(tasks) > 0, f"the count of tasks must be greater than 0: {ctx.batch}"

    prime_flow.execute(tasks, prime_dir)
    ctx.set_flag(STEP_PRIME_GENERATED)
    proceed()


@desc()
def save_prime_to_db(ctx: Context, proceed):
    """
    pipe: 将原始面保存到数据库中
    """
    if ctx.has_flag(STEP_PRIME_SAVE_TO_DB):
        return proceed()

    prime_path = ctx.prime_dir / "polygon_valid.tsv"
    segment_path = ctx.prime_dir / "segment.tsv"
    prime_to_db.save_prime(prime_path, segment_path)

    ctx.set_flag(STEP_PRIME_SAVE_TO_DB)
    proceed()


@desc()
def calc_prime_tag(ctx: Context, proceed):
    """
    pipe: 计算原始面的标签
    """
    if ctx.has_flag(STEP_PRIME_TAGGED):
        return proceed()

    for x in ctx.prev_batch:
        ctx.set_flag(f"DIFF.{x}")

    prime_tag.execute(ctx.batch, ctx.prev_batch, ctx.city_py, ctx.prime_dir)

    ctx.set_flag(STEP_PRIME_TAGGED)
    proceed()


@desc()
def download_verified_image(
    ctx: Context, proceed, filter_flag: str, filter_prime: Callable[[prime_tag.PrimeTags], bool]
):
    """
    pipe: 下载掘金核实图片
    """
    if ctx.has_flag(STEP_IMAGE_DOWNLOADED):
        return proceed()

    ctx.set_flag(f"FLAG.{filter_flag.upper()}")
    shutil.rmtree(ctx.image_dir, ignore_errors=True)

    tag_path = ctx.prime_dir / prime_tag.FILE_NAME_TAG
    prime_ids = [x.prime_id for x in prime_tag.read_tag_file(tag_path) if filter_prime(x.tags)]
    verified_image.execute(prime_ids, ctx.image_dir)

    ctx.set_flag(STEP_IMAGE_DOWNLOADED)
    proceed()


def build_image_batch(ctx: Context, proceed):
    """
    构建掘金批次号
    """
    afs_flag_file = ctx.batch_dir / STEP_IMAGE_SAVE_TO_AFS
    if afs_flag_file.exists():
        # 重试的时候，如果先前已经完成 AFS 上传了，就用之前的批次
        ctx.batch_image = afs_flag_file.read_text().strip()
    else:
        today = datetime.now().strftime("%Y%m%d")
        suffix = "_".join(sorted(x.name.replace("FLAG.", "").lower() for x in ctx.batch_dir.glob("FLAG.*")))
        ctx.batch_image = f"{ctx.batch_dir.name}_{suffix}_{today}"

    proceed()


@desc()
def export_juejin_approval_push(ctx: Context, proceed):
    """
    pipe: 导出掘金准入报告 json 文件
    """
    approval_path = utils.ensure_path(ctx.verified_dir / "approval_push_analysis.json")
    analysis = push_info.get_analysis(ctx.image_dir)
    utils.write_json(approval_path, dataclasses.asdict(analysis))
    proceed()


@desc()
def save_image_to_db(ctx: Context, proceed):
    """
    pipe: 将图片信息保存到数据库中
    """
    if ctx.has_flag(STEP_IMAGE_SAVE_TO_BD):
        return proceed()

    verified_to_db.save_image(ctx.image_dir, ctx.batch_image)

    ctx.set_flag(STEP_IMAGE_SAVE_TO_BD)
    proceed()


@desc()
def save_image_to_afs(ctx: Context, proceed):
    """
    pipe: 将图片保存到 AFS
    """
    if ctx.has_flag(STEP_IMAGE_SAVE_TO_AFS):
        return proceed()

    # 1. 打包
    tar_path = ctx.image_dir / f"{ctx.batch_image}.tar"
    tar_path.unlink(missing_ok=True)
    function.exec_shell_cmd(
        cmd=f"tar -cf {tar_path.name} side_view",
        cwd=tar_path.parent,
    )

    # 2. 上传
    afs_root = "/user/map-data-streeview/aoi-ml/parking/storefront/street_picture"
    AFS_TOOL.put(tar_path, afs_root)
    assert AFS_TOOL.test(f"{afs_root}/{tar_path.name}", mode="-e"), f"failed to put '{tar_path}'"

    # 3. 删除本地文件
    tar_path.unlink()
    logger.info(f"uploaded to afs: {ctx.image_dir}")

    ctx.set_flag(STEP_IMAGE_SAVE_TO_AFS, tar_path.stem)
    proceed()


@desc()
def clear_side_view_dir(ctx: Context, proceed):
    """
    在 side_view 被上传至如流后，及时清理本地资源
    """
    side_view_dir = ctx.image_dir / "side_view"
    if not side_view_dir.exists():
        return proceed()

    # 保留一份图片名称文件，因为图片可能下载失败，所以不一定和 image.*.tsv 对应的上
    side_view_path = ctx.image_dir / "side_view.txt"
    image_name_rows = [[x.name] for x in side_view_dir.glob("*.jpg")]
    tsv.write_tsv(side_view_path, image_name_rows)

    shutil.rmtree(side_view_dir)
    proceed()


@desc()
def push_batch_to_redis(ctx: Context, proceed):
    """
    pipe: 上传批次到 redis，给老范投采集
    """
    if ctx.has_flag(STEP_NOTICE_COLLECT):
        return proceed()

    _add_push_batch(ctx.batch)

    ctx.set_flag(STEP_NOTICE_COLLECT)
    proceed()


@desc()
def create_icafe_issue(ctx: Context, proceed):
    """
    pipe: 创建 icafe 卡片
    """
    if ctx.has_flag(STEP_NOTICE_ICAFE):
        return proceed()

    analysis = push_info.get_analysis(ctx.image_dir)
    detail = icafe.get_approval_push_detail(analysis)
    fields = icafe.get_approval_push_fields(
        plan_id=ctx.plan_id,
        batch=ctx.batch,
        batch_image=ctx.batch_image,
        batch_diff=",".join(sorted(ctx.prev_batch)),
        missing_url=upload_file(ctx.image_dir / "missing_reason.tsv"),
    )
    icafe_url = icafe.create_issue(
        card_type=icafe.CARD_TYPE_PUSH,
        title=f"【门前 - 准入报告】{ctx.city_zh} - {ctx.batch_image}",
        detail=detail,
        fields=fields,
    )
    if icafe_url:
        ctx.set_flag(STEP_NOTICE_ICAFE, icafe_url)

    proceed()


def notify_if_exception(ctx: Context, proceed):
    """
    作为第一个 pipe，捕获整个管线中的未处理异常，向如流报警
    """
    try:
        proceed()
    except Exception as ex:
        msg = (
            f"【原始面 - 任务失败】\n"
            f"- 任务批次：{ctx.batch}\n"
            f"- 掘金批次：{ctx.batch_image}\n"
            f"- 任务路径：{ctx.batch_dir}\n"
            f"- 异常信息：{ex}\n"
        )
        ruliu.send(
            [
                {"type": "TEXT", "content": msg},
                {"type": "AT", "atuserids": ["zhangdingping_cd"]},
            ]
        )


# helpers:


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(file_path: Path) -> str:
    """
    上传文件
    """
    url_post = "http://mapde-poi.baidu-int.com/prod/compat/upload"
    url_get = "https://mapde-poi.baidu-int.com/beefs/get?uuid={0}"

    files = {"file": open(file_path, "rb")}
    response = requests.post(url_post, files=files)
    uuid = response.text
    return url_get.format(uuid)


def _set_flag(folder: Path, flag: str, payload=""):
    with open(folder / flag, "w") as f:
        f.write(payload)


def _has_flag(folder: Path, flag: str):
    file = folder / flag
    return file.exists()


def _clear_and_create_folder(folder: Path):
    if folder.exists():
        shutil.rmtree(folder, ignore_errors=True)

    utils.ensure_dir(folder)


def _add_push_batch(batch_juejin: str):
    with rt.RedisTool("aoi") as rt_client:
        qingbao_queue_key = f"storefront_qingbao_city"
        rt_client.redis_conn.lpush(qingbao_queue_key, batch_juejin)


def _init_global_logging(batch_dir: Path):
    def warn_to_logger(message, category, filename, lineno, _file=None, _line=None):
        warner.warning(f"{filename}:{lineno}: {category.__name__}: {message}")

    warnings.filterwarnings("always")
    warnings.showwarning = warn_to_logger

    # configure loguru
    logger.remove()
    logger.add(batch_dir / "flow_loguru.log")

    # configure logging
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    for handler in root_logger.handlers[:]:
        handler.flush()
        handler.close()
        root_logger.removeHandler(handler)

    handler = logging.FileHandler(batch_dir / "flow_logging.log", mode="a")
    formatter = logging.Formatter("%(asctime)s | %(levelname)s | %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
    handler.setFormatter(formatter)
    root_logger.addHandler(handler)


def _get_filter_prime_beijing(enable_diff: bool):
    position_tags = {"core_and_haidian"}
    pv_tags = {"TOP60", "TOP60-TOP80", "TOP80-TOP90", "TOP90-TOP100"}
    diff_tags = {"new", "contain"} if enable_diff else {""}

    def fn(tags: prime_tag.PrimeTags) -> bool:
        return tags.pv_tag in pv_tags and set(tags.area_tags) & position_tags and tags.diff_tag in diff_tags

    return "core_and_haidian_top100", fn


def _get_filter_prime_shanghai_mid_ring(enable_diff: bool):
    position_tags = {"mid_ring"}
    pv_tags = {"TOP60", "TOP60-TOP80", "TOP80-TOP90", "TOP90-TOP100"}
    diff_tags = {"new", "contain"} if enable_diff else {""}

    def fn(tags: prime_tag.PrimeTags) -> bool:
        return tags.pv_tag in pv_tags and set(tags.area_tags) & position_tags and tags.diff_tag in diff_tags

    return "mid_ring_top100", fn


def _get_filter_prime_shanghai_bua_diff_mid_ring(enable_diff: bool):
    pv_tags = {"TOP60", "TOP60-TOP80"}
    diff_tags = {"new", "contain"} if enable_diff else {""}

    def fn(tags: prime_tag.PrimeTags) -> bool:
        return (
            tags.pv_tag in pv_tags
            and len(tags.area_tags) == 1
            and "bua_area" in tags.area_tags
            and tags.diff_tag in diff_tags
        )

    return "bua_diff_mid_ring_top80", fn


def _get_filter_prime_shanghai(enable_diff: bool):
    _, mid_ring = _get_filter_prime_shanghai_mid_ring(enable_diff)
    _, bua_diff_mid_ring = _get_filter_prime_shanghai_bua_diff_mid_ring(enable_diff)

    def fn(tags: prime_tag.PrimeTags) -> bool:
        return mid_ring(tags) or bua_diff_mid_ring(tags)

    return "mid_ring_top100_and_bua_area_top80", fn


def _get_filter_prime_std(enable_diff: bool):
    position_tags = {"bua_area"}
    pv_tags = {"TOP60", "TOP60-TOP80"}
    diff_tags = {"new", "contain"} if enable_diff else {""}

    def fn(tags: prime_tag.PrimeTags) -> bool:
        return tags.pv_tag in pv_tags and set(tags.area_tags) & position_tags and tags.diff_tag in diff_tags

    return "bua_area_top80", fn


def _get_filter_prime_pv_in_bua(enable_diff: bool):
    position_tags = {"bua_area"}
    pv_tags = {"TOP60", "TOP60-TOP80"}
    diff_tags = {"new", "contain"} if enable_diff else {""}

    def fn(tags: prime_tag.PrimeTags) -> bool:
        return tags.pv_tag in pv_tags and set(tags.area_tags) & position_tags and tags.diff_tag in diff_tags

    return "pv_in_bua_top80", fn


def get_filter_by_city_zh(city_zh: str):
    """
    根据城市名称获取对应的过滤器，以兼容乱七八糟的过滤规则
    """
    filter_map = {
        "北京市": _get_filter_prime_beijing,
        "上海市": _get_filter_prime_shanghai,
    }
    if city_zh in filter_map:
        return filter_map[city_zh]
    else:
        city_zhs = [x[0] for x in cityutils.CITY_ITEMS]
        idx_city = city_zhs.index(city_zh)
        # TOP45 城使用“TOP80 pv 中的建成区部分”，后面的城市使用“建成区中的 TOP80 pv 部分”
        if idx_city < 45:
            return _get_filter_prime_std
        else:
            return _get_filter_prime_pv_in_bua


@logger.catch
def main(plan_id: str, city_zh: str, prev_batch: list[str]):
    """
    主函数
    """
    filter_getter = get_filter_by_city_zh(city_zh)
    filter_flag, filter_prime = filter_getter(bool(prev_batch))
    pipe = pipeline.Pipeline(
        notify_if_exception,
        create_task_if_not_exists,
        run_prime_task,
        save_prime_to_db,
        calc_prime_tag,
        partial(download_verified_image, filter_flag=filter_flag, filter_prime=filter_prime),
        build_image_batch,
        export_juejin_approval_push,
        save_image_to_db,
        save_image_to_afs,
        push_batch_to_redis,
        create_icafe_issue,
        clear_side_view_dir,
    )
    desc.attach(pipe)
    desc.output = logger.info

    prod_dir = PROD_DIR
    prod_dir = prod_dir / plan_id
    ctx = Context(plan_id, city_zh, prod_dir=prod_dir, prev_batch=prev_batch)
    pipe(ctx)


if __name__ == "__main__":
    b_batch = sys.argv[3:] if len(sys.argv) > 3 else []
    main(sys.argv[1], sys.argv[2], b_batch)
