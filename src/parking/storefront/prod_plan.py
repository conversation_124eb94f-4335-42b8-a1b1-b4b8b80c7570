"""
生产计划
"""

import sys
from pathlib import Path

from src.parking.recognition import dbutils
from src.parking.storefront import prod_flow
from src.tools import tsv, pgsql, linq


def get_plan_id(batch: str):
    """
    获取计划ID
    TODO: 未来应该建一个 park_storefront_plan 表来查
    """
    fragments = batch.split("_")
    if len(fragments) == 3:
        return fragments[1]
    else:
        return ""


def get_prev_batch_map():
    """
    获取之前的批次列表，按城市中文名分组
    """
    sql = """
        select a.city, a.batch
        from park_storefront_task a
        join park_storefront_strategy b on a.task_id = b.task_id
        where a.city != '' group by 1,2;
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    return linq.group_by(ret, key=lambda x: x[0], value=lambda x: x[1])


def main(plan_path: Path):
    """
    主函数
    """
    plan_id = plan_path.stem.lower()
    assert plan_id.isalnum(), plan_id

    city_zh_list = [x[0] for x in tsv.read_tsv(plan_path)]
    prev_batch_map = get_prev_batch_map()

    for city_zh in city_zh_list:
        print("START", city_zh)
        prev_batch = prev_batch_map.get(city_zh, [])
        # 因为可能运行失败，需要重试，那么 prev_batch 中不应该包含当前任务计划的批次
        prev_batch = [x for x in prev_batch if get_plan_id(x) != plan_id]
        prod_flow.main(plan_id, city_zh, prev_batch)
        print("END", city_zh)


if __name__ == "__main__":
    main(Path(sys.argv[1]))
