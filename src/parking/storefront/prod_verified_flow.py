"""
门前情报生产流程，该流程负责：掘金核实完成后到工艺准出之前的工作
"""
import sys
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path

import requests
from retrying import retry
from tqdm import tqdm

import src.parking.storefront.diff.polygon_differ as geo_differ
from src.parking.storefront import task_flow, prod_flow, prime_tag, polygon_diff
from src.parking.storefront.approval import pull_info, icafe
from src.parking.storefront.utils import cityutils
from src.parking.storefront.verify import verified_flow
from src.tools import utils, pipeline, tsv


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def upload_file(file_path: Path) -> str:
    """
    上传文件
    """
    url_post = "http://mapde-poi.baidu-int.com/prod/compat/upload"
    url_get = "https://mapde-poi.baidu-int.com/beefs/get?uuid={0}"

    files = {"file": open(file_path, "rb")}
    response = requests.post(url_post, files=files)
    uuid = response.text
    return url_get.format(uuid)


def get_batch_diff(batch_dir: Path) -> str:
    """
    获取批次差异
    """
    names = [x.name.replace("DIFF.", "") for x in batch_dir.glob("DIFF.*")]
    return ",".join(sorted(names))


def calc_verified_tag(polygon_dir: Path):
    """
    输出核实面的 priority_analysis.tag.tsv 文件
    """
    verifieds = list(tsv.read_tsv(polygon_dir / "verified.tsv"))
    polygons = [
        geo_differ.Polygon(face_id=face_id, geom=geom, uid=face_id) for _task_id, _prev_ids, face_id, geom in verifieds
    ]
    save_path = polygon_dir / prime_tag.FILE_NAME_DIFF
    polygon_diff.process_verified(polygons, save_path)

    rows = list(tsv.read_tsv(save_path, splitter=","))
    tag_dict = {}
    for row in rows:
        face_id = row[0]
        if prime_tag.filter_new(row):
            tag_dict[face_id] = "new"
        elif prime_tag.filter_contain(row):
            tag_dict[face_id] = "contain"
        else:
            tag_dict[face_id] = "duplicate"

    tag_path = utils.ensure_path(polygon_dir / prime_tag.FILE_NAME_TAG)
    verified_ids = [x[2] for x in verifieds]
    tag_rows = [[x, tag_dict.get(x, "error")] for x in verified_ids]
    tsv.write_tsv(tag_path, tag_rows)


def main(plan_id: str, batch: str, batch_image: str, disable_tqdm=False):
    """
    主函数
    """
    batch_dir = prod_flow.PROD_DIR / plan_id / batch
    save_dir = batch_dir / "verified"
    assert (batch_dir / prod_flow.STEP_IMAGE_SAVE_TO_AFS).exists()

    work_dir = task_flow.get_output_dir(save_dir, "polygon", verified_flow.VERSION)
    print(work_dir)

    # 生成核实面
    save_pipe = pipeline.Pipeline(
        # partial(save_task_info, file_path=utils.ensure_path(work_dir / "tasks.tsv")),
        partial(verified_flow.save_prime_summary, file_path=utils.ensure_path(work_dir / "primes.tsv")),
        partial(verified_flow.save_prime, save_dir=utils.ensure_dir(work_dir / "json_infos")),
        partial(verified_flow.save_verified, file_path=utils.ensure_path(work_dir / "verified.tsv")),
        partial(verified_flow.save_segment, file_path=utils.ensure_path(work_dir / "segment.tsv")),
        verified_flow.save_tags(save_dir=work_dir),
    )
    prime_ids = task_flow.get_strategy_ids(status="VERIFYING", batch=batch, batch_image=batch_image)
    tasks = ((prime_id, batch_image) for prime_id in prime_ids)
    with Pool(32) as pool:
        for ctx in tqdm(pool.imap_unordered(verified_flow.process, tasks), total=len(prime_ids), disable=disable_tqdm):
            save_pipe(ctx)

    # 生成核实面差分文件：
    calc_verified_tag(work_dir)

    # 输出准出报告：
    analysis = pull_info.get_analysis(work_dir)
    detail = icafe.get_approval_pull_detail(analysis)
    fields = icafe.get_approval_pull_fields(
        plan_id=plan_id,
        batch=batch,
        batch_image=batch_image,
        batch_diff=get_batch_diff(batch_dir),
        batch_polygon=work_dir.name,
        polygon_url=upload_file(work_dir / "verified.tsv"),
    )
    city_py = batch.split("_")[0]
    city_zh = cityutils.PINYIN_TO_ZH[city_py]
    icafe.create_issue(
        card_type=icafe.CARD_TYPE_PULL,
        title=f"【门前 - 准出报告】{city_zh} - {batch_image}",
        detail=detail,
        fields=fields,
    )


if __name__ == "__main__":
    main(sys.argv[1], sys.argv[2], sys.argv[3])
