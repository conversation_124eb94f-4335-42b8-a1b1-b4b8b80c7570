"""
修形任务相关
"""
from pathlib import Path
from typing import Iterable

import psycopg2.extras

from src.parking.recognition import dbutils
from src.parking.storefront import storage
from src.tools import pgsql, tsv, utils, linq

TEMP_DIR = Path("tmp")


def get_store_points_by_city(city: str):
    """
    获取指定城市的所有临街POI
    """
    sql = f"""
        select st_astext(geometry), b.classify, b.bid
        from poi a
        inner join poi_spatial_classify b on a.bid = b.bid
        where a.city = '{city}';
    """
    # 从 POI 主库里查，从库的中文索引都不可用
    return _fetch_store_data(pgsql.POI_CONFIG, sql)


def get_store_points_by_wkt(wkt: str):
    """
    获取指定区域的所有临街POI
    """
    sql = f"""
        select st_astext(geometry), b.classify, b.bid
        from poi a
        inner join poi_spatial_classify b on a.bid = b.bid
        where st_contains('SRID=4326;{wkt}', geometry);
    """
    return _fetch_store_data(pgsql.POI_SLAVER_CONFIG, sql)


def get_street_regions(store_points: Iterable[str]):
    """
    获取指定临街POI所在的街区
    """
    seen_region_ids = set()
    for point in store_points:
        info = storage.get_street_region(point)
        if info is None:
            continue

        region_id, region_geom = info
        if region_id in seen_region_ids:
            continue

        seen_region_ids.add(region_id)
        yield region_id, region_geom.wkt


def set_tasks(tasks: list[tuple[str, str]], batch: str):
    """
    保存任务到数据库
    :param tasks: [(region_id, region_geom)]
    :param batch: 这批任务的批次号
    """
    sql = """
        insert into park_storefront_task (region_id, geom, batch)
        values %s;
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cur:
        values = [(region_id, f"SRID=4326;{region_geom}", batch) for region_id, region_geom in tasks]
        psycopg2.extras.execute_values(cur, sql, values, page_size=1000)


def get_tasks_by_batch(batch: str) -> list[tuple[int, str]]:
    """
    根据批次号获取任务
    """
    sql = """
        select task_id, st_astext(geom) from park_storefront_task
        where batch = %s and status = 'INIT';
    """
    return dbutils.fetch_all(pgsql.POI_CONFIG, sql, [batch])


def get_tasks_by_id(task_ids: list[int]) -> list[tuple[int, str]]:
    """
    根据任务ID获取任务
    """
    sql = """
        select task_id, st_astext(geom) from park_storefront_task
        where task_id in %s;
    """
    return dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(task_ids)])


def get_tasks_by_wkt(wkt: str) -> list[tuple[int, str]]:
    """
    根据WKT获取任务
    """
    sql = """
        select region_id, task_id, st_astext(geom) from park_storefront_task
        where st_intersects(%s, geom);
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [f"SRID=4326;{wkt}"])
    grouped = linq.group_by(ret, key=lambda x: x[0], value=lambda x: x[1:])
    return [tasks[0] for tasks in grouped.values()]


def _fetch_store_data(config, sql: str):
    # 先导出一个城市全量的数据，然后根据 classify 筛选，不要直接用 like 查 classify，不然卡死你
    temp_path = utils.ensure_path(TEMP_DIR / f"{utils.md5(sql)}.tsv", cleanup=False)
    if not temp_path.exists():
        pgsql.copy_to_tsv(config, sql, temp_path)

    city_stream = tsv.read_tsv(temp_path)
    data = (x[0] for x in city_stream if x[1].startswith("临街POI"))
    return data
