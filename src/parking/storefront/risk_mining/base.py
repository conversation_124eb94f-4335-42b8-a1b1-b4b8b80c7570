"""
风险挖掘公共函数包
"""
from typing import Iterable

from src.parking.recognition import dbutils
from src.tools import pgsql


def get_online_items():
    """
    获取在线的门前停车场
    """
    sql = """
        select distinct a.bid, b.cityname, st_astext(a.area)
        from parking a
        join mesh_conf b on a.mesh_id = b.mesh_id::int
        where a.show_tag = '门前停车场' and a.park_spec = 1 and a.status = 1
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    return ret


def get_online_item_by_bid(bids: Iterable[str]):
    """
    获取在线的门前停车场通过给定的 bids
    """
    sql = """
        select distinct a.bid, b.cityname, st_astext(a.area)
        from parking a
        join mesh_conf b on a.mesh_id = b.mesh_id::int
        where a.bid in %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (tuple(bids),))
    return ret


def get_online_bid_by_city(city: list[str]):
    """
    获取在线的门前停车场 bids 通过给定的城市
    """
    sql = """
        select bid from park_online_data
        where show_tag = '门前停车场' 
            and park_spec in (1, 2) 
            and status = 1 
            and area is not null
            and city_name in %s 
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(city)])
    return [r[0] for r in ret]


def get_online_bid():
    """
    获取全量的在线门前停车场 bids
    """
    sql = """
        select bid from park_online_data
        where show_tag = '门前停车场' 
            and park_spec in (1, 2) 
            and status = 1 
            and area is not null
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    return [r[0] for r in ret]


def get_storefront_parks():
    """
    获取全量的新门前面数据
    """
    sql = """
        select distinct bid, st_astext(area) 
        from park_online_data 
        where show_tag = '门前停车场' 
        and park_spec in (1, 2) 
        and status = 1
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    return ret


def get_parks_with_poly(gcj_poly_wkt: str):
    """
    获取给定范围内的停车场
    """
    sql = """
        select 
            distinct bid, 
            st_astext(
                case 
                    when show_area is not null then show_area
                    else area
                end
            ) as geom_text
        from parking 
        where st_intersects(area, st_geomfromtext(%s, 4326)) and status = 1
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (gcj_poly_wkt,))
    return ret


def get_parent_bid(bid: str):
    """
    获取父点bid
    """
    sql = """
        select parent_id
        from parking 
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, (bid,))
    return ret


def get_cover_aoi_poi_bid(poly_wkt: str):
    """
    获取与给定面压盖的非商单AOI.poi_bid
    """
    sql = """
        select b.poi_bid 
        from blu_face a inner join blu_face_poi b 
        on a.face_id = b.face_id
        where a.src!= 'SD' 
        and st_intersects(st_geomfromtext(%s, 4326), a.geom)
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (poly_wkt,))
    return ret


def get_poi_info(bid: str):
    """
    获取poi信息
    """
    sql = """
        select std_tag, click_pv, st_astext(geometry), relation_bid, city
        from poi
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, (bid,))
    return ret


def get_park_info(bid: str):
    """
    获取park信息
    """
    sql = """
        select 
            park_spec, 
            status, 
            show_tag, 
            st_astext(gcj_geom), 
            std_tag, 
            precise, 
            open_limit_new
        from park_online_data
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, (bid, ))
    return ret


def get_park_data(bid: str):
    """
    获取park信息
    """
    sql = """
        select 
            park_spec, 
            status, 
            show_tag, 
            st_astext(gcj_geom), 
            std_tag, 
            precise, 
            open_limit_new,
            st_astext(area)
        from park_online_data
        where bid = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, (bid, ))
    columns = [
        "park_spec",
        "status",
        "show_tag",
        "gcj_geom",
        "std_tag",
        "precise",
        "open_limit_new",
        "area"
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_park_7days_accept_ratio(bid: str):
    """
    获取停车场的7天采纳率
    """
    sql = """
        select avg(accept_percent)
        from park_low_accept_list_history
        where park_bid = %s
    """
    ret = dbutils.fetch_one(pgsql.ACCEPT_DATA, sql, (bid,))
    return ret


def get_no_area_parks_with_poly(poly_wkt: str, show_tag: str):
    """
    获取一定范围内的无面停车场
    """
    sql = """
        select distinct bid, st_astext(gcj_geom) 
        from park_online_data 
        where show_tag = %s 
        and area is null
        and status = 1
        and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, (show_tag, poly_wkt,))
    return ret


def create_duplicate_log_table():
    """
    create_duplicate_log_table
    """
    create_table_sql = f"""
        create table if not exists park_storefront_duplicate_log (
            id serial primary key,
            storefront_park_bid character varying(128) NOT NULL,
            dup_park_bid character varying(128) NOT NULL,
            remark json,
            status INT DEFAULT 0 NOT NULL,
            create_time timestamp without time zone DEFAULT now(),
            update_time timestamp without time zone DEFAULT now()
        )
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        try:
            pgsql.execute(conn, create_table_sql)
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e


def insert_duplicate_log(storefront_park_bid, dup_park_bid, remark):
    """
    插入冗余停车场记录
    """
    sql = """
        insert into park_storefront_duplicate_log (storefront_park_bid, dup_park_bid, remark)
        values (%s, %s, %s)
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        try:
            pgsql.execute(conn, sql, (storefront_park_bid, dup_park_bid, remark))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e


def is_park_storefront_prod_parking(bid):
    """
    是否是新门前产线生成的停车场bid
    """
    sql = """
        select * 
        from park_storefront_prod_parking 
        where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, (bid,))
    return ret


def get_wait_push_duplicate_parks():
    """
    获取待推送冗余停车场数据
    """
    sql = """
        select id, dup_park_bid
        from park_storefront_duplicate_log
        where status = 0
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    return ret


def get_pushed_duplicate_parks():
    """
    获取已推送冗余停车场数据
    """
    sql = """
        select id, dup_park_bid
        from park_storefront_duplicate_log
        where status = 2
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    return ret


def pushed_then_duplicate_park_status(id, status):
    """
    推送完更新推送状态
    """
    sql = """
        update park_storefront_duplicate_log 
        set status = %s 
        where id = %s
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        try:
            pgsql.execute(conn, sql, (status, id))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e


def is_line_across_road(gcj_wkt: str, road_level: int) -> bool:
    """
    判断给定linestring wkt是否跨指定等级道路
    """
    sql = """
        select * 
        from nav_link
        where kind < %s 
        and st_intersects(st_geomfromtext(%s, 4326), geom)
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, (road_level, gcj_wkt))
    return len(ret) > 0


def is_street_poi(bid: str) -> bool:
    """
    判断给定的poi是否是临街poi
    """
    if not bid or not isinstance(bid, str):
        print("[WARN] invalid bid:", bid)
        return False
    sql = """
         select distinct bid
         from poi_prod_scene_data where 
         bid = %s
         and scene_class4 ~ '临街'
    """
    ret = dbutils.fetch_all(pgsql.TRAJ_DB, sql, (bid,))
    return len(ret) > 0


def is_in_protect_list(bid: str) -> bool:
    """
    判断是否是防护集合停车场
    """
    sql = """
         select *
         from over_heat_protection 
         where park_bid = %s
    """
    ret1 = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, (bid,))

    sql = """
         select *
         from high_heat_protection 
         where park_bid = %s
    """
    ret2 = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, (bid,))
    return len(ret1 + ret2) > 0


def get_aoi_info(bid: str):
    """
    判断bid是否是非商单基础院落AOI
    """
    sql = """
        select b.poi_bid, area 
        from blu_face a inner join blu_face_poi b 
        on a.face_id = b.face_id
        where a.src!= 'SD'
        and a.aoi_level = 2  
        and b.poi_bid = %s
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql, (bid, ))
    return ret


def get_all_aoi_list():
    """
    获取所有是非商单基础院落AOI
    """
    sql = """
        select b.poi_bid, st_astext(a.geom) 
        from blu_face a inner join blu_face_poi b 
        on a.face_id = b.face_id
        where a.src!= 'SD'
        and a.aoi_level = 2  
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql)
    columns = [
        "poi_bid",
        "aoi_wkt",
    ]
    result = [dict(zip(columns, row)) for row in ret]
    return result


def get_all_duplicate_logs_remarks():
    """
    获取冗余下线履历JSON数据
    """
    sql = """
        select remark
        from park_storefront_duplicate_log
    """
    ret = dbutils.fetch_all(pgsql.POI_CONFIG, sql)
    return ret


def get_all_road_bids(park_bid: str):
    """
    获取所有的出入口bids
    """
    sql = """
        select distinct bid 
        from parking
        where parent_id = %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, (park_bid, ))
    return ret


def get_root_bid(bid: str):
    """
    获取root_bid
    """
    sql = """
          select root_bid
          from park_main_poi
          and bid = %s
       """
    ret = dbutils.fetch_one(pgsql.POI_CONFIG, sql, (bid,))
    return ret


def mc2gcj(wkt) -> str:
    """
    将百度墨卡托坐标转换为gcj02坐标
    """
    sql = "select st_astext(mc2gcj(st_geomfromtext(%s, 3857)))"
    res = dbutils.fetch_one(pgsql.COMPUTE_CONFIG, sql, (wkt,))
    return res[0]


def gcj2mc(wkt) -> str:
    """
    将 gcj02 坐标转换为百度墨卡托坐标
    """
    sql = "select st_astext(gcj2mc(st_geomfromtext(%s, 4326)))"
    res = dbutils.fetch_one(pgsql.COMPUTE_CONFIG, sql, (wkt,))
    return res[0]
