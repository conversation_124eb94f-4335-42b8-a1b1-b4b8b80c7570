"""
挖掘重复的停车场数据并推送下线策略
"""
import shapely.wkt
import tqdm as tqdm
import logging
import multiprocessing
import json
import sys

from functools import partial
from dataclasses import dataclass, field, asdict
from typing import List, Tuple
from src.parking.storefront import risk_mining
from src.parking.storefront.diff import polygon_differ
from src.parking.storefront.risk_mining.config import DUPLICATE_PARKS_RUN_NUM_WORKERS, HIGH_LEVEL_ROAD_KIND
from shapely.wkt import loads


# 添加日志配置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", stream=sys.stdout)
logger = logging.getLogger(__name__)


@dataclass
class DuplicatePark:
    """
    重复停车场信息
    """

    storefront_park_bid: str = field(default="", init=False)  # 门前停车场bid
    duplicate_park_bid: str = field(default="", init=False)  # 重复停车场bid
    duplicate_park_city: str = field(default="", init=False)  # 重复停车场城市
    duplicate_park_show_tag: str = field(default="", init=False)  # 重复停车场空间属性
    duplicate_park_click_pv: float = field(default=-1, init=False)  # 重复停车场click_pv
    duplicate_park_accept_ratio: float = field(default=-1, init=False)  # 重复停车场采纳率
    duplicate_park_spec: float = field(default=0, init=False)  # 重复停车场park_spec
    duplicate_park_precise: float = field(default=0, init=False)  # 重复停车场precise
    duplicate_reason: str = field(default="", init=False)  # 重复原因
    duplicate_intersects_iou: float = field(default=0, init=False)  # 门前和重复停车场范围相交iou
    duplicate_link_iou: float = field(default=0, init=False)  # 门前和重复停车场link投影相交iou
    duplicate_link_ioa: float = field(default=0, init=False)  # 门前和重复停车场link投影相交ioa
    duplicate_link_iob: float = field(default=0, init=False)  # 门前和重复停车场link投影相交iob
    duplicate_two_parks_dist: float = field(default=0, init=False)  # 两停车场距离
    duplicate_exist_parent_bid: bool = field(default=False, init=False)  # 重复停车场是否存在父点
    duplicate_cover_aoi_tag: str = field(default="", init=False)  # 重复停车场压盖的AOI.tag
    duplicate_parent_std_tag: str = field(default="", init=False)  # 重复停车场父点std_tag
    duplicate_parent_is_street_poi: bool = field(default="", init=False)  # 重复停车场父点是否是临街POI
    duplicate_parent_is_in_storefront_park_area: bool = field(default="", init=False)  # 重复停车场父点是否在门前面内
    duplicate_parent_is_aoi: bool = field(default=False, init=False)  # 重复停车场父点是否是AOI
    duplicate_parent_aoi_area: float = field(default=-1, init=False)  # 重复停车场父点aoi面积
    area_diff_ratio: float = field(default=-1, init=False)  # 重复停车场中没有被门前停车场面覆盖的比例
    gap_line_across_high_road: bool = field(default=False, init=False)  # 两停车场连线是否跨高等级道路
    is_in_protect_list: bool = field(default=False, init=False)  # 是否在防护集合


def run() -> None:
    """
    run
    """
    storefront_parks = risk_mining.get_storefront_parks()
    logger.info("get_storefront_parks: %d", len(storefront_parks))

    storefront_parks = _trans_storefront_parks_area_wkt(storefront_parks)
    logger.info("_trans_storefront_parks_area_wkt: %d", len(storefront_parks))

    duplicate_parks = find_all_duplicate_parks(storefront_parks)
    logger.info("find_all_duplicate_parks: %d", len(storefront_parks))

    risk_mining.create_duplicate_log_table()
    logger.info("create_duplicate_log_table success",)

    # _save_duplicate_data_to_db(duplicate_parks)
    # logger.info("_save_duplicate_data_to_db success")

    _save_result_to_review(duplicate_parks, "./duplicate_parks.csv")
    logger.info("_save_result_to_review success")


def _trans_storefront_parks_area_wkt(storefront_parks: List[Tuple[str, str]]):
    """
    将门前面3857转gcj02
    """
    new_storefront_parks = []
    for storefront_park in storefront_parks:
        bid, area = storefront_park
        gcj_area = risk_mining.mc2gcj(area)
        new_storefront_parks.append([bid, gcj_area])
    return new_storefront_parks


def find_all_duplicate_parks(storefront_parks) -> List[DuplicatePark]:
    """
    基于门前停车场获取全量重复停车场
    """
    result: List[DuplicatePark] = []

    result += _find_duplicate_parks(storefront_parks, "地上停车场")
    result += _find_duplicate_parks(storefront_parks, "路侧停车位")
    result += _find_duplicate_parks(storefront_parks, "路侧停车场")
    result += _find_duplicate_parks(storefront_parks, "临时停车点")
    result += _find_duplicate_parks(storefront_parks, "门前停车场")
    result += _find_duplicate_parks(storefront_parks, "停车场")
    # result += _find_duplicate_parks(storefront_parks, "地下停车场")
    # result += _find_duplicate_parks(storefront_parks, "立体停车场")

    filtered_result = _filter_potential_valid_parks(result)

    return filtered_result


def _filter_potential_valid_parks(dup_parks: List[DuplicatePark]) -> List[DuplicatePark]:
    """
    过滤掉潜在正确的停车场：
    """
    result: List[DuplicatePark] = []
    for dup_park in dup_parks:
        (storefront_park_bid, dup_park_bid, dup_exist_parent_bid, dup_park_spec,) = (
            dup_park.storefront_park_bid,
            dup_park.duplicate_park_bid,
            dup_park.duplicate_exist_parent_bid,
            dup_park.duplicate_park_spec,
        )
        if dup_park_spec == 1:
            logger.info(f"{dup_park_bid}, park_spec = 1的不下线")
            continue

        if dup_exist_parent_bid:
            if _filter_exist_parent_dup_park(dup_park):
                result.append(dup_park)
            else:
                logger.info(f"{dup_park_bid}, 有父点策略判断不下线")
            continue
        else:
            if _filter_not_exist_parent_dup_park(dup_park):
                result.append(dup_park)
            else:
                logger.info(f"{dup_park_bid}, 无父点策略判断不下线")

    return result


def _filter_exist_parent_dup_park(dup_park: DuplicatePark) -> bool:
    """
    过滤存在父点的重复停车场：
    """
    (
        storefront_park_bid,
        dup_park_bid,
        dup_park_show_tag,
        dup_exist_parent_bid,
        dup_reason,
        dup_dist,
        across_high_road,
        dup_park_spec,
        dup_intersects_iou,
        dup_link_ioa,
        dup_link_iou,
        dup_link_iob,
        area_diff_ratio,
        dup_accept_ratio,
        dup_park_click_pv,
        dup_park_precise
    ) = (
        dup_park.storefront_park_bid,
        dup_park.duplicate_park_bid,
        dup_park.duplicate_park_show_tag,
        dup_park.duplicate_exist_parent_bid,
        dup_park.duplicate_reason,
        dup_park.duplicate_two_parks_dist,
        dup_park.gap_line_across_high_road,
        dup_park.duplicate_park_spec,
        dup_park.duplicate_intersects_iou,
        dup_park.duplicate_link_ioa,
        dup_park.duplicate_link_iou,
        dup_park.duplicate_link_iob,
        dup_park.area_diff_ratio,
        dup_park.duplicate_park_accept_ratio,
        dup_park.duplicate_park_click_pv,
        dup_park.duplicate_park_precise
    )

    if dup_park_show_tag in ("地下停车场", "立体停车点", "路侧停车位", '路侧停车场'):
        # 暂不处理
        return False

    if dup_accept_ratio >= 0.8:
        # 采纳率高的暂不下线
        return False

    if dup_park_click_pv >= 20:
        # 算路pv高的暂不下线
        return False

    if across_high_road:
        # 跨高等级道路暂不下线
        return False

    # 单点场景
    if dup_reason == "单点停车场在门前buffer范围内":
        if dup_park_show_tag == "门前停车场":
            prod_parking = risk_mining.is_park_storefront_prod_parking(dup_park_bid)
            if prod_parking is not None:
                logger.info(f"{dup_park_bid}, 新门前停车场不处理")
                return False

        if dup_dist < 15 or (dup_park_precise == 0 and dup_dist < 20):
            return True
        return False

    # 非单点场景
    if dup_intersects_iou <= 0.2:
        # 面交集很小的暂不下线
        return False

    if area_diff_ratio > 0.6:
        # 重复停车场没有被门前覆盖的范围过大，暂不下线
        return False

    if dup_link_ioa < 0.3:
        # ioa过小，暂不下线
        return False

    if dup_park_show_tag == "地上停车场":
        # 门前和地上重复：下线地上
        if dup_dist < 15 or (dup_dist < 20 and dup_park_precise == 0):
            return True
        return False
    elif dup_park_show_tag == "临时停车点":
        # 门前和临时停车点重复：下线临时
        if dup_dist < 15 or (dup_dist < 20 and dup_park_precise == 0):
            return True
        return False
    elif dup_park_show_tag == "门前停车场":
        # 门前和旧门前重复：下线旧门前
        prod_parking = risk_mining.is_park_storefront_prod_parking(dup_park_bid)
        if prod_parking is not None:
            logger.info(f"{dup_park_bid}, 新门前停车场不处理")
            return False

        if dup_dist < 15 or (dup_dist < 20 and dup_park_precise == 0):
            return True
        return False
    elif dup_park_show_tag == "停车场":
        # 门前和停车场重复：下线停车场
        if dup_dist < 15 or (dup_dist < 20 and dup_park_precise == 0):
            return True
    return False

    # if dup_intersects_iou < 0.4 or dup_link_ioa < 0.4:
    #     if not (dup_intersects_iou > 0.1 and (dup_link_ioa > 0.5 or dup_link_iou > 0.5 or dup_link_iob > 0.5)):
    #         logger.info(
    #             "not (dup_intersects_iou > 0.1 and (dup_link_ioa > 0.5 or dup_link_iou > 0.5 or dup_link_iob > 0.5))"
    #         )
    #         return False

    # if dup_parent_std_tag in ("房地产;住宅区", "金融;银行", "公司企业", "运动健身;体育场馆", "政府机构", "房地产;写字楼", "酒店、房地产;住宅区酒店", "购物"):
    #     return True
    # return False


def _filter_not_exist_parent_dup_park(dup_park: DuplicatePark) -> bool:
    """
    过滤不存在父点的重复停车场：
    * 门前和有面地上重复：全部下线地上
    * 门前和有面门前停车场重复：下旧门前
    * 门前和有面临时停车点重复：全部下线临时
    todo：门前和有面路侧停车场重复：先等评估结论
    todo：门前和路侧停车位重复：暂不处理
    * 单点停车场需要缩短阈值，距离20m内下线，同时需要判断是否跨路(>=7级路)，若跨路则不下线，非跨路数据再下线。
    """
    (
        storefront_park_bid,
        dup_park_bid,
        dup_park_show_tag,
        dup_exist_parent_bid,
        dup_reason,
        dup_dist,
        across_high_road,
        dup_park_spec,
        dup_intersects_iou,
        dup_link_ioa,
        dup_link_iou,
        dup_link_iob,
        area_diff_ratio,
        dup_accept_ratio,
        dup_park_click_pv,
        dup_park_precise
    ) = (
        dup_park.storefront_park_bid,
        dup_park.duplicate_park_bid,
        dup_park.duplicate_park_show_tag,
        dup_park.duplicate_exist_parent_bid,
        dup_park.duplicate_reason,
        dup_park.duplicate_two_parks_dist,
        dup_park.gap_line_across_high_road,
        dup_park.duplicate_park_spec,
        dup_park.duplicate_intersects_iou,
        dup_park.duplicate_link_ioa,
        dup_park.duplicate_link_iou,
        dup_park.duplicate_link_iob,
        dup_park.area_diff_ratio,
        dup_park.duplicate_park_accept_ratio,
        dup_park.duplicate_park_click_pv,
        dup_park.duplicate_park_precise
    )

    if area_diff_ratio >= 0.9:
        # 面差过大过滤
        return False

    if dup_accept_ratio >= 0.8:
        # 采纳率高的暂不下线
        return False

    if dup_park_click_pv >= 20:
        # 4、算路pv高的暂不下线
        return False

    if across_high_road:
        # 跨高等级道路暂不下线
        return False

    # 单点场景
    if dup_reason == "单点停车场在门前buffer范围内":
        if dup_park_show_tag in ("地上停车场", "临时停车点", "停车场"):
            # 非精准
            if dup_park_precise == 0 and dup_dist < 40 and not across_high_road:
                return True

            # 单点地上停车场/临时停车点/停车场，距离小于25m且未跨高等级道路的可下线
            if dup_dist < 25 and not across_high_road:
                return True
        elif dup_park_show_tag == "门前停车场":
            # 单点停车场旧门前，距离40m内，且未跨高等级道路的旧门前停车场可下线
            prod_parking = risk_mining.is_park_storefront_prod_parking(dup_park_bid)
            if prod_parking is not None:
                logger.info(f"{dup_park_bid}, 新门前停车场不处理")
                return False

            if dup_dist < 40 and not across_high_road:
                return True
        # elif dup_park_show_tag == "路侧停车场":
        #     if dup_park_spec != 0:
        #         logger.info(f"{dup_park_bid}, 新路侧停车场不处理")
        #         return False
        #
        #     if dup_dist < 40 and not across_high_road:
        #         return True

    # 非单点场景
    if dup_intersects_iou <= 0.05:
        # 面交集很小的暂不下线
        return False
    
    if area_diff_ratio > 0.75:
        # 重复停车场没有被门前覆盖的范围过大，暂不下线
        return False

    if dup_park_show_tag == "地上停车场":
        # 门前和地上重复：下线地上
        if dup_dist < 25 or (dup_dist < 40 and dup_park_precise == 0):
            return True
        return False
    elif dup_park_show_tag == "路侧停车位":
        # 暂不处理
        logger.info(f"{dup_park_bid}, 路侧停车位先不处理")
        return False
    # elif dup_park_show_tag == "路侧停车场":
    #     # 门前和旧路侧重复：下线旧路侧
    #     if dup_park_spec != 0:
    #         logger.info(f"{dup_park_bid}, 新路侧停车场不处理")
    #         return False
    #     if dup_dist < 25 or (dup_dist < 40 and dup_park_precise == 0):
    #         return True
    #     return False
    elif dup_park_show_tag == "临时停车点":
        # 门前和临时停车点重复：下线临时
        if dup_dist < 25 or (dup_dist < 40 and dup_park_precise == 0):
            return True
        return False
    elif dup_park_show_tag == "门前停车场":
        # 门前和旧门前重复：下线旧门前
        prod_parking = risk_mining.is_park_storefront_prod_parking(dup_park_bid)
        if prod_parking is not None:
            logger.info(f"{dup_park_bid}, 新门前停车场不处理")
            return False

        if dup_dist < 25 or (dup_dist < 40 and dup_park_precise == 0):
            return True
        return False
    elif dup_park_show_tag == "停车场" and dup_reason != "单点停车场在门前buffer范围内":
        # 门前和停车场重复：下线停车场
        if dup_dist < 25 or (dup_dist < 40 and dup_park_precise == 0):
            return True

        # # TODO: 尝试
        # if dup_intersects_iou < 0.4 or dup_link_ioa < 0.4:
        #     if dup_intersects_iou > 0.1 and (dup_link_ioa > 0.5 or dup_link_iou > 0.5 or dup_link_iob > 0.5):
        #         print(dup_park_bid)
        #         return True
        return False
    elif dup_park_show_tag == "地下停车场":
        # 不可下线，大多数实地存在地下停车场，非重复
        return False
    elif dup_park_show_tag == "立体停车场":
        # 不可下线，大多数实地存在地下停车场，非重复
        return False
    return False


def _find_duplicate_parks(storefront_parks, show_tag) -> List[DuplicatePark]:
    """
    根据全量门前停车场获取全量重复地上停车场
    """
    result: List[DuplicatePark] = []
    logger.info("_find_duplicate_parks start")

    chunk_size = max(1, len(storefront_parks) // DUPLICATE_PARKS_RUN_NUM_WORKERS)  # 拆分任务
    with multiprocessing.Pool(processes=DUPLICATE_PARKS_RUN_NUM_WORKERS) as pool:
        # 使用 partial 绑定 show_tag 参数
        func = partial(_get_duplicate_parks, show_tag=show_tag)
        print("processing: ", show_tag)
        results = list(tqdm.tqdm(pool.imap(func, storefront_parks, chunksize=chunk_size), total=len(storefront_parks)))

    # 合并所有进程计算结果
    for r in results:
        result.extend(r)

    return result


def _get_duplicate_parks(storefront_park, show_tag) -> List[DuplicatePark]:
    """
    根据单个门前停车场获取全量重复停车场
    """
    result: List[DuplicatePark] = []
    storefront_park_bid, storefront_park_area = storefront_park
    result += _find_point_only_duplicate_parks(storefront_park, show_tag)

    def _get_storefront_park(wkt: str) -> List[polygon_differ.Polygon]:
        """
        获取门前停车场面
        """
        return [polygon_differ.Polygon(face_id=storefront_park_bid, geom=storefront_park_area)]

    def _get_park_polygons(poly_wkt) -> List[polygon_differ.Polygon]:
        """
        获取附近停车场面
        """
        polygons: List[polygon_differ.Polygon] = []
        buf_wkt = shapely.wkt.loads(poly_wkt).buffer(50 * 1e-5).wkt
        parks = risk_mining.get_parks_with_poly(buf_wkt)
        for park in parks:
            bid, area_wkt = park
            park_data = risk_mining.get_park_info(bid)
            if park_data is None:
                continue

            online_status, online_show_tag = park_data[1], park_data[2]
            if online_status != 1 or online_show_tag != show_tag:
                # logging.info(f"{bid}, online_status != 1 or online_show_tag != show_tag")
                continue

            polygons.append(polygon_differ.Polygon(face_id=bid, geom=area_wkt))
        return polygons

    park_polygons = _get_park_polygons(storefront_park_area)
    diff_res = []
    try:
        diff_res = polygon_differ.diff_center(park_polygons, 30 * 1e-5, _get_storefront_park,)
    except Exception as e:
        logger.error(f"Error polygon_differ.diff_center: {e}")

    for diff in diff_res:
        if diff is None or diff.similarity is None:
            continue

        # 相似度
        similarity = diff.similarity.get_detail()
        link_similarity, geom_similarity = similarity["link"], similarity["geom"]

        # 交并比
        link_iou, link_ioa, link_iob = (
            link_similarity["iou"],
            link_similarity["ioa"],
            link_similarity["iob"],
        )
        geom_iou = geom_similarity["iou"]
        if geom_iou == 0 and link_iou == 0 and link_ioa == 0 and link_iob == 0:
            continue

        # 门前和重复停车场面
        a_face_ids, b_face_ids = diff.a_face_ids, diff.b_face_ids
        if not a_face_ids or not b_face_ids:
            logger.warning(f"skipping due to empty face_ids: a_face_ids={a_face_ids}, b_face_ids={b_face_ids}")
            continue

        dup_park = DuplicatePark()
        dup_park.storefront_park_bid = b_face_ids[0]
        dup_park.duplicate_park_bid = a_face_ids[0]
        dup_park.duplicate_park_show_tag = show_tag
        dup_park.duplicate_reason = "门前停车场与 " + show_tag + " 范围压盖or link投影有交集"
        dup_park.duplicate_intersects_iou = geom_iou
        dup_park.duplicate_link_iou = link_iou
        dup_park.duplicate_link_ioa = link_ioa
        dup_park.duplicate_link_iob = link_iob

        # 不与自己判重
        if storefront_park_bid == dup_park.duplicate_park_bid:
            continue

        # park_spec/precise
        dup_park_info = risk_mining.get_park_info(dup_park.duplicate_park_bid)
        if dup_park_info:
            dup_park.duplicate_park_spec = dup_park_info[0]
            dup_park.duplicate_park_precise = dup_park_info[5]

        # click_pv、城市
        duplicate_park_poi = risk_mining.get_poi_info(dup_park.duplicate_park_bid)
        if duplicate_park_poi:
            dup_park.duplicate_park_click_pv = duplicate_park_poi[1]
            dup_park.duplicate_park_city = duplicate_park_poi[4]

        # 是否在防护集合
        dup_park.is_in_protect_list = risk_mining.is_in_protect_list(dup_park.duplicate_park_bid)

        # 最近7天采纳率
        duplicate_park_accept_ratio = risk_mining.get_park_7days_accept_ratio(dup_park.duplicate_park_bid)
        if duplicate_park_accept_ratio is not None and duplicate_park_accept_ratio[0] is not None:
            dup_park.duplicate_park_accept_ratio = round(duplicate_park_accept_ratio[0], 4)

        # 两停车场距离和面积差占比
        dist, area_diff_ratio = -1, -1
        a_c, b_c = diff.a_characteristics_set[0], diff.b_characteristics_set[0]
        try:
            if a_c and a_c and a_c.polygon and b_c.polygon:
                a_sp = shapely.wkt.loads(a_c.polygon.geom)
                b_sp = shapely.wkt.loads(b_c.polygon.geom)
                dist = a_sp.distance(b_sp) * 1e5
                area_diff_ratio = a_sp.difference(a_sp.intersection(b_sp)).area / a_sp.area
        except Exception as e:
            logger.error(e)
        dup_park.duplicate_two_parks_dist = dist
        dup_park.area_diff_ratio = area_diff_ratio

        # 两停车场连线是否跨高等级道路
        dup_park.gap_line_across_high_road = _is_two_parks_across_high_road(
            dup_park.storefront_park_bid, dup_park.duplicate_park_bid
        )

        # 父点挂接
        parent_bid_res = risk_mining.get_parent_bid(dup_park.duplicate_park_bid)
        if parent_bid_res is not None and parent_bid_res[0] not in ("0", 0, "", None):
            dup_park.duplicate_exist_parent_bid = True
            parent_bid = parent_bid_res[0]

            # 父点.std_tag
            parent_poi_info = risk_mining.get_poi_info(parent_bid)
            if parent_poi_info is not None:
                dup_park.duplicate_parent_std_tag = parent_poi_info[0]

            # 父点是否是临街POI
            parent_is_street_poi = risk_mining.is_street_poi(parent_bid)
            dup_park.duplicate_parent_is_street_poi = parent_is_street_poi

            # 父点是否在门前面内
            if parent_poi_info is not None:
                is_parent_poi_in_area = shapely.wkt.loads(storefront_park_area).intersects(
                    shapely.wkt.loads(parent_poi_info[2])
                )
                dup_park.duplicate_parent_is_in_storefront_park_area = is_parent_poi_in_area

            # 父点AOI信息
            if parent_poi_info is not None:
                parent_aoi1 = risk_mining.get_aoi_info(parent_bid)
                parent_aoi2 = risk_mining.get_aoi_info(parent_poi_info[3])
                if parent_aoi1 is not None:
                    dup_park.duplicate_parent_is_aoi = True
                    dup_park.duplicate_parent_aoi_area = parent_aoi1[1]
                elif parent_aoi2 is not None:
                    dup_park.duplicate_parent_is_aoi = True
                    dup_park.duplicate_parent_aoi_area = parent_aoi2[1]

        # aoi.poi.std_tag
        poi_bids = risk_mining.get_cover_aoi_poi_bid(b_c.polygon.geom)
        aoi_std_tags = ""
        for poi_bid in poi_bids:
            aoi_poi_info = risk_mining.get_poi_info(poi_bid)
            if aoi_poi_info is None:
                continue
            aoi_std_tags += aoi_poi_info[0]
        dup_park.duplicate_cover_aoi_tag = aoi_std_tags

        result.append(dup_park)
    return result


def _is_two_parks_across_high_road(park_bid_a, park_bid_b):
    """
    两停车场连线是否跨高等级道路
    """
    store_park = risk_mining.get_park_info(park_bid_a)
    if store_park is None:
        return False

    dup_park = risk_mining.get_park_info(park_bid_b)
    if dup_park is None:
        return False

    store_park_point = loads(store_park[3])
    dup_park_point = loads(dup_park[3])
    line_wkt = f"LINESTRING({store_park_point.x} {store_park_point.y}, {dup_park_point.x} {dup_park_point.y})"

    return risk_mining.is_line_across_road(line_wkt, HIGH_LEVEL_ROAD_KIND)


def _find_point_only_duplicate_parks(storefront_park, show_tag: str):
    """
    获取无面的重复停车场
    """
    result: List[DuplicatePark] = []
    storefront_park_bid, storefront_park_area = storefront_park
    storefront_park_area_buf = shapely.wkt.loads(storefront_park_area).buffer(50 * 1e-5).wkt
    parks = risk_mining.get_no_area_parks_with_poly(storefront_park_area_buf, show_tag)
    for park in parks:
        bid, p_wkt = park
        dup_park = DuplicatePark()
        dup_park.storefront_park_bid = storefront_park_bid
        dup_park.duplicate_park_bid = bid
        dup_park.duplicate_park_show_tag = show_tag
        dup_park.duplicate_reason = "单点停车场在门前buffer范围内"

        # 不与自己判重
        if storefront_park_bid == dup_park.duplicate_park_bid:
            continue

        # park_spec/precise
        dup_park_info = risk_mining.get_park_info(dup_park.duplicate_park_bid)
        if dup_park_info:
            dup_park.duplicate_park_spec = dup_park_info[0]
            dup_park.duplicate_park_precise = dup_park_info[5]

        # click_pv、城市
        duplicate_park_poi = risk_mining.get_poi_info(dup_park.duplicate_park_bid)
        if duplicate_park_poi:
            dup_park.duplicate_park_click_pv = duplicate_park_poi[1]
            dup_park.duplicate_park_city = duplicate_park_poi[4]

        # 是否在防护集合
        dup_park.is_in_protect_list = risk_mining.is_in_protect_list(dup_park.duplicate_park_bid)

        # 最近7天采纳率
        duplicate_park_accept_ratio = risk_mining.get_park_7days_accept_ratio(dup_park.duplicate_park_bid)
        if duplicate_park_accept_ratio is not None and duplicate_park_accept_ratio[0] is not None:
            dup_park.duplicate_park_accept_ratio = round(duplicate_park_accept_ratio[0], 4)

        # 两停车场距离
        try:
            dup_park_sp = shapely.wkt.loads(p_wkt)
            storefront_park_sp = shapely.wkt.loads(storefront_park_area)
            dist = storefront_park_sp.distance(dup_park_sp) * 1e5
        except Exception as e:
            logger.error(e)
            raise e
        dup_park.duplicate_two_parks_dist = dist
        if dist >= 50:
            # 50m不计算为重复
            continue

        # 父点挂接
        parent_bid_res = risk_mining.get_parent_bid(dup_park.duplicate_park_bid)
        if parent_bid_res is not None and parent_bid_res[0] not in ("0", 0, "", None):
            dup_park.duplicate_exist_parent_bid = True
            parent_bid = parent_bid_res[0]

            # 父点.std_tag
            parent_poi_info = risk_mining.get_poi_info(parent_bid)
            if parent_poi_info is not None:
                dup_park.duplicate_parent_std_tag = parent_poi_info[0]

            # 父点是否是临街POI
            parent_is_street_poi = risk_mining.is_street_poi(parent_bid)
            dup_park.duplicate_parent_is_street_poi = parent_is_street_poi

            # 父点是否在门前面内
            if parent_poi_info is not None:
                is_parent_poi_in_area = shapely.wkt.loads(storefront_park_area).intersects(
                    shapely.wkt.loads(parent_poi_info[2])
                )
                dup_park.duplicate_parent_is_in_storefront_park_area = is_parent_poi_in_area

            # 父点AOI信息
            if parent_poi_info is not None:
                parent_aoi1 = risk_mining.get_aoi_info(parent_bid)
                parent_aoi2 = risk_mining.get_aoi_info(parent_poi_info[3])
                if parent_aoi1 is not None:
                    dup_park.duplicate_parent_is_aoi = True
                    dup_park.duplicate_parent_aoi_area = parent_aoi1[1]
                elif parent_aoi2 is not None:
                    dup_park.duplicate_parent_is_aoi = True
                    dup_park.duplicate_parent_aoi_area = parent_aoi2[1]

        # 两停车场连线是否跨高等级道路
        dup_park.gap_line_across_high_road = _is_two_parks_across_high_road(
            dup_park.storefront_park_bid, dup_park.duplicate_park_bid
        )

        # aoi.poi.std_tag
        poi_bids = risk_mining.get_cover_aoi_poi_bid(storefront_park_area)
        aoi_std_tags = ""
        for poi_bid in poi_bids:
            aoi_poi_info = risk_mining.get_poi_info(poi_bid)
            if aoi_poi_info is None:
                continue
            aoi_std_tags += aoi_poi_info[0]
        dup_park.duplicate_cover_aoi_tag = aoi_std_tags
        result.append(dup_park)
    return result


def _save_duplicate_data_to_db(result: List[DuplicatePark]) -> None:
    """
    将重复停车场数据存到库中
    """
    for dup_park in result:
        _save_result_to_log(dup_park)
        logger.info(f"{dup_park.storefront_park_bid}, {dup_park.duplicate_park_bid}, _save_result_to_log done")


def _save_result_to_log(dup_park: DuplicatePark):
    """
    保存重复停车场记录
    """
    storefront_park_bid, duplicate_park_bid = dup_park.storefront_park_bid, dup_park.duplicate_park_bid
    risk_mining.insert_duplicate_log(
        storefront_park_bid, duplicate_park_bid, json.dumps(asdict(dup_park), ensure_ascii=False, default=str)
    )


def _save_result_to_review(result: List[DuplicatePark], output_path: str):
    """
    导出评估数据
    """
    with open(output_path, "w") as file:
        header = (
            "storefront_park_bid\t"
            "duplicate_park_bid\t"
            "duplicate_park_city\t"
            "duplicate_park_show_tag\t"
            "duplicate_reason\t"
            "duplicate_intersects_iou\t"
            "duplicate_link_iou\t"
            "duplicate_link_ioa\t"
            "duplicate_link_iob\t"
            "duplicate_two_parks_dist\t"
            "duplicate_park_accept_ratio\t"
            "duplicate_park_spec\t"
            "duplicate_park_precise\t"
            "duplicate_cover_aoi_tag\t"
            "duplicate_park_click_pv\t"
            "gap_line_across_high_road\t"
            "duplicate_parent_std_tag\t"
            "duplicate_parent_is_street_poi\t"
            "duplicate_parent_is_in_storefront_park_area\t"
            "duplicate_parent_is_aoi\t"
            "duplicate_parent_aoi_area\t"
            "area_diff_ratio\t"
            "is_in_protect_list\t"
            "duplicate_exist_parent_bid\n"
        )
        file.write(header)
        for item in result:
            strs = (
                f"{item.storefront_park_bid}\t"
                f"{item.duplicate_park_bid}\t"
                f"{item.duplicate_park_city}\t"
                f"{item.duplicate_park_show_tag}\t"
                f"{item.duplicate_reason}\t"
                f"{item.duplicate_intersects_iou}\t"
                f"{item.duplicate_link_iou}\t"
                f"{item.duplicate_link_ioa}\t"
                f"{item.duplicate_link_iob}\t"
                f"{item.duplicate_two_parks_dist}\t"
                f"{item.duplicate_park_accept_ratio}\t"
                f"{item.duplicate_park_spec}\t"
                f"{item.duplicate_park_precise}\t"
                f"{item.duplicate_cover_aoi_tag}\t"
                f"{item.duplicate_park_click_pv}\t"
                f"{item.gap_line_across_high_road}\t"
                f"{item.duplicate_parent_std_tag}\t"
                f"{item.duplicate_parent_is_street_poi}\t"
                f"{item.duplicate_parent_is_in_storefront_park_area}\t"
                f"{item.duplicate_parent_is_aoi}\t"
                f"{item.duplicate_parent_aoi_area}\t"
                f"{item.area_diff_ratio}\t"
                f"{item.is_in_protect_list}\t"
                f"{item.duplicate_exist_parent_bid}\n"
            )
            file.write(strs)


if __name__ == "__main__":
    """
    main
    """
    run()
