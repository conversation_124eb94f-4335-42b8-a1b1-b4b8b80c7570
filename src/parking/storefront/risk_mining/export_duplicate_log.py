"""
导出推送表数据
"""
import logging
import csv

from src.parking.storefront import risk_mining

logger = logging.getLogger(__name__)


def run():
    """
    run
    """
    all_remarks = risk_mining.get_all_duplicate_logs_remarks()
    json_list = [x[0] for x in all_remarks]

    # 获取所有记录中出现的所有 key 作为 header
    all_keys = set()
    for item in json_list:
        all_keys.update(item.keys())
    fieldnames = sorted(all_keys)  # 可以不排序

    with open("duplicate_logs.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames, delimiter="\t", extrasaction="ignore")
        writer.writeheader()
        writer.writerows(json_list)

    print("✅ 导出 CSV 完成：duplicate_logs.csv")


if __name__ == "__main__":
    run()


if __name__ == "__main__":
    """
    main
    """
    run()
