"""
筛选 POI 密度低的门前停车场
"""
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Iterable

import shapely.ops
from loguru import logger
from shapely import LineString, Point, wkt
from tqdm import tqdm

from src.parking.storefront import risk_mining
from src.parking.storefront.post_process import central_line, autocomplete
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.tools import tsv, utils


def get_density_segments(baseline: LineString, pts: Iterable[Point], segment_size: float):
    """
    获取密度片段
    """

    def get_segments():
        a = 0
        while a < baseline.length:
            b = a + segment_size
            yield shapely.ops.substring(
                baseline, start_dist=a, end_dist=(b if b < baseline.length else baseline.length)
            )
            a = b

    for segment in get_segments():
        vertical_pts = [pt for pt in pts if geometric.get_foot_point(pt, segment)]
        density = len(vertical_pts) / (segment.length / METER)
        yield segment, density


def get_central_line(geom: str):
    """
    获取中心线
    """
    # noinspection PyBroadException
    try:
        return central_line.get_central_line_irregular(wkt.loads(geom))
    except:
        return None


def get_low_density_feature(items: list[tuple], threshold: float):
    """
    获取低密度特征
    """
    max_density = max(item[2] for item in items)

    def get_continuous_low_segments(indexes):
        n = 0
        for i in indexes:
            density = items[i][2]
            if density < threshold:
                n += 1
            else:
                if n > 0:
                    yield n
                    n = 0

        if n > 0:
            yield n

    low_segments = list(get_continuous_low_segments(range(len(items))))
    max_low_segments = max(low_segments) if len(low_segments) > 0 else 0
    sum_low_segments = sum(low_segments) if len(low_segments) > 0 else 0
    return max_low_segments, sum_low_segments, len(items), max_density


def process(item: tuple):
    """
    并发函数
    """
    bid, city, geom = item
    baseline = get_central_line(geom)
    if baseline is None:
        return [(bid, city, 0.0, "LINESTRING EMPTY")]

    pts = autocomplete.get_relation_store_pois(geom, search_buffer=50 * METER)
    # pts = turnoff_point_cluster.get_zhongyuan_points(geom)
    if not pts:
        return [(bid, city, 0.0, baseline.wkt)]

    pts = [pt.point for pt in pts]
    result = [
        (bid, city, density, segment.wkt)
        for segment, density in get_density_segments(baseline, pts, segment_size=30 * METER)
    ]
    return result


@logger.catch
def main():
    """
    主函数
    """
    output_dir = Path("output")
    work_dir = output_dir / "poi_density" / "case_bid_20250213"
    result_path = utils.ensure_path(work_dir / "result_doctor.tsv")
    feature_path = utils.ensure_path(work_dir / "feature_doctor.tsv")

    bids = risk_mining.get_online_bid()
    # bids = [x[0] for x in tsv.read_tsv("TODO.txt")]
    items = risk_mining.get_online_item_by_bid(bids)
    # items = risk_mining.get_online_items()
    with Pool(64) as pool:
        for payload in tqdm(pool.imap_unordered(process, items), total=len(items)):
            tsv.write_tsv(result_path, payload, mode="a")
            feature = get_low_density_feature(payload, threshold=0.03)
            bid, city = payload[0][:2]
            tsv.write_tsv(feature_path, [[bid, city, *feature]], mode="a")


if __name__ == "__main__":
    main()
