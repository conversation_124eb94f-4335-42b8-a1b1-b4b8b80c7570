"""
筛选压盖人行路的停车场面。
P.S. 这个策略的准确性极差，因为停车场压盖人行路算是正常现象，思路本身有问题，仅作备份，不上线
"""
from pathlib import Path

from loguru import logger
from shapely import wkt
from tqdm import tqdm

from src.parking.recognition import dbutils
from src.parking.storefront import risk_mining
from src.parking.storefront.utils.geometric import METER
from src.tools import pgsql, tsv, linq, utils


def get_nav_links(geom: str):
    """
    获取压盖的的所有道路信息
    """

    def get_length(g1: str, g2: str):
        g1 = wkt.loads(g1)
        g2 = wkt.loads(g2)
        line = g1.intersection(g2)
        return line.length

    sql = """
        select link_id, kind, form, st_astext(geom) from nav_link
        where st_intersects(%s, geom)
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{geom}"])
    return [(link_id, kind, form, get_length(geom, link) / METER) for link_id, kind, form, link in ret]


@logger.catch
def main():
    """
    主函数
    """
    output_dir = Path("output")
    work_dir = output_dir / "road"

    items = risk_mining.get_online_items()
    results = [(bid, city, get_nav_links(geom)) for bid, city, geom in tqdm(items)]
    results = [x for x in results if x[-1]]
    results = [(bid, city, *link) for bid, city, links in results for link in links]
    tsv.write_tsv(utils.ensure_path(work_dir / "park_nav_links.tsv"), results)

    filtered_results = [(bid, city, length) for bid, city, link_id, kind, form, length in results if kind == 10]
    grouped_results = linq.group_by(filtered_results, key=lambda x: (x[0], x[1]), value=lambda x: x[-1])
    length_results = [(*k, sum(v)) for k, v in grouped_results.items()]
    tsv.write_tsv(utils.ensure_path(work_dir / "park_nav_links_kind_10_length.tsv"), length_results)


if __name__ == "__main__":
    main()
