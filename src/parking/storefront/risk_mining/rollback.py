"""
下线问题数据回滚脚本
"""
import requests
import sys
import logging
import tqdm as tqdm
import json
import src.model_mysql.beeflow_model as beeflow_model

from time import sleep
from src.parking.storefront import risk_mining


# 添加日志配置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", stream=sys.stdout)
logger = logging.getLogger(__name__)


bf_model = beeflow_model.BFQuery()


def run():
    """
    run
    """
    park_bids = [x[1] for x in risk_mining.get_pushed_duplicate_parks() if x is not None]
    insure_dup_park_bids = []
    park_bids_should_rollback = [bid for bid in park_bids if bid not in insure_dup_park_bids]
    print(len(park_bids), len(park_bids_should_rollback), len(park_bids) - len(park_bids_should_rollback))
    for park_bid in tqdm.tqdm(park_bids_should_rollback):
        if park_bid is None:
            continue

        crk_bids = get_all_wait_rollback_churukou(park_bid)
        need_rollback_bids = [park_bid] + crk_bids
        print(need_rollback_bids)
        for need_rollback_bid in need_rollback_bids:
            _push_duplicate_park_online(need_rollback_bid)
        print("pushed: ", park_bid)


def get_all_wait_rollback_churukou(park_bid):
    """
    获取所有需要回滚的出入口
    """
    result = []
    road_bids = risk_mining.get_all_road_bids(park_bid)
    for road_bid in road_bids:
        if road_bid is None or road_bid in ("0", 0):
            continue
        res = bf_model.queryall(
            f"select * from park_change_log where param = 'status' and value = 2 and bid = '{road_bid[0]}' and source = 'STRATEGY_PROCESS_DEL_ACCESS_BY_STATUS' and create_time >='2025-04-01';"
        )
        if len(res) > 0:
            result.append(road_bid[0])
    return result


def _push_duplicate_park_online(bid):
    """
    推送单个bid上线
    """
    _dataUpdateRequest(
        {
            "bid": bid,
            "force_update": 1,
            "status": 1,
            "source": "STRATEGY_PARKING_STOREFRONT_OFFLINE_DUPLICATE_PARK_ROLLBACK",
        }
    )
    logger.info(f"_push_online, {bid}")
    sleep(0.5)


def _dataUpdateRequest(req_map):
    """
    发送更新请求
    """
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    payload = json.dumps(req_map)
    logger.info(payload)
    headers = {"Content-Type": "application/json"}
    response = requests.request("POST", url, headers=headers, data=payload)
    if response.status_code != 200:
        logger.error(f"Request failed with status {response.status_code}: {response.text}")
        raise Exception(f"Failed request: {response.status_code}")
    logger.info(response.text)


if __name__ == "__main__":
    """
    main
    """
    run()
