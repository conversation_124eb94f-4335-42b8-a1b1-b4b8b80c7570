"""
通过轨迹与核实面的交割点，计算段落置信度，以指导人工核实的主次观察点位
1. 获取待处理的面 list
2. 获取这些面 list 所对应的临街 POI bid
3. 获取这些 bid 所对应的轨迹，并构建 面-轨迹 pairs
4. 针对 面-轨迹 pairs 进行交割点、起终点计算 + 聚类
5. 可视化以上信息，进行评估迭代

```cmd
awk -F '\t' '$10 > 80 {print $1}' diff.tsv
```
"""
from dataclasses import dataclass, field
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path

import cv2
import numpy as np
import shapely.ops
from loguru import logger
from shapely import wkt, Point, Polygon, LineString
from tqdm import tqdm

from src.aikit import preview_image, satellite_imagery, boundary, algo
from src.parking.recognition import dbutils
from src.parking.storefront import cluster_projection
from src.parking.storefront.post_process import autocomplete
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.parking.storefront.verify import repair_polygon
from src.tools import utils, pgsql, pipeline, tsv
from src.tools.end_point_track_provider import get_end_point_track_by_api
from src.trajectory.utils import trajutils

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    上下文
    """

    work_dir: Path
    task_id: int
    strategy_id: int
    turing_id: str
    baseline: LineString
    geom: Polygon
    manual_geom: Polygon
    street_region: Polygon
    buffer_geom: Polygon = field(init=False)
    store_pois: list[autocomplete.Poi] = field(default_factory=list)
    no_traj_bids: list[str] = field(default_factory=list)
    traj_lines: list[LineString] = field(default_factory=list)
    used_traj_lines: list[LineString] = field(default_factory=list)
    pass_ranges: list[tuple[float, float]] = field(default_factory=list)
    heat_ranges: list[tuple[tuple[float, float], int]] = field(default_factory=list)
    derivative_ranges: list[tuple[tuple[float, float], int]] = field(default_factory=list)
    errors: list[str] = field(default_factory=list)
    diffs: list[float] = field(default_factory=list)


@desc()
def fetch_store_pois(ctx: Context, proceed, search_buffer: float):
    """
    获取临街 POI 信息
    """
    pois = autocomplete.get_relation_store_pois(ctx.geom.wkt, search_buffer=search_buffer)
    ctx.store_pois.extend(pois)

    proceed()


@desc()
def fetch_traj_infos_from_db(ctx: Context, proceed):
    """
    获取轨迹信息从数据库（@李凡）
    """
    bids = [p.bid for p in ctx.store_pois]

    traj_infos = [(bid, wkt.loads(ln)) for bid in bids for ln in trajutils.get_dest_traj_by_bid(bid)]
    traj_infos = [(bid, ln) for bid, ln in traj_infos if ln.geom_type == "LineString"]
    has_traj_bids = {bid for bid, _ in traj_infos}

    ctx.no_traj_bids = [bid for bid in bids if bid not in has_traj_bids]
    ctx.traj_lines = [ln for _, ln in traj_infos]

    if not ctx.traj_lines:
        ctx.errors.append("no_traj_lines")
        return

    proceed()


@desc()
def fetch_traj_infos_from_http(ctx: Context, proceed):
    """
    获取轨迹信息从 HTTP API（@导航）
    """
    bids = [p.bid for p in ctx.store_pois]

    traj_infos = [(bid, ln) for bid in bids for ln in get_end_point_track_by_api(bid)]
    traj_infos = [(bid, ln) for bid, ln in traj_infos if ln.geom_type == "LineString"]
    has_traj_bids = {bid for bid, _ in traj_infos}

    ctx.no_traj_bids = [bid for bid in bids if bid not in has_traj_bids]
    ctx.traj_lines = [ln for _, ln in traj_infos]

    if not ctx.traj_lines:
        ctx.errors.append("no_traj_lines")
        return

    proceed()


@desc()
def try_buffer_polygon(ctx: Context, proceed, try_buffer: float):
    """
    尝试 buffer 停车场面
    """
    if ctx.geom.geom_type != "Polygon" or ctx.baseline.geom_type != "LineString":
        ctx.errors.append("invalid_geom_or_baseline")
        return

    geom = ctx.geom.buffer(try_buffer).intersection(ctx.street_region)
    buffer_geom = repair_polygon.repair_park_polygon(geom, ctx.street_region)
    ctx.buffer_geom = buffer_geom if buffer_geom else ctx.geom
    ctx.baseline = repair_polygon.calc_baseline(ctx.buffer_geom.wkt, ctx.street_region)
    proceed()


@desc()
def calc_pass_ranges_v1(ctx: Context, proceed):
    """
    计算穿行区间 v1：取轨迹与面的交集线段，作为穿行区间，【不允许】多次穿行
    """
    pass_ranges = []
    used_traj_lines = []
    for traj_line in ctx.traj_lines:
        start_pt, end_pt = Point(traj_line.coords[0]), Point(traj_line.coords[-1])
        if not ctx.buffer_geom.contains(start_pt) and not ctx.buffer_geom.contains(end_pt):
            continue

        clipped_traj_line = traj_line.intersection(ctx.buffer_geom)
        if clipped_traj_line.geom_type != "LineString" or clipped_traj_line.is_empty:
            continue

        used_traj_lines.append(traj_line)
        # noinspection PyBroadException
        pts = [Point(x, y) for x, y in clipped_traj_line.coords]
        pass_range = _get_project_range(ctx.baseline, pts)
        pass_ranges.append(pass_range)

    ctx.pass_ranges = pass_ranges
    ctx.heat_ranges = algo.calc_overlap_ranges(pass_ranges)
    ctx.used_traj_lines = used_traj_lines
    proceed()


@desc()
def calc_pass_ranges_v2(ctx: Context, proceed):
    """
    计算穿行区间 v1：取轨迹与面的交集线段，作为穿行区间，【允许】多次穿行
    """
    pass_ranges = []
    used_traj_lines = []
    for traj_line in ctx.traj_lines:
        start_pt, end_pt = Point(traj_line.coords[0]), Point(traj_line.coords[-1])
        if not ctx.buffer_geom.contains(start_pt) and not ctx.buffer_geom.contains(end_pt):
            continue

        clipped_traj_line = traj_line.intersection(ctx.buffer_geom)
        if clipped_traj_line.is_empty:
            continue

        used_traj_lines.append(traj_line)
        # noinspection PyBroadException
        pts = [Point(x, y) for ln in geometric.flat_line(clipped_traj_line) for x, y in ln.coords]
        pass_range = _get_project_range(ctx.baseline, pts)
        pass_ranges.append(pass_range)

    ctx.pass_ranges = pass_ranges
    ctx.heat_ranges = algo.calc_overlap_ranges(pass_ranges)
    ctx.used_traj_lines = used_traj_lines
    proceed()


def calc_excess_pass_length(ctx: Context, proceed, n: list[int]):
    """
    计算穿行区间超出当前停车场 baseline 的长度
    """

    def calc(limit: int):
        pts = [Point(x, y) for p in geometric.flat_polygon(ctx.manual_geom) for x, y in p.exterior.coords]
        small_baseline, _ = cluster_projection.get_projection(ctx.baseline, pts)
        valid_ranges = [r for r in ctx.heat_ranges if r[1] > limit]
        lines = [shapely.ops.substring(ctx.baseline, a, b) for (a, b), _ in valid_ranges]
        lines = [x for x in lines if x.length > 0]
        line = shapely.ops.linemerge(lines)

        big_baselines = [
            ln for ln in geometric.flat_line(line) if not small_baseline.contains(ln) and small_baseline.intersects(ln)
        ]
        line = shapely.unary_union(big_baselines)

        width = 0.1 * METER
        diff = line.buffer(width).difference(small_baseline.buffer(2 * width))
        return diff.length / 2 - width if diff.length > 0 else 0.0

    ctx.diffs = [calc(x) for x in n]
    proceed()


@desc()
def save_result(ctx: Context, proceed):
    """
    保存结果
    """
    used = {ln.wkt for ln in ctx.used_traj_lines}
    result = {
        "task_id": ctx.task_id,
        "strategy_id": ctx.strategy_id,
        "baseline": ctx.baseline.wkt,
        "geom": ctx.geom.wkt,
        "manual_geom": ctx.manual_geom.wkt,
        "buffer_geom": ctx.buffer_geom.wkt,
        "store_pois": [p.bid for p in ctx.store_pois],
        "no_traj_bids": ctx.no_traj_bids,
        "useless_traj_lines": [ln.wkt for ln in ctx.traj_lines if ln.wkt not in used],
        "used_traj_lines": [ln.wkt for ln in ctx.used_traj_lines],
        "weighted_lines": [
            {"geom": shapely.ops.substring(ctx.baseline, a, b).wkt, "weight": w} for (a, b), w in ctx.heat_ranges
        ],
    }
    utils.write_json(utils.ensure_path(ctx.work_dir / "json_info" / f"{ctx.task_id}-{ctx.strategy_id}.json"), result)
    proceed()


@desc()
def draw_preview(ctx: Context, proceed):
    """
    绘制预览图
    """
    if not ctx.heat_ranges:
        ctx.errors.append("no_heat_ranges")
        return

    max_weight = max(w for r, w in ctx.heat_ranges)
    if max_weight <= 5:
        ctx.errors.append(f"max_weight_too_small: {max_weight}")
        return

    geoms = [ctx.geom, ctx.manual_geom, ctx.buffer_geom]
    merged_geom = shapely.unary_union([x.envelope for x in geoms])
    bounds = boundary.from_wkt(merged_geom.wkt, buffer=50 * METER)
    image = satellite_imagery.crop(bounds)
    if image is None:
        ctx.errors.append("no_satellite_imagery")
        return

    for line in ctx.used_traj_lines:
        preview_image.draw_linestring(image, line.wkt, bounds, thickness=1, color=preview_image.COLOR_BLUE)

    preview_image.draw_polygon(image, ctx.geom.wkt, bounds, thickness=4)
    preview_image.draw_polygon(image, ctx.manual_geom.wkt, bounds, thickness=4, color=preview_image.COLOR_GREEN)
    preview_image.draw_polygon(image, ctx.buffer_geom.wkt, bounds, thickness=4, color=preview_image.COLOR_CYAN)

    preview_image.draw_linestring(image, ctx.baseline.wkt, bounds, thickness=2, color=preview_image.COLOR_PURPLE)

    weighted_lines = [(shapely.ops.substring(ctx.baseline, a, b).wkt, w) for (a, b), w in ctx.heat_ranges]
    for line, weight in weighted_lines:
        preview_image.draw_linestring(image, line, bounds, thickness=16, color=weight_to_color(min(weight, 30), 30))

    save_path = utils.ensure_path(ctx.work_dir / "preview" / f"{ctx.task_id}-{ctx.strategy_id}.jpg")
    cv2.imwrite(str(save_path), image)
    proceed()


# helpers:


def weight_to_color(weight, max_weight):
    """
    将权重映射到RGB色彩空间
    """
    # 将权重映射到0-120的Hue值（蓝色到红色）
    hue = 120 - int((1 - weight / max_weight) * 120)
    hsv_color = np.uint8([[[hue, 255, 255]]])  # 饱和度和亮度设置为最大
    rgb_color = cv2.cvtColor(hsv_color, cv2.COLOR_HSV2BGR)
    return tuple(float(x) for x in rgb_color[0][0][::-1])  # 返回色彩为BGR


def _buffer_polygon(polygon: Polygon, baseline: LineString, buffer: float) -> Polygon:
    sideline, i_line = geometric.get_sidelines(polygon, baseline, eps=1 * METER)

    sideline = geometric.trim_linestring(sideline, -0.5, 10 * METER)
    sideline = geometric.trim_linestring_for_buffer(sideline, (buffer + 1 * METER))
    sideline = geometric.trim_linestring_for_buffer(sideline, -(buffer + 1 * METER))
    sideline = geometric.extend_linestring(sideline, 1 * METER)

    center_point = i_line.interpolate(i_line.length / 2)
    is_left = geometric.is_left(center_point, sideline)
    buffer *= 1 if is_left else -1
    return sideline.buffer(buffer, cap_style="flat", single_sided=True)


def _get_project_range(target_line: LineString, points: list[Point]) -> tuple[float, float]:
    project_distances = [(p, target_line.project(p)) for p in points]
    project_points = [(p, target_line.interpolate(d), d) for p, d in project_distances]

    project_points.sort(key=lambda x: x[-1])
    min_project_len = project_points[0][-1]
    max_project_len = project_points[-1][-1]
    return min_project_len, max_project_len


# IO:


def export_turing_results():
    """
    导出图灵作业结果，用于策略评估验证
    """
    sql_turing = """
        select prev_info_id, turing_id, st_astext(a.geom) 
        from park_storefront_turing_result a
        join park_storefront_verify_pushed b on a.info_id = b.info_id
        where recall = '1' and precise != '1' and b.info_source = 'strategy'
    """
    sql_strategy = """
        select id, task_id, st_astext(baseline), st_astext(geom)
        from park_storefront_strategy
        where id in %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql_turing)
    turings = [(int(info_id), turing_id, geom) for info_id, turing_id, geom in ret]
    turings = [x for x in turings if x[0] > 0]

    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql_strategy, [tuple(x[0] for x in turings)])
    strategies = {strategy_id: (task_id, baseline, geom) for strategy_id, task_id, baseline, geom in ret}

    for strategy_id, turing_id, manual_geom in turings:
        task_id, baseline, geom = strategies[strategy_id]
        yield task_id, strategy_id, turing_id, baseline, geom, manual_geom


def get_contexts_from_online(work_dir: Path):
    """
    将线上的数据转化为 Context 对象
    """
    contexts = [
        Context(
            work_dir=work_dir,
            task_id=task_id,
            strategy_id=strategy_id,
            turing_id=turing_id,
            baseline=wkt.loads(baseline),
            geom=wkt.loads(geom),
            manual_geom=wkt.loads(manual_geom),
            street_region=repair_polygon.get_street_region(geom.wkt),
        )
        for task_id, strategy_id, turing_id, baseline, geom, manual_geom in export_turing_results()
    ]
    return contexts


def get_contexts_from_file(file_path: Path, work_dir: Path):
    """
    从文件中读取数据，并转化为 Context 对象
    """
    contexts = [
        Context(
            work_dir=work_dir,
            task_id=task_id,
            strategy_id=strategy_id,
            turing_id=turing_id,
            baseline=wkt.loads(baseline),
            geom=wkt.loads(geom),
            manual_geom=wkt.loads(manual_geom),
            street_region=repair_polygon.get_street_region(geom.wkt),
        )
        for task_id, strategy_id, turing_id, baseline, geom, manual_geom in tsv.read_tsv(file_path)
    ]
    return contexts


def get_contexts_from_bids(bids: list[str], work_dir: Path):
    """
    根据指定 bid 的数据转化为 Context 对象
    """

    def get_context(bid: str):
        sql = """
            select st_astext(geom) from park_storefront_prod_parking
            where bid = %s
        """
        ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [bid])
        geom_wkt = ret[0]
        if not geom_wkt:
            logger.warning(f"not found '{bid}'")
            return None

        geom = wkt.loads(ret[0])
        street_region = repair_polygon.get_street_region(geom.wkt)
        return Context(
            work_dir=work_dir,
            task_id=int(bid),
            strategy_id=int(bid),
            turing_id=bid,
            geom=geom,
            manual_geom=geom,
            baseline=repair_polygon.calc_baseline(geom.wkt, street_region),
            street_region=street_region,
        )

    contexts = [get_context(bid) for bid in bids]
    contexts = [x for x in contexts if x is not None]
    return contexts


pipe = pipeline.Pipeline(
    partial(fetch_store_pois, search_buffer=50 * METER),
    # fetch_traj_infos_from_db,
    fetch_traj_infos_from_http,
    partial(try_buffer_polygon, try_buffer=50 * METER),
    # calc_pass_ranges1,
    calc_pass_ranges_v2,
    partial(calc_excess_pass_length, n=list(range(0, 11))),
    save_result,
    draw_preview,
)
desc.attach(pipe)
desc.disabled = True


def process(ctx: Context):
    """
    并行函数
    """
    try:
        pipe(ctx)
    except Exception as e:
        ctx.errors.append(str(e))

    return ctx


@logger.catch
def main():
    """
    主函数
    """
    output_dir = Path("output")
    work_dir = output_dir / "track" / "case_bid_20250213"
    error_path = utils.ensure_path(work_dir / "error.tsv")
    bids_path = utils.ensure_path(work_dir / "bids.txt")
    diff_path = utils.ensure_path(work_dir / "diff.tsv")

    # contexts = get_contexts_from_online(work_dir)
    # contexts = get_contexts_from_file(Path("output_track_filter/turing_info_error.tsv"), work_dir)
    # bids = [x[0] for x in tsv.read_tsv("online_bids.txt")]
    # bids = risk_mining.get_online_bid_by_city(["广州市", "深圳市"])
    bids = [x[0] for x in tsv.read_tsv("case_bid_20250213.txt")]
    contexts = get_contexts_from_bids(bids, work_dir)
    with Pool(8) as pool:
        # for ctx in tqdm(map(process, contexts), total=len(contexts)):
        for ctx in tqdm(pool.imap_unordered(process, contexts), total=len(contexts)):
            rows = [[x.bid] for x in ctx.store_pois]
            tsv.write_tsv(bids_path, rows, mode="a")

            if ctx.errors:
                error = [ctx.task_id, ctx.strategy_id, ",".join(ctx.errors)]
                tsv.write_tsv(error_path, [error], mode="a")
                continue

            row = [
                ctx.task_id,
                ctx.strategy_id,
                ctx.manual_geom.wkt,
                ctx.buffer_geom.wkt,
                *[x / METER for x in ctx.diffs],
            ]
            # 取 n=5 进行筛选
            tsv.write_tsv(diff_path, [row], mode="a")


if __name__ == "__main__":
    main()
