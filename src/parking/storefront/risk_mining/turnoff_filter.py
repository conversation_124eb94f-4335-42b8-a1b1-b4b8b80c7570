"""
将停车场面按中心线分段（5m 一段），分别计算每个段落的熄火点密度（线密度：单位长度内的熄火点的个数）
筛选条件（根据 bad case 卡的阈值，使用博士轨迹）：若左 or 右末端存在连续 >= 6 个的低密度段落（6*5m=30m，低密度 < 0.25），
且所有段落的最大密度 > 4（防止本来就没熄火点），则输出

```cmd
awk -F '\t' '($3 >= 6 || $4 >= 6) && $NF > 4 {print $1}' feature_doctor.tsv 
```
"""
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Iterable

import shapely.ops
from loguru import logger
from shapely import LineString, Point, wkt
from tqdm import tqdm

from src.parking.storefront import risk_mining, turnoff_point_cluster
from src.parking.storefront.post_process import central_line
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.tools import tsv, utils


def get_density_segments(baseline: LineString, pts: Iterable[Point], segment_size: float):
    """
    获取分段密度
    """

    def get_segments():
        a = 0
        while a < baseline.length:
            b = a + segment_size
            yield shapely.ops.substring(
                baseline, start_dist=a, end_dist=(b if b < baseline.length else baseline.length)
            )
            a = b

    for segment in get_segments():
        vertical_pts = [pt for pt in pts if geometric.get_foot_point(pt, segment)]
        density = len(vertical_pts) / (segment.length / METER)
        yield segment, density


def get_central_line(geom: str):
    """
    获取停车场中心线
    """
    # noinspection PyBroadException
    try:
        return central_line.get_central_line_irregular(wkt.loads(geom))
    except:
        return None


def get_low_density_feature(items: list[tuple], threshold: float):
    """
    获取低密度特征
    """
    max_density = max(item[2] for item in items)

    def get_continuous_low_segments(indexes):
        for i in indexes:
            density = items[i][2]
            if density < threshold:
                yield items[i]
            else:
                break

    start_low_segments = list(get_continuous_low_segments(range(len(items))))
    end_low_segments = list(get_continuous_low_segments(reversed(range(len(items)))))
    return len(start_low_segments), len(end_low_segments), len(items), max_density


def process(item: tuple):
    """
    并行函数
    """
    bid, city, geom = item
    baseline = get_central_line(geom)
    if baseline is None:
        return [(bid, city, 0.0, "LINESTRING EMPTY")]

    pts = turnoff_point_cluster.get_doctor_points(geom)
    # pts = turnoff_point_cluster.get_zhongyuan_points(geom)
    if not pts:
        return [(bid, city, 0.0, baseline.wkt)]

    pts = [pt.point for pt in pts]
    result = [
        (bid, city, density, segment.wkt)
        for segment, density in get_density_segments(baseline, pts, segment_size=5 * METER)
    ]
    return result


@logger.catch
def main():
    """
    主函数
    """
    output_dir = Path("output")
    work_dir = output_dir / "turnoff" / "case_bid_20250213"
    result_path = utils.ensure_path(work_dir / "result_doctor.tsv")
    feature_path = utils.ensure_path(work_dir / "feature_doctor.tsv")

    # bids = risk_mining.get_online_bid_by_city(["广州市", "深圳市"])
    bids = [x[0] for x in tsv.read_tsv("case_bid_20250213.txt")]
    # items = risk_mining.get_online_items()
    items = risk_mining.get_online_item_by_bid(bids)
    with Pool(64) as pool:
        for payload in tqdm(pool.imap_unordered(process, items), total=len(items)):
            tsv.write_tsv(result_path, payload, mode="a")
            feature = get_low_density_feature(payload, threshold=0.25)
            bid, city = payload[0][:2]
            tsv.write_tsv(feature_path, [[bid, city, *feature]], mode="a")


if __name__ == "__main__":
    main()
