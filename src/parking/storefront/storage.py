"""
数据存储模块，作为数据库访问的抽象
"""
from dataclasses import dataclass
from typing import Optional

from shapely import wkt, Polygon, Point

from src.fix_geom_process.tools import get_road_width
from src.parking.recognition import dbutils
from src.tools import pgsql

METER = 1e-5


@dataclass(frozen=True)
class PoiInfo:
    """
    风险点 POI 信息
    """

    bid: str
    name: str
    std_tag: str
    geom: Point


def get_street_region(point_wkt: str) -> Optional[tuple[str, Polygon]]:
    """
    获取街道区域
    """
    sql = """
        select face_id, st_astext(geom) from street_region_20241109
        where st_contains(geom, %s);
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [f"SRID=4326;{point_wkt}"])
    return (ret[0], wkt.loads(ret[1])) if ret else None


def get_storefront_poi_infos(region_wkt: str) -> list[PoiInfo]:
    """
    获取临街POI
    """
    sql = """
        with p as (
            select bid, name, std_tag, geometry from poi
            where st_contains(%s, geometry)
        )
        select p.bid, p.name, p.std_tag, st_astext(p.geometry)
        from p 
        inner join poi_spatial_classify a on p.bid = a.bid
        where a.classify like '临街POI%%';
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [f"SRID=4326;{region_wkt}"])
    return [PoiInfo(bid=bid, name=name, std_tag=std_tag, geom=wkt.loads(geom)) for bid, name, std_tag, geom in ret]


def get_poi_info_by_bid(bids: list[str]) -> list[PoiInfo]:
    """
    获取指定 BID 的 POI
    """
    sql = """
        select bid, name, std_tag, st_astext(geometry) from poi
        where bid in %s
            and click_pv > 20;
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(bids)])
    return [PoiInfo(bid=bid, name=name, std_tag=std_tag, geom=geom) for bid, name, std_tag, geom in ret]


def get_nav_faces(geom: str):
    """
    获取道路面
    """
    sql = """
        select link_id, s_nid, e_nid, st_astext(geom), dir, kind, lane_l, lane_r, form
        from nav_link
        where kind < 8 and st_intersects(%s, geom); 
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{geom}"])
    links = [(x[3], get_road_width(*x[-5:])) for x in ret]
    links = [wkt.loads(geom).buffer((width + 2) * METER / 2) for geom, width in links]
    return links


def get_bud_faces(geom: str):
    """
    获取建筑物面
    """
    sql = """
        select st_astext(geom) from bud_face
        where st_intersects(geom, %s);
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [f"SRID=4326;{geom}"])
    return [x[0] for x in ret]


def get_recognition_faces(geom: str):
    """
    获取识别面
    """
    sql = """
        select st_astext(geom) from recognition_parking_result
        where st_intersects(geom, %s) and confidence > 0.2;
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [f"SRID=4326;{geom}"])
    return [x[0] for x in ret]
