"""
按街区任务流转时，可复用的函数
"""
from pathlib import Path
from typing import Union, Iterable

from src.parking.recognition import dbutils
from src.tools import pgsql


def get_task_ids(status: str, batch: str = None) -> list[int]:
    """
    根据 task 的状态和批次（可选）筛选任务
    """
    if batch:
        sql = """
            select task_id from park_storefront_task
            where batch = %s and status = %s
        """
        ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [batch, status])
    else:
        sql = """
            select task_id from park_storefront_task
            where status = %s
        """
        ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [status])

    return [r[0] for r in ret]


def update_task_status(conn, task_id: Union[int, Iterable[int]], from_status: str, to_status: str):
    """
    更新任务状态：从 from_status 变为 to_status，使用外部传入的 conn，以保障整个过程处于同一个事务中
    """
    if isinstance(task_id, int):
        task_ids = (task_id,)
    else:
        task_ids = tuple(task_id)

    sql = """
        update park_storefront_task set status = %s
        where status = %s and task_id in %s 
    """
    pgsql.execute(conn, sql, [to_status, from_status, task_ids])


def get_strategy_ids(status: str, batch: str, batch_image: str) -> list[int]:
    """
    根据 strategy 的状态和批次（可选）筛选策略面
    """
    sql = """
        select b.id 
        from park_storefront_task a
        inner join park_storefront_strategy b on a.task_id = b.task_id
        where a.batch = %s and b.batch_image = %s and b.status = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [batch, batch_image, status])
    return [r[0] for r in ret]


def update_strategy_status(conn, strategy_id: Union[int, Iterable[int]], from_status: str, to_status: str):
    """
    更新策略面的状态：从 from_status 变为 to_status，使用外部传入的 conn，以保障整个过程处于同一个事务中
    """
    if isinstance(strategy_id, int):
        strategy_ids = (strategy_id,)
    else:
        strategy_ids = tuple(strategy_id)

    sql = """
        update park_storefront_strategy set status = %s
        where status = %s and id in %s 
    """
    pgsql.execute(conn, sql, [to_status, from_status, strategy_ids])


def get_output_dir(work_dir: Union[Path, str], step: str, version: str) -> Path:
    """
    获取输出目录路径，用于储存策略的输出文件，自动累加 version 中的 build 号
    """
    work_dir = Path(work_dir) if isinstance(work_dir, str) else work_dir
    i = 0
    while True:
        output_dir = work_dir / f"{step}-v{version}.{i}"
        if not output_dir.exists():
            return output_dir

        i += 1
