"""
门前轨迹工程
每日例行
"""
import logging
import subprocess
import sys
import os
import src.parking.storefront.traj.dest.conf as conf
import src.parking.storefront.traj.dest.sqlutils as sqlutils

from datetime import datetime, timedelta
from src.parking.storefront.traj.dest.traj_to_db import do_traj_to_db_run
from src.tools import pgsql
from src.parking.storefront.flow.monitor.util import send_hi


# 添加日志配置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", stream=sys.stdout)
logger = logging.getLogger(__name__)


def run():
    """
    run
    """
    # 更新终点轨迹文件
    logger.info("step_1_start: update_target_bid_list")
    bids_list = update_target_bid_list()
    logger.info(f"step_1_end: update_target_bid_list: {len(bids_list)}")

    # 清理目录
    logger.info("step_2_start: 清理轨迹数据目录")
    clear_dir(conf.DEST_TRAJ_INPUT)
    logger.info("step_2_end: 清理轨迹数据目录")

    # 下载轨迹数据
    logger.info("step_3_start: download_traj_files")
    download_traj_files()
    logger.info("step_3_end: download_traj_files")

    # 轨迹入库
    logger.info("step_4_start: process_to_db")
    do_traj_to_db_run()
    logger.info("step_4_end: process_to_db")

    # 轨迹库切换
    logger.info("step_5_start: switch_db")
    switch_db()
    logger.info("step_5_end: switch_db")

    # 轨迹库清理
    logger.info("step_6_start: truncate_traj_update_db")
    truncate_traj_update_db()
    logger.info("step_6_end: truncate_traj_update_db")

    # 写处理完成标志
    logger.info("step_7_start: 写入处理完成标志")
    touch_done_flag()
    logger.info("step_7_end: 写入处理完成标志")

    # 如流群推送完成消息
    logger.info("step_8_start: send_hi")
    send_hi(f"临街轨迹入库完成，有效poi轨迹数量共：{len(bids_list)}")
    logger.info("step_8_end: send_hi")


def truncate_traj_update_db():
    """
    清理用于更新数据的轨迹库
    """
    conn = pgsql.get_connection_ttl(pgsql.TRAJ_DB2)
    cursor = conn.cursor()
    sql = """
       truncate park_storefront_dest_traj_update;
    """
    try:
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        logger.error(f"truncate park_storefront_dest_traj_update failed: {e}")


def switch_db():
    """
    切换轨迹库
    """
    conn = pgsql.get_connection_ttl(pgsql.TRAJ_DB2)
    cursor = conn.cursor()
    sql = """
       ALTER TABLE park_storefront_dest_traj RENAME TO park_storefront_dest_traj_bak;
       ALTER TABLE park_storefront_dest_traj_update RENAME TO park_storefront_dest_traj;
       ALTER TABLE park_storefront_dest_traj_bak RENAME TO park_storefront_dest_traj_update;
    """
    try:
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        logger.error(f"switch_db failed: {e}")


def clear_dir(dir_path):
    """
    删除目录下所有文件和子目录
    """
    if "park_storefront_result" not in dir_path:
        return

    for root, dirs, files in os.walk(dir_path):
        for name in files:
            try:
                os.remove(os.path.join(root, name))
            except Exception as e:
                logger.error(f"删除文件失败: {name}, 错误: {e}")
        for name in dirs:
            try:
                subprocess.call(["rm", "-rf", os.path.join(root, name)])
            except Exception as e:
                logger.error(f"删除目录失败: {name}, 错误: {e}")


def touch_done_flag():
    """
    创建一个处理完成的标志文件
    """
    date_str = datetime.now().strftime("%Y%m%d")
    done_file_path = f"{conf.DEST_TRAJ_INPUT}/_TRAJ_TO_DB_DONE_{date_str}"
    try:
        with open(done_file_path, "w") as f:
            f.write(datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\n")
    except Exception as e:
        logger.error(f"创建标志文件失败: {e}")


def update_target_bid_list():
    """
    更新终点轨迹列表文件
    """
    bids_list = sqlutils.get_target_bids_list()
    delete_target_bids_old_file()
    create_target_bids_new_file(bids_list)
    link_target_bids_file()
    return bids_list


def delete_target_bids_old_file():
    """
    删除历史终点文件
    """
    try:
        exec_shell_cmd(
            f"cd {conf.BID_LIST_DIR};"
            f"[ -f target_bids_park_storefront.list ] && rm target_bids_park_storefront.list;"
        )
    except Exception as e:
        logger.error(f"delete_target_bids_old_file failed: {e}")


def create_target_bids_new_file(bids_result):
    """
    创建轨迹终点bids文件
    """
    file = open(f"{conf.BID_LIST_DIR}/target_bids_park_storefront.list", "w")
    for item in bids_result:
        strs = f"{item[0]}\t\n"
        file.write(strs)
    file.close()


def link_target_bids_file():
    """
    软链接终点文件
    """
    try:
        exec_shell_cmd(
            f"cd {conf.FETCH_TRAJ_PATH};"
            f"ln -sf ./bid_list/target_bids_park_storefront.list target_bids_park_storefront.list;"
        )
    except Exception as e:
        logger.error(f"link_target_bids_file failed: {e}")


def download_traj_files(start_date=None, days=60, force=1):
    """
    下载轨迹数据
    """
    if start_date is None:
        start_date = (datetime.today() - timedelta(days=0)).strftime("%Y%m%d")

    shell_cmd = [
        "sh",
        f"{conf.FETCH_TRAJ_PATH}/park_storefront_traj_get.sh",
        start_date,
        str(days),
        str(force),
    ]
    print(f"[INFO] 开始执行脚本：{' '.join(shell_cmd)}")

    try:
        cwd_path = conf.FETCH_TRAJ_PATH
        subprocess.check_call(shell_cmd, cwd=cwd_path)
        print(f"[SUCCESS] 下载完成：起始日期 {start_date}，共 {days} 天")
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] 下载脚本执行失败: {e}")


def exec_shell_cmd(cmd, cwd=None):
    """
    执行 shell 命令
    :param cmd: 要执行的 shell 命令（字符串）
    :param cwd: 指定工作目录（可选）
    :return: 返回标准输出字符串
    """
    print(f"[CMD] {cmd}")
    try:
        result = subprocess.check_output(cmd, shell=True, cwd=cwd, stderr=subprocess.STDOUT)
        print(result.decode("utf-8"))
        return result.decode("utf-8")
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] 命令执行失败：{e.output.decode('utf-8')}")
        raise e


if __name__ == "__main__":
    """
    main
    """
    run()
