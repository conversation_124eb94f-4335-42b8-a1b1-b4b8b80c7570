"""
数据获取层
"""
from src.parking.recognition import dbutils
from src.tools import pgsql


def get_target_bids_list():
    """
    获取门前终点bids
    """
    sql = f"""
        select distinct bid
        from sync_traj_bid_record
        where batch in ('storefront_work_improve')
    """
    bids_list = dbutils.fetch_all(pgsql.TRAJ_DB2, sql)
    return bids_list


def create_park_storefront_dest_traj_table():
    """
    create table
    """
    sql = f"""
        CREATE TABLE IF NOT EXISTS park_storefront_dest_traj (
            id          BIGINT PRIMARY KEY DEFAULT nextval('park_storefront_dest_traj_id_seq'),
            bid         VARCHAR(128) NOT NULL DEFAULT '',
            geom        GEOMETRY(POINT, 4326),  -- 定义 geometry 类型，并指定 SRID 为 4326 (WGS 84)
            hash_index  INTEGER DEFAULT -1 CHECK (hash_index = 0),
            props       TEXT,
            traj_time   TIMESTAMP WITHOUT TIME ZONE DEFAULT '1970-01-01 00:00:00',
            create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT now(),
            cuid        VARCHAR(2000) NOT NULL DEFAULT ''
        );
        CREATE SEQUENCE park_storefront_dest_traj_id_seq;
        -- 创建索引
        CREATE INDEX IF NOT EXISTS park_storefront_dest_traj_bid_idx ON park_storefront_dest_traj (bid);
        CREATE INDEX IF NOT EXISTS park_storefront_dest_traj_geom_idx ON park_storefront_dest_traj USING gist (geom);
    """
    pass
