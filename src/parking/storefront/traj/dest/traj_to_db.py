"""
终点轨迹入库
"""
import logging
import sys
import shapely.wkt
import src.parking.storefront.traj.dest.conf as conf

from src.trajectory.utils.coord_trans import wgs84_to_gcj02
from datetime import datetime
from pathlib import Path
from shapely.geometry import LineString
from multiprocessing import Pool, cpu_count
from src.tools import pgsql


# 添加日志配置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", stream=sys.stdout)
logger = logging.getLogger(__name__)


def traj_to_db(file_path, traj_time):
    """
    轨迹文件入库
    """
    conn = pgsql.get_connection_ttl(pgsql.TRAJ_DB2)
    cursor = conn.cursor()

    count = 0
    batch_size = conf.TRAJ_COMMIT_BATCH_NUM

    with open(file_path, encoding="utf-8") as f:
        try:
            for line in f.readlines():
                try:
                    bid, cuid, wkt = line.rstrip().split("\t")
                except Exception as e:
                    bid, wkt = line.rstrip().split("\t")
                    cuid = ""
                    logger.error(f"traj_to_db parse failed: {e}")
                gcj_wkt = trans_wgs84_linestring_to_gc02(wkt)

                sql = """
                    INSERT INTO park_storefront_dest_traj_update (
                        bid, cuid, geom, traj_time
                    ) VALUES (
                        %s, %s, ST_GeomFromText(%s, 4326), %s
                    );
                """
                try:
                    cursor.execute(sql, (bid, cuid, gcj_wkt, traj_time))
                except Exception as e:
                    logger.error(f"insert failed: {e}")

                count += 1
                if count % batch_size == 0:
                    conn.commit()
                    logger.info(f"Committed {count} rows")

            if count % batch_size != 0:
                conn.commit()
                logger.info(f"Final commit for {count} rows")

        except Exception as e:
            logger.error(f"traj_to_db failed: {e}")
        finally:
            cursor.close()
            conn.close()


def trans_wgs84_linestring_to_gc02(line_wkt):
    """
    wgs84线转gcj02
    """
    line_coords = shapely.wkt.loads(line_wkt).coords

    trans_line_points = []
    for coord in line_coords:
        gcj_lon, gcj_lat = wgs84_to_gcj02(coord[0], coord[1])
        trans_line_points.append((gcj_lon, gcj_lat))
    return LineString(trans_line_points).wkt


def process_directory(curr_dir):
    """
    process_directory
    """
    if not Path(curr_dir / "done.flag").exists():
        return  # AFS还没拉完，不处理

    if Path(curr_dir / "success_to_pg.flag").exists():
        return  # 已入库过，不再重复入库

    # 入库
    traj_time = datetime.strptime(curr_dir.name.split("/")[-1], "%Y%m%d")
    files = list(curr_dir.glob("**/*"))
    for count, file in enumerate(files, start=1):
        logger.info(f"curr_dir: {curr_dir}, process: {count}")
        traj_to_db(file, traj_time)

    # 入库完成标志
    Path(curr_dir / "success_to_pg.flag").touch()


def do_traj_to_db_run():
    """
    多进程入库，每个进程处理一个日期目录
    """
    data_dir = Path(conf.DEST_TRAJ_INPUT)
    dir_list = [d for d in data_dir.iterdir() if d.is_dir()]
    dir_list.sort()

    if len(dir_list) == 0:
        logger.info("无文件")
        return

    logger.info(f"共发现 {len(dir_list)} 个目录，将使用 {min(cpu_count(), 20)} 个进程并发处理")
    with Pool(processes=min(cpu_count(), 20)) as pool:
        pool.map(process_directory, dir_list)


if __name__ == '__main__':
    """
    main
    """
    do_traj_to_db_run()
