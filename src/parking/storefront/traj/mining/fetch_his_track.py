#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取历史轨迹
"""

import json
import statistics
import sys
from dataclasses import dataclass
from typing import Optional

import numpy as np
from shapely import wkt, LineString, Point, Polygon, MultiPolygon
from shapely.ops import split, unary_union
from shapely.geometry.base import CAP_STYLE, JOIN_STYLE

from src.parking_roadside.db.model import PGModel
from src.parking_roadside.api.tracks_api import get_tracks_all
from scipy.stats import linregress
from src.parking.storefront import polygon_strategy
from src.parking.storefront.flow.model.road import get_complete_ld_polygon_2d
from src.parking.storefront.flow.context import gen_ctx
from src.parking.storefront.utils import geometric
from dacite import from_dict
from rdp import rdp

METER = 0.00001  # 近似1米


@dataclass
class TrackItem:
    """
    轨迹点
    """
    bos_key: str
    cdid: str
    create_time: str
    has_pic: bool
    hell: int
    id: str
    imp_ts: int
    is_ld: int
    link_id: str
    meshid: int
    north: float
    radius: float
    speed: float
    time: str
    track_id: str
    x: float
    y: float
    unix_timestamp: int
    source: Optional[int] = 0
    pic_url: Optional[str] = ""


@dataclass
class TrackInfo:
    """
    轨迹信息
    """
    source_id: str
    cdid: str
    geom: Polygon
    line: LineString
    track_type: str
    line_type: int
    max_distance: float
    std_distance: float
    max_diff: float
    std_diff: float
    parallel_angle: float
    speed_trend: float
    speeds: list[float]
    angles: list[float]
    bos_keys: list[str]
    track_list: list[TrackItem]


def speed_trend_regression(speeds):
    """
    计算速度趋势回归
    最小二乘法拟合
    :param speeds: list
    :return: >0 整体加速， <0 整体减速， 0 无明显趋势
    """
    x = np.arange(len(speeds))  # 生成时间索引
    slope, _, _, _, _ = linregress(x, speeds)

    return slope
    # if slope > 0:
    #     return 0
    # elif slope < 0:
    #     return 1
    # else:
    #     return 22


# def cal_speed_static(speeds):
#     """
#     计算速度距离拟合直线的方差和标准差
#     :param speeds: list
#     :return: mean, std
#     """
#     return statistics.mean(speeds), statistics.stdev(speeds)


def calc_line_is_straight(line: LineString):
    """
    判断线段是否近似直线
    最小二乘法，拟合一条直线， 获得距离
    :param line: LineString
    :return:
    """
    coords = np.array(line.coords)
    x, y = coords[:, 0], coords[:, 1]
    # 拟合一次函数 y = kx + b
    A = np.vstack([x, np.ones_like(x)]).T
    k, b = np.linalg.lstsq(A, y, rcond=None)[0]

    # 计算每个点到拟合直线 y = kx + b 的垂直距离
    distances = np.abs(k * x - y + b) / np.sqrt(k ** 2 + 1)
    return distances


def bearing_diffs(bearings):
    """
    差分偏北角
    """
    diffs = np.diff(bearings)
    # 把 -180~180 之外的变化值规整进来
    diffs = (diffs + 180) % 360 - 180
    return np.abs(diffs)


def is_curved(coords, tolerance=0.5):
    """
    判断是否有曲率
    """
    coords = np.array(coords)
    x, y = coords[:, 0], coords[:, 1]
    if len(x) < 3:
        return False
    # 拟合二次曲线
    coeffs = np.polyfit(x, y, deg=2)
    a = coeffs[0]
    # 如果二次项非零，说明有曲率
    print('is_curved', abs(a))
    return abs(a) > tolerance


def calc_line_type(line: LineString, bearing: list):
    """
    判断线段类型， 近似直线，折线,  U型
    return (line_type, max_distance, std_distance, max_diff, std_diff)
    0 直线  1 折线  2 U型  3 未知
    """
    distances = calc_line_is_straight(line)
    max_distance = np.max(distances)
    std_distance = np.std(distances)
    print('distances', distances, max_distance)
    diffs = bearing_diffs(bearing)
    max_diff = np.max(diffs)
    std_diff = np.std(diffs)
    print('diff', max_diff, std_diff, diffs)
    # is_curved(line.coords)
    if np.max(distances) < METER * 10 and max_diff < 20:  # 距离小于10米， 最大角度差小于15度
        return 0, max_distance, std_distance, max_diff, std_diff
    elif np.any(diffs > 30):  # 有任意角度差大于30
        return 1, max_distance, std_distance, max_diff, std_diff
    else:
        if len(line.coords) > 3:
            start = np.array(line.coords[0])
            mid = np.array(line.coords[len(line.coords) // 2])
            end = np.array(line.coords[-1])

            # 两段向量
            v1 = mid - start
            v2 = end - mid
            angle = angle_between_vectors(v1, v2)
            if angle > 150:
                return 2, max_distance, std_distance, max_diff, std_diff

        return 3, max_distance, std_distance, max_diff, std_diff


def calculate_angle(line: LineString, polygon_geom):
    """
    计算线段与多边形的交点，并计算交点到线段的切线与多边形边界的夹角
    """
    res_angles = []
    if isinstance(polygon_geom, Polygon):
        polygons = [polygon_geom]
    elif isinstance(polygon_geom, MultiPolygon):
        polygons = list(polygon_geom.geoms)
    else:
        print("传入的不是 Polygon 或 MultiPolygon：", type(polygon_geom))
        return res_angles
    for polygon in polygons:
        intersection_points = line.intersection(polygon.boundary)

        if intersection_points.is_empty:
            print("没有交点，无法计算角度")
            continue

        # 如果有多个交点，只取第一个交点
        intersection_point_list = []
        if intersection_points.geom_type == "MultiPoint":
            intersection_point_list.extend(intersection_points.geoms)
        else:
            intersection_point_list.append(intersection_points)

        res_angles = []
        for intersection_point in intersection_point_list:
            # 找到交点的索引
            coords = list(line.coords)
            for i in range(len(coords) - 1):
                segment = LineString([coords[i], coords[i + 1]])
                if segment.distance(intersection_point) < 1e-8:  # 精度为1e-8
                    tangent_line = segment
                    break
            else:
                print("未找到切线", intersection_point.wkt, line.wkt)
                continue

            # 找到多边形的边界段
            for i in range(len(polygon.exterior.coords) - 1):
                segment = LineString([polygon.exterior.coords[i], polygon.exterior.coords[i + 1]])
                if segment.distance(intersection_point) < 1e-8:  # 精度为1e-8
                    boundary_segment = segment
                    break
            else:
                print("未找到多边形边界", intersection_point.wkt, polygon.wkt)
                continue

            # 计算两条线段的方向向量
            def line_direction(_line):
                x1, y1 = _line.coords[0]
                x2, y2 = _line.coords[1]
                return np.array([x2 - x1, y2 - y1])

            v1 = line_direction(tangent_line)
            v2 = line_direction(boundary_segment)

            # 计算夹角（弧度制转角度制）
            angle_rad = np.arccos(np.clip(np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2)), -1.0, 1.0))
            angle_deg = np.degrees(angle_rad)
            res_angles.append(angle_deg)
    return res_angles


def simplified_coords(coords, epsilon=0.00001):
    """
    简化坐标点集合
    道格拉斯普克算法
    """
    # epsilon = 0.00001
    reduced_coords = rdp(coords, epsilon)
    return reduced_coords


def angle_between_vectors(v1, v2):
    """计算两个向量的夹角（单位：度）"""
    dot_product = np.dot(v1, v2)
    norm_product = np.linalg.norm(v1) * np.linalg.norm(v2)
    if norm_product == 0:
        return 0  # 避免除零错误
    cos_theta = np.clip(dot_product / norm_product, -1.0, 1.0)
    return np.degrees(np.arccos(cos_theta))


def check_parallel(line_obj, polygon_geom):
    """
    判断线段是否与多边形平行
    """
    if isinstance(polygon_geom, Polygon):
        polygons = [polygon_geom]
    elif isinstance(polygon_geom, MultiPolygon):
        polygons = list(polygon_geom.geoms)
    else:
        print("传入的不是 Polygon 或 MultiPolygon：", type(polygon_geom))
        return False
    edges = []
    for geom_obj in polygons:
        coords = simplified_coords(coords=list(geom_obj.exterior.coords))
        simple_polygon = Polygon(coords)
        # 获取多边形的每条边
        curr_edges = [LineString([simple_polygon.exterior.coords[i], simple_polygon.exterior.coords[i + 1]])
                      for i in range(len(simple_polygon.exterior.coords) - 1)]
        edges.extend(curr_edges)
    # 找出与线段最近的多边形边
    nearest_edge = None
    min_distance = float('inf')
    for edge in edges:
        dist = line_obj.distance(edge)
        if dist < min_distance:
            min_distance = dist
            nearest_edge = edge
    if nearest_edge is None:
        return False
    # 计算线段和最近边的夹角
    line_vector = np.array(
        [line_obj.coords[1][0] - line_obj.coords[0][0], line_obj.coords[1][1] - line_obj.coords[0][1]])
    edge_vector = np.array(
        [nearest_edge.coords[1][0] - nearest_edge.coords[0][0],
         nearest_edge.coords[1][1] - nearest_edge.coords[0][1]])

    angle = angle_between_vectors(line_vector, edge_vector)
    print('check_parallel', angle)
    return angle


def distance_points_in_area(tracks, tracks_idx_in_area, polygon):
    """
    计算轨迹点在区域内的距离
    """
    boundary = polygon.boundary
    distances = []
    for n, track in enumerate(tracks):
        if n not in tracks_idx_in_area:
            continue
        point = Point(track['x'], track['y'])
        distances.append(point.distance(boundary) / METER)
    return distances


def split_area(line_obj: LineString, polygon: Polygon) -> float:
    """
    切分面, 判断比例
    """
    split_polygons = split(polygon, line_obj)
    if len(split_polygons.geoms) == 2:
        poly1, poly2 = split_polygons.geoms
        area1 = poly1.area
        area2 = poly2.area
        ratio = area1 / area2 if area2 != 0 else float('inf')
    else:
        ratio = 1.0
    return ratio


def try_buffer(polygon: Polygon, baseline: LineString, buffer: float) -> Polygon:
    """try_buffer
    计算缓冲区
    """
    try:
        sideline, i_line = geometric.get_sidelines(polygon, baseline, eps=1 * METER)

        sideline = geometric.trim_linestring(sideline, -0.5, 10 * METER)
        sideline = geometric.trim_linestring_for_buffer(sideline, (buffer + 1 * METER))
        sideline = geometric.trim_linestring_for_buffer(sideline, -(buffer + 1 * METER))
        sideline = geometric.extend_linestring(sideline, 1 * METER)

        center_point = i_line.interpolate(i_line.length / 2)
        is_left = geometric.is_left(center_point, sideline)
        buffer *= 1 if is_left else -1
        return sideline.buffer(buffer, cap_style="flat", single_sided=True)
    except Exception as e:
        print(e)
        return None


def adjust_ld(geom_str: str, baseline: str) -> str:
    """
    通过ld调整
    """
    ctx = gen_ctx(autocommit=True)
    baseline_obj = wkt.loads(baseline)
    parking_geom_obj = wkt.loads(geom_str)
    buffer_1 = try_buffer(parking_geom_obj, baseline_obj, 0.0002)
    ld_polygon = get_complete_ld_polygon_2d(ctx, buffer_1.buffer(0.0002).wkt)
    ld_polygon_obj = wkt.loads(ld_polygon)
    ld_polygon_union = ld_polygon_obj.buffer(0.0001).buffer(-0.0001)
    res_parking = parking_geom_obj.difference(ld_polygon_union)
    res_parking_union = res_parking.buffer(0.0001).buffer(-0.0001)
    if isinstance(res_parking_union, Polygon):
        coords = simplified_coords(coords=list(res_parking_union.exterior.coords))
        if len(coords) < 4:
            print('err', res_parking_union.wkt)
            return ""
        simple_polygon = Polygon(coords)
    elif isinstance(res_parking_union, MultiPolygon):
        res_list = []
        for poly in res_parking_union.geoms:
            coords = simplified_coords(coords=list(poly.exterior.coords))
            if len(coords) < 4:
                print('err', poly.wkt)
                continue
            cur_simple_polygon = Polygon(coords)
            res_list.append(cur_simple_polygon)
        simple_polygon = unary_union(res_list)
    else:
        raise ValueError("Unsupported geometry type.")
    buffer_res = try_buffer(simple_polygon, baseline_obj, 0.0002)
    if buffer_res is not None:
        return buffer_res.wkt
    return res_parking_union.wkt


def process_track(geom, source_id, cdid, tracks_item):
    """
    处理轨迹
    """
    geom_obj = wkt.loads(geom)
    point_list = [
        (item['x'], item['y'])
        for item in tracks_item
    ]
    line_obj = LineString(point_list)
    start_in_area = False
    end_in_area = False
    full_in_area = True
    speeds = []
    start_speed = 0
    end_speed = 0
    speed_in_area = []
    speed_out_area = []
    track_idx_in_area = []  # 记录轨迹点在停车场内的索引
    for n, track in enumerate(tracks_item):
        point = Point(track['x'], track['y'])
        if n == 0:
            start_speed = track['speed']
        elif n == len(tracks_item) - 1:
            end_speed = track['speed']
        if geom_obj.contains(point):
            if n == 0:
                start_in_area = True
            elif n == len(tracks_item) - 1:
                end_in_area = True
            speed_in_area.append(track['speed'])
            track_idx_in_area.append(n)
        else:
            full_in_area = False
            speed_out_area.append(track['speed'])
        speeds.append(track['speed'])

    speed_trend = speed_trend_regression(speeds)
    avg_speed = sum(speeds) / len(speeds)
    min_speed = min(speeds)
    if avg_speed > 8 or min_speed > 5:  # 平均速度大于8m/s # 最小速度大于5
        print('filter_by_base_speed', source_id, cdid, geom, line_obj.wkt, avg_speed, start_speed, end_speed)
        return
    angle_list = []
    parallel_angle = 0
    if not full_in_area:
        # 角度不过滤了
        # angle_list = calculate_angle(line_obj, geom_obj)
        # match_angle = False
        # for angle in angle_list:
        #     if 15 <= angle <= 165:
        #         # print('filter_by_angle', source_id, cdid, geom, line_obj.wkt, angle)
        #         match_angle = True
        #         break
        # if not match_angle:
        #     print('filter_by_angle', source_id, cdid, geom, line_obj.wkt, angle_list)
        #     return
        distances = distance_points_in_area(tracks_item, track_idx_in_area, geom_obj)
        data_distances = np.array(distances)
        mean = np.mean(data_distances)
        std = np.std(data_distances)
        # 移除标准差大于1m的轨迹噪点
        filtered = data_distances[abs(data_distances - mean) <= 1.0 * std]
        avg_distance = np.mean(filtered)
        if avg_distance < 2:  # 平均值小于2m
            # print('tobe_check_distance', source_id, cdid, geom, line_obj.wkt, std_distance)
            print('filter_by_distance', source_id, cdid, geom, line_obj.wkt, distances, avg_distance)
            return
        # parallel_angle = check_parallel(line_obj, geom_obj)
        # print('parallel_angle', parallel_angle)
        # if cdid == '':

    else:
        parallel_angle = check_parallel(line_obj, geom_obj)
        if parallel_angle < 15 or parallel_angle > 165:
            print('filter_by_parallel', source_id, cdid, geom, line_obj.wkt, parallel_angle)
            return
    bearings = [
        item['north']
        for item in tracks_item
    ]
    # 0 直线  1 折线  2 曲线(暂时不要)  3 未知
    line_type, max_distance, std_distance, max_diff, std_diff = calc_line_type(line_obj, bearings)
    track_type = ""
    if full_in_area:  # 所有的点都在面内
        track_type = 'full_in_area'
        print('full in area', cdid, line_obj.wkt)
        if line_type == 0:  # 直线不要
            print('filter_by_line_type', source_id, cdid, geom, line_obj.wkt, line_type, max_distance,
                  std_distance, max_diff,
                  std_diff)
            return
    elif start_in_area or end_in_area:  # 起点或终点在面内
        track_type = 'partial_in_area'
        print('partial in area', cdid, line_obj.wkt)
        if line_type != 0 and (start_in_area and speed_trend < -0.1) or (end_in_area and speed_trend > 0.1):
            # 不是直线，加速且起点在不在区域内 或 减速且终点在面里
            print('filter_by_speed_trend', source_id, cdid, geom, line_obj.wkt, speed_trend, start_in_area,
                  end_in_area)
            return
        if (start_in_area and start_speed > 6) or (end_in_area and end_speed > 6):
            # todo 取平均值
            print('filter_by_speed_trend_2', source_id, cdid, geom, line_obj.wkt, speed_trend, start_in_area,
                  end_in_area)
            return
        min_speed_in_area = min(speed_in_area)
        if min_speed_in_area > 4:  # 面内的最小速度大于4
            print('filter_by_speed_in_area', source_id, cdid, geom, line_obj.wkt, min_speed_in_area)
            return
    elif not start_in_area and not end_in_area:  # 起点和终点均不在停车场内， 可能是穿过了停车场
        track_type = 'pass_through'
        print('pass through', cdid, line_obj.wkt)
        # 并没有轨迹点 直接过滤
        if len(track_idx_in_area) == 0:
            print('filter_by_empty_track', source_id, cdid, geom, line_obj.wkt, track_idx_in_area)
            return
        if line_type == 0:  # 直线不要
            print('filter_by_line_type', source_id, cdid, geom, line_obj.wkt, line_type, max_distance,
                  std_distance, max_diff,
                  std_diff)
            return
        if line_type != 2:  # 折线则继续判断
            std_speed_in_area = np.std(speed_in_area)
            if std_speed_in_area > 4:  # 面内速度标准差大于4
                print('filter_by_speed_in_area', source_id, cdid, geom, line_obj.wkt, std_speed_in_area)
                return
            split_ratio = split_area(line_obj, geom_obj)
            if split_ratio > 10 or split_ratio < 0.1:  # 分割比例大于1.5
                print('filter_by_split_ratio', source_id, cdid, geom, line_obj.wkt, split_ratio)
                return
        # print('tobe_check_ratio', source_id, cdid, geom, line_obj.wkt, split_ratio)
        # # 如果是穿过了停车场，要求进入趋势和出去趋势不同
        # median_track_idx = int(statistics.median(track_idx_in_area))
        # speed_list1 = [
        #     item['speed']
        #     for item in tracks_item[:median_track_idx]
        # ]
        # speed_list2 = [
        #     item['speed']
        #     for item in tracks_item[median_track_idx:]
        # ]
        # trend1 = speed_trend_regression(speed_list1)
        # trend2 = speed_trend_regression(speed_list2)
        # if trend1 * trend2 > 0:  # 进入趋势和出去趋势相同
        #     print('filter_by_enter_exit_trend', source_id, cdid, geom, line_obj.wkt, trend1, trend2, 'ratio',
        #           split_ratio)
        #     continue

    else:  # 剩余情况， 起点和终点都在面内，但是部分点不在面内, 可能是漂移或者面不准
        track_type = 'other'
        print('other_case', cdid, line_obj.wkt)
        if line_type == 0:  # 直线不要
            print('filter_by_line_type', source_id, cdid, geom, line_obj.wkt, line_type, max_distance,
                  std_distance, max_diff, std_diff)
            return
    bos_keys = [
        item['bos_key']
        for item in tracks_item
    ]
    print('matched', source_id, cdid, geom, line_obj.wkt,
          track_type, line_type, max_distance, std_distance, max_diff, std_diff,
          parallel_angle,
          speed_trend, ' - '.join(map(str, speeds)),
          angle_list,
          ', '.join(bos_keys), sep='\t')

    res = {
        'source_id': str(source_id),
        'cdid': cdid,
        'geom': geom_obj,
        'line': line_obj,
        'track_type': track_type,
        'line_type': line_type,
        'max_distance': float(max_distance),
        'std_distance': float(std_distance),
        'max_diff': float(max_diff),
        'std_diff': float(std_diff),
        'parallel_angle': float(parallel_angle),
        'speed_trend': speed_trend,
        'speeds': speeds,
        'angles': angle_list,
        'bos_keys': bos_keys,
        'track_list': tracks_item,
    }
    res_data = from_dict(data_class=TrackInfo, data=res)
    return res_data


def insert_history(match_result: list[TrackInfo]):
    """
    插入历史表
    """
    if len(match_result) == 0:
        return
    values = []
    for item in match_result:
        cdid = ""
        track_data = []
        for track_item in item.track_list:
            track_data.append({
                'bos_key': track_item.bos_key,
                'cdid': track_item.cdid,
                'x': track_item.x,
                'y': track_item.y,
                'speed': track_item.speed,
                'north': track_item.north,
                'time': track_item.unix_timestamp,
            })
        values.append((
            item.source_id,
            json.dumps(track_data),
            cdid,
            'done',
            'history'
        ))
    print('insert_history', values)
    if not DEBUG:
        with PGModel() as model:
            cursor = model.get_cursor('online_rw')
            cursor.executemany('''
                insert into parking_collect_data_history ( source_id, data, track_uuid, status, batch )
                values (%s, %s, %s, %s, %s)
                ''', values)


def process(source_id, geom, baseline):
    """
    运行
    """
    print('process', source_id)
    # buffered_geom_obj = wkt.loads(buffered_geom)
    if baseline != "":
        geom = adjust_ld(geom, baseline)
    geom_obj = wkt.loads(geom)
    buffered_geom_obj = geom_obj.buffer(80 * METER)
    tracks_all = get_tracks_all(buffered_geom_obj, 24)
    track_map = {}  # 通过车天聚合
    # 按照时间排序
    cdid_list = []
    if TEST_RECALL:
        cdid_list = recall_case[source_id]
    sorted_tracks = sorted(tracks_all, key=lambda x: int(x['bos_key'].split('_')[-1]))
    for track in sorted_tracks:
        if track['cdid'] == '':
            continue
        if TEST_RECALL:
            if track['cdid'] not in cdid_list:
                continue
        track['unix_timestamp'] = int(track['bos_key'].split("_")[-1])
        if track['cdid'] not in track_map:
            track_map[track['cdid']] = []
        track_map[track['cdid']].append(track)
    match_tracks = []
    track_map2 = {}
    for cdid, tracks_item in track_map.items():
        # 时间或者距离差异过大，拆分成两端
        prev_time = 0
        prev_track = None
        tracks = []
        if cdid not in track_map2:
            track_map2[cdid] = []
        for track in tracks_item:
            current_track = Point(track['x'], track['y'])
            if ((prev_time > 0 and track['unix_timestamp'] - prev_time > 15 * 60 * 1000) or  # 15分钟
                    (prev_track is not None and current_track.distance(prev_track) > 30 * METER)  # 40米
            ):
                track_map2[cdid].append(tracks)
                tracks = []
            tracks.append(track)
            prev_time = track['unix_timestamp']
            prev_track = current_track
        if len(tracks) > 0:
            track_map2[cdid].append(tracks)

    for cdid, tracks_list in track_map2.items():
        for tracks_item in tracks_list:
            if len(tracks_item) < 2:
                print('filter_by_track_num', source_id, cdid, geom, len(tracks_item))
                continue
            point_list = [
                (item['x'], item['y'])
                for item in tracks_item
            ]
            line_obj = LineString(point_list)
            if not line_obj.intersects(geom_obj):
                print('filter_not_intersects', source_id, cdid, geom, line_obj.wkt, line_obj.wkt)
                continue
            res_track = process_track(geom, source_id, cdid, tracks_item)
            if res_track is None:
                continue
            match_tracks.append(res_track)
    insert_history(match_tracks)


recall_case = {
    '98369': ['20250320105201vof', '20250308210201vof', '20250307158165ra', '20250220104801vof', '20250207093804vof',
              '20250218933219ra', '20250218933236ra', '20240515153501vof'],
    '98436': ['20250328437100ra', '20250315091801vof', '20250320323978ra', '20250211842006ra', '20250211842006ra',
              '20250216904792ra', '20250122578093ra', '20250216904785ra', '20241222192097ra', '20250216904776ra'],
    '98774': ['20240106070701vof', '20240113082101vof', '20250225103007vof', '20250212114401vof', '20250305104801vof',
              '20250307120201vof'],
    '99328': ['20250309125001vof', '20250224015728ra', '20250216904909ra', '20240505115601vof'],
    '99584': ['20250307124601vof', '20250218081403vof', '20250122578102ra', '20241004447013ra', '20231224093501vof',
              '20240108155601vof', '20240326202101vof', '20240106134901vof'],
    '99845': ['20250302093485ra', '20241210083009vof'],
    '100172': ['20250313238622ra', '20250306145495ra', '20250218105001vof', '20250220960856ra', '20250127648332ra',
               '20250226041319ra', '20240930417341ra', '20231110063501vof', '20240208075601vof'],
    '100266': ['20240724926994ra', '20250224015574ra'],
    '100302': ['20250218933331ra'],
    '101159': ['20250330452532ra', '20250323095001vof', '20250218933489ra', '20250115101812vof', '20240106105601vof'],
    '113239': ['20240904227491ra', '20241004447013ra', '20250124604790ra', '20250115484001ra', '20250115484001ra',
               '20250228067578ra', '20250211842045ra', '20250314250519ra', '20250304145401vof', '20250329439300ra'],
    '137979': ['20250307158230ra', 'P_yika6017870_1741938173075', '20250304119652ra', '20250218070606vof',
               '20250301080660ra', '20241221181364ra', '20240910195802vof', '20231213531847ra'],
    '138141': ['20240401222735ra', '20250220960514ra'],
    '138376': ['20250329450427ra', '20250331465419ra', '20250309185375ra', '20250309185375ra', '20250308171384ra',
               '20250302195201vof', '20250107382248ra', '20250121564677ra', '20250123591887ra', '20241115780964ra'],
    '138491': ['20231217204901vof'],
    '138570': ['20250326181601vof', '20250311201801vof', '20250221102415vof', '20250226041365ra', '20250221102415vof',
               '20250223001817ra', '20250223001823ra', '20240829186370ra'],
    '139605': ['20250306152201vof'],
    '140913': ['20240208124201vof', '20250221133403vof', '20250222988056ra', '20250221135002vof', '20250221133403vof',
               '20250221115203vof', '20250302093580ra', '20250128662395ra', '20241027602180ra', '20240208124201vof'],
    '140926': ['20250402490520ra', '20250310198875ra', '20250312101601vof', '20250308113601vof', '20250220960569ra',
               '20250212851821ra', '20250127648336ra', '20250123591870ra', '20250205657419ra', '20241011493344ra'],
    '141486': ['20250221974656ra', '20250114150810vof', '20250221974633ra', '20250304119656ra'],
    '142115': ['202520250330452532ra', '20250221974733ra', '20250103330364ra', '20240215145601vof'],
    '142150': ['20250211082001vof', '20240227092101vof'],
    '142707': ['20250323363483ra', '20250313141601vof', '20250316085401vof', '20250220205801vof', '20250303106898ra',
               '20250216905258ra', '20250130690157ra', '20250125130810vof', '20250120551137ra', '20241220170122ra'],
    '143666': ['20250328174201vof', '20250308210201vof', '20250221134802vof', '20250227054406ra', '20250221111404vof',
               '20241220170120ra', '20240518514252ra', '20240208170001vof', '20240325122801vof', '20240106094901vof'],
    '145168': ['20250316274662ra', '20250220960499ra', '20250220960479ra'],
    '145193': ['20250325398943ra', '20250312162001vof', '20250302093226ra', '20250216904975ra', '20250216904975ra',
               '20240901209007ra', '20240517507958ra', '20240317128398ra'],
    '146153': ['20250305172401vof'],
    '146462': ['20250305153001vof', '20250311125601vof', '20250314163401vof', '20250228175601vof', '20250120551146ra',
               '20240221112801vof', '20240117102801vof', '20231213205602vof'],
    '163418': ['20241019547961ra', '20240713861892ra'],
    '163583': ['20250303175401vof', '20250303106717ra', '20250210831512ra', '20241213092606vof', '20240920345494ra'],
}


def run_test_recall():
    """
    测试召回
    """
    source_ids = recall_case.keys()
    run_juejin_verified(source_ids)


def run_turing_verified(pid=None):
    """
    处理图灵成果面
    """
    with PGModel() as model:
        if pid is None:
            sql = """
            select bid,
            st_astext(geom) as parking_geom
            from park_storefront_prod_parking 
            where 
            bid_status = 'effected'
            and city = '江门'
            order by random()
            limit 100;
            """
            cursor = model.get_cursor('online')
            cursor.execute(sql)
            res = cursor.fetchall()
        else:
            sql = """
            select bid,
            st_astext(geom) as parking_geom
            from park_storefront_prod_parking 
            where 
            id = %s
            """
            cursor = model.get_cursor('online')
            cursor.execute(sql, (pid,))

            res = cursor.fetchall()

        for row in res:
            id = row['bid']
            parking_geom = row['parking_geom']
            print('start', id)
            parking_geom_obj = wkt.loads(parking_geom)
            parking_geom_obj = parking_geom_obj.buffer(5 * METER, cap_style=CAP_STYLE.flat,
                                                       join_style=JOIN_STYLE.round)  # 出入口作业面很小
            if isinstance(parking_geom_obj, Polygon):
                process(id, parking_geom, "")
            elif isinstance(parking_geom_obj, MultiPolygon):
                for poly in parking_geom_obj.geoms:
                    process(id, poly.wkt, "")
            else:
                raise Exception('unknown type {}'.format(type(parking_geom_obj)))


def run_juejin_verified(source_ids=[]):
    """
    处理掘金成果面
    """
    with PGModel() as model:
        # sql = """
        # select
        #     source_id,
        #     st_astext(parking_geom) as parking_geom,
        #     st_astext(st_buffer(parking_geom::geography, 80)) as buffered_geom
        # from parking_collect_data
        # where
        #     source = 'parkstore'
        #     and tag != 'parkstore_online'
        # order by random()
        # limit 200
        # """
        sql_city = """
        select
            a.id,
            st_astext(a.baseline) as baseline_wkt,
            st_astext(a.geom) as parking_geom
--            st_astext(st_buffer(parking_geom::geography, 80)) as buffered_geom
        from park_storefront_strategy a 
        left join park_storefront_task b on a.task_id = b.task_id
        where
            a.step = 'VERIFIED'
            and b.city = '保定市'
        order by random()
        limit 200
        """
        sql = """
        select
            id,
            st_astext(baseline) as baseline_wkt,
            st_astext(geom) as parking_geom
--            st_astext(st_buffer(parking_geom::geography, 80)) as buffered_geom
        from park_storefront_strategy
        where
            step = 'VERIFIED'
        order by random()
        limit 10
        """
        sql_src = """
        select
            source_id,
            st_astext(parking_geom) as parking_geom,
            st_astext(st_buffer(parking_geom::geography, 80)) as buffered_geom
        from parking_collect_data
        where
            source = 'parkstore'
            and source_id in %s
        and tag != 'parkstore_online'
        """
        sql2 = """
        select
            st_astext(baseline) as baseline_wkt
        from park_storefront_strategy
        where id = %s
        """
        cursor = model.get_cursor('online')
        cursor.execute(sql_city)
        res = cursor.fetchall()
        for row in res:
            print('start', row['id'])
            # if not row['source_id'].isdigit():
            #     print('filter_not_int', row['source_id'])
            #     continue
            # cursor.execute(sql2, (row['source_id'],))
            # source_row = cursor.fetchone()
            baseline_wkt = row['baseline_wkt']
            # polygon_obj = wkt.loads(row['parking_geom'])
            parking_geom = row['parking_geom']
            # line_obj = wkt.loads(baseline_wkt)
            # repair = polygon_strategy.repair_by_ld_road(polygon_obj, line_obj)
            # parking_geom = row['parking_geom']
            # if repair is not None:
            #     if repair.area / polygon_obj.area > 0.7:
            #         print('use repair', repair.wkt)
            #         parking_geom = repair.wkt
            #     else:
            #         print('repair err', repair.wkt)
            parking_geom_obj = wkt.loads(parking_geom)
            if isinstance(parking_geom_obj, Polygon):
                process(row['id'], parking_geom, baseline_wkt)
            elif isinstance(parking_geom_obj, MultiPolygon):
                for poly in parking_geom_obj.geoms:
                    process(row['id'], poly.wkt, baseline_wkt)
            else:
                raise Exception('unknown type {}'.format(type(parking_geom_obj)))


def run_by_source_id(source_id):
    """
    通过source_id执行
    """
    with PGModel() as model:
        sql = """
        select
            id,
            st_astext(baseline) as baseline_wkt,
            st_astext(geom) as parking_geom
        from park_storefront_strategy
        where
            id = %s
        """
        cursor = model.get_cursor('online')
        cursor.execute(sql, (int(source_id),))
        res = cursor.fetchone()
        baseline_wkt = res['baseline_wkt']
        parking_geom = res['parking_geom']
        parking_geom_obj = wkt.loads(parking_geom)
        if isinstance(parking_geom_obj, Polygon):
            process(res['id'], parking_geom_obj.wkt, baseline_wkt)
        elif isinstance(parking_geom_obj, MultiPolygon):
            for poly in parking_geom_obj.geoms:
                process(res['id'], poly.wkt, baseline_wkt)
        else:
            raise Exception('unknown type {}'.format(type(parking_geom_obj)))


# TEST_RECALL = True
TEST_RECALL = False
DEBUG = False

if __name__ == "__main__":
    # run_test_recall()
    # run_juejin_verify(sys.argv[1].split(','))
    # if len(sys.argv) > 1:
    #    run(sys.argv[1].split(','))
    # else:
    # run_juejin_verified()
    run_turing_verified(sys.argv[1])
    # run_by_source_id(sys.argv[1])
