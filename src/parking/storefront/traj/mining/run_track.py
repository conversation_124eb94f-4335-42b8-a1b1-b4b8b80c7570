#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
执行路侧轨迹
"""

from src.parking_roadside.db.model import PGModel
from shapely import wkt, LineString, Point, Polygon, MultiPolygon
from src.parking.storefront.traj.mining.fetch_his_track import process
from shapely.geometry.base import CAP_STYLE, JOIN_STYLE

METER = 0.00001  # 近似1米


def main():
    """
    main函数
    """
    with PGModel() as model:
        sql = """
        select id, bid,
        st_astext(geom) as parking_geom
        from park_storefront_prod_parking 
        where 
        bid_status = 'effected'
        and status in (
        'PARK_ACCESS_INTELLIGENCE',
        'INITED_BID',
        'READY'
        )
        and park_type = 'close'
        and not zhongyuan_complete
        limit 100
        """
        cursor = model.get_cursor('online_rw')
        cursor.execute(sql)
        res = cursor.fetchall()

        for row in res:
            pid = row['id']
            bid = row['bid']
            parking_geom = row['parking_geom']
            print('start', bid)
            parking_geom_obj = wkt.loads(parking_geom)
            parking_geom_obj = parking_geom_obj.buffer(5 * METER, cap_style=CAP_STYLE.flat,
                                                       join_style=JOIN_STYLE.round)  # 出入口作业面很小
            if isinstance(parking_geom_obj, Polygon):
                process(bid, parking_geom, "")
            elif isinstance(parking_geom_obj, MultiPolygon):
                for poly in parking_geom_obj.geoms:
                    process(bid, poly.wkt, "")
            else:
                raise Exception('unknown type {}'.format(type(parking_geom_obj)))

            cursor.execute("""
                update park_storefront_prod_parking 
                set zhongyuan_complete = true where id = %s""", (pid,))


if __name__ == '__main__':
    main()
