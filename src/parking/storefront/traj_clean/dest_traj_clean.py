"""
终点轨迹清洗
目的：去除低质漂移、道路上轨迹，保留进出门前的轨迹。辅助作业提效
"""
import os
import json
import tqdm
from shapely.wkt import loads, dumps
from shapely.ops import unary_union
from sklearn.cluster import DBSCAN
from scipy.spatial.distance import directed_hausdorff
from shapely.geometry import LineString, Point, MultiLineString
import numpy as np

CLIP_BUFFER_DISTANCE = 10 / 110000  # 轨迹裁剪范围
AREA_BUFFER = 50 / 110000  # 门前面buffer
DRAFT_DIST_LIMIT = 30 / 110000  # 两点允许的漂移距离限制


def run():
    """
    run
    """
    result = clean_trajectories()
    save_result(result)


def clean_trajectories():
    """
    清洗所有轨迹
    """
    folder_path = "./shuf_135_json_info_20250214"
    result = []
    for filename in tqdm.tqdm(os.listdir(folder_path)):
        if filename.endswith(".json"):
            file_path = os.path.join(folder_path, filename)
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                result += clean_traj(data)
    return result


def clean_traj(item):
    """
    清洗轨迹
    """
    result = []
    bid, poly, useless_traj_lines, used_traj_lines = (
        item["bid"],
        item["geom"],
        item["useless_traj_lines"],
        item["used_traj_lines"],
    )
    lines = useless_traj_lines + used_traj_lines
    poly_sp = loads(poly).buffer(50 / 110000)
    valid_lines = []
    for line_wkt in lines:
        line_sp = loads(line_wkt)
        if not line_sp.intersects(poly_sp):
            # 轨迹与门前面无交集，过滤
            continue

        if not poly_sp.contains(Point(line_sp.coords[-1])):
            # 轨迹终点不在门前面范围内，过滤
            continue

        # 裁剪轨迹
        clipped_line = clip_line_to_buffer(line_sp, poly_sp)
        if clipped_line is None:
            continue

        # 过滤低质轨迹
        filtered_line = filter_drift_traj(clipped_line)
        if filtered_line:
            valid_lines.append(filtered_line)

    if valid_lines:
        # clusters = cluster_trajectories(valid_lines)
        # representative_trajectories = [get_representative_trajectory(cluster) for cluster in clusters.values()]
        # multi_line = unary_union(representative_trajectories)
        multi_line = unary_union(valid_lines)
        result.append({"bid": bid, "filtered_traj": dumps(multi_line), "poly": poly})
    return result


def clip_line_to_buffer(line, polygon):
    """
    裁剪轨迹
    只保留轨迹在门前面边界buffer范围内的部分
    """
    polygon_buffer = polygon.buffer(CLIP_BUFFER_DISTANCE)
    clipped = line.intersection(polygon_buffer)

    if clipped.is_empty:
        return None
    return clipped


def filter_drift_traj(line):
    """
    过滤低质轨迹
    """
    if line.geom_type == "MultiLineString":
        valid_lines = []
        for part in line.geoms:
            coords = list(part.coords)
            if len(coords) < 2:
                continue

            for i in range(len(coords) - 1):
                point1 = Point(coords[i])
                point2 = Point(coords[i + 1])
                if point1.distance(point2) > DRAFT_DIST_LIMIT:
                    break
            else:
                valid_lines.append(part)
        if len(valid_lines) > 0:
            return MultiLineString(valid_lines)
        return None
    else:
        coords = list(line.coords)
        if len(coords) < 2:
            return line

        for i in range(len(coords) - 1):
            point1 = Point(coords[i])
            point2 = Point(coords[i + 1])
            if point1.distance(point2) > DRAFT_DIST_LIMIT:
                return None  # 轨迹不合格，丢弃
        return line  # 轨迹合格，保留


def cluster_trajectories(valid_lines, eps=0.00009090909091, min_samples=1):
    """
    轨迹聚类
    - valid_lines: 预处理后的轨迹
    - eps: Hausdorff 距离阈值（轨迹相似度）
    - min_samples: 最小聚类数量
    """
    num_trajs = len(valid_lines)
    distance_matrix = np.zeros((num_trajs, num_trajs))

    # 计算轨迹之间的 Hausdorff 距离矩阵
    for i in range(num_trajs):
        for j in range(i + 1, num_trajs):
            dist = hausdorff_distance(valid_lines[i], valid_lines[j])
            distance_matrix[i, j] = dist
            distance_matrix[j, i] = dist

    # DBSCAN 聚类
    dbscan = DBSCAN(eps=eps, min_samples=min_samples, metric="precomputed")
    labels = dbscan.fit_predict(distance_matrix)

    # 按聚类编号分组轨迹
    clusters = {}
    for idx, label in enumerate(labels):
        if label == -1:
            continue  # 跳过噪声点
        if label not in clusters:
            clusters[label] = []
        clusters[label].append(valid_lines[idx])

    return clusters


def get_representative_trajectory(cluster):
    """
    选出每个聚类中最具代表性的轨迹（平均 Hausdorff 距离最小）
    """
    num_trajs = len(cluster)
    min_avg_dist = float("inf")
    best_traj = None

    for traj in cluster:
        total_dist = sum(hausdorff_distance(traj, other) for other in cluster)
        avg_dist = total_dist / num_trajs

        if avg_dist < min_avg_dist:
            min_avg_dist = avg_dist
            best_traj = traj

    return best_traj


def hausdorff_distance(traj1, traj2):
    """
    计算两个轨迹（LineString 或 MultiLineString）之间的 Hausdorff 距离
    """
    if traj1.geom_type == "MultiLineString":
        coords1 = np.vstack([np.array(line.coords) for line in traj1.geoms])
    else:
        coords1 = np.array(traj1.coords)

    if traj2.geom_type == "MultiLineString":
        coords2 = np.vstack([np.array(line.coords) for line in traj2.geoms])
    else:
        coords2 = np.array(traj2.coords)

    # 计算 Hausdorff 距离
    return directed_hausdorff(coords1, coords2)[0]


def save_result(result, filename="filtered_trajectories.json"):
    """
    保存清洗后的轨迹数据到 JSON 文件
    """
    output_path = os.path.join("./", filename)

    # 以 JSON 格式写入文件，确保中文不乱码
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=4)

    print(f"清洗后的轨迹已保存至 {output_path}")


if __name__ == "__main__":
    """
    main
    """
    run()
