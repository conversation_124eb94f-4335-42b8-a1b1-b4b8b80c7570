"""
熄火点聚类
"""
from datetime import timedelta

from src.tools import turnoff_point


def get_zhongyuan_points(range_wkt: str):
    """
    获取众源熄火点
    """

    def filter_points(point: turnoff_point.OtherTurnoffPoint):
        if point.parking_type == "start_car":
            return point.duration > timedelta(minutes=2)
        elif point.parking_type == "mid_stop_car":
            return point.duration > timedelta(minutes=20)
        elif point.parking_type == "end_car":
            return point.duration > timedelta(minutes=5)
        else:
            raise ValueError(f"Unknown parking_type: {point.parking_type}")

    points = turnoff_point.get_points_of_other(range_wkt)
    # points = [x for x in points if filter_points(x)]
    return points


def get_doctor_points(range_wkt: str):
    """
    获取博士熄火点
    """
    points = turnoff_point.get_points_of_doctor(range_wkt)
    points = [x for x in points if x.duration.total_seconds() > 2 * 60]
    return points
