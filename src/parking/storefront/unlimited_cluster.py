"""
无限制聚类：不借助街区进行 POI 聚类
"""
import json
import math
from collections import defaultdict
from dataclasses import dataclass, field
from pathlib import Path

import cv2
import numpy as np
import shapely
from loguru import logger
from shapely import LineString, wkt, Point
from tqdm import tqdm

from src.aikit import satellite_imagery, boundary, preview_image
from src.parking.recognition import dbutils
from src.parking.storefront.utils import geometric
from src.tools import tsv, pgsql, utils, linq

METER = 1e-5


@dataclass(frozen=True)
class Link:
    """
    nav_link 对象
    """

    link_id: str
    s_nid: str
    e_nid: str
    kind: int
    form: str
    direction: int
    geom: LineString

    def to_dict(self):
        """
        序列化为字典
        """
        return {
            "link_id": self.link_id,
            "s_nid": self.s_nid,
            "e_nid": self.e_nid,
            "kind": self.kind,
            "form": self.form,
            "direction": self.direction,
            "geom": self.geom.wkt,
        }

    @staticmethod
    def from_dict(d: dict):
        """
        从字典反序列化
        """
        return Link(
            link_id=d["link_id"],
            s_nid=d["s_nid"],
            e_nid=d["e_nid"],
            kind=int(d["kind"]),
            form=d["form"],
            direction=int(d["direction"]),
            geom=wkt.loads(d["geom"]),
        )


@dataclass
class Joint:
    """
    路链中的交叉口
    """

    joint_type: str  # "end" | "cross" | "fork"
    entry_nid: str  # 进入此分叉区域的点
    edges: set[tuple[str, str, str]]  # 仅 "cross"，[(link_id, s_nid, e_nid)]
    joint_id: str = field(default="")

    def __post_init__(self):
        self.joint_id = utils.md5("-".join(sorted(x[0] for x in self.edges))) if self.edges else self.entry_nid

    def to_dict(self):
        """
        序列化为字典
        """
        return {
            "joint_id": self.joint_id,
            "joint_type": self.joint_type,
            "entry_nid": self.entry_nid,
            "edges": list(self.edges),
        }

    @staticmethod
    def from_dict(d: dict):
        """
        从字典反序列化
        """
        return Joint(
            joint_id=d["joint_id"],
            joint_type=d["joint_type"],
            entry_nid=d["entry_nid"],
            edges=set((link_id, s_nid, e_nid) for link_id, s_nid, e_nid in d["edges"]),
        )


@dataclass
class LinkChain:
    """
    路链，由一系列 link 组成
    """

    links: list[Link]  # 路链中的所有 link，按顺序排列
    start_joint: Joint  # 交叉口
    end_joint: Joint  # 另一端的交叉口
    line: LineString  # 路链的线段
    chain_id: str = field(default="")  # 路链 ID，link_id 排序后取 md5

    def __post_init__(self):
        self.chain_id = utils.md5("-".join(sorted(x.link_id for x in self.links)))

    def union(self, other: "LinkChain") -> "LinkChain":
        """
        合并两个路链
        """
        if self.start_joint.joint_id == other.start_joint.joint_id:
            links = [*reversed(self.links), *other.links]
            s_j, e_j = self.end_joint, self.end_joint
        elif self.start_joint.joint_id == other.end_joint.joint_id:
            links = [*reversed(self.links), *reversed(other.links)]
            s_j, e_j = self.end_joint, self.start_joint
        elif self.end_joint.joint_id == other.start_joint.joint_id:
            links = [*self.links, *other.links]
            s_j, e_j = self.start_joint, self.end_joint
        elif self.end_joint.joint_id == other.end_joint.joint_id:
            links = [*self.links, *reversed(other.links)]
            s_j, e_j = self.start_joint, self.start_joint
        else:
            raise ValueError(f"cannot union two chains: {self.to_dict()} - {other.to_dict()}")

        link_geoms = [link.geom for link in links]
        return LinkChain(links=links, start_joint=s_j, end_joint=e_j, line=union_continuous_links(link_geoms))

    def to_dict(self):
        """
        序列化为字典
        """
        return {
            "chain_id": self.chain_id,
            "links": [link.to_dict() for link in self.links],
            "start_joint": self.start_joint.to_dict(),
            "end_joint": self.end_joint.to_dict(),
            "line": self.line.wkt,
        }

    @staticmethod
    def from_dict(d: dict):
        """
        从字典反序列化
        """
        return LinkChain(
            chain_id=d["chain_id"],
            links=[Link.from_dict(link) for link in d["links"]],
            start_joint=Joint.from_dict(d["start_joint"]),
            end_joint=Joint.from_dict(d["end_joint"]),
            line=wkt.loads(d["line"]),
        )


def filter_link(point: Point, links: list[Link]):
    """
    过滤出一条链中与 point 最近的 link，并确保 point 与最邻近点的连线与垂足处 link 是垂直的，否则返回 None
    """
    foot_points = [(geometric.get_foot_point(point, link.geom), link) for link in links]
    valid_links = [(point.distance(p), l) for p, l in foot_points if p is not None]
    if len(valid_links) == 0:
        return None

    valid_links.sort(key=lambda x: x[1].kind)
    _, link = min(valid_links, key=lambda x: x[0])
    return link


def try_get_adjoin_nid(nid: str) -> str:
    """
    尝试获取跨图幅的邻接节点
    """
    sql = """
        select adjoin_nid from nav_node
        where node_id = %s
    """
    ret = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [nid])
    adjoin_nid = ret[0] if ret else None
    return adjoin_nid if adjoin_nid else None


def search_link(link_id: str, nid: str, seen_link_ids: set[str]):
    """
    搜索路链
    """

    def is_cross_link(link: Link):
        return "50" in link.form

    current_link_id = link_id
    current_nid = nid
    while True:
        seen_link_ids.add(current_link_id)
        next_links = get_links_by_nid(current_nid)
        next_links = [x for x in next_links if x.link_id != current_link_id]  # 不回头
        is_ring = any(x.link_id in seen_link_ids for x in next_links)
        if is_ring:
            yield "ring", current_nid
            break

        if len(next_links) == 0:
            adjoin_nid = try_get_adjoin_nid(current_nid)
            # 可能是跨图幅断开的
            if adjoin_nid:
                current_nid = adjoin_nid
                continue
            else:
                yield "end", current_nid
                break

        non_cross_links = [x for x in next_links if not is_cross_link(x)]
        num_total = len(next_links)
        num_non_cross = len(non_cross_links)
        num_cross = num_total - num_non_cross
        # 1. 有且仅有 1 条非叉路，则继续
        if num_non_cross == 1 and num_cross == 0:
            next_link = non_cross_links[0]
            current_link_id = next_link.link_id
            current_nid = next_link.s_nid if next_link.s_nid != current_nid else next_link.e_nid
            yield next_link
        # 2. 否则，只要存在非叉路，都是为 fork 类型
        elif num_non_cross > 0:
            yield "fork", current_nid
            break
        # 3. 不满足以上条件，说明：num_non_cross == 0 && num_cross > 0
        else:
            yield "cross", current_nid
            break


def search_cross_links(nid: str, edges: set[tuple], seen_nids: set[str], seen_link_ids: set[str]):
    """
    搜索交叉口
    """
    seen_nids.add(nid)
    candidate_links = get_links_by_nid(nid)
    cross_links = [x for x in candidate_links if "50" in x.form and x.link_id not in seen_link_ids]
    for cross_link in cross_links:
        seen_link_ids.add(cross_link.link_id)
        edges.add((cross_link.link_id, cross_link.s_nid, cross_link.e_nid))
        candidate_nids = {cross_link.s_nid, cross_link.e_nid}
        candidate_nids -= seen_nids
        for nid in candidate_nids:
            search_cross_links(nid, edges, seen_nids, seen_link_ids)


def union_continuous_links(lines: list[LineString]) -> LineString:
    """
    合并连续的 link，返回一条 LineString
    """

    def is_same_point(p1: Point, p2: Point, threshold=1e-8):
        return abs(p1.x - p2.x) < threshold and abs(p1.y - p2.y) < threshold

    def union_link(link1: LineString, link2: LineString):
        points1 = [Point(x, y) for x, y in link1.coords]
        p11, p12 = points1[0], points1[-1]
        points2 = [Point(x, y) for x, y in link2.coords]
        p21, p22 = points2[0], points2[-1]
        if is_same_point(p11, p21):
            points1.reverse()
            return LineString(points1 + points2[1:])
        elif is_same_point(p11, p22):
            return LineString(points2 + points1[1:])
        elif is_same_point(p12, p21):
            return LineString(points1 + points2[1:])
        elif is_same_point(p12, p22):
            points2.reverse()
            return LineString(points1 + points2[1:])
        else:
            raise ValueError(f"links is not continuous: {link1.wkt}, {link2.wkt}")

    union_line = lines[0]
    for line in lines[1:]:
        union_line = union_link(union_line, line)

    return union_line


def get_link_chain(link: Link):
    """
    获取 link 的链路
    """
    seen = set()
    chain1 = list(search_link(link.link_id, link.s_nid, seen))
    joint_type1, joint_nid1 = chain1[-1]
    links1 = chain1[:-1]
    edges = set()
    if joint_type1 == "cross":
        search_cross_links(joint_nid1, edges, set(), set())
    joint1 = Joint(joint_type=joint_type1, entry_nid=joint_nid1, edges=edges)

    chain2 = list(search_link(link.link_id, link.e_nid, seen))
    joint_type2, joint_nid2 = chain2[-1]
    links2 = chain2[:-1]
    edges = set()
    if joint_type2 == "cross":
        search_cross_links(joint_nid2, edges, set(), set())
    joint2 = Joint(joint_type=joint_type2, entry_nid=joint_nid2, edges=edges)

    links = [*links1[::-1], link, *links2]
    link_geoms = [x.geom for x in links]
    return LinkChain(links=links, start_joint=joint1, end_joint=joint2, line=union_continuous_links(link_geoms))


def get_links_by_nid(nid: str):
    """
    获取节点所在的 link
    """
    sql = """
        select link_id, s_nid, e_nid, kind, form, dir, st_astext(geom)
        from nav_link
        where 1 = 1
            and (s_nid = %(nid)s or e_nid = %(nid)s)
            and kind <= 7 and viad != 1
            and form !~ '10' and form !~ '11' and form !~ '15'
            and form !~ '16' and form !~ '17' and form !~ '20'
            and form !~ '31' and form !~ '34' and form !~ '35'
            and form !~ '39'
            and form !~ '52' and form !~ '53'
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, {"nid": nid})
    return [
        Link(
            link_id=link_id,
            s_nid=s_nid,
            e_nid=e_nid,
            kind=kind,
            form=form,
            direction=direction,
            geom=wkt.loads(geom),
        )
        for link_id, s_nid, e_nid, kind, form, direction, geom in ret
    ]


def get_links_by_point(point: str, buffer: float):
    """
    获取指定点附近的 link
    """
    sql = """
        select link_id, s_nid, e_nid, kind, form, dir, st_astext(geom)
        from nav_link
        where 1 = 1
            and st_dwithin(%s, geom, %s)
            and kind <= 7 and viad != 1
            and form !~ '10' and form !~ '11' and form !~ '15'
            and form !~ '16' and form !~ '17' and form !~ '20'
            and form !~ '31' and form !~ '34' and form !~ '35'
            and form !~ '39' and form !~ '50'
            and form !~ '52' and form !~ '53'
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{point}", buffer])
    return [
        Link(
            link_id=link_id,
            s_nid=s_nid,
            e_nid=e_nid,
            kind=kind,
            form=form,
            direction=direction,
            geom=wkt.loads(geom),
        )
        for link_id, s_nid, e_nid, kind, form, direction, geom in ret
    ]


def draw_links(file_path: Path):
    """
    绘制 debug 预览图
    """
    save_dir = utils.ensure_dir(file_path.parent / "debug_links")

    cases = linq.group_by(tsv.read_tsv(file_path), key=lambda r: f"{r[-6]}-{r[2]}")
    for group_id, values in tqdm(cases.items()):
        foots = [wkt.loads(case[3]) for case in values]
        points = [wkt.loads(case[1]) for case in values]
        lines = {case[-1] for case in values}
        assert len(lines) == 1
        line = wkt.loads(lines.pop())

        union_geom = shapely.unary_union([*foots, *points, line])
        bounds = boundary.from_wkt(union_geom.wkt, 20 * METER)
        image = satellite_imagery.crop(bounds)
        if image is None:
            continue

        point_wkts = [x.wkt for x in points]
        preview_image.draw_point(image, point_wkts, bounds, radius=4, color=preview_image.COLOR_RED)

        pts = [Point(x, y) for x, y in line.coords]
        p0, p1 = pts[0], pts[1]
        preview_image.draw_arrow(image, p1.wkt, p0.wkt, bounds, thickness=4, color=preview_image.COLOR_BLUE)
        preview_image.draw_linestring(image, line.wkt, bounds, thickness=4, color=preview_image.COLOR_BLUE)

        foot_lines = [LineString([p, q]) for p, q in zip(foots, points)]
        foot_line_wkts = [x.wkt for x in foot_lines]
        preview_image.draw_linestring(image, foot_line_wkts, bounds, thickness=1, color=preview_image.COLOR_GREEN)

        cv2.imwrite(str(save_dir / f"{group_id}.jpg"), image)


def get_rotation_angle(a: np.ndarray, b: np.ndarray, reverse=False):
    """
    计算两个向量的旋转角度
    """
    dot = np.dot(a, b)
    det = np.cross(a, b)
    angle = math.atan2(det, dot)
    if reverse:
        angle = -angle

    if angle < 0:
        angle = 2 * math.pi + angle

    return angle


def group_store_pois(file_path: Path, batch: str):
    """
    将 POI 按照链路进行分组
    """
    chain_dir = file_path.parent / f"link_chain_{batch}_chains"
    point_path = file_path.parent / f"link_chain_{batch}_points.tsv"

    done_pois = {x[3] for x in tsv.read_tsv(point_path)} if point_path.exists() else set()
    store_pois = [x[0] for x in tsv.read_tsv(file_path)]
    store_pois = [x for x in store_pois if x not in done_pois]

    # 点分组：分 link chain
    for poi_wkt in tqdm(store_pois, desc="group"):
        poi = wkt.loads(poi_wkt)
        links = list(get_links_by_point(poi.wkt, 50 * METER))
        link = filter_link(poi, links)
        if link is None:
            continue

        link_chain = get_link_chain(link)
        try:
            left = geometric.is_left(poi, link_chain.line)
        except:
            # FIXME: POINT(116.338660826 39.9820870686) 这个 POI 点作为输入会出此问题
            print(poi_wkt)
            continue

        chain_path = utils.ensure_path(chain_dir / f"{link_chain.chain_id}.json", cleanup=False)
        if not chain_path.exists():
            json_str = json.dumps(link_chain.to_dict(), indent=2)
            chain_path.write_text(json_str, encoding="utf8")

        point_info = [link_chain.chain_id, int(left), "-", poi_wkt]
        tsv.write_tsv(point_path, [point_info], mode="a")


@dataclass
class Context:
    """
    上下文
    """

    stores: dict[tuple[str, bool], list[tuple[str, str]]]  # (chain_id, left) -> [(bid, poi_wkt)]
    chains: dict[str, LinkChain]  # chain_id -> LinkChain
    joint2chains: dict[str, list[LinkChain]]  # joint_id -> [LinkChain]


def is_continuous_left(chain: LinkChain, left: bool, next_chain: LinkChain):
    """
    已知一个点在 chain 的左边，判断是否它也在 next_chain 的左边
    """
    if chain.start_joint.joint_id == next_chain.start_joint.joint_id:
        return not left
    elif chain.start_joint.joint_id == next_chain.end_joint.joint_id:
        return left
    elif chain.end_joint.joint_id == next_chain.start_joint.joint_id:
        return left
    elif chain.end_joint.joint_id == next_chain.end_joint.joint_id:
        return not left
    else:
        raise ValueError("cannot union two chains")


def next_link_chain(ctx: Context, chain: LinkChain, left: bool, reverse: bool):
    """
    获取与当前路链相连的下一个链路
    """
    line_pts = [(x, y) for x, y in chain.line.coords]
    if reverse:
        joint = chain.end_joint
        p0, p1 = line_pts[-1], line_pts[-2]
        end_link_id = chain.links[-1].link_id
    else:
        joint = chain.start_joint
        p0, p1 = line_pts[0], line_pts[1]
        end_link_id = chain.links[0].link_id

    end_vec = np.array(p1) - np.array(p0)
    if joint.joint_type == "ring":
        # 无法处理，直接跳过
        # TODO: 记录这批 POI 聚类失败的原因
        return None
    elif joint.joint_type == "end":
        # 无需处理
        return None
    elif joint.joint_type == "fork":
        # 已存在的 chain
        next_chains = ctx.joint2chains[joint.joint_id]
        next_chains = [x for x in next_chains if x.chain_id != chain.chain_id]
        if next_chains:
            next_chain = min(
                next_chains,
                key=lambda x: get_rotation_angle(get_vec_from_chain(x, joint), end_vec, reverse),
            )
            return next_chain.chain_id, is_continuous_left(chain, left, next_chain)

        # 新建的 chain
        next_links = get_links_by_nid(joint.entry_nid)
        next_links = [x for x in next_links if x.link_id != end_link_id]
        next_link = min(
            next_links,
            key=lambda x: get_rotation_angle(get_vec_from_line(x.geom, p0), end_vec, reverse),
        )
        new_chain = get_link_chain(next_link)
        ctx.chains[new_chain.chain_id] = new_chain
        return new_chain.chain_id, is_continuous_left(chain, left, new_chain)
    elif joint.joint_type == "cross":
        pass
    else:
        raise ValueError(f"unknown joint type: {joint.joint_type}")


def get_vec_from_line(line: LineString, end_point: tuple[float, float]):
    """
    获取线段的末端向量
    """

    def is_same_point(p1: tuple[float, float], p2: tuple[float, float], eps=1e-8):
        return abs(p1[0] - p2[0]) <= eps and abs(p1[1] - p2[1]) <= eps

    pts = [(x, y) for x, y in line.coords]
    if is_same_point(pts[0], end_point):
        return np.array(pts[1]) - np.array(pts[0])
    elif is_same_point(pts[-1], end_point):
        return np.array(pts[-2]) - np.array(pts[-1])
    else:
        raise ValueError(f"end point not in line: {line.wkt}")


def get_vec_from_chain(chain: LinkChain, joint: Joint):
    """
    获取链路的末端向量
    """
    pts = [(x, y) for x, y in chain.line.coords]
    if chain.start_joint.joint_id == joint.joint_id:
        return np.array(pts[1]) - np.array(pts[0])
    elif chain.end_joint.joint_id == joint.joint_id:
        return np.array(pts[-2]) - np.array(pts[-1])
    else:
        raise ValueError("joint not in chain")


def build_context(work_dir: Path, batch: str):
    """
    构建上下文
    """
    chain_dir = work_dir / f"link_chain_{batch}_chains"
    point_path = work_dir / f"link_chain_{batch}_points.tsv"

    stores = defaultdict(list)
    for x in tsv.read_tsv(point_path):
        stores[(x[0], bool(int(x[1])))].append((x[2], x[3]))

    chains = {}
    for chain_path in chain_dir.glob("*.json"):
        obj = utils.read_json(chain_path)
        chain = LinkChain.from_dict(obj)
        chains[chain.chain_id] = chain

    joint_chains = [(j.joint_id, chain) for chain in chains.values() for j in (chain.start_joint, chain.end_joint)]
    joint2chains: dict[str, list[LinkChain]] = linq.group_by(joint_chains, key=lambda x: x[0], value=lambda x: x[1])

    return Context(stores=stores, chains=chains, joint2chains=joint2chains)


def group_link_chains(ctx: Context):
    """
    按链路分组
    """
    max_turning_distance = 50 * METER
    save_path = utils.ensure_path(Path("tmp/combine_chain.txt"))

    adjoin_chains = []
    for (chain_id, left), pois in tqdm(ctx.stores.items()):
        chain = ctx.chains[chain_id]
        points = [wkt.loads(geom) for _bid, geom in pois]
        dists = [chain.line.project(p) for p in points]
        if min(dists) < max_turning_distance:
            next_info = next_link_chain(ctx, chain, left, reverse=False)
        elif chain.line.length - max(dists) < max_turning_distance:
            next_info = next_link_chain(ctx, chain, left, reverse=True)
        else:
            continue

        if next_info:
            adjoin_chains.append(((chain_id, left), next_info))

    components = find_connected_components(adjoin_chains)
    for component in components:
        chain_id, _ = component[0]
        union_chain = ctx.chains[chain_id]
        for chain_id, left in component[1:]:
            chain = ctx.chains[chain_id]
            try:
                union_chain = union_chain.union(chain)
            except ValueError as err:
                for c_id, _ in component:
                    print(ctx.chains[c_id].to_dict())

                raise err

        tsv.write_tsv(save_path, [[union_chain.line.wkt]], mode="a")


def find_connected_components(edges):
    """
    查询连通图
    """
    # 构建图的邻接表
    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)

    # 用于存储访问过的节点
    visited = set()

    # 用于存储所有的连通分量
    components = []

    # 深度优先搜索找到连通分量
    def dfs(n, c):
        visited.add(n)
        c.append(n)
        for neighbor in graph[n]:
            if neighbor not in visited:
                dfs(neighbor, c)

    # 遍历所有节点，找到所有连通分量
    for node in graph:
        if node not in visited:
            component = []
            dfs(node, component)
            components.append(component)

    return components


@logger.catch
def main():
    """
    主函数
    """
    work_dir = Path("/home/<USER>/dingping/aoi-ml/src/parking/storefront/tmp")
    # work_dir = Path(r"C:\Users\<USER>\Downloads\link_chain_v9")
    file_path = work_dir / "beijing_haidian_store_poi.tsv"
    # file_path = work_dir / "debug_case.txt"
    # save_path = work_dir / "debug_links_chain_v2.tsv"

    # group_store_pois(file_path, batch="v9")
    ctx = build_context(work_dir, batch="v9")
    group_link_chains(ctx)
    # draw_links(save_path)


if __name__ == "__main__":
    main()
