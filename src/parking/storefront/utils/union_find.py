"""
并查集算法，由 ChatGPT 提供
"""

from collections import defaultdict


class UnionFind:
    """
    并查集
    """

    def __init__(self):
        self.parent = {}
        self.rank = {}

    def find(self, x):
        """
        查找元素 x 所属的集合的代表元素（根节点）。
        该方法通常使用路径压缩技术来优化，即在查找过程中直接将沿途的节点直接连接到根节点，减少后续查找时间。
        :param x: 需要查找的元素
        :return: 元素 x 所属集合的代表元素（根节点）
        """
        if self.parent[x] != x:
            self.parent[x] = self.find(self.parent[x])  # 路径压缩

        return self.parent[x]

    def union(self, x, y):
        """
        合并元素 x 和元素 y 所属的集合。
        该方法首先找到 x 和 y 的根节点（代表元素），如果根节点相同，则它们已经在同一个集合中，无需合并。
        如果根节点不同，则将它们合并，通常使用按秩合并（根据树的深度合并）来保持树的平衡，减少树的高度。
        :param x: 需要合并的第一个元素
        :param y: 需要合并的第二个元素
        """
        root_x = self.find(x)
        root_y = self.find(y)

        if root_x != root_y:
            # 合并时根据秩来优化
            if self.rank[root_x] > self.rank[root_y]:
                self.parent[root_y] = root_x
            elif self.rank[root_x] < self.rank[root_y]:
                self.parent[root_x] = root_y
            else:
                self.parent[root_y] = root_x
                self.rank[root_x] += 1

    def add(self, x):
        """
        向并查集中添加一个新元素 x。
        如果元素 x 已经存在于并查集中，则该方法通常不需要执行任何操作（除非需要更新某些属性）。
        初始化时，每个元素的父节点都是自己，表示每个元素单独成一个集合。
        :param x: 需要添加的元素
        """
        if x not in self.parent:
            self.parent[x] = x
            self.rank[x] = 0


def merge_sets(list_of_sets: list[set]):
    """
    合并存在交集的集合，使用并查集
    """
    uf = UnionFind()

    # 将每个元素加入并查集
    for s in list_of_sets:
        for element in s:
            uf.add(element)

    # 对每个集合中的元素做 union 操作
    for s in list_of_sets:
        elements = list(s)
        for i in range(1, len(elements)):
            uf.union(elements[0], elements[i])

    # 使用 find 获取每个集合的根节点，将根节点对应的元素收集在一起
    groups = defaultdict(set)
    for element in uf.parent:
        root = uf.find(element)
        groups[root].add(element)

    # 转换为列表形式返回
    return list(groups.values())
