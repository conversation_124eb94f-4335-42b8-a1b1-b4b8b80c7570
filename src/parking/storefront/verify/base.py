"""
通用成员
"""
import uuid
from dataclasses import dataclass, field
from pathlib import Path
from typing import Protocol, Optional

import cv2
import numpy as np
from shapely import Point, LineString, ops as shapely_opts

from src.parking.recognition import dbutils
from src.tools import pgsql

# TODO: 该路径为 rpm01 的缓存目录，在其它机器上运行时需要替换
CACHE_DIR = Path("/home/<USER>/dingping/tmp") / "track_image"


@dataclass
class ViewImage:
    """
    全景或众源照片
    """

    image_type: str
    track_id: str
    time: str
    direction: np.ndarray  # shape: (2,)
    position: Point
    image: np.ndarray
    raw: dict[str, any] = field(default_factory=dict)


class ViewPoint(Protocol):
    """
    观察点协议
    """

    geom: Point
    foot: Point
    side_views: list[ViewImage] = field(default_factory=list)  # 侧视图，即全景影像

    @property
    def view_id(self):
        """
        观察点 id
        """
        pass


@dataclass
class DefaultViewPoint:
    """
    视角点
    """

    geom: Point  # projection.line 上的点，即位于道路 link 上的点
    foot: Point  # geom 投影到门前面上的垂足
    side_views: list[ViewImage] = field(default_factory=list)  # 侧视图，即全景影像

    @property
    def view_id(self):
        """
        观察点 id
        """
        return uuid.uuid4().hex


def get_view_positions(line: LineString, max_angle: float, segment_length):
    """
    获取给定线段 line 上的观察点
    :param line: 需要观察的道路线
    :param max_angle: 道路线的角度阈值，用于打断道路线以增加观察点，因为拐弯较大的话，全景视野会被遮挡，需要增加观察点
    :param segment_length: 每个视角点可覆盖的范围，如：一个全景视角可以负责 100m，故长度超过 100m 则需要增加观察点
    :return: 观察点：需要通过全景观察的点位
    """

    def get_segment_count(length: float):
        return int(length // segment_length) + 1

    line = line.simplify(5 * 1e-5)
    points = get_turning_points(line, max_angle)
    project_distances = [line.project(Point(x)) for x in points]
    project_distances = [0, *project_distances, line.length]
    segments = [
        shapely_opts.substring(line, start, end) for start, end in zip(project_distances[:-1], project_distances[1:])
    ]
    for segment in segments:
        n = get_segment_count(segment.length)
        for i in range(n):
            half = segment.length / (2 * n)
            distance = (2 * i + 1) * half
            point: Point = segment.interpolate(distance)
            yield point


def get_turning_points(line: LineString, max_angle: float):
    """
    获取道路线上的拐点
    """
    line_points = [np.array(x) for x in line.coords]
    pairs = [(line_points[i - 1], line_points[i], line_points[i + 1]) for i in range(1, len(line_points) - 1)]
    sum_angle = 0
    for p, o, q in pairs:
        v1 = o - p
        v2 = q - o
        angle = np.degrees(np.arccos(_cos_vec(v1, v2)))
        sum_angle += angle
        if sum_angle > max_angle:
            yield o
            sum_angle = 0


def _cos_vec(v1, v2):
    dot_product = np.dot(v1, v2)
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    val = dot_product / (norm_v1 * norm_v2)
    return np.clip(val, -1, 1)


def get_brightness(image: np.ndarray):
    """
    获取图片的亮度
    """
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = hsv_image[:, :, 2].mean()
    brightness = round(brightness)
    return brightness


def get_link_info(link_id: str) -> Optional[tuple[int, int]]:
    """
    获取道路类型，返回：(kind, viad)
    """
    sql = """
        select kind, viad from nav_link where link_id = %s;
    """
    ret = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [link_id])
    return ret


def get_direction(theta: float):
    """
    将 north 转化为方向向量（单位向量）
    """
    return np.array([np.sin(np.radians(theta)), np.cos(np.radians(theta))])
