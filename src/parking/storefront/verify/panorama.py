"""
输出门前停车场附近的全景照片，并投影到相应视角，用于人工作业。
"""
import cv2
import numpy as np
from loguru import logger
from shapely import Point

from src.aikit import boundary, satellite_imagery
from src.aikit.track import track_point
from src.parking.storefront import verify
from src.parking.storefront.verify import ViewImage, CACHE_DIR

METER = 1e-5


def get_nearest_image(point: Point, buffer: float, nearly_month: int):
    """
    获取距离给定点 point 最近的一张全景影像，搜索半径为 buffer，搜索时间范围为最近 nearly_month 个月。
    :param point: 给定的目标点
    :param buffer: 搜索空间范围
    :param nearly_month: 搜索时间范围
    :return: 最近的全景影像，如果没有找到则返回 None
    """

    def get_nearest_point(track_points: list[dict], p: Point):
        track_infos = [(Point(x["x"], x["y"]), x) for x in track_points]
        return min(track_infos, key=lambda x: p.distance(x[0]))

    bounds = boundary.from_wkt(point.wkt, buffer=buffer)
    for i in range(1, nearly_month + 1):
        tracks = list(track_point.get_track_points(bounds, track_point.one_month(i), track_point.SRC_QUANJING))
        tracks = [x for x in tracks if x["has_pic"]]

        while len(tracks) > 0:
            position, track = get_nearest_point(tracks, point)
            track_id, pic_url = track["id"], track["pic_url"]
            pic_path = CACHE_DIR / "panorama" / f"{track_id}.jpg"
            if not pic_path.exists() or not satellite_imagery._is_jpg(pic_path):
                try:
                    satellite_imagery._download(pic_url, pic_path, timeout=10)
                except Exception as e:
                    tracks.remove(track)
                    logger.error(f"[download-panorama] url='{pic_url}', error={e}")
                    continue

            # 读取图片并判空，谨防 'Premature end of JPEG file' 等图片损坏错误
            image = cv2.imread(str(pic_path))
            if image is None:
                tracks.remove(track)
                continue

            yield ViewImage(
                image_type="panorama",
                track_id=track_id,
                time=track["time"],
                direction=verify.get_direction(track["north"]),
                position=position,
                image=image,
                raw=track,
            )
            # 正常情况下，取到一张满意的图后就应该终止生成器；如果没有终止，说明对图片不满意，那么整个车天的图都差不多，所以都不要了
            tracks = [x for x in tracks if x["cdid"] != track["cdid"]]


def to_normalized_vec(p1: Point, p2: Point) -> np.ndarray:
    """
    构建单位向量：p1 -> p2
    """
    start = np.array([p1.x, p1.y])
    end = np.array([p2.x, p2.y])
    vec = end - start
    vec = vec / np.linalg.norm(vec)
    return vec


def get_projected_image_size(lat_range: tuple, lon_range: tuple):
    """
    获取投影后的图片尺寸
    """
    lat_min, lat_max = lat_range
    lon_min, lon_max = lon_range
    width = int((lon_max - lon_min) / 360 * 4096)
    height = int((lat_max - lat_min) / 180 * 2048)
    return width, height


def get_lon_range(vehicle_vec: np.ndarray, view_vec: np.ndarray, view_angle=120):
    """
    获取经度范围，根据车辆行驶方向和视角方向，给出指定视野角度的范围
    :param vehicle_vec: 车辆行驶方向
    :param view_vec: 视角方向
    :param view_angle: 视野角度
    :return: 经度范围
    """
    cross_product = np.cross(vehicle_vec, view_vec)
    dot_product = np.dot(vehicle_vec, view_vec)
    angle = np.arccos(np.clip(dot_product, -1.0, 1.0))
    if cross_product > 0:
        angle = -angle

    angle_degrees = np.degrees(angle)
    angle_degrees = angle_degrees % 360

    half_angle = view_angle / 2
    a, b = angle_degrees - half_angle, angle_degrees + half_angle
    a, b = a + 90, b + 90
    return a, b
