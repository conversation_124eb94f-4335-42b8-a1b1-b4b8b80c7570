"""
推送图片到掘金
TODO: 合入流水线
"""
import requests

API = "http://gzdt-sys-rpm100vqs1j3.gzdt.baidu.com:8016/audit/api/inter/parkingfrontpic"
AFS_BASE = "/user/map-data-streeview/aoi-ml/parking/storefront/street_picture"


def main():
    """
    主函数
    """
    body = {
        # "afs_path": f"{AFS_BASE}/huizhou_20250208_bua_area_top80_20250208.tar",
        # "afs_path": f"{AFS_BASE}/guiyang_20250219_bua_area_top80_20250219.tar",
        # "afs_path": f"{AFS_BASE}/taiyuan_20250219_bua_area_top80_20250219.tar",
        # "afs_path": f"{AFS_BASE}/dalian_20250219_bua_area_top80_20250219.tar",
        # "afs_path": f"{AFS_BASE}/nanchang_20250219_bua_area_top80_20250219.tar",
        # "afs_path": f"{AFS_BASE}/shantou_20250219_bua_area_top80_20250219.tar",
        # "afs_path": f"{AFS_BASE}/jiaxing_20250219_bua_area_top80_20250219.tar",
        # "afs_path": f"{AFS_BASE}/changzhou_20250226_bua_area_top80_20250226.tar",
        # 2025-02-27
        # "afs_path": f"{AFS_BASE}/shaoxing_20250226_bua_area_top80_20250226.tar",
        # "afs_path": f"{AFS_BASE}/taizhou1_20250226_bua_area_top80_20250226.tar",
        # "afs_path": f"{AFS_BASE}/xianyang_20250226_bua_area_top80_20250226.tar",
        # "afs_path": f"{AFS_BASE}/luoyang_20250226_bua_area_top80_20250226.tar",
        # "afs_path": f"{AFS_BASE}/jiangmen_20250219_bua_area_top80_20250219.tar",
        # "afs_path": f"{AFS_BASE}/zhanjiang_20250219_bua_area_top80_20250219.tar",
        # "afs_path": f"{AFS_BASE}/nantong_20250226_bua_area_top80_20250226.tar",
        # "afs_path": f"{AFS_BASE}/baoding_20250226_bua_area_top80_20250226.tar",
        # "afs_path": f"{AFS_BASE}/shanghai_20250227_bua_area_no_build_top100_20250301.tar",
        # "afs_path": f"{AFS_BASE}/beijing_20250227_bua_area_no_build_top100_20250301.tar",
        # 补召：
        # "afs_path": f"{AFS_BASE}/beijing_20250305_bua_area_top100_20250306.tar",
        # "afs_path": f"{AFS_BASE}/shanghai_patch_20250306_bua_area_top100_20250307.tar",
        # "afs_path": f"{AFS_BASE}/guangzhou_patch_20250306_bua_area_top80_20250307.tar",
        # "afs_path": f"{AFS_BASE}/shenzhen_patch_20250306_bua_area_top80_20250307.tar",
        # "afs_path": f"{AFS_BASE}/chengdu_patch_20250310_bua_area_top80_20250310.tar",
        # "afs_path": f"{AFS_BASE}/wuhan_patch_20250310_bua_area_top80_20250310.tar",
        # "afs_path": f"{AFS_BASE}/xian_patch_20250310_bua_area_top80_20250310.tar",
        # "afs_path": f"{AFS_BASE}/chongqing_patch_20250310_bua_area_top80_20250310.tar",
        # "afs_path": f"{AFS_BASE}/dongguan_patch_20250310_bua_area_top80_20250310.tar",
        # "afs_path": f"{AFS_BASE}/nanjing_patch_20250310_bua_area_top80_20250310.tar",
        # "afs_path": f"{AFS_BASE}/kunming_patch_20250310_bua_area_top80_20250311.tar",
        # "afs_path": f"{AFS_BASE}/tianjin_patch_20250311_bua_area_top80_20250311.tar",
        # "afs_path": f"{AFS_BASE}/foshan_patch_20250311_bua_area_top80_20250311.tar",
        # "afs_path": f"{AFS_BASE}/changsha_patch_20250311_bua_area_top80_20250311.tar",
        # "afs_path": f"{AFS_BASE}/ningbo_patch_20250311_bua_area_top80_20250311.tar",
        # "afs_path": f"{AFS_BASE}/TODO.tar",
        "token": "gD2fI9UKZUtYhvww",
    }
    ret = requests.post(url=API, json=body)
    print(ret.json())


if __name__ == "__main__":
    main()
