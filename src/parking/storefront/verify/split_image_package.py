"""
将准备上传掘金的任务进行分包，以面为维度，确保同一个面的图片都在一个包里
"""
import shutil
import sys
from pathlib import Path

from src.tools import utils, linq


def main(limit_count: int, work_dir: Path):
    """
    主函数
    """
    batch = work_dir.name.lstrip("segments_")
    image_dir = work_dir / "side_view"

    images = [(p.stem.split("-"), p) for p in image_dir.glob("*.jpg")]
    images = [(x[2], p) for x, p in images]
    print(f"{len(images)=}")
    image_groups = linq.group_by(images, lambda x: x[0], value=lambda x: x[1])
    print(f"{len(image_groups)=}")

    image_count = 0
    part = 0
    for image_paths in image_groups.values():
        if image_count > limit_count:
            print(f"part_{part}: {image_count}")
            image_count = 0
            part += 1

        target_dir = utils.ensure_dir(work_dir / "juejin_tasks" / f"{batch}.part_{part}")
        for image_path in image_paths:
            if image_path.exists():
                shutil.copy(image_path, target_dir)
                image_count += 1
            else:
                print(f"not found {image_path.name=}")


if __name__ == "__main__":
    main(int(sys.argv[1]), Path(sys.argv[2]))
