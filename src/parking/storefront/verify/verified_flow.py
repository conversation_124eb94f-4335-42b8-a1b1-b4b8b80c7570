"""
核实面生成
"""
import collections
import math
from dataclasses import dataclass, field
from functools import partial
from pathlib import Path
from typing import Literal, Iterable, Callable

import shapely.ops
from shapely import Point, LineString, Polygon, wkt

from src.parking.recognition import dbutils
from src.parking.storefront import verify, turnoff_point_cluster
from src.parking.storefront.utils import geometric, union_find
from src.parking.storefront.utils.geometric import METER
from src.tools import pgsql, utils, pipeline, linq, tsv, turnoff_point

# VERSION = "1.0.0"  # 初始化提交
# VERSION = "1.1.0"  # 修复拐角裁切问题
# VERSION = "1.1.1"  # 修复单碎片过滤问题
# VERSION = "2.0.0"  # 采用切外侧线的方法进行核实修形
# VERSION = "2.1.0"  # 熄火点+邻接结论
# VERSION = "2.1.1"  # 修复 buffer 问题
# VERSION = "2.2.0"  # 扩展相邻结论 review 策略为任意长度的滑动窗口，并设定阈值为 4
# VERSION = "2.2.1"  # 增加熄火点策略筛选：（1）去除连续 >= 3 个的纠正结果；（2）去除处理后落单的纠正结果
# VERSION = "2.2.2"  # 滑动窗口阈值重定为 3
# VERSION = "2.2.3"  # 修改熄火点 buffer 为 20m，密度阈值为 0.3
# VERSION = "2.3.0"  # 兼容按街区为单位跑数据
# VERSION = "2.3.1"  # 兼容 conclusion=10 的情况（交叉验证不一致）
# VERSION = "2.4.0"  # 加入众源熄火点，并适配多进程
# VERSION = "2.4.1"  # 修复重置过长纠正段落的 bug
# VERSION = "2.4.2"  # 过长纠正段落阈值改为 4
# VERSION = "2.4.3"  # 只要街区内包含图片缺失，就不处理该街区
# VERSION = "2.4.4"  # 修改滑动窗口参数 n=4，m=2
# VERSION = "3.2.0"  # 回退到之前的参数，因为新参数效果不好
# VERSION = "3.3.0"  # 熄火点纠正策略增加：保留的段落中至少要有一个段落是原生有效的
# VERSION = "3.4.0"  # 以面维度进行处理，同时去除门前路合并、邻近合并策略
VERSION = "3.4.1"  # 修复对环形面的兼容


CONCLUSION_PRE_YES = -100  # 值域之外的值，用于临时标记
CONCLUSION_SOLID_NO = -101  # 高置信无效，不可纠正
CONCLUSION_UNKNOWN = -1
CONCLUSION_YES = 1
CONCLUSION_NO = 2
CONCLUSION_NOT_SURE = 3
CONCLUSION_PENDING = -10
CONCLUSION_MISSING = -11

EPS = 1e-8

ENABLE_PENDING_AS_NOT_SURE = False
ENABLE_MISSING_AS_NOT_SURE = True


@dataclass
class Image:
    """
    核实图片
    """

    image_id: str
    batch: str
    conclusion: int

    @property
    def url(self):
        """
        图片访问地址
        """
        host = "https://lutao-pic.su.bcebos.com/park_storefront"
        return f"{host}/{self.batch}.tar/{self.image_id}.jpg" if self.batch and self.image_id else ""

    @property
    def is_empty(self):
        """
        是否有图片
        """
        return not self.image_id

    def to_dict(self):
        """
        转为字典
        """
        return {
            "image_id": self.image_id,
            "batch": self.batch,
            "conclusion": self.conclusion,
            "url": self.url,
        }

    @staticmethod
    def from_dict(d: dict):
        """
        从字典创建对象
        """
        return Image(image_id=d["image_id"], batch=d["batch"], conclusion=d["conclusion"])


@dataclass
class Segment:
    """
    核实片段
    """

    segment_id: str
    geom: Point
    images: list[Image] = field(default_factory=list)
    conclusion: int = field(init=False)

    def __post_init__(self):
        conclusion = _merge_conclusions(self.images)
        if conclusion == CONCLUSION_MISSING and ENABLE_MISSING_AS_NOT_SURE:
            conclusion = CONCLUSION_NOT_SURE

        if conclusion == CONCLUSION_PENDING and ENABLE_PENDING_AS_NOT_SURE:
            conclusion = CONCLUSION_NOT_SURE

        self.conclusion = conclusion

    def to_dict(self):
        """
        转为字典
        """
        return {
            "segment_id": self.segment_id,
            "geom": self.geom.wkt,
            "images": [x.to_dict() for x in self.images],
        }

    @staticmethod
    def from_dict(d: dict):
        """
        从字典创建对象
        """
        return Segment(
            segment_id=d["segment_id"], geom=wkt.loads(d["geom"]), images=[Image.from_dict(x) for x in d["images"]]
        )


@dataclass
class PrimePolygon:
    """
    原始面
    """

    strategy_id: int
    baseline: LineString
    geom: Polygon
    is_reuse: bool
    segments: list[Segment] = field(default_factory=list)

    @property
    def status(self) -> Literal["pending", "missing", "ready"]:
        """
        该原始面的核实作业状态
        """
        if len(self.segments) == 0 or any(x.conclusion == CONCLUSION_MISSING for x in self.segments):
            return "missing"

        if any(x.conclusion == CONCLUSION_PENDING for x in self.segments):
            return "pending"

        return "ready"

    def to_dict(self):
        """
        转为字典
        """
        return {
            "strategy_id": self.strategy_id,
            "status": self.status,
            "baseline": self.baseline.wkt,
            "geom": self.geom.wkt,
            "is_reuse": self.is_reuse,
            "segments": [x.to_dict() for x in self.segments],
        }

    @staticmethod
    def from_dict(d: dict):
        """
        从字典创建对象
        """
        return PrimePolygon(
            strategy_id=d["strategy_id"],
            baseline=wkt.loads(d["baseline"]),
            geom=wkt.loads(d["geom"]),
            segments=[Segment.from_dict(x) for x in d["segments"]],
            is_reuse=d["is_reuse"],
        )


# PART-1 裁剪 & 以面为单位的流程


@dataclass
class ClipInfo:
    """
    核实片段裁切信息
    """

    segment: Segment
    range: tuple[float, float]
    conclusion: int = field(init=False)

    def __post_init__(self):
        self.conclusion = self.segment.conclusion


@dataclass
class ClipContext:
    """
    核实上下文
    """

    task_id: int
    park: PrimePolygon
    is_left: bool = field(init=False)
    sideline: LineString = field(init=False)
    clip_infos: list[ClipInfo] = field(default_factory=list)
    clip_ranges: list[tuple[float, float]] = field(default_factory=list)
    polygons: list[Polygon] = field(default_factory=list)
    tags: dict[str, list[list]] = field(default_factory=lambda: collections.defaultdict(list))


def get_outside_line(ctx: ClipContext, proceed, expected_buffer: float, clip_eps: float):
    """
    获取外侧线
    """
    geom, baseline = ctx.park.geom, ctx.park.baseline

    # noinspection PyBroadException
    try:
        # 核实面阶段应该优先使用 v2，因为其面较为规整，滚球法所求的中心线有更高的概率符合预期
        outside_line, inside_line = geometric.get_sidelines_v2(geom, baseline)
    except:
        outside_line, inside_line = geometric.get_sidelines_v1(geom, baseline, eps=clip_eps)

    outside_line = _trim_sideline(outside_line, expected_buffer, clip_eps)
    outside_line = geometric.extend_linestring(outside_line, clip_eps)

    inner_point = inside_line.interpolate(inside_line.length / 2)
    is_left = geometric.is_left(inner_point, outside_line)

    ctx.is_left = is_left
    ctx.sideline = outside_line
    proceed()


def get_clip_infos(ctx: ClipContext, proceed, max_angle: float):
    """
    获取裁切片段信息
    """
    ctx.clip_infos = _get_clip_infos(ctx.park.baseline, ctx.sideline, ctx.park.segments, max_angle)
    proceed()


def review_set_solid_no(ctx: ClipContext, proceed, solid_count: int):
    """
    根据连续的 SOLID_NO 数量，修改片段结论
    """
    conclusions = [c.conclusion for c in ctx.clip_infos]
    for a, b in _get_continuous_range(conclusions, CONCLUSION_NO):
        if b - a < solid_count:
            continue

        for i in range(a, b):
            ctx.clip_infos[i].conclusion = CONCLUSION_SOLID_NO

    proceed()


def review_by_turnoff_point_set_pre_yes(
    ctx: ClipContext,
    proceed,
    provider: Callable[[str], list[turnoff_point.TurnoffPoint]],
    buffer: float,
    force_min_density: float,
    min_density: float,
):
    """
    熄火点纠正：STEP-1 设置候选段落为 PRE_YES
    """

    def get_density(ci: ClipInfo):
        buf = buffer
        a, b = ci.range
        fragment_line = shapely.ops.substring(ctx.sideline, a, b)
        fragment_line = _trim_sideline(fragment_line, buf, eps=1 * METER)
        buf *= 1 if ctx.is_left else -1
        fragment = fragment_line.buffer(buf, cap_style="flat", single_sided=True)
        pts = provider(fragment.wkt)
        return (len(pts) / (b - a)) * METER

    densities = {}
    for info in ctx.clip_infos:
        conclusion = info.conclusion
        if conclusion == CONCLUSION_NO:
            density = get_density(info)
            densities[info.segment.segment_id] = density
            review = CONCLUSION_PRE_YES if density >= force_min_density else conclusion
            info.conclusion = review
        elif conclusion == CONCLUSION_NOT_SURE:
            density = get_density(info)
            densities[info.segment.segment_id] = density
            review = CONCLUSION_PRE_YES if density >= min_density else conclusion
            info.conclusion = review

    for info in ctx.clip_infos:
        s = info.segment
        if s.segment_id in densities:
            tag = [
                ctx.park.strategy_id,
                s.segment_id,
                s.conclusion,
                info.conclusion,
                densities[s.segment_id],
                s.geom.wkt,
            ]
            ctx.tags["review_by_turnoff_point.set_pre_yes"].append(tag)

    proceed()


def review_by_turnoff_point_filter_pre_yes(ctx: ClipContext, proceed, max_correct_count: int):
    """
    熄火点纠正：STEP-2 过滤掉异常候选段落：
      1. 连续超过 max_correct_count 个的 PRE_YES
      2. 落单的 PRE_YES
      3. 一段连续的 PRE_YES 中，至少要有一个原生的 YES 段落
    """
    # 排除连续 > max_correct_count 个 PRE_YES
    conclusions = [c.conclusion for c in ctx.clip_infos]
    invalid_ranges = [
        (a, b) for a, b in _get_continuous_range(conclusions, CONCLUSION_PRE_YES) if b - a > max_correct_count
    ]
    for a, b in invalid_ranges:
        for info in ctx.clip_infos[a:b]:
            assert info.conclusion == CONCLUSION_PRE_YES, "can only deny pre-yes conclusion"
            info.conclusion = CONCLUSION_NO
            s = info.segment
            tag = [ctx.park.strategy_id, s.segment_id, s.conclusion, f"too_long={b - a}", s.geom.wkt]
            ctx.tags["review_by_turnoff_point.filter_pre_yes"].append(tag)

    # 排除落单的 PRE_YES
    for i, info in enumerate(ctx.clip_infos):
        if info.conclusion != CONCLUSION_PRE_YES:
            continue

        left_conclusion = ctx.clip_infos[i - 1].conclusion if i > 0 else CONCLUSION_NO
        right_conclusion = ctx.clip_infos[i + 1].conclusion if i < len(ctx.clip_infos) - 1 else CONCLUSION_NO
        adjoin_conclusions = [left_conclusion, right_conclusion]
        if all(x not in (CONCLUSION_YES, CONCLUSION_PRE_YES) for x in adjoin_conclusions):
            info.conclusion = CONCLUSION_NO
            s = info.segment
            tag = [ctx.park.strategy_id, s.segment_id, s.conclusion, f"alone", s.geom.wkt]
            ctx.tags["review_by_turnoff_point.filter_pre_yes"].append(tag)

    # 一段连续的 PRE_YES 中，至少要粘连一个原生的 YES 段落
    for a, b in _get_continuous_range(conclusions, CONCLUSION_PRE_YES):
        adjoin_a = a - 1
        if adjoin_a >= 0 and ctx.clip_infos[adjoin_a].segment.conclusion == CONCLUSION_YES:
            continue

        adjoin_b = b + 1
        if adjoin_b < len(ctx.clip_infos) and ctx.clip_infos[adjoin_b].segment.conclusion == CONCLUSION_YES:
            continue

        infos = ctx.clip_infos[a:b]
        for info in infos:
            info.conclusion = CONCLUSION_NO
            s = info.segment
            tag = [ctx.park.strategy_id, s.segment_id, s.conclusion, f"not_connected_to_yes", s.geom.wkt]
            ctx.tags["review_by_turnoff_point.filter_pre_yes"].append(tag)

    proceed()


def review_by_turnoff_point_set_pre_yes_to_yes(ctx: ClipContext, proceed):
    """
    熄火点纠正：STEP-3 将剩余的 PRE_YES 正式转换为 YES
    """
    # 将剩余的 PRE_YES 转换为 YES
    for info in ctx.clip_infos:
        if info.conclusion == CONCLUSION_PRE_YES:
            info.conclusion = CONCLUSION_YES

            s = info.segment
            tag = [
                ctx.park.strategy_id,
                s.segment_id,
                s.conclusion,
                info.conclusion,
                s.geom.wkt,
            ]
            ctx.tags["review_by_turnoff_point.set_pre_yes_to_yes"].append(tag)

    proceed()


def review_by_adjoin_conclusion(ctx: ClipContext, proceed, n: int, m: int, label: int):
    """
    掘金纠正：根据相邻片段结论纠正核实片段结论，
    连续 n 个片段中，若有 m 个片段不为 label，则将其改为 label，注意：这 m 个片段，不能位于滑动窗口的两端
    """
    infos = ctx.clip_infos
    for i in range(len(infos) - n + 1):
        window = infos[i : i + n]
        not_items = [x for x in window if x.conclusion != label]
        if len(not_items) > m:
            continue

        not_indexes = {window.index(x) for x in not_items}
        # 如果非 label 片段有位于滑动窗口两端的项，则不可以触发纠正策略
        if 0 in not_indexes or (n - 1) in not_indexes:
            continue

        for not_item in not_items:
            # 先置为预备 YES，否则先变 YES 的会带动后变 YES，导致一大片全连起来了
            not_item.conclusion = CONCLUSION_PRE_YES

    for info in infos:
        if info.conclusion == CONCLUSION_PRE_YES:
            info.conclusion = label

            segment = info.segment
            tag = [ctx.park.strategy_id, segment.segment_id, segment.conclusion, info.conclusion, segment.geom.wkt]
            ctx.tags[f"review_by_adjoin_conclusion.{n}_{m}_{label}"].append(tag)

    proceed()


def review_normalize_conclusion(ctx: ClipContext, proceed):
    """
    标准化核实片段结论
    """
    for info in ctx.clip_infos:
        if info.conclusion == CONCLUSION_SOLID_NO:
            info.conclusion = CONCLUSION_NO

    proceed()


def merge_clip_ranges(ctx: ClipContext, proceed):
    """
    合并核实片段区间
    """
    ctx.clip_ranges = list(_merge_clip_ranges(ctx.clip_infos))
    proceed()


def make_clipped_polygons(ctx: ClipContext, proceed, buffer: float):
    """
    制作裁切结果
    """
    sideline, is_left = ctx.sideline, ctx.is_left
    to_clip_sideline = sideline.buffer(0.001 * METER)

    def prune(target: Polygon):
        geoms = target.difference(to_clip_sideline)
        geoms = geometric.flat_polygon(geoms)
        is_handled = len(geoms) > 1
        if not is_handled:
            return is_handled, target

        geoms = [x for x in geoms if geometric.is_left_polygon(x, sideline) == is_left]
        return is_handled, max(geoms, key=lambda x: x.area)

    # 修剪外侧线：根据有效区间裁切外侧线，并修剪末端
    fragments = [shapely.ops.substring(sideline, a, b) for a, b in ctx.clip_ranges]
    fragments = [_trim_sideline(x, buffer, eps=1 * METER) for x in fragments]

    # buffer 得到新面
    buffer *= 1 if is_left else -1
    polygons = []
    for fragment in fragments:
        geom = fragment.buffer(buffer, cap_style="flat", single_sided=True)
        handled, polygon = prune(geom)
        if handled:
            tag = [geom.wkt, polygon.wkt]
            ctx.tags["make_clipped_polygons.pruned"].append(tag)

        polygons.append(polygon)

    ctx.polygons = polygons
    proceed()


# PART-2 合并&以街区为单位的流程


@dataclass
class VerifiedPolygon:
    """
    核实面
    """

    prime_ids: list[int]
    prev_face_ids: list[str]
    geom: Polygon
    face_id: str = field(init=False)

    def __post_init__(self):
        self.face_id = utils.md5(self.geom.wkt)


@dataclass
class Context:
    """
    上下文
    """

    task_id: int
    primes: list[PrimePolygon]
    verifieds: list[VerifiedPolygon] = field(default_factory=list)
    tags: dict[str, list[list]] = field(default_factory=lambda: collections.defaultdict(list))


clip_pipe = pipeline.Pipeline(
    partial(get_outside_line, expected_buffer=10 * METER, clip_eps=1 * METER),
    partial(get_clip_infos, max_angle=60),
    # partial(review_set_solid_no, solid_count=8),
    partial(
        review_by_turnoff_point_set_pre_yes,
        provider=turnoff_point_cluster.get_doctor_points,
        buffer=20 * METER,
        force_min_density=1.0,
        min_density=0.3,
    ),
    partial(
        review_by_turnoff_point_set_pre_yes,
        provider=turnoff_point_cluster.get_zhongyuan_points,
        buffer=20 * METER,
        force_min_density=1.2,
        min_density=0.3,
    ),
    partial(review_by_turnoff_point_filter_pre_yes, max_correct_count=4),
    review_by_turnoff_point_set_pre_yes_to_yes,
    partial(review_by_adjoin_conclusion, n=4, m=2, label=CONCLUSION_YES),
    # review_normalize_conclusion,
    merge_clip_ranges,
    partial(make_clipped_polygons, buffer=10 * METER),
)
reuse_clip_pipe = pipeline.Pipeline(
    partial(get_outside_line, expected_buffer=10 * METER, clip_eps=1 * METER),
    partial(get_clip_infos, max_angle=60),
    # partial(review_set_solid_no, solid_count=5),
    partial(
        review_by_turnoff_point_set_pre_yes,
        provider=turnoff_point_cluster.get_doctor_points,
        buffer=20 * METER,
        force_min_density=1.0,
        min_density=0.3,
    ),
    partial(
        review_by_turnoff_point_set_pre_yes,
        provider=turnoff_point_cluster.get_zhongyuan_points,
        buffer=20 * METER,
        force_min_density=1.2,
        min_density=0.3,
    ),
    partial(review_by_turnoff_point_filter_pre_yes, max_correct_count=2),
    review_by_turnoff_point_set_pre_yes_to_yes,
    partial(review_by_adjoin_conclusion, n=3, m=1, label=CONCLUSION_YES),
    # review_normalize_conclusion,
    merge_clip_ranges,
    partial(make_clipped_polygons, buffer=10 * METER),
)


def clip_by_verify(ctx: Context, proceed):
    """
    核实裁切流程
    """

    def clip_one(clip: ClipContext):
        try:
            if clip.park.is_reuse:
                reuse_clip_pipe(clip)
            else:
                clip_pipe(clip)
        except Exception as e:
            clip.tags["error"].append([str(e)])

        if len(clip.polygons) == 0:
            clip.tags["error"].append(["nothing"])

    for prime in ctx.primes:
        clip_context = ClipContext(task_id=ctx.task_id, park=prime)
        clip_one(clip_context)
        verifieds = [
            VerifiedPolygon(prime_ids=[clip_context.park.strategy_id], prev_face_ids=[], geom=p)
            for p in clip_context.polygons
        ]
        ctx.verifieds.extend(verifieds)
        for k, v in clip_context.tags.items():
            ctx.tags[k].extend(v)

    proceed()


def merge_by_nearby(ctx: Context, proceed, max_distance: float):
    """
    合并相邻停车场
    """
    count = len(ctx.verifieds)
    pairs = []
    for i in range(count):
        for j in range(i + 1, count):
            a, b = ctx.verifieds[i], ctx.verifieds[j]
            dist = a.geom.distance(b.geom)
            if dist <= max_distance:
                pairs.append((a, b))

    face2parks = {x.face_id: x for x in ctx.verifieds}
    face_id_pairs = [{a.face_id, b.face_id} for a, b in pairs]
    merged_face_ids = union_find.merge_sets(face_id_pairs)
    merged_face_ids = [x for x in merged_face_ids if len(x) > 1]
    merged_parks = []
    for face_ids in merged_face_ids:
        parks = [face2parks[face_id] for face_id in face_ids]
        merged_park = _merge_verified_polygons(parks)
        tag = [",".join(str(x) for x in merged_park.prime_ids), merged_park.face_id, "merge_by_nearby"]
        ctx.tags["merge_by_nearby"].append(tag)
        merged_parks.append(merged_park)

    ctx.verifieds = _replace_verified_polygons(ctx.verifieds, merged_parks)
    proceed()


def merge_by_same_storefront_road(ctx: Context, proceed):
    """
    合并同一门前道路的停车场
    """
    sql = """
        select s_nid, e_nid from nav_link
        where st_intersects(geom, %s)
            and kind = 8      -- 其它道路
            and form !~ '34'  -- 辅路
            -- and form !~ '52'  -- 内部路
            -- and from ~ '82'   -- 门前路
    """

    def get_nids(geom: str) -> set[str]:
        ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [f"SRID=4326;{geom}"])
        nids = {nid for nid_pair in ret for nid in nid_pair}
        return nids

    def merge(parks: list[VerifiedPolygon]):
        face2nids = [(x.face_id, get_nids(x.geom.wkt)) for x in parks]
        face2nids = [(k, v) for k, v in face2nids if v]
        if not face2nids:
            return

        face2nids = {k: v for k, v in face2nids}
        nid2faces = [(face_id, nid) for face_id, nids in face2nids.items() for nid in nids]
        nid2faces = linq.group_by(nid2faces, key=lambda x: x[1], value=lambda x: x[0])

        face2parks = {x.face_id: x for x in parks}
        merged_nids = union_find.merge_sets(list(face2nids.values()))
        for nid_set in merged_nids:
            face_ids = {face_id for nid in nid_set for face_id in nid2faces[nid]}
            merged_parks = [face2parks[face_id] for face_id in face_ids]

            assert len(merged_parks) >= 1
            if len(merged_parks) == 1:
                continue

            merged_park = _merge_verified_polygons(merged_parks)
            tag = [
                ",".join(str(x) for x in merged_park.prime_ids),
                merged_park.face_id,
                "merge_by_same_storefront_road",
                ",".join(nid_set),
            ]
            ctx.tags["merge_by_same_storefront_road"].append(tag)
            yield merged_park

    ctx.verifieds = _replace_verified_polygons(ctx.verifieds, merge(ctx.verifieds))
    proceed()


# PART-3 保存结果信息


def save_task_info(ctx: Context, proceed, file_path: Path):
    """
    保存任务信息
    """
    missing = sum(1 for p in ctx.primes if p.status == "missing")
    pending = sum(1 for p in ctx.primes if p.status == "pending")
    ready = sum(1 for p in ctx.primes if p.status == "ready")
    row = [ctx.task_id, missing, pending, ready, len(ctx.verifieds)]
    tsv.write_tsv(file_path, [row], mode="a")

    proceed()


def save_prime_summary(ctx: Context, proceed, file_path: Path):
    rows = [(ctx.task_id, prime.strategy_id, prime.status, prime.is_reuse, len(ctx.verifieds)) for prime in ctx.primes]
    tsv.write_tsv(file_path, rows, mode="a")
    proceed()


def save_prime(ctx: Context, proceed, save_dir: Path):
    """
    保存原始面及观察点相关信息
    """
    for prime in ctx.primes:
        save_path = utils.ensure_path(save_dir / prime.status / f"{ctx.task_id}-{prime.strategy_id}.json")
        utils.write_json(save_path, prime.to_dict())

    proceed()


def save_verified(ctx: Context, proceed, file_path: Path):
    """
    保存核实面结果
    """
    rows = [[ctx.task_id, ",".join(str(x) for x in p.prime_ids), p.face_id, p.geom.wkt] for p in ctx.verifieds]
    tsv.write_tsv(file_path, rows, mode="a")

    proceed()


def save_segment(ctx: Context, proceed, file_path: Path):
    rows = [[p.strategy_id, s.segment_id, s.conclusion] for p in ctx.primes for s in p.segments]
    tsv.write_tsv(file_path, rows, mode="a")

    proceed()


def save_tags(save_dir: Path):
    """
    保存调试信息
    """
    cleaned_files = set()

    def pipe(ctx: Context, proceed):
        for key, rows in ctx.tags.items():
            debug_path = save_dir / f"debug.{key}.tsv"
            if key not in cleaned_files:
                utils.ensure_path(debug_path, cleanup=True)
                cleaned_files.add(key)

            rows = [[ctx.task_id, *row] for row in rows]
            tsv.write_tsv(debug_path, rows, mode="a")

        proceed()

    return pipe


# helpers:


def _merge_verified_polygons(items: list[VerifiedPolygon]):
    union_geom = shapely.unary_union([p.geom for p in items])
    union_geom = union_geom.simplify(0.01 * METER)
    return VerifiedPolygon(
        prime_ids=list({x for p in items for x in p.prime_ids}),
        prev_face_ids=list({p.face_id for p in items}),
        geom=union_geom,
    )


def _replace_verified_polygons(total: list[VerifiedPolygon], new: Iterable[VerifiedPolygon]):
    new_items = list(new)
    to_remove_face_ids = {x for p in new_items for x in p.prev_face_ids}
    items = [x for x in total if x.face_id not in to_remove_face_ids]
    return items + new_items


def _get_continuous_range(conclusions: list[int], expected: int):
    start_pointer = -1
    for pointer, conclusion in enumerate(conclusions):
        if conclusion == expected:
            if start_pointer < 0:
                start_pointer = pointer
        else:
            if start_pointer < 0:
                continue
            else:
                yield start_pointer, pointer
                start_pointer = -1

    if start_pointer >= 0:
        yield start_pointer, len(conclusions)


def _trim_sideline(sideline: LineString, expected_buffer: float, eps: float):
    sideline = geometric.trim_linestring(sideline, -0.5, expected_buffer)
    trim_buffer = expected_buffer + eps
    sideline = geometric.trim_linestring_for_buffer(sideline, trim_buffer)
    sideline = geometric.trim_linestring_for_buffer(sideline, -trim_buffer)
    return sideline


def _get_clip_infos(baseline: LineString, sideline: LineString, segments: list[Segment], max_angle: float):
    turning_points = list(verify.get_turning_points(baseline, max_angle=max_angle))
    turning_dists = sorted(sideline.project(Point(p)) for p in turning_points)

    segment_pairs = [(sideline.project(s.geom), s) for s in segments if geometric.get_foot_point(s.geom, sideline)]
    segment_pairs.sort(key=lambda x: x[0])

    segments = [s for _, s in segment_pairs]
    segment_dists = [d for d, _ in segment_pairs]
    segment_dist_pairs = [(a, b) for a, b in zip(segment_dists, segment_dists[1:])]

    segment_clip_dists = []
    i_turning = 0
    for a, b in segment_dist_pairs:
        turning_dist = turning_dists[i_turning] if i_turning < len(turning_dists) else math.inf
        if b < turning_dist:
            segment_clip_dists.append((a + b) / 2)
        else:
            segment_clip_dists.append(turning_dist)
            i_turning += 1

    segment_clip_dists = [0, *segment_clip_dists, sideline.length]
    segment_ranges = [(a, b) for a, b in zip(segment_clip_dists, segment_clip_dists[1:])]
    assert len(segment_ranges) == len(segments), f"segment_ranges ({len(segment_ranges)}) != segments ({len(segments)})"

    clip_infos = [ClipInfo(segment=s, range=(a, b)) for s, (a, b) in zip(segments, segment_ranges) if abs(b - a) > EPS]
    return clip_infos


def _merge_clip_ranges(infos: list[ClipInfo]):
    # 根据核实结论保留有效的段落：删除无效段落，并合并连续的有效段落
    current_a, current_b = -1.0, -1.0
    for info in infos:
        conclusion, (a, b) = info.conclusion, info.range
        if conclusion == CONCLUSION_YES:
            if current_a < 0:
                current_a = a

            current_b = b
        elif current_a >= 0 and current_b >= 0:
            yield current_a, current_b
            current_a, current_b = -1.0, -1.0

    if current_a >= 0 and current_b >= 0:
        yield current_a, current_b


# 停车场数据模型构建


@dataclass
class ConclusionItem:
    """
    核实结论项
    """

    current_id: int  # strategy_id
    segment_id: str
    segment_point: str
    image_name: str
    batch: str
    conclusion: int
    reuse_id: int = field(default=-1)  # strategy_id

    @property
    def has_image(self):
        """
        是否有图片
        """
        return self.image_name is not None

    @property
    def is_reuse(self):
        """
        是否可以复用
        """
        return self.reuse_id > 0


def _merge_conclusions(images: list[Image]):
    """
    结论汇总
    """

    def step1(results: list[int]):
        counter = collections.Counter(results)
        max_freq = max(counter.values())
        most_freq_conclusions = [k for k, v in counter.items() if v == max_freq]
        if len(most_freq_conclusions) == 1:
            return most_freq_conclusions[0]

        if CONCLUSION_YES in most_freq_conclusions:
            return CONCLUSION_YES

        if CONCLUSION_NOT_SURE in most_freq_conclusions:
            return CONCLUSION_NOT_SURE

        return CONCLUSION_NO

    def step2(results: list[int], prev_result: int):
        if prev_result != CONCLUSION_YES and CONCLUSION_YES in results:
            return CONCLUSION_NOT_SURE

        return prev_result

    def get_retry_number(batch: str):
        # 缺图
        if not batch:
            return -1

        # 第 1 批次，没有重试
        if "_retry" not in batch:
            return 0

        # 第 1 此重试，不带数字
        if "_retry_" not in batch:
            return 1

        # 多次重试，末尾会带数字
        return int(batch.split("_")[-1])

    def has_missing(imgs: list[Image]) -> bool:
        return not imgs or any(x.is_empty for x in imgs)

    def has_pending(imgs: list[Image]) -> bool:
        return any(not x.is_empty and x.conclusion == CONCLUSION_UNKNOWN for x in imgs)

    # 筛出最后一次重试的结果
    if not images:
        return CONCLUSION_PENDING

    images = linq.group_by(images, key=lambda x: get_retry_number(x.batch))
    _, images = max(images.items(), key=lambda x: x[0])

    if has_missing(images):
        return CONCLUSION_MISSING

    if has_pending(images):
        return CONCLUSION_PENDING

    conclusions = [x.conclusion for x in images]
    if not conclusions:
        return CONCLUSION_UNKNOWN

    if len(conclusions) == 1:
        return conclusions[0]

    conclusion1 = step1(conclusions)
    conclusion2 = step2(conclusions, conclusion1)
    return conclusion2


def _normalize_conclusion(c: str):
    if c == "1":
        return CONCLUSION_YES
    elif c == "2":
        return CONCLUSION_NO
    elif c == "3" or c == "" or c == "10":  # 10 表示 '交叉验证不一致'
        return CONCLUSION_NOT_SURE
    else:
        return CONCLUSION_UNKNOWN


def _try_replace_with_reuse(prime_id: int, items: list[ConclusionItem], batch_image: str):
    # 一张图都没有，可能是被判定为重用的面，也可能是全下载失败的
    may_reuse = all(not c.has_image for c in items)
    if may_reuse:
        reuse_items = _get_reuse_conclusions_by_strategy_id(prime_id, batch_image)
        if reuse_items:
            return reuse_items

    return items


def get_prime_by_id(prime_id: int, batch_image: str):
    """
    获取策略id对应的所有原始面
    """
    prime_conclusions = _get_conclusions_by_prime_id(prime_id, batch_image)
    prime_conclusions = _try_replace_with_reuse(prime_id, prime_conclusions, batch_image)
    polygon_info = _get_polygon_info_by_strategy_id(prime_id)
    if not polygon_info:
        # 这里是几乎不可能发生的，除非有不法分子手动删库，理论上应该加断言直接报错
        return

    segment_conclusions = linq.group_by(prime_conclusions, key=lambda x: (x.segment_id, x.segment_point))
    segments = [
        Segment(
            segment_id=seg_id,
            geom=wkt.loads(point),
            images=[Image(image_id=c.image_name, batch=c.batch, conclusion=c.conclusion) for c in conclusions],
        )
        for (seg_id, point), conclusions in segment_conclusions.items()
    ]
    baseline, geom = polygon_info
    is_reuse = all(c.is_reuse for c in prime_conclusions)
    return PrimePolygon(
        strategy_id=prime_id,
        baseline=wkt.loads(baseline),
        geom=wkt.loads(geom),
        is_reuse=is_reuse,
        segments=segments,
    )


def get_task_id_by_strategy_id(strategy_id: int) -> int:
    sql = """
        select task_id from park_storefront_strategy
        where id = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [strategy_id])
    return ret[0]


def _get_polygon_info_by_strategy_id(strategy_id: int):
    sql = """
        select st_astext(baseline), st_astext(geom)
        from park_storefront_strategy
        where id = %s
    """
    ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql, [strategy_id])
    return ret


def _get_conclusions_by_prime_id(prime_id: int, batch_image: str):
    sql = """
        with t as (
            select jsonb_array_elements_text(result->'segment_ids') as seg_id
            from park_storefront_strategy
            where id = %s and step = 'PRIME'
        )
        select distinct
            t.seg_id,
            st_astext(a.view_point),
            b.image_name,
            c.batch_id,
            c.conclusion
        from t
        inner join park_storefront_segment a on t.seg_id = a.segment_id
        left join park_storefront_image b on t.seg_id = b.segment_id and b.batch = %s
        left join park_storefront_verify c on b.image_name = c.image_id and c.batch_id = %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [prime_id, batch_image, batch_image])
    return [
        ConclusionItem(
            current_id=prime_id,
            segment_id=segment_id,
            segment_point=segment_point,
            image_name=image_name,
            batch=batch,
            conclusion=_normalize_conclusion(conclusion),
        )
        for segment_id, segment_point, image_name, batch, conclusion in ret
    ]


def _get_reuse_conclusions_by_strategy_id(strategy_id: int, batch_image: str):
    sql = """
        -- 对于可复用的面，取出其对应的复用面
        with t1 as (
            select
                strategy_id as curr_id,
                cast(jsonb_array_elements_text(result->'b_ids') as integer) as reuse_id
            from park_storefront_strategy_diff
            where strategy_id = %s and status = 'VERIFY_DIFFED_REUSE'
        ),
        -- 取出复用面的观察点 id
        t2 as (
            select t1.curr_id, t1.reuse_id, jsonb_array_elements_text(result->'segment_ids') as seg_id
            from t1
            inner join park_storefront_strategy a on t1.reuse_id = a.id
        )
        -- 查询这些观察点 id 对应的图片信息
        select distinct 
            t2.curr_id, 
            t2.reuse_id, 
            t2.seg_id,
            st_astext(a.view_point),
            b.image_name, 
            c.batch_id,
            c.conclusion
        from t2
        inner join park_storefront_segment a on t2.seg_id = a.segment_id
        left join park_storefront_image b on t2.seg_id = b.segment_id and b.batch = %s
        left join park_storefront_verify c on b.image_name = c.image_id and c.batch_id = %s
        order by t2.curr_id
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [strategy_id, batch_image, batch_image])
    return [
        ConclusionItem(
            current_id=current_id,
            reuse_id=reuse_id,
            segment_id=segment_id,
            segment_point=segment_point,
            image_name=image_name,
            batch=batch,
            conclusion=_normalize_conclusion(conclusion),
        )
        for current_id, reuse_id, segment_id, segment_point, image_name, batch, conclusion in ret
    ]


# 流程组合

flow_pipe = pipeline.Pipeline(
    clip_by_verify,
    # partial(merge_by_nearby, max_distance=6 * METER),
    # merge_by_same_storefront_road,
)


def process(task: tuple[int, str]):
    """
    处理任务，为多线程任务调用
    """
    prime_id, batch_image = task
    prime = get_prime_by_id(prime_id, batch_image)
    primes = [prime]
    task_id = get_task_id_by_strategy_id(prime_id)
    ctx = Context(task_id=task_id, primes=primes)
    ready = sum(1 for p in primes if p.status == "ready")
    if ready == len(primes):  # 全都准备好了
        flow_pipe(ctx)

    return ctx


def debug():
    """
    使用 json-info 文件进行调试
    """
    json_path = Path("tmp/4455567-1245286.json")
    json_obj = utils.read_json(json_path)
    prime = PrimePolygon.from_dict(json_obj)
    ctx = Context(task_id=0, primes=[prime])
    flow_pipe(ctx)
    for v in ctx.verifieds:
        print(v.geom.wkt)


if __name__ == "__main__":
    debug()
