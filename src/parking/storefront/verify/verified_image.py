"""
下载掘金图片
"""
import logging
import os
import shutil
import sys
import uuid
from dataclasses import dataclass, field
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Literal

import cv2
import numpy as np
from loguru import logger
from shapely import wkt, Point, Polygon
from tqdm import tqdm

from src.aikit.track import sphere
from src.parking.recognition import dbutils
from src.parking.storefront import verify, prime_tag
from src.parking.storefront.utils.geometric import METER
from src.parking.storefront.verify import panorama, zhongyuan, ViewImage
from src.tools import pipeline, tsv, utils, pgsql

VERSION = "1.0.0"  # 初始化提交

IDX_TAG_TYPE = 0
PROD_DIR = Path(os.getenv("STOREFRONT_PROD_DIR", "product"))

desc = pipeline.get_desc()


@dataclass
class ViewPoint:
    """
    观察点
    """

    segment_id: str
    geom: Point
    foot: Point
    side_views: list[ViewImage] = field(default_factory=list)
    tags: list[list[str]] = field(default_factory=list)
    missing_type: str = field(default="")
    missing_reason: str = field(default="")

    @property
    def view_id(self):
        """
        观察点 ID，为了适配 ViewPoint Protocol
        """
        return self.segment_id


@dataclass
class PrimePolygon:
    """
    原始面
    """

    strategy_id: int
    task_id: int
    face_id: str
    geom: Polygon
    view_points: list[ViewPoint]
    reuse: bool = field(default=False)
    result: Literal["full", "partial", "empty"] = field(default="empty")


@desc()
def filter_reuse(prime: PrimePolygon, proceed):
    """
    筛掉 reuse 的面，不需要重新下载
    """
    if prime.reuse:
        return

    proceed()


@desc()
def fetch_panorama_image(prime: PrimePolygon, proceed, nearly_month: int, min_brightness: int):
    """
    获取临街视角的全景图像
    """

    for view_point in prime.view_points:
        if len(view_point.side_views) > 0:
            continue

        for side_image in panorama.get_nearest_image(view_point.geom, buffer=20 * METER, nearly_month=nearly_month):
            view_vec = panorama.to_normalized_vec(side_image.position, view_point.foot)
            lat_range = (-35, 35)
            lon_range = panorama.get_lon_range(side_image.direction, view_vec)
            width, height = panorama.get_projected_image_size(lat_range, lon_range)
            side_image.image = sphere.project_as_cylinder(side_image.image, lat_range, lon_range, width, height)

            image_header = [side_image.track_id, side_image.time, side_image.position.wkt]
            # 图片太暗了，重新选其它时段
            brightness = verify.get_brightness(side_image.image)
            if brightness < min_brightness:
                view_point.tags.append(["panorama_dark", brightness] + image_header)
                continue

            # 高架道路上的轨迹点不要，因为看不见
            link_id = side_image.raw.get("link_id")
            if is_viaduct(link_id):
                view_point.tags.append(["panorama_viaduct", link_id] + image_header)
                continue

            view_point.side_views.append(side_image)
            # 找到时间最近的一张全景后，即可退出，只需要一张就够了
            break

        if len(view_point.side_views) <= 0:
            tag = ["no_panorama_image"]
            view_point.tags.append(tag)

    proceed()


@desc()
def fetch_zhongyuan_image(prime: PrimePolygon, proceed, nearly_month: int, min_brightness: int):
    """
    获取临街视角的众源图像
    """
    for view_point in prime.view_points:
        if len(view_point.side_views) > 0:
            continue

        def filter_tracks(tracks: list[dict], track: dict, image: np.ndarray):
            # 图片太暗了，重新选其它时段
            track_id = track["id"] if track["id"] else track["bos_key"]
            position = Point(track["x"], track["y"])
            image_header = [track_id, track["time"], position.wkt]
            brightness = verify.get_brightness(image)
            if brightness < min_brightness:
                view_point.tags.append(["zhongyuan_dark", brightness] + image_header)
                return [x for x in tracks if x["cdid"] != track["cdid"]]

            # 高架道路上的轨迹点不要，因为看不见
            link_id = track.get("link_id")
            if is_viaduct(link_id):
                view_point.tags.append(["zhongyuan_viaduct", link_id] + image_header)
                return [x for x in tracks if x["cdid"] != track["cdid"]]

            return None

        zhongyuan_images = zhongyuan.get_nearest_zhongyuan_images(
            view_point,
            buffer=10 * METER,
            nearly_month=nearly_month,
            projection_polygon=prime.geom,
            filter_tracks=filter_tracks,
            max_image_num=5,
        )
        if not zhongyuan_images:
            tag = ["no_zhongyuan_image"]
            view_point.tags.append(tag)
            continue

        view_point.side_views.extend(zhongyuan_images)

    proceed()


@desc()
def save_image(prime: PrimePolygon, proceed, save_dir: Path):
    """
    保存核实资料
    """
    utils.ensure_dir(save_dir)
    for vp in prime.view_points:
        for image in vp.side_views:
            image_id = f"{image.image_type}-{prime.task_id}-{prime.face_id}-{vp.view_id}-{image.track_id}"
            cv2.imwrite(str(save_dir / f"{image_id}.jpg"), image.image)

    proceed()


def save_tags(prime: PrimePolygon, proceed, file_path: Path):
    """
    保存警告信息
    """
    for view_point in prime.view_points:
        vp_header = [prime.task_id, prime.strategy_id, prime.face_id, view_point.segment_id]
        if view_point.tags:
            rows = [vp_header + tag for tag in view_point.tags]
            tsv.write_tsv(file_path, rows, mode="a")

    proceed()


@desc()
def set_result(prime: PrimePolygon, proceed):
    """
    根据图片下周的情况，给出下载结论
    """
    if all(x.side_views for x in prime.view_points):
        prime.result = "full"
    elif all(not x.side_views for x in prime.view_points):
        prime.result = "empty"
    else:
        prime.result = "partial"

    proceed()


@desc()
def analyze_missing_image(prime: PrimePolygon, proceed):
    """
    分析图片缺失原因：缺失全景、缺失图片
    """
    for view_point in prime.view_points:
        image_types = {x.image_type for x in view_point.side_views}
        if len(image_types) == 1 and "panorama" in image_types:
            # 有图且全景，符合预期，通过
            continue

        tag_types = [x[IDX_TAG_TYPE] for x in view_point.tags]
        if len(image_types) == 0:
            view_point.missing_type = "no_image"
            if not has_high_level_road(view_point.geom.wkt):
                tag_types.append("no_road")

        elif len(image_types) == 1 and "zhongyuan" in image_types:
            view_point.missing_type = "no_panorama"
        else:
            view_point.missing_type = "unknown"

        seen = set()
        uniq = [x for x in tag_types if not (x in seen or seen.add(x))]
        view_point.missing_reason = ",".join(uniq)

    proceed()


# save pipes


def save_image_result(prime: PrimePolygon, proceed, file_path: Path, result: str):
    """
    保存观察点的图片下载结论
    """
    if prime.result == result:
        for view_point in prime.view_points:
            segment_cols = _get_segment_columns(prime, view_point)
            rows = [[*segment_cols, *image_cols] for image_cols in _get_images_columns(view_point)]
            tsv.write_tsv(file_path, rows, mode="a")

    proceed()


def save_image_missing_reason(prime: PrimePolygon, proceed, file_path: Path):
    for view_point in prime.view_points:
        if view_point.missing_reason:
            segment_cols = _get_segment_columns(prime, view_point)
            rows = [[*segment_cols, view_point.missing_type, view_point.missing_reason]]
            tsv.write_tsv(file_path, rows, mode="a")

    proceed()


def _get_images_columns(view_point: ViewPoint):
    if not view_point.side_views:
        return [["<empty>"]]

    rows = [
        [
            image.image_type,
            image.track_id,
            image.time,
            Point(image.direction).wkt,
            image.position.wkt,
        ]
        for image in view_point.side_views
    ]
    return rows


def _get_segment_columns(prime: PrimePolygon, view_point: ViewPoint):
    row = [
        prime.task_id,
        prime.strategy_id,
        prime.face_id,
        view_point.segment_id,
        view_point.geom.wkt,
        view_point.foot.wkt,
    ]
    return row


def save_image_reuse(prime: PrimePolygon, proceed, file_path: Path):
    """
    保存可重用的观察点信息
    """
    if prime.reuse:
        rows = [[prime.task_id, prime.strategy_id, prime.face_id, vp.segment_id] for vp in prime.view_points]
        tsv.write_tsv(file_path, rows, mode="a")
        # 不需要流入后续
        return

    proceed()


# helpers:


def has_high_level_road(point: str):
    sql = """
        select link_id from nav_link
        where kind < 8 and st_intersects(geom, st_buffer(%s, 10 * 1e-5))
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG_WITH_INDEX, sql, [f"SRID=4326;{point}"])
    return bool(ret)


def is_viaduct(link_id: str) -> bool:
    """
    判断是否是高架道路
    """
    if not link_id:
        return False

    link_info = verify.get_link_info(link_id)
    if link_info is None:
        return False

    kind, viad = link_info
    return kind <= 2 or viad == 1


def get_prime_by_strategy_id(strategy_id: int):
    sql_prime = """
        select id, task_id, face_id, st_astext(geom), result->'segment_ids'
        from park_storefront_strategy
        where id = %s
    """
    sql_segment = """
        select segment_id, st_astext(view_point), st_astext(target_point)
        from park_storefront_segment
        where segment_id in %s
    """
    prime = dbutils.fetch_one(pgsql.POI_CONFIG, sql_prime, [strategy_id])
    strategy_id, task_id, face_id, polygon_wkt, segment_ids = prime
    segments = dbutils.fetch_all(pgsql.POI_CONFIG, sql_segment, [tuple(segment_ids)])
    view_points = [
        ViewPoint(segment_id=segment_id, geom=wkt.loads(geom), foot=wkt.loads(foot))
        for segment_id, geom, foot in segments
    ]
    return PrimePolygon(
        strategy_id=strategy_id, task_id=task_id, face_id=face_id, geom=wkt.loads(polygon_wkt), view_points=view_points
    )


def _init_global_logging(log_path: Path):
    logging.basicConfig(
        level=logging.INFO,
        filename=utils.ensure_path(log_path),
        filemode="a",
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


WORK_DIR = PROD_DIR / "images" / f"image_{uuid.uuid4().hex}"
pipe = pipeline.Pipeline(
    filter_reuse,
    partial(fetch_panorama_image, nearly_month=36, min_brightness=50),
    partial(fetch_zhongyuan_image, nearly_month=12, min_brightness=50),
    partial(save_image, save_dir=WORK_DIR / "side_view"),
    set_result,
    analyze_missing_image,
)
desc.attach(pipe)
desc.disabled = True


def process_prime(strategy_id: int):
    prime = get_prime_by_strategy_id(strategy_id)
    pipe(prime)
    return prime


def execute(prime_ids: list[int], output_dir: Path):
    logger.info(WORK_DIR.name)
    save_pipe = pipeline.Pipeline(
        partial(save_tags, file_path=utils.ensure_path(WORK_DIR / f"warning.tsv")),
        partial(save_image_missing_reason, file_path=utils.ensure_path(WORK_DIR / f"missing_reason.tsv")),
        partial(save_image_reuse, file_path=utils.ensure_path(WORK_DIR / f"image.reuse.tsv")),
        partial(save_image_result, result="full", file_path=utils.ensure_path(WORK_DIR / f"image.full.tsv")),
        partial(save_image_result, result="partial", file_path=utils.ensure_path(WORK_DIR / f"image.partial.tsv")),
        partial(save_image_result, result="empty", file_path=utils.ensure_path(WORK_DIR / f"image.empty.tsv")),
    )
    prime_ids = list(prime_ids)
    with Pool(32) as pool, open(output_dir.parent / "progress_image.log", "w") as f_progress:
        for prime in tqdm(pool.imap_unordered(process_prime, prime_ids), total=len(prime_ids), file=f_progress):
            save_pipe(prime)

    # 移动到 output_dir 下
    shutil.move(WORK_DIR, output_dir)
    logger.info(f"mv: {WORK_DIR} -> {output_dir}")


@logger.catch
def main(batch_dir: Path):
    """
    主函数
    """
    _init_global_logging(WORK_DIR / "verified_image.log")
    position_tags = {"bua_area"}
    pv_tags = {"TOP60", "TOP60-TOP80"}
    prime_ids = [
        x.prime_id
        for x in prime_tag.read_tag_file(batch_dir / prime_tag.FILE_NAME_TAG)
        if x.tags.pv_tag in pv_tags and set(x.tags.area_tags) & position_tags
    ]
    prime_ids = list(prime_ids)
    # 移动到 batch_dir 下
    target_dir = (
        batch_dir / f"image_{'_'.join(x.lower() for x in pv_tags)}_{'_'.join(x.lower() for x in position_tags)}"
    )
    execute(prime_ids, target_dir)


if __name__ == "__main__":
    main(Path(sys.argv[1]))
