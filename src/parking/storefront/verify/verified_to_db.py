"""
核实面入库操作
"""
import sys
from pathlib import Path

import numpy as np
import psycopg2.extras
import shapely.ops
from loguru import logger
from shapely import wkt

from src.parking.recognition import dbutils
from src.parking.storefront import task_flow, prime_tag
from src.parking.storefront.verify import verified_flow
from src.tools import tsv, pgsql

STRATEGY = f"verified.juejin-{verified_flow.VERSION}"


def save_verified(work_dir: Path):
    """
    保存核实面到策略履历库
    """
    idx_strategy_id = 1

    cases = list(tsv.read_tsv(work_dir / "verified.tsv"))
    cases = [(int(task_id), [int(i) for i in ids.split(",")], face_id, geom) for task_id, ids, face_id, geom in cases]
    strategy_ids = [strategy_id for case in cases for strategy_id in case[idx_strategy_id]]
    info_dict = _get_info_by_strategy_id(strategy_ids)

    tag_path = work_dir / prime_tag.FILE_NAME_TAG
    duplicate_ids = (
        {face_id for face_id, diff_tag in tsv.read_tsv(tag_path) if diff_tag == "duplicate"}
        if tag_path.exists()
        else set()
    )

    def get_value(case: tuple):
        task_id, prev_ids, face_id, geom = case
        infos = [info_dict[i] for i in prev_ids]
        if not infos:
            logger.warning(f"{face_id} not found.")
            return None

        prev_face_ids = [info[0] for info in infos]
        baselines = list({info[1] for info in infos})
        if len(baselines) > 1:
            lines = [wkt.loads(line) for line in baselines]
            baseline = shapely.ops.unary_union(lines).wkt
        elif len(baselines) == 1:
            baseline = baselines[0]
        else:
            raise ValueError("baselines < 1")

        status = "DUPLICATE" if face_id in duplicate_ids else ""
        value = [
            task_id,
            prev_face_ids,
            face_id,
            f"SRID=4326;{baseline}",
            f"SRID=4326;{geom}",
            STRATEGY,
            "VERIFIED",
            status,
        ]
        return value

    sql = """
        insert into park_storefront_strategy (task_id, prev_face_ids, face_id, baseline, geom, strategy, step, status)
        values %s
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cur:
        values = [get_value(x) for x in cases]
        values = [x for x in values if x]
        logger.info(f"{len(values)} values.")
        psycopg2.extras.execute_values(cur, sql, values, page_size=1000)

        primes = list(tsv.read_tsv(work_dir / "primes.tsv"))
        idx_status = 2
        total = len(primes)
        primes = [x for x in primes if x[idx_status] == "ready"]
        valid_prime_ids = [int(x[idx_strategy_id]) for x in primes if int(x[-1]) > 0]
        invalid_prime_ids = [int(x[idx_strategy_id]) for x in primes if int(x[-1]) <= 0]
        logger.info(f"valid: {len(valid_prime_ids)}, nothing: {len(invalid_prime_ids)}, pending: {total-len(primes)}")
        task_flow.update_strategy_status(conn, valid_prime_ids, from_status="VERIFYING", to_status="VERIFIED")
        task_flow.update_strategy_status(conn, invalid_prime_ids, from_status="VERIFYING", to_status="VERIFIED.NOTHING")


def save_image(work_dir: Path, batch_image: str):
    """
    保存掘金图片相关信息到数据库
    """
    idx_task_id = 0
    idx_prime_id = 1
    idx_face_id = 2
    idx_segment_id = 3
    idx_image_type = 6
    idx_track_id = 7
    idx_track_time = 8
    idx_direction = -2
    idx_track_point = -1
    sql_image = """
        insert into park_storefront_image
            (segment_id, image_type, track_id, track_time, track_north, track_point, image_name, batch)
        values %s
    """
    sql_strategy = """
        update park_storefront_strategy
        set batch_image = %s
        where id in %s
    """
    image_names = _get_image_names(work_dir)

    def parse_north(direction: str) -> float:
        point = wkt.loads(direction)
        x, y = point.x, point.y
        theta = np.degrees(np.arctan2(x, y))
        return theta if theta >= 0 else theta + 360

    def get_image_name(row: tuple):
        image_type = row[idx_image_type]
        task_id = row[idx_task_id]
        face_id = row[idx_face_id]
        segment_id = row[idx_segment_id]
        track_id = row[idx_track_id]
        name = f"{image_type}-{task_id}-{face_id}-{segment_id}-{track_id}.jpg"
        return name if name in image_names else None

    rows = [row for path in work_dir.glob("image.*.tsv") for row in tsv.read_tsv(path)]
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cur:
        values = [
            (
                x[idx_segment_id],
                x[idx_image_type],
                x[idx_track_id],
                x[idx_track_time],
                parse_north(x[idx_direction]),
                f"SRID=4326;{x[idx_track_point]}",
                _get_stem_from_name(get_image_name(x)),
                batch_image,
            )
            for x in rows
            if x[idx_image_type] != "<empty>"
        ]
        logger.info(f"{len(values)} values.")
        psycopg2.extras.execute_values(cur, sql_image, values)

        prime_ids = {x[idx_prime_id] for x in rows}
        logger.info(f"{len(prime_ids)} prime polygons.")
        pgsql.execute(conn, sql_strategy, [batch_image, tuple(prime_ids)])
        task_flow.update_strategy_status(conn, prime_ids, from_status="", to_status="VERIFYING")
        conn.commit()


def _get_info_by_strategy_id(strategy_ids: list[int]):
    sql = """
        select id, face_id, st_astext(baseline) from park_storefront_strategy
        where id in %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(strategy_ids)])
    return {strategy_id: (face_id, line) for strategy_id, face_id, line in ret}


def _get_image_names(image_dir: Path) -> set[str]:
    side_dir = image_dir / "side_view"
    if side_dir.exists():
        return {x.name for x in side_dir.glob("*.jpg")}

    side_path = image_dir / "side_view.txt"
    if side_path.exists():
        return {x[0] for x in tsv.read_tsv(side_path)}

    raise FileNotFoundError(f"not found any images in '{image_dir}'")


def _get_stem_from_name(name: str) -> str:
    if name and "." in name:
        return name[: name.rindex(".")]
    else:
        return name


@logger.catch
def main(mode: str, file_path: Path):
    """
    主函数
    """
    if mode == "verified":
        save_verified(file_path)
    else:
        raise ValueError(f"'{mode}' not supported")


if __name__ == "__main__":
    main(sys.argv[1], Path(sys.argv[2]))
