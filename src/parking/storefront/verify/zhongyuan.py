"""
输出门前停车场附近的众源照片，用于人工作业。
"""
from typing import Optional, Callable

import cv2
import numpy as np
from loguru import logger
from shapely import Point, Polygon

from src.aikit import satellite_imagery
from src.aikit.track import track_point
from src.parking.storefront import verify
from src.parking.storefront.verify import ViewImage, ViewPoint, CACHE_DIR


def get_nearest_zhongyuan_images(
    viewpoint: ViewPoint,
    buffer: float,
    nearly_month: int,
    projection_polygon: Polygon,
    filter_tracks: Callable[[list[dict], dict, np.ndarray], Optional[list[dict]]],
    max_image_num: int = 5,
) -> Optional[list[ViewImage]]:
    """
    获取距离给定点 point 附近的众源影像，搜索半径为 buffer，搜索时间范围为最近 nearly_month 个月。
    :param viewpoint: 给定的目标点
    :param buffer: 搜索空间范围
    :param nearly_month: 搜索时间范围
    :param filter_tracks: 过滤器函数，输入参数是轨迹点信息和图片，若符合条件，则返回 None，否则返回过滤后的轨迹候选点列表
    :param projection_polygon: 投影面
    :param max_image_num: 最大获取的众源图片数
    :return: 众源影像列表，如果没有找到则返回 None
    """

    def filter_track_direction(track_points: list[dict], vp: ViewPoint):
        """
        按照方向筛选轨迹
        """
        target_direction = np.array([vp.foot.x - vp.geom.x, vp.foot.y - vp.geom.y])
        # 轨迹方向
        track_directions = [verify.get_direction(x["north"]) for x in track_points]
        # 方向正确
        # 点乘筛选夹角小于等于正负90度的方向
        magnitude_a = np.linalg.norm(target_direction)
        dots = [np.dot(target_direction, x) / magnitude_a for x in track_directions]
        cross_product = [np.cross(target_direction, x) / magnitude_a for x in track_directions]
        # 叉乘确保在左边
        foot_tracks = [np.array([vp.foot.x - p["x"], vp.foot.y - p["y"]]) for p in track_points]
        magnitude_vps = [np.linalg.norm(x) for x in foot_tracks]
        result_tracks = []
        for i in range(len(track_points)):
            # 确保真实轨迹点到目标点和轨迹方向的夹角小于90度
            dots_vp = np.dot(foot_tracks[i], track_directions[i]) / magnitude_vps[i]
            vp_tracks = [np.array([p["x"] - vp.geom.x, p["y"] - vp.geom.y]) for p in track_points]
            cross_vptracks = [np.cross(x, target_direction) for x in vp_tracks]
            # 保证轨迹点在目标观察点右边
            if dots[i] >= 0 and cross_product[i] > 0 and dots_vp > 0.05 and cross_vptracks[i] >= 0:
                result_tracks.append(track_points[i])
            elif dots[i] >= 0 and cross_product[i] > 0 and dots_vp > 0.7:
                result_tracks.append(track_points[i])
        return result_tracks

    def clip_image(track, image):
        # 裁剪图片的右半部分，计算保留的比例
        track_direction = verify.get_direction(track["north"])
        foot_tracks = [viewpoint.foot.x - track["x"], viewpoint.foot.y - track["y"]]
        magnitude_vps = np.linalg.norm(foot_tracks)
        dots_vp = np.dot(foot_tracks, track_direction) / magnitude_vps
        remain_ratio = dots_vp / 2 + 0.5
        remain_ratio = np.clip(remain_ratio, 0, 1)
        width = image.shape[1]
        right_half = image[:, int(width * (1 - remain_ratio)) :]
        return right_half

    def get_images_by_tracks(bounds, i: int, now_image_num: int):
        tracks = list(track_point.get_track_points(bounds, track_point.one_month(i), track_point.SRC_ZHONGYUAN))
        tracks = [x for x in tracks if x["has_pic"]]
        result_images = []
        if len(tracks) > 0:
            tracks = filter_track_direction(tracks, viewpoint)
            for track in tracks:
                track_id = track["id"] if track["id"] else track["bos_key"]
                position = Point(track["x"], track["y"])
                pic_url = track["pic_url"]
                pic_path = CACHE_DIR / "zhongyuan" / f"{track_id}.jpg"
                if not pic_path.exists() or not satellite_imagery._is_jpg(pic_path):
                    try:
                        satellite_imagery._download(pic_url, pic_path, timeout=10)
                    except Exception as e:
                        logger.error(f"[download-zhongyuan] url='{pic_url}', error={e}")
                        continue
                image = cv2.imread(str(pic_path))
                if image is None:
                    continue
                right_half = clip_image(track, image)
                if right_half is None:
                    continue
                tracks_no = filter_tracks(tracks, track, image)
                if tracks_no is not None:
                    continue

                result_images.append(
                    ViewImage(
                        image_type="zhongyuan",
                        track_id=track_id,
                        time=track["time"],
                        direction=verify.get_direction(track["north"]),
                        position=position,
                        image=right_half,
                    )
                )
                if len(result_images) + now_image_num >= max_image_num:
                    return result_images
        return result_images

    point = viewpoint.geom
    target_area = projection_polygon.intersection(point.buffer(1.5 * buffer))
    bounds_inside = target_area.bounds
    target_area = point.buffer(buffer).difference(projection_polygon)
    bounds_other = target_area.bounds
    result_images = []
    for i in range(1, nearly_month + 1):
        # 优先获取靠近道路内侧的观察点影像
        get_images = get_images_by_tracks(bounds_inside, i, len(result_images))
        if len(get_images) > 0:
            result_images.extend(get_images)
        if len(result_images) >= max_image_num:
            return result_images
        if len(result_images) == 0:
            get_images = get_images_by_tracks(bounds_other, i, len(result_images))
            if len(get_images) > 0:
                result_images.extend(get_images)
            if len(result_images) >= max_image_num:
                return result_images
    return result_images
