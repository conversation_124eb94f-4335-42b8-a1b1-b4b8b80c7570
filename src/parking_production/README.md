# 纯手工停车场面制作流程

全流程代码分为两个部分：POI 交接流程和此处的后处理流程。POI
交接流程分为[面聚类](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/map-poi/spatial-attribute/blob/master/spatial_attribute/operator/park/parking_grid/run_parking_grid.sh)
和[面挂接&修形](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/map-poi/spatial-attribute/blob/master/spatial_attribute/operator/park/parking_grid/park_relate_operator/start_shell.sh)
两个部分，此处代码用于处理交接代码的输出。

此处代码所操作的数据储存于 `query.PARK_PRODUCTION` 中。

## 1. 入库

> `import_to_db_from_tsv.py`

将 `park_relate_operator` 算子产出的结果（`get_show_area` tsv 文件）入 pg 库的 `query.PARK_PRODUCTION`
表，以获得空间索引查询的能力，及为后续处理做准备。

## 2. 关联策略

### 2.1 旧关联初始化：填充 bid 字段

> `fill_bid_field.py`

#### 2.1.1 处理 `related`

若 `relate_type == 'related'`，则直接将 `relate_bids` 的第一项赋值给 `bid`；

#### 2.1.2 处理 `related_multi`

若 `relate_type == 'related_multi'`，则从 `relate_bids` 中挑选出 pv 最大的 bid，赋值给 `bid`，下面是获取到达 pv 的方法：

```shell
# 先 `ls` 列出最新的（文件名是`年月日.csv`），再 `get` 下载单个文件，这个目录存了半年的，很多数据。
afsshell --username=map-dataop --password=map-dataop ls afs://aries.afs.baidu.com:9902/user/map-de-gzhxy/mis/poi_pv/month
```

### 2.2 新关联关系计算：POI 找面

> `park_poi_relation.py`

P.S. 与母库去重，去掉已关联的 bid（TODO: 这里存在一个问题：已关联 bid，其边框可能在后续处理中失效，这样其又变为了未关联，此处是否需要迭代至收敛？）

## 2.3 非路侧小碎片处理

> `repair_non_roadside_fragment.py`

## 2.4 压盖处理

> `repair_overlap_beautify.py`

## 2.5 POI 关联多个面

> `one_poi_multi_park.py`

## 2.6 导出 tsv 文件

> `export_area.py`

> `export_show_area.py`

## 2.7 生成地下停车场

> `underground_park.py`

NOTE: 前面的流程均不处理地下停车场，地下停车场单独处理，流程完全独立。
