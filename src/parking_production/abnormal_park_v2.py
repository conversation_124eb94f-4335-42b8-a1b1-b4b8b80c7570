"""
异常面过滤策略 v2，我们把从 POI 交接过来的策略称作 v1，之后 AOI 团队的策略从 v2 开始
"""
import math

from shapely import wkt

from src.parking_production import query
from src.tools import pgsql, utils


def get_aoi_overlap_info(wkt_str: str):
    """
    获取并计算位于 AOI 内的停车场的压盖情况信息（用于判定该停车场是否有效）
    :return: [face_id, std_tag, ioa, iou, aoi_geom]
    """
    sql_query_aoi = """
        select face_id, st_astext(geom) from blu_face
        where aoi_level = 2
            and src != 'SD'
            and st_intersects(geom, st_geomfromtext(%s, 4326));
    """
    conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
    # noinspection PyBroadException
    try:
        geom_gcj = query.mc2gcj(wkt_str)
        ret = pgsql.fetch_all(conn, sql_query_aoi, [geom_gcj])
    except:
        conn.rollback()
        return None

    if not ret:
        return None

    face_id, aoi_geom, ioa = max(
        ((face_id, aoi_geom, utils.calc_ioa(aoi_geom, geom_gcj)) for face_id, aoi_geom in ret),
        key=lambda x: x[-1],
    )
    sql_std_tag = """
        select std_tag from blu_face_poi
        where face_id = %s;
    """
    ret = pgsql.fetch_one(conn, sql_std_tag, [face_id])
    if not ret:
        return None

    std_tag = ret[0]
    iou = utils.calc_iou(aoi_geom, geom_gcj)
    return face_id, std_tag, ioa, iou, aoi_geom


def is_abnormal_aoi_overlap_info(overlap_info: tuple):
    """
    判断停车场是否属于 AOI 内的小型停车场（v3）
    :param overlap_info: 使用 get_aoi_overlap_info() 返回的信息：(face_id, std_tag, ioa, iou, aoi_geom)
    """
    std_tag = overlap_info[1]
    ioa = float(overlap_info[2])
    iou = float(overlap_info[3])
    if std_tag not in (
        "公司企业;公司",
        "公司企业;园区",
        "房地产;住宅区",
        "房地产",
        "休闲娱乐;农家院",
        "房地产;写字楼",
        "教育培训;其他",
    ):
        return False

    if ioa < 0.85:
        return False

    if iou > 0.3:
        return False

    return True


def is_too_short_roadside_park(wkt_str: str, show_tag: str, shape_type: str):
    """
    判断停车场是否属于短小的路侧停车位（v1）
    """
    if show_tag not in ("路侧停车位", "临时停车点") or shape_type not in (
        "roadbased_luce",
        "roadbased_luce_v2",
    ):
        return False

    # noinspection PyBroadException
    try:
        geom = wkt.loads(wkt_str)
        area = geom.area
    except:
        return False

    if area >= 10 * 40:
        return False

    return True


def get_shape_info(wkt_str: str):
    """
    计算停车场的形状信息
    :return: (f_original, f_open, f_close, f_diff), (r_open, r_close)
    """
    # noinspection PyBroadException
    try:
        geom = wkt.loads(wkt_str)
        area_original: float = geom.area
        f_original = calc_shape_factor(geom)

        geom_open = geom.buffer(-4).buffer(4)
        area_open: float = geom_open.area
        f_open = calc_shape_factor(geom_open)

        geom_close = geom.buffer(5).buffer(-5)
        area_close: float = geom_close.area
        f_close = calc_shape_factor(geom_close)

        f_diff: float = max(abs(f_original - f_open), abs(f_original - f_close))

        r_open = area_open / area_original
        r_close = area_original / area_close
    except:
        return None

    factors = f_original, f_open, f_close, f_diff
    ratios = r_open, r_close
    return factors, ratios


def is_abnormal_shape_info(shape_info: tuple, tag: str, shape_type: str):
    """
    判断停车场的形状是否异常（v1）
    :param shape_info: 使用 get_shape_info() 返回的信息：(f_original, f_open, f_close, f_diff), (r_open, r_close)
    :param tag: 停车场标签，取值如：park, luce, park_luce, aoi, ...
    :param shape_type: 形状来源，取值如：roadbased, concave, roadbased_luce, roadbased_luce_v2, ...
    """
    (f_original, f_open, f_close, f_diff), (r_open, r_close) = shape_info

    if tag != "park":
        return False

    if shape_type not in ("roadbased", "concave"):
        return False

    is_abnormal = f_original < 0.3 or (r_open > 0.7 and r_close > 0.8 and f_diff > 0.2)
    return is_abnormal


def calc_shape_factor(geom):
    """
    计算形状因子
    """
    return 4 * math.pi * geom.area / geom.length**2
