# !/usr/bin/env python3
"""
导出数据库到 tsv 文件，用于上线。
1. 不上地下停车场；
2. 只上 related, related_v2 类型的停车场（单关联）。
"""
from dataclasses import field, dataclass
from pathlib import Path
from typing import Callable

from src.parking_production import query
from src.tools import pgsql, tsv, utils, pipeline

desc = pipeline.get_desc()

TAG_INDEX = 5
RELATE_TYPE_INDEX = 6
SHAPE_TYPE_INDEX = 7
GEOM_INDEX = -2
ONLINE_GEOM_INDEX = -1


@dataclass
class Context:
    """
    上下文
    """

    region: str
    items: list = field(default_factory=list)


@desc()
def query_valid_area(ctx: Context, proceed):
    """
    查询有效的停车场面
    """
    query_sql = f"""
        select 
            a.bid, a.face_id, b.name, b.std_tag, b.show_tag,                  -- 基础信息
            a.tag, a.relate_type, a.shape_type,                               -- 后处理信息
            st_astext(b.geometry), st_astext(a.area),                         -- 几何信息
            st_astext(c.area)                                                 -- 线上信息
        from {query.PARK_PRODUCTION} a
        inner join poi b on a.bid = b.bid
        inner join {query.PARK_ONLINE} c on a.bid = c.bid
        where a.region = %s
            and b.show_tag != '地下停车场'
            and a.bid != ''
            and a.relate_type in ('related', 'related_v2')
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ctx.items = pgsql.fetch_all(conn, query_sql, [ctx.region])

    proceed()


# noinspection DuplicatedCode
def output_new_to_tsv(get_path: Callable[[str], Path]):
    """
    输出新增数据
    """

    @desc("output new to tsv")
    def pipe(ctx: Context, proceed):
        new = [x[:ONLINE_GEOM_INDEX] for x in ctx.items if not x[ONLINE_GEOM_INDEX]]
        save_path = get_path(ctx.region)
        tsv.write_tsv(save_path, new)
        print(f"{len(new)=}")
        proceed()

    return pipe


def output_change_to_tsv(get_path: Callable[[str], Path]):
    """
    输出变化数据
    """

    @desc("output change to tsv")
    def pipe(ctx: Context, proceed):
        change = [add_iou_column(x) for x in ctx.items if x[ONLINE_GEOM_INDEX]]
        save_path = get_path(ctx.region)
        tsv.write_tsv(save_path, change)
        print(f"{len(change)=}")
        proceed()

    return pipe


# helper:


def add_iou_column(row: list):
    """
    计算并添加本批次面与线上面的 iou 到列末尾
    """
    current = row[-2]
    prev = row[-1]
    iou = utils.calc_iou(current, prev)
    return [*row, f"{iou:.3f}"]


def main():
    """
    主函数
    """
    save_dir = Path("./products") / f"area_{query.today_str()}"
    save_dir.mkdir(exist_ok=True, parents=True)

    pipe = pipeline.Pipeline(
        query_valid_area,
        output_new_to_tsv(lambda r: save_dir / f"new_{r}.tsv"),
        output_change_to_tsv(lambda r: save_dir / f"change_{r}.tsv"),
    )
    desc.attach(pipe)

    regions = query.get_regions()
    for region in regions:
        print(f"region: {region}")
        pipe(Context(region=region))


if __name__ == "__main__":
    main()
