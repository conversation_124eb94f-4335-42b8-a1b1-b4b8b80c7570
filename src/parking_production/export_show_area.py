"""
导出数据库到 tsv 文件，用于上线。
TODO: 把这里的所有策略，全部分解出来，并将执行结果持久化到数据库中，此处文件仅做纯导出，不带任何数据处理。
"""
from dataclasses import field, dataclass
from pathlib import Path
from typing import Callable, Iterable

from shapely import wkt
from tqdm import tqdm

from src.parking_production import query, abnormal_park_v2
from src.seg_post_process import smooth_point_string
from src.tools import pgsql, tsv, utils, pipeline

desc = pipeline.get_desc()

TAG_INDEX = 5
SHOW_TAG_INDEX = 4
SHAPE_TYPE_INDEX = 7
BID_INDEX = 0
ID_INDEX = 1
GEOM_INDEX = -3
ONLINE_ID_INDEX = -2
ONLINE_GEOM_INDEX = -1


@dataclass
class Context:
    """
    上下文
    """

    region: str
    items: list = field(default_factory=list)
    new_items: list = field(default_factory=list)
    covered_items: list = field(default_factory=list)
    change_items: list = field(default_factory=list)
    retire_bids: list[str] = field(default_factory=list)


@desc()
def query_valid_show_area(ctx: Context, proceed):
    """
    查询有效的停车场面
    """
    query_sql = f"""
        select 
            a.bid, a.face_id, b.name, b.std_tag, b.show_tag,   -- 基础信息
            a.tag, a.relate_type, a.shape_type,                -- 后处理信息
            st_astext(b.geometry), st_astext(a.geom),          -- 几何信息
            cover_show_area_id, st_astext(c.show_area)         -- 线上信息
        from {query.PARK_PRODUCTION} a
        inner join poi b on a.bid = b.bid
        inner join {query.PARK_ONLINE} c on a.bid = c.bid
        where a.region = %s
            and a.bid != ''
            and a.relate_type in ('related', 'related_v2', 'related_multi')
            and a.result->'non_roadside_fragment'->>'error' is null
            and a.result->'overlap'->>'error' is null
            and a.result->'multi_relation'->>'error' is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ctx.items = pgsql.fetch_all(conn, query_sql, [ctx.region])

    proceed()


@desc()
def smooth_non_roadside_park(ctx: Context, proceed):
    """
    平滑非路侧停车场
    """

    def process_one(row: tuple):
        columns: list[any] = list(row)
        tag, geom = columns[TAG_INDEX], columns[GEOM_INDEX]
        if tag != "luce":
            geom = smooth_polygon(geom)

        if not geom:
            return None

        columns[GEOM_INDEX] = geom
        return columns

    smoothed_items = map(process_one, ctx.items)
    ctx.items = [x for x in tqdm(smoothed_items, total=len(ctx.items)) if x]
    proceed()


def filter_invalid_park(history_dir: Path):
    """
    过滤不应该上线的停车场
    :param history_dir: 历史上线过的面文件目录
    """

    def is_valid_v1(row: list):
        """
        内容生态部交接过来的逻辑
        """
        geom_str = row[GEOM_INDEX]
        tag = row[TAG_INDEX]
        shape_type = row[SHAPE_TYPE_INDEX]
        if not geom_str:
            return False

        # noinspection PyBroadException
        try:
            geom = wkt.loads(geom_str)
            convex = geom.convex_hull
            convex_ratio = geom.area / convex.area
        except:
            return False

        if (
            (convex.area > 30000 and convex_ratio < 0.7)
            or (convex.area > 50000 and convex_ratio < 0.8)
            or (convex.area > 70000 and convex_ratio < 0.85)
        ):
            return False
        if tag == "park" and geom.area < 200:
            return False
        if tag == "luce" and shape_type == "concave" and geom.area < 100:
            return False

        return True

    def is_valid_v2(row: list):
        geom_str = row[GEOM_INDEX]
        show_tag = row[SHOW_TAG_INDEX]
        shape_type = row[SHAPE_TYPE_INDEX]
        if abnormal_park_v2.is_too_short_roadside_park(geom_str, show_tag, shape_type):
            return False

        overlap_info = abnormal_park_v2.get_aoi_overlap_info(geom_str)
        if overlap_info and abnormal_park_v2.is_abnormal_aoi_overlap_info(overlap_info):
            return False

        shape_info = abnormal_park_v2.get_shape_info(geom_str)
        tag = row[TAG_INDEX]
        if shape_info and abnormal_park_v2.is_abnormal_shape_info(shape_info, tag, shape_type):
            return False

        return True

    def is_not_down_park(row: list):
        show_tag = row[SHOW_TAG_INDEX]
        return show_tag != "地下停车场"

    def get_bids_from_path(dir_path: Path, file_prefix: str):
        return {row[0] for bids_path in dir_path.glob(f"{file_prefix}*") for row in tsv.read_tsv(bids_path)}

    manual_bids = get_bids_from_path(history_dir / "manual", "park_face_manual_")
    semi_auto_bids = get_bids_from_path(history_dir / "semi_auto", "park_face_semi_auto_")
    non_auto_bids = manual_bids | semi_auto_bids

    def is_not_non_auto_park(row: list):
        return row[BID_INDEX] not in non_auto_bids

    @desc("filter invalid park")
    def pipe(ctx: Context, proceed):
        ctx.items = [
            x
            for x in tqdm(ctx.items)
            if is_valid_v1(x) and is_valid_v2(x) and is_not_down_park(x) and is_not_non_auto_park(x)
        ]
        proceed()

    return pipe


def filter_overlap_online(log_path: Path):
    """
    过滤与线上数据有压盖的数据
    """
    log_path.unlink(missing_ok=True)
    sql = f"""
        select bid, st_astext(show_area) from {query.PARK_ONLINE}
        where st_intersects(st_geomfromtext(%s, 3857), show_area);
    """

    @desc("filter overlap online")
    def pipe(ctx: Context, proceed):
        with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
            items = [(x, pgsql.fetch_one(conn, sql, [x[GEOM_INDEX]])) for x in tqdm(ctx.items)]

        ctx.items = [data for data, ret in items if not ret]
        discard_items = [[ctx.region, *data, *ret] for data, ret in items if ret]
        tsv.write_tsv(log_path, discard_items, mode="a")

        print(f"overlap: {len(discard_items)}")
        proceed()

    return pipe


@desc()
def split_products(ctx: Context, proceed):
    """
    将数据分为新增、变更、下线数据集
    """

    # 新增流程（1/2）：新增 show_area，新增 cover_show_area_id
    ctx.new_items = [x for x in ctx.items if not x[ONLINE_GEOM_INDEX]]
    # 新增流程（2/2）：对于这次新增的 show_area，若包含了其它 bid，则要更新这些 bid 的 cover_show_area_id，
    # 但不必把 show_area 给它们。
    new_face_ids = {x[ID_INDEX] for x in ctx.new_items}
    ctx.covered_items = get_new_covered_items(new_face_ids)

    # 更新流程（1/2）：更新 show_area，更新 cover_show_area_id
    ctx.change_items = [x for x in ctx.items if x[ONLINE_GEOM_INDEX]]
    # 更新流程（2/2）：对于线上已有 show_area 的数据，若更新，则意味着要下线之前的 cover_show_area_id
    to_retire_bids = get_retire_bids(ctx.change_items)
    # 而对于本次需要新增和更新的 bid，则无需下线，否则都会被覆盖，为保证更少的推送次数，应从下线 bid 中减去这部分 bid
    to_new_bids = {x[BID_INDEX] for x in ctx.new_items}
    covered_bids = {x[0] for x in ctx.covered_items}
    to_change_bids = {x[BID_INDEX] for x in ctx.change_items}
    ctx.retire_bids = to_retire_bids - to_new_bids - covered_bids - to_change_bids
    proceed()


def output_tsv(get_data: Callable[[Context], list], save_path: Path):
    """
    输出数据
    """
    save_path.unlink(missing_ok=True)

    @desc("output to tsv")
    def pipe(ctx: Context, proceed):
        rows = get_data(ctx)
        data = [[ctx.region, *row] for row in rows]
        tsv.write_tsv(save_path, data, mode="a")
        print(f"{save_path.stem}: {len(data)}")
        proceed()

    return pipe

    # helper:


def get_new_covered_items(new_face_ids: Iterable):
    """
    获取本次新增面关联的 cover_show_area_id 数据
    """
    sql = f"""
        with relate_multi as (
            select unnest(relate_bids) as relate_bid, face_id, bid from {query.PARK_PRODUCTION}
            where face_id in %s and array_length(relate_bids, 1) > 1
        )
        select r.relate_bid, r.face_id, r.bid
        from relate_multi r inner join {query.PARK_ONLINE} p on r.relate_bid = p.bid
        where r.relate_bid != r.bid and p.show_area is null;
    """
    if not new_face_ids:
        return []

    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        return pgsql.fetch_all(conn, sql, [tuple(new_face_ids)])


def get_retire_bids(change_items: list):
    """
    获取因本次变更而要下线的数据
    """
    change_online_ids = {
        x[ONLINE_ID_INDEX]
        for x in change_items
        # 其实有 show_area 必有 cover_show_area_id，这里 if 检查，纯属对线上乱七八糟的数据不放心
        if x[ONLINE_ID_INDEX]
    }
    # 用这次将要变更的 cover_show_area_id 查询波及的 bid，用于下线其原有的 cover_show_area_id
    sql = f"""
        select bid from {query.PARK_ONLINE}
        where cover_show_area_id in %s;
    """
    if not change_online_ids:
        return set()

    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql, [tuple(change_online_ids)])

    to_retire_bids = {x[0] for x in ret}
    return to_retire_bids


def smooth_polygon(geom: str):
    """
    平滑边框
    """
    # noinspection PyBroadException
    try:
        if not wkt.loads(geom).is_valid:
            return None

        if geom.startswith("POLYGON"):
            return smooth_point_string.smooth_polygon(geom)
        elif geom.startswith("MULTIPOLYGON"):
            return smooth_point_string.smooth_multi_polygon(geom)
    except:
        return None


def add_iou_column(row: list):
    """
    计算并添加本批次面与线上面的 iou 到列末尾
    """
    current = row[-2]
    prev = row[-1]
    iou = utils.calc_iou(current, prev)
    return [*row, f"{iou:.3f}"]


def main():
    """
    主函数
    """
    history_dir = Path("/home/<USER>/dingping/parking/park_face")

    timestamp = query.today_str()
    save_dir = Path(f"./products/show_area_{timestamp}")
    save_dir.mkdir(exist_ok=True, parents=True)

    overlap_log_path = save_dir / "overlap_online.tsv"

    pipe = pipeline.Pipeline(
        query_valid_show_area,
        smooth_non_roadside_park,
        filter_invalid_park(history_dir),
        filter_overlap_online(overlap_log_path),
        split_products,
        output_tsv(lambda ctx: ctx.new_items, save_dir / f"new.tsv"),
        output_tsv(lambda ctx: ctx.covered_items, save_dir / f"new_covered.tsv"),
        output_tsv(lambda ctx: ctx.change_items, save_dir / f"change.tsv"),
        output_tsv(lambda ctx: [[x] for x in ctx.retire_bids], save_dir / f"change_retire.tsv"),
    )
    desc.attach(pipe)

    regions = query.get_regions()
    for region in regions:
        print(f"region: {region}")
        pipe(Context(region=region))


if __name__ == "__main__":
    main()
