"""
旧关联初始化：填充 bid 字段
"""
import math
from dataclasses import dataclass
from pathlib import Path

import click
from tqdm import tqdm

from src.parking_production import query
from src.tools import pgsql, tsv, pipeline, utils

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    上下文
    """

    region: str


@desc()
def resolve_related(ctx: Context, proceed):
    """
    related 场景，直接取 relate_bids 第一个。
    因为直接全库赋值可能非常慢，所以先取 region 列表，再分批赋值，这样可以看到进度，缓解等待焦虑。
    """
    update_bid_sql = f"""
        update {query.PARK_PRODUCTION}
        set bid = relate_bids[1]
        where region = %s
            and relate_type = 'related'
            and bid = '';
    """

    conn = pgsql.get_connection(pgsql.POI_CONFIG)
    pgsql.execute(conn, update_bid_sql, [ctx.region])
    proceed()


def resolve_related_multi(pv_file: Path):
    """
    related_multi 场景，取 pv 大的 POI 来挂接
    """

    pv_dict = {
        x[0]: int(x[1]) for x in tsv.read_tsv(pv_file, skip_header=True, splitter=",")
    }
    query_sql = f"""
        select id, relate_bids from {query.PARK_PRODUCTION}
        where region = %s
            and relate_type = 'related_multi'
            and bid = '';
    """
    update_bid_sql = f"""
        update {query.PARK_PRODUCTION} set bid = %s
        where id = %s;
    """

    @desc("resolve_related_multi")
    def pipe(ctx: Context, proceed):
        with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
            ret = pgsql.fetch_all(conn, query_sql, [ctx.region])

        batch_size = 1000
        total = math.ceil(len(ret) / batch_size)
        for items in tqdm(utils.for_batch(ret, batch_size), total=total):
            values = [
                (
                    max(
                        ((bid, pv_dict.get(bid, 0)) for bid in relate_bids),
                        key=lambda x: x[1],
                    )[0],
                    park_id,
                )
                for park_id, relate_bids in items
                if relate_bids
            ]

            conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
            pgsql.execute_many(conn, update_bid_sql, values)

        proceed()

    return pipe


def main(pv_file: Path):
    """
    主函数
    """
    pipe = pipeline.Pipeline(
        resolve_related,
        resolve_related_multi(pv_file),
    )
    desc.attach(pipe)

    regions = query.get_regions()
    for region in regions:
        print(f"region: {region}")
        pipe(Context(region))


@click.command()
@click.argument("pv_file", type=click.Path(exists=True, dir_okay=False, path_type=Path))
def for_cmd(pv_file: Path):
    """
    命令行调用
    """
    main(pv_file)


if __name__ == "__main__":
    for_cmd()
