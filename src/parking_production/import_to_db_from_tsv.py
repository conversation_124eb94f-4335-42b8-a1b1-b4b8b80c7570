"""
将 park_relate_operator 算子产出的结果（get_show_area tsv 文件）入 pg 库
的 query.PARK_PRODUCTION 表，以获得空间索引查询的能力，及为后续处理做准备。
"""

import hashlib
import json
from pathlib import Path

import click
from tqdm import tqdm

from src.parking_production import query
from src.tools import tsv, pgsql, utils


def import_to_db(file_path: Path, region: str, batch: str):
    """
    导入数据到数据库
    """
    insert_sql = f"""
        INSERT INTO
            {query.PARK_PRODUCTION} (
                face_id, area, show_area, geom, tag, shape_type, relate_type, relate_bids, region, batch
            )
        VALUES (
            %(face_id)s,
            ST_GeomFromText(%(area)s, 3857),
            ST_GeomFromText(%(show_area)s, 3857),
            ST_GeomFromText(%(show_area)s, 3857),
            %(tag)s,
            %(shape_type)s,
            %(relate_type)s,
            %(relate_bids)s,
            %(region)s,
            %(batch)s
        );
    """

    for values in tqdm(utils.for_batch(tsv.read_tsv(file_path), 1000), desc=region):
        json_values = [json.loads(value) for _, value in values]
        insert_values = [
            {
                "face_id": hashlib.md5(x["show_area"].encode()).hexdigest(),
                "area": x["area"],
                "show_area": x["show_area"],
                "tag": x["tag"],
                "shape_type": x["show_type"],
                "relate_type": x["relate_type"],
                "relate_bids": x["releated_bid"].split("|"),  # 原始代码中的错别字
                "region": region,
                "batch": batch,
            }
            for x in json_values
        ]
        conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
        # TODO: 该为一条语句插入多个值，或许会更快：VALUES (...), (...), ...
        pgsql.execute_many(conn, insert_sql, insert_values)


def main(data_dir: Path):
    """
    主函数
    """
    batch = query.today_str()
    print(f"batch: '{batch}', import to db from '{data_dir}'")
    for file_path in data_dir.rglob("*/get_show_area"):
        region = file_path.parent.stem
        import_to_db(file_path, region, batch)

    print("completed!")


@click.command()
@click.argument(
    "data_dir", type=click.Path(exists=True, file_okay=False, path_type=Path)
)
def for_cmd(data_dir: Path):
    """
    命令行调用
    """
    main(data_dir)


if __name__ == "__main__":
    for_cmd()
