# !/usr/bin/env python3
"""
POI 关联多个面：选取面积最大的与之挂接
"""
from dataclasses import dataclass, field

from src.parking_production import query
from src.tools import pgsql, linq, pipeline

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    上下文
    """

    region: str
    bids: set[str] = field(default_factory=set)
    related_cases: list[tuple] = field(default_factory=list)


@desc()
def clear_history_result(ctx: Context, proceed):
    """
    清理历史结果
    """
    query.clear_result("multi_relation", ctx.region)
    proceed()


@desc()
def query_multi_related_poi(ctx: Context, proceed):
    """
    查询待处理的多关联 POI
    """
    sql = f"""
        SELECT bid FROM {query.PARK_PRODUCTION}
        WHERE region = %s
            and bid != ''
            and relate_type in ('related', 'related_v2', 'related_multi')
            and result->'non_roadside_fragment'->>'error' is null
            and result->'overlap'->>'error' is null
        GROUP BY bid HAVING COUNT(*) > 1;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_SLAVER_CONFIG)
    ret = pgsql.fetch_all(conn, sql, [ctx.region])
    ctx.bids = {x[0] for x in ret}
    proceed()


@desc()
def query_related_cases(ctx: Context, proceed):
    """
    查询相关的面
    """
    sql = f"""
        SELECT id, bid, st_area(show_area) FROM {query.PARK_PRODUCTION}
        WHERE bid in %s
            and relate_type in ('related', 'related_v2', 'related_multi')
            and result->'non_roadside_fragment'->>'error' is null
            and result->'overlap'->>'error' is null;
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_SLAVER_CONFIG)
    ctx.related_cases = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])
    proceed()


@desc()
def calc_and_update_result(ctx: Context, proceed):
    """
    计算并更新 pk 结果
    """
    sql = f"""
        UPDATE {query.PARK_PRODUCTION}
        SET result = jsonb_set(result, '{{multi_relation}}', '{"error": "one poi to many park"}') 
        WHERE id in %s;
    """
    cases_group = linq.group_by(ctx.related_cases, key=lambda x: x[1])
    to_discard_ids = [
        x[0]
        for item in cases_group.values()
        for x in sorted(item, key=lambda x: x[2], reverse=True)[1:]
    ]
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    pgsql.execute(conn, sql, [tuple(to_discard_ids)])
    proceed()


def main():
    """
    主函数
    """
    regions = query.get_regions()
    pipe = pipeline.Pipeline(
        clear_history_result,
        query_multi_related_poi,
        pipeline.print_desc(lambda x: f"{len(x.bids)=}"),
        query_related_cases,
        pipeline.print_desc(lambda x: f"{len(x.related_cases)=}"),
        calc_and_update_result,
    )
    desc.attach(pipe)
    for region in regions:
        print(f"region: {region}")
        pipe(Context(region))


if __name__ == "__main__":
    main()
