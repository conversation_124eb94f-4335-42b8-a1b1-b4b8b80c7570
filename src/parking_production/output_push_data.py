"""
输出上线数据，根据工艺提供的 bid 列表，从 blu_face 库中查询出对应的上线数据：
1. geom：作为 show_area 和 area，注意：geom 可以覆盖当前的 show_area（因为是人工做的，可信度比当前停车场技术高），
   但不必覆盖 area，因为 area 就应该是那个鬼样子，面太好可能导致导航算法漂移。
2. 计算车位规模：st_area(geom) / 2.5 * 6，不要覆盖线上的车位规模，因为线上的车位规模是模型计算的，更准一点。
3. 下线这次被覆盖 show_area 的其它 cover_show_area_id。
"""
from dataclasses import dataclass, field
from pathlib import Path

import click

from src.parking_production import query
from src.tools import tsv, pgsql, pipeline

DATA_DIR = Path("/home/<USER>/dingping/parking/park_face")

AREA_PER_CAR = 2.5 * 6

SRC_MANUAL = "manual"
SRC_SEMI_AUTO = "semi_auto"
SRC_AUTO = "auto"

desc = pipeline.get_desc()


@dataclass
class Context:
    """
    上下文
    """

    bids: set[str] = field(default_factory=set)
    items: list = field(default_factory=list)
    retire_items: list = field(default_factory=list)


def read_bids(file_path: Path):
    """
    读取文件，获取 bid 列表
    """

    @desc("read_bids")
    def pipe(ctx: Context, proceed):
        ctx.bids = {x[0] for x in tsv.read_tsv(file_path)}
        proceed()

    return pipe


def subtract_bids(src: str):
    """
    从文件读取的 bid 列表中减去已存在的 bid（指定源），得到待上线数据
    """

    @desc("subtract_bids")
    def pipe(ctx: Context, proceed):
        src_dir = DATA_DIR / src
        files = src_dir.glob(f"park_face_{src}_*.txt")
        minuend_bids = {x[0] for file in files for x in tsv.read_tsv(file)}
        ctx.bids -= minuend_bids
        proceed()

    return pipe


@desc()
def query_release_items(ctx: Context, proceed):
    """
    查询出待上线数据，包括 geom，并计算出车位规模
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = f"""
            select b.poi_bid, a.face_id, round(st_area(gcj2mc(a.geom)) / {AREA_PER_CAR}), st_astext(gcj2mc(a.geom))
            from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where a.src != 'SD' and b.poi_bid in %s;
        """
        ctx.items = pgsql.fetch_all(conn, sql, [tuple(ctx.bids)])

    proceed()


@desc()
def query_retire_items(ctx: Context, proceed):
    """
    获取需要下线的 cover_show_area_id。
    show_area 为空但有 cover_show_area_id 的 POI，其 cover_show_area_id 对应的 show_area 在别的 POI 上，
    可能被下线了，故需要一并下线。
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        has_face_bids = {x[0] for x in ctx.items}

        sql = f"""
            select distinct cover_show_area_id from {query.PARK_ONLINE}
            where cover_show_area_id != '' and bid in %s;
        """
        ret = pgsql.fetch_all(conn, sql, [tuple(has_face_bids)])
        face_ids = [x[0] for x in ret]

        sql = f"""
            select bid, cover_show_area_id from {query.PARK_ONLINE}
            where cover_show_area_id in %s;
        """
        retire_items = pgsql.fetch_all(conn, sql, [tuple(face_ids)])
        # 去除有 show_area 数据的，其会在上线时自动覆盖原有的 cover_show_area_id，不必单独做下线处理
        ctx.retire_items = [
            (bid, face_id) for bid, face_id in retire_items if bid not in has_face_bids
        ]

    proceed()


def output_missing_bids(save_path: Path):
    """
    输出没有查询到框的 bid，发给 PM，问他们是否符合预期
    """

    @desc("output_missing_bids")
    def pipe(ctx: Context, proceed):
        valid_bids = {x[0] for x in ctx.items}
        missing_bids = ctx.bids - valid_bids
        tsv.write_tsv(save_path, [[x] for x in missing_bids])
        proceed()

    return pipe


def output_show_area_release(save_path: Path):
    """
    输出待上线的 show_area 数据，不会与线上 diff，直接覆盖线上
    """

    @desc("output_show_area_release")
    def pipe(ctx: Context, proceed):
        items = [[bid, geom, face_id] for bid, face_id, _num, geom in ctx.items]
        tsv.write_tsv(save_path, items)
        proceed()

    return pipe


def output_show_area_retire(save_path: Path):
    """
    输出需要下线的 cover_show_area_id 数据
    """

    @desc("output_show_area_retire")
    def pipe(ctx: Context, proceed):
        items = [[bid, "", face_id] for bid, face_id in ctx.retire_items]
        tsv.write_tsv(save_path, items)
        proceed()

    return pipe


def output_area_release(save_path: Path):
    """
    输出待上线的 area 数据，会与线上 diff，线上存在就不上了
    """

    @desc("output_show_area_release")
    def pipe(ctx: Context, proceed):
        with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
            has_face_bids = {x[0] for x in ctx.items}
            sql = f"""
                select bid from {query.PARK_ONLINE}
                where bid in %s and area is not null;
            """
            ret = pgsql.fetch_all(conn, sql, [tuple(has_face_bids)])

        online_bids = {x[0] for x in ret}
        items = [
            [bid, geom, face_id]
            for bid, face_id, _num, geom in ctx.items
            if bid not in online_bids
        ]
        tsv.write_tsv(save_path, items)
        proceed()

    return pipe


def output_car_number_release(save_path: Path):
    """
    输出待上线的 car_number 数据，会与线上 diff，线上存在就不上了
    """

    @desc("output_car_number_release")
    def pipe(ctx: Context, proceed):
        with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
            has_face_bids = {x[0] for x in ctx.items}
            sql = f"""
                select bid from {query.PARK_ONLINE}
                where bid in %s
                    and vague_parking_lot != 0
                    and vague_parking_lot is not null;
            """
            ret = pgsql.fetch_all(conn, sql, [tuple(has_face_bids)])

        online_bids = {x[0] for x in ret}
        items = [
            [bid, int(num)]
            for bid, _face_id, num, _geom in ctx.items
            if bid not in online_bids
        ]
        tsv.write_tsv(save_path, items)
        proceed()

    return pipe


@click.command()
@click.argument(
    "file_path", type=click.Path(exists=True, readable=True, path_type=Path)
)
def main(file_path: Path):
    """
    主函数
    """
    save_dir = DATA_DIR / SRC_SEMI_AUTO / "output"
    save_dir.mkdir(parents=True, exist_ok=True)

    debug_dir = DATA_DIR / SRC_SEMI_AUTO / "debug"
    debug_dir.mkdir(parents=True, exist_ok=True)

    pipe = pipeline.Pipeline(
        read_bids(file_path),
        pipeline.print_desc(lambda x: f"{len(x.bids)=}"),
        subtract_bids(SRC_MANUAL),
        pipeline.print_desc(lambda x: f"{len(x.bids)=}"),
        query_release_items,
        pipeline.print_desc(lambda x: f"{len(x.items)=}"),
        query_retire_items,
        pipeline.print_desc(lambda x: f"{len(x.retire_items)=}"),
        output_missing_bids(debug_dir / f"missing_{file_path.name}"),
        output_show_area_retire(save_dir / f"retire_show_area_{file_path.stem}.tsv"),
        output_show_area_release(save_dir / f"push_show_area_{file_path.stem}.tsv"),
        output_area_release(save_dir / f"push_area_{file_path.stem}.tsv"),
        output_car_number_release(save_dir / f"push_car_number_{file_path.stem}.tsv"),
    )

    ctx = Context()
    pipe(ctx)


if __name__ == "__main__":
    main()
