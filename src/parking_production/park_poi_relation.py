"""
关联停车场面到附近的 POI，基于计算出的特征（见 class Feature），判定规则见函数：eval_feature()
"""
import math
from dataclasses import dataclass, field
from multiprocessing.pool import Pool
from pathlib import Path

import click
import shapely
from shapely import Point, wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.aoi_cleanup_strategy import road_tools
from src.parking_production import query
from src.tools import pgsql, pipeline, tsv, linq

QUERY_BUFFER = 50
METER_GCJ = 1e-5

desc = pipeline.get_desc()
get_connection = pgsql.get_connection_ttl


@dataclass
class Feature:
    """
    关联特征，用于决定是否可关联
    """

    # POI 与停车场面是否同属一个 AOI 内，-1 表示 POI 没有命中 AOI，>= 0 表示停车场面与 AOI 的 ioa
    with_aoi: float = -1
    # POI 与停车场面是否有内部路组连通，-1 表示周围没有内部路组/停车场面与内部路组不相交，>= 0 表示 POI 与内部路组的最短距离
    with_inner_road_group: float = math.inf
    # POI 与停车场面是否有外部路组连通，-1 表示周围没有外部路/停车场面与外部路不相交，>= 0 表示 POI 与外部路的最短距离
    # 连通的外部路之间不可有分叉点。
    with_outer_road: float = math.inf
    # POI 与停车场面的连线跨几个建筑
    cross_building: float = 0
    # POI 与停车场面的最短距离
    face_distance: float = math.inf


@dataclass
class ParkFace:
    """
    停车场面
    """

    face_id: str
    relate_type: str
    tag: str
    geom: BaseGeometry


@dataclass
class Intermediate:
    """
    中间结果，用于传递上下文
    """

    poi: Point = None
    std_tag: str = ""
    show_tag: str = ""
    blu_faces: list[tuple[str, BaseGeometry]] = field(default_factory=list)
    bud_faces: list[tuple[str, BaseGeometry]] = field(default_factory=list)
    inner_roads: list[tuple[str, str, str, BaseGeometry]] = field(default_factory=list)
    outer_roads: list[tuple[str, str, str, BaseGeometry]] = field(default_factory=list)


@dataclass
class Context:
    """
    上下文
    """

    bid: str
    intermediate: Intermediate = field(default_factory=Intermediate)
    candidates: list[tuple[ParkFace, Feature]] = field(default_factory=list)
    result: tuple[ParkFace, Feature] = None
    error: str = ""


# pipes:


@desc()
def query_poi(ctx: Context, proceed):
    """
    查询 POI
    """
    sql = """
        select std_tag, show_tag, st_astext(geometry) from poi
        where bid = %s
    """
    conn = get_connection(pgsql.POI_SLAVER_CONFIG)
    ret = pgsql.fetch_one(conn, sql, [ctx.bid])
    if not ret:
        ctx.error = "not-found:poi"
        return

    std_tag, show_tag, geom = ret
    ctx.intermediate.std_tag = std_tag
    ctx.intermediate.show_tag = show_tag
    ctx.intermediate.poi = wkt.loads(geom)
    proceed()


@desc()
def query_blu_face(ctx: Context, proceed):
    """
    查询 AOI
    """
    sql = """
        select face_id, st_astext(geom) from blu_face
        where aoi_level in (2, 3) and src != 'SD' and st_contains(geom, st_geomfromtext(%s, 4326))
    """
    conn = get_connection(pgsql.BACK_CONFIG)
    ret = pgsql.fetch_all(conn, sql, [ctx.intermediate.poi.wkt])
    ctx.intermediate.blu_faces = [(face_id, wkt.loads(geom)) for face_id, geom in ret]
    proceed()


@desc()
def query_bud_face(ctx: Context, proceed):
    """
    查询建筑物面
    """
    sql = """
        select face_id, st_astext(geom) from bud_face
        where st_intersects(st_buffer(st_geomfromtext(%s, 4326), %s), geom)
    """
    conn = get_connection(pgsql.BACK_CONFIG)
    ret = pgsql.fetch_all(
        conn, sql, [ctx.intermediate.poi.wkt, QUERY_BUFFER * METER_GCJ]
    )
    ctx.intermediate.bud_faces = [(face_id, wkt.loads(geom)) for face_id, geom in ret]
    proceed()


@desc()
def query_inner_road(ctx: Context, proceed):
    """
    查询内部路
    """
    sql = """
        select link_id, s_nid, e_nid, st_astext(geom) from nav_link
        where form = '52' and st_intersects(st_buffer(st_geomfromtext(%s, 4326), %s), geom)
    """
    conn = get_connection(pgsql.ROAD_CONFIG)
    ret = pgsql.fetch_all(
        conn, sql, [ctx.intermediate.poi.wkt, QUERY_BUFFER * METER_GCJ]
    )
    ctx.intermediate.inner_roads = [
        (link_id, s_nid, e_nid, wkt.loads(geom)) for link_id, s_nid, e_nid, geom in ret
    ]
    proceed()


@desc()
def query_outer_road(ctx: Context, proceed):
    """
    查询外部路
    """
    sql = """
        select link_id, s_nid, e_nid, st_astext(geom) from nav_link
        where kind <= 7 and st_intersects(st_buffer(st_geomfromtext(%s, 4326), %s), geom)
    """
    conn = get_connection(pgsql.ROAD_CONFIG)
    ret = pgsql.fetch_all(
        conn, sql, [ctx.intermediate.poi.wkt, QUERY_BUFFER * METER_GCJ]
    )
    ctx.intermediate.outer_roads = [
        (link_id, s_nid, e_nid, wkt.loads(geom)) for link_id, s_nid, e_nid, geom in ret
    ]
    proceed()


@desc()
def query_park_face(ctx: Context, proceed):
    """
    查询停车场面，并填充 ctx.candidates
    """
    sql = f"""
        select id, relate_type, tag, st_astext(show_area) from {query.PARK_PRODUCTION}
        where relate_type not in ('related', 'related_multi')  -- 已在前置流程解决，不归该流程管辖
            and bid = ''                                       -- 已经关联的面不能作为候选项了
            and st_intersects(st_buffer(st_geomfromtext(%s, 3857), %s), show_area)
    """
    conn = get_connection(pgsql.POI_SLAVER_CONFIG)
    geom_mc = query.gcj2mc(ctx.intermediate.poi.wkt)
    ret = pgsql.fetch_all(conn, sql, [geom_mc, QUERY_BUFFER])
    park_faces = [
        ParkFace(face_id, relate_type, tag, wkt.loads(query.mc2gcj(geom)))
        for face_id, relate_type, tag, geom in ret
    ]
    for park_face in park_faces:
        if not park_face.geom.is_valid:
            park_face.geom = park_face.geom.buffer(0)

    park_faces = [x for x in park_faces if x.geom.is_valid]
    ctx.candidates = [(x, Feature()) for x in park_faces]
    if not ctx.candidates:
        ctx.error = "not-found:park_face_in_query_buffer"
        return

    proceed()


@desc()
def calc_with_aoi(ctx: Context, proceed):
    """
    计算停车场面是否和 POI 同属一个 AOI 内
    """
    blu_faces = ctx.intermediate.blu_faces
    if not blu_faces:
        return proceed()

    for park, feature in ctx.candidates:
        max_ioa = max(calc_ioa(geom, park.geom) for face_id, geom in blu_faces)
        # 这里的 AOI 都已包含了 POI，故只需要检查 AOI 是否包含 park_face 即可。
        feature.with_aoi = max_ioa

    proceed()


@desc()
def calc_with_inner_road_group(ctx: Context, proceed):
    """
    计算是否有内部路组连通停车场面和 POI
    """
    inner_roads = ctx.intermediate.inner_roads
    poi = ctx.intermediate.poi

    road_ids = [(link_id, s_nid, e_nid) for link_id, s_nid, e_nid, geom in inner_roads]
    road_dict = {link_id: geom for link_id, s_nid, e_nid, geom in inner_roads}
    road_group_ids = list(road_tools.search_inner_road_group(road_ids))
    road_groups = [
        shapely.unary_union([road_dict[link_id] for link_id in link_ids])
        for link_ids in road_group_ids
    ]
    for park, feature in ctx.candidates:
        adjacent_road_groups = [x for x in road_groups if park.geom.intersects(x)]
        if not adjacent_road_groups:
            continue

        min_distance = min(poi.distance(x) for x in adjacent_road_groups)
        feature.with_inner_road_group = min_distance / METER_GCJ

    proceed()


@desc()
def calc_with_outer_road(ctx: Context, proceed):
    """
    计算是否有外部路连通停车场面和 POI
    """
    poi = ctx.intermediate.poi
    outer_roads = ctx.intermediate.outer_roads
    for park, feature in ctx.candidates:
        adjacent_roads = [
            geom
            for link_id, s_nid, e_nid, geom in outer_roads
            if park.geom.intersects(geom)
        ]
        if not adjacent_roads:
            continue

        min_distance = min(poi.distance(x) for x in adjacent_roads)
        feature.with_outer_road = min_distance / METER_GCJ

    proceed()


@desc()
def calc_face_distance(ctx: Context, proceed):
    """
    计算 POI 距离停车场面的最小距离
    """
    poi = ctx.intermediate.poi
    for park, feature in ctx.candidates:
        feature.face_distance = poi.distance(park.geom) / METER_GCJ

    proceed()


@desc()
def calc_cross_building(ctx: Context, proceed):
    """
    计算 POI 与停车场面是否被建筑隔开
    """
    poi = ctx.intermediate.poi
    for park, feature in ctx.candidates:
        shortest_line = shapely.shortest_line(poi, park.geom)
        feature.cross_building = sum(
            (0.5 if geom.contains(poi) else 1) if geom.intersects(shortest_line) else 0
            for face_id, geom in ctx.intermediate.bud_faces
        )

    proceed()


@desc()
def calc_result(ctx: Context, proceed):
    """
    评估特征值，并生成最终的输出
    """
    ctx.candidates.sort(key=lambda x: x[1].face_distance)
    ret = linq.first_or_default(ctx.candidates, lambda x: eval_feature(x[1]))
    if not ret:
        ctx.error = "recall-failed:no_valid_candidate"
        return proceed()

    ctx.result = ret
    proceed()


# helpers:


def calc_ioa(geom1, geom2):
    """
    计算压盖面积比（intersection over area）：即对于 geom2 而言，其被 geom1 所压盖的面积占自身面积的比例。
    """
    a = geom1.intersection(geom2)

    a = a.area
    b = geom2.area
    return a / b if b > 0 else 0.0


def eval_feature(f: Feature) -> bool:
    """
    CORE: 评估特征值，并最终决策是否可以关联
    """
    if f.with_aoi > 0.5:
        return f.face_distance < 30 and f.cross_building < 1

    if f.with_inner_road_group < 10 or f.with_outer_road < 10:
        return f.cross_building < 0.5

    return f.face_distance < 1


def get_related_bids(bids: set[str]):
    """
    查询已关联的 bid，避免重复处理
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = f"""
            SELECT bid FROM {query.PARK_PRODUCTION}
            WHERE relate_type IN ('related', 'related_multi') AND bid IN %s;
        """
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])
        related_bids = {x[0] for x in ret}
        return related_bids


def get_online_bids():
    """
    NOTE: 这个库每天0点开始同步，大约2点前会同步完成，例行时需要注意本脚本的启动时间。
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = f"""
            select bid from {query.PARK_ONLINE}
            where precise = 1
                and std_tag in ('交通设施;路侧停车位', '交通设施;停车场')
                and show_tag in ('停车场', '地上停车场', '临时停车点', '路侧停车位', '路侧停车场', '门前停车场')
                and show_area is null;
        """
        ret = pgsql.fetch_all(conn, sql)
        online_bids = {x[0] for x in ret}
        return online_bids


def get_bids(done_path: Path):
    """
    获取所有需要处理的 POI bid，单独放在一个函数里，是为了独立的临时 bid 列表可以释放内存
    """
    online_bids = get_online_bids()
    related_bids = get_related_bids(online_bids)
    done_bids = {x[0] for x in tsv.read_tsv(done_path)} if done_path.exists() else set()
    bids = online_bids - related_bids - done_bids

    print(f"{len(bids)=}, {len(online_bids)=}, {len(related_bids)=}, {len(done_bids)=}")
    return list(bids)


PIPE = pipeline.Pipeline(
    query_poi,
    query_park_face,
    query_blu_face,
    query_bud_face,
    query_inner_road,
    query_outer_road,
    calc_face_distance,
    calc_with_aoi,
    calc_with_inner_road_group,
    calc_with_outer_road,
    calc_cross_building,
    calc_result,
)
desc.attach(PIPE)
desc.output = lambda x: None


def execute_once(bid: str) -> Context:
    """
    处理单个 POI，主要用于多进程并行
    """
    ctx = Context(bid)
    PIPE(ctx)
    return ctx


def output_to_db(bids: list[str], result_path: Path):
    """
    导出结果到数据库
    """
    sql = f"""
        UPDATE {query.PARK_PRODUCTION}
        SET bid = %s, relate_bids = %s, relate_type = %s
        WHERE id = %s;
    """
    with Pool(8) as pool:
        for ctx in tqdm(pool.imap(execute_once, bids), total=len(bids)):
            conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
            if ctx.error:
                # 错误要另外记录，因为错误是针对这个 bid 为什么没挂上的，而数据库是针对停车场面的
                error = [ctx.bid, "error", ctx.error]
                tsv.write_tsv(result_path, [error], mode="a")
                continue

            park_face, feature = ctx.result
            args = [ctx.bid, [ctx.bid], "related_v2", park_face.face_id]
            pgsql.execute(conn, sql, args)

            success = [ctx.bid, "success", park_face.face_id]
            tsv.write_tsv(result_path, [success], mode="a")


@click.command()
@click.argument(
    "result_path", type=click.Path(exists=True, dir_okay=False, path_type=Path)
)
def main(result_path: Path):
    """
    主函数：支持【断点恢复】
    """
    bids = get_bids(result_path)
    output_to_db(bids, result_path)


if __name__ == "__main__":
    main()
