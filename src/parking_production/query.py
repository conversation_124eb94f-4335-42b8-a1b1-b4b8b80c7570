# !/usr/bin/env python3
"""
公共函数
"""
from datetime import datetime, timedelta

from src.tools import pgsql


def today_str():
    """
    获取今天的日期字符串，e.g. '20240424'
    """
    return datetime.now().strftime("%Y%m%d")


def yesterday_str():
    """
    获取昨天的日期字符串，e.g. '20240423'
    """
    yesterday = datetime.now() - timedelta(days=1)
    return yesterday.strftime("%Y%m%d")


PARK_PRODUCTION = "park_face_original"
# PARK_ONLINE = f"park_online_data_{yesterday_str()}"
PARK_ONLINE = f"park_online_data"  # 它是个 VIEW，会指向最新的表
FLOW_KEYS = ["non_roadside_fragment", "overlap", "multi_relation"]


def get_regions():
    """
    获取 query.PARK_PRODUCTION.region 的值域
    """
    query_region_sql = f"""
        select distinct region from {PARK_PRODUCTION};
    """
    importance_regions = ["beijing", "shanghai", "guangdong", "jiangsu", "zhejiang"]
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        regions = {x[0] for x in pgsql.fetch_all(conn, query_region_sql)}

    other_regions = list(regions - set(importance_regions))
    other_regions.sort()
    return [*importance_regions, *other_regions]


def gcj2mc(geom: str) -> str:
    """
    将 gcj02 坐标转换为百度墨卡托坐标
    """
    sql = """
        select st_astext(gcj2mc(st_geomfromtext(%s, 4326)));
    """
    conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
    ret = pgsql.fetch_one(conn, sql, [geom])
    return ret[0]


def mc2gcj(geom: str) -> str:
    """
    将百度墨卡托坐标转换为 gcj02 坐标
    """
    sql = """
        select st_astext(mc2gcj(st_geomfromtext(%s, 4326)));
    """
    conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
    ret = pgsql.fetch_one(conn, sql, [geom])
    return ret[0]


def clear_result(current: str, region: str):
    """
    清理 current 工序的成果
    """
    clear_sql = f"""
        update {PARK_PRODUCTION}
        set result = result - '{current}'
        where region = %s
            and result->'{current}' is not null;
    """
    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute(conn, clear_sql, [region])


def reset_geom(current: str, region: str):
    """
    清理 current 工序的结果，并恢复 query.PARK_PRODUCTION.geom 到上一个工序的结果
    """
    index = FLOW_KEYS.index(current)
    if index < 0:
        raise ValueError(f"invalid flow: {current}")

    clear_result(current, region)

    case_sqls = []
    while index > 0:
        index -= 1
        prev = FLOW_KEYS[index]
        when = f"""
            when result->'{prev}'->>'error' is null and result->'{prev}'->>'result' is not null
            then st_geomfromtext(result->'{prev}'->>'result', 3857)
        """
        case_sqls.append(when)

    if case_sqls:
        whens = "".join(case_sqls)
        case_sql = f"""
            case
            {whens}
            else show_area
            end
        """
    else:
        case_sql = "show_area"

    restore_prev_sql = f"""
        update {PARK_PRODUCTION}
        set geom = {case_sql}
        where region = %s;
    """

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute(conn, restore_prev_sql, [region])


def update_geom(current: str, region: str):
    """
    更新 current 工序的结果到 query.PARK_PRODUCTION.geom
    """
    update_sql = f"""
        update {PARK_PRODUCTION}
        set geom = st_geomfromtext(result->'{current}'->>'result', 3857)
        where region = %s
            and result->'{current}'->>'result' is not null
            and result->'{current}'->>'error' is null;
    """

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute(conn, update_sql, [region])
