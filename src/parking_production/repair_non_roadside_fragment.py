"""
停车场面修形：仅针对“非路侧的碎片化场景”。
"""

import math
from dataclasses import dataclass, field
from functools import reduce
from multiprocessing.pool import Pool

from psycopg2.extras import <PERSON>son
from shapely import wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.fix_geom_process import tools
from src.parking_production import query
from src.seg_post_process import smooth_point_string
from src.tools import pgsql, pipeline, utils

METER = 1e-5

get_connection = pgsql.get_connection_ttl


@dataclass
class Context:
    """
    上下文
    """

    bid: str
    geom: BaseGeometry  # 它在 pipe 中的坐标都是 gcj02
    face_id: str
    error: str = None
    result: any = None
    diff_iou: float = math.nan
    area_ratio: float = math.nan
    buildings: list[tuple[str, str]] = field(default_factory=list)
    inner_roads: list[tuple[str, str]] = field(default_factory=list)
    outer_roads: list[tuple] = field(default_factory=list)

    def result_bd09mc_str(self):
        """
        将 ctx.result 导出为可入库的坐标格式
        """
        if not self.result:
            return None

        result_str = self.result if isinstance(self.result, str) else self.result.wkt
        return query.gcj2mc(result_str)


def convert_bd09mc_to_gcj02(ctx: Context, proceed):
    """
    pipe: 转换 bd09mc 坐标到 gcj02
    """
    geom_gcj = query.mc2gcj(ctx.geom.wkt)
    ctx.geom = wkt.loads(geom_gcj)

    proceed()


def filter_by_max_area(max_area: float = math.inf):
    """
    pipe: 面积大小筛选
    """

    def pipe(ctx: Context, proceed):
        if ctx.geom.area > max_area:
            ctx.error = "error: area is too large"
            return

        proceed()

    return pipe


def filter_by_min_area(min_area: float = 0):
    """
    pipe: 面积大小筛选
    """

    def pipe(ctx: Context, proceed):
        if ctx.error:
            return proceed()

        geom = wkt.loads(ctx.result) if isinstance(ctx.result, str) else ctx.result
        if geom.area < min_area:
            ctx.error = "error: repaired area is too small"
            return

        proceed()

    return pipe


def check_result(ctx: Context, proceed):
    """
    检查结果是否是有效的 wkt
    """
    # noinspection PyBroadException
    try:
        wkt.loads(ctx.result)
    except:
        ctx.error = "error: invalid wkt"
        ctx.result = ""

    proceed()


def filter_by_iou(min_iou: float):
    """
    pipe: iou 筛选
    """

    def pipe(ctx: Context, proceed):
        if not ctx.result.startswith("POLYGON") or "EMPTY" in ctx.result:
            return proceed()

        iou = utils.calc_iou(ctx.result, str(ctx.geom))
        ctx.diff_iou = iou
        if iou < min_iou:
            ctx.error = "error: iou too small"
            return

        proceed()

    return pipe


def filter_by_area_ratio(min_area_ratio: float):
    """
    pipe: 压盖面积率筛选
    """

    def pipe(ctx: Context, proceed):
        if not ctx.result.startswith("POLYGON") or "EMPTY" in ctx.result:
            return proceed()

        area_ratio = utils.calc_ioa(ctx.geom, ctx.result)
        ctx.area_ratio = area_ratio
        if area_ratio < min_area_ratio:
            ctx.error = "error: area-ratio too small"
            return

        proceed()

    return pipe


def get_features(ctx: Context, proceed):
    """
    pipe: 查询周边要素
    """
    buffer_geom = str(ctx.geom.buffer(30 * METER))
    ctx.buildings = get_buildings(buffer_geom)
    ctx.inner_roads = get_inner_roads(buffer_geom)
    ctx.outer_roads = get_outer_roads(buffer_geom)
    proceed()


def calc_building_overlap(max_overlap_ratio: float):
    """
    pipe: 计算建筑物压盖，并筛掉压盖过多的 case
    """

    def pipe(ctx: Context, proceed):
        if not ctx.buildings:
            proceed()
            return

        union_buildings = reduce(
            lambda s, x: s.union(x), (geom for _, geom in ctx.buildings)
        )
        free_geom = ctx.geom.difference(union_buildings)
        overlap_ratio = 1 - free_geom.area / ctx.geom.area
        if overlap_ratio > max_overlap_ratio:
            ctx.error = "error: excessively overlapped by buildings"
            return

        proceed()

    return pipe


def calc_min_rect(ctx: Context, proceed):
    """
    pipe: 获取最小外接矩形
    """
    ctx.result = ctx.geom.minimum_rotated_rectangle
    proceed()


def diff_overlap(ctx: Context, proceed):
    """
    pipe: 去除与其它要素的压盖
    """
    for _, geom in ctx.buildings:
        ctx.result = ctx.result.difference(geom)

    for _, direction, kind, lane_l, lane_r, form, geom in ctx.outer_roads:
        line_width = tools.get_road_width(direction, kind, lane_l, lane_r, form)
        line_buffer = line_width * METER / 2
        line = geom.buffer(line_buffer)
        ctx.result = ctx.result.difference(line)

    proceed()


def smooth_parking(opening_buffer: float):
    """
    pipe: 平滑停车场面
    """

    def pipe(ctx: Context, proceed):
        geoms = flat_geom(ctx.result)
        geom = max(geoms, key=lambda x: x.area)
        geom = geom.buffer(-opening_buffer * METER).buffer(opening_buffer * METER)
        if geom.is_empty:
            # TODO: 当 geom.is_empty 时，计算原始 geom 的主轴，再外扩。
            ctx.error = "error: geom is empty after opening operation"
            return
        elif geom.geom_type == "MultiPolygon":
            geom = max(geom.geoms, key=lambda x: x.area)

        ctx.result = smooth_point_string.smooth_polygon(str(geom))
        proceed()

    return pipe


# helpers:


def flat_geom(geom_wkt: str):
    """
    将 MultiPolygon 展开为 Polygon
    """

    def flat(geometry):
        geom_type = geometry.geom_type
        if geom_type == "Polygon":
            yield geometry
        elif geom_type == "MultiPolygon":
            yield from geometry.geoms
        elif geom_type == "GeometryCollection":
            yield from (x for g in geometry.geoms for x in flat(g))

    geom = wkt.loads(geom_wkt) if type(geom_wkt) is str else geom_wkt
    return list(flat(geom))


def get_inner_roads(geom):
    """
    数据库：根据 geom 查询压盖的内部路
    """
    conn = get_connection(pgsql.ROAD_CONFIG)
    sql = """
        select link_id, st_astext(geom) from nav_link
        where form = '52' and st_intersects(st_geomfromtext(%s, 4326), geom);
    """
    return pgsql.fetch_all(conn, sql, [geom])


def get_outer_roads(geom):
    """
    数据库：根据 geom 查询压盖的高等级道路
    """
    conn = get_connection(pgsql.ROAD_CONFIG)
    sql = """
        select link_id, dir, kind, lane_l, lane_r, form, st_astext(geom) from nav_link
        where kind < 8 and st_intersects(st_geomfromtext(%s, 4326), geom);
    """
    ret = pgsql.fetch_all(conn, sql, [geom])
    return [(*x[:-1], wkt.loads(x[-1])) for x in ret]


def get_buildings(geom):
    """
    数据库：根据 geom 查询压盖的建筑物
    """
    conn = get_connection(pgsql.BACK_CONFIG)
    sql = """
        select face_id, st_astext(geom) from bud_face
        where st_intersects(st_geomfromtext(%s, 4326), geom);
    """
    ret = pgsql.fetch_all(conn, sql, [geom])
    return [(face_id, wkt.loads(geom)) for face_id, geom in ret]


PIPE = pipeline.Pipeline(
    convert_bd09mc_to_gcj02,
    filter_by_max_area(max_area=2000 * METER**2),
    get_features,
    calc_building_overlap(max_overlap_ratio=0.8),
    calc_min_rect,
    diff_overlap,
    smooth_parking(opening_buffer=2),
    check_result,
    filter_by_min_area(min_area=2.5 * 6 * METER**2),  # 标准车位面积：2.5m*6m
    filter_by_iou(min_iou=0.2),
    filter_by_area_ratio(min_area_ratio=0.2),
)


def execute_once(item: tuple[str, str, str]):
    """
    修复给定的 geoms
    """
    bid, geom, face_id = item
    ctx = Context(bid, wkt.loads(geom), face_id)
    PIPE(ctx)
    return ctx


def process_region(pool, region: str):
    """
    处理单个 region 内的所有面
    """
    query_sql = f"""
        select bid, st_astext(show_area), id from {query.PARK_PRODUCTION}
        where region = %s
            and tag != 'luce'
            and shape_type = 'concave'
            and bid != ''
            and relate_type in ('related', 'related_v2', 'related_multi');
    """
    update_result_sql = f"""
        update {query.PARK_PRODUCTION}
        set result = jsonb_set(result, '{{non_roadside_fragment}}', %s, true) 
        where id = %s;
    """

    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        geoms = pgsql.fetch_all(conn, query_sql, [region])

    for ctx in tqdm(pool.imap(execute_once, geoms), total=len(geoms)):
        # 保存结果
        value = {
            "result": ctx.result_bd09mc_str(),
            "error": ctx.error if ctx.error else None,
            "feature": [
                "nan" if math.isnan(ctx.diff_iou) else ctx.diff_iou,
                "nan" if math.isnan(ctx.area_ratio) else ctx.area_ratio,
                ctx.geom.area / METER**2,
            ],
        }
        conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
        pgsql.execute(conn, update_result_sql, [Json(value), ctx.face_id])

    print(f"update: {query.PARK_PRODUCTION}.geom field...")
    query.update_geom("non_roadside_fragment", region)


def main():
    """
    主函数：入口点
    """
    regions = query.get_regions()
    with Pool(8) as pool:
        for region in regions:
            print(f"region: {region}")
            process_region(pool, region)

    print("completed!")


if __name__ == "__main__":
    main()
