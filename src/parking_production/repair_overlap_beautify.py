# !/usr/bin/env python3
"""
美化停车场面

author: @chenjie_02
"""
from dataclasses import dataclass, field

import shapely.wkt
from psycopg2.extras import Json
from shapely import Polygon, MultiPolygon, GeometryCollection
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.model.aoi_model import AoiModel
from src.parking_production import query
from src.tools import pipeline, pgsql

SAFE_DISTANCE = 1
SIMPLIFY_TOLERANCE = 1
PRECISION = 1e-15
SINGLE_PARKING_MIN_AREA = 16
PARKING_MIN_AREA = SINGLE_PARKING_MIN_AREA * 3
PARK_TAG_AOI = "aoi"
PARK_TAG_LUCE = "luce"

desc = pipeline.get_desc()


@dataclass
class Park:
    """
    存储停车场信息
    """

    id: int
    bid: str
    tag: str
    recommended_tag: str
    geom: BaseGeometry
    cropped: bool = False
    original_geom: BaseGeometry = None
    iou: float = 0.0
    intersection_area: float = 0.0
    exclusive_area: float = 0.0
    overlap_id: int = -1
    overlap_wkt: str = ""
    overlap_tag: str = ""
    reason: str = ""
    can_process: bool = True


@dataclass
class Context:
    """
    批处理上下文
    """

    region: str
    bids: list[str] = field(default_factory=list)
    parks: list[Park] = field(default_factory=list)
    overlap_parks: dict[int, list[Park]] = field(default_factory=dict)
    error_ids: set[int] = field(default_factory=set)

    def mark_park_error(self, park: Park, reason: str, geom):
        park.reason = reason
        park.can_process = False
        park.geom = geom
        self.error_ids.add(park.id)

    def can_process_park(self, park: Park):
        return park.id not in self.error_ids and park.can_process

    def get_all_overlap_parks(self):
        id_set = set()
        for parks in self.overlap_parks.values():
            for park in parks:
                if park.id not in id_set:
                    yield park
                    id_set.add(park.id)


def make_geom_valid(geom):
    """
    本地轻量化 make valid 方案，防止打爆 pg。
    """
    if not geom:
        return Polygon()

    geom = geom.buffer(0)

    if not geom.is_valid:
        return Polygon()

    if geom.geom_type == "MultiPolygon":
        polygons = []
        for polygon in geom.geoms:
            if make_geom_valid(polygon):
                polygons.append(polygon)
        geom = MultiPolygon(polygons)

    if geom.geom_type == "Polygon" and any(geom.interiors):
        geom = Polygon(geom.exterior)

    return geom


@desc()
def reset_geom(ctx: Context, proceed):
    query.reset_geom("overlap", ctx.region)
    proceed()


@desc()
def load_bids(ctx: Context, proceed):
    """
    加载所有停车面 bid
    """
    sql = f"""
        select distinct bid from {query.PARK_PRODUCTION}
        where region = %s
            and bid != ''
            and relate_type in ('related', 'related_v2', 'related_multi')
            and result->'non_roadside_fragment'->>'error' is null;
    """

    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ctx.bids = [bid for bid, in pgsql.fetch_all(conn, sql, [ctx.region])]

    proceed()


@desc()
def load_parks(ctx: Context, proceed):
    """
    加载所有停车面
    """
    sql = f"""
        select id, tag, st_astext(geom)
        from {query.PARK_PRODUCTION}
        where bid = %s;
    """

    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        for bid in tqdm(ctx.bids):
            for park_id, tag, wkt in pgsql.fetch_all(conn, sql, (bid,)):
                park = Park(
                    id=park_id,
                    bid=bid,
                    tag=tag,
                    recommended_tag=tag,
                    original_geom=shapely.wkt.loads(wkt),
                    geom=make_geom_valid(shapely.wkt.loads(wkt)),
                )
                if park.geom.area < PARKING_MIN_AREA:
                    ctx.mark_park_error(park, "too small", park.geom)

                ctx.parks.append(park)

    proceed()


def get_intersects_aois(wkt, aoi_conn, poi_conn):
    """
    获取和指定 wkt 相交的 AOI。
    """
    get_intersects_aoi_sql = """
        select b.poi_bid, st_astext(a.geom) 
        from blu_face a
        inner join blu_face_poi b 
        on a.face_id = b.face_id
        where st_intersects(a.geom, st_geomfromtext(%s, 4326)) and
              a.kind != '52' and
              a.aoi_level = 2 and
              a.src != 'SD';
    """
    get_tag_sql = """
        select std_tag from poi where bid = %s;
    """
    ignored_tag = ["行政区划", "行政地标"]
    aois = []

    for bid, aoi_wkt in pgsql.fetch_all(aoi_conn, get_intersects_aoi_sql, (wkt,)):
        row = pgsql.fetch_one(poi_conn, get_tag_sql, (bid,))
        tag = row[0] if row else ""
        if tag in ignored_tag:
            continue

        aois.append(shapely.wkt.loads(aoi_wkt))

    return aois


def get_intersects_road_faces(wkt, road_conn):
    """
    获取和指定 wkt 相交的道路面。
    """
    fixed_road_width = 16e-5  # 默认 4 车道，单车道 4 米，这样才可能停车。
    inner_road = "52"

    # copy from get_out_line_by_geom_v2
    get_intersects_aoi_sql = """
        select st_astext(geom), form 
        from nav_link
        where st_intersects(geom, st_geomfromtext(%s, 4326)) and
              kind < 10;
    """
    road_faces = []

    for link_wkt, form in pgsql.fetch_all(road_conn, get_intersects_aoi_sql, (wkt,)):
        if inner_road in form:
            continue

        road_faces.append(
            shapely.wkt.loads(link_wkt).buffer(fixed_road_width, join_style="mitre")
        )

    return road_faces


def get_recommended_tag(park_geom, intersects_aois_geom, intersects_road_faces_geom):
    """
    根据压盖的 aoi 和道路面，推荐合适的停车场 tag。
    """
    min_iou = 0.5

    intersects_aois_geom = GeometryCollection(intersects_aois_geom)
    intersects_road_faces_geom = GeometryCollection(intersects_road_faces_geom)

    intersection_area_of_aois = intersects_aois_geom.intersection(park_geom).area
    intersection_area_of_road_faces = intersects_road_faces_geom.intersection(
        park_geom
    ).area

    union_area_of_aois = intersects_aois_geom.union(park_geom).area
    union_area_of_road_faces = intersects_road_faces_geom.union(park_geom).area

    iou_of_aois = intersection_area_of_aois / union_area_of_aois
    iou_of_road_faces = intersection_area_of_road_faces / union_area_of_road_faces

    if (
        intersection_area_of_aois > intersection_area_of_road_faces
        and iou_of_aois >= min_iou
    ):
        return PARK_TAG_AOI

    if (
        intersection_area_of_road_faces > intersection_area_of_aois
        and iou_of_road_faces >= min_iou
    ):
        return PARK_TAG_LUCE

    return None


def mc2gcj(geom: str) -> str:
    """
    将百度墨卡托坐标转换为 gcj02 坐标
    """
    sql = """
        select st_astext(mc2gcj(st_geomfromtext(%s, 4326)));
    """
    conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
    ret = pgsql.fetch_one(conn, sql, [geom])
    return ret[0]


@desc()
def correct_tag(ctx: Context, proceed):
    """
    不太相信目前库中的 tag，加个修正的步骤。
    """
    with (
        pgsql.get_connection(pgsql.BACK_CONFIG) as aoi_conn,
        pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as poi_conn,
        AoiModel() as aoi_model,
    ):
        for park in tqdm(ctx.parks + [x for x in ctx.get_all_overlap_parks()]):
            if not ctx.can_process_park(park):
                continue

            intersects_aois = get_intersects_aois(park.geom.wkt, aoi_conn, poi_conn)
            intersects_road_faces = get_intersects_road_faces(
                park.geom.wkt, aoi_model.conn_road
            )
            recommended_tag = get_recommended_tag(
                park.geom, intersects_aois, intersects_road_faces
            )
            park.recommended_tag = recommended_tag if recommended_tag else park.tag

    proceed()


@desc()
def load_overlap_parks(ctx: Context, proceed):
    """
    加载压盖停车面
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = f"""
            select id, bid, tag, st_astext(geom) 
            from {query.PARK_PRODUCTION}
            where st_intersects(geom, st_geomfromtext(%s, 3857))
                and bid != ''
                and relate_type in ('related', 'related_v2', 'related_multi')
                and result->'non_roadside_fragment'->>'error' is null
                and bid != %s;
        """

        for park in tqdm(ctx.parks):
            if not ctx.can_process_park(park):
                continue

            if park.id not in ctx.overlap_parks:
                ctx.overlap_parks[park.id] = []

            buffered_geom = park.geom.buffer(SAFE_DISTANCE)

            for park_id, bid, tag, wkt in pgsql.fetch_all(
                conn, sql, (buffered_geom.wkt, park.bid)
            ):
                geom = make_geom_valid(shapely.wkt.loads(wkt))
                if geom.area < PARKING_MIN_AREA:
                    continue

                ctx.overlap_parks[park.id].append(
                    Park(
                        id=park_id,
                        bid=bid,
                        tag=tag,
                        recommended_tag=tag,
                        geom=geom,
                    )
                )

    proceed()


def should_be_cropped(current_park: Park, overlap_park: Park):
    """
    判断当前的停车场面是否应该被裁剪
    """
    if PARK_TAG_LUCE not in current_park.recommended_tag:
        return (
            True
            if PARK_TAG_LUCE in overlap_park.recommended_tag
            else current_park.geom.area > overlap_park.geom.area
        )
    else:
        return (
            False
            if PARK_TAG_LUCE not in overlap_park.recommended_tag
            else current_park.geom.area > overlap_park.geom.area
        )


@desc()
def crop_park(ctx: Context, proceed):
    """
    裁剪停车面，防止压盖。
    """
    for park in tqdm(ctx.parks):
        if not ctx.can_process_park(park):
            continue

        old_area = park.geom.area
        overlap_parks = ctx.overlap_parks.get(park.id, [])

        for overlap_park in overlap_parks:
            if not ctx.can_process_park(overlap_park) or not should_be_cropped(
                park, overlap_park
            ):
                continue

            # buffer 的目的是防止精度导致后续的自相交判定错误
            buffered_geom = overlap_park.geom.buffer(SAFE_DISTANCE)
            park.geom = make_geom_valid(park.geom.difference(buffered_geom))
            if not park.geom:
                ctx.mark_park_error(park, "overlap too much", geom=Polygon())

        park.cropped = old_area != park.geom.area

    proceed()


@desc()
def clear_fragmentary_park(ctx: Context, proceed):
    """
    清理碎片
    """
    for park in ctx.parks:
        if not ctx.can_process_park(park) or not park.cropped:
            continue

        polygons = []
        if park.geom.geom_type != "Polygon":
            for polygon in park.geom.geoms:
                if polygon.area >= PARKING_MIN_AREA:
                    polygons.append(polygon)
            park.geom = MultiPolygon(polygons)

    proceed()


@desc()
def fill_exclusive_area(ctx: Context, proceed):
    """
    计算独占面积（未被其它面压盖的面积）
    """
    for park in tqdm(ctx.parks):
        if not ctx.can_process_park(park):
            continue

        exclusive_geom = park.original_geom

        for overlap_park in ctx.overlap_parks[park.id]:
            exclusive_geom = exclusive_geom.difference(overlap_park.geom)

        park.exclusive_area = exclusive_geom.area

    proceed()


@desc()
def beautify_park(ctx: Context, proceed):
    """
    美化停车面
    """
    for park in tqdm(ctx.parks):
        if not ctx.can_process_park(park) or not park.cropped:
            continue

        try:
            park.geom = park.geom.simplify(SIMPLIFY_TOLERANCE, preserve_topology=True)
            park.geom = park.geom.buffer(-SAFE_DISTANCE, join_style="mitre")
            park.geom = park.geom.buffer(SAFE_DISTANCE, join_style="mitre")
            park.geom = park.geom.simplify(SIMPLIFY_TOLERANCE, preserve_topology=True)
            park.geom = shapely.set_precision(park.geom, PRECISION)
            park.geom = make_geom_valid(park.geom)

            if not park.geom:
                park.geom = Polygon()
                ctx.mark_park_error(park, "beautify error", Polygon())
        except:
            park.geom = Polygon()
            ctx.mark_park_error(park, "beautify error", Polygon())

    proceed()


@desc()
def filter_records_by_area(ctx: Context, proceed):
    """
    过滤面积过小的停车面
    """
    for park in ctx.parks:
        if park.geom.area < PARKING_MIN_AREA or park.exclusive_area < PARKING_MIN_AREA:
            ctx.mark_park_error(park, "too small", park.geom)
            continue

        polygons = []
        if park.geom.geom_type != "Polygon":
            for polygon in park.geom.geoms:
                if polygon.area >= PARKING_MIN_AREA:
                    polygons.append(polygon)
            geom = MultiPolygon(polygons)
            if geom.area < PARKING_MIN_AREA:
                ctx.mark_park_error(park, "too small", park.geom)

    proceed()


@desc()
def output_to_db(ctx: Context, proceed):
    """
    保存记录
    """
    update_sql = f"""
        update {query.PARK_PRODUCTION}
        set result = jsonb_set(result, '{{overlap}}', %s, true)
        where id = %s;
    """

    values = [
        (
            Json(
                {
                    "result": park.geom.wkt if not park.geom.is_empty else None,
                    "error": park.reason if park.reason else None,
                }
            ),
            park.id,
        )
        for park in ctx.parks
    ]

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute_many(conn, update_sql, values)

    query.update_geom("overlap", ctx.region)

    proceed()


def main():
    """
    主函数
    """
    regions = query.get_regions()

    main_pipe = pipeline.Pipeline(
        # reset_geom,
        load_bids,
        load_parks,
        load_overlap_parks,
        # correct_tag,
        fill_exclusive_area,
        crop_park,
        clear_fragmentary_park,
        beautify_park,
        filter_records_by_area,
        output_to_db,
    )
    desc.attach(main_pipe)

    for region in regions:
        print(f"region: {region}")
        ctx = Context(region=region)
        main_pipe(ctx)


if __name__ == "__main__":
    main()
