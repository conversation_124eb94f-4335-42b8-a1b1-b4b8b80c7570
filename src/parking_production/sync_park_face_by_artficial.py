"""
例行脚本：同步来自其它母库的停车场面，并推送
"""

from dataclasses import dataclass, field
from typing import Callable, Iterable

from src.tools import pgsql, pipeline
from tqdm import tqdm
import requests
from src.parking_production import query

VERSION = "2.0.0"  # 初始化提交
URL = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"

desc = pipeline.get_desc()


@dataclass
class ParkFaceItem:
    """
    代表单个停车场面
    """

    bid: str
    face_id: str
    geom: str
    others: list[str]
    source: str = field(init=False)


@dataclass
class Context:
    """
    上下文
    """

    bids: list[str]
    items: list[ParkFaceItem] = field(default_factory=list)


# pipe:


def send(payload: any):
    """
    封装请求
    """
    headers = {"Content-Type": "application/json"}
    response = requests.post(URL, headers=headers, json=payload)
    return response.json()


def area(bid: str, face_id: str, strategy: str, batch: str, polygon: str):
    """
    发送请求
    """
    payload = {
        "bid": bid,
        "area": polygon,
        "area_batch": batch,
        "batch_id": batch,
        "source": strategy,
        "source_id": face_id,
    }
    return send(payload)


def collect_from(collector: Callable[[list[str]], Iterable[ParkFaceItem]]):
    """
    收集指定来源的数据
    """
    source = collector.__name__

    @desc(f"collect from {source} ...")
    def pipe(ctx: Context, proceed):
        items = list(collector(ctx.bids))
        for item in items:
            item.source = source

        if items:
            bids = {x.bid for x in ctx.items}
            ctx.items.extend(x for x in items if x.bid not in bids)

        proceed()

    return pipe


def push_item():
    """
    导出推送文件
    """

    @desc(f"export push_item")
    def pipe(ctx: Context, proceed):
        bach_id = f"rg_{query.today_str()}"
        for item in tqdm(ctx.items):
            send(
                {
                    "bid": item.bid,
                    "area": item.geom,
                    "area_batch": bach_id,
                    "batch_id": bach_id,
                    "source": "RG_PARK_AREA",
                    "source_id": item.face_id,
                }
            )

        proceed()

    return pipe


# source:


def blu_face(bids: list[str]):
    """
    collector: 来自 AOI
    """
    sql = """
        select poi_bid, src, a.face_id, st_astext(b.geom)
        from blu_face_poi a
        inner join blu_face b on a.face_id = b.face_id
        where a.poi_bid in %s;
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])

    return [ParkFaceItem(bid=bid, face_id=face_id, geom=geom, others=[src]) for bid, src, face_id, geom in ret]


# bids provider:


def get_4categories_bids():
    """
    获取所有四大垂类停车场的 bid
    """
    sql = """
        select distinct a.bid from park_4categories_list_2024q4 a
        inner join park_online_data b on a.bid = b.bid
        where b.area is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)
    bids = [x[0] for x in ret]

    firtSql = """
        select distinct a.bid from first_push_park_4categories_list_2024q4 a
        inner join park_online_data b on a.bid = b.bid
        where b.area is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sec = pgsql.fetch_all(conn, firtSql)
    bids.extend([x[0] for x in sec if x[0] not in bids])
    return bids


def get_all_bids():
    """
    获取所有停车场的 bid
    """
    sql = """
        select distinct bid from park_online_data a
        where a.area is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    return [x[0] for x in ret]


BID_PROVIDERS = {
    "4categories": get_4categories_bids,
    "all": get_all_bids,
}


def main():
    """
    主函数
    """
    bid_source = "4categories"

    pipe = pipeline.Pipeline(
        collect_from(blu_face),
        push_item(),
    )
    desc.attach(pipe)

    bids = BID_PROVIDERS[bid_source]()
    ctx = Context(bids=bids)
    pipe(ctx)


if __name__ == "__main__":
    main()
