"""
例行脚本：统计停车场面投放状态并写入数据库
"""

from dataclasses import dataclass

from src.tools import pgsql, pipeline
from src.parking_production import query
from datetime import datetime, time, timedelta

desc = pipeline.get_desc()

main_condition = " (main_poi_bid in (select bid from park_4categories_list_2024q4) \
                or \
                 main_poi_bid in (select root_bid from park_4categories_list_2024q4 ))"


@dataclass
class Context:
    """
    上下文
    """


def push_statistics_issued_park():
    """
    获取停车场面投放状态并写入数据库
    """

    @desc(f"export push_statistics_issued_park")
    def pipe(ctx: Context, proceed):

        yesterday_str = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        yesterday = query.yesterday_str()

        issued_sql = f"""
            select count(*) from integration_qb 
            where src = 4 and qb_type = 8 and created_at::date = %s
        """
        issued_main_sql = f"""
                    select count(*) from integration_qb 
                    where src = 4 
                    and qb_type = 8 
                    and created_at::date = %s 
                    and {main_condition}
                """
        with pgsql.get_connection(pgsql.POI_CONFIG) as poi_conn:
            all_count_ret = pgsql.fetch_all(poi_conn, issued_sql, [yesterday_str])
            main_count_ret = pgsql.fetch_all(poi_conn, issued_main_sql, [yesterday_str])
            all_count = 0
            main_count = 0
            if all_count_ret:
                all_count = all_count_ret[0][0]
            if main_count_ret:
                main_count = main_count_ret[0][0]
            print(all_count)
            print(main_count)

        sql = """
            select * from information_statistics
            where information_type = 'issued_information'
            and src_type = 8
            and date_time = %s;
        """
        ret = pgsql.fetch_all(poi_conn, sql, [yesterday])
        print(ret)
        if ret:
            update_sql = """
                           update information_statistics set all_count = %s,
                           main_count = %s
                           where information_type = 'issued_information'
                           and src_type = 8
                           and date_time = %s;
                       """
            pgsql.execute(poi_conn, update_sql, [all_count, main_count, yesterday])
        else:
            insert_sql = """
                           insert into information_statistics
                           (information_type, src_type, all_count,main_count,date_time,create_time)
                           values ('issued_information', 8, %s, %s, %s, now());
                       """
            pgsql.execute(poi_conn, insert_sql, [all_count, main_count, yesterday])

        proceed()

    return pipe


def push_statistics_overstock_park():
    """
    获取停车场面积压状态并写入数据库
    """

    @desc(f"export push_statistics_overstock_park")
    def pipe(ctx: Context, proceed):
        yesterday = query.yesterday_str()

        overstock_sql = f"""
            select count(*) from integration_qb 
            where src = 4 and qb_type = 8 
            and status in (0,1,2)
        """
        overstock_main_sql = f"""
            select count(*) from integration_qb 
            where src = 4 
            and qb_type = 8 
            and status in (0,1,2)
            and {main_condition}
        """
        with pgsql.get_connection(pgsql.POI_CONFIG) as poi_conn:
            all_count_ret = pgsql.fetch_all(poi_conn, overstock_sql, [])
            main_count_ret = pgsql.fetch_all(poi_conn, overstock_main_sql, [])
            all_count = 0
            main_count = 0
            if all_count_ret:
                all_count = all_count_ret[0][0]
            if main_count_ret:
                main_count = main_count_ret[0][0]
            print(all_count)
            print(main_count)

        sql = """
            select * from information_statistics
            where information_type = 'overstock_information'
            and src_type = 8
            and date_time = %s;
        """
        ret = pgsql.fetch_all(poi_conn, sql, [yesterday])
        print(ret)
        if ret:
            update_sql = """
                update information_statistics set all_count = %s,
                main_count = %s
                where information_type = 'overstock_information'
                and src_type = 8
                and date_time = %s;
            """
            pgsql.execute(poi_conn, update_sql, [all_count, main_count, yesterday])
        else:
            insert_sql = """
                insert into information_statistics
                (information_type, src_type, all_count,main_count,date_time,create_time)
                values ('overstock_information', 8, %s, %s, %s, now());
            """
            pgsql.execute(poi_conn, insert_sql, [all_count, main_count, yesterday])

        proceed()

    return pipe


def push_statistics_valid_rate_park():
    """
    获取停车场面有效无效状态占比并写入数据库
    """

    @desc(f"export push_statistics_invalid_park")
    def pipe(ctx: Context, proceed):

        _end = datetime.today()
        today_at_1800 = datetime.combine(_end, time(0, 0))
        yesterday_at_1800 = datetime.combine(_end - timedelta(days=1), time(0, 0))

        yesterday = query.yesterday_str()

        all_sql = f"""
            select count(*) from integration_qb 
            where src = 4 and qb_type = 8 
            and status in (3,4,5,6,7)
            and status_updated_at between %s and %s 
        """

        main_sql = f"""
            select count(*) from integration_qb 
            where src = 4 
            and qb_type = 8 
            and status in (3,4,5,6,7)
            and status_updated_at between %s and %s 
            and {main_condition}
        """

        all_valid_sql = f"""
            select count(*) from integration_qb 
            where src = 4 and qb_type = 8 
            and status in (3,4)
            and status_updated_at between %s and %s 
        """

        main_valid_sql = f"""
            select count(*) from integration_qb 
            where src = 4 
            and qb_type = 8 
            and status in (3,4)
            and status_updated_at between %s and %s 
            and {main_condition}
        """

        all_invalid_sql = f"""
            select count(*) from integration_qb 
            where src = 4 and qb_type = 8 
            and status in (5,6,7)
            and status_updated_at between %s and %s 
        """

        main_invalid_sql = f"""
            select count(*) from integration_qb 
            where src = 4 
            and qb_type = 8 
            and status in (5,6,7)
            and status_updated_at between %s and %s 
            and {main_condition}
        """

        with pgsql.get_connection(pgsql.POI_CONFIG) as poi_conn:
            all_count_ret = pgsql.fetch_all(poi_conn, all_sql, [yesterday_at_1800, today_at_1800])
            main_count_ret = pgsql.fetch_all(poi_conn, main_sql, [yesterday_at_1800, today_at_1800])
            all_valid_count_ret = pgsql.fetch_all(poi_conn, all_valid_sql, [yesterday_at_1800, today_at_1800])
            main_valid_count_ret = pgsql.fetch_all(poi_conn, main_valid_sql, [yesterday_at_1800, today_at_1800])
            all_invalid_count_ret = pgsql.fetch_all(poi_conn, all_invalid_sql, [yesterday_at_1800, today_at_1800])
            main_invalid_count_ret = pgsql.fetch_all(poi_conn, main_invalid_sql, [yesterday_at_1800, today_at_1800])

            all_count = 0
            main_count = 0
            all_valid = 0
            main_valid = 0
            all_invalid = 0
            main_invalid = 0

            if all_count_ret:
                all_count = all_count_ret[0][0]
            if main_count_ret:
                main_count = main_count_ret[0][0]
            if all_valid_count_ret:
                all_valid = all_valid_count_ret[0][0]
            if main_valid_count_ret:
                main_valid = main_valid_count_ret[0][0]
            if all_invalid_count_ret:
                all_invalid = all_invalid_count_ret[0][0]
            if main_invalid_count_ret:
                main_invalid = main_invalid_count_ret[0][0]

            print(all_count)
            print(main_count)
            print(all_valid)
            print(main_valid)
            print(all_invalid)
            print(main_invalid)

            all_valid_rate = 0
            main_valid_rate = 0
            if all_count > 0:
                all_valid_rate = all_valid / all_count
            if main_count > 0:
                main_valid_rate = main_valid / main_count
            print(all_valid_rate)
            print(main_valid_rate)

        valid_sql = """
            select * from information_statistics
            where information_type = 'valid_information'
            and src_type = 8
            and date_time = %s;
        """
        valid_ret = pgsql.fetch_all(poi_conn, valid_sql, [yesterday])
        print(valid_ret)
        if valid_ret:
            update_sql = """
                       update information_statistics set all_count = %s,
                       main_count = %s
                       where information_type = 'valid_information'
                       and src_type = 8
                       and date_time = %s;
                   """
            pgsql.execute(poi_conn, update_sql, [all_valid_rate, main_valid_rate, yesterday])
        else:
            insert_sql = """
                       insert into information_statistics
                       (information_type, src_type, all_count,main_count,date_time,create_time)
                       values ('valid_information', 8, %s, %s, %s, now());
                   """
            pgsql.execute(poi_conn, insert_sql, [all_valid_rate, main_valid_rate, yesterday])

        invalid_sql = """
            select * from information_statistics
            where information_type = 'invalid_information'
            and src_type = 8
            and date_time = %s;
        """
        invalid_ret = pgsql.fetch_all(poi_conn, invalid_sql, [yesterday])
        print(invalid_ret)
        if invalid_ret:
            update_sql = """
                update information_statistics set all_count = %s,
                main_count = %s
                where information_type = 'invalid_information'
                and src_type = 8
                and date_time = %s;
            """
            pgsql.execute(poi_conn, update_sql, [all_invalid, main_invalid, yesterday])
        else:
            insert_sql = """
                insert into information_statistics
                (information_type, src_type, all_count,main_count,date_time,create_time)
                values ('invalid_information', 8, %s, %s, %s, now());
            """
            pgsql.execute(poi_conn, insert_sql, [all_invalid, main_invalid, yesterday])
        proceed()

    return pipe


def main():
    """
    主函数
    """

    pipe = pipeline.Pipeline(
        push_statistics_issued_park(),
        push_statistics_overstock_park(),
        push_statistics_valid_rate_park(),
    )
    desc.attach(pipe)

    ctx = Context()
    pipe(ctx)


if __name__ == "__main__":
    main()
