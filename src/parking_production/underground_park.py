# !/usr/bin/env python3
"""
生产地下停车场边框（使用 AOI 面或建筑物面）
"""
import json
import sys
from collections import defaultdict
from dataclasses import dataclass, field
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path

import psycopg2.extras
import requests
import shapely
from loguru import logger
from psycopg2.extras import Json
from shapely import wkt
from tqdm import tqdm

from src.parking.recognition import dbutils
from src.parking_production import query
from src.tools import pgsql, pipeline, tsv, linq, utils

PREFIX = "STRATEGY_UNDERGROUND_ROUTINE"
# VERSION = "2.0.0"  # 交接整理初始版
# VERSION = "2.1.0"  # 增加 entry_rect 类型
# VERSION = "2.1.1"  # 更改 blu_face 取面逻辑：仅使用父点 bid 所在的 AOI 面（逻辑关联），而非使用空间关联关系
# VERSION = "2.2.0"  # 仅使用 bud_face + entry_rect，并且二者 union，而不是选其中之一
VERSION = "2.2.1"  # 针对 房地产;住宅区 不要使用 bud_face，仅 entry_rect，使用 gcj02 坐标推送


@dataclass
class FaceInfo:
    """
    面信息
    """

    face_type: str  # 'blu_face' or 'bud_face' or 'entry_rect'
    geom: str  # wkt: POLYGON
    face_source_ids: dict[str, list[str]]  # blu_face.face_id or bud_face.struct_id

    @property
    def face_id(self):
        """
        面 ID
        """
        return utils.md5(self.geom)


@dataclass
class Context:
    """
    上下文
    """

    bid: str
    name: str
    parent_bid: str
    parent_name: str
    parent_std_tag: str
    geom_gcj02: str  # POINT
    faces: list[FaceInfo] = field(default_factory=list)


def try_using_bud_face(ctx: Context, proceed):
    """
    尝试使用建筑物面来生成地下停车场
    """
    conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
    sql = """
        select struct_id from bud_face
        where st_contains(geom, st_geomfromtext(%s, 4326));
    """
    ret = pgsql.fetch_one(conn, sql, [ctx.geom_gcj02])
    if ret:
        struct_id = ret[0]
    else:
        sql = """
            select struct_id, st_distance(geom, st_geomfromtext(%(point)s, 4326)) from bud_face
            where st_intersects(geom, st_buffer(st_geomfromtext(%(point)s, 4326), 0.001));
        """
        ret = pgsql.fetch_all(conn, sql, {"point": ctx.geom_gcj02})
        if ret:
            sorted_by_distance = sorted(ret, key=lambda x: x[1])
            struct_id, min_distance = sorted_by_distance[0]
            struct_id = struct_id if min_distance < 20 * 1e-5 else None
        else:
            struct_id = None

    if struct_id:
        sql = """
            select st_astext(st_buffer(st_unaryunion(st_buffer(st_collect(geom), 1 * 1e-5)), -1 * 1e-5)) from bud_face
            where struct_id = %s;
        """
        ret = pgsql.fetch_one(conn, sql, [struct_id])
        geom_str = ret[0]
        if geom_str.startswith("MULTIPOLYGON"):
            geom = wkt.loads(geom_str)
            point = wkt.loads(ctx.geom_gcj02)
            polygon = linq.first_or_default(geom.geoms, lambda x: x.contains(point))
            geom_str = polygon.wkt if polygon else ""

        if geom_str:
            ctx.faces.append(FaceInfo("bud_face", geom_str, {"bud_face.struct_id": [struct_id]}))

    proceed()


def try_using_blu_face(ctx: Context, proceed):
    """
    尝试使用 AOI 面来生成地下停车场
    """
    sql = """
        select a.face_id, st_astext(a.geom)
        from blu_face a
        inner join blu_face_poi b on a.face_id = b.face_id
        where a.aoi_level != '1'
            and a.src != 'SD'
            and b.poi_bid = %s;
    """
    conn = pgsql.get_connection_ttl(pgsql.BACK_CONFIG)
    ret = pgsql.fetch_one(conn, sql, [ctx.parent_bid])
    if ret:
        face_id, geom = ret
        ctx.faces.append(FaceInfo("blu_face", geom, {"blu_face.face_id": [face_id]}))

    proceed()


def try_using_entry_rect(ctx: Context, proceed):
    """
    尝试使用入口矩形来生成地下停车场
    """
    sql_road_relation = f"""
        with links as (
            select
            json_array_elements(
                case
                    when road_relation_childrens->'link_info' is not null then
                        road_relation_childrens->'link_info'
                    else
                        -- 当没有入口时，fallback 到高通量 link
                        road_relation->'link_info'
                end 
            ) as link
            from park_online_data
            where bid = %s
        )
        select link->>'link_id'
        from links
        where link->>'orientation' = '1'
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_SLAVER_CONFIG)
    ret = pgsql.fetch_all(conn, sql_road_relation, [ctx.bid])
    short_link_ids = [x[0] for x in ret]
    link_ids = [get_long_link_id(x) for x in short_link_ids]
    link_ids = [x for x in link_ids if x]
    if not link_ids:
        return proceed()

    sql_link_geom = """
        select st_astext(st_unaryunion(st_buffer(st_collect(geom), 5 * 1e-5, 'endcap=square'))) from nav_link
        where link_id in %s;
    """
    conn = pgsql.get_connection_ttl(pgsql.ROAD_CONFIG)
    ret = pgsql.fetch_one(conn, sql_link_geom, [tuple(link_ids)])
    if not ret:
        return proceed()

    link_wkt = ret[0]
    ctx.faces.append(FaceInfo("entry_rect", link_wkt, {"nav_link.link_id": link_ids}))

    proceed()


def break_if_face_found(ctx: Context, proceed):
    """
    如果已经找到面了，就停止继续执行
    """
    if len(ctx.faces) > 0:
        return

    proceed()


def break_if_std_tag(ctx: Context, proceed, std_tags: list[str]):
    """
    如果 parent_std_tag 在 std_tags 里，就停止继续执行
    """
    if ctx.parent_std_tag in std_tags:
        return

    proceed()


# helpers:


def get_long_link_id(short_id: str):
    """
    将 short_id 转换为 long_id（可在 nav_link 里查询的）
    """
    sql = """
        select sid from image_r
        where tid = %s;
    """
    conn = pgsql.get_connection_ttl(pgsql.TRANS_ID)
    ret = pgsql.fetch_one(conn, sql, [short_id])
    return ret[0] if ret else None


def merge_face_info(faces: list[FaceInfo]):
    """
    合并多个 FaceInfo
    """
    if not faces:
        return None

    geoms = [wkt.loads(f.geom) for f in faces]
    geom = shapely.unary_union(geoms)
    face_type = ",".join(sorted(set([f.face_type for f in faces])))
    return FaceInfo(
        face_type=face_type,
        geom=geom.wkt,
        face_source_ids=merge_dict_list([f.face_source_ids for f in faces]),
    )


def merge_dict_list(dict_list: list[dict[str, list[str]]]):
    """
    合并多个 dict 列表，key 相同的值会被合并
    """
    merged = defaultdict(list)
    for d in dict_list:
        for k, v in d.items():
            merged[k].extend(v)

    return dict(merged)


# launch:


def get_under_park_all():
    """
    bid provider: 所有地下停车场
    """
    sql = f"""
        select a.bid, a.name, c.bid, c.name, c.std_tag, st_astext(a.gcj_geom)
        from {query.PARK_ONLINE} a
        left join poi c on a.parent_id = c.bid
        where a.show_tag = '地下停车场' and (a.area is null or a.show_area is null);
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    return ret


def get_under_park_top90():
    """
    bid provider: TOP90 的地下停车场
    """
    sql = f"""
        select a.bid, a.name, c.bid, c.name, c.std_tag, st_astext(a.gcj_geom)
        from {query.PARK_ONLINE} a
        inner join park_value_list b on a.bid = b.bid
        left join poi c on a.parent_id = c.bid
        where b.value in ('TOP80', 'TOP80-TOP90')
            and a.show_tag = '地下停车场'
            and a.area is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    return ret


def get_under_park_4categories():
    """
    bid provider:  四大垂类的地下停车场
    """
    sql = f"""
        select a.bid, a.name, c.bid, c.name, c.std_tag, st_astext(a.gcj_geom)
        from {query.PARK_ONLINE} a
        inner join park_4categories_bid_list b on a.bid = b.bid
        left join poi c on a.parent_id = c.bid
        where a.show_tag = '地下停车场'
            and a.area is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    return ret


def get_3d_park():
    """
    bid provider: 立体停车场
    """
    sql = f"""
        select a.bid, a.name, c.bid, c.name, c.std_tag, st_astext(a.gcj_geom)
        from {query.PARK_ONLINE} a
        left join poi c on a.parent_id = c.bid
        where a.show_tag = '立体停车场'
            and a.area is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    return ret


def get_park_add_not_20():
    """
    bid provider: park_add_not_20 地下停车场集合
    """
    sql = """
        select a.bid, a.name, c.bid, c.name, c.std_tag, st_astext(a.gcj_geom)
        from park_online_data a
        inner join park_add_not_20 b on a.bid = b.bid
        left join poi c on a.parent_id = c.bid
        where b.has_polygon = 0 and b.show_tag in ('地下停车场')
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    return ret


def get_park_from_bid(bids: list[str]):
    """
    bid provider: 指定 bid 的停车场
    """
    sql = """
        select a.bid, a.name, c.bid, c.name, c.std_tag, st_astext(a.gcj_geom)
        from park_online_data a
        left join poi c on a.parent_id = c.bid
        where a.bid in %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(bids)])
    return ret


BID_PROVIDERS = {
    "all": get_under_park_all,
    "routine_all": get_under_park_all,
    "top90": get_under_park_top90,
    "4categories": get_under_park_4categories,
    "3d_park": get_3d_park,
    "add_20": get_park_add_not_20,
}


def generate_parking_polygon(parkings: list, batch: str, work_dir: Path):
    """
    生成停车场的多边形
    """
    success_path = utils.ensure_path(work_dir / f"success.tsv")
    failed_path = utils.ensure_path(work_dir / f"failed.tsv")

    pipe = pipeline.Pipeline(
        try_using_entry_rect,
        partial(break_if_std_tag, std_tags=["房地产;住宅区"]),
        # break_if_face_found,
        # try_using_blu_face,
        # break_if_face_found,
        try_using_bud_face,
    )
    contexts = (
        Context(
            bid=bid,
            name=name,
            parent_bid=parent_bid,
            parent_name=parent_name,
            parent_std_tag=parent_std_tag,
            geom_gcj02=geom,
        )
        for bid, name, parent_bid, parent_name, parent_std_tag, geom in parkings
    )
    with Pool(4) as p, open(work_dir / "progress_parking_polygon.log", "w") as f_progress:
        for ctx in tqdm(p.imap_unordered(pipe, contexts), total=len(parkings), file=f_progress):
            face = merge_face_info(ctx.faces)
            basic_item = [
                ctx.bid,
                ctx.name,
                ctx.parent_bid,
                ctx.parent_name,
                ctx.parent_std_tag,
                ctx.geom_gcj02,
                batch,
            ]
            if face:
                item = [*basic_item, face.face_type, face.face_id, json.dumps(face.face_source_ids), face.geom]
                tsv.write_tsv(success_path, [item], mode="a")
            else:
                tsv.write_tsv(failed_path, [basic_item], mode="a")


def save_success_to_db(success_path: Path):
    """
    将成功生成的数据保存到数据库中
    """
    idx_bid = 0
    idx_name = 1
    idx_parent_bid = 2
    idx_parent_name = 3
    idx_parent_std_tag = 4
    idx_point = 5
    idx_batch = 6
    idx_face_type = 7
    idx_face_id = 8
    idx_source_ids = 9
    idx_geom = 10

    sql = """
        insert into park_underground_strategy (
            bid, name, parent_bid, parent_name, parent_std_tag, point, batch, status,
            face_id, face_type, geom, face_source_ids
        )
        values %s
        on conflict (bid, batch) do nothing
    """
    values = [
        (
            x[idx_bid],
            x[idx_name],
            x[idx_parent_bid],
            x[idx_parent_name],
            x[idx_parent_std_tag],
            f"SRID=4326;{x[idx_point]}",
            x[idx_batch],
            "SUCCESS",
            x[idx_face_id],
            x[idx_face_type],
            f"SRID=4326;{x[idx_geom]}",
            Json(json.loads(x[idx_source_ids])),
        )
        for x in tsv.read_tsv(success_path)
    ]
    if not values:
        logger.warning("[save success to db] nothing to insert")
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cur:
        psycopg2.extras.execute_values(cur, sql, values, page_size=10000)

    logger.info(f"[save success to db] {len(values)} rows inserted")


def save_failed_to_db(success_path: Path):
    """
    保存失败的数据到数据库
    """
    idx_bid = 0
    idx_name = 1
    idx_parent_bid = 2
    idx_parent_name = 3
    idx_parent_std_tag = 4
    idx_point = 5
    idx_batch = 6

    sql = """
        insert into park_underground_strategy (
            bid, name, parent_bid, parent_name, parent_std_tag, point, batch, status
        )
        values %s
        on conflict (bid, batch) do nothing
    """
    values = [
        (
            x[idx_bid],
            x[idx_name],
            x[idx_parent_bid],
            x[idx_parent_name],
            x[idx_parent_std_tag],
            f"SRID=4326;{x[idx_point]}",
            x[idx_batch],
            "FAILED",
        )
        for x in tsv.read_tsv(success_path)
    ]
    if not values:
        logger.warning("[save failed to db] nothing to insert")
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn, conn.cursor() as cur:
        psycopg2.extras.execute_values(cur, sql, values, page_size=10000)

    logger.info(f"[save failed to db] {len(values)} rows inserted")


def push_data_by_batch(batch: str):
    """
    批量推送数据到上线
    """
    sql = """
        select bid, face_id, st_astext(geom), batch
        from park_underground_strategy
        where batch = %s and status = 'SUCCESS'
    """
    cases = dbutils.fetch_all(pgsql.POI_CONFIG, sql, [batch])
    success_bids = []
    for bid, face_id, geom, batch in cases:
        resp = push_data(bid, face_id, geom, batch)
        if resp["errno"] == 0:
            success_bids.append(bid)

    return success_bids


def update_status(batch: str, bids: list[str], status: str):
    """
    更新数据库状态
    """
    sql = """
        update park_underground_strategy
        set status = %s, updated_at = now()
        where batch = %s and bid in %s
    """
    if not bids:
        return

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        pgsql.execute(conn, sql, [status, batch, tuple(bids)])


def push_data(bid: str, face_id: str, geom: str, batch: str):
    """
    推送数据到上线
    """
    api = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    payload = {
        "bid": bid,
        "force_update": 1,  # 强制更新
        "source": batch,
        "source_id": face_id,
        "show_area": geom,
        "show_area_batch": batch,
        "cover_show_area_id": face_id,
        "area": geom,
        "area_batch": batch,
    }
    resp = requests.post(url=api, json=payload)
    return resp.json()


def _set_flag(folder: Path, flag: str, payload=""):
    with open(folder / flag, "w") as f:
        f.write(payload)


def _has_flag(folder: Path, flag: str):
    file = folder / flag
    return file.exists()


@logger.catch
def main(product_type: str, file_path: Path):
    """
    主函数
    """
    assert product_type != "file" or file_path is not None, "file_path is required when product_type == 'file'"

    work_dir = Path("./products")
    if product_type == "file":
        bids = [x[0] for x in tsv.read_tsv(file_path)]
        parking_list = get_park_from_bid(bids)
    else:
        provider = BID_PROVIDERS[product_type]
        parking_list = provider()

    batch = f"{PREFIX}-{VERSION}-{query.today_str()}"
    save_dir = utils.ensure_dir(work_dir / "underground" / f"{batch}-{product_type.upper()}")
    if not _has_flag(save_dir, "DONE.GENERATED"):
        logger.info(f"[{batch}] generating parking polygon for '{product_type}'...")
        generate_parking_polygon(parking_list, batch, save_dir)
        _set_flag(save_dir, "DONE.GENERATED")

    if not _has_flag(save_dir, "DONE.SAVE_TO_DB"):
        logger.info(f"[{batch}] saving parking polygon to db...")
        save_success_to_db(save_dir / "success.tsv")
        save_failed_to_db(save_dir / "failed.tsv")
        _set_flag(save_dir, "DONE.SAVE_TO_DB")

    if product_type == "routine_all":
        if not _has_flag(save_dir, "DONE.PUSHED"):
            logger.info(f"[{batch}] pushing data...")
            pushed_bids = push_data_by_batch(batch)
            tsv.write_tsv(save_dir / "DONE.PUSHED", [[x] for x in pushed_bids])
            logger.info(f"[{batch}] pushed {len(pushed_bids)} bids.")

        if _has_flag(save_dir, "DONE.PUSHED") and not _has_flag(save_dir, "DONE.UPDATED"):
            logger.info(f"[{batch}] updating status...")
            pushed_bids = [x[0] for x in tsv.read_tsv(save_dir / "DONE.PUSHED")]
            update_status(batch, pushed_bids, "PUSHED")
            _set_flag(save_dir, "DONE.UPDATED")

    logger.info(f"[{batch}] done")


if __name__ == "__main__":
    main(sys.argv[1], Path(sys.argv[2]) if len(sys.argv) > 2 else None)
