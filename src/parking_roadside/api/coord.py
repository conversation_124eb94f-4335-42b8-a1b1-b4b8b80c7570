# -*- coding: utf-8 -*-
"""
坐标转换
"""
import requests


def trans_bd_2_gc(wkt):
    """
    百度坐标系转国测
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/coordsTrans"
    payload = {
        'wkt': wkt,
        'from': 3,
        'to': 1
    }
    # print(payload)
    response = requests.request("POST", url, data=payload)
    if response.status_code == 200:
        return response.json()['data']
    else:
        return None


def trans_gc_2_bdmc(wkt):
    """
    国测坐标系转百度mc
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/coordsTrans"
    payload = {
        'wkt': wkt,
        'from': 1,
        'to': 2
    }
    # print(payload)
    response = requests.request("POST", url, data=payload)
    if response.status_code == 200:
        return response.json()['data']
    else:
        return None


def trans_bdmc_2_gc(wkt):
    """
    百度mc坐标系转国测
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/coordsTrans"
    payload = {
        'wkt': wkt,
        'from': 2,
        'to': 1
    }
    # print(payload)
    response = requests.request("POST", url, data=payload)
    if response.status_code == 200:
        return response.json()['data']
    else:
        return None
