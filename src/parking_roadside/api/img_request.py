#!/usr/bin/env python
# -*- encoding: utf-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Description
roadlight_request_offline.py
"""
# from urllib.request import urlretrieve
# import json
# import numpy as np
import cv2
import time
import sys
import os
import json
import base64
import random
import requests
# from io import BytesIO
# from PIL import Image
# import io
# from PIL import Image as PILImage
import numpy as np

# import argparse
# import logging

path_add = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, path_add)
from xvision_img import XvisionDemo


class FeatureReq(XvisionDemo):
    """
    FEATURE_DE_IMG_ZYELEMENT_GPU demo
    """

    def prepare_request(self, img_path, img_name):
        """
        将图片转为base64格式，并构造请求数据

        Args:
            input_pth (str): 图片路径

        Returns:
            str: 请求数据，已json格式返回
        """
        feat_args = {
            "image": "",
            "feat_args": {
                "name": ""
            },
        }
        if isinstance(img_path, str):
            # 传入的是图片路径
            feat_args['image'] = base64.b64encode(open(img_path, "rb").read()).decode("utf-8")
        else:
            # 传入的是二进制数据
            feat_args['image'] = base64.b64encode(img_path).decode("utf-8")
        # feat_args['feat_args']['name'] = img_path.split('/')[-1].split('.')[0]

        feat_args['feat_args']['name'] = img_name
        req_data = {
            "appid": "FEATURE_DE_IMG_LARGE_SEG_41CLS_MODEL_GPU",
            "logid": 1234567,
            "format": "string",
            "from": "xvision",
            "clientip": "0.0.0.0",
            "req": feat_args,
        }
        req_data['req']['feat_args'] = json.dumps(req_data['req']['feat_args'])
        return json.dumps(req_data)


def base64_2_image_data(base64_code):
    """
    将base64编码的图片转换成图像array
    :param base64_code:
    :return: img array
    """
    if len(base64_code) == 0:
        print("base64 code is empty")
        return None
    img_as_np = np.frombuffer(base64_code, dtype=np.uint8)
    img = cv2.imdecode(img_as_np, flags=1)
    return img


def feature_calculate(img_path, img_name, url):
    """
    功能：特征计算
    输入：
        input_data:本地图片文件
    输出：
        图片特征
    """
    feature_demo = FeatureReq()

    # 生成算子输入
    feature_data = feature_demo.prepare_request(img_path, img_name)
    # print('feature_data:', eval(feature_data).keys())
    headers = {'Content-Type': 'application/json; charset=UTF-8'}
    res = requests.post(url, feature_data, headers=headers, timeout=(10, 60))
    res_data = res.text
    return res_data


def download_img(url_img):
    """
    下载图片
    """
    response_pic = requests.get(url_img)
    if response_pic.status_code == 200:
        image_content = response_pic.content
        # img_array = base64_2_image_data(image_content)
        return image_content
    else:
        print('img is not exists')
        return None


def request_parking_line(url_img):
    """
    获取识别停车线
    """
    # url_img = 'http://m.map.baidu.com:8013/20240909253360ra/20240909253360ra00002314.jpg'
    # url_img = 'http://m.map.baidu.com:8011/P_93fdd78a9ae10c7212ca704ccb0b01e8_1729660651838'
    # url_img = 'http://m.map.baidu.com:8011/P_yika6541730_1722129226414'
    try_cnt = 5
    busy_cnt = 0
    res_data = None
    while try_cnt > 0:
        url = "http://10.65.35.35:8010/infer/parking_line"
        img_name = url_img.split('/')[-1]
        try:
            response_pic = requests.get(url_img, timeout=(5, 30))
            if response_pic.status_code == 200:
                image_content = response_pic.content
            else:
                raise Exception('img is not exists', response_pic.status_code, url_img)
            # img_array = base64_2_image_data(image_content)
            img_path = image_content
            # img_flag = infer_test(img_path, img_name)
            res_data = json.loads(feature_calculate(img_path, img_name, url))
            # print('res_data:', res_data)
            if res_data['err_no'] == 0:
                img_flag = json.loads(res_data['feature_res'])
                return img_flag
            elif res_data['err_no'] != '4900':  # server is busy 会一直请求
                busy_cnt += 1
                if busy_cnt < 50:
                    continue
            else:
                time.sleep(0.5)
                raise Exception('infer request failed', res_data)
        except Exception as e:
            print(f"HTTP GET request failed: {url}", e)
            time.sleep(0.5)
        try_cnt -= 1
        if try_cnt <= 0:
            print(f"HTTP GET request failed: {url} exhaust", res_data)
            return None


if __name__ == '__main__':
    res = request_parking_line('http://m.map.baidu.com:8011/P_93fdd78a9ae10c7212ca704ccb0b01e8_1729660651838')
    print(res)
