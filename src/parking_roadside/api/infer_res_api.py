# -*- coding: utf-8 -*-
"""
Created on 2019/1/16 15:07
"""
import json
import sys
import cv2

import numpy as np
import requests

# 保障线程安全
cv2.setNumThreads(1)


def multi_get_parking_line_bycdid_worker(cd_id, pic_track_dict):
    """
    多线程计算每一个车天的识别结果
    1， ogr对象在序列化的时候会有问题 不要传递与返回ogr对象
    2， worker可以访问全局对象 全局对象是否会进行序列化呢？试一下, 试了 不会序列化全局对向
    """

    parking_line_list = []

    url = 'http://m.map.baidu.com:8013/elementdir/%s/%s_210.rec' % (cd_id, cd_id)
    response = requests.get(url)
    if response.status_code == 404:
        return []

    # 有些数据不是json类型 个别 需要剔除

    try:
        data = json.loads(response.text)
        rec_list = data['labels']
        print("fetch_info_success", len(rec_list))
        for label in rec_list:
            # 210024 众源车位线类型
            if label['rt1_type'] == "210024":
                pic_id = label['pic_id']
                if pic_id not in pic_track_dict:
                    continue
                track = pic_track_dict[pic_id]
                print("label", json.dumps(label))

                # pic_url = track['pic_url']
                pic_url = 'http://m.map.baidu.com:8013/%s/%s.jpg' % (cd_id, pic_id)
                point_gcj_xy = (float(track['x']), float(track['y']))
                # varea_link_id = track['link_id']
                # if varea_link_id != match_link_id:
                #     continue

                parking_line_list.append([cd_id, pic_id, pic_url, track, label])

            # if len(parking_line_list) >= 3:
            #     return parking_line_list

    except Exception as e:
        return []

    print("fetch_info_complete")
    return parking_line_list


def multi_get_parking_line_bycdid_worker_helper(item):
    """
    多线程计算每一个车天的识别结果
    """
    return multi_get_parking_line_bycdid_worker(item[0], item[1])


def get_full_infer_res(cd_id, rec_id=210):
    """
    依据车道id获取识别结果
    """
    url = 'http://m.map.baidu.com:8013/elementdir/%s/%s_%s.rec' % (cd_id, cd_id, rec_id)
    try:
        response = requests.get(url, timeout=(3, 20))
        if response.status_code == 404:
            return []

        # 有些数据不是json类型 个别 需要剔除
        data = json.loads(response.text)
        rec_list = data['labels']
        return rec_list
    except Exception as e:
        return []


def img_denoised(gray):
    """
    first erode img to remove samll regions,
    second via dialate to fill the small hole.
    """

    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    gray = cv2.erode(gray, kernel, iterations=1)
    gray = cv2.dilate(gray, kernel, iterations=1)
    return gray


def check_stopcarline_in_left_or_right(segment_image):
    """
    判断道路两侧有车还是左侧右侧 还是右侧有车
    小汽车标识：30 + 40=70
    停车位线标识:46
    """
    m, n = segment_image.shape
    binary_image = np.zeros((m, n), np.uint8)
    binary_image[segment_image != 46] = 255
    binary_image = cv2.bitwise_not(binary_image)

    gray = img_denoised(binary_image)

    is_car_in_left = False
    is_car_in_right = False
    contours, hierarchy = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    for contour in contours:
        area = cv2.contourArea(contour)
        # print(area)
        if area < 450:
            continue

        """
        m, n = self.segment_image.shape
        contour 是坐标， 947与n 对应, 553与m对应
        [[ 947  553]
         [ 947  554]
         [ 947  555]
        """
        contour_points = contour.reshape(-1, 2)
        # 使用NumPy计算x和y坐标的均值
        mean_point = np.mean(contour_points, axis=0).astype(int)

        # 打印均值坐标或进行其他处理
        # print(mean_point)
        if mean_point[0] > n / 2:
            is_car_in_right = True
        else:
            is_car_in_left = True

    if is_car_in_left and is_car_in_right:
        return "double_car"
    elif is_car_in_left and not is_car_in_right:
        return "left_car"
    elif not is_car_in_left and is_car_in_right:
        return "right_car"
    else:
        return "no_car"


def get_infer_by_pic(cdid, pic_id):
    """
    通过图片获取识别结果
    """
    url = "http://m.map.baidu.com:8013/segment/%s/%s.png" % (cdid, pic_id)
    # resp = urllib.request.urlopen(url)
    try_cnt = 3
    resp = None
    while try_cnt > 0:
        try:
            resp = requests.get(url, timeout=(5, 60))
            if resp.status_code == 404:
                return 0
            elif resp.status_code != 200:
                try_cnt -= 1
                continue
            else:
                break
        except Exception as e:
            try_cnt -= 1

    if resp is None:
        return 0
    image = np.asarray(bytearray(resp.content), dtype="uint8")
    resp.close()
    if image is None:
        return 0
    image_layer = cv2.imdecode(image, cv2.IMREAD_COLOR)[:, :, 0]
    # print(image_layer)
    res = check_stopcarline_in_left_or_right(image_layer)
    if res == 'no_car':
        return 0
    else:
        return 1


def multi_get_infer_by_pic_helper(cdid, pic_id):
    """
    并发用，返回入参
    """
    return cdid, pic_id, get_infer_by_pic(cdid, pic_id)


if __name__ == "__main__":
    get_infer_by_pic(sys.argv[1], sys.argv[2])
