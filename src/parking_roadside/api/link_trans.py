# -*- coding: utf-8 -*-
"""
link_id转换
"""
import requests


def get_short_id(long_id):
    """
    长id转短id
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/roadLinkTrans"
    payload = {
        'ids': long_id,
        'trans_type': 1,
    }
    response = requests.request("get", url, params=payload)
    if response.status_code == 200:
        res = response.json()
        if 'data' in res and long_id in res['data']:
            return res['data'][long_id]
    return ""


def get_long_id(short_id):
    """
    短id转长id
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/roadLinkTrans"
    payload = {
        'ids': short_id,
        'trans_type': 2,
    }
    response = requests.request("get", url, params=payload)
    if response.status_code == 200:
        res = response.json()
        if 'data' in res and short_id in res['data']:
            return res['data'][short_id]
    return ""


def get_multi_short_id(long_id_list):
    """
    批量长id转短id
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/roadLinkTrans"
    payload = {
        'ids': ",".join(long_id_list),
        'trans_type': 1,
    }
    response = requests.request("get", url, params=payload)
    if response.status_code == 200:
        res = response.json()
        if 'data' in res:
            return res['data']
    return {}


def get_multi_long_id(short_id_list):
    """
    批量短id转长id
    """
    url = "http://mapde-poi.baidu-int.com/prod/api/roadLinkTrans"
    payload = {
        'ids': ",".join(short_id_list),
        'trans_type': 2,
    }
    response = requests.request("get", url, params=payload)
    if response.status_code == 200:
        res = response.json()
        if 'data' in res:
            return res['data']
    return {}
