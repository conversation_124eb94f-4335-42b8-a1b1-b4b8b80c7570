# -*- coding: utf-8 -*-
"""
操作poi
"""
import requests


def offline_poi(bid):
    """
    下线poi
    """
    # 正式连接
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    payload = {
        "source": "STRATEGY_ROADSIDE_DIFF",
        "bid": bid,
        "status": 2,
        "force_update": 1,
        "priority": 5
    }
    print(payload)
    response = requests.request("POST", url, json=payload)
    print(response.text)


def get_info_by_bid(bid):
    """
    获取poi信息
    """
    req = requests.get(
        "https://mapde-poi.baidu-int.com/prod/api/getPoiDetail",
        params={"bid": f"{bid}"},
        timeout=20.0,
    )
    req.close()
    resp = req.json()
    if resp["code"] != 0:
        return None
    return req.json()


def update_poi_relation(bid, road_relation):
    """
    更新road relation
    """
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    payload = {
        "source": "STRATEGY_ROADSIDE_TMP",
        "bid": bid,
        "road_relation": road_relation,
        'priority': 5
    }
    print(payload)
    response = requests.request("POST", url, json=payload)
    print(response.text)


def update_poi_area(bid, area):
    """
    更新area
    """
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    payload = {
        "source": "STRATEGY_ROADSIDE",
        "bid": bid,
        "area": area,
        'priority': 5
    }
    print(payload)
    response = requests.request("POST", url, json=payload)
    print(response.text)


def update_spec(bid):
    """
    更新road relation
    """
    url = "http://mapde-poi.baidu-int.com/prod/parking/submitStrategyTask"
    payload = {
        "source": "STRATEGY_ROADSIDE",
        "bid": bid,
        "park_spec": 1,
        'priority': 5
    }
    print(payload)
    response = requests.request("POST", url, json=payload)
    print(response.text)
