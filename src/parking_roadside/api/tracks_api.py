#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
轨迹api
"""
import json
import time
import random
import hashlib
import requests
from shapely.geometry import Point


def get_tracks_all(park_traj_buffer_sp, months=12):
    """
    获取指定区域范围内，指定时间全部轨迹信息
    """
    t_time = int(time.time())
    # 1.2 查询源: 指定6个源  众源区域更新  众源高频
    track_source_list = ['vnet', 'varea', 'vmonitor', 'point', 'qblutao', 'qbother', 'qbpoi', 'qbrobot', 'qbgate']
    # track_source_list = ['varea']
    # 最长12个月
    # months = 12
    tracks_all = []
    for i in range(months):
        s_time = t_time - 3600 * 24 * 30 * (i + 1)
        e_time = t_time - 3600 * 24 * 30 * i  # 接口最大只能查询1个月
        time_box = [s_time, e_time]
        for source in track_source_list:
            # print(f"source:{source}\tmonth:{i},\ttime_box:{time_box}")
            tracks = get_tracks(source, park_traj_buffer_sp, time_box)
            if tracks is None or len(tracks) == 0:
                continue
            for track in tracks:
                point = Point(track['x'], track['y'])
                if not park_traj_buffer_sp.contains(point):
                    continue
                tracks_all.append(track)
    return tracks_all


def get_tracks(source, polygon, time_box):
    """查询指定geo范围内 指定时间范围内，指定源的采集轨迹信息
    """
    # 测试
    # polygon = "POLYGON((121.19358384038789 31.345139316112146,121.19486873891255 31.3451499545161,121.19445452757095
    # 31.343814252337854,121.19299706172076 31.34427125335543,121.19358384038789 31.345139316112146))"
    # polygon = "POLYGON((121.19427589755615 31.345212152768717,121.19370310825542 31.344831587313898,121.19403272419876
    # 31.344448613896315,121.1946174696994 31.344856289809602,121.19427589755615 31.345212152768717))"
    stime, etime = time_box
    # # 办公网
    # track_url_v2 = 'http://m.map.baidu.com:8014/api/v2/track'
    # # 内网
    track_url_v2 = 'http://img.map.baidu-int.com/dedc/api/v2/track'
    appkey = 'aoi_park'
    sc = '4f4999b72996a70b372d0b2f1a28e72b'
    left, bottom, right, top = polygon.bounds
    sc_str = '%sappkey%sbottom%fetime%sleft%fright%fsource%sstime%stop%ftype10%s' % \
             (sc, appkey, bottom, etime, left, right, source, stime, top, sc)
    sign = hashlib.md5(sc_str.encode('utf-8')).hexdigest()

    url = track_url_v2 + '?left=%f&right=%f&top=%f&type=10&bottom=%f&' \
                         'appkey=%s&source=%s&stime=%s&etime=%s&&sign=%s' % \
          (left, right, top, bottom, appkey, source, stime, etime, sign)
    # print("url: {}".format(url))
    res = http_get(url)
    # print(f"res:{res}")
    if res is None:
        print("get_track_pics error: {}".format(url))
        return
    return res.get('tracks')


def get_track_detail_by_cdid(cdid):
    """
    通过cdid获取轨迹详情
    """
    url = "http://m.map.baidu.com:8013/tracklist/{}.track".format(cdid)
    try_cnt = 3
    while try_cnt > 0:
        try:
            response = requests.get(url, timeout=(5, 30))
            res = response.json()
            return res
        except Exception as e:
            print(f"request_failed, sleep: {url}")
            sleep_time = random.uniform(0.1, 2.0)
            time.sleep(sleep_time)
        try_cnt -= 1
        if try_cnt <= 0:
            print(f"HTTP GET request failed: {url}")
    return {}


def http_get(url, try_cnt=10):
    """
    get 请求
    """
    sleep_time = random.uniform(0.01, 0.1)
    time.sleep(sleep_time)
    while try_cnt > 0:
        try:
            response = requests.get(url, timeout=(5, 30))
            res = response.json()
            if res.get('errno') == 0:
                return res
        except Exception as e:
            # log.error("HTTP GET request failed: {} \n{}".format(e, url))
            print(f"request_failed, sleep: {url}")
            sleep_time = random.uniform(0.1, 2.0)
            time.sleep(sleep_time)
            # print(f"http_get_error\t{url}\t{e}")
        try_cnt -= 1
        if try_cnt <= 0:
            print(f"HTTP GET request failed: {url}")
    return None
