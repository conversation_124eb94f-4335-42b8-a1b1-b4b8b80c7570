# Copyright (c) Alibaba, Inc. and its affiliates.
"""
Copyright (c) Alibaba, Inc. and its affiliates.
"""

import os, sys, torch
import codecs
import cv2
import time
from typing import List, Literal
from swift.llm import (InferEngine, InferRequest, PtEngine, RequestConfig,
                       load_dataset, get_model_tokenizer, get_template, ModelType, BaseArguments)
from swift.plugin import InferStats
from swift.tuners import Swift
from PIL import Image
from swift.llm import (InferEngine, InferRequest, InferClient, RequestConfig, load_dataset, run_deploy,
                       DeployArguments)
from swift.plugin import InferStats
import base64
from io import BytesIO
import requests
import numpy as np
import mimetypes
import json
from datetime import datetime
from urllib.request import urlopen
import random


def url_to_img(url):
    """
    url
    """

    resp = urlopen(url)
    image = np.asarray(bytearray(resp.read()), dtype="uint8")
    image = cv2.imdecode(image, cv2.IMREAD_COLOR)
    return image


def infer_batch(engine: 'InferEngine', infer_requests: List['InferRequest']):
    """
    infer_batch
    """
    request_config = RequestConfig(max_tokens=128, temperature=0.3)
    metric = InferStats()
    resp_list = engine.infer(infer_requests, request_config, metrics=[metric])
    query0 = infer_requests[0].messages[0]['content']
    # print(f'query0: {query0}')
    # print(f'response0: {resp_list[0].choices[0].message.content}')
    # print(f'metric: {metric.compute()}')
    # metric.reset()  # reuse
    return resp_list


def get_data(mm_type: Literal['text', 'image', 'video', 'audio'], query, images):
    """
    get_data
    """
    data = {}
    if mm_type == 'text':
        messages = [{'role': 'user', 'content': query}]
    elif mm_type == 'image':
        # The number of <image> tags must be the same as len(images).
        messages = [{'role': 'user', 'content': query}]
        # Support URL/Path/base64/PIL.Image
        # print("image:", images)
        data['images'] = images
    data['messages'] = messages
    return data


def process_pic(image, max_size):
    """
    process_pic
    """
    # 获取图像的原始宽度和高度
    height, width = image.shape[:2]
    # 计算缩放比例
    scale = 1
    if width > max_size or height > max_size:
        scale = min(max_size / width, max_size / height)
    # 计算新的宽度和高度
    new_width = int(width * scale)
    new_height = int(height * scale)
    # 使用cv2.resize进行缩放
    resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
    # 对图像进行base64编码
    _, imagebytes = cv2.imencode('.jpg', resized_image)
    base64_image = base64.b64encode(imagebytes).decode()
    image_url = f"data:image/jpeg;base64,{base64_image}"
    return image_url


def log(message):
    """
    log
    """

    # 获取当前时间，格式化为字符串，精确到秒
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    # 打印日志信息，包含当前时间和消息内容
    print(f"[{current_time}] {message}")


def run_single(host, port):
    """
    run_single
    """
    port = int(port)
    engine = InferClient(host=host, port=port)
    try:
        local_path = "./VP_AA10024080621172_1728780689020_20241204992781ra_1567912515037270991.png"
        img2 = cv2.imread(local_path)
        bid_image = process_pic(local_path, 1024)
        query = "<image>这张图中是否存在政府规划的路侧停车位？只要回答‘是’或‘否’即可，不用展开阐述"
        infer_requests = [InferRequest(**get_data('image', query, [bid_image]))]
        resp_list = infer_batch(engine, infer_requests)
        response_content = resp_list[0].choices[0].message.content
    except Exception as e:
        print(e)
        response_content = "req_error"
    log(f"{query}\t{local_path}\t{response_content}")


def run_single_boskey(engine, boskey):
    """
    run_single_boskey
    """
    try:
        # local_path = "./VP_AA10024080621172_1728780689020_20241204992781ra_1567912515037270991.png"
        url = "http://m.map.baidu.com:8011/%s" % boskey

        # image = cv2.imread(img_path)
        image = url_to_img(url)
        bid_image = process_pic(image, 1024)

        query = "<image>这张图中是否存在政府规划的路侧停车位？只要回答‘是’或‘否’即可，不用展开阐述"
        infer_requests = [InferRequest(**get_data('image', query, [bid_image]))]
        resp_list = infer_batch(engine, infer_requests)
        response_content = resp_list[0].choices[0].message.content
    except Exception as e:
        print(e)
        response_content = "req_error"

    return response_content

def run_one_boskey_random(boskey):

    """
    run_single_boskey
    """
    #host_list = [("*************", "8000"), ("************", "8000"), ("**************", "8000")]
    #host_list = [("************", "800"), ("************", "8002"), ("************", "8003")]

    host_list = [("************", "8002")]
    random_item = random.choice(host_list)
    engine = InferClient(host=random_item[0], port=random_item[1])

    try:
        # local_path = "./VP_AA10024080621172_1728780689020_20241204992781ra_1567912515037270991.png"
        url = "http://m.map.baidu.com:8011/%s" % boskey

        # image = cv2.imread(img_path)
        image = url_to_img(url)
        bid_image = process_pic(image, 1024)

        query = "<image>这张图中是否存在政府规划的路侧停车位？只要回答‘是’或‘否’即可，不用展开阐述"
        infer_requests = [InferRequest(**get_data('image', query, [bid_image]))]
        resp_list = infer_batch(engine, infer_requests)
        response_content = resp_list[0].choices[0].message.content
    except Exception as e:
        print(e)
        response_content = "req_error"

    return response_content

def data2file(datas, out_put_path, mode='w'):
    """
    保存文件
    """
    if mode == 'a':
        fout = open(out_put_path, 'a')
    else:
        fout = open(out_put_path, 'w')
    for data in datas:
        if isinstance(data, list) or isinstance(data, tuple):
            line = '\t'.join(map(str, data))
        else:
            line = str(data)
        fout.write(line + '\n')
    fout.close()
