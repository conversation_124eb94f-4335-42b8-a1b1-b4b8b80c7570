# -*- encoding: utf-8 -*-
"""
double check是否正常下线
"""

from src.parking_roadside.db.model import PGModel
from src.parking_roadside.api.send_poi import get_info_by_bid


def fetch_full_offline_list():
    """
    获取所有需要下线的bid
    """
    with PGModel() as pg:
        sql = """
        select * from parking_roadside_bid_map where status in (0, 2)
        """
        cursor = pg.get_cursor('online')
        cursor.execute(sql)
        res = cursor.fetchall()
        return res


def run():
    """
    主函数
    """
    offline_list = fetch_full_offline_list()
    for item in offline_list:
        bid = item['bid']
        print('start', bid)
        poi_info = get_info_by_bid(bid)
        status = 0
        if (poi_info is not None and poi_info.get('data') and poi_info.get('data').get('base')
                and poi_info.get('data').get('base').get('status')):
            status = int(poi_info.get('data').get('base').get('status'))
        if status == 1:
            print('need_offline', bid)


if __name__ == '__main__':
    run()
