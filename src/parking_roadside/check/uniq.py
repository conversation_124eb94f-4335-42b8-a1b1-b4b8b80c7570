# -*- encoding: utf-8 -*-
"""
线上判重
"""
import json

from src.parking_roadside.db.pg import fetch_parking_line_poi
from src.parking_roadside.api.send_poi import get_info_by_bid, update_poi_area, update_poi_relation
from src.parking_roadside.db.master_back import query_parking_ach_by_pid


def run(city=""):
    """
    city: 城市名称 为空全量
    """
    res = fetch_parking_line_poi(city)
    link_map = {}
    dup_list = {}
    for row in res:
        bid = row['bid']
        poi_info = get_info_by_bid(bid)
        if poi_info is None or not poi_info.get('data') or not poi_info.get('data').get('base'):
            print("no poi info found", bid, poi_info)
            return
        data = poi_info.get('data')
        base = data.get('base')
        ext = json.loads(base['extension'])
        if not ext.get('road_relation'):
            continue
        road_relation = json.loads(ext.get('road_relation'))
        for item in road_relation['link_info']:
            link_id = item['link_id']
            if link_id in link_map and bid != link_map[link_id]:
                print("duplicate link", link_id, bid, link_map[link_id])
                if bid not in dup_list:
                    dup_list[bid] = set()
                dup_list[bid].add(link_map[link_id])
            else:
                link_map[link_id] = bid
    return dup_list


def repush_by_master_back(bid, field_type):
    """
    通过母库重新推送
    """
    back_info = query_parking_ach_by_pid(bid)
    if back_info is None:
        print("no back info found", bid)
        return
    if field_type == "area":
        update_poi_area(bid, back_info['area_wkt'])
    elif field_type == "road_relation":
        road_relation = json.loads(back_info['road_relation'])
        link_info = road_relation['link_info']
        new_link_info = []
        link_map = []
        for item in link_info:
            link_id = item['link_id']
            if link_id in link_map:
                continue
            link_map.append(link_id)
            new_link_info.append(item)
        new_road_relation = {'link_info': new_link_info}
        update_poi_relation(bid, new_road_relation)


if __name__ == '__main__':
    run()
