# -*- coding: utf-8 -*-
"""
质检启动
"""
import sys

from shapely import wkt

from check import uniq
from api.send_poi import get_info_by_bid
from diff_offline_poi import offline_jz


def main(city):
    """
    入口方法
    """
    dup_list = uniq.run(city)
    for bid_1, dup_2 in dup_list.items():
        if len(dup_2) == 0:
            continue
        for bid_2 in dup_2:
            print('diff', bid_1, bid_2)
            diff_offline(bid_1, bid_2, True)


def diff_offline(bid_1, bid_2, send=False):
    """
    比较两个bid哪个该下线
    """
    poi_info1 = get_info_by_bid(bid_1)
    poi_info2 = get_info_by_bid(bid_2)
    data1 = poi_info1.get('data')
    data2 = poi_info2.get('data')
    area_1 = None
    area_2 = None
    offline_bid = ""
    if data1.get('travel_info').get('parking_info') and data1.get('travel_info').get('parking_info').get('area'):
        area_1_wkt = data1.get('travel_info').get('parking_info').get('area')
        area_1 = wkt.loads(area_1_wkt)
    if data2.get('travel_info').get('parking_info') and data2.get('travel_info').get('parking_info').get('area'):
        area_2_wkt = data2.get('travel_info').get('parking_info').get('area')
        area_2 = wkt.loads(area_2_wkt)
    if not area_1 or not area_2:
        return False
    else:
        intersection = area_1.intersection(area_2)
        union = area_1.union(area_2)
        if union.area == 0:
            return 0.0
        iou = intersection.area / union.area
        if iou > 0.8:  # 交并比大于 80% 则认为是同一个停车场，直接删除一个
            if area_1.area > area_2.area:
                print('offline', bid_2, bid_1 + " is butter " + str(iou))
                offline_bid = bid_2
            else:
                print('offline', bid_1, bid_2 + " is butter " + str(iou))
                offline_bid = bid_1
        if offline_bid == "":
            inter_1 = area_1.intersection(union)
            iou_1 = inter_1.area / union.area

            inter_2 = area_2.intersection(union)
            iou_2 = inter_2.area / union.area
            if iou_1 > 0.9:  # 交并比低于80% 但是 bid_1 占了融合后的90% 以上，则删除 bid_2
                print('offline', bid_2,
                      '{} is large iou:{}, iou_choose:{}, iou_offline: {}'.format(bid_1, iou, iou_1, iou_2))
                offline_bid = bid_2
            elif iou_2 > 0.9:
                print('offline', bid_1,
                      '{} is large iou:{}, iou_choose:{}, iou_offline: {}'.format(bid_2, iou, iou_2, iou_1))
                offline_bid = bid_1
        if offline_bid == "":
            print("keep both", bid_1, bid_2, iou)
        elif send:
            offline_jz(offline_bid)


if __name__ == '__main__':
    # with open('dup_list.txt', 'r') as f:
    #     lines = f.readlines()
    #     for line in lines:
    #         _bid_1, _bid_2 = line.strip().split()
    #         diff_offline(_bid_1, _bid_2)
    _city = ""
    if len(sys.argv) > 1:
        _city = sys.argv[1]
    main(_city)
