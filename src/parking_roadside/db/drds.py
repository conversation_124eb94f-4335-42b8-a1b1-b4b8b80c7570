# -*- coding: utf-8 -*-
"""
bee_flow mysql操作
parking_line_infer_res迁移至pg库
"""
import pymysql.cursors
from src.parking_roadside.db.model import PGModel


def add_parking_line_infer_res(pic_id, cdid, link_id, version, have_line, info_res_json):
    """
    添加停车线推理结果
    """
    with PGModel() as pg_model:
        query_sql = """
        select * from parking_line_infer_result where pic_id = %s 
        """
        insert_sql = """
        insert into parking_line_infer_result (pic_id, cdid, link_id, version, have_line, infer_result)
        values (%s, %s, %s, %s, %s, %s)
        """
        update_sql = """
        update parking_line_infer_result set have_line = %s, infer_result = %s where pic_id = %s;
        """
        cursor = pg_model.get_cursor('infer')
        cursor.execute(query_sql, (pic_id,))
        res = cursor.fetchone()
        if res is None:
            cursor.execute(insert_sql, (pic_id, cdid, link_id, version, have_line, info_res_json))
        if res is not None:
            if have_line != -1 and res['have_line'] == -1:
                cursor.execute(update_sql, (have_line, info_res_json, pic_id))


def bak_add_parking_line_infer_res(pic_id, cdid, link_id, version, have_line, info_res_json):
    """
    添加停车线推理结果
    """
    with pymysql.connect(
            host='*************',
            port=5730,
            user='bee_flow',
            password='nl4c/mqeTcsgpH',
            database='bee_flow',
            charset='utf8') as _bee_flow_conn:
        query_sql = """
        select * from parling_line_infer_result where pic_id = %s 
        """
        insert_sql = """
        insert into parling_line_infer_result (pic_id, cdid, link_id, version, have_line, infer_result)
        values (%s, %s, %s, %s, %s, %s)
        """
        update_sql = """
        update parling_line_infer_result set have_line = %s, infer_result = %s where pic_id = %s;
        """
        _bee_flow_conn.begin()
        cursor = _bee_flow_conn.cursor(pymysql.cursors.DictCursor)
        cursor.execute(query_sql, (pic_id,))
        res = cursor.fetchone()
        if res is None:
            cursor.execute(insert_sql, (pic_id, cdid, link_id, version, have_line, info_res_json))
        if res is not None:
            if have_line != -1 and res['have_line'] == -1:
                cursor.execute(update_sql, (have_line, info_res_json, pic_id))
        _bee_flow_conn.commit()


def fetch_parking_line_infer_res(pic_id):
    """
    查询停车线推理结果
    """
    with PGModel() as pg_model:
        cursor = pg_model.get_cursor('infer')
        query = """
        select * from parking_line_infer_result where pic_id = %s 
        """
        # 使用游标执行查询
        cursor.execute(query, (pic_id,))
        return cursor.fetchone()


def bak_fetch_parking_line_infer_res(pic_id):
    """
    查询停车线推理结果
    """
    with PGModel() as pg_model:
        query = """
        select * from parling_line_infer_result where pic_id = %s 
        """
        # 使用游标执行查询
        cursor = pg_model.get_cursor('infer')
        cursor.execute(query, (pic_id,))
        return cursor.fetchone()


def fetch_parking_line_infer_res_by_link(link_id):
    """
    根据link_id查询停车线推理结果
    """
    with PGModel() as pg_model:
        query = """
        select * from parking_line_infer_result where link_id = %s 
        -- and have_line > 1
        """
        # 使用游标执行查询
        cursor = pg_model.get_cursor('infer')
        cursor.execute(query, (link_id,))
        return cursor.fetchall()


def bak_fetch_parking_line_infer_res_by_link(link_id):
    """
    根据link_id查询停车线推理结果
    """
    with pymysql.connect(
            host='*************',
            port=5730,
            user='bee_flow',
            password='nl4c/mqeTcsgpH',
            database='bee_flow',
            charset='utf8') as _bee_flow_conn:
        query = """
        select * from parling_line_infer_result where link_id = %s 
        -- and have_line > 1
        """
        # 使用游标执行查询
        cursor = _bee_flow_conn.cursor(pymysql.cursors.DictCursor)
        cursor.execute(query, (link_id,))
        return cursor.fetchall()


def update_parking_infer_by_pic(pic_id, have_line, info_res):
    """
    更新停车线推理结果
    """
    with PGModel() as pg_model:
        query = """
        update parking_line_infer_result set have_line = %s, infer_result = %s where pic_id = %s
        """
        # 使用游标执行查询
        cursor = pg_model.get_cursor('infer')
        cursor.execute(query, (have_line, info_res, pic_id))


def bak_update_parking_infer_by_pic(pic_id, have_line, info_res):
    """
    更新停车线推理结果
    """
    with pymysql.connect(
            host='*************',
            port=5730,
            user='bee_flow',
            password='nl4c/mqeTcsgpH',
            database='bee_flow',
            charset='utf8') as _bee_flow_conn:
        query = """
        update parling_line_infer_result set have_line = %s, infer_result = %s where pic_id = %s
        """
        # 使用游标执行查询
        cursor = _bee_flow_conn.cursor()
        cursor.execute(query, (have_line, info_res, pic_id))
        _bee_flow_conn.commit()


def query_bid_from_push_data(source_id):
    """
    查询推送数据中的bid
    """
    with pymysql.connect(
            host='*************',
            port=5730,
            user='bee_flow',
            password='nl4c/mqeTcsgpH',
            database='bee_flow',
            charset='utf8') as _bee_flow_conn:
        query = """
        select * from parking_push_data where source_id = %s and bid != ''
        order by push_id desc
        """
        # 使用游标执行查询
        cursor = _bee_flow_conn.cursor(pymysql.cursors.DictCursor)
        cursor.execute(query, (source_id,))
        return cursor.fetchone()


def add_parking_track(chains_uid, link_id_list, center_point, batch_id):
    """
    添加停车线履历
    """
    with pymysql.connect(
            host='*************',
            port=5730,
            user='bee_flow',
            password='nl4c/mqeTcsgpH',
            database='bee_flow',
            charset='utf8') as _bee_flow_conn:
        query = """
        insert into parking_line_track (chains_uid, link_id_list, center_point, batch_id) values (%s, %s, %s, %s)
        """
        # 使用游标执行查询
        cursor = _bee_flow_conn.cursor()
        cursor.execute(query, (chains_uid, link_id_list, center_point, batch_id))
        last_insert_id = cursor.lastrowid
        _bee_flow_conn.commit()
        return last_insert_id


def update_parking_track(pid, result_uid, result_message):
    """
    更新停车线履历
    """
    with pymysql.connect(
            host='*************',
            port=5730,
            user='bee_flow',
            password='nl4c/mqeTcsgpH',
            database='bee_flow',
            charset='utf8') as _bee_flow_conn:
        query = """
        update parking_line_track set result_uid = %s, result_message = %s where id = %s
        """
        # 使用游标执行查询
        cursor = _bee_flow_conn.cursor()
        cursor.execute(query, (result_uid, result_message, pid))
        _bee_flow_conn.commit()
