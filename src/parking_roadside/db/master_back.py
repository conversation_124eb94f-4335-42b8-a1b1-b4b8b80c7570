# -*- coding: utf-8 -*-
"""
poi库查询
"""

import psycopg2.extras

# psql -h ************** -p 5432 -U master_back_se_ro -d master_back -Wmapread
master_back_info = {
    "database": "master_back",
    "host": "*************",
    "port": 8033,
    "user": "master_back",
    "password": "master_back",
}


def query_parking_ach_by_pid(bid):
    """
    查询pid对应的成果
    """
    with psycopg2.connect(
            database=master_back_info["database"],
            user=master_back_info["user"],
            password=master_back_info["password"],
            host=master_back_info["host"],
            port=master_back_info["port"],
    ) as _master_back_info:
        _master_back_info.autocommit = True
        query = """
        select *, 
            st_astext(area) as area_wkt
        from parking where bid = %s
"""
        cursor = _master_back_info.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cursor.execute(query, (bid,))
        return cursor.fetchone()
