# -*- coding: utf-8 -*-
"""
# 脚本公用一个链接
# 每个方法单独创建、释放？ 太消耗性能
# 连接池？ 如果定义5个链接，同时启100个脚本，就要创建500个连接，占用太多, 定义1、2个和单独一个没啥区别
# 仅限pg， mysql有链接代理，不整这个费事
# 注意！！！ 多线程会有问题
# 目前pg也有连接池了，非特殊情况，默认还是短链接
"""
import time

import psycopg2.extras
import requests

pg_config_dict = {
    "online": {
        "database": "poi_online",
        "host": "*************",
        "port": 8032,
        "user": "poi_aoi_rw",
        "password": "poi_aoi_rw",
    },
    "online_rw": {
        "database": "poi_online_rw",
        "host": "*************",
        "port": 8032,
        "user": "poi_aoi_rw",
        "password": "poi_aoi_rw",
    },
    # psql -h ************** -p 7432 -U master_back_user_24_1223_se_rw -W mdsavjqp master_back_user_24_1223
    # 路链用数据库
    "chains_db": {
        "database": "master_back_user_24_1223",
        "user": "master_back_user_24_1223_se_rw",
        "password": "mdsavjqp",
        "host": "**************",
        "port": 7432
    },
    "master_back": {
        "database": "master_back",
        "user": "master_back",
        "password": "master_back",
        "host": "*************",
        "port": 8033
    },
    "trans_id_info": {
        "database": "trans_id",
        "host": "*************",
        "port": 5532,
        "user": "trans_id_se_rw",
        "password": "cpmkukky",
    },
    "infer": {
        "database": "aoi_dw",
        "host": "*************",
        "port": 8034,
        "user": "aoi_dw_r",
        "password": "aoi_dw_pw_2025",
    }
}
global_pg_dict = {}
_expire_time = 0


def get_link_info():
    """
    获取link_info数据库连接信息
    """
    url = 'http://mapde-poi.baidu-int.com/prod/api/rcmanage/naming'
    response = requests.post(url)
    if response.status_code == 200:
        # 解析JSON响应
        data = response.json().get("data", {})

        # 填充link_info
        _pg_road_info = {
            "host": data.get("host"),
            "port": data.get("port"),
            "user": data.get("user"),
            "password": data.get("passwd"),
            "database": data.get("db")
        }
        return _pg_road_info


class PGModel:
    """
    """
    conn_dict = {}
    short_conn = True

    def __init__(self, short_conn=True):
        if not short_conn:
            self.conn_dict = global_pg_dict
        else:
            self.conn_dict = {}
        self.short_conn = short_conn

    def __enter__(self):
        return self

    def connect(self, name):
        """建立新的数据库连接"""
        if name == "road":
            pg_config_dict['road'] = get_link_info()
        elif name not in pg_config_dict:
            raise ValueError(f"{name} is not a valid database configuration.")
        if name in self.conn_dict and self.conn_dict[name]:
            self.conn_dict[name].close()  # 关闭现有连接
        pg_config = pg_config_dict[name]
        self.conn_dict[name] = psycopg2.connect(
            database=pg_config["database"],
            user=pg_config["user"],
            password=pg_config["password"],
            host=pg_config["host"],
            port=pg_config["port"],
        )
        if not self.short_conn:
            print("建立长链接", name)
            global global_pg_dict, _expire_time
            global_pg_dict[name] = self.conn_dict[name]
            _expire_time = time.time() + 7200
        print("数据库连接已建立", name)

    def get_connection(self, name):
        """获取有效的数据库连接"""
        # 检查连接是否超时
        if not self.short_conn:
            if (name not in self.conn_dict
                    or self.conn_dict[name] is None
                    or self.conn_dict[name].closed
                    or time.time() > _expire_time):
                self.connect(name)
            return self.conn_dict[name]
        else:
            self.connect(name)
        return self.conn_dict[name]

    def get_cursor(self, name):
        """获取游标"""
        conn = self.get_connection(name)
        conn.autocommit = True
        return conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.short_conn:
            try:
                for key, value in self.conn_dict.items():
                    if value:
                        value.close()
            except Exception as e:
                pass
