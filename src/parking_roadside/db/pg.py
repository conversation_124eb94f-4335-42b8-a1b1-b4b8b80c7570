# -*- coding: utf-8 -*-
"""
poi库查询
"""
import uuid
import sys
import os

sys.path.append(os.path.abspath(os.path.dirname(__file__)))

from road import fetch_link_by_id
from model import PGModel


def fetch_aoi_by_line(line_geom_str):
    """
    查询aoi
    """
    with PGModel() as dao:
        query = """
        select *, st_astext(geom) as geom_wkt from blu_face 
        where 
            ST_Intersects(geom, st_geomfromtext(%s, 4326)) and src != 'SD' and kind != '52'
            and st_area(geom) < 0.00015
        ;
        """
        # 使用游标执行查询
        cursor = dao.get_cursor("master_back")
        cursor.execute(query, (line_geom_str,))
        return cursor.fetchall()


def add_road_chains(batch_id, link_ids):
    """
    添加道路链接
    """
    with PGModel() as pg_online:
        link_ids.sort()
        link_ids_str = ','.join(link_ids)
        list_uuid = str(uuid.uuid5(uuid.NAMESPACE_DNS, link_ids_str)).replace('-', '')
        values = []
        for link_id in link_ids:
            link_item = fetch_link_by_id(link_id)
            values.append((list_uuid, batch_id, link_id, "SRID=4326;" + link_item['geom_wkt']))
        query = """
        insert into parking_road_chains (uid, batch_id, link_id, link_geom) values (%s, %s, %s, %s)
        """
        # 使用游标执行查询
        cursor = pg_online.get_cursor("chains_db")
        cursor.executemany(query, values)


def fetch_road_chains(link_id, batch_id):
    """
    查询道路链接
    """
    with PGModel() as dao:
        query = """
        select
            a.uid as uid,
            string_agg(link_id, ',') as link_ids_str,
            st_astext(st_union(link_geom)) as geom_wkt
        from parking_road_chains a
        where a.uid = (
            select uid
            from parking_road_chains
            where link_id = %s and batch_id = %s
            order by id desc
            limit 1
        )
        group by a.uid;
    """
        cursor = dao.get_cursor("chains_db")
        cursor.execute(query, (link_id, batch_id))
        return cursor.fetchone()


def add_achievement(values):
    """
    添加识别成果
    """
    with PGModel() as dao:
        print("insert_achievement", values)
        query = """
        insert into parking_line_turing_work (uid, batch_id, link_id_list, line_geom, area_geom, status)
        values (%s, %s, %s, %s, %s, -2)
    """
        cursor = dao.get_cursor("online_rw")
        return cursor.executemany(query, values)


def add_update_achievement(values):
    """
    更新识别成果
    """
    with PGModel() as dao:
        print("update_achievement", values)
        query = """
        insert into parking_line_turing_work 
        (uid, batch_id, link_id_list, line_geom, area_geom, prev_work_id, work_type, status)
        values (%s, %s, %s, %s, %s, %s, 1, -2)
    """
        cursor = dao.get_cursor("online_rw")
        return cursor.executemany(query, values)


def add_achievement_tmp(work_id):
    """
    添加识别成果
    """
    with PGModel() as dao:
        print("insert_achievement", work_id)
        query = """
        insert into parking_line_turing_work 
        (uid, batch_id, link_id_list, line_geom, area_geom, turing_id, extend, message, bp_message) 
        select 
            uid|| 'a', 'repush_haidian_1130_direct_push', link_id_list, line_geom, area_geom, turing_id, extend, id, bp_message 
        from parking_line_turing_work where id = %s
    """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (work_id,))


def add_achievement_ch_tmp(work_id, batch_id, line, polygon, message):
    """
    添加识别成果
    """
    with PGModel() as dao:
        print("insert_achievement", work_id)
        query = """
        insert into parking_line_turing_work 
        (uid, status, batch_id, link_id_list, line_geom, area_geom, turing_id, extend, message, bp_message, 
        work_type, prev_work_id) 
        select 
            uid, status, %s, link_id_list, %s, %s, turing_id, extend, %s, bp_message, work_type, prev_work_id
        from parking_line_turing_work where id = %s ;
    """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (batch_id, "SRID=4326;" + line, "SRID=4326;" + polygon, message, work_id))


def add_auto_update_link_ach(work_id: int, link_id_str: str, batch_id: str, message: str, side_json: str):
    """
    添加自动更新 link 成果
    prev_work_id, work_type
    """
    with PGModel() as dao:
        print("insert_achievement", work_id)
        qry = """
        insert into parking_line_turing_work 
        (uid, status, batch_id, link_id_list, line_geom, area_geom, turing_id, extend, 
        message, work_type, prev_work_id, side_json) 
        select 
            uid, 0, %s, %s, turing_line_geom, turing_area_geom, turing_id, extend, %s, 1, %s, %s
        from parking_line_turing_work where id = %s ;
        """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(qry, (batch_id, link_id_str, message, work_id, side_json, work_id))


def add_upd_identification(work_id: int, link_id: str, batch_id: str):
    """
    添加更新识别记录
    """
    with PGModel() as dao:
        print("add_upd_identification", work_id)
        qry = f"""
        insert into parking_roadside_upd_ir 
        (work_id, link_id, batch_id) 
        values (%s, %s, %s)
        """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(qry, (work_id, link_id, batch_id))


def query_work_intersects(line_geom, pids):
    """
    查询成果差分
    """
    with PGModel() as dao:
        query = """
        select *,
            st_astext(area_geom) as area_geom_wkt,
            st_astext(line_geom) as line_geom_wkt
        from parking_line_turing_work where 
        st_intersects(line_geom, st_geomfromtext(%s, 4326)) and status in (2, 3, 4, 5) and id not in %s
"""
        cursor = dao.get_cursor("online")
        cursor.execute(query, (line_geom, tuple(pids)))
        return cursor.fetchall()


def query_work_by_turing(turing_id):
    """
    通过tuling id查询成果
    """
    with PGModel() as dao:
        query = """
            select *,
                st_astext(area_geom) as area_geom_wkt,
                st_astext(line_geom) as line_geom_wkt,
                st_astext(turing_area_geom) as turing_area_geom_wkt
            from parking_line_turing_work where 
            turing_id = %s 
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (turing_id,))
        return cursor.fetchone()


def query_work_by_uid(uid):
    """
    通过uid查询成果
    """
    with PGModel() as dao:
        query = """
            select *, 
                st_astext(area_geom) as area_geom_wkt,
                st_astext(line_geom) as line_geom_wkt,
                st_astext(turing_area_geom) as turing_area_geom_wkt
            from parking_line_turing_work where 
            uid = %s 
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (uid,))
        return cursor.fetchall()


def query_work_by_batch_id(batch_id):
    """
    通过批次号查询成果
    """
    with PGModel() as dao:
        query = """
            select *, 
                st_astext(area_geom) as area_geom_wkt,
                st_astext(line_geom) as line_geom_wkt
            from parking_line_turing_work where 
            batch_id = %s 
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (batch_id,))
        return cursor.fetchall()


def query_work_by_geom(geom_str):
    """
    通过区域查询成果
    """
    with PGModel() as dao:
        query = """
        select *, 
            st_astext(area_geom) as area_geom_wkt,
            st_astext(line_geom) as line_geom_wkt
        from parking_line_turing_work where 
        st_intersects(area_geom, st_geomfromtext(%s, 4326))
"""
        cursor = dao.get_cursor("online")
        cursor.execute(query, (geom_str,))
        return cursor.fetchall()


def query_work_by_link_id(link_id):
    """
    通过link_id查询成果
    """
    with PGModel() as dao:
        query = """
        select *, 
            st_astext(area_geom) as area_geom_wkt,
            st_astext(line_geom) as line_geom_wkt
        from parking_line_turing_work where 
        link_id_list like %s
"""
        cursor = dao.get_cursor("online")
        cursor.execute(query, ('%%{}%%'.format(link_id),))
        return cursor.fetchall()


def fetch_rebuild_work():
    """
    查询需要重建的成果
    """
    with PGModel() as dao:
        query = """
        select *, st_astext(area_geom) as area_geom_wkt
        from parking_line_turing_work 
        where 
            status = 7 
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query)
        return cursor.fetchall()


def fetch_online_roadside_parking(geom_str):
    """
    查询线上路侧停车位
    """
    with PGModel() as dao:
        query = """
            select * , st_astext(gcj_geom) as gcj_geom_wkt 
            from park_online_data 
            where 
            show_tag in ('路侧停车场', '路侧停车位', '临时停车点', '临时停车场' )
            and st_intersects(st_geomfromtext(%s, 3857), bd_geom)
            and park_spec = 0
            """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (geom_str,))
        return cursor.fetchall()


def fetch_online_roadside_parking_bygcj(geom_str):
    """
    查询线上路侧停车位
    """
    with PGModel() as dao:
        query = """
            select * , 
                st_astext(gcj_geom) as gcj_geom_wkt, 
                st_astext(st_buffer(gcj_geom::geography, 60)) as gcj_geom_wkt_buffer
            from park_online_data 
            where 
            show_tag in ('路侧停车场', '路侧停车位', '临时停车点', '临时停车场' )
            and park_spec = 0
            and st_intersects(st_geomfromtext(%s, 4326), gcj_geom)
            """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (geom_str,))
        return cursor.fetchall()


def query_online_park_by_bid(bid):
    """
    查询线上停车场数据
    """
    with PGModel() as dao:
        query = """
            select * , 
                st_astext(gcj_geom) as gcj_geom_wkt, 
                st_astext(st_buffer(gcj_geom::geography, 60)) as gcj_geom_wkt_buffer
            from park_online_data 
            where 
            bid = %s
            """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (bid,))
        return cursor.fetchone()


def fetch_work_to_be_diff():
    """
    查询需要差异化的成果
    """
    with PGModel() as dao:
        query = """
        select 
            a.bid 
        from parking_roadside_bid_map a 
        where a.status = 1
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query)
        return cursor.fetchall()


def fetch_work_to_be_diff_by_create(created_at):
    """
    查询需要差异化的成果
    """
    with PGModel() as dao:
        query = """
        select 
            bid 
        from parking_roadside_bid_map 
        where status = 1
         and created_at >= %s
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (created_at,))
        return cursor.fetchall()


def fetch_work_to_be_update_bid():
    """
    查询需要更新bid的成果
    """
    with PGModel() as dao:
        query = """
        select 
            *, st_astext(area_geom) as area_geom_wkt, 
        st_astext(turing_area_geom) as turing_area_geom_wkt
        from parking_line_turing_work 
        where status = 3
    """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(query)
        return cursor.fetchall()


def fetch_work_res_by_link(link_id):
    """
    查询link_id对应的成果
    """
    with PGModel() as dao:
        query = """
        select *, st_astext(area_geom) as area_geom_wkt
        from parking_line_turing_work a, parking_roadside_link_map b 
        where 
        a.id = b.work_id and 
        link_id_list = %s and status in (3, 5) and turing_result = 1
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (link_id,))
        return cursor.fetchall()


def fetch_work_res_by_links(link_ids):
    """
    查询link_id对应的成果
    """
    with PGModel() as dao:
        query = """
        select *, st_astext(area_geom) as area_geom_wkt
        from parking_line_turing_work a, parking_roadside_link_map b 
        where 
        a.id = b.work_id and 
        link_id_list in %s and status in (3, 5) and turing_result = 1
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (tuple(link_ids),))
        return cursor.fetchall()


def query_bid_by_uid(uid):
    """
    查询bid
    """
    with PGModel() as dao:
        query = """
        select * from parking_roadside_bid_map where uid = %s
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (uid,))
        return cursor.fetchone()


def query_work_by_link_id_list(link_id_list):
    """
    查询link_id_list对应的成果
    """
    with PGModel() as dao:
        query = """
        select *, 
            st_astext(area_geom) as area_geom_wkt,
            st_astext(line_geom) as line_geom_wkt
        from parking_line_turing_work where 
        link_id_list  = %s  and status != 6
"""
        cursor = dao.get_cursor("online")
        cursor.execute(query, (link_id_list,))
        return cursor.fetchone()


def query_juejin_pic(img_id):
    """
    查询掘金图片
    """
    with PGModel() as dao:
        query = """
        select * from park_storefront_verify 
        where image_id = %s 
        and type = 1 
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (img_id,))
        return cursor.fetchone()


def query_juejin_pic_by_link(link_id):
    """
    查询link_id对应的掘金图片
    """
    with PGModel() as dao:
        query = """
        select * from park_storefront_verify 
        where image_id like %s 
        and type = 1 
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, ("%" + link_id,))
        return cursor.fetchall()


def query_juejin_pic_by_id(work_id):
    """
    查询work_id对应的掘金图片
    """
    with PGModel() as dao:
        query = """
        select *, st_astext(point) as point_wkt
        from parking_line_jujin_prev a
        left join park_storefront_verify b on a.image_id = b.image_id
        where a.work_id = %s
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (work_id,))
        return cursor.fetchall()


def query_vlm_by_id(work_id):
    """
    查询work_id对应的掘金图片
    """
    with PGModel() as dao:
        query = """
        select *, st_astext(point) as point_wkt
        from parking_line_jujin_prev_model_res a
        left join park_storefront_verify b on a.image_id = b.image_id
        where a.work_id = %s
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (work_id,))
        return cursor.fetchall()


def query_work_by_pid(pid):
    """
    查询pid对应的成果
    """
    with PGModel() as dao:
        query = """
        select *, 
            st_astext(area_geom) as area_geom_wkt,
            st_astext(line_geom) as line_geom_wkt,
            st_astext(turing_area_geom) as turing_area_geom_wkt
        from parking_line_turing_work where id = %s
"""
        cursor = dao.get_cursor("online")
        cursor.execute(query, (pid,))
        return cursor.fetchone()


def update_work_status_by_id(work_id, status, msg):
    """
    更新状态
    """
    with PGModel() as dao:
        query = """ update parking_line_turing_work 
        set status = %s, message = %s, updated_at = now() 
        where id = %s ; """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (status, msg, work_id))


def add_parking_line_juejin_prev(work_id, link_id, batch_id, point_wkt, image_id, north):
    """
    添加掘金前处理图片
    """
    with PGModel() as dao:
        query = """
            insert into parking_line_jujin_prev (work_id, link_id, batch_id, point, image_id, north) 
            values (%s, %s, %s, %s, %s, %s)  
        """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (work_id, link_id, batch_id, "SRID=4326;" + point_wkt, image_id, north))


def add_parking_line_juejin_prev_model_res(link_id, batch_id, point_wkt,
                                           image_id, track_id, work_id,
                                           north, vlm_res, zy_res, is_need_push, work_split_index):
    """
    添加掘金前处理图片+模型结果
    """
    with PGModel() as dao:
        query = """
            insert into parking_line_jujin_prev_model_res (link_id, batch_id, point, 
            image_id, track_id, work_id,
            north, vlm_res, zy_res, is_need_push, work_split_index
            ) 
            values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)  
        """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (link_id, batch_id, "SRID=4326;" + point_wkt, image_id,
                                      track_id, work_id, north, vlm_res, zy_res, is_need_push, work_split_index))


def fetch_juejin_prev_by_area(area_wkt):
    """
    获取区域内掘金投放结果
    :param area_wkt:
    :return:
    :rtype: list
    """
    with PGModel() as dao:
        query = """
        select * from parking_line_jujin_prev where st_intersects(point, %s)
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, ('SRID=4326;' + area_wkt,))
        return cursor.fetchall()


def fetch_parking_line_poi(city_name=""):
    """
    获取成果poi
    """
    with PGModel() as dao:
        cursor = dao.get_cursor("online")
        if city_name == "":
            query = ' select * from parking_roadside_bid_map where status = 1;'
            cursor.execute(query)
        else:
            query = ' select * from parking_roadside_bid_map where status = 1 and city_name = %s;'
            cursor.execute(query, (city_name,))
        return cursor.fetchall()


def add_tmp_parking_ach(work_id, polygon_wkt):
    """
    添加临时停车位成果
    """
    with PGModel() as dao:
        query = """
        insert into tmp_parking_line_ach (work_id, area_geom) values (%s, %s);
    """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (work_id, 'SRID=4326;' + polygon_wkt))


def get_ach_by_link_id(link_id):
    """
    获取成果
    """
    with PGModel() as dao:
        query = """
        select b.bid, b.poi_uid, b.status 
        from parking_line_turing_work a 
        left join parking_roadside_bid_map b on a.uid = b.uid 
        where 
        turing_link_id_list like %s ;
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, ("%%{}%%".format(link_id),))
        return cursor.fetchone()


def add_bid_map(uid, bid, poi_uid, status, city_name):
    """
    新增bid映射关系
    """
    with PGModel() as dao:
        query = """
        insert into parking_roadside_bid_map (uid, bid, poi_uid, status, city_name) values (%s, %s, %s, %s, %s)
    """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (uid, bid, poi_uid, status, city_name))


def update_map_status(bid, status):
    """
    更新映射状态
    """
    with PGModel() as dao:
        query = """
        update parking_roadside_bid_map 
        set status = %s 
        where bid = %s ;
    """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (status, bid))


def get_full_work_by_status_message(status, message):
    """
    通过状态和message获取数据
    """
    with PGModel() as dao:
        query = """
        select *, 
            st_astext(area_geom) as area_geom_wkt,
            st_astext(line_geom) as line_geom_wkt
        from parking_line_turing_work
        where 
        status = %s and message = %s
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query, (status, message))
        return cursor.fetchall()


def update_work_extend(work_id, extend, status, msg):
    """
    更新状态
    """
    with PGModel() as dao:
        query = """ update parking_line_turing_work 
        set status = %s, message = %s, extend = %s, updated_at = now()
        where id = %s ; """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (status, msg, extend, work_id))


def update_turing_work_res_changed(work_id, short_link_id_str, new_line, new_polygon, status, message):
    """
    更新图灵成果
    link有变更
    """
    with PGModel() as dao:
        sql = '''
        update parking_line_turing_work 
            set turing_link_id_list = %s,
            turing_line_geom = %s,
            turing_area_geom = %s,
            status = %s,
            message = %s
        where id = %s'''
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(sql,
                              (short_link_id_str, "SRID=4326;" + new_line, "SRID=4326;" + new_polygon, status, message,
                               work_id))


def update_turing_work_res_no_change(work_id, status, message):
    """
    更新图灵成果，link无变更
    """
    with PGModel() as dao:
        query = """
        update parking_line_turing_work 
            set turing_area_geom = area_geom, 
            turing_line_geom = line_geom, 
            turing_link_id_list = link_id_list, 
            status = %s,
            message = %s
        where id = %s """
        cursor = dao.get_cursor("online_rw")
        return cursor.execute(query, (status, message, work_id))


def get_intersects_near_by(area):
    """
    获取相交的临街poi
    """
    with PGModel() as dao:
        query = """
        select *
        from poi_cluster_area
        where st_intersects(buffered_geom, st_geomfromtext(%s, 4326)) 
        and pv_cate = ANY ('{TOP80-TOP90,TOP90-TOP100}');
        """
        cursor = dao.get_cursor("chains_db")
        cursor.execute(query, ("srid=4326;" + area,))
        return cursor.fetchall()


def query_link_map_by_work_id(work_id):
    """
    查询link map是否已经写入
    """
    with PGModel() as dao:
        query = """
        select * from parking_roadside_link_map where work_id = %s 
    """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(query, (work_id,))
        return cursor.fetchone()


def query_link_map_by_link_ids(link_ids):
    """
    通过link查询map信息
    """
    with PGModel() as dao:
        query = """
            select * from parking_roadside_link_map where long_link_id in %s 
        """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(query, (tuple(link_ids),))
        return cursor.fetchall()


def add_link_map(work_id, link_map):
    """
    写入link map
    """
    with PGModel() as dao:
        # return cursor.execute(query, (work_id, link_id, link_name, link_length, link_direction, link_type, link
        values = []
        query = """
            insert into parking_roadside_link_map (work_id, link_id, long_link_id) values (%s, %s, %s)
        """
        for short_id, long_id in link_map.items():
            values.append((work_id, short_id, long_id))
        # 使用游标执行查询
        cursor = dao.get_cursor("online_rw")
        cursor.executemany(query, values)


def fetch_all_to_be_update():
    """
    获取待更新数据
    """
    with PGModel() as dao:
        query = """
        select * from parking_roadside_upd_ir
        where status = 'INIT'
    """
        cursor = dao.get_cursor("online")
        cursor.execute(query)
        return cursor.fetchall()


def update_change_map(pid, status, new_uid):
    """
    更新待更新列表状态
    """
    with PGModel() as dao:
        query = """
        update parking_roadside_upd_ir
        set status = %s, updated_at = now(), new_uid = %s
        where id = %s and status = 'INIT'
    """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(query, (status, new_uid, pid,))


def get_changed_histories(old_short_link_ids: list) -> list:
    """
    获取改变记录
    """
    with PGModel() as dao:
        query = """
        select *
        from road_release_update_record
        where link_v1_arr && %s
        """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(query, (old_short_link_ids, ))
        return cursor.fetchall()


def maintain_link_v1_arr():
    """
    维护 link_v1_arr 字段
    """
    with PGModel() as dao:
        query = """
        update road_release_update_record set link_v1_arr = string_to_array(link_v1, ',')::text[] 
        where change != 1 AND (array_length(link_v1_arr, 1) = 0 OR link_v1_arr IS NULL)
        """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(query)
        return


def get_road_change_versions() -> list:
    """
    获取道路改变版本信息
    """
    with PGModel() as dao:
        qry = f"""
        select version from road_release_update_record group by version 
        """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(qry)
        infos = cursor.fetchall()
        if not infos:
            return []
        return [item['version'] for item in infos]


def remove_road_change_by_version(version: str):
    """
    删除某个版本的改变记录信息
    """
    with PGModel() as dao:
        qry = f"""
        delete from road_release_update_record where version = %s
        """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(qry, (version, ))
        return


def add_link_invalid_upd_history(bid: str, work_id: int, upd_type: str):
    """
    增加 link 失效更新记录
    """
    with PGModel() as dao:
        qry = f"""
        insert into parking_roadside_upd_history (bid, type, work_id, status) 
        values (%s, %s, %s, %s)
        """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(qry, (bid, upd_type, work_id, 'INIT'))
        return


def had_updated_link_invalid(bid: str, work_id: int, upd_type: str) -> bool:
    """
    是否更新过
    """
    with PGModel() as dao:
        qry = f"""
        select id from parking_roadside_upd_history 
        where bid = %s and work_id = %s and type = %s and status = 'INIT' 
        """
        cursor = dao.get_cursor("online_rw")
        cursor.execute(qry, (bid, work_id, upd_type))
        res = cursor.fetchone()
        if not res:
            return False
        return True

