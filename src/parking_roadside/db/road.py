# -*- coding: utf-8 -*-
"""
道路库查询
"""
import os
import sys

sys.path.append(os.path.abspath(os.path.dirname(__file__)))
from model import PGModel


def check_diversion(geom_str):
    """
    查询导流线
    """

    with PGModel() as dao:
        # 使用游标执行查询
        query = """
        select count(*) as count
        from NAV_LANE_MARKING_PL 
        where ST_Intersects(ST_Force3D(st_geomfromtext(%s, 4326)), geom) 
        and longitudinal_pl_type like '%%10%%'
        """
        cursor = dao.get_cursor("road")
        cursor.execute(query, (geom_str,))
        res = cursor.fetchone()
        if res is not None and res['count'] > 0:
            return True

        query = """
        select count(*) as count
        from NAV_LANE_LINK 
        where 
        lane_type like '%%40%%'
        and ST_Intersects(ST_Force3D(st_geomfromtext(%s, 4326)), geom) 
        """
        cursor = dao.get_cursor("road")
        cursor.execute(query, (geom_str,))
        res = cursor.fetchone()
        if res is not None and res['count'] > 0:
            return True
        return False


def fetch_link_by_id(link_id):
    """
    查询link信息
    """

    with PGModel() as dao:
        query = """
        select *,st_astext(geom) as geom_wkt from nav_link where link_id = %s
        """
        # 使用游标执行查询
        cursor = dao.get_cursor("road")
        cursor.execute(query, (link_id,))
        return cursor.fetchone()


def fetch_multi_links_by_id(link_ids):
    """
    查询多组link信息
    """

    with PGModel() as dao:
        query = """
        select st_astext(ST_LineMerge(ST_Union(geom))) as geom_wkt 
        from nav_link where link_id in %s
        """
        # 使用游标执行查询
        cursor = dao.get_cursor("road")
        cursor.execute(query, (tuple(link_ids),))
        return cursor.fetchone()


def fetch_node_by(node_id):
    """
    查询node信息
    """
    with PGModel() as dao:
        query = """
        select *,st_astext(geom) as geom_wkt from nav_node where node_id = %s
        """
        # 使用游标执行查询
        cursor = dao.get_cursor("road")
        cursor.execute(query, (node_id,))
        return cursor.fetchone()


def fetch_link_by_node(node_id):
    """
    查询node相邻的link信息
    """
    with PGModel() as dao:
        # node_id_list = [node_id]
        # query = """
        # select adjoin_nid from nav_node where node_id = %s
        # """
        # cursor.execute(query, (node_id,))
        # res = cursor.fetchone()
        # if res is not None and res.get("adjoin_nid"):
        #     node_id_list.append(res["adjoin_nid"])

        query = """
        select *, st_astext(geom) as geom_wkt from nav_link where s_nid = %s or e_nid = %s
        """
        cursor = dao.get_cursor("road")
        cursor.execute(query, (node_id, node_id))
        return cursor.fetchall()


def fetch_nav_line_marking(line_str):
    """
    查询导航线标线信息
    """

    with PGModel() as dao:
        print(line_str)
        query = """
        SELECT ST_AsText(st_union(ST_Force2D(geom))) as geom_wkt
        FROM NAV_LANE_ROAD_PG
        WHERE 
            ST_Intersects(
                ST_Force3D(st_geomfromtext(%s, 4326)), geom)
        """
        # 使用游标执行查询
        cursor = dao.get_cursor("road")
        cursor.execute(query, (line_str,))
        res_list = cursor.fetchone()
        return res_list


def get_short_link_id(long_link_ids):
    """
    获取trans_id中长link_id对应的短link_id
    """
    quoted_tid_list = "','".join(map(str, long_link_ids))
    query = """
        SELECT sid, tid FROM image_r WHERE sid IN ('%s')
        """ % quoted_tid_list

    with PGModel() as dao:
        cursor = dao.get_cursor("trans_id_info")
        cursor.execute(query)

        # 处理结果
        long_link2short_link = {}
        for row in cursor.fetchall():
            long_link2short_link[row['sid']] = row['tid']
        return long_link2short_link


def get_long_link_id(short_link_id):
    """
    查询长link_id
    """
    query = """
        SELECT sid, tid FROM image_r WHERE tid = %s
        """

    with PGModel() as dao:
        # 使用游标执行查询
        cursor = dao.get_cursor("trans_id_info")
        cursor.execute(query, (short_link_id,))
        res = cursor.fetchone()
        if res is not None and "sid" in res:
            return res['sid']
        return ""


def fetch_road(polygon_str):
    """
    通过polygon_str查询道路信息
    """
    with PGModel() as dao:
        query = """
        select 
            *,st_astext(geom) as geom_wkt 
        from nav_link 
        where ST_Intersects(geom, ST_GeomFromText(%s, 4326))
            and kind > 2 and kind <= 9 and  viad != 1
            and form not like '%%10%%' and form not like '%%11%%' and form not like '%%15%%' 
            and form not like '%%16%%' and form not like '%%17%%' and form not like '%%20%%' 
            and form not like '%%31%%' and form not like '%%35%%' and form not like '%%37%%' 
            and form not like '%%38%%' and form not like '%%39%%' and form not like '%%50%%' 
            and form not like '%%52%%' and form not like '%%53%%'
        """
        # 使用游标执行查询
        cursor = dao.get_cursor("road")
        cursor.execute(query, (polygon_str,))
        res_list = cursor.fetchall()
        return res_list


def fetch_road_by_mesh_id(mesh_ids):
    """
    通过mesh_id查询道路信息
    """
    with PGModel() as dao:
        query = """
            select *,st_astext(geom) as geom_wkt from nav_link where 
            mesh_id in %s
         and kind >=2 and kind <= 9 and  viad != 1
         and form not like '%%10%%' and form not like '%%11%%' and form not like '%%15%%' 
         and form not like '%%16%%' and form not like '%%17%%' and form not like '%%20%%' 
         and form not like '%%31%%' and form not like '%%35%%' and form not like '%%37%%' 
         and form not like '%%38%%' and form not like '%%39%%' and form not like '%%50%%' 
         and form not like '%%52%%' and form not like '%%53%%'
        """
        # 使用游标执行查询
        cursor = dao.get_cursor("road")
        cursor.execute(query, (tuple(mesh_ids),))
        res_list = cursor.fetchall()
        return res_list
