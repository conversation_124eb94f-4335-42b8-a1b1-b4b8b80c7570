# -*- encoding: utf-8 -*-
"""
数据diff下线
"""
import sys

from db.pg import fetch_online_roadside_parking, fetch_work_to_be_diff, query_work_by_uid, \
    fetch_work_to_be_diff_by_create, query_online_park_by_bid, update_map_status
from db.road import fetch_multi_links_by_id
from db.master_back import query_parking_ach_by_pid
from api.send_poi import offline_poi, get_info_by_bid
from api.coord import trans_gc_2_bdmc
from api.link_trans import get_multi_long_id
from shapely import wkt
from datetime import datetime, timedelta, date


def run_online(created_at=''):
    """
    线上数据差分
    """
    print('run_online', created_at)
    if created_at != '':
        work = fetch_work_to_be_diff_by_create(created_at)
    else:
        work = fetch_work_to_be_diff()
    bid_list = []
    for w in work:
        bid_list.append(w['bid'])
    for w in work:
        run_by_bid(w['bid'], bid_list)


def run_by_bid(bid, bid_list=None):
    """
    单个bid执行
    """
    if bid_list is None:
        bid_list = []
    parking_info = query_parking_ach_by_pid(bid)
    if parking_info is None:
        print('no parking info', bid)
        return
    res = fetch_his_poi_by_parking_area(parking_info['area_wkt'])
    res.extend(fetch_his_poi_by_link_area(parking_info['road_relation']))
    tobe_offline_bid = set()
    for r in res:
        if r['bid'] in bid_list:
            continue
        if r['create_source'] == 'zt_life.ruitu_parking':  # 智能空间商单
            continue
        tobe_offline_bid.add(r['bid'])
        print("to_be_offline", bid, r['bid'], r['name'], r['show_tag'], r['city_name'], r['gcj_geom_wkt'],
              sep="\t")
    for bid in tobe_offline_bid:
        print('send_offline', bid)
        api_ret = offline_poi(bid)
        print("push_ret", api_ret)


def fetch_his_poi_by_parking_area(area_wkt):
    """
    通过停车场范围获取冗余poi
    """
    bdmc_area = trans_gc_2_bdmc(area_wkt)
    buffer_area = wkt.loads(bdmc_area).buffer(10)  # mc坐标 10m 缓冲区
    print("area", bdmc_area, buffer_area.wkt)
    return fetch_online_roadside_parking(buffer_area.wkt)


def fetch_his_poi_by_link_area(road_relation):
    """
    通过道路范围获取冗余poi
    """
    res = []
    if road_relation is None or 'link_info' not in road_relation:
        print('no link_info')
        return res
    link_list = [
        item['link_id']
        for item in road_relation['link_info']
    ]
    if link_list is None or len(link_list) == 0:
        print('no link_ids')
        return res
    long_link_id = get_multi_long_id(link_list).values()
    road_info = fetch_multi_links_by_id(long_link_id)
    if road_info is None or 'geom_wkt' not in road_info or road_info['geom_wkt'] is None:
        print('link expired')
        return res
    bdmc_area = trans_gc_2_bdmc(road_info['geom_wkt'])
    buffer_area = wkt.loads(bdmc_area).buffer(20)  # mc坐标 10m 缓冲区
    print("area", bdmc_area, buffer_area.wkt)
    res = fetch_online_roadside_parking(buffer_area.wkt)
    print('roads', res)
    return res


def run_by_uid_file(fil_name):
    """
    通过uid文件下线
    """
    work = fetch_work_to_be_diff()
    bid_list = []
    for w in work:
        bid_list.append(w['bid'])
    with open(fil_name, 'r') as f:
        for line in f.readlines():
            work = query_work_by_uid(line.strip())
            if work is None:
                continue
            for w in work:
                res = fetch_online_roadside_parking(w['turing_area_geom_wkt'])
                for r in res:
                    if r['bid'] in bid_list:
                        continue
                    if r['create_source'] in ['chuilei_scope.rg', 'parking.satellite_traj']:
                        continue
                    api_ret = offline_poi(r['bid'])
                    print("push_ret", api_ret)
                    print("to_be_offline", r['bid'], r['name'], r['show_tag'], r['city_name'], r['gcj_geom_wkt'],
                          sep="\t")


def offline_by_bid_file(file_name, is_jz=False):
    """
    通过bid文件下线
    """
    with open(file_name, 'r') as f:
        for line in f.readlines():
            bid = line.strip()
            parking = query_online_park_by_bid(bid)
            if (parking is None or
                    parking['create_source'] == 'zt_life.ruitu_parking'):  # 智能空间商单
                print('park_online_not_found, skip', bid)
                continue
            if not is_jz and parking['park_spec'] == 1:  # 非精准就不下线
                print('park_is_jz, skip', bid)
                continue
            online_info = get_info_by_bid(bid)
            if (online_info is not None and online_info.get('data') and online_info.get('data').get('base') and
                    online_info.get('data').get('base').get('status') == 2):  # 已经下线了
                print('park_already_offline, skip', bid)
                continue
            if is_jz:
                offline_jz(bid)
            else:
                print('send_offline', bid)
                api_ret = offline_poi(bid)


def offline_jz(bid):
    """
    下线精准， 需要改库的状态
    """
    update_map_status(bid, 2)
    print('send_offline', bid)
    offline_poi(bid)


def run_test(file_name):
    """
    测试
    """
    with open(file_name, 'r') as f:
        for line in f.readlines():
            bid = line.strip()
            api_ret = offline_poi(bid)
            print("push_ret", api_ret)


if __name__ == '__main__':
    yesterday = datetime.today() - timedelta(days=1)
    run_online(yesterday.strftime("%Y-%m-%d"))
    # offline_by_bid_file(sys.argv[1])
