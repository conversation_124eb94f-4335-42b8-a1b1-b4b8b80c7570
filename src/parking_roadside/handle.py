# -*- coding: utf-8 -*-
"""
路侧识别流水线
"""
import sys
import uuid
from dataclasses import dataclass, field
from shapely import wkt
from db.drds import add_parking_track, update_parking_track
from db.pg import add_achievement
from src.tools import pipeline
from road_chains import get_parking_road_chains
from merge_turnoff import get_turnoff_by_multi_link
from process import run_chains_by_link_id
from db.road import fetch_multi_links_by_id
from api.tracks_api import get_tracks_all
from api.infer_res_api import get_full_infer_res
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from api.infer_res_api import multi_get_parking_line_bycdid_worker_helper


@dataclass
class Context:
    """
    上下文
    """
    debug: bool = True
    track_log_id: int = 0
    chains_res: dict = field(default_factory=dict)  # 路链
    batch_id: str = ""
    result: list[dict] = field(default_factory=list)  # 结果 uid, link_ids_str, 测线， 面
    err_msg: str = ""


def gen_track_id(ctx: Context, proceed):
    """
    写入log
    """
    _link_ids = sorted(ctx.chains_res['link_id_list'])
    link_ids_str = ','.join(_link_ids)
    list_uuid = str(uuid.uuid5(uuid.NAMESPACE_DNS, link_ids_str)).replace('-', '')
    chains_obj = wkt.loads(ctx.chains_res['geom_wkt'])
    center_point_wkt = ''
    if chains_obj is not None:
        center_point_wkt = chains_obj.centroid.wkt
    if not ctx.debug and ctx.batch_id != "":
        track_log_id = add_parking_track(list_uuid, link_ids_str, center_point_wkt, ctx.batch_id)
        ctx.track_log_id = track_log_id
    print("gen_track_id", ctx.track_log_id)
    proceed()


def gen_chains_res(link_id: str):
    """
    生成路链
    """

    def pipe(ctx: Context, proceed):
        print("handle", link_id, ctx.batch_id, ctx.debug)
        chains_res = get_parking_road_chains(link_id)
        print("chains_res", chains_res)
        print("chains_wkt", chains_res['geom_wkt'])
        if len(chains_res['link_id_list']) == 0:
            ctx.err_msg = "没有路链"
            return
        ctx.chains_res = chains_res
        proceed()

    return pipe


def filter_turnoff(min_count=20):
    """
    过滤熄火点
    """

    def pipe(ctx: Context, proceed):
        doc_len, zy_len = get_turnoff_by_multi_link(ctx.chains_res['link_id_list'])
        if doc_len + zy_len <= min_count:
            ctx.err_msg = "熄火点不足doc:{}, zy:{}".format(doc_len, zy_len)
            return
        proceed()

    return pipe


def check_have_res_helper(to_be_exe_list):
    """
    并发执行
    """
    with ThreadPoolExecutor(max_workers=30) as executor:
        future_to_num = {executor.submit(multi_get_parking_line_bycdid_worker_helper, item): item for item in
                         to_be_exe_list}
        for future in as_completed(future_to_num):
            result = future.result()
            if len(result) > 0:
                executor.shutdown(wait=False, cancel_futures=True)
                return True
    return False


def filter_no_infer_res(ctx: Context, proceed):
    """
    过滤没有众源识别结果的
    """
    link_union = fetch_multi_links_by_id(ctx.chains_res['link_id_list'])
    link_wkt = link_union['geom_wkt']
    link_geom = wkt.loads(link_wkt)
    polygon_geom = link_geom.buffer(0.0003, cap_style=2, join_style=1)
    tracks_all = get_tracks_all(polygon_geom, months=12)
    pic_map = {}
    ra_map = {}
    for track in tracks_all:
        if track['link_id'] not in ctx.chains_res['link_id_list']:
            continue
        if 'vof' not in track['cdid']:
            if track['cdid'] not in ra_map:
                ra_map[track['cdid']] = []
            ra_map[track['cdid']].append(track)
            pic_map[track['id']] = track
    to_be_exe_list = []
    # 获取已经识别的
    for cdid, track in ra_map.items():
        to_be_exe_list.append((cdid, pic_map))
    res = check_have_res_helper(to_be_exe_list)

    if not res:
        ctx.err_msg = "没有中业识别结果"
        return
    proceed()


def process_infer(ctx: Context, proceed):
    """
    处理识别
    """
    res = run_chains_by_link_id(ctx.chains_res['link_id_list'], 2)
    if res is None:
        ctx.err_msg = "没有识别结果"
    else:
        ctx.result = res
    proceed()


def save_result(ctx: Context):
    """
    保存结果
    """
    if ctx.debug or ctx.batch_id == "":
        print("is debug", ctx.result, ctx.err_msg)
        return
    print("save_result", ctx.track_log_id, ctx.result, ctx.err_msg)
    if ctx.err_msg != "":
        update_parking_track(ctx.track_log_id, "", ctx.err_msg)
    elif len(ctx.result) == 0:
        update_parking_track(ctx.track_log_id, "", "no_data")
    else:
        sql_vals = []
        for res in ctx.result:
            sql_vals.append((
                res['uid'],
                ctx.batch_id,
                res['short_link_id_str'],
                res['line_wkt'],
                res['area_wkt'],
            ))
        add_achievement(sql_vals)
        update_parking_track(ctx.track_log_id, ctx.result[0]['uid'], "success")


def force_run(link_id, batch_id, debug=True):
    """
    强制运行
    不考虑熄火点和是否有众源识别结果
    """
    ctx = Context(debug=debug, batch_id=batch_id)
    pipe = pipeline.Pipeline(
        gen_chains_res(link_id),
        gen_track_id,
        process_infer,
    )
    pipe(ctx)
    save_result(ctx)


def main(link_id, batch_id, debug=True):
    """
    主函数：入口点
    """
    ctx = Context(debug=debug, batch_id=batch_id)
    pipe = pipeline.Pipeline(
        gen_chains_res(link_id),
        gen_track_id,
        filter_turnoff(),
        filter_no_infer_res,
        process_infer,
    )
    pipe(ctx)
    save_result(ctx)


if __name__ == "__main__":
    main(sys.argv[1], '', True)
