# -*- encoding: utf-8 -*-
"""
数据diff下线
"""
from concurrent.futures import ThreadPoolExecutor, as_completed
from shapely import wkt
from db.pg import (query_work_by_batch_id, query_work_by_pid, add_achievement_ch_tmp, query_work_by_uid,
                   fetch_work_res_by_links, update_work_status_by_id, get_full_work_by_status_message,
                   query_juejin_pic_by_id)
from shapely.ops import substring
from db.road import get_long_link_id
from process import gen_polygon
import traceback

DEBUG = False
"""
# "门前可停" => 1,
# "门前不可停" => 2,
"无法判断" => 3,
# "路边有停车位" => 4,
# "路边无停车位（停放车辆数≥5）" => 5,
# "路边无停车位（停放车辆数<5）" => 6,
"路边有车位线" => 7,
"路边无车位线" => 8,
"看不清车位线" => 9,
"交叉验证不一致" => 10,
"""


def run_by_uid(uid):
    """
    通过uid执行
    """
    res = query_work_by_uid(uid)
    for item in res:
        run_item(item['id'])


def run_item(work_id):
    """
    执行单条数据
    """
    print("run", work_id)
    work_res = query_work_by_pid(work_id)
    if work_res is None:
        print("not found", work_id)
        return
    if not DEBUG and work_res['status'] != -1 and work_res['message'] != 'send_juejin':
        return
    long_ids = []
    if work_res['turing_link_id_list'] != '':
        short_id_str = work_res['turing_link_id_list']
        if not set(short_id_str.split(",")) & set(work_res['link_id_list'].split(",")):
            return "rebind_link", work_id
    else:
        short_id_str = work_res['link_id_list']
    print("start base check")
    include_juejin_num = 0
    juejin_match = False
    # 先统一看有无掘金结果
    juejin_ret_list = query_juejin_pic_by_id(work_id)
    if len(juejin_ret_list) == 0:
        print("no send juejin")
        return
    for ret in juejin_ret_list:
        if ret['conclusion'] is None or int(ret['conclusion']) == 0:
            print("juejin not full complete")
            return
        include_juejin_num += 1
        if int(ret['conclusion']) == 7:
            juejin_match = True
    link_obj = wkt.loads(work_res['line_geom_wkt'])
    line_length = link_obj.length
    interval = 0.00028
    if include_juejin_num < line_length / interval * 1.5:
        print("no jujin res", work_id, sep='\t')
        if not DEBUG:
            update_work_status_by_id(work_id, 6, 'not_enough_juejin')
        return
    if not juejin_match:
        print("skipped", work_id, short_id_str, ",".join(long_ids), work_res['id'], work_res['turing_id'], sep='\t')
        if not DEBUG:
            update_work_status_by_id(work_id, 6, 'no_juejin_result')
        return

    print("start check exists")
    exits_link = False
    work_res_2 = fetch_work_res_by_links(short_id_str.split(','))
    # for link_id in short_id_str.split(','):
    #     long_ids.append(get_long_link_id(link_id))
    #     work_res_2 = fetch_work_res_by_link(link_id)
    if work_res_2 is not None and len(work_res_2) > 0:
        for work2 in work_res_2:
            if work2['turing_id'] == work_res['turing_id']:
                continue
            print("exists", "link_id", short_id_str, "id1", work_res['id'], "id2", work2['id'])
            exits_link = True
            break
    if exits_link:
        print("duplicate", work_id, sep='\t')
        if not DEBUG:
            update_work_status_by_id(work_id, 6, 'duplicate')
        return
    # 按照间隔进行截取
    print("start strategy")
    start = 0
    seg_list = []
    while start < line_length:
        print("start interval", start)
        current_interval = interval
        not_parking_line = 0
        if start + current_interval * 1.5 > line_length:
            current_interval = current_interval * 1.5
        end = min(start + current_interval, line_length)
        segment = substring(link_obj, start, end)
        segment_polygon = segment.buffer(0.0003, cap_style=2, join_style=1)
        juejin_res = []
        have_line = False
        for ret in juejin_ret_list:
            point = wkt.loads(ret['point_wkt'])
            if not segment_polygon.contains(point):
                continue
            if int(ret['conclusion']) == 7:  # 有停车线
                not_parking_line = 0
                have_line = True
            if not have_line and int(ret['conclusion']) in [8]:  # 无停车线
                not_parking_line += 1
            if not have_line and int(ret['conclusion']) in [3]:  # 无法判断 给一半
                not_parking_line += 0.5
            if not have_line and int(ret['conclusion']) in [10]:  # 交叉验证不一致 给3
                not_parking_line += 0.3
            juejin_res.append(ret)
        seg_list.append({
            'start': start,
            'score': not_parking_line,
            'juejin_res': juejin_res,
            'seg_obj': segment
        })
        start += current_interval
    juejin_changed = False
    line_group = []
    step = []
    for n, seg in enumerate(seg_list):
        print("start", n, seg['start'], seg['seg_obj'].wkt)
        current_interval = interval
        if seg['start'] + current_interval * 1.5 > line_length:
            current_interval = interval * 1.5
        current_not_match = False
        prev_not_match = False
        next_not_match = False
        for res in seg['juejin_res']:
            if int(res['conclusion']) not in [7]:
                current_not_match = True
            else:
                current_not_match = False
                break
        if n >= 1:
            for res in seg_list[n - 1]['juejin_res']:
                if int(res['conclusion']) not in [7]:
                    prev_not_match = True
                else:
                    prev_not_match = False
                    break
        if n <= len(seg_list) - 2:
            for res in seg_list[n + 1]['juejin_res']:
                if int(res['conclusion']) not in [7]:
                    next_not_match = True
                else:
                    next_not_match = False
                    break
        score_round = 0
        if len(seg['juejin_res']) > 0:
            score_round = round(seg['score'] / len(seg['juejin_res']), 2)
        print("point_score", score_round, current_not_match, prev_not_match, next_not_match)
        # if not_match_num > 3: # 无停车位
        if len(step) > 0:  # 当前有起始点 需判断打断
            if (
                    (len(seg['juejin_res']) > 0 and round(seg['score'] / len(seg['juejin_res']), 2) > 0.75 and
                     (prev_not_match or next_not_match)) or  # 掘金判断80%判断无停车位
                    (current_not_match and prev_not_match and next_not_match)  # 前后都没有停车位
            ):
                print("cut_by_strategy", n, seg['score'], prev_not_match, next_not_match)
                juejin_changed = True
                step.append(seg['start'])
                line_group.append(step)
                step = []
        else:  # 无起始点，判断开始
            if not current_not_match or (not next_not_match and n < len(seg_list) - 2):
                print("set_start", n, seg['score'])
                step.append(seg['start'])
            elif n == 0:
                juejin_changed = True
    if len(step) > 0:
        line_group.append(step)
    res_polygon = None
    if juejin_changed:
        for item in line_group:
            if len(item) > 1:
                line_from, line_end = item
            elif len(item) == 1:
                line_from, line_end = item[0], line_length
            else:
                print("line_err", item)
                continue
            if line_end > line_length:
                line_end = line_length
            print("start,end", line_from, line_end, link_obj.length)
            juejin_obj = substring(link_obj, line_from, line_end)
            juejin_polygon = gen_polygon(juejin_obj)
            print("res", juejin_changed, short_id_str, juejin_polygon,
                  work_res['id'], work_res['turing_id'], sep='\t')
            if not DEBUG:
                add_achievement_ch_tmp(work_id, work_res['batch_id'] + "_juejin_changed", juejin_obj.wkt,
                                       juejin_polygon, 'juejin_verified')
            if res_polygon is None:
                res_polygon = wkt.loads(juejin_polygon)
            else:
                res_polygon = res_polygon.union(wkt.loads(juejin_polygon))
        if not DEBUG:
            update_work_status_by_id(work_id, 6, 'juejin_changed')
    else:
        if not DEBUG:
            update_work_status_by_id(work_id, work_res['status'], 'juejin_verified')
        print("res", juejin_changed, short_id_str, work_res['turing_area_geom_wkt'],
              work_res['id'], work_res['turing_id'], sep='\t')
    # if res_polygon is not None:
    #     add_tmp_parking_ach(work_id, res_polygon.wkt)


def run_by_file(file_name):
    """
    通过文件执行
    """
    with open(file_name, 'r') as file:
        lines = file.readlines()

    with ThreadPoolExecutor(max_workers=30) as executor:
        # 提交任务并存储 Future 对象
        futures = [executor.submit(run_item, line.strip()) for line in lines]

        # 等待所有任务完成
        for future in as_completed(futures):
            try:
                # 获取任务的结果（可选）
                future.result()
            except Exception as e:
                print(f"任务执行时出错: {e}")
                stack_info = traceback.format_exc()
                print("Stack trace:")
                print(stack_info)


def run_by_batch(batch_id):
    """
    通过批次号执行
    """
    res = query_work_by_batch_id(batch_id)
    if res is None:
        return
    with ThreadPoolExecutor(max_workers=30) as executor:
        # 提交任务并存储 Future 对象
        futures = [
            executor.submit(run_item, work['id'])
            for work in res if work['turing_result'] == 1
        ]

        # 等待所有任务完成
        for future in as_completed(futures):
            try:
                # 获取任务的结果（可选）
                future.result()
            except Exception as e:
                print(f"任务执行时出错: {e}")
                stack_info = traceback.format_exc()
                print("Stack trace:")
                print(stack_info)


def batch_run_todo():
    """
    批量执行全部待处理任务
    """
    work_list = get_full_work_by_status_message(-1, 'send_juejin')
    sorted_list = sorted(work_list, key=lambda x: x['id'])
    for work in sorted_list:
        run_item(work['id'])


if __name__ == '__main__':
    # run_by_file(sys.argv[1])
    # run_item(sys.argv[1])
    # run_juejin("1122.anyl.txt")
    batch_run_todo()
