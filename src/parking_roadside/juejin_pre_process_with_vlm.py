# -*- encoding: utf-8 -*-
"""
掘金投图策略
"""
import json
import os
import shutil
import subprocess
import sys
import random
import tarfile
import time
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from datetime import datetime
from multiprocessing import Pool, Manager, Process
import requests
import math
import uuid
from shapely import wkt, Point, LineString
from shapely.ops import substring
from PIL import Image
from io import BytesIO

from api.tracks_api import get_tracks_all
from api.infer_res_api import get_full_infer_res
from db.road import get_long_link_id, fetch_link_by_id
from db.drds import fetch_parking_line_infer_res
from db.pg import query_work_by_pid, add_parking_line_juejin_prev, query_juejin_pic, update_work_status_by_id, \
    get_full_work_by_status_message, fetch_juejin_prev_by_area, add_parking_line_juejin_prev_model_res

from api.vlm_engine import run_one_boskey_random

# 是否重发图片，设置False 如果图片已经回调过，则不会重发掘金
RESEND_IMAGE = False
# 设置未true不会推送写入数据
# DEBUG = True
DEBUG = False
# 是否获取已经发送过的图片，设置为True，会从数据库中获取已经发送过的图片，不会重新选择图片
FETCH_ALREADY_SEND_IMAGE = True


# 将角度转换为弧度
def deg_to_rad(degrees):
    """
    将角度转换为弧度
    """
    return degrees * math.pi / 180


def calculate_north_angle(point1, point2):
    """
    计算两点之间的方位角，并将其转换为北偏角
    """
    line = LineString((point1, point2))
    print("line_wkt", line.wkt)
    # 将经纬度转换为弧度
    x1, y1 = point1
    x2, y2 = point2
    lat1_rad = deg_to_rad(x1)
    lon1_rad = deg_to_rad(y1)
    lat2_rad = deg_to_rad(x2)
    lon2_rad = deg_to_rad(y2)

    # 计算经度差
    delta_lon = lon2_rad - lon1_rad

    # 使用大圆航迹公式计算方位角
    y = math.sin(delta_lon) * math.cos(lat2_rad)
    x = math.cos(lat1_rad) * math.sin(lat2_rad) - math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(delta_lon)

    # 计算方位角（弧度）
    azimuth = math.atan2(y, x)
    print("azimuth", math.degrees(azimuth), (y, x))

    # 将方位角转换为北偏角
    north_azimuth = (90 + math.degrees(azimuth)) % 360  # 保证角度为0到360之间
    return north_azimuth


def calculate_line_angle(line_geom):
    """
    以三点角度代替计算线段曲率
    """
    if len(line_geom.coords) <= 3:
        return 0
    # 取首尾点和几何中心点
    start_pt, end_pt, center_pt = line_geom.coords[0], line_geom.coords[-1], line_geom.centroid
    start_x, start_y = start_pt
    end_x, end_y = end_pt
    center_x, center_y = center_pt.x, center_pt.y
    ab = (center_x - start_x, center_y - start_y)
    bc = (end_x - start_x, end_y - start_y)
    dot_product = ab[0] * bc[0] + ab[1] * bc[1]
    magnitude_ab = math.sqrt(ab[0] ** 2 + ab[1] ** 2)
    magnitude_bc = math.sqrt(bc[0] ** 2 + bc[1] ** 2)

    # 防止除以零
    if magnitude_ab == 0 or magnitude_bc == 0:
        return 0

    # 计算余弦值并取反余弦
    cos_theta = dot_product / (magnitude_ab * magnitude_bc)
    cos_theta = max(-1, min(1, cos_theta))  # 防止精度问题导致超出[-1, 1]
    angle_rad = math.acos(cos_theta)

    # 转换为角度
    angle_deg = math.degrees(angle_rad)
    return angle_deg


def check_point_in_bounds(point, bounds):
    """
    判断点是否在边界内
    """
    if bounds.contains(point):
        return True
    else:
        return False


def process_tracks(cdid, tracks, link_geom, is_fulu, link_id_list):
    """
    策略过滤轨迹
    """
    if len(tracks) == 1:
        print("filter_one_point_track", cdid)
        return cdid, []
    min_distance = None
    max_distance = None
    new_tracks = []
    for k, track in enumerate(tracks):
        if track['link_id'] not in link_id_list:
            # print("filter_track_not_chains", cdid, track['link_id'])
            continue
        # 获取投影点
        point = Point(track['x'], track['y'])
        distance = link_geom.project(point)
        if min_distance is None or distance < min_distance:
            min_distance = distance
        if max_distance is None or distance > max_distance:
            max_distance = distance
        track['unix_timestamp'] = int(track['bos_key'].split("_")[-1])
        track['have_parking'] = False  # 是否有停车标志 先都赋值没有，下面更新
        new_tracks.append(track)
    if len(new_tracks) == 0:
        print("filter_track_no_points", cdid, len(new_tracks))
        return cdid, []
    if link_geom.length * 0.5 >= max_distance - min_distance and len(new_tracks) < 4:
        print("filter_track_not_enough", cdid, len(new_tracks))
        return cdid, []
    infer_res_210 = get_full_infer_res(cdid, 210)
    if is_fulu:
        print("fetch fulu infer")
        infer_res_3 = get_full_infer_res(cdid, 3)
        full_res = infer_res_210 + infer_res_3
        not_fulu_sign_track_id = []
        # print("infer_res", cdid)
        for label in full_res:
            # print("label", label)
            if label['rt1_type'] in ['63002', '63003', '63004', '63005', '63006', '63007', '63008', '63009',
                                     '63010', '63011', '63012', '63013', '63014', '63015', '63016', '63017',
                                     '63018', '63019', '63020', '63021', '210002', '210004', '210007', '210009',
                                     '210011']:
                # print("have_sign_infer", cdid, label)
                not_fulu_sign_track_id.append(label['pic_id'])
        new_tracks_2 = []
        for new_track in new_tracks:
            if new_track['id'] in not_fulu_sign_track_id:
                print("filter_track_has_sign", cdid, new_track['id'], new_track['bos_key'])
                continue
            new_tracks_2.append(new_track)
        new_tracks = new_tracks_2
    have_parking_pic_id = []
    for label in infer_res_210:
        if label['rt1_type'] == "210024":
            #     print("track_have_parking_line", cdid, label)
            have_parking_pic_id.append(label['pic_id'])
    if len(have_parking_pic_id) > 0:
        for i, track in enumerate(new_tracks):
            if track['id'] in have_parking_pic_id:
                new_tracks[i]['have_parking'] = True
            elif 'vof' in track['cdid']:
                cache_res = fetch_parking_line_infer_res(track['bos_key'])
                if cache_res is not None and "have_line" in cache_res and cache_res["have_line"] > 0:
                    new_tracks[i]['have_parking'] = True
    return cdid, new_tracks


def choose_track(link_geom, is_fulu, link_id_list):
    """
    选择合适的轨迹
    普通路buffer30米，辅路buffer15米
    轨迹通过车天聚合， 每个车天最少有4个点或者长度占link的50%
    除了首尾点，其他点必须都在chains上
    """
    buffer_size = 0.0003
    if is_fulu:
        buffer_size = 0.00015
    polygon_geom = link_geom.buffer(buffer_size, cap_style=2, join_style=1)
    tracks_all = get_tracks_all(polygon_geom, months=10)
    track_map = {}  # 通过车天聚合
    for track in tracks_all:
        if track['cdid'] not in track_map:
            track_map[track['cdid']] = []
        track_map[track['cdid']].append(track)
    track_res_group = {}
    # 涉及到中业识别结果调用，并发处理车天
    with ThreadPoolExecutor(max_workers=30) as executor:
        futures = {executor.submit(process_tracks, cdid, tracks, link_geom, is_fulu, link_id_list): cdid for
                   cdid, tracks in track_map.items()}
        for future in futures:
            cdid, new_tracks = future.result()
            if new_tracks:
                track_res_group[cdid] = new_tracks
    return track_res_group


def calculate_weight(item):
    """
    计算轨迹的权重
    """
    month_weight = 1.0
    infer_weight = 0.5
    current_time = int(time.time())
    unix_timestamp = round(item['unix_timestamp'] / 1000, 0)
    time_diff = current_time - unix_timestamp  # 时间差（秒）
    months_diff = time_diff // (30 * 24 * 60 * 60)  # 换算成月数
    month_weight = 0.7 ** months_diff  # 权重公式

    if item['have_parking']:
        infer_weight = 1.0
    return month_weight * infer_weight


def weighted_sample(group, n):
    """
    按权重抽取
    """
    selected = []
    for _ in range(n):
        if not group:
            break
        total_weight = sum(entry["weight"] for entry in group)
        r = random.uniform(0, total_weight)
        cumulative_weight = 0
        for entry in group:
            cumulative_weight += entry["weight"]
            if r <= cumulative_weight:
                selected.append(entry['item'])
                group.remove(entry)  # 移除已选 item
                break
    return selected


def run(work_id, session_id, path_dir):
    """
    通过work_id运行
    """
    print("run", work_id)
    work_res = query_work_by_pid(work_id)
    if work_res is None:
        return
    long_ids = []
    link_info = None
    for link_id in work_res['link_id_list'].split(','):
        long_id = get_long_link_id(link_id)
        long_ids.append(long_id)
        if link_info is None:
            link_info = fetch_link_by_id(long_id)
    link_obj = wkt.loads(work_res['line_geom_wkt'])
    is_sc = False  # 是否sc道路
    is_fulu = False  # 是否是辅路
    if link_info is not None and int(link_info['md']) == 0:
        is_sc = True
    if link_info is not None and '34' in link_info['form'].split(','):
        is_fulu = True

    # 获取 LineString 的总长度
    line_length = link_obj.length
    print("origin_info", work_res['link_id_list'], link_obj.wkt)
    # 先筛选合适的轨迹 是否有车位线已经识别 having_line
    tracks_group = choose_track(link_obj, is_fulu, long_ids)
    # print("tracks_group", tracks_group)
    interval = 0.00028
    # 按照间隔进行截取
    start = 0
    pick_images = []

    have_parking_bos_key = {}
    work_split_index = 0  # 分割index 相同index选择最多5张图片 就这个是一个索引 后续进行优化减量
    while start < line_length:

        work_split_index += 1
        end = min(start + interval, line_length)
        segment = substring(link_obj, start, end)
        segment_polygon = segment.buffer(0.0003, cap_style=2, join_style=1)
        print("start_len", start, segment.wkt, segment_polygon.wkt)
        angle = calculate_line_angle(segment)
        seg_north_angle = calculate_north_angle(segment.coords[0], segment.coords[-1])
        need_parallel = False
        print("segment_base", angle, seg_north_angle)
        if angle == 0 or angle > 110:  # 平滑的直线则需要计算线段和轨迹近似平行
            need_parallel = True
        match_track = []
        for cdid, tracks in tracks_group.items():
            for track in tracks:
                # 先判断轨迹是否在segment内
                point = Point(track['x'], track['y'])
                if not segment_polygon.contains(point):
                    # print("filter_track_out_of_segment", cdid, track['bos_key'], point.wkt)
                    continue
                if need_parallel:
                    # 通过判断两个北偏角是否小于30度来判断平行
                    diff_angle = abs(seg_north_angle - track['north'])
                    if 30 < diff_angle < 120 or 210 < diff_angle < 330:
                        print("filter_track_not_parallel", cdid, track['bos_key'], track['north'])
                        continue
                match_track.append(track)

                # 输出全部有车位线的图片
                have_parking = track['have_parking']
                if have_parking:
                    bos_key = track['bos_key']
                    have_parking_bos_key[bos_key] = bos_key

        print("match_track", len(match_track))
        if len(match_track) < 3:
            print("match_track_not_enough", work_res['uid'], len(match_track), sep="\t")
        max_pic = 5
        if len(match_track) <= max_pic:
            for item in match_track:
                pick_images.append([item, work_split_index])
        else:
            pic_his = {}
            if FETCH_ALREADY_SEND_IMAGE:
                his_send = fetch_juejin_prev_by_area(segment_polygon.wkt)
                if his_send is not None and len(his_send) > 0:
                    for his_send_item in his_send:
                        pic_his[his_send_item['image_id']] = his_send_item
            # 构造初始权重
            items_with_weights = [
                {"item": item, "weight": calculate_weight(item)}
                for item in match_track
            ]
            selected_items = []
            # 先筛选历史推送过的
            if len(pic_his) > 0:
                for k, entry in enumerate(items_with_weights):
                    item = entry['item']
                    image_name = "{}_{}_{}".format(item['bos_key'], item['cdid'], item['link_id'])
                    if image_name in pic_his:
                        selected_items.append(item)
                        del items_with_weights[k]
            print("choose_history", len(selected_items))
            max_pic = max_pic - len(selected_items)
            # 进行不放回抽样
            if not is_sc:  # 非sc道路 不需要保障正反方向都有
                selected_items.extend(weighted_sample(items_with_weights, max_pic))
            else:
                list_a = []
                list_b = []
                for entry in items_with_weights:
                    if len(list_a) == 0:
                        list_a.append(entry)
                        continue
                    base_north = list_a[0]['item']['north']
                    diff = abs(entry['item']['north'] - base_north)
                    if min(diff, 360 - diff) < 90 or min(diff, 360 - diff) > 270:  # 角度小于90 同向
                        list_a.append(entry)
                    else:
                        list_b.append(entry)
                selected_a = weighted_sample(list_a, 5)
                selected_b = weighted_sample(list_b, 5)
                for i in range(max_pic):
                    if i % 2 == 0:  # 偶数 取a 奇数 取b
                        if len(selected_a) > 0:
                            selected_items.append(selected_a.pop())
                        elif len(selected_b) > 0:
                            selected_items.append(selected_b.pop())
                    else:
                        if len(selected_b) > 0:
                            selected_items.append(selected_b.pop())
                        elif len(selected_a) > 0:
                            selected_items.append(selected_a.pop())
            for item in selected_items:
                pick_images.append([item, work_split_index])

        start += interval

    print('workid=%s, pick_images=%s,have_parking_bos_key=%s' % (work_id, len(pick_images), len(have_parking_bos_key)))

    img_res_cacle_result = []
    pick_images_new = []
    for pick_image, work_split_index in pick_images:

        bos_key = pick_image['bos_key']
        vlm_res = run_one_boskey_random(bos_key)

        zy_res = 1 if bos_key in have_parking_bos_key else 0
        if bos_key in have_parking_bos_key and vlm_res == '是':
            is_need_push = 0
        elif bos_key not in have_parking_bos_key and vlm_res == '否':
            is_need_push = 0
        else:
            is_need_push = 1

        # 写入pg表
        img_res_cacle_result.append([work_id, pick_image, vlm_res, zy_res, is_need_push, work_split_index])

        if is_need_push:
            pick_images_new.append(pick_image)

    print('img_res_cacle_result', len(img_res_cacle_result))

    # 写入parking_line_jujin_prev_model_res
    for work_id, pick_image, vlm_res, zy_res, is_need_push, work_split_index in img_res_cacle_result:
        track_id = pick_image['track_id']  # track_id有可能是空
        point = Point(pick_image['x'], pick_image['y'])
        north = pick_image['north']
        link_id = pick_image['link_id']
        batch_id = work_res['batch_id'] + "_" + session_id
        image_id = "{}_{}_{}".format(pick_image['bos_key'], pick_image['cdid'], pick_image['link_id'])
        add_parking_line_juejin_prev_model_res(link_id, batch_id, point.wkt,
                                               image_id, track_id, work_id,
                                               north, vlm_res, zy_res, is_need_push,
                                               work_split_index)

    print("pick_images_new", len(pick_images_new))

    # # 保存图片
    for pick_image in pick_images_new:
        print("save_img", pick_image['bos_key'], pick_image['cdid'], pick_image['link_id'])
        image_name = "{}_{}_{}".format(pick_image['bos_key'], pick_image['cdid'], pick_image['link_id'])
        res = None
        if not RESEND_IMAGE:
            print("fetch_his_image", image_name)
            res = query_juejin_pic(image_name)
        if res is None:
            image_name = save_img(pick_image['bos_key'], image_name, path_dir)
        if image_name is None:
            print("save_img_failed")
            continue
        point = Point(pick_image['x'], pick_image['y'])
        # if not DEBUG:
        #     add_parking_line_juejin_prev(work_id, pick_image['link_id'], work_res['batch_id'] + "_" + session_id,
        #                                  point.wkt, image_name, pick_image['north'])

    if not DEBUG:
        update_work_status_by_id(work_id, work_res['status'], 'send_juejin_vlm')
    print("save_img_finished", work_id)
    return


def save_worker(queue, patch_dir):
    """
    保存图片进程
    """
    while True:
        job = queue.get()
        if job is None:
            break
        print(f"Processing job: {job}")
        image, name = job
        image.save(patch_dir + "/" + name)
        print(f"Saved: {patch_dir}/{name}")
    print("save_worker finished")


def save_img(bos_key, name, path_dir):
    """
    保存图片到本地
    """
    image_url = 'http://m.map.baidu.com:8011/' + bos_key
    print("start_download", image_url, name)
    try_cnt = 5
    while try_cnt > 0:
        try:
            response = requests.get(image_url, timeout=(5, 60))
        except Exception as e:
            print(f"Exception occurred during request: {e}")
            try_cnt -= 1
            continue
        # 检查请求是否成功
        if response.status_code == 200:
            # 将图片保存到本地
            img_data = BytesIO(response.content)
            with Image.open(img_data) as img:
                width, height = img.size
                left = width // 3
                top = 0
                right = width
                bottom = height
                box = (left, top, right, bottom)
                # 裁剪图片
                cropped_img = img.crop(box)
                # 保存裁剪结果
                cropped_img.save(path_dir + "/{}.png".format(name))
                # save_img_queue.put((cropped_img, "{}.png".format(name)))
                print("推送队列 {}.png".format(name))
            return name
        else:
            print(f"请求失败，状态码: {response.status_code}")
            try_cnt -= 1
    return None


def run_with_queue(args):
    """
    在全局作用域中定义的任务函数，适配队列。
    """
    work_id, tmp_id, path_dir = args
    return run(work_id, tmp_id, path_dir)  # 确保 run 方法支持队列


def batch_run_todo(path):
    """
    通过数据库查询批量运行任务
    """
    work_list_all = get_full_work_by_status_message(-1, 'turnoff_matched')
    work_list = []
    # 只做某个批次ningde_part_0322
    for info in work_list_all:
        if info['batch_id'] == 'ningde_part_0322':
            work_list.append(info)
    print('work_list_all=%s, work_list=%s' % (len(work_list_all), len(work_list)))

    # if len(work_list) < 1000:
    #     print("没有足够的任务待处理")
    #     return

    top_1000 = sorted(work_list, key=lambda x: x['id'])[:10]
    session_id = f"{datetime.now().strftime('%Y%m%d')}_{uuid.uuid4().hex}"
    path_dir = f"{path}/{session_id}"
    os.makedirs(path_dir, exist_ok=False)
    print("创建文件夹：", path_dir)

    with Manager() as manager:
        # save_img_queue = manager.Queue()

        # 启动保存图片的进程
        # worker = Process(target=save_worker, args=(save_img_queue, path_dir,))
        # worker.start()

        # 构建任务列表
        to_be_run_list = [
            (work['id'], session_id, path_dir)
            for work in top_1000
        ]

        with ProcessPoolExecutor(max_workers=30) as executor:
            results = list(executor.map(run_with_queue, to_be_run_list))
            print("Tasks completed:", results)

        # 发送停止信号
        # save_img_queue.put(None)
        # worker.join()

        # 打包结果
        if not DEBUG:
            tar_file = f"roadside_{session_id}.tar"
            tar_path = f"{path}/roadside_{session_id}.tar"
            create_tar(path_dir, tar_path)
            upload_afs(tar_path)
            push_to_lutao(tar_file)
            print("删除文件夹：", path_dir)
            shutil.rmtree(path_dir)


def create_tar(path, out_file):
    """
    将指定目录下的所有内容打包成 tar 文件
    :param path: 要打包的目录路径
    :param out_file: 输出 tar 文件的路径
    """
    if not os.path.isdir(path):
        raise ValueError(f"{path} 不是有效的目录")

    with tarfile.open(out_file, "w") as tar:
        for root, _, files in os.walk(path):
            for file in files:
                if file.lower().endswith(".png"):
                    full_path = os.path.join(root, file)
                    full_path = str(full_path)
                    current_path_dir = str(path)
                    # 计算相对路径
                    arc_name = os.path.relpath(full_path, current_path_dir)
                    tar.add(full_path, arcname=arc_name)
    print(f"目录 {path} 已打包成 {out_file}")


def upload_afs(tar_path):
    """
    上传文件到 AFS
    """
    process = subprocess.Popen(
        ['/home/<USER>/afs/bin/afsshell', '--username=map-data-streeview', '--password=map-data-streeview', 'put',
         '{}'.format(tar_path),
         'afs://aries.afs.baidu.com:9902/user/map-data-streeview/aoi-ml/parking/roadside/street_picture/'],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True
    )
    stdout, stderr = process.communicate()
    print(stdout)
    print(stderr)
    return stdout, stderr


def push_to_lutao(tar_file):
    """
    发送到路淘
    """
    # curl -d '{
    #     "afs_path":"/user/map-data-streeview/aoi-ml/parking/roadside/street_picture/road_side_1126.tar",
    #     "token":"gD2fI9UKZUtYhvww",
    #     "type":1
    # }' 'http://gzdt-sys-rpm100vqs1j3.gzdt.baidu.com:8016/audit/api/inter/parkingfrontpic'
    data = {
        "afs_path": "/user/map-data-streeview/aoi-ml/parking/roadside/street_picture/{}".format(tar_file),
        "token": "gD2fI9UKZUtYhvww",
        "type": 1
    }
    print('发送请求', json.dumps(data))
    rep = requests.post('http://inner-lutao.baidu-int.com/audit/api/inter/parkingfrontpic', json=data)
    print(rep.text)


if __name__ == "__main__":

    # work_id = sys.argv[1]
    # run(work_id, 'test', './img_path_test/')
    if len(sys.argv) > 1:
        _path = sys.argv[1]
    else:
        _path = "/home/<USER>/chenlinsong/aoi-ml/src/parking_roadside/img_path_test"
    batch_run_todo(_path)
