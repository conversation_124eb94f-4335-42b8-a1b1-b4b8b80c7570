"""
link 失效
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/RQaqyYDT_O/_O9549mpnb3GfV
"""
import csv
import dataclasses
import json
import logging
import multiprocessing
from typing import List

import shapely.ops
import shapely.wkt
from tqdm import tqdm

from src.parking.storefront.flow.context import gen_ctx
from src.parking_roadside import plane
from src.parking_roadside.db import pg
from src.parking_roadside.db import road
from src.parking_roadside.db.master_back import query_parking_ach_by_pid
from src.parking_roadside.db.pg import (query_bid_by_uid, fetch_parking_line_poi)
from src.parking_roadside.road_chains import get_parking_road_chains, get_storefront_park_road_chains
from src.tools.aoi_tools import calc_iou

METER = 1e-5

# 修复方式
REPAIR_NON = 0  # 不需要修复
REPAIR_AUTO = 1  # 自动修复
REPAIR_WORK = 2  # 人工作业修复
REPAIR_IDF = 3  # 走识别修复
REPAIR_IDF_V2 = 5  # 走识别修复
REPAIR_AUTO_V2 = 4  #

ROAD_CHAIN_COVERED_PARK_MIN_RATE = 0.99


def _get_short_id2_long_id(short_link_ids: list) -> dict:
    resp = {}
    for s_lid in short_link_ids:
        l_lid = road.get_long_link_id(s_lid)
        if l_lid != '':
            resp[s_lid] = l_lid
    return resp


def get_id2link_infos(long_link_ids: list) -> dict:
    """获取id对应的link信息"""
    return _get_id2link_infos(long_link_ids)


def _get_id2link_infos(long_link_ids) -> dict:
    resp = {}
    for l_lid in long_link_ids:
        link = road.fetch_link_by_id(l_lid)
        if not link:
            continue
        resp[l_lid] = link
    return resp


def _get_short_link_ids(road_relation: dict) -> list:
    if 'link_info' not in road_relation:
        return []
    return list(set([item['link_id'] for item in road_relation['link_info']]))


def _get_invalid_short_link_ids(road_relation: dict) -> tuple:
    """
    获取失效的短 link_id
    """
    short_link_ids = _get_short_link_ids(road_relation)
    return _get_invalid_short_link_ids_by(short_link_ids)


def _get_invalid_short_link_ids_by(short_link_ids):
    sid2lid = _get_short_id2_long_id(short_link_ids)
    lid2link = _get_id2link_infos(list(sid2lid.values()))
    invalid = []
    effected = []
    for short_id, long_id in sid2lid.items():
        if long_id not in lid2link:
            invalid.append(short_id)
        else:
            effected.append(long_id)
    return invalid, effected


@dataclasses.dataclass
class ChangedHistory:
    """
    改变的历史
    """
    pk_id: int
    link_v1: str
    link_v2: str
    change: int

    def get_invalid_short_lids(self) -> list:
        """
        获取失效的 link_ids
        """
        return self.link_v1.split(',')

    def get_effected_short_lids(self) -> list:
        """
        获取有效的 link_ids
        """
        return self.link_v2.split(',')


@dataclasses.dataclass
class ChangedHistories:
    """
    改变的历史集合
    """
    histories: List[ChangedHistory]

    def get_invalid(self) -> list:
        """
        获取失效的长 link_id 集合
        """
        lids = []
        for _history in self.histories:
            lids += _history.get_invalid_short_lids()
        return list(set(lids))

    def get_deleted(self) -> list:
        """
        获取删除的 link_ids
        """
        lids = []
        for _history in self.histories:
            if _history.change == 2:
                lids += _history.get_invalid_short_lids()
        return list(set(lids))

    def get_merged(self) -> list:
        """
        获取合并的
        """
        lids = []
        for _history in self.histories:
            if _history.change == 4:
                lids += _history.get_invalid_short_lids()
        return list(set(lids))

    def get_merged_histories(self) -> List[ChangedHistory]:
        """
        获取合并的记录
        """
        resp = []
        for _history in self.histories:
            if _history.change == 4:
                resp.append(_history)
        return resp


def _get_changed_histories(invalid_short_lids: list) -> ChangedHistories:
    records = pg.get_changed_histories(invalid_short_lids)
    changes = []
    for _record in records:
        if _record['change'] in [1, 5]:
            continue
        changes.append(ChangedHistory(
            pk_id=_record['id'],
            link_v1=_record['link_v1'],
            link_v2=_record['link_v2'],
            change=_record['change'],
        ))
    return ChangedHistories(histories=changes)


@dataclasses.dataclass
class LinkInvalidPark:
    """
    link 失效路侧停车场
    """
    data: dict
    poi: dict
    invalid_short_link_ids: list  # 失效的短 link_id
    effected_long_link_ids: list  # 有效的长 link_id
    related_road_chains: list  # 有关联的路链
    changed_histories: ChangedHistories = None

    def __post_init__(self):
        self.bid = self.poi['bid']
        self.wkt = self.data['area_wkt']
        self.road_relation = self.data['road_relation']
        self.road_chain_covered_rates = self._calc_road_chain_covered_rates()
        self.geometry = shapely.wkt.loads(self.data['area_wkt'])
        self._init_road_info()

    def _init_road_info(self):
        self.orientations = list(set([item['orientation'] for item in self.road_relation['link_info']]))
        self.lid2ori = {item['link_id']: item['orientation'] for item in self.road_relation['link_info']}
        self.lid2link = {}
        for item in self.road_relation['link_info']:
            long_id = road.get_long_link_id(item['link_id'])
            link = road.fetch_link_by_id(long_id)
            self.lid2link[item['link_id']] = link

    def _calc_road_chain_covered_rates(self) -> list:
        rates = []
        for chain in self.related_road_chains:
            rates.append(_calc_covered_rate(chain['geom_wkt'], self.wkt))
        return rates

    def road_chain_covered_park(self) -> bool:
        """
        路连能覆盖，返回 True
        """
        for rate in self.road_chain_covered_rates:
            if rate >= ROAD_CHAIN_COVERED_PARK_MIN_RATE:
                return True
        return False

    def get_road_chain_represent_link_id(self) -> list:
        """
        获取每条路链代表的 link_id
        """
        link_ids = []
        for chain in self.related_road_chains:
            link_ids.append(chain['link_id_list'][0])
        return link_ids

    def ori_is_unique(self) -> bool:
        """
        方向是否唯一，是返回 True
        """
        return len(self.orientations) == 1

    def get_unique_ori(self) -> int:
        """
        获取唯一的方向
        """
        if not self.ori_is_unique():
            raise Exception(f"方向不唯一")
        return self.orientations[0]

    def get_short_lids(self) -> list:
        """
        获取短 link_id 集合
        """
        return _get_short_link_ids(self.road_relation)

    def save_changed_histories(self, histories: ChangedHistories):
        """
        保存改变的历史记录
        """
        self.changed_histories = histories

    def changed_ori_is_conflict(self) -> bool:
        """
        改变 link 的朝向是否有冲突
        """
        histories = self.changed_histories.get_merged_histories()
        for _history in histories:
            link_ids = _history.get_invalid_short_lids()
            oris = set()
            for lid in link_ids:
                oris.add(self.lid2ori[lid])
            if len(oris) != 1:
                return True
        return False

    def _get_big_ori(self, link_ids: list) -> int:
        """
        获取大方向（最长距离的方向）
        """
        max_len = 0
        max_lid = ''
        for _lid in link_ids:
            if _lid not in self.lid2ori:
                continue
            link = self.lid2link[_lid]
            if not link:
                continue
            link_geo = shapely.wkt.loads(link['geom_wkt'])
            inte_geo = link_geo.intersection(self.geometry)
            if inte_geo.length > max_len:
                max_len = inte_geo.length
                max_lid = _lid
        if max_lid == '':
            raise Exception(f"{self.bid}; {link_ids} 获取不到最大方向")
        return max_lid

    def get_new_lid2ori(self) -> dict:
        """
        获取最新的 link_id 及对应的朝向
        """
        lid2ori = {}
        invalid = []
        histories = self.changed_histories.get_merged_histories()
        for _history in histories:
            old_lids = _history.get_invalid_short_lids()
            new_lids = _history.get_effected_short_lids()

            # 获取最大的方向
            temp_ori = self._get_big_ori(old_lids)
            for _n_lid in new_lids:
                lid2ori[_n_lid] = temp_ori
            invalid += old_lids

        for _lid, _ori in self.lid2ori.items():
            if _lid in invalid:
                continue
            lid2ori[_lid] = _ori
        return lid2ori


def _get_road_chain_dirs(road_chain: dict) -> list:
    dirs = []
    for link_id in road_chain['link_id_list']:
        link = road.fetch_link_by_id(link_id)
        if link:
            dirs.append(link['dir'])
    return dirs


def _dir_is_allow(dirs: list) -> bool:
    """
    合规的返回 True
    """
    dirs = list(set(dirs))
    if len(dirs) == 0:
        return False
    if len(dirs) == 1 and dirs[0] == 1:
        return True
    if len(dirs) == 2 and 2 in dirs and 3 in dirs:
        return True
    return False


def _road_chain_dir_is_allow(road_chain: dict) -> tuple:
    dirs = _get_road_chain_dirs(road_chain)
    allow = _dir_is_allow(dirs)
    return allow, dirs


def _filter_intersects_only_point(a, bs: list, items: list) -> list:
    """
    过滤仅仅相交于点的
    """
    resp = []
    for idx, b in enumerate(bs):
        i = a.intersection(b)
        if i.geom_type == 'Point':
            continue
        resp.append(items[idx])
    return resp


def _get_intersects_links(wkt: str) -> list:
    """
    获取相交的
    """
    links = road.fetch_road(wkt)
    if not links:
        return []

    geo = shapely.wkt.loads(wkt)
    sps = [shapely.wkt.loads(item['geom_wkt']) for item in links]
    return _filter_intersects_only_point(geo, sps, links)


def get_related_road_chains(wkt: str, version: str) -> list:
    """
    获取一定范围内的路链
    """
    # return _get_related_road_chains(wkt, effected_long_lids=[], filter=False)
    return _get_related_road_chains(wkt, effected_long_lids=[], filter=True, version=version)


def _get_related_road_chains(park_wkt: str, effected_long_lids: list, filter: bool = True, version='v1') -> list:
    """
    获取有关的路链，会去重
    filter: 是否过滤; 过滤会把一些特殊路链过滤掉，比如垂直的
    """
    park_geo = shapely.wkt.loads(park_wkt)
    shrink = park_geo.buffer(-2 * METER)  # 缩放一下

    direct_geo = park_geo
    if park_geo.geom_type == 'MultiPolygon':
        direct_geo = list(park_geo.geoms)[0]  # 取一个代表
    park_direct = plane.calc_polygon_x_angle(direct_geo)
    print(f"面角度：{park_direct}")

    links = _get_intersects_links(shrink.wkt)
    all_long_lids = list(set(effected_long_lids + [a_link['link_id'] for a_link in links]))

    def _gen_unique_key(link_ids: links) -> str:
        """
        生成唯一key
        """
        link_ids = sorted(link_ids)
        return ';'.join(link_ids)

    key2chain = {}
    for link_id in all_long_lids:
        try:
            if version == 'v1':
                chain = get_parking_road_chains(link_id)
            else:
                chain = get_storefront_park_road_chains(link_id)
        except Exception as e:
            logging.exception(e)
            continue
        if len(chain['link_id_list']) == 0:
            # 没有获取到路链怎么办
            continue
        uk = _gen_unique_key(chain['link_id_list'])
        if uk in key2chain:
            continue
        if not filter:
            # 不过滤
            key2chain[uk] = chain
            continue

        line_geo = shapely.wkt.loads(chain['geom_wkt'])
        print(f"路链：{line_geo}")

        line_geo = line_geo.intersection(park_geo)
        if str(line_geo.geom_type).upper() == 'MULTILINESTRING':
            line_geo = shapely.ops.linemerge(list(line_geo.geoms))
        if str(line_geo.geom_type).upper() == 'MULTILINESTRING':
            print("相交不是线，那么应该取一个代表，最长的线")
            line_geo = max(line_geo.geoms, key=lambda line: line.length)
        if str(line_geo.geom_type).upper() != 'LINESTRING':
            print(f"和路链相交的不是线:{line_geo}")
            continue
        if line_geo.is_empty:
            print("路链是空的")
            continue
        line_direct = plane.calc_line_x_angle(line_geo)
        diff_direct = abs(line_direct - park_direct)
        print(f"路链角度：{line_direct}; 差异角度：{diff_direct}")
        if 30 < diff_direct < 150:
            print(f"角度过大，放弃")
            continue
        key2chain[uk] = chain
    return list(key2chain.values())


def _road_chain_covered_park(chain_wkt: str, park_wkt: str) -> bool:
    """
    路链范围是否覆盖了停车场面；覆盖了返回 True
    """
    covered_rate = _calc_covered_rate(chain_wkt, park_wkt)
    if covered_rate < ROAD_CHAIN_COVERED_PARK_MIN_RATE:
        return False
    return True


def _calc_covered_rate(chain_wkt, park_wkt):
    """
    计算覆盖范围
    """
    line_geo = shapely.wkt.loads(chain_wkt).buffer(5 * METER)
    park_geo = shapely.wkt.loads(park_wkt)
    inte_geo = line_geo.intersection(park_geo)
    covered_rate = calc_iou(park_wkt, inte_geo.wkt)
    return covered_rate


def _can_auto_relate(park: LinkInvalidPark) -> tuple:
    """
    能否自动关联，能返回 True
    """
    chain_num = len(park.related_road_chains)
    if chain_num == 0:
        # print(f"{park.bid} 没有关联上路链")
        return False, "没有关联上路链"
    if chain_num > 1:
        # print(f"{park.bid} 关联上了多个路链:{chain_num}")
        return False, f"关联上了多个路链:{chain_num}; {park.get_road_chain_represent_link_id()}"
    road_chain = park.related_road_chains[0]
    if not _road_chain_covered_park(road_chain['geom_wkt'], park.wkt):
        # print(f"{park.bid} 路链不能完整覆盖停车场面")
        return False, f"路链不能完整覆盖停车场面; {park.road_chain_covered_rates}; {park.get_road_chain_represent_link_id()}"
    allow, dirs = _road_chain_dir_is_allow(road_chain)
    if not allow:
        # print(f"{park.bid} 方向不唯一")
        return False, f"方向不唯一; {dirs}"
    return True, ""


def _handle_link_attr_change(park: LinkInvalidPark) -> tuple:
    """
    是否有属性变更
    """
    if park.road_chain_covered_park():
        return None, REPAIR_NON, ""
    # for chain in park.related_road_chains:
    #     if _road_chain_covered_park(chain['geom_wkt'], park.wkt):
    #         return None, REPAIR_NON
    # reason =
    reason = (f"{park.bid} 路链覆盖不了, 需要走识别; "
              f"覆盖比例：{park.road_chain_covered_rates}; {park.get_road_chain_represent_link_id()}")
    print(reason)
    # _add_upd_ir('auto_link_invalid_250620', park)
    return park, REPAIR_IDF, reason


def _handle_poi_history(poi: dict) -> tuple:
    """
    处理一个 poi 有 link 失效
    """
    park = query_parking_ach_by_pid(poi['bid'])
    road_relation = park['road_relation']
    invalid_short_lids, effected_long_lids = _get_invalid_short_link_ids(road_relation)

    not_link_invalid = len(invalid_short_lids) == 0  # 无 link 缺失
    if not_link_invalid:
        filter = False
    else:
        filter = True
    print(f"{poi['bid']} 失效: {not_link_invalid}；{invalid_short_lids}; 还有效：{effected_long_lids}")

    road_chains = _get_related_road_chains(park['area_wkt'], effected_long_lids, filter)
    invalid_park = LinkInvalidPark(park, poi, invalid_short_lids, effected_long_lids, road_chains)
    if not_link_invalid:
        return _handle_link_attr_change(invalid_park)

    can, reason = _can_auto_relate(invalid_park)
    if can:
        chain = invalid_park.related_road_chains[0]
        reason = f"{invalid_park.bid} 能自动关联;{chain['geom_wkt']}"
        print(reason)
        return invalid_park, REPAIR_AUTO, reason
        # return invalid_park, REPAIR_IDF
    else:
        reason = f"{invalid_park.bid} 自动关联解决不了: {reason}"
        print(reason)
        return invalid_park, REPAIR_IDF_V2, reason


def _get_intersects_link_ids(wkt: str, link_ids: list) -> list:
    geo = shapely.wkt.loads(wkt)
    lid2geo = {}
    for _link_id in link_ids:
        link = road.fetch_link_by_id(_link_id)
        if not link:
            continue
        lgeo = shapely.wkt.loads(link['geom_wkt'])
        if lgeo.intersects(geo):
            lid2geo[_link_id] = lgeo
    return _filter_intersects_only_point(geo, list(lid2geo.values()), list(lid2geo.keys()))


def _add_auto_work_ach(batch_id: str, park: LinkInvalidPark):
    """
    添加自动作业成果
    """
    # chain_link_ids = park.related_road_chains[0]['link_id_list']
    chain_link_ids_list = [item['link_id_list'] for item in park.related_road_chains]
    idx2_link_ids, works = _associate_prev_work_and_link(park, chain_link_ids_list)
    for idx, _work in enumerate(works):
        _type = 'auto_work_v1'
        if pg.had_updated_link_invalid(park.bid, _work['id'], _type):
            print(
                f"{_type} 更新过了; bid: {park.bid}; uid: {park.poi['uid']}; work_id:{_work['id']};{idx2_link_ids[idx]}")
            continue
        # pg.add_link_invalid_upd_history(park.bid, _work['id'], _type)

        link_ids = idx2_link_ids[idx]
        link_str = ','.join(link_ids)
        # side_dict = {_link_id: park.get_unique_ori() for _link_id in link_ids}
        # side_json = json.dumps(side_dict, ensure_ascii=False)
        # pg.add_auto_update_link_ach(_work['id'], link_str, batch_id, 'auto_release', side_json)
        print(
            f"add_auto_update_link_ach; bid: {park.bid}; uid: {park.poi['uid']}; "
            f"work_id:{_work['id']};{idx2_link_ids[idx]}")


def _add_auto_work_ach_v2(batch_id: str, park: LinkInvalidPark):
    """
    添加自动作业成果
    """
    new_lid2ori = park.get_new_lid2ori()
    new_chain_link_ids = list(new_lid2ori.keys())
    idx2_link_ids, works = _associate_prev_work_and_link(park, new_chain_link_ids)
    for idx, _work in enumerate(works):
        _type = 'auto_work_v2'
        if pg.had_updated_link_invalid(park.bid, _work['id'], _type):
            print(f"{park.bid}; {_work['id']}; {_type} 更新过了")
            continue
        # pg.add_link_invalid_upd_history(park.bid, _work['id'], _type)

        link_ids = idx2_link_ids[idx]
        link_str = ','.join(link_ids)
        side_dict = {_link_id: new_lid2ori[_link_id] for _link_id in link_ids}
        side_json = json.dumps(side_dict, ensure_ascii=False)
        # pg.add_auto_update_link_ach(_work['id'], link_str, batch_id, 'auto_release', side_json)
        print(f"add_auto_update_link_ach; bid: {park.bid}; uid: {park.poi['uid']};{idx2_link_ids[idx]}")


def _add_upd_ir(batch_id: str, park: LinkInvalidPark):
    """
    新增更新识别
    """
    chain_link_ids_list = [item['link_id_list'] for item in park.related_road_chains]
    idx2_link_ids, works = _associate_prev_work_and_link(park, chain_link_ids_list)
    for idx, _work in enumerate(works):
        # _type = 'upd_ir'
        # if pg.had_updated_link_invalid(park.bid, _work['id'], _type):
        #     print(f"{_type} 更新过了; bid: {park.bid}; work_id: {_work['id']};")
        #     continue
        # pg.add_link_invalid_upd_history(park.bid, _work['id'], _type)

        _link_id = idx2_link_ids[idx][0]
        pg.add_upd_identification(_work['id'], _link_id, batch_id)
        print(f"add_upd_identification; bid: {park.bid}; work_id: {_work['id']};_link_id: {_link_id}")


def _associate_prev_work_and_link(park: LinkInvalidPark, chain_link_ids_list: list):
    """
    组合之前的 work 和 新的 link
    """
    works = pg.query_work_by_uid(park.poi['uid'])
    works = [_work for _work in works if _work['status'] == 5]
    if park.geometry.geom_type == 'MultiPolygon':
        geoms = list(park.geometry.geoms)
    else:
        geoms = [park.geometry]
    if len(works) != len(geoms):
        raise Exception(f"{park.bid} 面的数量和作业数量不一致")

    idx2_link_ids = {}
    for idx, _work in enumerate(works):
        resp_link_ids = []
        for chain_link_ids in chain_link_ids_list:
            link_ids = _get_intersects_link_ids(geoms[idx].wkt, chain_link_ids)
            if len(link_ids) == 0:
                continue
            resp_link_ids = chain_link_ids
            break
        if len(resp_link_ids) == 0:
            raise Exception(f"{park.bid} 的面匹配不到 link: {chain_link_ids_list}")
        idx2_link_ids[idx] = resp_link_ids
    return idx2_link_ids, works


def handle_center(handled_fn: callable, batch_id: str = '', multi: int = 30):
    """
    处理历史所有失效的 link
    """
    pois = _get_pois()
    print(f"总共有：{len(pois)}")
    with multiprocessing.Pool(processes=multi) as pool:
        result = pool.map(handled_fn, pois)
    result = [_item for _item in result if _item[0] is not None]

    _to_csv(result)

    # if batch_id == '':
    #     today = datetime.today()
    #     batch_id = f"auto_link_invalid_{today.strftime('%m%d')}"
    # auto_num, idf_num, auto_num_v2 = 0, 0, 0
    # for item in tqdm(result):
    #     try:
    #         park: LinkInvalidPark = item[0]
    #         repair_mode = item[1]
    #         if repair_mode == REPAIR_AUTO:
    #             _add_auto_work_ach(batch_id, park)
    #             auto_num += 1
    #         elif repair_mode == REPAIR_WORK:
    #             pass
    #         elif repair_mode == REPAIR_IDF:
    #             _add_upd_ir(batch_id, park)
    #             idf_num += 1
    #         elif repair_mode == REPAIR_AUTO_V2:
    #             _add_auto_work_ach_v2(batch_id, park)
    #             auto_num_v2 += 1
    #         else:
    #             print(f"{park.bid} 暂时不处理")
    #     except Exception as e:
    #         logging.exception(e)
    # print(f"处理了:{len(result)}; 自动化处理了:{auto_num}; 识别处理了:{idf_num}；自动处理v2：{auto_num_v2}")


def _to_csv(results: list):
    dst = './tmp/link_invalid.tsv'
    with open(dst, 'w') as hdw:
        writer = csv.writer(hdw, delimiter='\t')
        header = ['bid', 'mode', 'reason']
        writer.writerow(header)
        for item in tqdm(results, desc='写文件'):
            park: LinkInvalidPark = item[0]
            repair_mode = item[1]
            reason = item[2]
            writer.writerow([park.bid, repair_mode, reason])
    print(dst)


def _get_auto_link_invalid_0606_uids():
    qry = f"""
    select b.uid 
    from parking_roadside_upd_ir a left join parking_line_turing_work b 
    on a.work_id = b.id 
    where a.batch_id = 'auto_link_invalid_0606' 
    """
    ctx = gen_ctx(autocommit=True)
    return ctx.poi_db.get_values(qry)


def _get_pois():
    pois = fetch_parking_line_poi()
    uq_pois = []
    seen_bids = set()

    white_uids = {uid: '1' for uid in _get_auto_link_invalid_0606_uids()}
    # white_uids = []
    white_bids = [
        # '2531998480336311771',
        # '14509077608728948494',
        # '10878358574497432948',

        # '5418629067496119268',

        # 失效，不能完全覆盖
        # '18426764414364036566',

        # 失效，方向不唯一
        # '15642718298546398027',
    ]

    for _poi in tqdm(pois, desc='获取poi'):
        bid = _poi['bid']
        if len(white_uids) and _poi['uid'] not in white_uids:
            continue
        if len(white_bids) and _poi['bid'] not in white_bids:
            continue
        if bid not in seen_bids:
            seen_bids.add(bid)
            uq_pois.append(_poi)
    return uq_pois
    # return uq_pois[:2000]


def handle_daily():
    """
    处理每日 link 失效
    """
    print(f"开始清除多余版本数据")
    maintain_change_version()
    print(f"结束清除多余版本数据")

    print(f"开始维护 link_v1_arr")
    pg.maintain_link_v1_arr()
    print(f"结束维护 link_v1_arr")

    handle_center(_handle_poi_daily, multi=5)


def handle_history():
    """
    处理历史
    """
    handle_center(_handle_poi_history, multi=100)


def maintain_change_version():
    """
    维护改变历史的版本
    只保留最近的 5 个版本
    """
    versions = pg.get_road_change_versions()
    if len(versions) <= 5:
        print(f"当前数据版本数：{len(versions)}; 不处理")
        return

    versions = [float(_version) for _version in versions]
    saved_versions = sorted(versions, reverse=True)[:5]  # 降序排序后取前5个
    for _version in versions:
        if _version in saved_versions:
            continue
        print(f"{_version} 版本的数据需要被清除")
        pg.remove_road_change_by_version(str(_version))


def demo3():
    """demo"""
    pois = _get_pois()
    bids = [
        # '14631266649495362115',  # ok || 有失效的 link 在变更记录中未查询到
        # '13736120221260032965',  # ok  || 改变记录，在最新的母库中不存在
        # '12495304737910177085',  # ok
        # '17105443501902917917',  # ok  || 有失效的 link 在变更记录中未查询到
        # '2692743083108371584',  # ok  || 有失效的 link 在变更记录中未查询到
        # '6545095074818594387',  # ok  || 有失效的 link 在变更记录中未查询到
        #
        '495590612757901328',  # 转弯
        '3626277287987792165',  # 面弯的

        #     # '824182245475204805',  # 十字路口
        #     # '9310291217931413934',  # 十字路口
        #     # '10917925743138961635',  # 十字路口
        #     # '5195234025966919365',  # 环形状
        #     # '3497002678542305293',  # 十字路口，多个
        #     # '9933023899018311253',  # 十字路口，多个
        #     # '6977783534712852448',  # 十字路口，多个
        #     # '11579368115568203844',  # 十字路口，多个
        # '3871066850008241509',  # 获取路链异常
        # '11748010140599887681',  # 获取路链异常
        # '18337195834884443145',  # 获取路链异常
        # '10099938906370263839',  # 获取路链异常
        # '10255980983563115132',  # 获取路链异常
        # '1930717450189026415',  # 获取路链异常
        # '10955937326167687676',  # 获取路链异常
        # '9707341468631960081',  # 获取路链异常

        # '14858603741504079373',  # 多个路链, 路链太长导致的
        # '4957289248280328727',  # 多个路链, 路链太长导致的
        # '12020773431879214957',  # 多个路链, 路链太长导致的

        # '2023492967569069458',  # 没有关联上路链
        # '8998016524879881747',  # 多个路链，面太多
        # '5268581676509411585',  # 路链不能完整覆盖停车场面, 面弯的
    ]
    for _poi in pois:
        if _poi['bid'] not in bids:
            continue
        resp = _handle_poi_daily(_poi)
        # print(resp[1])
        # exit()


def demo2():
    """demo"""
    uid = '576b361682d343768e7205e277b325a1'
    poi = query_bid_by_uid(uid)
    park = query_parking_ach_by_pid(poi['bid'])
    invalid_short_lids, effected_long_lids = _get_invalid_short_link_ids(park['road_relation'])

    invalid_park = LinkInvalidPark(
        data=park,
        poi=poi,
        invalid_short_link_ids=invalid_short_lids,
        effected_long_link_ids=effected_long_lids,
        related_road_chains=_get_related_road_chains(park['area_wkt'], effected_long_lids)
    )
    _add_auto_work_ach('test', invalid_park)
    _add_upd_ir('test', invalid_park)


def _handle_poi_daily(poi: dict) -> tuple:
    """
    处理日常更新的一个 poi
    """
    park = query_parking_ach_by_pid(poi['bid'])
    road_relation = park['road_relation']
    invalid_short_lids, effected_long_lids = _get_invalid_short_link_ids(road_relation)
    if not invalid_short_lids:
        print(f"{poi['bid']} 没有失效的 link")
        return None, REPAIR_NON

    road_chains = _get_related_road_chains(park['area_wkt'], effected_long_lids, False)
    invalid_park = LinkInvalidPark(park, poi, invalid_short_lids, effected_long_lids, road_chains)

    histories = _get_changed_histories(invalid_short_lids)
    diff_lids = list(set(invalid_short_lids).difference(set(histories.get_invalid())))
    if len(diff_lids) != 0:
        # FIXME 先观察
        print(f"{poi['bid']} 有失效的 link 在变更记录中未查询到: {diff_lids}")
        return invalid_park, REPAIR_NON
    if len(histories.get_deleted()) > 0:
        print(f"{poi['bid']} 改变记录中，有删除的记录：{histories.get_deleted()}")
        return invalid_park, REPAIR_IDF

    invalid_short_lids, _ = _get_invalid_short_link_ids_by(histories.get_invalid())
    if len(invalid_short_lids) > 0:
        # FIXME 先观察
        print(f"{poi['bid']} 改变记录，在最新的母库中不存在: {invalid_short_lids}")
        return invalid_park, REPAIR_NON
    invalid_park.save_changed_histories(histories)

    # merged_lids = histories.get_merged()
    # merged_diff = set(merged_lids).difference(set(invalid_park.get_short_lids()))
    # if len(merged_diff) > 0:
    #     print(f"{poi['bid']} 合并记录中，存在其他 link，不知道 link 朝向：{merged_diff}")
    #     return invalid_park, REPAIR_IDF

    # if invalid_park.changed_ori_is_conflict():
    #     print(f"{poi['bid']} 改变的朝向有冲突")
    #     return invalid_park, REPAIR_IDF
    return invalid_park, REPAIR_AUTO_V2


if __name__ == '__main__':
    handle_history()
    # handle_daily()
