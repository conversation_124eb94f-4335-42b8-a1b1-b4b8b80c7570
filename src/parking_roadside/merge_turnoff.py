# -*- coding: utf-8 -*-
"""
融合熄火点, 计算导流区
"""

import os
import sys
from datetime import timed<PERSON><PERSON>

from shapely import wkt, MultiLineString
from db.pg import query_work_by_batch_id, fetch_work_res_by_links, update_work_status_by_id, update_work_extend, \
    get_full_work_by_status_message
from db.road import check_diversion, fetch_multi_links_by_id
from split_link import split_link_by_work_id

grandparent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
sys.path.append(grandparent_dir)

from src.tools.turnoff_point import get_points_of_doctor, get_points_of_other

# 是否要去重 debug用不需要去重
check_intersection = True


def get_turnoff_by_multi_link(link_id_list):
    """
    获取多条道路的熄火点
    :param link_id_list: [str] link_id列表
    :return:
    [doc_len, zy_len]  博士熄火点， 众源熄火点
    """
    work_link_geom = fetch_multi_links_by_id(link_id_list)
    if work_link_geom is None or 'geom_wkt' not in work_link_geom or work_link_geom['geom_wkt'] == '':
        return 0, 0
    geom_obj = wkt.loads(work_link_geom['geom_wkt'])
    if geom_obj is None:
        return 0, 0
    geom_obj_buffer = geom_obj.buffer(0.0001)
    doctor_turnoff_point_num = get_points_of_doctor(geom_obj_buffer.wkt)
    doc_len = len(doctor_turnoff_point_num)
    zy_len = 0
    zhongyuan_turnoff_point_num = get_points_of_other(geom_obj_buffer.buffer(0.0001, cap_style=2, join_style=1))
    for point in zhongyuan_turnoff_point_num:
        if (point.parking_type == "start_car" and point.duration > timedelta(minutes=2) or
                point.parking_type == "mid_stop_car" and point.duration > timedelta(minutes=60)):
            zy_len += 1
    return doc_len, zy_len


def run_item(work):
    """
    融合熄火点， 计算导流区
    """
    turing_id = work["turing_id"]
    exits_link = False
    if work['status'] > 0 and work["link_id_list"].strip() == '':
        return
    if work["link_id_list"].strip() == '':
        update_work_status_by_id(work['id'], work['status'], 'link_empty')
    if check_intersection and work['work_type'] == 0:  # 新增情报才判重
        link_id_list = work['link_id_list'].split(",")
        work_res = fetch_work_res_by_links(link_id_list)
        if work_res is not None and len(work_res) > 0:
            for work2 in work_res:
                if work2['turing_id'] == turing_id:
                    continue
                print("exists", "link_id", work['link_id_list'], "id1", work['id'], "id2", work2['id'])
                exits_link = True
                break
    if exits_link:
        update_work_status_by_id(work['id'], work['status'], 'ach_duplicate')
        return
    link_geom_str = work["area_geom_wkt"]
    turing_id = ""
    if work.get("turing_id"):
        turing_id = work.get("turing_id")
    work_link_geom = fetch_multi_links_by_id(work['link_id_list'].split(","))
    if work_link_geom is not None and 'geom_wkt' in work_link_geom:
        work_link_geom_obj = wkt.loads(work_link_geom['geom_wkt'])
        if isinstance(work_link_geom_obj, MultiLineString):
            print("geom_error", work['id'], work['link_id_list'], work_link_geom['geom_wkt'], sep="\t")
    geom_obj = wkt.loads(link_geom_str)
    geom_obj_buffer = geom_obj.buffer(0.0001)
    doctor_turnoff_point_num = get_points_of_doctor(link_geom_str)
    doc_len = len(doctor_turnoff_point_num)
    zy_len = 0
    zhongyuan_turnoff_point_num = get_points_of_other(geom_obj_buffer.buffer(0.0001, cap_style=2, join_style=1))
    for point in zhongyuan_turnoff_point_num:
        if (point.parking_type == "start_car" and point.duration > timedelta(minutes=2) or
                point.parking_type == "mid_stop_car" and point.duration > timedelta(minutes=60)):
            zy_len += 1
    match = "not_enough"
    if doc_len + zy_len > 20 or 'force' in work['batch_id']:
        extend = ""
        if check_diversion(work['area_geom_wkt']):
            extend = '{"diversion":1}'
        match = "matches"
        update_work_extend(work['id'], extend, -1, 'turnoff_matched')
    else:
        update_work_status_by_id(work['id'], work['status'], 'not_enough_turnoff')
    print(match, work['id'], turing_id, work['status'], work['link_id_list'], work['area_geom_wkt'], doc_len,
          zy_len, sep="\t")
    split_link_by_work_id(work['id'])


def run_full():
    """
    执行全部待处理
    """
    work_list = get_full_work_by_status_message(-2, '')
    for work in work_list:
        run_item(work)


def run_by_batch(batch_id):
    """
    通过批次号执行
    """
    work_list = query_work_by_batch_id(batch_id)
    for work in work_list:
        run_item(work)


if __name__ == '__main__':
    run_full()
    # run_by_batch(sys.argv[1])
