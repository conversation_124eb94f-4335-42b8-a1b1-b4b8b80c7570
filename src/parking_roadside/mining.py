"""
情报挖掘
"""
import csv
import dataclasses
import multiprocessing
from typing import List, Tuple
from tqdm import tqdm
import shapely.wkt

from src.parking.recognition import dbutils
from src.tools import pgsql
from src.parking.storefront.flow.db import join_str, array_chunk
from src.model_mysql import beeflow_model
from src.parking_roadside.db.model import pg_config_dict

METER = 1e-5
CHAIN_CONF = {
    'db': pg_config_dict['chains_db']['database'],
    'host': pg_config_dict['chains_db']['host'],
    'port': pg_config_dict['chains_db']['port'],
    'pwd': pg_config_dict['chains_db']['password'],
    'user': pg_config_dict['chains_db']['user'],
}


@dataclasses.dataclass
class RoadChain:
    """
    路链
    """
    uid: str
    link_id: str
    wkt: str


@dataclasses.dataclass
class Info:
    """
    情报
    """
    uid: str
    point: str
    source: str
    chain: RoadChain = None
    success: list = None
    fail: list = None

    def __post_init__(self):
        self.scope = shapely.wkt.loads(self.point).buffer(60 * METER)

    def get_chain_uid(self) -> str:
        """获取路链 uid"""
        if self.chain is None:
            return ''
        return self.chain.uid

    def get_chain_link_id(self) -> str:
        """获取路链的 link_id"""
        if self.chain is None:
            return ''
        return self.chain.link_id


def _get_info_by_old_park() -> List[Info]:
    """
    获取旧的路测停车场
    """
    qry = f"""
    select bid, st_astext(gcj_geom) 
    from park_online_data 
    where show_tag = '路侧停车场' and park_spec = 0 and status = 1 
--     limit 100
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)

    if not res:
        return []
    return [Info(
        uid=i[0],
        point=i[1],
        source='old_park',
    ) for i in res]


def find_nearest_chains(area_geom, point_geom):
    """
    查找最近的链
    """
    qry = """
            select 
                uid, 
                link_id,
                st_astext(link_geom) as link_geom_wkt,
                st_distance(link_geom, %s) as distance
            from parking_road_chains
            where 
                st_intersects(link_geom, %s)
            ORDER BY distance ASC
            LIMIT 1
            """
    return dbutils.fetch_one(CHAIN_CONF, qry, ('srid=4326;' + point_geom, 'srid=4326;' + area_geom))


def _bind_road_chain(infos: List[Info]) -> List[Info]:
    """
    绑定路链
    """
    num = 0
    for info in tqdm(infos, desc='绑定路链'):
        chain = find_nearest_chains(info.scope.wkt, info.point)
        if chain is None:
            print(f"{info.uid} 没有绑定路链")
            num += 1
            continue
        info.chain = RoadChain(
            uid=chain[0],
            link_id=chain[1],
            wkt=chain[2],
        )
    print(f"有 {num} 没有绑上路链")
    return infos


def _get_handled_histories(info: Info) -> tuple:
    """
    获取处理的历史记录
    返回成功的，失败的
    """
    bf_query = beeflow_model.BFQuery()
    qry = f"""
    select result_uid, result_message 
    from parking_line_track where chains_uid = '{info.get_chain_uid()}' 
    """
    res = bf_query.queryall(qry)
    if not res:
        return [], []
    successes, fails = [], []
    for i in res:
        item = {
            'result_uid': i[0],
            'result_message': i[1],
        }
        if item['result_uid'] == '':
            fails.append(item)
        else:
            successes.append(item)
    return _append_success_history(successes), fails


def _append_success_history(successes: list) -> list:
    if not successes:
        return []

    result_uids = [s['result_uid'] for s in successes]
    qry = f"""
    select uid, message from  parking_line_turing_work 
    where uid in ({join_str(result_uids)}) 
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    uid2msg = {}
    if res:
        uid2msg = {i[0]: i[1] for i in res}

    for s in successes:
        s['handle_message'] = uid2msg.get(s['result_uid'], '')
    return successes


def _analyse_success_history(successes: list) -> int:
    """
    分析成功的处理记录
    返回 0: 表示未知
    返回 1: 表示是需要的记录，所有记录都是 not_enough_turnoff 或者 no_juejin_result
    返回 2: 表示不是需要的
    """
    if len(successes) == 0:
        return 0

    for s in successes:
        # if s['handle_message'] not in ['not_enough_turnoff', 'no_juejin_result']:
        if s['handle_message'] not in ['not_enough_turnoff']:
            return 2
    return 1


def _analyse_failed_history(fails: list) -> int:
    """
    分析失败的处理记录
    返回 0: 表示未知
    返回 1: 表示是需要的记录，
    返回 2: 表示不是需要的
    """
    if len(fails) == 0:
        return 0
    keyword = '熄火点不足'
    for f in fails:
        if keyword in f['result_message']:
            return 1
    return 2


def _filter_infos(infos: List[Info]) -> Tuple[List[Info], List[Info]]:
    """
    过滤情报
    """
    infos = [i for i in infos if i.chain is not None]

    result = []
    filter = []
    for i in tqdm(infos, desc='过滤情报'):
        successes, fails = _get_handled_histories(i)
        i.success = successes
        i.fail = fails

        # 先分析成功的
        success_res = _analyse_success_history(successes)
        if success_res == 1:
            result.append(i)
            continue
        fail_res = _analyse_failed_history(fails)
        if fail_res == 1:
            result.append(i)
            continue
        filter.append(i)
    return result, filter


def _save_to_file(infos: List[Info], file: str) -> int:
    """
    保存情报
    """
    dst = file
    with open(dst, 'w') as hdw:
        writer = csv.writer(hdw, delimiter='\t')
        header = ['bid', 'link_id', 'chain_uid', 'success', 'fail']
        writer.writerow(header)
        for info in infos:
            writer.writerow([info.uid, info.chain.link_id, info.get_chain_uid(), info.success, info.fail])
    print(dst)
    return len(infos)


def _mining_part_old_park(infos: list) -> tuple:
    infos = _bind_road_chain(infos)
    print(f"有{len(infos)}个绑定了路链")
    infos, filter = _filter_infos(infos)
    return infos, filter


def _cal_chunk_size(multi: int, total: int) -> int:
    if total < multi:
        return total
    if total % multi == 0:
        return int(total / multi)
    return int(total / multi) + 1


def mining_by_old_park():
    """
    通过旧停车场挖掘情报
    """
    infos = _get_info_by_old_park()

    multi = 20
    info_chunks = array_chunk(infos, _cal_chunk_size(multi, len(infos)))
    print(f"有{len(infos)}个有旧停车场情报")
    multi = min(multi, len(info_chunks))
    print(f"并发度：{multi}")

    with multiprocessing.Pool(processes=multi) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.map(_mining_part_old_park, info_chunks)

    filtered = []
    response = []
    for r in results:
        response += r[0]
        filtered += r[1]
    print(f"过滤之后，还剩{len(response)}情报; 过滤了：{len(filtered)}")
    num = _save_to_file(response, './tmp/mining_info_v2.tsv')
    print(f"保存了：{num}")
    _save_to_file(filtered, './tmp/filtered_info_v2.tsv')


def _get_competitor() -> List:
    """
    获取竞品情报
    """
    qry = f"""
    select gid, st_astext(geom) from competitor_park 
    where poi_tag = '交通设施;停车场' and space_attr in ('路侧停车位', '路侧停车场') 
--     limit 100
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)

    if not res:
        return []
    return [Info(
        uid=i[0],
        point=i[1],
        source='competitor',
    ) for i in res]


def _filter_by_ach(infos: List[Info]) -> List[Info]:
    """
    根据成果过滤
    """
    result = []
    for info in tqdm(infos, desc='根据成果过滤'):
        qry = f"""
        select bid 
        from parking 
        where show_tag = '路侧停车场' and park_spec = 1 and status = 1 
        and st_intersects(area, st_geomfromtext('{info.scope.wkt}', 4326))
        """
        res = dbutils.fetch_one(pgsql.BACK_CONFIG, qry)
        if not res:
            result.append(info)
    print(f"根据成果过滤了：{len(infos) - len(result)}")
    return result


def _filter_by_old(infos: List[Info]) -> List[Info]:
    """
    根据已经投放过的 link 过滤
    """
    link_set = set()
    old_file = [
        './tmp/mining_info_v2.tsv',
    ]
    for file in old_file:
        with open(file, 'r') as hdr:
            reader = csv.reader(hdr, delimiter='\t')
            next(reader)
            for row in reader:
                link_set.add(row[1])

    result = []
    for info in tqdm(infos, desc='根据已经投放过的 link 过滤'):
        if info.get_chain_link_id() in link_set:
            continue
        result.append(info)
    print(f"根据已经投放过的 link 过滤：{len(infos) - len(result)}")
    return result


def _mining_part_competitor_info(infos: List[Info]) -> tuple:
    infos = _filter_by_ach(infos)
    infos = _bind_road_chain(infos)
    print(f"有{len(infos)}个绑定了路链")
    infos = _filter_by_old(infos)
    infos, filter = _filter_infos(infos)
    return _mining_part_old_park(infos)


def mining_by_competitor():
    """根据竞品挖掘"""
    infos = _get_competitor()

    multi = 20
    info_chunks = array_chunk(infos, _cal_chunk_size(multi, len(infos)))
    print(f"有{len(infos)}个有竞品停车场情报")
    multi = min(multi, len(info_chunks))
    print(f"并发度：{multi}")

    with multiprocessing.Pool(processes=multi) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.map(_mining_part_competitor_info, info_chunks)

    filtered = []
    response = []
    for r in results:
        response += r[0]
        filtered += r[1]
    print(f"过滤之后，还剩{len(response)}情报; 过滤了：{len(filtered)}")
    num = _save_to_file(response, './tmp/mining_competitor_info_v2.tsv')
    print(f"保存了：{num}")
    _save_to_file(filtered, './tmp/filtered_competitor_info_v2.tsv')


if __name__ == '__main__':
    # mining_by_old_park()
    mining_by_competitor()
