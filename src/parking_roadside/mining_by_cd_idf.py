"""
通过车天识别挖掘情报
"""
import csv
import dataclasses
import json
import logging
import multiprocessing
from datetime import datetime
from typing import List, Dict, Tuple
import requests

from tqdm import tqdm

from src.parking_roadside.road_chains import get_parking_road_chains
from src.parking_roadside.db import road
from src.parking.storefront.flow.db import join_str, array_chunk
from src.parking.recognition import dbutils
from src.tools import pgsql
from psycopg2.extras import Json


@dataclasses.dataclass
class IrRec:
    """
    识别结果
    """
    rt1_type: str
    task_id: str
    score: str
    pic_id: str
    link_id: str = ''


@dataclasses.dataclass
class Track:
    """
    轨迹
    """
    cdid: str
    pic_id: str
    link_id: str


def to_s_lids(long_link_ids) -> list:
    """长id 转换为短id"""
    long_link2short_link = road.get_short_link_id(long_link_ids)
    return list(long_link2short_link.values())


@dataclasses.dataclass
class RoadChain:
    """
    路链
    """
    link_id_list: list
    geom: str

    def __post_init__(self):
        self.uq_key = self._gen_unique_key()
        self.s_lids = to_s_lids(self.link_id_list)

    def _gen_unique_key(self) -> str:
        """
        生成唯一key
        """
        link_ids = sorted(self.link_id_list)
        return ';'.join(link_ids)


@dataclasses.dataclass
class HandledRoadChain:
    """
    处理过的路链
    """
    chain: RoadChain
    works: list
    ir_recs: List[IrRec] = None

    def get_ir_recs_detail(self) -> list:
        """获取识别明细"""
        return [dataclasses.asdict(ir) for ir in self.ir_recs]

    def get_chain_link_id(self) -> str:
        """获取路链中的一条 link"""
        return self.chain.link_id_list[0] if len(self.chain.link_id_list[0]) else ''


def _get_roadside_ir_recs_one_cd(cdid) -> List[IrRec]:
    """获取一个车天的路侧识别结果"""
    rec_url = 'http://m.map.baidu.com:8013/elementdir/%s/%s_210.rec' % (cdid, cdid)
    response = requests.get(rec_url)
    response.raise_for_status()  # 如果请求失败会抛出异常

    resp = []
    data = json.loads(response.text)
    rec_list = data['labels']
    print("fetch_info_success", len(rec_list))
    for label in rec_list:
        # 210024 众源车位线类型
        if label['rt1_type'] == "210024":
            resp.append(IrRec(
                rt1_type=label['rt1_type'],
                task_id=label['task_id'],
                score=label['score'],
                pic_id=label['pic_id'],
            ))
    return resp


def _get_tracks_one_cd(cdid: str, white_pic_ids: list) -> List[Track]:
    """
    获取一个车天的轨迹
    white_pic_ids: 白名单，在白名单的轨迹才会返回
    """
    track_url = f"http://m.map.baidu.com:8013/tracklist/{cdid}.track"
    response = requests.get(track_url)
    response.raise_for_status()  # 如果请求失败会抛出异常

    white_pic_ids_dict = {pid: 1 for pid in white_pic_ids}

    resp = []
    data = json.loads(response.text)
    for item in tqdm(data['track'], desc=f"获取轨迹:{cdid}"):
        if len(white_pic_ids_dict) > 0 and item['pic_id'] not in white_pic_ids_dict:
            continue
        resp.append(Track(
            cdid=cdid,
            pic_id=item['pic_id'],
            link_id=item['link_id']
        ))
    return resp


def _get_lid2ir_recs(ir_recs: List[IrRec], tracks: List[Track]) -> Dict[str, List[IrRec]]:
    """获取一根 link 对应的多个识别结果"""
    resp = {}
    pid2ir = {ir.pic_id: ir for ir in ir_recs}
    for tk in tracks:
        if tk.pic_id not in pid2ir:
            continue
        ir = pid2ir[tk.pic_id]
        ir.link_id = tk.link_id
        resp.setdefault(tk.link_id, []).append(ir)
    return resp


def _get_lid2chain(link_ids: list) -> Dict[str, RoadChain]:
    lid2chain = {}
    for lid in link_ids:
        try:
            chain = get_parking_road_chains(lid)
        except Exception as e:
            logging.exception(e)
            continue
        if len(chain['link_id_list']) == 0:
            continue
        lid2chain[lid] = RoadChain(
            link_id_list=chain['link_id_list'],
            geom=chain['geom_wkt'],
        )
    return lid2chain


def _get_chain_works(chain: RoadChain) -> list:
    """
    获取路链作业记录
    """
    qry = f"""
    select id, link_id_list, status, message, uid, is_automatic from parking_line_turing_work 
    where id in (
        select work_id from parking_roadside_link_map 
        where link_id in ({join_str(chain.s_lids)})
    ) 
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    if not res:
        return []
    return [{
        'work_id': i[0],
        'link_id_list': i[1],
        'status': i[2],
        'message': i[3],
        'uid': i[4],
        'is_automatic': i[5],
    } for i in res]


def _is_add(works: list):
    done_status = 5
    for w in works:
        if w['status'] == done_status:
            return False
    uids = [w['uid'] for w in works if w['status'] == 6]
    if _has_bid(uids):
        return False
    return True


def _has_bid(uids: list) -> bool:
    """
    有 bid 返回 True
    """
    if len(uids) == 0:
        return False
    qry = f"""
    select bid from parking_roadside_bid_map where uid in ({join_str(uids)})
    """
    res = dbutils.fetch_one(pgsql.POI_CONFIG, qry)

    if not res:
        return False
    return True


def _is_upd(works: list):
    done_status = 5
    for w in works:
        if w['status'] == done_status:
            return True
    return False


def _diff_chain(chains: List[RoadChain]) -> Tuple[List[HandledRoadChain], List[HandledRoadChain]]:
    """
    路链差分，返回 新增、更新 处理过的路链集合
    """
    new, upd = [], []
    for chain in tqdm(chains, desc="路链差分"):
        works = _get_chain_works(chain)
        handle = HandledRoadChain(chain=chain, works=works)
        if _is_add(works):
            new.append(handle)
            continue
        if _is_upd(works):
            upd.append(handle)
            continue
    return new, upd


def _chain_add_ir_rec_detail(handled: List[HandledRoadChain], lid2ir_recs: Dict[str, List[IrRec]]):
    """新增 添加识别明细"""
    for h in handled:
        ir_recs = []
        for lid in h.chain.link_id_list:
            if lid in lid2ir_recs:
                ir_recs += lid2ir_recs[lid]
        h.ir_recs = ir_recs
    return handled


def get_one_cd_lid2ir_recs(cdid: str):
    """获取一个车天中，link 对应的识别结果"""
    try:
        ir_recs = _get_roadside_ir_recs_one_cd(cdid)
        tracks = _get_tracks_one_cd(cdid, [ir.pic_id for ir in ir_recs])
        return _get_lid2ir_recs(ir_recs, tracks), ""
    except Exception as e:
        logging.exception(e)
        print(f"{cdid}异常")
        return {}, cdid


def analyse_ir_rec(lid2ir_recs: dict):
    """分析 link 对应的识别结果"""
    print(f"开始处理:{lid2ir_recs.keys()}")
    lid2chain = _get_lid2chain(list(lid2ir_recs.keys()))
    new, upd = _diff_chain(list(lid2chain.values()))
    return _chain_add_ir_rec_detail(new, lid2ir_recs), _chain_add_ir_rec_detail(upd, lid2ir_recs)


def _mining_detail(lid2ir_recs):
    params = []
    for lid, ir_recs in lid2ir_recs.items():
        params.append({lid: ir_recs})
    print(f"link 量级是：{len(lid2ir_recs)}")
    with multiprocessing.Pool(processes=90) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.map(analyse_ir_rec, params)
    new = []
    upd = []
    for r in results:
        new += r[0]
        upd += r[1]
    return new, upd


def _get_lid2ir_recs_by_cdids(cdids):
    print(f"车天数:{len(cdids)}")
    with multiprocessing.Pool(processes=90) as pool:
        # 使用 map 方法并行运行任务函数，并收集结果
        results = pool.map(get_one_cd_lid2ir_recs, cdids)

    lid2ir_recs = {}
    err_cdids = []
    for item in results:
        data, err_cdid = item
        if err_cdid != '':
            err_cdids.append(err_cdid)
            continue
        for lid, ir_recs in data.items():
            if lid not in lid2ir_recs:
                lid2ir_recs[lid] = ir_recs
            else:
                lid2ir_recs[lid] += ir_recs
                print(f"{lid} 不同车天有聚合")
    return lid2ir_recs, err_cdids


def _get_exists_not_consume_info(curs, link_id):
    """存在，且未消费，返回 True"""
    qry = f"""
    select id, batch_id from parking_roadside_mining_info
    where long_link_id = '{link_id}' and status = 'INIT'
    """
    curs.execute(qry)
    res = curs.fetchone()
    if not res:
        return None
    return {
        'id': res[0],
        'batch_id': res[1],
    }


def _save_info(curs, batch_id: str, consumer: str, info: HandledRoadChain, info_type: str):
    link_id = info.get_chain_link_id()
    if link_id == '':
        return
    status = 'INIT'
    memo = ''
    exists_info = _get_exists_not_consume_info(curs, link_id)
    if exists_info and exists_info['batch_id'] == batch_id:
        print(f"{batch_id}, {link_id} 存在且未消费，跳过")
        return
    if exists_info:
        status = 'DUPLICATE'
        memo = f"存在且未消费:{exists_info['id']}"
    qry = f"""
        insert into parking_roadside_mining_info 
            (long_link_id, type, batch_id, source, consumer, status, detail, memo)
        values 
            (%s, %s, %s, %s, %s, %s, %s, %s)
    """
    curs.execute(qry, [link_id, info_type, batch_id, 'idf_cd', consumer, status, Json(info.get_ir_recs_detail()), memo])


def _save_infos(new: list[HandledRoadChain], info_type: str, batch_id: str, consumer: str):
    """保存情报"""
    with pgsql.get_connection_ttl(pgsql.POI_CONFIG) as conn:
        curs = conn.cursor()
        for info in tqdm(new, desc="保存情报"):
            _save_info(curs, batch_id, consumer, info, info_type)
    print(f"保存新增了：{len(new)}")


def _get_batch_id(run_turnoff: bool = False) -> str:
    batch = "idf_cd"
    if not run_turnoff:
        batch += "_force"
    return batch + '_' + datetime.today().strftime("%Y%m%d")


def _get_consumer(batch_id: str) -> str:
    return 'rdsList:' + batch_id


def _get_cdids() -> list:
    """获取所有的车天号"""
    qry = f"""
    select cdid from identify_car_day_record where status = 'INIT'
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    if not res:
        return []
    return [i[0] for i in res]


def _maintain_car_day_status(cdids: list):
    """维护车天状态"""
    cdid_chunks = array_chunk(cdids, 200)
    with pgsql.get_connection_ttl(pgsql.POI_CONFIG) as conn:
        curs = conn.cursor()
        for cdid_chunk in tqdm(cdid_chunks, desc="维护车天状态"):
            qry = f"""
            update identify_car_day_record 
            set status = 'DONE' 
            where cdid in ({join_str(cdid_chunk)})
            """
            curs.execute(qry)


def mining():
    """
    情报挖掘
    把不同车天的 link 对应的识别结果聚合在一起
    聚合之后再统一分析
    """
    cdids = _get_cdids()
    # cdids = [cdids[0]]  # todo test
    lid2ir_recs, err_cdids = _get_lid2ir_recs_by_cdids(cdids)
    new, upd = _mining_detail(lid2ir_recs)

    batch_id = _get_batch_id(run_turnoff=False)
    consumer = _get_consumer(batch_id)
    _save_infos(new, 'new', batch_id, consumer)
    _maintain_car_day_status(list(set(cdids).difference(set(err_cdids))))


if __name__ == '__main__':
    mining()

