"""
开方式停车场，需要绑定 link
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/wBpTyxTfJn/cfdb2fa73dd745
规格文档
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Fr8QITM0sh/YZCUDtktsX/9zNYYzDm6SP7EQ
"""
import argparse
import csv
import dataclasses
import json
import logging
from typing import List, Union

from tqdm import tqdm
import shapely.wkt
import shapely.ops
from shapely.geometry import Point, LineString
import numpy as np

from src.parking_roadside.link_invalid import get_related_road_chains, get_id2link_infos
from src.parking_roadside.db import road
from src.parking.storefront.utils.geometric import get_foot_point, flat_line
from src.parking.storefront.diff.polygon_differ import get_line_by_location, interpolate
from src.parking.storefront.flow.coord_helper import gcj2bdmc
from src.parking.storefront.flow.model import ach
from src.parking.storefront.flow.context import gen_ctx, Context
from src.parking.storefront.flow.db import handle_apostrophe
from src.parking.storefront.check.check_area import calculate_angle
from src.parking.storefront.utils.geometric import flat_polygon

METER = 1e-5


@dataclasses.dataclass
class RoadChain:
    """
    路链接
    """
    wkt: str
    s_lid2link: dict

    def __post_init__(self):
        self.geo = shapely.wkt.loads(self.wkt)


@dataclasses.dataclass
class RoadRelationItem:
    """
    子关联关系
    参考路侧停车场规格，type=4（门前-开放场景，与导航对齐），12025897256509421237，
    {"link_info": [{"link_id": "1531982354", "orientation": 3,
    "point": "13050742.408788972,4704742.871581114", "type": 5},
    {"link_id": "1682932043", "orientation": 3, "point": "13050718.337780293,4704767.7976529095", "type": 5}]}
关联聚合 (road_relation_childrens)
    """
    link_id: str
    point: str
    type: int
    # node_id: str = ''


@dataclasses.dataclass
class RoadRelation:
    """
    关联关系
    """
    link_info: List[RoadRelationItem]


@dataclasses.dataclass
class GeomRelatedRoadChain:
    """
    范围和路链的关系
    """
    wkt: str
    chain: RoadChain

    def __post_init__(self):
        self.distances = []
        self.locations = self._get_projected_locations()

    def _get_foot_point(self, pt: Point) -> Union[Point, None]:
        ft = get_foot_point(pt, self.chain.geo)
        if ft is not None:
            return ft
        resp = []
        for link in self.chain.s_lid2link.values():
            l_geo = shapely.wkt.loads(link['geom_wkt'])
            ft = get_foot_point(pt, l_geo)
            if ft is None:
                continue
            resp.append({
                'ft': ft,
                'ds': pt.distance(ft),
            })
        resp.sort(key=lambda x: x['ds'])
        if len(resp) == 0:
            return None
        return resp[0]['ft']

    def _get_projected_locations(self) -> list:
        """获取映射的位置信息"""
        location = []
        boundary = shapely.wkt.loads(self.wkt).boundary
        for _cd in set(list(boundary.coords)):
            _pt = Point(_cd)
            _ft = self._get_foot_point(_pt)
            if _ft is None:
                # print(_pt, 11111)
                # print(self.chain.geo)
                # print(self.chain.geo.project(_pt, True))
                # print(interpolate(self.chain.geo, self.chain.geo.project(_pt, True)))
                location.append(-1)
                continue
            # print(_pt, _ft)
            # print(LineString([_pt, _ft]), _pt.distance(_ft) / METER)
            self.distances.append(_pt.distance(_ft))
            location.append(self.chain.geo.project(_pt, normalized=True))
        return sorted(location)

    def been_complete_coverage(self) -> bool:
        """
        范围被完全覆盖，返回 True
        """
        if len(self.locations) == 0:
            return False
        if self.locations[0] == -1:
            return False
        return True

    def get_average_distance(self) -> float:
        """
        获取面和路链的平均距离
        """
        if len(self.distances) == 0:
            return 100 * METER
        return sum(self.distances) / len(self.distances)

    def get_road_relation(self) -> RoadRelation:
        """
        获取关联关系
        """
        start_loc, end_loc = self.locations[0], self.locations[-1]
        projected_line = get_line_by_location(self.chain.geo, start_loc, end_loc)
        projected_area = projected_line.buffer(0.5 * METER)

        items = []
        for s_lid, link in self.chain.s_lid2link.items():
            link_geo = shapely.wkt.loads(link['geom_wkt'])
            intersect = projected_area.intersection(link_geo)
            if not intersect.length:
                continue
            center_gcj = interpolate(intersect, 0.5)
            center_bdmc = shapely.wkt.loads(gcj2bdmc(center_gcj.wkt))
            items.append(RoadRelationItem(
                link_id=s_lid,
                type=4,
                point=f"{center_bdmc.x},{center_bdmc.y}"
            ))
        return RoadRelation(link_info=items)


def _get_short_lid2link(long_link_ids: list) -> dict:
    l_lid2link = get_id2link_infos(long_link_ids)
    lid2sid = road.get_short_link_id(long_link_ids)
    result = {}
    for lid, sid in lid2sid.items():
        result[sid] = l_lid2link[lid]
    return result


def _get_related_road_chains(wkt: str) -> List[RoadChain]:
    result = []
    chains = get_related_road_chains(wkt, version='v2')
    for _chain in chains:
        s_lid2link = _get_short_lid2link(_chain['link_id_list'])

        geoms = [shapely.wkt.loads(link['geom_wkt']) for link in s_lid2link.values()]
        geos = shapely.ops.unary_union(geoms)
        if geos.geom_type == 'LineString':
            chain_wkt = geos.wkt
        else:
            chain_wkt = shapely.ops.linemerge(geos).wkt

        for line in flat_line(chain_wkt):
            _sid2link = {}
            area = line.buffer(0.5 * METER)
            for sid, link in s_lid2link.items():
                link_geo = shapely.wkt.loads(link['geom_wkt'])
                if area.contains(link_geo):
                    _sid2link[sid] = link
            if len(_sid2link) > 0:
                result.append(RoadChain(
                    wkt=line.wkt,
                    s_lid2link=_sid2link,
                ))
    return result


def _get_related_chains(wkt: str, buffer_size: float) -> List[GeomRelatedRoadChain]:
    geo = shapely.wkt.loads(wkt)
    buffer_wkt = geo.buffer(buffer_size).wkt
    print(buffer_wkt)
    road_chains = _get_related_road_chains(buffer_wkt)

    result = []
    for _chain in road_chains:
        result.append(GeomRelatedRoadChain(
            wkt=wkt,
            chain=_chain,
        ))
    return result


def _filter_by_completed_coverage(related_chains: List[GeomRelatedRoadChain]) -> List[GeomRelatedRoadChain]:
    return [_chain for _chain in related_chains if _chain.been_complete_coverage()]


def _sort_by_dist_asc(related_chains: List[GeomRelatedRoadChain]) -> List[GeomRelatedRoadChain]:
    related_chains.sort(key=lambda x: x.get_average_distance())
    return related_chains


def is_L_polygon(wkt: str) -> bool:
    """
    如果是 L 或者包含 L 形的几何，返回 True
    """
    geo = shapely.wkt.loads(wkt)
    assert geo.geom_type == 'Polygon', f"is_L_polygon 不支持类型：{geo.geom_type}"

    boundary = geo.boundary.simplify(5 * METER)
    coords = np.array(boundary.coords[:-1])  # 去除封闭的最后一个点（和第一个重复）
    for _i, i_cd in enumerate(coords):
        if _i == 0:  # 第一个角度
            bf = coords[-1]
        else:
            bf = coords[_i - 1]
        if _i == len(coords) - 1:  # 最后一个角
            af = coords[0]
        else:
            af = coords[_i + 1]
        angle = calculate_angle(bf, coords[_i], af)
        if angle < 60 or angle > 120:
            continue

        _pt = Point(i_cd)
        _buffer = _pt.buffer(15 * METER)
        split_res = geo.difference(_buffer)
        split_polygons = flat_polygon(split_res)
        if len(split_polygons) == 1:
            continue
        return True
    return False


def get_road_relation(wkt: str, buffer_size: float) -> tuple:
    """
    获取关联关系
    """
    if is_L_polygon(wkt):
        return None, f"是 L 形状的，暂不处理"
    related_chains = _get_related_chains(wkt, buffer_size)
    related_chains = _filter_by_completed_coverage(related_chains)
    # for r in related_chains:
    #     print(r.get_road_relation(), r.get_average_distance())
    # exit()
    related_chains = _sort_by_dist_asc(related_chains)
    if len(related_chains) == 0:
        return None, f"没有匹配上路链"
    return related_chains[0].get_road_relation(), ""


def demo1():
    """测试"""
    ctx = gen_ctx(autocommit=True)

    data = [
        {
            'iid': '680068296bda15739765d86b',
            'expected_lids': ['1633068622'],
        },
        {
            'iid': '67a1f7717dca0e8585502adf',
            # 'expected_lids': ['1635116255', '1635116110', '1558206349', '1617290962', '1617290961', '1576185512'],
            'expected_lids': ['1635116255、1635116110、1558206349、1617290962、1617290961、1576185512'],
        },
        {
            'iid': '67ee4d1a165d8cc16e23d8b2',
            'expected_lids': ['1707396926、1659602779'],
        },
        {
            'iid': '67a9a857a633e10265224f3b',
            'expected_lids': ['1624168534'],
        },
        {
            'iid': '67c51de105a202558e0c4a03',
            'expected_lids': ['1597498944、1620194086、1620194007、1530545112、1530541595'],
        },
        {
            'iid': '67d441912053dd591c5ac5b1',
            'expected_lids': ['1581409263、1579761848'],
        },
        {
            'iid': '67ced9d192cc2491675e671b',
            'expected_lids': ['1546594709、1542641022'],
        },
        {
            'iid': '68033b2171692b5b615bd4ed',
            'expected_lids': ['1528305771'],
        },
        {
            'iid': '67ee44d37ad71f88850ca715',
            'expected_lids': ['1531335668'],
        },
        {
            'iid': '67c6a4216bda15d54b59c816',
            'expected_lids': ['1614741865、1614743465、1614737694、1564045734'],
        },
        {
            'iid': '67b2ea9a956a0e56152e2b9f',
            'expected_lids': ['1601000402、1559070933、1530657922'],
        },
        {
            'iid': '67c05e6b8908304c2964e3dd',
            'expected_lids': ['1598192723、1701763892、1701763731、1616415911、1542739064、1542739123、1542739165'],
        },
        {
            'iid': '67c16758956a0eed182097ec',
            'expected_lids': ['1699725003'],
        },
        {
            'iid': '67b34bc415fbdb1e310f62a8',
            'expected_lids': ['1595899738'],
        },
        {
            'iid': '680880fb3c86ab2fd518a413',
            'expected_lids': ['1674068667'],
        },
        {
            'iid': '67fce453a712ddf3f7323534',
            'expected_lids': ['1692614022、1690575393'],
        },
        {
            'iid': '67adecf97b7667783e2e113c',
            'expected_lids': ['1611653923、1593136329、1593135095'],
        },
        {
            'iid': '67b587ce6bda1530b8563c87',
            'expected_lids': ['1600554483、1701779849、1701780065、1579819311'],
        },
        {
            'iid': '67ced71f15fbdb8b5375fdbb',
            'expected_lids': ['1653025481、1653025480、1597937926、1702780579'],
        },
        {
            'iid': '6805b42833afdd30d75b7558',
            'expected_lids': ['1647533369、1647532638'],
        },
        {
            'iid': '67adf06717f50caa2b774bdc',
            'expected_lids': ['1659280650、1674561052、1674561051'],
        },
        {
            'iid': '68036a83dda6cca0e13ce6bb',
            'expected_lids': ['1548416427、1548232537'],
        },
        {
            'iid': '67b80daa15fbdb4a993eb90d',
            'expected_lids': ['1616329163、1692869424、1692869423、1627605317、1627605050、1706300652'],
        },
        {
            'iid': '67bdb3a758b4b955a84d9383',
            'expected_lids': ['1527338071、1572172071、1553578602'],
        },
        {
            'iid': '67c05ea117241770a05ef7f5',
            'expected_lids': ['1551702812、1559163881、1553169246、1559639249、1560034310、1560832623、1557392289'
                              , '1551702600',
                              ],
        },
        {
            'iid': '67ac51e12dd62d183f40a3bd',
            'expected_lids': ['1709194082、1683316056'],
        },
        {
            'iid': '67a1f79f15fbdb6e231fbdca',
            'expected_lids': ['1682932043'],
        },
        {
            'iid': '67bdb1bb33afdd9dfc616d3c',
            'expected_lids': ['1685364434、1685367260'],
        },

        {
            'iid': '68006b9d46127714d4714e44',
            'expected_lids': ['1665962001、1665962038'],
        },
        {
            'iid': '67b3445bdda6cc77784650b2',
            'expected_lids': ['1623828714、1623828467、1602052489'],
        },
    ]

    def get_lastly(limit: int) -> list:
        return [
            '67ced9071bc7c06d35572e9e',
            '67ceda2e47fa586aa16eb205',
            '680350ae76d88a32493e6bc3',
            '67d1405cd2586b45265d4c49',
            '67ced71f15fbdb8b5375fdbb',
            '67ced6f4165d8c90de0483b8',
            '67ced73d50da1f8a1f79e656',
            '67ceda3a17f50c598e63c895',
            '67ced7ada40eaa14e416c26e',
            '67ced8c0a4aea17a9e7e4b20',
        ]
        _qry = f"""
        select turing_id from {ctx.turing_result_tab} 
        where data_type = '1209' and sub_result = '1' and recall = '2' limit {limit}
        """
        return ctx.poi_db.get_values(_qry)

    def _format_data(limit: int):
        iids = get_lastly(limit)
        resp = []
        for iid in iids:
            resp.append({
                'iid': iid,
                'expected_lids': [],
            })
        return resp

    data = _format_data(100000)

    white_iids = [
        # '67ced71f15fbdb8b5375fdbb',  # 道路有 交叉点内道路 (50)
        # '67bdb1bb33afdd9dfc616d3c',  # 有岔路口
        # '67a9a857a633e10265224f3b',  # 门前 8 级路
        # '67c16758956a0eed182097ec',  # 过滤门前路

        # '67d441912053dd591c5ac5b1',  # L 形
        # '67ced9d192cc2491675e671b',
        # '67fce453a712ddf3f7323534',
        # '67b3445bdda6cc77784650b2',
    ]

    def _get_wkt(iid: str) -> str:
        qry = f"""
        select st_astext(a.geom), st_astext(b.geom) 
        from {ctx.turing_result_tab} a left join {ctx.pushed_tab} b on a.info_id = b.info_id 
        where turing_id = '{iid}' 
        """
        res = ctx.poi_db.fetchone(qry)
        work_wkt, info_wkt = res
        work_geo = shapely.wkt.loads(work_wkt)
        if work_geo.is_empty:
            return info_wkt
        return work_wkt

    def _format_expected_lids(expected_lids: list) -> set:
        lids = []
        for i in expected_lids:
            lids += str(i).split('、')
        return set(lids)

    result = []
    for item in tqdm(data):
        if len(white_iids) > 0 and item['iid'] not in white_iids:
            continue
        print(f"开始处理；{item['iid']} ")
        wkt = _get_wkt(item['iid'])
        # print(wkt)

        expected_lids_set = _format_expected_lids(item['expected_lids'])
        item['expected_lids_set'] = expected_lids_set
        item['actual_lids'] = []
        item['contained'] = False
        item['more_lids'] = []
        item['diff_lids'] = []
        item['reason'] = []
        item['relation'] = []
        relation = None
        try:
            relation, reason = get_road_relation(wkt, 25 * METER)
            item['reason'] = reason
        except Exception as e:
            logging.exception(e)
            item['reason'] = str(e)

        if relation is not None:
            actual_lids = set([r_item.link_id for r_item in relation.link_info])
            contained = expected_lids_set.issubset(set(actual_lids))

            item['expected_lids_set'] = expected_lids_set
            item['actual_lids'] = actual_lids
            item['contained'] = contained
            item['more_lids'] = actual_lids.difference(expected_lids_set)
            item['diff_lids'] = expected_lids_set.difference(actual_lids)
            item['relation'] = [dataclasses.asdict(i) for i in relation.link_info]

        result.append(item)

    dest = './tmp/open_storefront_demo_v2.tsv'
    with open(dest, 'w') as hdw:
        writer = csv.writer(hdw, delimiter='\t')
        header = ['iid', 'expected_lids', 'expected_lids_set', 'actual_lids', 'contained',
                  'more_lids', 'diff_lids', 'reason', 'relation']
        writer.writerow(header)
        for item in result:
            writer.writerow(list(item.values()))
    print(dest)


def maintain_open_park_ach_road_relation(ctx: Context, limit: int):
    """
    维护开放停车场的 road_relation
    """
    where = f"""
    bid_status = 'effected' 
    and park_type = 'open' and status = 'READY' 
    """
    if limit > 0:
        where += f" limit {limit} "
    num = 0
    for parks in ach.get_parks_generate(ctx, where):
        for _park in parks:
            relation = None
            try:
                relation, reason = get_road_relation(_park.geom, 25 * METER)
            except Exception as e:
                logging.exception(e)
                reason = str(e)
            if relation is None:
                road_relation = {}
            else:
                road_relation = {'link_info': [dataclasses.asdict(i) for i in relation.link_info]}
            qry = f"""
            update {ctx.park_ach_tab} 
            set status = 'PARK_ACCESS_INTELLIGENCE', 
            road_relation_memo = '{reason}', 
            road_relation_strategy = '{handle_apostrophe(json.dumps(road_relation))}'
            where id = {_park.park_id} and status = 'READY' 
            """
            res = ctx.poi_db.execute(qry)

            num += res
            if res == 0:
                print(f"{_park.park_id} 维护 road_relation 失败")
            ctx.poi_db.commit_or_rollback()
    print(f"总共维护了: {num}")


def main():
    """
    主函数
    """
    limit = int(ARGS.limit)

    # ctx = gen_ctx(is_committed=False, debug=True)
    ctx = gen_ctx(autocommit=True, debug=True)
    maintain_open_park_ach_road_relation(ctx, limit)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='开放停车场策略绑定 link')
    parser.add_argument('--limit', type=int, default=10)

    ARGS = parser.parse_args()
    print(f"参数信息：{ARGS}")

    main()

