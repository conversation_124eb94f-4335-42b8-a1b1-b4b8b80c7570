# !/usr/bin/env python3
# coding=utf-8

"""
平面几何
"""
import math
from typing import Dict

import shapely.wkt
import shapely.geometry


class Angle:
    """
    角度
    """

    def __init__(self, degree: float):
        self._degree = degree
        self._radians = (math.pi / 180) * degree

    def radians(self) -> float:
        """
        获取弧度，比如 0，math.pi, n*math.pi
        """
        return float(self._radians)

    def degrees(self) -> float:
        """
        获取度数；比如 180，90
        """
        return self._degree

    def is_equal(self, angle1: 'Angle', tolerance: float = 1e-10) -> bool:
        """
        两个角度是否相等
        """
        if math.fabs(angle1._radians - self._radians) > tolerance:
            return False
        return True

    def sub(self, angle1: 'Angle') -> 'Angle':
        """
        减法
        """
        return Angle(degree=self.degrees() - angle1.degrees())

    def __str__(self) -> str:
        return str(self.degrees())


class AboutAngle(Angle):
    """
    近似的角度
    角度可能是 81.1, 81.2 81.9 那么这几个角度可以当成同一个角度 81 来处理
    """

    def __init__(self, degree: float, about: int):
        assert about > 0, f"{about} 必须大于 0"

        super().__init__(math.floor(degree / about) * about)


class AboutAngle1(AboutAngle):
    """
    近似的角度1，81.1, 81.2 81.9 那么这几个角度可以当成同一个角度 81 来处理
    """

    def __init__(self, degree: float):
        super().__init__(degree, 1)


class AboutAngle5(AboutAngle):
    """
    近似的角度5，81.1, 82.2 84.9 那么这几个角度可以当成同一个角度 80 来处理
    """

    def __init__(self, degree: float):
        super().__init__(degree, 5)


def azimuth_angle(x1, y1, x2, y2) -> Angle:
    """
    计算方位角函数[0,360)
    """
    angle = 0.0
    dx = x2 - x1
    dy = y2 - y1
    if x2 == x1:
        angle = math.pi / 2.0
        if y2 == y1:
            angle = 0.0
        elif y2 < y1:
            angle = 3.0 * math.pi / 2.0
    elif y2 == y1:
        if x2 > x1:
            angle = 0
        else:
            angle = math.pi
    elif x2 > x1 and y2 > y1:
        angle = math.atan(dy / dx)
    elif x2 > x1 and y2 < y1:
        angle = 3.0 * math.pi / 2.0 + math.atan(dx / -dy)
    elif x2 < x1 and y2 < y1:
        angle = 3.0 * math.pi / 2.0 + math.atan(dx / -dy)
    elif x2 < x1 and y2 > y1:
        angle = math.pi + math.atan(dy / dx)
    return Angle(degree=angle * 180 / math.pi)


def get_rect_other_point(a: shapely.geometry.Point, b: shapely.geometry.Point,
                         c: shapely.geometry.Point) -> shapely.geometry.Point:
    """
    已知矩形三点，求另外一点
    其中 a 点为直角
    """
    x = b.x + c.x - a.x
    y = b.y + c.y - a.y
    return shapely.geometry.Point(x, y)


def cal_angle(a: shapely.geometry.Point, b: shapely.geometry.Point, c: shapely.geometry.Point) -> Angle:
    """
    计算 向量 pa 与 pb 之间的夹角度 返回值在 [0, math.Pi] 之间
    """
    ang1 = azimuth_angle(a.x, a.y, b.x, b.y)
    ang2 = azimuth_angle(a.x, a.y, c.x, c.y)

    ang3 = math.fabs(ang1.degrees() - ang2.degrees())
    if ang3 > 180:
        ang3 = 360 - ang3
    return Angle(ang3)


def cal_angle_with_x_axis(a: shapely.geometry.Point, b: shapely.geometry.Point) -> Angle:
    """
    计算 ab 与 x-正轴 之间的夹角，返回值在 [0, math.Pi/2] 之间
    """
    if a.x == b.x:
        if a.y == b.y:
            degree = 0
        else:
            degree = 90
        return Angle(degree=degree)

    c = shapely.geometry.Point(b.x, a.y)
    return cal_angle(a, b, c)


def cal_straight_line_direct(a: shapely.geometry.Point, b: shapely.geometry.Point) -> Angle:
    """
    计算直线的方向;[0,90)
    a,b 是线的两点
    """
    angle = cal_angle_with_x_axis(a, b)
    if angle.radians() == math.pi / 2:
        return Angle(0)

    if (b.x < a.x and b.y >= a.y) or (b.x > a.x and b.y < a.y):
        angle = Angle(90 - angle.degrees())

    if angle.degrees() == 90:
        return Angle(0)
    return angle


def cal_direct(wkt: str, tolerance_angle: int = 1) -> Angle:
    """
    计算方向；方向用 角度和 x 轴正方向来衡量; [0,90)
    统计每条边的方向，累计权重（边长），挑权重最大的方向作为 wkt 的方向
    wkt 仅支持 polygon
    """
    about_angle2_weight: Dict[AboutAngle, float] = {}

    geo = shapely.wkt.loads(wkt)
    coords = list(geo.exterior.coords)
    for _idx, _coord in enumerate(coords):
        if _idx == len(coords) - 1:
            continue

        pointa = shapely.geometry.Point(_coord)
        pointb = shapely.geometry.Point(coords[_idx + 1])
        direct = cal_straight_line_direct(pointa, pointb)

        about_angle = AboutAngle(degree=direct.degrees(), about=tolerance_angle)
        distance = pointa.distance(pointb) / 1e-5

        if about_angle not in about_angle2_weight:
            about_angle2_weight[about_angle] = 0
        about_angle2_weight[about_angle] += distance

    max_weight = 0
    cur_about_angle = None
    for _about, _weight in about_angle2_weight.items():
        if _weight > max_weight:
            max_weight = _weight
            cur_about_angle = _about
    return cur_about_angle


def cal_rotated_angle(current: Angle, target: Angle) -> Angle:
    """
    计算旋转角度
    [-math.Pi/4, math.Pi/4) 之间
    正的角度是顺时针，负的角度是逆时针
    """
    diff = target.sub(current)
    left = -math.pi / 4
    righ = math.pi / 4
    if left <= diff.radians() < righ:
        return diff

    if diff.radians() < left:
        return Angle(90 + diff.degrees())
    return Angle(diff.degrees() - 90)


def calc_line_azimuth_angle(line: shapely.geometry.LineString) -> float:
    """
    计算线方位角函数[0,360)
    用线的两个端点
    """
    s_pt = line.coords[0]
    e_pt = line.coords[-1]
    return azimuth_angle(s_pt[0], s_pt[1], e_pt[0], e_pt[1]).degrees()


def calc_line_angle(line1: shapely.geometry.LineString, line2: shapely.geometry.LineString) -> float:
    """
    计算两条线之间的夹角；[0,180)
    """
    ang1 = calc_line_azimuth_angle(line1)
    ang2 = calc_line_azimuth_angle(line2)
    ang3 = math.fabs(ang1 - ang2)
    if ang3 > 180:
        ang3 = 360 - ang3
    return ang3


def calc_polygon_azimuth_angle(polygon: shapely.geometry.Polygon, tolerance_angle: int = 1) -> float:
    """
    计算 polygon 的方为角 [0,360)
    """
    about_angle2_weight: Dict[AboutAngle, float] = {}
    coords = list(polygon.exterior.coords)
    for _idx, _coord in enumerate(coords):
        if _idx == len(coords) - 1:
            continue

        pointa = shapely.geometry.Point(_coord)
        pointb = shapely.geometry.Point(coords[_idx + 1])
        direct = calc_line_azimuth_angle(shapely.geometry.LineString((_coord, coords[_idx + 1])))

        about_angle = AboutAngle(degree=direct, about=tolerance_angle)
        distance = pointa.distance(pointb) / 1e-5

        if about_angle not in about_angle2_weight:
            about_angle2_weight[about_angle] = 0
        about_angle2_weight[about_angle] += distance

    max_weight = 0
    cur_about_angle = None
    for _about, _weight in about_angle2_weight.items():
        if _weight > max_weight:
            max_weight = _weight
            cur_about_angle = _about
    return cur_about_angle.degrees()


def calc_polygon_x_angle(polygon: shapely.geometry.Polygon, tolerance_angle: int = 1) -> float:
    """
    计算 polygon 和 x轴 [0,180)
    """
    angle = calc_polygon_azimuth_angle(polygon, tolerance_angle)
    if 0 <= angle < 180:
        return angle
    return angle - 180


def calc_line_x_angle(line: shapely.geometry.LineString) -> float:
    """
    计算线 和 x轴 的夹角；[0,180)
    """
    angle = calc_line_azimuth_angle(line)
    if 0 <= angle < 180:
        return angle
    return angle - 180

