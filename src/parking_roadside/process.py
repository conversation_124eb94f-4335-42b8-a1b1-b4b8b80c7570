#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
执行路侧识别
"""

import json
import sys
import math
import uuid
import numpy as np
from shapely import wkt
from datetime import datetime
from shapely.geometry import Point, LineString, MultiLineString, MultiPoint
from shapely.ops import substring, unary_union, linemerge
from sklearn.cluster import DBSCAN
import concurrent.futures
import traceback
from dataclasses import dataclass
from typing import List, Iterator
from api.tracks_api import get_tracks_all
from api.infer_res_api import multi_get_infer_by_pic_helper
from api.img_request import request_parking_line
from db.road import fetch_link_by_id, fetch_nav_line_marking
from api.link_trans import get_multi_short_id
from db.drds import fetch_parking_line_infer_res, add_parking_line_infer_res, add_parking_track, update_parking_track
from road_chains import get_parking_road_chains

DEBUG = False  # 此处debug只为了打印一些信息，不影响程序运行, 也不影响数据入库


@dataclass
class TrackPoint:
    """
    点
    """
    x: float
    y: float

    def __iter__(self) -> Iterator[float]:
        yield self.x
        yield self.y


# [link_item['link_id'], key, c_geom_wkt, have_line_points, point_wkt, bos_key_list, total_points, cdid]
@dataclass
class TrackItem:
    """
    轨迹信息
    """
    pic_id: int
    bos_key: str
    cdid: str
    have_line: int
    point: TrackPoint


@dataclass
class CarDaysItem:
    """
    车天信息
    """
    cdid: str
    link_id: str
    track_id: int
    geom_wkt: str
    have_line_points: List[TrackPoint]
    point_wkt: str
    bos_key_list: list
    total_points: List[TrackPoint]
    track_list: List[TrackItem]


@dataclass
class LinkInferItem:
    """
    link信息
    """
    link_id: str
    have_parking_line: int
    confidence: float
    link_info: dict
    car_list: List[CarDaysItem]


def process_link(link_id, chains_list=()):
    """
    处理链路
    :param link_id:
    :param chains_list:
    :return: (hav_parking_line, car_list, link_item, confidence)
    """
    link_item = fetch_link_by_id(link_id)
    car_list = []
    hav_point_num = 0
    print("link_item", link_item)
    link_geom_str = link_item['geom_wkt']
    dir_link = ()  # 记录开始和结束的坐标
    if link_item['dir'] != 1:  # 不是双方向
        line_obj = wkt.loads(link_geom_str)
        if link_item['dir'] == 2:  # 顺方向
            dir_link = (line_obj.coords[0], line_obj.coords[-1])
        elif link_item['dir'] == 3:  # 逆方向
            dir_link = (line_obj.coords[-1], line_obj.coords[0])
    res_map = get_parking_line(link_id, link_geom_str, dir_link, chains_list)
    print("res_map_new", json.dumps(res_map, ensure_ascii=False))
    for key in res_map:
        total_points = []
        have_line_points = []
        bos_key_list = []
        cdid = ""
        track_list = []
        for item in res_map[key]:
            total_points.append(TrackPoint(item['x'], item['y']))
            bos_key_list.append(item['bos_key'])
            if item['have_line'] >= 1:
                have_line_points.append(TrackPoint(item['x'], item['y']))
                cdid = item['cdid']
            track_list.append(TrackItem(
                item['id'],
                item['bos_key'],
                item['cdid'],
                item['have_line'],
                TrackPoint(item['x'], item['y'])
            ))
        if len(total_points) > 1:
            line_obj = LineString(total_points)
            c_geom_wkt = line_obj.wkt
        elif len(total_points) == 1:
            line_obj = Point(total_points[0].x, total_points[0].y)
            c_geom_wkt = line_obj.wkt
        else:
            c_geom_wkt = ""
        # link_id  设备， 存在车位线的轨迹线， 存在车位线的点
        point_wkt = ""
        if len(have_line_points) > 1:
            point_obj = MultiPoint(have_line_points)
            point_wkt = point_obj.wkt
        elif len(have_line_points) == 1:
            point_obj = Point(have_line_points[0].x, have_line_points[0].y)
            point_wkt = point_obj.wkt
        car_list.append(CarDaysItem(
            cdid=cdid,
            link_id=link_item['link_id'],
            track_id=key,
            geom_wkt=c_geom_wkt,
            have_line_points=have_line_points,
            point_wkt=point_wkt,
            bos_key_list=bos_key_list,
            total_points=total_points,
            track_list=track_list
        ))
        # print_res.append(
        #     [link_item['link_id'], key, c_geom_wkt, have_line_points, point_wkt, bos_key_list, total_points, cdid])
        if len(have_line_points) > 0:
            hav_point_num += 1
        print("final_res", link_item['link_id'], key, cdid, c_geom_wkt, have_line_points)
    print("final_res_total", link_item['link_id'], len(car_list), hav_point_num)
    if hav_point_num >= 1:
        print("final_have_luce", link_item["link_id"])
    else:
        print("final_have_no_luce", link_item["link_id"])
    if len(res_map) == 0:
        confidence = 0
    else:
        confidence = round(hav_point_num / len(res_map), 4) * 100
    return hav_point_num >= 1, car_list, link_item, confidence


def get_parking_line(link_id, geom_str, link_dir=(), chains_list=()):
    """
    获取车位线识别
    """
    if chains_list == ():
        chains_list.add(link_id)
    geom_obj = wkt.loads(geom_str)
    print('fetch_tracks', link_id)
    tracks = get_tracks_all(geom_obj.buffer(0.0003))
    print('fetch_tracks_res', tracks)
    # 按时间排序 bos_key的最后以为是毫秒级的时间戳, 按轨迹行进顺序排序
    sorted_tracks = sorted(tracks, key=lambda x: int(x['bos_key'].split('_')[-1]))
    track_map = {}
    # 先通过车天号聚合，过滤一些轨迹点过少的
    for track in sorted_tracks:
        if track['cdid'] not in track_map:
            track_map[track['cdid']] = []
        track_map[track['cdid']].append(track)
    tobe_infer_id = set()
    tobe_infer_cdid = {}
    tobe_paddle_list = []
    match_link_tracks = []
    for cdid, tracks in track_map.items():
        if len(tracks) < 2:  # 一个车天只有一个轨迹的过滤
            continue
        if len(link_dir) > 0:  # 过滤轨迹方向与道路方向不一致的过滤
            dot_product = line_dir(link_dir,  # 上面已经按时间排序了，第一个点到最后一个点就是行进方向
                                   ((tracks[0]['x'], tracks[0]['y']), (tracks[-1]['x'], tracks[-1]['y'])))
            if dot_product is not None and dot_product < 0.1:
                continue
        for track in tracks:
            if track['link_id'] not in chains_list:
                continue
            track['have_line'] = 0
            if 'vof' not in track['cdid']:
                tobe_paddle_list.append((track['cdid'], track['id']))
            else:
                tobe_infer_id.add(track['bos_key'])
                if cdid not in tobe_infer_cdid:
                    tobe_infer_cdid[cdid] = 0
                tobe_infer_cdid[cdid] += 1
            match_link_tracks.append(track)
    rst = []
    print('fetch_infer', link_id, len(tobe_paddle_list))
    # 获取已经识别的
    with concurrent.futures.ThreadPoolExecutor(max_workers=30) as executor:
        future_to_num = {executor.submit(multi_get_infer_by_pic_helper, item[0], item[1]): item for item in
                         tobe_paddle_list}

        # 获取并整合结果
        for future in concurrent.futures.as_completed(future_to_num):
            result = future.result()
            rst.append(result)

    # 汇总所有ra识别结果
    have_link_id_map = {}
    for data in rst:
        if data[2] > 0:
            have_link_id_map[data[1]] = data[2]

    need_add_infer_num = max(0, 3 - len(have_link_id_map))
    infer_cdid_num = {}
    res_map = {}
    # 在按时间倒序，优先取近的车天
    sorted_tracks = sorted(match_link_tracks, key=lambda x: int(x['bos_key'].split('_')[-1]), reverse=True)
    for track in sorted_tracks:
        track['have_line'] = 0
        if track['id'] in have_link_id_map:
            track['have_line'] = 1
        if (track['bos_key'] in tobe_infer_id
                and need_add_infer_num > 0):  # 在识别列表里 且 识别点不够
            cdid = track['cdid']
            if cdid not in infer_cdid_num:
                if (len(infer_cdid_num) / len(tobe_infer_cdid) > 0.7
                        and len(infer_cdid_num) > 5):  # 最少识别5个车天，识别车天超过70%， 不识别新的车天了
                    continue
                else:
                    infer_cdid_num[cdid] = 0
            if infer_cdid_num[cdid] <= 5:  # 每个车天最多识别5张图片
                track['have_line'] = get_parking_line_by_key(track['bos_key'], track['cdid'], link_id)
                infer_cdid_num[cdid] += 1
            else:
                track['have_line'] = -1
        if track['have_line'] > 0:
            need_add_infer_num -= 1
            print("have_line", track['id'], track['have_line'], track['bos_key'])
        bos_keys = track['bos_key'].split('_')
        shebei_id = bos_keys[1]
        if shebei_id not in res_map:
            res_map[shebei_id] = []
        res_map[shebei_id].append(track)

    print("res_map", json.dumps(res_map, ensure_ascii=False))
    return res_map


def get_parking_line_by_key(key, cdid, link_id):
    """
    获取车位线识别
    """
    print("to_be_infer", key, link_id)
    res = fetch_parking_line_infer_res(key)
    if res is not None and "have_line" in res and res["have_line"] >= 0:
        return res["have_line"]

    url_img = 'http://m.map.baidu.com:8011/' + key
    infer_res = request_parking_line(url_img)
    print("url_img", url_img, infer_res)
    if infer_res is not None:
        have_line = 0
        if "parking_num" in infer_res:
            have_line = infer_res['parking_num']
        add_parking_line_infer_res(key, cdid, link_id, "2024-11-05", have_line, json.dumps(infer_res))
        return have_line
    else:
        add_parking_line_infer_res(key, cdid, link_id, "2024-11-05", -1, '')
    # else:
    # track['have_line'] = 0
    return 0


def line_dir(p1, p2):
    """
    计算方向
    """
    x1, y1, x2, y2 = p1[0][0], p1[0][1], p1[1][0], p1[1][1]
    x3, y3, x4, y4 = p2[0][0], p2[0][1], p2[1][0], p2[1][1]
    # 计算每条线的方向向量
    vector1 = (x2 - x1, y2 - y1)
    vector2 = (x4 - x3, y4 - y3)
    if vector1 == 0 and vector2 == 0:
        return None
        # res_map2[key] = res_item
        # continue
    # 计算向量的模长
    magnitude1 = math.sqrt(vector1[0] ** 2 + vector1[1] ** 2)
    magnitude2 = math.sqrt(vector2[0] ** 2 + vector2[1] ** 2)
    # 计算归一化后的方向向量
    if magnitude1 == 0:
        unit_vector1 = (0, 0)
    else:
        unit_vector1 = (vector1[0] / magnitude1, vector1[1] / magnitude1)
    if magnitude2 == 0:
        unit_vector2 = (0, 0)
    else:
        unit_vector2 = (vector2[0] / magnitude2, vector2[1] / magnitude2)
    # 计算夹角的余弦值（dot product of unit vectors）
    dot_product = unit_vector1[0] * unit_vector2[0] + unit_vector1[1] * unit_vector2[1]
    return dot_product


def run_chains_by_link_id(link_info, push_type=1):
    """
    入口方法
    """
    print("start_link", link_info)
    if push_type == 1:
        chains_res = get_parking_road_chains(link_info)
        print("chains_res", chains_res)
        print("chains_wkt", chains_res['geom_wkt'])
        link_id_list = chains_res['link_id_list']
    else:
        link_id_list = link_info
    link_total = []
    if len(link_id_list) == 0:
        return []
    guiji_map_by_key = {}  # 设备聚合
    for c_link_id in link_id_list:
        have_parking_line, car_list, link_item, confidence = process_link(c_link_id, set(link_id_list))
        for pro_item in car_list:
            # link_id  设备， 存在车位线轨迹线， 存在车位线的点, 存在车位线的点multi point, bos_key_list, total_points
            if DEBUG:
                if pro_item.cdid not in guiji_map_by_key:
                    guiji_map_by_key[pro_item.cdid] = [pro_item]
                else:
                    guiji_map_by_key[pro_item.cdid].append(pro_item)
        link_res_item = LinkInferItem(
            link_id=c_link_id,
            have_parking_line=have_parking_line,
            confidence=confidence,
            link_info=link_item,
            car_list=car_list
        )
        print("link_res_item",
              json.dumps(link_res_item,
                         default=lambda obj: obj.isoformat() if isinstance(obj, datetime) else obj.__dict__))
        link_total.append(link_res_item)
    print("link_total_list",
          json.dumps(link_total, default=lambda obj: obj.isoformat() if isinstance(obj, datetime) else obj.__dict__))
    # for debug
    if DEBUG:
        debug_res = []
        # 通过 key 聚合line
        for k, v in guiji_map_by_key.items():
            bos_key_list = []
            point_list_for_line = []
            poit_list_for_shibie = []
            for item in v:
                bos_key_list.extend(item.bos_key_list)
                if len(item.total_points) > 0:
                    point_list_for_line.extend(item.total_points)
                if len(item.have_line_points) > 0:
                    poit_list_for_shibie.extend(item.have_line_points)
            item_res = {"key": k, "line_wkt": "", "point_wkt": "", "bos_key_list": bos_key_list}
            if len(point_list_for_line) > 0:
                line_obj = LineString(point_list_for_line)
                item_res["line_wkt"] = line_obj.wkt
            if len(poit_list_for_shibie) > 1:
                point_obj = MultiPoint(poit_list_for_shibie)
                item_res["point_wkt"] = point_obj.wkt
            elif len(poit_list_for_shibie) == 1:
                point_obj = Point(poit_list_for_shibie[0])
                item_res["point_wkt"] = point_obj.wkt
            debug_res.append(item_res)
        print("debug_res", json.dumps(debug_res))
    # end debug
    link_parking_line = combine_line(link_total)
    uid = str(uuid.uuid4()).replace('-', '')
    sql_values = []
    for line_item in link_parking_line:
        compute_item = compute_line(line_item)
        for compute_row in compute_item:
            line_res = wkt.loads(compute_row['line_wkt'])
            if not line_res.is_simple:  # 自相交为异常直线
                print("filter_not_simple_line", compute_row['line_wkt'])
                continue
            short_link_id_str = ','.join(get_multi_short_id(compute_row['link_id']).values())
            items = {
                "uid": uid,
                "short_link_id_str": short_link_id_str,
                "line_wkt": "SRID=4326;{}".format(compute_row['line_wkt']),
                "area_wkt": "SRID=4326;{}".format(compute_row['buffer_polygon'])
            }
            print("success_res", ",".join(compute_row['link_id']), json.dumps(items), sep="\t")
            sql_values.append(items)
    return sql_values


def adjust_line_list(line_list):
    """
    把line_list的每个线段都按索引顺序排序
    :param line_list:
    :return:
    """
    new_line_list = []
    if len(line_list) <= 1:  # 只有一条线
        return line_list
    start_line = line_list[0]
    start_line_obj = wkt.loads(start_line.link_info['geom_wkt'])
    second_line_obj = wkt.loads(line_list[1].link_info['geom_wkt'])
    if (start_line_obj.coords[0] == second_line_obj.coords[0] or
            start_line_obj.coords[0] == second_line_obj.coords[-1]):  # 当前起点和下一个起点或终点相同 则需要交换
        line = wkt.loads(start_line.link_info['geom_wkt'])
        reversed_line = LineString(line.coords[::-1])
        start_line.link_info['geom_wkt'] = reversed_line.wkt
        new_line_list.append(start_line)
    else:
        new_line_list.append(start_line)

    for n, line_item in enumerate(line_list):
        if n == 0:
            continue
        link_info = line_item.link_info
        link_obj = wkt.loads(link_info['geom_wkt'])
        pre_link_info = line_list[n - 1].link_info
        pre_link_obj = wkt.loads(pre_link_info['geom_wkt'])
        if (link_obj.coords[-1] == pre_link_obj.coords[0] or
                link_obj.coords[-1] == pre_link_obj.coords[-1]):  # 当前终点和上一个起点或终点相同 则需要交换
            line = wkt.loads(link_info['geom_wkt'])
            reversed_line = LineString(line.coords[::-1])
            line_item.link_info['geom_wkt'] = reversed_line.wkt
            new_line_list.append(line_item)
        else:
            new_line_list.append(line_item)
    return new_line_list


def check_line_close(current_line, left_line, right_line):
    """
    判断线段向左拼接还是向右
    :param current_line:
    :param left_line:
    :param right_line:
    :return:  1 向左， 2 向右
    """
    # 两边都为空， 加到左边
    if left_line is None and right_line is None:
        return 1
    if left_line is not None:
        if current_line.intersects(left_line):
            return 1
        else:
            return 2
    if right_line is not None:
        if current_line.intersects(right_line):
            return 2
        else:
            return 1
    return 1


def gen_polygon(line_obj):
    """
    生成多边形
    """
    polygon_res_obj = line_obj.buffer(0.00004, cap_style=2, join_style=1)
    return polygon_res_obj.wkt


def calc_projection(point_obj, line_obj):
    """
    获取投影点
    """
    proj_distance = line_obj.project(point_obj)
    proj_point = line_obj.interpolate(proj_distance)
    return proj_point.x, proj_point.y


def compute_line(line_list):
    """
    计算起始点
    :param line_list:list(LinkInferItem)
    """
    line_list = adjust_line_list(line_list)
    # line_res = {"res": "", "source": []}
    start_point = []
    end_point = []
    start_link_obj = wkt.loads(line_list[0].link_info['geom_wkt'])
    end_link_obj = wkt.loads(line_list[-1].link_info['geom_wkt'])
    point_for_dir = (start_link_obj.coords[0], end_link_obj.coords[-1])
    # 处理首尾点 0 是起始线，-1是结尾线
    interval = 0.00014  # 14m
    for k in [-1, 0]:
        projected_points = []
        line_item = line_list[k]
        link_info = line_item.link_info
        line_obj = wkt.loads(link_info['geom_wkt'])
        # link_id = link_info['link_id']
        to_be_infer_list = []
        for item in line_item.car_list:
            print("debug_item", item)
            # 有识别结果的点
            have_line_points = item.have_line_points
            for track in item.track_list:
                if 'vof' in track.cdid and track.have_line == -1:
                    point_obj = Point(track.point)
                    # 计算投影
                    x, y = calc_projection(point_obj, line_obj)
                    to_be_infer_list.append({'track': track, 'proj_point': (x, y), 'link_id': item.link_id})
            for tmp in have_line_points:
                point_obj = Point(tmp)
                # 计算投影
                x, y = calc_projection(point_obj, line_obj)
                projected_points.append((x, y))
        # 判断下识别点够不够
        start = 0
        while start < line_obj.length:
            # 每15m需要2个点
            need_matched_num = 2
            end = min(start + interval, line_obj.length)
            segment = substring(line_obj, start, end)
            segment_polygon = segment.buffer(0.0003, cap_style=2, join_style=1)
            for point in projected_points:
                point_obj = Point(point)
                if segment_polygon.contains(point_obj):
                    need_matched_num -= 1
            # 识别点不够， 需要补充
            for to_be_infer_item in to_be_infer_list:
                if need_matched_num <= 0:
                    break
                track = to_be_infer_item['track']
                point_obj = Point(to_be_infer_item['proj_point'])
                if segment_polygon.contains(point_obj):
                    have_line = get_parking_line_by_key(track.bos_key, track.cdid, to_be_infer_item['link_id'])
                    if have_line > 0:
                        need_matched_num -= 1
                        projected_points.append(to_be_infer_item['proj_point'])
            start += interval

        if len(projected_points) == 0:
            print("filter_empty_point", k, link_info['link_id'])
            projected_points.append(line_obj.centroid)
        if k == 0:
            if len(projected_points) == 1:
                start_point = projected_points[0]
            else:
                start_point, _ = compute_point(projected_points, point_for_dir[-1])
        else:
            if len(projected_points) == 1:
                end_point = projected_points[0]
            else:
                end_point, _ = compute_point(projected_points, point_for_dir[0])

    origin_link_total_point = []
    for line_item in line_list:
        link_info = line_item.link_info
        line_obj = wkt.loads(link_info['geom_wkt'])
        origin_link_total_point.extend(line_obj.coords)
    line_origin = LineString(origin_link_total_point)
    # line_res['res_origin'] = line_origin.wkt
    print("line_origin", line_origin.wkt)
    road_area = fetch_nav_line_marking(line_origin.wkt)
    # 判断是否需要切
    remaining_lines_group = []
    if len(road_area) > 0 and road_area['geom_wkt'] is not None:
        lines_group = []
        print("area_geom", road_area['geom_wkt'])
        road_area_obj = wkt.loads(road_area['geom_wkt'])
        for n, line_item in enumerate(line_list):
            link_info = line_item.link_info
            line_obj = wkt.loads(link_info['geom_wkt'])
            if road_area_obj.contains(line_obj):  # 如果线完全在 Polygon 内，跳过（移除）
                continue
                # 如果线和 Polygon 相交，分割线
            if road_area_obj.intersects(line_obj):
                split_lines = line_obj.difference(road_area_obj)
                if split_lines.length * 90000 < 5:  # 相交后小于5米，跳过
                    continue
                # 切成了多个线段， 左边加一端，右边加一端
                if isinstance(split_lines, MultiLineString):
                    for k, part in enumerate(split_lines.geoms):
                        if part.length * 90000 < 5:
                            continue
                        left = None
                        right = None
                        if len(lines_group) > 0:  # 线段左边有数据
                            left = wkt.loads(lines_group[-1].link_info['geom_wkt'])
                        if n < len(line_list) - 1:  # 线段右边有数据
                            right = wkt.loads(line_list[n + 1].link_info['geom_wkt'])
                        side = check_line_close(part, left, right)
                        line_item.link_info['geom_wkt'] = part.wkt
                        if side == 1:  # 向左拼接打断
                            lines_group.append(line_item)
                            remaining_lines_group.append(lines_group)
                            lines_group = []
                        else:  # 打断向右拼
                            if len(lines_group) > 0:
                                remaining_lines_group.append(lines_group)
                            lines_group = [line_item]
                else:  # 如果只有一部分（还是一个 LineString）
                    left = None
                    right = None
                    if len(lines_group) > 0:  # 线段左边有数据
                        left = wkt.loads(lines_group[-1].link_info['geom_wkt'])
                    if n < len(line_list) - 1:  # 线段右边有数据
                        right = wkt.loads(line_list[n + 1].link_info['geom_wkt'])
                    side = check_line_close(split_lines, left, right)
                    line_item.link_info['geom_wkt'] = split_lines.wkt
                    if side == 1:  # 向左拼接打断
                        lines_group.append(line_item)
                        remaining_lines_group.append(lines_group)
                        lines_group = []
                    else:  # 打断向右拼
                        if len(lines_group) > 0:
                            remaining_lines_group.append(lines_group)
                        lines_group = [line_item]
            else:
                # 如果线不在 Polygon 内且不相交，保留整个线
                lines_group.append(line_item)
        if len(lines_group) > 0:
            remaining_lines_group.append(lines_group)
    else:
        remaining_lines_group = [line_list]

    res = []
    start_point_obj = Point(start_point)
    end_point_obj = Point(end_point)
    for n, line_group in enumerate(remaining_lines_group):
        link_ids = []
        point_list = []
        for line_item in line_group:
            link_info = line_item.link_info
            link_ids.append(link_info['link_id'])
            link_obj = wkt.loads(link_info['geom_wkt'])
            point_list.extend(link_obj.coords)
        line_res_obj = LineString(point_list)
        if n == 0 or n == len(remaining_lines_group) - 1:
            if n == 0:
                point_for_proj = start_point_obj
            else:
                point_for_proj = end_point_obj
            projection_distance = line_res_obj.project(point_for_proj)
            if projection_distance == 0 or projection_distance == line_res_obj.length:
                print("same_point_continue", n, point_for_proj, line_res_obj.wkt)
            else:
                # 获取投影点
                projected_point = line_res_obj.interpolate(projection_distance)
                print("Projected Point:", n, projected_point)
                # 获取从投影点到 LineString 末尾的新线段
                # coords = list(line_res_obj.coords)
                print("line_parking_wkt", n, line_res_obj.wkt)
                if n == 0:
                    # 从投影点到终点
                    new_coords = [coord for coord in line_res_obj.coords if
                                  line_res_obj.project(
                                      Point(coord)) > projection_distance]
                    if len(new_coords) > 0:
                        line_res_obj = LineString([projected_point] + new_coords)
                else:
                    # 从起始点到投影点
                    new_coords = [coord for coord in line_res_obj.coords if
                                  line_res_obj.project(Point(coord)) <= projection_distance]
                    # new_line = LineString(
                    #     [coord for coord in line_res_obj.coords if
                    #      line_res_obj.project(Point(coord)) <= projection_distance])
                    if len(new_coords) > 0:
                        line_res_obj = LineString(new_coords + [projected_point])
                print("new_line_parking_wkt", n, line_res_obj.wkt)
        if not line_res_obj.is_simple:  # 尝试修复重叠，不修复自相交
            fixed_line = linemerge(unary_union(line_res_obj))
            if isinstance(fixed_line, LineString):  # 如果不是merge后是一条直线则更新
                line_res_obj = fixed_line
        if line_res_obj.length < 0.00012:
            print("filter_zero_length", ",".join(link_ids))
            continue
        res.append({"link_id": link_ids, "line_wkt": line_res_obj.wkt, "buffer_polygon": gen_polygon(line_res_obj)})

    return res


# 计算合适的起点终点
def compute_point(point_list, point_item):
    """
    :param point_list:  候选点列表
    :param point_item:  参考点
    :return: 点， 聚类集合
    """
    if len(point_list) == 1:
        return point_list[0], point_list[0]
    elif len(point_list) == 0:
        return None, None
    start_proj_points_np = np.array(point_list)
    clustering = DBSCAN(eps=0.0002, min_samples=2).fit(start_proj_points_np)
    labels = clustering.labels_
    filtered_data = start_proj_points_np[labels != -1]
    pick_idx = 1
    if len(filtered_data) == 0:
        filtered_data = start_proj_points_np
        pick_idx = 2
    if len(filtered_data) == 1:
        return filtered_data[0], filtered_data[0]
    else:
        distances = np.linalg.norm(filtered_data - point_item, axis=1)  # 找到距离最大和次大的两个点的索引
        sorted_indices = np.argsort(distances)[::-1]  # 按距离从大到小排序
        if len(sorted_indices) > 1:
            if len(sorted_indices) > pick_idx:
                second_farthest_index = sorted_indices[pick_idx]
            else:
                second_farthest_index = sorted_indices[1]  # 第二远的点索引
        else:
            second_farthest_index = sorted_indices[0]  # 仅一个坐标
        return filtered_data[second_farthest_index].tolist(), filtered_data


def combine_line(link_total):
    """
    聚合单link
    :param link_total: list(LinkInferItem)
    :return list((LinkInferItem))
    """
    link_parking_line = []
    to_be_add = []
    for i in range(len(link_total)):
        if link_total[i].have_parking_line:  # 当前有车位线
            if (len(to_be_add) > 0 or  # 如果前一个或者后一个有车位线， 则拼接
                    (i > 0 and link_total[i - 1].have_parking_line) or
                    (i + 1 < len(link_total) and link_total[i + 1].have_parking_line)):
                to_be_add.append(link_total[i])
                continue
            line_obj = wkt.loads(link_total[i].link_info['geom_wkt'])
            if line_obj.length * 90000 > 50:  # and link_total[i]['confidence'] > 0.7 : # 单独link 置信度大于70%
                to_be_add.append(link_total[i])
        else:  # 当前没有车位线
            line_obj = wkt.loads(link_total[i].link_info['geom_wkt'])
            if (line_obj.length * 90000 < 25 and  # 如果长度小于25米 且前一个后一个都有车位线 , 则拼接
                    (i > 0 and link_total[i - 1].have_parking_line) and
                    (i + 1 < len(link_total) and link_total[i + 1].have_parking_line)):
                to_be_add.append(link_total[i])
            else:
                if len(to_be_add) > 0:
                    link_parking_line.append(to_be_add)
                    to_be_add = []
    if len(to_be_add) > 0:
        link_parking_line.append(to_be_add)
    for n, line_item in enumerate(link_parking_line):  # 循环每一段
        for i in [-1, 0]:  # 循环起点和终点
            item = line_item[i]
            line_obj = wkt.loads(item.link_info['geom_wkt'])
            if line_obj.length * 90000 < 25:  # 小于25米 若识别点少于2，则舍弃
                have_infer_num = 0
                for car_item in item.car_list:
                    have_infer_num += len(car_item.have_line_points)
                if have_infer_num < 3:
                    print("infer_point_to_less", item.link_info['link_id'])
                    del link_parking_line[n][i]
                    break
    # 本来要过滤不置信的，现在不用了
    print("combine_res_line_parking_line",
          json.dumps(link_parking_line,
                     default=lambda obj: obj.isoformat() if isinstance(obj, datetime) else obj.__dict__))
    return link_parking_line


if __name__ == "__main__":
    Debug = True
    # _link_parking_line = combine_line(a)
    # for _line_item in _link_parking_line:
    #     compute_line(_line_item)
    all_link = [
        "8201412513684926974"
    ]
    if len(sys.argv) > 1:
        all_link = sys.argv[1].split(",")
    for _link_id in all_link:
        try:
            run_chains_by_link_id(_link_id)
            print("complete", _link_id)
        except Exception as e:
            print("error_occupy", e, _link_id)
            stack_info = traceback.format_exc()
            print("Stack trace:")
            print(stack_info)
