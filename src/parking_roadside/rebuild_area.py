# -*- coding: utf-8 -*-
"""
掘金成果重新绑路
"""
import json
import sys

from db.pg import fetch_rebuild_work, fetch_work_res_by_link, query_work_by_turing, query_work_by_uid, \
    query_bid_by_uid, query_work_by_pid, fetch_parking_line_poi, update_turing_work_res_changed, \
    update_turing_work_res_no_change, update_work_status_by_id
from db.road import fetch_link_by_id, fetch_multi_links_by_id, get_short_link_id, get_long_link_id
from urllib.parse import unquote
from process import gen_polygon
from shapely.geometry import LineString, Point
from shapely import wkt, MultiLineString
from shapely.ops import nearest_points, substring
from api.coord import trans_bd_2_gc, trans_gc_2_bdmc
from api.send_poi import update_poi_relation, get_info_by_bid

DEBUG = False


def generate_new_line(link_list, point_list):
    """
    生成新的停车场线段
    """
    if len(point_list) < 2:
        return None, None
    multi_link = fetch_multi_links_by_id(link_list)
    if multi_link is None or 'geom_wkt' not in multi_link or multi_link['geom_wkt'] is None:
        return None, None
    line_obj = wkt.loads(multi_link['geom_wkt'])
    if isinstance(line_obj, MultiLineString):
        return None, None

    point_bd1 = Point(point_list[0])
    point1 = wkt.loads(trans_bd_2_gc(point_bd1.wkt))
    point_bd2 = Point(point_list[1])
    point2 = wkt.loads(trans_bd_2_gc(point_bd2.wkt))

    # 确定投影点在线段上的位置
    position1 = line_obj.project(point1)
    position2 = line_obj.project(point2)

    # 确保位置1在位置2之前
    start_position = min(position1, position2)
    end_position = max(position1, position2)

    sub_line = substring(line_obj, start_position, end_position)

    # 截取原始线段中间的部分
    return sub_line.wkt, gen_polygon(sub_line)


def fetch_old_item(turing_id, bp_message):
    """
    旧的图灵数据获取接口
    """
    for item in bp_message:
        data = json.loads(unquote(item['data']))
        if 'iid' in data and turing_id == data['iid']:
            return data


def run_turing_res_row(row):
    """
    图灵成果重新绑路
    """
    turing_id = row['turing_id']
    resp = json.loads(row['bp_message'])
    #    print("resp", resp)
    if 'messages' in resp:
        data = fetch_old_item(turing_id, resp['messages'])
    else:
        data = json.loads(unquote(resp['data']))
    link_list = []
    point_list = []
    if turing_id != data['iid']:
        if not DEBUG:
            update_work_status_by_id(row['id'], 6, 'turing id not match')
        print("turing id not match", turing_id, data['iid'])
        return
    edit_commit = data['edit_commit']
    if 'geo' not in edit_commit:
        if not DEBUG:
            update_work_status_by_id(row['id'], 6, 'turing edit_commit not found')
        print("edit_commit not found", data['edit_commit'])
        return
    geo = edit_commit['geo']
    for ele in geo:
        if ele.get('geometry') and ele.get('geometry').get('type') == 'LineString':
            link_list.append(ele.get('properties').get('link_id'))
        elif ele.get('geometry') and ele.get('geometry').get('type') == 'Point':
            point_list.append(ele.get('geometry').get('coordinates'))

    short_link_ids = get_short_link_id(link_list).values()
    are_equal = sorted(short_link_ids) == sorted(row['link_id_list'].split(","))
    if not set(short_link_ids) & set(row['link_id_list'].split(",")):
        print("not exists", ",".join(short_link_ids), ",".join(row['link_id_list'].split(",")))
        # link全部变更，轨迹漂移造成的误判，人工作业不会做起终点，不能直接改成成果, 后续得依据人工作业的成果重新挖掘
        if not DEBUG:
            update_work_status_by_id(row['id'], 6, 'link_total_change,' + ",".join(short_link_ids))
        return
    if not are_equal:
        exits_link = False
        for link_id in short_link_ids:
            work_res = fetch_work_res_by_link(link_id)
            if work_res is not None and len(work_res) > 0:
                for work in work_res:
                    if work['turing_id'] == turing_id:
                        continue
                    print("exists", "link_id", link_id, "turing", turing_id)
                    exits_link = True
                    break
            if exits_link:
                break
        if exits_link:
            # 人工绑路绑到了其他成果上
            if not DEBUG:
                print("new_link_exists", turing_id)
                update_work_status_by_id(row['id'], 6, 'duplicate_rebind')
            return
        new_line, new_polygon = generate_new_line(link_list, point_list)
        if new_polygon is None:
            print("work err", turing_id)
            if not DEBUG:
                update_work_status_by_id(row['id'], 6, 'work_error, gen new line failed')
            return
        # work_id, short_link_id_str, new_line, new_polygon, status, message
        if not DEBUG:
            print("new_line", new_line)
            update_turing_work_res_changed(row['id'], ",".join(short_link_ids), new_line, new_polygon, 2, '')
        return
    if not DEBUG:
        print("no change", turing_id)
        update_turing_work_res_no_change(row['id'], 2, '')
    return


def fetch_online(uid):
    """
    查询线上成果
    """
    map_info = query_bid_by_uid(uid)
    bid = ""
    if map_info is not None:
        bid = map_info["bid"]
    if bid == "":
        print("no bid found", uid)
        return
    poi_info = get_info_by_bid(bid)
    if (poi_info is None or not poi_info.get('data') or not poi_info.get('data').get('base') or
            not poi_info.get('data').get('travel_info')):
        print("no poi info found", bid, poi_info)
        return
    data = poi_info.get('data')
    if not data.get('travel_info').get('parking_info') or not data.get('travel_info').get('parking_info').get('area'):
        print("no area info found", bid, poi_info)
        return
    if not data.get('travel_info').get('parking_info') or not data.get('travel_info').get('parking_info').get(
            'park_spec'):
        print("no park_spec found", bid, poi_info)
        return
    if data.get('travel_info').get('parking_info').get('park_spec') != 1:
        print("park_spec not 1", bid, poi_info)
        return
    parking = data.get('travel_info').get('parking_info')
    parking_area = parking.get('area')
    base = data.get('base')
    ext = json.loads(base['extension'])
    need_rebuild = False
    if not ext.get('road_relation'):
        print("no road relation found", bid, ext)
        rebuild_link_by_uid(uid)
        return
    road_relation = json.loads(ext.get('road_relation'))
    area_obj = wkt.loads(parking_area)
    for item in road_relation['link_info']:
        point_item = item.get('point').split(",")
        point = Point(point_item[0], point_item[1])
        if not area_obj.contains(point):
            print("point not within area", point.wkt, area_obj.wkt)
            need_rebuild = True
            # run_link_uid(uid)
    if need_rebuild:
        rebuild_by_online(bid, road_relation, area_obj)


def rebuild_by_online(bid, road_relation, parking_area):
    """
    通过
    """
    #    parking_area = wkt.loads(parking_area_wkt)
    link_info_list = []
    for link in road_relation['link_info']:
        link_id = link.get('link_id')
        long_link_id = get_long_link_id(link_id)
        link_info = fetch_link_by_id(long_link_id)
        if link_info is None:
            print("link_id none", long_link_id)
            continue
        link_obj = wkt.loads(trans_gc_2_bdmc(link_info['geom_wkt']))
        intercept_link = link_obj.intersection(parking_area)
        if intercept_link.is_empty:
            continue
        center_point = intercept_link.interpolate(0.5, normalized=True)
        link_info_list.append({
            "link_id": link_id,
            "orientation": link.get('orientation'),
            "point": "{},{}".format(center_point.x, center_point.y),
            "type": 5
        })
    new_road_relation = {"link_info": link_info_list}
    update_poi_relation(bid, new_road_relation)
    print("need_rebuild", 'online', parking_area.wkt, json.dumps(road_relation), json.dumps(new_road_relation),
          sep="\t")


def rebuild_link_by_uid(uid):
    """
    """
    work_res = query_work_by_uid(uid)
    if work_res is None or len(work_res) == 0:
        return
    need_rebuild = False
    link_info_list = []
    for row in work_res:
        if row['turing_result'] != 1 or row['status'] == 6:
            continue
        combine_info = row['combine_info']
        combine_info_list = json.loads(combine_info)
        link_map = []
        for item in combine_info_list['link_info']:
            link_id = item['link_id']
            if link_id in link_map:
                continue
            short_link_ids = get_short_link_id([link_id])
            if link_id in short_link_ids:
                short_link_id = short_link_ids[link_id]
            else:
                print("short_link_id is None", link_id)
                continue
            # linkid点列需要和turing_area_geom的交集, 然后取交集的中心点
            link_info = fetch_link_by_id(link_id)
            if link_info is None:
                print("link_id none", link_id)
                continue
            link_geom = link_info['geom_wkt']
            parking_area = wkt.loads(row['turing_area_geom_wkt'])
            link_obj = wkt.loads(link_geom)
            intercept_link = link_obj.intersection(parking_area)
            if intercept_link.is_empty:
                continue
            center_point = intercept_link.interpolate(0.5, normalized=True)
            bd_mc_wkt = trans_gc_2_bdmc(center_point.wkt)
            point_obj = wkt.loads(bd_mc_wkt)
            link_info_list.append({
                "link_id": short_link_id,
                "point": "{},{}".format(point_obj.x, point_obj.y),
                "orientation": item['orientation'],
                "type": 5
            })
            link_map.append(link_id)

    if need_rebuild and len(link_info_list) > 0:
        road_relation = {"link_info": link_info_list}
        map_info = query_bid_by_uid(uid)
        if map_info is not None:
            bid = map_info["bid"]
            if map_info["status"] == 1:
                update_poi_relation(bid, road_relation)
                print("need_rebuild", 'db', bid, json.dumps(road_relation), sep="\t")


def run():
    """
    运行所有需要重建的数据
    """
    res = fetch_rebuild_work()
    for row in res:
        run_turing_res_row(row)


def run_by_poi():
    """
    通过线上poi执行
    """
    res = fetch_parking_line_poi()
    for row in res:
        fetch_online(row['uid'])


def run_by_turing_list(file_name):
    """
    通过uid文件执行
    """
    with open(file_name, 'r') as f:
        lines = f.readlines()
        for line in lines:
            uid = line.strip()
            # row = query_work_by_uid(turing_id)
            # if row is None:
            #     continue
            # row = query_work_by_pid(uid)
            # run_row(row)
            fetch_online(uid)


if __name__ == '__main__':
    run()
    # run_by_turing_list(sys.argv[1])
    # generate_new_line([], [[116.305844, 39.980777], [116.305865, 39.979319]])
