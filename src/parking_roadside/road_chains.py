# -*- coding: utf-8 -*-
"""
生成路链
"""

import math
from shapely import wkt
from db.pg import add_road_chains, fetch_road_chains, \
    fetch_aoi_by_line
from db.road import fetch_link_by_id, fetch_node_by, fetch_link_by_node, fetch_road


def get_parking_road_chains_cache(link_id, batch_id=""):
    """
    通过缓存
    :param link_id:
    :param batch_id:
    :return: dict {'link_id_list': [], 'node_id_list': [], 'geom_wkt': ''}
    """
    if batch_id != "":
        res = fetch_road_chains(link_id, batch_id)
        if res is not None and len(res) > 0:
            line_obj = wkt.loads(res['geom_wkt'])
            print("get from cache", link_id, res['link_ids_str'])
            return {
                "link_id_list": res['link_ids_str'].split(","),
                "node_id_list": [],
                "geom_wkt": line_obj
            }
    chains = get_parking_road_chains(link_id)
    if len(chains['link_id_list']) > 0 and batch_id != "":
        add_road_chains(batch_id, chains['link_id_list'])
    return chains


def is_include_aoi(link_geom_str):
    """
    是否包含aoi
    """
    aoi_list = fetch_aoi_by_line(link_geom_str)
    include_aoi = False
    if aoi_list is not None:
        for aoi in aoi_list:
            road_list = fetch_road(aoi['geom_wkt'])
            have_high_road = False
            for road_item in road_list:
                if road_item['kind'] < 8:
                    have_high_road = True
                    break
            if not have_high_road:
                include_aoi = True
    return include_aoi


def get_parking_road_chains(link_id):
    """
    获取停车路链
    :param link_id:
    :return: dict {'link_id_list': [], 'node_id_list': [], 'geom_wkt': ''}
    """

    def process_node_chain(link_obj, start_node_id):
        """
        处理节点链
        """
        road_chains = []
        node_chains = [start_node_id]
        current_node = fetch_node_by(start_node_id)
        res_geom = wkt.loads(link_obj['geom_wkt'])

        max_deep = 10000
        max_length = 1000
        while True:
            max_deep -= 1
            if max_deep == 0:
                raise Exception("link_id:{} 长度过长".format(link_obj['link_id']))
            tmp_link_id = link_obj['link_id']
            print("next_link", tmp_link_id)

            if current_node.get("adjoin_nid"):
                relative_links = fetch_link_by_node(current_node['adjoin_nid'])
            else:
                relative_links = fetch_link_by_node(current_node['node_id'])

            is_one_line, is_cut = None, False
            # 统计交叉路口数量
            jiaochalu_num = sum(1 for rel_link in relative_links if '50' in rel_link['form'].split(","))

            for rel_link in relative_links:
                if tmp_link_id == rel_link['link_id']:
                    # 跳过当前link
                    continue
                if rel_link['link_id'] in road_chains:
                    # 如果已经跳过了当前link，仍然在路链中，说明存在环，报错
                    raise Exception("link_id:{} 存在环".format(link_obj['link_id']))

                tmp_angle = cal_rel_link_angle(rel_link['geom_wkt'], link_obj['geom_wkt'], current_node['geom_wkt'])
                if (2 <= int(rel_link['kind']) <= 9 and  # 2-9级路
                        all(form not in ['10', '11', '15', '16', '17', '20', '31', '35', '37', '38', '39', '52',
                                         '53']  # 不是特定的道路类型
                            for form in rel_link['form'].split(",")) and
                        rel_link['viad'] != 1 and  # 非高架
                        rel_link['md'] == link_obj['md'] and  # dc sc必须一直
                        (tmp_angle > 330 or tmp_angle < 30)):  # 角度在30度之内
                    is_one_line = rel_link
                elif (int(rel_link['kind']) <= 7 and  # 存在60度-120度的7级路 则需要打断
                      '50' not in rel_link['form'].split(",") and
                      (60 < tmp_angle < 120 or 240 < tmp_angle < 300)):
                    is_cut = True
            if is_one_line is None or is_cut or jiaochalu_num > 1:
                # 没有符合条件的路段|需要打断
                break

            road_chains.append(is_one_line['link_id'])
            res_geom = res_geom.union(wkt.loads(is_one_line['geom_wkt']))

            # 如果下一条link的e_node是当前node 则使用下一条link的s_node继续循环
            if (is_one_line['e_nid'] == current_node['node_id'] or
                    is_one_line['e_nid'] == current_node.get('adjoin_nid')):
                current_node = fetch_node_by(is_one_line['s_nid'])
                node_chains.append(is_one_line['s_nid'])
            else:
                current_node = fetch_node_by(is_one_line['e_nid'])
                node_chains.append(is_one_line['e_nid'])

            if res_geom.length * 90000 > max_length:
                break

            link_obj = is_one_line

        return road_chains, node_chains, res_geom

    ret_dict = {'link_id_list': [], 'node_id_list': [], 'geom_wkt': ''}
    sd_link_obj = fetch_link_by_id(link_id)
    if not sd_link_obj:
        print("filter_not_exist_link", link_id)
        return ret_dict
    if sd_link_obj['kind'] >= 8 and is_include_aoi(sd_link_obj['geom_wkt']):
        print("filter_include_aoi", link_id)
        return ret_dict

    # 计算s node的延伸
    s_link_chains, s_node_chains, s_geom_obj = process_node_chain(sd_link_obj, sd_link_obj['s_nid'])
    sd_link_obj = fetch_link_by_id(link_id)
    # 计算e node的延伸
    e_link_chains, e_node_chains, e_geom_obj = process_node_chain(sd_link_obj, sd_link_obj['e_nid'])

    return {
        "link_id_list": s_link_chains[::-1] + [link_id] + e_link_chains,
        "node_id_list": s_node_chains[::-1] + e_node_chains,
        "geom_wkt": s_geom_obj.union(e_geom_obj).wkt
    }


def angle(p_angle):
    """
    angle

    Args:
        p_angle (_type_): _description_

    Returns:
        _type_: _description_
    """
    while p_angle < 0:
        p_angle = p_angle + 360
    while p_angle >= 360:
        p_angle = p_angle - 360

    return p_angle


def cal_rel_link_angle(p_link1_str, p_link2_str, p_node_str):
    """
    cal_rel_link_angle

    Args:
        p_link1_str (_type_): _description_
        p_link2_str (_type_): _description_
        p_node_str (_type_): _description_

    Returns:
        _type_: _description_
    """
    #     print p_link1._link_id, p_link2._link_id
    p_link1 = wkt.loads(p_link1_str)
    p_link2 = wkt.loads(p_link2_str)
    p_node = wkt.loads(p_node_str)

    if (math.fabs(p_node.coords[0][0] - p_link1.coords[0][0]) < 0.00001 and
            math.fabs(p_node.coords[0][1] - p_link1.coords[0][1]) < 0.00001):
        x1 = p_link1.coords[1][0]
        y1 = p_link1.coords[1][1]
    else:
        pt_count = len(list(p_link1.coords))
        x1 = p_link1.coords[pt_count - 2][0]
        y1 = p_link1.coords[pt_count - 2][1]

    if (math.fabs(p_node.coords[0][0] - p_link2.coords[0][0]) < 0.00001 and
            math.fabs(p_node.coords[0][1] - p_link2.coords[0][1]) < 0.00001):
        x2 = p_link2.coords[1][0]
        y2 = p_link2.coords[1][1]
    else:
        pt_count = len(list(p_link2.coords))
        x2 = p_link2.coords[pt_count - 2][0]
        y2 = p_link2.coords[pt_count - 2][1]

    angle1 = get_seg_angle(
        x1, y1, p_node.coords[0][0], p_node.coords[0][1]) * 180.0 / math.pi
    angle2 = get_seg_angle(
        p_node.coords[0][0], p_node.coords[0][1], x2, y2) * 180.0 / math.pi
    d_angle = angle(angle2 - angle1)
    return d_angle


def get_seg_angle(x1, y1, x2, y2):
    """get_seg_angle


    Args:
        x1 (_type_): _description_
        y1 (_type_): _description_
        x2 (_type_): _description_
        y2 (_type_): _description_

    Returns:
        _type_: _description_
    """
    line_angle = 0
    if x1 == x2:
        if y2 > y1:
            line_angle = math.pi / 2
        else:
            line_angle = 3 * math.pi / 2
    elif x2 > x1:
        if y2 >= y1:
            line_angle = math.atan((y2 - y1) / (x2 - x1))
        if y2 < y1:
            line_angle = 2 * math.pi + math.atan((y2 - y1) / (x2 - x1))
    elif x2 < x1:
        if y2 >= y1:
            line_angle = math.pi + math.atan((y2 - y1) / (x2 - x1))
        if y2 < y1:
            line_angle = math.atan((y2 - y1) / (x2 - x1)) + math.pi
    # print line_angle * 180.0 / math.pi
    return line_angle


def get_storefront_park_road_chains(link_id):
    """
    获取门前停车路链
    https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/wBpTyxTfJn/cfdb2fa73dd745
    :param link_id:
    :return: dict {'link_id_list': [], 'node_id_list': [], 'geom_wkt': ''}
    """
    allow_kinds = [4, 5, 6, 7, 9]

    def process_node_chain(link_obj, start_node_id):
        """
        处理节点链
        """
        road_chains = []
        node_chains = [start_node_id]
        current_node = fetch_node_by(start_node_id)
        res_geom = wkt.loads(link_obj['geom_wkt'])

        max_deep = 10000
        max_length = 1000
        while True:
            max_deep -= 1
            if max_deep == 0:
                raise Exception("link_id:{} 长度过长".format(link_obj['link_id']))
            tmp_link_id = link_obj['link_id']
            print("next_link", tmp_link_id)

            if current_node.get("adjoin_nid"):
                relative_links = fetch_link_by_node(current_node['adjoin_nid'])
            else:
                relative_links = fetch_link_by_node(current_node['node_id'])

            is_one_line, is_cut = None, False
            # 统计交叉路口数量
            # jiaochalu_num = sum(1 for rel_link in relative_links if '50' in rel_link['form'].split(","))
            jiaochalu_num = 0

            for rel_link in relative_links:
                if tmp_link_id == rel_link['link_id']:
                    # 跳过当前link
                    continue
                if rel_link['link_id'] in road_chains:
                    # 如果已经跳过了当前link，仍然在路链中，说明存在环，报错
                    raise Exception("link_id:{} 存在环".format(link_obj['link_id']))

                tmp_angle = cal_rel_link_angle(rel_link['geom_wkt'], link_obj['geom_wkt'], current_node['geom_wkt'])
                if (int(rel_link['kind']) in allow_kinds and
                        all(form not in ['10', '11', '15', '16', '17', '20', '31', '35', '37', '38', '39', '52',
                                         '53', '82']  # 不是特定的道路类型
                            for form in rel_link['form'].split(",")) and
                        rel_link['viad'] != 1 and  # 非高架
                        rel_link['md'] == link_obj['md'] and  # dc sc必须一直
                        (tmp_angle > 330 or tmp_angle < 30)):  # 角度在30度之内
                    is_one_line = rel_link
                elif (int(rel_link['kind']) <= 7 and  # 存在60度-120度的7级路 则需要打断
                      '50' not in rel_link['form'].split(",") and
                      (60 < tmp_angle < 120 or 240 < tmp_angle < 300)):
                    is_cut = True
            if is_one_line is None or is_cut or jiaochalu_num > 1:
                # 没有符合条件的路段|需要打断
                break

            road_chains.append(is_one_line['link_id'])
            res_geom = res_geom.union(wkt.loads(is_one_line['geom_wkt']))

            # 如果下一条link的e_node是当前node 则使用下一条link的s_node继续循环
            if (is_one_line['e_nid'] == current_node['node_id'] or
                    is_one_line['e_nid'] == current_node.get('adjoin_nid')):
                current_node = fetch_node_by(is_one_line['s_nid'])
                node_chains.append(is_one_line['s_nid'])
            else:
                current_node = fetch_node_by(is_one_line['e_nid'])
                node_chains.append(is_one_line['e_nid'])

            if res_geom.length * 90000 > max_length:
                break

            link_obj = is_one_line

        return road_chains, node_chains, res_geom

    ret_dict = {'link_id_list': [], 'node_id_list': [], 'geom_wkt': ''}
    sd_link_obj = fetch_link_by_id(link_id)
    """
    1、道路种别kind：①道路种别满足需求：4-7、9级路；② 道路种别过滤：1-3、8级路、10级路
    3、道路形态满足需求form：辅路、无属性，过滤82门前道路
    """
    if not sd_link_obj:
        print("filter_not_exist_link", link_id)
        return ret_dict
    if int(sd_link_obj['kind']) not in allow_kinds:
        print("filter_by_kind", link_id, sd_link_obj['kind'])
        return ret_dict
    # 过滤门前路
    if '82' in sd_link_obj['form'].split(","):
        print("filter_by_form", link_id, sd_link_obj['form'])
        return ret_dict

    # 计算s node的延伸
    s_link_chains, s_node_chains, s_geom_obj = process_node_chain(sd_link_obj, sd_link_obj['s_nid'])
    sd_link_obj = fetch_link_by_id(link_id)
    # 计算e node的延伸
    e_link_chains, e_node_chains, e_geom_obj = process_node_chain(sd_link_obj, sd_link_obj['e_nid'])

    return {
        "link_id_list": s_link_chains[::-1] + [link_id] + e_link_chains,
        "node_id_list": s_node_chains[::-1] + e_node_chains,
        "geom_wkt": s_geom_obj.union(e_geom_obj).wkt
    }

