# -*- coding: utf-8 -*-
"""
通过消息队列执行路侧停车场识别
"""
import json
import sys
import time

import src.tools.redis_tool as rt
import signal
import redis
from handle import main, force_run
from src.tools import pgsql
from src.parking.recognition import dbutils
from src.parking_roadside.send_redis_by_file import maintain_info_status

# 用于标记是否继续处理队列
continue_processing = True


def handle_sigterm(signum, frame):
    """
    处理 SIGTERM 信号，停止接收新的 Redis 消息
    """
    global continue_processing
    print("Received SIGTERM, stopping message reception...")
    continue_processing = False


# 注册 SIGTERM 信号处理器
signal.signal(signal.SIGTERM, handle_sigterm)


def process(max_execution_time, chains_name='run_roadside_chains'):
    """
    执行队列
    :return:
    """
    start_time = time.time()
    with rt.RedisTool('aoi') as rt_client, pgsql.get_connection_ttl(pgsql.POI_CONFIG) as conn:
        poi_curs = conn.cursor()

        i = 20
        reconnect = 200
        while i > 0 and reconnect > 0:
            if not continue_processing:
                print("Stopping reception of new messages...")
                break
            if time.time() - start_time > max_execution_time:
                print("Max execution time reached, stopping message reception...")
                break

            try:
                res = rt_client.redis_conn.rpop(chains_name)
            except (redis.ConnectionError, ConnectionResetError) as e:
                rt_client.reconnect()
                reconnect = reconnect - 1
                time.sleep(0.1)
                continue

            if res is None:
                i = i - 1
                time.sleep(10)
            else:
                item = json.loads(res)
                if 'link_id' not in item:
                    continue
                if 'force' in chains_name:
                    force_run(item['link_id'], item['batch_id'], False)
                else:
                    main(item['link_id'], item['batch_id'], False)
                if 'id' in item:
                    maintain_info_status(poi_curs, item, 'CONSUMED')


def get_chains_name_from_db():
    """从 db 中获取一个队列"""
    qry = f"""
    select distinct consumer from parking_roadside_mining_info 
    where status = 'PUSHED' and type = 'new' 
    """
    res = dbutils.fetch_all(pgsql.POI_CONFIG, qry)
    if not res:
        return ''
    with rt.RedisTool('aoi') as rt_client:
        for i in res:
            chain_name = i[0]
            num = rt_client.redis_conn.llen(chain_name)
            if num > 0:
                print(f"队列{chain_name}中有：{num}")
                return chain_name
    return ''


if __name__ == '__main__':
    _max_execution_time = 3600 * 24  # 保障运行稳定，最长运行时间一天
    _chains_name = 'run_roadside_chains'
    if len(sys.argv) > 1:
        _chains_name = sys.argv[1]
    else:
        chains_name_from_db = get_chains_name_from_db()
        if chains_name_from_db != '':
            _chains_name = chains_name_from_db
    process(_max_execution_time, _chains_name)
