# -*- coding: utf-8 -*-
"""
推送待识别redis
"""
import csv
import json
import sys

from tqdm import tqdm

from db.model import PGModel
from datetime import datetime
import src.tools.redis_tool as rt
from src.parking.storefront.utils import cityutils
from src.tools import pgsql
from src.parking.storefront.flow.db import join_str, array_chunk, join_int


def find_2_3_part(city_name):
    """
    查询是否在2/3建成区
    """
    with PGModel() as pg_model:
        cursor = pg_model.get_cursor('chains_db')
        sql = '''
        select distinct on (uid) link_id 
        from parking_road_chains a, bua_area_20241226_2_3th b 
        where st_intersects(a.link_geom, b.geom) 
        and city_name = %s
        '''
        cursor.execute(sql, (city_name,))
        rows = cursor.fetchall()
        return rows


def find_not_2_3_part(city_name):
    """
    查询是否在非2/3建成区
    """
    with PGModel() as pg_model:
        cursor = pg_model.get_cursor('chains_db')
        sql = '''
        select distinct on (uid) link_id 
        from parking_road_chains a, bua_area_20241226_2_3th b 
        where not st_intersects(a.link_geom, b.geom) 
        and batch_id = %s and city_name = %s
        '''
        cursor.execute(sql, (city_name, city_name))
        rows = cursor.fetchall()
        return rows


def find_full_bua(city_name):
    """
    查询全部建成区
    """
    with PGModel() as pg_model:
        cursor = pg_model.get_cursor('chains_db')
        sql = '''
        select distinct on (uid) link_id 
        from parking_road_chains
        where 
        batch_id = %s 
        '''
        cursor.execute(sql, (city_name,))
        rows = cursor.fetchall()
        return rows


def find_full_city(city_name):
    """
    查询全部非建成区
    """
    with PGModel() as pg_model:
        cursor = pg_model.get_cursor('chains_db')
        sql = '''
        select min(link_id) as link_id, st_astext(st_union(link_geom)) as link_geom_str
        from parking_road_chains 
        where 
        batch_id = %s 
        group by uid
        '''
        cursor.execute(sql, (city_name,))
        rows = cursor.fetchall()
        return rows


def check_is_bua(geom_str):
    """
    判断是否在建成区
    """
    with PGModel() as pg_model:
        cursor = pg_model.get_cursor('chains_db')
        sql = '''
        select count(*) as cnt
        from bua_area_20241226 
        where st_intersects(geom, %s) 
        '''
        cursor.execute(sql, ('SRID=4326;' + geom_str,))
        rows = cursor.fetchone()
        if rows['cnt'] > 0:
            return True
        return False


def run_part_by_city(city_name):
    """
    通过城市批量推送redis
    """
    pinyin = cityutils.ZH_TO_PINYIN[city_name]
    if pinyin == '':
        return 'city_name_err'
    today_str = datetime.now().strftime("%m%d")
    # rows = find_2_3_part(city_name)
    # rows = find_not_2_3_part(city_name)
    # rows = find_full_bua(city_name)
    rows = find_full_city(city_name)
    with rt.RedisTool('aoi') as rt_client:
        for row in rows:
            link_id = row['link_id']
            geom = row['link_geom_str']
            if check_is_bua(geom):
                continue
            batch_id = pinyin + '_full_' + today_str
            print("link_id", link_id, batch_id)
            data = {
                "batch_id": batch_id,
                "link_id": link_id
            }
            data_str = json.dumps(data)
            print(data_str)
            # res = rt_client.redis_conn.lpush("run_roadside_chains", data_str)


def run_by_file(file_path):
    """
    批量推送redis
    """
    with rt.RedisTool('aoi') as rt_client:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                item = line.strip().split()
                if len(item) < 5:
                    print('error line: ', line)
                link_id = item[3]
                batch_id = item[4]
                data = {
                    "batch_id": batch_id,
                    "link_id": link_id
                }
                data_str = json.dumps(data)
                print(data_str)
                # res = rt_client.redis_conn.lpush("run_roadside_chains", data_str)


def run_by_mining_file(file_path):
    """
    批量推送redis
    """
    batch_id = 'mining_info_by_old_park_0618'
    list_name = 'mining_info_by_old_park_0618_force'
    num = 0
    with rt.RedisTool('aoi') as rt_client, open(file_path, 'r') as hdr:
        reader = csv.reader(hdr, delimiter='\t')
        next(reader)
        for row in reader:
            link_id = row[1]
            data = {
                "batch_id": batch_id,
                "link_id": link_id
            }
            data_str = json.dumps(data)
            # print(data_str)
            # break
            res = rt_client.redis_conn.lpush(list_name, data_str)
            num += 1
    print(f"over:{num}")


def run_item(link_id, batch_id):
    """
    推送单个link和批次
    """
    with rt.RedisTool('aoi') as rt_client:
        data = {
            "batch_id": batch_id,
            "link_id": link_id
        }
        data_str = json.dumps(data)
        res = rt_client.redis_conn.rpush("run_roadside_chains", data_str)


def _get_wait_consumed_infos(curs, types: list):
    """获取待消费的情报"""
    if len(types) == 0:
        return []
    qry = f"""
    select id from parking_roadside_mining_info 
    where status = 'INIT' and type in ({join_str(types)})
    """
    curs.execute(qry)
    res = curs.fetchall()
    if not res:
        return []
    ids = [i[0] for i in res]
    id_chunks = array_chunk(ids, 200)

    for id_chunk in tqdm(id_chunks, desc=f"待消费总量：{len(ids)}; 分成了：{len(id_chunks)}批"):
        qry = f"""
        select id, long_link_id, batch_id, consumer 
        from parking_roadside_mining_info 
        where id in ({join_int(id_chunk)})
        """
        curs.execute(qry)
        infos = curs.fetchall()
        if not infos:
            continue
        for i in infos:
            yield {
                'id': i[0],
                'link_id': i[1],
                'batch_id': i[2],
                'consumer': i[3],
            }


def maintain_info_status(curs, info: dict, status: str):
    """维护情报状态"""
    qry = f"""
    update parking_roadside_mining_info 
    set status = '{status}', updated_at = now() 
    where id = {info['id']}
    """
    curs.execute(qry)


def run_by_db():
    """从 db 中消费"""
    num = 0
    tot = 0
    with rt.RedisTool('aoi') as rt_client:
        conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
        curs = conn.cursor()
        for info in _get_wait_consumed_infos(curs, types=['new']):
            link_id = info['link_id']
            data = {
                "batch_id": info['batch_id'],
                "link_id": link_id,
                "id": info['id'],
            }
            data_str = json.dumps(data)
            # print(data_str, info['consumer'])
            # exit()
            res = rt_client.redis_conn.lpush(info['consumer'], data_str)
            if isinstance(res, int) and res > 0:
                maintain_info_status(curs, info, 'PUSHED')
                num += 1
            tot += 1
    print(f"over:情报总量：{tot}; 成功消费量：{tot}")


if __name__ == '__main__':
    # run_by_file('')
    # run(sys.argv[1], sys.argv[2])
    # run_item(sys.argv[1], sys.argv[2])
    # run_part_by_city(sys.argv[1])
    # run_by_mining_file('./tmp/mining_info_v2.tsv')
    # run_by_mining_file('./tmp/mining_competitor_info_v2.tsv')
    run_by_db()
