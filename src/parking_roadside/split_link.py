# -*- coding: utf-8 -*-
"""
拆分link
"""

from db.model import PGModel
from db.pg import query_work_by_pid, query_link_map_by_work_id, add_link_map
from api.link_trans import get_multi_long_id


def split_link_by_work_id(work_id):
    """
    拆分缓存link
    """
    link_map = query_link_map_by_work_id(work_id)
    if link_map is not None and len(link_map) > 0:
        return None
    work_info = query_work_by_pid(work_id)
    if work_info is None or work_info['link_id_list'] == '':
        return None

    link_ids = work_info['link_id_list'].split(',')
    print('start', work_info['id'], work_info['link_id_list'])
    long_link_map = get_multi_long_id(link_ids)
    add_link_map(work_id, long_link_map)


def init_all():
    """
    初始化所有数据
    """
    with PGModel() as pg:
        cursor = pg.get_cursor('online')
        cursor.execute('''
        select id from parking_line_turing_work where status = -2 and message = 'not_enough_turnoff'
        and created_at < '2025-03-21'
        ''')
        works = cursor.fetchall()
        for w in works:
            split_link_by_work_id(w['id'])


if __name__ == '__main__':
    init_all()
