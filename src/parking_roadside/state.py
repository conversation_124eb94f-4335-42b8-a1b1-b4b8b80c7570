#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
路侧状态监控
"""
from base64 import b64encode
from urllib.parse import urlencode
from urllib.request import Request, build_opener
import requests

from db.model import PGModel
import src.tools.redis_tool as rt
from src.tools import notice_tool
from src.parking.storefront.utils import cityutils


def run():
    """
    状态监控
    """
    # 待识别路链
    list_len = 0
    with rt.RedisTool('aoi') as rt_client:
        list_len = rt_client.redis_conn.llen("run_roadside_chains")

    with PGModel() as dao:
        cursor = dao.get_cursor('online')
        cursor.execute("""
        -- 查询原始识别情报
select count(*) as cnt
from parking_line_turing_work
where status = -2
  and message = ''
        """)
        # 识别成果
        infer_res_cnt = cursor.fetchone()['cnt']

        cursor.execute("""
        -- 待投掘金情报
select count(*) as cnt
from parking_line_turing_work
where status = -1
  and message = 'turnoff_matched'
        """)
        # 待投掘金情报
        turnoff_matched_cnt = cursor.fetchone()['cnt']

        cursor.execute("""
        -- 查询掘金作业完成数据
select count(distinct a.id) as cnt
from parking_line_turing_work a
         left join parking_line_jujin_prev b on a.id = b.work_id
         left join park_storefront_verify c on b.image_id = c.image_id
where a.status = -1
  and a.message = 'send_juejin'
  and c.id is null;
        """)
        #
        juejin_woring_cnt = cursor.fetchone()['cnt']

    print(f"待识别路链: {list_len}", infer_res_cnt, turnoff_matched_cnt, juejin_woring_cnt)
    notice_tool.send_hi(
        f'路侧停车场运行情况\n待识别路链: {list_len}\n' +
        f'待计算导流区: {infer_res_cnt}\n待投掘金: {turnoff_matched_cnt}\n掘金作业中: {juejin_woring_cnt}',
        token='dd1d100d8350f4996c6bc8160b46a9731',
        group_id=10965961
    )


def tobe_automatic(send=False):
    """
    待自动化策略
    """
    with PGModel() as dao:
        cursor = dao.get_cursor('online')
        sql = """
select count(*) cnt, batch_id
from parking_line_turing_work
where status = -1
  and message = 'juejin_verified'
group by 2
        """
        cursor.execute(sql)
        res = cursor.fetchall()
        city_list = {}
        for row in res:
            batch_id = row['batch_id']
            cnt = row['cnt']
            city_pinyin = batch_id.split('_')[0]
            try:
                city_name = cityutils.PINYIN_TO_ZH[city_pinyin]
            except Exception:
                city_name = city_pinyin
            if city_name not in city_list:
                city_list[city_name] = {'cnt': 0, 'batch_list': []}
            city_list[city_name]['cnt'] += cnt
            city_list[city_name]['batch_list'].append(batch_id)
        for city_name, item in city_list.items():
            print(city_name, item['cnt'], ','.join(item['batch_list']), sep='\t')
            if send:
                for batch_id in item['batch_list']:
                    send_jenkins(batch_id)


city_range_list = [
    '广州市', '深圳市', '成都市', '杭州市', '武汉市', '西安市', '重庆市', '东莞市', '上海市', '南京市', '郑州市',
    '苏州市', '天津市', '佛山市', '长沙市', '宁波市', '合肥市', '昆明市', '南宁市', '青岛市', '厦门市', '济南市',
    '福州市', '石家庄市', '中山市', '温州市', '金华市', '无锡市', '惠州市', '贵阳市', '常州市', '太原市', '绍兴市',
    '大连市', '南昌市', '江门市', '台州市', '汕头市', '咸阳市', '嘉兴市', '洛阳市', '南通市', '湛江市', '保定市',
    '乌鲁木齐市', '临沂市', '海口市', '湖州市', '沈阳市', '绵阳市', '珠海市', '赣州市', '烟台市', '哈尔滨市', '银川市',
    '柳州市', '扬州市', '清远市', '呼和浩特市', '揭阳市', '桂林市', '宜昌市', '潍坊市', '南阳市', '盐城市', '廊坊市',
    '南充市', '济宁市', '德阳市', '邯郸市', '荆州市', '徐州市', '肇庆市', '襄阳市', '株洲市', '镇江市', '芜湖市',
    '新乡市', '淮安市', '黄冈市', '孝感市', '宜宾市', '眉山市', '泉州市', '兰州市', '渭南市', '包头市', '淄博市',
    '茂名市', '遵义市', '漳州市', '阳江市', '菏泽市', '衡阳市', '泸州市', '阜阳市', '河源市', '长春市', '泰州市',
    '常德市', '梅州市', '岳阳市', '沧州市', '安阳市', '许昌市', '九江市', '邢台市', '连云港市', '西宁市', '驻马店市',
    '湘潭市', '玉林市', '日照市', '聊城市', '商丘市', '开封市', '上饶市', '威海市', '乐山市', '资阳市', '六安市',
    '安庆市', '信阳市', '平顶山市', '鄂州市', '潮州市', '大理白族自治州', '郴州市', '曲靖市', '德州市', '韶关市',
    '达州市', '内江市', '大同市', '泰安市', '榆林市', '自贡市', '秦皇岛市', '汉中市', '宿迁市', '十堰市', '晋中市',
    '邵阳市', '汕尾市', '衢州市', '黄石市', '贵港市', '濮阳市', '丽水市', '广安市', '三亚市', '荆门市', '滁州市',
    '运城市', '莆田市', '遂宁市', '东营市', '咸宁市', '蚌埠市', '北海市', '宿州市', '宝鸡市', '衡水市', '马鞍山市',
    '亳州市', '云浮市', '拉萨市', '宜春市', '舟山市', '鄂尔多斯市', '承德市', '宣城市', '宁德市', '吉安市', '周口市',
    '赤峰市', '红河哈尼族彝族自治州', '永州市', '长治市', '张家口市', '凉山彝族自治州', '毕节市', '临汾市', '玉溪市',
    '鞍山市', '恩施土家族苗族自治州', '梧州市', '娄底市', '唐山市', '黔南布依族苗族自治州', '广元市', '河池市',
    '龙岩市', '益阳市', '澳门半岛', '仙桃市', '怀化市', '昭通市', '焦作市', '抚州市', '晋城市', '景德镇市',
    '楚雄彝族自治州', '延安市', '萍乡市', '喀什地区', '齐齐哈尔市', '淮北市', '黔东南苗族侗族自治州', '枣庄市',
    '雅安市', '安顺市', '商洛市', '鹤壁市', '西双版纳傣族自治州', '巴中市', '大庆市', '伊犁哈萨克自治州',
    '黔西南布依族苗族自治州', '吕梁市', '百色市', '来宾市', '丽江市', '通辽市', '黄山市', '铜仁市', '三明市',
    '六盘水市', '安康市', '漯河市', '文山壮族苗族自治州', '三门峡市', '贺州市', '锦州市', '抚顺市', '阿克苏地区',
    '保山市', '南平市', '湘西土家族苗族自治州', '丹东市', '钦州市', '忻州市', '乌兰察布市', '潜江市', '崇左市',
    '昌吉回族自治州', '盘锦市', '池州市', '普洱市', '天门市', '攀枝花市', '吉林市', '新余市', '巴音郭楞蒙古自治州',
    '巴彦淖尔市', '莱芜市', '庆阳市', '葫芦岛市', '防城港市', '呼伦贝尔市', '营口市', '滨州市', '朔州市', '朝阳市',
    '随州市', '吴忠市', '鹰潭市', '张家界市', '阳泉市', '儋州市', '淮南市', '酒泉市', '铜川市', '牡丹江市', '绥化市',
    '延边朝鲜族自治州', '乌海市', '济源市', '石嘴山市', '阿坝藏族羌族自治州', '阜新市', '辽阳市', '临沧市', '佳木斯市',
    '兴安盟', '中卫市', '和田地区', '琼海市', '石河子市', '陵水黎族自治县', '铁岭市', '锡林郭勒盟',
    '德宏傣族景颇族自治州', '武威市', '连江县', '铜陵市', '固原市', '平凉市', '哈密地区', '张掖市', '甘孜藏族自治州',
    '克拉玛依市', '临夏回族自治州', '本溪市', '定西市', '林芝地区', '陇南市', '文昌市', '嘉峪关市', '天水市', '海东市',
    '通化市', '鸡西市', '迪庆藏族自治州', '万宁市', '路环岛', '四平市', '东方市', '乐东黎族自治县', '澄迈县', '氹仔岛',
    '塔城地区', '黑河市', '白银市', '海西蒙古族藏族自治州', '定安县', '双鸭山市', '松原市', '临高县', '白城市',
    '日喀则市', '山南地区', '阿拉尔市', '博尔塔拉蒙古自治州', '金昌市', '白山市', '怒江傈僳族自治州', '吐鲁番地区',
    '阿勒泰地区', '昌江黎族自治县', '鹤岗市', '辽源市', '阿拉善盟', '七台河市', '昌都市', '五家渠市', '屯昌县',
    '海南藏族自治州', '琼中黎族苗族自治县', '伊春市', '图木舒克市', '保亭黎族苗族自治县', '克孜勒苏柯尔克孜自治州',
    '甘南藏族自治州', '金门县', '五指山市', '那曲地区', '海北藏族自治州', '黄南藏族自治州', '白沙黎族自治县', '北屯市',
    '阿里地区', '神农架林区', '玉树藏族自治州', '果洛藏族自治州', '大兴安岭地区', '铁门关市',
]


def check_push_invalid_jj():
    """
    查询推送掘金失效的数据
    """
    with PGModel() as dao:
        cursor = dao.get_cursor('online')
        sql = """
        select count(distinct a.id),
       sum(CASE WHEN c.id is null THEN 1 ELSE 0 END),
       regexp_replace(b.batch_id, '^.*2025', 'roadside_2025')
from parking_line_turing_work a
         left join parking_line_jujin_prev b on a.id = b.work_id
         left join park_storefront_verify c on b.image_id = c.image_id
where a.status = -1
  and a.message = 'send_juejin'
--   and c.id is null
group by 3;
        """
        cursor.execute(sql)
        res = cursor.fetchall()
        for row in res:
            batch_id = row[2] + ".tar"
            req_data = {
                "action": "get_miss_ptid_by_batch",
                "token": "gD2fI9UKZUtYhvww",
                "type": 1,
                "batch_id": batch_id,
            }
            rep = requests.post('http://inner-lutao.baidu-int.com/audit/api/inter/parkingfrontpic', json=req_data)
            if rep.status_code == 200:
                rep_data = rep.json()
                if 'data' in rep_data and 'status' in rep_data['data'] and rep_data['data']['status'] == 'error':
                    print(rep.text)
                    print('need_repush', "or b.batch_id like '%" + row[2].replace('roadside_', '') + "'", sep='\t')


def infer_res():
    """
    识别成果
    """
    with PGModel() as dao:
        cursor = dao.get_cursor('online')
        sql = """
select count(*) cnt, batch_id
from parking_line_turing_work
where status >= -1
  and batch_id not like '%juejin_changed'
  and batch_id like '%full%'
group by 2;
        """
        cursor.execute(sql)
        res = cursor.fetchall()
        print_res(res)


def auto_release_res():
    """
    自动化上线量
    """
    with PGModel() as dao:
        cursor = dao.get_cursor('online')
        sql = """
        -- 全域自动化上线量
        select count(*) as cnt, batch_id
from parking_line_turing_work
where status >= 1
  and status != 6
  and is_automatic
  and batch_id like '%_full_%'
  group by 2
        """
        cursor.execute(sql)
        res = cursor.fetchall()
        print_res(res)


def tobe_jj():
    """
    待投掘金
    """
    with PGModel() as dao:
        cursor = dao.get_cursor('online')
        sql = """
        -- 待投掘金情报
select count(*) cnt, batch_id
from parking_line_turing_work
where status = -1
  and message = 'turnoff_matched'
group by 2;
        """
        cursor.execute(sql)
        res = cursor.fetchall()
        print_res(res)


def jj_res():
    """
    掘金成果
    """
    with PGModel() as dao:
        cursor = dao.get_cursor('online')
        sql = """
        -- 掘金核实成果
select count(*) cnt, batch_id
from parking_line_turing_work
where status >= -1
  and message != 'send_juejin'
  and message != 'turnoff_matched'
  and status != 6
  and batch_id like '%full%'
  group by 2
        """
        cursor.execute(sql)
        res = cursor.fetchall()
        print_res(res)


def poi_res():
    """
    poi成果
    """
    db_res = {}
    with PGModel() as dao:
        cursor = dao.get_cursor('online')
        sql = """
             --  全域poi成果
    select count(distinct a.bid) cnt, city_name
    from parking_roadside_bid_map a
    left join parking_line_turing_work b on a.uid = b.uid
    where a.status = 1
    and b.batch_id like '%full%'
    group by 2
            """
        cursor.execute(sql)
        res = cursor.fetchall()
        for item in res:
            db_res[item['city_name']] = item['cnt']
    for city_name in city_range_list:
        cnt = 0
        if city_name in db_res:
            cnt = db_res[city_name]
        print(city_name, cnt, sep='\t')


def print_res(res):
    """
    输出结果
    """
    city_list = {}
    for row in res:
        batch_id = row['batch_id']
        cnt = row['cnt']
        city_pinyin = batch_id.split('_')[0]
        try:
            city_name = cityutils.PINYIN_TO_ZH[city_pinyin]
        except Exception:
            city_name = city_pinyin
        if city_name not in city_list:
            city_list[city_name] = {'cnt': 0, 'batch_list': []}
        city_list[city_name]['cnt'] += cnt
        city_list[city_name]['batch_list'].append(batch_id)
    for city_name in city_range_list:
        cnt = 0
        batch_id = ""
        if city_name in city_list:
            cnt = city_list[city_name]['cnt']
            batch_id = ','.join(city_list[city_name]['batch_list'])
        print(city_name, cnt, batch_id, sep='\t')


def send_jenkins(batch_id):
    """
    发送jenkins
    """
    url = 'http://gzxj-lss-06.gzxj.baidu.com:8706/jenkins/job/luce_auto_mining/buildWithParameters'
    req = Request(url)
    req.add_header('Content-Type', 'application/x-www-form-urlencoded')
    # req.add_header('Authorization', 'Basic %s' % base64.encodestring(self.token).replace('\n', ''))
    auth = b64encode(b"map:pwd123456").decode("ascii")
    req.add_header('Authorization', 'Basic %s' % auth)
    data = urlencode({'batch_id': batch_id}).encode('utf-8')
    opener = build_opener()
    response = opener.open(req, data)
    return response.read().decode('utf-8')


if __name__ == '__main__':
    run()
    # tobe_automatic()
    # tobe_automatic(True)
    # tobe_jj()
    # infer_res()
    # jj_res()
    # auto_release_res()
