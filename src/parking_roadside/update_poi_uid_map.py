# -*- encoding: utf-8 -*-
"""
掘金投图策略
"""
from db.pg import fetch_work_to_be_update_bid, query_bid_by_uid, add_bid_map
from db.drds import query_bid_from_push_data
from api.send_poi import get_info_by_bid
from mapio.utils import bid2uid


def run():
    """
    获取所有需要更新的bid
    """
    uid_bid_map = {}
    for work in fetch_work_to_be_update_bid():
        uid = work['uid']
        work_id = work['id']
        poi_info = query_bid_by_uid(uid)
        if poi_info is not None and poi_info['id'] != 0:
            continue
        source_id = "roadside_{}".format(uid)
        push_data = query_bid_from_push_data(source_id)
        bid = ""
        if push_data is not None and len(push_data) > 0:
            bid = push_data['bid']
        else:
            source_id = "roadside_{}".format(work_id)
            push_data = query_bid_from_push_data(source_id)
            if push_data is not None and len(push_data) > 0:
                bid = push_data['bid']
        if bid == "":
            continue
        if uid in uid_bid_map and bid != uid_bid_map[uid]:
            print("error", uid, bid, sep="\t")
        uid_bid_map[uid] = bid
    for uid, bid in uid_bid_map.items():
        poi_info = get_info_by_bid(bid)
        status = 0
        city_name = ''
        if (poi_info is not None and poi_info.get('data') and poi_info.get('data').get('base')
                and poi_info.get('data').get('base').get('status')):
            status = int(poi_info.get('data').get('base').get('status'))
        if (poi_info is not None and poi_info.get('data') and poi_info.get('data').get('base')
                and poi_info.get('data').get('base').get('city_name')):
            city_name = poi_info.get('data').get('base').get('city_name')
        print(uid, bid, bid2uid(bid), status, city_name, sep="\t")
        add_bid_map(uid, bid, bid2uid(bid), status, city_name)


if __name__ == '__main__':
    run()
