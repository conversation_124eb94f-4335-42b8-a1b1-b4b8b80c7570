"""
例行脚本：同步来自其它母库的停车场面，并生成推送文件
"""
from dataclasses import dataclass, field
from pathlib import Path
from typing import Callable, Iterable

from src.parking_production import query
from src.tools import pgsql, linq, tsv, utils, pipeline

VERSION = "2.0.0"  # 初始化提交

desc = pipeline.get_desc()


@dataclass
class ParkFaceItem:
    """
    代表单个停车场面
    """

    bid: str
    face_id: str
    geom: str
    others: list[str]
    source: str = field(init=False)


@dataclass
class Context:
    """
    上下文
    """

    bids: list[str]
    items: list[ParkFaceItem] = field(default_factory=list)


# pipe:


def collect_from(collector: Callable[[list[str]], Iterable[ParkFaceItem]], save_dir: Path):
    """
    收集指定来源的数据，并保存到指定目录下
    """
    source = collector.__name__

    @desc(f"collect from {source} ...")
    def pipe(ctx: Context, proceed):
        items = list(collector(ctx.bids))
        for item in items:
            item.source = source

        if items:
            bids = {x.bid for x in ctx.items}
            ctx.items.extend(x for x in items if x.bid not in bids)

            rows = [[x.bid, x.face_id, x.geom, *x.others] for x in items]
            tsv.write_tsv(save_dir / f"{source}.tsv", rows)
            print(f"collected: {len(items)} from '{source}'")

        proceed()

    return pipe


def export_push_file(file_path: Path):
    """
    导出推送文件
    """

    @desc(f"export push sync file to {file_path}")
    def pipe(ctx: Context, proceed):
        today = query.today_str()
        rows = [[x.bid, query.gcj2mc(x.geom), x.face_id, f"v{VERSION}-{today}-{x.source}"] for x in ctx.items]
        tsv.write_tsv(file_path, rows)
        print(f"resolved: {len(rows)} / {len(ctx.bids)}")
        proceed()

    return pipe


# source:


def blu_face(bids: list[str]):
    """
    collector: 来自 AOI
    """
    sql = """
        select poi_bid, src, a.face_id, st_astext(b.geom)
        from blu_face_poi a
        inner join blu_face b on a.face_id = b.face_id
        where a.poi_bid in %s;
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])

    return [ParkFaceItem(bid=bid, face_id=face_id, geom=geom, others=[src]) for bid, src, face_id, geom in ret]


def park_competitor(bids: list[str]):
    """
    collector: 来自竞品停车场
    """
    sql = """
        select a.matched_poi_bid, st_distance(b.geometry::geography, a.area::geography), st_astext(b.geometry), competitor_id, st_astext(a.area)
        from park_competitor a
        inner join poi b on a.matched_poi_bid = b.bid
        where matched_poi_bid in %s and area is not null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])

    grouped = linq.group_by(ret, lambda x: x[0])
    for _, items in grouped.items():
        if len(items) > 1:
            items.sort(key=lambda x: x[1])

        if items[0][1] > 0:
            continue

        bid, distance, point, competitor_id, geom = items[0]
        yield ParkFaceItem(bid=bid, face_id=utils.md5(geom), geom=geom, others=[competitor_id, point, distance])


def aoi_intelligence_history(bids: list[str]):
    """
    collector: 来自竞品 AOI
    """
    sql = """
        select a.bid, create_time, st_distance(b.geometry::geography, a.geom::geography), st_astext(b.geometry), a.id, st_astext(a.geom)
        from aoi_intelligence_history a
        inner join poi b on a.bid = b.bid
        where a.bid in %s and not st_isempty(a.geom);
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql, [tuple(bids)])

    grouped = linq.group_by(ret, lambda x: x[0])
    for _, items in grouped.items():
        if len(items) > 1:
            items.sort(key=lambda x: x[1], reverse=True)

        if items[0][2] > 0:
            continue

        bid, create_time, distance, point, competitor_id, geom = items[0]
        yield ParkFaceItem(
            bid=bid, face_id=utils.md5(geom), geom=geom, others=[competitor_id, point, distance, create_time]
        )


# bids provider:


def get_4categories_bids():
    """
    获取所有四大垂类停车场的 bid
    """
    sql = """
        select distinct bid from park_4categories_list a
        inner join park_online_data b on a.parking_bid = b.bid
        where b.area is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    return [x[0] for x in ret]


def get_all_bids():
    """
    获取所有停车场的 bid
    """
    sql = """
        select distinct bid from park_online_data a
        where a.area is null;
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    return [x[0] for x in ret]


BID_PROVIDERS = {
    "4categories": get_4categories_bids,
    "all": get_all_bids,
}


def main():
    """
    主函数
    """
    bid_source = "4categories"

    today = query.today_str()
    save_dir = utils.ensure_dir(Path("output_sync_park_face") / f"{today}_{bid_source}")
    push_file = save_dir / f"push_sync_{bid_source}_{today}.tsv"
    pipe = pipeline.Pipeline(
        collect_from(blu_face, save_dir),
        collect_from(park_competitor, save_dir),
        collect_from(aoi_intelligence_history, save_dir),
        export_push_file(push_file),
    )
    desc.attach(pipe)

    bids = BID_PROVIDERS[bid_source]()
    ctx = Context(bids=bids)
    pipe(ctx)


if __name__ == "__main__":
    main()
