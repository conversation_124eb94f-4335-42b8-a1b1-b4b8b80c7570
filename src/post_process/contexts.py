# !/usr/bin/env python3
"""
上下文
"""
from typing import List, Dict


class FeatureContext:
    """
    要素读取流程上下文
    """
    pass


class RecognitionContext:
    """
    识别结果处理流程上下文
    """
    pass


class StrategyScores:
    """
    策略评分
    """
    pass


class StrategyContext:
    """
    策略分析流程上下文
    """
    feature_ctx: FeatureContext
    recognition_ctx: RecognitionContext
    scores: StrategyScores
