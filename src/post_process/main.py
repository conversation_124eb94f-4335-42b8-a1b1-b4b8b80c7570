# -*- coding: utf-8 -*-
"""
模块入口文件
"""

import strategies

from contexts import FeatureContext, RecognitionContext, StrategyContext
from src.tools.pipeline import Pipeline


def main():
    """
    入口函数
    """

    pipeline = Pipeline(
        strategies.evaluate_border_closed,
        strategies.evaluate_poi_cover,
        strategies.evaluate_border_perimeter_rate,
    )

    ctx = StrategyContext()
    ctx.feature_ctx = FeatureContext()
    ctx.recognition_ctx = RecognitionContext()

    pipeline(ctx)


if __name__ == '__main__':
    main()
