# -*- coding: utf-8 -*-
"""
策略集
"""

from src.post_process.contexts import StrategyContext


def evaluate_border_closed(ctx: StrategyContext, proceed):
    """
    TODO: 实例代码，可以移除。
    """

    print('evaluate_border_closed')
    proceed()


def evaluate_poi_cover(ctx: StrategyContext, proceed):
    """
    TODO: 实例代码，可以移除。
    """

    print('evaluate_poi_cover')
    proceed()


def evaluate_border_perimeter_rate(ctx: StrategyContext, proceed):
    """
    TODO: 实例代码，可以移除。
    """

    print('evaluate_border_perimeter_rate')
    proceed()
