# !/usr/bin/env python3
"""
例行街区切割
"""
import argparse
import datetime
import multiprocessing
import shutil
import sqlite3
from dataclasses import dataclass, field
from pathlib import Path
from types import SimpleNamespace

import shapely.wkt
from retrying import retry
from shapely import Polygon
from tqdm import tqdm

from script.ttf_download import ttfa, ttfb
from src.aoi_cleanup_strategy.mesh_tools import get_mesh_id
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.pre_process.split_street import main as split_street
from src.tools import pipeline, pgsql, notice_tool, tsv
from src.tools.city_tools import all_city

desc = pipeline.get_desc()


@dataclass
class StreetRegion:
    """
    街区信息
    """
    id: str
    wkb: str
    mesh_id: str


@dataclass
class Context:
    """
    脚本执行上下文
    """
    work_dir: Path
    city: str
    table_name: str
    table_prefix: str
    ttfa_dir: Path = None
    ttfb_dir: Path = None
    restarted: bool = False
    regions: list[StreetRegion] = field(default_factory=list)

    def __post_init__(self):
        self.work_dir.mkdir(parents=True, exist_ok=True)

        self.ttfa_dir = self.work_dir / 'data' / 'ttfa'
        self.ttfa_dir.mkdir(parents=True, exist_ok=True)

        self.ttfb_dir = self.work_dir / 'data' / 'ttfb'
        self.ttfb_dir.mkdir(parents=True, exist_ok=True)


@desc()
def fill_city(ctx: Context, proceed):
    """
    填充城市名
    """
    get_city_sql = '''
        select city from split_street_status where status != 2 order by random() limit 1;
    '''
    reset_status_sql = '''
        update split_street_status set status = 0, start_time = null, end_time = null;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        row = poi_stab.fetch_one(get_city_sql)

        if row is None:
            # 全国已经切割完毕，需要重置状态。
            poi_stab.execute(reset_status_sql)
            row = poi_stab.fetch_one(get_city_sql)
            ctx.restarted = True

        if row is None:
            raise Exception('no city can be processed.')

        ctx.city = row[0]

    proceed()


@desc()
def fill_table_name(ctx: Context, proceed):
    """
    填充表名
    """
    all_tables = sorted(get_all_tables(ctx.table_prefix), key=lambda x: x[1], reverse=True)
    current_table = all_tables[0][0]

    if ctx.restarted:
        # 状态重置后需要做 3 件事：
        # 1. 将视图指向次新表
        # 2. 删除最旧表
        # 3. 创建最新表
        update_view(ctx.table_prefix, current_table)
        drop_expired_table(ctx.table_prefix, expire_day=30)
        today = datetime.date.today().strftime('%Y%m%d')
        ctx.table_name = f"{ctx.table_prefix}_{today}"
        create_table(ctx.table_name)  # 当所有城市切割好后，视图就可以指向最新表。
    else:
        ctx.table_name = current_table

    proceed()


@desc()
def mark_start_time(ctx: Context, proceed):
    """
    标记开始时间
    """
    sql = '''
        update split_street_status set status = 1, start_time = now() where city = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(sql, [ctx.city])

    proceed()


# noinspection PyUnusedLocal
@desc()
def check_status(ctx: Context, proceed):
    """
    检查状态
    """
    sql = '''
        select 1 from split_street_status where status = 1 limit 1;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        if poi_stab.execute(sql) is not None:
            raise Exception("there is other city being processed.")

    proceed()


@desc()
def check_args(ctx: Context, proceed):
    """
    检查参数
    """
    if ctx.city not in all_city():
        raise ValueError(f"{ctx.city} is not a valid city name.")

    if ctx.table_name == '':
        raise ValueError(f"{ctx.table_name} is not a valid table name.")

    sql = '''
        select 1 
        from pg_catalog.pg_tables
        where schemaname = 'public' 
        and tablename = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG) as poi_stab:
        if poi_stab.fetch_one(sql, [ctx.table_name]) is None:
            raise ValueError(f"{ctx.table_name} is not a valid table name.")

    proceed()


@retry(stop_max_attempt_number=4, wait_random_min=1000, wait_random_max=5000)
def __download_ttfa(ctx: Context):
    """
    下载道路数据
    """
    timeout = 7200
    save_dir = ctx.ttfa_dir / ctx.city
    shutil.rmtree(save_dir, ignore_errors=True)

    process = multiprocessing.Process(target=ttfa.download_by_city, args=(ctx.city, str(ctx.work_dir)))
    process.start()
    process.join(timeout)

    if process.is_alive():
        process.terminate()
        process.join()
        raise TimeoutError(f"download {ctx.city} ttfa timeout.")

    if not save_dir.exists():
        raise Exception(f"download {ctx.city} ttfa failed.")


@desc()
def download_ttfa(ctx: Context, proceed):
    """
    下载道路数据
    """
    __download_ttfa(ctx)
    proceed()


@retry(stop_max_attempt_number=4, wait_random_min=1000, wait_random_max=5000)
def __download_ttfb(ctx: Context):
    """
    下载背景数据
    """
    timeout = 7200
    save_dir = ctx.ttfb_dir / ctx.city
    shutil.rmtree(save_dir, ignore_errors=True)

    process = multiprocessing.Process(target=ttfb.download_by_city, args=(ctx.city, str(ctx.work_dir)))
    process.start()
    process.join(timeout)

    if process.is_alive():
        process.terminate()
        process.join()
        raise TimeoutError(f"download {ctx.city} ttfb timeout.")

    if not save_dir.exists():
        raise Exception(f"download {ctx.city} ttfb failed.")


@desc()
def download_ttfb(ctx: Context, proceed):
    """
    下载背景数据
    """
    __download_ttfb(ctx)
    proceed()


@desc()
def generate_street_regions(ctx: Context, proceed):
    """
    生成街区
    """
    timeout = 3600

    args = SimpleNamespace(
        back_db_path=ctx.ttfb_dir / ctx.city,
        road_db_path=ctx.ttfa_dir / ctx.city,
        road_level=7,
        process_number=4,
    )
    process = multiprocessing.Process(target=split_street, args=(args,))
    process.start()
    process.join(timeout)

    if process.is_alive():
        process.terminate()
        process.join()
        raise TimeoutError(f"{ctx.city} split street timeout.")

    proceed()


@desc()
def load_street_regions(ctx: Context, proceed):
    """
    加载街区
    """
    get_wkb_sql = '''
        select st_geomfromtext(%s, 4326);
    '''
    get_region_sql = f'''
        select face_id, geom from region_face;
    '''
    region_dir = ctx.ttfb_dir / ctx.city / 'region'

    with PgsqlStabilizer(pgsql.COMPUTE_CONFIG) as compute_stab:
        for region_path in tqdm([x for x in region_dir.rglob("*.ttfr")]):
            with sqlite3.connect(str(region_path)) as conn:
                cursor = conn.cursor()
                cursor.execute(get_region_sql)
                for face_id, wkt in cursor.fetchall():
                    geom = Polygon(shapely.wkt.loads(wkt).exterior)
                    first_x_y = list(geom.boundary.coords)[0]
                    mesh_id = get_mesh_id(float(first_x_y[0]), float(first_x_y[1]))

                    ctx.regions.append(StreetRegion(
                        id=face_id,
                        mesh_id=mesh_id,
                        wkb=compute_stab.fetch_one(get_wkb_sql, [wkt])[0],
                    ))

    proceed()


def create_table(table_name):
    """
    创建数据库表
    """
    create_table_sql = f'''
        create table if not exists {table_name}(
            face_id varchar(128) not null primary key,
            mesh_id varchar(6) not null,
            geom geometry(Geometry, 4326),
            city varchar(254) not null default ''
        );
    '''
    clear_table_sql = f'''delete from {table_name};'''
    create_index_sql = f'''
        create index if not exists {table_name}_geom_index 
        on {table_name} 
        using gist (geom);
        
        create index if not exists {table_name}_mesh_id_index 
        on {table_name} 
        using btree (mesh_id);
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(create_table_sql)
        poi_stab.execute(clear_table_sql)
        poi_stab.execute(create_index_sql)


def update_view(view_name, table_name):
    """
    更新数据库视图
    """
    create_view_sql = f'''
        create or replace view {view_name} as
        select * from {table_name};
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(create_view_sql)


def get_all_tables(table_prefix):
    """
    获取所有街区表
    """
    sql = f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name LIKE '{table_prefix}_%';
        """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        ret = pgsql.fetch_all(conn, sql)

    tables = [(x[0], x[0].split("_")[-1]) for x in ret]
    return [(table_name, datetime.datetime.strptime(day, "%Y%m%d")) for table_name, day in tables]


def drop_expired_table(table_prefix: str, expire_day: int = 8):
    """
    删除过期数据库表
    """
    tables = get_all_tables(table_prefix)
    today = datetime.datetime.now()
    expire = today - datetime.timedelta(days=expire_day)
    expired_tables = [table_name for table_name, day in tables if day < expire]

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        for table_name in expired_tables:
            # NOTE: 此处不要把 {table_name} 写成 %s 的形式，pgsql 会把它当成字符串，加上引号，导致 sql 语法错误
            sql_clear = f"""
                drop table if exists {table_name};
            """
            pgsql.execute(conn, sql_clear)

    return expired_tables


@desc()
def save_street_regions_to_db(ctx: Context, proceed):
    """
    保存街区到数据库
    """
    delete_sql = f'''
        delete from {ctx.table_name} 
        where city = %s;
    '''
    temp_file_path = Path(ctx.ttfb_dir / 'temp.csv')
    tsv.write_tsv(temp_file_path, [[x.id, x.mesh_id, x.wkb, ctx.city] for x in ctx.regions])

    with (
        open(temp_file_path, 'r', encoding='utf-8') as f,
        pgsql.get_connection(pgsql.POI_CONFIG) as poi_conn,
        poi_conn.cursor() as cur,
    ):
        try:
            cur.execute(delete_sql, [ctx.city])
            # noinspection PyTypeChecker
            cur.copy_from(f, table=ctx.table_name, columns=(
                'face_id',
                'mesh_id',
                'geom',
                'city',
            ))
        except Exception as e:
            print(e)
            raise e

    proceed()


@desc()
def mark_end_time(ctx: Context, proceed):
    """
    标记结束时间
    """
    sql = '''
        update split_street_status set status = 2, end_time = now() where city = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(sql, [ctx.city])

    proceed()


def mark_failed(ctx: Context):
    """
    标记任务失败
    """
    sql = '''
        update split_street_status set status = -1, end_time = now() where city = %s;
    '''

    with PgsqlStabilizer(pgsql.POI_CONFIG) as poi_stab:
        poi_stab.execute(sql, [ctx.city])


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--mode',
        dest='mode',
        type=str,
        choices=['auto', 'manual', 'test'],
        default='manual',
        required=False,
    )
    parser.add_argument(
        '--city',
        dest='city',
        type=str,
        default='',
        required=False,
    )
    parser.add_argument(
        '--table-name',
        dest='table_name',
        type=str,
        default='',
        required=False,
    )
    return parser.parse_args()


def create_pipeline(mode):
    """
    创建策略执行管道
    """
    if mode == 'auto':
        return pipeline.Pipeline(
            check_status,
            fill_city,
            fill_table_name,
            mark_start_time,
            download_ttfa,
            download_ttfb,
            generate_street_regions,
            load_street_regions,
            save_street_regions_to_db,
            mark_end_time,
        )
    elif mode == 'manual':
        return pipeline.Pipeline(
            check_args,
            download_ttfa,
            download_ttfb,
            generate_street_regions,
            load_street_regions,
            save_street_regions_to_db,
            mark_end_time,
        )
    elif mode == 'test':
        return pipeline.Pipeline(
            download_ttfa,
            download_ttfb,
            generate_street_regions,
        )

    raise ValueError(f'Unknown mode: {mode}')


def clear_cache(ctx: Context):
    """
    清理缓存
    """
    shutil.rmtree(ctx.ttfa_dir / ctx.city, ignore_errors=True)
    shutil.rmtree(ctx.ttfb_dir / ctx.city, ignore_errors=True)


def alert_to_infoflow(e):
    """
    异常信息如流通知
    """
    # noinspection SpellCheckingInspection
    notice_tool.send_hi(
        f'例行街区切割脚本异常！{e}',
        atuserids=['chenjie02_cd'],
        token='d5070dd11c100081e2110cb89f9e71680'
    )


def main(args):
    """
    主函数
    """
    main_pipe = create_pipeline(args.mode)
    desc.attach(main_pipe)
    ctx = Context(
        work_dir=Path('cache/auto_split_street'),
        city=args.city,
        table_name=args.table_name,
        table_prefix='street_region',
    )

    if args.mode != 'test':
        try:
            main_pipe(ctx)
        except Exception as e:
            mark_failed(ctx)
            alert_to_infoflow(e)
        finally:
            clear_cache(ctx)
    else:
        try:
            main_pipe(ctx)
        finally:
            clear_cache(ctx)


if __name__ == '__main__':
    main(parse_args())
