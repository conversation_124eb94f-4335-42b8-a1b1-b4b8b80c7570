# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""用于创建目标检测数据集"""
import sys
from os import path

from osgeo import ogr

root_path = path.dirname(path.dirname(__file__) + "/../../")
sys.path.insert(0, root_path)

import rtree
from src.pre_process.data_provider import ImageProvider, ImageJsonProvider
from src.pre_process.dataset import *


class DetectionProductDataset(Dataset):
    """目标检测数据集"""
    def __init__(self):
        super().__init__()
        self.city = ""
        self.distinct = self.args.distinct
        self.mode = DatasetMode.Product

    def _fill_arguments(self, parser):
        super()._fill_arguments(parser)
        parser.add_argument(
            '--distinct',
            dest='distinct',
            help='是否去重，即如果一个 polygon 已完整出现在一张大图中，后续再遍历到它时会跳过它。',
            type=bool,
            default=False,
            required=False,
        )

    def _get_region_items(self):
        if not self.distinct:
            return self.__get_region_items_in_common_mode()
        else:
            return self.__get_region_items_in_distinct_mode()

    def _fill_providers(self):
        image_json_provider = ImageJsonProvider(self)
        image_provider = ImageProvider(self)

        image_json_provider.ready_data()
        image_provider.ready_data()

        self.providers.append(image_json_provider)
        self.providers.append(image_provider)

    def __get_region_items_in_common_mode(self):
        with open(self.region_file_path, "r", encoding="utf8") as f:
            return [
               (
                   region["uuid"],
                   self.resize_region(ogr.CreateGeometryFromWkt(region["poly_geom"])),
                   region["city"][0]
               )
               for region in jsonpickle.decode(f.read())
            ]

    def __get_region_items_in_distinct_mode(self):
        with open(self.region_file_path, "r", encoding="utf8") as f:
            region_rtree = rtree.index.Index(interleaved=False)
            regions = dict()
            region_count = 0
            region_accept = set()

            for region_json in jsonpickle.decode(f.read()):
                region_uuid = region_json["uuid"]
                geom = ogr.CreateGeometryFromWkt(region_json["poly_geom"])
                resized_geom = self.resize_region(geom)
                city = region_json["city"][0]
                x_min, x_max, y_min, y_max = geom.GetEnvelope()
                regions[region_count] = (region_uuid, geom, resized_geom, city)
                region_rtree.insert(region_count, (x_min, x_max, y_min, y_max))
                region_count += 1

            # 1. 遍历 regions，取出 resized_geom，根据 resized_geom 找完全包含在内的 region，标记为已接受
            # 2. 继续遍历 regions，如果发现了已经标记为接受的 region，则跳过，否则进入第 1 步
            distinct_regions = []
            for region_item in regions.items():
                index_center, region_center = region_item
                if index_center in region_accept:
                    continue

                region_uuid, _, resized_geom, city = region_center
                region_accept.add(index_center)
                x_min, x_max, y_min, y_max = resized_geom.GetEnvelope()
                for index_inner in region_rtree.intersection((x_min, x_max, y_min, y_max)):
                    region_other = regions[index_inner]
                    _, geom, _, _ = region_other
                    if resized_geom.Contains(geom):
                        region_accept.add(index_inner)

                distinct_regions.append((region_uuid, resized_geom, city))

            return distinct_regions


if __name__ == '__main__':
    DetectionProductDataset().create()
