# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""用于创建屋顶样式数据集"""
import sys
from os import path

root_path = path.dirname(path.dirname(__file__) + "/../../")
sys.path.insert(0, root_path)

from src.pre_process.data_provider import ImageProvider, DataProvider, DataProviderHelper
from src.pre_process.create_seg_dataset import SemanticDataset
from src.pre_process.raster_provider import RegionInfo, Point, Bounds
from src.pre_process.documents import RoofStyle, BudFace


class RoofData:
    def __init__(self, row_id: str, region_info: RegionInfo, dataset: SemanticDataset):
        self.row_id = row_id
        self.region_info = region_info
        self.dataset = dataset

        bud_face_layer = dataset.get_layer("back", "bud_face")
        if bud_face_layer is None:
            raise Exception("can not find bud_face layer!")
        self.bud_faces = bud_face_layer.get_features_in_coordinates(region_info.gcj_expanded_geom.GetEnvelope())
        self.__create_data()

    def to_json(self):
        return {
            "results": self.results,
            "extra": self.extra
        }

    def __create_data(self):
        self.results = {
            "elements": list(self.__create_elements()),
            "relations": {
                "overlap": {},
                "association": [],
                "hollowedArea": {},
                "composition": [],
                "inclusion": {},
                "junction": [],
            },
            "size": {
                "width": self.region_info.pixel_size.width,
                "height": self.region_info.pixel_size.height,
            },
            "dimension": {
                "dimension1": 1,
            },
            "meta": {
                "isErrorFrame": False,
                "errorReason": ''
            },
        }
        self.extra = {
            "adminCheckConfirmed": False,
            "pageId": self.row_id,
            "projectionInfo": {},
            "verifiedUuids": [],
        }

    def __create_elements(self):
        element_count = 0
        roof_style_count_dict = {}

        for bud_face in self.bud_faces:
            element_count += 1

            roof_style_value = bud_face.get_roof_style_value()
            roof_style_text = bud_face.get_roof_style_text()
            roof_style_count = roof_style_count_dict.get(roof_style_value, None)
            if roof_style_count is None:
                roof_style_count = 0
            roof_style_count += 1
            roof_style_count_dict[roof_style_value] = roof_style_count

            yield {
                "attribute": {
                    "type": "1",
                    # 不知道这里为啥是 number3 而不是 number。
                    "number3": element_count,
                },
                "uuid": bud_face.id,
                "markType": "area",
                "typeId": f"{roof_style_value}_{roof_style_count}",
                "text": f"{roof_style_text}_{roof_style_count}",
                "points": list(self.__get_pixel_points(bud_face)),
                "rotation": 0,
            }

    def __get_pixel_points(self, bud_face: BudFace):
        x_min, x_max, y_min, y_max = self.region_info.gcj_expanded_geom.GetEnvelope()
        region_bounds = Bounds(
            left=x_min,
            top=y_max,
            right=x_max,
            bottom=y_min
        )

        boundary = bud_face.geom.GetBoundary()
        points = boundary.GetPoints()

        for point in points:
            pixel = self.dataset.raster_provider.coord_to_pixel(region_bounds, point)
            yield {
                "x": pixel.x,
                "y": pixel.y,
            }


class RoofCsvProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('roof', r"json_info/roof", dataset)
        self.line_count = 0

    def ready_data(self):
        super().ready_data()
        with open(f"{self.save_folder_path}/roof.tsv", 'x', encoding='utf8') as f:
            f.write("img\turl\tres\r")

    def can_output(self, region_info: RegionInfo):
        bud_face_layer = self.dataset.get_layer("back", "bud_face")
        if bud_face_layer is None:
            return False

        # 街区范围内必须有建筑物，且屋顶样式不能为 ”未调查“ 和 ”其它“。
        inner_bud_faces = bud_face_layer.get_features_in_geometry(region_info.gcj_geom)
        filtered_bud_faces = filter(
            lambda face: face.roof_style is not (RoofStyle.Unknown or RoofStyle.Other),
            inner_bud_faces
        )

        return True if any(filtered_bud_faces) else False

    def output(self, region_info: RegionInfo):
        self.line_count += 1
        with open(f"{self.save_folder_path}/roof.tsv", 'a', encoding='utf8') as f:
            json = DataProviderHelper.to_json(RoofData(str(self.line_count), region_info, self.dataset).to_json())
            f.write(f"{region_info.id}.jpg\timages/{region_info.id}.jpg\t{json}\r")


class RoofDataset(SemanticDataset):
    """屋顶数据集"""
    def _fill_providers(self):
        roof_json_provider = RoofCsvProvider(self)
        image_provider = ImageProvider(self)

        roof_json_provider.ready_data()
        image_provider.ready_data()

        self.providers.append(roof_json_provider)
        self.providers.append(image_provider)


if __name__ == '__main__':
    RoofDataset().create()
