# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""用于创建语义分割扫街产出数据集"""
import math
import sys
from os import path

root_path = path.dirname(path.dirname(__file__) + "/../../")
sys.path.insert(0, root_path)

from src.pre_process.dataset import *
from src.pre_process.create_seg_dataset import SemanticDataset
from src.pre_process.raster_provider import Bounds


class SemanticScanProductDataset(SemanticDataset):
    def __init__(self):
        super().__init__()
        self.min_side_length = self.args.min_side_length
        self.min_area = self.args.min_area
        self.strange_ratio_min = self.args.strange_ratio_min
        self.strange_ratio_max = self.args.strange_ratio_max
        self.large_mode = self.args.large_mode
        self.mode = DatasetMode.Product

    def _get_region_items(self):
        region_items = []
        for region_face in self.get_features("region", "region_face"):
            if self.large_mode:
                if not self.__filter_region_item_in_large_mode(region_face):
                    continue
                for region_item in self.__create_region_item_on_large_mode(region_face):
                    if region_item is None:
                        continue
                    region_items.append(region_item)
            else:
                if not self.__filter_region_item_in_common_mode(region_face):
                    continue
                region_items.append((region_face.id, region_face.geom, self.city))

        return region_items

    def __filter_region_item_in_common_mode(self, region_face):
        gcj_geom = region_face.geom
        # 小数点后第 5 位对应米。
        gcj_expanded_geom: ogr.Geometry = gcj_geom.Buffer(self.padding * 1e-5)
        wgs_bounds = RasterHelper.get_wgs_bounds(gcj_expanded_geom)
        pixel_size = self.raster_provider.get_region_pixel_size(wgs_bounds)

        # 过滤掉边长太长的。
        if self.max_side_length > 0 and \
                (pixel_size.width > self.max_side_length or pixel_size.height > self.max_side_length):
            return False

        return True

    def __filter_region_item_in_large_mode(self, region_face):
        gcj_geom = region_face.geom
        # 小数点后第 5 位对应米。
        gcj_expanded_geom: ogr.Geometry = gcj_geom.Buffer(self.padding * 1e-5)
        wgs_bounds = RasterHelper.get_wgs_bounds(gcj_expanded_geom)
        pixel_size = self.raster_provider.get_region_pixel_size(wgs_bounds)

        # 过滤掉边长太小的。
        if self.min_side_length > 0 and \
                pixel_size.width <= self.min_side_length and \
                pixel_size.height <= self.min_side_length:
            return False

        # 超过一整个图幅的直接忽略
        if pixel_size.width > 23000 or pixel_size.height > 15000:
            return False

        return True

    def __create_region_item_on_large_mode(self, region_face):
        gcj_geom = region_face.geom
        wgs_bounds = RasterHelper.get_wgs_bounds(gcj_geom)
        wgs_x_min, wgs_x_max, wgs_y_min, wgs_y_max = (wgs_bounds.left,
                                                      wgs_bounds.right,
                                                      wgs_bounds.bottom,
                                                      wgs_bounds.top)
        gcj_x_min, gcj_x_max, gcj_y_min, gcj_y_max = gcj_geom.GetEnvelope()

        wgs_x_difference = wgs_x_max - wgs_x_min
        wgs_y_difference = wgs_y_max - wgs_y_min
        gcj_x_difference = gcj_x_max - gcj_x_min
        gcj_y_difference = gcj_y_max - gcj_y_min

        region_coord_length = \
            self.min_side_length \
            / self.raster_provider.raster_unit_pixel_length \
            * self.raster_provider.raster_coord_length

        x_count = math.ceil(wgs_x_difference / region_coord_length)
        y_count = math.ceil(wgs_y_difference / region_coord_length)

        if x_count == 0 or y_count == 0:
            return None

        gcj_unit_x_coord = gcj_x_difference / x_count
        gcj_unit_y_coord = gcj_y_difference / y_count

        for x in range(x_count):
            for y in range(y_count):
                gcj_bounds = Bounds(
                    left=gcj_x_min + x * gcj_unit_x_coord,
                    top=gcj_y_min + (y + 1) * gcj_unit_y_coord if y < y_count - 1 else gcj_y_max,
                    right=gcj_x_min + (x + 1) * gcj_unit_x_coord if x < x_count - 1 else gcj_x_max,
                    bottom=gcj_y_min + y * gcj_unit_y_coord
                )
                polygon = RasterHelper.convert_to_geometry(gcj_bounds)
                yield f'{region_face.id}_{x}_{y}', polygon, self.city

    def _fill_arguments(self, parser):
        super()._fill_arguments(parser)
        parser.add_argument(
            '--min-side-length',
            dest='min_side_length',
            help='边长小于等于此值的图片将被忽略。',
            type=int,
            default=-1,
            required=False,
        )
        parser.add_argument(
            '--min-area',
            dest='min_area',
            help='面积小于此值的街区将被忽略，单位：平方米。',
            type=float,
            default=2000,
            required=False,
        )
        parser.add_argument(
            '--strange-ratio-min',
            dest='strange_ratio_min',
            help='面积与周长的平方比（最小值），用于描述街区的畸形程度，值越小越畸形，值域为：[0, 1/4*PI]。',
            type=float,
            default=0.025,
            required=False,
        )
        parser.add_argument(
            '--strange-ratio-max',
            dest='strange_ratio_max',
            help='面积与周长的平方比（最大值），用于描述街区的畸形程度，值越小越畸形，值域为：[0, 1/4*PI]。',
            type=float,
            default=1 / 4 * math.pi,
            required=False,
        )
        parser.add_argument(
            '--large-mode',
            dest='large_mode',
            help='是否启用大街区模式。',
            type=bool,
            default=False,
            required=False,
        )


if __name__ == '__main__':
    SemanticScanProductDataset().create()
