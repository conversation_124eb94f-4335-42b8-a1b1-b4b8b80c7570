# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""用于创建语义分割训练数据集"""
import sys
from os import path

root_path = path.dirname(path.dirname(__file__) + "/../../")
sys.path.insert(0, root_path)

from src.pre_process.dataset import *
from src.pre_process.documents import *
from src.pre_process.draw_seg_image import main as draw_seg_image
from src.pre_process.data_provider import DataProviderFactory, FilterMode, DataProviderHelper


class DrawSemanticImageArgs:
    """绘制语义分割图片参数"""
    def __init__(self, output_path, data_types):
        self.dataset_dir = output_path
        self.dir_names = DataProviderHelper.get_valid_data_types(data_types)
        self.tag_file = f'{root_path}/src/pre_process/aoi_tag_29.txt'
        self.only_contained = False
        self.only_border = False
        self.output_dir = None
        self.process_number = 8


class SemanticDataset(Dataset):
    """语义分割训练数据集"""
    def __init__(self):
        super().__init__()
        self.poi_db_path = self.args.poi_db_path
        self.back_db_path = self.args.back_db_path
        self.road_db_path = self.args.road_db_path
        self.region_db_path = self.args.region_db_path
        self.city = self.args.city
        self.data_types = self.args.data_types
        self.geometries: List[ogr.Geometry] = []
        self.rtree = rtree.index.Index(interleaved=False)

    def create(self):
        """创建数据集"""
        self._load_data()
        self._output()
        if self.draw_image:
            args = DrawSemanticImageArgs(self.output_path, self.data_types)
            if args.dir_names != '':
                draw_seg_image(args)

    def _get_region_items(self):
        return [
            (
                region_face.id,
                self.resize_region(region_face.geom),
                self.city
            )
            for region_face in self.get_features("region", "region_face")
        ]

    def _load_data(self):
        super()._load_data()

        filter_mode = self.filter_mode.lower()
        if filter_mode == FilterMode.Poi:
            self.__load_poi()
        elif filter_mode == FilterMode.Intelligence:
            self.__load_region_file()

    def _fill_arguments(self, parser):
        super()._fill_arguments(parser)
        parser.add_argument(
            '--back-db-path',
            dest='back_db_path',
            help='背景文件路径或者文件夹路径。',
            type=str,
            required=False,
        )
        parser.add_argument(
            '--road-db-path',
            dest='road_db_path',
            help='路网文件路径或者文件夹路径。',
            type=str,
            required=False,
        )
        parser.add_argument(
            '--poi-db-path',
            dest='poi_db_path',
            help='poi 文件路径或者文件夹路径。',
            type=str,
            required=False,
        )
        parser.add_argument(
            '--region-db-path',
            dest='region_db_path',
            help='街区文件路径或者文件夹路径。',
            type=str,
            required=False,
        )
        parser.add_argument(
            '--city',
            dest='city',
            help='设置数据集所在城市名。',
            type=str,
            default='',
            required=False,
        )
        parser.add_argument(
            '--data-types',
            dest='data_types',
            help='设置数据集需要产出的数据类型。（多种类型用竖线分隔）',
            type=str,
            default='poi|inner_road|outer_road|road_gate|water_area|label|image|raster',
            required=False,
        )

    def _fill_documents(self):
        self.documents.append(PoiDocument(self.poi_db_path))
        self.documents.append(BackDocument(self.back_db_path))
        self.documents.append(RoadDocument(self.road_db_path))
        self.documents.append(RegionDocument(self.region_db_path))

        for document in self.documents:
            document.load()

        self.__build_layer_relations()

    def _fill_providers(self):
        factory = DataProviderFactory(self)
        self.providers.extend(factory.create_providers(self.data_types))

    def __build_layer_relations(self):
        """建立各层之间的关联关系"""

        # blu_face 的 tag 依赖 poi.std_tag
        poi_layer = self.get_layer("poi", "poi")
        if poi_layer is not None:
            for blu_face in self.get_features("back", "blu_face"):
                poi = poi_layer.get_poi(blu_face.poi_mid)
                if poi is not None:
                    blu_face.tag = poi.std_tag

    def __load_poi(self):
        poi_layer = self.get_layer("poi", "poi")
        if poi_layer is None:
            return

        poi_count = 0
        for poi in poi_layer.features:
            x_min, x_max, y_min, y_max = poi.geom.GetEnvelope()
            self.rtree.insert(poi_count, (x_min, x_max, y_min, y_max))
            self.geometries.append(poi.geom)
            poi_count += 1

    def __load_region_file(self):
        with open(self.region_file_path, "r", encoding="utf8") as f:
            line_count = 0
            for line in f.readlines():
                wkt = line.strip()
                if wkt == '':
                    continue

                geometry = ogr.CreateGeometryFromWkt(wkt)
                if geometry is None:
                    continue

                x_min, x_max, y_min, y_max = geometry.GetEnvelope()
                self.rtree.insert(line_count, (x_min, x_max, y_min, y_max))
                self.geometries.append(geometry)
                line_count += 1


if __name__ == '__main__':
    SemanticDataset().create()
