# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""用于产出数据集"""
import os
import shutil
from abc import abstractmethod
from os import path
from typing import List

import jsonpickle

from src.pre_process.documents import PoiLevels
from src.pre_process.raster_provider import Bounds, RegionInfo, RasterHelper


class DatasetMode:
    Training = "training"
    Product = "product"


class FilterMode:
    Intelligence = 'intelligence'
    Poi = 'poi'
    Full = 'full'


class WaterAreaInfoItem:
    def __init__(self):
        self.id = ""
        self.kind = ""
        self.geom = ""


class WaterAreaInfo:
    def __init__(self):
        self.id = ""
        self.water_list: List[WaterAreaInfoItem] = []


class RoadGateInfoItem:
    def __init__(self):
        self.id = ""
        self.access = ""
        self.geom = ""
        self.road_node_in = ""
        self.road_node_out = ""


class RoadGateInfo:
    def __init__(self):
        self.id = ""
        self.gate_list: List[RoadGateInfoItem] = []


class RoadAccessTypes:
    In = "in"
    Out = "out"
    InOut = "in-out"


class PoiInfoItem:
    def __init__(self):
        self.id = ""
        self.name = ""
        self.address = ""
        self.level = 0
        self.tag = ""
        self.geom = ""
        self.children: List[PoiInfoItem] = []


class PoiInfo:
    def __init__(self):
        self.id = ""
        self.poi_list: List[PoiInfoItem] = []


class OuterRoadInfoItem:
    def __init__(self):
        self.id = ""
        self.level = ""
        self.geom = ""


class OuterRoadInfo:
    def __init__(self):
        self.id = ""
        self.road_list: List[OuterRoadInfoItem] = []


class InnerRoadInfoItem:
    def __init__(self):
        self.id = ""
        self.geom = ""


class InnerRoadInfo:
    def __init__(self):
        self.id = ""
        self.road_list: List[InnerRoadInfoItem] = []


class LabelInfoItem:
    def __init__(self):
        self.id = ""
        self.name = ""
        self.tag = ""
        self.contained = False
        self.geom = ""


class LabelInfo:
    def __init__(self):
        self.id = ""
        self.aoi_list: List[LabelInfoItem] = []


class ImageInfo:
    def __init__(self):
        self.id = ""
        self.width = 0
        self.height = 0
        self.region = Bounds(0.0, 0.0, 0.0, 0.0)
        self.city = ""
        self.geom = ""
        self.wgs_geom = ""


class DataProvider:
    def __init__(self, name, file_name, dataset):
        self.name = name
        self.file_name = file_name
        self.dataset = dataset
        self.save_folder_path = ''

    def ready_data(self):
        if self.file_name != "":
            self.save_folder_path = path.join(self.dataset.output_path, self.file_name)

            if path.exists(self.save_folder_path):
                shutil.rmtree(self.save_folder_path)
            os.makedirs(self.save_folder_path)

    def can_output(self, region_info: RegionInfo):
        return True

    @abstractmethod
    def output(self, region_info: RegionInfo):
        pass


class WaterAreaJsonProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('water_area', r"json_info/water_area", dataset)

    def output(self, region_info: RegionInfo):
        blc_face_layer = self.dataset.get_layer("back", "blc_face")
        if blc_face_layer is None:
            return

        water_area_info = WaterAreaInfo()
        water_area_info.id = region_info.id

        for water_area in blc_face_layer.get_features_in_coordinates(region_info.gcj_expanded_geom.GetEnvelope()):
            if not water_area.is_water_area():
                continue

            water_area_info_item = WaterAreaInfoItem()
            water_area_info_item.id = water_area.id
            water_area_info_item.kind = int(water_area.kind)
            water_area_info_item.geom = water_area.geom.ExportToWkt()
            water_area_info.water_list.append(water_area_info_item)

        DataProviderHelper.output_json(self.save_folder_path, region_info.id, water_area_info)


class PoiJsonProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('poi', r"json_info/poi", dataset)

    def output(self, region_info: RegionInfo):
        def get_poi_basic_info(parent_poi, child_pois):
            poi_info_item = PoiInfoItem()
            poi_info_item.id = parent_poi.id
            poi_info_item.name = parent_poi.name
            poi_info_item.address = parent_poi.address
            poi_info_item.geom = parent_poi.geom.ExportToWkt()
            poi_info_item.level = parent_poi.level
            poi_info_item.tag = parent_poi.std_tag

            # 通过父点找子点
            valid_child_pois = list(filter(lambda child_poi: (child_poi.relation == parent_poi.id and
                                                              parent_poi.relation != child_poi.id), child_pois))
            # 提取多级父子关系
            poi_info_item.children = [get_poi_basic_info(valid_child_poi, child_pois)
                                      for valid_child_poi in valid_child_pois]

            return poi_info_item

        poi_layer = self.dataset.get_layer("poi", "poi")
        if poi_layer is None:
            return

        all_pois = list(poi_layer.get_features_in_coordinates(region_info.gcj_expanded_geom.GetEnvelope()))
        primary_pois = list(filter(lambda poi: poi.level == PoiLevels.Primary, all_pois))
        minor_pois = list(filter(lambda poi: poi.level == PoiLevels.Minor, all_pois))

        poi_info = PoiInfo()
        poi_info.id = region_info.id
        poi_info.poi_list = [get_poi_basic_info(primary_poi, minor_pois) for primary_poi in primary_pois]

        DataProviderHelper.output_json(self.save_folder_path, region_info.id, poi_info)


class InnerRoadJsonProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('inner_road', r"json_info/inner_road", dataset)

    def output(self, region_info: RegionInfo):
        nav_link_layer = self.dataset.get_layer("road", "nav_link")
        if nav_link_layer is None:
            return

        inner_road_info = InnerRoadInfo()
        inner_road_info.id = region_info.id

        for nav_link in nav_link_layer.get_features_in_coordinates(region_info.gcj_expanded_geom.GetEnvelope()):
            if nav_link.is_outer():
                continue

            inner_road_info_item = InnerRoadInfoItem()
            inner_road_info_item.id = nav_link.id
            inner_road_info_item.geom = nav_link.geom.ExportToWkt()
            inner_road_info.road_list.append(inner_road_info_item)

        DataProviderHelper.output_json(self.save_folder_path, region_info.id, inner_road_info)


class OuterRoadJsonProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('outer_road', r"json_info/outer_road", dataset)

    def output(self, region_info: RegionInfo):
        nav_link_layer = self.dataset.get_layer("road", "nav_link")
        if nav_link_layer is None:
            return

        outer_road_info = OuterRoadInfo()
        outer_road_info.id = region_info.id

        for nav_link in nav_link_layer.get_features_in_coordinates(region_info.gcj_expanded_geom.GetEnvelope()):
            if not nav_link.is_outer():
                continue

            outer_road_info_item = OuterRoadInfoItem()
            outer_road_info_item.id = nav_link.id
            outer_road_info_item.level = int(nav_link.kind)
            outer_road_info_item.geom = nav_link.geom.ExportToWkt()
            outer_road_info.road_list.append(outer_road_info_item)

        DataProviderHelper.output_json(self.save_folder_path, region_info.id, outer_road_info)


class RoadGateJsonProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('road_gate', r"json_info/road_gate", dataset)

    def output(self, region_info: RegionInfo):
        nav_gate_layer = self.dataset.get_layer("road", "nav_gate")
        nav_link_layer = self.dataset.get_layer("road", "nav_link")
        nav_node_layer = self.dataset.get_layer("road", "nav_node")
        if nav_gate_layer is None or nav_link_layer is None or nav_node_layer is None:
            return

        def get_road_node(link_id, node_id):
            link = nav_link_layer.get_link(link_id)
            if link is not None:
                x, y, _ = link.geom.GetPoint(link.geom.GetPointCount() - 2) \
                    if link.e_nid == node_id else link.geom.GetPoint(1)

                return f'POINT ({x} {y})'

            return ""

        road_gate_info = RoadGateInfo()
        road_gate_info.id = region_info.id

        for nav_node in nav_node_layer.get_features_in_coordinates(region_info.gcj_expanded_geom.GetEnvelope()):
            gates = nav_gate_layer.get_gates_by_node_id(nav_node.id)
            if gates is None or not any(gates):
                continue

            road_gate_info_item = RoadGateInfoItem()
            road_gate_info_item.id = nav_node.id
            road_gate_info_item.geom = nav_node.geom.ExportToWkt()
            road_gate_info_item.road_node_in = get_road_node(gates[0].in_link_id, nav_node.id)
            road_gate_info_item.road_node_out = get_road_node(gates[0].out_link_id, nav_node.id)
            road_gate_info.gate_list.append(road_gate_info_item)

        DataProviderHelper.output_json(self.save_folder_path, region_info.id, road_gate_info)


class LabelJsonProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('label', r"json_info/label", dataset)

    def can_output(self, region_info: RegionInfo):
        if self.dataset.mode.lower() == DatasetMode.Product:
            return True

        blu_face_layer = self.dataset.get_layer("back", "blu_face")
        if blu_face_layer is None:
            return False

        # 街区范围内必须有 aoi
        inner_blu_faces = blu_face_layer.get_features_in_geometry(region_info.gcj_geom)
        if not any(inner_blu_faces):
            return False

        return True

    def output(self, region_info: RegionInfo):
        label_info = LabelInfo()
        label_info.id = region_info.id

        blu_face_layer = self.dataset.get_layer("back", "blu_face")
        poi_layer = self.dataset.get_layer("poi", "poi")

        if self.dataset.mode.lower() == DatasetMode.Training:
            if blu_face_layer is not None and poi_layer is not None:
                blu_faces = blu_face_layer.get_features_in_coordinates(region_info.gcj_expanded_geom.GetEnvelope())
                for blu_face in blu_faces:
                    label_info_item = LabelInfoItem()
                    label_info_item.id = blu_face.id
                    label_info_item.name = blu_face.name
                    label_info_item.geom = blu_face.geom.ExportToWkt()
                    label_info_item.std_tag = blu_face.tag
                    label_info_item.contained = region_info.gcj_geom.Contains(blu_face.geom)
                    label_info.aoi_list.append(label_info_item)

        DataProviderHelper.output_json(self.save_folder_path, region_info.id, label_info)


class ImageJsonProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('image', r"json_info/image", dataset)

    def output(self, region_info: RegionInfo):
        image_info = ImageInfo()
        image_info.id = region_info.id
        image_info.city = self.dataset.city

        if self.dataset.fixed_side_length > 0:
            image_info.width = self.dataset.fixed_side_length
            image_info.height = self.dataset.fixed_side_length

            left = (region_info.pixel_size.width - image_info.width) * 0.5
            top = (region_info.pixel_size.height - image_info.height) * 0.5
            right = left + image_info.width
            bottom = top + image_info.height

            x_min, x_max, y_min, y_max = region_info.gcj_expanded_geom.GetEnvelope()
            image_info.region = Bounds(
                left=x_min + self.dataset.raster_provider.get_coord_length(left),
                top=y_max - self.dataset.raster_provider.get_coord_length(top),
                right=x_min + self.dataset.raster_provider.get_coord_length(right),
                bottom=y_max - self.dataset.raster_provider.get_coord_length(bottom),
            )
            image_info.geom = RasterHelper.convert_to_geometry(image_info.region).ExportToWkt()
        elif self.dataset.clip:
            image_info.width = min(region_info.pixel_size.width, self.dataset.max_side_length)
            image_info.height = min(region_info.pixel_size.height, self.dataset.max_side_length)

            left = (region_info.pixel_size.width - image_info.width) * 0.5
            top = (region_info.pixel_size.height - image_info.height) * 0.5
            right = left + image_info.width
            bottom = top + image_info.height

            x_min, x_max, y_min, y_max = region_info.gcj_expanded_geom.GetEnvelope()
            image_info.region = Bounds(
                left=x_min + self.dataset.raster_provider.get_coord_length(left),
                top=y_max - self.dataset.raster_provider.get_coord_length(top),
                right=x_min + self.dataset.raster_provider.get_coord_length(right),
                bottom=y_max - self.dataset.raster_provider.get_coord_length(bottom),
            )
            image_info.geom = RasterHelper.convert_to_geometry(image_info.region).ExportToWkt()
        else:
            image_info.width = region_info.pixel_size.width
            image_info.height = region_info.pixel_size.height
            image_info.geom = region_info.gcj_geom.ExportToWkt()

            x_min, x_max, y_min, y_max = region_info.gcj_expanded_geom.GetEnvelope()
            image_info.region = Bounds(
                left=x_min,
                top=y_max,
                right=x_max,
                bottom=y_min
            )

        wgs_bounds = RasterHelper.get_wgs_bounds(region_info.gcj_expanded_geom)
        image_info.wgs_geom = RasterHelper.convert_to_geometry(wgs_bounds).ExportToWkt()
        DataProviderHelper.output_json(self.save_folder_path, region_info.id, image_info)


class ImageProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('raster', "images", dataset)

    def can_output(self, region_info: RegionInfo):
        if not self.dataset.clip and self.dataset.max_side_length >= 0:
            valid_size = (self.dataset.max_side_length >= region_info.pixel_size.height and
                          self.dataset.max_side_length >= region_info.pixel_size.width)

            if not valid_size:
                return False

        # noinspection PyBroadException
        try:
            self.dataset.raster_provider.download_rasters(region_info)
            return True
        except Exception as e:
            print(e)
            return False

    def output(self, region_info: RegionInfo):
        region_image = self.dataset.raster_provider.combine_rasters(region_info)

        if self.dataset.fixed_side_length > 0:
            left = (region_info.pixel_size.width - self.dataset.fixed_side_length) * 0.5
            top = (region_info.pixel_size.height - self.dataset.fixed_side_length) * 0.5
            right = left + self.dataset.fixed_side_length
            bottom = top + self.dataset.fixed_side_length
            region_image = region_image.crop((left, top, right, bottom))
        elif self.dataset.clip:
            width = min(region_info.pixel_size.width, self.dataset.max_side_length)
            height = min(region_info.pixel_size.height, self.dataset.max_side_length)
            left = (region_info.pixel_size.width - width) * 0.5
            top = (region_info.pixel_size.height - height) * 0.5
            right = left + width
            bottom = top + height
            region_image = region_image.crop((left, top, right, bottom))

        region_image.save(path.join(self.save_folder_path, f'{region_info.id}.jpg'), "JPEG")


class IntelligenceDataProvider(DataProvider):
    def __init__(self, dataset):
        super().__init__('', '', dataset)

    def can_output(self, region_info: RegionInfo):
        # 在街区范围内找情报点，如果找不到则不需要输出
        x_min, x_max, y_min, y_max = region_info.gcj_geom.GetEnvelope()
        for index in self.dataset.rtree.intersection((x_min, x_max, y_min, y_max)):
            geometry = self.dataset.geometries[index]
            if region_info.gcj_geom.Contains(geometry):
                return True

        return False

    def output(self, region_info: RegionInfo):
        pass


class DataProviderFactory:
    def __init__(self, dataset):
        self.dataset = dataset

        providers: List[DataProvider] = [
            LabelJsonProvider(dataset),
            PoiJsonProvider(dataset),
            WaterAreaJsonProvider(dataset),
            InnerRoadJsonProvider(dataset),
            OuterRoadJsonProvider(dataset),
            RoadGateJsonProvider(dataset),
            ImageJsonProvider(dataset),
            ImageProvider(dataset),
        ]

        self.providers_dict = {}
        for provider in providers:
            self.providers_dict[provider.name] = provider

    def create_provider(self, data_type: str):
        return self.providers_dict.get(data_type, None)

    def create_providers(self, data_types: str):
        if self.dataset.filter_mode.lower() != FilterMode.Full:
            yield IntelligenceDataProvider(self.dataset)

        for data_type in data_types.split('|'):
            provider = self.create_provider(data_type)
            if provider is None:
                continue
            provider.ready_data()
            yield provider


class DataProviderHelper:
    SupportedTypes = set('label|inner_road|outer_road|road_gate|water_area|poi'.split('|'))

    @staticmethod
    def to_json(save_object):
        return jsonpickle.encode(save_object, unpicklable=False, separators=(',', ':'))

    @staticmethod
    def output_json(save_folder_path, json_name, save_object):
        with open(path.join(save_folder_path, f'{json_name}.json'), 'w', encoding='utf8') as f:
            f.write(DataProviderHelper.to_json(save_object))

    @staticmethod
    def get_valid_data_types(full_data_types: str):
        return '|'.join(set(full_data_types.split('|')).intersection(DataProviderHelper.SupportedTypes))
