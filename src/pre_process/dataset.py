# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""用于存储数据集信息"""
import argparse
import os
import uuid
from abc import abstractmethod
from os import path
from typing import List

import jsonpickle
from osgeo import ogr
from tqdm import tqdm

from src.pre_process.data_provider import RegionInfo, DatasetMode, DataProvider, FilterMode
from src.pre_process.documents import Document
from src.pre_process.raster_provider import <PERSON><PERSON><PERSON><PERSON>, RasterProviderFactory, Bounds


class Dataset:
    def __init__(self):
        # 用于解决 json 乱码
        jsonpickle.set_preferred_backend('json')
        jsonpickle.set_encoder_options('json', ensure_ascii=False)

        self.providers: List[DataProvider] = []
        self.documents: List[Document] = []

        parser = argparse.ArgumentParser()
        self._fill_arguments(parser)
        args = parser.parse_args()
        self.args = args
        self.id = args.id
        self.padding = args.padding
        self.output_path = f'{args.output_path}/{args.id}'
        self.root_path = args.output_path
        self.max_side_length = args.max_side_length
        self.fixed_side_length = self.args.fixed_side_length
        self.end = args.end
        self.ignore_err = args.ignore_err
        self.start = args.start
        self.draw_image = self.args.draw_image
        self.mode = self.args.mode
        self.filter_mode = self.args.filter_mode
        self.large_mode = False
        self.region_file_path = self.args.region_file_path
        self.raster_provider = RasterProviderFactory(args).create()
        self.clip = self.args.clip

        if self.output_path is not None and not path.exists(self.output_path):
            os.makedirs(self.output_path)

    def create(self):
        self._load_data()
        self._output()

    def get_document(self, document_name: str):
        for document in self.documents:
            if document.name == document_name:
                return document

        return None

    def get_layer(self, document_name: str, layer_name: str):
        document = self.get_document(document_name)
        if document is None:
            return None

        return document.get_layer(layer_name)

    def get_features(self, document_name: str, layer_name: str):
        layer = self.get_layer(document_name, layer_name)
        if layer is None:
            return

        yield from layer.features

    def resize_region(self, gcj_geom: ogr.Geometry):
        # 用于解决精度误差，比如像素 2048 转为经纬度后不可能刚好是整数倍张瓦片图，可以外扩 10 像素，
        # 尽量下全瓦片图，然后用固定范围去截取就能保证拼合后的大图长宽全都一致
        interval = 10

        if self.fixed_side_length > 0:
            gcj_left, gcj_right, gcj_bottom, gcj_top = gcj_geom.GetEnvelope()
            center = Bounds(gcj_left, gcj_top, gcj_right, gcj_bottom).get_center()
            coord_length = self.raster_provider.get_coord_length(self.fixed_side_length + interval)
            fixed_bounds = Bounds.create(center, coord_length)
            return RasterHelper.convert_to_geometry(fixed_bounds)
        elif self.clip:
            gcj_left, gcj_right, gcj_bottom, gcj_top = gcj_geom.GetEnvelope()
            center = Bounds(gcj_left, gcj_top, gcj_right, gcj_bottom).get_center()

            # 如果长宽大于最大值，索性再外扩 10 像素，原理同 fixed_side_length > 0 时的情况。
            coord_width = gcj_right - gcj_left
            coord_height = gcj_top - gcj_bottom
            max_coord_length = self.raster_provider.get_coord_length(self.max_side_length)
            buffered_max_coord_length = self.raster_provider.get_coord_length(self.max_side_length + interval)
            overstep_width = coord_width > max_coord_length
            overstep_height = coord_height > max_coord_length
            coord_width = buffered_max_coord_length if overstep_width else coord_width
            coord_height = buffered_max_coord_length if overstep_height else coord_height

            fixed_bounds = Bounds(
                left=center.x - coord_width * 0.5,
                top=center.y + coord_height * 0.5,
                right=center.x + coord_width * 0.5,
                bottom=center.y - coord_height * 0.5
            )
            return RasterHelper.convert_to_geometry(fixed_bounds)
        else:
            return gcj_geom

    def _fill_arguments(self, parser):
        parser.add_argument(
            '--raster-host',
            dest='raster_host',
            help='影像获取地址。',
            type=str,
            required=False,
        )
        parser.add_argument(
            '--raster-type',
            dest='raster_type',
            help='影像类型。',
            type=str,
            default='Google2022',
            required=False,
        )
        parser.add_argument(
            '--raster-year',
            dest='raster_year',
            help='影像年份。',
            type=int,
            default=2022,
            required=False,
        )
        parser.add_argument(
            '--raster-src',
            dest='raster_src',
            help='影像源。',
            type=str,
            default='afs',
            required=False,
        )
        parser.add_argument(
            '--id',
            dest='id',
            help='数据集 id。',
            type=str,
            default=f'dataset_{uuid.uuid4().hex}',
            required=False,
        )
        parser.add_argument(
            '--start',
            dest='start',
            help='指定街区产出起始位置。',
            type=int,
            default=0,
            required=False,
        )
        parser.add_argument(
            '--end',
            dest='end',
            help='指定街区产出结束位置。',
            type=int,
            required=False,
        )
        parser.add_argument(
            '--padding',
            dest='padding',
            help='街区外扩距离（单位：米）。',
            type=float,
            default=0,
            required=False,
        )
        parser.add_argument(
            '--level',
            dest='level',
            help='指定需要下载的影像级别。',
            type=int,
            default=19,
            required=False,
        )
        parser.add_argument(
            '--ignore-err',
            dest='ignore_err',
            help='是否忽略错误。',
            type=int,
            default=0,
            required=False,
        )
        parser.add_argument(
            '--cache-path',
            dest='cache_path',
            help='指定缓存目录。',
            type=str,
            required=False,
        )
        parser.add_argument(
            '--output-path',
            dest='output_path',
            help='指定数据输出目录。',
            type=str,
            required=True,
        )
        parser.add_argument(
            '--max-side-length',
            dest='max_side_length',
            help='边长大于此值的图片将被忽略。',
            type=int,
            default=-1,
            required=False,
        )
        parser.add_argument(
            '--fixed-side-length',
            dest='fixed_side_length',
            help='将产出图片固定为此大小。',
            type=int,
            default=-1,
            required=False,
        )
        parser.add_argument(
            '--draw-image',
            dest='draw_image',
            help='是否需要绘制图片。',
            default=True,
            action='store_true',
        )
        parser.add_argument(
            '--no-draw-image',
            dest='draw_image',
            help='是否需要绘制图片。',
            action='store_false',
        )
        parser.add_argument(
            '--mode',
            dest='mode',
            help='数据集产出模式。',
            type=str,
            default=DatasetMode.Training,
            required=False,
        )
        parser.add_argument(
            '--filter-mode',
            dest='filter_mode',
            help='数据集过滤模式。',
            type=str,
            default=FilterMode.Intelligence,
            required=False,
        )
        parser.add_argument(
            '--region-file-path',
            dest='region_file_path',
            help='语义识别范围定义文件路径。',
            type=str,
            required=False,
        )
        parser.add_argument(
            '--clip',
            dest='clip',
            help='当图片尺寸过大时是否需要裁剪。',
            default=True,
            action='store_true',
        )
        parser.add_argument(
            '--no-clip',
            dest='clip',
            help='当图片尺寸过大时是否需要裁剪。',
            action='store_false',
        )

    def _load_data(self):
        self._fill_documents()
        self._fill_providers()
        self.region_items = self._get_region_items()[self.start:self.end]

    def _fill_documents(self):
        pass

    @abstractmethod
    def _fill_providers(self):
        pass

    @abstractmethod
    def _get_region_items(self) -> []:
        pass

    def _output(self):
        for region in tqdm(self.region_items, total=len(self.region_items), desc='outputting regions'):
            if self.ignore_err != 0:
                try:
                    region_info = self.__create_region_info(region)
                    if all(item.can_output(region_info) for item in self.providers):
                        for provider in self.providers:
                            provider.output(region_info)
                except Exception as e:
                    print(f'error occurred when processing, ignore ... : {e.args}')
            else:
                region_info = self.__create_region_info(region)
                if all(item.can_output(region_info) for item in self.providers):
                    for provider in self.providers:
                        provider.output(region_info)

    def __create_region_info(self, region_item):
        region_info_id, gcj_geom, city = region_item
        # 小数点后第 5 位对应米。
        gcj_expanded_geom: ogr.Geometry = gcj_geom.Buffer(self.padding * 1e-5)
        wgs_bounds = RasterHelper.get_wgs_bounds(gcj_expanded_geom)

        region_info = RegionInfo()
        region_info.id = region_info_id
        region_info.city = city
        region_info.gcj_geom = gcj_geom
        region_info.gcj_expanded_geom = gcj_expanded_geom
        region_info.pixel_size = self.raster_provider.get_region_pixel_size(wgs_bounds)
        region_info.rasters = list(self.raster_provider.create_rasters(wgs_bounds))

        return region_info
