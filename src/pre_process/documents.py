# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""用于存储地图数据"""
import glob
import sqlite3
from abc import abstractmethod
from enum import IntFlag
from os import path

import rtree
from osgeo import ogr
from tqdm import tqdm


class PoiLevels:
    Primary = 1
    Minor = 2


class NavLinkKind(IntFlag):
    Working = 0
    HighSpeed = 1
    CityHighSpeed = 2
    National = 3
    Province = 4
    County = 6
    Village = 7
    Other = 8
    Special = 9
    Walking = 10
    FerryboatForPerson = 11
    FerryboatForCar = 11


class WaterAreaKind(IntFlag):
    Sea = 1 << 1
    River = 1 << 2
    Lake = 1 << 3
    Reservoir = 1 << 4
    Harbour = 1 << 5
    Canal = 1 << 6


class RoofStyle:
    # 未调查
    Unknown = 0
    # 平屋顶
    Even = 1
    # 女儿墙平屋顶
    Wall = 2
    # 单坡顶
    SingleRoof = 3
    # 两坡顶
    DoubleRoof = 4
    # 四坡顶
    HippedRoof = 5
    # 锥屋顶
    ConicalRoof = 6
    # 拱屋顶
    ArchRoof = 7
    # 其它
    Other = 99


class BluFace:
    def __init__(self, face_id, poi_mid, name, wkt):
        self.id = face_id
        self.poi_mid = poi_mid
        self.name = name
        self.tag = ""
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)


class BlcFace:
    water_area_kinds = \
        WaterAreaKind.Sea | \
        WaterAreaKind.River | \
        WaterAreaKind.Lake | \
        WaterAreaKind.Reservoir | \
        WaterAreaKind.Harbour | \
        WaterAreaKind.Canal

    def __init__(self, face_id, kind, wkt):
        self.id = face_id
        self.kind = WaterAreaKind(int(kind))
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)

    def is_water_area(self):
        return self.kind != 0 and (BlcFace.water_area_kinds & self.kind == self.kind)


class BudFace:
    roof_style_text_dict = {
        # 未调查也视为其它。
        RoofStyle.Unknown: "其它",
        RoofStyle.Even: "平屋顶",
        RoofStyle.Wall: "女儿墙平屋顶",
        RoofStyle.SingleRoof: "单坡顶",
        RoofStyle.DoubleRoof: "两坡顶",
        RoofStyle.HippedRoof: "四坡顶",
        RoofStyle.ConicalRoof: "锥屋顶",
        RoofStyle.ArchRoof: "拱屋顶",
        RoofStyle.Other: "其它",
    }

    roof_style_value_dict = {
        # 未调查也视为其它。
        RoofStyle.Unknown: "9",
        RoofStyle.Even: "2",
        RoofStyle.Wall: "3",
        RoofStyle.SingleRoof: "4",
        RoofStyle.DoubleRoof: "5",
        RoofStyle.HippedRoof: "6",
        RoofStyle.ConicalRoof: "7",
        RoofStyle.ArchRoof: "8",
        RoofStyle.Other: "9",
    }

    def __init__(self, face_id, roof_style, wkt):
        self.id = face_id
        self.roof_style = roof_style
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)

    def get_roof_style_value(self):
        return self.roof_style_value_dict[self.roof_style]

    def get_roof_style_text(self):
        return self.roof_style_text_dict[self.roof_style]


class NavLink:
    outer_road_kinds = \
        NavLinkKind.HighSpeed | \
        NavLinkKind.CityHighSpeed | \
        NavLinkKind.National | \
        NavLinkKind.Province | \
        NavLinkKind.County | \
        NavLinkKind.Village

    def __init__(self, link_id, s_nid, e_nid, kind, wkt):
        self.id = link_id
        self.s_nid = s_nid
        self.e_nid = e_nid
        self.kind = NavLinkKind(int(kind))
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)

    def is_outer(self):
        return NavLink.outer_road_kinds & self.kind == self.kind


class NavGate:
    def __init__(self, gate_id, node_id, in_link_id, out_link_id, wkt):
        self.id = gate_id
        self.node_id = node_id
        self.in_link_id = in_link_id
        self.out_link_id = out_link_id
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)


class NavNode:
    def __init__(self, node_id, wkt):
        self.id = node_id
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)


class Poi:
    def __init__(self, mid, name, address, relation, std_tag, wkt):
        self.id = mid
        self.name = name
        self.address = address
        self.relation = relation
        self.std_tag = std_tag
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)
        self.level = PoiLevels.Primary if self.relation == "" else PoiLevels.Minor


class RegionFace:
    def __init__(self, face_id, wkt):
        self.id = face_id
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)


class FeatureLayer:
    def __init__(self, name: str):
        self.name = name
        self.rtree = rtree.index.Index(interleaved=False)
        self.features = []
        self.record_count = 0

    def load(self, cur):
        # noinspection PyBroadException
        try:
            res = cur.execute(self._get_query_sql())
            records = list(res.fetchall())

            for record in records:
                feature = self._create_feature(record)
                x_min, x_max, y_min, y_max = feature.geom.GetEnvelope()
                self.rtree.insert(self.record_count, (x_min, x_max, y_min, y_max))
                self.features.append(feature)
                self.record_count += 1
        except Exception:
            pass

    @abstractmethod
    def _get_query_sql(self) -> str:
        pass

    @abstractmethod
    def _create_feature(self, record):
        pass

    def get_features_in_coordinates(self, coordinates):
        x_min, x_max, y_min, y_max = coordinates
        for index in self.rtree.intersection((x_min, x_max, y_min, y_max)):
            yield self.features[index]

    def get_features_in_geometry(self, polygon: ogr.Geometry):
        if polygon.GetGeometryType() != ogr.wkbPolygon:
            return

        for feature in self.get_features_in_coordinates(polygon.GetEnvelope()):
            if polygon.Contains(feature.geom):
                yield feature


class BluFaceLayer(FeatureLayer):
    def __init__(self):
        super().__init__("blu_face")

    def _get_query_sql(self) -> str:
        return "SELECT face.face_id, poi.poi_id, face.name_ch, face.geom " \
               "FROM blu_face face, blu_face_poi poi " \
               "WHERE face.face_id = poi.face_id"

    def _create_feature(self, record):
        return BluFace(face_id=record[0], poi_mid=record[1], name=record[2], wkt=record[3])


class BlcFaceLayer(FeatureLayer):
    def __init__(self):
        super().__init__("blc_face")

    def _get_query_sql(self) -> str:
        return "SELECT face_id, kind, geom FROM blc_face"

    def _create_feature(self, record):
        return BlcFace(face_id=record[0], kind=record[1], wkt=record[2])


class BudFaceLayer(FeatureLayer):
    def __init__(self):
        super().__init__("bud_face")

    def _get_query_sql(self) -> str:
        return "SELECT face_id, roof_style, geom FROM bud_face"

    def _create_feature(self, record):
        return BudFace(face_id=record[0], roof_style=record[1], wkt=record[2])


class NavLinkLayer(FeatureLayer):
    def __init__(self):
        super().__init__("nav_link")
        self.__feature_dict = {}

    def load(self, cur):
        super().load(cur)
        for feature in self.features:
            self.__feature_dict[feature.id] = feature

    def get_link(self, link_id):
        return self.__feature_dict.get(link_id, None)

    def _get_query_sql(self) -> str:
        return "SELECT link_id, s_nid, e_nid, kind, geom FROM nav_link"

    def _create_feature(self, record):
        return NavLink(link_id=record[0], s_nid=record[1], e_nid=record[2], kind=record[3], wkt=record[4])


class NavGateLayer(FeatureLayer):
    def __init__(self):
        super().__init__("nav_gate")
        self.__node_to_gates_dict = {}

    def load(self, cur):
        super().load(cur)
        for feature in self.features:
            gates = self.__node_to_gates_dict.get(feature.node_id, None)
            if gates is None:
                self.__node_to_gates_dict[feature.node_id] = []

            self.__node_to_gates_dict[feature.node_id].append(feature)

    def get_gates_by_node_id(self, node_id):
        return self.__node_to_gates_dict.get(node_id, None)

    def _get_query_sql(self) -> str:
        return "SELECT gate.gate_id, gate.node_id, gate.in_linkid, gate.out_linkid, node.geom " \
               "FROM nav_gate gate, nav_node node " \
               "WHERE gate.node_id = node.node_id"

    def _create_feature(self, record):
        return NavGate(gate_id=record[0], node_id=record[1], in_link_id=record[2], out_link_id=record[3], wkt=record[4])


class NavNodeLayer(FeatureLayer):
    def __init__(self):
        super().__init__("nav_node")

    def _get_query_sql(self) -> str:
        return "SELECT node_id, geom FROM nav_node"

    def _create_feature(self, record):
        return NavNode(node_id=record[0], wkt=record[1])


class PoiLayer(FeatureLayer):
    def __init__(self):
        super().__init__("poi")
        self.__feature_dict = {}

    def load(self, cur):
        super().load(cur)
        for feature in self.features:
            self.__feature_dict[feature.id] = feature

    def get_poi(self, poi_id):
        return self.__feature_dict.get(poi_id, None)

    def _get_query_sql(self) -> str:
        return "SELECT mid, name, address, relation, std_tag, geom " \
               "FROM poi"

    def _create_feature(self, record):
        return Poi(
            mid=record[0],
            name=record[1],
            address=record[2],
            relation=record[3],
            std_tag=record[4],
            wkt=record[5],
        )


class RegionFaceLayer(FeatureLayer):
    def __init__(self):
        super().__init__("region_face")

    def _get_query_sql(self) -> str:
        return "SELECT face_id, geom FROM region_face"

    def _create_feature(self, record):
        return RegionFace(face_id=record[0], wkt=record[1])


class Document:
    def __init__(self, name: str, document_path: str):
        self.name = name

        if document_path is not None:
            self.document_paths = [document_path] \
                if path.exists(document_path) and path.isfile(document_path) \
                else glob.glob(fr"{document_path}/*{self._get_extension()}")
        else:
            self.document_paths = []

        self.layers = self._create_layers()

    def load(self):
        for document_path in tqdm(
            iterable=self.document_paths,
            total=len(self.document_paths),
            desc=f"loading {self.name} document"
        ):
            con = sqlite3.connect(document_path)
            cur = con.cursor()

            for layer in self.layers:
                layer.load(cur)

            con.close()

    @abstractmethod
    def _get_extension(self) -> str:
        pass

    @abstractmethod
    def _create_layers(self):
        pass

    def get_layer(self, layer_name: str):
        for layer in self.layers:
            if layer.name == layer_name:
                return layer

        return None


class BackDocument(Document):
    def __init__(self, document_path: str):
        super().__init__("back", document_path)

    def _get_extension(self):
        return ".ttfb"

    def _create_layers(self):
        return [
            BlcFaceLayer(),
            BluFaceLayer(),
            BudFaceLayer(),
        ]


class RoadDocument(Document):
    def __init__(self, document_path: str):
        super().__init__("road", document_path)

    def _get_extension(self):
        return ".ttfa"

    def _create_layers(self):
        return [
            NavLinkLayer(),
            NavGateLayer(),
            NavNodeLayer()
        ]


class PoiDocument(Document):
    def __init__(self, document_path: str):
        super().__init__("poi", document_path)

    def _get_extension(self):
        return ".ttfc"

    def _create_layers(self):
        return [PoiLayer()]


class RegionDocument(Document):
    def __init__(self, document_path: str):
        super().__init__("region", document_path)

    def _get_extension(self):
        return ".ttfr"

    def _create_layers(self):
        return [RegionFaceLayer()]
