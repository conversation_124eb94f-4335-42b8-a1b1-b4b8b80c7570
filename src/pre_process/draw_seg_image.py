# !/usr/bin/env python3
"""
用于绘制语义分割模型所需的图片，包括：标注信息、多通道信息。
"""
import cv2
import argparse
import numpy as np
from typing import Tuple, Dict, List

from src.tools import utils
from src.tools import pipeline
from pathlib import Path
from osgeo import ogr
from tqdm import tqdm
from multiprocessing import Pool

TRUE_VALUE = 1


class LabelOptions:
    """
    绘制 label 时的配置选项
    """
    # 是否仅处理被街区几何体所包含的 AOI。
    only_contained: bool
    # 是否仅绘制 边框
    only_border: bool


class SegDrawingContext:
    """
    Context: 绘制语义分割模型所需图片时的上下文
    """
    width: int
    height: int
    # 坐标转换参数：与 gdal 中 SetGeoTransform() API 所用参数相同。此处转换方向是：经纬度 --> 像素坐标
    geo2pixel_transform: Tuple[float, float, float, float, float, float]
    pixel2geo_transform: Tuple[float, float, float, float, float, float]
    category_dict: Dict[str, int]
    json_info_dir: Path
    output_dir: Path
    # 当前被生成的图片名称（不含扩展名）
    image_stem: str
    output_image_dict: Dict[str, np.ndarray]
    label_options: LabelOptions


def parse_args():
    """
    解析命令函参数
    """
    parser = argparse.ArgumentParser('Split dataset')
    parser.add_argument(
        '--dataset-dir',
        dest='dataset_dir',
        help='The dir path of json_info.',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--dir-names',
        dest='dir_names',
        help='A name list of info-dir to be drawn.',
        type=str,
        # default='label',
        default='label|inner_road|outer_road|road_gate|water_area|poi',
    )
    parser.add_argument(
        '--tag-file',
        dest='tag_file',
        help='A name list of info-dir to be drawn.',
        type=str,
        default='./aoi_tag_29.txt',
    )
    parser.add_argument(
        '--only-contained',
        dest='only_contained',
        help='Indicates whether to draw only these aoi to label, which are fully contained within the street-block.',
        action='store_true',
        default=False,
    )
    parser.add_argument(
        '--only-border',
        dest='only_border',
        help='Indicates whether to draw only aoi-border to label.',
        action='store_true',
        default=False,
    )
    parser.add_argument(
        '--output-dir',
        dest='output_dir',
        help='The dir path of output images.',
        type=str,
        default=None,
    )
    parser.add_argument(
        '--process-number',
        dest='process_number',
        help='Number of concurrent processes.',
        type=int,
        default=8,
    )
    return parser.parse_args()


def get_file_path(ctx: SegDrawingContext, dir_name: str, ext_name='json'):
    """
    获取上下文中 json_info 文件夹下，指定文件夹中的文件路径。
    @param ctx: 当前上下文
    @param dir_name: 指定文件夹
    @param ext_name: 指定的扩展名
    @return: 文件路径
    """
    return ctx.json_info_dir / dir_name / f'{ctx.image_stem}.{ext_name}'


def get_output_dir(output_dir: Path, dir_name: str):
    """
    获取标准的输出子目录路径。
    @param output_dir: 输出目录
    @param dir_name: 输出子目录名称
    @return: 输出子目录路径
    """
    return output_dir / (f'aux_images_{dir_name}' if dir_name != 'label' else 'labels')


def create_empty_image(ctx: SegDrawingContext):
    return np.zeros((ctx.height, ctx.width), dtype=np.int8)


def parse_image_info(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 解析图片信息
    """
    image_info_path = get_file_path(ctx, 'image')
    image_info = utils.read_json(image_info_path)

    ctx.width = image_info['width']
    ctx.height = image_info['height']
    ctx.geo2pixel_transform = utils.get_transform_info_from_json(image_info)
    ctx.pixel2geo_transform = utils.get_transform_info_from_json(image_info, mode='pixel2geo')

    proceed()


def draw_seg_label(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 绘制语义分割的 Label 图片
    """
    BORDER_COLOR = 1
    BORDER_WIDTH = 10

    info = utils.read_json(get_file_path(ctx, 'label'))
    aoi_list = info['aoi_list']
    image = create_empty_image(ctx)
    for aoi in aoi_list:
        aoi_tag = aoi['tag']
        if not ctx.label_options.only_border and aoi_tag not in ctx.category_dict:
            continue

        if ctx.label_options.only_contained and not aoi['contained']:
            continue

        geom = ogr.CreateGeometryFromWkt(aoi['geom'])

        w1, h1 = utils.convert_coordinate((0, 0), ctx.pixel2geo_transform)
        w2, h2 = utils.convert_coordinate((ctx.width, ctx.height), ctx.pixel2geo_transform)
        area_aoi = geom.Area()
        area_image = (w2 - w1) * (h1 - h2)
        if area_aoi >= area_image:
            # 排除面积大于图片的 AOI，其将使整个图片被染成单色，对训练意义不大。
            continue

        points = utils.get_polygon_points(geom, ctx.geo2pixel_transform)
        if not ctx.label_options.only_border:
            channel = ctx.category_dict[aoi_tag]
            cv2.fillPoly(image, pts=[points], color=channel)

        cv2.polylines(image, pts=[points], isClosed=True, color=BORDER_COLOR, thickness=BORDER_WIDTH)

    ctx.output_image_dict['label'] = image
    proceed()


def draw_inner_road(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 绘制内部路
    """
    info = utils.read_json(get_file_path(ctx, 'inner_road'))
    road_list = info['road_list']
    image = create_empty_image(ctx)
    for road in road_list:
        geom = ogr.CreateGeometryFromWkt(road['geom'])
        points = utils.get_linestring_points(geom, ctx.geo2pixel_transform)
        cv2.polylines(image, pts=[points], isClosed=False, color=TRUE_VALUE, thickness=10)

    ctx.output_image_dict['inner_road'] = image
    proceed()


def draw_outer_road(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 绘制外部路
    """
    info = utils.read_json(get_file_path(ctx, 'outer_road'))
    road_list = info['road_list']
    image = create_empty_image(ctx)
    for road in road_list:
        level = int(road['level'])
        road_color = 7 - level + 100
        road_width = 7 - level + 2

        geom = ogr.CreateGeometryFromWkt(road['geom'])
        points = utils.get_linestring_points(geom, ctx.geo2pixel_transform)
        cv2.polylines(image, pts=[points], isClosed=False, color=road_color, thickness=road_width)

    ctx.output_image_dict['outer_road'] = image
    proceed()


def draw_water_area(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 绘制水系
    """
    info = utils.read_json(get_file_path(ctx, 'water_area'))
    road_list = info['water_list']
    image = create_empty_image(ctx)
    for road in road_list:
        geom = ogr.CreateGeometryFromWkt(road['geom'])
        points = utils.get_polygon_points(geom, ctx.geo2pixel_transform)
        cv2.fillPoly(image, pts=[points], color=TRUE_VALUE)

    ctx.output_image_dict['water_area'] = image
    proceed()


def draw_road_gate(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 绘制道路大门
    """
    gate_size = 10

    info = utils.read_json(get_file_path(ctx, 'road_gate'))
    gate_list = info['gate_list']
    image = create_empty_image(ctx)
    for gate in gate_list:
        road_in_point = utils.get_point(gate['road_node_in'], ctx.geo2pixel_transform)
        road_out_point = utils.get_point(gate['road_node_out'], ctx.geo2pixel_transform)
        gate_point = utils.get_point(gate['geom'], ctx.geo2pixel_transform)

        vector_in = utils.normalize(utils.get_vector(road_in_point, gate_point))
        vector_out = utils.normalize(utils.get_vector(road_out_point, gate_point))
        vector = np.add(vector_in, vector_out)
        vector = utils.normalize(vector) if np.sum(vector) > 1e-3 else [vector_in[1], -vector_in[0]]
        vector = [vector[0] * gate_size, vector[1] * gate_size]
        p_start = np.add(gate_point, vector)
        p_end = np.subtract(gate_point, vector)

        p_start = (round(p_start[0]), round(p_start[1]))
        p_end = (round(p_end[0]), round(p_end[1]))

        cv2.line(image, p_start, p_end, color=TRUE_VALUE, thickness=4)

    ctx.output_image_dict['road_gate'] = image
    proceed()


def draw_poi_cloud(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 绘制 POI，电子云版本。（非常慢，约1张/秒，且内存占用巨大，难以开多进程）
    """
    styles = {
        1: (0.25, 1.5),
        2: (0.2, 2.0)
    }
    info = utils.read_json(get_file_path(ctx, 'poi'))
    poi_list = info['poi_list']

    width = ctx.width
    height = ctx.height

    if len(poi_list) == 0:
        return np.zeros((height, width))

    a, b = (width / height, 1) if width > height else (1, height / width)
    x_axis = np.linspace(-1 * a, 1 * a, width)[np.newaxis, :]
    y_axis = np.linspace(-1 * b, 1 * b, height)[:, np.newaxis]

    def get_all_poi(node_list):
        """
        递归获取所有 POI 点。
        """
        for node in node_list:
            yield node
            yield from node['children']

    def draw_single_poi(center_x, center_y, distance_power):
        """
        绘制单个 POI
        """
        return np.power((x_axis - center_x) ** 2 + (y_axis - center_y) ** 2, 0.5 / distance_power)

    poi_list = [*get_all_poi(poi_list)]
    poi_layer = []
    for poi in poi_list:
        point = utils.get_point(poi['geom'], ctx.geo2pixel_transform)
        x = (2.0 * point[0] / width - 1) * a
        y = (2.0 * point[1] / height - 1) * b
        limit, p = styles[poi['level']]

        single_poi_layer = draw_single_poi(x, y, p)
        single_poi_layer[single_poi_layer > limit] = limit
        single_poi_layer /= limit
        poi_layer.append(single_poi_layer)

    poi_layer = np.amin(poi_layer, axis=0)
    poi_layer = (1 - poi_layer) * 255

    ctx.output_image_dict['poi'] = poi_layer
    proceed()


def draw_poi_isoline(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 绘制 POI，等高线版本。
    """

    def draw_isoline(canvas, center: Tuple[int, int], color: int, number: int):
        """
        绘制等高线模拟 POI 影响范围
        """
        for i in range(1, number + 1):
            size = round(i * i)
            axes = (size, size)
            cv2.ellipse(canvas,
                        center=center,
                        axes=axes,
                        angle=0,
                        startAngle=0,
                        endAngle=360,
                        color=color,
                        thickness=1)

    def draw_single_poi(canvas, current_poi, parent_poi):
        """
        绘制单个 POI
        """
        tag_str = current_poi['tag']
        if tag_str not in ctx.category_dict:
            return

        point = utils.get_point(current_poi['geom'], ctx.geo2pixel_transform)
        point = (point[0], point[1])
        poi_tag = ctx.category_dict[tag_str]
        level = int(current_poi['level'])
        draw_isoline(canvas, point, poi_tag, (3 - level) * 4)

        if parent_poi:
            cv2.line(canvas, parent_poi, point, color=poi_tag, thickness=2)

        for child_poi in current_poi['children']:
            draw_single_poi(canvas, child_poi, point)

    info = utils.read_json(get_file_path(ctx, 'poi'))
    poi_list = info['poi_list']
    image = create_empty_image(ctx)
    for poi in poi_list:
        draw_single_poi(image, poi, None)

    ctx.output_image_dict['poi'] = image
    proceed()


def save_images(ctx: SegDrawingContext, proceed):
    """
    Pipeline: 保存管线中生成的所有图片到本地。
    """
    for dir_name, image in ctx.output_image_dict.items():
        save_dir = get_output_dir(ctx.output_dir, dir_name)
        save_path = save_dir / f'{ctx.image_stem}.png'
        cv2.imwrite(str(save_path), image)

    proceed()


def process(data):
    """
    指定处理管线，适配并发 API 要求，无特别含义。
    """
    drawing_pipeline, ctx = data
    drawing_pipeline(ctx)


def build_pipeline(dir_names: List[str]):
    """
    根据指定的目录名称，选取相应的生成函数，构建处理管线。
    """
    draw_func_dict = {
        'label': draw_seg_label,
        'inner_road': draw_inner_road,
        'outer_road': draw_outer_road,
        'poi': draw_poi_isoline,
        'road_gate': draw_road_gate,
        'water_area': draw_water_area,
    }

    pipes = [parse_image_info]
    pipes.extend(draw_func_dict[x] for x in dir_names)
    pipes.append(save_images)
    return pipeline.Pipeline(*pipes)


def main(args):
    """
    脚本入口函数
    """
    dataset_dir = Path(args.dataset_dir)
    output_dir = Path(args.output_dir) if args.output_dir else dataset_dir
    dir_names = args.dir_names.split('|')

    # 先创建好目录，不要放到 pipeline，否则并发可能重复创建导致异常。
    for dir_name in dir_names:
        save_dir = get_output_dir(output_dir, dir_name)
        if not save_dir.exists():
            save_dir.mkdir()

    json_info_dir = dataset_dir / 'json_info'
    image_info_dir = json_info_dir / 'image'
    category_dict = utils.read_aoi_categories_txt(args.tag_file)
    drawing_pipeline = build_pipeline(dir_names)

    def generate_data():
        """
        生成并发任务包
        """
        for image_info_path in image_info_dir.glob('*.json'):
            ctx = SegDrawingContext()
            ctx.json_info_dir = json_info_dir
            ctx.output_dir = output_dir
            ctx.image_stem = image_info_path.stem
            ctx.category_dict = category_dict
            ctx.output_image_dict = {}

            options = LabelOptions()
            options.only_contained = args.only_contained
            options.only_border = args.only_border
            ctx.label_options = options

            yield drawing_pipeline, ctx

    # with Pool(args.process_number) as p:
    #     # FIXME: 先迭代整个 list 是为了获取数量信息供进度条使用，不确定当文件数量巨大时，是否占用较大的内存。
    #     # 若出现内存问题，可取消此处迭代，采用无进度的进度条样式。
    #     data_list = [*generate_data()]
    #     _ = list(tqdm(p.imap(process, data_list), total=len(data_list)))

    for data in tqdm([*generate_data()]):
        process(data)


if __name__ == '__main__':
    main(parse_args())
