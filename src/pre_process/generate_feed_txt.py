# !/usr/bin/env python3
"""
生成用于生产数据所用的语义分割输入文件（feed.txt，多通道）。
"""

import argparse
import json
from pathlib import Path
from tqdm import tqdm

# 附加通道的文件夹名称，注意，这里的顺序是有意义的，不可随意调换。
VIS_AUX_CHANNEL_LIST = [
    'outer_road',
    'poi',
    'water_area',
    'inner_road',
    'road_gate',
]
AUX_CHANNEL_LIST = [
    'inner_road',
    'outer_road',
    'poi',
    'road_gate',
    'water_area',
]


def parse_args():
    """
    解析命令函参数
    """
    parser = argparse.ArgumentParser('Split dataset')
    parser.add_argument(
        '--dataset-dir',
        dest='dataset_dir',
        help='The dir path of dataset.',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--count',
        dest='count',
        help='The count of the feed.txt file.',
        type=int,
        default=1
    )
    parser.add_argument(
        '--image-size-limit',
        dest='image_size_limit',
        help='The limit of image size. (like 1024*512)',
        type=lambda x: [int(x) for x in x.split('*')],
        default='0*0'
    )
    parser.add_argument(
        '--legacy',
        dest='legacy',
        help='',
        action='store_true',
        default=False
    )
    return parser.parse_args()


def format_line(image_id: str, legacy: bool):
    """
    根据给定的图片 id 构造训练文件（*.txt）中的单行文本。
    """
    if legacy:
        aux_segments = ' '.join(f'aux_images_{x}/{image_id}.png' for x in AUX_CHANNEL_LIST)
        return f'images/{image_id}.jpg {aux_segments} labels/{image_id}.png\n'
    else:
        aux_segments = ' '.join(f'aux_images_{x}/{image_id}.png' for x in VIS_AUX_CHANNEL_LIST)
        return f'images/{image_id}.jpg {aux_segments}\n'


def write_to_txt(save_path, collection, legacy: bool):
    """
    将给定额集合对应按行写入给定的保存路径。
    """
    with open(save_path, 'w') as f:
        f.writelines(format_line(x, legacy) for x in collection)


def main(args):
    """
    主函数
    """
    dataset_dir = Path(args.dataset_dir)
    image_info_dir = dataset_dir / 'json_info' / 'image'
    width_limit, height_limit = args.image_size_limit
    image_size_limit = width_limit * height_limit
    file_count = args.count

    feed_list = []
    feed_out_of_limit_list = []
    for image_info_path in tqdm([*image_info_dir.glob('*.json')]):
        image_id = image_info_path.stem
        if image_size_limit == 0:
            feed_list.append(image_id)
            continue

        image_info = json.loads(image_info_path.read_text())
        actual_size = image_info['width'] * image_info['height']
        if actual_size > image_size_limit:
            feed_out_of_limit_list.append(image_id)
        else:
            feed_list.append(image_id)

    if feed_out_of_limit_list:
        write_to_txt(dataset_dir / f'feed_out_of_limit.txt', feed_out_of_limit_list, args.legacy)

    if file_count <= 1:
        write_to_txt(dataset_dir / 'feed.txt', feed_list, args.legacy)
        return

    feed_count = len(feed_list)
    count_per_file = feed_count // file_count
    feed_group_list = [feed_list[i:(i + count_per_file)] for i in range(0, feed_count, count_per_file)]

    for i in range(0, len(feed_group_list)):
        write_to_txt(dataset_dir / f'feed_{i}.txt', feed_group_list[i], args.legacy)


if __name__ == '__main__':
    main(parse_args())
