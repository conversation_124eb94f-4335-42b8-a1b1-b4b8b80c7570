# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""用于生成停车位面"""
import argparse
import sys
import uuid
from os import path
from typing import Dict, Set, List

root_path = path.dirname(path.dirname(__file__) + "/../../")
sys.path.insert(0, root_path)

import ogr
import osr
from shapely import wkt as wkt_util, geometry, ops
from src.pre_process.documents import FeatureLayer, Document


class NavLdSdRelNew:
    def __init__(self, rel_id, lane_group_id, s_link_id, e_link_id):
        self.id = rel_id
        self.lane_group_id = lane_group_id
        self.s_link_id = s_link_id
        self.e_link_id = e_link_id


class NavLaneMarkingPl:
    def __init__(self, marking_pl_id, longitudinal_pl_type, lane_group_id, length, wkt):
        self.id = marking_pl_id
        self.longitudinal_pl_type = longitudinal_pl_type
        self.lane_group_id = lane_group_id
        self.length = float(length)
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)
        self.geom.FlattenTo2D()


class NavLink:
    def __init__(self, link_id, wkt):
        self.id = link_id
        self.name = ''
        self.geom: ogr.Geometry = ogr.CreateGeometryFromWkt(wkt)
        self.geom.FlattenTo2D()


class NavLinkName:
    def __init__(self, link_name_id, link_id, name_id):
        self.id = link_name_id
        self.link_id = link_id
        self.name_id = name_id


class NavName:
    def __init__(self, name_id, name_ch):
        self.id = name_id
        self.name_ch = name_ch


class NavLinkLayer(FeatureLayer):
    def __init__(self):
        super().__init__("nav_link")
        self.__feature_dict = {}

    def load(self, cur):
        super().load(cur)
        for feature in self.features:
            self.__feature_dict[feature.id] = feature

    def get_link(self, link_id):
        return self.__feature_dict.get(link_id, None)

    def _get_query_sql(self) -> str:
        return "SELECT link_id, geom FROM nav_link"

    def _create_feature(self, record):
        return NavLink(link_id=record[0], wkt=record[1])


class NavLaneMarkingPlLayer(FeatureLayer):
    def __init__(self):
        super().__init__("nav_lane_marking_pl")
        self.__feature_dict = {}

    def load(self, cur):
        super().load(cur)
        for feature in self.features:
            self.__feature_dict[feature.id] = feature

    def get_parking_lanes(self):
        for feature in self.features:
            if feature.longitudinal_pl_type != '':
                arr = feature.longitudinal_pl_type.split(',')
                if '11' in arr:
                    yield feature

    def _get_query_sql(self) -> str:
        return "SELECT marking_pl_id, longitudinal_pl_type, lane_group_id, length, geom FROM nav_lane_marking_pl"

    def _create_feature(self, record):
        return NavLaneMarkingPl(
            marking_pl_id=record[0],
            longitudinal_pl_type=record[1],
            lane_group_id=record[2],
            length=record[3],
            wkt=record[4])


class NavLinkNameLayer(FeatureLayer):
    def __init__(self):
        super().__init__("nav_link_name")
        self.__feature_dict = {}

    def load(self, cur):
        res = cur.execute(self._get_query_sql())
        records = list(res.fetchall())

        for record in records:
            feature = self._create_feature(record)
            self.features.append(feature)
            self.__feature_dict[feature.id] = feature

    def _get_query_sql(self) -> str:
        return "SELECT link_name_id, link_id, name_id FROM nav_link_name"

    def _create_feature(self, record):
        return NavLinkName(link_name_id=record[0], link_id=record[1], name_id=record[2])


class NavNameLayer(FeatureLayer):
    def __init__(self):
        super().__init__("nav_name")
        self.__feature_dict = {}

    def load(self, cur):
        res = cur.execute(self._get_query_sql())
        records = list(res.fetchall())

        for record in records:
            feature = self._create_feature(record)
            self.features.append(feature)
            self.__feature_dict[feature.id] = feature

    def get_name(self, name_id):
        return self.__feature_dict.get(name_id, None)

    def _get_query_sql(self) -> str:
        return "SELECT name_id, name_ch FROM nav_name"

    def _create_feature(self, record):
        return NavName(name_id=record[0], name_ch=record[1])


class NavLdSdRelNewLayer(FeatureLayer):
    def __init__(self):
        super().__init__("nav_ld_sd_rel_new")
        self.__group_to_link_dict: Dict[str, Set[str]] = {}

    def load(self, cur):
        res = cur.execute(self._get_query_sql())
        records = list(res.fetchall())

        for record in records:
            feature = self._create_feature(record)
            self.features.append(feature)
            links = self.__group_to_link_dict.get(feature.lane_group_id, None)
            if links is None:
                links = set()

            links.add(feature.s_link_id)
            links.add(feature.e_link_id)
            self.__group_to_link_dict[feature.lane_group_id] = links

    def get_links(self, lane_group_id):
        return self.__group_to_link_dict.get(lane_group_id, None)

    def _get_query_sql(self) -> str:
        return "SELECT rel_id, lane_group_id, s_link_id, e_link_id FROM nav_ld_sd_rel_new"

    def _create_feature(self, record):
        return NavLdSdRelNew(rel_id=record[0], lane_group_id=record[1], s_link_id=record[2], e_link_id=record[3])


class LdRoadDocument(Document):
    def __init__(self, document_path: str):
        super().__init__("road", document_path)

    def _get_extension(self):
        return ".ttfa"

    def _create_layers(self):
        return [
            NavLaneMarkingPlLayer(),
            NavLinkLayer(),
            NavLinkNameLayer(),
            NavNameLayer(),
            NavLdSdRelNewLayer(),
        ]


def get_nearest_link(parking_lane, links):
    centroid = parking_lane.geom.Centroid()
    min_distance = sys.maxsize
    nearest_link = None
    for link_id in links:
        nav_link = nav_link_layer.get_link(link_id)
        if nav_link is not None:
            distance = centroid.Distance(nav_link.geom) * 1e5
            if distance < min_distance:
                min_distance = distance
                nearest_link = nav_link

    return nearest_link


def create_parking_faces():
    for nav_link_name in nav_link_name_layer.features:
        nav_link = nav_link_layer.get_link(nav_link_name.link_id)
        if nav_link is not None:
            nav_name = nav_name_layer.get_name(nav_link_name.name_id)
            if nav_name is not None:
                nav_link.name = nav_name.name_ch

    parking_lanes = list(nav_lane_marking_pl_layer.get_parking_lanes())
    parking_faces = []

    for parking_lane in parking_lanes:
        links = nav_ld_sd_rel_new_layer.get_links(parking_lane.lane_group_id)
        if links is not None:
            # 挑选距离最近的线，用它的名称作为停车位面的名称。
            nearest_link = get_nearest_link(parking_lane, links)

            face_id = uuid.uuid4().hex
            face_name = f'路侧停车位({nearest_link.name})' if nearest_link.name != '' else '路侧停车位'
            face_length = parking_lane.length

            parking_faces.append((face_id, face_name, face_length, parking_lane.geom))

    group_by_name: Dict[str, Dict[str, object]] = {}

    # 按名字对停车位面进行分组。
    for face in parking_faces:
        face_id, face_name, _, _ = face
        face_dict = group_by_name.get(face_name, None)
        if face_dict is None:
            face_dict = dict()

        face_dict[face_id] = face
        group_by_name[face_name] = face_dict

    for face_group in group_by_name.items():
        merged_line_name, faces = face_group
        links = []

        for face in faces.values():
            face_id, face_name, face_length, parking_lane_geom = face
            links.append(wkt_util.loads(parking_lane_geom.ExportToWkt()))

        multi_line = geometry.MultiLineString(links)
        merged_line = ops.linemerge(multi_line)
        split_lines = []

        if merged_line.type is 'LineString':
            split_lines.append(merged_line)
        else:
            split_lines = [line for line in merged_line]

        for line in split_lines:
            left_hand_side = line.buffer(2.5 * 1e-5, single_sided=True)
            face_geom = ogr.CreateGeometryFromWkt(left_hand_side.wkt)
            face_id = uuid.uuid4().hex

            # 计算当前停车位面所覆盖的停车位线，以便求出车位线长度。
            face_total_length = 0
            for face in faces.values():
                _, _, face_length, parking_lane_geom = face
                if parking_lane_geom.Touches(face_geom):
                    face_total_length += face_length

            if face_geom is not None:
                yield face_id, merged_line_name, face_total_length, face_geom


def output_parking_faces(output_tab_path, parking_faces):
    # 创建 mapinfo 文档（ogr 会根据文件后缀名自动判断是输出 mif 还是 tab）
    driver = ogr.GetDriverByName("MapInfo File")
    data_source = driver.CreateDataSource(output_tab_path)

    # 写入层和字段信息
    spatial_reference = osr.SpatialReference("")
    layer = data_source.CreateLayer(
        "parking_face",
        spatial_reference,
        options=["BOUNDS=-180,-90,180,90"])

    field_guid = ogr.FieldDefn("face_id", ogr.OFTString)
    layer.CreateField(field_guid, 0)

    field_name = ogr.FieldDefn("name", ogr.OFTString)
    layer.CreateField(field_name, 0)

    field_length = ogr.FieldDefn("length", ogr.OFTReal)
    layer.CreateField(field_length, 0)

    for face_id, face_name, face_length, face_geom in parking_faces:
        feature = ogr.Feature(layer.GetLayerDefn())
        feature.SetField("face_id", face_id)
        feature.SetField("name", face_name)
        feature.SetField("length", face_length)
        feature.SetGeometry(face_geom)
        layer.CreateFeature(feature)


def fill_arguments(parser):
    parser.add_argument(
        '--output-path',
        dest='output_path',
        help='tab 输出地址。',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--road-db-path',
        dest='road_db_path',
        help='路网文件路径或者文件夹路径。',
        type=str,
        required=True,
    )
    return parser


if __name__ == '__main__':
    args = fill_arguments(argparse.ArgumentParser()).parse_args()

    document = LdRoadDocument(args.road_db_path)
    document.load()

    nav_lane_marking_pl_layer = document.get_layer('nav_lane_marking_pl')
    nav_link_layer = document.get_layer('nav_link')
    nav_link_name_layer = document.get_layer('nav_link_name')
    nav_name_layer = document.get_layer('nav_name')
    nav_ld_sd_rel_new_layer = document.get_layer('nav_ld_sd_rel_new')

    output_parking_faces(args.output_path, create_parking_faces())
    print('done')
