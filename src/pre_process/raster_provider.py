# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""google 瓦片图"""
import hashlib
import math
import os
from abc import abstractmethod
from os import path
from time import time
from typing import List, Dict

import requests
from PIL import Image
from osgeo import ogr
from retrying import retry

from src.pre_process.transform import gcj2wgs
from src.tools.afs_tool import AfsTool
from src.tools.function import exec_shell_cmd

Image.MAX_IMAGE_PIXELS = 1000 * 1000 * 1000


class Point:
    def __init__(self, x: float = 0, y: float = 0):
        self.x = x
        self.y = y


class Bounds:
    def __init__(self, left, top, right, bottom):
        self.left = left
        self.top = top
        self.right = right
        self.bottom = bottom

    @staticmethod
    def create(center: Point, side_length):
        return Bounds(
            left=center.x - side_length * 0.5,
            top=center.y + side_length * 0.5,
            right=center.x + side_length * 0.5,
            bottom=center.y - side_length * 0.5
        )

    def get_cells(self):
        for column in range(self.left, self.right):
            for row in range(self.bottom, self.top):
                yield column, row

    def get_center(self):
        return Point((self.left + self.right) * 0.5, (self.top + self.bottom) * 0.5)


class Size:
    def __init__(self, width: int = 0, height: int = 0):
        self.width = width
        self.height = height


class RasterInfo:
    def __init__(self):
        self.pixel_position = Point()
        self.raster_position = Point()
        self.mesh_position = Point()
        self.level = 0
        self.save_path = ''

    def get_name(self):
        return f'{self.level}_{self.raster_position.x}_{self.raster_position.y}.jpg'


class RegionInfo:
    def __init__(self):
        self.id = ""
        self.gcj_geom = ogr.Geometry(ogr.wkbNone)
        self.gcj_expanded_geom = ogr.Geometry(ogr.wkbNone)
        self.wgs_bounds: Bounds = Bounds(0.0, 0.0, 0.0, 0.0)
        self.rasters: List[RasterInfo] = []
        self.pixel_size = Size()
        self.city = ""


class RasterHelper:
    @staticmethod
    def convert_to_geometry(bounds: Bounds) -> ogr.Geometry:
        return ogr.CreateGeometryFromWkt(f'POLYGON (('
                                         f'{bounds.left} {bounds.top},'
                                         f'{bounds.right} {bounds.top},'
                                         f'{bounds.right} {bounds.bottom},'
                                         f'{bounds.left} {bounds.bottom},'
                                         f'{bounds.left} {bounds.top}))')

    @staticmethod
    def get_wgs_bounds(gcj_geom: ogr.Geometry):
        gcj_left, gcj_right, gcj_bottom, gcj_top = gcj_geom.GetEnvelope()
        wgs_left_top = gcj2wgs(gcj_left, gcj_top)

        return Bounds(
            left=wgs_left_top[0],
            top=wgs_left_top[1],
            right=wgs_left_top[0] + (gcj_right - gcj_left),
            bottom=wgs_left_top[1] - (gcj_top - gcj_bottom)
        )

    @staticmethod
    def get_bounds_geom(geom: ogr.Geometry):
        """
        获取外接矩形的 geom
        """
        left, right, bottom, top = geom.GetEnvelope()
        bounds = Bounds(left, top, right, bottom)
        return RasterHelper.convert_to_geometry(bounds)


class RasterProviderFactory:
    def __init__(self, args):
        self.args = args
        self.providers: List[RasterProvider] = [
            Google2021RasterProvider(args),
            Google2022RasterProvider(args),
            Google2023RasterProvider(args),
            GoogleAnnualRasterProvider(args),
        ]

    def create(self):
        for provider in self.providers:
            if provider.name == self.args.raster_type.lower():
                if self.args.raster_host is not None:
                    provider.set_host(self.args.raster_host)
                return provider

        return None


class RasterStorageFactory:
    def __init__(self, args):
        self.args = args
        self.storages: List[RasterStorage] = [
            HttpRasterStorage(args),
            AfsRasterStorage(args),
            AfsRasterStorage2023(args),
        ]

    def create(self):
        for storage in self.storages:
            if storage.name == self.args.raster_src.lower():
                return storage

        return None


class RasterStorage:
    def __init__(self, args):
        self.cache_path = args.cache_path
        self.level = args.level
        self.host = ''
        self.name = ''

    @abstractmethod
    def download(self, region_info: RegionInfo):
        pass

    def combine(self, region_info: RegionInfo):
        region_image = Image.new('RGB', (region_info.pixel_size.width, region_info.pixel_size.height), (0, 0, 0))
        for raster in region_info.rasters:
            raster_image = Image.open(raster.save_path)
            region_image.paste(raster_image, (raster.pixel_position.x, raster.pixel_position.y))

        return region_image


class HttpRasterStorage(RasterStorage):
    def __init__(self, args):
        super().__init__(args)
        self.name = 'http'

    def download(self, region_info: RegionInfo):
        for raster in region_info.rasters:
            raster_name = raster.get_name()
            raster_name_hash = hashlib.md5(raster_name.encode('utf-8')).hexdigest()[0:2]
            save_folder_path = path.join(self.cache_path, raster_name_hash)
            if not path.exists(save_folder_path):
                os.mkdir(save_folder_path)
            raster.save_path = path.join(save_folder_path, raster_name)
            if not path.exists(raster.save_path):
                HttpRasterStorage.__download_file(self.__get_url(raster), raster.save_path)

    @staticmethod
    @retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
    def __download_file(url, save_path):
        response = requests.get(url)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
        elif response.status_code == 404:
            return
        else:
            raise Exception(f'下载失败，url={url}, status_code={response.status_code}')

    def __get_url(self, raster: RasterInfo):
        timestamp = int(time() * 1000)
        appkey = 'map-aoi-tile'
        secret = '2aeb2332de572858516c68519a71df12'
        sign = hashlib.md5(f'{appkey}|{timestamp}|{secret}'.encode('utf-8')).hexdigest()

        return f'{self.host}' \
               f'{raster.mesh_position.x}_{raster.mesh_position.y}/' \
               f'{self.level}/' \
               f'{raster.raster_position.x}_{raster.raster_position.y}.jpg' \
               f'?appkey={appkey}&t={timestamp}&sign={sign}'


class AfsRasterStorage(RasterStorage):
    def __init__(self, args):
        super().__init__(args)
        self.name = 'afs'
        try:
            self.afs = AfsTool()
        except Exception as e:
            print(e)

    def download(self, region: RegionInfo):
        for tar_name in AfsRasterStorage.__get_tar_names(region):
            tar_path = f'{self.cache_path}/{tar_name}.tar'
            if not path.exists(tar_path):
                self.afs.get(f'{self.host}/{tar_name}.tar', self.cache_path)

    def combine(self, region: RegionInfo):
        self.__unzip(region)
        return super().combine(region)

    @staticmethod
    def __get_tar_names(region: RegionInfo):
        tar_names = set()
        for raster in region.rasters:
            tar_names.add(AfsRasterStorage.__get_tar_name(raster))
        return tar_names

    @staticmethod
    def __get_tar_name(raster: RasterInfo):
        return f'{raster.mesh_position.x}_{raster.mesh_position.y}'

    def __unzip(self, region: RegionInfo):
        # 将瓦片图按照图幅分组。
        raster_dict: Dict[str, List[RasterInfo]] = {}
        for raster in region.rasters:
            tar_name = AfsRasterStorage.__get_tar_name(raster)
            rasters = raster_dict.get(tar_name, None)
            if rasters is None:
                rasters = []
            rasters.append(raster)
            raster_dict[tar_name] = rasters

        for tar_name in raster_dict.keys():
            tar_path = f'{self.cache_path}/{tar_name}.tar'
            rasters = raster_dict.get(tar_name)

            image_names = []
            for raster in rasters:
                image_name = f'{self.level}/{raster.raster_position.x}_{raster.raster_position.y}.jpg'
                print(image_name)
                image_path = f'{self.cache_path}/{image_name}'
                raster.save_path = image_path
                if not path.exists(image_path):
                    image_names.append(f'./{image_name}')

            if not any(image_names):
                continue
            exec_shell_cmd(f"tar -xf {tar_path} -C {self.cache_path}")


class AfsRasterStorage2023(RasterStorage):
    """
    23 年瓦片 afs 存储
    """

    def __init__(self, args):
        super().__init__(args)
        self.name = 'afs2023'
        self.max_level = 20
        self.mesh_unit_coord_length = 0.17578125
        self.raster_unit_coord_length = 0.0006866455078125

        map_level = min(self.level, self.max_level)
        zoom_times = 1 << (self.max_level - map_level)
        self.raster_coord_length = self.raster_unit_coord_length * zoom_times

        try:
            self.afs = AfsTool()
        except Exception as e:
            print(e)

    def combine(self, region: RegionInfo):
        self.__unzip(region)
        return super().combine(region)

    def download(self, region: RegionInfo):
        for tar_name in self.__get_tar_names(region):
            tar_path = f'{self.cache_path}/{tar_name}.tar'
            if not path.exists(tar_path):
                self.afs.get(f'{self.host}/{tar_name}.tar', self.cache_path)

    def __get_tar_names(self, region: RegionInfo):
        tar_names = set()
        for raster in region.rasters:
            tar_names.add(self.__get_tar_name(raster))
        return tar_names

    def __get_tar_name(self, raster: RasterInfo):
        mesh_x = math.floor(180 / self.mesh_unit_coord_length + raster.mesh_position.x)
        mesh_y = math.floor(90 / self.mesh_unit_coord_length - raster.mesh_position.y)

        return f'12_{mesh_x + 1}_{mesh_y}'  # 这里的 1 是测试出来的，反正 22 和 23 年存在偏差。

    def __get_image_name(self, raster: RasterInfo):
        raster_x = math.floor(180 / self.raster_coord_length + raster.raster_position.x)
        raster_y = math.floor(90 / self.raster_coord_length - raster.raster_position.y)

        return f'{raster_x}_{raster_y - 1}'  # 这里的 1 是测试出来的，反正 22 和 23 年存在偏差。

    def __unzip(self, region: RegionInfo):
        # 将瓦片图按照图幅分组。
        raster_dict: Dict[str, List[RasterInfo]] = {}
        for raster in region.rasters:
            tar_name = self.__get_tar_name(raster)
            rasters = raster_dict.get(tar_name, None)
            if rasters is None:
                rasters = []
            rasters.append(raster)
            raster_dict[tar_name] = rasters

        for tar_name in raster_dict.keys():
            tar_path = f'{self.cache_path}/{tar_name}.tar'
            rasters = raster_dict.get(tar_name)

            image_names = []
            for raster in rasters:
                level = self.level - 1  # 23 年瓦片图用的是水经注导出，level 需要减一。
                image_name = f'{tar_name}/{level}/{self.__get_image_name(raster)}.jpg'
                image_path = f'{self.cache_path}/{image_name}'
                raster.save_path = image_path
                if not path.exists(image_path):
                    image_names.append(f'./{image_name}')

            if not any(image_names):
                continue
            exec_shell_cmd(f"tar -xf {tar_path} -C {self.cache_path}")


class RasterProvider:
    def __init__(self, args):
        self.cache_path = args.cache_path
        self.level = args.level
        self.name = ''
        self.raster_storage = RasterStorageFactory(args).create()

        if self.cache_path is not None and not path.exists(self.cache_path):
            os.makedirs(self.cache_path)
            os.makedirs(path.join(self.cache_path, str(self.level)))

    def set_host(self, host):
        self.raster_storage.host = host

    @abstractmethod
    def create_rasters(self, wgs_bounds: Bounds):
        pass

    def download_rasters(self, region_info: RegionInfo):
        self.raster_storage.download(region_info)

    def combine_rasters(self, region_info: RegionInfo):
        return self.raster_storage.combine(region_info)

    @abstractmethod
    def coord_to_pixel(self, bounds: Bounds, point):
        pass

    @abstractmethod
    def get_region_pixel_size(self, bounds: Bounds) -> Size:
        pass

    @abstractmethod
    def get_coord_length(self, pixel_length):
        pass

    @staticmethod
    def get_pixel_length(self, coord_length):
        pass


class Google2021RasterProvider(RasterProvider):
    def __init__(self, args):
        super().__init__(args)
        self.name = 'google2021'
        self.max_level = 19
        self.mesh_unit_coord_length = 0.17578125
        self.raster_unit_coord_length = 0.001373291015625
        self.raster_unit_pixel_length = 256
        self.set_host('http://m.map.baidu.com:8006/tilepic/')

        map_level = min(self.level, self.max_level)
        zoom_times = 1 << (self.max_level - map_level)
        self.raster_coord_length = self.raster_unit_coord_length * zoom_times

    def get_region_pixel_size(self, bounds: Bounds) -> Size:
        width = round(self.get_pixel_length(bounds.right - bounds.left))
        height = round(self.get_pixel_length(bounds.top - bounds.bottom))
        return Size(width, height)

    def get_coord_length(self, pixel_length):
        return pixel_length / self.raster_unit_pixel_length * self.raster_coord_length

    def get_pixel_length(self, coord_length):
        return coord_length / self.raster_coord_length * self.raster_unit_pixel_length

    def coord_to_pixel(self, bounds: Bounds, point):
        x, y = point

        return Point(
            x=int(self.get_pixel_length(x - bounds.left)),
            y=int(self.get_pixel_length(bounds.top - y))
        )

    def create_rasters(self, wgs_bounds: Bounds):
        raster_range = Bounds(
            left=int(math.floor(wgs_bounds.left / self.raster_coord_length)),
            top=int(math.ceil(wgs_bounds.top / self.raster_coord_length)),
            right=int(math.ceil(wgs_bounds.right / self.raster_coord_length)),
            bottom=int(math.floor(wgs_bounds.bottom / self.raster_coord_length))
        )

        for column, row in raster_range.get_cells():
            raster_info = RasterInfo()
            raster_info.pixel_position = self.coord_to_pixel(
                bounds=wgs_bounds,
                point=(column * self.raster_coord_length, (row + 1) * self.raster_coord_length)
            )
            raster_info.raster_position = Point(column, row)
            raster_info.mesh_position = self.__get_raster_mesh_position(column, row)
            raster_info.level = self.level
            yield raster_info

    def __get_raster_mesh_position(self, column: int, row: int):
        raster_origin_point = Point(
            x=column * self.raster_coord_length,
            # google 瓦片图原点在左下角，但是屏幕原点在左上角，差一格，故而 +1。
            y=(row + 1) * self.raster_coord_length
        )

        # 0.5 是为了把图幅原点移动到图幅中心，防止浮点数误差导致图幅编号计算偏移。
        return Point(
            x=int((raster_origin_point.x + self.raster_coord_length * 0.5) /
                  self.mesh_unit_coord_length),
            y=int((raster_origin_point.y - self.raster_coord_length * 0.5) /
                  self.mesh_unit_coord_length)
        )


class GoogleAnnualRasterProvider(Google2021RasterProvider):
    """
    Google 年更瓦片图提供器 （22 年后，包括 22 年）
    """

    def __init__(self, args):
        super().__init__(args)
        if not hasattr(self, 'year'):
            self.year = args.raster_year

        self.name = 'google_annual'
        self.max_level = 20
        self.raster_unit_coord_length = 0.0006866455078125
        self.set_host(f'http://m.map.baidu.com:8006/tilepic/{self.year}_')

        map_level = min(self.level, self.max_level)
        zoom_times = 1 << (self.max_level - map_level)
        self.raster_coord_length = self.raster_unit_coord_length * zoom_times


class Google2022RasterProvider(GoogleAnnualRasterProvider):
    """
    Google 2022 瓦片图提供器
    """

    def __init__(self, args):
        self.year = 2022
        super().__init__(args)
        self.name = 'google2022'


class Google2023RasterProvider(GoogleAnnualRasterProvider):
    """
    Google 2023 瓦片图提供器
    """

    def __init__(self, args):
        self.year = 2023
        super().__init__(args)
        self.name = 'google2023'
