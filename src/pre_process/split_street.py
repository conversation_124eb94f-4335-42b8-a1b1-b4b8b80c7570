# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""街区切割前置处理"""
import glob
import os
import sys
from multiprocessing import Pool
from enum import Enum, IntFlag
from os import path
from typing import List, Dict, Set
from datetime import datetime

root_path = path.dirname(path.dirname(__file__) + "/../../")
sys.path.insert(0, root_path)

import shutil
import sqlite3

import argparse
import uuid
import rtree
from osgeo import ogr
from tqdm import tqdm

from src.tools.minimal_cycle_basis import MinimalCycleBasis


class GraphNode:
    """表示一个无向图中的节点"""

    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y
        self.wkt = f"POINT ({x} {y})"
        self.geom = ogr.CreateGeometryFromWkt(self.wkt)


class GraphSegment:
    """表示无向图中的一条线段
    两个点组成一条线段，我们用这两个点的序号标记一条线段
    """

    def __init__(self, start_index: int, end_index: int):
        self.start_index = start_index
        self.end_index = end_index


class Graph:
    """包含一个无向图中所有的节点和线段"""

    def __init__(self, segments: List[GraphSegment], nodes: List[GraphNode]):
        self.segments = segments
        self.nodes = nodes

    def get_nodes(self):
        return [[node.x, node.y] for node in self.nodes]

    def get_segments(self):
        return [[segment.start_index, segment.end_index] for segment in self.segments]


class StreetData:
    def __init__(self, wkt: str):
        self.face_id = uuid.uuid4().hex
        self.link_id = uuid.uuid4().hex
        self.node_id = uuid.uuid4().hex
        self.face_topo_id = uuid.uuid4().hex
        self.region_document_path = None

        if wkt != "" and not None:
            self.geom = ogr.CreateGeometryFromWkt(wkt)
            self.wkt = wkt


class MeshData:
    def __init__(self, road_document_path: str):
        self.road_document_path = road_document_path
        self.mesh_id = self.get_document_mesh_id(road_document_path)
        self.bound_left, self.bound_right, self.bound_bottom, self.bound_top = get_mesh_rect(self.mesh_id)
        self.mesh_side_nodes: List[GraphNode] = []
        self.streets: List[StreetData] = []
        self.road_level: int = 7
        self.rtree = rtree.index.Index(interleaved=False)

    def build_rtree(self):
        for index, street in enumerate(self.streets):
            x_min, x_max, y_min, y_max = street.geom.GetEnvelope()
            self.rtree.insert(index, (x_min, x_max, y_min, y_max))

    def get_overlap_streets(self, node: GraphNode):
        bound_length = 0.00001
        x_min, x_max, y_min, y_max = node.geom.GetEnvelope()
        coordinates = (x_min - bound_length, x_max + bound_length, y_min - bound_length, y_max + bound_length)
        for index in self.rtree.intersection(coordinates):
            yield self.streets[index]

    def overlap_node(self, node: GraphNode):
        bound_length = 0.00001
        node_bound_left = node.x - bound_length
        node_bound_right = node.x + bound_length
        node_bound_top = node.y + bound_length
        node_bound_bottom = node.y - bound_length

        return (self.bound_left <= node_bound_left <= self.bound_right) or \
            (self.bound_left <= node_bound_right <= self.bound_right) or \
            (self.bound_bottom <= node_bound_top <= self.bound_top) or \
            (self.bound_bottom <= node_bound_bottom <= self.bound_top)

    @staticmethod
    def get_document_mesh_id(document_path: str):
        return int(path.splitext(path.basename(document_path))[0].replace("merge_", ""))


class WaterAreaKind(IntFlag):
    Sea = 1 << 1
    River = 1 << 2
    Lake = 1 << 3
    Reservoir = 1 << 4
    Harbour = 1 << 5
    Canal = 1 << 6


expected_kinds = \
    WaterAreaKind.Sea | \
    WaterAreaKind.River | \
    WaterAreaKind.Lake | \
    WaterAreaKind.Reservoir | \
    WaterAreaKind.Harbour | \
    WaterAreaKind.Canal


class WaterAreaLevel(Enum):
    Level0 = 0
    Level1 = 1
    Level2 = 2
    Level3 = 3
    Level4 = 4
    Level5 = 5
    Level6 = 6
    Level7 = 7
    Level8 = 8


expected_levels: Set[WaterAreaLevel] = {
    WaterAreaLevel.Level1,
    WaterAreaLevel.Level2,
    WaterAreaLevel.Level3,
    WaterAreaLevel.Level4,
}


class WaterAreaData:
    def __init__(self, wkt: str, kind: str, level: int):
        self.geom = ogr.CreateGeometryFromWkt(wkt)
        self.kind = kind
        self.level = level

    def is_expected(self):
        return self.__is_expected_kind() and self.__is_expected_level()

    def __is_expected_kind(self):
        kind = WaterAreaKind(int(self.kind))
        return kind != 0 and (expected_kinds & kind) == kind

    def __is_expected_level(self):
        return WaterAreaLevel(self.level) in expected_levels


class SplitStreetContext:
    def __init__(self, args):
        self.back_db_path = args.back_db_path
        self.road_db_path = args.road_db_path
        self.road_level = args.road_level
        self.process_number = args.process_number

        self.region_db_folder = path.join(path.dirname(self.back_db_path), "region") \
            if path.exists(self.back_db_path) and path.isfile(self.back_db_path) \
            else path.join(self.back_db_path, "region")

        self.mesh_data_dict: Dict[int, MeshData] = {}

        self.road_document_paths = [self.road_db_path] \
            if path.exists(self.road_db_path) and path.isfile(self.road_db_path) \
            else glob.glob(fr"{self.road_db_path}/*.ttfa")
        for document_path in self.road_document_paths:
            mesh_data = MeshData(document_path)
            mesh_data.road_level = self.road_level
            self.mesh_data_dict[mesh_data.mesh_id] = mesh_data

        self.back_document_paths = [self.back_db_path] \
            if path.exists(self.back_db_path) and path.isfile(self.back_db_path) \
            else glob.glob(fr"{self.back_db_path}/*.ttfb")
        for document_path in self.back_document_paths:
            mesh_id = MeshData.get_document_mesh_id(document_path)
            mesh_data = self.mesh_data_dict.get(mesh_id, None)
            if mesh_data is not None:
                mesh_data.back_document_path = document_path

        self.expected_blc_face_levels: Set[WaterAreaLevel] = {
            WaterAreaLevel.Level1,
            WaterAreaLevel.Level2,
            WaterAreaLevel.Level3,
            WaterAreaLevel.Level4,
        }

        if path.exists(self.region_db_folder):
            shutil.rmtree(self.region_db_folder)
        os.mkdir(self.region_db_folder)

    def get_mesh_side_nodes(self):
        nodes: Dict[str, GraphNode] = {}

        for mesh_data in self.mesh_data_dict.values():
            for node in mesh_data.mesh_side_nodes:
                nodes[node.wkt] = node

        return nodes.values()

    def get_all_water_areas(self):
        get_water_area_sql = "SELECT face_id, geom, kind, dis_class FROM blc_face"
        water_areas: List[WaterAreaData] = []

        for document_path in self.back_document_paths:
            con = sqlite3.connect(document_path)
            cur = con.cursor()
            water_area_res = cur.execute(get_water_area_sql)
            water_area_records = water_area_res.fetchall()

            for water_area_record in water_area_records:
                wkt = water_area_record[1]
                kind = water_area_record[2]
                level = water_area_record[3]

                water_area = WaterAreaData(
                    wkt=wkt,
                    kind=kind,
                    level=level
                )

                if water_area.is_expected():
                    water_areas.append(water_area)

            con.close()

        return water_areas


# noinspection SpellCheckingInspection,PyPep8Naming
def get_mesh_rect(mesh_id: int):
    """根据图幅号计算图幅边界
    代码来自：https://console.cloud.baidu-int.com/devops/icode/repos/baidu/lbsgkpoicollect/gmapface/blob
    /master/Baidu.Guoke.Data/Util/Common.cs#L1360
    """
    QOFFSET_X = 1 / 8
    QOFFSET_YEX = [0.08333, 0.08334, 0.08333]

    sy = mesh_id // 10000
    sx = (mesh_id % 10000) // 100
    qy = (mesh_id % 100) // 10
    qx = mesh_id % 10

    l: float = sx + 60 + qx * QOFFSET_X
    r = l + QOFFSET_X

    nsy = sy // 3
    nmody = sy % 3
    nqy = nmody * 8 + qy
    sb = nsy * 2.0
    b = sb
    ndt = 10000000
    nb = int(b * ndt)
    nt = 0

    for i in range(nqy + 1):
        nmod = i % 3
        if i == nqy:
            nt = nb + int(QOFFSET_YEX[nmod] * ndt)
            break

        nb += int(QOFFSET_YEX[nmod] * ndt)

    b = nb / (ndt * 1.0)
    t = nt / (ndt * 1.0)

    return l, r, b, t


def get_bound_nodes(mesh_data: MeshData):
    # 图幅边界上的点也要考虑
    # 图幅边界构成一个矩形，结合图廓点就能提取图幅边界上所有的点
    # 道路节点与图幅边界最近距离不能超过 2 厘米，否则视为在图幅边界上
    min_distance = 0.0000002

    left_top_node = GraphNode(mesh_data.bound_left, mesh_data.bound_top)
    right_top_node = GraphNode(mesh_data.bound_right, mesh_data.bound_top)
    right_bottom_node = GraphNode(mesh_data.bound_right, mesh_data.bound_bottom)
    left_bottom_node = GraphNode(mesh_data.bound_left, mesh_data.bound_bottom)

    # 图幅左边界
    left_nodes = [node for node in mesh_data.mesh_side_nodes if abs(node.x - mesh_data.bound_left) < min_distance] + \
                 [left_top_node, left_bottom_node]
    left_nodes.sort(key=lambda node: node.y)
    yield left_nodes

    # 图幅上边界
    top_nodes = [node for node in mesh_data.mesh_side_nodes if
                 abs(node.y - mesh_data.bound_top) < min_distance and
                 mesh_data.bound_left < node.x < mesh_data.bound_right] + [left_top_node, right_top_node]
    top_nodes.sort(key=lambda node: node.x)
    yield top_nodes

    # 图幅右边界
    right_nodes = [node for node in mesh_data.mesh_side_nodes if abs(node.x - mesh_data.bound_right) < min_distance] + \
                  [right_top_node, right_bottom_node]
    right_nodes.sort(key=lambda node: node.y)
    yield right_nodes

    # 图幅下边界
    bottom_nodes = [node for node in mesh_data.mesh_side_nodes if
                    abs(node.y - mesh_data.bound_bottom) < min_distance and
                    mesh_data.bound_left < node.x < mesh_data.bound_right] + [left_bottom_node, right_bottom_node]
    bottom_nodes.sort(key=lambda node: node.x)
    yield bottom_nodes


def create_graph_nodes(mesh_data: MeshData):
    get_links_sql = f"SELECT geom FROM nav_link WHERE kind <= {mesh_data.road_level}"
    # 2 为图廓点
    get_mesh_side_nodes_sql = f"SELECT geom FROM nav_node WHERE form = 2"
    nodes_on_links: List[List[GraphNode]] = []

    con = sqlite3.connect(mesh_data.road_document_path)
    cur = con.cursor()
    wkt_res = cur.execute(get_links_sql)
    wkt_records = wkt_res.fetchall()
    mesh_side_node_res = cur.execute(get_mesh_side_nodes_sql)
    mesh_side_node_records = mesh_side_node_res.fetchall()
    con.close()

    # 提取节点
    for wkt_record in wkt_records:
        link = ogr.CreateGeometryFromWkt(wkt_record[0])
        link_nodes: List[GraphNode] = []

        for node_index in range(link.GetPointCount()):
            # GetPoint returns a tuple not a Geometry
            point = link.GetPoint(node_index)
            graph_node = GraphNode(float(point[0]), float(point[1]))
            link_nodes.append(graph_node)

        nodes_on_links.append(link_nodes)

    # 提取图廓点
    mesh_side_nodes: List[GraphNode] = []

    for mesh_side_node_record in mesh_side_node_records:
        point = ogr.CreateGeometryFromWkt(mesh_side_node_record[0]).GetPoint(0)
        graph_node = GraphNode(float(point[0]), float(point[1]))
        mesh_side_nodes.append(graph_node)

    mesh_data.mesh_side_nodes = mesh_side_nodes
    bound_nodes = [*get_bound_nodes(mesh_data)]
    return nodes_on_links + bound_nodes


def create_graph(nodes_on_links: List[List[GraphNode]]):
    segments: List[GraphSegment] = []
    nodes: List[GraphNode] = []

    total = 0
    for item in nodes_on_links:
        total += len(item)

    # 使用点的 wkt 信息作为 key，点编号作为 value，做一个字典
    id_dict: Dict[str, int] = {}

    # 当前点编号
    node_index = 0

    for nodes_on_single_link in nodes_on_links:
        prev_node_index = -1

        for node in nodes_on_single_link:
            if id_dict.get(node.wkt, None) is None:
                # 新点，线段的第一个点无法构成边，所以不统计
                nodes.append(GraphNode(node.x, node.y))

                if prev_node_index >= 0:
                    segments.append(GraphSegment(prev_node_index, node_index))

                prev_node_index = node_index
                id_dict[node.wkt] = node_index
                node_index += 1
            else:
                # 旧点，prevNodeIndex != oldNodeIndex 这个限制必须要加，在极端情况下，图幅角点，可能刚好和路网上的坐标点重合，
                # 另外在收集这些点的时候，刚好位置相邻，当满足这两个条件时，问题就来了。我们设路网上的一个点为 a，图幅的一个角点为 A，
                # A 和 a 坐标重合，且在集合中位置相邻，首先 a 先进入 if id_dict.get(node.wkt, None) is None 判断，
                # 发现是新点，则 prevNodeIndex 设置为 a，idDict 中也有了 a 的记录，接着 A 也随之进入 if 判断，因为是使用点的 wkt
                # 信息作为 key，所以 A 被发现已经存在，故而进入 else 分支，这时，oldNodeIndex == a，prevNodeIndex == a，
                # 最后会创建一条首尾点相同的线段，导致最后无向图构造失败（无向图不该存在位置相同的点），上面的结果等同于这样一个事实：
                # a 的相邻点是 a，导致最小圈基算法在逆时针优先寻找相邻点时会产生无限循环。
                old_node_index = id_dict[node.wkt]
                if prev_node_index >= 0 and prev_node_index != old_node_index:
                    segments.append(GraphSegment(prev_node_index, old_node_index))

                prev_node_index = old_node_index

    return Graph(
        segments=segments,
        nodes=nodes
    )


def create_single_document_basic_streets(mesh_data: MeshData):
    # 构造无向图
    graph = create_graph(create_graph_nodes(mesh_data))

    # 计算闭合面
    cycles = MinimalCycleBasis(
        positions=graph.get_nodes(),
        edges=graph.get_segments()
    ).extract_cycles()

    # 生成基础街区面
    basic_streets: List[StreetData] = []
    for cycle in cycles:
        ring = ogr.Geometry(ogr.wkbLinearRing)
        for node_index in cycle:
            node = graph.nodes[node_index]
            ring.AddPoint(node.x, node.y)

        polygon = ogr.Geometry(ogr.wkbPolygon)
        ring.FlattenTo2D()
        polygon.AddGeometry(ring)
        basic_streets.append(StreetData(polygon.ExportToWkt()))

    return basic_streets


def create_basic_streets(mesh_data: MeshData):
    mesh_data.streets = create_single_document_basic_streets(mesh_data)
    wkt_list = [street.wkt for street in mesh_data.streets]
    points = [(node.x, node.y) for node in mesh_data.mesh_side_nodes]
    return mesh_data.mesh_id, wkt_list, points


def delete_streets(data):
    region_document_path, street_id_list = data

    con = sqlite3.connect(region_document_path)
    cur = con.cursor()

    for street_id in street_id_list:
        face_id, link_id, node_id, face_topo_id = street_id
        delete_face_sql = f"DELETE FROM region_face WHERE face_id = '{face_id}'"
        delete_link_sql = f"DELETE FROM region_link WHERE link_id = '{link_id}'"
        delete_node_sql = f"DELETE FROM region_node WHERE node_id = '{node_id}'"
        delete_face_topo_sql = f"DELETE FROM region_face_topo WHERE id = '{face_topo_id}'"

        cur.execute(delete_face_sql)
        cur.execute(delete_link_sql)
        cur.execute(delete_node_sql)
        cur.execute(delete_face_topo_sql)

    con.commit()
    con.close()


def cutting_street_by_water_area(context: SplitStreetContext):
    streets_to_create: List[StreetData] = []
    streets_to_delete: List[StreetData] = []
    water_areas = context.get_all_water_areas()
    tree = rtree.index.Index(interleaved=False)

    # 构造 R 树
    for index in range(len(water_areas)):
        water_area = water_areas[index]
        x_min, x_max, y_min, y_max = water_area.geom.GetEnvelope()
        tree.insert(index, (x_min, x_max, y_min, y_max))

    # 水系切割
    for mesh_data in tqdm(context.mesh_data_dict.values(), total=len(context.mesh_data_dict), desc='水系切割'):
        for street in mesh_data.streets:
            x_min, x_max, y_min, y_max = street.geom.GetEnvelope()
            index_list = list(tree.intersection((x_min, x_max, y_min, y_max)))

            if not any(index_list):
                continue

            multi_polygon = ogr.Geometry(ogr.wkbMultiPolygon)
            for index in index_list:
                multi_polygon.AddGeometry(water_areas[index].geom)

            combined_geometry = multi_polygon.UnionCascaded()
            difference = street.geom.Difference(combined_geometry)
            if difference is None:
                continue

            geometry_type = difference.GetGeometryType()

            if geometry_type == ogr.wkbMultiPolygon:
                streets_to_delete.append(street)
                for index in range(difference.GetGeometryCount()):
                    streets_to_create.append(StreetData(difference.GetGeometryRef(index).ExportToWkt()))
            elif geometry_type == ogr.wkbPolygon:
                streets_to_delete.append(street)
                streets_to_create.append(StreetData(difference.ExportToWkt()))

    # 对需要删除的街区按照文件分组
    streets_to_delete_dict = {}
    for street in streets_to_delete:
        streets = streets_to_delete_dict.get(street.region_document_path, None)
        if streets is None:
            streets_to_delete_dict[street.region_document_path] = []

        streets_to_delete_dict[street.region_document_path].append((
            street.face_id,
            street.link_id,
            street.node_id,
            street.face_topo_id
        ))

    # 删除旧街区
    with Pool(context.process_number) as p:
        for _ in tqdm(iterable=p.imap_unordered(delete_streets, streets_to_delete_dict.items()),
                      total=len(streets_to_delete_dict),
                      desc='删除旧街区'):
            pass

    # 输出新街区
    __output_region_documents(context, streets_to_create, "split", 1024)


def __output_region_documents(
        context: SplitStreetContext,
        streets: List[StreetData],
        document_prefix: str,
        batch_size: int = 256):
    streets_count = len(streets)
    batch_count = streets_count // batch_size + (1 if streets_count % batch_size > 0 else 0)

    for batch_index in tqdm(range(batch_count), total=batch_count, desc=f'输出 {document_prefix}'):
        batch_streets = streets[batch_index * batch_size:(batch_index + 1) * batch_size]
        document_name = f"{document_prefix}_{batch_index}.ttfr"
        document_path = path.join(context.region_db_folder, document_name)
        output_region_document(document_path, batch_streets)


def output_inner_region_documents(context: SplitStreetContext):
    for mesh_data in tqdm(context.mesh_data_dict.values(), total=len(context.mesh_data_dict), desc='输出内部街区'):
        document_name = f"{mesh_data.mesh_id}.ttfr"
        document_path = path.join(context.region_db_folder, document_name)
        output_region_document(document_path, mesh_data.streets)


def combine_streets_on_mesh_side(context: SplitStreetContext):
    streets_on_mesh_side = get_streets_on_mesh_side(context)

    """
    开始合并这些在图幅接边处的区域面
    接边可能为以下 3 种，总结下来就是跨图幅的面才能合并，同图幅不能合并，除非它们在相邻图幅有共线面
    ——————————————————————————————————————————
    |                                        |
    |                                        |
    |                                        |
    |    1            2              3       |
    |  ------    -----------    -----------  |
    |  |    |    |    |    |    |         |  |
    |  |    |    |    |    |    |         |  |
    ———+————+————+————+————+————+—————————+———
    |  |    |    |    |    |    |    |    |  |
    |  |    |    |    |    |    |    |    |  |
    |  ------    -----------    -----------  |
    |                                        |
    |                                        |
    |                                        |
    |                                        |
    ——————————————————————————————————————————    
    """

    # 在真正合并之前，需要知道哪些面可以合并，我们把这些可以合并成一个大面的面划分到一个组，这个组会有一个递增的 id，
    # 而这个字典就是用于存储面和组的映射，面的 key 我们直接用它的 face_id 表示
    street_to_combining_group_dict: Dict[str, int] = {}

    # 这个就是所谓的组了
    combining_group_dict: Dict[int, List[StreetData]] = {}

    # 接下来开始填充 combiningGroupDict 以便后续真正合并
    fill_mesh_side_group(
        streets_on_mesh_side,
        street_to_combining_group_dict,
        combining_group_dict
    )

    street_on_mesh_side_combined: List[StreetData] = []

    # 把可以合并的面合并
    for streets in combining_group_dict.values():
        if not any(streets):
            continue

        combine_result = determine_if_streets_can_be_combined(streets)
        if not combine_result[0]:
            continue

        street_on_mesh_side_combined.append(combine_result[1])

    # 合并之后原来的小面需要删除
    for mesh_data in context.mesh_data_dict.values():
        index = 0
        while index < len(mesh_data.streets):
            if street_to_combining_group_dict.get(mesh_data.streets[index].face_id, None) is not None:
                mesh_data.streets.pop(index)
                index -= 1

            index += 1

    return street_on_mesh_side_combined


def get_mesh_data_on_node(context: SplitStreetContext, node: GraphNode):
    for mesh_data in context.mesh_data_dict.values():
        if mesh_data.overlap_node(node):
            yield mesh_data


def get_streets_on_mesh_side(context: SplitStreetContext):
    street_dict: Dict[str, Dict[int, List[StreetData]]] = {}
    mesh_side_nodes = list(context.get_mesh_side_nodes())
    node_count = len(mesh_side_nodes)

    for node in tqdm(mesh_side_nodes, total=node_count, desc='提取接边街区'):
        streets_in_single_document_dict: Dict[int, List[StreetData]] = {}
        for mesh_data in get_mesh_data_on_node(context, node):
            streets = mesh_data.get_overlap_streets(node)
            if not any(streets):
                continue

            streets_in_single_document_dict[mesh_data.mesh_id] = list(streets)

        if len(streets_in_single_document_dict) > 1:
            street_dict[node.wkt] = streets_in_single_document_dict

    return street_dict


def output_region_document(region_document_path: str, streets: List[StreetData]):
    shutil.copyfile(f"{root_path}/src/pre_process/data/template.ttfr", region_document_path)
    con = sqlite3.connect(region_document_path)
    cur = con.cursor()

    for street_index in range(len(streets)):
        street = streets[street_index]
        face_id = street.face_id
        node_id = street.node_id
        link_id = street.link_id
        face_topo_id = street.face_topo_id
        street.region_document_path = region_document_path

        face_wkt = street.wkt
        link = street.geom.GetGeometryRef(0)
        link_wkt = link.ExportToWkt().replace("LINEARRING", "LINESTRING")
        point = link.GetPoint(0)
        point_wkt = f"POINT ({point[0]} {point[1]})"
        insert_face_sql = f"INSERT INTO region_face VALUES('{face_id}', '{face_wkt}', 1)"
        insert_node_sql = f"INSERT INTO region_node VALUES('{node_id}', '{point_wkt}')"
        insert_link_sql = f"INSERT INTO region_link VALUES('{link_id}', '{link_wkt}', '{node_id}', '{node_id}')"
        insert_face_topo_sql = f"INSERT INTO region_face_topo VALUES('{face_topo_id}', '{face_id}', '{link_id}')"
        cur.execute(insert_face_sql)
        cur.execute(insert_node_sql)
        cur.execute(insert_link_sql)
        cur.execute(insert_face_topo_sql)

    con.commit()
    con.close()


def fill_mesh_side_group(street_dict: Dict[str, Dict[int, List[StreetData]]],
                         street_to_combining_group_dict: Dict[str, int],
                         combining_group_dict: Dict[int, List[StreetData]]):
    for streets_in_single_document_dict in tqdm(street_dict.values(), total=len(street_dict), desc='街边分组'):
        # streets_in_single_document_dict 这个字典记录了包含某个接边点位置的所有区域面，这些区域面现在用 ttfa 图幅加以区分了，
        # 下面的工作就是把处在不同 ttfa 中的面合并即可，为了提速，我们使用级联合并
        # 假设现在有两份 ttfa，图幅分别是 A、B，我们需要把 A 中的所有面分别和 B 中所有面两两合并
        for item_current in streets_in_single_document_dict.items():
            mesh_id = item_current[0]
            # street_current_list 可以看成是 A 中的所有面
            street_current_list = item_current[1]
            for item_other in streets_in_single_document_dict.items():
                if mesh_id == item_other[0]:
                    # 同图幅不合并
                    continue
                # street_other_list 可以看成是 B 中的所有面
                street_other_list = item_other[1]
                # 合并测试，如果合并后是 PolygonGeometry 则能合并，否则不能
                combine_mesh_side_group(
                    street_to_combining_group_dict,
                    combining_group_dict,
                    street_current_list,
                    street_other_list
                )


def combine_mesh_side_group(street_to_combining_group_dict: Dict[str, int],
                            combining_group_dict: Dict[int, List[StreetData]],
                            street_current_list: List[StreetData],
                            street_other_list: List[StreetData]):
    for street_current in street_current_list:
        # 看一下当前面是否已经被分组
        group_id_current = street_to_combining_group_dict.get(street_current.face_id, None)
        for street_other in street_other_list:
            street_current_and_other = [street_current, street_other]
            combine_result = determine_if_streets_can_be_combined(street_current_and_other)
            if not combine_result[0]:
                continue

            group_id_other = street_to_combining_group_dict.get(street_other.face_id, None)
            if group_id_other is not None:
                # 其它面被分组了
                if group_id_current is not None:
                    # 很不巧，当前面也被分组了，我们继续判断它们是否在一个组内
                    if group_id_current != group_id_other:
                        # 不在一个组，则以当前组为准，其它组内的元素全部转移到当前组
                        group_other = combining_group_dict[group_id_other]
                        for item_other in group_other:
                            combining_group_dict[group_id_current].append(item_other)
                            street_to_combining_group_dict[item_other.face_id] = group_id_current

                        group_other.clear()
                else:
                    # 当前面没有被分组，则把当前面添加到其它组中
                    street_to_combining_group_dict[street_current.face_id] = group_id_other
                    combining_group_dict[group_id_other].append(street_current)
            else:
                # 其它面没有被分组
                if group_id_current is not None:
                    # 但是当前面被分组了，则全部归入当前组
                    street_to_combining_group_dict[street_other.face_id] = group_id_current
                    combining_group_dict[group_id_current].append(street_other)
                else:
                    # 当前面也没有被分组，那么就新建一个组
                    group_id = len(combining_group_dict)

                    combining_group_dict[group_id] = street_current_and_other
                    street_to_combining_group_dict[street_current.face_id] = group_id
                    street_to_combining_group_dict[street_other.face_id] = group_id


def determine_if_streets_can_be_combined(streets: List[StreetData]):
    multi_polygon = ogr.Geometry(ogr.wkbMultiPolygon)
    for street in streets:
        multi_polygon.AddGeometry(street.geom)

    combined_geometry = multi_polygon.UnionCascaded()
    # 合并后必须是 PolygonGeometry 才算合并成功
    success = combined_geometry is not None and combined_geometry.GetGeometryType() == ogr.wkbPolygon
    return (True, StreetData(combined_geometry.ExportToWkt())) if success else (False, StreetData(""))


def execute_split_street(context: SplitStreetContext):
    """
    执行街区切割
    """
    with Pool(context.process_number) as p:
        for item in tqdm(iterable=p.imap_unordered(create_basic_streets, context.mesh_data_dict.values()),
                         total=len(context.mesh_data_dict),
                         desc='街区切割'):
            mesh_id, wkt_list, points = item
            mesh_data = context.mesh_data_dict[mesh_id]
            mesh_data.streets = [StreetData(wkt) for wkt in wkt_list]
            mesh_data.mesh_side_nodes = [GraphNode(point[0], point[1]) for point in points]

    for mesh_data in context.mesh_data_dict.values():
        mesh_data.build_rtree()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser('Split street')
    parser.add_argument(
        '--back-db-path',
        dest='back_db_path',
        help='背景文件路径或者文件夹路径。',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--road-db-path',
        dest='road_db_path',
        help='路网文件路径或者文件夹路径。',
        type=str,
        required=True,
    )
    parser.add_argument(
        '--road-level',
        dest='road_level',
        help='指定街区切割等级。',
        type=int,
        required=False,
        default=7
    )
    parser.add_argument(
        '--process-number',
        dest='process_number',
        help='指定并发执行的进程数。',
        type=int,
        default=8,
    )
    return parser.parse_args()


def main(args):
    context = SplitStreetContext(args)

    # 对所有的道路进行区域面切分
    execute_split_street(context)
    # 输出接边 ttfr
    __output_region_documents(context, combine_streets_on_mesh_side(context), "mesh_side")
    # 输出内部 ttfr
    output_inner_region_documents(context)
    # 水系切割
    cutting_street_by_water_area(context)


if __name__ == '__main__':
    print(datetime.now())
    main(parse_args())
    print(datetime.now())
