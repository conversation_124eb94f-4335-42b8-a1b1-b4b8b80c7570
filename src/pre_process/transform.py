# !/usr/bin/env python3
"""
接近无损的经纬度坐标转换
"""

import math
from math import sin, cos, sqrt, fabs

a = 6378245.0
f = 1 / 298.3
b = a * (1 - f)
ee = 1 - (b * b) / (a * a)


def out_of_china(lng, lat):
    """
    判断坐标是否在中国
    """
    return not (72.004 <= lng <= 137.8347 and 0.8293 <= lat <= 55.8271)


def transform_latitude(x, y):
    """
    转换维度
    """
    ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * sqrt(fabs(x))
    ret = ret + (20.0 * sin(6.0 * x * math.pi) + 20.0 * sin(2.0 * x * math.pi)) * 2.0 / 3.0
    ret = ret + (20.0 * sin(y * math.pi) + 40.0 * sin(y / 3.0 * math.pi)) * 2.0 / 3.0
    ret = ret + (160.0 * sin(y / 12.0 * math.pi) + 320.0 * sin(y * math.pi / 30.0)) * 2.0 / 3.0
    return ret


def transform_longitude(x, y):
    """
    转换经度
    """
    ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * sqrt(fabs(x))
    ret = ret + (20.0 * sin(6.0 * x * math.pi) + 20.0 * sin(2.0 * x * math.pi)) * 2.0 / 3.0
    ret = ret + (20.0 * sin(x * math.pi) + 40.0 * sin(x / 3.0 * math.pi)) * 2.0 / 3.0
    ret = ret + (150.0 * sin(x / 12.0 * math.pi) + 300.0 * sin(x * math.pi / 30.0)) * 2.0 / 3.0
    return ret


def wgs2gcj(wgs_longitude, wgs_latitude):
    """
    WGS-84 to GCJ-02
    """

    if out_of_china(wgs_longitude, wgs_latitude):
        return wgs_longitude, wgs_latitude

    diff_latitude = transform_latitude(wgs_longitude - 105.0, wgs_latitude - 35.0)
    diff_longitude = transform_longitude(wgs_longitude - 105.0, wgs_latitude - 35.0)
    radian_latitude = wgs_latitude / 180.0 * math.pi

    magic = sin(radian_latitude)
    magic = 1 - ee * magic * magic
    sqrt_magic = sqrt(magic)

    diff_latitude = (diff_latitude * 180.0) / ((a * (1 - ee)) / (magic * sqrt_magic) * math.pi)
    diff_longitude = (diff_longitude * 180.0) / (a / sqrt_magic * cos(radian_latitude) * math.pi)
    gcj_latitude = wgs_latitude + diff_latitude
    gcj_longitude = wgs_longitude + diff_longitude

    return gcj_longitude, gcj_latitude


def gcj2wgs(gcj_longitude, gcj_latitude):
    """
    GCJ-02 to WGS-84
    """

    g0 = (gcj_longitude, gcj_latitude)
    w0 = g0
    g1 = wgs2gcj(w0[0], w0[1])
    # w1 = w0 - (g1 - g0)
    w1 = tuple(map(lambda x: x[0] - (x[1] - x[2]), zip(w0, g1, g0)))
    # delta = w1 - w0
    delta = tuple(map(lambda x: x[0] - x[1], zip(w1, w0)))

    while abs(delta[0]) >= 1e-6 or abs(delta[1]) >= 1e-6:
        w0 = w1
        g1 = wgs2gcj(w0[0], w0[1])
        # w1 = w0 - (g1 - g0)
        w1 = tuple(map(lambda x: x[0] - (x[1] - x[2]), zip(w0, g1, g0)))
        # delta = w1 - w0
        delta = tuple(map(lambda x: x[0] - x[1], zip(w1, w0)))

    return w1
