# 语义分割矢量化模块

配套文档：[语义分割结果矢量化方案](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/sjJIdVhyAGyiUa)

## 1. 使用方法

- `main.py`：执行入口，执行前需配置的常量均在文件开头列出。
- `pipeline.py`：整个包的对外导出文件，外部使用仅需导出其中的 `PIPELINE` 常量，并构造相应的 context 以供调用，具体使用方法参考 `main.py`。

## 2. 算法说明

- `vectorize.py`：位图矢量化，根据8连通规则，得到初步 Graph 结构。
- `simplify.py`：抽稀 Graph 结构。
- 几何修形算法：
    - `trend_line.py`：回归直线法。
    - `extension_line.py`：延长线法。
- `polygonize.py`：提取闭环（多边形）。
