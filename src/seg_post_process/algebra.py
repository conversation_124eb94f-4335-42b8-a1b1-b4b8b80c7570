# !/usr/bin/env python3
"""
线性代数、几何计算模块
"""
import math
from typing import Tuple

FLOAT_TOLERANCE = 1e-8
ZERO_VEC = (0, 0)
COMMON_LINE = 'COMMON_LINE'


def vec(point1, point2):
    """
    获取给定两点所构成的向量，方向：point1 -> point2。
    """
    x1, y1 = point1
    x2, y2 = point2
    return (x2 - x1), (y2 - y1)


def multiply(vec1, vec2):
    """
    求向量叉乘
    """
    x1, y1 = vec1
    x2, y2 = vec2
    return x1 * y2 - x2 * y1


def dot(vec1, vec2):
    """
    求向量点乘
    """
    x1, y1 = vec1
    x2, y2 = vec2
    return x1 * x2 + y1 * y2


def cos_vec(vec1, vec2):
    return dot(normalize_vec(vec1), normalize_vec(vec2))


def normalize_vec(target_vec):
    d_2 = calculate_point_distance_squared(ZERO_VEC, target_vec)
    d = math.sqrt(d_2)
    if d == 0:
        return ZERO_VEC

    p1, p2 = target_vec
    return p1 / d, p2 / d


def resize_vec(vec0, length):
    """
    将给定向量的长度改变为指定长度，返回新的向量。
    """
    dx, dy = vec0
    original_length = math.sqrt(dx * dx + dy * dy)
    if original_length == 0:
        return vec0

    frac = length / original_length
    return frac * dx, frac * dy


def equals_vec(vec1, vec2):
    """
    比较给定的向量是否相等（也可以是两点），考虑了浮点数的判等。
    """
    x1, y1 = vec1
    x2, y2 = vec2
    return abs(x1 - x2) < FLOAT_TOLERANCE and abs(y1 - y2) < FLOAT_TOLERANCE


def get_linear_params(point1, point2) -> Tuple[float, float, float]:
    """
    根据跟定的两点，求过这两点的直线方程：Ax + By + C = 0
    @return: 返回直线方程的系数： (A, B, C)
    """
    x1, y1 = point1
    x2, y2 = point2
    dx = x2 - x1
    if dx == 0:
        return 1., 0., -x1

    dy = y2 - y1
    if dy == 0:
        return 0., 1., -y1

    return 1 / dx, -1 / dy, y1 / (y2 - y1) - x1 / (x2 - x1)


def calculate_intersection_point(segment1, segment2):
    """
    计算给定两线段的交点。
    @return: 返回交点；若没有交点，则返回 None；若线段平行且重合，则返回 algebra.COMMON_LINE。
    """
    p11, p12 = segment1
    p21, p22 = segment2
    a1, b1, c1 = get_linear_params(p11, p12)
    a2, b2, c2 = get_linear_params(p21, p22)
    product = multiply((a1, b1), (a2, b2))
    if product == 0:
        # 两直线平行：分为共线与不共线情况
        k = (a1 / a2) if a2 != 0 else (b1 / b2)
        is_common_line = abs(k * c2 - c1) < FLOAT_TOLERANCE
        is_overlap = dot(vec(p21, p11), vec(p21, p12)) < FLOAT_TOLERANCE
        is_overlap = is_overlap or dot(vec(p22, p11), vec(p22, p12)) < FLOAT_TOLERANCE
        return COMMON_LINE if is_common_line and is_overlap else None

    x0 = multiply((b1, b2), (c1, c2)) / product
    y0 = -multiply((a1, a2), (c1, c2)) / product

    intersection = (x0, y0)

    vec11 = vec(intersection, p11)
    vec12 = vec(intersection, p12)
    vec21 = vec(intersection, p21)
    vec22 = vec(intersection, p22)

    in_segment = dot(vec11, vec12) < FLOAT_TOLERANCE and dot(vec21, vec22) < FLOAT_TOLERANCE
    if not in_segment:
        return None

    # 直接返回原版的点，而非计算后的点，防止浮点误差。
    if equals_vec(vec11, ZERO_VEC):
        return p11

    if equals_vec(vec12, ZERO_VEC):
        return p12

    if equals_vec(vec21, ZERO_VEC):
        return p21

    if equals_vec(vec22, ZERO_VEC):
        return p22

    return intersection


def calculate_point_distance_squared(point1, point2):
    """
    计算两点距离的平方。（考虑开放性能，没有进行开方，外部可根据需求自行开方）
    """
    x1, y1 = point1
    x2, y2 = point2
    dx = x2 - x1
    dy = y2 - y1
    return dx * dx + dy * dy


def get_distance_squared_func(point1: Tuple[int, int], point2: Tuple[int, int]):
    """
    根据给定的两点，求出其所在的直线，返回一个函数：该函数用于计算给定点到该直线的距离。
    """
    x1, y1 = point1
    x2, y2 = point2
    dx = x1 - x2
    if dx == 0:
        def distance_vertical(point: Tuple[int, int]):
            x, _ = point
            distance_x = x - x1
            return 1.0 * distance_x * distance_x

        return distance_vertical
    else:
        dy = y1 - y2
        k = 1.0 * dy / dx
        b = y1 - k * x1

        def distance(point: Tuple[int, int]):
            x, y = point
            temp = k * x - y + b
            return (temp * temp) / (1 + k * k)

        return distance
