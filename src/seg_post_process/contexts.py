# !/usr/bin/env python3
"""
上下文：seg_post_process 包内部使用的上下文。
"""

from pathlib import Path
from typing import Any
from typing import List
from typing import Tuple

import numpy as np

from src.seg_post_process import models
from src.tools import utils


class ImageInfo:
    """
    图片信息
    """

    # 'pseudo-color', 'heatmap', 'heatmap-adaptive'
    image_type: str
    image: np.ndarray
    image_path: Path
    width: int
    height: int
    align_info: Any


class PathInfo:
    """
    目录文件信息
    """

    image_dir: Path
    image_info_dir: Path
    output_dir: Path
    vectorize_info_dir: Path
    failed_case_dir: Path
    debug_dir: Path

    def __init__(self, image_info_dir: Path, image_dir: Path, output_dir: Path):
        self.image_dir = image_dir
        self.image_info_dir = image_info_dir
        self.output_dir = output_dir
        self.vectorize_info_dir = self.output_dir / "vectorize_info"
        self.failed_case_dir = self.output_dir / "failed_cases"
        self.debug_dir = self.output_dir / "debug"

    def ensure_dirs(self, enable_debug=True):
        """
        创建输出目录，如果其不存在。
        """
        utils.ensure_dir(self.output_dir)
        utils.ensure_dir(self.vectorize_info_dir)
        utils.ensure_dir(self.failed_case_dir)
        if enable_debug:
            utils.ensure_dir(self.debug_dir)


class Context:
    # Input
    task_id: str
    image_info: ImageInfo
    path_info: PathInfo
    # 标记是否启用 DEBUG 模式，启用后会输出处理流程中的过程产物（如：细化、修边、修角等图片）
    enable_debug: bool
    # Output
    graph_group: models.GraphGroup
    tags: List[List[str]]
    rings: List[List[Tuple[int, int]]]
    features: Any

    def __init__(self):
        self.enable_debug = True
        self.graph_group = models.GraphGroup()
