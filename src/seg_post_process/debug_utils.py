# !/usr/bin/env python3
"""
DEBUG 工具集。
"""
from PIL import ImageDraw, Image


def draw_lines(canvas: ImageDraw, lines, fill=(255, 255, 255)):
    """
    画线段集到给定的 canvas，线段集格式：
    [[y1, x1], [y2, x2], [y3, x3]] => (y1, x1) -> (y2, x2), (y3, x3)
    因为这里接收的是 cv2 导出的坐标：x 和 y 是相反的。（其图片矩阵的 shape 是 (h, w, c)）
    """
    for line in lines:
        line = [(x[1], x[0]) for x in line]
        canvas.line(line, width=1, fill=fill)


def draw_points(canvas: ImageDraw, points, radius=2, fill=(0, 255, 0)):
    """
    画点集到给定的 canvas，点集格式：
    [[y1, x1], [y2, x2], [y3, x3]]
    因为这里接收的是 cv2 导出的坐标：x 和 y 是相反的。（其图片矩阵的 shape 是 (h, w, c)）
    """
    points = [(x[1] - radius, x[0] - radius, x[1] + radius, x[0] + radius) for x in points]
    for point in points:
        canvas.ellipse(point, fill=fill)


def save_pil_image(image: Image, output_path):
    """
    保存图片到指定路径。
    """
    if image.mode != 'RGB':
        image = image.convert('RGB')

    image.save(output_path)
