# !/usr/bin/env python3
"""
修复原始识别 Graph，采用延长线法。
"""
from typing import Optional, Any, Tuple, List, Dict

import numpy as np
from PIL import ImageDraw, Image

from src.seg_post_process import algebra
from src.seg_post_process import contexts
from src.seg_post_process import debug_utils
from src.seg_post_process import models
from src.seg_post_process import parameters
from src.seg_post_process import trend_line
from src.tools import pipeline, utils

CONFIG = parameters.CONFIG["extension_line"]
ABNORMAL_LINE_LENGTH: int = CONFIG["abnormal_line_length"]
SHORT_LINE_LENGTH: int = CONFIG["short_line_length"]
COS_ANGLE_LIMIT: float = CONFIG["cos_angle_limit"]
EXTENSION_LENGTH: int = CONFIG["extension_length"]

HANDLED = []


class CrossFlange:
    """
    代表一处延长线交点的具体信息。
    """

    # flange 类型
    flange_type: str
    # 交点类型
    intersection_type: str
    # 交点
    intersection_point: Tuple[int, int]
    # 末端点
    end_point: Tuple[int, int]
    # 末端点：仅在延长线与延长线相交的情况下有值。
    second_end_point: Optional[Tuple[int, int]]
    # 识别线段的两个端点：仅在延长线交于识别线中部时有值。
    node_points: Optional[Tuple[Tuple[int, int], Tuple[int, int]]]

    def get_end_points(self):
        yield self.end_point
        if self.second_end_point:
            yield self.second_end_point

        if self.intersection_type == "end":
            yield self.intersection_point

    def __repr__(self):
        return (
            f"{self.intersection_type}: {self.intersection_point} -> {self.end_point}"
        )


class ExtensionLine:
    # 被沿伸的末端线，延伸点为 end_line[0]
    end_line: List[Tuple[Tuple[int, int], Tuple[int, int]]]
    # 延长线
    vec: List[Tuple[Tuple[int, int], Tuple[int, int]]]
    # 末端线长度
    end_line_length: float

    def is_short(self):
        return self.end_line_length < SHORT_LINE_LENGTH


class Context:
    # 输入
    image_info: contexts.ImageInfo
    path_info: contexts.PathInfo
    graph_group: models.GraphGroup
    enable_debug: bool
    # 中间变量
    original_lines: List[Tuple[Tuple[int, int], Tuple[int, int]]]
    extension_lines: List[ExtensionLine]
    # 输出
    all_flanges: List[CrossFlange]
    nearest_flanges: List[CrossFlange]


def pipes():
    return pipeline.Pipeline(
        generate_lines,
        generate_flanges,
        pick_nearest_flanges,
        filter_flanges,
        merge_intersection_points_to_graph,
        debug_draw_extension_line,
    )


def ctx_adaptor(ctx: contexts.Context):
    context = Context()
    context.graph_group = ctx.graph_group
    context.image_info = ctx.image_info
    context.path_info = ctx.path_info
    context.enable_debug = ctx.enable_debug
    return context


def generate_lines(ctx: Context, proceed):
    """
    生成两套线段集：
    （1）延长线段集：延长末端点，延长长度为 EXTENSION_LENGTH。
    （2）识别线段集：将原识别 Graph 打断为直线段，以供后续求交点。
    """
    end_lines = [
        [*models.search_line(x, x.children[0])]
        for x in ctx.graph_group.get_all_nodes()
        if len(x.children) == 1
    ]
    extension_lines = [get_extension_line(x) for x in end_lines]
    extension_lines = [
        x for x in extension_lines if x.end_line_length > ABNORMAL_LINE_LENGTH
    ]

    original_lines = [
        [*models.get_line_segments(x)] for x in ctx.graph_group.get_all_lines()
    ]
    original_lines = [segment for segments in original_lines for segment in segments]

    ctx.extension_lines = extension_lines
    ctx.original_lines = original_lines
    proceed()


def get_extension_line(end_line: list):
    line = ExtensionLine()
    line.end_line = end_line
    line.end_line_length = models.calculate_line_length(end_line)
    line.vec = change_length(generate_trend_line(end_line), EXTENSION_LENGTH)
    return line


def generate_flanges(ctx: Context, proceed):
    """
    生成 flange，即输入延长线相交信息。
    """
    # 线与线两两相交可用 rtree 优化，但一般一张图中的线段是比较少的，构造 rtree 得不偿失。
    flanges = []
    # (1/2) 求延长线与识别线的交点
    for extension_line in ctx.extension_lines:
        for original_line in ctx.original_lines:
            extended_point, _ = extension_line.vec
            p1, p2 = original_line
            # 排除延长线与其所延长的识别线相交的情况
            if algebra.equals_vec(extended_point, p1) or algebra.equals_vec(
                extended_point, p2
            ):
                continue

            point = algebra.calculate_intersection_point(
                extension_line.vec, original_line
            )
            if not point:
                continue

            if point == algebra.COMMON_LINE:
                distance_p1 = algebra.calculate_point_distance_squared(
                    extended_point, p1
                )
                distance_p2 = algebra.calculate_point_distance_squared(
                    extended_point, p2
                )
                point = p1 if distance_p1 < distance_p2 else p2

            point = to_int_point(point)
            _, intersection_node = ctx.graph_group.get_graph_and_node(point)
            is_node_point = algebra.equals_vec(point, p1) or algebra.equals_vec(
                point, p2
            )
            is_end_point = (
                intersection_node is not None and len(intersection_node.children) == 1
            )
            flange = CrossFlange()
            flange.flange_type = "short" if extension_line.is_short() else "normal"
            flange.intersection_type = (
                "end" if is_end_point else "node" if is_node_point else "new"
            )
            flange.intersection_point = point
            flange.end_point = extended_point
            flange.second_end_point = None
            flange.node_points = (p1, p2) if flange.intersection_type == "new" else None
            flanges.append(flange)

    # (2/2) 求延长线与延长线的交点
    extension_line_count = len(ctx.extension_lines)
    for i in range(extension_line_count):
        for j in range(i + 1, extension_line_count):
            extension_line1 = ctx.extension_lines[i]
            extension_line2 = ctx.extension_lines[j]
            extension_vec1 = extension_line1.vec
            extension_vec2 = extension_line2.vec
            point = algebra.calculate_intersection_point(extension_vec1, extension_vec2)
            if not point or point == algebra.COMMON_LINE:
                continue

            extended_point1, _ = extension_vec1
            extended_point2, _ = extension_vec2
            in_end_point = algebra.equals_vec(
                point, extended_point1
            ) or algebra.equals_vec(point, extended_point2)
            if in_end_point:
                # 延长线交于识别线端点的情况已在 (1/2) 中处理。
                continue

            flange = CrossFlange()
            flange.flange_type = (
                "short"
                if extension_line1.is_short() or extension_line2.is_short()
                else "normal"
            )
            flange.intersection_type = "new"
            flange.intersection_point = to_int_point(point)
            flange.end_point = extended_point1
            flange.second_end_point = extended_point2
            flange.node_points = None
            flanges.append(flange)

    ctx.all_flanges = flanges
    proceed()


def filter_flanges(ctx: Context, proceed):
    """
    对以下形式的 flange 做更严格的过滤，并希望它交点的夹角为 pi/2。
      （1）孤立短线段
      （2）毛刺短线段
    """
    filtered_flanges = []
    for flange in ctx.nearest_flanges:
        if flange.flange_type == "normal":
            filtered_flanges.append(flange)
            continue

        p0 = flange.intersection_point
        p1 = flange.end_point
        if flange.intersection_type == "new":
            p2 = (
                flange.second_end_point
                if flange.second_end_point
                else flange.node_points[0]
            )
            cos_value = algebra.cos_vec(algebra.vec(p0, p1), algebra.vec(p0, p2))
            if abs(cos_value) < COS_ANGLE_LIMIT:
                filtered_flanges.append(flange)
        else:
            _, node = ctx.graph_group.get_graph_and_node(p0)
            for child in node.children:
                cos_value = algebra.cos_vec(
                    algebra.vec(p0, p1), algebra.vec(p0, child.point)
                )
                if abs(cos_value) < COS_ANGLE_LIMIT:
                    filtered_flanges.append(flange)

    ctx.nearest_flanges = filtered_flanges
    proceed()


def pick_nearest_flanges(ctx: Context, proceed):
    """
    选取合适的延长线交点，选取原则：若一个交点同时为其相交线上离延长点最近的交点，则称该交点符合选择条件。
    """
    intersection_point_dict = convert_to_intersection_point_dict(ctx.all_flanges)
    end_point_dict = convert_to_end_point_dict(ctx.all_flanges)

    end_point_count = 0
    end_points = list(end_point_dict.keys())
    flanges = []
    while end_point_count != len(end_points):
        end_point_count = len(end_points)
        for end_point in list(end_points):
            # 获取该末端点A延长后的交点列表
            related_flanges = end_point_dict[end_point]
            for related_flange in related_flanges:
                # 获取交于同一点的所有末端点（延长线）
                other_end_points = intersection_point_dict[
                    related_flange.intersection_point
                ]
                is_intersected_at_original_line = len(other_end_points) == 1
                if is_intersected_at_original_line:
                    append_if_missing(flanges, related_flange)
                    remove_if_exists(end_points, end_point)
                    end_point_dict[end_point] = HANDLED
                    break

                found = False
                other_end_points = [x for x in other_end_points if x != end_point]
                for other_end_point in other_end_points:
                    other_related_flanges = end_point_dict[other_end_point]
                    if (
                        other_related_flanges != HANDLED
                        and other_related_flanges[0] == related_flange
                    ):
                        append_if_missing(flanges, related_flange)
                        remove_if_exists(end_points, end_point)
                        remove_if_exists(end_points, other_end_point)
                        end_point_dict[end_point] = HANDLED
                        end_point_dict[other_end_point] = HANDLED
                        found = True
                        break

                if found:
                    break

    ctx.nearest_flanges = flanges
    proceed()


def merge_intersection_points_to_graph(ctx: Context, proceed):
    """
    将选取的延长交点并入 Graph 中。
    """
    rearrange_t_type_flanges(ctx.nearest_flanges)
    for flange in ctx.nearest_flanges:
        graph1, end_node1 = ctx.graph_group.get_graph_and_node(flange.end_point)
        if flange.intersection_type == "new":
            intersection_graph, intersection_node = ctx.graph_group.get_graph_and_node(
                flange.intersection_point
            )
            if not intersection_graph:
                _, intersection_node = graph1.get_or_create_node(
                    flange.intersection_point
                )
            else:
                ctx.graph_group.merge_graph(graph1, intersection_graph)

            graph1.nodes[flange.intersection_point] = intersection_node
            if flange.second_end_point:
                graph2, end_node2 = ctx.graph_group.get_graph_and_node(
                    flange.second_end_point
                )
                intersection_node.bind_two_way(end_node1)
                intersection_node.bind_two_way(end_node2)
            else:
                graph2, end_node2 = ctx.graph_group.get_graph_and_node(
                    flange.node_points[0]
                )
                graph3, end_node3 = ctx.graph_group.get_graph_and_node(
                    flange.node_points[1]
                )
                assert graph2 == graph3
                end_node2.unbind_two_way(end_node3)
                intersection_node.bind_two_way(end_node1)
                intersection_node.bind_two_way(end_node2)
                intersection_node.bind_two_way(end_node3)
        else:
            graph2, intersection_node = ctx.graph_group.get_graph_and_node(
                flange.intersection_point
            )
            intersection_node.bind_two_way(end_node1)

        ctx.graph_group.merge_graph(graph1, graph2)

    proceed()


def debug_draw_extension_line(ctx: Context, proceed):
    """
    可视化延长线为图片，供 DEBUG 使用。
    """
    if ctx.enable_debug:
        image = Image.fromarray(
            np.zeros((ctx.image_info.height, ctx.image_info.width, 3), dtype=np.uint8)
        )
        canvas = ImageDraw.Draw(image)

        extension_lines = [*get_extension_lines(ctx.nearest_flanges)]
        all_extension_lines = [*get_extension_lines(ctx.all_flanges)]
        debug_utils.draw_lines(
            canvas, [x.vec for x in ctx.extension_lines], fill=(32, 32, 32)
        )
        debug_utils.draw_lines(canvas, extension_lines, fill=(0, 255, 0))
        debug_utils.draw_points(
            canvas, [x[0] for x in extension_lines], fill=(0, 255, 255)
        )
        debug_utils.draw_points(
            canvas, [x[0] for x in all_extension_lines], fill=(255, 0, 0), radius=1
        )
        debug_utils.draw_lines(canvas, ctx.original_lines)

        save_dir = utils.ensure_dir(ctx.path_info.debug_dir / "extension_line_images")
        save_path = save_dir / ctx.image_info.image_path.name
        debug_utils.save_pil_image(image, str(save_path))

    proceed()


def rearrange_t_type_flanges(flanges: List[CrossFlange]):
    """
    重排形如 T 的交点，T 类型交点指：延长线交于识别线中部，此时应打断原识别线两端点的连接关系，插入交点。

    若先后有多个交点在同一线段上，形如：p1 - A - B - p2，若直接合并则不能如愿，因为：
    先被处理的 flange 拆分了原线段，形成新节点：p1 - A - p2；
    而后被处理的 flange 里仍然保留原有的 p1、p2，将形成 p1 - B - p2，这导致形成一个面积为 0 的环：
    p1 - A - p2 - B - p1
    """
    t_type_flanges = {}
    for segment, flange in ((x.node_points, x) for x in flanges if x.node_points):
        add_to_dict(t_type_flanges, segment, flange)

    t_type_flanges = [(k, v) for k, v in t_type_flanges.items() if len(v) > 1]
    for segment, flange_list in t_type_flanges:
        p1, p2 = segment
        flange_list = [
            (algebra.calculate_point_distance_squared(p1, x.intersection_point), x)
            for x in flange_list
        ]
        flange_list.sort(key=lambda x: x[0])

        connect_point = p1
        for flange in (x[1] for x in flange_list):
            flange.node_points = (connect_point, p2)
            connect_point = flange.intersection_point
            # 重拍顺序
            flanges.remove(flange)
            flanges.append(flange)


def convert_to_intersection_point_dict(flanges: List[CrossFlange]):
    """
    转换 flange list 为交点字典，即：key - 交点，value - 与该交点相连的末端点。
    """
    point_dict = {}
    for flange in flanges:
        for end_point in flange.get_end_points():
            add_to_dict(point_dict, flange.intersection_point, end_point)

    return point_dict


def convert_to_end_point_dict(flanges: List[CrossFlange]):
    """
    转换 flange list 为末端点字典，即：key - 末端点，value - 由该末端点发射的延长线上的所有 flange。
    """
    collect_dict = {}
    for flange in flanges:
        for end_point in flange.get_end_points():
            add_to_dict(collect_dict, end_point, flange)

    point_dict = {}
    for end_point, flange_set in collect_dict.items():
        flange_list = [
            (
                algebra.calculate_point_distance_squared(
                    end_point, x.intersection_point
                ),
                x,
            )
            for x in flange_set
        ]
        flange_list.sort(key=lambda x: x[0])
        point_dict[end_point] = [x[1] for x in flange_list]

    return point_dict


def add_to_dict(target: Dict[Any, set], key, value):
    """
    添加一组 key, value 到给定字典中，其中字典的 value 是集合 set()。
    """
    if key not in target:
        target[key] = set()

    target[key].add(value)


def append_if_missing(target: list, value):
    """
    添加 value 到给定 list，如果该 value 原本不存在于该 list。
    """
    if value not in target:
        target.append(value)


def remove_if_exists(target: list, value):
    """
    移除 value 从给定 list，如果该 value 原本存在于该 list。
    """
    if value in target:
        target.remove(value)


def to_int_point(point):
    """
    转换一个点的坐标由 float 到 int。
    """
    x, y = point
    return round(x), round(y)


def generate_trend_line(nodes: List[models.GraphNode]):
    """
    生产带趋势的延长线（即：延长回归直线，而延长非末端最后一段）
    """
    if len(nodes) == 2:
        reg_vec = algebra.vec(nodes[1].point, nodes[0].point)
    else:
        turning_indexes = [*trend_line.get_turning_point_indexes(nodes, 8), len(nodes)]
        turning_indexes.sort()
        first_segment = nodes[0 : turning_indexes[0] + 1]
        reg_vec, _ = trend_line.get_regression_line([x.point for x in first_segment])

    start_point = nodes[0].point
    ext_x, ext_y = np.add(start_point, reg_vec)
    return start_point, (ext_x, ext_y)


def change_length(segment, length):
    """
    改变给定线段的长度到指定长度。
    """
    p1, p2 = segment
    vec = algebra.vec(p1, p2)
    vec = algebra.resize_vec(vec, length)
    x2, y2 = np.add(p1, vec)
    return p1, (x2, y2)


# DEBUG UTILS:
def get_extension_lines(flanges: List[CrossFlange]):
    """
    将 flange 信息转换为直线段系，用于可视化。
    """
    for flange in flanges:
        yield flange.intersection_point, flange.end_point
        if flange.second_end_point:
            yield flange.intersection_point, flange.second_end_point
