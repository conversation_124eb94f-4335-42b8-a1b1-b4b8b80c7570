# !/usr/bin/env python3
"""
热力图矢量化前置处理模块：输出使用局部阈值细化后的位图结果。本算法由 @刘希岩（<EMAIL>） 同学开发。

当前该模块仅作为独立脚本使用，还未接入流程，因为具体如何使用局部阈值矢量化的结果还未考虑完全，其使用路线主要有两个：
1. 用于竞品自动化验真（较为明确，无需调研）：用局部阈值结果代替原预测图对竞品框进行“边压盖计算”，可提升有效率。
2. 用于 AOI 召回（需调研）：由于局部阈值会产生大量 POLYGON 碎片，故需要用内部路进行 merge 处理，但内部路仍在打底阶段，
应待其完成打底后，再考虑研发。（自己处理轨迹数据比较麻烦，且工作内容与内部路的算法重合）
"""

import cv2
import numpy as np


def thinning_for_line_channel(img):
    """
    针对语义分割产出的线通道进行矢量化预处理
    """
    h, w, c = img.shape
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    gray = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel, 2)
    gray = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel, 2)
    gray = cv2.normalize(gray, dst=None, alpha=0, beta=255, norm_type=cv2.NORM_MINMAX)
    gray = cv2.GaussianBlur(gray, (17, 17), 0)
    # gray = cv2.medianBlur(gray, 9)
    ret, gray = cv2.threshold(gray, 15, 255, cv2.THRESH_TOZERO)
    img_final = np.zeros((h, w, 3), dtype=np.uint8)
    # cv2.imwrite(save_dir_1, gray)
    binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 99, -5)
    binary = cv2.GaussianBlur(binary, (9, 9), 0)
    # cv2.imwrite(save_dir_2, binary)
    thin = cv2.ximgproc.thinning(binary)
    contours, _hierarchy = _cv2_find_contours(thin, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    cv2.drawContours(img_final, contours, -1, (255, 255, 255), 1)
    img_final = cv2.cvtColor(img_final, cv2.COLOR_RGB2GRAY)
    return img_final


def thinning_for_background_channel(gray):
    """
    针对语义分割产出的背景通道进行矢量化预处理
    """
    gray = 255 - gray

    h, w = gray.shape
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
    # gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    gray = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel, 2)
    gray = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel, 2)
    gray = cv2.normalize(gray, dst=None, alpha=0, beta=255, norm_type=cv2.NORM_MINMAX)
    gray = cv2.GaussianBlur(gray, (17, 17), 0)
    # gray = cv2.medianBlur(gray, 9)
    ret, gray = cv2.threshold(gray, 50, 255, cv2.THRESH_TOZERO)
    img_final = np.zeros((h, w, 3), dtype=np.uint8)

    binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 3, -1)
    # binary = cv2.GaussianBlur(binary, (9, 9), 0)

    thin = cv2.ximgproc.thinning(binary)
    contours, _hierarchy = _cv2_find_contours(thin, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    cv2.drawContours(img_final, contours, -1, (255, 255, 255), 1)
    img_final = cv2.cvtColor(img_final, cv2.COLOR_RGB2GRAY)
    return img_final


def _cv2_find_contours(image, mode: int, method: int):
    img_contours = cv2.findContours(image, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    if len(img_contours) == 2:
        # opencv4  只返回两个参数
        contours, hierarchy = tuple(img_contours)
    elif len(img_contours) == 3:
        # opencv3， 返回三个参数，第一参数我们用不到
        # noinspection PyTupleAssignmentBalance
        _, contours, hierarchy = tuple(img_contours)
    else:
        raise ValueError("Invalid number of outputs from findContours")

    return contours, hierarchy
