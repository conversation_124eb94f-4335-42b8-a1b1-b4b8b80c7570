# !/usr/bin/env python3
"""
入口文件
"""
import logging
import shutil
from multiprocessing.pool import Pool
from pathlib import Path

from tqdm import tqdm

from src.seg_post_process import contexts
from src.seg_post_process import pipeline
from src.tools.machine_group import machine_opera

# 标识是否启用 DEBUG 模式，以输出中间产物。
ENABLE_DEBUG = True

# 输入目录：
# TODO: 根据需求自行输入，以下路径留作示例。
# 表示语义分割的 feed 目录，要求其中包含 feed/json_info/image/*.json 信息。
FEED_DIR = Path(r"C:\Users\<USER>\Downloads\q2_source_uncover_bids_1000")
# 表示语义分割的 product 目录，要求其中包含 product/pseudo_color_prediction/images/*.png 图片。
PRODUCT_DIR = Path(r"C:\Users\<USER>\Downloads\q2_source_uncover_bids_1000")

MAIN_PIPELINE = pipeline.pipes()


def try_process(ctx: contexts.Context):
    """
    对原 pipeline 进行包装，捕获 pipeline 中的异常。
    """
    # noinspection PyBroadException
    try:
        MAIN_PIPELINE(ctx)
    except Exception as e:
        shutil.copy(
            ctx.image_info.image_path,
            ctx.path_info.failed_case_dir / ctx.image_info.image_path.name,
        )
        logging.exception(e)


def get_feed_image_paths(path_info: contexts.PathInfo):
    """
    获取需要处理的图片路径
    """
    feed_image_paths = [*path_info.image_dir.glob("*.png")]
    # 在恢复执行时排除已生产的案例。
    done_id_set = set(x.stem for x in path_info.vectorize_info_dir.glob("*.json"))
    feed_image_paths = [x for x in feed_image_paths if x.stem not in done_id_set]
    return feed_image_paths


def get_image_dir(product_dir: Path, image_type: str):
    """
    获取待处理图片的目录
    """

    def resolve_image_dir(image_dir: Path):
        nested_image_dir = image_dir / "images"
        return nested_image_dir if nested_image_dir.exists() else image_dir

    if image_type == "pseudo-color":
        feed_image_dir = resolve_image_dir(product_dir / "pseudo_color_prediction")
    elif image_type == "heatmap" or image_type == "heatmap-adaptive":
        feed_image_dir = resolve_image_dir(product_dir / "heatmap_prediction")
    else:
        raise ValueError(f"invalid image_type: {image_type}")

    return feed_image_dir


def execute(path_info: contexts.PathInfo, image_type="pseudo-color"):
    """
    执行本包的入口函数。
    """
    path_info.ensure_dirs(ENABLE_DEBUG)
    feed_image_paths = get_feed_image_paths(path_info)

    def generate_data():
        """
        生成并发任务包
        """
        for feed_image_path in feed_image_paths:
            image_info = contexts.ImageInfo()
            image_info.image_type = image_type
            image_info.image_path = feed_image_path

            ctx = contexts.Context()
            ctx.enable_debug = ENABLE_DEBUG
            ctx.task_id = feed_image_path.stem
            ctx.path_info = path_info
            ctx.image_info = image_info
            yield ctx

    # opera 性能不是很好，开启 10 个进程会失败，改成 4 进程执行正常
    pool_size = 4 if machine_opera() else 10
    with Pool(pool_size) as p:
        _ = list(
            tqdm(
                p.imap(try_process, generate_data()),
                total=len(feed_image_paths),
                desc="seg_vec_post_job",
            )
        )

    # DEBUG: 此处用于 debug 执行，debug 时为了方便打断点，不使用多进程并发。
    # for context in tqdm(generate_data(), total=len(feed_image_paths)):
    #     MAIN_PIPELINE(context)


def run_main(feed_dir, product_dir):
    """
    给流水线使用的入口函数
    """
    print(f"街区json路径{feed_dir}")
    print(f"识别文件路径{product_dir}")
    if (product_dir / "pseudo_color_prediction").is_dir():
        # 优先使用矢量图
        image_type = "pseudo-color"
    elif (product_dir / "heatmap_prediction").is_dir():
        # 存在热力图，使用热力图
        image_type = "heatmap-adaptive"
    else:
        raise Exception("无识别文件")

    path_info = contexts.PathInfo(
        image_dir=get_image_dir(product_dir, image_type),
        image_info_dir=feed_dir / "json_info" / "image",
        output_dir=product_dir / "output_vectorize",
    )
    execute(path_info, image_type)


def main():
    """
    主函数：用于测试
    """
    image_type = "heatmap-adaptive-background"

    work_dir = Path(r"C:\Users\<USER>\Downloads\parking_polygon_missing_20240808")
    part_dir = work_dir / "part_0"

    path_info = contexts.PathInfo(
        image_dir=part_dir / "heatmap_prediction" / "0" / "image",
        image_info_dir=work_dir / "_json_info" / "image",
        output_dir=part_dir / "output_vectorize",
    )
    execute(path_info, image_type=image_type)


if __name__ == "__main__":
    main()
