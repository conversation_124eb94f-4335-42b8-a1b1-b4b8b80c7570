# !/usr/bin/env python3
"""
Graph 模型
"""
import math
from typing import Tuple, List, Set, Dict

from src.seg_post_process import algebra


class GraphNode:
    """
    代表图结构中的一个节点
    """
    point: Tuple[int, int]
    children: List['GraphNode']

    def __init__(self, point: Tuple[int, int]):
        self.point = point
        self.children = []

    def bind_two_way(self, other: 'GraphNode'):
        """
        为两个图节点创建相互连通的关系。
        """
        if other == self:
            return

        if other not in self.children:
            self.children.append(other)

        if self not in other.children:
            other.children.append(self)

    def unbind_two_way(self, other: 'GraphNode'):
        """
        解绑图节点。
        @param other:
        @return:
        """
        if other in self.children:
            self.children.remove(other)

        if self in other.children:
            other.children.remove(self)

    def __repr__(self):
        return f'{self.point} -> {len(self.children)}'


class Graph:
    nodes: Dict[Tuple[int, int], GraphNode]

    def __init__(self):
        self.nodes = {}

    def get_or_create_node(self, point: Tuple[int, int]):
        """
        获取已有的图节点，若不存在，则创建一个新的返回
        @return: 返回一个元组：is_new, node，前者为 bool 值，标识返回的 node 是否是新创建的。
        """
        node = self.nodes.get(point)
        is_new = node is None
        if not node:
            node = GraphNode(point)

        return is_new, node

    def get_first_node(self) -> GraphNode:
        """
        获取 Graph 中的一个节点。
        """
        for _, node in self.nodes.items():
            return node

    def __contains__(self, item):
        return item in self.nodes


class GraphGroup:
    """
    代表图结构
    """
    graphs: List[Graph]

    def __init__(self):
        self.graphs = []

    def create_graph(self):
        """
        创建一个 Graph，并添加到 graphs 中。
        """
        graph = Graph()
        self.graphs.append(graph)
        return graph

    def merge_graph(self, target_graph: Graph, *moved_graphs: Graph):
        for moved_graph in moved_graphs:
            if moved_graph == target_graph:
                continue

            self.graphs.remove(moved_graph)
            for point, node in moved_graph.nodes.items():
                target_graph.nodes[point] = node

    def get_graph_and_node(self, point: Tuple[int, int]):
        """
        根据指定的点返回对应的 Node 及其所在的 Graph，若不存在，则都返回 None。
        @return: Graph, Node 或 None, None
        """
        for graph in self.graphs:
            node = graph.nodes.get(point)
            if node:
                return graph, node

        return None, None

    def get_all_nodes(self):
        """
        获得 graphs 中所有 graph 的 node。
        """
        for graph in self.graphs:
            yield from graph.nodes.values()

    def get_all_lines(self):
        """
        将 Graph 拆分成线，即在每个出入度 > 2 的节点处将 Graph 拆开。
        """
        all_lines = []
        for graph in self.graphs:
            head_node = graph.get_first_node()
            end_node = first_end_or_fork_node(head_node)
            if len(end_node.children) == 0:
                continue

            if end_node:
                line_nodes_list = search_lines(end_node, set())
                line_nodes_list = map(lambda line: [x.point for x in line], line_nodes_list)
                all_lines.extend(line_nodes_list)

        return all_lines

    def __contains__(self, item):
        return any(x for x in self.graphs if item in x)


def search_line(start_node: GraphNode, search_node: GraphNode):
    """
    根据给定点找到连通的线，直至遇到出入度不为 2 节点，若遇到闭环则从 start_node 处打断。
    @param start_node: 起始点。
    @param search_node: 与起始点相邻的下一个点，代表搜索的方向。
    @return: 返回整条线上的每一个点，start_node -> end_node。
    """
    yield start_node
    yield search_node
    prev_node = start_node
    while len(search_node.children) == 2 and search_node != start_node:
        next_index = 0 if search_node.children[0] != prev_node else 1
        prev_node = search_node
        search_node = search_node.children[next_index]
        yield search_node


def search_lines(start_node: GraphNode, seen_ways: Set[Tuple[GraphNode, GraphNode]]):
    """
    将 Graph 拆分成线，即在每个出入度 > 2 的节点处将 Graph 拆开。
    """
    children_count = len(start_node.children)
    if children_count == 0:
        raise 'isolated points cannot form a line'

    line_segments = []
    for child in start_node.children:
        if (start_node, child) in seen_ways:
            continue

        segment = [*search_line(start_node, child)]
        # 将已找到线段的次端点记录下来，以确保下次遍历其端点的子节点时，不会重复遍历该线段。
        seen_ways.add((start_node, child))
        seen_ways.add((segment[-1], segment[-2]))
        line_segments.append(segment)

        segments = search_lines(segment[-1], seen_ways)
        line_segments.extend(segments)

    return line_segments


def calculate_line_length(line: List[GraphNode]):
    """
    计算折线的长度。（累加所有直线段）
    """
    if len(line) < 2:
        raise 'The number of line points must not be less than 2.'

    prev_node = line[0]
    length = 0
    for search_node in line[1:]:
        distance = algebra.calculate_point_distance_squared(prev_node.point, search_node.point)
        length += math.sqrt(distance)
        prev_node = search_node

    return length


def first_end_or_fork_node(node: GraphNode):
    """
    以给定的起始节点搜索末端点或分叉点，返回第一个符合条件的点。
    """
    if len(node.children) != 2:
        return node

    prev_node = node
    search_node = node.children[0]
    while len(search_node.children) == 2 and search_node != node:
        next_index = 0 if search_node.children[0] != prev_node else 1
        prev_node = search_node
        search_node = search_node.children[next_index]

    return search_node


def get_line_segments(line):
    """
    将一条折线打断成直线段集合。
    """
    if len(line) < 2:
        raise 'The number of line points must not be less than 2.'

    point1 = line[0]
    for point2 in line[1:]:
        yield point1, point2
        point1 = point2
