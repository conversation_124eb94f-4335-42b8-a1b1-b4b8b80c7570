# 配置书写规范：
# 1. 以下每一个值都必须加注释，谁敢不加，用 git blame 找出来鞭尸。
# 2. Table 名与对应算法的文件名要保持一致，即：[xxx] 必然对应一个叫 xxx.py 的文件。
# 3. 每张 Table 之间空 1 行。

[trend_line]
# 大于该值的线段可作为“主线”。
min_main_line_length = 50
# 小于该值的直线不可求回归直线，太短了，误差会很大。
min_trend_line_length = 10
# 回归直线域：在回归直线两侧该值范围内的点，将被认为是在该回归直线的域内。
regression_scope_threshold = 16
# 抽稀阈值：用抽稀算法来判定曲线的拐点，这是抽稀的阈值。
turning_threshold = 8

[extension_line]
# 小于该值的末端线段将直接被移除。
abnormal_line_length = 10
# 小于该值的末端线段将被判定为 "short" 类型，该类型将受到更严格的限制。
short_line_length = 60
# 对于 "short" 类型链接端，要求连接线与被连接线之间的夹角接近 pi/2，即 cos 接近 0，此处取 0.1。
cos_angle_limit = 0.1
# 延长线的最长沿伸距离。
extension_length = 120
