# !/usr/bin/env python3
"""
组装本包内的算法为一个 pipeline
"""
import hashlib
import json
import os
from typing import List
from typing import Tuple

import cv2
import numpy as np
from PIL import Image
from PIL import ImageDraw

from src.seg_post_process import contexts
from src.seg_post_process import debug_utils
from src.seg_post_process import extension_line
from src.seg_post_process import heatmap
from src.seg_post_process import polygonize
from src.seg_post_process import simplify
from src.seg_post_process import trend_line
from src.seg_post_process import vectorize
from src.tools import pipeline
from src.tools import utils

current_directory = os.path.dirname(os.path.abspath(__file__))
AOI_PSEUDO_COLOR_TO_TAG_INDEX = utils.read_aoi_pseudo_color_file(f"{current_directory}/aoi_pseudo_color_tag29.txt")
AOI_TAG_INDEX_TO_TAG_NAME = dict(
    (y, x) for x, y in utils.read_aoi_categories_txt(f"{current_directory}/../pre_process/aoi_tag_29.txt").items()
)
BORDER_PSEUDO_COLOR_VALUE = 75


def pipes():
    """
    导出所有 pipe 并组合为一个 pipeline。
    """
    return pipeline.Pipeline(
        init_image_info,
        vectorize_image,
        simplify_graph,
        trend_line.repair_by_trend_line,
        extension_line.pipes().adapt(extension_line.ctx_adaptor),
        polygonize_graph,
        debug_draw_final_graph_group,
        extract_poi_tags,
        generate_vectorize_info,
        persistent_vectorize_info,
    )


# pipe function:


def init_image_info(ctx: contexts.Context, proceed):
    """
    将不同类型的输入图片，转化为细化后的二值图片。
    """
    align_info = get_image_info(ctx)
    image_size = align_info["height"], align_info["width"]
    init_image = INIT_IMAGE_STRATEGY[ctx.image_info.image_type]
    image = init_image(ctx.image_info.image_path, image_size)
    if ctx.enable_debug:
        thinned_image_dir = utils.ensure_dir(ctx.path_info.debug_dir / "thinned_images")
        thinned_image_path = thinned_image_dir / f"{ctx.image_info.image_path.stem}.png"
        if not thinned_image_path.exists():
            cv2.imwrite(str(thinned_image_path), image)

    ctx.image_info.image = image
    ctx.image_info.align_info = align_info
    ctx.image_info.height, ctx.image_info.width = image.shape

    proceed()


def vectorize_image(ctx: contexts.Context, proceed):
    """
    矢量化二值细化图。
    """
    vectorize.vectorize_image(ctx.image_info.image, ctx.graph_group)
    proceed()


def simplify_graph(ctx: contexts.Context, proceed):
    """
    抽稀原始矢量图。
    """
    simplify.simplify_graph(ctx.graph_group, threshold=1)
    proceed()


def polygonize_graph(ctx: contexts.Context, proceed):
    """
    提取矢量图中的最小圈基为 POLYGON。
    """
    polygonize.clear_end_lines(ctx.graph_group)
    rings = polygonize.get_min_rings(ctx.graph_group)
    ctx.rings = list(map(lambda ring: [node.point for node in ring], rings))
    proceed()


def extract_poi_tags(ctx: contexts.Context, proceed):
    """
    从识别图中提取给定 POLYGON 所压盖到的 POI Tag。
    """
    if ctx.image_info.image_type == "heatmap-adaptive-background":
        # 仅 heatmap-adaptive-background 支持置信度的输出
        prediction_image_path = ctx.path_info.image_dir / f"{ctx.task_id}.png"
        prediction_image = cv2.imread(str(prediction_image_path))
        ctx.tags = [extract_confidence_by_ring(prediction_image, x) for x in ctx.rings]
    else:
        ctx.tags = [[] for _ in ctx.rings]

    proceed()


def generate_vectorize_info(ctx: contexts.Context, proceed):
    """
    生成矢量化信息。
    """
    transform_info = utils.get_transform_info_from_json(ctx.image_info.align_info, "pixel2geo")
    features = []
    for tag, ring in zip(ctx.tags, ctx.rings):
        if tag is None:
            continue

        geom = [utils.convert_coordinate((x[1], x[0]), transform_info) for x in ring]
        geom = [f"{x} {y}" for x, y in geom]
        geom = f'POLYGON(({", ".join(geom)}))'
        features.append(
            {
                "id": hashlib.md5(geom.encode("utf8")).hexdigest(),
                "tags": tag,
                "geom": geom,
            }
        )

    ctx.features = features
    proceed()


def persistent_vectorize_info(ctx: contexts.Context, proceed):
    """
    持久化矢量化信息。
    """
    vectorize_file_path = ctx.path_info.vectorize_info_dir / f"{ctx.task_id}.json"

    vectorize_info = {
        "id": ctx.task_id,
        "features": ctx.features,
    }
    vectorize_info_str = json.dumps(vectorize_info, ensure_ascii=False)
    with open(vectorize_file_path, "w", encoding="utf8") as f:
        f.write(vectorize_info_str)

    proceed()


def debug_draw_final_graph_group(ctx: contexts.Context, proceed):
    """
    DEBUG: 将 pipeline 上下文绘制为可视化信息。
    """
    if ctx.enable_debug:
        image = Image.fromarray(np.zeros((ctx.image_info.height, ctx.image_info.width, 3), dtype=np.uint8))
        canvas = ImageDraw.Draw(image)
        debug_utils.draw_lines(canvas, ctx.graph_group.get_all_lines())

        save_dir = utils.ensure_dir(ctx.path_info.debug_dir / "final_graph_group_images")
        save_path = save_dir / f"{ctx.image_info.image_path.stem}.png"
        debug_utils.save_pil_image(image, str(save_path))

    proceed()


# help functions:


def init_pseudo_color_image(image_path, image_size):
    """
    初始化伪彩色预测图。
    """
    image = cv2.imread(str(image_path))
    # 若语义分割模型进行了 resize，则输出的 predicate 图片与原尺寸不一致，需要在此处恢复原尺寸。
    image = resume_image_size(image, image_size)
    image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    image[image != BORDER_PSEUDO_COLOR_VALUE] = 0
    # 输入的图片会被当作二值图片，并且细化时，会将 0-127 的色值当作背景色，128-255 当作目标色。
    image[image == BORDER_PSEUDO_COLOR_VALUE] = 255
    image = cv2.ximgproc.thinning(image)
    return image


def init_thinned_image(image_path, image_size):
    """
    初始化细化后的图片。
    """
    image = cv2.imread(str(image_path), cv2.IMREAD_GRAYSCALE)
    image = resume_image_size(image, image_size)
    return image


def init_heatmap_image(image_path, image_size):
    """
    固定阈值法处理热力图。
    """
    # 由于热力图使用 softmax 融合 border 和 background 通道，故其值为 0.5 时，
    # border 概率与 background 相等，以此分界，与 np.argmax() 比较的预测结果等效。
    threshold = 0.5

    image = cv2.imread(str(image_path), cv2.IMREAD_GRAYSCALE)

    threshold = int(threshold * 256)
    image[image >= threshold] = 255
    image[image < threshold] = 0

    image = resume_image_size(image, image_size)
    image = cv2.ximgproc.thinning(image)
    return image


def init_heatmap_adaptive_image(image_path, image_size):
    """
    局部阈值法处理热力图。
    """
    image = cv2.imread(str(image_path))
    image = resume_image_size(image, image_size)
    image = heatmap.thinning_for_line_channel(image)
    return image


def init_heatmap_for_background_channel(image_path, image_size):
    """
    热力图背景通道。
    """
    image = cv2.imread(str(image_path), cv2.IMREAD_GRAYSCALE)
    image = resume_image_size(image, image_size)
    image = heatmap.thinning_for_background_channel(image)
    return image


INIT_IMAGE_STRATEGY = {
    "pseudo-color": init_pseudo_color_image,
    "thinned": init_thinned_image,
    "heatmap": init_heatmap_image,
    "heatmap-adaptive": init_heatmap_adaptive_image,
    "heatmap-adaptive-background": init_heatmap_for_background_channel,
}


def get_image_info(ctx: contexts.Context):
    image_info_path = ctx.path_info.image_info_dir / f"{ctx.task_id}.json"
    return utils.read_json(image_info_path)


def resume_image_size(image: np.ndarray, image_size):
    h, w = image_size
    actual_h, actual_w = image.shape[:2]
    if actual_h != h or actual_w != w:
        return cv2.resize(image, (w, h), interpolation=cv2.INTER_NEAREST)

    return image


def extract_tags_by_ring(prediction_image, polygon: List[Tuple[int, int]]):
    """
    提取 Tag。
    """
    cropped_image = crop_label_image(prediction_image, [[x[1], x[0]] for x in polygon])
    if cropped_image is None:
        return None

    h, w = cropped_image.shape
    cropped_image = cropped_image.reshape((h * w,))
    tags = set(cropped_image)
    return [
        AOI_TAG_INDEX_TO_TAG_NAME[AOI_PSEUDO_COLOR_TO_TAG_INDEX.index(tag) + 2]
        for tag in tags
        if tag in AOI_PSEUDO_COLOR_TO_TAG_INDEX
    ]


def extract_confidence_by_ring(prediction_image, polygon: list[tuple[int, int]]):
    """
    提取置信度。
    """
    cropped_image = crop_label_image(prediction_image, [[x[1], x[0]] for x in polygon])
    if cropped_image is None:
        return None

    h, w = cropped_image.shape
    array = cropped_image.reshape((h * w,))
    total = sum(array)
    area = len([x for x in array if x > 0])
    confidence = 1.0 * total / (area * 255)
    confidence = 1 - confidence  # 因为是从背景通道提取的颜色，所以取反（越黑置信度才越高）
    return [confidence]


def crop_label_image(img, polygon: list[list[int]]):
    """
    多边形裁剪图片。
    Ref to: https://stackoverflow.com/a/48301735
    """
    img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
    pts = np.array(polygon)

    # (1) Crop the bounding rect
    rect = cv2.boundingRect(pts)
    x, y, w, h = rect
    if w == 0 or h == 0:
        return None

    cropped = img[y : y + h, x : x + w].copy()
    h, w = cropped.shape[:2]
    if h == 0 or w == 0:
        return None

    # (2) make mask
    pts = pts - pts.min(axis=0, initial=None)

    mask = np.zeros((h, w), np.uint8)
    cv2.drawContours(mask, [pts], -1, (255, 255, 255), -1, cv2.LINE_AA)

    # (3) do bit-op
    dst = cv2.bitwise_and(cropped, cropped, mask=mask)
    return dst
