# !/usr/bin/env python3
"""
多边形化模块（提取最小圈基）
"""
from typing import Dict, List

from src.tools import minimal_cycle_basis
from src.seg_post_process import models


def clear_end_lines(graph_group: models.GraphGroup):
    """
    清理未成环的线段。
    """
    for graph in list(graph_group.graphs):
        latest_line_count = 0
        while True:
            end_lines = [[*models.search_line(x, x.children[0])] for x in graph.nodes.values() if len(x.children) == 1]
            if latest_line_count == len(end_lines):
                break

            latest_line_count = len(end_lines)
            for end_line in end_lines:
                end_node = end_line[-1]
                sub_end_node = end_line[-2]
                end_node.unbind_two_way(sub_end_node)
                for useless_node in end_line[:-1]:
                    if useless_node.point in graph.nodes:
                        graph.nodes.pop(useless_node.point)

        island_points = [x for x in graph.nodes.values() if len(x.children) == 0]
        for island_point in island_points:
            graph.nodes.pop(island_point.point)

        if len(graph.nodes) == 1 or len(graph.nodes) == 0:
            graph_group.graphs.remove(graph)


def fill_edges(edges: set, node: models.GraphNode, nodes: Dict[models.GraphNode, int]):
    """
    搜索 Graph 的连接关系，并填充到 edges 中。
    """
    current_index = nodes[node]
    for child in node.children:
        next_index = nodes[child]
        edge = (current_index, next_index)
        reverse_edge = (next_index, current_index)
        if edge in edges or reverse_edge in edges:
            continue

        edges.add(edge)
        fill_edges(edges, child, nodes)


def get_edges(start_node: models.GraphNode, nodes: Dict[models.GraphNode, int]):
    """
    返回 Graph 中的连接关系。
    """
    edges = set()
    fill_edges(edges, start_node, nodes)
    return [[x[0], x[1]] for x in edges]


def get_min_rings(graph_group: models.GraphGroup):
    """
    提取 Graph 中的最小圈基。
    """
    result: List[List[models.GraphNode]] = []
    for graph in graph_group.graphs:
        node_items = [([x[0], x[1]], y) for x, y in graph.nodes.items()]
        node_list = [x for _, x in node_items]
        nodes = dict((x[1], i) for i, x in enumerate(node_items))
        positions = [x[0] for x in node_items]
        edges = get_edges(graph.get_first_node(), nodes)
        min_cycle_extractor = minimal_cycle_basis.MinimalCycleBasis(positions=positions,
                                                                    edges=edges,
                                                                    dead_loop_count=5000)
        cycles = min_cycle_extractor.extract_cycles()
        for cycle in cycles:
            result.append([node_list[index] for index in cycle])

    return result
