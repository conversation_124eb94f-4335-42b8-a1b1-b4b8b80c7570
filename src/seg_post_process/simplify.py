# !/usr/bin/env python3
"""
细化处理模块：<PERSON> 算法。
GraphGroup -> GraphGroup
"""
from functools import reduce
from typing import List

from src.seg_post_process import algebra
from src.seg_post_process import models


def simplify_graph(graph_group: models.GraphGroup, threshold=1):
    """
    抽稀 Graph
    """
    for graph in graph_group.graphs:
        head_node = graph.get_first_node()
        end_node = models.first_end_or_fork_node(head_node)
        if len(end_node.children) == 0:
            continue

        for line_segment in models.search_lines(end_node, set()):
            simplify_douglas_peucker(graph, line_segment, threshold)


def simplify_douglas_peucker(graph: models.Graph, edge_nodes: List[models.GraphNode], threshold: float):
    """
    道格拉斯抽稀算法。
    """
    if len(edge_nodes) < 3:
        return

    start_node = edge_nodes[0]
    mid_nodes = edge_nodes[1:-1]
    end_node = edge_nodes[-1]
    get_distance_square = algebra.get_distance_squared_func(start_node.point, end_node.point)
    # i + 1 是因为 mid_nodes 不含 start_node，故索引比 edge_nodes 领先 1 位，而此处需要 edge_nodes 的索引，故 +1 补齐。
    distance_list = [(i + 1, get_distance_square(x.point)) for (i, x) in enumerate(mid_nodes)]
    max_index, max_distance = reduce(lambda max_value, x: x if x[1] > max_value[1] else max_value, distance_list)
    if max_distance > threshold * threshold:
        simplify_douglas_peucker(graph, edge_nodes[:max_index + 1], threshold)
        simplify_douglas_peucker(graph, edge_nodes[max_index:], threshold)
    else:
        # 解除首位节点与中间可替代节点的连接，不必反向解除，因为中间节点接下来会从图中移除。
        start_node.children.remove(mid_nodes[0])
        end_node.children.remove(mid_nodes[-1])
        # 连接首位节点，用单线段代替原先的线段集。
        start_node.bind_two_way(end_node)
        # 移除被抽稀的点。
        for useless_node in mid_nodes:
            # NOTE: 此处不检查 key 是否存在，若不存在，则会崩溃；若崩溃，则一定是算法有 bug！
            # 因为理论上，这里被抽稀掉的节点，其出入度一定为 2，且只可能被抽稀一次，
            # 所以同一个点被抽稀两次，一定是算法有问题，应当解决算法问题，而不是在此处加保护条件。
            graph.nodes.pop(useless_node.point)
