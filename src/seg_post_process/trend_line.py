# !/usr/bin/env python3
"""
修复原始识别 Graph，采用回归直线法。
"""
from functools import reduce
from typing import List

import cv2
import numpy as np
from PIL import Image, ImageDraw

from src.seg_post_process import algebra, debug_utils
from src.seg_post_process import contexts
from src.seg_post_process import models
from src.seg_post_process import parameters
from src.tools import utils

CONFIG = parameters.CONFIG["trend_line"]
MIN_MAIN_LINE_LENGTH: int = CONFIG["min_main_line_length"]
MIN_TREND_LINE_LENGTH: int = CONFIG["min_trend_line_length"]
REGRESSION_SCOPE_THRESHOLD: int = CONFIG["regression_scope_threshold"]
TURNING_THRESHOLD: int = CONFIG["turning_threshold"]

ROTATE_MAT_LEFT = np.array([[0, -1], [1, 0]])
ROTATE_MAT_RIGHT = np.array([[0, 1], [-1, 0]])


def repair_by_trend_line(ctx: contexts.Context, proceed):
    """
    算法入口函数（导出函数）。
    """
    debug_lines = []
    while True:
        connect_pairs = [*find_connect_pairs(ctx.graph_group)]
        connect_pairs = filter_connect_pairs(connect_pairs, ctx.graph_group)
        if len(connect_pairs) == 0:
            break

        for node1, node2 in connect_pairs:
            graph1, _ = ctx.graph_group.get_graph_and_node(node1.point)
            graph2, _ = ctx.graph_group.get_graph_and_node(node2.point)
            ctx.graph_group.merge_graph(graph1, graph2)
            node1.bind_two_way(node2)

        debug_lines.extend(connect_pairs)

    if ctx.enable_debug:
        debug_draw_trend_line(ctx, [(x[0].point, x[1].point) for x in debug_lines])

    proceed()


def filter_connect_pairs(connect_lines, graph_group: models.GraphGroup):
    """
    过滤掉相交的连接点对。
    """
    invalid_lines = set()
    original_lines = [
        [*models.get_line_segments(x)] for x in graph_group.get_all_lines()
    ]
    original_lines = [segment for segments in original_lines for segment in segments]
    # 连接线不能与现存的线段有交点
    for p1, p2 in connect_lines:
        for line in original_lines:
            if is_intersection((p1.point, p2.point), line):
                invalid_lines.add((p1, p2))
                break

    # 连接线不能与其它连接线相交
    for i in range(len(connect_lines)):
        for j in range(i + 1, len(connect_lines)):
            p11, p12 = line1 = connect_lines[i]
            p21, p22 = line2 = connect_lines[j]

            if is_intersection((p11.point, p12.point), (p21.point, p22.point)):
                invalid_lines.add(line1)
                invalid_lines.add(line2)
                break

    return [x for x in connect_lines if x not in invalid_lines]


def is_intersection(line1, line2):
    """
    判断给定的两直线是否相交。
    """
    intersection_point = algebra.calculate_intersection_point(line1, line2)
    if intersection_point is None:
        return False

    p1, p2 = line1
    is_self_node = intersection_point == p1 or intersection_point == p2
    return not is_self_node and intersection_point != algebra.COMMON_LINE


def find_connect_pairs(graph_group: models.GraphGroup):
    """
    找到图中适合连接的点对。
    """
    end_lines = [
        [*models.search_line(x, x.children[0])]
        for x in graph_group.get_all_nodes()
        if len(x.children) == 1
    ]
    linear_end_lines = [
        next(get_linear_segments(x, [*get_turning_point_indexes(x, TURNING_THRESHOLD)]))
        for x in end_lines
    ]
    main_lines = [
        x for x in end_lines if models.calculate_line_length(x) > MIN_MAIN_LINE_LENGTH
    ]
    turning_indexes = [
        [*get_turning_point_indexes(line, TURNING_THRESHOLD)] for line in main_lines
    ]

    for line, indexes in zip(main_lines, turning_indexes):
        segment = next(get_linear_segments(line, indexes))
        line_length = models.calculate_line_length(segment)
        if line_length < MIN_TREND_LINE_LENGTH:
            continue

        reg_vec, mean_point = get_regression_line([x.point for x in segment])
        in_scope = get_in_scope_func(
            reg_vec, mean_point, width=REGRESSION_SCOPE_THRESHOLD
        )
        contained_lines = [
            x for x in linear_end_lines if in_scope(map(lambda node: node.point, x))
        ]
        connect_pair = try_get_connect_pair(segment[0], reg_vec, contained_lines)
        if connect_pair:
            yield connect_pair


def get_linear_segments(line, turning_indexes):
    """
    将给定折线，按拐点索引列表打断为趋近直线的折线段。
    """
    turning_indexes.sort()
    turning_indexes.append(len(line))
    prev_index = 0
    for index in turning_indexes:
        yield line[prev_index : index + 1]
        prev_index = index


def try_get_connect_pair(end_node, direction, contained_lines):
    """
    根据给定点与给定方向，从给定的 contained_lines 线段集中找出最邻近的、符合限制条件的线段，用于连接。
    """
    nearest_nodes = [
        try_get_nearest_node(end_node, direction, x) for x in contained_lines
    ]
    nearest_nodes = [x for x in nearest_nodes if x]
    if not nearest_nodes:
        return None

    _, nearest_node = reduce(
        lambda min_value, x: x if x[0] < min_value[0] else min_value,
        [
            (algebra.calculate_point_distance_squared(end_node.point, x.point), x)
            for x in nearest_nodes
        ],
    )

    return (nearest_node, end_node) if nearest_node else None


def try_get_nearest_node(node, direction, line):
    """
    获取 line 上与 node 距离最近的一个端点，并只在以下条件成立时返回结果，否则均返回 None：
     （1）line 整体处于 node 的 direction 一侧时。
     （2）靠近 node 的端点，其出入度必须为 1。
    """
    node1, node2 = line[0], line[-1]
    vec1 = algebra.vec(node.point, node1.point)
    vec2 = algebra.vec(node.point, node2.point)
    if algebra.dot(direction, vec1) <= 0 or algebra.dot(direction, vec2) <= 0:
        return None

    d1 = algebra.calculate_point_distance_squared(node.point, node1.point)
    d2 = algebra.calculate_point_distance_squared(node.point, node2.point)
    nearest_node = node1 if d1 < d2 else node2
    return nearest_node if len(nearest_node.children) == 1 else None


def get_in_scope_func(reg_vec, reg_point, width: int):
    """
    返回一个函数，该函数用于判断一个点是否在给定直线的 buffer 范围内。
    """
    reg_vec = algebra.resize_vec(reg_vec, width)
    vec_left = np.matmul(ROTATE_MAT_LEFT, reg_vec)
    vec_right = np.matmul(ROTATE_MAT_RIGHT, reg_vec)
    p_left = reg_point + vec_left
    p_right = reg_point + vec_right

    def in_scope(points):
        for point in points:
            vec1 = algebra.vec(p_left, point)
            vec2 = algebra.vec(p_right, point)
            if np.cross(reg_vec, vec1) * np.cross(reg_vec, vec2) > 0:
                return False

        return True

    return in_scope


def get_turning_point_indexes(edge_nodes: List[models.GraphNode], threshold: float):
    """
    获取一条线上大于阈值的拐点，利用道格拉斯抽稀算法。
    """
    if len(edge_nodes) < 3:
        return

    start_node = edge_nodes[0]
    mid_nodes = edge_nodes[1:-1]
    end_node = edge_nodes[-1]
    get_distance_square = algebra.get_distance_squared_func(
        start_node.point, end_node.point
    )
    # i + 1 是因为 mid_nodes 不含 start_node，故索引比 edge_nodes 领先 1 位，而此处需要 edge_nodes 的索引，故 +1 补齐。
    distance_list = [
        (i + 1, get_distance_square(x.point)) for (i, x) in enumerate(mid_nodes)
    ]
    max_index, max_distance = reduce(
        lambda max_value, x: x if x[1] > max_value[1] else max_value, distance_list
    )
    if max_distance > threshold**2:
        yield max_index
        yield from get_turning_point_indexes(edge_nodes[: max_index + 1], threshold)
        yield from get_turning_point_indexes(edge_nodes[max_index:], threshold)


def get_regression_line(points):
    """
    计算回归直线。
    """
    vec_x, vec_y, mean_x, mean_y = cv2.fitLine(
        np.array(points), cv2.DIST_L2, 0, 0.01, 0.01
    ).reshape(4)
    start_x, start_y = points[0]
    # 直线的方向：由内部指向第一个点。
    line_vec = algebra.vec((mean_x, mean_y), (start_x, start_y))
    if np.dot(line_vec, (vec_x, vec_y)) < 0:
        vec_x, vec_y = -vec_x, -vec_y

    return (vec_x, vec_y), (mean_x, mean_y)


# DEBUG UTILS:
def debug_draw_trend_line(ctx: contexts.Context, trend_lines):
    """
    可视化延长线为图片，供 DEBUG 使用。
    """
    image = Image.fromarray(
        np.zeros((ctx.image_info.height, ctx.image_info.width, 3), dtype=np.uint8)
    )
    canvas = ImageDraw.Draw(image)

    debug_utils.draw_lines(canvas, ctx.graph_group.get_all_lines())
    debug_utils.draw_lines(canvas, trend_lines, fill=(0, 255, 0))
    # debug_utils.draw_points(canvas, [line[0] for line in trend_lines], fill=(0, 255, 255))
    # debug_utils.draw_points(canvas, [line[1] for line in trend_lines], fill=(0, 255, 255))

    save_dir = utils.ensure_dir(ctx.path_info.debug_dir / "trend_line_images")
    save_path = save_dir / ctx.image_info.image_path.name
    debug_utils.save_pil_image(image, str(save_path))
