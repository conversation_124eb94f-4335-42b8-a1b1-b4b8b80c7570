# !/usr/bin/env python3
"""
矢量化模块：负责将由识别结果细化后的图片处理为矢量数据。
ThinnedImage -> GraphGroup
注意：此模块输出的 Graph 为相邻像素的连通结果，因此节点数极多，需要经由抽稀后方可正常使用。
"""
import numpy as np
from numba import jit
from src.seg_post_process import models

# 旋转矩阵表：按8连通搜索时，向前的5个搜索方向。
ROTATE_MATRIX = [
    np.array([[0, -1], [1, 0]]),
    np.array([[1, -1], [1, 1]]),
    np.array([[1, 0], [0, 1]]),
    np.array([[1, 1], [-1, 1]]),
    np.array([[0, 1], [-1, 0]]),
]
# 以下3组常量，为确保4连通优先原则。
# 禁用方向列表：其索引与 ROTATE_MATRIX 对应，当已在某个方向上找到连通像素时，
# 其方向索引对应的 BAN_DIRECTION_TABLE 中的列表中的索引将被禁用。
BAN_DIRECTION_TABLE = [[1], [0, 2], [1, 3], [2, 4], [3]]
DIRECTION4 = [0, 2, 4]
DIRECTION8 = [1, 3]

SEARCH_4CONNECTED_OFFSET = [(0, 1), (1, 0), (0, -1), (-1, 0)]
SEARCH_8CONNECTED_OFFSET = [(1, 1), (1, -1), (-1, -1), (-1, 1)]
BAN_INDEX = [[3, 0], [0, 1], [1, 2], [2, 3]]


def vectorize_image(image: np.ndarray, graph_group: models.GraphGroup):
    """
    矢量化图片
    """
    for x, y, pixel in iter_image(image):
        if pixel == 0:
            continue

        first_point = (x, y)
        if first_point in graph_group:
            continue

        graph = graph_group.create_graph()
        _, first_node = graph.get_or_create_node(first_point)
        graph.nodes[first_point] = first_node

        for second_point in get_adjacent_points_8connected(image, first_point):
            _, second_node = graph.get_or_create_node(second_point)
            first_node.bind_two_way(second_node)
            search_connected_block(image, graph, second_node, first_node)


@jit(nopython=True)
def iter_image(image: np.ndarray):
    """
    逐像素迭代一张 ndarray 图片

    REMARKS: 整个算法有 90% 的时间耗费在 `image[x, y]` 这句上，故采用 jit 优化，优化结果如下：
    选取建筑物识别结果 afs: aoi-ml/build/tile_map 下 `upernet_rslm_dv2` 结果：100002, 100007, 100012, 100017, 100022
    共计 20 张识别结果图。
    cp39  - no-jit: 95s
    cp312 - no-jit: 88s
    cp39  -    jit: 37s
    cp312 -    jit: 29s
    """
    h, w = image.shape
    for x in range(h):
        for y in range(w):
            yield x, y, image[x, y]


def get_points_8connected(image, current_point, prev_point):
    """
    按 8 连通的规则获取 current_point 的所有连通像素的坐标。
    """
    direction_vector = np.subtract(current_point, prev_point)
    is_slanted = direction_vector[0] != 0 and direction_vector[1] != 0
    first_indexes, second_indexes = (DIRECTION8, DIRECTION4) if is_slanted else (DIRECTION4, DIRECTION8)

    def search_next_point(index):
        """
        获取下一个连通点。
        """
        h, w = image.shape
        matrix = ROTATE_MATRIX[index]
        delta = np.matmul(matrix, direction_vector)
        delta = (delta[0] >> 1, delta[1] >> 1) if (index >> 1 << 1 != index) and is_slanted else delta
        next_x, next_y = np.add(current_point, delta)
        if next_x < 0 or next_y < 0 or next_x >= h or next_y >= w:
            return None

        return (next_x, next_y) if image[next_x, next_y] > 0 else None

    ban_direction_indexes = [False, False, False, False, False]
    for i in first_indexes:
        next_point = search_next_point(i)
        if next_point:
            for ban_index in BAN_DIRECTION_TABLE[i]:
                ban_direction_indexes[ban_index] = True

            yield next_point

    for i in second_indexes:
        if ban_direction_indexes[i]:
            continue

        next_point = search_next_point(i)
        if next_point:
            yield next_point


def search_connected_block(
    image: np.ndarray,
    graph: models.Graph,
    current_node: models.GraphNode,
    prev_node: models.GraphNode,
):
    """
    扫描给定的 image 中的像素连通块，并填充至 graph 中。
    """
    while True:
        graph.nodes[current_node.point] = current_node
        next_points = [*get_points_8connected(image, current_node.point, prev_node.point)]
        adjacent_count = len(next_points)

        is_new = False
        for next_point in next_points:
            is_new, next_node = graph.get_or_create_node(next_point)
            current_node.bind_two_way(next_node)
            if not is_new:
                continue

            if adjacent_count > 1:
                search_connected_block(image, graph, next_node, current_node)
            else:
                current_node, prev_node = next_node, current_node

        if adjacent_count != 1 or not is_new:
            break


def get_adjacent_points_8connected(image, point):
    """
    获取给定点的相邻点，按 8 连通规则。
    """
    h, w = image.shape
    x, y = point

    ban_record = [0, 0, 0, 0]
    for i, (offset_x, offset_y) in enumerate(SEARCH_4CONNECTED_OFFSET):
        next_x, next_y = x + offset_x, y + offset_y
        if next_x < 0 or next_y < 0 or next_x >= h or next_y >= w:
            continue

        if image[next_x, next_y] > 0:
            for ban_i in BAN_INDEX[i]:
                ban_record[ban_i] = 1

            yield next_x, next_y

    for i, (offset_x, offset_y) in enumerate(SEARCH_8CONNECTED_OFFSET):
        if ban_record[i]:
            continue

        next_x, next_y = x + offset_x, y + offset_y
        if next_x < 0 or next_y < 0 or next_x >= h or next_y >= w:
            continue

        if image[next_x, next_y] > 0:
            yield next_x, next_y
