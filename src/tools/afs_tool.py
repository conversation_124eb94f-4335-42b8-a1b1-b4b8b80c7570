# !/usr/bin/env python3

import os

import src.tools.conf_tools as ct
from src.tools import function as F


class AfsTool:
    """
    afs操作方法
    """

    def __init__(self, cluster="aries"):
        self.shell = ""
        self.username = ""
        self.password = ""
        self.host = ""
        self.cmd_base = ""
        afs_list = ct.get_config("afs")
        if cluster not in afs_list:
            print("afs 没有配置")
            return
        afs_cfg = afs_list[cluster]
        self.shell = afs_cfg["shell"]
        self.username = afs_cfg["username"]
        self.password = afs_cfg["password"]
        self.host = "afs://" + afs_cfg["host"]
        self.set_shell(self.shell)

    def set_shell(self, shell):
        """
        设置 afs 可执行文件路径
        """
        self.shell = shell
        self.cmd_base = f"{self.shell} --username={self.username} --password={self.password}"

    def list(self, directory=""):
        if directory == "":
            print("文件夹为空")
            return
        return self._exec(f"ls {self.host}/{directory}")

    def get(self, remote_file, local_dir):
        """
        下载远程文件到本地
        :param remote_file:
        :param local_dir:
        :return:
        """
        os.makedirs(local_dir, exist_ok=True)
        return self._exec(f"get {self.host}/{remote_file} {local_dir}")

    def put(self, local_file, remote_dir):
        """
        本地文件（文件夹）上传到远程
        :param local_file:
        :param remote_dir:
        :return:
        """
        # self.mkdir(remote_dir)
        self._exec(f"put --override {local_file} {self.host}/{remote_dir}")

    def mkdir(self, remote_dir):
        self._exec(f"mkdir {self.host}/{remote_dir}/")

    def test(self, remote_path, mode: str):
        code, _ = self._exec(f"test {mode} {self.host}/{remote_path}")
        return code == 0

    def rm(self, remote_path, recursive=False):
        cmd = f"rm --recursive {self.host}/{remote_path}" if recursive else f"rm {self.host}/{remote_path}"
        self._exec(cmd)

    def _exec(self, cmd=""):
        return F.exec_shell_cmd(f"{self.cmd_base} {cmd}")


if __name__ == "__main__":
    afs = AfsTool()
    afs.mkdir("/user/map-data-streeview/aoi-ml/test/test1")
    afs.put(
        "/root/codes/baidu_beec_aoi-ml/baidu/beec/aoi-ml/flow_data/aoi_seg_post_job/log/afs_shell.log",
        "/user/map-data-streeview/aoi-ml/test/test1",
    )
    # afs.get("/user/map-data-streeview/aoi-ml/ttfa/ttfa_top40.tar", os.path.dirname(__file__))
