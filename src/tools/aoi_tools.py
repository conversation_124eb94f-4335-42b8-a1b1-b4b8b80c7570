# !/usr/bin/env python3
import json
import os
import traceback
from typing import Dict

import numpy
import requests
from shapely import wkt, geometry, ops
from functools import partial
from shapely.validation import make_valid
import pyproj
import math

ROOT_PATH = os.path.abspath(os.path.dirname(__file__) + "/../../")


def get_mesh_list(cityname: str):
    """
    从网络获取城市的图幅号列表
    :param cityname 城市名称 如上海市
    :return: [] 数组，各元素为图幅号
    """
    mesh_list = []
    url = "http://gzbh-map-safetyzone-sz36.gzbh.baidu.com:8080/dataservice/api/meshconfapi"
    params = {"action": "getcitymeshlist", "cityname": cityname}
    params = json.dumps(params)
    print(params)
    ret = requests.post(url, params)
    if ret.status_code != 200:
        return mesh_list
    resp = ret.json()
    msgcode = resp["code"]
    if msgcode != 0:
        return mesh_list
    for mesh_item in resp["data"]["rows"]:
        mesh_list.append(mesh_item["mesh_id"])
    return mesh_list


def get_area(wkt_str: str = ""):
    """计算指定区域的面积
    :param wkt_str 区域的wkt_string
    :return float
    """
    geom = make_valid(wkt.loads(wkt_str))
    geom_area = ops.transform(
        partial(
            pyproj.transform,
            pyproj.Proj(init="EPSG:4326"),
            pyproj.Proj(proj="aea", lat_1=geom.bounds[1], lat_2=geom.bounds[3]),
        ),
        geom,
    )
    return geom_area.area


def calc_iou(wkt_geom1: str, wkt_geom2: str) -> float:
    """
    计算两个区域的交集与并集比
    :param wkt_geom1 第一个区域的wkt
    :param wkt_geom2 第二个区域的wkt
    """
    try:
        w1 = wkt.loads(wkt_geom1)
        w2 = wkt.loads(wkt_geom2)
        # 交集
        wi = w1.intersection(w2)
        if wi.is_empty:
            return 0
        a_wi = get_area(wi.wkt)
        # 并集
        wu = w1.union(w2)
        a_wu = get_area(wu.wkt)
        return a_wi / a_wu
    except Exception as e:
        # 有错误，记录错误信息
        print("[error]", e, wkt_geom1, wkt_geom2)
        print(traceback.format_exc())
        return 0


def get_aoi_tag_weight() -> Dict[str, int]:
    """
    获取标签权重
    """
    tag_dict = {}
    with open(ROOT_PATH + "/resources/tags_weights.txt", "r") as f:
        data = f.read().split("\n")
        for line in data:
            tag_line = line.strip().split(" ")
            if len(tag_line) < 2:
                continue
            tag_dict[tag_line[0]] = int(tag_line[1])
    return tag_dict


def chaikins_corner_cutting(coords, refinements=5):
    """平滑处理
    :param coords: 坐标
    :param refinements:  平滑次数
    :return: 平滑后的坐标
    """
    coords = numpy.array(coords)
    for _ in range(refinements):
        L = coords.repeat(2, axis=0)
        R = numpy.empty_like(L)
        R[0] = L[0]
        R[2::2] = L[1:-1:2]
        R[1:-1:2] = L[2::2]
        R[-1] = L[-1]
        coords = L * 0.75 + R * 0.25
    return coords


def cal_mesh_id(x: float, y: float):
    """
        根据传入的坐标计算所在的图幅号，传入坐标为国测局经纬度信息
    Args:
        x: 经度坐标
        y: 纬度坐标
    Return:
        ret: 该经纬度对应的图幅号
    """
    intm1m2 = math.floor(y * 1.5)
    intj1 = math.floor(x)
    dblj2 = x - math.floor(x)
    intm3m4 = math.floor(intj1 - 60)
    intm5 = math.floor((y - intm1m2 / 1.5) * 12.0)
    intm6 = math.floor(dblj2 * 8.0)
    if (
        intm1m2 < 0
        or intm1m2 > 99
        or intm3m4 < 0
        or intm3m4 > 99
        or intm5 < 0
        or intm5 > 7
        or intm6 < 0
        or intm6 > 7
    ):
        return 0
    ret = int(intm1m2) * 10000 + int(intm3m4) * 100 + int(intm5) * 10 + int(intm6)
    return ret


def get_important_aoi_tag():
    return {
        "购物;购物中心",
        "医疗;综合医院",
        "医疗;专科医院",
        "教育培训;高等院校",
        "文化传媒;展览馆",
        "休闲娱乐;剧院",
        "运动健身;体育场馆",
    }
