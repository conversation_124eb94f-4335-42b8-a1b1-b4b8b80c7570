# !/usr/bin/env python3
"""
封装了 bos 相关的帮助方法
"""

from baidubce.auth import bce_credentials
from baidubce.bce_client_configuration import BceClientConfiguration
from baidubce.services.bos import bos_client

AK = "11b2eec931ed4613981861844b252dad"
SK = "bf41defd09cf4b2590d4a876676ef598"
ENDPOINT = "su.bcebos.com"
API = "https://lutao-pic.su.bcebos.com"
BUCKET = "lutao-pic"


class BosClient:
    """
    BosClient 类
    """

    def __init__(self, bos_dir_name):
        self.client = bos_client.BosClient(BceClientConfiguration(
            credentials=bce_credentials.BceCredentials(AK, SK),
            endpoint=ENDPOINT
        ))
        self.bos_dir_name = bos_dir_name

    def put_object_from_file(self, key, file_path):
        """
        上传文件到 bos
        """
        self.client.put_object_from_file(
            bucket=BUCKET,
            key=f'{self.bos_dir_name}/{key}',
            file_name=file_path,
        )
        return f'{API}/{self.bos_dir_name}/{key}'

    def get_object(self, key):
        """
        从 bos 下载数据
        """
        return self.client.get_object(bucket_name=BUCKET, key=f'{self.bos_dir_name}/{key}')

    def delete_object(self, key):
        """
        删除 bos 上的数据
        """
        return self.client.delete_object(bucket_name=BUCKET, key=f'{self.bos_dir_name}/{key}')
