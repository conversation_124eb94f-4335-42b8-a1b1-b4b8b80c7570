# !/usr/bin/env python3
import json
import os.path
import sys
from typing import List

root_path = os.path.abspath(os.path.dirname(__file__) + "/../../")
sys.path.insert(0, root_path)
from src.tools.aoi_tools import get_mesh_list


def top40_city() -> List[str]:
    """
    top40 城市
    """
    return ['常州市', '武汉市', '贵阳市', '南通市', '天津市', '北京市', '合肥市', '泉州市', '沈阳市', '哈尔滨市', '青岛市', '金华市', '东莞市', '佛山市',
            '成都市', '中山市', '无锡市', '厦门市', '昆明市', '苏州市', '郑州市', '南宁市', '嘉兴市', '重庆市', '石家庄市', '西安市', '广州市', '深圳市',
            '杭州市', '温州市', '济南市', '临沂市', '长沙市', '南京市', '长春市', '惠州市', '宁波市', '南昌市', '福州市', '上海市']


def top40_pinying() -> List[str]:
    return ['changzhoushi', 'wuhanshi', 'guiyangshi', 'nantongshi', 'tianjinshi', 'beijingshi', 'hefeishi',
            'quanzhoushi', 'shenyangshi', 'haerbinshi', 'qingdaoshi', 'jinhuashi', 'dongwanshi', 'foshanshi',
            'chengdoushi', 'zhongshanshi', 'wuxishi', 'shamenshi', 'kunmingshi', 'suzhoushi', 'zhengzhoushi',
            'nanningshi', 'jiaxingshi', 'zhongqingshi', 'shijiazhuangshi', 'xianshi', 'guangzhoushi', 'shenzhenshi',
            'hangzhoushi', 'wenzhoushi', 'jinanshi', 'linyishi', 'zhangshashi', 'nanjingshi',
            'zhangchunshi', 'huizhoushi', 'ningboshi', 'nanchangshi', 'fu2zhoushi', 'shanghaishi']


def city_41_200() -> List[str]:
    return [
        "太原市", "大连市", "台州市", "江门市", "潍坊市", "保定市", "洛阳市", "滁州市", "徐州市", "海口市", "绍兴市", "珠海市", "乌鲁木齐市", "廊坊市", "扬州市",
        "济宁市", "湖州市", "赣州市", "烟台市", "盐城市", "汕头市", "唐山市", "邯郸市", "三亚市", "泰州市", "呼和浩特市", "南阳市", "淮安市", "兰州市", "咸阳市",
        "揭阳市", "湛江市", "清远市", "桂林市", "菏泽市", "连云港市", "镇江市", "阜阳市", "沧州市", "商丘市", "新乡市", "漳州市", "茂名市", "淄博市", "周口市", "衡阳市",
        "威海市", "肇庆市", "株洲市", "潮州市", "遵义市", "宿州市", "开封市", "安阳市", "绵阳市", "泰安市", "邢台市", "柳州市", "上饶市", "德阳市", "银川市", "芜湖市",
        "常德市", "德州市", "岳阳市", "大理白族自治州", "宜昌市", "襄阳市", "许昌市", "宿迁市", "驻马店市", "河源市", "眉山市", "日照市", "聊城市", "信阳市", "九江市",
        "毕节地区", "荆州市", "梅州市", "曲靖市", "红河哈尼族彝族自治州", "湘潭市", "玉林市", "滨州市", "秦皇岛市", "乐山市", "六安市", "鄂尔多斯市", "东营市", "宜宾市",
        "郴州市", "马鞍山市", "黄冈市", "贵港市", "莆田市", "平顶山市", "阳江市", "焦作市", "濮阳市", "晋中市", "南充市", "包头市", "龙岩市", "蚌埠市", "张家口市",
        "宁德市", "亳州市", "渭南市", "安庆市", "凉山彝族自治州", "衡水市", "百色市", "安顺市", "北海市", "宣城市", "泸州市", "鞍山市", "西宁市", "衢州市", "韶关市",
        "榆林市", "吉安市", "宜春市", "大同市", "孝感市", "长治市", "枣庄市", "汉中市", "黔东南苗族侗族自治州", "邵阳市", "云浮市", "娄底市", "运城市", "怀化市",
        "黔南布依族苗族自治州", "临汾市", "益阳市", "大庆市", "宝鸡市", "西双版纳傣族自治州", "丽水市", "黄山市", "来宾市", "漯河市", "内江市", "抚州市", "永州市", "钦州市",
        "荆门市", "赤峰市", "丽江市", "南平市", "玉溪市", "承德市", "十堰市", "淮南市", "汕尾市", "吉林市", "梧州市"
    ]


def city_41_200_pinying() -> List:
    return [
        "taiyuanshi", "dalianshi", "tai2zhoushi", "jiangmenshi", "weifangshi", "baodingshi", "luoyangshi", "chuzhoushi",
        "xuzhoushi", "haikoushi", "shaoxingshi", "zhuhaishi", "wulumuqishi", "langfangshi", "yangzhoushi",
        "jiningshi", "huzhoushi", "ganzhoushi", "yantaishi", "yanchengshi", "shantoushi", "tangshanshi", "handanshi",
        "sanyashi", "tai4zhoushi", "huhehaoteshi", "nanyangshi", "huaianshi", "lanzhoushi", "xianyangshi",
        "jieyangshi", "zhanjiangshi", "qingyuanshi", "guilinshi", "hezeshi", "lianyungangshi", "zhenjiangshi",
        "fuyangshi", "cangzhoushi", "shangqiushi", "xinxiangshi", "zhangzhoushi", "maomingshi", "ziboshi", "zhoukoushi",
        "hengyangshi",
        "weihaishi", "zhaoqingshi", "zhuzhoushi", "chaozhoushi", "zunyishi", "xiuzhoushi", "kaifengshi", "anyangshi",
        "mianyangshi", "taianshi", "xingtaishi", "liuzhoushi", "shangraoshi", "deyangshi", "yinchuanshi", "wuhushi",
        "changdeshi", "dezhoushi", "yueyangshi", "dalibaizuzizhizhou", "yichangshi", "xiangyangshi", "xuchangshi",
        "xiuqianshi", "zhumadianshi", "heyuanshi", "meishanshi", "rizhaoshi", "liaochengshi", "xinyangshi",
        "jiujiangshi",
        "bijiediqu", "jingzhoushi", "meizhoushi", "qujingshi", "honghehanizuyizuzizhizhou", "xiangtanshi", "yu4linshi",
        "binzhoushi", "qinhuangdaoshi", "leshanshi", "liuanshi", "eerduosishi", "dongyingshi", "yibinshi",
        "chenzhoushi", "maanshanshi", "huanggangshi", "guigangshi", "putianshi", "pingdingshanshi", "yangjiangshi",
        "jiaozuoshi", "puyangshi", "jinzhongshi", "nanchongshi", "baotoushi", "longyanshi", "bangbushi",
        "zhangjiakoushi",
        "ningdeshi", "bozhoushi", "weinanshi", "anqingshi", "liangshanyizuzizhizhou", "hengshuishi", "baiseshi",
        "anshunshi", "beihaishi", "xuanchengshi", "luzhoushi", "anshanshi", "xiningshi", "quzhoushi", "shaoguanshi",
        "yu2linshi", "jianshi", "yi2chunshi", "datongshi", "xiaoganshi", "zhangzhishi", "zaozhuangshi", "hanzhongshi",
        "qiandongnanmiaozudongzuzizhizhou", "shaoyangshi", "yunfushi", "loudishi", "yunchengshi", "huaihuashi",
        "qiannanbuyizumiaozuzizhizhou", "linfenshi", "yiyangshi", "daqingshi", "baojishi",
        "xishuangbannadaizuzizhizhou", "lishuishi", "huangshanshi", "laibinshi", "luoheshi", "neijiangshi",
        "fu3zhoushi",
        "yongzhoushi", "qinzhoushi",
        "jingmenshi", "chifengshi", "lijiangshi", "nanpingshi", "yuxishi", "chengdeshi", "shiyanshi", "huainanshi",
        "shanweishi", "jilinshi", "wuzhoushi"
    ]


def city_201_360() -> List[str]:
    """
    > 201以上
    :return:
    """
    return [
        "舟山市", "崇左市", "锦州市", "阿拉善盟", "拉萨市", "黔西南布依族苗族自治州", "河池市", "楚雄彝族自治州", "晋城市", "咸宁市", "自贡市", "淮北市", "黄石市", "达州市",
        "保山市", "昭通市", "朝阳市", "忻州市", "景德镇市", "延安市", "喀什地区", "通辽市", "香港特别行政区", "六盘水市", "三门峡市", "遂宁市", "铜仁地区", "齐齐哈尔市",
        "文山壮族苗族自治州", "营口市", "巴音郭楞蒙古自治州", "阿克苏地区", "吕梁市", "普洱市", "三明市", "广安市", "恩施土家族苗族自治州", "安康市", "抚顺市", "防城港市", "广元市",
        "葫芦岛市", "资阳市", "池州市", "随州市", "贺州市", "湘西土家族苗族自治州", "伊犁哈萨克自治州", "盘锦市", "鄂州市", "乌兰察布市", "鹤壁市", "昌吉回族自治州", "雅安市",
        "甘孜藏族自治州", "德宏傣族景颇族自治州", "儋州市", "丹东市", "铜陵市", "天水市", "绥化市", "铁岭市", "萍乡市", "辽阳市", "张家界市", "阜新市", "阿坝藏族羌族自治州",
        "临沧市", "巴彦淖尔市", "佳木斯市", "朔州市", "松原市", "巴中市", "吴忠市", "酒泉市", "定西市", "四平市", "延边朝鲜族自治州", "庆阳市", "陵水黎族自治县", "平凉市",
        "万宁市", "商洛市", "琼海市", "呼伦贝尔市", "白城市", "陇南市", "阳泉市", "和田地区", "牡丹江市", "武威市", "仙桃市", "新余市", "铜川市", "澄迈县", "白银市",
        "澳门特别行政区", "鹰潭市", "攀枝花市", "林芝市", "文昌市", "济源市", "通化市", "海西蒙古族藏族自治州", "锡林郭勒盟", "张掖市", "兴安盟", "海东地区", "中卫市",
        "本溪市", "迪庆藏族自治州", "固原市", "哈密市", "临夏回族自治州", "天门市", "鸡西市", "乐东黎族自治县", "石嘴山市", "克拉玛依市", "昌都地区", "潜江市", "黑河市",
        "吐鲁番市", "乌海市", "辽源市", "白山市", "塔城地区", "嘉峪关市", "定安县", "东方市", "双鸭山市", "怒江傈僳族自治州", "石河子市", "甘南藏族自治州", "保亭黎族苗族自治县",
        "阿勒泰地区", "鹤岗市", "日喀则地区", "海南藏族自治州", "博尔塔拉蒙古自治州", "昌江黎族自治县", "临高县", "金昌市", "山南市", "伊春市", "七台河市", "阿拉尔市",
        "琼中黎族苗族自治县", "巢湖市", "那曲市", "五指山市", "白沙黎族自治县", "屯昌县", "克孜勒苏柯尔克孜自治州", "海北藏族自治州", "五家渠市", "大兴安岭地区", "图木舒克市",
        "果洛藏族自治州", "玉树藏族自治州", "神农架林区", "阿里地区", "黄南藏族自治州", "台北市", "新北市", "桃园市", "高雄市", "台中市", "云林县", "台南市", "宜兰县",
        "南投县", "屏东县", "新竹县", "彰化县", "花莲县", "嘉义县", "苗栗县", "台东县", "基隆市", "新竹市", "澎湖县", "嘉义市", "南沙群岛", "西沙群岛", "东沙群岛"
    ]


def city_210_360_pinying() -> List[str]:
    return [
        "zhoushanshi", "chongzuoshi", "jinzhoushi", "alashanmeng", "lasashi", "qianxinanbuyizumiaozuzizhizhou",
        "hechishi", "chuxiongyizuzizhizhou", "jinchengshi", "xianningshi", "zigongshi", "huaibeishi", "huangshishi",
        "dazhoushi",
        "baoshanshi", "zhaotongshi", "chaoyangshi", "xinzhoushi", "jingdezhenshi", "yananshi", "kashidiqu",
        "tongliaoshi", "xianggangtebiehangzhengqu", "liupanshuishi", "sanmenxiashi", "suiningshi", "tongrendiqu",
        "qiqihaershi",
        "wenshanzhuangzumiaozuzizhizhou", "yingkoushi", "bayinguolengmengguzizhizhou", "akesudiqu", "lüliangshi",
        "puershi", "sanmingshi", "guanganshi", "enshitujiazumiaozuzizhizhou", "ankangshi", "fushunshi",
        "fangchenggangshi", "guangyuanshi",
        "huludaoshi", "ziyangshi", "chizhoushi", "suizhoushi", "hezhoushi", "xiangxitujiazumiaozuzizhizhou",
        "yilihasakezizhizhou", "panjinshi", "ezhoushi", "wulanchabushi", "hebishi", "changjihuizuzizhizhou", "yaanshi",
        "ganzicangzuzizhizhou", "dehongdaizujingpozuzizhizhou", "danzhoushi", "dandongshi", "tonglingshi",
        "tianshuishi", "suihuashi", "tielingshi", "pingxiangshi", "liaoyangshi", "zhangjiajieshi", "fuxinshi",
        "abacangzuqiangzuzizhizhou",
        "lincangshi", "bayannaoershi", "jiamusishi", "shuozhoushi", "songyuanshi", "bazhongshi", "wuzhongshi",
        "jiuquanshi", "dingxishi", "sipingshi", "yanbianchaoxianzuzizhizhou", "qingyangshi", "lingshuilizuzizhixian",
        "pingliangshi",
        "wanningshi", "shangluoshi", "qionghaishi", "hulunbeiershi", "baichengshi", "longnanshi", "yangquanshi",
        "hetiandiqu", "mudanjiangshi", "wuweishi", "xiantaoshi", "xinyushi", "tongchuanshi", "chengmaixian",
        "baiyinshi",
        "aomentebiehangzhengqu", "yingtanshi", "panzhihuashi", "linzhishi", "wenchangshi", "jiyuanshi", "tonghuashi",
        "haiximengguzucangzuzizhizhou", "xilinguolemeng", "zhangyeshi", "xinganmeng", "haidongdiqu", "zhongweishi",
        "benxishi", "diqingcangzuzizhizhou", "guyuanshi", "hamishi", "linxiahuizuzizhizhou", "tianmenshi", "jixishi",
        "ledonglizuzizhixian", "shizuishanshi", "kelamayishi", "changdoudiqu", "qianjiangshi", "heiheshi",
        "tulufanshi", "wuhaishi", "liaoyuanshi", "baishanshi", "tachengdiqu", "jiayuguanshi", "dinganxian",
        "dongfangshi", "shuangyashanshi", "nujianglisuzuzizhizhou", "shihezishi", "gannancangzuzizhizhou",
        "baotinglizumiaozuzizhixian",
        "aletaidiqu", "hegangshi", "rikazediqu", "hainancangzuzizhizhou", "boertalamengguzizhizhou",
        "changjianglizuzizhixian", "lingaoxian", "jinchangshi", "shannanshi", "yi1chunshi", "qitaiheshi", "alaershi",
        "qiongzhonglizumiaozuzizhixian", "chaohushi", "naqushi", "wuzhishanshi", "baishalizuzizhixian", "tunchangxian",
        "kezilesukeerkezizizhizhou", "haibeicangzuzizhizhou", "wujiaqushi", "daxinganlingdiqu", "tumushukeshi",
        "guoluocangzuzizhizhou", "yushucangzuzizhizhou", "shennongjialinqu", "alidiqu", "huangnancangzuzizhizhou",
        "taibeishi", "xinbeishi", "taoyuanshi", "gaoxiongshi", "taizhongshi", "yunlinxian", "tainanshi",
        "yilanxian",
        "nantouxian", "pingdongxian", "xinzhuxian", "zhanghuaxian", "hualianxian", "jiayixian", "miaolixian",
        "taidongxian", "jilongshi", "xinzhushi", "penghuxian", "jiayishi", "nanshaqundao", "xishaqundao",
        "dongshaqundao"
    ]


def all_city() -> List[str]:
    """
    中国所有城市，如果不全的话自行补充
    """
    return [*top40_city(), *city_41_200(), *city_201_360()]


def meshid_to_city(mesh_id: str) -> str:
    """
    图幅号转对应的城市
    :mesh_id 图幅号： 如 str(702712) (需要转一下string)
    返回中文地址：阿勒泰地区
    """
    path = root_path + "/resources/mesh2city.json"
    with open(path, 'r') as f:
        mesh_map = json.load(f)
        return mesh_map.get(mesh_id, "")


def create_meshiid_city_cache():
    """
    生成meshid对应的缓存
    :return:
    """
    all = all_city()
    mesh_map = dict()
    for city in all:
        meshes = get_mesh_list(city)
        for i in meshes:
            mesh_map[i] = city
    with open(root_path + "/resources/mesh2city.json", 'w') as f:
        json.dump(mesh_map, f, indent=2, ensure_ascii=False)


def create_pinyin_cache():
    """
    创建拼音对照表
    :return:
    """
    all_list = [*list(zip(top40_city(), top40_pinying())), *list(zip(city_41_200(), city_41_200_pinying())),
                *list(zip(city_201_360(), city_210_360_pinying()))]
    all_map = {}
    for item in all_list:
        city_name, city_pinying = item
        all_map[city_name] = city_pinying
    with open(root_path + "/resources/city_pinying.json", 'w') as f:
        json.dump(all_map, f, indent=2, ensure_ascii=False)


def get_city_pinying(city_zhcn):
    """
    获取指定城市的拼音
    :param city_zhcn: 中文
    :return:
    """
    all_list = [*list(zip(top40_city(), top40_pinying())), *list(zip(city_41_200(), city_41_200_pinying())),
                *list(zip(city_201_360(), city_210_360_pinying()))]
    all_map = {}
    for item in all_list:
        city_name, city_pinying = item
        all_map[city_name] = city_pinying
    return all_map.get(city_zhcn, "")


if __name__ == '__main__':
    # create_meshiid_city_cache()
    # create_pinyin_cache()
    print(get_city_pinying("上海市"))
