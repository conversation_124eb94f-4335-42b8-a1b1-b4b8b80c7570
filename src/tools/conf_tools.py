# !/usr/bin/env python3
import os
import subprocess
from typing import Dict
from typing import Tuple

import yaml

# 项目根目录
root_path = os.path.dirname(os.path.dirname(__file__) + "/../../")


class ConfigLoader:
    """
    配置文件加载,只加载一次
    """

    def __init__(self):
        self.conf_data = dict()
        yaml_path = os.path.join(root_path, "conf/config.yml")
        if os.path.exists(root_path + "/.develop"):
            yaml_path = os.path.join(root_path, "conf/config_test.yml")
        print("开始解析yaml：{}".format(yaml_path))
        if not os.path.exists(yaml_path):
            print("读取配置文件{}失败".format(yaml_path))
            exit(-1)
        with open(yaml_path, 'r', encoding="utf-8") as file:
            file_data = file.read()
            self.conf_data = yaml.load(file_data, Loader=yaml.Loader)

    def get_conf_detail(self, conf_name) -> Dict:
        """
        获取指定的配置
        :return:dict
        """
        if conf_name in self.conf_data:
            return self.conf_data[conf_name]
        else:
            return dict()


loader = ConfigLoader()


def get_config(conf_name) -> Dict:
    """ 获取配置信息
    :param conf_name: 配置文件名称
    :return:
    """
    return loader.get_conf_detail(conf_name)


def get_pg_conf(db_name) -> Dict:
    pg_conf = get_config('pg')
    if pg_conf is not None and pg_conf.keys().__contains__(db_name):
        return pg_conf.get(db_name)
    else:
        print("未配置数据库链接")
        return dict()


def is_debug():
    """ 是否是测试环境
    :return: boolean
    """
    if get_config("run_mode") == "debug":
        return True
    else:
        return False


def get_redis_conf(name='default') -> Tuple[str, int, str]:
    """
    获取redis的配置
    :param name: 配置信息
    :return:
    """
    redis_conf = get_config('redis')
    if name not in redis_conf:
        print(f"redis {name} 没有配置")
        return "", 0, ""
    else:
        cfg = redis_conf[name]
        redis_host = str(cfg['host']) if cfg['host'] is not None else ""
        redis_port = int(cfg['port']) if cfg['port'] is not None and cfg['port'] != "" else 0
        redis_auth = str(cfg['auth']) if cfg['auth'] is not None else ""
        redis_bns = str(cfg['bns']) if cfg['bns'] is not None else ""
        if redis_host and redis_port > 0:
            return redis_host, redis_port, redis_auth
        elif redis_bns:
            return get_a_host_by_bns(redis_bns)
        else:
            return "", 0, ""


def get_mysql_conf(name="default") -> Tuple[str, str, str, str, str]:
    """
    获取mysql配置
    :param name: mysql 类型
    :return:
    """
    mysql_conf = get_config("mysql")
    if name not in mysql_conf:
        print(f"读取mysql配置失败:{name}")
        return "", "", "", "", ""
    else:
        cfg = mysql_conf[name]
        host = cfg['host'] if cfg['host'] is not None else ""
        port = cfg['port'] if cfg['port'] is not None else ""
        user = cfg['user'] if cfg['user'] is not None else ""
        pwd = cfg['pwd'] if cfg['pwd'] is not None else ""
        db = cfg['db'] if cfg['db'] is not None else ""
        return host, port, user, pwd, db


def get_a_host_by_bns(host_bns: str) -> Tuple[str, int, str]:
    """
    根据bns获取一个有效的ip和端口
    :param host_bns:
    :return:
    """
    data = ("", 0, "")
    ret = subprocess.run("get_instance_by_service -ips {}".format(host_bns) + " | awk '{print $2,$3,$4}'", shell=True,
                         stdout=subprocess.PIPE)
    code = ret.returncode
    if code == 0:
        stdout = ret.stdout
        host_result = stdout.decode('UTF-8').split("\n")
        if len(host_result) < 1:
            return data
        for i in host_result:
            item_list = i.split(" ")
            if len(item_list) < 3:
                continue
            if item_list[2] != '0':
                # 状态不是0，实例有问题
                continue
            return item_list[0], int(item_list[1]), ""
        return data
    else:
        print("获取bns失败{}, 状态码{}, 返回{}".format(host_bns, code, ret.stdout))
        return data


if __name__ == '__main__':
    pass
    # host, port = get_a_host_by_bns("group.bdrp-BeePlatform-proxy.redis.all")
    # print(host, port)
    # print(get_mysql_conf())
