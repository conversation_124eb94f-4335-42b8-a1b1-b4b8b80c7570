"""
配置文件加载工具。建议的配置文件规范：

1. 配置文件名由 3 部分构成：main-name[.mode].suffix，其中 mode 可选。
    - main-name: 配置文件的名称，建议和使用该配置文件的模块同名，并放置在一起。
    - [mode]: 可选，配置文件的其它使用场景，如 dev, test, debug 等。
    - suffix: 配置文件后缀，如 toml, json, yaml 等。

2. 其它场景的配置文件，只需要放置用于替换的配置即可（值的路径要和 main-name 文件中一致）。

示例：
app.py
- app.toml
- app.dev.toml
- app.debug.toml
"""
import os
from functools import lru_cache
from os import PathLike
from pathlib import Path
from typing import Callable, Union

import toml
import yaml

from src.tools import utils

global_mode = os.getenv("CONFIG_MODE", "")
"""
全局模式，当其不为空，且 `from_*` 的局部 mode 为空时，将使用该全局模式。
"""


@lru_cache
def from_toml(file_path: Union[str, PathLike], mode: str = "") -> dict[str, any]:
    """
    使用 config_file.load 加载 toml 文件。
    """
    return _load_with_loader(toml.load, file_path, mode or global_mode)


@lru_cache
def from_json(file_path: Union[str, PathLike], mode: str = "") -> dict[str, any]:
    """
    使用 config_file.load 加载 json 文件。
    """
    return _load_with_loader(utils.read_json, file_path, mode or global_mode)


@lru_cache
def from_yaml(file_path: Union[str, PathLike], mode: str = "") -> dict[str, any]:
    """
    使用 config_file.load 加载 yaml 文件。
    """

    def read_yaml(file: Path) -> dict[str, any]:
        with open(file, "r", encoding="utf-8") as f:
            return yaml.load(f, Loader=yaml.Loader)

    return _load_with_loader(read_yaml, file_path, mode or global_mode)


def _load_with_loader(
    loader: Callable[[Path], dict],
    file_path: Union[str, PathLike],
    mode: str = "",
) -> dict[str, any]:
    """
    加载配置文件，支持配置文件覆盖。
    @param loader: 加载器，自行指定 toml, json 等
    @param file_path: 配置文件路径（{main-name}.{suffix}）
    @param mode: 需要覆盖的配置，如 dev, test, debug 等，注意，文件名必须为 {main-name}.{mode}.{suffix}
    @return: 配置文件内容
    """
    config_path = Path(file_path)
    main_config = loader(config_path)
    if mode:
        override_file_name = f"{config_path.stem}.{mode}{config_path.suffix}"
        override_path = config_path.parent / override_file_name
        if override_path.exists():
            override_config = loader(override_path)
            _override_values(main_config, override_config)

    return main_config


def _override_values(a: dict, b: dict):
    """
    用 b 中的值覆盖 a 中同路径的值，若 b 中值的路径在 a 中不存在，则将不理睬。
    """
    for k, v in b.items():
        if isinstance(v, dict):
            if k in a:
                _override_values(a[k], v)
        else:
            a[k] = v

    return a
