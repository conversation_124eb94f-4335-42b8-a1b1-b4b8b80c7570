# !/usr/bin/env python3
"""
封装 AOI 关联 POI 的逻辑。
"""
import datetime
import json
from dataclasses import dataclass, field

import pymysql
import requests
import shapely.wkt
from retrying import retry
from tqdm import tqdm

from src.batch_process import tags
from src.batch_process.flow_process_aoi import update_main_poi_of_aoi
from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.tools import pgsql
from src.tools.conf_tools import get_mysql_conf


@dataclass
class PoiRecord:
    """
    存储需要关联的主点信息。
    """
    bid: str
    mid: str
    name: str
    wkt: str
    parent_bid: str
    pv: int
    index_tag: str
    source: str = 'pv'


def _get_connected_poi_by_pv(pois: list[PoiRecord]):
    """
    按照 pv 关联 poi。
    """
    high_pv = 210
    mid_bid_length = 4
    # pv 优先级：高 pv 父点 > 高 pv 子点 > 低 pv 父点 > 低 pv 子点

    if not any(pois):
        return None

    # 高 pv 父点
    parent_poi_records = [x for x in pois if len(x.parent_bid) < mid_bid_length]
    high_pv_parent_poi_records = [x for x in parent_poi_records if x.pv >= high_pv]
    high_pv_parent_poi_records.sort(key=lambda item: item.pv, reverse=True)
    if any(high_pv_parent_poi_records):
        return high_pv_parent_poi_records[0]

    # 高 pv 子点
    children_poi_records = [x for x in pois if len(x.parent_bid) >= mid_bid_length]
    high_pv_children_poi_records = [x for x in children_poi_records if x.pv >= high_pv]
    high_pv_children_poi_records.sort(key=lambda item: item.pv, reverse=True)
    if any(high_pv_children_poi_records):
        return high_pv_children_poi_records[0]

    # 低 pv 父点
    low_pv_parent_poi_records = [x for x in parent_poi_records if x.pv < high_pv]
    low_pv_parent_poi_records.sort(key=lambda item: item.pv, reverse=True)
    if any(low_pv_parent_poi_records):
        return low_pv_parent_poi_records[0]

    # 低 pv 子点
    low_pv_children_poi_records = [x for x in children_poi_records if x.pv < high_pv]
    low_pv_children_poi_records.sort(key=lambda item: item.pv, reverse=True)
    if any(low_pv_children_poi_records):
        return low_pv_children_poi_records[0]

    return None


def _get_connected_poi_by_competitor(conn: PgsqlStabilizer, aoi_geom, pois: list[PoiRecord]):
    """
    根据竞品关联 poi。
    """
    min_iou = 0.6
    bids = set(x.bid for x in pois)

    sql = f'''
        select bid, st_astext(geom)
        from aoi_intelligence_history
        where st_intersects(st_geomfromtext(%s, 4326), geom);
    '''
    connected_poi_bid = None
    max_iou = 0
    for record in conn.fetch_all(sql, (aoi_geom.wkt,)):
        bid, wkt = record
        if bid in bids:
            geom = shapely.wkt.loads(wkt).buffer(0)
            intersection = aoi_geom.intersection(geom)
            combined = aoi_geom.union(geom)
            iou = intersection.area / combined.area
            if iou >= min_iou and iou > max_iou:
                max_iou = iou
                connected_poi_bid = bid

    return [x for x in pois if x.bid == connected_poi_bid][0] if connected_poi_bid else None


def _get_connected_poi_by_tag(pois: list[PoiRecord]):
    """
    使用垂类关联 poi。
    """
    community_poi_records = [x for x in pois if '房地产' in x.index_tag and '住宅区' in x.index_tag]
    return _get_connected_poi_by_pv(community_poi_records)


def exists_to_c_main_poi_relation(aoi_conn, bid):
    """
    是否存在 C 端主点关联关系
    """
    sql = '''
        select 1 
        from blu_face a
        inner join blu_face_poi b
        on a.face_id = b.face_id
        where b.poi_bid = %s and
              a.src != 'SD'
        limit 1;
    '''
    return pgsql.fetch_one(aoi_conn, sql, (bid,)) is not None


def exists_main_poi_relation(aoi_conn, bid):
    """
    是否存在主点关联关系
    """
    sql = '''
        select 1 
        from blu_face_poi 
        where poi_bid = %s 
        limit 1;
    '''
    return pgsql.fetch_one(aoi_conn, sql, (bid,)) is not None


def _get_pending_pois(poi_conn: PgsqlStabilizer, aoi_conn, aoi_geom, ignore_online_relation=False):
    """
    获取可关联为主点的 poi 集合。
    """
    sql = '''
        select bid, mid, name, st_astext(geometry), relation_bid, click_pv, std_tag
        from poi
        where st_intersects(st_geomfromtext(%s, 4326), geometry);
   '''
    poi_records = []
    for poi_record in poi_conn.fetch_all(sql, (aoi_geom.wkt,)):
        bid, mid, name, wkt, relation_bid, click_pv, std_tag = poi_record
        if std_tag not in tags.COMPETITOR_CAN_ONLINE:
            continue

        if exists_to_c_main_poi_relation(aoi_conn, bid):
            # 需要忽略所有 C 端正在使用中的 bid。
            continue
        if not ignore_online_relation and exists_main_poi_relation(aoi_conn, bid):
            continue

        poi_records.append(PoiRecord(bid, mid, name, wkt, relation_bid, click_pv, std_tag))

    return poi_records


def __get_str_iou(str1, str2):
    """
    计算两个字符串的相似度
    """
    set1 = set(str1)
    set2 = set(str2)
    intersection_length = len(set1.intersection(set2))
    union_length = len(set1.union(set2))
    return intersection_length / union_length if union_length > 0 else 0.0


def __try_connect_to_poi_rude(poi_conn: PgsqlStabilizer, aoi_conn, aoi_geom, ignore_online_relation=False):
    """
    尝试对 aoi 边框关联主点（粗略版本）。
    """
    pois = _get_pending_pois(poi_conn, aoi_conn, aoi_geom, ignore_online_relation)
    connected_poi = _get_connected_poi_by_competitor(poi_conn, aoi_geom, pois)
    if connected_poi:
        connected_poi.source = 'competitor'
        return connected_poi
    connected_poi = _get_connected_poi_by_tag(pois)
    if connected_poi:
        connected_poi.source = 'tag'
        return connected_poi
    return _get_connected_poi_by_pv(pois)


def __get_poi_priority_by_relation(pv: int, parent_bid) -> int:
    """
    通过 poi 关联关系获取 poi 优先级：
    4：高 pv 父点
    3：高 pv 子点
    2：低 pv 父点
    1：低 pv 子点
    """
    high_pv = 210
    mid_bid_length = 4

    if pv >= high_pv:
        if len(parent_bid) < mid_bid_length:
            return 4
        else:
            return 3
    else:
        if len(parent_bid) < mid_bid_length:
            return 2
        else:
            return 1


def __get_poi_priority_by_tag(tag: str) -> int:
    """
    通过 poi tag 获取 poi 优先级：
    """
    if tag == '房地产;住宅区':
        return 99
    elif tag == '医疗;综合医院':
        return 98
    elif tag == '医疗;专科医院':
        return 97
    elif tag == '购物;购物中心':
        return 96
    else:
        return 0


def __try_connect_to_poi_accurate(
        poi_conn: PgsqlStabilizer,
        aoi_conn,
        aoi_geom,
        original_poi: PoiRecord,
        ignore_online_relation=False):
    """
    尝试对 aoi 边框关联主点（精确版本）。
    """
    pois = _get_pending_pois(poi_conn, aoi_conn, aoi_geom, ignore_online_relation)
    same_tag_pois = [x for x in pois if x.index_tag == original_poi.index_tag]
    if len(same_tag_pois) == 1:
        return same_tag_pois[0]

    if any(same_tag_pois):
        return sorted(
            [x for x in same_tag_pois],
            key=lambda x: (__get_str_iou(x.name, original_poi.name), __get_poi_priority_by_relation(x.pv, x.parent_bid))
        )[:-1]
    else:
        return sorted(
            [x for x in pois],
            key=lambda x: (__get_str_iou(x.name, original_poi.name), __get_poi_priority_by_tag(x.index_tag))
        )[:-1]


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def get_poi_detail(bid: str) -> PoiRecord:
    """
    获取 poi 详情。
    demo: https://mapde-poi.baidu-int.com/pre/api/getPoiDetail?bid=14667450070452654368
    """
    success_http_code = 200
    success_data_code = 0

    response = requests.get(f'https://mapde-poi.baidu-int.com/pre/api/getPoiDetail?bid={bid}')
    if response.status_code != success_http_code:
        raise Exception(f"请求失败，http 状态码:{response.status_code}")

    data = response.json()
    if data['code'] != success_data_code:
        raise Exception(f"请求失败，数据状态码:{response.status_code}")

    return PoiRecord(
        bid=bid,
        mid='',
        name=data['data']['base']['name'],
        wkt='',
        parent_bid=data['data']['base']['parent_id'],
        pv=0,
        index_tag=data['data']['base']['std_tag'],
    )


def try_connect_to_poi(
        poi_conn: PgsqlStabilizer,
        aoi_conn,
        aoi_geom,
        original_bid=None,
        ignore_online_relation=False):
    """
    尝试对 aoi 边框关联主点。
    """
    if original_bid:
        # 若提供失效前的 poi 信息，则可使用更为精准的关联策略。
        return __try_connect_to_poi_accurate(
            poi_conn,
            aoi_conn,
            aoi_geom,
            get_poi_detail(original_bid),
            ignore_online_relation
        )
    else:
        return __try_connect_to_poi_rude(poi_conn, aoi_conn, aoi_geom, ignore_online_relation)


def get_bids_in_working():
    """
    获取正处于作业过程中的 bid 集合。
    """
    host, port, user, pwd, database = get_mysql_conf('beeflow')
    with (
        pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database, charset="utf8mb4") as conn,
        conn.cursor() as cursor,
    ):
        sql = '''
            select a.bid
            from strategy_feature_list a 
            
            inner join task_record b 
            on a.task_id=b.task_id 
            
            inner join task_plan c 
            on b.plan_id=c.plan_id 
            
            where a.work_status in (1, 2) and 
                  c.plan_status in (0, 1) and 
                  a.strategy_type in (31, 37, 61, 301, 302, 304);
        '''
        cursor.execute(sql)
        return [x[0] for x in cursor.fetchall()]


def get_face_ids_in_batch_processing():
    """
    获取正处于批处理过程中的 face_id 集合。
    """
    day_before = datetime.datetime.now() - datetime.timedelta(days=7)

    with pgsql.get_connection(pgsql.POI_CONFIG) as conn:
        sql = f'''
            select face_id 
            from gate_bid_change_history 
            where imp_state = 0 and flow_state = '待入库'
            and create_time > '{day_before.strftime("%Y-%m-%d 0:00:00")}';
        '''
        return [x[0] for x in pgsql.fetch_all(conn, sql)]


@dataclass
class BatchMainPoiTool:
    """
    封装主点批处理记录的推送逻辑。
    """
    batch_records: list[dict] = field(default_factory=list)

    def insert_record(self, *record):
        """
        插入一条批处理记录。
        """
        current_bid, face_id, aoi_src, mesh_id, mid, src = record

        if mesh_id == '':
            return

        # 下面几个魔法数字不知道什么意思。
        self.batch_records.append({
            'bid': current_bid,
            'face_id': face_id,
            'action': 'update_main_poi',
            'imp_state': 0,
            'src': src,
            'flow_state': '待入库',
            'mesh_id': mesh_id,
            'resource': 6,
            'pri': 9000,
            'extra': json.dumps(
                {
                    'poi_mid': mid,
                    'src': aoi_src,
                }
            )
        })

    def execute(self):
        """
        执行批处理操作。
        """
        with PgsqlStabilizer(pgsql.BACK_CONFIG) as back_stabilizer:
            processed_face_ids = []

            for record in tqdm(self.batch_records):
                extra = json.loads(record['extra'])
                result = update_main_poi_of_aoi(
                    back_stabilizer,
                    record['face_id'],
                    extra['src'],
                    record['bid'],
                    extra['poi_mid'],
                )
                if result.success:
                    processed_face_ids.append(record['face_id'])
                else:
                    print(result.msg)

            return processed_face_ids
