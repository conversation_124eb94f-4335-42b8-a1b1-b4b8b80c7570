import sys
from src.tools import function as F


class Logger:
    def __init__(self, name='stdout', stream=sys.stdout):
        self.terminal = stream
        self.log = F.get_logging_client(name)

    def write(self, message):
        self.log.info(message)

    def flush(self):
        pass

    def __del__(self):
        F.close_log_client(self.log)


class ConsoleTransferLog:
    def __init__(self):
        """
        初始化，拦截sys.stdout, sys.stderr
        """
        self.stdout_o = sys.stdout
        self.stderr_o = sys.stderr
        sys.stdout = Logger('stdout', sys.stdout)
        sys.stderr = Logger('std_error', sys.stderr)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        还原std_out 和std_error
        :param exc_type:
        :param exc_val:
        :param exc_tb:
        :return:
        """
        sys.stdout = self.stdout_o
        sys.stderr = self.stderr_o
