# !/usr/bin/env python3
"""
图片等资源上传到deoss
"""
import hashlib
import os.path
import time
import requests
import sys
from typing import Dict
import json

ROOT_DIR = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

from src.tools import conf_tools, function


class DeossTool:
    def __init__(self, is_img=True):
        deoss_map = conf_tools.get_config("deoss")
        self.ak = str(deoss_map['ak'])
        self.sk = str(deoss_map['sk'])
        self.url = str(deoss_map['url'])
        self.bucket = str(deoss_map['bucket'])
        self.deoss_log_client = function.get_logging_client("deoss_logs")
        self.is_img = is_img

    def __del__(self):
        function.close_log_client(self.deoss_log_client)

    def generate_sign(self, reqParam: Dict[str, any], timestamp=None):
        """
        生成 请求参数
        :param reqParam:
        :param timestamp:
        :return:
        """
        if not timestamp:
            timestamp = str(int(time.time()) + 86400 * 365 * 30)
        sign_str = ""
        ksort = sorted(reqParam.keys())
        for key in ksort:
            if key != 'sign':
                sign_str += key + "=" + str(reqParam[key]).strip() + "&"
        sign_str += 'sk={}&timestamp={}'.format(self.sk, timestamp)
        return str(hashlib.md5(sign_str.encode("utf-8")).hexdigest()).lower() + "." + self.ak + "." + timestamp

    def delete_file(self, object_id):
        """
        删除文件
        :param object_id:
        :return:
        """
        params = {
            "object_id": object_id,
            "bucket": self.bucket
        }
        params['sign'] = self.generate_sign(params)
        url = self.url + f"/deoss/object/delete?object_id={object_id}&bucket={self.bucket}&sign={params['sign']}"
        response = requests.request("GET", url, timeout=120)
        print(response.json())

    def upload(self, file_path: str, obj_id="", mime="image/png"):
        """
        文件上传
        :param file_path:
        :param obj_id
        :param mime:
        :return:
        """
        if not os.path.isfile(file_path):
            raise Exception(f"文件{file_path}不存在")
        url = self.url + '/deoss/object/upload'
        file_sha256 = self.get_file_hash(file_path)
        with open(file_path, 'rb') as f:
            payload = {
                "sha256sum": file_sha256,
                "bucket": self.bucket,
                "mime": mime,
            }
            if obj_id:
                payload['object_id'] = obj_id
            payload['sign'] = self.generate_sign(payload)
            files = {"file": f}
            return self.oss_request("POST", url, payload, files=files)

    def get_file_hash(self, file_path):
        with open(file_path, 'rb') as f:
            return hashlib.sha256(f.read()).hexdigest()

    def upload_part(self, file_path, obj_id="", mime="image/png"):
        """
        按照任务流上传，支持断点续传
        :param file_path:
        :param obj_id: 文件名称
        :param mime: 文件类型
        :return:
        """
        if not os.path.isfile(file_path):
            raise Exception(f"文件{file_path}不存在")
        sha256sum = self.get_file_hash(file_path)
        with open(file_path, 'rb') as f:
            params = {
                "sha256sum": sha256sum,
                "bucket": self.bucket,
                "mime": mime
            }
            if obj_id:
                params['object_id'] = obj_id
            sign = self.generate_sign(params)
            payload = f
            url = self.url + f"/deoss/object/uploadPart?sha256sum={sha256sum}&sign={sign}&bucket={self.bucket}&mime={mime}"
            if obj_id:
                url += f"&object_id={obj_id}"
            resp = self.oss_request("GET", url, payload)
            return resp

    def oss_request(self, method, url, data, files=None, headers=None):
        response = requests.request(method, url, headers=headers, data=data, files=files, timeout=120)
        code = response.status_code
        content = json.loads(response.content.decode("utf-8"))
        if response.status_code != 200 or content.get("errno", -1) != 0:
            self.deoss_log_client.info(f"上传失败: {url}, 状态码 {code}, 结果 {content}\n")
            return ""
        if 'data' not in content or 'object_id' not in content['data']:
            self.deoss_log_client.info(f"上传失败: {url}, 状态码 {code}, 结果 {content}\n")
            return ""
        # self.deoss_log_client.info(f"上传成功: {url}, resp: {content}")
        # 线上deoss的 img接口无法使用，直接获取download url
        return self.get_download_url(content['data']['object_id'])

    def get_download_url(self, object_id):
        """
       获取文件下载地址
       :param object_id:
       :return:
       """
        url = self.url + "/deoss/object/download?"
        params = {
            "object_id": object_id,
            "bucket": self.bucket,
        }
        params['sign'] = self.generate_sign(params)
        return url + f"object_id={object_id}&bucket={self.bucket}&sign={params['sign']}"

    def get_img_url(self, object_id):
        """
        获取图片访问地址
        :param object_id:
        :return:
        """
        url = self.url + "/deoss/object/img?"
        params = {
            "object_id": object_id,
            "bucket": self.bucket,
        }
        params['sign'] = self.generate_sign(params)
        return url + f"object_id={object_id}&bucket={self.bucket}&sign={params['sign']}"


if __name__ == '__main__':
    t = DeossTool()
    t.delete_file("model_vis_f009aa9a253e4dfa996d6d58dcb53da6_pred.png")
