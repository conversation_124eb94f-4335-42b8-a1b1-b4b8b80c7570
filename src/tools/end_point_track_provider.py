# !/usr/bin/env python3
"""
终点轨迹获取器
"""
import json
import re
from typing import Iterator

import requests
from retrying import retry
from shapely import LineString

from src.pre_process.transform import wgs2gcj
from src.tools import pgsql

API = 'http://chenxi.vpn.guoke.baidu.com/gpoint_janus'
TRACK_POINT_SPLITTER = '[;@]'
COORDINATE_SPLITTER = '[,]'
STOP_MAX_ATTEMPT_NUMBER = 8
WAIT_RANDOM_MIN = 1000
WAIT_RANDOM_MAX = 5000


def parse_track_point(track_point: str):
    """
    解析单个轨迹点文本，返回 x, y 坐标。
    """
    fragments = list(filter(None, re.split(COORDINATE_SPLITTER, track_point)))
    if len(fragments) < 2:
        return None

    x = float(fragments[0])
    y = float(fragments[1])

    if x < 0 or y < 0:
        return None

    return x, y


def parse_track_text(track_text: str):
    """
    解析轨迹文本，返回 LineString。
    """
    if not track_text or track_text == '':
        return None

    fragments = list(filter(None, re.split(TRACK_POINT_SPLITTER, track_text)))
    if len(fragments) < 2:
        return None

    points = []
    for track_point in fragments[1:]:
        wgs_point = parse_track_point(track_point)
        if wgs_point is None:
            continue

        x, y = wgs_point
        points.append(wgs2gcj(x, y))

    return LineString(points)


def parse_response(response_data, bid: str):
    """
    解析响应数据，返回轨迹文本集合。
    """
    success_code = 0

    try:
        track_data = response_data["content"]["navi_dest_traj"][bid]
        result_code = track_data["redis_result"]["error"]
        return track_data.get("trajs", []) if result_code == success_code else []
    except Exception as e:
        print(bid + "获取终点轨迹出错" + str(e))
        return []


@retry(
    stop_max_attempt_number=STOP_MAX_ATTEMPT_NUMBER,
    wait_random_min=WAIT_RANDOM_MIN,
    wait_random_max=WAIT_RANDOM_MAX
)
def get_end_point_track_by_api(bid: str) -> Iterator[LineString]:
    """
    通过接口获取轨迹，返回 LineString 列表。
    """
    request_data = json.dumps(
        {
            'qt': "navi_dest_traj",
            'bid': bid,
            'yddak': 'map-aoi@baidu.com_yddak_273db2a4d6b5a6b465abb7d0effcc7bd'
        }
    )
    headers = {'Content-Type': 'application/json'}
    response_data = requests.post(API, headers=headers, data=request_data).json()
    for track_text in parse_response(response_data, bid):
        line = parse_track_text(track_text)
        if line:
            yield line


def get_end_point_track_by_database(track_conn, bid: str) -> Iterator[LineString]:
    """
    通过数据库获取轨迹，返回 LineString 列表。
    """
    sql = '''
        select tra_info from traj_data where bid = %s;
    '''
    for track_text, in pgsql.fetch_all(track_conn, sql, (bid,)):
        line = parse_track_text(track_text)
        if line:
            yield line
