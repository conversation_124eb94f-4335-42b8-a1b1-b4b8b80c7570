# !/usr/bin/env python3
"""
包含一些文件下载帮助方法
"""
import urllib.request
from pathlib import Path

from retrying import retry

from src.tools.afs_tool import AfsTool


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def download_file_by_http(url, save_path: Path):
    """
    使用 http 协议下载文件。
    """
    urllib.request.urlretrieve(url, save_path)


def download_file_by_afs(url, save_path: Path, cluster="aries"):
    """
    使用 afs 协议下载文件。
    """
    parent_dir = save_path.parent
    AfsTool(cluster=cluster).get(url, parent_dir)
    afs_file_path = Path(url).name
    local_file_path = parent_dir / afs_file_path
    local_file_path.rename(save_path)
