# !/usr/bin/env python3
"""
封装了流式入库相关的帮助方法
DOC: https://ku.baidu-int.com/d/13fdlyjNpf9WMt
"""
import uuid
from dataclasses import dataclass
from http import HTTPStatus
from typing import Literal

import requests
from requests import Response

API_MAP = {
    'formal': 'http://mapde-poi.baidu-int.com/aoidataprod/stream/backFeature/aoi',
    'practice': 'http://mapde-poi.baidu-int.com/aoidatapre/stream/backFeature/aoi',
}
TIMEOUT = 10  # 单位：秒。


@dataclass
class Result:
    """
    执行结果
    """
    msg: str
    success: bool

    @staticmethod
    def success(msg=''):
        return Result(msg=msg, success=True)

    @staticmethod
    def fail(msg=''):
        return Result(msg=msg, success=False)


class FlowProcess:
    """
    流式入库构建器
    """

    def __init__(self, platform: Literal['formal', 'practice'] = 'formal'):
        self.data_list = []
        self.api = API_MAP[platform]

    @staticmethod
    def __check_response(response: Response):
        """
        检查请求是否成功
        """
        if response.status_code != HTTPStatus.OK:
            raise Exception(f'status code: {response.status_code}')

        response_content = response.json()
        if response_content['ErrNo'] != 0:
            raise Exception(response_content['Msg'])

        print(f"流式入库执行成功，返回 ID: {response_content['Data']['retid']}")

    def __send_request(self, method, data, **kwargs):
        """
        发送请求
        """
        request = {
            'auto_topo': False,
            'data_list': data if isinstance(data, list) else [data],
        }

        if 'transaction_id' in kwargs:
            request['trans'] = {
                'uuid': kwargs['transaction_id'],
                'seq': 0,
            }

            if 'total' not in kwargs:
                raise ValueError('缺少参数 total')

            request['trans']['total'] = int(kwargs['total'])

        print(request)
        return requests.request(method=method, url=self.api, json=request, timeout=TIMEOUT)

    def add(self, data_list):
        """
        添加新增数据
        """
        self.data_list.extend([('POST', data) for data in data_list])
        return self

    def delete(self, data_list):
        """
        添加删除数据
        """
        self.data_list.extend([('DELETE', data) for data in data_list])
        return self

    def update(self, data_list):
        """
        添加更新数据
        """
        self.data_list.extend([('PUT', data) for data in data_list])
        return self

    def execute(self):
        """
        执行流式入库操作
        """
        try:
            total = len(self.data_list)
            transaction_id = uuid.uuid4().hex

            for method, data in self.data_list:
                FlowProcess.__check_response(self.__send_request(
                    method,
                    data,
                    total=total,
                    transaction_id=transaction_id,
                ))

            return Result.success()
        except Exception as e:
            return Result.fail(f'流式入库失败：{str(e)}')
