# !/usr/bin/env python3
"""
定义一些基础方法
"""

import logging
import os
import subprocess
import sys
import time
from logging.handlers import TimedRotatingFileHandler

# 项目根目录
ROOT_DIR = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)


def get_host_name() -> str:
    """
    获取执行机器的名称
    :return:
    """
    # opera 的 hostname 返回的是主机的名称，对排查问题没有帮助，需要从环境变量去读取参数获取实际的opera值
    opera_id = os.environ.get("MATRIX_INSTANCE_ID")
    if opera_id is not None:
        return str(opera_id)
    # 物理机器， 不需要执行命令，直接读取环境变量即可
    host_name = os.environ.get("HOSTNAME")
    if host_name is not None:
        return str(host_name)
    return ""


def exec_shell_cmd(cmd, timeout=None, cwd=None):
    """
    执行cmd命令
    :param timeout: 超时时间，这个只是超时返回，实际上进程没有停止，所以这个参数最好不要频繁使用
    :param cmd: 命令行
    :param cwd: 工作目录
    :return: 结果数组
    """
    output_list = []
    try:
        ret = subprocess.run(
            f"{cmd}",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True,
            timeout=timeout,
            cwd=cwd,
        )
        code = ret.returncode
        stdout = ret.stdout
        stderr = ret.stderr
        for i in stdout.decode("UTF-8").split("\n"):
            if i.strip() == "":
                continue
            output_list.append(i)
        for i in stderr.decode("UTF-8").split("\n"):
            if i.strip() == "":
                continue
            output_list.append(i)
            print("错误信息:\t" + i)
        return code, output_list
    except Exception as e:
        print("执行失败：", e.args)
        return -1, [str(e)]


def get_logging_client(name):
    """
    日志客户端
    :param name:
    :return:
    """
    logger = logging.getLogger(name)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    log_path = ROOT_DIR + "/logs/" + name + "/"
    if not os.path.isdir(log_path):
        os.makedirs(log_path, exist_ok=True)
    info_file_name = (
        f"info-{name}-"
        + time.strftime("%Y-%m-%d", time.localtime(time.time()))
        + ".log"
    )
    ch = TimedRotatingFileHandler(
        filename=log_path + info_file_name,
        when="MIDNIGHT",
        interval=1,
        backupCount=40,
        encoding="utf-8",
    )
    ch.setLevel(logging.INFO)

    ch.setFormatter(formatter)
    logger.addHandler(ch)
    return logger


def close_log_client(logger: logging):
    """
    remove log client
    :param logger:
    :return:
    """
    for i in logger.handlers:
        i.close()
    logger.handlers.clear()


if __name__ == "__main__":
    pass
    # F.get_logging_client("bbb").debug("hellp")
