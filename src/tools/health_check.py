# !/usr/bin/env python3
"""
健康检测
"""
import os
import sys
import time

root_path = os.path.abspath(os.path.dirname(os.path.abspath(__file__)) + "/../../")
if root_path not in sys.path:
    sys.path.insert(0, root_path)

from src.tools.function import exec_shell_cmd
from src.tools.machine_group import init_envirement_script
from src.model_mysql.aoi_ml_model import AoiMlModel


def check_pg_ok():
    """
    检测pg是否可用
    :return:
    """
    key = "check_pg_ok_status"
    ret_success = "ok"
    ret_fail = "fail"
    with AoiMlModel() as aoi_ml:
        ret = aoi_ml.get_store(key)
        # 如果已经检测过，使用检测过的数据
        if ret:
            return ret == ret_success

        t1 = time.time()
        # 主要原理： 查询指定的sql（如果在数据同步阶段查询会超级长），利用linux的timeout 强制结束进程避免进程卡死造成泄漏，判断数据库是否可用
        exec_shell_cmd(
            f"{init_envirement_script()}timeout 5 python3 {root_path}/script/tools/pre_check_item.py --item pg_check")
        t2 = time.time()
        # 理论上速度很快，不需要1s即可完成，异常情况执行时间会大幅增加
        print("本次执行时间：", t2 - t1)
        ret = ret_success
        if (t2 - t1) > 4:
            ret = ret_fail
        aoi_ml.set_store(key, ret, 290)
        return ret


if __name__ == '__main__':
    check_pg_ok()
