# !/usr/bin/env python3
"""
封装了 icafe 相关的帮助方法
"""

import requests

API_FORMAT = 'http://icafeapi.baidu-int.com/api/v2/space/{0}/{1}/{2}'


def create_card(space, title, **kwargs):
    """
    创建一个 icafe 卡片
    """
    url = API_FORMAT.format(space, 'issue', 'new')
    values = {
        'username': kwargs.get('username', ''),
        'password': kwargs.get('password', ''),
        'notifyEmails': kwargs.get('notify_emails', []),
        'issues': [
            {
                'title': title,
                'detail': kwargs.get('detail', ''),
                'type': kwargs.get('type', 'Case'),
                'fields': {
                    '负责人': kwargs.get('owner', ''),
                },
            }
        ]
    }

    return requests.post(url, json=values).json()
