from typing import Callable
from typing import Iterable
from typing import TypeVar

T = TypeVar('T')
K = TypeVar('K')
V = TypeVar('V')


def first_or_default(collection: Iterable[T], predicate: Callable[[T], bool] = None, default: T = None):
    for item in collection:
        if predicate is None or predicate(item):
            return item

    return default


def group_by(collection: Iterable[T], key: Callable[[T], K], value: Callable[[T], V] = None):
    result = {}
    for item in collection:
        group_key = key(item)
        if group_key not in result:
            result[group_key] = []

        v = value(item) if value else item
        result[group_key].append(v)

    return result


def count_by(collection: Iterable[T], key: Callable[[T], K]):
    result = {}
    for item in collection:
        group_key = key(item)
        if group_key not in result:
            result[group_key] = 0

        result[group_key] += 1

    return result
