# !/usr/bin/env python3
# coding=utf-8

"""
锁
"""
import abc
import fcntl
import functools
import logging
import os.path
import time
import uuid

import redis


class Locker:
    """
    锁
    """

    @abc.abstractmethod
    def lock(self) -> bool:
        """
        加锁
        """
        pass

    @abc.abstractmethod
    def unlock(self) -> bool:
        """
        释放锁
        """
        pass


class FileLocker(Locker):
    """
    文件锁
    """

    def __init__(self, uqid: str):
        self.lock_file = f'./data/lock/{uqid}.lock'
        self.lock_stop = f'./data/lock/stop.lock'

    def lock(self) -> bool:
        """
        加锁
        """
        if os.path.exists(self.lock_file):
            return False

        if os.path.exists(self.lock_stop):
            print(f"大环境不好，所有的加锁申请都拦截，都申请不通过")
            return False

        handler = open(self.lock_file, 'w')
        try:
            fcntl.lockf(handler, fcntl.LOCK_EX | fcntl.LOCK_NB)
            handler.write(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
            return True
        except IOError:
            return False

    def unlock(self) -> bool:
        """
        释放锁
        """
        os.system(f"rm -rf {self.lock_file}")
        return True


class RedisLocker(Locker):

    def __init__(self, rds: redis.Redis, key: str, ex: int):
        self.rds = rds
        self.key = key
        self.ex = ex
        self.val = str(uuid.uuid4())

    def lock(self) -> bool:
        res = self.rds.set(name=self.key, value=self.val, ex=self.ex, nx=True)
        if res is None:
            return False
        if res is True:
            return True
        return False

    def unlock(self) -> bool:
        rds_val = self.rds.get(self.key)
        if rds_val is not None and self.val == rds_val:
            self.rds.delete(self.key)
            return True
        return False


def lock_guardian(locker: Locker, fun, *args, **kwargs):
    """
    加锁守护者, 执行流程是：
    若加锁成功：加锁成功 -> 执行 -> 释放锁 -> 结束
    若加锁失败：加锁失败 -> 抛出异常
    """
    if not locker.lock():
        raise Exception("加锁失败")
    try:
        print(f"----------- 加锁保护中,开始执行 -----------")
        res = fun(*args, **kwargs)
        print(f"----------- 加锁保护中,执行结束 -----------")
        return res
    finally:
        res = locker.unlock()
        print(f"释放了锁:{res}")


def lock_guardian_with_retry(locker: Locker, times: int, sleep_time: int, fun, *args, **kwargs):
    """
    加锁守护者，若加锁失败会重试, 执行流程是：
    若加锁成功：加锁成功 -> 执行 -> 释放锁 -> 结束
    若加锁失败：加锁失败 -> 抛出异常 -> 重试
    """
    for i in range(0, times):
        try:
            return lock_guardian(locker, fun, *args, **kwargs)
        except Exception as e:
            logging.exception(e)
            print(f"lock_guardian err:{str(e)}")
            time.sleep(sleep_time)
    return None

