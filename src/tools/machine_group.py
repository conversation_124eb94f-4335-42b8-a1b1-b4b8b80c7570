# !/usr/bin/env python3
"""
机器组
"""
from .function import get_host_name
from .conf_tools import is_debug


def machine_de16():
    """
    是否为de16机器
    :return:
    """
    return "map-de16" in get_host_name()


def machine_de17():
    """
    检测是否为de17机器
    :return:
    """
    return "map-de17" in get_host_name()


def machine_debug():
    """
    是否为debug机器
    :return:
    """
    return is_debug()


def machine_opera():
    """
    opera机器组
    :return:
    """
    return "opera-Polaris-AoiStrategy" in get_host_name()


def machine_opera_0():
    return "0.opera-Polaris-AoiStrategy" in get_host_name()


def machine_opera_1():
    return "1.opera-Polaris-AoiStrategy" in get_host_name()


def machine_opera_2():
    return "2.opera-Polaris-AoiStrategy" in get_host_name()


def machine_opera_3():
    return "3.opera-Polaris-AoiStrategy" in get_host_name()


# def machine_na002():
#     """
#     na002机器
#     :return:
#     """
#     return "map-na002" in get_host_name()


def machine_gpu_rpm01():
    return 'rpm01-a7c9a' in get_host_name()


def init_envirement_script():
    """
    环境的基础初始化脚本
    :return:
    """
    if machine_opera():
        return "source /home/<USER>/.bashrc;"
    if machine_gpu_rpm01():
        return "source /home/<USER>/chenbaojun/bashrc;"
    if machine_de16():
        return "source /home/<USER>/.bashrc;"
    return "source /home/<USER>/.bashrc;"
