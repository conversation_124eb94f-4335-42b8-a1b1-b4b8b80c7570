# !/usr/bin/env python3
import configparser
import os
import random
import string
import time
from collections import deque
from typing import Callable
from typing import Generator
from typing import Iterable
from typing import TypeVar

import pymysql
import requests

# TODO: move config.ini file to ./conf dir.
root_path = os.path.dirname(os.path.dirname(__file__) + "/../../")

config = configparser.ConfigParser()
config.read(f"{root_path}/src/aoi_cleanup_strategy/config.ini", 'utf8')

T = TypeVar('T')


def consume_mesh_task_queue(
        tasks: Iterable[tuple[str, T]],
        lock_prefix: str,
        action: Callable[[T], bool],
        on_completed: Callable[[str, T], None],
):
    """
    消费图幅任务队列，流程为：lock(mesh_id) -> consumer() or retry -> on_completed() or retry -> unlock(mesh_id)。
    @param tasks: 待处理的图幅任务：[(mesh_id, any-data)]
    @param lock_prefix: 锁前缀，与业务相关的名字，以区分锁的使用方。
    @param action: 抢到图幅锁后进行的操作。
    @param on_completed: 完成回调。
    """
    queue = deque(tasks)
    while len(queue) > 0:
        mesh_id, items = queue.popleft()
        completed = lock_mesh(mesh_id, lock_prefix, lambda: action(items))
        if completed:
            # 成功则触发回调（一般是写履历库）。
            on_completed(mesh_id, items)
        else:
            # 不成功则把失败的处理项重新添加到队列末端。
            queue.append((mesh_id, items))
            time.sleep(2)


def lock_for_each(tasks: Iterable[tuple[str, T]], lock_prefix: str) -> Generator[tuple[str, T], bool, None]:
    """
    依次锁定每个图幅，自动重试抢锁失败的任务。
    @param tasks: 待处理的图幅任务：[(mesh_id, any-data)]
    @param lock_prefix: 锁前缀，与业务相关的名字，以区分锁的使用方。
    """
    queue = deque(tasks)
    while len(queue) > 0:
        mesh_id, items = queue.popleft()
        mesh_batch = MeshBatch('prod_lbs_data_process', lock_prefix=lock_prefix + '-{}')
        locked = mesh_batch.try_lock(mesh_id)
        success = False
        if locked:
            try:
                success = yield mesh_id, items
            finally:
                _ = mesh_batch.try_unlock(mesh_id)

        # success 可能为 None，即用户没有反馈执行结果，按 True 处理。
        # 解锁失败不重试，因为本次操作已成功，只不过影像其它任务而已。
        if not locked or success is False:
            # 不成功则把失败的处理项重新添加到队列末端。
            queue.append((mesh_id, items))
            time.sleep(2)


def lock_mesh(mesh_id: str, lock_prefix: str, action: Callable[[], bool]):
    """
    锁定指定的图幅。
    @param mesh_id: 带锁定的图幅。
    @param lock_prefix: 锁前缀，与业务相关的名字，以区分锁的使用方。
    @param action: 锁定成功时执行的回调。
    @return: 若成功锁定，则执行 action，并返回 True；否则不执行，并返回 False。
    """
    mesh_batch = MeshBatch('prod_lbs_data_process', lock_prefix=lock_prefix + '-{}')
    if not mesh_batch.try_lock(mesh_id):
        return False

    try:
        return action()
    finally:
        _ = mesh_batch.try_unlock(mesh_id)


class MeshBatch:
    def __init__(self, lbs_data_process_name, lock_prefix='batch-blu-access-{}'):
        self.lock_prefix = lock_prefix
        self.source_name = config[lbs_data_process_name]['source_name']
        self.action = int(config[lbs_data_process_name]['action'])
        self.proc_version = config[lbs_data_process_name]['proc_version']
        self.expimp_url = config[lbs_data_process_name]['expimp_url']
        self.lbs_data_process_conn = create_mysql_client(lbs_data_process_name)

    def try_lock(self, mesh_id: str):
        if not self._mesh_id_locked(mesh_id):
            print(f"图幅锁-{mesh_id}已被抢占")
            return False

        if self._mesh_id_lock_enough(num=15):
            print(f"图幅锁-{mesh_id}暂时等待队列处理...")
            time.sleep(20)
            return False

        if not self._preempt_mesh_id(mesh_id):
            print(f"图幅锁获取失败：'{mesh_id}'")
            return False

        return True

    def try_unlock(self, mesh_id: str):
        success = self._release_mesh_id_lock(mesh_id)
        if success:
            self._delete_mesh_cache(mesh_id)
            self._gen_mesh_cache(mesh_id)

        self.lbs_data_process_conn.close()
        return success

    def _mesh_id_locked(self, mesh_id):
        with self.lbs_data_process_conn.cursor() as cur:
            sql = "select * from ds_region_control where source_name = %s and region = %s"
            cur.execute(sql, [self.source_name, mesh_id])
            lock_record = cur.fetchone()
            if lock_record is not None:
                return False
            return True

    def _mesh_id_lock_enough(self, num=10):
        with self.lbs_data_process_conn.cursor() as cur:
            sql = "select count(*) from ds_region_control where source_name = %s and user_id like %s"
            cur.execute(sql, [self.source_name, "-".join(self.lock_prefix.split('-')[:-1]) + "%"])
            lock_total, = cur.fetchone()
            if lock_total > num:
                return True
            return False

    def _preempt_mesh_id(self, mesh_id):
        uid = self.lock_prefix.format(mesh_id)
        req_data = {
            "client_id": "beeflow",
            "user_id": uid,
            "req_id": int(mesh_id),
            "authcode": generate_random_str(16),
            "action": self.action,
            "params": {
                "proc_version": self.proc_version,
                "extra_type": "meshes",
                "extra_way": mesh_id,
                "format": "sqlite",
                "url_type": 3,
                "compress": 2,
                "lock_type": "w",
                "groupname": "",
                "cityname": "",
                "addinfo": {
                },
                "args": {
                    "dataurl": "",
                    "db_name": "",
                    "host": "",
                    "password": "",
                    "port": 0,
                    "username": ""
                }
            }
        }
        print("请求参数:'{}'".format(req_data))
        now_try, max_try = 0, 4
        res_data = {}
        while now_try < max_try:
            try:
                res = requests.post(self.expimp_url, json=req_data, timeout=10 * 60)
            except Exception as e:
                print(f"try {now_try} time", e)
                now_try += 1
                time.sleep(0.2)
            else:
                res_data = res.json()
                break
        print("请求结果：'{}'".format(res_data))
        return (
                "code" in res_data
                and res_data["code"] == 0
                and "contents" in res_data
                and res_data["contents"]["process_code"] == 0
        )

    def _release_mesh_id_lock(self, mesh_id):
        # with self.lbs_data_process_conn.cursor() as cur:
        #     sql = "delete from ds_region_control where source_name=%s and user_id=%s and region=%s and state = 1"
        #     cur.execute(sql, [self.source_name, self.lock_prefix.format(mesh_id), mesh_id])
        #     self.lbs_data_process_conn.commit()
        # print(f"图幅解锁-{mesh_id} 成功")
        uid = self.lock_prefix.format(mesh_id)
        req_data = {
            "user_id": uid,
            "client_id": "beeflow",
            "req_id": int(mesh_id),
            "action": 63,  # 非常重要
            "authcode": generate_random_str(16),
            "params": {
                "lock": 0,
                "proc_version": self.proc_version,
                "region": mesh_id,
                "region_type": "meshes",
                "source_code": 51
            }
        }
        print("释放锁参数:'{}'".format(req_data))
        now_try, max_try = 0, 4
        res_data = {}
        while now_try < max_try:
            try:
                res = requests.post(self.expimp_url, json=req_data, timeout=10 * 60)
            except Exception as e:
                print(f"try {now_try} time", e)
                now_try += 1
                time.sleep(0.2)
            else:
                res_data = res.json()
                break
        """
        {
           "process_code" : 0,
           "process_msg" : "返回码:0,成功, 1601787任务执行成功"
        }
        """
        print('清理锁返回数据', res_data)

        if (
                "code" in res_data
                and res_data["code"] == 0
                and "contents" in res_data
                and res_data["contents"]["process_code"] == 0
        ):
            print(f"unlock-mesh-success: {mesh_id=}")
            return True
        else:
            print(f"unlock-mesh-failed: {mesh_id=}")
            return False

    def _delete_mesh_cache(self, mesh_id):
        with self.lbs_data_process_conn.cursor() as cur:
            sql = "delete from ds_cache_51_6 where source_name=%s and region=%s"
            cur.execute(sql, [self.source_name, mesh_id])
            self.lbs_data_process_conn.commit()
        print(f"删除图幅缓存-{mesh_id} 成功")

    def _gen_mesh_cache(self, mesh_id, callback="http://facker.com"):
        # 出入重新生成缓存
        uid = self.lock_prefix.format(mesh_id)
        req_data = {
            "client_id": "beeflow",
            "user_id": uid,
            "req_id": int(mesh_id),
            "authcode": generate_random_str(16),
            "action": self.action,
            "url": callback,
            "params": {
                "proc_version": self.proc_version,
                "extra_type": "meshes",
                "extra_way": mesh_id,
                "format": "sqlite",
                "url_type": 3,
                "compress": 2,
                "lock_type": "r",
                "groupname": "",
                "cityname": "",
                "addinfo": {
                },
                "args": {
                    "dataurl": "",
                    "db_name": "",
                    "host": "",
                    "password": "",
                    "port": 0,
                    "username": ""
                }
            }
        }
        print("出库生成缓存请求参数:'{}'".format(req_data))
        now_try, max_try = 0, 4
        res_data = {}
        while now_try < max_try:
            try:
                res = requests.post(self.expimp_url, json=req_data, timeout=10 * 60)
            except Exception as e:
                print(f"try {now_try} time", e)
                now_try += 1
                time.sleep(0.2)
            else:
                res_data = res.json()
                break
        print("请求结果：'{}'".format(res_data))
        if "url" not in req_data:
            if (
                    "code" in res_data and res_data["code"] == 0
                    and "contents" in res_data
                    and res_data["contents"]["process_code"] == 0
            ):
                print(f"同步缓存生成成功：'{mesh_id}'")
            else:
                print(f"图幅：'{mesh_id}'缓存生成失败：'{res_data}'")
                return
        else:
            if "code" in res_data and res_data["code"] == 0:
                print(f"异步缓存生成成功：'{mesh_id}'")
            else:
                print(f"图幅：'{mesh_id}'缓存生成失败：'{res_data}'")
                return

        print(f"提交事务，{mesh_id}图幅批处理完成")


def generate_random_str(length=16):
    """
    生成一个指定长度的随机字符串，其中
    string.digits=0123456789
    string.ascii_letters=abcdefghigklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
    """
    str_list = [random.choice(string.digits + string.ascii_letters) for i in range(length)]
    random_str = ''.join(str_list)
    return random_str


def create_mysql_client(setting_name):
    conn = pymysql.connect(
        database=config[setting_name]['database'],
        user=config[setting_name]['user'],
        password=config[setting_name]['password'],
        host=config[setting_name]['host'],
        port=int(config[setting_name]['port']),
    )
    return conn
