# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""最小圈基 python 实现, 源自: https://console.cloud.baidu-int.com/devops/icode/repos/baidu/lbsgkpoicollect/gmapface/tree
/master/Baidu.Guoke.Algorithm/MinimalCycleBasis """

from enum import Enum
from typing import Dict, List, Set


class DeadLoopError(Exception):
    """
    当循环次数大于 DEAD_LOOP_COUNT 时发生。
    """
    pass


class Position:
    """表示一个节点位置"""

    def __init__(self, x: float = 0, y: float = 0):
        self.x = x
        self.y = y


class VisitStatus(Enum):
    """节点访问状态"""

    Unvisited = 0
    Discovered = 1
    Finished = 2


class Vertex:
    """表示一个节点"""

    def __init__(self):
        self.name = 0
        self.position = Position()
        self.adjacent_vertexes: Set[Vertex] = set()
        self.visit_status = VisitStatus.Unvisited

    def reset_visit_status(self):
        """重置访问状态"""
        self.visit_status = VisitStatus.Unvisited

    def has_adjacent(self):
        """是否有相邻点"""
        return any(self.adjacent_vertexes)

    def __lt__(self, other):
        return self.name < other.name

    def __gt__(self, other):
        return self.name > other.name

    def __str__(self):
        return f'Name: {self.name}, Adjacent: {", ".join([str(item.name) for item in self.adjacent_vertexes])}'


class CycleTree:
    """环树，可以存放整个区中的环, 一个环可能有多级嵌套的子环, 故而构成一棵树."""

    def __init__(self):
        self.cycle: List[int] = []
        self.children: List[CycleTree] = []

    def get_cycles(self, cycles: List[List[int]]):
        """递归获取树中环"""

        for child in self.children:
            child.get_cycles(cycles)

        if any(self.cycle):
            cycles.append(self.cycle)


class MinimalCycleBasis:
    """最小圈基"""

    def __init__(self, positions: List[List[float]], edges: List[List[int]], dead_loop_count=-1):
        self.positions = [Position(item[0], item[1]) for item in positions]
        self.edges = edges
        self.vertexes: List[Vertex] = []
        self.dead_loop_count = dead_loop_count

    def extract_cycles(self):
        """提取环"""
        cycles: List[List[int]] = []

        if self.positions is None or self.edges is None or not any(self.positions) or not any(self.edges):
            # 啥也没有就直接返回
            return cycles

        # 生成节点
        self.__generate_vertex()

        # 获取无向图中所有的区
        components: List[List[Vertex]] = []

        for vertex in self.vertexes:
            # 访问过的一定是在区内了，没访问过的则还没有划分区
            if vertex.visit_status == VisitStatus.Unvisited:
                component = MinimalCycleBasis.__extract_component_by_dfs(vertex)
                components.append(component)

        # 重置节点状态，以便后续 dfs 遍历
        for vertex in self.vertexes:
            vertex.reset_visit_status()

        # 提取最小环
        for component in components:
            self.__extract_basis(component).get_cycles(cycles)

        return cycles

    def __generate_vertex(self):
        """生成节点"""
        # 根据连接线生成所有节点
        vertex_dict: Dict[int, Vertex] = {}

        for edge in self.edges:
            for name in edge:
                if vertex_dict.get(name, None) is None:
                    vertex = Vertex()
                    vertex.name = name
                    vertex.position = self.positions[name]

                    vertex_dict[name] = vertex
                    self.vertexes.append(vertex)

        # 根据连接线为每个节点生成相邻点
        for edge in self.edges:
            vertex_a = vertex_dict[edge[0]]
            vertex_b = vertex_dict[edge[1]]

            vertex_a.adjacent_vertexes.add(vertex_b)
            vertex_b.adjacent_vertexes.add(vertex_a)

    def __extract_cycle_from_closed_walk(self, closed_walk: List[Vertex]):
        """在闭合回路中提取环

        Args:
            closed_walk: 闭合回路中所有的节点
        """
        tree = CycleTree()

        # 用来判断是否走上了重复节点
        duplicates: Dict[int, int] = {}
        detachments: List[int] = []
        i = 1
        closed_walk_count = len(closed_walk)

        while i < closed_walk_count - 1:
            num = duplicates.get(closed_walk[i].name, None)
            if num is None:
                # 暂时没有发现重复的节点，继续往下走
                duplicates[closed_walk[i].name] = i
                i += 1
                continue

            # 在闭合回路中，走上了重复的节点，这说明有嵌套环，必须打断
            index_min = num
            index_max = i
            detachments.append(index_min)

            # 把重复节点之后的所有点都删除
            for j in range(index_min + 1, index_max + 1):
                vertex = closed_walk[j]
                if duplicates.get(vertex.name, None) is not None:
                    duplicates.pop(vertex.name)
                if j in detachments:
                    detachments.pop(detachments.index(j))

            # 删除闭合回路中重复断
            delete_start = index_min + 1
            for _ in range(index_max - index_min):
                closed_walk.pop(delete_start)

            closed_walk_count = len(closed_walk)

            # 节点被删除后，循环标志向前移动
            i = index_min
            i += 1

        # 下面我们开始处理嵌套环
        if closed_walk_count > 3:
            # 大于 3 才能够成一个面，因为首尾点是重复的
            # 我们先假设起点位置也有一个分支，因为上面的循环没有考虑到这种情况
            detachments.append(0)

            # 下面的步骤是将嵌套环分离
            for index in detachments:
                # 分支点
                original = closed_walk[index]

                # 分支点下一个点
                max_vertex = closed_walk[index + 1]

                # 闭合回路上分支点前一个点，假如起始点为分支点，则返回最后一个点
                min_vertex = closed_walk[index - 1] if index > 0 else closed_walk[closed_walk_count - 2]

                min_position = Position(
                    x=min_vertex.position.x - original.position.x,
                    y=min_vertex.position.y - original.position.y
                )

                max_position = Position(
                    x=max_vertex.position.x - max_vertex.position.x,
                    y=max_vertex.position.y - max_vertex.position.y
                )

                # 方向判断
                is_counterclockwise = MinimalCycleBasis.__is_counterclockwise(max_position, min_position)

                in_wedge: List[Vertex] = []
                adjacent = original.adjacent_vertexes
                for vertex in adjacent:
                    if vertex.name == min_vertex.name or vertex.name == max_vertex.name:
                        # 假如是闭合回路上前后两点，则忽略，我们关注的是内部嵌套环
                        continue

                    # 嵌套环上第一个点与分支点的向量
                    first_position = Position(
                        x=vertex.position.x - original.position.x,
                        y=vertex.position.y - original.position.y
                    )

                    contains_vertex = (MinimalCycleBasis.__is_counterclockwise(first_position, min_position) and
                                       MinimalCycleBasis.__is_counterclockwise(max_position, first_position)) \
                        if is_counterclockwise else \
                        (MinimalCycleBasis.__is_counterclockwise(first_position, min_position) or
                         MinimalCycleBasis.__is_counterclockwise(max_position, first_position))

                    if contains_vertex:
                        # 的确是在 min 和 max 的中间，的确是嵌套的
                        in_wedge.append(vertex)

                # 判断是否有嵌套环
                if any(in_wedge):
                    clone = Vertex()
                    clone.name = original.name
                    clone.position = original.position

                    # 将分支点打断，也就是复制一份放到整体的节点集合中
                    self.vertexes.append(clone)

                    # 打断后分支点变成了新旧两份，将旧的节点和嵌套环上的节点相邻关系删除，再把这个相邻关系复制到新点上
                    for vertex in in_wedge:
                        original.adjacent_vertexes.discard(vertex)
                        vertex.adjacent_vertexes.discard(original)
                        clone.adjacent_vertexes.add(vertex)
                        vertex.adjacent_vertexes.add(clone)

                    # 提取以新点作为起始点的区
                    component = MinimalCycleBasis.__extract_component_by_dfs(clone)

                    # 从上一步的区中提取环，并作为当前树的子树
                    tree.children.append(self.__extract_basis(component))

            tree.cycle = MinimalCycleBasis.__extract_cycle(closed_walk)
        else:
            # 闭合回路不是一个面
            original = closed_walk[0]
            adjacent = closed_walk[1]

            clone = Vertex()
            clone.name = original.name
            clone.position = original.position

            # 将分支点打断，也就是复制一份放到整体的节点集合中
            self.vertexes.append(clone)

            # 打断后分支点变成了新旧两份，将旧的节点和嵌套环上的节点相邻关系删除，再把这个相邻关系复制到新点上
            original.adjacent_vertexes.discard(adjacent)
            adjacent.adjacent_vertexes.discard(original)
            clone.adjacent_vertexes.add(adjacent)
            adjacent.adjacent_vertexes.add(clone)

            # 提取以新点作为起始点的区
            component = MinimalCycleBasis.__extract_component_by_dfs(clone)

            tree.children.append(self.__extract_basis(component))
            if len(tree.cycle) == 0 and len(tree.children) == 1:
                # 如果一棵树它的子树只有一个，则将这颗子树存于 Cycle 中，以区分尽头
                child = tree.children[-1]
                tree.cycle = child.cycle
                tree.children = child.children

        return tree

    def __extract_cycle_from_component(self, component: List[Vertex]):
        min_vertex = MinimalCycleBasis.__get_start_vertex(component)

        # 定义一个闭合回路，将起点复制一份作为终点
        closed_walk: List[Vertex] = []
        current_vertex = min_vertex
        start = current_vertex
        closed_walk.append(start)

        # 顺时针优先寻找相邻点
        adjacent = MinimalCycleBasis.__get_clockwise_most(start)
        while adjacent is not start:
            # 不相等说明还没有绕回来
            closed_walk.append(adjacent)

            if 0 < self.dead_loop_count < len(closed_walk):
                raise DeadLoopError()

            # 逆时针优先寻找相邻点
            next_vertex = MinimalCycleBasis.__get_counterclockwise_most(current_vertex, adjacent)
            current_vertex = adjacent
            adjacent = next_vertex

        # 已经闭合
        closed_walk.append(start)

        # 递归提取环
        cycle_tree = self.__extract_cycle_from_closed_walk(closed_walk)

        # 删除无相邻点的节点
        index = 0
        component_count = len(component)
        while index < component_count:
            if not component[index].has_adjacent():
                component_count -= 1

                # 用最后一项替换要删除的项
                component[index] = component[component_count]

                # 再删除最后一项，实现 O(1) 效率的删除
                component.pop()
                index -= 1

            index += 1

        return cycle_tree

    @staticmethod
    def __remove_filaments(component: List[Vertex]):
        """删除孤立线

        Args:
            component: 环中区

        【孤立线】即不能构成闭环的线段
        """
        leaf_points: List[Vertex] = []

        for vertex in component:
            if len(vertex.adjacent_vertexes) == 1:
                # 如果一个节点只有一个相邻点，则为端点
                leaf_points.append(vertex)

        if any(leaf_points):
            # 发现了端点，则说明有孤立线
            for vertex in leaf_points:
                vertex_temp = vertex
                while len(vertex_temp.adjacent_vertexes) == 1:
                    # 将端点和其相邻点互相去除依赖
                    adjacent = vertex_temp.adjacent_vertexes.pop()
                    adjacent.adjacent_vertexes.discard(vertex_temp)
                    vertex_temp.adjacent_vertexes.discard(adjacent)

                    # 当把端点删除后，可能会产生新的端点，我们把旧端点的相邻点（只有一个）
                    # 设置为当前遍历项，继续判断这个点是否可能是端点（只有一个相邻点）
                    vertex_temp = adjacent

            # 上面的步骤已经把端点和其相邻点互相去除了依赖（相邻点信息已经被删除），
            # 下面开始真正的删除
            index = 0
            component_count = len(component)
            while index < component_count:
                if not component[index].has_adjacent():
                    component_count -= 1

                    # 用最后一项替换要删除的项
                    component[index] = component[component_count]

                    # 再删除最后一项，实现 O(1) 效率的删除
                    component.pop()
                    index -= 1
                index += 1

    def __extract_basis(self, component: List[Vertex]):
        """提取圈基

        Args:
            component: 一个区中所有的节点
        """
        tree = CycleTree()

        while any(component):
            # 删除孤立线
            MinimalCycleBasis.__remove_filaments(component)
            if any(component):
                tree.children.append(self.__extract_cycle_from_component(component))

        if len(tree.cycle) == 0 and len(tree.children) == 1:
            # 如果一棵树它的子树只有一个，则将这颗子树存于 Cycle 中，以区分尽头
            child = tree.children[0]
            tree.cycle = child.cycle
            tree.children = child.children

        return tree

    @staticmethod
    def __get_counterclockwise_most(prev_vertex: Vertex, current_vertex: Vertex):
        """逆时针优先寻找相邻点

        Args:
            prev_vertex: 上一个节点
            current_vertex: 当前节点
        """
        target_vertex = None

        # 下一个节点是否在当前节点的逆时针方向
        is_counterclockwise = False

        # 原点指向当前节点的向量，我们则假设 prev 在 (0,-1) 坐标，具体的值无所谓，重要是符号，代表了向量方向
        vector_current = Position(0, -1)

        # 原点指向下一个节点的向量
        vector_next = Position()

        if prev_vertex is not None:
            vector_current.x = current_vertex.position.x - prev_vertex.position.x
            vector_current.y = current_vertex.position.y - prev_vertex.position.y

        for adjacent in current_vertex.adjacent_vertexes:
            if adjacent == prev_vertex:
                continue

            # 当前点与相邻点的向量方向
            vector_adjacent = Position(
                x=adjacent.position.x - current_vertex.position.x,
                y=adjacent.position.y - current_vertex.position.y
            )

            # 默认把第一个相邻点作为目标点
            if target_vertex is None:
                target_vertex = adjacent
                vector_next = vector_adjacent

                is_counterclockwise = MinimalCycleBasis.__is_counterclockwise_or_coincide(
                    vector_current, vector_next)
                continue

            if is_counterclockwise:
                # 【上一个相邻点】在【当前点】的逆时针方向，我们继续判断【下一个相邻点】是否在【上一个相邻点】逆时针方向
                if MinimalCycleBasis.__is_counterclockwise(vector_current, vector_adjacent) \
                        and MinimalCycleBasis.__is_counterclockwise(vector_next, vector_adjacent):
                    # 看来【下一个相邻点】逆时针角度更大点
                    target_vertex = adjacent
                    vector_next = vector_adjacent

                    is_counterclockwise = MinimalCycleBasis.__is_counterclockwise_or_coincide(
                        vector_current, vector_next)
            elif MinimalCycleBasis.__is_counterclockwise(vector_current, vector_adjacent) or \
                    MinimalCycleBasis.__is_counterclockwise(vector_next, vector_adjacent):
                # 【上一个相邻点】在【当前点】顺时针方向，这样可不行，我们要努力找到一个逆时针方向的相邻点，
                # 如果【下一个相邻点】直接在【当前点】的逆时针方向，那样最好，一步到位，最坏的情况也要在【上一个相邻点】的逆时针方向
                target_vertex = adjacent
                vector_next = vector_adjacent

                # 情况变好了点，但不知道是否已经跑到【当前点】逆时针方向了
                is_counterclockwise = MinimalCycleBasis.__is_counterclockwise(
                    vector_current, vector_next)

        return target_vertex

    @staticmethod
    def __get_clockwise_most(current: Vertex):
        """顺时针优先寻找相邻点

        只有开始遍历闭环时才使用，后续都用逆时针优先方法
        """
        target_vertex = None
        vector_next = Position()

        for adjacent in current.adjacent_vertexes:
            # 当前点与相邻点的向量方向
            vector_adjacent = Position(
                x=adjacent.position.x - current.position.x,
                y=adjacent.position.y - current.position.y
            )

            # 默认把第一个相邻点作为目标点
            if target_vertex is None:
                target_vertex = adjacent
                vector_next = vector_adjacent
                continue

            # 【下一个相邻点】是否在【上一个相邻点】的顺时针方向
            if MinimalCycleBasis.__is_counterclockwise(vector_adjacent, vector_next):
                target_vertex = adjacent
                vector_next = vector_adjacent

        return target_vertex

    @staticmethod
    def __is_counterclockwise(vector1: Position, vector2: Position):
        """判断向量 vector2 是否在 vector1 的逆时针方向"""
        return vector2.x * vector1.y - vector2.y * vector1.x < 0

    @staticmethod
    def __is_counterclockwise_or_coincide(vector1: Position, vector2: Position):
        """判断向量 vector2 是否在 vector1 的逆时针方向 或 重叠"""
        return vector2.x * vector1.y - vector2.y * vector1.x <= 0

    @staticmethod
    def __get_start_vertex(component: List[Vertex]):
        """获取区中起始点

        所有节点中找 x 最小的，如果有 x 一样的，找 y 最小的
        """
        min_vertex = component[0]

        for vertex in component:
            current_x = vertex.position.x
            min_x = min_vertex.position.x

            if current_x < min_x:
                min_vertex = vertex
            elif (abs(current_x - min_x) < 1e-8) and (vertex.position.y < min_vertex.position.y):
                min_vertex = vertex

        return min_vertex

    @staticmethod
    def __extract_component_by_dfs(vertex_initial: Vertex):
        """dfs 获取区

        Args:
            vertex_initial: 起始节点
        """
        # 待查询的区
        component: List[Vertex] = []

        # 先将起点入栈
        stack: List[Vertex] = [vertex_initial]

        while len(stack) > 0:
            # 将栈顶节点设置为已发现状态
            vertex = stack[-1]
            vertex.visit_status = VisitStatus.Discovered

            # 遍历栈顶节点所有的相邻点
            # 我们假设这个栈顶节点所有的相邻点都被访问过了
            all_discovered = True
            for adjacent in vertex.adjacent_vertexes:
                if adjacent.visit_status == VisitStatus.Unvisited:
                    # 如果某个相邻点为初始未访问状态，则将其入栈，回到循环头，此时栈顶节点就变成了这个相邻点
                    stack.append(adjacent)

                    # 看来相邻点还没有全部被访问过
                    all_discovered = False
                    break

            if all_discovered:
                # 这个栈顶节点所有的相邻点都被访问过了，于是我们设置它的状态为 Finished，并将其纳入区中后出栈
                vertex.visit_status = VisitStatus.Finished
                component.append(vertex)
                stack.pop()

        return component

    @staticmethod
    def __extract_cycle(closed_walk: List[Vertex]):
        # 我们假设闭合回路已经是一个标准的环了
        cycle: List[int] = []
        for vertex in closed_walk:
            cycle.append(vertex.name)

        v_0 = closed_walk[0]
        v_1 = closed_walk[1]

        # 看一下第一个点是否有内嵌环相连
        v_branch = v_0 if len(v_0.adjacent_vertexes) > 2 else None

        # 我们把顺时针最大边标记删除
        v_0.adjacent_vertexes.discard(v_1)
        v_1.adjacent_vertexes.discard(v_0)

        while (v_branch is None or v_1 != v_branch) and len(v_1.adjacent_vertexes) == 1:
            # 顺时针优先删除相邻点只有 2 个的节点
            adj = v_1.adjacent_vertexes.pop()
            v_1.adjacent_vertexes.discard(adj)
            adj.adjacent_vertexes.discard(v_1)
            v_1 = adj

        # 首尾点不一致，说明环上边没有完全被删除
        if v_1 is not v_0:
            # 标记上一个循环删除结束的地方
            v_branch = v_1

            while v_0 != v_branch and len(v_0.adjacent_vertexes) == 1:
                # 顺时针优先删除相邻点只有 2 个的节点
                v_1 = v_0.adjacent_vertexes.pop()
                v_0.adjacent_vertexes.discard(v_1)
                v_1.adjacent_vertexes.discard(v_0)
                v_0 = v_1

        return cycle
