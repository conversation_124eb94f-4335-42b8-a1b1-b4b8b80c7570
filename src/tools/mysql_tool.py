# !/usr/bin/env python3
import pymysql
from typing import <PERSON><PERSON>, List, Dict, Any
import os
import sys

root_path = os.path.abspath(os.path.dirname(__file__) + "/../../")
if root_path not in sys.path:
    sys.path.insert(0, root_path)

from src.tools.conf_tools import get_mysql_conf


class MysqlTool:
    def __init__(self, name="default"):
        host, port, user, pwd, database = get_mysql_conf(name)
        self.db = None
        try:
            self.connection = pymysql.connect(host=host, port=int(port), user=user, password=pwd, db=database,
                                              charset="utf8mb4")
        except Exception as e:
            print("链接mysql失败", e)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db is not None:
            self.db.close()

    @staticmethod
    def create_insert_sql(table: str, data: dict) -> <PERSON><PERSON>[str, List]:
        """
        生成插入的sql
        :param table:
        :param data:
        :return:
        """
        if len(data) < 1:
            return "", ()
        sql = f"insert into  {table} (__FIELDS__) values (__VALUES__)"
        field_str = ""
        value_str = ""
        value_list = []
        for k, v in data.items():
            field_str += f"`{k}`,"
            value_str += f"%s,"
            value_list.append(str(v))
        sql = sql.replace("__FIELDS__", field_str[0:-1]).replace('__VALUES__', value_str[0:-1])
        param_list = value_list
        return sql, param_list

    @staticmethod
    def create_duplicate_insert_sql(table: str, data: dict, dup: dict) -> Tuple[str, List]:
        """
        生成插入，冲突更新的sql
        :param table:
        :param data:
        :param dup: 冲突时更新的kv
        :return:
        """
        if len(data) < 1:
            return "", []
        sql = f"insert into {table} (__FIELDS__) values (__VALUES__) on duplicate key update __DUP__ "
        field_str = ""
        value_str = ""
        dup_str = ""
        value_list = []
        dup_list = []
        for k, v in data.items():
            field_str += f"`{k}`,"
            value_str += "%s,"
            value_list.append(str(v))
        for k, v in dup.items():
            dup_str += f"`{k}`=%s,"
            dup_list.append(str(v))
        sql = sql.replace("__FIELDS__", field_str[0:-1]) \
            .replace("__VALUES__", value_str[0:-1]).replace("__DUP__", dup_str[0:-1])
        param_list = [*value_list, *dup_list]
        return sql, param_list

    @staticmethod
    def parse_where(where: Any) -> Tuple[str, List]:
        """
        解析where查询，参考odp框架
        :param where: 
            参数支持 =, > < != in like between
             支持：字符串: "id=1 and name=2"
                   {"id =": 1, "name =": 2}
                   {"id >": 1}
                   {"id in": [1,2,3,4,5]}
                   {"id like": "%123%"}
                   {"id between": [1, 3]}
        :return: sql 与 参数
        """
        if not where:
            return "", []
        # 字符串类型，直接返回
        if type(where).__name__ == "str":
            return where, []
        if type(where).__name__ != "dict":
            return "", []
        where_str = " 1 "
        where_list = []
        # 字典类型
        if type(where).__name__ == "dict":
            for k, v in where.items():
                # in 处理
                if " in" in k:
                    if type(v).__name__ != "list":
                        print("in 的 value 不是list，生成sql失败")
                        return "", []
                    where_str += f" and {k} ("
                    for item in v:
                        where_str += f"%s,"
                        where_list.append(str(item))
                    where_str = where_str[0:-1] + ")"
                # like 处理
                elif " like" in k:
                    where_str += f" and {k} %s "
                    where_list.append(str(v))
                # between处理
                elif " between" in k:
                    if type(v).__name__ != "list" or len(v) != 2:
                        print("between 的value 不是list 或者 参数个数不是2 ")
                        return "", []
                    where_str += f" and {k} %s and %s "
                    where_list.append(v[0])
                    where_list.append(v[1])
                else:
                    # 其他情况：直接 key value 拼接
                    where_str += f" and {k} %s "
                    where_list.append(str(v))
        return where_str, where_list

    @staticmethod
    def create_query_sql(table: str, fields: Any = "*", where: Any = "1", other: str = "") -> Tuple[str, List]:
        """
        生成查询语句
        :param table: 表格名称
        :param fields: 字段， 支持字符串和dict
        :param where:
        :param other: 其他查询条件,str 拼在最后，例如 order by xxx limit xx
        :return:
        """
        sql = f"select __FIELDS__ from {table} where __WHERE__ {other}"
        fields_str = ""
        if type(fields).__name__ == "list":
            for i in fields:
                if i.__contains__(".") or i.__contains__("`") or i.__contains__("(") or i.__contains__(" "):
                    fields_str += f"{i},"
                else:
                    fields_str += f"`{i}`,"
            fields_str = fields_str[0:-1]
        elif type(fields).__name__ == "str":
            fields_str = fields
        else:
            print(f"生成sql失败,fields字段不是str或者list类型{fields}")
            return "", []
        where_str, args = MysqlTool.parse_where(where)
        if where_str == "":
            return "", []
        sql = sql.replace("__FIELDS__", fields_str).replace("__WHERE__", where_str)
        return sql, args

    @staticmethod
    def create_update_sql(table, data: dict, where: Any, other: str = "") -> Tuple[str, List]:
        """
        生成更新的sql
        :param table: 表格
        :param data: 更新字段
        :param where: where条件
        :param other: 其他参数
        :return:
        """
        sql = f"update {table} set __SET__ where __WHERE__ {other}"
        where_str, args = MysqlTool.parse_where(where)
        if where_str == "" or len(data) < 1:
            return "", []
        set_str = ""
        set_list = []
        for k, v in data.items():
            v = str(v)
            set_str += f"`{k}`=%s,"
            set_list.append(str(v))
        sql = sql.replace("__SET__", set_str[0:-1]).replace("__WHERE__", where_str)
        return sql, [*set_list, *args]

    @staticmethod
    def create_del_sql(table, where: Any, other: str = "") -> Tuple[str, List]:
        sql = f"delete from {table} where __WHERE__  {other} "
        where_str, where_args = MysqlTool.parse_where(where)
        if not where_str or where_str == "":
            return "", []
        sql = sql.replace("__WHERE__", where_str)
        return sql, where_args

    @staticmethod
    def print_sql(sql: str, args: List) -> str:
        # print(f"生成的sql: {sql} 参数列表: {args}")
        # print(f'替换后的sql:' + sql.replace("%s", '\'{}\'').format(*args))
        return sql.replace("%s", '\'{}\'').format(*args)


if __name__ == '__main__':
    # sql, args = MysqlTool.create_update_sql(
    #     table="aaa", data={"id": 2},
    #     where={"id in": [1], "name like": '%2%', "d>": 0, "f between": [1, 3]},
    #     other="order by id desc limit 10")
    # sql, args = MysqlTool.create_query_sql(
    #     table="aaa", fields=['a.id', 'key'],
    #     where={"id in": [1], "name like": '%2%', "d>": 0, "f between": [1, 3]},
    #     other="order by id desc limit 10")
    # print(MysqlTool.print_sql(sql, args))
    sql, args = MysqlTool.create_del_sql(
        table="aaa",
        where={"id in": [1], "name like": '%2%', "d>": 0, "f between": [1, 3]},
        other="order by id desc limit 10")
    print(MysqlTool.print_sql(sql, args))
