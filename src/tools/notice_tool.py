import requests

MIS_NOTICE = "d6cdb0b2b61ae8d99c2f436628c01006d"  # mis策略消息通知群
STABILITY_MONITOR = "d7b137e827d9a9d3e18faef6dd8a7b3c1"  # 稳定性监控群，仅发报错类通知


def send_hi(msg: str, atuserids: list[str] = None, token=STABILITY_MONITOR, group_id=None):
    """发送如流消息
    Args:
        msg：消息内容
        atuserids: at群的某个人
        token: 群机器人token
        group_id: 必须是int形式, 群号,如果为空则忽略
    """
    url = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=" + token
    # 如流消息的header信息
    header = {}
    # 如果的body信息
    if atuserids is not None:
        body = [
            {"type": "TEXT", "content": msg},
            {"atuserids": atuserids, "atall": False, "type": "AT"},
        ]
    else:
        body = [{"type": "TEXT", "content": msg}]
    # 指定群号发送
    if group_id is not None:
        header["toid"] = [group_id]
    data = {
        "message": {
            "header": header,
            "body": body,
        }
    }
    requests.post(url, json=data)
