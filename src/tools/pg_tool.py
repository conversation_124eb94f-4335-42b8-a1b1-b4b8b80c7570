# -*- coding: utf-8 -*-
"""
pg库链接接口
"""
from functools import lru_cache

import psycopg2
import requests

import src.tools.conf_tools as conf_tools


@lru_cache(maxsize=1000)
def get_road_db_from_dcmpf():
    """
    通过查询接口库路径获取库
    """
    api = conf_tools.get_config("nuts_api")
    road_node = conf_tools.get_config("road_node")
    request_data = {
        "pathname": road_node,
        "action": "query",
        "type": "dbinfo",
    }
    r = requests.post(api, json=request_data)
    res_data = r.json()
    if 'data' in res_data and 'db' in res_data['data']:
        config = res_data['data']
        config['pwd'] = config['passwd']
        return config
    else:
        return None


@lru_cache(maxsize=30)
def get_road_db_with_index():
    """
    通过查询接口库路径获取库
    """
    api = conf_tools.get_config("beeflow_road_db_query")
    request_data = {
    }
    r = requests.post(api, json=request_data)
    res_data = r.json()
    if 'data' in res_data and 'db' in res_data['data']:
        config = res_data['data']
        config['pwd'] = config['passwd']
        return config
    else:
        return None


class PgTool:
    """ Pg库查询接口
    :attributes
        conn_poi: 线上库
        conn_back: 背景库
        conn_road: 道路库
    """

    def __init__(self, road_use_conf=False):
        """
        初始化链接
        :param road_use_conf 是否使用配置中的道路库（默认请求接口中的道路库）
        """
        conf = conf_tools.get_config("pg")
        if "poi_online" in conf:
            online_conf = conf['poi_online']
            self.conn_poi = self.get_db_conn(online_conf)
        else:
            raise Exception("poi_online conf not found")

        if "master_back" in conf:
            back_conf = conf['master_back']
            self.conn_back = self.get_db_conn(back_conf)
        else:
            raise Exception("poi_online conf not found")

        road_conf = {}
        if conf_tools.is_debug() or road_use_conf:
            # 测试环境 road库从配置中读取
            if 'road' in conf:
                road_conf = conf['road']
            else:
                raise Exception("road conf not found")
        else:
            # 线上环境，road库从接口中获取
            road_conf = get_road_db_from_dcmpf()

        self.conn_road = self.get_db_conn(road_conf)

    def get_db_conn(self, conf):
        """
        获取db查询
        """
        conn = psycopg2.connect(database=conf["db"], user=conf["user"],
                                password=conf["pwd"],
                                host=conf["host"],
                                port=conf["port"])
        return conn

    def __enter__(self):
        """
        支持with操作
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        支持with操作，自动关闭链接
        """
        if not self.conn_poi.closed:
            self.conn_poi.commit()
            self.conn_poi.close()
        if not self.conn_back.closed:
            self.conn_back.commit()
            self.conn_back.close()
        if not self.conn_road.closed:
            self.conn_road.commit()
            self.conn_road.close()

    def get_conn_road(self):
        """
        获取道路链接库
        """
        return self.conn_road

    def get_conn_poi(self):
        """
        获取poi链接（poi库）
        """
        return self.conn_poi

    def get_conn_back(self):
        """
        获取master_back链接(背景库)
        """
        return self.conn_back
