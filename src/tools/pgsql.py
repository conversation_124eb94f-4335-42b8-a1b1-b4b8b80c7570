"""
TODO: 未来将使用 sqlalchemy 替换此处功能
"""
import functools
import threading
import time
from pathlib import Path
from typing import Callable, Optional
from typing import Mapping
from typing import Sequence
from typing import Union

import psycopg2
from psycopg2._psycopg import connection

from src.tools import pg_tool, config_file, tsv

CONFIG = config_file.from_toml(Path(__file__).parent / "pgsql.toml")
POI_CONFIG = CONFIG["poi"]
COMPUTE_CONFIG = CONFIG["compute"]
POI_SLAVER_CONFIG = CONFIG["poi_slaver"]
POI_TEST_CONFIG = CONFIG["poi_test"]
POI_WORK_RESULT_CONFIG = CONFIG["poi_work_result"]
AOI_RESUME_SLAVER_CONFIG = CONFIG["aoi_resume_slaver"]
CD_CONFIG = CONFIG["cd"]
AOI_RESUME_CONFIG = CONFIG["aoi_resume"]
BACK_CONFIG = CONFIG["back"]
BACK_ACHIEVEMENT_CONFIG = CONFIG["back_achievement"]
BACK_TEST_CONFIG = CONFIG["back_test"]
TRAJECTORY_CONFIG = CONFIG["trajectory"]
TRAJECTORY_POINT_CONFIG = CONFIG["trajectory_point"]
TRAJECTORY_GUIDANCE_CONFIG = CONFIG["trajectory_guidance"]
ROAD_CONFIG = pg_tool.get_road_db_from_dcmpf
ROAD_CONFIG_WITH_INDEX = pg_tool.get_road_db_with_index
TRANS_ID = CONFIG["trans_id"]
DEST_TRAJ = CONFIG["dest_traj"]
AOI_BACK = CONFIG["aoi_back"]
TRAJ_FEATURE = CONFIG["traj_feature_db"]
TRAJ_FEATURE_NEW = CONFIG["traj_feature_db_new"]
EQ_GUARANTEE_CONFIG = CONFIG["eq_guarantee"]
AOI_DEST_TRAJ = CONFIG["aoi_dest_traj_db"]
AOI_DEST_TRAJ2 = CONFIG["aoi_dest_traj_db2"]
ACCEPT_DATA = CONFIG["accept_data"]
TRAJ_DB = CONFIG["traj_db"]
TRAJ_DB2 = CONFIG["traj_db2"]
INDOOR = CONFIG["indoor"]
BYD_TRAJ = CONFIG["byd_traj"]

_local = threading.local()


def pgsql_cache(ttl: int = 600):
    # noinspection PyBroadException
    def clear(conn: Optional[connection]):
        if conn and not conn.closed:
            try:
                conn.commit()
            except Exception:
                pass

            try:
                conn.close()
            except Exception:
                pass

    def decorator(func):
        @functools.wraps(func)
        def wrapper(config: Union[dict, Callable[[], dict]]) -> connection:
            config_dict = config if isinstance(config, dict) else config()
            if not hasattr(_local, "cache"):
                _local.cache = {}

            key = hash(tuple(sorted(config_dict.items())))
            prev_conn, expire = _local.cache.get(key, (None, -1))
            if prev_conn is None or prev_conn.closed or expire < time.monotonic():
                clear(prev_conn)
                _local.cache[key] = (func(config_dict), time.monotonic() + ttl)

            return _local.cache[key][0]

        return wrapper

    return decorator


def get_connection(config: Union[dict, Callable[[], dict]]) -> connection:
    config = config if type(config) is dict else config()
    return psycopg2.connect(
        database=config["db"],
        user=config["user"],
        password=config["pwd"],
        host=config["host"],
        port=config["port"],
    )


@pgsql_cache(ttl=1200)
def get_connection_ttl(config: Union[dict, Callable[[], dict]]) -> connection:
    conn = psycopg2.connect(
        database=config["db"],
        user=config["user"],
        password=config["pwd"],
        host=config["host"],
        port=config["port"],
    )
    conn.autocommit = True
    return conn


def fetch_one(conn: connection, sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    with conn.cursor() as c:
        c.execute(sql, args)
        return c.fetchone()


def fetch_all(conn: connection, sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    with conn.cursor() as c:
        c.execute(sql, args)
        return c.fetchall()


def execute(conn: connection, sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    with conn.cursor() as c:
        c.execute(sql, args)

    conn.commit()


def execute_many(conn: connection, sql: str, args: Union[Sequence, Mapping[str, any], None] = None):
    with conn.cursor() as c:
        c.executemany(sql, args)

    conn.commit()


def copy_to_tsv(config, sql: str, save_path: Path, for_batch=None, batch_size=10_000):
    """
    导出 sql 数据到 tsv 文件
    :param config: 数据库配置
    :param sql: sql 语句
    :param save_path: tsv 文件路径
    :param for_batch: 批量处理函数：(conn, rows) -> rows，conn 为当前数据库连接，rows 为当前批量数据
    :param batch_size: 批量大小
    """
    with get_connection(config) as conn, conn.cursor(name=save_path.stem) as cursor:
        cursor.execute(sql)
        while True:
            rows = cursor.fetchmany(batch_size)
            if not rows:
                break

            rows = for_batch(conn, rows) if for_batch else rows
            tsv.write_tsv(save_path, rows, mode="a")
