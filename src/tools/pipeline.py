# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管线：用于组合多个策略函数为一个策略函数。
"""
import functools
from datetime import datetime
from typing import Callable
from typing import Union


def get_desc():
    """
    返回一个装饰器 @desc()，用于装饰一个 pipe 函数。可自动将其函数名打印为日志。

    - desc.disabled: bool 禁用日志打印。
    - desc.attach(Pipeline): 将装饰器与对应的 pipeline 进行关联，以获知整体流程的总步骤数。e.g. desc.attach(pipeline)
    - desc.output(str): 日志的输出函数，默认为 print()。
    """
    count = 0

    def descriptor(title: str = None):
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                if descriptor.disabled:
                    func(*args, **kwargs)
                    return

                text = title
                if text is None:
                    func_name: str = func.__name__
                    text = func_name.replace("_", " ")

                nonlocal count
                count += 1
                is_latest_pipe = False
                now = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                if descriptor.total > 0:
                    length = len(str(descriptor.total))
                    display_text = f"[{now}] step: {count:0{length}}/{descriptor.total} {text}"
                    if count >= descriptor.total:
                        count = 0
                        is_latest_pipe = True
                else:
                    display_text = f"[{now}] step: {count} {text}"

                descriptor.output(display_text)
                func(*args, **kwargs)
                if is_latest_pipe:
                    now = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    descriptor.output(f"[{now}] completed!")

            wrapper.is_desc = True
            return wrapper

        return decorator

    def attach_pipe(pipe: "Pipeline"):
        desc_count = 0

        def action(func):
            # 兼容 functools.partial()，其包装后的函数对象内含原始函数 func
            if hasattr(func, "func"):
                action(func.func)

            if hasattr(func, "is_desc"):
                nonlocal desc_count
                desc_count += 1

        walk_pipe(pipe, action)
        descriptor.total = desc_count

    descriptor.disabled = False
    descriptor.total = 0
    descriptor.attach = attach_pipe
    descriptor.output = print
    return descriptor


def print_desc(desc: Union[str, Callable[[any], str]]):
    """
    构建一个 pipe，用于打印日志。
    @param desc: 可直接使用 str，也可以提供一个函数，该函数的传入变量为该 pipeline 的 ctx。
    """

    def pipe(ctx: any, proceed):
        if isinstance(desc, str):
            print(desc)
        else:
            print(desc(ctx))

        proceed()

    return pipe


def empty_pipe(_, proceed):
    """
    提供一个空的中间件，一般用作组装时的起始中间件。
    """
    proceed()


def walk_pipe(pipe, action):
    if isinstance(pipe, Pipeline):
        walk_pipe(pipe.runner, action)
    elif isinstance(pipe, ComposedPipe):
        walk_pipe(pipe.current, action)
        walk_pipe(pipe.next, action)
    else:
        action(pipe)


class ComposedPipe:
    """
    组合两个（处理相同上下文）的中间件，返回一个新的中间件实例。
    注意：不要实现为高阶函数，因为闭包可能无法在多进程中使用。
    """

    def __init__(self, current_pipe, next_pipe):
        self.current = current_pipe
        self.next = next_pipe

    def __call__(self, *args, **kwargs):
        ctx, proceed = args
        self.current(ctx, lambda: self.next(ctx, proceed))


class ConvertedPipe:
    """
    转换一个 pipe 所接受的上下文类型，使之适配新的上下文，使用指定的上下文转换器。
    注意：不要实现为高阶函数，因为闭包可能无法在多进程中使用。
    """

    def __init__(self, pipe, converter):
        self.__pipe = pipe
        self.__converter = converter

    def __call__(self, *args, **kwargs):
        ctx, proceed = args
        self.__pipe(self.__converter(ctx), proceed)


class Pipeline:
    """
    管线：用于组合任意数量的中间件，以对相同的上下文进行处理。
    """

    def __init__(self, *pipes: Union[Callable[[any, Callable[[], None]], None], "Pipeline"]):
        runner = empty_pipe
        for pipe in pipes:
            runner = ComposedPipe(runner, pipe)

        self.runner = runner

    def __call__(self, ctx: any, proceed: Callable[[], None] = None):
        proceed = proceed if proceed else lambda: None
        self.runner(ctx, proceed)
        return ctx

    def adapt(self, converter) -> "Pipeline":
        """
        返回一个新的 pipeline，其接收经由 converter 转换后的上下文类型。

        Example:

        # pipe_x 表示：接受 x 类型上下文的 Pipeline 实例。
        pipe_c = Pipeline(
            pipe_a.adapt(convert_c_to_a),
            pipe_b.adapt(convert_c_to_b),
        )
        pipe_c(c)

        @param converter: 上下文转换器，形如 (ctx_a) -> ctx_b 的函数。
        @return: 返回一个新的 pipeline。
        """
        pipeline = Pipeline()
        pipeline.runner = ConvertedPipe(self.runner, converter)
        return pipeline
