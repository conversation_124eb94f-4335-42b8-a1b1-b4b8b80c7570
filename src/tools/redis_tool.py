# !/usr/bin/env python3

import redis
import os
import sys
import uuid
from typing import Tuple

ROOT_PATH = os.path.abspath(os.path.dirname(__file__) + "/../../")
if ROOT_PATH not in sys.path:
    sys.path.insert(0, ROOT_PATH)

from src.tools.conf_tools import get_redis_conf
from src.const.redis_key import ConstRedisKey


class RedisTool:
    """
    返回redis链接
    """

    def __init__(self, name='default'):
        self.redis_conn = None
        host, port, auth = get_redis_conf(name)
        # print(host, port, auth)
        self.host = host
        self.port = port
        self.auth = auth
        if port < 1 or host == "":
            raise Exception('无法获取redis链接')
        else:
            if auth != "":
                self.redis_conn = redis.Redis(host=host, port=port, decode_responses=True, password=auth)
            else:
                self.redis_conn = redis.Redis(host=host, port=port, decode_responses=True, db=0)

    def reconnect(self):
        """
        重新连接
        :return:
        """
        if self.auth != "":
            self.redis_conn = redis.Redis(host=self.host, port=self.port, decode_responses=True, password=self.auth)
        else:
            self.redis_conn = redis.Redis(host=self.host, port=self.port, decode_responses=True, db=0)

    def __enter__(self):
        """
        支持with操作
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        支持with操作，自动关闭链接
        :param exc_type:
        :param exc_val:
        :param exc_tb:
        :return:
        """
        if self.redis_conn is not None:
            self.redis_conn.close()
            self.redis_conn = None

    def set_lock(self, key, expire=600) -> Tuple[bool, str]:
        """
        设置锁
        :param key: key
        :param expire: 过期时间
        :return:
        """
        value = str(uuid.uuid4())
        ret = self.redis_conn.set(f"{ConstRedisKey.REDIS_LOCK_PREFIX}{key}", value, ex=expire, nx=True)
        if ret:
            return True, value
        else:
            return False, ""

    def release_lock(self, key, lock_value) -> bool:
        """
        释放锁，需要依赖加锁时候的value
        :param key:
        :param lock_value:
        :return:
        """
        ret = self.redis_conn.get(f"{ConstRedisKey.REDIS_LOCK_PREFIX}{key}")
        if ret is None or ret == "":
            return True
        if ret != lock_value:
            return False
        self.redis_conn.delete(key)
        return True


if __name__ == '__main__':
    # 测试
    with RedisTool() as rt:
        rt.redis_conn.set("a", "1", ex=3600)
        print(rt.redis_conn.get("a"))
