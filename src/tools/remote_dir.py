"""
远程目录同步，当前主要使用 AFS。基于假设：远程资源不可变。

- push: 确认远程资源不存在，则上传`dirName.tar`。
- fetch: 确认远程资源存在，则下载`dirName.tar`。
- pull: 在 fetch 的基础上，清空本地 `dirName`，并解压`dirName.tar`代替之。
"""

import shutil
from contextlib import contextmanager
from os import PathLike
from pathlib import Path
from typing import Union

from src.tools import afs_tool, function


class RemoteDir:
    """
    远程目录同步：代表一个远程根目录，可以同步其中的子目录。子目录在 AFS 上都存为`dirName.tar`。
    """

    def __init__(self, remote_root: str, local_root: PathLike = None):
        self.remote_root = remote_root
        self.local_root = local_root or Path("cache_remote_dir")
        self.__afs_service = afs_tool.AfsTool()

    @contextmanager
    def sync(
        self, relative_path: Union[PathLike, str], force_pull=False, force_push=False
    ) -> Path:
        """
        pull -> doSomething -> push，如果在 doSomething 中发生异常，则会清空本地文件夹，并且不会 push。
        """
        try:
            local_dir = self.pull(relative_path, force_pull)
            yield local_dir
        except Exception as e:
            raise e
        else:
            self.push(relative_path, force_push)

    def pull(self, relative_path: Union[PathLike, str], force=False):
        """
        下载远程资源到本地并解压，返回本地目录，若远程和本地同时不存在，则创建并返回一个空目录。
        当 force=True 时，清空本地资源，强制下载远程版本。
        """
        ok, local_dir = self._get_local_dir(relative_path)
        if ok:
            if force:
                shutil.rmtree(local_dir)
            else:
                return local_dir

        local_archive = self.fetch(relative_path, force)
        if local_archive:
            local_dir = _unpack_archive(local_archive)
            local_archive.unlink()
        else:
            local_dir.mkdir(parents=True, exist_ok=True)

        return local_dir

    def fetch(self, relative_path: Union[PathLike, str], force=False):
        """
        下载远程资源到本地（但并不解压），返回本地备份文件。当 force=True 时，清空本地资源，强制下载远程版本。
        """
        ok, local_archive = self._get_local_archive(relative_path)
        if ok:
            if force:
                local_archive.unlink()
            else:
                return local_archive

        ok, remote_archive = self._get_remote_archive(relative_path)
        if not ok:
            return None

        self.__afs_service.get(remote_archive, local_archive.parent)
        return local_archive

    def push(self, relative_path: Union[PathLike, str], force=False):
        """
        上传本地目录到远程。当 force=True 时，清空远程资源，强制上传本地版本。
        """
        ok, remote_archive = self._get_remote_archive(relative_path)
        if ok:
            if force:
                self.__afs_service.rm(remote_archive)
            else:
                return

        ok, local_dir = self._get_local_dir(relative_path)
        if not ok:
            return

        local_archive = _pack_archive(local_dir)
        self.__afs_service.put(local_archive, remote_archive)
        local_archive.unlink()

    def _get_remote_archive(self, relative_path: Union[PathLike, str]):
        """
        返回：是否可用，路径
        """
        remote_archive = f"{self.remote_root}/{relative_path}.tar"
        available = self.__afs_service.test(remote_archive, "-e")
        return available, remote_archive

    def _get_local_archive(self, relative_path: Union[PathLike, str]):
        """
        返回：是否可用，路径
        """
        local_archive = self.local_root / f"{relative_path}.tar"
        return local_archive.is_file(), local_archive

    def _get_local_dir(self, relative_path: Union[PathLike, str]):
        """
        返回：是否可用，路径
        """
        local_dir = self.local_root / relative_path
        if local_dir.is_file():
            local_dir.unlink()

        local_dir.mkdir(parents=True, exist_ok=True)
        return any(local_dir.iterdir()), local_dir


def _pack_archive(archive_dir: Path):
    """
    打包一个目录，返回打包后的文件。（备份文件与源目录同名同层级）
    """
    archive_file = archive_dir.parent / f"{archive_dir.name}.tar"
    if archive_file.exists():
        archive_file.unlink()

    function.exec_shell_cmd(
        cmd=f"tar -cf {archive_dir.name}.tar -C . {archive_dir.name}",
        cwd=archive_dir.parent,
    )
    return archive_file


def _unpack_archive(archive_file: Path):
    """
    解压一个压缩包，返回解压后的目录。（解压后的目录与源文件同名同层级）
    """
    archive_dir = archive_file.parent / archive_file.stem
    if archive_dir.exists():
        shutil.rmtree(archive_dir)

    function.exec_shell_cmd(
        cmd=f"tar -xf {archive_file.name}",
        cwd=archive_file.parent,
    )
    return archive_dir
