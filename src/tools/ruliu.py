"""
如流消息推送模块
"""
import requests


# 门前生产监控
TOKEN_STOREFRONT_PROD_MONITOR = "db356c6368b6a526edb31cba1bbd9a4c9"


def send(msgs: list[dict], access_token=TOKEN_STOREFRONT_PROD_MONITOR):
    """
    发送如流消息
    """
    webhook = f"http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token={access_token}"
    message = {"message": {"body": msgs}}
    resp = requests.post(webhook, json=message)
    return resp.json()
