# !/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import List

import pytest

from src.tools.minimal_cycle_basis import MinimalCycleBasis


class Point:
    def __init__(self, x, y):
        self.x = x
        self.y = y


class TestMinimalCycleBasis(object):
    # 读取测试数据
    with open("data/simple_graph.txt") as f:
        data = f.readlines()

    # 开始构造无向图
    point_count = int(data[0])
    node_list: List[Point] = []

    for point_index in range(1, point_count + 1):
        point_arr = list(filter(None, data[point_index].split(" ")))
        node_list.append(Point(int(point_arr[0]), int(point_arr[1])))

    line_start_index = point_count + 2
    line_count = int(data[line_start_index])
    line_list: List[Point] = []

    for line_index in range(line_start_index + 1, line_count + line_start_index + 1):
        line_arr = list(filter(None, data[line_index].split(" ")))
        line_list.append(Point(int(line_arr[0]), int(line_arr[1])))

    # 提取最小圈基
    cycles = MinimalCycleBasis(
        positions=[[item.x, item.y] for item in node_list],
        edges=[[item.x, item.y] for item in line_list]
    ).extract_cycles()

    # 验证圈基提取是否正确
    cycle_start_index = line_count + line_start_index + 2
    cycle_count = int(data[cycle_start_index])
    cycle_list: List[List[int]] = []

    for cycle_index in range(cycle_start_index + 1, cycle_count + cycle_start_index + 1):
        cycle_arr = list(filter(None, data[cycle_index].split(" ")))
        cycle_list.append([int(item) for item in cycle_arr])

    assert len(cycles) == len(cycle_list)

    for cycle_index in range(cycle_count):
        output_cycle = cycles[cycle_index]
        test_cycle = cycle_list[cycle_index]

        assert len(output_cycle) == len(test_cycle)

        for node_index in range(len(output_cycle)):
            output_node_index = output_cycle[node_index]
            test_node_index = test_cycle[node_index]

            assert output_node_index == test_node_index


if __name__ == '__main__':
    pytest.main()
