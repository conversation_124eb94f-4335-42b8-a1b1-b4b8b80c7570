# !/usr/bin/env python3
"""
轨迹提供器
"""
import hashlib
import time
from typing import Protocol
from urllib.parse import urlencode

import mapio.utils.coord
import psycopg2
import psycopg2.extras
import requests
import shapely.wkt
from retrying import retry
from shapely import LineString

from src.batch_process.pgsql_stabilizer import PgsqlStabilizer
from src.charging_station.helper import BnsExecutor, get_nested_value
from src.tools import pipeline, pgsql
from src.tools.end_point_track_provider import get_end_point_track_by_api
from src.tools.uid_bid_helper import bid_to_uid

desc = pipeline.get_desc()


class TrackProvider(Protocol):
    """
    轨迹提供器协议
    """

    def get_tracks(self, region, region_type=None, desired_fields=None, **kwargs):
        pass


def clip_records(records, desired_fields):
    """
    字段裁剪
    """
    clipped_records = []

    for record in records:
        record_dict = dict(record)

        useless_names = []
        for field_name in record_dict.keys():
            if field_name not in desired_fields:
                useless_names.append(field_name)

        for useless_name in useless_names:
            del record_dict[useless_name]

        clipped_values = []
        for field_name in desired_fields:
            clipped_values.append(record_dict[field_name])
        clipped_records.append(tuple(clipped_values))

    return clipped_records


class DestTrackProvider:
    """
    终点轨迹提供器
    """
    DATABASE_CONFIGS = {
        'aoi_dest_traj_db': {
            'host': '*************',
            'db': 'aoi_dest_traj_db',
            'port': '5432',
            'user': 'aoi_dest_traj_rw',
            'pwd': 'aoi_dest_traj_rw',
        },
        'aoi_dest_traj_db2': {
            'host': '*************',
            'db': 'aoi_dest_traj_db2',
            'port': '6432',
            'user': 'aoi_dest_traj_rw',
            'pwd': 'aoi_dest_traj_rw',
        }
    }
    MASTER_DATABASE = 'aoi_dest_traj_db'

    def __init__(self):
        self.stabs = dict()

    def __enter__(self):
        for database_name in DestTrackProvider.DATABASE_CONFIGS.keys():
            self.stabs[database_name] = PgsqlStabilizer(
                config=DestTrackProvider.DATABASE_CONFIGS[database_name],
                cursor_factory=psycopg2.extras.RealDictCursor,
            )

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        for stab in self.stabs.values():
            if stab.connection is not None:
                stab.connection.close()

    @staticmethod
    def __get_hash_index(bid):
        """
        使用 hashlib 计算 bid 的哈希值
        """
        hash_object = hashlib.md5(bid.encode())
        hash_value = int(hash_object.hexdigest(), 16)
        return hash_value % 10

    @staticmethod
    def __get_database_name(stab: PgsqlStabilizer, bid):
        sql = '''
            select db_name from dest_traj_bid_db_index where bid = %s limit 1;
        '''
        row = stab.fetch_one(sql, (bid,))
        if row is None:
            return None

        return row['db_name']

    def __get_tracks_by_bid(self, bid, desired_fields):
        sql = '''
            select id, bid, st_astext(geom) as geom, hash_index, props, traj_time, create_time 
            from dest_traj 
            where bid = %s and 
                  hash_index = %s;
        '''

        if DestTrackProvider.MASTER_DATABASE not in self.stabs:
            return []

        master_stab = self.stabs[DestTrackProvider.MASTER_DATABASE]
        database_name = DestTrackProvider.__get_database_name(master_stab, bid)
        if database_name not in self.stabs:
            return []

        stab = self.stabs[database_name]
        hash_index = DestTrackProvider.__get_hash_index(bid)
        rows = stab.fetch_all(sql, (bid, hash_index))

        return clip_records(rows, desired_fields) if any(desired_fields) else rows

    def __get_tracks_by_wkt(self, wkt, desired_fields):
        sql = '''
            select id, bid, st_astext(geom) as geom, hash_index, props, traj_time, create_time 
            from dest_traj 
            where st_intersects(geom, st_geomfromtext(%s, 4326));
        '''

        total_rows = []
        for stab in self.stabs.values():
            rows = stab.fetch_all(sql, (wkt,))
            total_rows.extend(clip_records(rows, desired_fields) if any(desired_fields) else rows)

        return total_rows

    def get_tracks(self, region, region_type=None, desired_fields=None, **kwargs):
        """
        获取轨迹
        """
        actual_region_type = region_type if region_type is not None else kwargs.get('region_type', 'bid')
        actual_desired_fields = desired_fields if desired_fields is not None else kwargs.get('desired_fields', [])

        if actual_region_type == 'bid':
            return self.__get_tracks_by_bid(region, actual_desired_fields)
        elif actual_region_type == 'wkt':
            return self.__get_tracks_by_wkt(region, actual_desired_fields)
        else:
            raise ValueError("Invalid region_type")


class DestApiTrackProvider:
    """
    API 方式的终点轨迹提供器（请注意，qps 务必控制在 1 以内）
    """

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    @staticmethod
    def __get_tracks_by_bid(bid):
        tracks = []

        for line in get_end_point_track_by_api(bid):
            tracks.append({'geom': line.wkt})

        return tracks

    # noinspection PyUnusedLocal
    def get_tracks(self, region, region_type=None, desired_fields=None, **kwargs):
        """
        获取轨迹
        """
        time.sleep(1)  # 固定 qps，防止打爆接口。

        actual_region_type = region_type if region_type is not None else kwargs.get('region_type', 'bid')

        if actual_region_type == 'bid':
            return self.__get_tracks_by_bid(region)
        else:
            raise ValueError("Invalid region_type")


class ExpTrackProvider:
    """
    经验轨迹提供器
    """

    def __enter__(self):
        self.stab = PgsqlStabilizer(pgsql.TRAJECTORY_CONFIG, cursor_factory=psycopg2.extras.RealDictCursor)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.stab.connection is not None:
            self.stab.connection.close()

    def __get_tracks_by_bid(self, bid, desired_fields):
        sql = '''
            select id, province, city, mesh_id, end_poi_uid, 
                   end_poi_name, st_astext(end_track_line) as end_track_line, num_yaw, yaw_rate, dist_dest, 
                   confidence, create_time, num_track, num_track_route_end, route_end_rank, 
                   route_end_percentage
            from exp_traj_monthly
            where end_poi_uid = %s;
        '''

        uid = bid_to_uid(bid)
        if uid is None:
            raise Exception(f'BID {bid} can not be converted to UID.')

        rows = self.stab.fetch_all(sql, (uid,))

        return clip_records(rows, desired_fields) if any(desired_fields) else rows

    def get_tracks(self, region, region_type=None, desired_fields=None, **kwargs):
        """
        获取轨迹
        """
        actual_region_type = region_type if region_type is not None else kwargs.get('region_type', 'bid')
        actual_desired_fields = desired_fields if desired_fields is not None else kwargs.get('desired_fields', [])

        if actual_region_type == 'bid':
            return self.__get_tracks_by_bid(region, actual_desired_fields)
        else:
            raise ValueError("Invalid region_type")


class DriveTrackProvider:
    """
    车行轨迹提供器（请注意，qps 务必控制在 1 以内）
    """
    BNS_HOST = 'group.opera-Online-NaviTrajAdvancedSearch-AdvancedSearch-000-gz.map-navi.all'

    def __init__(self):
        self.bns_executor = BnsExecutor(self.BNS_HOST, auto_retry=True)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    @staticmethod
    def __get_response(ip, port, data):
        """
        获取接口响应数据
        """
        drive_sub_src = 1
        short_link_id, src = data
        end_time = int(time.time())
        start_time = end_time - 15 * 24 * 60 * 60
        api = f'http://{ip}:{port}/AsService/as_link_service'

        params = {
            'sort': False,
            'opt': {
                'src': src,
                'sub_src': drive_sub_src,
                'with_data': True,
                'time': {
                    'time_s': start_time,
                    'time_e': end_time
                }
            },
            'ui_link_req': [
                {
                    'link_id': short_link_id
                }
            ]
        }
        return requests.post(api, json=params)

    @staticmethod
    def __get_short_link_id(link_id):
        api = "http://mapde-poi.baidu-int.com/prod/api/roadLinkTrans"
        resp = requests.get(api, params={"ids": link_id, "trans_type": 1})
        return resp.json()["data"][link_id]

    @staticmethod
    def __trans_wgs84_linestring_to_gc02(line_wkt):
        line_coords = shapely.wkt.loads(line_wkt).coords
        trans_line_points = []

        for coord in line_coords:
            gcj_lon, gcj_lat = mapio.utils.coord.wgs84_to_gcj02(coord[0], coord[1])
            trans_line_points.append((gcj_lon, gcj_lat))

        return LineString(trans_line_points).wkt

    def __parse_track(self, track_json):
        """
        解析轨迹
        """
        result = []

        traj_infos = get_nested_value(track_json, ['traj_info'])
        if traj_infos is None:
            return result

        for traj_info in traj_infos:
            cuid = traj_info['cuid']
            points = traj_info['traj_data']['traj_data']
            coordinates = [(point['x'], point['y']) for point in points]
            wkt = 'LINESTRING (' + ', '.join(f'{x} {y}' for x, y in coordinates) + ')'
            result.append({
                'cuid': cuid,
                'wkt': self.__trans_wgs84_linestring_to_gc02(wkt)
            })

        return result

    # noinspection PyUnusedLocal
    def get_tracks(self, region, region_type=None, desired_fields=None, **kwargs):
        """
        获取轨迹
        """
        actual_region_type = region_type if region_type is not None else kwargs.get('region_type', 'link_id')
        src = kwargs.get('src', 4)

        if actual_region_type == 'link_id':
            short_link_id = self.__get_short_link_id(region)
        elif actual_region_type == 'short_link_id':
            short_link_id = region
        else:
            raise Exception('Invalid region_type.')

        try:
            tracks = []

            outer_link_id = short_link_id + '0'  # 这里的 0 是意思是进入的方向
            outer_track_json = self.bns_executor.execute(self.__get_response, (outer_link_id, src)).json()
            tracks.extend(self.__parse_track(outer_track_json))

            return tracks

        except Exception as e:
            print(e)
            return []


class NavigationDrivingTrackProvider:
    """
    车行导航轨迹提供器
    """
    DEFAULT_AK = 'gfx6o6iqAhkENib7R6of8BMWR4OtLf7r'

    def __enter__(self):
        self.stab = PgsqlStabilizer(pgsql.POI_SLAVER_CONFIG)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.stab.connection is not None:
            self.stab.connection.close()

    @staticmethod
    @retry(stop_max_attempt_number=3, wait_random_min=1000, wait_random_max=2000)
    def __get_response(destination_geom, origin_geom, ak):
        """
        获取接口响应数据
        """
        url = "http://mapde-poi.baidu-int.com/api_navigate/v1/route_plan"
        params = {
            "origin": f"{origin_geom.y},{origin_geom.x}",
            "destination": f"{destination_geom.y},{destination_geom.x}",
            "ak": ak,
            "coord_type": 'gcj02',
            "ret_coordtype": "gcj02",
            "walkinfo": 1,
        }

        response_json = requests.get(url=url, params=params).json()
        status = response_json['status']
        if status != 0:
            raise Exception(response_json)

        return response_json

    def __get_tracks_by_bid(self, destination_bid, origin_wkt, ak):
        sql = '''
            select st_astext(geometry) from poi where bid = %s;
        '''

        row = self.stab.fetch_one(sql, (destination_bid,))
        if row is None:
            return None

        return self.__get_tracks_by_wkt(row[0], origin_wkt, ak)

    def __get_tracks_by_wkt(self, destination_wkt, origin_wkt, ak):
        try:
            response_json = self.__get_response(shapely.wkt.loads(destination_wkt), shapely.wkt.loads(origin_wkt), ak)
        except:
            return None

        all_coords = []
        steps = get_nested_value(response_json, ['result', 'routes', 0, 'steps'])

        if steps is None:
            return None

        for step in steps:
            path_wkt = f"LINESTRING ({step['path'].replace(',', ' ').replace(';', ', ')})"
            all_coords.extend(shapely.wkt.loads(path_wkt).coords)

        end_walk = get_nested_value(response_json, ['result', 'end_walk', 0])

        return {'wkt': LineString(all_coords).wkt, 'has_end_walk': end_walk is not None}

    # noinspection PyUnusedLocal
    def get_tracks(self, region, region_type=None, desired_fields=None, **kwargs):
        """
        获取轨迹
        """
        actual_region_type = region_type if region_type is not None else kwargs.get('region_type', 'bid')
        origin_wkt = kwargs.get('origin_wkt', None)
        ak = kwargs.get('ak', self.DEFAULT_AK)

        if origin_wkt is None:
            raise ValueError("origin_wkt is required.")

        if actual_region_type == 'bid':
            return self.__get_tracks_by_bid(region, origin_wkt, ak)
        elif actual_region_type == 'wkt':
            return self.__get_tracks_by_wkt(region, origin_wkt, ak)
        else:
            raise ValueError("Invalid region_type")


# noinspection PyTypeChecker
def get_provider(track_type) -> TrackProvider:
    """
    根据轨迹类型获取轨迹提供器
    """
    if track_type == 'exp':
        return ExpTrackProvider()
    elif track_type == 'dest':
        return DestTrackProvider()
    elif track_type == 'dest-api':
        return DestApiTrackProvider()
    elif track_type == 'drive':
        return DriveTrackProvider()
    elif track_type == 'nav-drive':
        return NavigationDrivingTrackProvider()
    else:
        raise Exception(f'Unknown track type: {track_type}.')
