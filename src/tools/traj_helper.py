# !/usr/bin/env python3
"""
封装了众源轨迹 user_id 加解密逻辑
"""


def decrypt_user_id(encrypted_user_id):
    device_code_length = 6
    encrypted_prefix = 'trajplat_'

    encrypted_user_id_without_prefix = encrypted_user_id.replace(encrypted_prefix, '')
    index_of_first_underscore = encrypted_user_id_without_prefix.find('_')
    index_of_first_device_code_number = index_of_first_underscore - device_code_length
    decrypted_user_id_start = encrypted_user_id_without_prefix[index_of_first_device_code_number::]
    decrypted_user_id_end = encrypted_user_id_without_prefix[:index_of_first_device_code_number]
    return f"{decrypted_user_id_start}{decrypted_user_id_end}"
