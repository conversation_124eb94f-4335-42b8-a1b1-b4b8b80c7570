# !/usr/bin/env python3

from src.tools import pgsql


def get_trajectory_data_by_bid(bid):
    """
    返回bid所有的轨迹数据，格式为：
        x,y,time,link,velocity;x,y,time,link,velocity
    """
    with pgsql.get_connection(pgsql.TRAJECTORY_CONFIG) as conn:
        sql = '''
            select tra_info from traj_data where bid = %s 
        '''
        traj_data_list = []
        for tra_info_tmp, in pgsql.fetch_all(conn, sql, [bid]):
            tra_info_tmp.split('@', 1)[1]
            traj_data_list.append(tra_info_tmp)
        return traj_data_list