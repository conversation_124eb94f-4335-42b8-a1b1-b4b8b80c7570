# !/usr/bin/env python3
import csv
from pathlib import Path
from typing import Iterable
from typing import Union

# WORKAROUND: 我 Windows 上装的库可能是 32bit 的，用 sys.maxsize 总是报超过 C Long 上限的异常
MAX_C_LONG = 2**31 - 1
csv.field_size_limit(MAX_C_LONG)


def read_tsv(file_path: Union[Path, str], splitter="\t", skip_header=False):
    with open(file_path, "r", newline="", encoding="utf8") as csv_file:
        r = csv.reader(csv_file, delimiter=splitter)
        if skip_header:
            next(r)

        yield from r


def write_tsv(
    file_path: Union[Path, str], items: Iterable[Iterable], splitter="\t", mode="w"
):
    with open(file_path, mode, newline="", encoding="utf8") as csv_file:
        writer = csv.writer(csv_file, delimiter=splitter, lineterminator="\n")
        writer.writerows(items)
