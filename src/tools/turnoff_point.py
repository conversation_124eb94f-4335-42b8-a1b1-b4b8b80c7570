"""
熄火点查询，包括：博士轨迹和众源轨迹
博士轨迹熄火点来自导航。
- 导航原始结果：afs://fenghuang.afs.baidu.com:9902/user/xingtian-map-navi/zhaohaifeng02/parking_mining/boshi_stop/merge_cluster
- 导航代码库：https://console.cloud.baidu-int.com/devops/icode/repos/baidu/car-navi-mining/smart-transportation/blob/master/parking_trans/boshi_stop/cluster.py
- 母库文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/Q_tTpgJWrByCvN
"""
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Protocol, Literal

from shapely import Point, wkt

from src.tools import pgsql


class TurnoffPoint(Protocol):
    """
    熄火点
    """

    vehicle_id: str
    point: Point
    duration: timedelta


@dataclass(frozen=True)
class DoctorTurnoffPoint:
    """
    博士轨迹熄火点
    """

    vehicle_id: str
    point: Point
    duration: timedelta

    start: datetime
    end: datetime


@dataclass(frozen=True)
class OtherTurnoffPoint:
    """
    其它轨迹熄火点
    """

    vehicle_id: str
    point: Point
    duration: timedelta

    uuid: int
    parking_type: Literal["start_car", "mid_stop_car", "end_car"]


def get_points_of_doctor(polygon: str) -> list[DoctorTurnoffPoint]:
    """
    博士轨迹熄火点查询
    :param polygon: 查询范围
    :return: 轨迹点
    """

    def to_turnoff_point(text: str):
        cols = text.split(",")
        assert len(cols) == 5

        x, y, vehicle_id, start, end = cols
        start = datetime.fromtimestamp(float(start))
        end = datetime.fromtimestamp(float(end))
        return DoctorTurnoffPoint(
            vehicle_id=vehicle_id,
            point=Point(float(x), float(y)),
            start=start,
            end=end,
            duration=(end - start),
        )

    sql = """
        select unnest(points) from doctor_traj_turn_off_cluster
        where st_intersects(bounds, %s);
    """
    with pgsql.get_connection(pgsql.DEST_TRAJ) as conn:
        ret = pgsql.fetch_all(conn, sql, [f"SRID=4326;{polygon}"])

    points = [to_turnoff_point(x[0]) for x in ret]
    geom = wkt.loads(polygon)
    return [p for p in points if geom.intersects(p.point)]


def get_points_of_other(polygon: str, source: Literal["31"] = "31") -> list[OtherTurnoffPoint]:
    """
    其它轨迹熄火点查询
    :param polygon: 查询范围
    :param source: 轨迹源：众源="31"
    :return: 轨迹点
    """

    def to_turnoff_point(cols: tuple):
        assert len(cols) == 5

        uuid, user_id, parking_type, parking_time, geom = cols
        return OtherTurnoffPoint(
            vehicle_id=user_id,
            point=wkt.loads(geom),
            duration=timedelta(minutes=parking_time),
            uuid=uuid,
            parking_type=parking_type,
        )

    sql = """
        select id, user_id, parking_type, parking_time, st_astext(geom) from parking_points
        where traj_src = %s and st_contains(%s, geom);
    """
    with pgsql.get_connection(pgsql.TRAJ_FEATURE) as conn:
        ret = pgsql.fetch_all(conn, sql, [source, f"SRID=4326;{polygon}"])

    return [to_turnoff_point(x) for x in ret]
