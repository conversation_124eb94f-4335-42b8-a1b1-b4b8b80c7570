# !/usr/bin/env python3
"""
封装了一体化平台推送相关的帮助方法
"""

from dataclasses import dataclass, asdict, field
from typing import Any, Iterable

import requests
from retrying import retry
from tqdm import tqdm

API = "https://mapde-poi.baidu-int.com/prod/integration"
POST_INTELLIGENCE = 'qbSyncV2'
ENCLOSE_INTELLIGENCE = 'qbStatusSyncV2'
INTELLIGENCE_TYPE_TICKET = 1  # 作业工单类型情报
INTELLIGENCE_TYPE_STRATEGY = 2  # 策略挖掘类型情报
STATUS_PENDING = 0  # 任务待创建：情报生成的初始状态
WORK_TYPE_UNKNOWN = 0  # 未知作业类型


@dataclass
class Intelligence:
    """
    情报数据
    """
    """业务线的情报id，业务线唯一"""
    ref_qb_id: str
    """情报数据的来源，比如: 1.0 闭环清单,关联关系作业产出等"""
    from_src: str
    """主点poi的bid，必填"""
    main_poi_bid: str
    """情报需要作业的数据类型，未知: 0, 大门关联: 2, 停车场出入口关联: 3, AOI边框: 4, 大门: 5"""
    src: int
    """情报类型，0未知 1常规 2挖掘 默认0"""
    qb_type: int = INTELLIGENCE_TYPE_STRATEGY
    """业务线的情报批次id，没有，可不填"""
    ref_qb_batch_id: str = ''
    """需要创建人工作业计划的类型，不是策略名称！！！"""
    strategy_type: str = ''
    """额外信息，注意，如果from_src是1.0闭环清单需要填入flow信息"""
    extra: dict[str, Any] = field(default_factory=dict)
    memo: str = ''


@dataclass
class IntelligenceStatus:
    """
    情报状态
    """
    ref_qb_id: str
    status: int


@dataclass
class Ticket:
    """
    作业工单
    """
    """用户传入的唯一 id 追踪 case 或去重使用"""
    ref_qb_id: str

    """说明情报的处理方式以及需要处理的业务线"""
    src: int
    work_factors: str
    work_type: int = WORK_TYPE_UNKNOWN
    """情报类型，0未知 1常规 2挖掘 默认0"""
    qb_type: int = INTELLIGENCE_TYPE_TICKET
    status: int = STATUS_PENDING
    """需要创建人工作业计划的类型，不是策略名称！！！"""
    strategy_type: str = ''

    """唯一定位情报关键信息的参数，如果都相等可能是情报重复或者元素发生多次变化"""
    main_poi_bid: str = ''
    ref_id: str = ''
    action: str = ''
    mark_geom: str = ''
    extra: dict[str, Any] = field(default_factory=dict)

    # 附加信息，如标记是否同一批创建计划等；
    ref_qb_batch_id: str = ''
    ref_qb_batch_name: str = ''
    memo: str = ''
    ref_factors: str = ''
    priority: int = 0
    nav_risk: int = 0


@dataclass
class FailMessage:
    data: object
    message: str


@retry(stop_max_attempt_number=8, wait_random_min=1000, wait_random_max=5000)
def __post_data(url, data):
    """
    推送数据
    """
    return requests.post(url, json=asdict(data))


def post_data(url, data: Iterable[object]):
    """
    推送数据
    """
    fail_data = []

    for item in tqdm(data):
        response = __post_data(url, item)
        if response.status_code != 200:
            fail_data.append(FailMessage(
                data=item,
                message=response.text,
            ))
            print(f'{response.status_code}: {response.text}')

    return fail_data


def post_intelligences(data: Iterable[Intelligence]):
    """
    推送情报
    """
    return post_data(f'{API}/{POST_INTELLIGENCE}', data)


def enclose_intelligences(data: Iterable[IntelligenceStatus]):
    """
    闭环情报
    """
    return post_data(f'{API}/{ENCLOSE_INTELLIGENCE}', data)


def post_tickets(data: Iterable[Ticket]):
    """
    推送作业工单
    """
    return post_data(f'{API}/{POST_INTELLIGENCE}', data)
