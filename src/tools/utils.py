# !/usr/bin/env python3
import csv
import hashlib
import json
import math
import os
import shutil
from pathlib import Path
from typing import Any, Generator, TypeVar, Union
from typing import Callable
from typing import Dict
from typing import Iterable
from typing import List
from typing import Optional
from typing import Tuple

import numpy as np
from shapely import wkt as shapely_wkt, wkt
from shapely.geometry.base import BaseGeometry

T = TypeVar("T")


def read_json(json_path):
    """
    读取指定的 Json 文件，并输出 Json 对象。
    """
    with open(str(json_path), "r", encoding="utf-8") as f:
        return json.loads(f.read())


def write_json(json_path, obj: any, indent=2):
    text = json.dumps(obj, ensure_ascii=False, indent=indent)
    Path(json_path).write_text(text, encoding="utf8")


def read_aoi_categories_json(json_path: str) -> Dict[str, int]:
    """
    从指定的文件中读取类别映射字典。
    :param json_path: 指定的配置文件。
    :return: POI 类别 --> 通道编号
    """
    category_list = read_json(json_path)
    return dict([(x["tag"], int(x["channel"])) for x in category_list])


def read_aoi_categories_txt(file_path):
    """
    从指定的文件中读取类别映射字典。
    :param file_path: 指定的配置文件。
    :return: POI 类别 --> 通道编号
    """
    with open(file_path, "r", encoding="utf-8") as f:
        csv_file = csv.reader(f)
        result: Dict[str, int] = {}
        for i, tag in enumerate(x[0] for x in csv_file):
            # + 2 表示跳过背景类别（0）和边框类别（1）
            result[tag] = i + 2

        return result


def read_aoi_pseudo_color_file(file_path):
    with open(file_path, "r") as f:
        lines = [x.replace("\n", "") for x in f.readlines()]
        lines = [int(line) for line in lines]
        return lines


def get_transform_info(point1, point2):
    """
    根据给定的配准信息，获取坐标转换的信息。
    :param point1: 第一个配准信息对，形如：((from_x, from_y), (to_x, to_y))
    :param point2: 第二个配准信息对，形如：((from_x, from_y), (to_x, to_y))
    :return: 符合 gdal API 标准的配准信息
    """

    # X_geo = GT(0) + X_pixel * GT(1) + Y_line * GT(2)
    # Y_geo = GT(3) + X_pixel * GT(4) + Y_line * GT(5)

    # In case of north up images:
    # GT(2), GT(4) coefficients are zero.
    # GT(1), GT(5) is the pixel size.
    # GT(0), GT(3) position is the top left corner of the top left pixel of the raster.

    ((x1, y1), (x_geo1, y_geo1)) = point1
    ((x2, y2), (x_geo2, y_geo2)) = point2

    gt2 = 0
    gt4 = 0
    gt1 = (x_geo1 - x_geo2) / (x1 - x2)
    gt0 = x_geo1 - gt1 * x1
    gt5 = (y_geo1 - y_geo2) / (y1 - y2)
    gt3 = y_geo1 - gt5 * y1

    return gt0, gt1, gt2, gt3, gt4, gt5


def detect_bbox_to_polygon(img_geom: str, bbox: List[float], img_width: float, img_height: float) -> str:
    """
    将识别的bbox区域转geom
    :param img_geom 图片对应的geom区域，形式：POLYGON((xx yy, xx2 yy2))
    :param bbox 识别结果的数组, 一般是四个参数 [x, y, w, h]
    :param img_width 图片宽
    :param img_height 图片高
    """
    geom = shapely_wkt.loads(img_geom)
    # 计算区域
    min_x, min_y, max_x, max_y = geom.bounds

    # 左上角映射为 0， 0 右下角映射为 x, y
    transform = get_transform_info(((0, 0), (min_x, max_y)), ((img_width, img_height), (max_x, min_y)))
    if len(bbox) < 4:
        print("无效的bbox")
        return ""
    # 计算左上角和右下角的点
    point1 = (bbox[0], bbox[1])
    point2 = (bbox[0] + bbox[2], bbox[1] + bbox[3])
    t1 = convert_coordinate(point1, transform)
    t2 = convert_coordinate(point2, transform)
    # 生成一个对应区域的polygon
    return "POLYGON(({} {},{} {}, {} {}, {} {}, {} {}))".format(
        t1[0], t1[1], t2[0], t1[1], t2[0], t2[1], t1[0], t2[1], t1[0], t1[1]
    )


def convert_coordinate(point: Tuple[float, float], transform_info):
    """
    根据指定的转换信息转换给定点的坐标。
    :param point: 给定的点坐标
    :param transform_info: 指定的转换信息
    :return: 转换后的点坐标
    """
    gt0, gt1, gt2, gt3, gt4, gt5 = transform_info
    x, y = point
    x_geo = gt0 + gt1 * x + gt2 * y
    y_geo = gt3 + gt4 * x + gt5 * y

    return x_geo, y_geo


def get_transform_info_from_json(image_info, mode: str = "geo2pixel"):
    """
    从给定的图片信息 Json 对象中获取坐标转换信息。

    图片信息的规格见文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/zMkVncP_sy/lvq-KcWANP/K6WO9p4rte59yT
    :param image_info: 给定的图片信息 Json 对象
    :param mode: 指定所获取的转换信息的方向，默认：经纬度 --> 像素坐标。
    :return: 符合 gdal API 标准的配准信息
    """
    width = image_info["width"]
    height = image_info["height"]
    region = image_info["region"]
    left = region["left"]
    top = region["top"]
    right = region["right"]
    bottom = region["bottom"]

    if mode == "geo2pixel":
        return get_transform_info(((left, top), (0, 0)), ((right, bottom), (width, height)))
    else:
        return get_transform_info(((0, 0), (left, top)), ((width, height), (right, bottom)))


def convert_to_int_point(point: Tuple[float, float]):
    """
    转换 float 类别的点为 int 类型的点。
    """
    x, y = point
    return [round(x), round(y)]


def get_vector(a: List[int], b: List[int]):
    """
    计算给定的两点所表示的向量： a --> b
    """
    return [b[0] - a[0], b[1] - a[1]]


def normalize(a: List[int]):
    """
    归一化指定的向量
    """
    x, y = a[0], a[1]
    length = math.sqrt(x * x + y * y)
    return [x / length, y / length]


def convert_points(points, transform_info):
    """
    将来自 gdal 的点集转换为 cv2 使用的格式，并转换其坐标。
    [(x1, y1), (x2, y2), ...] --> [[[x1, y1,]], [[x2, y2]], ...]
    """
    points = [convert_coordinate(x, transform_info) for x in points]
    points = [convert_to_int_point(p) for p in points]
    points = np.array(points).reshape((-1, 1, 2))
    return points


def get_polygon_points(geom, transform_info):
    """
    获取 POLYGON WKT 中的点集。
    """
    points = [*geom][0].GetPoints()
    points = convert_points(points, transform_info)
    return points


def get_linestring_points(geom, transform_info):
    """
    获取 LINESTRING WKT 中的点集。
    """
    points = geom.GetPoints()
    points = convert_points(points, transform_info)
    return points


def get_point(point_wkt: str, transform_info):
    """
    获取 POINT WKT 中的点。
    """
    if not point_wkt:
        return [0, 0]

    geom = wkt.loads(point_wkt)
    point = convert_coordinate((geom.x, geom.y), transform_info)
    return convert_to_int_point(point)


def ensure_dir(dir_path: Union[Path, str], cleanup: bool = False):
    """
    确保一个本地目录存在，若不存在则创建。
    """
    if cleanup and dir_path.exists():
        shutil.rmtree(dir_path, ignore_errors=True)

    dir_path = Path(dir_path) if isinstance(dir_path, str) else dir_path
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path


def ensure_path(file_path: Union[Path, str], cleanup: bool = True):
    """
    对于传入的文件，确保其父目录存在
    :param file_path: 文件路径
    :param cleanup: 是否清理之前的文件，如果存在
    :return: 传入的路径，原样输出
    """
    file_path = Path(file_path) if isinstance(file_path, str) else file_path
    file_path.parent.mkdir(parents=True, exist_ok=True)
    if cleanup:
        file_path.unlink(missing_ok=True)

    return file_path


def md5(text: str):
    """
    计算字符串的 MD5
    """
    return hashlib.md5(text.encode("utf-8")).hexdigest()


def print_table(
    rows: Iterable[Iterable],
    headers: List[str],
    formatters: Optional[Dict[str, Optional[Callable[[Any], str]]]] = None,
):
    def get_align(item):
        t = type(item)
        # 数字右对齐，其它居中对齐。
        return ">" if t == int or t == float else "^"

    def format_row(items):
        return " | ".join(f"{item:{align}{width}}" for item, align, width in items)

    formatters = formatters if formatters else {}
    formatted_rows = [
        [formatters[header](cell) if header in formatters else str(cell) for header, cell in zip(headers, row)]
        for row in rows
    ]
    widths = [max([len(header), *(len(row[i]) for row in formatted_rows)]) for i, header in enumerate(headers)]
    headers_text = format_row((header, "^", width) for header, width in zip(headers, widths))
    split_text = " | ".join("-".join("" for _ in range(x + 1)) for x in widths)
    row_texts = [
        format_row((f_cell, get_align(cell), width) for f_cell, cell, width in zip(f_row, row, widths))
        for f_row, row in zip(formatted_rows, rows)
    ]

    for row_text in (headers_text, split_text, *row_texts):
        print(f"| {row_text} |")


def calc_iou(geom1, geom2):
    # noinspection PyBroadException
    try:
        geom1 = _to_shapely_geom(geom1)
        geom2 = _to_shapely_geom(geom2)

        a = geom1.intersection(geom2)
        b = geom1.union(geom2)

        a = a.area
        b = b.area
        return a / b if b > 0 else 0.0
    except:
        return 0.0


def calc_ioa(geom1, geom2):
    """
    计算压盖面积比（intersection over area）：即对于 geom2 而言，其被 geom1 所压盖的面积占自身面积的比例。
    """
    # noinspection PyBroadException
    try:
        geom1 = _to_shapely_geom(geom1)
        geom2 = _to_shapely_geom(geom2)

        a = geom1.intersection(geom2)

        a = a.area
        b = geom2.area
        return a / b if b > 0 else 0.0
    except:
        return 0.0


def _to_shapely_geom(geom) -> BaseGeometry:
    """
    传入的类型可能有：str, BaseGeometry, gdal.ogr.Geometry，统一转成 BaseGeometry。
    """
    return geom if isinstance(geom, BaseGeometry) else wkt.loads(str(geom))


def for_batch(items: Iterable[T], batch_size: int) -> Generator[list[T], any, None]:
    """
    按照指定批次大小分割可迭代对象：items=(10_000_000), batch_size=100 -> ([100], [100], [100], ...)
    NOTE: 最后一个批次可能不足 batch_size，但不会丢弃。
    """
    batch = []
    for item in items:
        batch.append(item)
        if len(batch) >= batch_size:
            yield batch
            batch = []

    if batch:
        yield batch


def get_newest_subdir_name(root: str) -> str:
    """
    获取 root 下最新的子目录名称
    这里最新是根据子目录名称比较大小；也就是字符串比较大小，越大的认为越新
    Args:
        root: 根目录
    Returns: 子目录名称
    """
    if not os.path.exists(root):
        return ""

    def get_subdirectory_names(directory: str) -> list:
        return [str(d).lower() for d in os.listdir(directory) if os.path.isdir(os.path.join(directory, d))]

    names = get_subdirectory_names(root)
    if len(names) == 0:
        return ""

    newest = names[0]
    for _name in names:
        if _name > newest:
            newest = _name
    return newest
