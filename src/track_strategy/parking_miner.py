"""
停车场新增情报挖掘
"""
import json
import sys
from dataclasses import dataclass, field
from datetime import datetime
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path
from typing import Union

import cv2
import numpy as np
from shapely import Polygon, Point, LineString, wkt, MultiPoint
from tqdm import tqdm

from src.aikit import boundary, satellite_imagery, preview_image
from src.parking.recognition import dbutils
from src.parking.storefront import cluster_projection
from src.parking.storefront.utils import geometric
from src.parking.storefront.utils.geometric import METER
from src.tools import pipeline, pgsql, utils, tsv, linq

# VERSION = "2.2.0"  # 初始化提交
VERSION = "2.3.0"  # 关联的 DE 大门不得跨路，不得远离 AOI 30m 以上

desc = pipeline.get_desc()


@dataclass
class Gate:
    """
    门信息：可表示DE大门/AOI出入口
    """

    node_id: str
    point: Point
    link1: LineString
    link2: LineString

    def __hash__(self):
        return hash(self.node_id)

    def is_nearby_and_parallel(self, other: LineString, buffer: float):
        """
        判断一条线是否与该大门邻近且平行
        """
        return is_nearby_and_parallel(self, other, buffer)

    def to_dict(self):
        """
        转换为字典
        """
        return {"node_id": self.node_id, "point": self.point.wkt, "link1": self.link1.wkt, "link2": self.link2.wkt}


@dataclass
class TrajInfo:
    """
    轨迹信息
    """

    tid: int
    point: Point
    geom: LineString
    is_start: bool
    is_depth: bool = field(default=False)
    blu_accesses: list[Gate] = field(default_factory=list)
    nav_gate: Gate = field(default=None)
    end_speeds: list[float] = field(default_factory=list)

    def to_dict(self):
        """
        转换为字典
        """
        x = self
        return {
            "tid": x.tid,
            "point": x.point.wkt,
            "geom": x.geom.wkt,
            "is_start": x.is_start,
            "is_depth": x.is_depth,
            "blu_accesses": [b.to_dict() for b in x.blu_accesses],
            "nav_gate": x.nav_gate.to_dict() if x.nav_gate else None,
            "end_speeds": x.end_speeds,
        }


@dataclass
class Case:
    """
    样本信息，代表一个基础院落
    """

    face_id: str
    bid: str
    city: str
    std_tag: str
    name: str
    geom: Polygon
    stop_infos: list[TrajInfo]


@dataclass
class NewInfo:
    """
    新增情报
    """

    category: str
    point: Union[Point, MultiPoint]
    stop_infos: list[TrajInfo]
    show_tag: str


@dataclass
class Context:
    """
    上下文
    """

    case: Case
    blu_accesses: list[Gate] = field(default_factory=list)
    new_infos: list[NewInfo] = field(default_factory=list)
    unrelated_nav_gates: dict[Gate, int] = field(default_factory=dict)
    error: str = field(default="")


@desc()
def add_traj_tag_depth(ctx: Context, proceed, min_depth: float, min_length: float):
    """
    pipe：添加轨迹是否深入标签
    """
    blu_line = LineString(ctx.case.geom.exterior.coords)
    for x in ctx.case.stop_infos:
        is_depth = blu_line.distance(x.point) > min_depth or ctx.case.geom.intersection(x.geom).length >= min_length
        x.is_depth = is_depth

    proceed()


@desc()
def add_traj_tag_nav_gate(ctx: Context, proceed, buffer: float, max_dist: float):
    """
    pipe：添加轨迹是否穿过DE大门标签
    """
    sql_link = """
        select link_id from nav_link
        where kind <= 7 and st_intersects(geom, %s)
    """

    def is_cross_link(line: LineString):
        link = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql_link, [f"SRID=4326;{line.wkt}"])
        return link is not None

    blu_line = ctx.case.geom.exterior.simplify(0.1 * METER)
    for t in ctx.case.stop_infos:
        gates = get_nav_gate(t.geom.wkt, buffer)
        gate_dists = [(x, t.geom.project(x.point)) for x in gates if x.is_nearby_and_parallel(t.geom, buffer)]
        if not gate_dists:
            continue

        gate, _ = min(gate_dists, key=lambda x: x[1]) if t.is_start else max(gate_dists, key=lambda x: x[1])
        if gate.point.distance(ctx.case.geom) > max_dist:
            continue

        blu_point = blu_line.interpolate(blu_line.project(gate.point))
        connect_line = LineString([blu_point, gate.point])
        if is_cross_link(connect_line):
            continue

        t.nav_gate = gate

    proceed()


@desc()
def add_traj_tag_access_gate(ctx: Context, proceed, buffer: float):
    """
    pipe：添加轨迹是否穿过AOI出入口标签
    """
    blu_accesses = get_blu_access(ctx.case.bid)
    ctx.blu_accesses = blu_accesses
    for x in ctx.case.stop_infos:
        gates = [
            gate
            for gate in blu_accesses
            if gate.point.dwithin(x.geom, buffer) and gate.is_nearby_and_parallel(x.geom, buffer)
        ]
        x.blu_accesses = gates

    proceed()


@desc()
def analyze_high_quality_new_infos(ctx: Context, proceed, eps: float):
    """
    pipe：分析&产出高准新增情报
    """
    # 高质量情报
    high_stops = [x for x in ctx.case.stop_infos if x.is_depth and x.blu_accesses]
    if high_stops:
        clusters = cluster_projection.cluster_by_dbscan(high_stops, eps, 1, key=lambda x: x.point)
        if not clusters:
            return proceed()

        new_infos = [
            NewInfo(
                category="depth_and_blu_access",
                point=calc_avg_point([p.point for p in c]),
                stop_infos=c,
                show_tag="地上停车场",
            )
            for c in clusters
        ]
        ctx.new_infos.extend(new_infos)

    proceed()


@desc()
def analyze_low_quality_new_infos(ctx: Context, proceed, eps: float):
    """
    pipe：分析&产出低准新增情报
    """
    # 同一个 AOI，已有情报，就生成低质了，低质情报属于捡漏的
    if ctx.new_infos:
        return proceed()

    # 低质量情报
    low_stops = [x for x in ctx.case.stop_infos if x.is_depth or x.nav_gate or x.blu_accesses]
    if low_stops:
        clusters = cluster_projection.cluster_by_dbscan(low_stops, eps, 1, key=lambda x: x.point)
        if not clusters:
            return proceed()

        new_infos = [
            NewInfo(
                category="any_depth_nav_gate_blu_access",
                point=calc_avg_point([p.point for p in c]),
                stop_infos=c,
                show_tag="地上停车场",
            )
            for c in clusters
        ]
        ctx.new_infos.extend(new_infos)

    proceed()


@desc()
def analyze_underground_tag(ctx: Context, proceed, min_speed: float, ratio: float, n_end: int):
    """
    pipe：分析是否是地下停车场
    """
    for new_info in ctx.new_infos:
        end_infos = [x for x in new_info.stop_infos if not x.is_start]
        if not end_infos:
            continue

        tids = [x.tid for x in end_infos]
        speed_dict = get_end_speeds(tids)
        # 记录末端速度
        for end_info in end_infos:
            end_info.end_speeds = speed_dict.get(end_info.tid, [])

        # 方案1：计算末端速度的平均值
        speeds = [sum(x[n_end:]) / n_end for x in speed_dict.values()]
        num_has_speed = sum(1 for x in speeds if x > min_speed)
        # 方案2：不使用平均值，直接判定单点速度
        # speeds = list(speed_dict.values())
        # num_has_speed = sum(1 for x in speeds if not any(1 for v in x[-n_end:] if v < min_speed))
        has_speed_ratio = num_has_speed / len(speeds)
        if has_speed_ratio >= ratio:
            new_info.show_tag = "地下停车场"

    proceed()


@desc()
def merge_new_infos(ctx: Context, proceed):
    """
    pipe：合并相同空间属性的新增情报
    """
    if not ctx.new_infos:
        return proceed()

    ctx.new_infos = _merge_new_infos(ctx.new_infos)
    proceed()


@desc()
def analyze_gate_infos(ctx: Context, proceed):
    """
    pipe：分析AOI缺失DE大门关联情报
    """
    blu_access_nids = {x.node_id for x in ctx.blu_accesses}
    for x in ctx.case.stop_infos:
        nav_gate = x.nav_gate
        if not nav_gate:
            continue

        if nav_gate.node_id not in blu_access_nids:
            n = ctx.unrelated_nav_gates.get(nav_gate, 0)
            ctx.unrelated_nav_gates[nav_gate] = n + 1

    proceed()


@desc()
def draw_preview(ctx: Context, proceed, save_dir: Path, max_area: float):
    """
    pipe：绘制 debug 预览图
    """
    if ctx.case.geom.area > max_area:
        return proceed()

    if len(ctx.case.stop_infos) <= 0:
        return proceed()

    bounds = boundary.from_wkt(ctx.case.geom.wkt, buffer=30 * METER)
    image = satellite_imagery.crop(bounds)
    if image is None:
        return proceed()

    # 绘制 AOI 边框
    preview_image.draw_polygon(image, ctx.case.geom.wkt, bounds, thickness=4, color=preview_image.COLOR_YELLOW)

    # 绘制轨迹
    pass_infos = [x for x in ctx.case.stop_infos if x.is_depth and x.blu_accesses]
    pass_tids = {x.tid for x in pass_infos}
    no_pass_infos = [x for x in ctx.case.stop_infos if x.tid not in pass_tids]
    _draw_traj_info(image, no_pass_infos, bounds, color=(60, 60, 60))
    start_infos = [x for x in pass_infos if x.is_start]
    _draw_traj_info(image, start_infos, bounds, preview_image.COLOR_GREEN)
    end_infos = [x for x in pass_infos if not x.is_start]
    _draw_traj_info(image, end_infos, bounds, preview_image.COLOR_RED)

    new_pts = [p.wkt for x in ctx.new_infos for p in geometric.flat_point(x.point)]
    if new_pts:
        preview_image.draw_point(image, new_pts, bounds, radius=12, color=preview_image.COLOR_PURPLE)

    image_id = ctx.case.bid if ctx.case.bid else ctx.case.face_id
    save_path = utils.ensure_path(save_dir / f"{image_id}.jpg")
    cv2.imwrite(str(save_path), image)
    proceed()


# helpers:


def _merge_new_infos(infos: list[NewInfo]):
    grouped_new_infos = linq.group_by(infos, key=lambda x: x.show_tag)
    merged_new_infos = []
    for tag, new_infos in grouped_new_infos.items():
        if len(new_infos) == 1:
            merged_new_infos.append(new_infos[0])
            continue

        pts = [x.point for x in new_infos]
        merged_pt = MultiPoint(pts)
        stop_infos = [y for x in new_infos for y in x.stop_infos]
        category = ".".join(sorted({x.category for x in new_infos}))
        merged_info = NewInfo(category=category, point=merged_pt, stop_infos=stop_infos, show_tag=tag)
        merged_new_infos.append(merged_info)

    return merged_new_infos


def _draw_traj_info(image, infos: list[TrajInfo], bounds, color):
    if not infos:
        return

    pts = [x.point.wkt for x in infos]
    preview_image.draw_point(image, pts, bounds, radius=6, color=color)
    lines = [x.geom.wkt for x in infos]
    preview_image.draw_linestring(image, lines, bounds, thickness=2, color=color)

    gates = [x.nav_gate.link1.wkt for x in infos if x.nav_gate] + [x.nav_gate.link2.wkt for x in infos if x.nav_gate]
    if gates:
        preview_image.draw_linestring(image, gates, bounds, thickness=2, color=preview_image.COLOR_CYAN)
    gates = [x.nav_gate.point.wkt for x in infos if x.nav_gate]
    if gates:
        preview_image.draw_point(image, gates, bounds, radius=8, color=preview_image.COLOR_CYAN)

    gates = [b.link1.wkt for x in infos for b in x.blu_accesses] + [b.link2.wkt for x in infos for b in x.blu_accesses]
    if gates:
        preview_image.draw_linestring(image, gates, bounds, thickness=2, color=preview_image.COLOR_BLUE)
    gates = [b.point.wkt for x in infos for b in x.blu_accesses]
    if gates:
        preview_image.draw_point(image, gates, bounds, radius=4, color=preview_image.COLOR_BLUE)


def calc_avg_point(pts: list[Point]) -> Point:
    """
    计算平均点位
    """
    x = sum(p.x for p in pts) / len(pts)
    y = sum(p.y for p in pts) / len(pts)
    return Point(x, y)


def is_nearby_and_parallel(gate: Gate, line: LineString, buffer, tolerance=0.5):
    """
    判断指定 line 是否邻近且平行穿过大门
    """
    gate_direction, v_line = _get_gate_vertical_line(gate, buffer)
    pts = v_line.intersection(line)
    if not isinstance(pts, (Point, MultiPoint)):
        return False

    pt = min(geometric.flat_point(pts), key=lambda x: x.distance(gate.point))
    dist = line.project(pt)
    p1 = line.interpolate(dist + 5 * METER)
    p2 = line.interpolate(dist - 5 * METER)
    line_direction = np.array([p1.x - p2.x, p1.y - p2.y])
    line_direction /= np.linalg.norm(line_direction)

    cos_value = np.dot(gate_direction, line_direction)
    return abs(cos_value) < tolerance


def _get_gate_vertical_line(gate: Gate, buffer: float):
    def get_pt(ln: LineString, p: Point):
        d0 = ln.project(p)
        if abs(d0) < 1e-8:
            d1 = d0 + 5 * METER
        else:
            d1 = d0 - 5 * METER

        pt = ln.interpolate(d1)
        return np.array([pt.x, pt.y])

    p0 = np.array([gate.point.x, gate.point.y])
    p1 = get_pt(gate.link1, gate.point)
    p2 = get_pt(gate.link2, gate.point)

    vec1 = p1 - p0
    vec1 /= np.linalg.norm(vec1)
    vec2 = p2 - p0
    vec2 /= np.linalg.norm(vec2)

    vec = vec1 + vec2
    vec_len = np.linalg.norm(vec)
    if vec_len > 0:
        vec /= vec_len
    else:
        vec = np.array([-vec1[1], vec1[0]])
        vec /= np.linalg.norm(vec)

    v_p1 = p0 + vec * buffer
    v_p2 = p0 - vec * buffer
    return vec, LineString([v_p1, v_p2])


def get_end_speeds(tids: list[int]) -> dict[int, list[float]]:
    """
    获取末端速度
    """
    sql = """
        select id, speed_str from byd_all_traj
        where id in %s
    """
    ret = dbutils.fetch_all(pgsql.TRAJ_DB2, sql, [tuple(tids)])
    speed = {tid: [float(x) for x in speed_str.split(",")] for tid, speed_str in ret}
    return speed


def get_nav_gate(query_geom: str, buffer: float) -> list[Gate]:
    """
    获取DE大门
    """
    # 不要用 join，这两张表不知道为什么，`on a.node_id = b.node_id` 不走索引，非要 Seq Scan，慢的要死
    sql_node = """
        select node_id, st_astext(geom) from nav_node
        where st_dwithin(geom, %s, %s)
    """
    sql_gate = """
        select node_id from nav_gate
        where node_id in %s
    """
    nodes = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql_node, [f"SRID=4326;{query_geom}", buffer])
    node_ids = tuple(x[0] for x in nodes)
    if not node_ids:
        return []

    gates_nids = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql_gate, [node_ids])
    gate_nids = {x[0] for x in gates_nids}
    gates = [(nid, get_nav_link(nid), wkt.loads(geom)) for nid, geom in nodes if nid in gate_nids]
    gates = [(nid, links, point) for nid, links, point in gates if links]
    return [
        Gate(node_id=nid, point=point, link1=wkt.loads(link1), link2=wkt.loads(link2))
        for nid, (link1, link2), point in gates
    ]


def get_blu_access(blu_bid: str) -> list[Gate]:
    """
    获取AOI出入口
    """
    sql = """
        select distinct b.node_id
        from blu_access a
        inner join blu_access_gate_rel b on a.access_id = b.access_id
        where a.main_bid = %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [blu_bid])
    nids = [x[0] for x in ret]
    gates = [(nid, get_nav_link(nid), get_nav_node(nid)) for nid in nids]
    gates = [(nid, links, point) for nid, links, point in gates if links and point]
    return [
        Gate(node_id=nid, point=wkt.loads(point), link1=wkt.loads(link1), link2=wkt.loads(link2))
        for nid, (link1, link2), point in gates
    ]


def get_nav_link(node_id: str):
    """
    获取道路线
    """
    sql = """
        select st_astext(geom) from nav_link
        where s_nid = %s or e_nid = %s
    """
    ret = dbutils.fetch_all(pgsql.ROAD_CONFIG, sql, [node_id, node_id])
    if len(ret) != 2:
        return None

    links = [x[0] for x in ret]
    link1, link2 = links
    return link1, link2


def get_nav_node(node_id: str):
    """
    获取道路节点
    """
    sql = """
        select st_astext(geom) from nav_node
        where node_id = %s
    """
    ret = dbutils.fetch_one(pgsql.ROAD_CONFIG, sql, [node_id])
    if ret:
        return ret[0]

    return None


def get_case():
    """
    获取待处理的 AOI case
    """
    sql = """
        select 
            face_id, bid, city, std_tag, name, st_astext(geom),
            start_tids, st_astext(start_points), st_astext(start_lines),
            end_tids, st_astext(end_points), st_astext(end_lines)
        from byd_all_traj_substring
        where 1 = 1
            and cardinality(start_tids) + cardinality(end_tids) > 0
            and cardinality(related_parking_bids) + cardinality(contains_parking_bids) = 0
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql)
    return _build_cases(ret)


def get_case_by_bid(bids: list[str]):
    """
    获取待处理的 AOI case，根据指定的 bids
    """
    sql = """
        select 
            face_id, bid, city, std_tag, name, st_astext(geom),
            start_tids, st_astext(start_points), st_astext(start_lines),
            end_tids, st_astext(end_points), st_astext(end_lines)
        from byd_all_traj_substring
        where bid in %s
    """
    ret = dbutils.fetch_all(pgsql.POI_SLAVER_CONFIG, sql, [tuple(bids)])
    return _build_cases(ret)


def _build_cases(rows: list):
    return [
        Case(
            face_id=face_id,
            bid=bid,
            city=city,
            std_tag=std_tag,
            name=name,
            geom=wkt.loads(geom),
            stop_infos=[
                TrajInfo(uid, point, line, is_start=True)
                for uid, point, line in zip(
                    s_tids,
                    geometric.flat_point(s_points) if s_points else [],
                    geometric.flat_line(s_lines) if s_lines else [],
                )
            ]
            + [
                TrajInfo(uid, point, line, is_start=False)
                for uid, point, line in zip(
                    e_tids,
                    geometric.flat_point(e_points) if e_points else [],
                    geometric.flat_line(e_lines) if e_lines else [],
                )
            ],
        )
        for face_id, bid, city, std_tag, name, geom, s_tids, s_points, s_lines, e_tids, e_points, e_lines in rows
    ]


def save_to_db(file_path: Path):
    """
    保存本地持久化文件到数据库
    """
    sql_copy = (
        f"COPY park_discovery_info "
        f"(face_id, bid, city, std_tag, name, existed_show_tag, category, show_tag, info, geom, strategy, batch)"
        r"FROM STDIN WITH (FORMAT text, DELIMITER E'\t', NULL '\N')"
    )
    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as curs,
        open(file_path, "r", encoding="utf-8") as f,
    ):
        # noinspection PyTypeChecker
        curs.copy_expert(sql_copy, f)
        conn.commit()


def _build_json_info(stops: list[TrajInfo]):
    json_infos = [x.to_dict() for x in stops]
    s = json.dumps({"stop_infos": json_infos})
    return s


def main(file_path: Path):
    """
    主函数
    """
    today = datetime.now().strftime("%Y%m%d")
    exists_show_tags = {}
    if file_path and file_path.exists():
        bids = [x[0] for x in tsv.read_tsv(file_path)]
        exists_show_tags = {x[0]: x[1] for x in tsv.read_tsv(file_path) if len(x) > 1}
        cases = get_case_by_bid(bids)
        batch = f"{file_path.stem}_{VERSION}_{today}"
    else:
        cases = get_case()
        batch = f"blu_face_no_parking_{VERSION}_{today}"

    output_dir = utils.ensure_dir("output")
    work_dir = output_dir / batch

    new_path = utils.ensure_path(work_dir / "result.new.tsv")
    nothing_path = utils.ensure_path(work_dir / "result.nothing.tsv")
    gate_path = utils.ensure_path(work_dir / "result.gate.tsv")

    pipe = pipeline.Pipeline(
        partial(add_traj_tag_depth, min_depth=20 * METER, min_length=50 * METER),
        partial(add_traj_tag_nav_gate, buffer=10 * METER, max_dist=30 * METER),
        partial(add_traj_tag_access_gate, buffer=10 * METER),
        partial(analyze_high_quality_new_infos, eps=30 * METER),
        partial(analyze_low_quality_new_infos, eps=30 * METER),
        partial(analyze_underground_tag, min_speed=1, ratio=0.5, n_end=3),
        merge_new_infos,
        analyze_gate_infos,
        # partial(draw_preview, max_area=5000**2 * METER**2, save_dir=work_dir / "preview"),
    )
    desc.attach(pipe)
    desc.disabled = True

    contexts = (Context(c) for c in cases)
    with Pool(32) as p, open(new_path, "w", encoding="utf-8") as f_db:
        for ctx in tqdm(p.imap_unordered(pipe, contexts), total=len(cases)):
            c = ctx.case
            header = (c.face_id, c.bid, c.city, c.std_tag, c.name, exists_show_tags.get(c.bid, "unknown"))
            if ctx.new_infos:
                rows = [
                    (
                        *header,
                        x.category,
                        x.show_tag,
                        _build_json_info(x.stop_infos),
                        x.point.wkt,
                        f"byd_all_traj-{VERSION}",
                        batch,
                    )
                    for x in ctx.new_infos
                ]
                # 不能用 tsv，会被转义
                for row in rows:
                    f_db.write("\t".join(row) + "\n")
            else:
                row = (*header, len(ctx.case.stop_infos))
                tsv.write_tsv(nothing_path, [row], mode="a")

            if ctx.unrelated_nav_gates:
                rows = [(*header, gate.node_id, n) for gate, n in ctx.unrelated_nav_gates.items()]
                tsv.write_tsv(gate_path, rows, mode="a")

    # save_to_db(new_path)


if __name__ == "__main__":
    main(Path(sys.argv[1]) if len(sys.argv) == 2 else None)
