"""
停车场采纳轨迹30天聚合
"""
import re
import os
import traceback
import os.path
import tqdm
import sys
import subprocess

from src.trajectory.utils.pg_tool import PgTool
from datetime import datetime

BASE_AFS_PATH = "/home/<USER>/mnt/data_export/copy_accept_data/"  # 采纳数据存储的AFS
DAYS_THRESHOLD = 31  # 保留31天采纳数据


def run():
    """
    run
    """
    with PgTool() as pg:
        # 建表
        create_table(pg)
        print("create_table done")

        # copy数据入库
        tables = get_multi_days_park_accept_tables(pg)
        copy_to_park_accept_data(pg, tables)
        print("copy_to_park_accept_data done")

        # 清理过期的数据、文件
        del_expire_accept_db_data(pg)
        del_expire_accept_csv_files()
        print("del_expire_data done")


def get_multi_days_park_accept_tables(pg):
    """
    获取多天停车采纳数据表
    """
    sql = f"""
        select table_name 
        from information_schema.tables 
        where table_schema = 'public';
    """
    pg.cursor_accept_data_db.execute(sql)
    table_names = [row[0] for row in pg.cursor_accept_data_db.fetchall()]

    # 提取停车采纳数据表
    pattern = r"^park_low_accept_day_(\d{8})$"
    tables_with_dates = []
    for table in table_names:
        match = re.match(pattern, table)
        if not match:
            continue

        date_str = match.group(1)
        try:
            date_obj = datetime.strptime(date_str, "%Y%m%d")
            tables_with_dates.append((table, date_obj))
        except ValueError:
            continue

    # 只取最新多天
    tables_with_dates.sort(key=lambda x: x[1], reverse=True)
    latest_multi_tables = [table for table, _ in tables_with_dates[:DAYS_THRESHOLD]]
    print("latest_multi_tables: ", latest_multi_tables)

    return latest_multi_tables


def copy_to_park_accept_data(pg, tables):
    """
    采纳数据入库
    """
    for table_name in tqdm.tqdm(tables):
        if exist_accept_csv(table_name):
            continue

        print(f"process {table_name} ing")
        copy_to_with_psql(table_name)
        copy_from_with_psql(table_name)
        update_accept_time(pg, table_name)
        print(f"process {table_name} done")


def update_accept_time(pg, table_name):
    """
    更新采纳数据时间
    """
    try:
        # 从 table_name 中提取日期部分
        if isinstance(table_name, str) and table_name.startswith("park_low_accept_day_"):
            date_part = table_name.split("_")[-1]
        else:
            raise ValueError(f"日期提取错误")

        accept_time = datetime.strptime(date_part, "%Y%m%d")
        sql = """
             UPDATE park_accept_data 
             SET accept_time = %s
             WHERE accept_time <= '1980-01-01'
         """

        pg.cursor_traj_db.execute(sql, (accept_time,))
        pg.conn_traj_db.commit()
    except Exception as e:
        print(f"Error updating accept_time: {traceback.format_exc()}")
        pg.conn_traj_db.rollback()
        raise e


def copy_from_with_psql(table):
    """
    从afs copy数据入库
    """
    try:
        file_path = f"{BASE_AFS_PATH}/{table}.csv"

        # 设置环境变量来传递密码
        PG_PASSWORD = "traj_rw"
        env = os.environ.copy()
        env["PGPASSWORD"] = PG_PASSWORD

        # 构建 psql 命令
        psql_command = [
            "psql",
            "-U",
            "traj_rw",
            "-d",
            "traj_db",
            "-h",
            "10.56.135.221",
            "-p",
            "5432",
            "-c",
            f"\\COPY park_accept_data "
            f"(search_uid, search_bid, reco_park_bid, arrived_park_bid, arrived_parent_bid, "
            f"arrived_parent_pv, arrived_root_bid, arrived_root_pv, arrived_traj, arrived_end_point, "
            f"is_4cat_or_first, is_accept, uid, cuid) "
            f"FROM '{file_path}' DELIMITER E'\\t' CSV HEADER;",
        ]
        result = subprocess.run(psql_command, shell=True, capture_output=True, text=True, env=env)

        # 判断命令执行是否成功
        if result.returncode != 0:
            print(f"Error running \\COPY command: {result.stderr}")
        else:
            print(f"Data successfully copied from {file_path} to park_accept_data.")
    except subprocess.CalledProcessError as e:
        print(f"Error running \\COPY command: {e}")
        raise e
    except Exception as e:
        print(f"copy_from error：{traceback.format_exc()}")
        raise e


def copy_to_with_psql(table):
    """
    copy数据库到afs文件
    """
    try:
        # 设置环境变量，避免在命令行中传递密码
        PG_PASSWORD = "accept_rw_2025"
        env = os.environ.copy()
        env["PGPASSWORD"] = PG_PASSWORD

        # 构建 psql 命令
        file_path = f"{BASE_AFS_PATH}/{table}.csv"
        psql_command = [
            "psql",
            "-U",
            "accept_rw",
            "-d",
            "accept_data",
            "-h",
            "10.56.135.223",
            "-p",
            "8765",
            "-c",
            f"\\COPY {table} "
            f"(search_uid, search_bid, reco_park_bid, arrived_park_bid, arrived_parent_bid, "
            f"arrived_parent_pv, arrived_root_bid, arrived_root_pv, arrived_traj, arrived_end_point, "
            f"is_4cat_or_first, is_accept, uid, cuid) "
            f"TO '{file_path}' DELIMITER E'\\t' CSV HEADER;",
        ]
        result = subprocess.run(psql_command, capture_output=True, text=True, env=env)

        # 检查命令执行是否成功
        if result.returncode != 0:
            print(f"Error while running psql: {result.stderr}")
        else:
            print(f"Successfully copied data for table {table}")
    except subprocess.CalledProcessError as e:
        print(f"Error running psql command: {e}")
        raise e
    except Exception as e:
        print(f"copy_to error: {traceback.format_exc()}")
        raise e


def exist_accept_csv(table_name):
    """
    检测是否导出数据已经存在
    """
    file_path = os.path.join(BASE_AFS_PATH, f"{table_name}.csv")
    return os.path.exists(file_path)


def del_expire_accept_db_data(pg):
    """
    删除过期的采纳db数据并清理库
    """
    try:
        sql = f"""
            select accept_time::DATE 
            from park_accept_data
            where accept_time >= '1980-01-01'
            group by accept_time::DATE order by accept_time::DATE desc;
        """
        pg.cursor_traj_db.execute(sql)
        res = pg.cursor_traj_db.fetchall()
        if len(res) <= DAYS_THRESHOLD:
            return

        # 删除过期的数据
        expire_time_list = [x[0] for x in res[DAYS_THRESHOLD:]]
        for expire_time in expire_time_list:
            del_sql = f"""
                delete from park_accept_data
                where accept_time <= '{expire_time}';
            """
            pg.cursor_traj_db.execute(del_sql)
            pg.conn_traj_db.commit()

        # 清理数据库
        if len(expire_time_list) > DAYS_THRESHOLD:
            vacuum_sql = f"VACUUM FULL park_accept_data;"
            pg.cursor_traj_db.execute(vacuum_sql)
            pg.conn_traj_db.commit()
    except Exception as e:
        print(f"del_expire_accept_db_data error：{traceback.format_exc()}")
        raise e


def del_expire_accept_csv_files():
    """
    删除过期的采纳csv数据
    example: park_low_accept_day_20250104.csv
    """
    try:
        all_files = [f for f in os.listdir(BASE_AFS_PATH) if f.endswith(".csv")]

        # 删除过期文件
        files_with_dates = []
        for file in all_files:
            try:
                date_str = file.split("_")[-1].replace(".csv", "")
                file_date = datetime.strptime(date_str, "%Y%m%d")
                files_with_dates.append((file, file_date))
            except Exception as e:
                print(f"跳过文件 {file}，因为无法解析日期部分。错误: {e}")

        files_with_dates.sort(key=lambda x: x[1])

        # 删除过期文件
        files_to_delete = files_with_dates[:-DAYS_THRESHOLD]
        for file, _ in files_to_delete:
            file_path = os.path.join(BASE_AFS_PATH, file)
            if not os.path.exists(file_path):
                continue

            try:
                os.remove(file_path)
                print(f"删除文件: {file}")
            except Exception as e:
                print(f"无法删除文件 {file}，错误: {e}")
    except Exception as e:
        print(f"del_expire_accept_csv_files error: {traceback.format_exc()}")
        raise e


def exist_table(pg, table_name):
    """
    判断表是否存在
    """
    sql = f"""
         SELECT 1 
         FROM pg_catalog.pg_class 
         WHERE relname = %s 
         AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = 'public');
    """
    pg.cursor_traj_db.execute(sql, (table_name,))
    result = pg.cursor_traj_db.fetchone()
    return result[0] is not None


def create_table(pg):
    """
    create table `park_accept_data` sql
    """
    if exist_table(pg, "park_accept_data"):
        return

    sql = f"""
        CREATE TABLE IF NOT EXISTS park_accept_data (
            id                 bigint NOT NULL DEFAULT nextval('park_accept_data_id_seq'::regclass), 
            search_uid         character varying(128) NOT NULL DEFAULT ''::character varying,
            search_bid         character varying(128) NOT NULL DEFAULT ''::character varying,
            reco_park_bid      character varying(128) NOT NULL DEFAULT ''::character varying,
            arrived_park_bid   character varying(128) NOT NULL DEFAULT ''::character varying,
            arrived_parent_bid character varying(128) NOT NULL DEFAULT ''::character varying,
            arrived_parent_pv  integer NOT NULL DEFAULT 0,
            arrived_root_bid   character varying(128) NOT NULL DEFAULT ''::character varying,
            arrived_root_pv    integer NOT NULL DEFAULT 0,
            arrived_traj       geometry(Geometry, 4326),
            arrived_end_point  geometry(Geometry, 4326),
            is_4cat_or_first   integer NOT NULL DEFAULT 0,
            is_accept          integer NOT NULL DEFAULT 0,
            uid                character varying(128) NOT NULL DEFAULT ''::character varying,
            cuid               text,
            create_time        timestamp(6) WITHOUT TIME ZONE DEFAULT now(),
            accept_time        timestamp(6) WITHOUT TIME ZONE DEFAULT TIMESTAMP '1970-01-01 00:00:00', 
        
            CONSTRAINT park_accept_data_pkey PRIMARY KEY (id)
        );

        CREATE SEQUENCE park_accept_data_id_seq
            START WITH 1  -- 起始值
            INCREMENT BY 1;  -- 每次递增1
        
        CREATE INDEX park_accept_data_arrived_end_point_index
            ON park_accept_data USING gist (arrived_end_point);
        
        CREATE INDEX park_accept_data_arrived_park_bid_index
            ON park_accept_data USING btree (arrived_park_bid);
        
        CREATE INDEX park_accept_data_arrived_traj_index
            ON park_accept_data USING gist (arrived_traj);
        
        CREATE INDEX park_accept_data_reco_park_bid_index
            ON park_accept_data USING btree (reco_park_bid);
        
        CREATE INDEX park_accept_data_search_bid_index
            ON park_accept_data USING btree (search_bid);
        
        CREATE INDEX park_accept_data_search_uid_index
            ON park_accept_data USING btree (search_uid);
            
        CREATE INDEX idx_accept_time
            ON park_accept_data (accept_time);
    """
    pg.cursor_traj_db.execute(sql)
    pg.conn_traj_db.commit()


def copy_from(pg, table_version):
    """
    copy from afs
    """
    try:
        sql = """
            COPY park_accept_data
                (
                    search_uid,
                    search_bid,
                    reco_park_bid,
                    arrived_park_bid,
                    arrived_parent_bid,
                    arrived_parent_pv,
                    arrived_root_bid,
                    arrived_root_pv,
                    arrived_traj,
                    arrived_end_point,
                    is_4cat_or_first,
                    is_accept,
                    uid,
                    cuid
                ) FROM %s DELIMITER E'\t' CSV HEADER;
        """
        pg.cursor_traj_db.execute(sql, (f"{BASE_AFS_PATH}/park_low_accept_day_{table_version}.csv",))
        pg.conn_traj_db.commit()
    except Exception as e:
        print(f"copy_from error：{traceback.print_exc()}")
        pg.conn_traj_db.rollback()
        raise e


def copy_to(pg, table_version):
    """
    copy to afs
    """
    try:
        sql = f"""
            COPY park_low_accept_day_{table_version}
                (
                     search_uid,
                     search_bid,
                     reco_park_bid,
                     arrived_park_bid,
                     arrived_parent_bid,
                     arrived_parent_pv,
                     arrived_root_bid,
                     arrived_root_pv,
                     arrived_traj,
                     arrived_end_point,
                     is_4cat_or_first,
                     is_accept,
                     uid,
                     cuid
                ) TO '{BASE_AFS_PATH}/park_low_accept_day_{table_version}.csv' DELIMITER E'\t' CSV HEADER;
        """
        pg.cursor_accept_data_db.execute(sql)
        pg.conn_accept_data_db.commit()
    except Exception as e:
        print(f"copy_to error：{traceback.print_exc()}")
        pg.conn_accept_data_db.rollback()
        raise e


if __name__ == "__main__":
    """
    main
    """
    run()
