"""
select a.face_id, b.poi_bid, city_name
from blu_face a left
join blu_face_poi b on a.face_id = b.face_id
where city_name in ('北京市', '上海市', '广州市', '深圳市') and aoi_level = 2
"""
import sys
from dataclasses import dataclass, field
from datetime import datetime
from functools import partial
from multiprocessing.pool import Pool
from pathlib import Path

from loguru import logger
from shapely import Polygon, wkt
from shapely.geometry.base import BaseGeometry
from tqdm import tqdm

from src.parking.recognition import dbutils
from src.parking.storefront.verify.panorama import METER
from src.tools import pipeline, pgsql, tsv, utils

VERSION = "3.0.0"
TABLE_NAME = "byd_all_traj_substring"


@dataclass
class BluFace:
    """
    基础院落信息
    """

    bid: str
    name: str
    face_id: str
    std_tag: str
    geom: Polygon


@dataclass
class TrajInfo:
    """
    轨迹信息
    """

    is_start: bool
    tids: list[int]
    geoms: str
    points: str


@dataclass
class Context:
    """
    上下文
    """

    bid: str
    face_id: str
    city: str
    blu_face: BluFace = field(default=None)
    traj_infos: list[TrajInfo] = field(default_factory=list)
    related_parking_bids: list[str] = field(default_factory=list)
    contains_parking_bids: list[str] = field(default_factory=list)
    error: str = field(default="")


def fetch_blu_face(ctx: Context, proceed):
    """
    pipe：拉取基础院落信息
    """
    ctx.blu_face = get_blu_face(ctx.face_id, ctx.bid)
    if ctx.blu_face is None:
        ctx.error = f"invalid_face_id"
        return

    proceed()


def fetch_relate_parking(ctx: Context, proceed):
    """
    pipe：拉取父子关系关联的停车场
    """
    sql = """
        select bid from parking
        where parent_id = %s 
            and std_tag = '交通设施;停车场'
            and show_tag in ('地上停车场', '地下停车场', '立体停车场', '停车场')
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [ctx.bid])
    if ret:
        bids = [r[0] for r in ret]
        ctx.related_parking_bids.extend(bids)

    proceed()


def fetch_contains_parking(ctx: Context, proceed):
    """
    pipe：拉取空间关系关联的停车场
    """
    sql = """
        select bid from parking
        where std_tag = '交通设施;停车场' 
            and show_tag in ('地上停车场', '地下停车场', '立体停车场', '停车场')
            and st_contains(%s, gcj_geom)
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [f"SRID=4326;{ctx.blu_face.geom.wkt}"])
    if ret:
        bids = [r[0] for r in ret if r[0] not in ctx.related_parking_bids]
        ctx.contains_parking_bids.extend(bids)

    proceed()


def fetch_byd_traj_infos(ctx: Context, proceed, length: float):
    """
    pipe：拉取 BYD 轨迹信息
    """
    lines = get_byd_traj_lines(ctx.blu_face.geom.wkt, length)
    if not lines:
        ctx.error = f"not_found_traj_lines"
        return

    ctx.traj_infos = [
        TrajInfo(is_start=bool(is_start), tids=tids, geoms=geoms, points=points)
        for is_start, tids, geoms, points in lines
    ]
    proceed()


# helpers:


def get_byd_traj_lines(geom: str, length: float):
    """
    获取与 AOI 相交的轨迹线段，并截取指定长度
    """
    sql = """
        WITH
          qgeom AS (
            SELECT ST_GeomFromText(%(query_geom)s, 4326) AS geom
          ),
          candidates AS (
            SELECT
              b.id,
              b.start_point AS sp,
              b.end_point AS ep,
              ST_Length(b.geom) AS len,
              ST_Contains(q.geom, b.start_point) AS start_in,
              ST_Contains(q.geom, b.end_point) AS end_in,
              b.geom
            FROM byd_all_traj_v2 b
            CROSS JOIN qgeom q
            WHERE ST_Contains(q.geom, b.start_point) OR ST_Contains(q.geom, b.end_point)
          )
        SELECT 
            1, 
            array_agg(id order by id), 
            ST_Collect(ST_Line_Substring(geom, 0.0, LEAST(%(length)s / len, 1.0)) order by id), 
            ST_Collect(sp order by id)
        FROM candidates WHERE start_in and len > 0
        UNION ALL
        SELECT 
            0, 
            array_agg(id order by id), 
            ST_Collect(ST_Line_Substring(geom, GREATEST((len - %(length)s) / len, 0.0), 1.0) order by id), 
            ST_Collect(ep order by id)
        FROM candidates WHERE end_in and len > 0;
    """
    ret = dbutils.fetch_all(pgsql.TRAJ_DB2, sql, {"query_geom": geom, "length": length})
    return [(is_start, *infos) for is_start, *infos, in ret if not any(1 for x in infos if x is None)]


def get_blu_face(face_id: str, bid: str):
    """
    获取基础院落信息
    """
    sql_blu_face = """
        select name_ch, st_astext(geom) from blu_face
        where face_id = %s
    """
    ret = dbutils.fetch_one(pgsql.BACK_CONFIG, sql_blu_face, [face_id])
    if ret is None:
        return None

    name, geom = ret
    std_tag = ""
    if bid:
        sql_poi = """
            select name, std_tag from poi
            where bid = %s
        """
        ret = dbutils.fetch_one(pgsql.POI_SLAVER_CONFIG, sql_poi, [bid])
        if ret:
            name, std_tag = ret

    return BluFace(bid=bid, name=name, face_id=face_id, std_tag=std_tag, geom=wkt.loads(geom))


def save_to_db(table: str, file_path: Path):
    """
    保存数据到数据库
    """
    sql_clear = f"""
        -- 所有操作发生在同一个事务中，删除 VIEW 不会导致空窗期
        DROP VIEW IF EXISTS {TABLE_NAME};
        DROP TABLE IF EXISTS {table};
    """
    sql_create = f"""
        create table {table} (
            face_id varchar(64) primary key,
            bid varchar(64) not null default '',
            std_tag varchar(32) not null default '',
            name varchar(128) not null default '',
            city varchar(64) not null default '',
            geom geometry(geometry, 4326),
            related_parking_bids varchar(64)[] not null default '{{}}',
            contains_parking_bids varchar(64)[] not null default '{{}}',
            start_tids int[] not null default '{{}}',
            start_points geometry(geometry, 4326),
            start_lines geometry(geometry, 4326),
            end_tids int[] not null default '{{}}',
            end_points geometry(geometry, 4326),
            end_lines geometry(geometry, 4326),
            created_at timestamp not null default now()
        );
    """
    sql_copy = (
        f"COPY {table} "
        f"(bid, face_id, std_tag, name, city, related_parking_bids, contains_parking_bids, "
        f"geom, start_tids, start_points, start_lines, end_tids, end_points, end_lines) "
        r"FROM STDIN WITH (FORMAT text, DELIMITER E'\t', NULL '\N')"
    )
    sql_index = f"""
        create index idx_{table}_bid on {table} (bid);
        create index idx_{table}_std_tag on {table} (std_tag);
        create index idx_{table}_name on {table} (name);
        create index idx_{table}_city on {table} (city);
        create index idx_{table}_geom on {table} using gist (geom);
        create index idx_{table}_related_parking_bids on {table} using gin (related_parking_bids);
        create index idx_{table}_contains_parking_bids on {table} using gin (contains_parking_bids);
        create index idx_{table}_start_points on {table} using gist (start_points);
        create index idx_{table}_start_lines on {table} using gist (start_lines);
        create index idx_{table}_end_points on {table} using gist (end_points);
        create index idx_{table}_end_lines on {table} using gist (end_lines);
    """
    sql_view = f"""
        CREATE OR REPLACE VIEW {TABLE_NAME} AS
        SELECT * FROM {table};
    """
    with (
        pgsql.get_connection(pgsql.POI_CONFIG) as conn,
        conn.cursor() as curs,
        open(file_path, "r", encoding="utf-8") as f,
    ):
        curs.execute(sql_clear)
        curs.execute(sql_create)
        # noinspection PyTypeChecker
        curs.copy_expert(sql_copy, f)
        curs.execute(sql_index)
        curs.execute(sql_view)
        conn.commit()


def _format_geom(geom: BaseGeometry, null_str=r"\N"):
    return f"SRID=4326;{geom.wkt}" if geom and geom.is_valid and not geom.is_empty else null_str


def _format_arr(arr: list):
    if arr:
        s = ",".join(f'"{x}"' for x in arr)
        return f"{{{s}}}"
    else:
        return "{}"


def _format_traj_infos(traj_infos: list[TrajInfo]):
    def build(trajs: list[TrajInfo]):
        if trajs:
            assert len(trajs) == 1
            info = trajs[0]
            return info.tids, info.points, info.geoms

        return None, None, None

    start_tids, start_points, start_lines = build([t for t in traj_infos if t.is_start])
    end_tids, end_points, end_lines = build([t for t in traj_infos if not t.is_start])
    return start_tids, start_points, start_lines, end_tids, end_points, end_lines


def main(file_path: Path, overwrite: bool):
    """
    主函数
    """
    pipe = pipeline.Pipeline(
        fetch_blu_face,
        fetch_relate_parking,
        fetch_contains_parking,
        partial(fetch_byd_traj_infos, length=500 * METER),
    )

    today = datetime.now().strftime("%Y%m%d")
    work_dir = utils.ensure_dir(f"output/{file_path.stem}_{VERSION}_{today}")
    traj_info_path = utils.ensure_path(work_dir / "traj_info.db.tsv", cleanup=overwrite)
    error_path = utils.ensure_path(work_dir / "error.tsv", cleanup=overwrite)

    cases = list(tsv.read_tsv(file_path))
    if not overwrite:
        success_face_ids = set(t[1] for t in tsv.read_tsv(traj_info_path)) if traj_info_path.exists() else set()
        failed_face_ids = set(t[1] for t in tsv.read_tsv(error_path)) if error_path.exists() else set()
        done_face_ids = success_face_ids | failed_face_ids
        cases = [c for c in cases if c[0] not in done_face_ids]

    contexts = (Context(bid=bid if bid != r"\N" else "", face_id=face_id, city=city) for face_id, bid, city in cases)
    logger.info("export traj infos...")
    with Pool(64) as p, open(traj_info_path, "a", encoding="utf-8") as f:
        for ctx in tqdm(p.imap_unordered(pipe, contexts), total=len(cases)):
            if ctx.error:
                row = (ctx.bid, ctx.face_id, ctx.city, ctx.error)
                tsv.write_tsv(error_path, [row], mode="a")
                continue

            # AOI 基础信息
            header = (ctx.bid, ctx.face_id, ctx.blu_face.std_tag, ctx.blu_face.name, ctx.city)
            start_tids, start_points, start_lines, end_tids, end_points, end_lines = _format_traj_infos(ctx.traj_infos)

            # 附加信息
            related_parking = _format_arr(ctx.related_parking_bids)
            contains_parking = _format_arr(ctx.contains_parking_bids)
            info = (related_parking, contains_parking)

            # 几何数据
            geoms = (
                _format_geom(ctx.blu_face.geom),
                _format_arr(start_tids),
                start_points if start_lines else r"\N",
                start_lines if start_lines else r"\N",
                _format_arr(end_tids),
                end_points if end_points else r"\N",
                end_lines if end_lines else r"\N",
            )

            row = (*header, *info, *geoms)
            # 不能用 tsv，会被转义
            f.write("\t".join(row) + "\n")

    logger.info("save to db...")
    save_to_db(f"{TABLE_NAME}_{today}", traj_info_path)
    logger.info("done.")


if __name__ == "__main__":
    main(Path(sys.argv[1]), bool(int(sys.argv[2])) if len(sys.argv) > 2 else False)
