"""
批量将 *.gz (*.tsv) 文件转为 *.parquet 文件。
parquet 文件是一种由 Apache Parquet 定义的 开源列式存储格式。它被广泛应用于大数据生态系统（如 Hadoop、Spark、DuckDB 等），
用于高效压缩、快速读取和跨语言的数据共享。
实测：使用 parquet 文件尺寸为 gz 的 1/3，且运行 fast_parse.py 时，快 7-9 倍。
"""
import shutil
import sys
from multiprocessing.pool import Pool
from pathlib import Path

import polars as pl
from loguru import logger
from tqdm import tqdm

from src.tools import utils

traj_spec = {
    "point_id": pl.Int32,
    "lng": pl.Float32,
    "lat": pl.Float32,
    "data_source_type": pl.Int32,
    "uid": pl.String,
    "timestamp": pl.Int32,
    "line_speed": pl.Float32,
    "line_angle": pl.Float32,
    "point_speed": pl.Float32,
    "point_direction": pl.Float32,
    "status": pl.Int32,
    "link_sequence": pl.String,
    "dist_to_snode": pl.Float32,
    "traj_id": pl.String,
    "ref_point_x": pl.Float64,
    "ref_point_y": pl.Float64,
    "angle_diff": pl.Float32,
    "dist": pl.Float32,
    "confidence": pl.Int32,
    "navi_flag": pl.Int32,
    "in_viaduct": pl.Int32,
    "in_auxiliary": pl.Int32,
    "city_code": pl.Int32,
    "road_ver": pl.Float32,
    "link_len": pl.Float32,
    "turn_dir": pl.Int32,
    "match_prob": pl.String,
    "bits": pl.Int32,
    "db_traj_id": pl.String,
    "gps_radius": pl.Float32,
    "lng_bdmc": pl.Float64,
    "lat_bdmc": pl.Float64,
    "s2_index": pl.Int64,
    "mesh_id": pl.Int32,
    "province": pl.String,
    "date": pl.String,
    "ugc_id": pl.String,
}


def _process_cast_to_parquet(item: tuple):
    gz_file, parquet_file = item
    df = pl.read_csv(gz_file, separator="\t", has_header=False, schema_overrides=traj_spec)
    df.write_parquet(parquet_file)


def cast_to_parquet(gz_dir: Path, cleanup: bool):
    """
    将 gz 文件夹下的所有 gz 文件转为 parquet 文件，并保存到 parquet_dir 下。
    """
    parquet_dir = utils.ensure_dir(gz_dir.parent / f"{gz_dir.name}_parquet")
    gz_files = list(gz_dir.glob("*.gz"))
    tasks = ((gz_file, parquet_dir / f"{gz_file.stem}.parquet") for gz_file in gz_files)
    with Pool(64) as p:
        for _ in tqdm(p.imap_unordered(_process_cast_to_parquet, tasks), total=len(gz_files)):
            pass

    if cleanup:
        logger.info(f"rm {gz_dir}...")
        shutil.rmtree(gz_dir)

    logger.info("done")


if __name__ == "__main__":
    cast_to_parquet(Path(sys.argv[1]), bool(int(sys.argv[2])) if len(sys.argv) > 2 else False)
