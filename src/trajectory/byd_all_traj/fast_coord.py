"""
坐标转换函数的 numpy 版本，可以接受 ndarray 进行批量转换，目前只有 wgs84_to_gcj02 函数。
"""
import numpy as np

a = 6378245.0  # 长半轴
ee = 0.00669342162296594323  # 偏心率平方


def wgs84_to_gcj02(lng: np.ndarray, lat: np.ndarray) -> tuple[np.ndarray, np.ndarray]:
    """
    WGS84转GCJ02(火星坐标系)
    :param lng:WGS84坐标系的经度
    :param lat:WGS84坐标系的纬度
    """

    # mask: True 表示不在国内（out_of_china）
    mask = np.logical_not((lng > 73.66) & (lng < 135.05) & (lat > 3.86) & (lat < 53.55))

    # 初始化结果为原始坐标
    res_lng = lng.copy()
    res_lat = lat.copy()

    idx = ~mask  # 在国内的
    if np.any(idx):
        lng = lng[idx]
        lat = lat[idx]

        d_lat = transform_lat(lng - 105.0, lat - 35.0)
        d_lng = transform_lng(lng - 105.0, lat - 35.0)
        rad_lat = lat / 180.0 * np.pi
        magic = 1 - ee * np.sin(rad_lat) ** 2
        sqrt_magic = np.sqrt(magic)

        d_lat = (d_lat * 180.0) / ((a * (1 - ee)) / (magic * sqrt_magic) * np.pi)
        d_lng = (d_lng * 180.0) / (a / sqrt_magic * np.cos(rad_lat) * np.pi)

        res_lat[idx] = lat + d_lat
        res_lng[idx] = lng + d_lng

    return res_lng, res_lat


def transform_lat(lng, lat):
    """
    Transform Longitude, Latitude
    """
    ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * np.sqrt(np.fabs(lng))
    ret += (20.0 * np.sin(6.0 * lng * np.pi) + 20.0 * np.sin(2.0 * lng * np.pi)) * 2.0 / 3.0
    ret += (20.0 * np.sin(lat * np.pi) + 40.0 * np.sin(lat / 3.0 * np.pi)) * 2.0 / 3.0
    ret += (160.0 * np.sin(lat / 12.0 * np.pi) + 320 * np.sin(lat * np.pi / 30.0)) * 2.0 / 3.0
    return ret


def transform_lng(lng, lat):
    """
    Transform longitude, latitude
    """
    ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * np.sqrt(np.fabs(lng))
    ret += (20.0 * np.sin(6.0 * lng * np.pi) + 20.0 * np.sin(2.0 * lng * np.pi)) * 2.0 / 3.0
    ret += (20.0 * np.sin(lng * np.pi) + 40.0 * np.sin(lng / 3.0 * np.pi)) * 2.0 / 3.0
    ret += (150.0 * np.sin(lng / 12.0 * np.pi) + 300.0 * np.sin(lng / 30.0 * np.pi)) * 2.0 / 3.0
    return ret
