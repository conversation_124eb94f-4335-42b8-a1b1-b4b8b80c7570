"""
解析原始轨迹点数据，生成轨迹线数据
"""
import sys
from pathlib import Path

import numpy as np
import polars as pl

from src.parking.recognition import dbutils
from src.tools import pgsql
from src.trajectory.byd_all_traj import fast_coord


def _process_coord(s: pl.Series):
    lng = s.struct.field("lng").to_numpy()
    lat = s.struct.field("lat").to_numpy()
    lng_gcj, lat_gcj = fast_coord.wgs84_to_gcj02(lng, lat)
    return np.stack([lng_gcj, lat_gcj], axis=1)


def get_mesh_ids(city_zhs: list[str]) -> list[int]:
    """
    获取指定城市（中文名）的 mesh_id
    """
    sql = """
        select distinct mesh_id
        from mesh_conf_wkt
        where cityname in %s
    """
    ret = dbutils.fetch_all(pgsql.BACK_CONFIG, sql, [tuple(city_zhs)])
    return [int(r[0]) for r in ret]


def parse(df: pl.LazyFrame, output_path: Path, break_timespan=1800):
    """
    解析轨迹点文件，生成轨迹线
    """
    # mesh_ids = get_mesh_ids(["北京市", "上海市", "广州市", "深圳市"])
    df = (
        df
        # .filter(pl.col("mesh_id").is_in(mesh_ids))
        .sort(["uid", "timestamp"])
        .with_columns(
            pl.struct("lng", "lat")
            .map_batches(_process_coord, return_dtype=pl.Array(pl.Float32, shape=2))
            .arr.to_struct(fields=["lng_gcj", "lat_gcj"])
            .alias("gcj_arr")
        )
        .with_columns(
            pl.col("gcj_arr").struct.field("lng_gcj").alias("lng_gcj"),
            pl.col("gcj_arr").struct.field("lat_gcj").alias("lat_gcj"),
        )
        .with_columns((pl.col("lng_gcj").cast(pl.Utf8) + " " + pl.col("lat_gcj").cast(pl.Utf8)).alias("xy"))
        .with_columns(
            (pl.col("timestamp").diff() > break_timespan).fill_null(True).alias("new_session"),
            (pl.col("uid") != pl.col("uid").shift()).fill_null(True).alias("new_user"),
        )
        .with_columns((pl.col("new_session") | pl.col("new_user")).cum_sum().alias("new_segment"))
        .group_by(["new_segment", "uid"])
        .agg(
            pl.col("xy").alias("xy_list"),
            pl.col("point_speed").cast(pl.Utf8).alias("speed_list"),
            pl.from_epoch(pl.col("timestamp").first(), time_unit="s")
            .dt.to_string("%Y-%m-%d %H:%M:%S")
            .alias("start_time"),
            pl.from_epoch(pl.col("timestamp").last(), time_unit="s")
            .dt.to_string("%Y-%m-%d %H:%M:%S")
            .alias("end_time"),
            pl.col("mesh_id").first().alias("mesh_id"),
            pl.col("date").first().str.strptime(pl.Date, "%Y%m%d").alias("date"),
        )
        .filter(pl.col("xy_list").list.len() > 1)
        .with_columns(
            pl.col("speed_list").list.head(10).alias("start_speeds"),
            pl.col("speed_list").list.tail(10).alias("end_speeds"),
        )
        .with_columns(
            ("{" + pl.col("start_speeds").list.join(",") + "}").alias("start_speeds"),
            ("{" + pl.col("end_speeds").list.join(",") + "}").alias("end_speeds"),
            ("SRID=4326;LINESTRING(" + pl.col("xy_list").list.join(",") + ")").alias("wkt"),
            ("SRID=4326;POINT(" + pl.col("xy_list").list.first() + ")").alias("start_pt"),
            ("SRID=4326;POINT(" + pl.col("xy_list").list.last() + ")").alias("end_pt"),
        )
        .select(
            [
                "uid",
                "mesh_id",
                "start_pt",
                "start_time",
                "start_speeds",
                "end_pt",
                "end_time",
                "end_speeds",
                "date",
                "wkt",
            ]
        )
    )
    df = df.collect(engine="streaming")
    df.write_csv(output_path, separator="\t", include_header=False)


def main(parquet_dir: Path):
    """
    主函数，解析所有指定目录下的文件，生成轨迹线 tsv 文件
    """
    df = pl.scan_parquet(list(parquet_dir.glob("*.parquet")), low_memory=False)
    parse(df, parquet_dir.parent / f"output_{parquet_dir.name}.tsv")


if __name__ == "__main__":
    main(Path(sys.argv[1]))
