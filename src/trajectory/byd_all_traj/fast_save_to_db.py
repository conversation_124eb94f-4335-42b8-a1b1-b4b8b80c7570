"""
保存 BYD 解析后的 tsv 文件到数据库，这不是最快的方法，
据说使用 psycopg3 的 binary 写入会快 2-3 倍，并且使用 parquet 文件保存，大小只有 tsv 的三分之一。
但是 psycopg3 与 psycopg2 不兼容，不能同时安装，本仓库大量代码基于 psycopg2，不好改。
"""
import sys
from datetime import datetime, timedelta
from pathlib import Path

from src.parking.recognition import dbutils
from src.tools import pgsql


# TODO: 以下是使用 psycopg3 的 binary 入库方式，没有调研完全，仅供参考。
# def get_connection(config: dict):
#     return psycopg.connect(
#         database=config["db"],
#         user=config["user"],
#         password=config["pwd"],
#         host=config["host"],
#         port=config["port"],
#     )
#
#
# def save_to_db(df: pl.LazyFrame):
#     batch_size = 5_000_000
#     with get_connection(pgsql.TRAJ_DB2) as conn:
#         cur = conn.cursor()
#         copy_sql = "COPY your_table (col1, col2, col3) FROM STDIN (FORMAT BINARY)"
#         df.collect(engine="streaming").write_database()
#         for df_batch in df.collect(engine="streaming").iter_slices(n_rows=batch_size):
#             ps_df = df_batch.to_pandas()  # zero-copy
#             with cur.copy(copy_sql) as copy:
#                 for row in ps_df.itertuples(index=False, name=None):
#                     copy.write_row(row)
#
#         conn.commit()


def save_to_db(file_path: Path):
    """
    保存本地持久化文件到数据库，文件名必须：output_20250701_parquet.tsv 格式
    """
    today_str = file_path.stem.split("_")[1]
    dt = datetime.strptime(today_str, "%Y%m%d")
    from_date = dt.strftime("%Y-%m-%d")
    to_date = (dt + timedelta(days=1)).strftime("%Y-%m-%d")

    sql_partition = f"""
        create table if not exists byd_all_traj_{today_str} 
        partition of byd_all_traj for values from ('{from_date}') to ('{to_date}');
    """
    # 先执行建表，不要放到下面事务里，不然导数据时会锁表
    dbutils.execute(pgsql.BYD_TRAJ, sql_partition)

    sql_copy = (
        f"COPY byd_all_traj "
        f"(uid, mesh_id, start_point, start_time, start_speeds, end_point, end_time, end_speeds, traj_date, geom)"
        r"FROM STDIN WITH (FORMAT text, DELIMITER E'\t', NULL '\N')"
    )
    with (
        pgsql.get_connection(pgsql.BYD_TRAJ) as conn,
        conn.cursor() as curs,
        open(file_path, "r", encoding="utf-8") as f,
    ):
        # noinspection PyTypeChecker
        curs.copy_expert(sql_copy, f)
        conn.commit()


if __name__ == "__main__":
    save_to_db(Path(sys.argv[1]))
