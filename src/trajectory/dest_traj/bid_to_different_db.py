"""
终点轨迹入库目录
"""
import os.path
import sys
import tqdm

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("../../..")

from src.trajectory.utils.pg_tool import PgTool


def process_bid_to_different_db():
    """
    入库process
    """
    with PgTool() as pg:
        bid_to_different_db(pg)


def get_sync_traj_bid_record(pg):
    """
    获取待入库记录
    """
    sql = f"select bid from sync_traj_bid_record where status = 0 and diff_db_status = 0"
    pg.cursor_dest_traj_to_aoi.execute(sql)
    res_list = pg.cursor_dest_traj_to_aoi.fetchall()
    return res_list


def exist_in_db(pg, bid):
    """
    检查记录是否存在
    """
    check_sql = "SELECT 1 FROM dest_traj_bid_db_index WHERE bid = %s"
    pg.cursor_aoi_dest_traj.execute(check_sql, (bid,))
    exists = pg.cursor_aoi_dest_traj.fetchone()
    return exists


def bid_to_different_db(pg):
    """
    分发bid入不同的库
    """
    res_list = get_sync_traj_bid_record(pg)
    count = 0
    for item in tqdm.tqdm(res_list):
        count += 1
        bid = item[0]

        # 记录已存在，不重复插入
        exist = exist_in_db(pg, bid)
        if exist:
            update_diff_db_status(pg, bid)
            continue

        # 如果记录不存在，则插入
        aoi_res = get_aoi_wkt(pg, bid)
        if aoi_res is None:
            db_name = "aoi_dest_traj_db"
        else:
            db_name = "aoi_dest_traj_db2"
        sql = f"""
            insert into dest_traj_bid_db_index(bid, db_name) 
            values('{bid}', '{db_name}')
        """
        pg.cursor_aoi_dest_traj.execute(sql)
        if count > 1000:
            pg.conn_aoi_dest_traj.commit()
            count = 0
        update_diff_db_status(pg, bid)
    pg.conn_aoi_dest_traj.commit()


def get_aoi_wkt(pg, bid):
    """
    获取AOI信息
    """
    sql = f"""
        select st_astext(geom) 
        from blu_face f inner join blu_face_poi p on p.face_id = f.face_id
        where p.poi_bid = '{bid}' and f.aoi_level = 2 and f.src != 'SD'
    """
    pg.cursor_aoi.execute(sql)
    res = pg.cursor_aoi.fetchone()
    return res


def update_diff_db_status(pg, bid):
    """
    更新终点分发库状态
    """
    sql = f"update sync_traj_bid_record set diff_db_status = 2 where bid = '{bid}'"
    pg.cursor_dest_traj_to_aoi.execute(sql)
    pg.conn_dest_traj_to_aoi.commit()


if __name__ == "__main__":
    """
    main
    """
    print("---start process---")
    process_bid_to_different_db()
    print("---end process---")
