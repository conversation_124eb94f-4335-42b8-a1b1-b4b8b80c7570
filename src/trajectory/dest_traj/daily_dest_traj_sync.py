"""
每日例行更新终点轨迹，一次入最新一天
"""

import os.path
import sys
import tqdm

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("../../..")

from bid_to_different_db import process_bid_to_different_db
from dest_traj_to_db import process_dest_traj_to_db
from datetime import datetime, date, timedelta
from src.trajectory.utils.pg_tool import PgTool
from src.tools.function import exec_shell_cmd


def run():
    """
    run
    """
    with PgTool() as pg:
        start_time = datetime.today().strftime("%Y-%m-%d %H:%M")
        print(f"---update dest traj start: {start_time}---")

        # 更新终点轨迹文件
        print("step_1_start: update bids")
        bids_list = get_bids_list(pg)
        delete_target_bids_old_file()
        create_target_bids_new_file(bids_list)
        link_target_bids_file()
        print("step_1_end: update bids num: ", len(bids_list))

        # 终点bid分配到不同的pg库
        print("step_2_start: bid to different db")
        process_bid_to_different_db()
        print("step_2_end: bid to different db")

        # 拉取终点轨迹
        print("step_3_start: fetch dest traj")
        fetch_dest_traj()
        print("step_3_end: fetch dest traj")

        # 终点轨迹入库
        print("step_4_start: traj into db")
        process_dest_traj_to_db()
        print("step_4_end: traj into db")

        # 删除旧轨迹
        print("step_5_start: delete old traj")
        delete_old_traj(pg)
        print("step_5_end: delete old traj")

        # 清理磁盘
        print("step_6_start: vacuum full")
        vacuum_full(pg)
        print("step_6_end: vacuum full")

        end_time = datetime.today().strftime("%Y-%m-%d %H:%M")
        print(f"---update dest traj done: {end_time}---")


def fetch_dest_traj():
    """
    拉取3天前的当天轨迹
    """
    today = date.today()
    date_3_days_ago = today - timedelta(days=3)
    formatted_date = date_3_days_ago.strftime("%Y%m%d")
    try:
        exec_shell_cmd(
            f"cd /home/<USER>/aoi_cluster/midoutput/trajectory/debug_lh/;"
            f"[ -f run_daily_aoi.log ] && rm run_daily_aoi.log;"
            f"source ../../data_closed_loop/common_run.sh;"
            f"sh get_target_bid_traj_lh_no_redece_aoi.sh {formatted_date} 1 1;"
        )
    except:
        pass


def link_target_bids_file():
    """
    软链接终点文件
    """
    try:
        exec_shell_cmd(
            f"cd /home/<USER>/aoi_cluster/midoutput/trajectory/debug_lh/;"
            f"ln -sf ./bid_list/target_bid_list target_bid.list;"
        )
    except:
        pass


def delete_target_bids_old_file():
    """
    删除历史终点文件
    """
    try:
        exec_shell_cmd(
            f"cd /home/<USER>/aoi_cluster/midoutput/trajectory/debug_lh/bid_list/;"
            f"[ -f target_bid.list ] && rm target_bid.list;"
        )
    except:
        pass


def delete_old_traj(pg):
    """
    删除最旧一天的轨迹
    """
    today = date.today()
    date_32_days_ago = today - timedelta(days=32)
    formatted_date = date_32_days_ago.strftime("%Y-%m-%d")

    sql_dest = f"delete from dest_traj where traj_time<='{formatted_date}';"
    pg.cursor_aoi_dest_traj.execute(sql_dest)
    pg.conn_aoi_dest_traj.commit()

    sql_dest2 = f"delete from dest_traj where traj_time<='{formatted_date}';"
    pg.cursor_aoi_dest_traj2.execute(sql_dest2)
    pg.conn_aoi_dest_traj2.commit()


def vacuum_full(pg):
    """
    清理磁盘
    """
    sql_dest = f"VACUUM FULL"
    pg.cursor_aoi_dest_traj.execute(sql_dest)
    pg.conn_aoi_dest_traj.commit()
    print(f"aoi_dest_traj_db vacuum full done")

    sql_dest2 = f"VACUUM FULL"
    pg.cursor_aoi_dest_traj2.execute(sql_dest2)
    pg.conn_aoi_dest_traj2.commit()
    print(f"aoi_dest_traj_db2 vacuum full done")


def create_target_bids_new_file(bids_result):
    """
    创建轨迹终点bids文件
    """
    file = open("/home/<USER>/aoi_cluster/midoutput/trajectory/debug_lh/bid_list/target_bid_list", "w")
    for item in bids_result:
        strs = (
            f"{item[0]}\t\n"
        )
        file.write(strs)
    file.close()


def get_bids_list(pg):
    """
    获取终点bids
    """
    sql = f"""
        select distinct bid 
        from sync_traj_bid_record 
        where status = 0 and batch in ('parking_parent', 'key_categories', 'parking')
    """
    pg.cursor_dest_traj_to_aoi.execute(sql)
    res_list = pg.cursor_dest_traj_to_aoi.fetchall()
    return res_list


if __name__ == '__main__':
    run()