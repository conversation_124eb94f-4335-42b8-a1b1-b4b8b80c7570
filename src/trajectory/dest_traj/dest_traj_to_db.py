"""
终点轨迹入库
"""

import os.path
import sys
import traceback
import hashlib
import shapely.wkt

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("../../..")

from src.trajectory.utils.pg_tool import PgTool
from datetime import datetime
from pathlib import Path
from src.trajectory.utils.coord_trans import wgs84_to_gcj02
from shapely.geometry import LineString


bid_wkt_dict = {}


def traj_to_db(pg, file_path, traj_time):
    """
    轨迹文件入库
    """
    with open(file_path, encoding="utf-8") as f:
        curr_time = datetime.today().strftime("%Y-%m-%d")
        count = 0
        try:
            pg_conn, pg_cursor = None, None
            for line in f.readlines():
                # wgs84转gcj02
                try:
                    bid, cuid, wkt = line.rstrip().split("\t")
                except:
                    bid, wkt = line.rstrip().split("\t")
                gcj_wkt = trans_wgs84_linestring_to_gc02(wkt)

                # 获取AOI框
                aoi_wkt = None
                if bid in bid_wkt_dict:
                    aoi_wkt = bid_wkt_dict[bid]
                else:
                    aoi_res = get_aoi_wkt(pg, bid)
                    if aoi_res is not None:
                        aoi_wkt = aoi_res[0]
                        bid_wkt_dict[bid] = aoi_wkt
                if aoi_wkt is not None:
                    aoi_sp_buffer = shapely.wkt.loads(aoi_wkt).buffer(1000 / 110000)
                    bool_intersect = aoi_sp_buffer.intersects(shapely.wkt.loads(gcj_wkt))
                    if not bool_intersect:
                        continue

                # 计算hash
                hash_index = calcu_hash_index(bid)
                sql = f"""
                   insert into dest_traj (
                     bid, cuid, geom, hash_index, create_time, traj_time
                   ) values (
                     '{bid}', '{cuid}', st_geomfromtext('{gcj_wkt}', 4326), {hash_index} , '{curr_time}', '{traj_time}'
                   );
                """

                # 选择库
                db_name_res = get_db_name(pg, bid)
                if db_name_res is None:
                    continue
                db_name = db_name_res[0]
                if db_name is None:
                    continue
                if db_name == "aoi_dest_traj_db":
                    pg_conn = pg.conn_aoi_dest_traj
                    pg_cursor = pg.cursor_aoi_dest_traj
                elif db_name == "aoi_dest_traj_db2":
                    pg_conn = pg.conn_aoi_dest_traj2
                    pg_cursor = pg.cursor_aoi_dest_traj2
                pg_cursor.execute(sql)

                count = count + 1
                if count > 3000:
                    pg_conn.commit()
                    count = 0
            pg_conn.commit()
        except Exception as e:
            traceback.print_exc()


def get_aoi_wkt(pg, bid):
    """
    获取AOI信息
    """
    sql = f"""
        select st_astext(geom) 
        from blu_face f inner join blu_face_poi p on p.face_id = f.face_id
        where p.poi_bid = '{bid}' and f.aoi_level = 2 and f.src != 'SD'
    """
    pg.cursor_aoi.execute(sql)
    res = pg.cursor_aoi.fetchone()
    return res


def trans_wgs84_linestring_to_gc02(line_wkt):
    """
    wgs84线转gcj02
    """
    line_coords = shapely.wkt.loads(line_wkt).coords

    trans_line_points = []
    for coord in line_coords:
        gcj_lon, gcj_lat = wgs84_to_gcj02(coord[0], coord[1])
        trans_line_points.append((gcj_lon, gcj_lat))
    return LineString(trans_line_points).wkt


def get_db_name(pg, bid):
    """
    获取库名
    """
    sql = f"select db_name from dest_traj_bid_db_index where bid='{bid}'"
    pg.cursor_aoi_dest_traj.execute(sql)
    res = pg.cursor_aoi_dest_traj.fetchone()
    return res


def calcu_hash_index(bid):
    """
    使用 hashlib 计算 bid 的哈希值
    """
    hash_object = hashlib.md5(bid.encode())
    hash_value = int(hash_object.hexdigest(), 16)
    return hash_value % 10


def process_directory(curr_dir):
    """
    process_directory
    """
    with PgTool() as pg:
        if not Path(curr_dir / "done.flag").exists():
            return  # AFS还没拉完，不处理

        if Path(curr_dir / "success_to_pg.flag").exists():
            return  # 已入库过，不再重复入库

        # 入库
        traj_time = datetime.strptime(curr_dir.name.split("/")[-1], "%Y%m%d")

        files = list(curr_dir.glob("**/*"))
        for count, file in enumerate(files, start=1):
            print(f"curr_dir: {curr_dir}, process: {count}")
            traj_to_db(pg, file, traj_time)

        # 入库完成标志
        Path(curr_dir / "success_to_pg.flag").touch()


def process_dest_traj_to_db():
    """
    入库process
    """
    data_dir = Path("/home/<USER>/dest_traj_files/")
    dir_list = list(data_dir.iterdir())
    dir_list.sort()

    for dir in dir_list[0:1]:
        print(dir)
        process_directory(dir)


if __name__ == "__main__":
    """
    main
    """
    print("---start process---")
    process_dest_traj_to_db()
    print("---end process---")
