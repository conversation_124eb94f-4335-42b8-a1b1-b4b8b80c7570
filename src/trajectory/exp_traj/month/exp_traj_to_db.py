"""
最新经验轨迹AFS文件入库
"""

import os.path
import sys
import tqdm
import pandas as pd
import re
import json

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")

from src.trajectory.utils.pg_tool import PgTool
from datetime import datetime
from pathlib import Path
from json.decoder import JSONDecodeError


def load_traj_data(file_path):
    """
    加载经验轨迹数据
    """
    try:
        df = pd.read_csv(file_path)
    except pd.errors.EmptyDataError as e:
        print(f"DtypeWarning: {e}")
        df = pd.DataFrame()
    return df


def truncate_db():
    """
    清空轨迹表
    """
    with PgTool() as pg:
        sql = "truncate exp_traj_monthly_update"
        pg.cursor_dest_traj_to_aoi.execute(sql)
        print("truncate_db success")


def save_traj_to_db(df, version):
    """
    经验轨迹入库，使用参数化查询
    """
    with PgTool() as pg:
        sql = """
            INSERT INTO exp_traj_monthly_update (
                cluster_id, 
                province, 
                city, 
                mesh_id, 
                end_poi_uid, 
                end_poi_name,
                end_track_line, 
                num_yaw, 
                yaw_rate, 
                num_track, 
                dist_dest, 
                confidence, 
                create_time, 
                num_track_route_end, 
                route_end_rank, 
                route_end_percentage, 
                cuid_repetition_rate, 
                num_comfrom_openapi, 
                route_end_label,
                end_poi_distribution,
                version
            ) VALUES (%s, %s, %s, %s, %s, %s, ST_GeomFromText(%s, 4326), %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        curr_time = datetime.today().strftime("%Y-%m-%d")
        num = 0
        for _, row in tqdm.tqdm(df.iterrows()):
            (
                cluster_id,
                province,
                city,
                mesh_id,
                end_poi_uid,
                end_poi_name,
                end_track_line,
                num_yaw,
                yaw_rate,
                dist_dest,
                confidence,
                num_track,
                num_track_route_end,
                route_end_rank,
                route_end_percentage,
                cuid_repetition_rate,
                num_comfrom_openapi,
                route_end_label,
                end_poi_distribution,
                version,
            ) = (
                row["cluster_id"],
                row["province"],
                row["city"],
                row["mesh_id"],
                str(row["end_poi_uid"]),
                str(row["end_poi_name"]),
                row["end_track_line"],
                row["num_yaw"],
                row["yaw_rate"],
                row["dist_dest"],
                row["confidence"],
                row["num_track"],
                row["num_track_route_end"],
                row["route_end_rank"],
                row["route_end_percentage"],
                row["cuid_repetition_rate"],
                row["num_comfrom_openapi"],
                row["route_end_label"],
                row["end_poi_distribution"],
                version,
            )

            end_poi_name = remove_single_quotes(end_poi_name)
            end_poi_distribution = remove_single_quotes(end_poi_distribution)

            if len(end_poi_name) > 2000:
                print("end_poi_name too long", end_poi_name)
                end_poi_name = end_poi_name[0:2000]

            if not is_valid_json(end_poi_distribution):
                print("is_valid_json", end_poi_distribution)
                end_poi_distribution = "NULL"

            params = (
                cluster_id,
                province,
                city,
                mesh_id,
                end_poi_uid,
                end_poi_name,
                end_track_line,
                num_yaw,
                yaw_rate,
                num_track,
                dist_dest,
                confidence,
                curr_time,
                num_track_route_end,
                route_end_rank,
                route_end_percentage,
                cuid_repetition_rate,
                num_comfrom_openapi,
                route_end_label,
                end_poi_distribution,
                version,
            )

            pg.cursor_dest_traj_to_aoi.execute(sql, params)
            num += 1
            if num > 500:
                # 500条commit一次
                pg.conn_dest_traj_to_aoi.commit()
                num = 0

        if num > 0:
            pg.conn_dest_traj_to_aoi.commit()


def is_valid_json(json_str):
    """
    判断给定的字符串是否可以解析为有效的 JSON。
    """
    try:
        # 尝试解析 JSON 字符串
        json.loads(json_str)
        return True  # 如果解析成功，则是有效的 JSON
    except JSONDecodeError:
        return False  # 如果解析失败，则不是有效的 JSON
    except Exception as e:
        # 捕获其他异常
        print(f"发生错误: {e}")
        return False


def remove_single_quotes(input_str):
    """
    删除字符串中的所有单引号（'）
    """
    return input_str.replace("'", "")


def has_illegal_characters(s):
    """
    判断字符串是否含有非法字符
    """
    illegal_characters = re.compile(r"['\"]|nan")
    return bool(illegal_characters.search(s))


def is_alphanumeric(s):
    """
    判断字符串是否只包含字母或数字
    """
    pattern = r"^[a-zA-Z0-9]+$"
    return bool(re.match(pattern, s))


def create_table(pg):
    """
    建表
    """
    sql = f"""
       create table if not exists exp_traj_monthly_update (
            id             serial primary key,
            cluster_id     character varying(300) not null default ''::character varying,
            province       character varying(30) not null default ''::character varying,
            city           character varying(30) not null default ''::character varying,
            mesh_id        character varying(6) not null default ''::character varying,
            end_poi_uid    character varying(100) not null default ''::character varying,
            end_poi_name   character varying(2000) not null default ''::character varying,       
            end_track_line geometry,
            num_yaw        integer not null default 0,
            yaw_rate       double precision not null default 0,   
            num_track      integer not null default 0,
            dist_dest      double precision not null default 0,    
            confidence     double precision not null default 0,    
            create_time    timestamp without time zone default '1970-01-01 00:00:00'::timestamp without time zone,
            num_track_route_end integer not null default 0,
            route_end_rank integer not null default 0,
            route_end_percentage double precision not null default 0,
            cuid_repetition_rate double precision not null default 0,
            num_comfrom_openapi integer not null default 0,
            route_end_label character varying(1000) not null default ''::character varying,
            version character varying(200) not null default ''::character varying,
            end_poi_distribution jsonb
       );
       create index exp_traj_monthly_update_end_poi_uid_idx on exp_traj_monthly_update(end_poi_uid);
       create index exp_traj_monthly_update_cluster_id_idx on exp_traj_monthly_update(cluster_id);
       create index exp_traj_monthly_update_end_poi_distribution_idx ON exp_traj_monthly_update USING GIN (end_poi_distribution jsonb_path_ops);
    """
    pg.cursor_dest_traj_to_aoi.execute(sql)
    pg.conn_dest_traj_to_aoi.commit()


def process_exp_traj_to_db():
    """
    处理经验轨迹入库
    """
    # 排除无关的文件
    data_dir = Path("/home/<USER>/jingyanguiji/exp_traj/monthly_data/")
    dir_list = sorted((d for d in data_dir.iterdir() if d.is_dir() and "log" not in d.name), key=lambda x: x.name)

    # 获取最新的目录
    latest_dir = dir_list[-1] if dir_list else None  # 增加空检查，防止没有目录的情况
    if latest_dir is None:
        print("No directories found.")
        return False
    print(f"Latest directory: {latest_dir}")

    # 判断是否可处理
    if not Path(latest_dir / "_SUCCESS").exists():
        print("AFS is not fully fetched, skipping.")
        return False  # 当前AFS没fetch完，不处理

    if Path(latest_dir / "_TO_DB_SUCCESS").exists():
        print("AFS has already been processed and inserted into DB, skipping.")
        return False  # 当前AFS已经入库，不处理

    # 文件夹名作为版本号
    version = latest_dir.name

    # 入库前清表
    truncate_db()

    # 入库
    for file_name in tqdm.tqdm(latest_dir.glob("**/*.csv")):
        print(file_name)
        df = load_traj_data(file_name)
        save_traj_to_db(df, version)

    # 入库完成标志
    Path(latest_dir / "_TO_DB_SUCCESS").touch()
    return True


def rename_db():
    """
    轨迹入库完成，库替换更新
    """
    with PgTool() as pg:
        sql = f"""
        BEGIN;
        ALTER TABLE exp_traj_monthly RENAME TO exp_traj_monthly_update_bak;
        ALTER TABLE exp_traj_monthly_update RENAME TO exp_traj_monthly;
        ALTER TABLE exp_traj_monthly_update_bak RENAME TO exp_traj_monthly_update;
        COMMIT;
        """
        pg.cursor_dest_traj_to_aoi.execute(sql)
        pg.conn_dest_traj_to_aoi.commit()


if __name__ == "__main__":
    """
    main
    """
    print("start process")
    process_exp_traj_to_db()
    print("end process")
