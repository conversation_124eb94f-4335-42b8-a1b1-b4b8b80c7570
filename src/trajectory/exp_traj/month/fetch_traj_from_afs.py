"""
从AFS拉取经验轨迹文件
"""

import sys
import os
import time
import traceback
import subprocess
import shutil

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("..")

from datetime import datetime
from pathlib import Path
from pg_tool import PgTool

Monthly: str = "Monthly"  # 月级更新


class AfsConsts:
    """
    AFS配置常量
    """

    EXE_PATH = "/home/<USER>/chenbaojun/scripts/afs/bin/afsshell"
    U_AND_W = "--username=map-dataop --password=map-dataop"
    MONTHLY_TRAJ_PATH = "afs://aries.afs.baidu.com:9902/user/map-de-gzhxy/mis_newest/clusterTrackEndMonth"


class ProcessConsts:
    """
    处理目录
    de16
    """

    PROCESS_PATH = "/home/<USER>/jingyanguiji/exp_traj/monthly_data/"


def fetch_monthly_traj_files():
    """
    拉取月级更新的经验轨迹文件
    """
    # delete_files_in_directory(ProcessConsts.PROCESS_PATH)
    latest_folder_path = fetch_latest_afs_folder(Monthly)
    out_put_path = f"{ProcessConsts.PROCESS_PATH}/{latest_folder_path[-9:-1]}"
    if not Path(out_put_path).exists():
        delete_files_in_directory(ProcessConsts.PROCESS_PATH)
        fetch_afs_files(latest_folder_path[-9:-1])


def delete_files_in_directory(directory_path):
    """
    删除历史文件
    """
    dir_path = Path(directory_path)

    # 检查目录是否存在
    if dir_path.exists() and dir_path.is_dir():
        # 遍历目录中的所有文件和子目录
        for item in dir_path.iterdir():
            if item.is_dir():
                shutil.rmtree(item)  # 删除子目录
            else:
                item.unlink()  # 删除文件
        print(f"目录中的所有内容已被删除，但目录保留: {directory_path}")
    else:
        print(f"目录不存在或不是一个有效的目录: {directory_path}")


def fetch_afs_files(date_str):
    """
    拉取AFS文件
    """
    cmd_str = f"""
        {AfsConsts.EXE_PATH} {AfsConsts.U_AND_W} get {AfsConsts.MONTHLY_TRAJ_PATH}/{date_str} {ProcessConsts.PROCESS_PATH}
    """
    print(cmd_str)
    # 使用 subprocess 来直接执行命令
    process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    # 获取命令执行的输出和错误信息
    stdout, stderr = process.communicate()

    # 检查命令返回的状态码
    if process.returncode != 0:
        raise Exception(f"fetch_afs_files failed: {stderr.decode('utf-8')}")
    else:
        print(stdout.decode("utf-8"))


def fetch_latest_afs_folder(traj_type):
    """
    获取最新的AFS文件夹地址
    """
    cmd_str = ""
    if traj_type == "Monthly":
        cmd_str = f"{AfsConsts.EXE_PATH} {AfsConsts.U_AND_W} ls {AfsConsts.MONTHLY_TRAJ_PATH}"
        print(cmd_str)

    # 使用 subprocess 执行命令
    process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    # 获取命令的输出和错误信息
    stdout, stderr = process.communicate()

    # 检查命令执行状态
    if process.returncode != 0:
        raise Exception(f"fetch_latest_afs_folder failed: {stderr.decode('utf-8')}")

    # 将输出解析为文件夹列表
    out_list = stdout.decode("utf-8").splitlines()

    # 初始设定最新日期
    time_format = "%Y%m%d"
    latest_dt = datetime.strptime("19970101", time_format)
    latest_f = ""

    # 遍历文件夹列表，查找最新的文件夹
    for item in out_list:
        if "bak" in item:  # 排除包含 "bak" 的文件夹
            continue
        data_str = item[-9:-1]  # 假设日期信息在最后8个字符
        try:
            dt = datetime.strptime(data_str, time_format)
            if dt > latest_dt:
                latest_dt = dt
                latest_f = item
        except ValueError:
            continue  # 跳过无法解析的条目

    print(latest_f)
    return latest_f


def run():
    """
    run
    """
    date = time.strftime("%Y%m%d", time.localtime(time.time()))
    print(f"------start update experience trajectory: {date}------")
    fetch_monthly_traj_files()


def exist_latest_traj_afs():
    """
    判断是否有最新的经验轨迹文件
    AFS时间与库里时间比较
    """
    db_version = get_db_traj_version()
    afs_dir_name = fetch_latest_afs_folder(Monthly)
    afs_version = Path(afs_dir_name).name

    print("db_version:", db_version, "; afs_version:", afs_version)
    if db_version != afs_version:
        return True
    return False


def get_db_traj_version():
    """
    取出库里轨迹版本
    """
    with PgTool() as pg:
        sql = "select version from exp_traj_monthly limit 1"
        pg.cursor_dest_traj_to_aoi.execute(sql)
        res = pg.cursor_dest_traj_to_aoi.fetchone()
        if res is None:
            return ""
        return res[0]


if __name__ == "__main__":
    """
    main
    """
    try:
        run()
    except Exception as e:
        traceback.print_exc()
        raise e
