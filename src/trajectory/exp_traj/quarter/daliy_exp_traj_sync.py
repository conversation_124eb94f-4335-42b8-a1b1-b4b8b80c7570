"""
每日例行更新经验轨迹，一次入最新一天
"""

import os.path
import sys

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("../../..")

from fetch_traj_from_afs import fetch_quarter_traj_files, exist_latest_traj_afs
from exp_traj_to_db import process_exp_traj_to_db, rename_db
from datetime import datetime


def run():
    """
    run
    """
    start_time = datetime.today().strftime("%Y-%m-%d %H:%M")
    print(f"---update exp traj start: {start_time}---")

    # 判断是否有最新的轨迹文件更新
    print("step_1: exist_latest_traj_afs")
    need_update = exist_latest_traj_afs()
    if not need_update:
        return

    # 拉取经验轨迹文件
    print("step_2: fetch_quarter_traj_files")
    fetch_quarter_traj_files()

    # 经验轨迹入库
    print("step_3: process_exp_traj_to_db")
    process_success = process_exp_traj_to_db()
    if not process_success:
        return

    # 库更新
    print("step_4: rename_db")
    rename_db()

    end_time = datetime.today().strftime("%Y-%m-%d %H:%M")
    print(f"---update exp traj all done: {end_time}---")


if __name__ == "__main__":
    """
    main
    """
    run()
