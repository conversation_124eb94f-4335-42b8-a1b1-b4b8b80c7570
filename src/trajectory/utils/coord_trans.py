# !/usr/bin/env python3
# -*- coding: UTF-8 -*-

"""
坐标转换
支持输入输出格式为经纬度
支持输入输出格式为wkt
"""

import math
import shapely.wkt
from shapely.geometry import Point


def bd09_to_gcj02_wkt(p_wkt):
    """
    bd09转gcj02，输入输出为wkt
    """
    point = shapely.wkt.loads(p_wkt)
    lon, lat = bd09_to_gcj02(point.x, point.y)
    return Point(lon, lat).wkt


def gcj02_to_bd09_wkt(p_wkt):
    """
    gcj02转bd09，输入输出为wkt
    """
    point = shapely.wkt.loads(p_wkt)
    lon, lat = gcj02_to_bd09(point.x, point.y)
    return Point(lon, lat).wkt


def wgs84_to_gcj02_wkt(p_wkt):
    """
    wgs84转gcj02，输入输出为wkt
    """
    point = shapely.wkt.loads(p_wkt)
    lon, lat = wgs84_to_gcj02(point.x, point.y)
    return Point(lon, lat).wkt


def gcj02_to_wgs84_wkt(p_wkt):
    """
    gcj02转wgs84，输入输出为wkt
    """
    point = shapely.wkt.loads(p_wkt)
    lon, lat = gcj02_to_wgs84(point.x, point.y)
    return Point(lon, lat).wkt


def bd09_to_wgs84_wkt(p_wkt):
    """
    bd09转wgs84，输入输出为wkt
    """
    point = shapely.wkt.loads(p_wkt)
    lon, lat = bd09_to_wgs84(point.x, point.y)
    return Point(lon, lat).wkt


def wgs84_to_bd09_wkt(p_wkt):
    """
    wgs84转bd09，输入输出为wkt
    """
    point = shapely.wkt.loads(p_wkt)
    lon, lat = wgs84_to_bd09(point.x, point.y)
    return Point(lon, lat).wkt


def ll_to_mc_wkt(p_wkt):
    """
    经纬度转墨卡托，输入输出为wkt
    """
    point = shapely.wkt.loads(p_wkt)
    x, y = ll_to_mc([point.x, point.y])
    return Point(x, y).wkt


def mc_to_ll_wkt(p_wkt):
    """
    墨卡托转经纬度，输入输出为wkt
    """
    point = shapely.wkt.loads(p_wkt)
    x, y = mc_to_ll([point.x, point.y])
    return Point(x, y).wkt


def bd09_to_gcj02(lon, lat):
    """
    bd09转gcj02
    """
    x = lon - 0.0065
    y = lat - 0.006
    z = math.sqrt(x * x + y * y) - 0.00002 * math.sin(y * xPi)
    theta = math.atan2(y, x) - 0.000003 * math.cos(x * xPi)
    ggLon = z * math.cos(theta)
    ggLat = z * math.sin(theta)
    return [ggLon, ggLat]


def gcj02_to_bd09(lon, lat):
    """
    gcj02转bd09
    """
    z = math.sqrt(lon * lon + lat * lat) + 0.00002 * math.sin(lat * xPi)
    theta = math.atan2(lat, lon) + 0.000003 * math.cos(lon * xPi)
    bdLon = z * math.cos(theta) + 0.0065
    bdLat = z * math.sin(theta) + 0.006
    return [bdLon, bdLat]


def wgs84_to_gcj02(lon, lat):
    """
    wgs84转gcj02
    """
    if out_of_china(lon, lat):
        return [lon, lat]
    else:
        d_lat = transformLat(lon - 105.0, lat - 35.0)
        d_lon = transformLon(lon - 105.0, lat - 35.0)
        rad_lat = lat / 180.0 * PI
        magic = math.sin(rad_lat)
        magic = 1 - ee * magic * magic
        sqrt_magic = math.sqrt(magic)
        d_lat = (d_lat * 180.0) / ((a * (1 - ee)) / (magic * sqrt_magic) * PI)
        d_lon = (d_lon * 180.0) / (a / sqrt_magic * math.cos(rad_lat) * PI)
        mg_lat = lat + d_lat
        mg_lon = lon + d_lon
        return [mg_lon, mg_lat]


def gcj02_to_wgs84(lon, lat):
    """
    gcj02转wgs84
    """
    if out_of_china(lon, lat):
        return [lon, lat]
    else:
        d_lat = transformLat(lon - 105.0, lat - 35.0)
        d_lon = transformLon(lon - 105.0, lat - 35.0)
        rad_lat = lat / 180.0 * PI
        magic = math.sin(rad_lat)
        magic = 1 - ee * magic * magic
        sqrt_magic = math.sqrt(magic)
        d_lat = (d_lat * 180.0) / ((a * (1 - ee)) / (magic * sqrt_magic) * PI)
        d_lon = (d_lon * 180.0) / (a / sqrt_magic * math.cos(rad_lat) * PI)
        mg_lat = lat + d_lat
        mg_lon = lon + d_lon
        return [lon * 2 - mg_lon, lat * 2 - mg_lat]


def bd09_to_wgs84(lon, lat):
    """
    bd09转wgs84
    """
    gcj02Result = bd09_to_gcj02(lon, lat)
    return gcj02_to_wgs84(gcj02Result[0], gcj02Result[1])


def wgs84_to_bd09(lon, lat):
    """
    wgs84转bd09
    """
    gcj02Result = wgs84_to_gcj02(lon, lat)
    return gcj02_to_bd09(gcj02Result[0], gcj02Result[1])


def ll_to_mc(point):
    """
    经纬度转墨卡托
    """
    factor = None
    point[0] = get_loop(point[0], -180, 180)
    point[1] = get_range(point[1], -74, 74)

    temp = point.copy()
    for i in range(len(LL_BAND)):
        if temp[1] >= LL_BAND[i]:
            factor = LL2MC[i]
            break

    if factor is None:
        for i in range(len(LL_BAND) - 1, -1, -1):
            if temp[1] <= -LL_BAND[i]:
                factor = LL2MC[i]
                break

    mc = convertor(point, factor)
    point = [float(mc[0]), float(mc[1])]
    return point


def mc_to_ll(point):
    """
    墨卡托转经纬度
    """
    factor = None
    temp = [abs(point[0]), abs(point[1])]
    for i in range(len(MC_BAND)):
        if temp[1] >= MC_BAND[i]:
            factor = MC2LL[i]
            break

    lonlat = convertor(point, factor)
    point = [float(lonlat[0]), float(lonlat[1])]
    return point


def out_of_china(lon, lat):
    """
    判断是否在国外，在国外则不做偏移
    """
    # 纬度3.86~53.55,经度73.66~135.05
    return not (73.66 < lon < 135.05 and 3.86 < lat < 53.55)


def transformLon(lon, lat):
    """
    经度变换
    """
    ret = (
        300.0
        + lon
        + 2.0 * lat
        + 0.1 * lon * lon
        + 0.1 * lon * lat
        + 0.1 * math.sqrt(abs(lon))
    )
    ret += (
        (20.0 * math.sin(6.0 * lon * PI) + 20.0 * math.sin(2.0 * lon * PI)) * 2.0 / 3.0
    )
    ret += (20.0 * math.sin(lon * PI) + 40.0 * math.sin(lon / 3.0 * PI)) * 2.0 / 3.0
    ret += (
        (150.0 * math.sin(lon / 12.0 * PI) + 300.0 * math.sin(lon / 30.0 * PI))
        * 2.0
        / 3.0
    )
    return ret


def transformLat(lon, lat):
    """
    纬度变换
    """
    ret = (
        -100.0
        + 2.0 * lon
        + 3.0 * lat
        + 0.2 * lat * lat
        + 0.1 * lon * lat
        + 0.2 * math.sqrt(abs(lon))
    )
    ret += (
        (20.0 * math.sin(6.0 * lon * PI) + 20.0 * math.sin(2.0 * lon * PI)) * 2.0 / 3.0
    )
    ret += (20.0 * math.sin(lat * PI) + 40.0 * math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
    ret += (
        (160.0 * math.sin(lat / 12.0 * PI) + 320 * math.sin(lat * PI / 30.0))
        * 2.0
        / 3.0
    )
    return ret


# constant
xPi = 3.14159265358979324 * 3000.0 / 180.0
PI = 3.1415926535897932384626
a = 6378245.0
ee = 0.00669342162296594323

# mc and ll transform constant
EARTH_RADIUS = 6370996.81
MC_BAND = [12890594.86, 8362377.87, 5591021, 3481989.83, 1678043.12, 0]
LL_BAND = [75, 60, 45, 30, 15, 0]
MC2LL = [
    [
        1.410526172116255e-008,
        8.983055096488720e-006,
        -1.99398338163310,
        2.009824383106796e002,
        -1.872403703815547e002,
        91.60875166698430,
        -23.38765649603339,
        2.57121317296198,
        -0.03801003308653,
        1.733798120000000e007,
    ],
    [
        -7.435856389565537e-009,
        8.983055097726239e-006,
        -0.78625201886289,
        96.32687599759846,
        -1.85204757529826,
        -59.36935905485877,
        47.40033549296737,
        -16.50741931063887,
        2.28786674699375,
        1.026014486000000e007,
    ],
    [
        -3.030883460898826e-008,
        8.983055099835780e-006,
        0.30071316287616,
        59.74293618442277,
        7.35798407487100,
        -25.38371002664745,
        13.45380521110908,
        -3.29883767235584,
        0.32710905363475,
        6.856817370000000e006,
    ],
    [
        -1.981981304930552e-008,
        8.983055099779535e-006,
        0.03278182852591,
        40.31678527705744,
        0.65659298677277,
        -4.44255534477492,
        0.85341911805263,
        0.12923347998204,
        -0.04625736007561,
        4.482777060000000e006,
    ],
    [
        3.091913710684370e-009,
        8.983055096812155e-006,
        0.00006995724062,
        23.10934304144901,
        -0.00023663490511,
        -0.63218178102420,
        -0.00663494467273,
        0.03430082397953,
        -0.00466043876332,
        2.555164400000000e006,
    ],
    [
        2.890871144776878e-009,
        8.983055095805407e-006,
        -0.00000003068298,
        7.47137025468032,
        -0.00000353937994,
        -0.02145144861037,
        -0.00001234426596,
        0.00010322952773,
        -0.00000323890364,
        8.260885000000000e005,
    ],
]
LL2MC = [
    [
        -0.00157021024440,
        1.113207020616939e005,
        1.704480524535203e015,
        -1.033898737604234e016,
        2.611266785660388e016,
        -3.514966917665370e016,
        2.659570071840392e016,
        -1.072501245418824e016,
        1.800819912950474e015,
        82.5,
    ],
    [
        8.277824516172526e-004,
        1.113207020463578e005,
        6.477955746671608e008,
        -4.082003173641316e009,
        1.077490566351142e010,
        -1.517187553151559e010,
        1.205306533862167e010,
        -5.124939663577472e009,
        9.133119359512032e008,
        67.5,
    ],
    [
        0.00337398766765,
        1.113207020202162e005,
        4.481351045890365e006,
        -2.339375119931662e007,
        7.968221547186455e007,
        -1.159649932797253e008,
        9.723671115602145e007,
        -4.366194633752821e007,
        8.477230501135234e006,
        52.5,
    ],
    [
        0.00220636496208,
        1.113207020209128e005,
        5.175186112841131e004,
        3.796837749470245e006,
        9.920137397791013e005,
        -1.221952217112870e006,
        1.340652697009075e006,
        -6.209436990984312e005,
        1.444169293806241e005,
        37.5,
    ],
    [
        -3.441963504368392e-004,
        1.113207020576856e005,
        2.782353980772752e002,
        2.485758690035394e006,
        6.070750963243378e003,
        5.482118345352118e004,
        9.540606633304236e003,
        -2.710553267466450e003,
        1.405483844121726e003,
        22.5,
    ],
    [
        -3.218135878613132e-004,
        1.113207020701615e005,
        0.00369383431289,
        8.237256402795718e005,
        0.46104986909093,
        2.351343141331292e003,
        1.58060784298199,
        8.77738589078284,
        0.37238884252424,
        7.45,
    ],
]


def convertor(from_point, factor):
    """
    转换执行函数
    """
    if from_point is None or factor is None:
        return

    x = factor[0] + factor[1] * abs(from_point[0])
    temp = abs(from_point[1]) / factor[9]
    y = (
        factor[2]
        + factor[3] * temp
        + factor[4] * temp * temp
        + factor[5] * temp * temp * temp
        + factor[6] * temp * temp * temp * temp
        + factor[7] * temp * temp * temp * temp * temp
        + factor[8] * temp * temp * temp * temp * temp * temp
    )
    if from_point[0] < 0:
        x *= -1
    else:
        x *= 1

    if from_point[1] < 0:
        y *= -1
    else:
        y *= 1
    return [x, y]


def get_range(v, a, b):
    """
    获取经度范围
    """
    if a is not None:
        v = max(v, a)
    if b is not None:
        v = min(v, b)
    return v


def get_loop(v, a, b):
    """
    获取纬度范围
    """
    while v > b:
        v -= b - a
    while v < a:
        v += b - a
    return v


def to_radians(ang_deg):
    """
    计算弧度值
    """
    return math.pi * ang_deg / 180
