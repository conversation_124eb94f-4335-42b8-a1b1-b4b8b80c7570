# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pg库链接接口
"""
import psycopg2
import os

# 项目根目录
root_path = os.path.dirname(os.path.dirname(__file__) + "/../../")


class PgTool:
    """
    pg库操作工具
    """

    def __init__(self):
        """
        初始化连接
        """
        self.conn_aoi_dest_traj = psycopg2.connect(
            database="aoi_dest_traj_db",
            user="aoi_dest_traj_rw",
            password="aoi_dest_traj_rw",
            host="*************",
            port="5432",
        )
        self.cursor_aoi_dest_traj = self.conn_aoi_dest_traj.cursor()

        self.conn_aoi_dest_traj2 = psycopg2.connect(
            database="aoi_dest_traj_db2",
            user="aoi_dest_traj2_rw",
            password="aoi_dest_traj2_rw",
            host="*************",
            port="6432",
        )
        self.cursor_aoi_dest_traj2 = self.conn_aoi_dest_traj2.cursor()

        self.conn_dest_traj_to_aoi = psycopg2.connect(
            database="dest_traj_to_aoi",
            user="dest_traj_to_aoi_se_rw",
            password="uwymqbnx",
            host="*************",
            port="8432",
        )
        self.cursor_dest_traj_to_aoi = self.conn_dest_traj_to_aoi.cursor()

        self.conn_aoi = psycopg2.connect(
            database="master_back",
            user="master_back",
            password="master_back",
            host="*************",
            port="8033",
        )
        self.cursor_aoi = self.conn_aoi.cursor()

        self.conn_traj_db = psycopg2.connect(
            database="traj_db",
            user="traj_rw",
            password="traj_rw",
            host="*************",
            port="5432",
        )
        self.cursor_traj_db = self.conn_traj_db.cursor()

        self.conn_accept_data_db = psycopg2.connect(
            database="accept_data",
            user="accept_rw",
            password="accept_rw_2025",
            host="*************",
            port="8765",
        )
        self.cursor_accept_data_db = self.conn_accept_data_db.cursor()

    def __enter__(self):
        """
        支持with操作
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        支持with操作，自动关闭连接
        """
        if not self.conn_aoi_dest_traj.closed:
            self.conn_aoi_dest_traj.commit()
            self.conn_aoi_dest_traj.close()

        if not self.conn_aoi_dest_traj2.closed:
            self.conn_aoi_dest_traj2.commit()
            self.conn_aoi_dest_traj2.close()

        if not self.conn_dest_traj_to_aoi.closed:
            self.conn_dest_traj_to_aoi.commit()
            self.conn_dest_traj_to_aoi.close()

        if not self.conn_aoi.closed:
            self.conn_aoi.commit()
            self.conn_aoi.close()

        if not self.conn_traj_db.closed:
            self.conn_traj_db.commit()
            self.conn_traj_db.close()

        if not self.conn_accept_data_db.closed:
            self.conn_accept_data_db.commit()
            self.conn_accept_data_db.close()

    def commit(self):
        """
        提交
        """
        if not self.conn_aoi_dest_traj.closed:
            self.conn_aoi_dest_traj.commit()

        if not self.conn_aoi_dest_traj2.closed:
            self.conn_aoi_dest_traj2.commit()

        if not self.conn_dest_traj_to_aoi.closed:
            self.conn_dest_traj_to_aoi.commit()

        if not self.conn_aoi.closed:
            self.conn_aoi.commit()

        if not self.conn_traj_db.closed:
            self.conn_traj_db.commit()

        if not self.conn_accept_data_db.closed:
            self.conn_accept_data_db.commit()

    def rollback(self):
        """
        回滚
        """
        if not self.conn_aoi_dest_traj.closed:
            self.conn_aoi_dest_traj.rollback()

        if not self.conn_aoi_dest_traj2.closed:
            self.conn_aoi_dest_traj2.rollback()

        if not self.conn_dest_traj_to_aoi.closed:
            self.conn_dest_traj_to_aoi.rollback()

        if not self.conn_aoi.closed:
            self.conn_aoi.rollback()

        if not self.conn_traj_db.closed:
            self.conn_traj_db.rollback()

        if not self.conn_accept_data_db.closed:
            self.conn_accept_data_db.rollback()
