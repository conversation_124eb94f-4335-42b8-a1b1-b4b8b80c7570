"""
轨迹工具包
"""
import hashlib

from src.parking.recognition import dbutils
from src.tools import pgsql


def get_dest_traj_by_bid(bid: str) -> list[str]:
    """
    根据 bid 获取轨迹
    """
    db_name = get_dest_traj_db_name(bid)
    if not db_name:
        return []

    if db_name == "aoi_dest_traj_db":
        config = pgsql.AOI_DEST_TRAJ
    elif db_name == "aoi_dest_traj_db2":
        config = pgsql.AOI_DEST_TRAJ2
    else:
        raise KeyError(f"unknown db_name: {db_name}, must be 'aoi_dest_traj_db' or 'aoi_dest_traj_db2'")

    sql = f"""
        select st_astext(geom) from dest_traj
        where bid = %s and hash_index = %s
    """
    hash_index = calcu_hash_index(bid)
    ret = dbutils.fetch_all(config, sql, [bid, hash_index])
    return [r[0] for r in ret]


def get_dest_traj_db_name(bid: str) -> str:
    """
    获取库名
    """
    sql = """
        select db_name from dest_traj_bid_db_index where bid = %s
    """
    ret = dbutils.fetch_one(pgsql.AOI_DEST_TRAJ, sql, [bid])
    return ret[0] if ret else None


def calcu_hash_index(bid):
    """
    使用 hashlib 计算 bid 的哈希值
    """
    hash_object = hashlib.md5(bid.encode())
    hash_value = int(hash_object.hexdigest(), 16)
    return hash_value % 10
