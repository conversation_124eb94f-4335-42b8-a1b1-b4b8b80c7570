"""
处理 COWC 数据集，生成用于训练的图像和标签。
文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/1FWaLzu6_F/KYPqzMOrVde81P
"""
from dataclasses import dataclass
from pathlib import Path

import cv2
import numpy as np
from tqdm import tqdm

from src.tools import tsv, utils

SCALES = {
    "Columbus_CSUAV_AFRL": 0.5,
    "Potsdam_ISPRS": 0.6,
    "Selwyn_LINZ": 0.7,
    "Toronto_ISPRS": 0.6,
    "Utah_AGRC": 0.6,
    "Vaihingen_ISPRS": 0.7,
}


@dataclass
class Case:
    """
    代表一个原始的 COWC 图像-标注对。
    """

    name: str
    image: np.ndarray
    label_car: np.ndarray
    label_negative: np.ndarray


def load_case(image_path: Path):
    """
    加载一个 COWC 图像-标注对。
    """
    image = cv2.imread(str(image_path))

    label_car_path = image_path.parent / f"{image_path.stem}_Annotated_Cars.png"
    label_car = cv2.imread(str(label_car_path), cv2.IMREAD_GRAYSCALE)

    label_negative_path = image_path.parent / f"{image_path.stem}_Annotated_Negatives.png"
    label_negative = cv2.imread(str(label_negative_path), cv2.IMREAD_GRAYSCALE)

    source = image_path.parent.name
    case = Case(image_path.stem, image, label_car, label_negative)
    return case, source


def resize_case(case: Case, source: str):
    """
    将一个 COWC 图像-标注对缩放到指定比例。
    """
    scale = SCALES[source]
    image = resize(case.image, scale)
    label_car = resize(case.label_car, scale)
    label_negative = resize(case.label_negative, scale)
    return Case(case.name, image, label_car, label_negative)


def split_case(case: Case, size: int):
    """
    将一个 COWC 图像-标注对拆分成多个小图像-标注对。
    """
    return (
        Case(f"{case.name}_{i}", image, label_car, label_negative)
        for i, (image, label_car, label_negative) in enumerate(
            zip(
                split(case.image, size),
                split(case.label_car, size),
                split(case.label_negative, size),
            )
        )
    )


def add_noise_to_case(case: Case):
    """
    给 COWC 图像-标注对添加噪声。
    """
    noise_image = add_noise(case.image, noise_intensity=10)
    return Case(case.name, noise_image, case.label_car, case.label_negative)


def generate_label_from_case(case: Case):
    """
    生成标注，COWC 原始标注只有一个单像素点，不够大，这里画大一点。
    """
    image = np.zeros(case.label_car.shape, dtype=np.uint8)

    car_points = [(col, row) for row, col in zip(*np.nonzero(case.label_car))]
    if not car_points:
        return None

    for point in car_points:
        cv2.circle(image, point, 2, 1, thickness=cv2.FILLED)

    # neg_points = [(col, row) for row, col in zip(*np.nonzero(case.label_negative))]
    # for point in neg_points:
    #     cv2.circle(image, point, 2, 2, thickness=cv2.FILLED)

    return image


# helpers:


def resize(image: np.ndarray, scale: float):
    """
    将图像缩放到指定比例。
    """
    h, w = image.shape[:2]
    return cv2.resize(image, (int(w * scale), int(h * scale)))


def split(image: np.ndarray, advice_size: int):
    """
    将图像拆分成多个小图像。
    """
    h, w = image.shape[:2]
    num_rows = h // advice_size
    num_cols = w // advice_size

    num_rows = num_rows if num_rows > 0 else 1
    num_cols = num_cols if num_cols > 0 else 1

    if num_rows * num_cols < 2:
        yield image
        return

    for row in range(num_rows):
        for col in range(num_cols):
            start_row = row * advice_size
            if row < num_rows - 1:
                end_row = start_row + advice_size
            else:
                end_row = h

            start_col = col * advice_size
            if col < num_cols - 1:
                end_col = start_col + advice_size
            else:
                end_col = w

            yield image[start_row:end_row, start_col:end_col]


def add_noise(image: np.ndarray, noise_intensity):
    """
    给图像添加噪声。
    """
    noise = np.random.normal(0, noise_intensity, image.shape)
    return np.clip(image + noise, 0, 255).astype(np.uint8)


def preprocess(dataset_dir: Path):
    """
    预处理 COWC 数据集，生成符合训练要求的数据集。
    """
    content_dir = dataset_dir / "ground_truth_sets"
    output_dir = dataset_dir / "output"

    label_suffixes = ["_Annotated_Cars", "_Annotated_Negatives"]
    image_paths = [x for x in content_dir.rglob("*.png") if not any(x for suffix in label_suffixes if suffix in x.stem)]
    cases = (load_case(x) for x in image_paths)
    resized_cases = (resize_case(case, source) for case, source in cases)
    split_cases = (x for case in resized_cases for x in split_case(case, 1024))
    for case in tqdm(split_cases):
        save_path = utils.ensure_path(output_dir / "images_original" / f"{case.name}.jpg")
        cv2.imwrite(str(save_path), case.image)

        # 降低图片质量，使之与自有影像一样差。
        case = add_noise_to_case(case)
        label = generate_label_from_case(case)
        if label is None:
            continue

        save_path = utils.ensure_path(output_dir / "images" / f"{case.name}.jpg")
        cv2.imwrite(str(save_path), case.image, [cv2.IMWRITE_JPEG_QUALITY, 30])

        save_path = utils.ensure_path(output_dir / "labels" / f"{case.name}.png")
        cv2.imwrite(str(save_path), label)


def generate_index_txt(dataset_dir: Path):
    """
    生成用于 paddleseg 训练的索引文件。
    """
    output_dir = dataset_dir / "output"
    image_dir = output_dir / "images"
    rows = [[f"images/{x.name}", f"labels/{x.stem}.png"] for x in image_dir.glob("*")]
    tsv.write_tsv(output_dir / "index.txt", rows, splitter=" ")


def main():
    """
    主函数
    """
    # 数据集的备份可以在 AFS 上找到：/user/map-data-streeview/aoi-ml/datasets/cowc_dataset.zip
    dataset_dir = Path(r"C:\Users\<USER>\Downloads\cowc_dataset")
    preprocess(dataset_dir)
    generate_index_txt(dataset_dir)


if __name__ == "__main__":
    main()
