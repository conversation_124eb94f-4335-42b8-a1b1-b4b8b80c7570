"""
车辆识别流程
文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/1FWaLzu6_F/KYPqzMOrVde81P
"""
from pathlib import Path

import click
import cv2
import numpy as np
from psycopg2.extras import <PERSON>son
from shapely import wkt
from tqdm import tqdm

from src.aikit import boundary, satellite_imagery, preview_image
from src.tools import pgsql, utils, tsv, linq, remote_dir, function

IMAGE_VERSION = "google-2023"
IMAGE_LEVEL = 20
AFS_DIR = remote_dir.RemoteDir(remote_root="/user/map-data-streeview/aoi-ml/recognition_flow/vehicle")
MODEL_DIR = "car_cowc_20240131"


def get_blu_face_bounds_by_bid(bids: list[str], buffer: float) -> list[tuple[str, str]]:
    """
    获取 bid 所对应的 AOI 的边框信息
    :param bids: 待获取的 bid 列表
    :param buffer: buffer 距离，单位：米
    :return: [(bid, wkt)]
    """
    with pgsql.get_connection(pgsql.BACK_CONFIG) as conn:
        sql = """
            select b.poi_bid, st_astext(st_buffer(a.geom, %s * 1e-5))
            from blu_face a inner join blu_face_poi b on a.face_id = b.face_id
            where b.poi_bid in %s;
        """
        ret = pgsql.fetch_all(conn, sql, [buffer, tuple(bids)])
        return ret


def get_buffer_bounds_by_bid(bids: list[str], buffer: float) -> list[tuple[str, str]]:
    """
    获取 POI 所在的坐标 buffer 后的 bounds
    :param bids: 待获取的 bid 列表
    :param buffer: buffer 距离，单位：米
    :return: [(bid, wkt)]
    """
    with pgsql.get_connection(pgsql.POI_SLAVER_CONFIG) as conn:
        sql = """
            select bid, st_astext(st_buffer(geometry, %s * 1e-5)) from poi
            where bid in %s;
        """
        ret = pgsql.fetch_all(conn, sql, [buffer, tuple(bids)])
        return ret


def generate_case(geom_wkt: str):
    """
    生成用于识别的图片集
    :param geom_wkt: 边框信息
    :return: [(image, image_info)]
    """
    bounds = boundary.from_wkt(geom_wkt)
    left, top, right, bottom = bounds
    geo_per_pixel = satellite_imagery.GEO_PER_PIXEL_20
    pred_width, pred_height = (right - left) / geo_per_pixel, (top - bottom) / geo_per_pixel
    if pred_width * pred_height > 4096**2:
        return None, None

    image = satellite_imagery.crop(bounds, IMAGE_VERSION)
    if image is None:
        return None, None

    h, w = image.shape[:2]
    info = {
        "width": w,
        "height": h,
        "region": {"left": left, "top": top, "right": right, "bottom": bottom},
        "options": {"image_version": IMAGE_VERSION, "map_level": IMAGE_LEVEL},
    }
    return image, info


def insert_image_info(task_id: str, info_id: str, geom: str, info: dict[str, any]):
    """
    插入图片信息到数据库（poi_online.recognition_json_info）
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    sql = """
        INSERT INTO recognition_json_info (case_id, type, geom, info, batch, description)
        VALUES (%s, %s, ST_GeomFromText(%s, 4326), %s, %s, %s);
    """
    data = (
        info_id,
        "image",
        geom,
        Json(info),
        task_id,
        "recognition vehicles within the AOI",
    )
    pgsql.execute(conn, sql, data)


def insert_vehicle_result(batch: str, bid: str, points: list):
    """
    插入识别结果到数据库（poi_online.recognition_vehicle_result）
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    sql = """
        INSERT INTO recognition_vehicle_result (bid, geom, predict_area, image_version, image_level, batch)
        VALUES (%s, ST_GeomFromText(%s, 4326), %s, %s, %s, %s);
    """
    data = [(bid, f"POINT ({x} {y})", area, IMAGE_VERSION, IMAGE_LEVEL, batch) for (x, y), area in points]
    pgsql.execute_many(conn, sql, data)


def predict_vehicles(cases_dir: Path, output_dir: Path, gpu: int = 0):
    """
    使用 paddleseg 模型进行识别
    """
    log_path = output_dir.parent / "predict.log"
    commands = f"""
        source ~/chenbaojun/bashrc;
        export CUDA_VISIBLE_DEVICES={gpu};
        python -u predict_zyx.py \
            --config configs/quick_start/car.yml \
            --model_path output/{MODEL_DIR}/best_model/model.pdparams \
            --image_path {cases_dir.absolute()} \
            --save_dir {output_dir.absolute()} > {log_path.absolute()} 2>&1;
    """
    working_directory = "/home/<USER>/dingping/building_research/models/zyxpaddleseg"
    return_code, _ = function.exec_shell_cmd(commands, cwd=working_directory)
    if return_code != 0:
        raise Exception(f"predict_vehicles failed, return code: {return_code}")


def draw_preview(image: np.ndarray, aoi_geom: str, points: list[str]):
    """
    绘制预览图：于影像图上绘制 AOI 框和车辆识别结果。
    """
    bounds = boundary.from_wkt(aoi_geom)
    preview_image.draw_polygon(image, aoi_geom, bounds, color=preview_image.COLOR_BLUE)
    for point in points:
        preview_image.draw_point(image, point, bounds, radius=12, thickness=1, color=preview_image.COLOR_RED)


def extract_spots(image: np.ndarray):
    """
    提取图像中的斑点，输出其中心点坐标和面积，面积可作为置信度
    :param image: 待提取的图像
    :return: [((x, y), area)]
    """
    contours, _ = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours:
        m = cv2.moments(contour)

        if m["m00"] == 0:
            continue

        cx = int(m["m10"] / m["m00"])
        cy = int(m["m01"] / m["m00"])

        area: float = cv2.contourArea(contour)
        yield (cx, cy), area


def get_transform_by_bid(bid: str, mode: str):
    """
    根据 bid 获取配准信息
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    sql = """
        select info from recognition_json_info
        where case_id = %s;
    """
    ret = pgsql.fetch_one(conn, sql, [bid])
    if ret is None:
        return None

    return utils.get_transform_info_from_json(ret[0], mode)


# helpers:


def flat_geom(geom_wkt: str):
    """
    将 MultiPolygon 展开为 Polygon
    """

    def flat(geometry):
        if geometry.geom_type == "Polygon":
            ring = geometry.exterior
            yield list(ring.coords)
        elif geometry.geom_type == "MultiPolygon":
            yield from [x for g in geometry.geoms for x in flat(g)]

    geom = wkt.loads(geom_wkt)
    return list(flat(geom))


def to_pixel(geom: str, transform):
    """
    将几何体转换为像素坐标系
    """
    point = wkt.loads(geom)
    x, y = point.x, point.y
    pixel_x, pixel_y = utils.convert_coordinate((x, y), transform)
    return int(pixel_x), int(pixel_y)


# ------------------------------------------------------------------------------


def task_1_on_de16_cases(task_id: str, bids: list[str]):
    """
    生成识别 case，运行于 de16
    """
    conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
    sql = """
        select case_id from recognition_json_info
        where batch = %s;
    """
    ret = pgsql.fetch_all(conn, sql, [task_id])
    image_info_bids = {x[0] for x in ret}

    with AFS_DIR.sync(f"{task_id}/image") as image_dir:
        image_bids = {x.stem for x in image_dir.glob("*.jpg")}
        completed_bids = image_info_bids & image_bids

        cases = get_buffer_bounds_by_bid(bids, buffer=250)
        for bid, geom in tqdm(cases):
            if bid in completed_bids:
                continue

            image, info = generate_case(geom)
            if image is None:
                info = {"error": "image generation failed"}
                insert_image_info(task_id, bid, geom, info)
                continue

            # save image:
            image_path = utils.ensure_path(image_dir / f"{bid}.jpg")
            cv2.imwrite(str(image_path), image, [cv2.IMWRITE_JPEG_QUALITY, 100])
            # insert info into db:
            insert_image_info(task_id, bid, geom, info)


def task_2_on_3a10_predict(task_id: str, gpu: int):
    """
    运行预测，运行于 3a10（显卡机器）
    """
    with (
        AFS_DIR.sync(f"{task_id}/image") as image_dir,
        AFS_DIR.sync(f"{task_id}/predict") as predict_dir,
    ):
        predict_vehicles(image_dir, predict_dir, gpu)


def task_3_on_de16_interpret(task_id: str):
    """
    后处理：解释/筛选预测结果，运行于 de16
    """
    with AFS_DIR.sync(f"{task_id}/predict") as predict_dir:
        heatmap_dir = predict_dir / "heatmap_prediction" / "1"
        for heatmap_path in tqdm(list(heatmap_dir.glob("*.png"))):
            bid = heatmap_path.stem
            heatmap = cv2.imread(str(heatmap_path), cv2.IMREAD_GRAYSCALE)
            _, heatmap = cv2.threshold(heatmap, 128, 255, cv2.THRESH_BINARY)
            points = list(extract_spots(heatmap))
            transform = get_transform_by_bid(bid, mode="pixel2geo")
            points = [(utils.convert_coordinate((x, y), transform), area) for (x, y), area in points]
            insert_vehicle_result(task_id, bid, points)


def task_4_on_de16_preview(task_id: str):
    """
    生成预览图，运行于 de16
    """
    with (
        AFS_DIR.sync(f"{task_id}/image") as image_dir,
        AFS_DIR.sync(f"{task_id}/preview") as preview_dir,
    ):
        conn = pgsql.get_connection_ttl(pgsql.POI_CONFIG)
        sql = """
            select bid, st_astext(geom) from recognition_vehicle_result
            where batch = %s;
        """
        rows = pgsql.fetch_all(conn, sql, [task_id])
        vehicles = linq.group_by(rows, key=lambda x: x[0], value=lambda x: x[1])
        sql = """
            select case_id, st_astext(geom) from recognition_json_info
            where batch = %s and case_id in %s;
        """
        rows = pgsql.fetch_all(conn, sql, [task_id, tuple(vehicles.keys())])
        aoi_geoms = {bid: geom for bid, geom in rows}

        for bid, points in tqdm(vehicles.items()):
            image_path = image_dir / f"{bid}.jpg"
            aoi_geom = aoi_geoms.get(bid)
            if not image_path.exists() or not aoi_geom:
                continue

            image = cv2.imread(str(image_path))
            draw_preview(image, aoi_geom, points)
            cv2.imwrite(str(preview_dir / f"{bid}.jpg"), image)


@click.command()
@click.option("--task_id", required=True, type=str, help="task id")
@click.option(
    "--step",
    required=True,
    type=str,
    help="4 steps: cases | predict | interpret | preview",
)
@click.option(
    "--file",
    type=click.Path(exists=True, file_okay=True, readable=True),
    help="bid list file, where the bid must be in the first column, in tsv format.  (only 'cases' step)",
    default=None,
)
@click.option("--gpu", type=int, help="the GPU number (only 'predict' step)", default=0)
def main(task_id: str, step: str, file: str, gpu: int):
    """
    主函数
    """

    click.echo(f"{task_id=}, {step=}, {file=}, {gpu=}")

    if step == "cases":
        file_path = Path(file or "")
        if not file_path.is_file():
            text = "when the step is 'cases', the 'file' must exist!"
            click.echo(click.style(text, fg="red"))
            return

        bids = list({x[0] for x in tsv.read_tsv(file_path)})
        task_1_on_de16_cases(task_id, bids)
    elif step == "predict":
        task_2_on_3a10_predict(task_id, gpu)
    elif step == "interpret":
        task_3_on_de16_interpret(task_id)
    elif step == "preview":
        task_4_on_de16_preview(task_id)

    click.echo(f"task {step}: {task_id} finished!")


if __name__ == "__main__":
    main()
