#! /bin/bash
[[ -s "/home/<USER>/.jumbo/etc/bashrc" ]] && source "/home/<USER>/.jumbo/etc/bashrc"
export LD_LIBRARY_PATH=/home/<USER>/.jumbo/lib:$LD_LIBRARY_PATH
#export PATH=/opt/compiler/gcc-8.2/bin:$PATH
export LC_ALL=en_US.UTF-8
alias afsshell="/home/<USER>/aoiMl/afs/output/bin/afsshell"
export AFS_BASE=/user/map-data-streeview
mkdir -p /home/<USER>/aoiMl/weblogs
current=`date +%Y%m%d`
/home/<USER>/.jumbo/bin/python3 /home/<USER>/aoiMl/flask_web.py >>/home/<USER>/aoiMl/weblogs/flask_web_${current}.txt 2>&1