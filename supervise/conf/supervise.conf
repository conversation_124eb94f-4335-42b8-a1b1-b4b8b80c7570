#每个产品线可用一个统一的配置文件，若产品线中有不需要新功能的模块可将其配置在 [global]之前，如本配置文件中的[transmit]。
#要同时使用[global]及本身特殊的配置项的模块，其本身的配置段应该在[global]之后，如本配置文件中的[ui]
#所有有效项需要顶格写。"["与"模块名"及"模块名"与"]"之间不能有间隔。
#报警邮件列表、手机列表若在 [global] 中已配置，则其本身的配置段中不要再配。
#
#alarm_interval:报警间隔。在alarm_interval秒内只在第一次服务重启时报警。以后只写日志不报警。若该项未配置为配置为0则其它配置项均无效。最短时间为60s，小于60按60计算。
#alarm_mail: 报警邮箱，可配置多个，以空格或tab分隔。该行不能超过2048字符（包括行尾的空白字符），超过部分忽略。也可为空，为空时不报警。
#alarm_gsm: 报警手机，最多10个，以空格或tab分隔。需要权限，暂时没用。
#max_tries: 最大重启次数。若指定，则在alarm_interval时间内最多重启max_tries,超过后supervise不再重启服务，supervise发出告警并退出。可为空或0，为空或0时重启次数无限制。
#max_tries_if_coredumped: 若指定，则在alarm_interval时间内coredumped次数达到max_tries_if_coredumped后不再重启服务，supervise发出告警并退出。可为空或0，为空或0时重启次数无限制。

[someModuleThatNoNeedNewFunction]

[global]
alarm_interval : 300
alarm_mail : <EMAIL>
alarm_gsm :
max_tries : 0
max_tries_if_coredumped : 1
